import subprocess
import requests
import os


def get_changed_files():
    # Fetch the latest changes from the remote repository
    subprocess.run(["git", "fetch", "origin"])

    # Get the list of changed files
    changed_files = subprocess.check_output(
        ["git", "diff", "--name-only", "origin/master...HEAD"]
    )

    # Decode the bytes to a string and split into lines
    changed_files_str = changed_files.decode().splitlines()

    # Filter the list of changed files
    inclusions = [
        file
        for file in changed_files_str
        if file.endswith(".java")
        and not file.startswith("src/test")
        and not file.endswith("Mapper.java")
    ]

    # Sort the list and remove the directory path
    inclusions = sorted(inclusions, key=lambda x: x.split("/")[-1])

    return inclusions


def get_git_diff_output_of_file(file):
    #   $(git diff -U0 origin/master...HEAD "$file" | jq -R -s '.')
    git_diff = subprocess.check_output(
        ["git", "diff", "-U0", "origin/master...HEAD", file]
    )
    git_diff = git_diff.decode()
    return git_diff


def post_git_diff_to_llm(git_diff: str) -> str:
    if len(git_diff) <= 10:
        return f"git diff content two less:{git_diff}"

    url = "https://api.fireworks.ai/inference/v1/chat/completions"
    payload = {
        "model": "accounts/fireworks/models/llama-v3p1-405b-instruct",
        "max_tokens": 8000,
        "top_p": 1,
        "top_k": 40,
        "presence_penalty": 0,
        "frequency_penalty": 0,
        "temperature": 0.6,
        "messages": [
            {
                "role": "system",
                "content": """You are an expert code reviewer. Analyze the git diff output and provide a focused review on critical issues only. Your review should:

1. Identify severe bugs or functionality issues that could break the system.
2. Highlight significant security vulnerabilities.
3. Suggest major performance improvements that would have a substantial impact.
4. Point out any critical architectural problems.

For each issue:
1. Briefly describe the problem and its impact.
2. Provide a concise solution or approach to fix it.
3. Include a short code snippet for the fix if necessary.

Ignore minor issues, code style, documentation, and small optimizations.

If there has no critical issues, just say:No critical issues found

Prioritize the most critical issues. Be concise and direct in your feedback.""",
            },
            {
                "role": "user",
                "content": f"{git_diff}",
            },
        ],
    }
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "Authorization": f"Bearer {os.getenv('FIREWORKS_AI_KEY')}",
    }
    llm_response = requests.post(url, headers=headers, json=payload).json()
    print(f"llm_response:\n{llm_response}")
    return (
        llm_response.get("choices", [])[0]
        .get("message", {})
        .get("content", "error:empty response")
    )


overroll_markdown = ""

changed_files = get_changed_files()
if len(changed_files) == 0:
    print("no changed files")
    exit(0)

for file in changed_files:
    # Get the base name
    base_name = os.path.basename(file)
    overroll_markdown = f"{overroll_markdown}## Result of file:{base_name}\n"
    git_diff = get_git_diff_output_of_file(file)
    print(f"git_diff of file{base_name}\n\n{git_diff}\n\n")
    markdown_of_code_review = post_git_diff_to_llm(git_diff)
    overroll_markdown = f"{overroll_markdown}{markdown_of_code_review}"


# Save the overroll_markdown content to a local file
file_name = "code_review_result.md"
with open(file_name, "w") as file:
    file.write(overroll_markdown)
    print(f"file saved into:{file_name}")
