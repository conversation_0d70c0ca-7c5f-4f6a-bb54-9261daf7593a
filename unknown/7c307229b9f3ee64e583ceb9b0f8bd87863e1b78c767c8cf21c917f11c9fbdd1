package net.summerfarm.common.excel.utils;

import com.google.common.collect.Lists;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.ReflectUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFPrintSetup;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @Package: net.summerfarm.common.excel.utils
 * @Description: excel操作工具类
 * @author: <EMAIL>
 * @Date: 2016/8/10
 */
public class ExcelUtils {

    private static final Logger logger = LoggerFactory.getLogger(ExcelUtils.class);

    private String filePath;
    private String sheetName;
    private Workbook workBook;
    private Sheet sheet;
    private List<String> columnHeaderList;
    private List<List<String>> listData;
    private List<Map<String, String>> mapData;
    private boolean flag;

    public ExcelUtils(String filePath, String sheetName) {
        this.filePath = filePath;
        this.sheetName = sheetName;
        this.flag = false;
        this.load();
    }

    public ExcelUtils(Workbook workBook) {
        this.flag = false;
        sheet = workBook.getSheetAt(0);
        getSheetData();
    }

    public ExcelUtils(Workbook workBook,Integer integer) {
        this.flag = false;
        sheet = workBook.getSheetAt(0);
        getSheetDataByAdvance();
    }

    private void load() {
        FileInputStream inStream = null;
        try {
            inStream = new FileInputStream(new File(filePath));
            workBook = WorkbookFactory.create(inStream);
            sheet = workBook.getSheet(sheetName);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (inStream != null) {
                    inStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static String getCellValueStr(Cell cell) {
        String cellValue = "";
        DataFormatter formatter = new DataFormatter();
        if (cell != null) {
            switch (cell.getCellType()) {
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        cellValue = formatter.formatCellValue(cell);
                    } else {
                        DecimalFormat df = new DecimalFormat("#.##");
                        cellValue = df.format(cell.getNumericCellValue());
                    }
                    break;
                case STRING:
                    cellValue = cell.getStringCellValue();
                    break;
                case BOOLEAN:
                    cellValue = String.valueOf(cell.getBooleanCellValue());
                    break;
                case FORMULA:
                    cellValue = String.valueOf(cell.getCellFormula());
                    break;
                case BLANK:
                    cellValue = "";
                    break;
                case ERROR:
                    cellValue = "";
                    break;
                default:
                    cellValue = cell.toString().trim();
                    break;
            }
        }
        return cellValue.trim();
    }

    private void getSheetData() {
        listData = new ArrayList<List<String>>();
        mapData = new ArrayList<Map<String, String>>();
        columnHeaderList = new ArrayList<String>();
        int numOfRows = sheet.getLastRowNum() + 1;
        for (int i = 0; i < numOfRows; i++) {
            Row row = sheet.getRow(i);
            Map<String, String> map = new HashMap<String, String>();
            List<String> list = new ArrayList<String>();
            
            if (row != null) {
                int lastCellNum = row.getLastCellNum();
                boolean hasData = false;
                for (int j = 0; j < lastCellNum; j++) {
                    Cell cell = row.getCell(j);
                    String cellValue = getCellValueStr(cell);
                    if (StringUtils.isNotBlank(cellValue)) {
                        hasData = true;
                    }
                    if (i == 0) {
                        columnHeaderList.add(cellValue);
                    } else {
                        // Check if column header exists before accessing
                        if (j < columnHeaderList.size()) {
                            map.put(columnHeaderList.get(j), cellValue);
                        }
                    }
                    list.add(cellValue);
                }
                
                // Skip if row is empty
                if (!hasData) {
                    logger.warn("Row {} is empty, stopped at here", i);
                    break;
                }
            }
            if (i > 0) {
                mapData.add(map);
                listData.add(list);
            }
        }

        this.flag = true;
    }

    private void getSheetDataByAdvance() {
        listData = new ArrayList<List<String>>();
        mapData = new ArrayList<Map<String, String>>();
        columnHeaderList = new ArrayList<String>();
        int numOfRows = sheet.getLastRowNum() + 1;
        for (int i = 1; i < numOfRows; i++) {
            Row row = sheet.getRow(i);
            Map<String, String> map = new HashMap<String, String>();
            List<String> list = new ArrayList<String>();
            if (row != null) {
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    if (i == 1) {
                        columnHeaderList.add(getCellValueStr(cell));
                    } else {
                        map.put(columnHeaderList.get(j), getCellValueStr(cell));
                    }
                    list.add(getCellValueStr(cell));
                }
            }
            if (i > 1) {
                mapData.add(map);
                listData.add(list);
            }

        }
        flag = true;
    }

    public List<String> getColumnHeaderList() {
        return columnHeaderList;
    }

    public List<List<String>> getListData() {
        return listData;
    }

    public List<Map<String, String>> getMapData() {
        return mapData;
    }

    /**
     * 设置响应头
     * @param response
     * @param fileName
     * @throws UnsupportedEncodingException
     */
    public static void setResponseHeader(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.ms-excel; charset=utf-8");
        response.setHeader("Content-Disposition","attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
    }

    /**
     * 导出excel
     *
     * @param workbook
     * @param fileName
     * @param response
     * @throws IOException
     */
    public static void outputExcel(Workbook workbook, String fileName, HttpServletResponse response) throws IOException {
        OutputStream fileOut = null;
        try {
            fileOut = response.getOutputStream();
            response.setContentType("application/vnd.ms-excel; charset=utf-8");
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(fileOut);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if(fileOut != null){
                fileOut.close();
            }
        }
    }

    /**
     * 配送面单导出excel
     *
     * @param workbook
     * @param fileName
     * @param response
     * @throws IOException
     */
    public static void distributionListOutputExcel(Workbook workbook, String fileName, HttpServletResponse response) throws IOException {
        OutputStream fileOut = null;
        try {
            fileOut = response.getOutputStream();
            response.setContentType("application/vnd.ms-excel; charset=utf-8");
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, "UTF-8"));

            Iterator<Sheet> sheetIterator = workbook.sheetIterator();
            while (sheetIterator.hasNext()){
                Sheet sheet = sheetIterator.next();
                sheet.setFitToPage(true);
                sheet.setAutobreaks(true);

                PrintSetup printSetup = sheet.getPrintSetup();
                printSetup.setLandscape(true);
                printSetup.setScale((short) 100);
                printSetup.setFitHeight((short) 0); // 不限制高度，内容会根据实际需要进行缩放
                printSetup.setFitWidth((short) 1);
            }
            workbook.write(fileOut);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if(fileOut != null){
                fileOut.close();
            }
        }
    }
    /**
     * 配送面单导出压缩包
     * @param workbooks excel文件名 -> excel
     * @param fileName 压缩包文件名
     * @param response
     * @throws IOException
     */
    public static void distributionListOutputZip(Map<String, Workbook> workbooks, String fileName, HttpServletResponse response) throws IOException{
        OutputStream fileOut = null;
        ZipOutputStream zipOutputStream = null;
        try {
            fileOut = response.getOutputStream();
            zipOutputStream = new ZipOutputStream(fileOut);
            for (String name: workbooks.keySet()) {
                Workbook workbook = workbooks.get(name);
                response.setContentType("application/octet-stream; charset=utf-8");
                response.setHeader("Content-Disposition", "attachment; filename="
                        + URLEncoder.encode(fileName, "UTF-8"));
                ZipEntry z = new ZipEntry(name);
                zipOutputStream.putNextEntry(z);
                Iterator<Sheet> sheetIterator = workbook.sheetIterator();
                while (sheetIterator.hasNext()){
                    Sheet sheet = sheetIterator.next();
                    sheet.setFitToPage(true);
                    sheet.setAutobreaks(true);

                    PrintSetup printSetup = sheet.getPrintSetup();
                    printSetup.setLandscape(true);
                    printSetup.setScale((short) 100);
                    printSetup.setFitHeight((short) 0); // 不限制高度，内容会根据实际需要进行缩放
                    printSetup.setFitWidth((short) 1);
                }
                workbook.write(zipOutputStream);
            }
            zipOutputStream.flush();
        } catch (IOException e) {
            logger.error(Global.collectExceptionStackMsg(e));
        } finally {
            if (zipOutputStream != null){
                zipOutputStream.close();
            }
            if (fileOut != null){
                fileOut.close();
            }
        }
    }

    /**
     * 导出压缩包
     * @param workbooks excel文件名 -> excel
     * @param fileName 压缩包文件名
     * @param response
     * @throws IOException
     */
    public static void outputZip(Map<String, Workbook> workbooks, String fileName, HttpServletResponse response) throws IOException{
        OutputStream fileOut = null;
        ZipOutputStream zipOutputStream = null;
        try {
            fileOut = response.getOutputStream();
            zipOutputStream = new ZipOutputStream(fileOut);
            for (String name: workbooks.keySet()) {
                Workbook workbook = workbooks.get(name);
                response.setContentType("application/octet-stream; charset=utf-8");
                response.setHeader("Content-Disposition", "attachment; filename="
                        + URLEncoder.encode(fileName, "UTF-8"));
                ZipEntry z = new ZipEntry(name);
                zipOutputStream.putNextEntry(z);
                workbook.write(zipOutputStream);
            }
            zipOutputStream.flush();
        } catch (IOException e) {
            logger.error(Global.collectExceptionStackMsg(e));
        } finally {
            if (zipOutputStream != null){
                zipOutputStream.close();
            }
            if (fileOut != null){
                fileOut.close();
            }
        }
    }

    /**
     * 导出excel 数据类型都是String 有些可能不合适
     *
     * @param fileName
     * @param response
     * @throws IOException
     */
    public static void outputExcel(Map<String, List<List<String>>> data, String fileName, HttpServletResponse response) throws IOException {
        Workbook workbook = new HSSFWorkbook();
        for (Map.Entry<String, List<List<String>>> entry : data.entrySet()) {
            Sheet sheet = workbook.createSheet(entry.getKey());
            List<List<String>> sheetData = entry.getValue();
            for (int i = 0; i < sheetData.size(); i++) {
                Row row = sheet.createRow(i);
                List<String> rowData = sheetData.get(i);
                for (int j = 0; j < rowData.size(); j++) {
                    row.createCell(j).setCellValue(rowData.get(j));
                }
            }
        }
        outputExcel(workbook, fileName, response);
    }

    /**
     * 导出excel
     *
     * @param titles
     * @param datas
     * @param clazz
     * @param fields
     * @return
     */
    public static void exportExcel(String excelFileName, List<String> titles, List datas, Class clazz, List<String> fields, HttpServletResponse response) throws IOException {
        int cellNum = titles.size();
        if (cellNum != fields.size()) {
            throw new DefaultServiceException(ResultConstant.EXCEL_TITLE_FIELD_NOT_MATCH);
        }
        int rowIndex = 0;

        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Row row = sheet.createRow(rowIndex);
        for (short i = 0; i < cellNum; i++) {
            row.createCell(i).setCellValue(titles.get(i));
        }
        //生成数据

        if (datas != null) {
            Row dataRow = null;
            for (Object obj : datas) {
                dataRow = sheet.createRow(++rowIndex);
                for (short i = 0; i < cellNum; i++) {
                    dataRow.createCell(i).setCellValue(ReflectUtils.invokeGetMethod(clazz, obj, fields.get(i)) + "");
                }
            }
        }

        //输出
        OutputStream fileOut = response.getOutputStream();

        String fileName = excelFileName + System.currentTimeMillis() + ".xls";

        response.setContentType(Files.probeContentType(Paths.get(fileName)));
        response.setHeader("Content-Disposition", "attachment; filename="
                + URLEncoder.encode(fileName, "UTF-8"));
        workbook.write(fileOut);
        fileOut.close();

        //输出
        /*OutputStream fileOut ;
        if (StringUtils.isBlank(excelFileName)) {
            fileOut = response.getOutputStream();

            excelFileName = System.currentTimeMillis()+".xls";

            response.setContentType(Files.probeContentType(Paths.get(excelFile)));
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(excelFileName, "UTF-8"));
        }else {
            fileOut = new FileOutputStream(excelFileName);
        }
        workbook.write(fileOut);
        fileOut.close();*/
    }

    /**
     * 获取单元格里的内容
     *
     * @param cell
     * @return
     */
    public static Object getCellValue(Cell cell) {
        Assert.notNull(cell);
        Object obj;
        switch (cell.getCellType()) {
            case STRING:
                obj = cell.getRichStringCellValue().getString();
                break;
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    obj = cell.getDateCellValue();
                } else {
                    obj = cell.getNumericCellValue();
                }
                break;
            case BOOLEAN:
                obj = cell.getBooleanCellValue();
                break;
            case FORMULA:
                obj = cell.getCellFormula();
                break;
            case BLANK:
                obj = "";
                break;
            default:
                obj = null;
        }
        return obj;
    }

    /**
     * 设置列宽
     * 公式255.86x+184.27
     * 原本参数的单位是1/256个字符宽度
     *
     * @param x
     * @return
     */
    public static int getColumnWidth(double x) {
        return (int) Math.round(255.86 * x + 184.27);
    }

    /**
     * 设置边框
     *
     * @param workbook
     * @return
     */
    public static CellStyle getSurroundBorder(Workbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        return cellStyle;
    }

    public static String getCellStringValue(Cell cell){
        if (cell == null){
            return null;
        }

        return ExcelUtils.getCellValue(cell).toString();
    }

    /**
     * 下标转字母
     * @param columnIndex
     * @return
     */
    public static String excelColIndexToStr(int columnIndex) {
        if (columnIndex <= 0) {
            return null;
        }
        String columnStr = "";
        columnIndex--;
        do {
            if (columnStr.length() > 0) {
                columnIndex--;
            }
            columnStr = ((char) (columnIndex % 26 + (int) 'A')) + columnStr;
            columnIndex = (columnIndex - columnIndex % 26) / 26;
        } while (columnIndex > 0);
        return columnStr;
    }

    public static List<List<String>> detailExcelDynamicHead(List<String> fieldNames) {
        List<List<String>> list = Lists.newArrayList();
        for (String fieldName : fieldNames) {
            List<String> head = Lists.newArrayList();
            head.add(fieldName);
            list.add(head);
        }
        return list;
    }

    public static List<List<String>> detailExcelDynamicHead(String[] fieldNames) {
        List<String> list = new ArrayList<>(Arrays.asList(fieldNames));

        return detailExcelDynamicHead(list);
    }
}
