# 批量导入接口 "directory cannot be null" 异常修复

## 问题描述

`/products/batch-import` 接口报错：**提供方未知异常,异常信息:directory cannot be null**

## 问题分析

### 异常发生位置
异常发生在 `ProductsServiceImpl#createProductBatch` 方法中生成导入报告时：

```java
// 问题代码
CommonFileUtils.generateExcelFile(Global.REPORT_DIR, fileName, workbook);
```

### 根本原因
`Global.REPORT_DIR` 可能为 null，导致 `CommonFileUtils.generateExcelFile` 方法的 `dir` 参数为 null。

### Global.REPORT_DIR 计算逻辑
```java
// Global.java 中的定义
public static final String ROOT_PATH = System.getProperty("user.dir").substring(0, System.getProperty("user.dir").indexOf(File.separator));
public static final String REPORT_DIR = ROOT_PATH + File.separator + "data" + File.separator + "import_report";
```

**可能导致 null 的情况：**
1. `System.getProperty("user.dir")` 返回 null
2. `user.dir` 中不包含 `File.separator`，导致 `indexOf` 返回 -1
3. `substring(0, -1)` 可能导致异常或意外结果

## 修复方案

### 1. 添加安全的目录获取方法

```java
/**
 * 获取报告目录，确保目录不为null
 */
private String getReportDirectory() {
    try {
        // 优先使用Global.REPORT_DIR
        if (StringUtils.isNotBlank(Global.REPORT_DIR)) {
            return Global.REPORT_DIR;
        }
        
        // 备用方案：使用系统临时目录
        String tempDir = System.getProperty("java.io.tmpdir");
        if (StringUtils.isNotBlank(tempDir)) {
            String reportDir = tempDir + File.separator + "import_report";
            // 确保目录存在
            File dir = new File(reportDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (!created) {
                    logger.warn("Failed to create report directory: {}", reportDir);
                }
            }
            return reportDir;
        }
        
        // 最后备用方案：使用当前工作目录
        String userDir = System.getProperty("user.dir");
        if (StringUtils.isNotBlank(userDir)) {
            return userDir + File.separator + "import_report";
        }
        
        // 如果都失败，使用相对路径
        return "import_report";
        
    } catch (Exception e) {
        logger.error("Failed to get report directory, using fallback", e);
        return System.getProperty("java.io.tmpdir", ".");
    }
}
```

### 2. 修改调用代码

**修改前：**
```java
//生成导入报告
CommonFileUtils.generateExcelFile(Global.REPORT_DIR, fileName, workbook);
```

**修改后：**
```java
//生成导入报告
String reportDir = getReportDirectory();
CommonFileUtils.generateExcelFile(reportDir, fileName, workbook);
```

### 3. 同时修复 getTemplateOrReport 方法

```java
@Override
public void getTemplateOrReport(Integer type, String reportFile) {
    //下载报告
    if (StringUtils.isNotBlank(reportFile)) {
        String reportDir = getReportDirectory();
        if (reportFile.startsWith("填写指南")) {
            CommonFileUtils.exportFile(RequestHolder.getResponse(), reportDir, reportFile);
            return;
        }
        // ...
    }
    // ...
}
```

## 修复效果

### 1. 异常处理
- ✅ 解决了 "directory cannot be null" 异常
- ✅ 提供了多层备用方案，确保总能获取到有效目录
- ✅ 增加了异常捕获和日志记录

### 2. 目录获取策略
1. **优先使用** `Global.REPORT_DIR`（如果有效）
2. **备用方案1** 使用系统临时目录 + "import_report"
3. **备用方案2** 使用当前工作目录 + "import_report"  
4. **最后备用** 使用相对路径 "import_report"

### 3. 自动创建目录
- 自动检查目录是否存在
- 如果不存在则自动创建
- 创建失败时记录警告日志但不中断流程

## 测试建议

### 1. 正常场景测试
- 上传正确格式的Excel文件，验证导入成功
- 上传包含错误数据的Excel文件，验证报告生成

### 2. 异常场景测试
- 模拟 `Global.REPORT_DIR` 为 null 的情况
- 模拟系统属性获取失败的情况
- 验证备用目录方案是否生效

### 3. 目录权限测试
- 测试在没有写权限的目录下的行为
- 验证目录自动创建功能

## 注意事项

1. **向后兼容性**：修复保持了原有的功能逻辑，只是增加了安全性
2. **性能影响**：目录检查和创建操作对性能影响很小
3. **日志记录**：增加了详细的日志记录，便于问题排查
4. **异常恢复**：即使在极端情况下也能继续执行，不会中断整个导入流程

## 相关文件

- `src/main/java/net/summerfarm/service/impl/ProductsServiceImpl.java` - 主要修复文件
- `src/main/java/net/summerfarm/common/util/CommonFileUtils.java` - 文件工具类
- `src/main/java/net/summerfarm/contexts/Global.java` - 全局常量定义

## 部署建议

1. **测试环境验证**：先在测试环境部署并验证功能
2. **监控日志**：部署后关注相关日志输出
3. **备份数据**：重要数据建议提前备份
4. **回滚准备**：准备快速回滚方案以防万一
