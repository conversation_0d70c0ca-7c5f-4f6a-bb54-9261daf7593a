#!/bin/bash

# Fetch the latest changes from the remote repository
git fetch origin

# Get the list of changed files
changedFiles=$(git diff --name-only origin/master...HEAD)

# Convert the list to a comma-separated string

inclusions=$(echo "$changedFiles"|grep ".java"| grep -v "src/test" | grep -v "Mapper.java")
inclusions=$(echo "$inclusions" | awk -F/ '{print $NF, $0}' | sort | cut -d' ' -f2-)
echo -e "about to sonar scan for these files:\n$inclusions"

mkdir -p ./llama3

echo -e "Result time:`date`\n\n" > ./llama3/overroll_result.md

system_message="You are a experienced Java programmer, your job is to review others Java code.\n"
system_message="${system_message}Do the work step by step.\n"
system_message="${system_message}Analyze the Java code diff that will be provided to you to identify and fix any bugs or errors.\n"
system_message="${system_message}Submit a corrected version if needed. Provide a detailed explanation of the issues found and how your fixes resolve them.\n"
system_message="${system_message}Label line number of each fund issues.\n"
system_message="${system_message}Label the issues with (FATAL/MAJOR/MINOR), NO FIX NEEDED FOR MINOR ISSUES."

echo -e "system_message:${system_message}"

# Loop over the included files and execute the git diff command
diff_content=""
for file in $inclusions; do
  echo "Processing file: $file"
  base_name=$(basename $file)
  diff_content_of_file=$(git diff -U0 origin/master...HEAD "$file" | jq -R -s '.')
  cleaned_string=$(echo "$diff_content_of_file" | sed 's/^"\(.*\)"$/\1/')
  diff_content="${diff_content}\n${cleaned_string}"
  if grep -q "^\\s*public\\s*interface\\s" $file; then
      echo "The file:${base_name} defines a Java interface. will append implementation to this content"
      continue
  else
      echo "The file does not define a Java interface."
  fi

  echo '{
             "model": "accounts/fireworks/models/llama-v3p1-405b-instruct",
             "max_tokens": 16384,
             "top_p": 1,
             "top_k": 40,
             "presence_penalty": 0,
             "frequency_penalty": 0,
             "temperature": 0.6,
             "messages": [
             {
                "role": "system",
                "content": "'${system_message}'"
            },
                 {
                     "role": "user",
                     "content": "git diff result:\n\n'${cleaned_string}'"
                 }
             ]
           }' > ./llama3/payload.json

  llama3_result=$(curl -X POST \
    --url https://api.fireworks.ai/inference/v1/chat/completions \
    -H 'Accept: application/json' \
    -H 'Content-Type: application/json' \
    -H "Authorization: Bearer $FIREWORKS_AI_KEY" \
    -d @llama3/payload.json)
  echo "${llama3_result}" > ./llama3/${base_name}_llama_result.json
  jq -r '.choices[0].message.content' ./llama3/${base_name}_llama_result.json > ./llama3/${base_name}_llama_result.md
  echo -e "## Result of \`${base_name}\`\n\n" >> ./llama3/overroll_result.md
  jq -r '.choices[0].message.content' ./llama3/${base_name}_llama_result.json >> ./llama3/overroll_result.md
  diff_content=""
done