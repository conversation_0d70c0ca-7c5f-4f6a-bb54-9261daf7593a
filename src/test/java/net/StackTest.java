package net;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.util.DingTalkUtils;
import net.summerfarm.common.util.QrcodeUtils;
import net.summerfarm.enums.AfterSaleWoEnum;
import net.summerfarm.model.domain.easyexcel.bms.BmsDeliveryQuotationListExcel;
import sun.misc.BASE64Decoder;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import java.lang.reflect.Field;
import java.security.Key;
import java.security.SecureRandom;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

public class StackTest {

    private static Key key;
    private static String KEY_STR = "mykey";

    static {
        try {
            KeyGenerator generator = KeyGenerator.getInstance("DES");
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(KEY_STR.getBytes());
            generator.init(secureRandom);
            key = generator.generateKey();
            generator = null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }




    static boolean isbalance(String s) {
        Stack<Character> stack = new Stack<>();
        Map<Character, Character> map = new HashMap<>();
        map.put('(', ')');
        map.put('[', ']');
        map.put('{', '}');
        for (int i = 0; i < s.length(); i++) {
            char ch = s.charAt(i);
            if (map.containsKey(ch)) {
                stack.push(ch);
            } else {
                if (stack.isEmpty()) {
                    return false;
                } else {
                    Character pop = stack.pop();
                    if (ch != map.get(pop)) {
                        return false;
                    }
                }
            }
        }
        return stack.isEmpty();
    }

    public static void main(String[] args) {
//        JSONArray array = new JSONArray();
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("htOrderCode","1122333");
//        array.add(jsonObject);
//        QrcodeUtils.BAPI_qrcode_sc(null,array.toJSONString());
        BmsDeliveryQuotationListExcel bmsDeliveryQuotationListExcel = new BmsDeliveryQuotationListExcel();
        Field[] fields=bmsDeliveryQuotationListExcel.getClass().getDeclaredFields();
        String[] fieldNames=new String[fields.length];
        for(int i=0;i<fields.length;i++){
            fieldNames[i]=fields[i].getName();
        }
        List<String> list = new ArrayList<>(Arrays.asList(fieldNames));
        System.out.println(list.toString());
    }
}


