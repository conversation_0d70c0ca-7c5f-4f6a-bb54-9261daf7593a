package net.summerfarm;

import net.summerfarm.common.util.RedissonLockUtil;
import org.junit.Test;
import org.redisson.api.RLock;
import org.springframework.data.redis.core.RedisTemplate;
import javax.annotation.Resource;

/**
 * @desc
 * @Date 2022/11/11 15:00
 *
 **/
public class RedisUtilTest extends BaseTest {

    @Resource
    private RedisTemplate redisTemplate;

    @Test
    public void testLock() {

        RLock lock = RedissonLockUtil.lock("cullen_test", 100);
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            lock.unlock();
        }
    }

    @Test
    public void testRedis() {
        redisTemplate.opsForValue().set("cullen", "test");
        redisTemplate.opsForValue().get("cullen");

    }

}

