package net.summerfarm.common.util.qiNiu;

import com.qiniu.common.QiniuException;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.QiNiuConstant;
import net.summerfarm.enums.QiNiuUploadEnum;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @Package: net.summerfarm.common.utils.qiNiu
 * @Description: 七牛工具测试类
 * @author: <EMAIL>
 * @Date: 2016/7/21
 */

public class AuthTest {

    private static final Logger logger = LoggerFactory.getLogger(AuthTest.class);

    @Test
    public void getToken(){
        Auth auth = Auth.create(QiNiuConstant.ACCESS_KEY, QiNiuConstant.SECRET_KEY);
        String token=auth.uploadToken("suyuan", "this_is_a_test_img",300,null);
        logger.info("获得的token为：{}",token);
    }

    @Test
    public void enumTest(){
        QiNiuUploadEnum type = QiNiuUploadEnum.valueOf("SKU_BANNER1");
        logger.info(type.getCode());
    }

/*    @Test
    public void delete(){
        //构造一个带指定 Region 对象的配置类
        Configuration configuration = new Configuration(Region.region0());
        //...其他参数参考类注释
        String accessKey = QiNiuConstant.ACCESS_KEY;
        String secretKey = QiNiuConstant.SECRET_KEY;
        String bucket = QiNiuConstant.DEFAULT_BUCKET;
        Auth auth = Auth.create(accessKey, secretKey);
        BucketManager bucketManager = new BucketManager(auth, configuration);
        try {
            bucketManager.delete(bucket, "fileName");
        } catch (QiniuException ex) {
            //如果遇到异常，说明删除失败
            logger.info("七牛云删除失败");
        }
    }*/
}
