package net.summerfarm.bms;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.qiNiu.AuthTest;
import net.summerfarm.module.bms.domain.SettleAccountAggregate;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class BmsSettleAccountTest {
    private static final Logger logger = LoggerFactory.getLogger(AuthTest.class);
    @Resource
    SettleAccountAggregate settleAccountAggregate;
    public void generateBmsSettleAccount(){

    }
}
