package net.summerfarm.payment;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.routing.model.dto.*;
import net.summerfarm.payment.routing.service.PaymentChannelService;
import net.summerfarm.payment.routing.service.PaymentRuleService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2024-12-04
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
@ActiveProfiles("dev")
public class PaymentRoutingServiceTest {

    @Resource
    private PaymentChannelService paymentChannelService;
    @Resource
    private PaymentRuleService paymentRuleService;

    @Test
    public void testSaveChannel() {
        PaymentChannelSaveDTO dto = new PaymentChannelSaveDTO();
        dto.setTenantId(1L);
        dto.setBusinessLine("summerfarm");
        dto.setChannelName("微信原生");
        dto.setCompanyEntity("杭州鲜沐科技");
        dto.setMerchantNo("merchant001");
        dto.setPublicKey("public001");
        dto.setPrivateKey("private001");
        dto.setSecret("secret001");
        dto.setUserId("userId001");
        dto.setAppId("appId001");
        dto.setCertPath("/usr/local/certpath/wechat");
        dto.setOperatorAdminId(867L);
        Long id = paymentChannelService.saveChannel(dto);
        log.info("id: {}", id);
    }

    @Test
    public void testListPageChannels() {
        PaymentChannelQueryDTO dto = new PaymentChannelQueryDTO();
        dto.setTenantId(1L);
        dto.setBusinessLine("summerfarm");
        PageInfo<PaymentChannelListDTO> paymentChannelListDTOPageInfo = paymentChannelService.pageListChannel(dto);
        log.info("paymentChannelListDTOPageInfo: {}", paymentChannelListDTOPageInfo);
    }

    @Test
    public void testQueryListChannel() {
        PaymentChannelQueryDTO dto = new PaymentChannelQueryDTO();
        dto.setTenantId(1L);
        dto.setBusinessLine("summerfarm");
        List<PaymentChannelListDTO> paymentChannelListDTOS = paymentChannelService.listChannel(dto);
        log.info("paymentChannelListDTOS: {}", paymentChannelListDTOS);
    }

    @Test
    public void testChangeStatus() {
        PaymentChannelStatusDTO dto = new PaymentChannelStatusDTO();
        dto.setId(2L);
        dto.setStatus(1);
        boolean result = paymentChannelService.changeStatus(dto);
        log.info("result: {}", result);
    }

    @Test
    public void testQueryPageRuleList() {
        PaymentRuleQueryDTO dto = new PaymentRuleQueryDTO();
        dto.setTenantId(1L);
        dto.setBusinessLine("summerfarm");
        dto.setPageIndex(1);
        dto.setPageSize(10);
        PageInfo<PaymentRuleListDTO> paymentRuleListDTOPageInfo = paymentRuleService.pageListRule(dto);
        log.info("paymentRuleListDTOPageInfo: {}", paymentRuleListDTOPageInfo);
    }

    @Test
    public void testSaveRule() {
        PaymentRuleSaveDTO dto = new PaymentRuleSaveDTO();
        dto.setChannelId(2L);
        dto.setPaymentMethod("wechat");
        dto.setPlatform("miniapp");
        dto.setSceneName("小程序微信支付用原生");
        dto.setAreaNos(Lists.newArrayList(1001L));
        Long id = paymentRuleService.saveRule(dto);
        log.info("id: {}", id);
    }

    @Test
    public void testQueryRuleDetail() {
        PaymentRuleDetailDTO paymentRuleDetailDTO = paymentRuleService.queryRuleDetail(1L);
        log.info("paymentRuleDetailDTO: {}", paymentRuleDetailDTO);
    }

    @Test
    public void testQueryExistedCompanyEntities() {
        PaymentChannelCompanyEntityQueryDTO dto = PaymentChannelCompanyEntityQueryDTO.builder()
                .tenantId(1L)
                .businessLine("summerfarm")
                .statusList(Lists.newArrayList(1, 2))
                .build();
        List<String> companyEntities = paymentChannelService.queryCompanyEntities(dto);
        log.info("companyEntities: {}", companyEntities);
    }
}
