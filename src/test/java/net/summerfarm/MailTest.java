package net.summerfarm;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.task.MailUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/7/25  10:39
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class MailTest {
    @Resource
    private MailUtil mailUtil;

    @Test
    public void test() {
        mailUtil.sendMail("测试邮件", "这是一封测试邮件", new String[]{"<EMAIL>"}, new String[]{});
        System.out.println("ccc");
    }
}
