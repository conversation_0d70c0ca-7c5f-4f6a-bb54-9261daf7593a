/*
package net.summerfarm;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.es.EsIndexContext;
import net.summerfarm.es.EsClientPoolUtil;
import net.summerfarm.mapper.manage.MerchantMapper;
import net.summerfarm.model.domain.Contact;
import net.summerfarm.model.domain.Merchant;
import net.summerfarm.model.input.NearbyReq;
import net.summerfarm.model.input.wnc.FenceChangeAreaHandleMsg;
import net.summerfarm.model.input.wnc.FenceChangeMsg;
import net.summerfarm.model.input.wnc.FenceChangeOrderHandleMsg;
import net.summerfarm.model.vo.NearbyVO;
import net.summerfarm.model.vo.PoiVO;
import net.summerfarm.service.MerchantService;
import net.summerfarm.service.OrderService;
import net.summerfarm.service.SampleApplyService;
import net.summerfarm.service.contact.ContactService;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

*/
/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-12-19
 * @description
 *//*

public class ESTest extends BaseTest {
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private MerchantService merchantService;

    @Test
    public void test11(){
        Merchant merchant = merchantMapper.selectByPrimaryKey(11931L);
        AjaxResult result = merchantService.querySimilarMerchant(merchant);
        System.out.println(result);
    }

}
*/
