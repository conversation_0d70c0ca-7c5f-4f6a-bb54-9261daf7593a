package net.summerfarm.mapper.fms;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mapper.manage.PurchaseInvoiceMapper;
import net.summerfarm.model.domain.PurchaseInvoice;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class InvoiceMapperTest {

    @Resource
    PurchaseInvoiceMapper purchaseInvoiceMapper;

    @Test
    public void selectInvoiceByKeyTest() {
        String invoiceCode = null;
        String invoiceNumber = "12345678900987654321";
        List<PurchaseInvoice> purchaseInvoices = purchaseInvoiceMapper.selectInvoiceId(invoiceCode, invoiceNumber);

        log.info("" + purchaseInvoices.size());
        purchaseInvoices.forEach(item-> {
            log.info(JSON.toJSONString(purchaseInvoices));
        });
    }

}
