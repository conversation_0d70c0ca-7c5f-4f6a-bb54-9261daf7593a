package net.summerfarm.mapper;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.model.domain.OrderHeyTea;
import org.apache.http.util.Asserts;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
class OrderHeyTeaMapperTest {

    @Resource
    private OrderHeyTeaMapper orderHeyTeaMapper;

    @Test
    void selectByHtOrderCode() {
        List<OrderHeyTea> orderHeyTeas = orderHeyTeaMapper.selectByHtOrderCode("345675432");
        Assert.assertNotNull("返回信息不为空", orderHeyTeas);
    }
}