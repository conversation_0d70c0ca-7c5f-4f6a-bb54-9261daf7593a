package net.summerfarm.mapper;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.mapper.manage.MerchantSituationMapper;
import net.summerfarm.model.vo.MerchantSituationVO;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import javax.annotation.Resource;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
class MerchantSituationMapperTest {

  @Resource
  MerchantSituationMapper merchantSituationMapper;

  @Test
  void querySituation() {
    MerchantSituationVO merchantSituationVO = new MerchantSituationVO();

    PageHelper.startPage(1, 10);
    List<MerchantSituationVO> list = merchantSituationMapper.querySituationList(merchantSituationVO, "");
    PageInfo<MerchantSituationVO> page = PageInfoHelper.createPageInfo(list);
    log.info("page.getTotal = {}", page.getTotal());
    Assert.assertEquals(page.getTotal(), 77);

    merchantSituationVO.setCreatorId(1031);
    list = merchantSituationMapper.querySituationList(merchantSituationVO, "");
    page = PageInfoHelper.createPageInfo(list);
    log.info("page.getTotal = {}", page.getTotal());
    Assert.assertEquals(page.getTotal(), 5);

    merchantSituationVO.setCreatorId(null);
    merchantSituationVO.setAdminId(1031);
    list = merchantSituationMapper.querySituationList(merchantSituationVO, "");
    page = PageInfoHelper.createPageInfo(list);
    log.info("page.getTotal = {}", page.getTotal());
    Assert.assertEquals(page.getTotal(), 59);

    list = merchantSituationMapper.querySituationList(merchantSituationVO, "测试");
    page = PageInfoHelper.createPageInfo(list);
    log.info("page.getTotal = {}", page.getTotal());
    Assert.assertEquals(page.getTotal(), 31);
  }
}