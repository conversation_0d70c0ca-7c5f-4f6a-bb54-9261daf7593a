package net.summerfarm.mapper.warehouse;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.AreaTypeEnum;
import net.summerfarm.warehouse.enums.WarehouseLogisticsCenterStatus;
import net.summerfarm.warehouse.mapper.WarehouseStorageCenterMapper;
import net.summerfarm.warehouse.model.vo.WarehouseStorageCenterVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class WarehouseStorageCenterMapperTest {

    @Resource
    WarehouseStorageCenterMapper warehouseStorageCenterMapper;

    @Test
    public void testSelectAll() {
        LocalDateTime now = LocalDateTime.now();
        List<WarehouseStorageCenterVO> warehouseStorageCenterVOS = warehouseStorageCenterMapper.selectAll(AreaTypeEnum.INTERNAL_AREA.getType(), WarehouseLogisticsCenterStatus.VALID.ordinal());
        if (CollectionUtil.isNotEmpty(warehouseStorageCenterVOS)) {
            LocalDateTime todayBeginTime = LocalDateTime.now().with(LocalTime.MIN);
            Set<Integer> warehouseNos = warehouseStorageCenterVOS.stream()
                    // 当天新增的仓不增加配置,该配置由晚上的定时任务机制生成
                    .filter(warehouse -> warehouse.getCreateTime().isBefore(todayBeginTime))
                    .map(WarehouseStorageCenterVO::getWarehouseNo).collect(Collectors.toSet());
        }
    }
}
