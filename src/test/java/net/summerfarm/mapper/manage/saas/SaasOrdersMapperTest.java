package net.summerfarm.mapper.manage.saas;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.model.domain.saas.SaasOrders;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/7
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
class SaasOrdersMapperTest {
    @Resource
    private SaasOrdersMapper saasOrdersMapper;

    @Test
    void batchInsert() {
        List<SaasOrders> saasOrders = new ArrayList<>();
        SaasOrders orders = new SaasOrders();
        orders.setOrderNo("111");
        orders.setOrderTime(new Date());
        orders.setBelongDb("111");
        orders.setStoreName("111");
        orders.setTenantName("111");
        orders.setTotalPrice(new BigDecimal(1));
        saasOrders.add(orders);
        saasOrdersMapper.batchInsert(saasOrders);
        System.out.println(111);
    }
}