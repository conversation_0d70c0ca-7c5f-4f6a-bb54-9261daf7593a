package net.summerfarm.mapper.manage.inventory;

import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mapper.manage.ProductsMapper;
import net.summerfarm.model.vo.ProductVO;
import net.summerfarm.warehouse.mapper.WarehouseInventoryMappingMapper;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class WarehouseInventoryMappingMapperIntegrationTest {

    @Resource
    private WarehouseInventoryMappingMapper warehouseInventoryMappingMapper;
    @Resource
    private ProductsMapper productsMapper;

    @Test
    public void testListWareHouseNos(){
        List<Integer> storeNos = Arrays.asList(125,135);
        String sku = "1029406582817";

        List<WarehouseInventoryMapping> inventoryMappings = warehouseInventoryMappingMapper.listWareHouseNos(
                storeNos, sku);

        ProductVO productVO = productsMapper.queryProductBySku(sku);

        Assert.notEmpty(inventoryMappings);
    }
}
