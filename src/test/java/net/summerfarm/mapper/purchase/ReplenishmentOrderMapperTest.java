package net.summerfarm.mapper.purchase;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.pms.ReplenishmentOrderEnums;
import net.summerfarm.mapper.PurchaseReplenishmentOrderMapper;
import net.summerfarm.model.domain.purchase.PurchaseReplenishmentOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class ReplenishmentOrderMapperTest {

    @Autowired
    private PurchaseReplenishmentOrderMapper replenishmentOrderMapper;

    @Test
    public void mockReplenishmentOrderDataTest() {
        LocalDateTime now = LocalDateTime.now();
        PurchaseReplenishmentOrder order = new PurchaseReplenishmentOrder();
        order.setPdId(1L);
        order.setPdName("社会果实");
        order.setWarehouseNo(1);
        order.setWarehouseName("杭州总仓");
        order.setSkuId("4831436866");
        order.setViewDate(now.with(LocalTime.MIN));
        order.setOrderStatus(ReplenishmentOrderEnums.OrderStatus.WAIT_OPERATE.getValue());
        order.setReplenishmentType(ReplenishmentOrderEnums.ReplenishmentType.URGENT.getValue());
        order.setActivityFlag(Global.FALSE_FLAG);
        //order.setCurrentEnabledQuantity(12);
        order.setCurrentOnWayQuantity(23);
        order.setCurrentTransferInQuantity(12);
        order.setSalesHistoryQuantity(31);
        order.setTransferOutHistoryQuantity(12);
        order.setPreDay(3);
        order.setBacklogDay(4);
        order.setSafeWaterLevel(10);
//        order.setAdviceReplenishmentQuantity(1231);
        order.setSupplierId(8);
        order.setAdminId(1953);
        order.setFinalReplenishmentQuantity(1231);
        order.setFinalSupplierId(8);
        order.setFinalSupplierName("水果供应商1");
        order.setFinalAdminId(1953);
        order.setFinalAdminName("邹金鑫");
        order.setCreateDate(getCreateDate(now));
        order.setCreateTime(now);
        order.setUpdater(Global.SYSTEM_ID);
        order.setUpdateTime(now);
        order.setCreator(Global.SYSTEM_ID);
        order.setDelFlag(Global.TRUE_FLAG);
        replenishmentOrderMapper.insert(order);
    }

    private Integer getCreateDate(LocalDateTime viewDate){
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(BaseDateUtils.MID_DATE_FORMAT);
        return Integer.valueOf(viewDate.format(dateTimeFormatter));
    }

}
