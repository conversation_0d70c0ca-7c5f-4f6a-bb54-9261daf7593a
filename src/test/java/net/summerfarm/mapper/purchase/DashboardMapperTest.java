package net.summerfarm.mapper.purchase;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.offline.StockDashboardFutureMapper;
import net.summerfarm.mapper.offline.StockDashboardHistoryMapper;
import net.summerfarm.mapper.offline.WarehouseEstimatedConsumptionMapper;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.offline.StockDashboardFuture;
import net.summerfarm.model.domain.offline.StockDashboardHistory;
import net.summerfarm.model.domain.offline.WarehouseEstimatedConsumption;
import net.summerfarm.model.input.purchase.StockDashboardQueryInput;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class DashboardMapperTest {

    @Autowired
    private StockDashboardHistoryMapper stockDashboardHistoryMapper;
    @Autowired
    private StockDashboardFutureMapper stockDashboardFutureMapper;
    @Autowired
    private WarehouseEstimatedConsumptionMapper warehouseEstimatedConsumptionMapper;
    @Autowired
    private InventoryMapper inventoryMapper;

    @Test
    public void copyDashboardHistoryDataTest() {
        StockDashboardQueryInput queryInput = new StockDashboardQueryInput();
        List<StockDashboardHistory> stockDashboardHistories = stockDashboardHistoryMapper.selectList(queryInput);

        List<Inventory> inventories = inventoryMapper.selectByPdIds(Sets.newHashSet(3L, 19L));
        for (Inventory inventory : inventories) {
            for (StockDashboardHistory stockDashboardHistory : stockDashboardHistories) {
                stockDashboardHistory.setId(null);
                stockDashboardHistory.setPdId(inventory.getPdId().intValue());
                stockDashboardHistory.setWarehouseNo(2);
                stockDashboardHistory.setSkuId(inventory.getSku());
                stockDashboardHistoryMapper.insert(stockDashboardHistory);
            }
            if ("4831436866".equals(inventory.getSku())) {
                continue;
            }
            for (StockDashboardHistory stockDashboardHistory : stockDashboardHistories) {
                stockDashboardHistory.setId(null);
                stockDashboardHistory.setPdId(inventory.getPdId().intValue());
                stockDashboardHistory.setWarehouseNo(1);
                stockDashboardHistory.setSkuId(inventory.getSku());
                stockDashboardHistoryMapper.insert(stockDashboardHistory);
            }
        }
    }

    @Test
    public void mockDashboardHistoryDataTest() {
        LocalDateTime now = LocalDateTime.now().with(LocalTime.MIN);
        List<StockDashboardHistory> historyList = Lists.newArrayList();
        for (int i = 0; i < 30; i++) {
            StockDashboardHistory history = new StockDashboardHistory();
            history.setPdId(3);
            history.setSkuId("4831436866");
            history.setWarehouseNo(1);
            history.setViewDate(now.minusDays(i+1));
            history.setConsumption(120);
            history.setSalesQuantity(112);
            history.setTransferOutQuantity(8);
            history.setInitQuantity(120);
            history.setEnabledQuantity(0);
            history.setOnWayQuantity(110);
            history.setTransferInQuantity(10);
            historyList.add(history);
        }

        for (StockDashboardHistory history : historyList) {
            int insert = stockDashboardHistoryMapper.insert(history);
            System.out.println(insert);
        }
    }

    @Test
    public void mockDashboardFutureDataTest() {
        LocalDateTime now = LocalDateTime.now().with(LocalTime.MIN);
        List<StockDashboardFuture> futures = Lists.newArrayList();
        for (int i = 0; i < 81; i++) {
            StockDashboardFuture future = new StockDashboardFuture();
            future.setPdId(3);
            future.setSkuId("4831436866");
            future.setWarehouseNo(1);
            future.setViewDate(now.plusDays(i));
            future.setOnWayQuantity(110);
            future.setTransferInQuantity(10);
            futures.add(future);
        }

        for (StockDashboardFuture future : futures) {
            int insert = stockDashboardFutureMapper.insert(future);
            System.out.println(insert);
        }
    }

    @Test
    public void copyDashboardFutureDataTest() {
        StockDashboardQueryInput queryInput = new StockDashboardQueryInput();
        List<StockDashboardFuture> stockDashboardFutures = stockDashboardFutureMapper.selectList(queryInput);

        List<Inventory> inventories = inventoryMapper.selectByPdIds(Sets.newHashSet(3L, 19L));
        for (Inventory inventory : inventories) {
            for (StockDashboardFuture future : stockDashboardFutures) {
                future.setId(null);
                future.setPdId(inventory.getPdId().intValue());
                future.setWarehouseNo(2);
                future.setSkuId(inventory.getSku());
                stockDashboardFutureMapper.insert(future);
            }
            if ("4831436866".equals(inventory.getSku())) {
                continue;
            }
            for (StockDashboardFuture future : stockDashboardFutures) {
                future.setId(null);
                future.setPdId(inventory.getPdId().intValue());
                future.setWarehouseNo(1);
                future.setSkuId(inventory.getSku());
                stockDashboardFutureMapper.insert(future);
            }
        }
    }

    @Test
    public void mockDashboardConsumptionDataTest() {
        LocalDateTime now = LocalDateTime.now().with(LocalTime.MIN);
        List<WarehouseEstimatedConsumption> consumptions = Lists.newArrayList();
        for (int i = 0; i < 81; i++) {
            WarehouseEstimatedConsumption consumption = new WarehouseEstimatedConsumption();
            consumption.setPdId(3);
            consumption.setSkuId("4831436866");
            consumption.setWarehouseNo(1);
            consumption.setViewDate(now.plusDays(i));
//            consumption.setConsumption(120);
//            consumption.setEstimatedSales(110);
//            consumption.setEstimatedTransferOut(10);
            consumptions.add(consumption);
        }

        for (WarehouseEstimatedConsumption consumption : consumptions) {
            int insert = warehouseEstimatedConsumptionMapper.insert(consumption);
            System.out.println(insert);
        }
    }

    @Test
    public void copyDashboardConsumptionDataTest() {
        StockDashboardQueryInput queryInput = new StockDashboardQueryInput();
        List<WarehouseEstimatedConsumption> warehouseEstimatedConsumptions = warehouseEstimatedConsumptionMapper.selectList(queryInput);


        List<Inventory> inventories = inventoryMapper.selectByPdIds(Sets.newHashSet(3L, 19L));
        for (Inventory inventory : inventories) {
            for (WarehouseEstimatedConsumption consumption : warehouseEstimatedConsumptions) {
                consumption.setId(null);
                consumption.setPdId(inventory.getPdId().intValue());
                consumption.setWarehouseNo(2);
                consumption.setSkuId(inventory.getSku());
                warehouseEstimatedConsumptionMapper.insert(consumption);
            }
            if ("4831436866".equals(inventory.getSku())) {
                continue;
            }
            for (WarehouseEstimatedConsumption consumption : warehouseEstimatedConsumptions) {
                consumption.setId(null);
                consumption.setPdId(inventory.getPdId().intValue());
                consumption.setWarehouseNo(1);
                consumption.setSkuId(inventory.getSku());
                warehouseEstimatedConsumptionMapper.insert(consumption);
            }
        }
    }
}
