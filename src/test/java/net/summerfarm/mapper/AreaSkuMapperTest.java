package net.summerfarm.mapper;

import com.alibaba.fastjson.JSON;
import com.cosfo.summerfarm.model.dto.supplyprice.SummerfarmSkuPriceInfoDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.purchase.PurchaseArrangeProvider;
import net.summerfarm.manage.client.purchase.dto.PurchaseArrangeReqDTO;
import net.summerfarm.manage.client.purchase.dto.PurchaseArrangeResDTO;
import net.summerfarm.mapper.manage.AreaSkuMapper;
import net.summerfarm.mapper.manage.StockTaskProcessMapper;
import net.summerfarm.mapper.manage.TmsLackGoodsApprovedMapper;
import net.summerfarm.model.domain.AreaSku;
import net.summerfarm.model.domain.StockTaskProcess;
import net.summerfarm.model.domain.TmsLackGoodsApproved;
import net.xianmu.common.result.DubboResponse;
import org.joda.time.format.DateTimeFormatter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/15  11:04
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class AreaSkuMapperTest {
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private TmsLackGoodsApprovedMapper tmsLackGoodsApprovedMapper;
    @Resource
    private PurchaseArrangeProvider purchaseArrangeProvider;

    @Test
    public void testMyBatisFunction(){
        SummerfarmSkuPriceInfoDTO priceInfoDTO = areaSkuMapper.querySkuPrice("**********", Lists.newArrayList(1001));
        log.info("priceInfoDTO:{}", priceInfoDTO);
    }

    @Test
    public void test(){
        DubboResponse<PurchaseArrangeResDTO> purchaseArrangeResDTODubboResponse = purchaseArrangeProvider.queryPurchase(PurchaseArrangeReqDTO.builder().inStoreTaskId(402831L).build());
        System.out.println(purchaseArrangeResDTODubboResponse.getData().getArrangeId());
    }

    @Test
    public void infoUpdate(){
        areaSkuMapper.updateQualityDate(null, 1001, "**********", "2022.05.22");
    }

    @Test
    public void tmsLackGoodsApproved(){

        List<TmsLackGoodsApproved> tmsLackGoodsApproveds = tmsLackGoodsApprovedMapper.selectByDeliveryPathId(11948);
        if(tmsLackGoodsApproveds.size() > 0){
            log.info("缺货核准生成任务已生成，请勿重复生成");
        }
        System.out.println(tmsLackGoodsApproveds);
    }


}
