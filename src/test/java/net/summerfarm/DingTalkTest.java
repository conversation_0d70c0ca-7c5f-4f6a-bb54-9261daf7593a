package net.summerfarm;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.Client;
import com.aliyun.dingtalkworkflow_1_0.models.SaveIntegratedInstanceHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.SaveIntegratedInstanceRequest;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.google.common.collect.Lists;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstant;
import net.summerfarm.common.util.DingTalkUtils;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.bo.DingdingFormBO;
import net.summerfarm.dingding.bo.ProcessInstanceCreateBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.exception.DingdingProcessException;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.manage.client.ding.DingProcessProvider;
import net.summerfarm.manage.client.ding.dto.ProcessInstanceCreateDTO;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.mapper.manage.DepartmentStaffMapper;
import net.summerfarm.model.DTO.AreaInfoDTO;
import net.summerfarm.model.ding.dto.ProcessCallBackFailDTO;
import net.summerfarm.model.domain.DepartmentStaff;
import net.summerfarm.model.domain.market.ActivitySkuPrice;
import net.summerfarm.model.vo.CostChangeVo;
import net.summerfarm.mq.ApproveInstanceCallbackListener;
import net.summerfarm.mq.msgBody.ProcessCallBackMQDTO;
import net.summerfarm.service.MerchantService;
import net.summerfarm.service.StockTaskService;
import net.xianmu.robot.feishu.enums.MsgTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 获取钉钉注册url
 * @Date: 2020/11/26 20:55
 * @Author: <EMAIL>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class DingTalkTest {

    private static final Logger logger = LoggerFactory.getLogger(Test.class);


    @Resource
    private MerchantService merchantService;

    @Resource
    private DingProcessProvider dingProcessProvider;

    @Resource
    private DepartmentStaffMapper departmentStaffMapper;

    @Resource
    private StockTaskService stockTaskService;
    @Resource
    private ApproveInstanceCallbackListener approveInstanceCallbackListener;

    @Test
    public void test10(){
        stockTaskService.autoAfterSaleIn();
    }

    @Test
    public void test2() throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/call_back/get_call_back_failed_result");
        OapiCallBackGetCallBackFailedResultRequest req = new OapiCallBackGetCallBackFailedResultRequest();
        req.setHttpMethod("GET");
        OapiCallBackGetCallBackFailedResultResponse rsp = client.execute(req, DingTalkUtils.getWorkFlowAccessToken());
        System.out.println(rsp.getBody());
        System.out.println(rsp.getHasMore());
        ProcessCallBackFailDTO processCallBackFailDTO = JSONObject.parseObject(rsp.getBody(), ProcessCallBackFailDTO.class);
        System.out.println("测试环境拉取到的消息1"+processCallBackFailDTO);
        while (processCallBackFailDTO.getHasMore() != null && processCallBackFailDTO.getHasMore()) {
            OapiCallBackGetCallBackFailedResultResponse rsp1 = client.execute(req, DingTalkUtils.getWorkFlowAccessToken());
            processCallBackFailDTO = JSONObject.parseObject(rsp1.getBody(), ProcessCallBackFailDTO.class);
            System.out.println("测试环境拉取到的消息" + processCallBackFailDTO);
        }
    }

    @Test
    public void test5(){
        ProcessCallBackFailDTO processCallBackFailDTO = DingTalkUtils.pullProcessCallBackFailList();
        System.out.println("测试环境拉取到的消息"+processCallBackFailDTO);
    }

    @Test
    public void test3() {
        String templateId = "PROC-08630316-4E6B-432F-B795-1B82BFBA5B16";
        String testUserId = "16540499334149578";
        Long departmentId = 1L;
        OapiV2UserGetResponse.UserGetResponse dingdingUserInfo = DingTalkUtils.getDingdingUserInfo("16540499334149578");
        if (dingdingUserInfo == null) {
            log.error("未查询到钉钉用户信息:{}", "16540499334149578");
        }
        List<Long> deptIdList = dingdingUserInfo.getDeptIdList();
        if (CollectionUtils.isNotEmpty(deptIdList)) {
            departmentId = deptIdList.get(0);
        }
        ProcessInstanceCreateBO build = ProcessInstanceCreateBO.builder()
                .originatorUserId(testUserId)
                .dingdingForms(Lists.newArrayList(DingdingFormBO.builder()
                        .extValue("任务编号")
                        .formName("任务编号")
                        .formValue("1")
                        .build()))
                .deptId(departmentId)
                //.bizId()
                //.adminId()
                //.bizTypeEnum()
                .build();
        String processInstance = DingTalkUtils.createProcessInstance(build, templateId);
        System.out.println(processInstance);
    }

    @Test
    public void test1() {
        try {
            String templateId = "PROC-08630316-4E6B-432F-B795-1B82BFBA5B16";
            // dingProcessProvider.createProcess(ProcessInstanceCreateDTO.builder().build());
            Config config = new Config();
            config.protocol = "https";
            config.regionId = "central";
            // create client
            Client client = new Client(config);
            // DepartmentStaff departmentStaff = departmentStaffMapper.findOneByUserId("16540499334149578");
            Long departmentId = 1L;
            OapiV2UserGetResponse.UserGetResponse dingdingUserInfo = DingTalkUtils.getDingdingUserInfo("16540499334149578");
            if (dingdingUserInfo == null) {
                log.error("未查询到钉钉用户信息:{}", "16540499334149578");
                throw new DingdingProcessException("未查询到钉钉用户信息:" + "16540499334149578");
            }
            List<Long> deptIdList = dingdingUserInfo.getDeptIdList();
            if (CollectionUtils.isNotEmpty(deptIdList)) {
                departmentId = deptIdList.get(0);
            }
            StartProcessInstanceHeaders startProcessInstanceHeaders = new StartProcessInstanceHeaders();
            startProcessInstanceHeaders.xAcsDingtalkAccessToken = DingTalkUtils.getWorkFlowAccessToken();
            StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetailsDetails formComponentValues0Details0Details0 = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetailsDetails()
                    .setId("PhoneField_IZI2LP8QF6O0")
                    .setBizAlias("Phone")
                    .setName("PhoneField")
                    .setValue("123xxxxxxxx")
                    .setExtValue("总个数:1")
                    .setComponentType("PhoneField");
            StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails formComponentValues0Details0 = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails()
                    .setId("PhoneField_IZI2LP8QF6O0")
                    .setBizAlias("Phone")
                    .setName("PhoneField")
                    .setValue("123xxxxxxxx")
                    .setExtValue("总个数:1")
                    .setDetails(java.util.Arrays.asList(
                            formComponentValues0Details0Details0
                    ));
            StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues formComponentValues0 = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues()
                    .setId("PhoneField_IZI2LP8QF6O0")
                    .setBizAlias("myPhoneNumber")
                    .setName("PhoneField")
                    .setValue("123xxxxxxxx")
                    .setExtValue("总个数:1")
                    .setComponentType("PhoneField")
                    .setDetails(java.util.Arrays.asList(
                            formComponentValues0Details0
                    ));
            StartProcessInstanceRequest.StartProcessInstanceRequestTargetSelectActioners targetSelectActioners0 = new StartProcessInstanceRequest.StartProcessInstanceRequestTargetSelectActioners()
                    .setActionerKey("manual_1918_5cd3_xxxx_6a98")
                    .setActionerUserIds(java.util.Arrays.asList(
                            "26652461xxxx5992"
                    ));
            StartProcessInstanceRequest.StartProcessInstanceRequestApprovers approvers0 = new StartProcessInstanceRequest.StartProcessInstanceRequestApprovers()
                    .setActionType("会签：AND；或签：OR；单人：NONE")
                    .setUserIds(java.util.Arrays.asList(
                            "user1"
                    ));
            StartProcessInstanceRequest startProcessInstanceRequest = new StartProcessInstanceRequest()
                    .setOriginatorUserId("16540499334149578")
                    .setFormComponentValues(Lists.newArrayList(formComponentValues0))
                    .setProcessCode(templateId)
                    .setDeptId(departmentId)
                    .setMicroappAgentId(DingdingConstant.FLOW_AGENTID);
            try {
                client.startProcessInstanceWithOptions(startProcessInstanceRequest, startProcessInstanceHeaders, new RuntimeOptions());
            } catch (TeaException err) {
                log.info("异常", err);
                if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题
                    log.info("异常信息:{}:{}", err.code, err.message);
                }

            } catch (Exception _err) {
                log.info("异常", _err);
                TeaException err = new TeaException(_err.getMessage(), _err);
                if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题
                    log.info("异常信息:{}:{}", err.code, err.message);
                }

            }
        } catch (Exception e) {
            log.info("异常", e);
        }
    }

    @Test
    public void aa() {
        merchantService.improveStoreExamine();
    }

    /**
     * 获取回调的url 和事件
     *
     * @throws ApiException
     */
    @Test
    public void test12() throws ApiException {
        String a = "{\"EventType\":\"bpms_instance_change\",\"processCode\":\"11D03B1D-CE3D-4013-917D-C3EA1867A7E4\",\"processInstanceId\":\"002559B7-39E0-48CE-9622-F8DB305EECC0\",\"processType\":71,\"result\":\"agree\",\"title\":\"商品上下架\",\"type\":\"finish\"}";
        System.out.println (a);
        approveInstanceCallbackListener.process (JSON.parseObject (a, ProcessCallBackMQDTO.class));
    }
    @Test
    public void test() throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/call_back/get_call_back");
        OapiCallBackGetCallBackRequest request = new OapiCallBackGetCallBackRequest();
        request.setHttpMethod("GET");
        String token = "0747c9040b4832c39f447cc2b90b9a98";
        OapiCallBackGetCallBackResponse response = client.execute(request, token);
        String url = response.getUrl();
        List<String> callBackTag = response.getCallBackTag();
        System.out.println(url + " " + Arrays.toString(callBackTag.toArray()));
    }

    /**
     * @throws ApiException
     */
    @Test
    public void callTest() throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/call_back/register_call_back");
        OapiCallBackRegisterCallBackRequest request = new OapiCallBackRegisterCallBackRequest();
        request.setUrl("https://admin." + Global.TOP_DOMAIN_NAME + "/admin/dingTalkSync");
        request.setAesKey(Global.EN_AES_KEY);
        request.setToken(Global.TOKEN);
        request.setCallBackTag(Arrays.asList("bpms_task_change"));
        String token = "0747c9040b4832c39f447cc2b90b9a98";
        OapiCallBackRegisterCallBackResponse response = client.execute(request, token);
    }

    public String getToken() throws ApiException {
        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest request = new OapiGettokenRequest();
        request.setAppkey(Global.APP_KEY);
        request.setAppsecret(Global.APP_SECRET);
        request.setHttpMethod("GET");
        OapiGettokenResponse response = client.execute(request);
        return response.getAccessToken();
    }

    /**
     * 查询该模板下的所有审批实例
     *
     * @throws ApiException
     */
    @Test
    public void getProcessId() throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/listids");
        OapiProcessinstanceListidsRequest req = new OapiProcessinstanceListidsRequest();
        req.setProcessCode(Global.ACCOUNT_RECEIVABLE);
        req.setStartTime(1606400189000L);
        req.setSize(10L);
        req.setCursor(0L);
        OapiProcessinstanceListidsResponse response = client.execute(req, getToken());
        List<String> list = response.getResult().getList();
        for (String s : list) {
            System.out.println(s);
        }
    }

    @Test
    public void getProcessInfo() throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/get");
        OapiProcessinstanceGetRequest request = new OapiProcessinstanceGetRequest();
        request.setProcessInstanceId("0e4db8f8-c559-49b0-aaf7-21b0c3a57bf8");
        OapiProcessinstanceGetResponse response = client.execute(request, getToken());
        OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = response.getProcessInstance();
        String result = processInstance.getResult();
        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstance.getFormComponentValues();
        List<OapiProcessinstanceGetResponse.FormComponentValueVo> componentValuesList = formComponentValues.stream()
                .filter(item -> Objects.deepEquals(item.getName(), "充值编号") && Objects.deepEquals(item.getComponentType(), "TextField"))
                .collect(Collectors.toList());
        String value = componentValuesList.get(0).getValue();
        String status = processInstance.getStatus();
        System.out.println(status + "" + result);
    }

    @Test
    public void creatInstance() throws ApiException {

        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/create");
        OapiProcessinstanceCreateRequest request = new OapiProcessinstanceCreateRequest();
        request.setAgentId(Long.valueOf(Global.AGENT_ID));
        request.setProcessCode(Global.ACCOUNT_RECEIVABLE);
        //给组件填入值
        List<OapiProcessinstanceCreateRequest.FormComponentValueVo> formComponentValues = new ArrayList<OapiProcessinstanceCreateRequest.FormComponentValueVo>();
        OapiProcessinstanceCreateRequest.FormComponentValueVo titleComponent = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        titleComponent.setName("应收账款审批");
        titleComponent.setValue("应收账款test");
        OapiProcessinstanceCreateRequest.FormComponentValueVo rechargeNoComponent = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        rechargeNoComponent.setName("充值编号");
        Integer rechargeId = 12;
        rechargeNoComponent.setValue(rechargeId.toString());
        OapiProcessinstanceCreateRequest.FormComponentValueVo rechargeNumComponent = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        rechargeNumComponent.setName("收款金额");
        Double rechargeNum = 2.23;
        rechargeNumComponent.setValue(Double.toString(rechargeNum));
        OapiProcessinstanceCreateRequest.FormComponentValueVo picturePathsComponent = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        picturePathsComponent.setName("凭证");
        String picturePaths = "test/9aeiup9kxobmqaox2.png,test/uel8ryfhqs1w9g8f.jpg";
        String[] oldPictures = picturePaths.split(",");
        List<String> pictures = new ArrayList<>(1);
        for (String picture : oldPictures) {
            picture = Global.RECEIVABLE_PICTURE_PATH_PREFIX + picture;
            pictures.add(picture);
        }
        JSONArray jsonArray = JSONUtil.parseArray(pictures);
        picturePathsComponent.setValue(jsonArray.toJSONString(0));
        if (Objects.nonNull("reamrk")) {
            OapiProcessinstanceCreateRequest.FormComponentValueVo remarkComponent = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
            remarkComponent.setName("备注");
            remarkComponent.setValue("1234");
            formComponentValues.add(remarkComponent);
        }
        formComponentValues.add(titleComponent);
        formComponentValues.add(rechargeNoComponent);
        formComponentValues.add(rechargeNumComponent);
        formComponentValues.add(picturePathsComponent);

        request.setFormComponentValues(formComponentValues);
        //默认为系统工作人
        request.setOriginatorUserId("5957642744985517013");
        request.setDeptId(-1L);

        String accessToken = getToken();
        try {
            OapiProcessinstanceCreateResponse response = client.execute(request, accessToken);
            response.isSuccess();
            System.out.println(response.getErrmsg());
        } catch (ApiException e) {
            logger.info("创建应收账款审批实例失败 err={}", e.getErrMsg());
        }
    }

    @Test
    public void testSpaceId() throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/cspace/info");
        OapiProcessinstanceCspaceInfoRequest req = new OapiProcessinstanceCspaceInfoRequest();
        req.setUserId("1812");
        OapiProcessinstanceCspaceInfoResponse rsp = client.execute(req, DingTalkUtils.init().getToken());
        System.out.println(rsp.getBody());
    }

    @Resource
    private DingTalkMsgSender dingTalkMsgSender;

    @Test
    public void testFeiShu() {
        // 查询sku在所有区域
        String title = "【特价倒挂提醒】";
        StringBuilder content = new StringBuilder("##### " + title + "\n");
        content.append("> ###### 活动信息：测试消息推送").append("\n");
        content.append("> ###### 商品名称：").append("测试商品").append("\n");
        content.append("> ###### 商品规格：").append("楞个大一个").append("\n");
        content.append("> ###### 商品SKU：").append("xxxx2131xxx").append("\n");
        content.append("> ###### 倒挂城市如下：").append("\n");
        content.append("> ###### 库存仓：").append("航程").append("\n")
                .append("> ###### 日周期库存成本：")
                .append(25.89).append("\n")
                .append("> ###### 最新批次成本：")
                .append(78.99).append("\n")
                .append(">> ###### 批次号：").append("202312212321321312")
                .append("\n");

        DingTalkMsgReceiverIdBO bo = new DingTalkMsgReceiverIdBO();
        bo.setReceiverIdList(Collections.singletonList(10369L));
        bo.setTitle(title);
        bo.setContent("hhhh，这是一条测试消息");
        bo.setText(content.toString());
        bo.setMsgType(DingTalkMsgTypeEnum.MARKDOWN.getType());

        dingTalkMsgSender.sendMessageWithFeiShu(bo);
    }
}


