package net.summerfarm.task;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ManageApplication;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.AreaSkuMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.manage.SkuPriceTaskRecordMapper;
import net.summerfarm.module.products.inbound.scheduler.RiseSkuPriceJob;
import net.summerfarm.service.ActivityNewService;
import net.summerfarm.service.PriceStrategyService;
import net.summerfarm.service.helper.AdjustSkuPriceHelper;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.assertTrue;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ManageApplication.class)
@ActiveProfiles("dev2")
public class RiseSkuPriceJobTest {

    @Resource
    private RiseSkuPriceJob riseSkuPriceJob;

    @Mock
    private InventoryMapper inventoryMapper;
    @Mock
    private AdjustSkuPriceHelper adjustSkuPriceHelper;
    @Mock
    private ActivityNewService activityNewService;
    @Mock
    private AreaSkuMapper areaSkuMapper;
    @Mock
    private SkuPriceTaskRecordMapper skuPriceTaskRecordMapper;
    @Mock
    private AreaMapper areaMapper;
    @Mock
    private PriceStrategyService priceStrategyService;

    @Test
    public void testProcessResult() throws Exception {
        // 准备测试数据
        XmJobInput jobInput = new XmJobInput();
        jobInput.setJobParameters("{}");

        ProcessResult processResult = riseSkuPriceJob.processResult(jobInput);
        log.info("processResult:{}", processResult);
        assertTrue(processResult.getStatus() == InstanceStatus.SUCCESS);
    }
}