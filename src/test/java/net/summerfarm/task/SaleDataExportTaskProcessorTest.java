package net.summerfarm.task;

import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@ActiveProfiles("dev")
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class SaleDataExportTaskProcessorTest {


    @Resource
    SaleDataExportTaskProcessor saleDataExportTaskProcessor;

    @Test
    public void processResult() throws Exception {
        ProcessResult result = saleDataExportTaskProcessor.processResult(null);
        log.info("SaleDataExportTaskProcessor:{}", result);
        Assert.assertTrue(result.getStatus().equals(InstanceStatus.SUCCESS));

        XmJobInput context = new XmJobInput();
        context.setInstanceParameters("19311,10000000");
        result = saleDataExportTaskProcessor.processResult(context);

        log.info("SaleDataExportTaskProcessor:{}, XmJobInput:{}", result, context);
        Assert.assertTrue(result.getStatus().equals(InstanceStatus.SUCCESS));
    }
}
