package net.summerfarm.price;

import net.summerfarm.BaseTest;
import net.summerfarm.module.products.inbound.scheduler.RiseSkuPriceJob;
import net.summerfarm.module.products.inbound.scheduler.RollBackSkuPriceJob;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/5  15:29
 */
public class RiseJobTest extends BaseTest {
    @Resource
    RiseSkuPriceJob riseSkuPriceJob;
    @Resource
    RollBackSkuPriceJob rollBackSkuPriceJob;

    @Test
    public void testRiseJob() throws Exception {
        riseSkuPriceJob.processResult(new XmJobInput());
    }

    @Test
    public void testRollBackJob() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        rollBackSkuPriceJob.processResult(xmJobInput);
    }
}
