package net.summerfarm;

import com.alibaba.fastjson.JSON;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.common.Term;
import net.summerfarm.common.util.PreCutOffOrderUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.AreaEnum;
import net.summerfarm.model.domain.Area;
import net.summerfarm.model.domain.Config;
import org.junit.Test;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-12-09
 * @description
 */
public class HanLPTest {

    @Test
    public void test() throws IOException {
        getTimeByStoreNo(29);
    }



    public LocalDateTime getTimeByStoreNo(Integer storeNo){
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(), Global.CLOSING_ORDER_TIME);
        Config config = new Config();
        config.setValue("24,17,31,14,33,27,29,32,34,35");
        if(!Objects.isNull(config) && !StringUtils.isEmpty(config.getValue())){

            closingTime = Arrays.asList(config.getValue().split(Global.SEPARATING_SYMBOL)).contains(String.valueOf(storeNo)) ?
                    LocalDateTime.of(LocalDate.now(), Global.NJ_CLOSING_ORDER_TIME) : closingTime;

        }
        Config result = new Config();
        result.setValue("26,28");
        if(!Objects.isNull(result) && !StringUtils.isEmpty(result.getValue())){
            closingTime =Arrays.asList(result.getValue().split(Global.SEPARATING_SYMBOL)).contains(String.valueOf(storeNo)) ?
                    LocalDateTime.of(LocalDate.now(), Global.TWENTY_THREE_CLOSING_TIME) : closingTime;
        }
        return closingTime;


    }

    @Test
    public void calcChangeStartTime(){
        Area area = new Area();
        area.setNextDeliveryDate(LocalDate.of(2021,04,15));
        area.setChangeStatus(0);

        //正常截单
        List<LocalTime> timeList = new ArrayList<>();
        timeList.add(Global.CLOSING_ORDER_TIME);
        //大客户截单
        timeList.add(Global.CBD_BIG_CLOSING_ORDER_TIME);
        //南京截单
        timeList.add(Global.NAN_JING_CLOSING_ORDER_TIME);

        //计算最早一批截单时间
        timeList.sort(LocalTime::compareTo);
        LocalTime startTime = timeList.get(0);

        //计算日期
        LocalDate calcDate = startTime.isAfter(LocalTime.now()) ? LocalDate.now() : LocalDate.now().plusDays(1);
        if(area.getNextDeliveryDate() != null && area.getNextDeliveryDate().isAfter(calcDate)){
            calcDate = area.getNextDeliveryDate();
        }
        calcDate = LocalDate.of(2021,04,25);
        //开始切换日期（切换中的日期必然是当天，预约切换的状态需要根据配送日期计算）
        LocalDate startDate = LocalDate.of(2021,04,25);
        if(area.getChangeStatus() <= AreaEnum.ChangeStatus.READY.ordinal()){
            Integer[] dfArr = new Integer[]{2,6};
            if(dfArr != null && dfArr.length != 0){
                if(Objects.equals(dfArr[0], 0)){
                    startDate = calcDate;
                } else {
                    int week = calcDate.getDayOfWeek().getValue();
                    int firstWeek = dfArr[0];
                    int lastWeek = dfArr[dfArr.length - 1];
                    //lastweek 6 week 7
                    if(lastWeek < week){
                        startDate = calcDate.plusDays(7 + firstWeek - week);
                    } else {
                        for (Integer calcWeek : dfArr) {
                            if (calcWeek >= week) {
                                startDate = calcDate.plusDays(calcWeek - week);
                                break;
                            }
                        }
                    }
                }
            }
        }

        System.out.println(LocalDateTime.of(startDate, startTime));
    }
}
