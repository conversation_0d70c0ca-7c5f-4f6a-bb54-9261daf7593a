package net.summerfarm;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.DateUtil;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


import java.io.*;
import java.time.LocalDateTime;

import static net.summerfarm.enums.saas.BrandTypeEnum.out;


/**
 * <AUTHOR>
 * @title: OssTest
 * @date 2022/10/915:43
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class OssTest {


    @Test
    public void uploadFile() {
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("测试");
        title.createCell(1).setCellValue("哈哈哈");
        Row row = sheet.createRow(1);
        row.createCell(0).setCellValue("123");
        row.createCell(1).setCellValue("456");
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            byte[] uploadBytes = byteArrayOutputStream.toByteArray();
            //OssOperationUtil.uploadOverdueThreeDay("test/" + "拉拉.xls", uploadBytes);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void uploadOss() {

        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("测试");
        title.createCell(1).setCellValue("哈哈哈");
        Row row = sheet.createRow(1);
        row.createCell(0).setCellValue("123");
        row.createCell(1).setCellValue("456");
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            byte[] uploadBytes = byteArrayOutputStream.toByteArray();
            //OssOperationUtil.uploadOverdueThreeDay("test/" + "拉拉.xls", uploadBytes);
        } catch (
                IOException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void download() {
        String[] name = "xianmu-test-oss:manage/蜻蜓队长.jpg".split(":");
        System.out.println(name[0]);
        //System.out.println(name[1]);
        //URL url = OssOperationUtil.getUrl("蜻蜓.jpg");
        //System.out.println(url.toString());
    }

    public static String removeCharAt(String s, int pos) {
        return s.substring(0, pos) + s.substring(pos + 1);
    }



    @Test
    public void uploadByte() {

        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("测试");
        title.createCell(1).setCellValue("哈哈哈");
        Row row = sheet.createRow(1);
        row.createCell(0).setCellValue("123");
        row.createCell(1).setCellValue("456");
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            byte[] uploadBytes = byteArrayOutputStream.toByteArray();
            OssUploadUtil.upload("蜻蜓队长.xls", uploadBytes, OSSExpiredLabelEnum.THREE_DAY);
        } catch (Exception e) {
            log.error("出错了", e);
        }

    }

    @Test
    public void uploadFiles() {

        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("测试");
        title.createCell(1).setCellValue("哈哈哈");
        Row row = sheet.createRow(1);
        row.createCell(0).setCellValue("123");
        row.createCell(1).setCellValue("456");
        File file = null;
        FileOutputStream out = null;
        String fileNameSuffix = "蜻蜓队长"+ DateUtil.formatDateBasic(LocalDateTime.now())+".xls";
        try {
            String fileName = System.getProperty("java.io.tmpdir") + File.separator + fileNameSuffix;
            file = new File(fileName);
            out = new FileOutputStream(file);
            workbook.write(out);
            OssUploadUtil.upload(fileNameSuffix, file, OSSExpiredLabelEnum.NO_EXPIRATION);
        } catch (Exception e) {
            log.error("出错了", e);
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    log.error("关闭workbook失败", e);
                }
            }
            if (out != null){
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("关闭out失败", e);
                }
            }
            if (file != null){
                file.deleteOnExit();
            }
        }

    }
}
