package net.summerfarm;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.DateUtil;
import net.summerfarm.controller.auth.UserPermissionController;
import net.summerfarm.controller.auth.req.UserPermissionReq;
import net.summerfarm.mapper.manage.StockTaskProcessDetailMapper;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.param.QuantityChangeRecordQueryVO;
import net.summerfarm.model.vo.QuantityChangeRecordVO;
import net.summerfarm.model.vo.StockTaskProcessDetailVO;
import net.summerfarm.service.QuantityRecordService;
import net.summerfarm.task.FruitPriceAjustProcessor;
import net.xianmu.common.result.CommonResult;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * @Package: net.summerfarm
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/8/5
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class BaseTest {

    protected ApplicationContext applicationContext;

    @Resource
    private QuantityRecordService quantityRecordService;

    @Resource
    private StockTaskProcessDetailMapper stockTaskProcessDetailMapper;
    @Resource
    private FruitPriceAjustProcessor priceAjustProcessor;

    @Test
    public void test222(){
        List<StockTaskProcessDetailVO> stockTaskProcessDetailVOS = stockTaskProcessDetailMapper.selectByTaskId(207491);
        System.out.println(JSON.toJSONString(stockTaskProcessDetailVOS));
//        UserPermissionReq userPermissionReq = new UserPermissionReq();
//        userPermissionReq.setPageNum(1);
//        userPermissionReq.setPageSize(10);
//        userPermissionReq.setWarehouseNo(1);
//        userPermissionReq.setUserName("周琳丹");
//        CommonResult<PageInfo<Admin>> pageInfoCommonResult = userPermissionController.userWarehousePermission(userPermissionReq);
//        log.info("结果:{}",JSON.toJSONString(pageInfoCommonResult.getData().getList()));
    }


    @Test
    public void fruitTest() throws Exception {
        String param = "{\n" +
                "\t\"warehouseNo\": 20,\n" +
                "\t\"skus\": [\"596007117061\"]\n" +
                "}";
        priceAjustProcessor.processResult(JSON.parseObject(param, XmJobInput.class));
    }

    @Test
    public void quTest(){
        QuantityChangeRecordQueryVO quantityChangeRecordVO = new QuantityChangeRecordQueryVO();
        quantityChangeRecordVO.setSku("168606274223");
        quantityChangeRecordVO.setAreaNo(1);
        quantityChangeRecordVO.setStartTime(DateUtil.toLocalDateTime(1669564800000L));
        quantityChangeRecordVO.setEndTime(DateUtil.toLocalDateTime(1669910400000L));
//        quantityChangeRecordVO.setChangeBizTypeName(Lists.newArrayList(QuantityChangeTypeDTO.changeTypeName));


        AjaxResult ajaxResult = quantityRecordService.selectChangeList(1, 10, quantityChangeRecordVO, null);
        //List<QuantityChangeRecordVO> quantityChangeRecordVOS1 = quantityChangeRecordMapper.selectNByIds(quantityChangeRecordVOS);
        log.info("数据为:{}", JSON.toJSONString(ajaxResult.getData()));
    }

    public ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    protected MockMvc mockMvc;

    protected MockHttpServletRequest request;
    protected MockHttpServletResponse response;

    @Autowired
    protected WebApplicationContext wac;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(this.wac)
                .alwaysDo(MockMvcResultHandlers.print())
                .build();
        request = new MockHttpServletRequest();
        request.setCharacterEncoding("UTF-8");
        response = new MockHttpServletResponse();
    }
    private final ObjectMapper objectMapper = new ObjectMapper();

    public String toJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public <T> T decode(String jsonString, Class<T> valueType) {
        try {
            return objectMapper.readValue(jsonString, valueType);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}