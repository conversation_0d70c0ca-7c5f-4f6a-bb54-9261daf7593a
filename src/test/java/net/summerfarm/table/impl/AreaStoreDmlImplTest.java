package net.summerfarm.table.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import net.summerfarm.BaseTest;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.table.DbTableDml;
import org.apache.commons.compress.utils.Lists;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

public class AreaStoreDmlImplTest extends BaseTest {

    @Autowired
    @Qualifier("areaStoreDmlImpl")
    private DbTableDml dbTableDml;

    @Before
    public void setUp() {
    }

    @After
    public void tearDown() {
    }

    @Test
    public void tableDml() {
        DtsModel dtsModel = JSONObject.parseObject("{\"data\":[{\"id\":\"3930567\",\"area_no\":\"20\",\"sku\":\"596722617630\",\"quantity\":\"0\",\"update_time\":\"2023-09-04 15:03:18\",\"admin_id\":null,\"lead_time\":\"0\",\"market_price\":null,\"cost_price\":null,\"price_status\":\"0\",\"online_quantity\":\"10001\",\"sale_lock_quantity\":\"336\",\"lock_quantity\":\"336\",\"road_quantity\":\"0\",\"sync\":\"1\",\"safe_quantity\":\"0\",\"change\":\"0\",\"status\":\"0\",\"auto_transfer\":\"0\",\"support_reserved\":\"0\",\"reserve_max_quantity\":\"0\",\"reserve_min_quantity\":\"0\",\"reserve_use_quantity\":\"0\",\"warning_quantity\":null,\"send_warning_flag\":\"0\",\"advance_quantity\":\"0\",\"tenant_id\":null,\"warehouse_tenant_id\":null,\"owner_code\":null,\"owner_name\":null}],\"database\":\"xianmudb\",\"es\":1693810998000,\"id\":53454066,\"isDdl\":false,\"mysqlType\":{\"id\":\"int(11)\",\"area_no\":\"int(11)\",\"sku\":\"varchar(30)\",\"quantity\":\"int(10) unsigned\",\"update_time\":\"datetime\",\"admin_id\":\"int(11)\",\"lead_time\":\"int(3)\",\"market_price\":\"decimal(10,2)\",\"cost_price\":\"decimal(10,2)\",\"price_status\":\"int(2)\",\"online_quantity\":\"int(10) unsigned\",\"sale_lock_quantity\":\"int(11) unsigned\",\"lock_quantity\":\"int(11) unsigned\",\"road_quantity\":\"int(11) unsigned\",\"sync\":\"int(2)\",\"safe_quantity\":\"int(11)\",\"change\":\"int(11) unsigned\",\"status\":\"int(2)\",\"auto_transfer\":\"tinyint(2)\",\"support_reserved\":\"int(11)\",\"reserve_max_quantity\":\"int(11)\",\"reserve_min_quantity\":\"int(11)\",\"reserve_use_quantity\":\"int(11)\",\"warning_quantity\":\"int(11)\",\"send_warning_flag\":\"int(11)\",\"advance_quantity\":\"int(10) unsigned\",\"tenant_id\":\"bigint(20)\",\"warehouse_tenant_id\":\"bigint(20)\",\"owner_code\":\"varchar(32)\",\"owner_name\":\"varchar(64)\"},\"old\":[{\"update_time\":\"2023-09-04 14:16:50\",\"online_quantity\":\"9999\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":4,\"area_no\":4,\"sku\":12,\"quantity\":4,\"update_time\":93,\"admin_id\":4,\"lead_time\":4,\"market_price\":3,\"cost_price\":3,\"price_status\":4,\"online_quantity\":4,\"sale_lock_quantity\":4,\"lock_quantity\":4,\"road_quantity\":4,\"sync\":4,\"safe_quantity\":4,\"change\":4,\"status\":4,\"auto_transfer\":-6,\"support_reserved\":4,\"reserve_max_quantity\":4,\"reserve_min_quantity\":4,\"reserve_use_quantity\":4,\"warning_quantity\":4,\"send_warning_flag\":4,\"advance_quantity\":4,\"tenant_id\":-5,\"warehouse_tenant_id\":-5,\"owner_code\":12,\"owner_name\":12},\"table\":\"area_store\",\"threadId\":739850,\"ts\":1693810998422,\"type\":\"UPDATE\"}", DtsModel.class);

        dbTableDml.tableDml(dtsModel);
    }
}