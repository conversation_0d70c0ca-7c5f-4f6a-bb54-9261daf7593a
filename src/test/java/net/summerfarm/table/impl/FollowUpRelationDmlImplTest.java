package net.summerfarm.table.impl;

import net.summerfarm.BaseTest;
import net.summerfarm.mq.DtsModel;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

public class FollowUpRelationDmlImplTest extends BaseTest {

    @Resource
    private FollowUpRelationDmlImpl followUpRelationDml;

    @Test
    public void tableDml() {
        DtsModel model = new DtsModel();
        model.setType("UPDATE");
        Map<String, String> oldMap = new HashMap<>();
        oldMap.put("bdId", "1");
        model.setOld(Collections.singletonList(oldMap));
        followUpRelationDml.tableDml(model);
    }
}