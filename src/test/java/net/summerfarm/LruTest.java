package net.summerfarm;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.Map;
import java.util.Set;
/*
public class LinkedHashmapTest {
    public static void main(String[] args) {
        LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>(4, 0.75f, true);
        linkedHashMap.put(1, "a");
        linkedHashMap.put(2, "b");
        linkedHashMap.put(3, "c");
        linkedHashMap.put(4, "d");
        linkedHashMap.put(5, "c");
        linkedHashMap.put(6, "d");
        linkedHashMap.forEach((key,value)->{
            System.out.println(key+"--"+value);
        });
        System.out.println("----");
        linkedHashMap.get(3);
        linkedHashMap.get(2);
        linkedHashMap.get(1);
        linkedHashMap.get(4);

        linkedHashMap.forEach((k,v)->{
            System.out.println(k + "--" + v);
        });
    }
}*/

public class LruTest<K, V> {
    private final float DEFAULT_LOAD_FACTORY = 0.75f;
    private final int MAX_CACHE_SIZE;
    LinkedHashMap<K, V> linkedHashMap = null;

    public LruTest(int cacheSize) {
        this.MAX_CACHE_SIZE = cacheSize;
        int capacity = (int) Math.ceil(MAX_CACHE_SIZE / DEFAULT_LOAD_FACTORY) + 1;
        linkedHashMap = new LinkedHashMap<K, V>(capacity, DEFAULT_LOAD_FACTORY, true) {
            @Override
            protected boolean removeEldestEntry(Map.Entry eldest) {
                return size() > MAX_CACHE_SIZE;
            }
        };
    }

    public void put(K key, V value) {
        linkedHashMap.put(key, value);
    }

    public V get(K key) {
        return linkedHashMap.get(key);
    }

    public void remove(K key) {
        linkedHashMap.remove(key);
    }

    public Set<Map.Entry<K, V>> getAll() {
        return linkedHashMap.entrySet();
    }

    public static void main(String[] args) {
        LruTest<Object, Object> lruTest = new LruTest<>(5);
        lruTest.put(1, "a");
        lruTest.put(2, "b");
        lruTest.put(3, "c");
        lruTest.put(4, "d");
        lruTest.get(3);
        lruTest.put(5, "e");
        lruTest.put(6, "f");

        lruTest.getAll().forEach(objectObjectEntry -> System.out.println(objectObjectEntry.getKey() + "---" + objectObjectEntry.getValue()));

        System.out.println("-------");

    }
}
