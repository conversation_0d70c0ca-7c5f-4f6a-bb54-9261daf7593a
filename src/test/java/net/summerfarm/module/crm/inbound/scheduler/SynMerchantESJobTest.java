package net.summerfarm.module.crm.inbound.scheduler;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class SynMerchantESJobTest {

    @Resource
    private SynMerchantESJob synMerchantESJob;

    @Test
    public void processResult() throws Exception {
        XmJobInput input = new XmJobInput();
        input.setInstanceParameters("{\"mIds\":[349768]}");

        synMerchantESJob.processResult(input);
    }
}