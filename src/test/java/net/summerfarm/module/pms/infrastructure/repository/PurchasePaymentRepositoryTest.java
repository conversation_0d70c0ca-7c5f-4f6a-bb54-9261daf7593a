package net.summerfarm.module.pms.infrastructure.repository;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.pms.domain.repository.PurchasePaymentRepository;
import net.summerfarm.module.pms.infrastructure.model.offline.PurchasesInboundDetailAggregateOfflineEntity;
import net.summerfarm.module.pms.infrastructure.model.offline.PurchasesPaymentIntervalEntity;
import net.summerfarm.module.pms.infrastructure.param.PurchasesInboundQueryParam;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/3/14 11:27
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
class PurchasePaymentRepositoryTest {

    @Resource
    private PurchasePaymentRepository purchasePaymentRepository;

    @Test
    void queryPurchasesInboundInterval() {
        PurchasesInboundQueryParam param = new PurchasesInboundQueryParam();
        param.setSupplierList(Arrays.asList(77L,1831L));

        Optional<PurchasesPaymentIntervalEntity> purchasesPaymentIntervalEntity = purchasePaymentRepository.queryPurchasesInboundInterval(param);

        System.out.println(purchasesPaymentIntervalEntity);
    }

    @Test
    void queryPurchasesInbound() {

        PurchasesInboundQueryParam param = new PurchasesInboundQueryParam();
        param.setSupplierList(Arrays.asList(77L,1831L));
        param.setInboundIdGte(2L);
        param.setInboundIdLte(82L);

        List<PurchasesInboundDetailAggregateOfflineEntity> entityList = purchasePaymentRepository.queryPurchasesInbound(param);

        System.out.println(entityList);
    }
}