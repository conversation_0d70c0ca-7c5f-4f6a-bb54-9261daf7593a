package net.summerfarm.module.pms.domain;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.SaasThreadLocalUtil;
import net.summerfarm.model.DTO.SaasTokenInfoDTO;
import net.summerfarm.module.pms.model.input.PurchasesPaymentExportInput;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/3/14 13:24
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
class PurchasesAggregateTest {

    @Resource
    private PurchasesAggregate purchasesAggregate;

    @Test
    void export() {
        SaasTokenInfoDTO saasTokenInfoDTO = new SaasTokenInfoDTO();
        saasTokenInfoDTO.setTenantId(2L);
        SaasThreadLocalUtil.save(saasTokenInfoDTO);

        long time1 = System.currentTimeMillis();
        PurchasesPaymentExportInput input = new PurchasesPaymentExportInput();
        input.setSupplierList(Arrays.asList(1823L,
                1824L,
                1829L,
                1830L,
                1831L,
                1838L,
                1839L,
                1841L,
                1842L,
                1843L,
                1844L,
                1848L,
                1851L,
                1852L,
                1857L,
                1859L,
                1860L,
                1865L,
                1868L,
                1869L));
        input.setStartTime(LocalDateTime.of(2022,1,1,0,0,0));
        input.setEndTime(LocalDateTime.of(2024,1,1,0,0,0));

        purchasesAggregate.export(input);

        System.out.println("导出时间");
        System.out.println(System.currentTimeMillis() - time1);
        System.out.println("导出结束");

    }

    @Test
    void doExport(){
        PurchasesPaymentExportInput input = new PurchasesPaymentExportInput();
        input.setSupplierList(Arrays.asList(2223L));
        input.setStartTime(LocalDateTime.of(2023,1,11,0,0,0));
        input.setEndTime(LocalDateTime.of(2023,1,13,23,59,0));

        SaasPurchasesExportContext context = new SaasPurchasesExportContext();
        purchasesAggregate.doExport(input,context);
        System.out.println();
    }
}