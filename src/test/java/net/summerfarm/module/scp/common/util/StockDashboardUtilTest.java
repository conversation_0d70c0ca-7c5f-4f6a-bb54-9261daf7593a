package net.summerfarm.module.scp.common.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.scp.model.dto.stock.dashboard.CoreResultDTO;
import net.summerfarm.module.scp.model.input.stock.dashboard.CoreDashboardInput;
import net.summerfarm.module.scp.model.input.stock.dashboard.CoreQuantityInfoInput;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Slf4j
public class StockDashboardUtilTest {

    @Test
    public void coreStockDashboardCalculateTest() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(5);

        CoreDashboardInput input = new CoreDashboardInput();
        input.setStartDate(startDate);
        input.setEndDate(endDate);
        input.setStartStockQuantity(new BigDecimal("200"));
        input.setSafeWaterLevel(new BigDecimal("1"));

        List<CoreQuantityInfoInput> decreaseList = Lists.newArrayList();
        CoreQuantityInfoInput decreaseQuantity1 = new CoreQuantityInfoInput();
        decreaseQuantity1.setViewDate(startDate);
        decreaseQuantity1.setQuantity(new BigDecimal("60"));
        CoreQuantityInfoInput decreaseQuantity2 = new CoreQuantityInfoInput();
        decreaseQuantity2.setViewDate(startDate.plusDays(3));
        decreaseQuantity2.setQuantity(new BigDecimal("100"));
        CoreQuantityInfoInput decreaseQuantity3 = new CoreQuantityInfoInput();
        decreaseQuantity3.setViewDate(startDate.plusDays(3));
        decreaseQuantity3.setQuantity(new BigDecimal("60"));

        decreaseList.add(decreaseQuantity1);
        decreaseList.add(decreaseQuantity2);
        decreaseList.add(decreaseQuantity3);

        List<CoreQuantityInfoInput> increaseList = Lists.newArrayList();
        CoreQuantityInfoInput increaseQuantity1 = new CoreQuantityInfoInput();
        increaseQuantity1.setViewDate(startDate.plusDays(2));
        increaseQuantity1.setQuantity(new BigDecimal("20"));
        CoreQuantityInfoInput increaseQuantity2 = new CoreQuantityInfoInput();
        increaseQuantity2.setViewDate(startDate.plusDays(7));
        increaseQuantity2.setQuantity(new BigDecimal("20"));
        increaseList.add(increaseQuantity1);
        increaseList.add(increaseQuantity2);

        input.setDecreaseQuantity(decreaseList);
        input.setIncreaseQuantity(increaseList);
        input.setFirstSellOutBreak(true);

        log.info("input = [{}]", JSON.toJSONString(input));
        CoreResultDTO coreResultDTO = StockDashboardUtil.coreStockDashboardCalculate(input);
        log.info("coreResultDTO = [{}]", JSON.toJSONString(coreResultDTO));
    }
}
