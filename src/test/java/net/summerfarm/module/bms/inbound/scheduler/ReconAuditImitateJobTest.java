package net.summerfarm.module.bms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2023/4/10 17:45
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class ReconAuditImitateJobTest {

    @Resource
    private ReconAuditImitateJob reconAuditImitateJob;

    @Test
    public void processResult() {
        String text = "{\"bizId\":290,\"action\":\"agree\"}";
        JobContext build = JobContext.newBuilder().setInstanceParameters(text).build();

        ProcessResult processResult = null;
        try {
            processResult = reconAuditImitateJob.processResult(build);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        System.out.println(processResult);

    }
}