package net.summerfarm.module.bms.inbound.controller;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.model.input.reconciliation.ReconciliationInsertInput;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2023/4/12 11:55
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class ReconciliationCommandControllerTest {

    @Resource
    private ReconciliationCommandController reconciliationCommandController;

    @Test
    public void reconPaymentUpsert() {
    }

    @Test
    public void createRecon() {
        String text = "{\n" +
                "\t\"reconciliationProofUrl\": \"test/sm26cq0oiud5ox5y.jpg\",\n" +
                "\t\"remake\": \"买买买\",\n" +
                "\t\"settleAccountIds\": [1423, 1420],\n" +
                "\t\"businessType\": \"TRUNK_BUSINESS\",\n" +
                "\t\"bidderId\": 61\n" +
                "}";
        ReconciliationInsertInput input = JSON.parseObject(text,ReconciliationInsertInput.class);
        reconciliationCommandController.createRecon(input);
        System.out.println(1);
    }
}