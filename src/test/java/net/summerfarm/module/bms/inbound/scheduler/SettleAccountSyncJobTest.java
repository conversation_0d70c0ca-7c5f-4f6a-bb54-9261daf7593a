package net.summerfarm.module.bms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/3/29 16:57
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class SettleAccountSyncJobTest {

    @Resource
    private SettleAccountSyncJob settleAccountSyncJob;

    @Test
    void processResult() {
        try {
            String text = "[1020,1272,1273,1274,1275]";
            JobContext context = JobContext.newBuilder().setJobParameters(text).build();
            ProcessResult processResult = settleAccountSyncJob.processResult(context);
            System.out.println(processResult);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }
}