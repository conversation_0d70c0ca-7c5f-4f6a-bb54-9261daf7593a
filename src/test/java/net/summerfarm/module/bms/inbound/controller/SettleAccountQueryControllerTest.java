package net.summerfarm.module.bms.inbound.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.model.input.settleAccount.SettleAccountQueryInput;
import net.summerfarm.module.bms.model.output.settleAccount.SettleAccountBaseOutput;
import net.summerfarm.module.bms.model.output.settleAccount.SettleFulfillOutput;
import net.xianmu.common.result.CommonResult;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.io.IOException;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/3/29 15:56
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class SettleAccountQueryControllerTest {

    @Resource
    private SettleAccountQueryController settleAccountQueryController;

    @Test
    void querySettleAccountPage() {
        SettleAccountQueryInput queryInput = new SettleAccountQueryInput();
        queryInput.setBusinessType(QuotationEnum.BusinessType.DELIVERY_BUSINESS.name());

        CommonResult<PageInfo<SettleAccountBaseOutput>> pageInfoCommonResult = settleAccountQueryController.querySettleAccountPage(queryInput);

        System.out.println(pageInfoCommonResult);
    }

    @Test
    void queryTrunkSettleBase() {
        SettleAccountQueryInput queryInput = new SettleAccountQueryInput();
        queryInput.setBusinessType("TRUNK_BUSINESS");
        queryInput.setId(1447L);

        CommonResult<SettleFulfillOutput> settleFulfillOutputCommonResult = settleAccountQueryController.queryTrunkSettleBase(queryInput);
        System.out.println(settleFulfillOutputCommonResult);
    }

    @Test
    void exportSettleAccount() throws IOException {

        SettleAccountQueryInput queryInput = new SettleAccountQueryInput();
        queryInput.setBusinessType("TRUNK_BUSINESS");
        queryInput.setSettleAccountNo("8380078765249855488");
        queryInput.setDeliveryStartDate(LocalDate.of(2023,8,17));
        queryInput.setDeliveryEndDate(LocalDate.of(2023,8,17));

        settleAccountQueryController.exportSettleAccount(queryInput);
        System.out.println(1);
    }
}