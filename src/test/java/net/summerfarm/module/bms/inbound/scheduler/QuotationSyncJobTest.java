package net.summerfarm.module.bms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/3/20 14:53
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class QuotationSyncJobTest {

    @Resource
    private QuotationSyncJob quotationSyncJob;

    @Test
    void processResult() {
        try {
            String text = "[217]";
            JobContext context = JobContext.newBuilder().setJobParameters(text).build();
            ProcessResult processResult = quotationSyncJob.processResult(context);
            System.out.println(processResult);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}