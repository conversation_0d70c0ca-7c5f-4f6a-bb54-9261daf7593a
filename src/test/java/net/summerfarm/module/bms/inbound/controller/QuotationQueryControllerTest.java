package net.summerfarm.module.bms.inbound.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.model.input.QuotationQueryInput;
import net.summerfarm.module.bms.model.output.QuotationBaseOutput;
import net.summerfarm.module.bms.model.output.QuotationDetailOutput;
import net.xianmu.common.result.CommonResult;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/20 17:03
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class QuotationQueryControllerTest {

    @Resource
    private QuotationQueryController quotationQueryController;

    @Test
    void queryQuotationPage() {
        QuotationQueryInput input = new QuotationQueryInput();
        input.setBusinessType("DELIVERY_BUSINESS");
        input.setCarrierId(61L);
        input.setQuotaType(0);
        input.setServiceAreaId(80L);
        input.setStatus(0);
        input.setStoreNo(1L);

        CommonResult<PageInfo<QuotationBaseOutput>> pageInfoCommonResult = quotationQueryController.queryQuotationPage(input);
        System.out.println(pageInfoCommonResult);
    }

    @Test
    public void queryQuotationDetail(){
        QuotationQueryInput input = new QuotationQueryInput();
        input.setId(276L);

        CommonResult<QuotationDetailOutput> quotationDetailOutputCommonResult = quotationQueryController.queryQuotationDetail(input);
        System.out.println(quotationDetailOutputCommonResult);
    }
}