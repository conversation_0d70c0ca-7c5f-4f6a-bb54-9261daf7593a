package net.summerfarm.module.bms.inbound.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/4/4 11:01
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class CalcItemSyncJobTest {

    @Resource
    private CalcItemSyncJob calcItemSyncJob;

    @Test
    void processResult() throws Exception {

        System.out.println(0);
        calcItemSyncJob.processResult(null);
        System.out.println(1);
    }
}