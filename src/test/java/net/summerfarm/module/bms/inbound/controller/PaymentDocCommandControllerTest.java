package net.summerfarm.module.bms.inbound.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.model.input.payment.PaymentDocCancelInput;
import net.xianmu.common.result.CommonResult;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/24 13:36
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class PaymentDocCommandControllerTest {

    @Resource
    private PaymentDocCommandController paymentDocCommandController;

    @Test
    void cancelPaymentDoc() {
        PaymentDocCancelInput cancelInput = new PaymentDocCancelInput();
        cancelInput.setPaymentDocumentId(279L);
        CommonResult<Void> voidCommonResult = paymentDocCommandController.cancelPaymentDoc(cancelInput);
        System.out.println(voidCommonResult);
    }
}