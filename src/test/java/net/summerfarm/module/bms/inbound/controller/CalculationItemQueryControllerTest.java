package net.summerfarm.module.bms.inbound.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.common.constant.CalcItemConfigEnum;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.model.input.config.CalcItemConfigQueryInput;
import net.summerfarm.module.bms.model.input.config.CalcItemListOutput;
import net.summerfarm.module.bms.model.output.config.CalcItemConfigOutput;
import net.xianmu.common.result.CommonResult;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/3 18:00
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class CalculationItemQueryControllerTest {

    @Resource
    private CalculationItemQueryController calculationItemQueryController;

    @Test
    void queryQuotaItemList() {

        CalcItemConfigQueryInput input = new CalcItemConfigQueryInput();
        input.setSourceType(CalcItemConfigEnum.QuotaCalcItemSourceTypeEnum.QUOTE_ITEM.getCode());
        input.setBusinessType(QuotationEnum.BusinessType.TRUNK_BUSINESS.name());

        CommonResult<List<CalcItemConfigOutput>> listCommonResult = calculationItemQueryController.queryQuotaItemList(input);

        System.out.println(listCommonResult);

    }
}