package net.summerfarm.module.bms.inbound.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.model.input.reconciliation.ReconciliationQueryInput;
import net.summerfarm.module.bms.model.output.reconciliation.ReconciliationBaseOutput;
import net.xianmu.common.result.CommonResult;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/3/24 16:20
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class ReconciliationQueryControllerTest {

    @Resource
    private ReconciliationQueryController reconciliationQueryController;

    @Test
    void queryPage() {
        ReconciliationQueryInput input = new ReconciliationQueryInput();
        input.setBusinessType(QuotationEnum.BusinessType.DELIVERY_BUSINESS.name());
        input.setServiceAreaId(23);
        CommonResult<PageInfo<ReconciliationBaseOutput>> pageInfoCommonResult = reconciliationQueryController.queryPage(input);
        System.out.println(pageInfoCommonResult);
    }
}