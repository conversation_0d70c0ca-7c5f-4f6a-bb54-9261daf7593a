package net.summerfarm.module.bms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.lang.reflect.Constructor;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/3/24 11:47
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class PaymentDocSyncJobTest {

    @Resource
    private PaymentDocSyncJob paymentDocSyncJob;

    @Test
    void processResult() {
        try {
            String text = null;
            JobContext build1 = JobContext.newBuilder().setJobParameters(text).build();
            ProcessResult processResult = paymentDocSyncJob.processResult(build1);
            System.out.println(build1);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}