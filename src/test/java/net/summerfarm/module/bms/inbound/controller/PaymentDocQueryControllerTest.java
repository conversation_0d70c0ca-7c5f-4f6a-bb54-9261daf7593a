package net.summerfarm.module.bms.inbound.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.model.input.payment.PaymentDocQueryInput;
import net.summerfarm.module.bms.model.output.payment.PaymentDocBaseOutput;
import net.summerfarm.module.bms.model.output.payment.PaymentDocDetailOutput;
import net.xianmu.common.result.CommonResult;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/22 15:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class PaymentDocQueryControllerTest {

    @Resource
    private PaymentDocQueryController paymentDocQueryController;

    @Test
    void queryPaymentDocPage() {
        PaymentDocQueryInput input = new PaymentDocQueryInput();
        input.setBusinessType(QuotationEnum.BusinessType.DELIVERY_BUSINESS.name());

        CommonResult<PageInfo<PaymentDocBaseOutput>> pageInfoCommonResult = paymentDocQueryController.queryPaymentDocPage(input);

        System.out.println(JSON.toJSONString(pageInfoCommonResult));
    }

    @Test
    public void queryPaymentDetail() {
        PaymentDocQueryInput input = new PaymentDocQueryInput();
        input.setId(274L);

        CommonResult<PaymentDocDetailOutput> paymentDocDetailOutputCommonResult = paymentDocQueryController.queryPaymentDetail(input);

        System.out.println(paymentDocDetailOutputCommonResult);

    }

    @Test
    public void export(){
        PaymentDocQueryInput input = new PaymentDocQueryInput();
        input.setBusinessType("DELIVERY_BUSINESS");

        paymentDocQueryController.export(input);
        System.out.println(1);
    }
}