package net.summerfarm.module.bms.inbound.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.common.constant.CalcItemConfigEnum;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.model.input.config.CalcItemInsertInput;
import net.xianmu.common.result.CommonResult;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/4/4 11:19
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class CalculationItemCommandControllerTest {

    @Resource
    private CalculationItemCommandController calculationItemCommandController;

    @Test
    void insertCalcItem() {
        CalcItemInsertInput calcItemInsertInput = new CalcItemInsertInput();
        calcItemInsertInput.setQuoteName("啊A");
        calcItemInsertInput.setSourceType(CalcItemConfigEnum.QuotaCalcItemSourceTypeEnum.QUOTE_ITEM.getCode());
        calcItemInsertInput.setUnit("元");
        calcItemInsertInput.setBusinessType(QuotationEnum.BusinessType.DELIVERY_BUSINESS.name());

        CommonResult<Void> voidCommonResult = calculationItemCommandController.insertCalcItem(calcItemInsertInput);
        System.out.println(voidCommonResult);

    }
}