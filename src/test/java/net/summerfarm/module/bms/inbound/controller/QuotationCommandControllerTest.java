package net.summerfarm.module.bms.inbound.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.common.model.QuotationRegionBO;
import net.summerfarm.module.bms.model.input.QuotationCalcFormulaUpsertInput;
import net.summerfarm.module.bms.model.input.QuotationCalcItemUpsertInput;
import net.summerfarm.module.bms.model.input.QuotationUpsertInput;
import net.xianmu.common.result.CommonResult;
import org.junit.Test;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/3/27 16:43
 */
@Slf4j
public class QuotationCommandControllerTest extends BaseControllerTest {

    @Resource
    private QuotationCommandController quotationCommandController;

    @Test
    public void saveQuotation() {
        QuotationUpsertInput input = new QuotationUpsertInput();
        input.setBusinessType(QuotationEnum.BusinessType.DELIVERY_BUSINESS.name());
        input.setCarrierId(61L);
        input.setQuotaForm(0);
        input.setQuotaType(0);
        input.setStoreNo(22L);
        input.setServiceArea("1211");

        QuotationRegionBO regionBO = new QuotationRegionBO();
        regionBO.setArea("嘉荫县");
        regionBO.setProvince("黑龙江");
        regionBO.setCity("伊春市");
        input.setQuotationAreas(Arrays.asList(regionBO));

        QuotationCalcItemUpsertInput itemUpsertInput = new QuotationCalcItemUpsertInput();
        itemUpsertInput.setAmount(new BigDecimal("12"));
        itemUpsertInput.setUnit("点");
        itemUpsertInput.setBmsCalculationItemName("点位废");

        input.setQuotationDetails(Arrays.asList(itemUpsertInput));

        QuotationCalcFormulaUpsertInput formulaUpsertInput = new QuotationCalcFormulaUpsertInput();
        formulaUpsertInput.setFormula("商品数");
        formulaUpsertInput.setCalculateType(0);
        formulaUpsertInput.setCalculateName("转车费");

        input.setCalculateCosts(Arrays.asList(formulaUpsertInput));

        CommonResult<Void> voidCommonResult = quotationCommandController.saveQuotation(input);
        System.out.println(voidCommonResult);
    }

    @Test
    public void updateQuotation() {
    }

    @Test
    public void quotationDelete() {
    }
}