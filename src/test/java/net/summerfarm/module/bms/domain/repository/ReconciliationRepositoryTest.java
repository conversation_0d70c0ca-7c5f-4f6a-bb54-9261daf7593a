package net.summerfarm.module.bms.domain.repository;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.domain.model.reconciliation.ReconciliationDO;
import net.summerfarm.module.bms.domain.repository.param.PageParam;
import net.summerfarm.module.bms.domain.repository.param.ReconciliationQueryParam;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/24 16:11
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class ReconciliationRepositoryTest {

    @Resource
    private ReconciliationRepository reconciliationRepository;

    @Test
    void queryBasePageByParam() {
        ReconciliationQueryParam queryParam = new ReconciliationQueryParam();
        queryParam.setReconciliationNo("01202303072");
        queryParam.setServiceAreaId(3);

        PageInfo<ReconciliationDO> reconciliationDOS = reconciliationRepository.queryPageByParam(queryParam,new PageParam(1,10));

        System.out.println(reconciliationDOS);
    }
}