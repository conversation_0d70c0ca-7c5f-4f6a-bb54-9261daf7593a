package net.summerfarm.module.bms.domain.repository;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mapper.bms.BmsDeliveryQuotationRegionMapper;
import net.summerfarm.module.bms.domain.model.QuotationDO;
import net.summerfarm.module.bms.domain.repository.param.PageParam;
import net.summerfarm.module.bms.domain.repository.param.QuotationQueryParam;
import net.summerfarm.module.bms.domain.repository.quota.QuotationRepository;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/3/20 10:34
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class QuotationRepositoryTest {

    @Resource
    private QuotationRepository quotationRepository;

    @Resource
    private BmsDeliveryQuotationRegionMapper bmsDeliveryQuotationRegionMapper;

    @Test
    void queryQuotationList() {
        QuotationQueryParam queryParam = new QuotationQueryParam();
        PageInfo<QuotationDO> quotationDOS = quotationRepository.queryQuotationPage(queryParam,new PageParam(1,10));

        System.out.println(quotationDOS);


    }

    @Test
    void queryQuotationDetail() {
        QuotationQueryParam param = new QuotationQueryParam();
        param.setId(217L);
        param.setHasQueryFormula(true);
        param.setHasQueryItem(true);
        Optional<QuotationDO> quotationDO = quotationRepository.queryQuotationDetail(param);
        System.out.println(quotationDO);

    }
}