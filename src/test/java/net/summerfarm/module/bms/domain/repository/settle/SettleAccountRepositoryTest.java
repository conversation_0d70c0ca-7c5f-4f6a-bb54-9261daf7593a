package net.summerfarm.module.bms.domain.repository.settle;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.domain.model.settle.SettleAccountDO;
import net.summerfarm.module.bms.domain.repository.param.settle.SettleAccountQueryParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2023/4/6 14:49
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class SettleAccountRepositoryTest {

    @Resource
    private SettleAccountRepository settleAccountRepository;

    @Test
    public void queryPage() {
    }

    @Test
    public void queryList() {
        SettleAccountQueryParam param = new SettleAccountQueryParam();
        param.setSettleAccountIdList(Arrays.asList(1277,1276));
        param.setServiceAreaId(1);
        param.setProvince("浙江");

        List<SettleAccountDO> settleAccountDOS = settleAccountRepository.queryList(param);
        System.out.println(settleAccountDOS);
    }

    @Test
    public void querySettleAccountDetail() {
    }

    @Test
    public void updateByCreateRecon() {
    }

    @Test
    public void save() {
    }

    @Test
    public void checkSettleAccount() {
    }
}