package net.summerfarm.module.bms.domain.repository;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.domain.model.PaymentDocDO;
import net.summerfarm.module.bms.domain.repository.param.PageParam;
import net.summerfarm.module.bms.domain.repository.param.PaymentDocQueryParam;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/3/22 14:26
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class PaymentDocRepositoryTest {

    @Resource
    private PaymentDocRepository paymentDocRepository;

    @Test
    void queryByParam() {
        PaymentDocQueryParam param = new PaymentDocQueryParam();

        PageInfo<PaymentDocDO> paymentDocDOS = paymentDocRepository.queryPageByParam(param,new PageParam(1,10));
        System.out.println(paymentDocDOS);

    }

    @Test
    public void queryDetailById(){
        Long paymentDocId = 274L;

        Optional<PaymentDocDO> paymentDocDO = paymentDocRepository.queryDetailById(paymentDocId);

        System.out.println(paymentDocId);
    }
}