package net.summerfarm.module.bms.infrastructure.mapper;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.domain.repository.param.PaymentDocQueryParam;
import net.summerfarm.module.bms.infrastructure.model.BmsPaymentDocumentEntity;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/3/24 11:22
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class BmsPaymentDocumentEntityMapperTest {

    @Resource
    private BmsPaymentDocumentEntityMapper bmsPaymentDocumentEntityMapper;

    @Test
    void selectByParam() {
        PaymentDocQueryParam queryParam = new PaymentDocQueryParam();
        queryParam.setPaymentNo("***********");
        queryParam.setStoreNo(1);
        List<BmsPaymentDocumentEntity> bmsPaymentDocumentEntities = bmsPaymentDocumentEntityMapper.selectByParam(queryParam);

        System.out.println(bmsPaymentDocumentEntities);
    }

    @Test
    void selectAll4Update() {
    }

    @Test
    void initBusinessType() {
    }

    @Test
    void initPayee() {
    }
}