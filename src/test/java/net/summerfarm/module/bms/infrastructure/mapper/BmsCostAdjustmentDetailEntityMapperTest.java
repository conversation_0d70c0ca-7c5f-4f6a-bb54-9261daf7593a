package net.summerfarm.module.bms.infrastructure.mapper;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2023/4/21 11:33
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class BmsCostAdjustmentDetailEntityMapperTest {

    @Resource
    private BmsCostAdjustmentDetailEntityMapper bmsCostAdjustmentDetailEntityMapper;

    @Test
    public void selectCostAdjustDetail() {
        System.out.println(1);
        bmsCostAdjustmentDetailEntityMapper.selectCostAdjustDetail(1);
        System.out.println(2);
    }

    @Test
    public void insert() {
    }
}