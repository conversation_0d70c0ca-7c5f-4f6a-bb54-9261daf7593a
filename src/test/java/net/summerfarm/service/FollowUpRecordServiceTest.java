package net.summerfarm.service;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseTest;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.mapper.manage.FollowUpRecordMapper;
import net.summerfarm.model.vo.FollowUpRecordVO;
import org.junit.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 17:56
 */
public class FollowUpRecordServiceTest extends BaseTest {
    @Resource
    private FollowUpRecordService followUpRecordService;

    @Resource
    private FollowUpRecordMapper followUpRecordMapper;

    @Test
    public void downRecord(){
        // 取值
        FollowUpRecordVO selectKeys = new FollowUpRecordVO();
        Date now = new Date();
        LocalDate localDate = BaseDateUtils.date2LocalDate(now).minusWeeks(DateUtils.ONE_DATE);
        Date start = BaseDateUtils.localDate2Date(localDate);
        selectKeys.setEndTime(now);
        selectKeys.setStartTime(start);
        // 模拟发送消息
        JSONObject msgJson = new JSONObject();
        msgJson.put("params", selectKeys);
        msgJson.put("adminId", 1007);
        msgJson.put("UUID", "a62cf988-84f7-4990-aeb5-811ac647b517");
        String msg = msgJson.toJSONString();
        // 模拟接收,解析消息
        JSONObject jsonObject = JSONObject.parseObject(msg);
        String params = jsonObject.getString("params");
        String uId = jsonObject.getString("UUID");
        Integer adminId = jsonObject.getInteger("adminId");
        FollowUpRecordVO followUpRecordVO = JSONObject.parseObject(params, FollowUpRecordVO.class);

        followUpRecordService.downRecord(uId,adminId,followUpRecordVO);
    }

    @Test
    public void selectByStartTest(){
        FollowUpRecordVO selectKeys = new FollowUpRecordVO();
        Date now = new Date();
        LocalDate localDate = BaseDateUtils.date2LocalDate(now).minusWeeks(DateUtils.ONE_DATE);
        Date start = BaseDateUtils.localDate2Date(localDate);
        selectKeys.setEndTime(now);
        selectKeys.setStartTime(start);

        List<FollowUpRecordVO> followUpRecordVOS = followUpRecordMapper.selectByStart(selectKeys);
        followUpRecordVOS.forEach(System.out::println);
    }

    @Resource
    private VisitPlanService visitPlanService;
    @Test
    public void sendDingMessage(){
        followUpRecordService.sendDingMessage(437);
    }

    @Test
    public void sendEscortPlanToBd(){
        visitPlanService.sendEscortPlanToBd(1153);
    }

}
