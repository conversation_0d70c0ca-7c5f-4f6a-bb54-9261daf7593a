package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.SplitUtils;
import net.summerfarm.model.domain.MajorCategory;
import net.summerfarm.model.domain.PriceAdjustment;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.Executor;

/**
 * <AUTHOR> ct
 * create at:  2021/2/1  15:57
 */
@RunWith(SpringJUnit4ClassRunner.class)         //表示继承了SpringJUnit4ClassRunner类
@ContextConfiguration
public class MajorCategoryServiceTest extends BaseTest {

    @Resource
    private MajorCategoryService majorCategoryService;
    @Resource
    private Executor asyncServiceExecutor;

    @Test
    public void insertMajorCategory(){
        MajorCategory majorCategory = new MajorCategory();
        majorCategory.setAreaNo(1);
        majorCategory.setCategoryId(1);
        majorCategory.setAdminId(1);
        majorCategory.setDirect(1);
        majorCategory.setStatus(1);
        majorCategory.setType(1);
        majorCategoryService.insertMajorCategory(majorCategory);
    }
    @Test
    public void selectMajorCategory(){
        AjaxResult result = majorCategoryService.selectMajorCategory(1, 10, null);
        result.getData();
    }
    @Test
    public void updateMajorCategory(){
        MajorCategory majorCategory = new MajorCategory();
        majorCategory.setAreaNo(2);
        majorCategory.setCategoryId(1);
        majorCategory.setAdminId(1);
        majorCategory.setDirect(1);
        majorCategory.setStatus(1);
        majorCategory.setType(1);
        majorCategory.setId(2);
        majorCategoryService.updateMajorCategory(majorCategory);
    }
    @Test
    public void sendMsg(){
        majorCategoryService.sendDingTalkMsg();
    }


    @Test
    public void yys(){
        List<PriceAdjustment> poolVOList = new LinkedList<>();

        for (int i = 0;i<10;i++) {
            PriceAdjustment priceAdjustment = new PriceAdjustment();
            priceAdjustment.setId(i);
            poolVOList.add(priceAdjustment);
        }
        List<List<PriceAdjustment>> lists = SplitUtils.avgSplit(poolVOList, 5);
        for (List<PriceAdjustment> list : lists) {
            asyncServiceExecutor.execute(()->{

                System.out.println(list);
                String name = Thread.currentThread().getName();
                System.out.println(name);

                try {
                    Thread.sleep(10000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            });
        }
    }
}
