package net.summerfarm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.vo.MerchantSituationVO;
import net.summerfarm.service.impl.MerchantSituationServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR> ct create at:  2019/8/5  6:37 PM
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MerchantSituationServiceImpTest {

    @Spy
    @Resource(name = "merchantSituationService")
    private MerchantSituationService merchantSituationService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test() {
        Assert.assertNotNull(merchantSituationService);

        MerchantSituationServiceImpl spy = (MerchantSituationServiceImpl) merchantSituationService;

        Mockito.when(spy.getAdminId()).thenReturn(1031);
        Mockito.doReturn(true).when(spy).isSA();
        Assert.assertEquals(true, spy.isSA());
        MerchantSituationVO merchantSituationVO = new MerchantSituationVO();
        AjaxResult result = spy.queryMerchantSituationList(1, 10, merchantSituationVO, "");
        PageInfo pageInfo = (PageInfo) result.getData();
        Assert.assertEquals(AjaxResult.DEFAULT_SUCCESS, result.getCode());
        Assert.assertEquals(77, pageInfo.getTotal());

        Mockito.doReturn(false).when(spy).isSA();
        Mockito.doReturn(false).when(spy).isAreaSA();
        Mockito.doReturn(false).when(spy).isSaleSA();
        result = spy.queryMerchantSituationList(1, 10, merchantSituationVO, "");
        pageInfo = (PageInfo) result.getData();
        Assert.assertEquals(AjaxResult.DEFAULT_SUCCESS, result.getCode());
        Assert.assertEquals(5, pageInfo.getTotal());

        Mockito.doReturn(false).when(spy).isSA();
        Mockito.doReturn(false).when(spy).isAreaSA();
        Mockito.doReturn(true).when(spy).isSaleSA();
        result = spy.queryMerchantSituationList(1, 10, merchantSituationVO, "");
        pageInfo = (PageInfo) result.getData();
        Assert.assertEquals(AjaxResult.DEFAULT_SUCCESS, result.getCode());
        Assert.assertEquals(59, pageInfo.getTotal());

        result = spy.queryMerchantSituationList(1, 10, merchantSituationVO, "测试");
        pageInfo = (PageInfo) result.getData();
        Assert.assertEquals(AjaxResult.DEFAULT_SUCCESS, result.getCode());
        Assert.assertEquals(31, pageInfo.getTotal());

    }
}


