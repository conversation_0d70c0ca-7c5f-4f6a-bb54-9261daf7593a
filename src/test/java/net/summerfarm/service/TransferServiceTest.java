package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.dingding.handler.*;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.model.domain.Config;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration
public class TransferServiceTest extends BaseTest {

    @Resource
    private StockTaskingHandler stockTaskingHandler ;

    @Resource
    private CouponAuditHandle couponAuditHandle ;
    @Resource
    private CustomerFailHandler customerFailHandler ;
    @Resource
    private DmsAccountAuditHandle dmsAccountAuditHandle ;
    @Resource
    private StockDamageAuditHandler stockDamageAuditHandler ;
    @Resource
    private DingdingConfig dingdingConfig;
    @Resource
    private PriceStrategyAuditHandler priceStrategyAuditHandler;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private TransferService transferService;


    @Test
    public void test(){
        transferService.autoCreateTransferDate();
    }

    @Test
    public void tran(){
        //transferService.autoTransferTask(1, "**********", LocalDateTime.now());
    }

    @Test
    public void selectTransferProcessDetail(){
        transferService.selectTransferProcessDetail(4795);
    }

    @Test
    public void dingdingTest(){
        /*Config one = configMapper.selectOne("STOCKTAKING_AUDUT_CODE");
        System.out.println(one.getValue());

        String stocktaking_audut_code = dingdingConfig.getProcessCode("STOCKTAKING_AUDUT_CODE");
        System.out.println(stocktaking_audut_code);

*/
        String processCode = stockTaskingHandler.getProcessCode();
        System.out.println(processCode);
        System.out.println("===========");

        String processCode1 = couponAuditHandle.getProcessCode();
        System.out.println(processCode1);
        System.out.println("===========");

        String processCode2 = customerFailHandler.getProcessCode();
        System.out.println(processCode2);
        System.out.println("===========");

        String processCode3 = dmsAccountAuditHandle.getProcessCode();
        System.out.println(processCode3);
        System.out.println("===========");

        String processCode4 = stockDamageAuditHandler.getProcessCode();
        System.out.println(processCode4);
        System.out.println("===========");

        String processCode5 = priceStrategyAuditHandler.getProcessCode();
        System.out.println(processCode5);
        System.out.println("===========");
    }
    @Test
    public void tranf(){
         // transferService.autoTransferTask(1,"************",LocalDateTime.now(),54);
    }

    @Before
    public void setUp() {
    }

    @After
    public void tearDown(){
    }

    @Test
    public void inSkuAutoTransferTask() {
        transferService.inSkuAutoTransferTask();
    }

    @Test
    public void testAutoTransferTask() throws Exception {
        transferService.autoTransferTask(2, "************", LocalDateTime.now(), 107);
    }
}
