package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.model.domain.InterestRateRecord;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-09-11
 * @description
 */
public class InterestRateRecordServiceTest extends BaseTest {
    @Resource
    private InterestRateRecordService interestRateRecordService;

    @Test
    public void autoAudit(){
        List<InterestRateRecord> list = new ArrayList<>();
        InterestRateRecord record = new InterestRateRecord();
        record.setAreaNo(1001);
        record.setSku("672283651");
        record.setAutoFlagNew(1);
        record.setInterestRateNew(BigDecimal.valueOf(0.22));
        list.add(record);

        InterestRateRecord record2 = new InterestRateRecord();
        record2.setAreaNo(1001);
        record2.setSku("672283825");
        record2.setAutoFlagNew(1);
        record2.setInterestRateNew(BigDecimal.valueOf(0.23));
        list.add(record2);

        interestRateRecordService.insertBatch(list);
    }
}
