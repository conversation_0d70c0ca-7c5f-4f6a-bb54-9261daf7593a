package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.dao.PurchasePredictionDAO;
import net.summerfarm.mapper.manage.FruitPurchasePredictionMapper;
import net.summerfarm.model.domain.FruitPurchasePredictionDO;
import net.summerfarm.model.input.FruitPurchasePredictionReq;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2020-11-03
 */
public class PurchasePredictionTest extends BaseTest {

    @Autowired
    PurchasePredictionDAO purchasePredictionDAO;

    @Autowired
    PurchasePredictionService purchasePredictionService;

    @Autowired
    FruitPurchasePredictionMapper fruitPurchasePredictionMapper;

    @Autowired
    DingTalkService dingTalkService;

    @Test
    public void testListData(){
        FruitPurchasePredictionReq req = new FruitPurchasePredictionReq();
        req.setStoreNo(10);
        //req.setSku("5444555681");
        purchasePredictionService.listFruitData(1, 20, req);
    }

    @Test
    public void testSyncData(){
        purchasePredictionService.syncFruitData();
    }

    @Test
    public void testSelective(){
        FruitPurchasePredictionReq req  = new FruitPurchasePredictionReq();
        req.setStoreNo(10);
        List<FruitPurchasePredictionDO> list =fruitPurchasePredictionMapper.selectiveQuery(req);
        System.out.println(list);
    }

    @Test
    public void testCount(){
        purchasePredictionDAO.countTodayFruitData();
    }


    @Test
    public void testFruitRemind(){
        purchasePredictionService.fruitPurchaseRemind();
    }


}
