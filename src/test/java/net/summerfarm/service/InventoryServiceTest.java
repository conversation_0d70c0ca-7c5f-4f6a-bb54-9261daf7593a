package net.summerfarm.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.mapper.manage.DeliveryPlanMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.model.DTO.inventory.SamePropertyInventoryQueryDTO;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.input.InventoryReq;
import net.summerfarm.model.vo.DeliveryPlanVO;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.StockVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.*;

/**
 * @Package: net.summerfarm.service
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/7/22
 */
public class InventoryServiceTest extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(InventoryServiceTest.class);

    @Resource
    private InventoryService inventoryService;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private MerchantLifecycleService merchantLifecycleService;

    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;

    @Resource
    private DingTalkService dingTalkService;


    //DAO 测试

    @Test
    public void deleteAll(){
        String[] id ={"test20170808","test20170122"};
        System.out.println(inventoryMapper.deleteAll(id));
    }


    @Test
    public void count() {
        Map<String,Object> map = new HashMap<>();
        map.put("sku","201605310012");
        map.put("invId","1");
        int rs = inventoryMapper.count(map);
        logger.info("count result is : {}",rs);
        logger.info("count result is : {}",inventoryMapper.count(new HashMap<>()));
    }

    @Test
    public void selectMapper(){
        System.out.println(inventoryMapper.select(new InventoryReq()).size());
    }

    @Test
    public void update(){
        Inventory inventory=new Inventory();
        inventory.setSku("A002S01R001");
        inventory.setOrigin("hangzhou1");
        inventoryMapper.update(inventory);

        inventory.setSku("test20170808");
        inventory.setOrigin("hello");
        inventoryMapper.update(inventory);
    }

//    @Test
//    public void exportStoreStock(){
//        StockVO stockVO=new StockVO();
//        stockVO.setAreaNo(1);
//
//        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
//        inventoryService.storeStockDownload(stockVO, BaseConstant.XIANMU_TENANT_ID, httpServletResponse);
//        System.out.println(JSON.toJSONString(1));
//    }

//    @Test
//    public void matchSkuOrName(){
//        PageInfo<InventoryVO> inventoryVOPageInfo = inventoryService.matchSkuOrName(
//                1, 10, null, "16893", Arrays.asList(2,3), 197, null);
//        System.out.println(JSON.toJSONString(inventoryVOPageInfo));
//    }


    @Test
    public void selectStoreStock(){
        StockVO stockVO=new StockVO();
        stockVO.setSku("A002S01R001");
        System.out.println(JSON.toJSONString(inventoryMapper.selectStoreStock(stockVO)));
    }

    @Test
    public void selectSkus(){
        System.out.println(JSON.toJSONString(inventoryMapper.selectSkus(null,null,null)));
    }





    @Test
    public void select(){
        InventoryReq selectKeys= new InventoryReq();
        AjaxResult ajaxResult = inventoryService.select(2,10,selectKeys, "priority");
        Page<Inventory> page= (Page<Inventory>) ajaxResult.getData();
        logger.info("查询出结果数量为：{}",page.size());
        for(Inventory inventory:page){
            logger.info(inventory.toString());
        }
    }

    public static void main(String[]args){
        long l = 24 * 60 * 60 * 1000;
        System.out.println(l);
    }


    @Test
    public void daiTest(){
        StockVO selectKeys = new StockVO();
        selectKeys.setAreaNo(1);
        List<StockVO> stockVOS = inventoryMapper.selectStockNew(selectKeys);
        System.out.println("***********" + stockVOS.size());

    }

    @Test
    public void oi(){
//        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectDeliveryPlan("02158080647946887", LocalDate.of(2020, 2, 4), LocalDate.of(2020, 2, 8));
//        System.out.println(deliveryPlanVOS.size());

        //merchantLifecycleService.timingDelivryPlan();
    }

    @Test
    public void syncSkuDaySaleRankTest(){
        merchantLifecycleService.timingDelivryPlan("21:00:00");
    }

    @Test
    public void testCopyOrSave(){
        InventoryReq inventoryReq = JSON.parseObject("{\"7\":\"1mL*1mL\",\"21\":\"1\",\"27\":\"1\",\"afterSaleQuantity\":1,\"afterSaleUnit\":\"盒\",\"auditStatus\":1,\"baseSaleQuantity\":1,\"baseSaleUnit\":1,\"createRemark\":\"{\\\"commentPic\\\":\\\"\\\",\\\"commentInfo\\\":\\\"\\\",\\\"commentStore\\\":\\\"\\\",\\\"commentPrice\\\":\\\"\\\",\\\"createType\\\":0}\",\"creator\":1054,\"extType\":2,\"maturity\":\"\",\"origin\":\"\",\"pdName\":\"晓枫的牛肉\",\"realName\":\"晓枫的牛肉干\",\"saleValueList\":[{\"createTime\":\"2022-05-09 13:53:07\",\"creator\":\"昌泰平\",\"formatStr\":[\"mL\",\"L\",\"G\",\"KG\",\"个\",\"斤\",\"箱\",\"盒\",\"包\",\"袋\",\"瓶\",\"罐\",\"桶\",\"卷\",\"块\",\"片\",\"颗\",\"支\",\"条\",\"只\",\"张\",\"套\",\"组\"],\"formatType\":0,\"name\":\"规格\",\"pdId\":1513,\"productsPropertyId\":7,\"productsPropertyValue\":\"1mL*1mL\",\"type\":1,\"weight\":\"容量*数量\",\"firstWeight\":1,\"firstUnit\":\"mL\",\"secondWeight\":1,\"secondUnit\":\"mL\"},{\"createTime\":\"2022-05-09 13:53:07\",\"creator\":\"昌泰平\",\"formatType\":3,\"name\":\"尺寸\",\"pdId\":1513,\"productsPropertyId\":27,\"productsPropertyValue\":\"1\",\"type\":1,\"formatStr\":[],\"value\":\"1\"},{\"createTime\":\"2022-05-09 13:53:07\",\"creator\":\"昌泰平\",\"formatType\":3,\"name\":\"口味\",\"pdId\":1513,\"productsPropertyId\":21,\"productsPropertyValue\":\"1\",\"type\":1,\"formatStr\":[],\"value\":\"1\"}],\"samplePool\":0,\"type\":0,\"unit\":\"包\",\"volume\":\"0.01*0.01*0.01\",\"weight\":\"0_(测试)\",\"weightNum\":1,\"index\":\"name4\",\"skuPic\":\"\",\"msg\":\"测试\",\"saleValueData\":[{\"createTime\":\"2022-05-09 13:53:07\",\"creator\":\"昌泰平\",\"formatStr\":[\"mL\",\"L\",\"G\",\"KG\",\"个\",\"斤\",\"箱\",\"盒\",\"包\",\"袋\",\"瓶\",\"罐\",\"桶\",\"卷\",\"块\",\"片\",\"颗\",\"支\",\"条\",\"只\",\"张\",\"套\",\"组\"],\"formatType\":0,\"name\":\"规格\",\"pdId\":1513,\"productsPropertyId\":7,\"productsPropertyValue\":\"1mL*1mL\",\"type\":1,\"weight\":\"容量*数量\",\"firstWeight\":1,\"firstUnit\":\"mL\",\"secondWeight\":1,\"secondUnit\":\"mL\"},{\"createTime\":\"2022-05-09 13:53:07\",\"creator\":\"昌泰平\",\"formatType\":3,\"name\":\"尺寸\",\"pdId\":1513,\"productsPropertyId\":27,\"productsPropertyValue\":\"1\",\"type\":1,\"formatStr\":[],\"value\":\"1\"},{\"createTime\":\"2022-05-09 13:53:07\",\"creator\":\"昌泰平\",\"formatType\":3,\"name\":\"口味\",\"pdId\":1513,\"productsPropertyId\":21,\"productsPropertyValue\":\"1\",\"type\":1,\"formatStr\":[],\"value\":\"1\"}],\"commentPic\":\"\",\"commentInfo\":\"\",\"commentStore\":\"\",\"commentPrice\":\"\",\"createType\":0,\"_index\":4,\"_rowKey\":26,\"auditFlag\":\"\",\"pdId\":1513,\"pdNo\":\"897718720\",\"areaSkuVOList\":[],\"bindSku\":\"897718720434\"}",
                InventoryReq.class);
        AjaxResult copy = inventoryService.copy(inventoryReq);
        System.out.println(JSON.toJSONString(copy));
    }

    @Test
    public void testSelectBySpu(){
        InventoryReq inventoryReq = new InventoryReq();
        inventoryReq.setPdId(1513L);
        AjaxResult copy = inventoryService.selectBySpu(inventoryReq);
        System.out.println(JSON.toJSONString(copy));
    }

    @Test
    public void testInitBind(){
        inventoryService.initBindTask();
    }

}
