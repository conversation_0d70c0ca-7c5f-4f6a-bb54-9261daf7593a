package net.summerfarm.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.enums.FinanceBankFlowingWaterEnum;
import net.summerfarm.mapper.manage.FinanceBankFlowingWaterConfigMapper;
import net.summerfarm.mapper.manage.FinanceBankFlowingWaterMapper;
import net.summerfarm.model.DTO.BillOfChinaMerchantsBankDTO;
import net.summerfarm.model.domain.FinanceBankFlowingWater;
import net.summerfarm.model.domain.FinanceBankFlowingWaterConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

public class FinanceBankFlowingWaterServiceTest extends BaseService {

    @Resource
    private FinanceBankFlowingWaterService financeBankFlowingWaterService;

    @Resource
    private FinanceBankFlowingWaterConfigMapper financeBankFlowingWaterConfigMapper;

    @Resource
    private FinanceBankFlowingWaterMapper financeBankFlowingWaterMapper;

    @Test
    public void resultMessage(){
        String bankWater = "{\"response\":{\"body\":{\"ntrbptrsz1\":[{\"cotflg\":\"N\",\"crtamt\":\"0.00\",\"crtnbr\":\"0\",\"dbtamt\":\"0.00\",\"dbtnbr\":\"0\",\"trsseq\":\"0\"}]},\"head\":{\"bizcode\":\"\",\"funcode\":\"DCTRSINF\",\"reqid\":\"**************0061657876566003\",\"resultcode\":\"SUC0000\",\"resultmsg\":\"\",\"rspid\":\"**************5370001cdcserver-67bc887c6c-c9pgq\",\"userid\":\"N002981105\"}},\"signature\":{\"sigdat\":\"__signature_sigdat__\",\"sigtim\":\"**************\"}}";
        String s = "{\"response\":{\"body\":{\"ntqactrsz2\":[{\"amtcdr\":\"D\",\"athflg\":\"N\",\"bbknbr\":\"75\",\"busnam\":\"企业银行支付\",\"etydat\":\"********\",\"etytim\":\"183448\",\"infflg\":\"1\",\"narext\":\"22S2000094366\",\"naryur\":\"测试\",\"refnbr\":\"K2707100019795C\",\"refsub\":\"\",\"reqnbr\":\"**********\",\"rpyacc\":\"***************\",\"rpyadr\":\"广东省深圳市\",\"rpybnk\":\"招商银行\",\"rpynam\":\"银企直连专用测试企业43\",\"rsv30z\":\"**\",\"rsvflg\":\"N\",\"trsamt\":\"-1.00\",\"trsanl\":\"CPGATR\",\"trsblv\":\"127972.16\",\"trscod\":\"CPAA\",\"vltdat\":\"********\",\"yurref\":\"********183222\"}],\"ntrbptrsz1\":[{\"cotflg\":\"N\",\"crtamt\":\"0.00\",\"crtnbr\":\"0\",\"dbtamt\":\"-1.00\",\"dbtnbr\":\"1\",\"trsseq\":\"1\"}]},\"head\":{\"bizcode\":\"\",\"funcode\":\"DCTRSINF\",\"reqid\":\"********1658139627544\",\"resultcode\":\"SUC0000\",\"resultmsg\":\"\",\"rspid\":\"202207181820270850001cdcserver-7767d5576-kfksp\",\"userid\":\"N002981105\"}},\"signature\":{\"sigdat\":\"x7HKcAAaFK0Nw0W4/Zjs7Fx8eQDbylZpdZhNGy0kFGvIMs29mmnGfMNlllkGXZl5C8bYrgwLkd8+/fJT0YTzPA==\",\"sigtim\":\"20220718182027\"}}";

        JSONObject jsonObject = JSON.parseObject(s);
        JSONObject responseData = JSON.parseObject(jsonObject.get("response").toString());
        JSONObject bodyData = JSON.parseObject(responseData.get("body").toString());
        JSONArray items = JSONObject.parseArray(bodyData.get("ntrbptrsz1").toString());
        JSONObject one = JSON.parseObject(items.get(0).toString());
        if (!ObjectUtils.isEmpty(bodyData.get("ntqactrsz2"))){
            JSONArray item = JSONObject.parseArray(bodyData.get("ntqactrsz2").toString());
            List<BillOfChinaMerchantsBankDTO> billOfChinaMerchantsBankDTOS = JSONArray.parseArray(item.toJSONString(), BillOfChinaMerchantsBankDTO.class);
            //插入数据库 查询流水号是否存在 不存在插入
            for (BillOfChinaMerchantsBankDTO billOfChinaMerchantsBankDTO : billOfChinaMerchantsBankDTOS) {
                FinanceBankFlowingWater flowingWater = financeBankFlowingWaterMapper.selectBySerialNumber(billOfChinaMerchantsBankDTO.getRefnbr());
                if (!ObjectUtils.isEmpty(flowingWater)) {
                    continue;
                }
                //插入数据 处理dto
                FinanceBankFlowingWater financeBankFlowingWater = new FinanceBankFlowingWater();
                Date date = BaseDateUtils.string2Date(billOfChinaMerchantsBankDTO.getEtydat(), BaseDateUtils.MID_DATE_FORMAT);
                financeBankFlowingWater.setTradingDay(BaseDateUtils.date2String(date));
                Date time = BaseDateUtils.string2Date(billOfChinaMerchantsBankDTO.getEtydat() + billOfChinaMerchantsBankDTO.getEtytim(), BaseDateUtils.NUMBER_DATE_FORMAT);
                LocalDateTime localDateTime = BaseDateUtils.date2LocalDateTime(time);
                LocalTime localTime = localDateTime.toLocalTime();
                String tradingTime = localTime.toString();
                financeBankFlowingWater.setTradingTime(tradingTime);
                financeBankFlowingWater.setValueDate(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getVltdat()) ? null : billOfChinaMerchantsBankDTO.getVltdat());
                financeBankFlowingWater.setTradingType(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getTrscod()) ? null : billOfChinaMerchantsBankDTO.getTrscod());
                financeBankFlowingWater.setAbstractText(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getNaryur()) ? null : billOfChinaMerchantsBankDTO.getNaryur());
                financeBankFlowingWater.setTransactionAmount(billOfChinaMerchantsBankDTO.getTrsamt());
                financeBankFlowingWater.setMark(billOfChinaMerchantsBankDTO.getAmtcdr());
                financeBankFlowingWater.setSerialNumber(billOfChinaMerchantsBankDTO.getRefnbr());
                financeBankFlowingWater.setProcessInstanceNumber(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getReqnbr()) ? null : billOfChinaMerchantsBankDTO.getReqnbr());
                financeBankFlowingWater.setBusinessName(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getBusnam()) ? null : billOfChinaMerchantsBankDTO.getBusnam());
                financeBankFlowingWater.setPurpose(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getNusage()) ? null : billOfChinaMerchantsBankDTO.getNusage());
                financeBankFlowingWater.setBusinessReferenceNumber(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getYurref()) ? null : billOfChinaMerchantsBankDTO.getYurref());
                financeBankFlowingWater.setBusinessSummary(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getBusnar()) ? null : billOfChinaMerchantsBankDTO.getBusnar());
                financeBankFlowingWater.setOtherSummaries(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getOtrnar()) ? null : billOfChinaMerchantsBankDTO.getOtrnar());
                financeBankFlowingWater.setBankAreaNo(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getRpybbk()) ? null : billOfChinaMerchantsBankDTO.getRpybbk());
                financeBankFlowingWater.setUserName(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getRpynam()) ? null : billOfChinaMerchantsBankDTO.getRpynam());
                financeBankFlowingWater.setAccountNumber(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getRpyacc()) ? null : billOfChinaMerchantsBankDTO.getRpyacc());
                financeBankFlowingWater.setBankNo(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getRpybbn()) ? null : billOfChinaMerchantsBankDTO.getRpybbn());
                financeBankFlowingWater.setBankName(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getRpybnk()) ? null : billOfChinaMerchantsBankDTO.getRpybnk());
                financeBankFlowingWater.setBankAddress(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getRpyadr()) ? null : billOfChinaMerchantsBankDTO.getRpyadr());
                financeBankFlowingWater.setCompanyDivision(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getGsbbbk()) ? null : billOfChinaMerchantsBankDTO.getGsbbbk());
                financeBankFlowingWater.setCompanyAccount(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getGsbacc()) ? null : billOfChinaMerchantsBankDTO.getGsbacc());
                financeBankFlowingWater.setCompanyName(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getGsbnam()) ? null : billOfChinaMerchantsBankDTO.getGsbnam());
                financeBankFlowingWater.setInformationSigns(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getInfflg()) ? null : billOfChinaMerchantsBankDTO.getInfflg());
                financeBankFlowingWater.setInformationExistence(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getAthflg()) ? null : billOfChinaMerchantsBankDTO.getAthflg());
                financeBankFlowingWater.setBillNo(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getChknbr()) ? null : billOfChinaMerchantsBankDTO.getChknbr());
                financeBankFlowingWater.setReversalFlag(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getRsvflg()) ? null : billOfChinaMerchantsBankDTO.getRsvflg());
                financeBankFlowingWater.setExtendedSummary(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getNarext()) ? null : billOfChinaMerchantsBankDTO.getNarext());
                financeBankFlowingWater.setTransactionAnalysisCode(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getTrsanl()) ? null : billOfChinaMerchantsBankDTO.getTrsanl());
                financeBankFlowingWater.setPaymentOrderNo(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getRefsub()) ? null : billOfChinaMerchantsBankDTO.getRefsub());
                financeBankFlowingWater.setEnterpriseIdentificationCode(ObjectUtils.isEmpty(billOfChinaMerchantsBankDTO.getFrmcod()) ? null : billOfChinaMerchantsBankDTO.getFrmcod());
                financeBankFlowingWater.setCreateTime(LocalDateTime.now());
                financeBankFlowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.TO_BE_CLAIMED.ordinal());
                financeBankFlowingWaterMapper.insertSelective(financeBankFlowingWater);
            }
        }else {
            System.out.println("123");
        }
        //JSONArray items = JSONObject.parseArray(bodyData.get("ntrbptrsz1").toString());
        JSONObject headData = JSON.parseObject(responseData.get("head").toString());

        //JSONArray item = JSONObject.parseArray(bodyData.get("ntrbptrsz2").toString());


    }

    @Test
    public void time(){
        FinanceBankFlowingWaterConfig financeBankFlowingWaterConfig = new FinanceBankFlowingWaterConfig();
        financeBankFlowingWaterConfig.setTradingDay("********");
        financeBankFlowingWaterConfig.setLastSerialNumber(5);
        //今日该接口增量查询次数 从0开始
        int num = 0;
        if (!ObjectUtils.isEmpty(financeBankFlowingWaterConfig)) {
            num = num + financeBankFlowingWaterConfig.getLastSerialNumber() + 1;
        }
        System.out.println(num);
    }

}
