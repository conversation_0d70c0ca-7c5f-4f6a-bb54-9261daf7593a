package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.common.base.BaseService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;

import static org.junit.Assert.*;

public class ConversionSkuConfigServiceTest extends BaseTest {

    @Resource
    private ConversionSkuConfigService conversionSkuConfigService;

    @Before
    public void setUp() {
        System.out.println("start");
        System.out.println("----------------------------------------------------------");
    }

    @After
    public void tearDown() {
        System.out.println("end");
    }

    @Test
    public void syncSkuQuantity() {
        conversionSkuConfigService.syncSkuQuantity();
    }
}