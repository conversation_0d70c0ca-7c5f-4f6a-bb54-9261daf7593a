package net.summerfarm.service;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.BaseTest;
import net.summerfarm.model.DTO.GoodsCheckTaskDO;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * @desc
 * <AUTHOR>
 * @Date 2022/11/7 18:07
 **/
@Slf4j
public class StoreGoodsTaskServiceTest extends BaseTest {

    @Resource
    private StoreGoodsTaskService storeGoodsTaskService;

    @Test
    public void testCreateGoodsCheckTask() {
        GoodsCheckTaskDO goodsCheckTaskDO = GoodsCheckTaskDO.builder()
                .sku("1006382632840")
                .warehouseNo(1)
                .operator("cullen")
                .build();
        Long taskNo = storeGoodsTaskService.createGoodsCheckTask(goodsCheckTaskDO);
        log.info("task:{}", taskNo);
    }

    @Test
    public void testCreateGoodsCheckTaskOfDay() {
        storeGoodsTaskService.createGoodsCheckTaskOfDay();
    }

    @Test
    public void testCreateGoodsCheckTaskOfMonth() {
        storeGoodsTaskService.createGoodsCheckTaskOfMonth();
    }

    @Test
    public void testCreateGoodsCheckTaskOfNewGoods() {
        storeGoodsTaskService.createGoodsCheckTaskOfNewGoods();
    }

}
