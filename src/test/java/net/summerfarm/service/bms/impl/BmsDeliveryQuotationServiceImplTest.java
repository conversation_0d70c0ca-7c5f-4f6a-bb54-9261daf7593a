package net.summerfarm.service.bms.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.model.input.bms.BmsDeliveryQuotationQuery;
import net.summerfarm.service.bms.BmsDeliveryQuotationService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.io.IOException;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2023/4/13 10:36
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class BmsDeliveryQuotationServiceImplTest {

    @Resource
    private BmsDeliveryQuotationService bmsDeliveryQuotationService;

    @Test
    public void exportDeliveryQuotationList() throws IOException {

        BmsDeliveryQuotationQuery query = new BmsDeliveryQuotationQuery();
        query.setBusinessType("DELIVERY_BUSINESS");
        bmsDeliveryQuotationService.exportDeliveryQuotationList(query);

        System.out.println(1);
    }
}