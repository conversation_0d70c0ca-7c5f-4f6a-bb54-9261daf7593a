package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.ArrivalNoticeMapper;
import net.summerfarm.model.vo.ArrivalNoticeVO;
import net.summerfarm.model.vo.FenceVO;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR> ct
 * create at:  2021/8/13  19:23
 */
public class FenceServiceTest extends BaseTest {

    @Resource
    FenceService fenceService;
    @Resource
    WarehouseService warehouseService;
    @Resource
    InventoryService inventoryService;
    @Resource
    AreaSkuService areaSkuService;
    @Resource
    ArrivalNoticeMapper arrivalNoticeMapper;
    @Resource
    AreaMapper areaMapper;

    @Test
    public void sfa(){
        ArrivalNoticeVO arrivalNoticeVO = new ArrivalNoticeVO();
        arrivalNoticeVO.setType(1);
        arrivalNoticeMapper.selectNewSubscription(arrivalNoticeVO);
    }
    @Test
    public void shfd(){
        FenceVO fenceVO = new FenceVO();
        fenceVO.setStoreNo( 108);
        fenceVO.setAreaNo(1001);
        //fenceService.insertMapping(fenceVO);
    }
}
