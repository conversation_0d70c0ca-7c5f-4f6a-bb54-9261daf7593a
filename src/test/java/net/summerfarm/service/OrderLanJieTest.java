package net.summerfarm.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.WholeOrderAfterSaleOrder;
import net.summerfarm.enums.SampleApplyStatusEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.Role;
import net.summerfarm.model.domain.SampleApply;
import net.summerfarm.model.input.OrderReq;
import net.summerfarm.model.vo.AfterSaleOrderVO;
import net.summerfarm.model.vo.DeliveryPlanVO;
import net.summerfarm.model.vo.OrderVO;
import net.summerfarm.mq.MQData;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.MDC;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static net.summerfarm.mq.MType.INTERCEPT_ORDER;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderLanJieTest {

    @Resource
    AreaSkuMapper areaSkuMapper;
    @Resource
    private SampleApplyService sampleApplyService;
    @Resource
    RoleMapper roleMapper;
    @Resource
    AdminAuthExtendMapper adminAuthExtendMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderServicel;
    @Resource
    private SampleApplyMapper sampleApplyMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    private DeliveryPathService deliveryPathService;
    @Resource
    private OrderService orderService;

    @Resource
    private WholeOrderAfterSaleOrder wholeOrderAfterSaleOrder;
    @Test
    public void orderDetail(){

        AjaxResult ajaxResult = orderService.selectOrderDetails("02165295265262610");
        OrderVO data = (OrderVO) ajaxResult.getData();
        System.out.println(data.getDeliveryStatus());
        List<DeliveryPlanVO> deliveryPlans = data.getDeliveryPlen();
        for (DeliveryPlanVO deliveryPlanVO : deliveryPlans) {
            System.out.println(deliveryPlanVO);
        }
    }

    @Test
    public void blockChargebacksTest(){
        ArrayList<String> strings = new ArrayList<>();
        strings.add("04165232397403624");
//        orderService.blockChargebacks(strings);
    }

    @Test
    public void Tss(){
        OrderReq orderReq = new OrderReq();
        AjaxResult ajaxResult = orderService.selectOrderList(1, 30, orderReq, null);

        PageInfo<OrderVO> o = (PageInfo<OrderVO>)ajaxResult.getData();
        List<OrderVO> list = o.getList();
        for (OrderVO orderVO : list) {
            System.out.println(orderVO);
        }
    }

    @Test
    public void notifyTest(){
        Map<String, String> md = new HashMap<>(2);
        md.put("title","【处理提醒】您有一个退单退款的的单子待处理");
        String sb ="【处理提醒】您有一个退单退款的的单子待处理\n"+
                        "> ##### 客户名称：XXXX \n" +
                        "> ##### 客户地址：浙江省杭州市西湖区龙章路4号\n" +
                        "> ##### 运营服务区域：杭州\n" +
                        "> ##### 订单编号：13421312312232\n" +
                        "> ##### 下单时间：2022-03-23 12:12:32\n" +
                        "> ##### 实付金额：120元\n" +
                        "> ##### 发起人：小王\n" +
                        "> ##### 如有疑问，请联系仓配进行核对\n" ;
        md.put("text",sb);
        DingTalkRobotUtil.sendMsgAndAtAll(DingTalkRobotUtil.MARKDOWN,"https://oapi.dingtalk.com/robot/send?access_token=eec167d974e261c204ebae4c05c90052cd71024a86be557fddb7ec2975f3d979",() -> md);

    }

    @Test
    public void afterMapperTest(){
        ArrayList<String> strings = new ArrayList<>();
        strings.add("a");
        strings.add("c");
        strings.add("d");
        String s = JSON.toJSONString(strings);
        System.out.println(s);
    }

    @Test
    public void Handle(){
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setAfterSaleOrderNo("20601132050011");
        afterSaleOrderVO.setHandleType(11);
        afterSaleOrderVO.setRecoveryType(1);
        afterSaleOrderVO.setHandleNum(new BigDecimal(10));
        afterSaleOrderService.handle(afterSaleOrderVO);
    }

    @Test
    public void TestTMS(){
        ArrayList<String> strings = new ArrayList<>();
        strings.add("01165303710204225");
        AjaxResult result = deliveryPathService.sendOrderInterception(strings,
                null, true, null);
        System.out.println(result);
    }

    @Test
    public void Audit() throws InterruptedException {
        afterSaleOrderService.audit("62211188050017",1,null);
    }

    @Test
    public void MQTest(){
        MQData mqData = new MQData();
        mqData.setData("213445365436");

        System.out.println((String)mqData.getData());
    }

    @Test
    public void coupTest(){

        /*AjaxResult calcAfterSaleCoupon(String orderNo, String sku, Integer expectQuantity, int suitId, int deliveryed,
        Integer type, Integer deliveryId, Integer handleType, String afterSaleOrderNo);
        handleType: 0
        orderNo: 01165328816400365
        deliveryed: 0
        suitId: 0
        afterSaleOrderNo: 84521114050005
        quantity: 1;*/
        /*afterSaleOrderService.calcAfterSaleCoupon("01165328816400365",null,null,0
        ,0,null,);*/
    }

    @Test
    public void orderDatail(){
        AjaxResult result = orderService.selectOrderDetails("02165338073389814");


    }

    @Test
    public void AdminRoleTest(){
        Role query = new Role();
        query.setRolename("销售主管");
        List<Role> select = roleMapper.select(query);
        Role role = select.get(0);
       /* System.out.println("roleID"+role.getRoleId());
        List<Integer> adminRoleIds = adminRoleMapper.selectByRoleId(role.getRoleId());
        AdminAuthExtend adminAuthExtend = adminAuthExtendMapper.selectByAdminId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(),adminRoleIds.get(0));
        System.out.println(adminAuthExtend.getUserId());*/
    }

    @Test
    public void AjaxResultTest(){
        String result = JSON.toJSONString(AjaxResult.getOK());

        if (AjaxResult.DEFAULT_SUCCESS.equalsIgnoreCase(JSON.parseObject(result, AjaxResult.class).getCode())) {
            System.out.println("cao");
        }
    }

    @Test
    public void MQTests(){
        String name = INTERCEPT_ORDER.name();
        System.out.println(name);
    }

    @Test
    public void afterSlectPage(){
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setOrderNo("01165335911160966");
        AjaxResult result = afterSaleOrderService.selectPage(1, 10, afterSaleOrderVO);
    }

    @Test
    public void saveTest(){
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setAfterSaleRemarkType(9);
        afterSaleOrderVO.setAfterSaleUnit("1");
        afterSaleOrderVO.setAfterSaleType("其他");
        afterSaleOrderVO.setDeliveryed(0);
        afterSaleOrderVO.setHandleNum(new BigDecimal(172.00));
        afterSaleOrderVO.setHandleType(2);
        afterSaleOrderVO.setOrderNo("01165830004964725");
        afterSaleOrderVO.setQuantity(1);
        afterSaleOrderVO.setSku("858360126572");
        afterSaleOrderVO.setSuitId(0);
        afterSaleOrderVO.setRecoveryNum(new BigDecimal(0));
        afterSaleOrderVO.setRecoveryType(0);
        afterSaleOrderVO.setRefundType("缺货");
//        afterSaleOrderVO.setDeliveryId(117718);
        afterSaleOrderService.save(afterSaleOrderVO);
    }
    @Test
    public void sampl(){
        SampleApply sampleApply = new SampleApply();
        sampleApply.setSampleId(780);
        sampleApply.setRemark("系统退单");
        sampleApply.setStatus(SampleApplyStatusEnum.CANCEL.getId());
        sampleApplyMapper.updateSampleApply(sampleApply);
    }

    @Test
    public void afterBu(){


       /* afterSaleOrderService.closeAfterSale(afterSaleOrderNo);
        AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
        afterSaleOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        afterSaleOrder.setAfterSaleRemark("系统退单");
        afterSaleOrderMapper.updateByAfterSaleOrderNo(afterSaleOrder);*/
    }

    @Test
    public void after(){
        afterSaleOrderService.selectPage(1,1000000,null);
    }

    @Test
    public void selectOrderListTest() {
        MDC.put("xm-inbound-flag","orderService.selectOrderListTest");
        OrderReq selectKeys = new OrderReq();
        selectKeys.setTimeDimension(1);
        AjaxResult ajaxResult = orderService.selectOrderList(1, 30, selectKeys, null);
        log.info("ajaxResult:{}", ajaxResult);
        Assert.assertTrue(null != ajaxResult && ajaxResult.getData() != null);
    }

    @Test
    public void MQ1Test(){
        wholeOrderAfterSaleOrder.notifySuccess("01165484420405963");
    }

    @Test
    public void cao(){
        String[] a = {"5483773380"};
        areaSkuMapper.closeShowAndSale(a,"CAO");
    }
}
