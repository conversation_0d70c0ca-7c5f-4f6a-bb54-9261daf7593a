package net.summerfarm.service.FenceTest;

import net.summerfarm.BaseTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.model.domain.AdCodeMsg;
import net.summerfarm.model.vo.FenceVO;
import net.summerfarm.service.FenceService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/8/10  15:07
 */
public class FenceServiceTest extends BaseTest {

    @Resource
    FenceService fenceService;

    /**
    *
    */
    @Test
    public void  addService(){
        GaoDeUtil.addService();
    }

    @Test
    public void Fence(){
        FenceVO fenceVO = new FenceVO();
        fenceVO.setAreaNo(1002);
        fenceVO.setStoreNo(1);
        fenceVO.setFenceName("12341667");
        fenceVO.setDeliveryFrequent("0");
        List<AdCodeMsg> adCodeMsgs = new ArrayList<>();
        AdCodeMsg adCodeMsg = new AdCodeMsg();
        adCodeMsg.setAdCode("12346167");
        adCodeMsg.setArea("余杭dfa62区76");
        adCodeMsg.setProvince("we6r37we7rwe6r");
        adCodeMsg.setArea("杭州re62wf6市");
        adCodeMsg.setLevel("1264357");
        adCodeMsgs.add(adCodeMsg);
        fenceVO.setAdCodes(adCodeMsgs);
        fenceService.insertFence(fenceVO);
    }

    @Test
    public void selectFence(){
        fenceService.initFenceDelivery();

    }

    @Test
    public void select(){
        Integer res = fenceService.getAreaNo("佛山市", "三水区");
    }

    @Test
    public void fenceDetail(){
        fenceService.changeFenceByAreaNo(1001);


    }
}

