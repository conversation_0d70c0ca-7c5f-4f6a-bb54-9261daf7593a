package net.summerfarm.service;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseTest;
import net.summerfarm.model.domain.WarehousePatrolCheck;
import net.summerfarm.model.domain.InventoryWMSInfo;
import net.summerfarm.task.MailUtil;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import org.junit.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * WMS 测试
 * <AUTHOR>
 * @Create 2020-11-13
 */
public class WMSServiceTest extends BaseTest {

    @Resource
    private StockTaskService stockTaskService;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private MailUtil mailUtil;

    @Resource
    WarehouseLogisticsService warehouseLogisticsService;

    @Test
    public void testSkuWmsInfo(){
        String sku = "5430586807";
        InventoryWMSInfo info = inventoryService.queryWMSInfo(sku);
        System.out.println(JSONObject.toJSONString(info));
        warehouseLogisticsService.selectCloseTime(1);
    }

    @Test
    public void testSelectDetail(){
        Integer id = 756;
//        AjaxResult result = stockTaskService.selectStockTaskDetail(id);
//        System.out.println(JSONObject.toJSONString(result));
    }

    @Test
    public void testSendMail(){

//        List<WarehousePatrolCheck> checks = warehousePatrolCheckMapper.selectPatrolCheck(LocalDate.of(2022,1,25), 0);
        System.out.println(1);
    }




}
