package net.summerfarm.service.coupon;

import com.alibaba.fastjson.JSON;
import java.util.List;
import javax.annotation.Resource;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.CouponEnum;
import net.summerfarm.model.domain.Coupon;
import net.summerfarm.service.impl.CouponServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev2")
public class CouponServiceTest {

    private static final Logger log = LoggerFactory.getLogger(CouponServiceTest.class);

    @Resource
    private CouponServiceImpl couponService;

    @Test
    public void testExportCouponDetails() {
        log.info("123/456/abc/7809".replaceAll("//", "_"));
        Coupon selectKeys = new Coupon();
        selectKeys.setStatus(CouponEnum.Status.EFFECTIVE.ordinal());
        AjaxResult<List<Coupon>> couponList = couponService.selectAll(selectKeys);
        Assert.assertTrue(null != couponList && CollectionUtils.isNotEmpty(couponList.getData()));
        AjaxResult exportResult = couponService.statisticsListExcel(couponList.getData().get(0).getId(), -1L);
        log.info("exportResult:{}", JSON.toJSONString(exportResult));

        int id = 33511;
        exportResult = couponService.statisticsListExcel(id, -100L);
        log.info("exportResult:{}", JSON.toJSONString(exportResult));
        Assert.assertTrue(null != exportResult);
    }
}
