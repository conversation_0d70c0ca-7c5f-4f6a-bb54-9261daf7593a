package net.summerfarm.service;

import com.alibaba.fastjson.JSON;
import net.summerfarm.BaseTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.input.SupplierReq;
import org.junit.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

@ActiveProfiles("dev")
public class SupplierServiceTest extends BaseTest {

    @Resource
    SupplierService supplierService;

    @Test
    public void update(){
        String json = "{\"supplierType\":0,\"supplierName\":\"杭州3\",\"taxNumber\":\"****************\",\"address\":\"杭州西湖\",\"connectList\":[{\"adminName\":\"测试哈哈\",\"id\":2465,\"name\":\"阿萨姆\",\"phone\":\"***************\",\"position\":\"12\",\"supplierId\":1224}],\"id\":1224,\"manager\":\"阿斯顿\",\"invoice\":true,\"name\":\"爱奇艺\",\"productArray\":\"水果鲜果22\",\"deliveryFrequent\":\"\",\"settleType\":0,\"settleForm\":0,\"creditDays\":1,\"customStartDate\":\"\",\"categoryArray\":\"4/类目测试\",\"contract\":\"\",\"remark\":\"\",\"accountList\":[{\"account\":\"阿萨姆\",\"accountAscription\":\"\",\"accountBank\":\"\",\"accountName\":\"杭州3\",\"id\":1401,\"payType\":2,\"supplierId\":1224},{\"payType\":2,\"accountBank\":\"\",\"account\":\"233\",\"accountName\":\"杭州3\",\"accountAscription\":\"\",\"showMsg1\":false,\"showMsg2\":false,\"showMsg3\":false,\"supplierId\":1224}]}";
        SupplierReq supplierReq = JSON.parseObject(json, SupplierReq.class);
        supplierService.update(supplierReq);
    }

    @Test
    public void detail(){
        AjaxResult detail = supplierService.detail(1233);
    }

    @Test
    public void insert(){
        String s = "{\"account\":\"\",\"accountBank\":\"\",\"address\":\"审核测试\",\"connectList\":[{\"phone\":\"***********\",\"name\":\"Ethan\",\"position\":\"java\",\"id\":2517,\"adminName\":\"熊旭康\"}],\"id\":\"\",\"manager\":\"Ethan\",\"selectWeek\":[],\"name\":\"审核测试23123\",\"payType\":\"\",\"accountName\":\"\",\"productArray\":\"水果鲜果226\",\"categoryArray\":\"4/类目测试\",\"deliveryFrequent\":\"\",\"settleType\":1,\"customCycle\":1,\"settleForm\":0,\"creditDays\":15,\"customStartDate\":\"2022-04-11\",\"remark\":\"\",\"invoice\":true,\"accountList\":[{\"payType\":2,\"accountBank\":\"\",\"account\":\"123\",\"accountName\":\"审核测试\",\"accountAscription\":\"\",\"showMsg1\":false,\"showMsg2\":false,\"showMsg3\":false}],\"supplierType\":0,\"supplierName\":\"审核测试\",\"taxNumber\":\"*****************\",\"supplierFileList\":[{\"fileType\":1,\"fileUrl\":\"www.baidu.com\",\"startDate\":\"2022-04-16\",\"endDate\":\"2024-04-15\"},{\"fileType\":2,\"fileUrl\":\"www.baidu.com\",\"startDate\":\"2022-04-16\",\"endDate\":\"2024-04-15\"}],\"supplierGradeReq\":{\"moduleType\":1,\"totalScore\":99,\"scoreJson\":[{\"no\":1,\"score\":25,\"remark\":\"kkkkk\"},{\"no\":2,\"score\":25,\"remark\":\"2222\"},{\"no\":3,\"score\":25,\"remark\":\"3333\"},{\"no\":4,\"score\":24,\"remark\":\"444\"}]}";
        SupplierReq supplierReq = JSON.parseObject(s , SupplierReq.class);
        supplierService.insert(supplierReq);
    }


}
