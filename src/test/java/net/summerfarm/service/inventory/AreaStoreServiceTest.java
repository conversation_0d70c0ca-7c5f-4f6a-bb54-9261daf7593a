package net.summerfarm.service.inventory;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.BaseTest;
import net.summerfarm.enums.OtherStockChangeTypeEnum;
import net.summerfarm.enums.SaleStockChangeTypeEnum;
import net.summerfarm.facade.wms.AreaStoreFacade;
import net.summerfarm.facade.wms.dto.AreaStoreLockReq;
import net.summerfarm.facade.wms.dto.AreaStoreQueryReq;
import net.summerfarm.facade.wms.dto.AreaStoreQueryRes;
import net.summerfarm.facade.wms.dto.AreaStoreUnLockReq;
import net.summerfarm.facade.wms.dto.OrderLockSkuDetailReqDTO;
import net.summerfarm.facade.wms.dto.OrderUnLockSkuDetailReqDTO;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.model.domain.AreaStore;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.QuantityChangeRecord;
import net.summerfarm.model.domain.SampleSku;
import net.summerfarm.service.AreaStoreService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.robot.feishu.FeishuBotUtil;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class AreaStoreServiceTest extends BaseTest {

    @Resource
    private AreaStoreService areaStoreService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private AreaStoreFacade areaStoreFacade;

    @Test
    public void testselectAreaStore(){

        AreaStore areaStore = areaStoreService.selectAreaStore(1, "1029406582817");
        Assert.assertNotNull(areaStore);
    }


    @Test
    public void testupdateOnlineStockByStoreNo(){
        Integer storeNo = 125;
        String sku = "1029406582817";
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        Integer onlineQuantity = -1;
        areaStoreService.updateOnlineStockByStoreNo(false, onlineQuantity, sku, storeNo, OtherStockChangeTypeEnum.ONLINE_STOCK_CHANGE, null, recordMap,1);
    }

    @Test
    public void testSendFeiShuMsg() {
        Config config = configMapper.selectOne("closeTimeUpdateNoticeRobot");
        if (Objects.nonNull(config)) {
            StringBuilder append = new StringBuilder();
            append.append("城配仓(编号:");
            append.append(1);
            append.append(")新增/修改截单时间,请检查销售出库任务、省心送冻结任务，及时补偿截单任务");
            String noticeMsg = append.toString();
            CommonResult<Boolean> sendResult = FeishuBotUtil.sendTextMsgAndAtAll(config.getValue(), noticeMsg);
            if (ResultStatusEnum.BAD_REQUEST.getStatus().equals(sendResult.getStatus())
                    || !sendResult.getData()) {
                log.error("城配仓更新截单时间，发送飞书消息通知失败, webhook:{}, noticeMsg:{}, result:{}",
                        config.getValue(), noticeMsg, JsonUtil.toJson(sendResult));
            }
        } else {
            log.warn("城配仓更新截单时间，发送飞书消息通知失败,缺少飞书群机器人配置信息 store:{}", 1);
        }
    }

    @Test
    public void storeLock(){
        AreaStoreLockReq storeLockReq = new AreaStoreLockReq();
        Integer sampleId = 25862;
        Integer contactId = 338944;
        storeLockReq.setOrderNo(String.valueOf(sampleId));
        storeLockReq.setOperatorNo(String.valueOf(sampleId));
        storeLockReq.setIdempotentNo(String.valueOf(sampleId));
        storeLockReq.setOrderType(SaleStockChangeTypeEnum.DEMO_OUT.getTypeName());
        storeLockReq.setContactId(contactId.longValue());
        storeLockReq.setOperatorName("贾昆");
        List<OrderLockSkuDetailReqDTO> orderLockSkuDetailReqDTOS = new ArrayList<>();
        OrderLockSkuDetailReqDTO orderLockSkuDetailReqDTO = new OrderLockSkuDetailReqDTO();
        orderLockSkuDetailReqDTO.setSkuCode("5414188760");
        orderLockSkuDetailReqDTO.setOccupyQuantity(1);
        orderLockSkuDetailReqDTOS.add(orderLockSkuDetailReqDTO);
        storeLockReq.setOrderLockSkuDetailReqDTOS(orderLockSkuDetailReqDTOS);
        areaStoreFacade.storeLock(storeLockReq);
    }

    @Test
    public void storeUnLock() {
        String sampleId = "02170054811746854";
        Integer contactId = 337881;
        AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
        areaStoreUnLockReq.setContactId(Long.valueOf(contactId));
        areaStoreUnLockReq.setOrderType(SaleStockChangeTypeEnum.TIMING_PLAN_DEL.getTypeName());
        areaStoreUnLockReq.setOrderNo(sampleId);
        areaStoreUnLockReq.setIdempotentNo(sampleId + 5654191);
        areaStoreUnLockReq.setOperatorNo(sampleId);
        areaStoreUnLockReq.setOperatorName("贾昆");
        areaStoreUnLockReq.setOrderSubNo("5654191");
        areaStoreUnLockReq.setSource(203);
        areaStoreUnLockReq.setMerchantId(289611L);
        List<OrderUnLockSkuDetailReqDTO> orderReleaseSkuDetailReqDTOS = new ArrayList<>();
        OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
        orderUnLockSkuDetailReqDTO.setSkuCode("5414188760");
        orderUnLockSkuDetailReqDTO.setReleaseQuantity(2);
        orderReleaseSkuDetailReqDTOS.add(orderUnLockSkuDetailReqDTO);
        areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(orderReleaseSkuDetailReqDTOS);
        areaStoreFacade.storeUnLock(areaStoreUnLockReq);
    }

    @Test
    public void storeGetInfo(){
        Integer contactId = 338944;
        AreaStoreQueryReq areaStoreQueryReq = new AreaStoreQueryReq();
        areaStoreQueryReq.setContactId(contactId.longValue());
        areaStoreQueryReq.setSkuCodeList(Collections.singletonList("5414188760"));
        Map<String, AreaStoreQueryRes> queryResList = areaStoreFacade.getInfo(areaStoreQueryReq);
        log.info("storeGetInfo[]queryResList:{}", JSON.toJSONString(queryResList));
    }

}
