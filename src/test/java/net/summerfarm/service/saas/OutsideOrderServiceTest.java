package net.summerfarm.service.saas;

import com.cosfo.summerfarm.model.input.SummerfarmAfterSaleOrderInput;
import com.cosfo.summerfarm.model.input.SummerfarmAfterSaleOrderItemInput;
import com.cosfo.summerfarm.model.input.SummerfarmOrderInput;
import com.cosfo.summerfarm.model.input.SummerfarmOrderItemInput;
import net.summerfarm.BaseTest;
import net.summerfarm.model.domain.QuantityChangeRecord;
import net.summerfarm.service.FenceService;
import net.summerfarm.service.OrderService;
import net.summerfarm.service.QuantityChangeRecordService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/5/20  13:57
 */
public class OutsideOrderServiceTest extends BaseTest {

    @Resource
    OutsideOrderService outsideOrderService;
    @Resource
    OrderService orderService;
    @Resource
    FenceService fenceService;
    @Resource
    QuantityChangeRecordService quantityChangeRecordService;


    @Test
    public void pullOrder(){
        SummerfarmOrderInput input = new SummerfarmOrderInput();
        List<SummerfarmOrderItemInput> itemInputs = new ArrayList<>();
        SummerfarmOrderItemInput itemInput = new SummerfarmOrderItemInput();
        itemInput.setSkuId(299L);
        itemInput.setDeliveryType(0);
        itemInput.setQuantity(1);
        itemInputs.add(itemInput);

        input.setTenantId(2L);
        input.setStoreId(2L);
        input.setName("12435");
        input.setStoreName("dgag");
        input.setArea("海宁市");
        input.setCity("嘉兴市");
        input.setProvince("浙江");
        input.setAddress("皮都锦江大酒店");
        input.setDeliveryType(0);
        input.setPhone("1231124");
        input.setOrderNo("01165312756727624");
        input.setItemInputList(itemInputs);

        outsideOrderService.sendOutsideOrder(input);
    }
    @Test
    public void time(){
//        SummerfarmOrderInput input = new SummerfarmOrderInput();
//        input.setOrderNo("OR165362204813864");
//        input.setTenantId(2L);
//        input.setStoreId(19L);
//        outsideOrderService.queryOutsideOrder(input);
    }

    @Test
    public void contact(){
        HashMap<String, QuantityChangeRecord> map = new HashMap<>();
        QuantityChangeRecord quantityChangeRecord = new QuantityChangeRecord();
        quantityChangeRecord.setSku("************");
        quantityChangeRecord.setAreaNo(1);
        quantityChangeRecord.setQuantity(123);
         map.put("************",quantityChangeRecord);
        quantityChangeRecordService.insertBatchRecord(map);
    }


    @Test
    public void pullAfterOrder(){
        SummerfarmAfterSaleOrderInput input = new SummerfarmAfterSaleOrderInput();
        List<SummerfarmAfterSaleOrderItemInput> itemInputs = new ArrayList<>();
        SummerfarmAfterSaleOrderItemInput itemInput = new SummerfarmAfterSaleOrderItemInput();
        itemInput.setSku("************");
        //itemInput.setSkuId(299L);
        itemInput.setDeliveryType(1);
        itemInput.setQuantity(1);
        itemInputs.add(itemInput);

        input.setTenantId(2L);
        input.setStoreId(2L);
        input.setName("陈修浩");
        input.setStoreName("陈修浩的店铺");
        input.setArea("余杭区");
        input.setCity("杭州市");
        input.setProvince("浙江");
        input.setAddress("金都雅苑");
        input.setDeliveryType(1);
        input.setPhone("17606524067");
        input.setOrderNo("OR166087964166913");
        input.setItemInputList(itemInputs);

        outsideOrderService.afterSaleDelivery(input);
    }

    @Test
    public void getAfterSalePassData(){

       /* List<OutsideAfterSaleVO> inDatas = outsideOrderService.getAfterSalePassData(new Date(), 1,1);

        List<OutsideAfterSaleVO> outDatas = outsideOrderService.getAfterSalePassData(new Date(), 0,1);

        System.out.println(inDatas);
        System.out.println(outDatas);*/
    }
    @Test
    public void querytAfter(){
//        OrderVO orderVO = outsideOrderService.queryAfterOutsideOrder("AS166314875298204");
//        System.out.println(orderVO);
    }
}
