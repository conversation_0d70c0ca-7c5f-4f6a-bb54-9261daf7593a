package net.summerfarm.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.service.HelpOrderService;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

import java.io.*;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
//@RunWith(SpringRunner.class)
@Slf4j
public class HelpOrderServiceImplTest {

    @Resource
    private HelpOrderService helpOrderService;

    @Test
    void heyTeaBatchHelperOrder() throws IOException {
        String strUrl = "C:\\Users\\<USER>\\Downloads\\喜茶批量代下单.xls";
        File file = new File(strUrl);
        InputStream inputStream = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile(file.getName(), inputStream);
        helpOrderService.heyTeaBatchHelperOrder(multipartFile);
    }

    @Test
    public void testBatchImportOrder() throws IOException {
        String strUrl = "/Users/<USER>/Desktop/批量上传代下单-部分失效SKU.xls";
        File file = new File(strUrl);
        InputStream inputStream = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile(file.getName(), inputStream);
        long startedAt = System.currentTimeMillis();
        AjaxResult ajaxResult = helpOrderService.batchHelpOrder(multipartFile);
        log.info("======>>>>>>\najaxResult:{}, timeCost:{}s", ajaxResult, (System.currentTimeMillis() - startedAt) / 1000);
        Assert.assertTrue(null != ajaxResult && ajaxResult.isSuccess());

//        strUrl = "/Users/<USER>/Desktop/批量上传代下单-失效的SKU.xls";
//        file = new File(strUrl);
//        inputStream = new FileInputStream(file);
//        multipartFile = new MockMultipartFile(file.getName(), inputStream);
//        startedAt = System.currentTimeMillis();
//        ajaxResult = helpOrderService.batchHelpOrder(multipartFile);
//        log.info("======>>>>>>\n批量上传代下单-失效的SKU ajaxResult:{}, timeCost:{}s", ajaxResult, (System.currentTimeMillis() - startedAt) / 1000);
//        Assert.assertTrue(null != ajaxResult && ajaxResult.isSuccess());
    }
}