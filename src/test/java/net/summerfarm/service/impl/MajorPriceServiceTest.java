package net.summerfarm.service.impl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class MajorPriceServiceTest {

    @Resource
    MajorPriceServiceImpl majorPriceService;

    @Test
    public void test(){
        majorPriceService.lowPriceWhenPriceChange(1001,  "858748027042");
    }

}
