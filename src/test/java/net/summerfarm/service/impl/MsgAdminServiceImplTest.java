package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSONObject;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class MsgAdminServiceImplTest {

    @Test
    public void testParseArgs() {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("one", "123");
        jsonObject.put("two", "1355");
        List<String> res = MsgAdminServiceImpl.paraseArgs(jsonObject);
        Assert.assertEquals("123", res.get(0));
        Assert.assertEquals("1355", res.get(1));



    }

}