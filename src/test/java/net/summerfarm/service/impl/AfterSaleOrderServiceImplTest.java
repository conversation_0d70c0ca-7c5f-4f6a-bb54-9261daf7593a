package net.summerfarm.service.impl;

import java.net.MalformedURLException;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.model.input.AfterSaleProofExportInput;
import net.summerfarm.service.AfterSaleOrderService;
import net.xianmu.common.result.CommonResult;

@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev2")
@Slf4j
public class AfterSaleOrderServiceImplTest {
    
    @Autowired
    private AfterSaleOrderService afterSaleOrderService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Test
    public void testAfterSaleProofExport() throws InterruptedException {
        // 先把锁给删除掉，否则可能一直被锁住
        String key = "admin_after_sale_proof_download:-1";
        redisTemplate.delete(key);

        AfterSaleProofExportInput input = new AfterSaleProofExportInput();
        input.setDomain("https://azure.summerfarm.net/");
        input.setStartTime(LocalDateTime.parse("2024-09-01T00:00:00"));
        input.setEndTime(LocalDateTime.parse("2024-09-30T00:00:00"));
        input.setAfterSaleOrderStatus(0);
        log.info("input:{}", JSON.toJSONString(input));
        CommonResult<Void> result = afterSaleOrderService.afterSaleProofExport(input);
        log.info("CommonResult:{}", JSON.toJSONString(result));
        Assert.assertTrue(result != null && result.getStatus() == 200);
        // 因为导出任务是离线的，所以这里需要等待至少1分钟
        TimeUnit.MINUTES.sleep(1L);
    }
}
