package net.summerfarm.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.service.PurchaseInvoiceService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/** 
* PurchaseInvoiceServiceImpl Tester. 
* 
* <AUTHOR> name> 
* @since <pre>01/20/2022</pre> 
* @version 1.0 
*/
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class PurchaseInvoiceServiceImplTest {

    @Resource
    private PurchaseInvoiceService purchaseInvoiceService;

    @Test
    public void splitByPdf() {
        String file = "123.pdf";
        String[] split = file.split("\\.");
        System.out.println(split[0]);
        System.out.println(split[1]);

    }

    @Test
    public void pattern() {
        Pattern pattern = Pattern.compile("[0-9]*");
        Matcher isNum = pattern.matcher("123");
        if( !isNum.matches() ){
            System.out.println("no");
        }
    }

    @Test
    public void time() {
        String day = BaseDateUtils.localDateTimeToStringSix(LocalDateTime.now());
        System.out.println(day);
        StringBuilder message = new StringBuilder();
        System.out.println(ObjectUtils.isEmpty(message) ? 0 : 1);
    }

    @Test
    public void calculation() {
        BigDecimal a = new BigDecimal(1.23);
        BigDecimal b = new BigDecimal(-1.23);
        System.out.println(a.subtract(b));
    }


    @Test
    public void checkWallets() {
        System.out.println(1);
        purchaseInvoiceService.checkWallets(1205L,2,0);
        System.out.println(2);
    }
}
