package net.summerfarm.service.impl;

import net.summerfarm.BaseTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.QueryDeliverySourceEnum;
import net.summerfarm.model.vo.DriverLocationVO;
import net.summerfarm.service.TmsTaskService;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR> ct
 * create at:  2022/3/29  10:49
 */
public class TmsTaskServiceImplTest extends BaseTest {
    @Resource
    TmsTaskService tmsTaskService;

    @Test
    public void de(){
        tmsTaskService.deliveryPathDetail(10227);
    }

    @Test
    public void completeInterceptTmsTask(){
        tmsTaskService.completeInterceptTmsTask("2022-08-31");
    }

    @Test
    public void getCurrentDriverLocation(){
       /* AjaxResult<DriverLocationVO> currentDriverLocation = tmsTaskService.getCurrentDriverLocation("01166252634894536", QueryDeliverySourceEnum.MALL.getCode());
        System.out.println(currentDriverLocation);


        AjaxResult<DriverLocationVO> currentDriverLocation1 = tmsTaskService.getCurrentDriverLocation("01166252634894536", QueryDeliverySourceEnum.MANAGE.getCode());
        System.out.println(currentDriverLocation1);*/
    }
}
