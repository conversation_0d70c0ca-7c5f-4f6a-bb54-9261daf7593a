package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.enums.*;
import net.summerfarm.mapper.manage.DeliveryPlanMapper;
import net.summerfarm.mapper.manage.OrdersMapper;
import net.summerfarm.model.vo.DeliveryDetailVO;
import net.summerfarm.model.vo.OrderVO;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.enums.DeliveryOrderStatusEnum;
import net.summerfarm.tms.enums.DistOrderStatusEnum;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: 查询配送详情测试类
 * date: 2022/8/30 14:46
 *
 * <AUTHOR>
 */
@SpringBootTest
public class DistOrderServiceImplTest {

    private DistOrderServiceImpl mockDistOrderService;
    @Resource
    private DistOrderServiceImpl distOrderService;

    @BeforeEach
    public void setUp() throws Exception {
    }

    private DistOrderServiceImpl mock(OrderTypeEnum orderTypeEnum, OrderStatusEnum orderStatusEnum,DeliveryTypeEnum deliveryTypeEnum, InterceptFlagEnum interceptFlagEnum, DeliveryStatusEnum deliveryStatusEnum) {
        OrdersMapper ordersMapper = Mockito.mock(OrdersMapper.class);
        DeliveryPlanMapper deliveryPlanMapper = Mockito.mock(DeliveryPlanMapper.class);
        OrderVO orderVO = new OrderVO();
        orderVO.setStatus((short)orderStatusEnum.getId());
        orderVO.setType(orderTypeEnum.getId());
        Mockito.when(ordersMapper.selectByOrderyNo("orderNo")).thenReturn(orderVO);
        DeliveryDetailVO deliveryDetailVO = getDeliveryDetailVO(deliveryTypeEnum, interceptFlagEnum, deliveryStatusEnum);
        Mockito.when(deliveryPlanMapper.selectDeliveryDetailByOrderNo("orderNo",LocalDate.of(2022,8,30),null)).thenReturn(deliveryDetailVO);
        return new DistOrderServiceImpl(ordersMapper, deliveryPlanMapper);
    }

    private DeliveryDetailVO getDeliveryDetailVO(DeliveryTypeEnum deliveryTypeEnum, InterceptFlagEnum interceptFlagEnum, DeliveryStatusEnum deliveryStatusEnum) {
        if (deliveryTypeEnum == null && deliveryStatusEnum == null && interceptFlagEnum == null){
            return null;
        }
        DeliveryDetailVO deliveryDetailVO = new DeliveryDetailVO();
        deliveryDetailVO.setDeliveryPlanId(1);
        deliveryDetailVO.setDeliveryType(deliveryTypeEnum == null?null:deliveryTypeEnum.ordinal());
        deliveryDetailVO.setPathStatus(deliveryStatusEnum==null?null:deliveryStatusEnum.getStatus());
        deliveryDetailVO.setInterceptFlag(interceptFlagEnum==null?null:interceptFlagEnum.getCode());
        deliveryDetailVO.setFinishTime(LocalDateTime.now());
        deliveryDetailVO.setSignForStatus(0);
        deliveryDetailVO.setDeliveryPic("xyz.jpg");
        deliveryDetailVO.setStoreNo(1);
        deliveryDetailVO.setStoreName("杭州城配仓");
        deliveryDetailVO.setDriver("张三");
        deliveryDetailVO.setDriverPhone("123");
        return deliveryDetailVO;
    }

    @Test
    void queryNormalOrderDetailTest() {
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        distOrderQuery.setOuterOrderId("01166150727151203");
        TmsResult<DistOrderDTO> tmsResult = distOrderService.queryDetail(distOrderQuery);
        if (tmsResult.isSuccess()){
            System.out.println(JSON.toJSONString(tmsResult.getData()));
        }

    }

    @Test
    void queryTimingOrderDetailTest() {
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        distOrderQuery.setOuterOrderId("02166176303405268");
        distOrderQuery.setDeliveryTime(LocalDate.of(2022,8,30));
//        distOrderQuery.setDeliveryTime(LocalDate.of(2022,8,31));
        TmsResult<DistOrderDTO> tmsResult = distOrderService.queryDetail(distOrderQuery);
        if (tmsResult.isSuccess()){
            System.out.println(JSON.toJSONString(tmsResult.getData()));
        }

    }

    @Test
    void queryDetailMockTest() {
        mockDistOrderService = mock(OrderTypeEnum.NORMAL,OrderStatusEnum.DELIVERING,DeliveryTypeEnum.DELIVERY,InterceptFlagEnum.normal,DeliveryStatusEnum.COMPLETE_DELIVERY);
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        distOrderQuery.setOuterOrderId("orderNo");
        distOrderQuery.setDeliveryTime(LocalDate.of(2022,8,30));
        TmsResult<DistOrderDTO> tmsResult = mockDistOrderService.queryDetail(distOrderQuery);
        Assertions.assertTrue(tmsResult.isSuccess());
        Assertions.assertEquals(DistOrderStatusEnum.COMPLETE_DELIVERY.getCode(),tmsResult.getData().getStatus());
        Assertions.assertEquals(1,tmsResult.getData().getDistId());
        Assertions.assertEquals("1",tmsResult.getData().getOutBusinessNo());
        Assertions.assertEquals("杭州城配仓",tmsResult.getData().getBeginSiteName());
        Assertions.assertEquals("张三",tmsResult.getData().getDeliveryBatchList().get(0).getDriver());
        Assertions.assertEquals("123",tmsResult.getData().getDeliveryBatchList().get(0).getDriverPhone());
        Assertions.assertEquals(DeliveryOrderStatusEnum.SIGN_SUC.getCode(),tmsResult.getData().getDeliveryOrderList().get(0).getStatus());
        Assertions.assertEquals("xyz.jpg",tmsResult.getData().getDeliveryOrderList().get(0).getSignInPic());
    }

    @Test
    void queryDetailByErrorOrderStatusMockTest() {
        mockDistOrderService = mock(OrderTypeEnum.NORMAL,OrderStatusEnum.NO_PAYMENT,null,null,null);
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        distOrderQuery.setOuterOrderId("orderNo");
        distOrderQuery.setDeliveryTime(LocalDate.of(2022,8,30));
        Assertions.assertThrows(DefaultServiceException.class,()->mockDistOrderService.queryDetail(distOrderQuery),"该订单无有效配送计划");
    }

    @Test
    void queryDetailByDirectOrderTypeMockTest() {
        mockDistOrderService = mock(OrderTypeEnum.DIRECT,OrderStatusEnum.DELIVERING,null,null,null);
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        distOrderQuery.setOuterOrderId("orderNo");
        distOrderQuery.setDeliveryTime(LocalDate.of(2022,8,30));
        Assertions.assertThrows(DefaultServiceException.class,()->mockDistOrderService.queryDetail(distOrderQuery),"该订单无有效配送计划");
    }

    @Test
    void queryDetailBySelfOrderMockTest() {
        mockDistOrderService = mock(OrderTypeEnum.NORMAL,OrderStatusEnum.DELIVERING,DeliveryTypeEnum.SELF,InterceptFlagEnum.normal,null);
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        distOrderQuery.setOuterOrderId("orderNo");
        distOrderQuery.setDeliveryTime(LocalDate.of(2022,8,30));
        Assertions.assertThrows(DefaultServiceException.class,()->mockDistOrderService.queryDetail(distOrderQuery),"该订单不支持查看配送详情");
    }

    @Test
    void queryDetailByInterceptOrderMockTest() {
        mockDistOrderService = mock(OrderTypeEnum.NORMAL,OrderStatusEnum.DELIVERING,DeliveryTypeEnum.DELIVERY,InterceptFlagEnum.intercept,null);
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        distOrderQuery.setOuterOrderId("orderNo");
        distOrderQuery.setDeliveryTime(LocalDate.of(2022,8,30));
        Assertions.assertThrows(DefaultServiceException.class,()->mockDistOrderService.queryDetail(distOrderQuery),"该订单不支持查看配送详情");
    }

    @Test
    void queryDetailByRefundsOrderTypeForNonArrivalsMockTest() {
        mockDistOrderService = mock(OrderTypeEnum.NORMAL,OrderStatusEnum.DRAWBACK,DeliveryTypeEnum.DELIVERY,InterceptFlagEnum.normal,null);
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        distOrderQuery.setOuterOrderId("orderNo");
        distOrderQuery.setDeliveryTime(LocalDate.of(2022,8,30));
        Assertions.assertThrows(DefaultServiceException.class,()->mockDistOrderService.queryDetail(distOrderQuery),"该订单不支持查看配送详情");
    }
}
