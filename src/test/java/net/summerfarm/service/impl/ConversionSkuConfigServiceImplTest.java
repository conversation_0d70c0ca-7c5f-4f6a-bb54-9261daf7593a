package net.summerfarm.service.impl;

import net.summerfarm.BaseTest;
import net.summerfarm.service.ConversionSkuConfigService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;

import static org.junit.Assert.*;

public class ConversionSkuConfigServiceImplTest extends BaseTest {

    @Resource
    private ConversionSkuConfigService conversionSkuConfigService;

    @Before
    public void setUp() {
    }

    @After
    public void tearDown(){
    }

    @Test
    public void syncSkuQuantity() {
        conversionSkuConfigService.syncSkuQuantity();
    }
}