package net.summerfarm.service.impl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@SpringBootTest
@RunWith(SpringRunner.class)
public class CheckBillServiceImplTest {
    @Resource
    private CheckBillServiceImpl service;

    @Test
    public void check() {
        String res = service.checkBill();
        System.out.println(res);
    }
}