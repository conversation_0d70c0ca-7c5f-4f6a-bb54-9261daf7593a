package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mapper.manage.AfterSaleOrderMapper;
import net.summerfarm.model.domain.VisitPlan;
import net.summerfarm.model.input.VisitPlanInput;
import net.summerfarm.model.vo.AreaSkuVO;
import net.summerfarm.model.vo.AreaStoreVO;
import net.summerfarm.model.vo.VisitPlanVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2019/8/2  11:46 AM
 */

@RunWith(SpringJUnit4ClassRunner.class)         //表示继承了SpringJUnit4ClassRunner类
@ContextConfiguration
public class VisitPlanServiceTest extends BaseTest {

    @Resource
    private VisitPlanService visitPlanService;

    @Resource
    private BDExtService bdExtService;

    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;

    @Resource
    private OrderService orderService;

    @Resource
    private AreaSkuService areaSkuService;


    @Resource
    private ProductStockService productStockService;

    @Test
    public  void insert(){
        VisitPlan visitPlan = new VisitPlan();
        visitPlan.setAdminId(1);
        visitPlan.setContactId(23);
        visitPlan.setExpectedContent("hahahaha");
        visitPlan.setMId(280L);
        visitPlan.setExpectedTime(LocalDateTime.now());
        visitPlan.setStatus(0);
        visitPlanService.insertVisitPlan(visitPlan);
    }

    @Test
    public void queryNumber(){
        visitPlanService.queryDayAndMonth();
    }


    @Test
    public void dfdf(){
        visitPlanService.queryDayAndMonth();
    }

    @Test
    public void deliveryPathDownload(){
        LocalDate localDate = LocalDate.of(2020, 6, 19);
        AjaxResult result = orderService.deliveryPathDownload(1, localDate, null);
        Object data = result.getData();
    }

    @Test
    public void df(){
        AreaSkuVO areaSkuVO = new AreaSkuVO();
        areaSkuVO.setParentNo(1);
        areaSkuVO.setSku("2274182870");
        areaSkuService.selectSkuInfo(1,2,areaSkuVO);
    }
    @Test
    public void df1(){
        AreaStoreVO areaStoreVO = new AreaStoreVO();
        areaStoreVO.setId(26434);
        areaStoreVO.setOnlineQuantity(1999);
        productStockService.selectBySku("5401607086",1);
    }
}
