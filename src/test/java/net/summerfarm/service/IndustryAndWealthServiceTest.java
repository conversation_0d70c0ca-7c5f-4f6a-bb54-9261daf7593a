package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.mapper.offline.FinanceCashEttlementDocumentDetailsMapper;
import net.summerfarm.model.domain.FileDownloadRecord;
import net.summerfarm.model.domain.FinanceCashEttlementDocumentDetails;
import net.summerfarm.model.vo.MajorPriceAutoUpdateVO;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @title: IndustryAndWealthServiceTest
 * @date 2022/6/918:29
 */
public class IndustryAndWealthServiceTest extends BaseTest {
    @Resource
    private FinanceCashEttlementDocumentDetailsMapper financeCashEttlementDocumentDetailsMapper;
    @Resource
    private IndustryAndWealthService industryAndWealthService;
    @Test
    public void addSql(){

        FinanceCashEttlementDocumentDetails financeCashEttlementDocumentDetails = new FinanceCashEttlementDocumentDetails();
        financeCashEttlementDocumentDetails.setWhereaboutsOfFunds(3);
        financeCashEttlementDocumentDetails.setOrderNo("021653558217232564");
        financeCashEttlementDocumentDetails.setCity("浙江温州市");
        financeCashEttlementDocumentDetails.setPayType("小程序支付");
        financeCashEttlementDocumentDetails.setMId(1211L);
        financeCashEttlementDocumentDetails.setMname("鲜沐科技");
        financeCashEttlementDocumentDetails.setEndTime(BaseDateUtils.stringToLocalDateTime("2022-05-12 22:38:03"));
        financeCashEttlementDocumentDetails.setBankPayEndTime(BaseDateUtils.stringToLocalDateTime("2022-05-12 22:38:03"));
        financeCashEttlementDocumentDetails.setDeliveryTime(DateUtils.stringToLocalDate("********"));
        financeCashEttlementDocumentDetails.setFinishTime(BaseDateUtils.stringToLocalDateTime("2022-05-12 22:38:03"));
        financeCashEttlementDocumentDetails.setOutTimesFee(BigDecimal.ZERO);
        financeCashEttlementDocumentDetails.setDeliveryFee(BigDecimal.ZERO);
        financeCashEttlementDocumentDetails.setOrderType(0);
        financeCashEttlementDocumentDetails.setOrderSaleType(0);
        financeCashEttlementDocumentDetails.setSku("************");
        financeCashEttlementDocumentDetails.setType(0);
        financeCashEttlementDocumentDetails.setAmount(1);
        financeCashEttlementDocumentDetails.setTaxRateValue(BigDecimal.valueOf(0.13));
        financeCashEttlementDocumentDetails.setTaxAmount(BigDecimal.valueOf(6.26));
        financeCashEttlementDocumentDetails.setPriceAmount(BigDecimal.valueOf(66));
        financeCashEttlementDocumentDetails.setDetailedType(0);
        financeCashEttlementDocumentDetails.setRevenueRecognition(DateUtils.stringToLocalDate("20220519"));

        // 创建一个线程池
        ExecutorService exec = Executors.newFixedThreadPool(10);
        // 定义一个任务集合
        List<Callable<Boolean>> tasks = new ArrayList<Callable<Boolean>>();
        // 确定每条线程的数据
        for (int j = 0; j < 20; j++) {
            tasks.add(new Callable<Boolean>() {
                @Override
                public Boolean call() throws Exception {
                    for (int i = 0; i < 2000; i++) {
                        financeCashEttlementDocumentDetailsMapper.insertSelective(financeCashEttlementDocumentDetails);
                    }
                    return true;
                }
            });
        }
        try {
            exec.invokeAll(tasks);
            exec.shutdown();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void downloadCurrentIIncome(){
        industryAndWealthService.downloadCurrentIIncome(5514L,"收入-现结订单单据2022-05-15-2022-05-16压缩文件.zip","2022-05-15-2022-05-16现结收入明细表压缩文件1655049991981.zip");
    }

    @Test
    public void submitDate(){
        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
        fileDownloadRecord.setStartTime(DateUtils.stringToLocalDate("20220513"));
        fileDownloadRecord.setEndTime(DateUtils.stringToLocalDate("20220519"));
        fileDownloadRecord.setType(8);
        industryAndWealthService.submitDate(fileDownloadRecord);
    }
}
