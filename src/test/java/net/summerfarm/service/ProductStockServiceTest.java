package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.AreaStore;
import net.summerfarm.model.vo.AreaStoreVO;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR> ct
 * create at:  2020/9/1  14:40
 */
public class ProductStockServiceTest extends BaseTest {

    @Resource
    private ProductStockService productStockService;
    @Resource
    private AreaStoreService areaStoreService;


    @Test
    public void selectAll(){
        AjaxResult result = productStockService.selectBySku("2274182870", 1);
    }

    @Test
    public void trustReserveQuantity(){
        AreaStore areaStore = new AreaStore();
        areaStore.setSku("2274182870");
        areaStore.setAreaNo(10);
        areaStore.setSupportReserved(0);
        productStockService.trustReserveQuantity(areaStore);
    }
    @Test
    public void selectBySku(){
        productStockService.updateReserveQuantity(1,"5470608806",200,0);
    }
    @Test
    public void  updateReserveQuantity(){
        productStockService.selectBySku("5470608806",1);
    }

    @Test
    public void updateTwo(){
        AreaStoreVO areaStoreVO = new AreaStoreVO();
        areaStoreVO.setSupportReserved(1);
        areaStoreVO.setId(4780);
        areaStoreVO.setSync(0);
        productStockService.updateTwo(areaStoreVO);
    }

    @Test
    public void auto(){
//        productStockService.batchTrustChange(1, 89);
    }
}
