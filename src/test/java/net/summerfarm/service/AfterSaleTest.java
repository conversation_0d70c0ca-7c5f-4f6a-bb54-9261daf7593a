package net.summerfarm.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.summerfarm.model.SummerfarmResult;
import com.cosfo.summerfarm.model.input.SummerfarmStockInput;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.BaseTest;
import net.summerfarm.facade.StockTaskStorageFacade;
import net.summerfarm.mapper.manage.MajorPriceMapper;
import net.summerfarm.mapper.manage.PurchasesPlanMapper;
import net.summerfarm.model.domain.PurchasesPlan;
import net.summerfarm.service.inner.InnerService;
import org.junit.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Repeat;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Create 2020-09-28
 */
@Slf4j
public class AfterSaleTest extends BaseTest {

    @Autowired
    MajorPriceMapper majorPriceMapper;

    @Resource
    StockTaskStorageFacade stockTaskStorageFacade;

    @Resource
    private PurchasesPlanMapper purchasesPlanMapper;

    @Test
    public void test10(){
        List<PurchasesPlan> purchasesPlans = purchasesPlanMapper.selectWithPurchasesNos(Lists.newArrayList("2022102812344024"));
        log.info("介绍过:{}", JSON.toJSONString(purchasesPlans));
    }

    @Test
    public void test1(){
        HashMap map = new HashMap();
        map.put("adminId", 305L);
        map.put("startTime", "2020-09-01 00:00:00");
        map.put("endTime", "2020-09-30 00:00:00");
        map.put("mId", "84");

        List<String> li = majorPriceMapper.selectBillOrderNo(map);
        System.out.println(li);
    }

    @Autowired
    ProductStockService productStockService;
    @Resource
    AreaStoreService areaStoreService;

    @Test
    public void test2(){
        SummerfarmStockInput summerfarmStockInput = new SummerfarmStockInput();
        summerfarmStockInput.setArea("怀柔区");
        summerfarmStockInput.setCity("北京市");
        List<Long> objects = new ArrayList<>();
        objects.add(301L);
        objects.add(302L);
        objects.add(303L);
        objects.add(304L);
        summerfarmStockInput.setSkuIdList(objects);
        //SummerfarmResult summerfarmResult = areaStoreService.queryAreaStoreQuantityList(summerfarmStockInput);
        //System.out.println(summerfarmResult);
    }


    @Autowired
    RedissonClient redissonClient;

    @Test
    public void test3(){
        String key = "tbccc";
        RLock lock = redissonClient.getLock(key);

        try {
            System.out.println("**********************");
            boolean b = lock.tryLock(1000, 3000*10000, TimeUnit.MILLISECONDS);
            System.out.println(b);
            //Thread.sleep(1000*60);
            System.out.println("**********************");
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            //lock.unlock();
        }
    }

    @Test
    public void test4(){
        RLock lock1 = redissonClient.getLock("tbaaa");
        RLock lock2 = redissonClient.getLock("tbccc");
        RLock lock3 = redissonClient.getLock("tbddd");
        RLock mlock = redissonClient.getMultiLock(lock1, lock2, lock3);
        try {
            boolean b =  mlock.tryLock(1000, 3000*10000, TimeUnit.MILLISECONDS);
            System.out.println("**********************");
            System.out.println(b);
            Thread.sleep(1000*60);
            System.out.println("**********************");
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            mlock.unlock();
        }
    }

    @Autowired
    InnerService innerService;

    @Test
    public void testTime()  {
        //Integer stockTaskId = stockTaskStorageFacade.getStockTaskId(328l);
    }

}
