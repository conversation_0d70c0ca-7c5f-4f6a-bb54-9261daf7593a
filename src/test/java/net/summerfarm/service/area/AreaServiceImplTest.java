package net.summerfarm.service.area;

import com.alibaba.fastjson.JSON;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.vo.AreaVO;
import net.summerfarm.service.AreaService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@Slf4j
@SpringBootTest
@ActiveProfiles("qa")
@RunWith(SpringJUnit4ClassRunner.class)
public class AreaServiceImplTest {

    @Resource
    private AreaService areaService;

    @Test
    public void testSaveNew() {
        AjaxResult ajaxResult = areaService.save(new AreaVO());
        log.info("测试结果:{}", JSON.toJSONString(ajaxResult));
        Assert.assertTrue(ajaxResult != null && ajaxResult.isSuccess());
    }

    @Test
    public void testUpdateArea() {
        AreaVO areaVO = new AreaVO();
        areaVO.setAreaNo(1001);
        AjaxResult ajaxResult = areaService.update(areaVO);
        log.info("更新area测试结果:{}", JSON.toJSONString(ajaxResult));
        Assert.assertTrue(ajaxResult != null && ajaxResult.isSuccess());
    }
}
