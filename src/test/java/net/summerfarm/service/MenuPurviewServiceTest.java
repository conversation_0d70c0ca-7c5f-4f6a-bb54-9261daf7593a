package net.summerfarm.service;




import net.summerfarm.common.AjaxResult;
import net.summerfarm.mapper.manage.AdminMapper;
import net.summerfarm.mapper.manage.MenuPurviewMapper;
import net.summerfarm.mapper.manage.PurviewMapper;
import net.summerfarm.model.DTO.MenuPurviewDTO;
import net.summerfarm.model.domain.MenuPurview;
import net.summerfarm.model.domain.Purview;
import net.summerfarm.model.vo.MenuPurviewVO;
import net.summerfarm.model.vo.PurviewVO;
import net.summerfarm.model.vo.RoleVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration
public class MenuPurviewServiceTest {
    private static final Logger logger = LoggerFactory.getLogger(MenuPurviewServiceTest.class);

    @Resource
    MenuPurviewService menuPurviewService;

    @Resource
    PurviewService purviewService;

    @Resource
    MenuPurviewMapper menuPurviewMapper;

    @Resource
    PurviewMapper purviewMapper;
    @Resource
    AdminService adminService;

    @Resource
    AdminMapper adminMapper;

    @Test
    public void test() {
//        List<MenuPurview> menuPurviews = menuPurviewService.queryAll();
//        if (CollectionUtils.isEmpty(menuPurviews)){
//            System.out.println("------------------");
//        }
//
//        for (MenuPurview menuPurview : menuPurviews) {
//            System.out.println(menuPurview.toString());
//        }

    }

    @Test
    public void insert() {
        MenuPurview menuPurview = new MenuPurview();
        menuPurview.setName("类目管理");
        menuPurview.setParentId(1);
        menuPurview.setStatus(0);
        menuPurview.setType("B");
        menuPurview.setModule("后台");
        Integer in = menuPurviewService.insertMenuPurview(menuPurview);
        if (in > 0) {
            System.out.println("插入成功");
        }

    }

    @Test
    public void update() {
        MenuPurview menuPurview = new MenuPurview();
        menuPurview.setName("类目管理");
        menuPurview.setParentId(1);
        menuPurview.setStatus(1);
        menuPurview.setType("B");
        menuPurview.setModule("后台");
        menuPurview.setId(2);
        Integer in = menuPurviewService.updateMenuPurview(menuPurview);
        if (in > 0) {
            System.out.println("更改成功");
        }
    }

    @Test
    public void queryList() {

        List<Integer> integers = new ArrayList<>();
        integers.add(22);
        List<MenuPurview> menuPurviews = menuPurviewMapper.queryMenuPurviewList(integers);
        if (!CollectionUtils.isEmpty(menuPurviews)) {
            System.out.println("查询成功" + menuPurviews.toString());
        }
    }

    @Test
    public void queryPurview() {
        List<MenuPurview> menuPurviews = menuPurviewMapper.queryAllMenuPurview();
        logger.info("-----" + menuPurviews.size());
        System.out.println("查询成功" + menuPurviews.toString());

    }

    @Test
    public void su() {
        List<PurviewVO> purviews = purviewMapper.select(null);
        List<MenuPurviewDTO> menuPurviewDTOS = new ArrayList<>();
        List<MenuPurviewVO> menuPurviewVOS1 = new ArrayList<>();

        //菜单和接口权限关联
        List<MenuPurviewVO> menuPurviewVOS = menuPurviewMapper.queryAll();

        for (MenuPurviewVO men : menuPurviewVOS) {

            if (Objects.equals(men.getType(), "A")) {
                MenuPurviewDTO menuPurviewDTO = compareDto(men);
                menuPurviewDTOS.add(menuPurviewDTO);
                continue;
            }


            for (PurviewVO pu : purviews) {
                if (Objects.equals(men.getId(), pu.getMenuId())) {
                    men.getPurviews().add(pu);
                }
            }
        }
        for (MenuPurviewVO menuPurviewVO : menuPurviewVOS) {
            if (Objects.equals(menuPurviewVO.getType(), "B")) {
                menuPurviewVOS1.add(menuPurviewVO);
            }
        }

        for (MenuPurviewDTO men : menuPurviewDTOS) {

            for (MenuPurviewVO menu : menuPurviewVOS1) {

                if (Objects.equals(men.getId(), menu.getParentId())) {
                    men.getPurviews().add(menu);
                }

            }
        }
        for (MenuPurviewDTO men : menuPurviewDTOS) {
            System.out.println(men.getName() + "---" + men.toString());

        }

    }

    private MenuPurviewDTO compareDto(MenuPurviewVO menuPurviewVO) {

        MenuPurviewDTO menuPurviewDTO = new MenuPurviewDTO();
        menuPurviewDTO.setId(menuPurviewVO.getId());
        menuPurviewDTO.setModule(menuPurviewVO.getModule());
        menuPurviewDTO.setName(menuPurviewVO.getName());
        menuPurviewDTO.setType(menuPurviewVO.getType());
        menuPurviewDTO.setStatus(menuPurviewVO.getStatus());
        menuPurviewDTO.setParentId(menuPurviewVO.getParentId());
        menuPurviewDTO.setDescription(menuPurviewVO.getDescription());
        return menuPurviewDTO;

    }

    @Test
    public void getMenuPurviews(){

        List<RoleVO> roleVOs = new ArrayList<>();

        RoleVO roleVO1 = new RoleVO();
        roleVO1.setRoleId(2);
        roleVOs.add(roleVO1);
        ArrayList<Integer> arry = new ArrayList<>();
        List<MenuPurview> menuPurviews= null;
        //该用户是否有超级管理员角色

        for ( RoleVO roleVO: roleVOs) {

            if(!CollectionUtils.isEmpty(roleVO.getPurviews())){
                List<Purview> pur = roleVO.getPurviews();
                pur.stream().forEach(purview -> arry.add(purview.getMenuId()));
            }
        }



        if(CollectionUtils.isEmpty(arry)){
            System.out.println("*************** null");
        }
        List<Integer> arrays = arry.stream().distinct().collect(Collectors.toList());
        //B级权限
        menuPurviews = menuPurviewMapper.queryMenuPurviewList(arrays);

        ArrayList<Integer> parentIds = new ArrayList<>();
        menuPurviews.forEach(menuPurview -> parentIds.add(menuPurview.getParentId()));
        List<Integer> arrayParentIds = arry.stream().distinct().collect(Collectors.toList());
        //A级权限
        List<MenuPurview> menuPurviewParents = menuPurviewMapper.queryMenuPurviewList(arrayParentIds);
        menuPurviews.addAll(menuPurviewParents);
        System.out.println("*********");

    }

}
