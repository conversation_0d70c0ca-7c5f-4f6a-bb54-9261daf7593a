package net.summerfarm.service;

import com.alibaba.fastjson.JSON;
import com.qiniu.util.Json;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.ExchangeItemConfig;
import net.summerfarm.model.domain.ExchangeScopeConfig;
import net.summerfarm.model.vo.ExchangeBaseInfoVO;
import net.summerfarm.model.vo.ExchangeScopeConfigVO;
import net.summerfarm.service.impl.ExchangeActivityServiceImpl;
import org.checkerframework.checker.units.qual.A;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 换购测试类
 * @date 2022/9/14 14:15
 * @Version 1.0
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class ExchangeTest {

    @Resource
    private ExchangeActivityService exchangeActivityService;
    @Test
    public void insert(){
        AjaxResult result1 = exchangeActivityService.queryExchangeScopeItem(77l);
        ExchangeBaseInfoVO exchangeBaseInfoVO = new ExchangeBaseInfoVO();
        exchangeBaseInfoVO.setType(1);
        exchangeBaseInfoVO.setEffectTimeType(1);
//        exchangeBaseInfoVO.setDiscountPercentage(1);
        exchangeBaseInfoVO.setPurchaseLimit(1);
        exchangeBaseInfoVO.setTriggerNum(1);
        exchangeBaseInfoVO.setName("wocao");
        exchangeBaseInfoVO.setStatus(1);
        List<ExchangeScopeConfigVO> exchangeScopeConfigVOS = new ArrayList<>();
        ExchangeScopeConfigVO exchangeScopeConfigVO = new ExchangeScopeConfigVO();
        exchangeScopeConfigVO.setBaseInfoId(1L);
        exchangeScopeConfigVO.setStatus(1);
        exchangeScopeConfigVO.setType(1);
        exchangeScopeConfigVO.setScopeId(2L);
        exchangeScopeConfigVOS.add(exchangeScopeConfigVO);
        exchangeBaseInfoVO.setExchangeScopeConfigVOList(exchangeScopeConfigVOS);
        exchangeActivityService.insertExchangeActivity(exchangeBaseInfoVO);
    }

    @Test
    public void selectPage(){
        ExchangeBaseInfoVO exchangeBaseInfoVO = new ExchangeBaseInfoVO();
        AjaxResult result = exchangeActivityService.queryPage(exchangeBaseInfoVO, 10, 1);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void query(){
        AjaxResult result = exchangeActivityService.queryExchangeBaseInfoDetail(1L);
        System.out.println(JSON.toJSONString(result));
//        exchangeActivityService.
    }

    @Test
    public void close(){
        ExchangeBaseInfoVO exchangeBaseInfoVO = new ExchangeBaseInfoVO();
        exchangeBaseInfoVO.setId(73L);
//        exchangeActivityService.closeActivity(exchangeBaseInfoVO);
        ExchangeScopeConfig scopeConfig = new ExchangeScopeConfig();
        scopeConfig.setId(135L);
        exchangeActivityService.openExchangeScope(scopeConfig);
    }

    @Test
    public void exchangePage(){
        List<ExchangeItemConfig> exchangeItemConfigList = new ArrayList<>();
        ExchangeItemConfig exchangeItemConfig = new ExchangeItemConfig();
        exchangeItemConfig.setPriority(1);
        exchangeItemConfig.setScopeConfigId(1L);
        exchangeItemConfig.setSku("123");
        exchangeItemConfig.setAdjustType(1);
        exchangeItemConfig.setAmount(new BigDecimal(2));
        exchangeItemConfigList.add(exchangeItemConfig);
        System.out.println(JSON.toJSONString(exchangeItemConfigList));
//        AjaxResult ok = AjaxResult.getOK();
//        System.out.println(JSON.toJSONString(ok));
        /*ExchangeBaseInfoVO exchangeBaseInfoVO = new ExchangeBaseInfoVO();
        HashSet<String> strings = new HashSet<>();
        strings.add("卧槽");
//        exchangeBaseInfoVO.setAreaName(strings);
        exchangeBaseInfoVO.setAreaNo(1001);
//        exchangeBaseInfoVO.setMerchantName("都是什么人");
        exchangeBaseInfoVO.setCreator("无情");
        exchangeBaseInfoVO.setStatus(1);
        exchangeBaseInfoVO.setCreateTime(LocalDateTime.now());
        exchangeBaseInfoVO.setName("活动名称");
        exchangeBaseInfoVO.setRemark("备注啊");
        exchangeBaseInfoVO.setTriggerNum(1);
        exchangeBaseInfoVO.setPurchaseLimit(1);
        exchangeBaseInfoVO.setDiscount(new BigDecimal(2));
        exchangeBaseInfoVO.setDiscountPercentage(10);
        exchangeBaseInfoVO.setType(1);
        exchangeBaseInfoVO.setEffectTimeType(1);
        exchangeBaseInfoVO.setStartTime(LocalDateTime.now());
        exchangeBaseInfoVO.setEndTime(LocalDateTime.now());
        String s = JSON.toJSONString(exchangeBaseInfoVO);
        System.out.println(s);
        System.out.println("=============");
        ArrayList<ExchangeScopeConfigVO> exchangeScopeConfigs = new ArrayList<>();
        ArrayList<ExchangeItemConfig> exchangeItemConfigs = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            ExchangeScopeConfigVO exchangeScopeConfig = new ExchangeScopeConfigVO();
            exchangeScopeConfig.setBaseInfoId(1L);
            exchangeScopeConfig.setScopeId(12L);
            exchangeScopeConfig.setType(1);
            exchangeScopeConfig.setStatus(1);
            exchangeScopeConfigs.add(exchangeScopeConfig);

            ExchangeItemConfig exchangeItemConfig = new ExchangeItemConfig();
            exchangeItemConfig.setPriority(1);
            exchangeItemConfig.setScopeConfigId(1L);
            exchangeItemConfig.setSku("123");
            exchangeItemConfig.setAdjustType(1);
            exchangeItemConfig.setAmount(new BigDecimal(2));
            exchangeItemConfigs.add(exchangeItemConfig);
            System.out.println(JSON.toJSONString(exchangeItemConfig));
            exchangeScopeConfig.setExchangeItemConfigList(exchangeItemConfigs);
            System.out.println("2222222222222222");
            System.out.println(JSON.toJSONString(exchangeScopeConfig));
        }
        exchangeBaseInfoVO.setExchangeScopeConfigVOList(exchangeScopeConfigs);

        System.out.println(JSON.toJSONString(exchangeBaseInfoVO));*/
    }
}
