package net.summerfarm.service;

import cn.hutool.core.util.ReflectUtil;
import net.summerfarm.common.util.CronUtils;
import net.summerfarm.common.util.SpringContextUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-10-29
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ScheduleTaskTest {
    @Test
    public void beanTest() throws NoSuchMethodException {
        String entrance = "areaServiceImpl.selectAll";
        String[] strArr = entrance.split("\\.");

        //获取bean
        Object service = SpringContextUtil.getBean(strArr[0]);

        //调用方法
        ReflectUtil.invoke(service, service.getClass().getMethod(strArr[1]));
    }

    @Test
    public void methodTest() throws NoSuchMethodException {
        //    boolean inChange(Integer areaNo, Integer adminId, Long mId);
        String entrance = "areaServiceImpl.inChange";
        String[] strArr = entrance.split("\\.");

        //获取bean
        Object service = SpringContextUtil.getBean(strArr[0]);

        //调用方法
        Method m = service.getClass().getMethod(strArr[1], Integer.class, Integer.class, Long.class);
        Arrays.stream(m.getParameters()).forEach(el -> {
            System.out.println(el.getType().getName());
        });
        System.out.println(LocalDate.class.getName());
    }

    @Test
    public void cronTest(){
        LocalDateTime xx = CronUtils.getNextExecTime("00 00 18 * * ?", LocalDateTime.of(LocalDate.now(), LocalTime.of(18, 0, 0)));
        System.out.println(xx);
    }
}
