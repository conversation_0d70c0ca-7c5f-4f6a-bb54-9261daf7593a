package net.summerfarm.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseTest;
import net.summerfarm.mapper.manage.MerchantOuterMapper;
import net.summerfarm.model.domain.DeliveryPathShortSku;
import net.summerfarm.model.domain.OrderItem;
import net.summerfarm.model.vo.OrderItemVO;
import net.summerfarm.mq.MallListListener;
import net.summerfarm.task.quartz.JobManage;
import org.junit.Test;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 外部对接-订单同步
 * @createTime 2021年10月24日 16:49:00
 */
public class OrderOuterInfoServiceTest extends BaseTest {

    @Resource
    private OrderOuterInfoService orderOuterInfoService;

    @Resource
    private MallListListener mallListListener;

    @Resource
    private JobManage jobManage;

    @Resource
    private MerchantOuterMapper merchantOuterMapper;

    /**
     * 向外部对接平台，推送门店信息
     */
    @Test
    public void pushStoreTest() {
        JSONObject contentJson = new JSONObject();
        contentJson.put("method", "storeadd");
        contentJson.put("outerNo", "WJ00009");
        contentJson.put("outerPlatformId", "2");
        contentJson.put("areaNo", "1001");
        String content = JSON.toJSONString(contentJson);
        orderOuterInfoService.pushStore(content);
    }

    /**
     * 向外部对接平台，初始化推送商品上下架、商品价格信息
     */
    @Test
    public void pushOnSaleInitTest() {
        orderOuterInfoService.pushOnSaleInit();
    }

    /**
     * 向外部对接平台，推送商品上下架信息
     */
    @Test
    public void pushOnSaleUpdatePrice() {
        orderOuterInfoService.pushOnSaleUpdatePrice("5417115233",1001,0,918);
    }

    /**
     * 向外部对接平台，推送订单发货通知
     */
    @Test
    public void pushDeliveryNoticeTest() {
        List<OrderItemVO> shortSkuList = null;

        // 订单发货明细根据sku分组，每个sku的应发货数量
        List<OrderItemVO> orderItemSkuList = new ArrayList<>();
        shortSkuList.stream().collect(Collectors.groupingBy(OrderItem::getSku)).forEach((sku, item) -> {
            int sum = item.stream().mapToInt(OrderItem::getAmount).sum();
            OrderItemVO orderItemVO = new OrderItemVO();
            orderItemVO.setSku(sku);
            orderItemVO.setAmount(sum);
            orderItemSkuList.add(orderItemVO);

        });


        JSONObject contentJson = new JSONObject();
        contentJson.put("orderNo", "04163599599545900");
        String content = JSON.toJSONString(contentJson);
        orderOuterInfoService.pushDeliveryNotice(content);
    }

    /**
     * 向外部对接平台，推送订单收货通知
     */
    @Test
    public void pushReceiptNoticeTest() {
        // 订单发货明细
        List<OrderItemVO> orderItemList = new ArrayList<>();
        OrderItemVO orderItem1 = new OrderItemVO();
        orderItem1.setOrderNo("04163522884362384");
        orderItem1.setSku("5450251810");
        orderItem1.setAmount(3);
        orderItemList.add(orderItem1);
        /*OrderItemVO orderItem2 = new OrderItemVO();
        orderItem2.setOrderNo("04163522884362384");
        orderItem2.setSku("5417115233");
        orderItem2.setAmount(2);
        orderItemList.add(orderItem2);*/
        // 短缺sku
        List<DeliveryPathShortSku> shortSkuList=new ArrayList<>();
        /*DeliveryPathShortSku shortSku1=new DeliveryPathShortSku();
        shortSku1.setSku("5450251810");
        shortSku1.setShortCnt(2);
        shortSkuList.add(shortSku1);

        DeliveryPathShortSku shortSku2=new DeliveryPathShortSku();
        shortSku2.setSku("5450251810");
        shortSku2.setShortCnt(1);
        shortSkuList.add(shortSku2);*/
        // 请求数据
        JSONObject contentJson = new JSONObject();
        contentJson.put("orderItemList", orderItemList);
        contentJson.put("shortSkuList", shortSkuList);
        String content = JSON.toJSONString(contentJson);
        System.out.println(content);
        orderOuterInfoService.pushReceiptNotice(content);
    }

    /**
     * 报价单根据生效时间、失效时间推送上下架、价格信息
     */
    @Test
    public void executeMajorPriceValidInvalidTimeTest(){
        orderOuterInfoService.executeMajorPriceValidInvalidTime(1,918,"2021-11-03 15:16:00");
    }

    /**
     * 外部对接，推送商品上下架、价格往mq放数据
     */
    @Test
    public void mqPushOnSaleUpdatePriceTest(){
        orderOuterInfoService.mqPushOnSaleUpdatePrice("296201302533",1001,null,1);
    }

    /**
     * 外部对接，查询明日配送的订单
     */
    @Test
    public void orderDeliveryNotice(){
        orderOuterInfoService.orderDeliveryNotice();
    }

}
