package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.mapper.manage.QuantityChangeRecordMapper;
import net.summerfarm.mapper.manage.QuantityRecordMapper;
import net.summerfarm.mapper.manage.StockTakingItemMapper;
import net.summerfarm.model.domain.QuantityChangeRecord;
import org.junit.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

public class QuantityChangeRecordTest extends BaseTest {

    @Resource
    private QuantityChangeRecordMapper quantityChangeRecordMapper;

    @Resource
    private StockTakingItemMapper stockTakingItemMapper;
    @Resource
    private ConversionSkuConfigService conversionSkuConfigService;


    @Test
    public void test() {
        QuantityChangeRecord record = new QuantityChangeRecord();
        record.setSku("101566500");
        record.setAreaNo(1);
        record.setRecorder("测试");

        quantityChangeRecordMapper.insertBySelect(record);

    }

    @Resource
    private QuantityRecordMapper recordMapper;
    @Test
    public void testQuery(){
        List<String> r2 = recordMapper.selectListByEndTimeByAreaNo(1,"虚拟库存",LocalDateTime.of(LocalDate.of(2020,01,01), LocalTime.MIN),LocalDateTime.of(LocalDate.of(2020,03,28), LocalTime.MIN),1001);
        System.out.println(r2);
    }
    @Test
    public void dataSku(){
        conversionSkuConfigService.dataSkuQuantity();
    }
}
