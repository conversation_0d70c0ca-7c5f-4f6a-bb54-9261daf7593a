package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mapper.manage.WarehouseStockExtMapper;
import net.summerfarm.model.domain.WarehouseStockExt;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/3/4  15:13
 */
public class WarehouseStockExtTest extends BaseTest {

    @Resource
    private WarehouseStockExtMapper warehouseStockExtMapper;

    @Resource
    private  SkuBatchCodeService skuBatchCodeService;


    @Test
    public void insert(){
        WarehouseStockExt warehouseStockExt = new WarehouseStockExt(2,"123524");
        warehouseStockExt.setStatus(1);
        WarehouseStockExt warehouseStockExt1 = warehouseStockExtMapper.selectWarehouseStockExt(warehouseStockExt);
    }

    @Test
    public void update(){
        AjaxResult result = skuBatchCodeService.queryDetailSkuBatchCode("000005328S00001");
        result.getData();
    }
}
