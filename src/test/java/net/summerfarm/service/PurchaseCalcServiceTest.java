package net.summerfarm.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.AreaSkuMapper;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.model.domain.Area;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class PurchaseCalcServiceTest {
    @Resource
    private PurchaseCalculateService purchaseCalculateService;
    @Resource
    private MerchantLifecycleService merchantLifecycleService;

    @Resource
    private VisitPlanService visitPlanService;
    @Test
    public void task() {
//        visitPlanService.sendDingTalkMsg();
    }

    @Test
    public void ttt(){
        Area area = new Area(1, 122, "ssss");
        log.info("area：{}", area);
        log.info("area：{}", JSONObject.toJSONString(area));

      float f =  BigDecimal.valueOf(70).divide(BigDecimal.valueOf(1308),  4, BigDecimal.ROUND_HALF_UP).divide(BigDecimal.valueOf(30), 4, BigDecimal.ROUND_HALF_UP).floatValue();
        System.out.println(f);
    }

    @Resource
    private AreaMapper areaMapper;
    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Test
    public void syncSkuQualityDate(){
     /*   Integer areaNo = 1001;
        String sku = "5476178015";
        //查询城市对应仓
        Area store = areaMapper.selectByAreaNo(areaNo);

        //查询sku有剩余批次（已兼容使用中心仓的库存情况）
        List<StoreRecordVO> recordList = storeRecordMapper.selectQuantity(sku);
        recordList = recordList.stream()
                .filter(el-> Objects.equals(store.getParentNo(), el.getAreaNo()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(recordList) &&  recordList.stream().noneMatch(el -> el.getQualityDate() == null)) {
            recordList.sort(Comparator.comparing(StoreRecordVO::getQualityDate));
            LocalDate qualityDate = recordList.get(0).getQualityDate();

            String dateStr = null;
            if(qualityDate.isAfter(LocalDate.now())){
                dateStr = qualityDate.format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
            }
            areaSkuMapper.updateQualityDate(null, areaNo, sku, dateStr);
        }*/
    }

    @Test
    public void create(){
        purchaseCalculateService.saleVolume();
    }

    @Test
    public void timingOrderLockSomeTime(){
        merchantLifecycleService.timingSendMsg();
    }

    @Resource
    private BiPurchasesConfigService biPurchasesConfigService;

    @Test
    public void test() throws IOException {
        biPurchasesConfigService.biPurchasesConfigUpdate();
    }

}
