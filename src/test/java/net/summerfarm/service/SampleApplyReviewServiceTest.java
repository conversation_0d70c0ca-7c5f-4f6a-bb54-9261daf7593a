package net.summerfarm.service;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mapper.manage.SampleApplyMapper;
import net.summerfarm.mapper.manage.SampleApplyReviewMapper;
import net.summerfarm.model.domain.SampleApply;
import net.summerfarm.model.domain.SampleApplyReview;
import net.summerfarm.model.vo.SampleApplyReviewVO;
import net.summerfarm.model.vo.SampleApplyVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SampleApplyReviewServiceTest {

    private static final Logger logger = LoggerFactory.getLogger(SampleApplyReviewServiceTest.class);

    @Resource
    private SampleApplyReviewService sampleApplyReviewService;

    @Resource
    private SampleApplyReviewMapper sampleApplyReviewMapper;

    @Resource
    private SampleApplyMapper sampleApplyMapper;

    @Test
    public void selectSampleApplyReviewTest(){
        SampleApply sampleApply = new SampleApply();
        AjaxResult ajaxResult = sampleApplyReviewService.selectSampleApplyReview(1, 10, sampleApply);
        logger.info(ajaxResult.toString());
    }

    @Test
    public void selectSampleApplyReviewVOByIdTest(){
        AjaxResult ajaxResult = sampleApplyReviewService.selectSampleApplyReviewVOById(7);
        logger.info(JSONUtil.toJsonStr(ajaxResult));
    }

    @Test
    public void isReviewTest(){
        SampleApplyReview review = sampleApplyReviewMapper.isReview(228,null);
        System.out.println(Objects.isNull(review));
    }

    @Test
    public void selectByBdIds(){
        SampleApply sampleApply = new SampleApply();
//        sampleApply.setMName("测试08192");
//        sampleApply.setStatus(0);
//        sampleApply.setAreaNo(1001);

        List<SampleApplyReviewVO> sampleApplies = sampleApplyReviewMapper.selectByBdIds(sampleApply.getMName(),sampleApply.getStatus(),sampleApply.getAreaNo(),null);
        for (SampleApplyReviewVO apply : sampleApplies) {
            System.out.println(apply);
        }
    }

    @Test
    public void closeSampleApply(){
        LocalDateTime endTime = LocalDateTime.of(2020,05,12,10,41,26);

        List<Integer> ids = sampleApplyMapper.querySituationListTime(endTime);
        logger.info(ids.toString());
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        // 关闭申请
        SampleApplyReview sampleApplyReview = new SampleApplyReview();
        sampleApplyReview.setReviewName("系统审核");
        sampleApplyReview.setReviewRemark("超时关闭");
        sampleApplyReview.setAuditTime(new Date());
        sampleApplyReview.setStatus(1);
        for (Integer id : ids) {
            sampleApplyMapper.closeSampleApply(id);
            sampleApplyReview.setSampleId(id);
            logger.info(sampleApplyReview.getSampleId().toString());
            sampleApplyReviewMapper.insertSelective(sampleApplyReview);
        }
    }

    @Test
    public void test(){
        SampleApply sampleApply = new SampleApply();
//        sampleApply.setBdId(312);
        List<SampleApplyVO> sampleApplyVOS = sampleApplyMapper.selectSampleApplies(sampleApply, "");
        sampleApplyVOS.forEach(System.out::println);
    }
}
