package net.summerfarm.service.srm.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mapper.srm.SrmSupplierOfferMapper;
import net.summerfarm.model.domain.srm.SrmSupplierOffer;
import net.summerfarm.model.domain.srm.SrmSupplierOfferDetail;
import net.summerfarm.model.domain.srm.SrmSupplierOfferDetailStepPrice;
import net.summerfarm.model.input.srm.SrmSupplierOfferQuery;
import net.summerfarm.model.param.srm.SrmSupplierOfferDetailParam;
import net.summerfarm.model.param.srm.SrmSupplierOfferDetailStepPriceParam;
import net.summerfarm.model.param.srm.SrmSupplierOfferParam;
import net.summerfarm.model.vo.srm.SrmSupplierOfferDetailStepPriceVo;
import net.summerfarm.model.vo.srm.SrmSupplierOfferDetailVo;
import net.summerfarm.model.vo.srm.SrmSupplierOfferVo;
import net.summerfarm.service.InventoryService;
import net.summerfarm.service.srm.SrmSupplierOfferDetailService;
import net.summerfarm.service.srm.SrmSupplierOfferDetailStepPriceService;
import net.summerfarm.warehouse.mapper.WarehouseStorageCenterMapper;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;
@ActiveProfiles("local")
class SrmSupplierOfferServiceImplTest {

    private SrmSupplierOfferServiceImpl srmSupplierOfferServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        srmSupplierOfferServiceImplUnderTest = new SrmSupplierOfferServiceImpl();
        srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService = mock(SrmSupplierOfferDetailService.class);
        srmSupplierOfferServiceImplUnderTest.inventoryService = mock(InventoryService.class);
        srmSupplierOfferServiceImplUnderTest.warehouseStorageCenterMapper = mock(WarehouseStorageCenterMapper.class);
        srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper = mock(SrmSupplierOfferMapper.class);
        srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailStepPriceService = mock(
                SrmSupplierOfferDetailStepPriceService.class);
    }

    @Test
    void testSave() {
        // Setup
        final SrmSupplierOfferParam srmSupplierOfferParam = new SrmSupplierOfferParam();
        srmSupplierOfferParam.setSku("sku");
        srmSupplierOfferParam.setWarehouseNo(0L);
        srmSupplierOfferParam.setSupplierId(0L);
        srmSupplierOfferParam.setQuantity(0);
        final SrmSupplierOfferDetailParam srmSupplierOfferDetailParam = new SrmSupplierOfferDetailParam();
        srmSupplierOfferDetailParam.setId(0L);
        srmSupplierOfferDetailParam.setOfferId(0L);
        srmSupplierOfferDetailParam.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailParam.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SrmSupplierOfferDetailStepPriceParam srmSupplierOfferDetailStepPriceParam = new SrmSupplierOfferDetailStepPriceParam();
        srmSupplierOfferDetailStepPriceParam.setId(0L);
        srmSupplierOfferDetailStepPriceParam.setOfferDetailId(0L);
        srmSupplierOfferDetailStepPriceParam.setQuantity(0);
        srmSupplierOfferDetailStepPriceParam.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailParam.setStepPriceList(Arrays.asList(srmSupplierOfferDetailStepPriceParam));
        srmSupplierOfferParam.setSrmSupplierOfferDetailParam(srmSupplierOfferDetailParam);

        // Configure WarehouseStorageCenterMapper.selectByWarehouseNo(...).
        final WarehouseStorageCenter warehouseStorageCenter = new WarehouseStorageCenter();
        warehouseStorageCenter.setId(0);
        warehouseStorageCenter.setWarehouseNo(0);
        warehouseStorageCenter.setWarehouseName("warehouseName");
        warehouseStorageCenter.setManageAdminId(0);
        warehouseStorageCenter.setType(0);
        warehouseStorageCenter.setAreaManageId(0);
        warehouseStorageCenter.setStatus(0);
        warehouseStorageCenter.setAddress("address");
        warehouseStorageCenter.setPoiNote("poiNote");
        warehouseStorageCenter.setMailToAddress("mailToAddress");
        warehouseStorageCenter.setUpdater(0);
        warehouseStorageCenter.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        warehouseStorageCenter.setCreator(0);
        warehouseStorageCenter.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(srmSupplierOfferServiceImplUnderTest.warehouseStorageCenterMapper.selectByWarehouseNo(0))
                .thenReturn(warehouseStorageCenter);

        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper.selectOfferIdBySupplierWarehouseSku(0L, 0L,
                "sku")).thenReturn(0L);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper.insertSelective(
                new SrmSupplierOffer())).thenReturn(0);

        // Configure SrmSupplierOfferDetailService.save(...).
        final AjaxResult ajaxResult = new AjaxResult();
        ajaxResult.setCode("code");
        ajaxResult.setMsg("msg");
        ajaxResult.setData("data");
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService.save(
                new SrmSupplierOfferDetailParam())).thenReturn(ajaxResult);

        // Run the test
        final AjaxResult result = srmSupplierOfferServiceImplUnderTest.save(srmSupplierOfferParam);

        // Verify the results
        verify(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper).insertSelective(new SrmSupplierOffer());
        verify(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService).save(
                new SrmSupplierOfferDetailParam());
    }

    @Test
    void testSave_SrmSupplierOfferDetailServiceReturnsError() {
        // Setup
        final SrmSupplierOfferParam srmSupplierOfferParam = new SrmSupplierOfferParam();
        srmSupplierOfferParam.setSku("sku");
        srmSupplierOfferParam.setWarehouseNo(0L);
        srmSupplierOfferParam.setSupplierId(0L);
        srmSupplierOfferParam.setQuantity(0);
        final SrmSupplierOfferDetailParam srmSupplierOfferDetailParam = new SrmSupplierOfferDetailParam();
        srmSupplierOfferDetailParam.setId(0L);
        srmSupplierOfferDetailParam.setOfferId(0L);
        srmSupplierOfferDetailParam.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailParam.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SrmSupplierOfferDetailStepPriceParam srmSupplierOfferDetailStepPriceParam = new SrmSupplierOfferDetailStepPriceParam();
        srmSupplierOfferDetailStepPriceParam.setId(0L);
        srmSupplierOfferDetailStepPriceParam.setOfferDetailId(0L);
        srmSupplierOfferDetailStepPriceParam.setQuantity(0);
        srmSupplierOfferDetailStepPriceParam.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailParam.setStepPriceList(Arrays.asList(srmSupplierOfferDetailStepPriceParam));
        srmSupplierOfferParam.setSrmSupplierOfferDetailParam(srmSupplierOfferDetailParam);

        // Configure WarehouseStorageCenterMapper.selectByWarehouseNo(...).
        final WarehouseStorageCenter warehouseStorageCenter = new WarehouseStorageCenter();
        warehouseStorageCenter.setId(0);
        warehouseStorageCenter.setWarehouseNo(0);
        warehouseStorageCenter.setWarehouseName("warehouseName");
        warehouseStorageCenter.setManageAdminId(0);
        warehouseStorageCenter.setType(0);
        warehouseStorageCenter.setAreaManageId(0);
        warehouseStorageCenter.setStatus(0);
        warehouseStorageCenter.setAddress("address");
        warehouseStorageCenter.setPoiNote("poiNote");
        warehouseStorageCenter.setMailToAddress("mailToAddress");
        warehouseStorageCenter.setUpdater(0);
        warehouseStorageCenter.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        warehouseStorageCenter.setCreator(0);
        warehouseStorageCenter.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(srmSupplierOfferServiceImplUnderTest.warehouseStorageCenterMapper.selectByWarehouseNo(0))
                .thenReturn(warehouseStorageCenter);

        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper.selectOfferIdBySupplierWarehouseSku(0L, 0L,
                "sku")).thenReturn(0L);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper.insertSelective(
                new SrmSupplierOffer())).thenReturn(0);

        // Configure SrmSupplierOfferDetailService.save(...).
        final AjaxResult ajaxResult = AjaxResult.getError();
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService.save(
                new SrmSupplierOfferDetailParam())).thenReturn(ajaxResult);

        // Run the test
        final AjaxResult result = srmSupplierOfferServiceImplUnderTest.save(srmSupplierOfferParam);

        // Verify the results
        verify(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper).insertSelective(new SrmSupplierOffer());
        verify(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService).save(
                new SrmSupplierOfferDetailParam());
    }

    @Test
    void testSelect() {
        // Setup
        final SrmSupplierOfferQuery srmSupplierOfferQuery = new SrmSupplierOfferQuery();
        srmSupplierOfferQuery.setPdName("pdName");
        srmSupplierOfferQuery.setWarehouseNos(Arrays.asList(0));
        srmSupplierOfferQuery.setSupplierId(0L);

        // Configure SrmSupplierOfferMapper.selectByPdNameAndWarehouseId(...).
        final SrmSupplierOfferVo srmSupplierOfferVo = new SrmSupplierOfferVo();
        srmSupplierOfferVo.setId(0L);
        srmSupplierOfferVo.setSku("sku");
        srmSupplierOfferVo.setWarehouseNo(0L);
        srmSupplierOfferVo.setSupplierId(0L);
        srmSupplierOfferVo.setStatus(0);
        srmSupplierOfferVo.setLowestPrice(new BigDecimal("0.00"));
        srmSupplierOfferVo.setHighestPrice(new BigDecimal("0.00"));
        srmSupplierOfferVo.setPdName("pdName");
        srmSupplierOfferVo.setSupplierName("supplierName");
        srmSupplierOfferVo.setWarehouseName("warehouseName");
        srmSupplierOfferVo.setPic("pic");
        srmSupplierOfferVo.setWeight("weight");
        final List<SrmSupplierOfferVo> srmSupplierOfferVos = Arrays.asList(srmSupplierOfferVo);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper.selectByPdNameAndWarehouseId(
                new SrmSupplierOfferQuery())).thenReturn(srmSupplierOfferVos);

        // Run the test
        final AjaxResult result = srmSupplierOfferServiceImplUnderTest.select(0, 0, srmSupplierOfferQuery);

        // Verify the results
    }

    @Test
    void testSelect_SrmSupplierOfferMapperReturnsNoItems() {
        // Setup
        final SrmSupplierOfferQuery srmSupplierOfferQuery = new SrmSupplierOfferQuery();
        srmSupplierOfferQuery.setPdName("pdName");
        srmSupplierOfferQuery.setWarehouseNos(Arrays.asList(0));
        srmSupplierOfferQuery.setSupplierId(0L);

        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper.selectByPdNameAndWarehouseId(
                new SrmSupplierOfferQuery())).thenReturn(Collections.emptyList());

        // Run the test
        final AjaxResult result = srmSupplierOfferServiceImplUnderTest.select(0, 0, srmSupplierOfferQuery);

        // Verify the results
    }

    @Test
    void testDetail() {
        // Setup
        // Configure SrmSupplierOfferDetailService.getByOfferId(...).
        final SrmSupplierOfferDetailVo srmSupplierOfferDetailVo = new SrmSupplierOfferDetailVo();
        srmSupplierOfferDetailVo.setId(0L);
        srmSupplierOfferDetailVo.setOfferId(0L);
        srmSupplierOfferDetailVo.setStatus(0);
        srmSupplierOfferDetailVo.setStartTime(LocalDate.of(2020, 1, 1));
        srmSupplierOfferDetailVo.setEndTime(LocalDate.of(2020, 1, 1));
        final SrmSupplierOfferDetailStepPriceVo srmSupplierOfferDetailStepPriceVo = new SrmSupplierOfferDetailStepPriceVo();
        srmSupplierOfferDetailStepPriceVo.setQuantity(0);
        srmSupplierOfferDetailStepPriceVo.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailVo.setStepPriceVoList(Arrays.asList(srmSupplierOfferDetailStepPriceVo));
//        srmSupplierOfferDetailVo.setPdName("pdName");
//        srmSupplierOfferDetailVo.setWarehouseName("warehouseName");
//        srmSupplierOfferDetailVo.setWarehouseAddress("warehouseAddress");
//        srmSupplierOfferDetailVo.setPic("pic");
//        srmSupplierOfferDetailVo.setWeight("weight");
        final List<SrmSupplierOfferDetailVo> srmSupplierOfferDetailVos = Arrays.asList(srmSupplierOfferDetailVo);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService.getByOfferId(0L))
                .thenReturn(srmSupplierOfferDetailVos);

        // Run the test
        final AjaxResult result = srmSupplierOfferServiceImplUnderTest.detail(6L);

        // Verify the results
    }

    @Test
    void testDetail_SrmSupplierOfferDetailServiceReturnsNoItems() {
        // Setup
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService.getByOfferId(6L))
                .thenReturn(Collections.emptyList());

        // Run the test
        final AjaxResult result = srmSupplierOfferServiceImplUnderTest.detail(6L);

        // Verify the results
    }

    @Test
    void testGetSupplierOfferPrice() {
        // Setup
        final SrmSupplierOfferParam param = new SrmSupplierOfferParam();
        param.setSku("sku");
        param.setWarehouseNo(0L);
        param.setSupplierId(0L);
        param.setQuantity(0);
        final SrmSupplierOfferDetailParam srmSupplierOfferDetailParam = new SrmSupplierOfferDetailParam();
        srmSupplierOfferDetailParam.setId(0L);
        srmSupplierOfferDetailParam.setOfferId(0L);
        srmSupplierOfferDetailParam.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailParam.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SrmSupplierOfferDetailStepPriceParam srmSupplierOfferDetailStepPriceParam = new SrmSupplierOfferDetailStepPriceParam();
        srmSupplierOfferDetailStepPriceParam.setId(0L);
        srmSupplierOfferDetailStepPriceParam.setOfferDetailId(0L);
        srmSupplierOfferDetailStepPriceParam.setQuantity(0);
        srmSupplierOfferDetailStepPriceParam.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailParam.setStepPriceList(Arrays.asList(srmSupplierOfferDetailStepPriceParam));
        param.setSrmSupplierOfferDetailParam(srmSupplierOfferDetailParam);

        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper.selectOfferIdBySupplierWarehouseSku(0L, 0L,
                "sku")).thenReturn(0L);

        // Configure SrmSupplierOfferDetailService.getByOfferIdAndStatus(...).
        final SrmSupplierOfferDetail srmSupplierOfferDetail = new SrmSupplierOfferDetail();
        srmSupplierOfferDetail.setId(0L);
        srmSupplierOfferDetail.setOfferId(0L);
        srmSupplierOfferDetail.setStatus(0);
        srmSupplierOfferDetail.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setCreator("creator");
        srmSupplierOfferDetail.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setUpdater("updater");
        final List<SrmSupplierOfferDetail> srmSupplierOfferDetails = Arrays.asList(srmSupplierOfferDetail);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService.getByOfferIdAndStatus(0L,
                0)).thenReturn(srmSupplierOfferDetails);

        // Configure SrmSupplierOfferDetailStepPriceService.getStepPriceByOfferDetailId(...).
        final SrmSupplierOfferDetailStepPrice srmSupplierOfferDetailStepPrice = new SrmSupplierOfferDetailStepPrice();
        srmSupplierOfferDetailStepPrice.setId(0L);
        srmSupplierOfferDetailStepPrice.setOfferDetailId(0L);
        srmSupplierOfferDetailStepPrice.setQuantity(0);
        srmSupplierOfferDetailStepPrice.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailStepPrice.setCreator("creator");
        srmSupplierOfferDetailStepPrice.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailStepPrice.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailStepPrice.setUpdater("updater");
        final List<SrmSupplierOfferDetailStepPrice> srmSupplierOfferDetailStepPrices = Arrays.asList(
                srmSupplierOfferDetailStepPrice);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailStepPriceService.getStepPriceByOfferDetailId(
                0L)).thenReturn(srmSupplierOfferDetailStepPrices);

        // Run the test
        final AjaxResult result = srmSupplierOfferServiceImplUnderTest.getSupplierOfferPrice(param);

        // Verify the results
    }

    @Test
    void testGetSupplierOfferPrice_SrmSupplierOfferDetailServiceReturnsNoItems() {
        // Setup
        final SrmSupplierOfferParam param = new SrmSupplierOfferParam();
        param.setSku("sku");
        param.setWarehouseNo(0L);
        param.setSupplierId(0L);
        param.setQuantity(0);
        final SrmSupplierOfferDetailParam srmSupplierOfferDetailParam = new SrmSupplierOfferDetailParam();
        srmSupplierOfferDetailParam.setId(0L);
        srmSupplierOfferDetailParam.setOfferId(0L);
        srmSupplierOfferDetailParam.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailParam.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SrmSupplierOfferDetailStepPriceParam srmSupplierOfferDetailStepPriceParam = new SrmSupplierOfferDetailStepPriceParam();
        srmSupplierOfferDetailStepPriceParam.setId(0L);
        srmSupplierOfferDetailStepPriceParam.setOfferDetailId(0L);
        srmSupplierOfferDetailStepPriceParam.setQuantity(0);
        srmSupplierOfferDetailStepPriceParam.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailParam.setStepPriceList(Arrays.asList(srmSupplierOfferDetailStepPriceParam));
        param.setSrmSupplierOfferDetailParam(srmSupplierOfferDetailParam);

        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper.selectOfferIdBySupplierWarehouseSku(0L, 0L,
                "sku")).thenReturn(0L);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService.getByOfferIdAndStatus(0L,
                0)).thenReturn(Collections.emptyList());

        // Configure SrmSupplierOfferDetailStepPriceService.getStepPriceByOfferDetailId(...).
        final SrmSupplierOfferDetailStepPrice srmSupplierOfferDetailStepPrice = new SrmSupplierOfferDetailStepPrice();
        srmSupplierOfferDetailStepPrice.setId(0L);
        srmSupplierOfferDetailStepPrice.setOfferDetailId(0L);
        srmSupplierOfferDetailStepPrice.setQuantity(0);
        srmSupplierOfferDetailStepPrice.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailStepPrice.setCreator("creator");
        srmSupplierOfferDetailStepPrice.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailStepPrice.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailStepPrice.setUpdater("updater");
        final List<SrmSupplierOfferDetailStepPrice> srmSupplierOfferDetailStepPrices = Arrays.asList(
                srmSupplierOfferDetailStepPrice);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailStepPriceService.getStepPriceByOfferDetailId(
                0L)).thenReturn(srmSupplierOfferDetailStepPrices);

        // Run the test
        final AjaxResult result = srmSupplierOfferServiceImplUnderTest.getSupplierOfferPrice(param);

        // Verify the results
    }

    @Test
    void testGetSupplierOfferPrice_SrmSupplierOfferDetailStepPriceServiceReturnsNoItems() {
        // Setup
        final SrmSupplierOfferParam param = new SrmSupplierOfferParam();
        param.setSku("sku");
        param.setWarehouseNo(0L);
        param.setSupplierId(0L);
        param.setQuantity(0);
        final SrmSupplierOfferDetailParam srmSupplierOfferDetailParam = new SrmSupplierOfferDetailParam();
        srmSupplierOfferDetailParam.setId(0L);
        srmSupplierOfferDetailParam.setOfferId(0L);
        srmSupplierOfferDetailParam.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailParam.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SrmSupplierOfferDetailStepPriceParam srmSupplierOfferDetailStepPriceParam = new SrmSupplierOfferDetailStepPriceParam();
        srmSupplierOfferDetailStepPriceParam.setId(0L);
        srmSupplierOfferDetailStepPriceParam.setOfferDetailId(0L);
        srmSupplierOfferDetailStepPriceParam.setQuantity(0);
        srmSupplierOfferDetailStepPriceParam.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailParam.setStepPriceList(Arrays.asList(srmSupplierOfferDetailStepPriceParam));
        param.setSrmSupplierOfferDetailParam(srmSupplierOfferDetailParam);

        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper.selectOfferIdBySupplierWarehouseSku(0L, 0L,
                "sku")).thenReturn(0L);

        // Configure SrmSupplierOfferDetailService.getByOfferIdAndStatus(...).
        final SrmSupplierOfferDetail srmSupplierOfferDetail = new SrmSupplierOfferDetail();
        srmSupplierOfferDetail.setId(0L);
        srmSupplierOfferDetail.setOfferId(0L);
        srmSupplierOfferDetail.setStatus(0);
        srmSupplierOfferDetail.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setCreator("creator");
        srmSupplierOfferDetail.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setUpdater("updater");
        final List<SrmSupplierOfferDetail> srmSupplierOfferDetails = Arrays.asList(srmSupplierOfferDetail);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService.getByOfferIdAndStatus(0L,
                0)).thenReturn(srmSupplierOfferDetails);

        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailStepPriceService.getStepPriceByOfferDetailId(
                0L)).thenReturn(Collections.emptyList());

        // Run the test
        final AjaxResult result = srmSupplierOfferServiceImplUnderTest.getSupplierOfferPrice(param);

        // Verify the results
    }

    @Test
    void testGetSupplierOfferPriceList() {
        // Setup
        final SrmSupplierOfferParam param = new SrmSupplierOfferParam();
        param.setSku("sku");
        param.setWarehouseNo(0L);
        param.setSupplierId(0L);
        param.setQuantity(0);
        final SrmSupplierOfferDetailParam srmSupplierOfferDetailParam = new SrmSupplierOfferDetailParam();
        srmSupplierOfferDetailParam.setId(0L);
        srmSupplierOfferDetailParam.setOfferId(0L);
        srmSupplierOfferDetailParam.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailParam.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SrmSupplierOfferDetailStepPriceParam srmSupplierOfferDetailStepPriceParam = new SrmSupplierOfferDetailStepPriceParam();
        srmSupplierOfferDetailStepPriceParam.setId(0L);
        srmSupplierOfferDetailStepPriceParam.setOfferDetailId(0L);
        srmSupplierOfferDetailStepPriceParam.setQuantity(0);
        srmSupplierOfferDetailStepPriceParam.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailParam.setStepPriceList(Arrays.asList(srmSupplierOfferDetailStepPriceParam));
        param.setSrmSupplierOfferDetailParam(srmSupplierOfferDetailParam);

        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper.selectOfferIdBySupplierWarehouseSku(0L, 0L,
                "sku")).thenReturn(0L);

        // Configure SrmSupplierOfferDetailService.getByOfferIdAndStatus(...).
        final SrmSupplierOfferDetail srmSupplierOfferDetail = new SrmSupplierOfferDetail();
        srmSupplierOfferDetail.setId(0L);
        srmSupplierOfferDetail.setOfferId(0L);
        srmSupplierOfferDetail.setStatus(0);
        srmSupplierOfferDetail.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setCreator("creator");
        srmSupplierOfferDetail.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setUpdater("updater");
        final List<SrmSupplierOfferDetail> srmSupplierOfferDetails = Arrays.asList(srmSupplierOfferDetail);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService.getByOfferIdAndStatus(0L,
                0)).thenReturn(srmSupplierOfferDetails);

        // Configure SrmSupplierOfferDetailStepPriceService.getStepPriceByOfferDetailId(...).
        final SrmSupplierOfferDetailStepPrice srmSupplierOfferDetailStepPrice = new SrmSupplierOfferDetailStepPrice();
        srmSupplierOfferDetailStepPrice.setId(0L);
        srmSupplierOfferDetailStepPrice.setOfferDetailId(0L);
        srmSupplierOfferDetailStepPrice.setQuantity(0);
        srmSupplierOfferDetailStepPrice.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailStepPrice.setCreator("creator");
        srmSupplierOfferDetailStepPrice.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailStepPrice.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailStepPrice.setUpdater("updater");
        final List<SrmSupplierOfferDetailStepPrice> srmSupplierOfferDetailStepPrices = Arrays.asList(
                srmSupplierOfferDetailStepPrice);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailStepPriceService.getStepPriceByOfferDetailId(
                0L)).thenReturn(srmSupplierOfferDetailStepPrices);

        // Run the test
        final AjaxResult result = srmSupplierOfferServiceImplUnderTest.getSupplierOfferPriceList(param);

        // Verify the results
    }

    @Test
    void testGetSupplierOfferPriceList_SrmSupplierOfferDetailServiceReturnsNoItems() {
        // Setup
        final SrmSupplierOfferParam param = new SrmSupplierOfferParam();
        param.setSku("sku");
        param.setWarehouseNo(0L);
        param.setSupplierId(0L);
        param.setQuantity(0);
        final SrmSupplierOfferDetailParam srmSupplierOfferDetailParam = new SrmSupplierOfferDetailParam();
        srmSupplierOfferDetailParam.setId(0L);
        srmSupplierOfferDetailParam.setOfferId(0L);
        srmSupplierOfferDetailParam.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailParam.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SrmSupplierOfferDetailStepPriceParam srmSupplierOfferDetailStepPriceParam = new SrmSupplierOfferDetailStepPriceParam();
        srmSupplierOfferDetailStepPriceParam.setId(0L);
        srmSupplierOfferDetailStepPriceParam.setOfferDetailId(0L);
        srmSupplierOfferDetailStepPriceParam.setQuantity(0);
        srmSupplierOfferDetailStepPriceParam.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailParam.setStepPriceList(Arrays.asList(srmSupplierOfferDetailStepPriceParam));
        param.setSrmSupplierOfferDetailParam(srmSupplierOfferDetailParam);

        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper.selectOfferIdBySupplierWarehouseSku(0L, 0L,
                "sku")).thenReturn(0L);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService.getByOfferIdAndStatus(0L,
                0)).thenReturn(Collections.emptyList());

        // Configure SrmSupplierOfferDetailStepPriceService.getStepPriceByOfferDetailId(...).
        final SrmSupplierOfferDetailStepPrice srmSupplierOfferDetailStepPrice = new SrmSupplierOfferDetailStepPrice();
        srmSupplierOfferDetailStepPrice.setId(0L);
        srmSupplierOfferDetailStepPrice.setOfferDetailId(0L);
        srmSupplierOfferDetailStepPrice.setQuantity(0);
        srmSupplierOfferDetailStepPrice.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailStepPrice.setCreator("creator");
        srmSupplierOfferDetailStepPrice.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailStepPrice.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailStepPrice.setUpdater("updater");
        final List<SrmSupplierOfferDetailStepPrice> srmSupplierOfferDetailStepPrices = Arrays.asList(
                srmSupplierOfferDetailStepPrice);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailStepPriceService.getStepPriceByOfferDetailId(
                0L)).thenReturn(srmSupplierOfferDetailStepPrices);

        // Run the test
        final AjaxResult result = srmSupplierOfferServiceImplUnderTest.getSupplierOfferPriceList(param);

        // Verify the results
    }

    @Test
    void testGetSupplierOfferPriceList_SrmSupplierOfferDetailStepPriceServiceReturnsNoItems() {
        // Setup
        final SrmSupplierOfferParam param = new SrmSupplierOfferParam();
        param.setSku("sku");
        param.setWarehouseNo(0L);
        param.setSupplierId(0L);
        param.setQuantity(0);
        final SrmSupplierOfferDetailParam srmSupplierOfferDetailParam = new SrmSupplierOfferDetailParam();
        srmSupplierOfferDetailParam.setId(0L);
        srmSupplierOfferDetailParam.setOfferId(0L);
        srmSupplierOfferDetailParam.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetailParam.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SrmSupplierOfferDetailStepPriceParam srmSupplierOfferDetailStepPriceParam = new SrmSupplierOfferDetailStepPriceParam();
        srmSupplierOfferDetailStepPriceParam.setId(0L);
        srmSupplierOfferDetailStepPriceParam.setOfferDetailId(0L);
        srmSupplierOfferDetailStepPriceParam.setQuantity(0);
        srmSupplierOfferDetailStepPriceParam.setPrice(new BigDecimal("0.00"));
        srmSupplierOfferDetailParam.setStepPriceList(Arrays.asList(srmSupplierOfferDetailStepPriceParam));
        param.setSrmSupplierOfferDetailParam(srmSupplierOfferDetailParam);

        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferMapper.selectOfferIdBySupplierWarehouseSku(0L, 0L,
                "sku")).thenReturn(0L);

        // Configure SrmSupplierOfferDetailService.getByOfferIdAndStatus(...).
        final SrmSupplierOfferDetail srmSupplierOfferDetail = new SrmSupplierOfferDetail();
        srmSupplierOfferDetail.setId(0L);
        srmSupplierOfferDetail.setOfferId(0L);
        srmSupplierOfferDetail.setStatus(0);
        srmSupplierOfferDetail.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setCreator("creator");
        srmSupplierOfferDetail.setCreateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        srmSupplierOfferDetail.setUpdater("updater");
        final List<SrmSupplierOfferDetail> srmSupplierOfferDetails = Arrays.asList(srmSupplierOfferDetail);
        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailService.getByOfferIdAndStatus(0L,
                0)).thenReturn(srmSupplierOfferDetails);

        when(srmSupplierOfferServiceImplUnderTest.srmSupplierOfferDetailStepPriceService.getStepPriceByOfferDetailId(
                0L)).thenReturn(Collections.emptyList());

        // Run the test
        final AjaxResult result = srmSupplierOfferServiceImplUnderTest.getSupplierOfferPriceList(param);

        // Verify the results
    }
}
