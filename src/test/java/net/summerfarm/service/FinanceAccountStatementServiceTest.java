package net.summerfarm.service;

import net.summerfarm.common.base.BaseService;
import net.summerfarm.mapper.manage.FinanceBankFlowingWaterConfigMapper;
import net.summerfarm.mapper.manage.FinanceBankFlowingWaterMapper;
import net.summerfarm.model.domain.FinanceBankFlowingWaterConfig;
import net.summerfarm.module.pms.model.input.AccountSupplierQueryInput;
import net.summerfarm.module.pms.model.vo.FinanceAccountAdjustDetailVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class FinanceAccountStatementServiceTest extends BaseService {

    @Resource
    private FinanceAccountStatementService financeAccountStatementService;

    @Resource
    private FinanceBankFlowingWaterConfigMapper financeBankFlowingWaterConfigMapper;

    @Resource
    private FinanceBankFlowingWaterMapper financeBankFlowingWaterMapper;

    @Test
    public void resultMessage(){
        AccountSupplierQueryInput financeAccountStatementDetailInput = new AccountSupplierQueryInput();
        financeAccountStatementDetailInput.setSupplierId(1282);
        financeAccountStatementDetailInput.setSkipStoreOnly(1);
        financeAccountStatementService.selectWarehousingOrderBySupplier( financeAccountStatementDetailInput);
    }

    @Test
    public void time(){
        FinanceBankFlowingWaterConfig financeBankFlowingWaterConfig = new FinanceBankFlowingWaterConfig();
        financeBankFlowingWaterConfig.setTradingDay("********");
        financeBankFlowingWaterConfig.setLastSerialNumber(5);
        //今日该接口增量查询次数 从0开始
        int num = 0;
        if (!ObjectUtils.isEmpty(financeBankFlowingWaterConfig)) {
            num = num + financeBankFlowingWaterConfig.getLastSerialNumber() + 1;
        }
        System.out.println(num);
    }

    @Test
    public void adjustDetailsList(){
        List<FinanceAccountAdjustDetailVO> noDataList = financeAccountStatementService.adjustDetailsList(-1L);
        Assert.isTrue(CollectionUtils.isEmpty(noDataList));
        List<FinanceAccountAdjustDetailVO> dataList = financeAccountStatementService.adjustDetailsList(1L);
        Assert.isTrue(!CollectionUtils.isEmpty(dataList));
    }


}
