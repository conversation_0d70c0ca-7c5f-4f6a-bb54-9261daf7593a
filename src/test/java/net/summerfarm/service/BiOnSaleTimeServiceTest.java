package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.model.domain.StockSkuStatistics;
import net.summerfarm.mapper.offline.StockSalesVolumeMapper;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
public class BiOnSaleTimeServiceTest extends BaseTest {

    @Resource
    private BiOnSaleTimeService biOnSaleTimeService;

    @Resource
    private StockSalesVolumeMapper stockSalesVolumeMapper;

    @Test
    public void test() {
//        biOnSaleTimeService.update(LocalDate.of(2020, 3, 4));
//      List<StockSkuStatistics> select = stockSalesVolumeMapper.select1();
//        System.out.println(select.toString());
    }
}
