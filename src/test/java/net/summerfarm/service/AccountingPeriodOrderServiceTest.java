package net.summerfarm.service;

import com.github.pagehelper.PageHelper;
import net.summerfarm.BaseTest;
import net.summerfarm.enums.AccountingPeriodOrderTypeEnum;
import net.summerfarm.enums.FinancialAuditEnum;
import net.summerfarm.enums.PeriodOrderWriteOffStatus;
import net.summerfarm.enums.periodOrderTypeEnum;
import net.summerfarm.mapper.manage.FinanceAccountingPeriodOrderMapper;
import net.summerfarm.model.input.AccountingPeriodOrderQuery;
import net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO;
import org.junit.Test;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2021/12/23  16:56
 */
public class AccountingPeriodOrderServiceTest extends BaseTest {

    @Resource
    AccountingPeriodOrderService accountingPeriodOrderService;

    @Resource
    private FinanceAccountingPeriodOrderMapper financeAccountingPeriodOrderMapper;


    @Test
    public void allDownload(){
        List<Long> ids = new ArrayList<>();
        ids.add(407L);
        accountingPeriodOrderService.allDownload(ids,1048,"test384.zip",0);
    }

    @Test
    public void storeBillDownload(){
        accountingPeriodOrderService.storeBillDownload(564L,"************.xls",0);
    }

    @Test
    public void billDownload(){
        accountingPeriodOrderService.billDownload(806L,874,"**********.xls",0);
    }

    @Test
    public void generateAccountingPeriodBill(){
        accountingPeriodOrderService.generateAccountingPeriodBill();
    }

    @Test
    public void adad(){
        AccountingPeriodOrderQuery accountingPeriodOrderQuery = new AccountingPeriodOrderQuery();
        accountingPeriodOrderQuery.setSalerId(895);
        List<FinanceAccountingPeriodOrderVO> financeAccountingPeriodOrderList = financeAccountingPeriodOrderMapper.selectBySalerIdList(accountingPeriodOrderQuery);
        for (FinanceAccountingPeriodOrderVO financeAccountingPeriodOrderVO : financeAccountingPeriodOrderList) {
            if (ObjectUtils.isEmpty(accountingPeriodOrderQuery.getPeriodOrderType())) {
                if (Objects.equals(financeAccountingPeriodOrderVO.getPeriodOrderType(), AccountingPeriodOrderTypeEnum.NOT_ENSURE.ordinal()) && Objects.equals(financeAccountingPeriodOrderVO.getCustomerConfirmStatus(), PeriodOrderWriteOffStatus.UN_CONFIRMED.getId())) {
                    System.out.println(1);
                }
                if (Objects.equals(financeAccountingPeriodOrderVO.getPeriodOrderType(), AccountingPeriodOrderTypeEnum.NOT_ENSURE.ordinal()) && Objects.equals(financeAccountingPeriodOrderVO.getCustomerConfirmStatus(), PeriodOrderWriteOffStatus.CONFIRMED.getId())
                        && Objects.equals(financeAccountingPeriodOrderVO.getFinancialAudit(), FinancialAuditEnum.NOT_APPROVED.ordinal())) {
                    System.out.println(2);
                }
                if (Objects.equals(financeAccountingPeriodOrderVO.getPeriodOrderType(), AccountingPeriodOrderTypeEnum.HAS_ENSURE.ordinal()) && Objects.equals(financeAccountingPeriodOrderVO.getCustomerConfirmStatus(), PeriodOrderWriteOffStatus.CONFIRMED.getId())
                        && Objects.equals(financeAccountingPeriodOrderVO.getFinancialAudit(), FinancialAuditEnum.APPROVED.ordinal()) && !Objects.equals(financeAccountingPeriodOrderVO.getReceiptStatus(), PeriodOrderWriteOffStatus.UN_COLLECTED.getId())) {
                    System.out.println(3);
                }
                if (Objects.equals(financeAccountingPeriodOrderVO.getPeriodOrderType(), AccountingPeriodOrderTypeEnum.HAS_ENSURE.ordinal()) && Objects.equals(financeAccountingPeriodOrderVO.getCustomerConfirmStatus(), PeriodOrderWriteOffStatus.CONFIRMED.getId())
                        && Objects.equals(financeAccountingPeriodOrderVO.getFinancialAudit(), FinancialAuditEnum.APPROVED.ordinal()) && Objects.equals(financeAccountingPeriodOrderVO.getReceiptStatus(), PeriodOrderWriteOffStatus.UN_COLLECTED.getId())) {
                    System.out.println(4);
                }

            }
            financeAccountingPeriodOrderVO.setPeriodOrderType(accountingPeriodOrderQuery.getPeriodOrderType());
        }


    }

}
