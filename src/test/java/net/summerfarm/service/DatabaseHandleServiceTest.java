package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.mapper.manage.ProductsMapper;
import net.summerfarm.model.domain.Products;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-03-02
 * @description
 */
public class DatabaseHandleServiceTest extends BaseTest {
    @Resource
    private DataBaseHandleService dataBaseHandleService;
    @Resource
    private ProductsMapper productsMapper;

    @Test
    public void snapSingleTest() {
        Integer id = dataBaseHandleService.createRestore("测试咩");
        Products products = productsMapper.selectByPdId(612L);
        Products newProducts = productsMapper.selectByPdId(611L);
        newProducts.setPdId(612L);
        newProducts.setPdName("哇咔咔");
        newProducts.setSlogan("wowowo");
        dataBaseHandleService.addRestoreData(id, products, newProducts, Products.class, "pdId", "pdName", "slogan", "categoryId", "origin");
    }

    @Test
    public void snapListTest() {
        Integer id = dataBaseHandleService.createRestore("测试咩");
        Products products = productsMapper.selectByPdId(612L);
        Products newProducts = productsMapper.selectByPdId(611L);
        newProducts.setPdId(612L);
        newProducts.setPdName("哇咔咔");
        newProducts.setSlogan("wowowo");
        dataBaseHandleService.addRestoreData(id, Collections.singletonList(products), Collections.singletonList(newProducts), Products.class, "pdId", "pdName", "slogan", "categoryId", "origin");
    }

    @Test
    public void snap(){
        dataBaseHandleService.snap(27);
    }
}
