package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * @Package: net.summerfarm.service
 * @Description: 角色业务逻辑测试类
 * @author: <EMAIL>
 * @Date: 2016/8/8
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration
public class RoleServiceTest {

    private static final Logger logger = LoggerFactory.getLogger(RoleServiceTest.class);

    @Resource
    private RoleService roleService;

    @Test
    public void 查询角色信息及拥有权限(){
       // AjaxResult ajaxResult = roleService.selectPurviews(1);
       // logger.info(ajaxResult.toString());
    }
}
