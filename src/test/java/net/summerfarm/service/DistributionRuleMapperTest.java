package net.summerfarm.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.DistributionFree;
import net.summerfarm.model.domain.DistributionFreeRule;
import net.summerfarm.model.domain.DistributionRule;
import net.summerfarm.model.domain.OrderItem;
import net.summerfarm.model.input.MajorMerchantQuery;
import net.summerfarm.model.input.MajorPriceInput;
import net.summerfarm.model.vo.*;
import net.summerfarm.module.pms.infrastructure.mapper.OldAllocationOrderItemMapper;
import net.summerfarm.module.pms.model.vo.AllocationOrderItemEntityVO;
import org.apache.commons.lang.StringEscapeUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2019/7/3  3:25 PM
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration
public class DistributionRuleMapperTest extends BaseTest {

    @Resource
    private DistributionRuleMapper distributionRuleMapper;

    @Resource
    private DistributionFreeRuleMapper distributionFreeRuleMapper;

    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    private MajorPriceMapper majorPriceMapper;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private AdminService adminService;

    @Resource
    private MajorPriceService majorMerchantDetail;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private OrderService orderService;

    @Resource
    private InventoryService inventoryService;

    @Resource
    private  MajorPriceService majorPriceService;
    @Resource
    private AreaStoreMapper areaStoreMapper;

    @Resource
    private QuantityRecordMapper quantityRecordMapper;

    @Resource
    private OldAllocationOrderItemMapper oldAllocationOrderItemMapper;

    @Resource
    private  StockTaskItemMapper stockTaskItemMapper;
    
    @Resource
    private ProductsMapper productsMapper;

    @Resource
    private StoreRecordMapper storeRecordMapper;

    @Resource
    private SkuMappingMapper skuMappingMapper;

    @Resource
    private  OrderItemMapper orderItemMapper;

    @Resource
    private PurchasesPlanMapper purchasesPlanMapper;

    @Resource
    private MajorRebateMapper majorRebateMapper;

    @Resource
    private MerchantLifecycleMapper merchantLifecycleMapper;

    @Resource
    private FollowUpRelationService followUpRelationService;
    @Resource
    private ReportService reportService;
    @Resource
    private MerchantService merchantService;


    @Test
    public void insert() {
        ArrayList<DistributionRule> distributionRules = new ArrayList<>();
        DistributionRule dtr = new DistributionRule();
        dtr.setDeliveryFee(new BigDecimal(10));
        dtr.setAreaNo(10010);
        dtr.setAdminId(10);
        dtr.setStatus(1);
        distributionRules.add(dtr);
        Integer integer = distributionRuleMapper.insertBathRule(distributionRules);
        System.out.println(integer);

    }

    @Test
    public void query() {
        List<DistributionRule> distributionRules = distributionRuleMapper.selectByAdminId(10);
        for (DistributionRule distributionRule : distributionRules) {
            System.out.println("***" + distributionRule.toString());

        }

    }

    @Test
    public void update() {
        DistributionRule distributionRule = new DistributionRule();
        distributionRule.setAdminId(10);
        distributionRule.setStatus(0);
        Integer integer = distributionRuleMapper.updateRule(distributionRule);
        System.out.println("***********************" + integer);
    }


    @Test
    public void insertdis() {
        DistributionFreeRule distributionFreeRule = new DistributionFreeRule();
        ArrayList<DistributionFreeRule> distributionFreeRules = new ArrayList<>();
        distributionFreeRule.setDistributionId(1);
        ArrayList<DistributionFree> distributionFrees = new ArrayList<>();

        DistributionFree distributionFree = new DistributionFree();
        distributionFree.setAmount(new BigDecimal(10));
        distributionFree.setCategory(1);
        distributionFree.setType(1);

        DistributionFree distributionFree1 = new DistributionFree();

        distributionFree1.setCategory(2);
        distributionFree1.setType(0);
        distributionFree1.setNumber(100);

        distributionFrees.add(distributionFree);
        distributionFrees.add(distributionFree1);
        String s = JSON.toJSONString(distributionFrees);
        distributionFreeRule.setRule(s);
        distributionFreeRule.setStatus(0);
        distributionFreeRules.add(distributionFreeRule);
        Integer integer = distributionFreeRuleMapper.insertFreeRule(distributionFreeRules);
        System.out.println("**********" + integer);
    }


    @Test
    public void select() {
        List<DistributionFreeRule> distributionFreeRules = distributionFreeRuleMapper.queryById(1);
        for (DistributionFreeRule distributionFreeRule : distributionFreeRules) {
            String rule = distributionFreeRule.getRule();
            List<DistributionFree> distributionFrees = JSONObject.parseArray(rule, DistributionFree.class);
            for (DistributionFree distributionFree : distributionFrees) {
                System.out.println("--------------" + distributionFree.toString());
            }
        }
    }

    @Test
    public void updatefree() {
  /*      DistributionFreeRule distributionFreeRule = new DistributionFreeRule();

        distributionFreeRule.setId(1);
        distributionFreeRule.setStatus(1);
        Integer integer = distributionFreeRuleMapper.updateFreeRule(distributionFreeRule);
        System.out.println("---------" + integer);
*/
        distributionRuleMapper.selectByAdminId(10);


    }

    private Integer df(DistributionFree distributionFree) {

        Integer category = distributionFree.getCategory();
        Integer type = distributionFree.getType();
        if (category.intValue() == 0) {

        }
        return 0;

    }



    @Test
    public void major() {
        MajorPriceInput majorPriceInput = new MajorPriceInput();
        majorPriceInput.setAdminId(4);
        List<MajorPriceInput> majorPriceInputs = majorPriceMapper.selectMajorPrice(majorPriceInput);
        for (MajorPriceInput priceInput : majorPriceInputs) {
            System.out.println(priceInput.getSku() + "************" + priceInput.getCharacters());
        }
    }

    @Test
    public void insertVO() {
        ArrayList<DistributionRuleVO> distributionRuleVOS = new ArrayList<>();
        DistributionRuleVO distributionRuleVO = new DistributionRuleVO();
        distributionRuleVO.setAdminId(1);
        distributionRuleVO.setStatus(0);
        distributionRuleVO.setDeliveryFee(new BigDecimal(10));
        distributionRuleVO.setId(123);
        distributionRuleVOS.add(distributionRuleVO);
        Integer integer = distributionRuleMapper.insertBathRuleVO(distributionRuleVOS);
        System.out.println(integer + "****************");
        for (DistributionRuleVO ruleVO : distributionRuleVOS) {
            System.out.println("---------------" + ruleVO.getId());
        }
    }

    @Test
    public void adminvo() {
        List<DistributionRuleVO> distributionRuleVOS = distributionRuleMapper.queryListRule(4);
        for (DistributionRuleVO distributionRuleVO : distributionRuleVOS) {
            List<DistributionFreeRule> distributionFreeRuleS = distributionRuleVO.getDistributionFreeRules();
            for (DistributionFreeRule distributionFreeRule : distributionFreeRuleS) {
                System.out.println(distributionFreeRule.getRule());
            }
        }

    }


    @Test
    public void suanfa() {

        List<DistributionFreeRule> distributionFreeRules = new ArrayList<>();
        List<DistributionFreeVO> distributionFreeRuleVOS = new ArrayList<>();
        List<OrderItem> orderItems = new ArrayList<>();

        HashMap<Integer, Boolean> hashMap = new HashMap<>();

        for (DistributionFreeRule distributionFreeRule : distributionFreeRules) {
            String rule = distributionFreeRule.getRule();
            List<DistributionFreeVO> distributionFreeVOS = JSON.parseArray(rule, DistributionFreeVO.class);
            for (DistributionFreeVO distributionFreeVO : distributionFreeVOS) {
                distributionFreeVO.setDistributionId(distributionFreeRule.getDistributionId());
            }
            distributionFreeRuleVOS.addAll(distributionFreeVOS);
        }

        for (DistributionFreeVO distributionFreeRuleVO : distributionFreeRuleVOS) {

            //是否满足免配送费
            Boolean isDistributionFree = false;
            //金额
            BigDecimal totalFee = BigDecimal.ZERO;
            //件数
            Integer totalAmount = 0;

            for (OrderItem orderItem : orderItems) {

                //金额
                if (Objects.equals(distributionFreeRuleVO.getType(), 0)) {

                    BigDecimal fee = as(distributionFreeRuleVO.getCategory(), orderItem);

                    if (totalFee.compareTo(distributionFreeRuleVO.getAmount()) >= 0) {
                        isDistributionFree = true;
                        break;
                    }
                    totalFee = totalFee.add(fee);

                }
                //件数
                else if (Objects.equals(distributionFreeRuleVO.getType(), 1)) {

                    Integer amount = ad(distributionFreeRuleVO.getCategory(), orderItem);

                    if (totalAmount >= distributionFreeRuleVO.getNumber()) {
                        isDistributionFree = true;
                        break;
                    }
                    totalAmount += amount;
                }
            }

            hashMap.put(distributionFreeRuleVO.getDistributionId(), isDistributionFree);
        }

        boolean b = hashMap.containsValue(true);

    }

    //金额
    private BigDecimal as(Integer a, OrderItem orderItem) {

        BigDecimal multiply = new BigDecimal(orderItem.getAmount()).multiply(orderItem.getPrice());
        //全部金额
        if (Objects.equals(a, 0)) {

            return multiply;
        }
        //代仓
        else if (Objects.equals(a, 1) && Objects.equals(orderItem.getStatus(), 1)) {
            return multiply;
        }
        //自营
        else if (Objects.equals(a, 2) && Objects.equals(orderItem.getStatus(), 2)) {
            return multiply;
        }
        //乳制品
        else if (Objects.equals(a, 3) && Objects.equals(orderItem.getCategoryId(), 3)) {
            return multiply;
        }
        //非乳制品
        else if (Objects.equals(a, 4) && Objects.equals(orderItem.getCategoryId(), 4)) {
            return multiply;
        }
        return BigDecimal.ZERO;

    }

    //件数
    private Integer ad(Integer a, OrderItem orderItem) {

        //件数
        Integer amount = orderItem.getAmount();

        //全部
        if (Objects.equals(a, 0)) {
            return amount;
        }
        //代仓
        else if (Objects.equals(a, 1) && Objects.equals(orderItem.getStatus(), 1)) {
            return amount;
        }
        //自营
        else if (Objects.equals(a, 2) && Objects.equals(orderItem.getStatus(), 2)) {
            return amount;
        }
        //乳制品
        else if (Objects.equals(a, 3) && Objects.equals(orderItem.getCategoryId(), 3)) {
            return amount;
        }
        //非乳制品
        else if (Objects.equals(a, 4) && Objects.equals(orderItem.getCategoryId(), 4)) {
            return amount;
        }
        return 0;

    }

    @Test
    public void sdf() {
        AjaxResult ajaxResult = majorMerchantDetail.majorMerchantDetail(new MajorMerchantQuery());
    }

    @Test
    public void fg() {
        AdminVO adminVO = new AdminVO();
        adminVO.setType(2);
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(2);
        List<DistributionRuleVO> distributionRuleVOS = distributionRuleMapper.queryRuleList(integers);
        for (DistributionRuleVO distributionRuleVO : distributionRuleVOS) {

            List<DistributionFreeRule> distributionFreeRules = distributionRuleVO.getDistributionFreeRules();
            String s = JSON.toJSONString(distributionFreeRules);
            System.out.println(s);

            List<DistributionFreeRule> distributionFreeRules1 = JSON.parseArray(s, DistributionFreeRule.class);
            System.out.println(distributionFreeRules1.toString());
            char[] chars = s.toCharArray();

            for (int i = 1; i < chars.length - 1; i++) {
                if (Objects.equals(chars[i], '[')) {
                    chars[i - 1] = '\\';
                }
                if (Objects.equals(chars[i], ']')) {
                    chars[i + 1] = '\\';
                    break;
                }
            }
            String s2 = String.valueOf(chars);
            s2.replaceAll("\"", "");
            String s3 = StringEscapeUtils.unescapeJavaScript(s2);
            System.out.println("***************" + s3);
        }


    }

    @Test
    public void df() {

    }

    @Test
    public void dg() {
        ArrayList<Integer> list = new ArrayList<>();
        list.add(21);

        List<DistributionRuleVO> distributionRuleVO = distributionRuleMapper.queryRuleList(list);

        if (!CollectionUtils.isEmpty(distributionRuleVO)) {
            DistributionRuleVO distributionRuleVO1 = distributionRuleVO.get(0);
            List<DistributionFreeRule> distributionFreeRules = distributionRuleVO1.getDistributionFreeRules();
            int size = distributionFreeRules.size();
            System.out.println(size);
            String rule = JSON.toJSONString(distributionRuleVO);

            char[] chars = rule.toCharArray();

            for (int i = 1; i < chars.length - 1; i++) {
                if (Objects.equals(chars[i], '[')) {
                    if (Objects.equals(chars[i - 1], '"')) {
                        chars[i - 1] = '\\';
                    }

                }
                if (Objects.equals(chars[i], ']')) {
                    if (Objects.equals(chars[i + 1], '"')) {
                        chars[i + 1] = '\\';
                    }

                }
            }
            String rules = String.valueOf(chars);
            String s = StringEscapeUtils.unescapeJavaScript(rules);
            System.out.println(s);

        }
    }

    @Test
    public void sdsf(){
//        AjaxResult ajaxResult = inventoryService.selectSkus(null, 0,null);
//        ajaxResult.getData();

    }

    @Test
    public void qw(){
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(2);
            List<DistributionRuleVO> distributionRuleVOS = distributionRuleMapper.queryRuleList(integers);
        for (DistributionRuleVO distributionRuleVO : distributionRuleVOS) {
            System.out.println(distributionRuleVO.getDistributionFreeRules().size());
        }
    }

    @Test
    public void kl(){
        MajorAreaStoreVO majorAreaStoreVO = new MajorAreaStoreVO();
        majorAreaStoreVO.setAdminId(277);
        areaStoreMapper.queryMajorAreaStoreVO(majorAreaStoreVO);
    }



    @Test
    public void  jk(){
        List<AllocationOrderItemEntityVO> stockAllocationItemVOS = oldAllocationOrderItemMapper.selectWithDetail("2019071501026418");
        for (AllocationOrderItemEntityVO stockAllocationItemVO : stockAllocationItemVOS) {
            System.out.println(stockAllocationItemVO.getNameRemakes());
        }
    }

    @Test
    public void st(){
        stockTaskItemMapper.selectByStockTaskId(1);
    }
    
    @Test
    public void pc(){
        List<StoreRecordVO> storeRecordVOS = storeRecordMapper.selectQuantitySum(null);
        for (StoreRecordVO storeRecordVO : storeRecordVOS) {
            System.out.println(storeRecordVO.getNameRemakes());
        }
    }

    @Test
    public void sp(){
        List<SkuMappingVO> skuMappingVOS = skuMappingMapper.selectList(null);
        for (SkuMappingVO skuMappingVO : skuMappingVOS) {
            System.out.println(skuMappingVO.getNameRemakes());
        }
    }

    @Test
    public void hg(){
        List<OrderItemVO> orderItemVOS = orderItemMapper.selectOrderItemByOrderNo("01155911963265538");
        for (OrderItemVO orderItemVO : orderItemVOS) {
            System.out.println(orderItemVO.getSkuType() );
        }
    }

    @Test
    public void mj(){
        List<PurchasesPlanResultVO> purchasesPlanResultVOS = purchasesPlanMapper.selectVOByPurchasesNo("2019071801313001");
        for (PurchasesPlanResultVO purchasesPlanResultVO : purchasesPlanResultVOS) {
            System.out.println(purchasesPlanResultVO.getNameRemakes());
        }
    }

    @Test
    public void as(){
        List<SKUVO> skuvos = inventoryMapper.selectSkusByType(1, 2);
        for (SKUVO skuvo : skuvos) {
            System.out.println(skuvo.getNameRemakes());
        }
    }

    @Test
    public  void dfh(){
        LocalDate date = LocalDate.now();
        LocalDateTime now1 = date.atTime(LocalTime.now());
        Integer selectLast = merchantLifecycleMapper.selectLast(11731L);
    }
    @Test
      public void ev(){
        MerchantVO merchant = new MerchantVO();
        merchant.setIslock((byte)1);
        AjaxResult result = merchantService.selectMerchantList(merchant);
        result.getData();
    }

}

