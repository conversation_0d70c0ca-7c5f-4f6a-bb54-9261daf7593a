package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.model.bo.GoodsCodeBO;
import net.summerfarm.model.domain.SkuBatchCode;
import net.summerfarm.model.vo.SkuBatchCodeVO;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/12/15  17:32
 */
public class BatchCodeServiceTest extends BaseTest {

    @Resource
    SkuBatchCodeService skuBatchCodeService;

    @Resource
    OrderService orderService;

    @Test
    public void save(){

        SkuBatchCode skuBatchCode = new SkuBatchCode();
        skuBatchCode.setSku("123124");
        skuBatchCode.setPurchaseNo("123123234235");
        skuBatchCode.setProductionDate(LocalDate.now());
        skuBatchCodeService.createBatchCode(skuBatchCode,1);
    }

    @Test
    public void selectGoodsCodeBOMsg(){

        SkuBatchCodeVO skuBatchCodeVO = new SkuBatchCodeVO();
        skuBatchCodeVO.setSku("873338444121");
        skuBatchCodeVO.setPurchaseNo("2021122112325015");
        skuBatchCodeVO.setProductionDate(LocalDate.now().plusDays(1L));
        skuBatchCodeVO.setCanPrintNumber(3);
        skuBatchCodeVO.setWarehouseNo(10);
        skuBatchCodeService.selectGoodsCodeBOMsg(skuBatchCodeVO);
    }
    @Test
    public void querySkuBatchCode(){

        skuBatchCodeService.querySkuBatchCode("000000006S00011");
    }

    @Test
    public void selectOrderDetails(){
        orderService.selectOrderDetails("01164025579741684");
    }
}
