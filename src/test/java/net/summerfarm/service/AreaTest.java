package net.summerfarm.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.PreCutOffOrderUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.manage.client.delivery.AfterDeliveryPatchProvider;
import net.summerfarm.manage.client.delivery.dto.req.AfterDeliveryPatchQueryReq;
import net.summerfarm.manage.client.delivery.dto.res.AfterDeliveryPatchDTO;
import net.summerfarm.manage.client.ding.DingProcessProvider;
import net.summerfarm.manage.client.ding.dto.ProcessDetailDTO;
import net.summerfarm.manage.client.ding.dto.ProcessDetailResDTO;
import net.summerfarm.mapper.manage.AdminMapper;
import net.summerfarm.mapper.manage.DeliveryPlanMapper;
import net.summerfarm.model.DTO.tms.LogisticsInformationDTO;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.domain.AdvancePurchase;
import net.summerfarm.model.domain.StockInspectDetail;
import net.summerfarm.model.vo.AdvancePurchaseVO;
//import net.summerfarm.mq.Producer;
import net.summerfarm.model.vo.LargeAreaVO;
import net.summerfarm.model.vo.StockTaskVO;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MallListListener;
import net.summerfarm.service.advancePurchase.AdvancePurchaseService;
import net.summerfarm.service.tms.TmsTrunkService;
import net.summerfarm.task.ManageScheduleConfig;
import net.summerfarm.tms.base.MqEvent;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.security.Provider;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-10-15
 * @description
 */
public class AreaTest extends BaseTest {
    @Resource
    private PurchasesBackService purchasesBackService;

    @Resource
    private ManageScheduleConfig manageScheduleConfig;

    @Resource
    private AdvancePurchaseService advancePurchaseService;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private AreaService areaService;

    @Resource
    private ProductsService productsService;
    @Resource
    private StockTaskService stockTaskService;
    @Resource
    private LargeAreaService largeAreaService;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private RedisTemplate<String,String> redisTemplate;
    @Resource
    private PreCutOffOrderUtil preCutOffOrderUtil;
    @Resource
    TmsTrunkService tmsTrunkService;
    @Resource
    NormalTaskService normalTaskService;
    @Resource
    private AfterDeliveryPatchProvider afterDeliveryPatchProvider;
    @Resource
    private StockInspectDetailService stockInspectDetailService;

    @Test
    public void processTest(){
       // DubboResponse<List<ProcessDetailResDTO>> processDetail = dingProcessProvider.getProcessDetail(ProcessDetailDTO.builder().bizId(2541L).bizType(5).channel(0).build());
    }


    @Test
    public void addAdvancePurchase(){
        AdvancePurchase advancePurchase = new AdvancePurchase();
        advancePurchase.setSupplierId(2);
        advancePurchase.setStatus(0);
        advancePurchase.setAmount(BigDecimal.valueOf(10000));
        advancePurchase.setUseAmount(BigDecimal.valueOf(10000));
        advancePurchaseService.addAdvancePurchase(advancePurchase);
    }

    @Test
    public void selectList(){
        AdvancePurchaseVO advancePurchaseVO = new AdvancePurchaseVO();
        AjaxResult result = advancePurchaseService.selectAdvancePurchase(1, 10, advancePurchaseVO);
        result.getData();
    }
    @Test
    public void selectDetail(){
        AjaxResult result = advancePurchaseService.selectAdvanceDetailById(1);
        result.getData();
    }

    @Test
    public void audit(){
        AdvancePurchaseVO advancePurchaseVO = new AdvancePurchaseVO();
        advancePurchaseVO.setId(3);
        advancePurchaseVO.setAuditStatus(3);
        advancePurchaseService.auditAdvancePurchase(advancePurchaseVO);
    }

    @Test
    public void again(){
        AdvancePurchaseVO advancePurchaseVO = new AdvancePurchaseVO();
        advancePurchaseVO.setId(3);
        advancePurchaseVO.setAmount(BigDecimal.valueOf(10000));
        advancePurchaseService.updateAdvancePurchase(advancePurchaseVO);
    }

    @Test
    public void add(){
        List<LargeAreaVO> largeAreaVOS = largeAreaService.selectAll(null);
        System.out.println(1);
    }

    @Test
    public void addf(){

        Admin admin = adminMapper.selectByPrimaryKey(1);
        admin.getDisabled();
    }

    @Resource
    private DingTalkService dingTalkService;

    @Resource
    private PriceAdjustService priceAdjustService;
    @Test
    public void ffff(){
        priceAdjustService.selectLeftInStock(1,"873338444121");
    }

    @Test
    public void supportAddOrder(){
        System.out.println("ssss");

//        ChangeFence changeFence = new ChangeFence();
//        changeFence.setType(1);
//        changeFence.setFenceId(253);
//        changeFence.setAreaNo(1001);
//        changeFence.setChangeToFenceId(286);
//        changeFence.setChangeAcmId("1033");
//        areaService.readyChangeStore(changeFence);
    }

    @Resource
    private MallListListener mallListListener;
    @Test
    public void test(){
        MQData mqData = JSONObject.parseObject("{\"data\":\"{\"areaNo\":1001,\"skus\":[\"863146110007\"]}\",\"type\":\"PURCHASES_CONFIG\"}", MQData.class);
      //  mallListListener.onMessage(mqData);
    }
    @Test
    public void dp(){
       //areaService.handleOrder(1001);
        tmsTrunkService.autoCreateDistOrder(168180);
    }

    /**
     * 自动货损
     */
    @Test
    public void dpDamage(){
        //areaService.handleOrder(1001);
        stockTaskService.saasInStockTask();
    }




    /*
    * 定时任务
    * */
//    @Test
//    public void autoMail(){
//        JSONObject msgJson = new JSONObject();
//        msgJson.put("outerOrderId", String.valueOf(163945));
//        msgJson.put("source", 104);
//
//        MqEvent mqEvent = new MqEvent(MqConstants.Event.WAREHOUSE_TASK_COMPLETE, msgJson);
//        producer.sendDataToQueue("tms-list", JSON.toJSONString(mqEvent));
//    }

    @Test
    public void afsf(){
        AfterDeliveryPatchQueryReq build =
                AfterDeliveryPatchQueryReq.builder().orderNo("01167057944850437").storeNo(1).type(19).deliveryTime(LocalDate.now().plusDays(1)).build();
        afterDeliveryPatchProvider.queryByOrderNo(build);
    }

    @Resource
    private MerchantLifecycleService merchantLifecycleService;

    @Test
    public void testsss(){
        merchantLifecycleService.timingOrderLockSomeTime("22:00:00");
    }

}
