package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.mapper.manage.MerchantReviewRecordMapper;
import net.summerfarm.mapper.manage.SampleApplyMapper;
import net.summerfarm.model.domain.SampleApply;
import net.summerfarm.service.splitArea.SplitAreaService;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Create 2020-12-22
 */
public class SampleApplyTest extends BaseTest {

    @Resource
    SampleApplyMapper sampleApplyMapper;

    @Resource
    SampleApplyService sampleApplyService;

    @Resource
    StockTaskService stockTaskService;

    @Resource
    private MerchantReviewRecordMapper merchantReviewRecordMapper;

    @Resource
    private SplitAreaService splitAreaService;
    @Test
    public void test1(){
        SampleApply sa = sampleApplyMapper.selectSampleById(65);
        System.out.println(sa);
    }

    @Test
    public void test2() {
        Integer id = 106;
        sampleApplyService.cancelSampleApply(id);
    }

    @Test
    public void test3(){
        splitAreaService.handleSplitArea();
    }


    @Test
    public void test4(){

        sampleApplyMapper.updateSampleByMId(6L,1449L,null);

    }
}
