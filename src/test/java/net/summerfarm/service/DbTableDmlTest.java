package net.summerfarm.service;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseTest;
import net.summerfarm.factory.DbTableDmlFactory;
import net.summerfarm.mq.BinLogListener;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.table.DbTableDml;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Description
 * @date 2022/4/22 11:17
 */
public class DbTableDmlTest extends BaseTest {

    @Resource
    private ApplicationContext applicationContext;

    @Test
    public void dmlImplTest(){

    }
}
