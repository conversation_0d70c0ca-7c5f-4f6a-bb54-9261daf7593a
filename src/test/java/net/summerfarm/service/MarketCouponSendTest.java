package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.MarketCouponSendDetailStatusEnum;
import net.summerfarm.enums.MarketCouponSendStatusEnum;
import net.summerfarm.mapper.manage.MarketCouponSendDetailMapper;
import net.summerfarm.mapper.manage.MarketCouponSendMapper;
import net.summerfarm.model.domain.MarketCouponSend;
import net.summerfarm.model.input.MarketCouponSendQuery;
import org.junit.Test;

import javax.annotation.Resource;

public class MarketCouponSendTest extends BaseTest {

    @Resource
    private MarketCouponSendService marketCouponSendService;
    @Resource
    private MarketCouponSendDetailMapper couponSendDetailMapper;
    @Resource
    private MarketCouponSendMapper marketCouponSendMapper;

    @Test
    public void selectMerchantCoupon() {
        marketCouponSendService.sendMerchantCouponAndUpdateStatus(3L,"1006", MarketCouponSendStatusEnum.AUDIT_PASS);
    }

    @Test
    public void select() {
        MarketCouponSendQuery couponSendQuery = new MarketCouponSendQuery();
        couponSendQuery.setCouponId(16923L);
        marketCouponSendService.select(1, 10, couponSendQuery);
    }

    @Test
    public void aa(){
        couponSendDetailMapper.updateStatus(87L, MarketCouponSendDetailStatusEnum.HAD_SEND.getStatus());
        MarketCouponSend marketCouponSend = marketCouponSendMapper.selectByPrimaryKey(90L);
    }

}
