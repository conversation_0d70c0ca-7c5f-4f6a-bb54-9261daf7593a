package net.summerfarm.service;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiProcessinstanceListidsRequest;
import com.dingtalk.api.response.OapiProcessinstanceListidsResponse;
import com.taobao.api.ApiException;
import net.summerfarm.BaseTest;
import net.summerfarm.common.util.DingTalkUtils;
import net.summerfarm.contexts.Global;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 钉钉接入服务测试
 * @Date: 2020/11/27 12:45
 * @Author: <EMAIL>
 */

public class DingTalkServiceTest extends BaseTest {
    @Resource
    DingTalkService dingTalkService;

    @Test
    public void getProcessId() throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/listids");
        OapiProcessinstanceListidsRequest req = new OapiProcessinstanceListidsRequest();
        req.setProcessCode(Global.ACCOUNT_RECEIVABLE);
        req.setStartTime(1606436361000L);
        req.setSize(20L);
        req.setCursor(0L);
        String accessToken = DingTalkUtils.init().getToken();
        OapiProcessinstanceListidsResponse response = client.execute(req,accessToken);
        List<String> list = response.getResult().getList();
        for (String s : list) {
            System.out.println(s);
        }
    }
    @Test
    public void callBackTest(){
        //从执行后上面取
        String processId = "00c95659-99eb-44d6-bc24-e7c976aad7b1";
        dingTalkService.rechargeHandle(processId);
    }

    @Test
    public void getDeptMsg(){
        dingTalkService.getDeptMsg();
    }
}


