package net.summerfarm.service;

import com.alibaba.fastjson.JSON;
import com.cosfo.summerfarm.model.input.SummerfarmStockInput;
import net.summerfarm.BaseTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mapper.manage.AfterSaleDeliveryPathMapper;
import net.summerfarm.mapper.manage.DeliveryPathMapper;
import net.summerfarm.model.domain.DeliveryPath;
import net.summerfarm.model.input.OrderReq;
import net.summerfarm.model.vo.ClosingOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/12/11  11:33
 */
@RunWith(SpringJUnit4ClassRunner.class)         //表示继承了SpringJUnit4ClassRunner类
@ContextConfiguration
@ActiveProfiles("dev")
public class OrderServiceTest extends BaseTest {

    @Resource
    private OrderService orderService;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Resource
    private DeliveryPathMapper deliveryPathMapper;
    @Resource
    TmsTaskService tmsTaskService;
    @Resource
    AreaStoreService areaStoreService;
    @Resource
    private StockTaskService stockTaskService;

    @Test
    public void delivertStart(){
//        orderService.deliveryPathStart(1, LocalDate.of(2021,8,8),LocalDate.of(2021,8,9));
    }

    @Test
    public  void sterSaleDeliveryPath(){
        List<ClosingOrder> afterSale = afterSaleDeliveryPathMapper.closingOrderByAfterSale(LocalDate.of(2020, 12, 12), null, 1);
        afterSale.size();
    }
    @Test
    public  void wer(){
        DeliveryPath deliveryPath = new DeliveryPath();
        deliveryPath.setPath("B");
        deliveryPath.setPathStatus(2);
        deliveryPath.setDeliveryTime(LocalDate.now().plusDays(1));
        deliveryPath.setStoreNo(1);
        int status = deliveryPathMapper.updateStatusByPath(deliveryPath);
        System.out.println(status);
    }

    @Test
    public void sdf(){
        SummerfarmStockInput input = new SummerfarmStockInput();
        List<Long> list = new ArrayList<>();
        list.add(299L);
        input.setSkuIdList(list);
        input.setCity("辽宁市");
        input.setArea("沈阳区");
        //areaStoreService.queryAreaStoreQuantityList(input);
//        afterSaleDeliveryPathMapper.updateMerchantId(1L,123L) ;
    }

    @Test
    public void selectOrderList() {
        orderService.selectOrderDetails("02166427552276257");

    }

    @Test
    public void testautoDamageTask() {
        stockTaskService.autoDamageTask(LocalDate.now());

    }

}
