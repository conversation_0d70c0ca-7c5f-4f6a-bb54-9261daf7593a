package net.summerfarm.service;

import net.summerfarm.BaseTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class PurchasesServiceTest extends BaseTest {

    @Resource
    PurchasesService purchasesService;

    @Resource
    private JavaMailSenderImpl mailSender;

    @Test
    public void calcPurchaseQuantity() {
        purchasesService.purchaseAdvanceMonitor();
    }

    @Test
    public void oneClickDelivery() {
        purchasesService.oneClickDelivery("20220818181239006");
    }
    @Test
    public void batchSendExpireSkuDingTalkMsg() {
        purchasesService.batchSendExpireSkuDingTalkMsg();
    }


}
