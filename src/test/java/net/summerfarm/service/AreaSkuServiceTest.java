package net.summerfarm.service;

import com.alibaba.fastjson.JSON;
import net.summerfarm.BaseTest;
import net.summerfarm.enums.OpenSaleEnum;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.AreaSkuMapper;
import net.summerfarm.mapper.manage.OrdersMapper;
import net.summerfarm.model.domain.AreaStore;
import net.summerfarm.model.domain.OrderItem;
import org.junit.Test;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-07-17
 * @description
 */
public class AreaSkuServiceTest extends BaseTest {
    @Resource
    private AreaSkuService areaSkuService;
    @Resource
    private PriceAdjustService priceAdjustService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    AreaStoreService areaStoreService;

    @Test
    public void syncQualityDate(){
        areaSkuService.syncQualityDate();
    }

    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private AreaMapper areaMapper;
    @Test
    public void test(){
       redisTemplate.opsForZSet().add("zzz","dasdas",100);
       redisTemplate.opsForZSet().add("zzz","saasda",100);
       redisTemplate.opsForZSet().add("zzz","ggggsd",100);
       redisTemplate.opsForZSet().add("zzz","fffffq",100);
       redisTemplate.opsForZSet().add("zzz","wewrtr",100);
    }

    @Test
    public void testSort() {
        areaSkuService.autoCalcSort();
    }

    @Test
    public void task(){
        areaSkuService.syncQualityDate();
    }

    @Test
    public void closeSale(){
        areaSkuService.insertAreaSku(29301,1002);
    }

    @Test
    public void openSea(){
        AreaStore areaStore = new AreaStore(1,"858063364315");
        areaStore.setOnlineQuantity(1);
        areaStoreService.openSale(areaStore, OpenSaleEnum.STOCK_TURN_ON.ordinal());
    }

    @Test
    public void selectStoreSaleAmountNew(){
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime startTime = endTime.minusDays(1);
        Integer largeAreaNo = 1;
        List<OrderItem> orderItemList = ordersMapper.selectStoreSaleAmount(largeAreaNo, startTime, endTime);
        List<OrderItem> orderItemNewList = ordersMapper.selectStoreSaleAmountNew(largeAreaNo, startTime, endTime);

        orderItemList.sort(Comparator.comparing(OrderItem::getSku));
        orderItemNewList.sort(Comparator.comparing(OrderItem::getSku));


        System.err.println(JSON.toJSONString(orderItemList));
        System.err.println(JSON.toJSONString(orderItemNewList));
        System.err.println(JSON.toJSONString(orderItemList).equals(JSON.toJSONString(orderItemNewList)));
    }
}
