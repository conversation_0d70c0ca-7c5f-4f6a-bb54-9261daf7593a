package net.summerfarm.service;

import net.summerfarm.BaseTest;
import net.summerfarm.mapper.manage.AfterSaleOrderMapper;
import net.summerfarm.mapper.manage.OrdersMapper;
import net.summerfarm.mapper.manage.StockTaskMapper;
import net.summerfarm.model.vo.OrderItemVO;
import net.summerfarm.model.vo.OrderVO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2019/8/16  2:23 PM
 */

public class DataReportServiceTest extends BaseTest {

    @Resource
    DataReportService dataReportService;
    @Resource
    StockTaskMapper stockTaskMapper;
    @Resource
    ReportService reportService;
    @Resource
    OrdersMapper ordersMapper;


    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;


    @Test
    public void dfdf(){

       //dataReportService.autoProfit();
    }
    @Test
    public void dfd(){
        //dataReportService.autoSellOutEmail();
        OrderVO orderVO = new OrderVO();
        orderVO.setMajorAdminId(277);
        List<OrderItemVO> orderItemVOS = ordersMapper.selectMajorOrderItem(orderVO);
        int a = orderItemVOS.size();

    }
}
