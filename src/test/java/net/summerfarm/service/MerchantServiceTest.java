package net.summerfarm.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import net.summerfarm.BaseTest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.mapper.manage.AdminDataPermissionMapper;
import net.summerfarm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.mapper.manage.MerchantMapper;
import net.summerfarm.mapper.manage.SampleSkuMapper;
import net.summerfarm.model.domain.AdminDataPermission;
import net.summerfarm.model.domain.Area;
import net.summerfarm.model.domain.Merchant;
import net.summerfarm.model.domain.SampleSku;
import net.summerfarm.model.vo.FollowWhiteListVO;
import net.summerfarm.model.vo.MerchantCouponVO;
import net.summerfarm.model.vo.MerchantVO;
import net.summerfarm.mq.DtsModel;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * MerchantService测试类
 * @author: <EMAIL>
 * @Date: 2016/7/19
 */
public class MerchantServiceTest extends BaseTest{

    private static final Logger logger= LoggerFactory.getLogger(MerchantServiceTest.class);

    @Resource
    private MerchantService merchantService;

    @Resource
    private OrderService orderService;

    @Resource
    private MerchantCouponService merchantCouponService;
    @Resource
    private FollowWhiteListService followWhiteListService;
    @Resource
    private ReportService reportService;
    @Resource
    private FollowUpRelationService followUpRelationService;
    @Resource
    private FollowUpRelationMapper followUpRelationMapper;

    @Resource
    private SampleSkuMapper sampleSkuMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Test
    public void selectMerchantCoupon() {
        MerchantCouponVO selectKeys = new MerchantCouponVO();

        merchantCouponService.select(1,10,selectKeys);
    }

    /**
     * 查询测试
     */
    @Test
    public void select(){
//        Merchant selectKeys=new Merchant();
//        selectKeys.setPhone("214748");
//        selectKeys.setmId(1L);
////        AjaxResult ajaxResult = merchantService.select(1,5,null);
////        PageInfo<Merchant> page= (PageInfo<Merchant>) ajaxResult.getData();
//        logger.info("查询出结果：{}",page);
//        for(Merchant merchant:page.getList()){
//            logger.info(merchant.toString());
//        }
    }

    @Test
    public void distrubutionList() throws IOException {
        Date start =DateUtils.localDateTime2Date(LocalDateTime.of(2016,10,24,00,00,00));
        Date end =DateUtils.localDateTime2Date(LocalDateTime.of(2016,10,26,00,00,00));
//        orderService.distributionList(start,end,response);
    }

    @Resource
    private AdminDataPermissionMapper adminDataPermissionMapper;
    @Test
    public void testReview(){
        List<AdminDataPermission> adminDataPermissions = adminDataPermissionMapper.selectByAdminId(178);
        System.out.println(adminDataPermissions);
    }

    
    @Test
    public void selectMerchant(){
        MerchantVO merchantVO = new MerchantVO();
        merchantVO.setAreaNo(1001);
        AjaxResult ajaxResult = merchantService.selectMerchantList(1, 10, merchantVO);
        Object data = ajaxResult.getData();

    }

    @Test
    public void selectMid(){
        MerchantVO merchantVO = new MerchantVO();
        merchantVO.setmId(11731L);
        AjaxResult ajaxResult = merchantService.selectDetail(merchantVO);
        Object data = ajaxResult.getData();
    }

    @Test
    public void rt(){
        FollowWhiteListVO followWhiteList = new FollowWhiteListVO();
        followWhiteList.setMSize("大客户");
        AjaxResult ajaxResult = followWhiteListService.queryWhiteList(1, 10, followWhiteList);
        ajaxResult.getData();
    }

    @Test
    public void rt1(){
        MerchantVO merchantVO = new MerchantVO();
        AjaxResult ajaxResult = merchantService.queryBDPrivateSea(0,merchantVO);
        ajaxResult.getData();
    }
    @Test
    public void gh(){
        AjaxResult ajaxResult = reportService.BDMerchantMember(1001);
        Object data = ajaxResult.getData();
    }

    @Test
    public void er(){
        ArrayList<SampleSku> sampleSkus = new ArrayList<>();
        SampleSku sampleSku = new SampleSku();
        sampleSku.setPdName("1");
        sampleSku.setSampleId(1);
        sampleSku.setAmount(1);
        sampleSku.setWeight("124");
        sampleSku.setSku("123");
        sampleSkus.add(sampleSku);
        sampleSkuMapper.insertSampleSku(sampleSkus);

    }
    @Autowired
    private QuickBiReportService quickBiReportService;

    @Test
    public void t1(){
        String r = quickBiReportService.getReportUrl("de38675d-22fb-43e1-a149-a025cef49861");
        System.out.println("*********************************************************");
        System.out.println();
        System.out.println(r);
        System.out.println();
        System.out.println("*********************************************************");
    }

    @Test
    public void queryCluePool(){
        Merchant merchant = new Merchant();
        merchant.setMname("好街坊");
        merchant.setPoiNote("31.22986,121.46924");
        merchantService.queryCluePool(merchant);

    }

    @Test
    public void matchAreaNo(){
        MerchantVO selectKeys = new MerchantVO();
        selectKeys.setMname("西猫咖啡猫咖");
        List<MerchantVO> merchantVOs = merchantMapper.select(selectKeys);
        MerchantVO merchantVO = merchantVOs.get(0);
        List<Area> areas = new ArrayList<>();
        Area area = new Area();
        area.setMapSection(merchantVO.getMapSection());
        area.setStatus(true);
        area.setAreaNo(merchantVO.getAreaNo());
        areas.add(area);
        Integer areaNo = Global.matchAreaNo(merchantVO.getProvince(), merchantVO.getCity(), merchantVO.getArea(), areas);
        System.out.println(areaNo);
    }

    @Test
    public void urgeAudit() {
        Long mid = 349858L;
        merchantService.urgeAudit(mid);
    }

    @Test
    public void sendNormalOperationMsg(){
        String dtsModelStr = "{\"data\":[{\"recharge_amount\":\"0.00\",\"merge_time\":null,\"mcontact\":\"茶野tea\",\"invitecode\":\"seeegj\",\"house_number\":\"梅林街道梅丽路9号Apark一个公园L1层15号铺\",\"channel_code\":\"2BeYBR\",\"business_license\":null,\"type\":\"茶饮\",\"examine_type\":\"1\",\"last_order_time\":null,\"province\":\"广东\",\"role_id\":\"6\",\"clue_pool\":\"1\",\"pull_black_operator\":null,\"m_id\":\"349641\",\"area\":\"罗湖区\",\"openid\":\"oiB0c6PiJBFkwcPEOdWMy82jP2i0\",\"sku_show\":null,\"pull_black_remark\":null,\"display_button\":\"1\",\"pre_register_flag\":\"0\",\"company_brand\":null,\"inviter_channel_code\":\"2kzMvs\",\"change_pop\":\"1\",\"area_no\":\"14564\",\"size\":\"单店\",\"phone\":\"***********\",\"poi_note\":\"114.141049,22.562802\",\"login_time\":null,\"admin_id\":null,\"member_integral\":\"0.00\",\"grade\":null,\"pop_view\":\"0\",\"server\":\"1\",\"rank_id\":null,\"other_proof\":null,\"unionid\":\"ok4fcwVBlCItY5zlUzYdeJRBGuk0\",\"cash_amount\":\"0.00\",\"city\":\"深圳市\",\"direct\":null,\"remark\":null,\"mname\":\"茶野tea\",\"trade_area\":null,\"audit_user\":\"2050\",\"register_time\":\"2024-08-01 22:56:15.0\",\"updater\":null,\"audit_time\":\"2024-08-02 09:39:23.0\",\"cash_update_time\":null,\"update_time\":\"2024-08-02 09:39:22.0\",\"operate_status\":\"3\",\"mp_openid\":null,\"first_login_pop\":\"0\",\"merchant_type\":\"普通\",\"door_pic\":\"fe-biz/xm-mall/cl8zbgskwrq\",\"islock\":\"0\",\"address\":\"爱国路3048号御湖峰家园 御湖峰家园A栋\",\"show_price\":\"1\",\"merge_admin\":null,\"trade_group\":null,\"enterprise_scale\":\"未知\",\"shop_sign\":null}],\"database\":\"xianmudb\",\"es\":1722562762000,\"id\":1211294056,\"isDdl\":null,\"mysqlType\":null,\"old\":[{\"clue_pool\":\"0\",\"audit_user\":\"0\",\"audit_time\":\"2024-08-01 22:56:18.0\",\"operate_status\":\"2\",\"enterprise_scale\":null}],\"pkNames\":[\"m_id\"],\"sql\":null,\"sqlType\":null,\"table\":\"merchant\",\"ts\":null,\"type\":\"UPDATE\"}";
        DtsModel dtsModel = JSON.parseObject(dtsModelStr, DtsModel.class);
        merchantService.sendNormalOperationMsg(dtsModel);
    }

//    @Resource
//    private Producer producer;
//    @Test
//    public void ttt(){
//        producer.sendDataToQueue(RocketMqMessageConstant.MANAGE_LIST, 1234);
//    }
}
