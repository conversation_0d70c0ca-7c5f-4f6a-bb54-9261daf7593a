package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.mapper.manage.CouponMapper;
import net.summerfarm.mapper.manage.MerchantSituationQuotaMapper;
import net.summerfarm.model.vo.MerchantSituationVO;
import net.summerfarm.model.vo.MerchantVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2019/8/5  6:37 PM
 */
@RunWith(SpringJUnit4ClassRunner.class)         //表示继承了SpringJUnit4ClassRunner类
@ContextConfiguration
public class MerchantSituationServiceTest {
    @Resource
    private MerchantSituationService merchantSituationService;

    @Resource
    private MerchantService merchantService;

    @Test
    public void queryList(){
        MerchantSituationVO merchantSituationVO = new MerchantSituationVO();
        AjaxResult ajaxResult = merchantSituationService.queryMerchantSituationList(1, 10, merchantSituationVO, "");
        Object data = ajaxResult.getData();
    }

    @Test
    public void examine(){
        MerchantSituationVO merchantSituation = new MerchantSituationVO();
        merchantSituation.setId(1);
        merchantSituation.setExamineId(2);
        merchantSituation.setExamineName("程天");
        merchantSituation.setExamineRemark("er");
        merchantSituation.setCouponAmount(new BigDecimal(10.00));
        merchantSituation.setCreateLocation(1);
        merchantSituation.setCreatorId(1);
        merchantSituation.setStatus(MerchantSituationVO.SITUATION_STATUS_PENDING);
        merchantSituationService.examineMerchantSituation(merchantSituation,1);
    }

    @Test
    public void approval(){
        MerchantSituationVO merchantSituation = new MerchantSituationVO();
        merchantSituation.setId(1);
        merchantSituation.setApprovalId(2);
        merchantSituation.setApprovalName("程天");
        merchantSituation.setApprovalRemark("er");
        merchantSituation.setApprovalTime(LocalDateTime.now());
        merchantSituation.setCouponAmount(new BigDecimal(10.00));
        merchantSituation.setCreateLocation(1);
        merchantSituation.setCreatorId(1);
        merchantSituation.setStatus(MerchantSituationVO.SITUATION_STATUS_CLOSE);
        merchantSituationService.approval(merchantSituation,0);
    }

    @Test
    public void insert(){
        MerchantSituationVO merchantSituationVO = new MerchantSituationVO();
        merchantSituationVO.setStatus(0);
        merchantSituationVO.setCreateLocation(1);
        merchantSituationVO.setMerchantId(11806L);
        String s  = "10.01";
        merchantSituationVO.setThreshold(new BigDecimal(s));
        merchantSituationVO.setCouponAmount(new BigDecimal(10.00));
        merchantSituationVO.setCreatorName("杨珊");
        merchantSituationService.insertMerchantSituation(merchantSituationVO);
    }

    @Test
    public void we(){
        MerchantVO merchantVO = new MerchantVO();
        AjaxResult ajaxResult = merchantService.queryBDPrivateSea(0,merchantVO);
        ajaxResult.getData();
    }

    @Test
    public void  auto(){
//        merchantSituationService.autoTreeDayCloseSituation();
    }
}


