package net.summerfarm.binlog;


import com.alibaba.fastjson.JSON;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.table.impl.AreaSkuDmlImpl;
import net.summerfarm.table.impl.AreaStoreDmlImpl;
import net.summerfarm.table.impl.InterestRateConfigDmlImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class TestEsAreaStore {


    @Resource
    AreaStoreDmlImpl areaStoreDml;

    @Resource
    AreaSkuDmlImpl areaSkuDml;

    @Resource
    InterestRateConfigDmlImpl interestRateConfigDml;

    @Test
    public void test(){
        DtsModel dtsModel = JSON.parseObject("{\"id\":9677,\"database\":\"xianmudb\",\"table\":\"area_store\",\"pkNames\":[\"id\"],\"isDdl\":false,\"type\":\"UPDATE\",\"es\":1658998393000,\"ts\":1658998393224,\"sql\":\"\",\"sqlType\":{\"id\":4,\"area_no\":4,\"sku\":12,\"quantity\":4,\"update_time\":93,\"admin_id\":4,\"lead_time\":4,\"market_price\":3,\"cost_price\":3,\"price_status\":4,\"online_quantity\":4,\"sale_lock_quantity\":4,\"lock_quantity\":4,\"road_quantity\":4,\"sync\":4,\"safe_quantity\":4,\"change\":4,\"status\":4,\"auto_transfer\":-6,\"support_reserved\":4,\"reserve_max_quantity\":4,\"reserve_min_quantity\":4,\"reserve_use_quantity\":4,\"warning_quantity\":4,\"send_warning_flag\":4,\"advance_quantity\":4},\"mysqlType\":{\"id\":\"int(11)\",\"area_no\":\"int(11)\",\"sku\":\"varchar(30)\",\"quantity\":\"int(10) unsigned\",\"update_time\":\"datetime\",\"admin_id\":\"int(11)\",\"lead_time\":\"int(3)\",\"market_price\":\"decimal(10,2)\",\"cost_price\":\"decimal(10,2)\",\"price_status\":\"int(2)\",\"online_quantity\":\"int(10) unsigned\",\"sale_lock_quantity\":\"int(11) unsigned\",\"lock_quantity\":\"int(11) unsigned\",\"road_quantity\":\"int(11) unsigned\",\"sync\":\"int(2)\",\"safe_quantity\":\"int(11)\",\"change\":\"int(11) unsigned\",\"status\":\"int(2)\",\"auto_transfer\":\"tinyint(2)\",\"support_reserved\":\"int(11)\",\"reserve_max_quantity\":\"int(11)\",\"reserve_min_quantity\":\"int(11)\",\"reserve_use_quantity\":\"int(11)\",\"warning_quantity\":\"int(11)\",\"send_warning_flag\":\"int(11)\",\"advance_quantity\":\"int(10) unsigned\"},\"data\":[{\"id\":\"536179\",\"area_no\":\"1\",\"sku\":\"858748027042\",\"quantity\":\"100937\",\"update_time\":\"2022-07-28 16:53:13\",\"admin_id\":null,\"lead_time\":\"0\",\"market_price\":null,\"cost_price\":\"95.0\",\"price_status\":\"5\",\"online_quantity\":\"111630\",\"sale_lock_quantity\":\"308\",\"lock_quantity\":\"308\",\"road_quantity\":\"0\",\"sync\":\"1\",\"safe_quantity\":\"0\",\"change\":\"0\",\"status\":\"0\",\"auto_transfer\":\"0\",\"support_reserved\":\"0\",\"reserve_max_quantity\":\"0\",\"reserve_min_quantity\":\"0\",\"reserve_use_quantity\":\"0\",\"warning_quantity\":null,\"send_warning_flag\":\"0\",\"advance_quantity\":\"0\"}],\"old\":[{\"update_time\":\"2022-07-28 15:35:38\",\"online_quantity\":\"111635\"}]}",
                DtsModel.class);

        areaStoreDml.tableDml(dtsModel);
    }

    @Test
    public void test1(){
        DtsModel dtsModel = JSON.parseObject("{\"id\":21663,\"database\":\"xianmudb\",\"table\":\"area_sku\",\"pkNames\":[\"id\"],\"isDdl\":false,\"type\":\"INSERT\",\"es\":1659065376000,\"ts\":1659065376285,\"sql\":\"\",\"sqlType\":{\"id\":4,\"sku\":12,\"area_no\":4,\"quantity\":4,\"share\":-6,\"original_price\":3,\"price\":3,\"update_time\":93,\"on_sale\":-6,\"add_time\":93,\"priority\":4,\"pd_priority\":4,\"ladder_price\":12,\"limited_quantity\":4,\"sales_mode\":4,\"show\":4,\"info\":12,\"m_type\":4,\"show_advance\":-6,\"advance\":12,\"corner_status\":4,\"corner_open_time\":93,\"open_sale\":4,\"open_sale_time\":93,\"close_sale\":4,\"close_sale_time\":93,\"fix_flag\":4,\"fix_num\":4},\"mysqlType\":{\"id\":\"int(11)\",\"sku\":\"varchar(30)\",\"area_no\":\"int(11)\",\"quantity\":\"int(10)\",\"share\":\"tinyint(1)\",\"original_price\":\"decimal(10,2)\",\"price\":\"decimal(10,2)\",\"update_time\":\"datetime\",\"on_sale\":\"tinyint(1)\",\"add_time\":\"datetime\",\"priority\":\"int(11)\",\"pd_priority\":\"int(11)\",\"ladder_price\":\"varchar(500)\",\"limited_quantity\":\"int(11)\",\"sales_mode\":\"int(2)\",\"show\":\"int(11)\",\"info\":\"varchar(255)\",\"m_type\":\"int(2)\",\"show_advance\":\"tinyint(1)\",\"advance\":\"varchar(50)\",\"corner_status\":\"int(11)\",\"corner_open_time\":\"datetime\",\"open_sale\":\"int(11)\",\"open_sale_time\":\"datetime\",\"close_sale\":\"int(11)\",\"close_sale_time\":\"datetime\",\"fix_flag\":\"int(11)\",\"fix_num\":\"int(11)\"},\"data\":[{\"id\":\"54188\",\"sku\":\"296186311503\",\"area_no\":\"1001\",\"quantity\":\"0\",\"share\":\"1\",\"original_price\":null,\"price\":\"50.0\",\"update_time\":\"2022-07-29 11:29:36\",\"on_sale\":\"1\",\"add_time\":\"2022-07-29 11:29:36\",\"priority\":\"99\",\"pd_priority\":\"99\",\"ladder_price\":\"[]\",\"limited_quantity\":null,\"sales_mode\":\"0\",\"show\":\"1\",\"info\":\"2021.09.10\",\"m_type\":\"0\",\"show_advance\":\"0\",\"advance\":null,\"corner_status\":\"1\",\"corner_open_time\":null,\"open_sale\":\"0\",\"open_sale_time\":null,\"close_sale\":null,\"close_sale_time\":null,\"fix_flag\":\"0\",\"fix_num\":null}],\"old\":null}",
                DtsModel.class);
        areaSkuDml.tableDml(dtsModel);

    }

    @Test
    public void testOnlineQuantity(){
        DtsModel dtsModel = JSON.parseObject("{\"id\":290465,\"database\":\"xianmudb\",\"table\":\"area_store\",\"pkNames\":[\"id\"],\"isDdl\":false,\"type\":\"UPDATE\",\"es\":1661925810000,\"ts\":1661925810843,\"sql\":\"\",\"sqlType\":{\"id\":4,\"area_no\":4,\"sku\":12,\"quantity\":4,\"update_time\":93,\"admin_id\":4,\"lead_time\":4,\"market_price\":3,\"cost_price\":3,\"price_status\":4,\"online_quantity\":4,\"sale_lock_quantity\":4,\"lock_quantity\":4,\"road_quantity\":4,\"sync\":4,\"safe_quantity\":4,\"change\":4,\"status\":4,\"auto_transfer\":-6,\"support_reserved\":4,\"reserve_max_quantity\":4,\"reserve_min_quantity\":4,\"reserve_use_quantity\":4,\"warning_quantity\":4,\"send_warning_flag\":4,\"advance_quantity\":4},\"mysqlType\":{\"id\":\"int(11)\",\"area_no\":\"int(11)\",\"sku\":\"varchar(30)\",\"quantity\":\"int(10) unsigned\",\"update_time\":\"datetime\",\"admin_id\":\"int(11)\",\"lead_time\":\"int(3)\",\"market_price\":\"decimal(10,2)\",\"cost_price\":\"decimal(10,2)\",\"price_status\":\"int(2)\",\"online_quantity\":\"int(10) unsigned\",\"sale_lock_quantity\":\"int(11) unsigned\",\"lock_quantity\":\"int(11) unsigned\",\"road_quantity\":\"int(11) unsigned\",\"sync\":\"int(2)\",\"safe_quantity\":\"int(11)\",\"change\":\"int(11) unsigned\",\"status\":\"int(2)\",\"auto_transfer\":\"tinyint(2)\",\"support_reserved\":\"int(11)\",\"reserve_max_quantity\":\"int(11)\",\"reserve_min_quantity\":\"int(11)\",\"reserve_use_quantity\":\"int(11)\",\"warning_quantity\":\"int(11)\",\"send_warning_flag\":\"int(11)\",\"advance_quantity\":\"int(10) unsigned\"},\"data\":[{\"id\":\"355355\",\"area_no\":\"1\",\"sku\":\"858337600378\",\"quantity\":\"2000\",\"update_time\":\"2022-08-31 14:03:30\",\"admin_id\":null,\"lead_time\":\"0\",\"market_price\":null,\"cost_price\":\"10.0\",\"price_status\":\"0\",\"online_quantity\":\"96378\",\"sale_lock_quantity\":\"163\",\"lock_quantity\":\"37063\",\"road_quantity\":\"110\",\"sync\":\"0\",\"safe_quantity\":\"0\",\"change\":\"0\",\"status\":\"2\",\"auto_transfer\":\"0\",\"support_reserved\":\"0\",\"reserve_max_quantity\":\"0\",\"reserve_min_quantity\":\"0\",\"reserve_use_quantity\":\"0\",\"warning_quantity\":null,\"send_warning_flag\":\"0\",\"advance_quantity\":\"0\"}],\"old\":[{\"update_time\":\"2022-08-31 14:01:47\",\"online_quantity\":\"963878\"}]}", DtsModel.class);
        areaStoreDml.tableDml(dtsModel);
    }
}
