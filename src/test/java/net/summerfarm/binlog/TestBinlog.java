package net.summerfarm.binlog;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.factory.DbTableDmlFactory;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.StockTaskService;
import net.summerfarm.table.DbTableDml;
import net.summerfarm.task.SkuG2gProcessor;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.time.LocalDate;

@ActiveProfiles("dev")
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class TestBinlog {

    @Resource
    private DbTableDmlFactory dbTableDmlFactory;

    @Resource
    private StockTaskService stockTaskService;
    @Resource
    private SkuG2gProcessor skuG2gProcessor;

    @Test
    public void test(){

        try {
            skuG2gProcessor.processResult (null);
        } catch (Exception e) {
            throw new RuntimeException (e);
        }
    }

    @Test
    public void test2(){
        DtsModel dtsModel = JSON.parseObject("{\"id\":431099638,\"database\":\"xianmudb\",\"table\":\"area_store\",\"pkNames\":[\"id\"],\"isDdl\":null,\"type\":\"UPDATE\",\"es\":1662191186000,\"ts\":null,\"sql\":null,\"sqlType\":null,\"mysqlType\":null,\"data\":[{\"advance_quantity\":\"0\",\"support_reserved\":\"0\",\"road_quantity\":\"105\",\"update_time\":\"2022-09-03 15:46:26.0\",\"price_status\":\"0\",\"lead_time\":\"0\",\"id\":\"407569\",\"sku\":\"296485613626\",\"cost_price\":\"139.99\",\"reserve_min_quantity\":\"0\",\"lock_quantity\":\"3\",\"quantity\":\"330\",\"reserve_use_quantity\":\"0\",\"change\":\"0\",\"online_quantity\":\"327\",\"sale_lock_quantity\":\"3\",\"sync\":\"1\",\"safe_quantity\":\"0\",\"area_no\":\"10\",\"warning_quantity\":null,\"send_warning_flag\":\"0\",\"auto_transfer\":\"0\",\"admin_id\":null,\"market_price\":null,\"reserve_max_quantity\":\"0\",\"status\":\"0\"}],\"old\":[{\"quantity\":\"225\"}]}"                , DtsModel.class);
        DbTableDml creator = dbTableDmlFactory.creator(dtsModel.getTable());
        creator.tableDml(dtsModel);
    }

    @Test
    public void testAutoTemporaryTransfer(){
        stockTaskService.autoTemporaryTransfer(LocalDate.now());
    }


    @Test
    public void testAreaSku() {
        DtsModel dtsModel = JSONObject.parseObject("{\"data\":[{\"id\":\"90735\",\"sku\":\"5414266771\",\"area_no\":\"1001\",\"quantity\":\"0\",\"share\":\"1\",\"original_price\":\"0.0\",\"price\":\"45.0\",\"update_time\":\"2024-07-10 01:00:13\",\"on_sale\":\"1\",\"add_time\":\"2021-04-15 19:49:29\",\"priority\":\"99\",\"pd_priority\":\"1\",\"ladder_price\":\"[]\",\"limited_quantity\":\"2\",\"sales_mode\":\"2\",\"show\":\"1\",\"info\":null,\"m_type\":\"0\",\"show_advance\":\"0\",\"advance\":null,\"corner_status\":\"0\",\"corner_open_time\":null,\"open_sale\":\"0\",\"open_sale_time\":null,\"close_sale\":null,\"close_sale_time\":null,\"fix_flag\":\"0\",\"fix_num\":null,\"updater\":null},{\"id\":\"90736\",\"sku\":\"5417115233\",\"area_no\":\"1001\",\"quantity\":\"0\",\"share\":\"1\",\"original_price\":\"0.0\",\"price\":\"45.0\",\"update_time\":\"2024-07-10 01:00:13\",\"on_sale\":\"1\",\"add_time\":\"2021-04-15 19:49:29\",\"priority\":\"99\",\"pd_priority\":\"1\",\"ladder_price\":\"[]\",\"limited_quantity\":\"2\",\"sales_mode\":\"2\",\"show\":\"1\",\"info\":null,\"m_type\":\"0\",\"show_advance\":\"0\",\"advance\":null,\"corner_status\":\"0\",\"corner_open_time\":null,\"open_sale\":\"0\",\"open_sale_time\":null,\"close_sale\":null,\"close_sale_time\":null,\"fix_flag\":\"0\",\"fix_num\":null,\"updater\":null}],\"database\":\"xianmudb\",\"es\":1722052327000,\"id\":181842308,\"isDdl\":false,\"mysqlType\":{\"id\":\"int(11)\",\"sku\":\"varchar(30)\",\"area_no\":\"int(11)\",\"quantity\":\"int(10)\",\"share\":\"tinyint(1)\",\"original_price\":\"decimal(10,2)\",\"price\":\"decimal(10,2)\",\"update_time\":\"datetime\",\"on_sale\":\"tinyint(1)\",\"add_time\":\"datetime\",\"priority\":\"int(11)\",\"pd_priority\":\"int(11)\",\"ladder_price\":\"varchar(500)\",\"limited_quantity\":\"int(11)\",\"sales_mode\":\"int(2)\",\"show\":\"int(11)\",\"info\":\"varchar(255)\",\"m_type\":\"int(2)\",\"show_advance\":\"tinyint(1)\",\"advance\":\"varchar(50)\",\"corner_status\":\"int(11)\",\"corner_open_time\":\"datetime\",\"open_sale\":\"int(11)\",\"open_sale_time\":\"datetime\",\"close_sale\":\"int(11)\",\"close_sale_time\":\"datetime\",\"fix_flag\":\"int(11)\",\"fix_num\":\"int(11)\",\"updater\":\"varchar(64)\"},\"old\":null,\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":4,\"sku\":12,\"area_no\":4,\"quantity\":4,\"share\":-6,\"original_price\":3,\"price\":3,\"update_time\":93,\"on_sale\":-6,\"add_time\":93,\"priority\":4,\"pd_priority\":4,\"ladder_price\":12,\"limited_quantity\":4,\"sales_mode\":4,\"show\":4,\"info\":12,\"m_type\":4,\"show_advance\":-6,\"advance\":12,\"corner_status\":4,\"corner_open_time\":93,\"open_sale\":4,\"open_sale_time\":93,\"close_sale\":4,\"close_sale_time\":93,\"fix_flag\":4,\"fix_num\":4,\"updater\":12},\"table\":\"area_sku\",\"threadId\":143581,\"ts\":1722052327917,\"type\":\"INSERT\"}", DtsModel.class);
//        DtsModel dtsModel = JSONObject.parseObject("{\"data\":[{\"id\":\"2186\",\"sku\":\"5414266771\",\"area_no\":\"1001\",\"quantity\":\"0\",\"share\":\"1\",\"original_price\":\"0.0\",\"price\":\"13.0\",\"update_time\":\"2024-07-27 11:47:30\",\"on_sale\":\"1\",\"add_time\":\"2020-10-09 15:06:28\",\"priority\":\"99\",\"pd_priority\":\"1\",\"ladder_price\":\"[]\",\"limited_quantity\":\"2\",\"sales_mode\":\"2\",\"show\":\"1\",\"info\":\"\",\"m_type\":\"0\",\"show_advance\":\"0\",\"advance\":null,\"corner_status\":\"0\",\"corner_open_time\":null,\"open_sale\":null,\"open_sale_time\":null,\"close_sale\":\"0\",\"close_sale_time\":null,\"fix_flag\":\"0\",\"fix_num\":null,\"updater\":\"测试哈哈\"},{\"id\":\"6465\",\"sku\":\"5414266771\",\"area_no\":\"29251\",\"quantity\":\"0\",\"share\":\"1\",\"original_price\":\"0.0\",\"price\":\"45.0\",\"update_time\":\"2024-07-27 11:47:30\",\"on_sale\":\"1\",\"add_time\":\"2021-04-13 11:12:15\",\"priority\":\"99\",\"pd_priority\":\"1\",\"ladder_price\":\"[]\",\"limited_quantity\":\"2\",\"sales_mode\":\"2\",\"show\":\"1\",\"info\":\"\",\"m_type\":\"0\",\"show_advance\":\"0\",\"advance\":null,\"corner_status\":\"0\",\"corner_open_time\":null,\"open_sale\":null,\"open_sale_time\":null,\"close_sale\":\"0\",\"close_sale_time\":null,\"fix_flag\":\"0\",\"fix_num\":null,\"updater\":\"测试哈哈\"}],\"database\":\"xianmudb\",\"es\":1722052050000,\"id\":181705272,\"isDdl\":false,\"mysqlType\":{\"id\":\"int(11)\",\"sku\":\"varchar(30)\",\"area_no\":\"int(11)\",\"quantity\":\"int(10)\",\"share\":\"tinyint(1)\",\"original_price\":\"decimal(10,2)\",\"price\":\"decimal(10,2)\",\"update_time\":\"datetime\",\"on_sale\":\"tinyint(1)\",\"add_time\":\"datetime\",\"priority\":\"int(11)\",\"pd_priority\":\"int(11)\",\"ladder_price\":\"varchar(500)\",\"limited_quantity\":\"int(11)\",\"sales_mode\":\"int(2)\",\"show\":\"int(11)\",\"info\":\"varchar(255)\",\"m_type\":\"int(2)\",\"show_advance\":\"tinyint(1)\",\"advance\":\"varchar(50)\",\"corner_status\":\"int(11)\",\"corner_open_time\":\"datetime\",\"open_sale\":\"int(11)\",\"open_sale_time\":\"datetime\",\"close_sale\":\"int(11)\",\"close_sale_time\":\"datetime\",\"fix_flag\":\"int(11)\",\"fix_num\":\"int(11)\",\"updater\":\"varchar(64)\"},\"old\":[{\"update_time\":\"2024-07-27 11:43:26\",\"on_sale\":\"1\"},{\"update_time\":\"2024-07-27 11:43:26\",\"on_sale\":\"1\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":4,\"sku\":12,\"area_no\":4,\"quantity\":4,\"share\":-6,\"original_price\":3,\"price\":3,\"update_time\":93,\"on_sale\":-6,\"add_time\":93,\"priority\":4,\"pd_priority\":4,\"ladder_price\":12,\"limited_quantity\":4,\"sales_mode\":4,\"show\":4,\"info\":12,\"m_type\":4,\"show_advance\":-6,\"advance\":12,\"corner_status\":4,\"corner_open_time\":93,\"open_sale\":4,\"open_sale_time\":93,\"close_sale\":4,\"close_sale_time\":93,\"fix_flag\":4,\"fix_num\":4,\"updater\":12},\"table\":\"area_sku\",\"threadId\":143581,\"ts\":1722052050069,\"type\":\"UPDATE\"}", DtsModel.class);
        DbTableDml creator = dbTableDmlFactory.creator(dtsModel.getTable());
        creator.tableDml(dtsModel);
    }
}
