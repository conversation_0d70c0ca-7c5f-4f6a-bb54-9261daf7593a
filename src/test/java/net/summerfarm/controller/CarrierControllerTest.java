package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseControllerTest;
import org.junit.Test;

/**
 * @Classname CarrierControllerTest
 * @Description 承运商测试类
 * @Date 2021/12/30 17:32
 * @Created by hx
 */
public class CarrierControllerTest extends BaseControllerTest {
    @Test
    public void add() throws Exception {
        String params = "{\n" +
                "    \"carrierAccountList\":[{\n" +
                "        \"payType\":1,\n" +
                "        \"account_name\":\"四川蜀味茶韵供应链有限公司\",\n" +
                "        \"account_bank\":\"中国建设银行股份有限公司成都金河支行\",\n" +
                "        \"account_ascription\":\"成都\",\n" +
                "        \"account\":\"51050150860800003710\"\n" +
                "    }],\n" +
                "    \"carrierName\":\"测试1\",\n" +
                "    \"director\":\"haha\",\n" +
                "    \"directorPhone\":\"***********\",\n" +
                "    \"address\":\"江西\",\n" +
                "    \"cooperationAgreement\":\"/url/res\"\n" +
                "}";
        postTest("/carrier/add", JSONObject.toJSONString(params));
    }

    @Test
    public void update() throws Exception {
        String params = "{\n" +
                "    \"carrierAccountList\":[{\n" +
                "        \"payType\":1,\n" +
                "        \"account_name\":\"四川蜀味茶韵供应链有限公司1\",\n" +
                "        \"account_bank\":\"中国建设银行股份有限公司成都金河支行1\",\n" +
                "        \"account_ascription\":\"成都2\",\n" +
                "        \"account\":\"510501508608000037101\"\n" +
                "    }],\n" +
                "    \"carrierName\":\"测试2\",\n" +
                "    \"director\":\"haha1\",\n" +
                "    \"directorPhone\":\"***********\",\n" +
                "    \"address\":\"江西2\",\n" +
                "    \"cooperationAgreement\":\"/url/re1s\",\n" +
                "    \"id\":1\n" +
                "}";
        postTest("/carrier/add", JSONObject.toJSONString(params));
    }


}
