package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

public class SalesDataControllerTest extends BaseControllerTest {
    @Test
    public void selectBdDataByZoneName() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/salesdata/selectBdDataByZoneName/1/10")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
    @Test
    public void selectBdData() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/salesdata/selectBdData")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 根据当前登录账号拥有的城市权限查询BD名单
     * @throws Exception .perform方法异常
     */
    @Test
    public void selectBDByArea() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/salesdata/selectBDByArea")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 销售业务数据页面
     * @throws Exception .perform方法异常
     */
    @Test
    public void salesTeamData() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/salesdata/salesTeamData")
//                        .param("mName","茶百道")
//                        .param("bdId","1032")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 导出特定数据excel文档
     * @throws Exception .perform方法异常
     */
    @Test
    public void orderData() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/salesdata/orderData")
//                        .param("mName","茶百道")
//                        .param("bdId","1032")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 显示客户数据详情
     * @throws Exception .perform方法异常
     */
    @Test
    public void orderDetails() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/salesdata/orderDetails")
                        .param("bdId","1429")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
    /**
     * 发送邮件
     * @throws Exception
     */
    @Test
    public void sendMail() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/salesdata/sendMail")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
