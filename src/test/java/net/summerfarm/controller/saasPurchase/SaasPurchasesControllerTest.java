package net.summerfarm.controller.saasPurchase;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.SaasThreadLocalUtil;
import net.summerfarm.model.DTO.SaasTokenInfoDTO;
import net.summerfarm.module.pms.model.input.PurchasesPaymentExportInput;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @date 2023/3/16 14:41
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
class SaasPurchasesControllerTest {

    @Resource
    private SaasPurchasesController saasPurchasesController;

    @Test
    void paymentExport() {
        SaasTokenInfoDTO saasTokenInfoDTO = new SaasTokenInfoDTO();
        saasTokenInfoDTO.setTenantId(2L);
        SaasThreadLocalUtil.save(saasTokenInfoDTO);

        String text = "{\"startTime\":\"2023-02-15\",\"endTime\":\"2023-03-16\",\"supplierList\":[\"2189\",\"2202\",\"2190\",\"2205\",\"2207\",\"2208\",\"2209\"]}";

        long time1 = System.currentTimeMillis();
        PurchasesPaymentExportInput input = JSON.parseObject(text,PurchasesPaymentExportInput.class);
        saasPurchasesController.paymentExport(input);

        System.out.println("导出时间");
        System.out.println(System.currentTimeMillis() - time1);
        System.out.println("导出结束");
    }
}