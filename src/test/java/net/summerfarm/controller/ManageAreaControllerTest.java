package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.model.input.SaveManageAreaInput;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/28 17:38
 */
public class ManageAreaControllerTest extends BaseControllerTest {

    /**
     *  区域配置列表
     */
    @Test
    public void selectManageArea() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/managearea/selectManageArea/1/10")
//                        .param("areaNo","1001")
                        .param("departmentAdminId","875")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void saveManageArea() throws Exception{
        SaveManageAreaInput s = new SaveManageAreaInput();
        s.setManageAdminId(900);
        s.setParentAdminId(869);
        s.setDepartmentAdminId(875);
        s.setZoneName("赛博坦");
        List<Integer> subCity = new ArrayList<>();
        subCity.add(29296);
        subCity.add(29302);
        subCity.add(29311);
        s.setSubCity(subCity);
        mvc.perform(
                MockMvcRequestBuilders.post("/managearea/saveManageArea")
                        .accept(MediaType.APPLICATION_JSON)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JSONObject.toJSONString(s))
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
