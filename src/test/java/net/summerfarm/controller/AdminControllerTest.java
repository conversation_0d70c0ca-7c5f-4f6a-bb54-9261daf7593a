package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.model.domain.AdminDataPermission;
import net.summerfarm.model.input.MajorPriceInput;
import net.summerfarm.model.vo.AdminVO;
import net.summerfarm.model.vo.RoleVO;
import org.junit.Test;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.web.bind.annotation.RequestMethod;

import java.math.BigDecimal;
import java.util.*;

public class AdminControllerTest extends BaseControllerTest{

    @Test
    public void personalInfo() throws Exception{
        System.out.println(BigDecimal.valueOf(22).setScale(2,BigDecimal.ROUND_UP).compareTo(BigDecimal.valueOf(22).setScale(0,BigDecimal.ROUND_UP)));

        getTest("/admin/personalInfo");
    }

    @Test
    public void specifiedRole() throws Exception{
        JSONObject params = new JSONObject();
        params.put("roleIds","1");
        params.put("dataPermissions", "1,2,3");

        doTest("/admin/16/role", RequestMethod.PUT, null, params.toJSONString(), MediaType.APPLICATION_JSON);
    }
    @Test
    public void addAdmin() throws Exception{
        AdminVO params = new AdminVO();
        params.setRealname("西索sama");
        params.setUsername("<EMAIL>");
        params.setPhone("18767121234");
        params.setPassword("hello1234");
        params.setGender(true);
        params.setDepartment("someplace only we know");
        List<RoleVO> roleVOS = new ArrayList<>();
        RoleVO roleVO = new RoleVO();
        roleVO.setRoleId(1);
        roleVOS.add(roleVO);
        params.setRoleVOs(roleVOS);
        List<AdminDataPermission> dataPermissions = new ArrayList<>();
        AdminDataPermission adminDataPermission = new AdminDataPermission();
        adminDataPermission.setPermissionValue("0");
        adminDataPermission.setPermissionName("全部");
        dataPermissions.add(adminDataPermission);
        params.setDataPermissions(dataPermissions);
        BeanMap paramsMap = BeanMap.create(params);
        paramsMap.put("password","hello1234");
        postTest("/admin", JSONObject.toJSONString(paramsMap));
    }

    @Test
    public void updateMajorPrice()throws Exception{
        Integer[] deleteList={127};
        System.out.println(JSONObject.toJSONString(deleteList));
        List<MajorPriceInput> list = new ArrayList<>();
        MajorPriceInput majorPriceInput = new MajorPriceInput();
        majorPriceInput.setSku("635625201");
        majorPriceInput.setPdName("水果类目10");
        majorPriceInput.setWeight("sss");
        majorPriceInput.setAreaNo(3306);
        majorPriceInput.setAdminId(4);
        majorPriceInput.setPriceType(0);
        majorPriceInput.setPrice(BigDecimal.valueOf(998));
        majorPriceInput.setAreaName("西湖");
        majorPriceInput.setDirect(0);
        list.add(majorPriceInput);
        mvc.perform(
                MockMvcRequestBuilders.post("/admin/update/majorprice")
                .param("list", JSONObject.toJSONString(list))
//                .param("deleteList",JSONObject.toJSONString(deleteList))
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
