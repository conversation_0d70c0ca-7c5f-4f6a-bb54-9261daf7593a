package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

public class SupplierControllerTest extends BaseControllerTest {

    @Test
    public void select() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/purchases/store/record/8/30")
                        .param("storeNo","1")
                        .param("onSale","true")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 供应商信息分页查询
     * @throws Exception
     */
    @Test
    public void selectSupplierName() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/supplier/selectSupplierName/1/10")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

}
