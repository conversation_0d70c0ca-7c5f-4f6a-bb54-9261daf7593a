package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/9/28
 */
public class AreaControllerTest extends BaseControllerTest {

    @Test
    public void selectAll() throws Exception {
        getTest("/area");
    }

    @Test
    public void selectTest() throws Exception{
        String testData = "{\"fruitPrice\":200,\"totalPrice\":1000,\"dairyPrice\":800.92}";
        mvc.perform(
                MockMvcRequestBuilders.get("/area/test")
                .param("deliveryRule",testData)
                .accept(MediaType.APPLICATION_JSON)
                .session(session))
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.forwardedUrl(null))
            .andDo(MockMvcResultHandlers.print())
            .andReturn();
    }
}