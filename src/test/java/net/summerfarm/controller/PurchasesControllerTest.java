package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.model.DTO.StoreRecordDTO;
import net.summerfarm.model.domain.StoreRecord;
import net.summerfarm.model.domain.SupplierConnect;
import net.summerfarm.model.vo.StoreRecordVO;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.io.File;
import java.io.FileInputStream;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * @Package: net.summerfarm.controller
 * @Description: controller测试类
 * @author: <EMAIL>
 * @Date: 2016/8/16
 */
public class PurchasesControllerTest extends BaseControllerTest {

    @Test
    public void selectOutStoreRecord() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/purchases/1/10")
                        .param("areaNo","1")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void select() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/purchases/detail/2018100800903001")
//                        .param("purchaseNo","2018120301627001")
//                        .param("areaNo","1")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void selectSupplier() throws Exception{
        SupplierConnect supplierConnect = new SupplierConnect();
        supplierConnect.setSupplierId(2);
        supplierConnect.setName("Laowang");
        supplierConnect.setPhone("1736636");
        mvc.perform(
                MockMvcRequestBuilders.delete("/supplier/conn/delete")
                        .param("id","125")
//                        .contentType(MediaType.APPLICATION_JSON_UTF8)
//                        .param("categoryId","3")
//                        .param("name","沃儿徳")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void selectPlanDetail() throws Exception{
        StoreRecordVO storeRecordVO = new StoreRecordVO();
        storeRecordVO.setAreaNo(1);
        storeRecordVO.setIsPurchase(1);
        List<StoreRecordDTO> storeRecordList = new ArrayList<>();
        StoreRecordDTO storeRecord = new StoreRecordDTO();
        storeRecord.setBatch("2018120701643001");
        storeRecord.setId(136);
        storeRecord.setQualityDate(LocalDate.of(2018,12,22));
        storeRecord.setQuantity(1);
        storeRecord.setSku("657881476");

        storeRecordList.add(storeRecord);
//        storeRecordVO.setStoreRecordList(storeRecordList);
        System.out.println(JSONObject.toJSONString(storeRecordVO));

        mvc.perform(
                MockMvcRequestBuilders.post("/store-record")
                        .accept(MediaType.APPLICATION_JSON)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JSONObject.toJSONString(storeRecordVO))
                        .session(session)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 修改供应商历史记录列表
     * @throws Exception
     */
    @Test
    public void selectUpdateSupplierLog() throws Exception{
        mvc.perform(MockMvcRequestBuilders.get("/purchases/updateSupplierLog/1/10")
                .accept(MediaType.APPLICATION_JSON)
                .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 修改供应商模板下载
     * @throws Exception
     */
    @Test
    public void updateSupplierLogTemplate() throws Exception{
        mvc.perform(MockMvcRequestBuilders.get("/purchases/updateSupplierLogTemplate")
                .accept(MediaType.APPLICATION_JSON)
                .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 修改供应商数据上传
     * @throws Exception
     */
    @Test
    public void uploadUpdateSupplierLog() throws Exception{
        ResultActions resultActions = mvc.perform(MockMvcRequestBuilders.fileUpload("/purchases/upload/updateSupplierLog")
                .file(new MockMultipartFile("file","test","application/ms-excel",new FileInputStream(new File("C:/Users/<USER>/Downloads/testUpload.xls")))));
        MvcResult mvcResult = resultActions
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andReturn();
        String result = mvcResult.getResponse().getContentAsString();
        Assert.assertNotNull(result);
    }

}
