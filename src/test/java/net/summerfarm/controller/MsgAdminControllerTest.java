package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/4/25
 */
public class MsgAdminControllerTest extends BaseControllerTest {

    @Test
    public void select() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/msg-admin/1/10")
                .session(session)
                .accept(MediaType.APPLICATION_JSON)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void selectAll() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/msg-admin/admin/1/10")
                        .session(session)
                        .accept(MediaType.APPLICATION_JSON)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void selectInfo() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/msg-admin/admin/1")
                        .session(session)
                        .accept(MediaType.APPLICATION_JSON)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void update() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.post("/msg-admin/1")
                        .session(session).param("status", "2")
                        .accept(MediaType.APPLICATION_JSON)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void read() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.post("/msg-admin/read/1")
                        .session(session).param("read", "true")
                        .accept(MediaType.APPLICATION_JSON)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}