package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * FinancialInvoiceController Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>11/19/2021</pre>
 */
public class FinancialInvoiceControllerTest extends BaseControllerTest {

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: 查询订单列表（默认只展示近一个月的）筛选和查询接口
     */
    @Test
    public void testSelectOrderList() throws Exception {
//TODO: Test goes here...
        mvc.perform(
                MockMvcRequestBuilders.get("/financial-invoice/orderList")
                        .param("invoiceConfigId", "165")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * Method: 提交开票信息接口
     */
    @Test
    public void testInvoiceSave() throws Exception {
//TODO: Test goes here...
        mvc.perform(
                MockMvcRequestBuilders.post("/financial-invoice/invoiceSave")
                        .param("amountMoney", "10000")
                        .param("invoiceId", "10")
                        .param("invoiceType", "0")
                        .param("orderNoList", "[0,1]")
                        .param("creatorRemark", "123")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * Method: 获取开票信息列表
     */
    @Test
    public void testInvoiceQuery() throws Exception {
//TODO: Test goes here...

        mvc.perform(
                MockMvcRequestBuilders.get("/financial-invoice/invoiceQuery/1/10")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * Method: 开票处理接口
     */
    @Test
    public void testInvoiceHandle() throws Exception {
//TODO: Test goes here...
        mvc.perform(
                MockMvcRequestBuilders.post("/financial-invoice/invoiceHandle")
                        .param("id", "123")
                        .param("invoiceResult", "0")
                        .param("handlerRemark", "122")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * Method: 对应发票id的订单列表展示
     */
    @Test
    public void testInvoiceOrderList() throws Exception {
//TODO: Test goes here...
        mvc.perform(
                MockMvcRequestBuilders.post("/financial-invoice/invoiceOrderList/10")
                        .param("queryType", "1")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * Method: 对应发票id的订单导出
     */
    @Test
    public void testInvoiceOrderListExport() throws Exception {
//TODO: Test goes here...
        mvc.perform(
                MockMvcRequestBuilders.get("/financial-invoice/invoiceOrderList/excelExport/500")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * Method: CRM获取开票信息列表
     */
    @Test
    public void testInvoiceQueryCRM() throws Exception {
//TODO: Test goes here...

        mvc.perform(
                MockMvcRequestBuilders.get("/financial-invoice/CRM/invoiceQuery/1/10")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * Method: statusQuery()
     */
    @Test
    public void testStatusQuery() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/financial-invoice/statusQuery")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * Method: statusUpdate(@RequestBody FinancialInvoiceQuery financialInvoiceQuery)
     */
    @Test
    public void testStatusUpdate() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.post("/financial-invoice/statusUpdate")
                        .param("storeMallSwitch", "0")
                        .param("brandMallSwitch", "0")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


} 
