package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

public class PurchaseControllerTest extends BaseControllerTest {

    /**
     * 采购单详情
     * @throws Exception
     */
    @Test
    public void testDetail() throws Exception {
        mvc.perform(
                        MockMvcRequestBuilders.get("/purchases/detail")
                                .param("purchaseNo","2021102793470008")
                                .accept(MediaType.APPLICATION_JSON)
                                .session(session)
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


    /**
     * 采购单分页查询
     * @throws Exception
     */
    @Test
    public void testSelect() throws Exception {
        mvc.perform(
                        MockMvcRequestBuilders.get("/purchases")
                                .param("purchaseNo","2021102793470008")
                                .accept(MediaType.APPLICATION_JSON)
                                .session(session)
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


    /**
     * 新建采购单
     * @throws Exception
     */
    @Test
    public void testSavePurchase() throws Exception {
        //TODO: Test goes here...
        mvc.perform(
                        MockMvcRequestBuilders.post("/purchases")
                                .param("areaNo","10")
                                .param("deliveryNo","")
                                .param("deliveryType","1")
                                .param("flightNumber","")
                                .param("logisticsCost","")
                                .param("logisticsPaymentType","1")
                                .param("purchaseNo","")
                                .param("purchasePlace","")
                                .param("purchaseTime","2021-10-27")
                                .param("purchaser","缪庆军")
                                .param("purchasesPlanResultVOS[0].characters","")
                                .param("purchasesPlanResultVOS[0].checkReport","")
                                .param("purchasesPlanResultVOS[0].isNew","false")
                                .param("purchasesPlanResultVOS[0].isSave","true")
                                .param("purchasesPlanResultVOS[0].marketPrice","2")
                                .param("purchasesPlanResultVOS[0].nameRemakes","")
                                .param("purchasesPlanResultVOS[0].pdName","水果鲜果")
                                .param("purchasesPlanResultVOS[0].price","200")
                                .param("purchasesPlanResultVOS[0].priceHint","false")
                                .param("purchasesPlanResultVOS[0].productionDate","2021-10-27")
                                .param("purchasesPlanResultVOS[0].purchaseNo","")
                                .param("purchasesPlanResultVOS[0].purchasePrice","1")
                                .param("purchasesPlanResultVOS[0].purchasesPlans[0].checkReport","")
                                .param("purchasesPlanResultVOS[0].purchasesPlans[0].inQuantity","0")
                                .param("purchasesPlanResultVOS[0].purchasesPlans[0].productionDate","")
                                .param("purchasesPlanResultVOS[0].purchasesPlans[0].qualityDate","")
                                .param("purchasesPlanResultVOS[0].purchasesPlans[0].supplierId","40")
                                .param("purchasesPlanResultVOS[0].qualityDate","2021-11-06")
                                .param("purchasesPlanResultVOS[0].qualityTime","10")
                                .param("purchasesPlanResultVOS[0].qualityTimeUnit","day")
                                .param("purchasesPlanResultVOS[0].quantity","200")
                                .param("purchasesPlanResultVOS[0].advQuantity","20")
                                .param("purchasesPlanResultVOS[0].supplierId","40")
                                .param("purchasesPlanResultVOS[0].title","水果鲜果")
                                .param("purchasesPlanResultVOS[0].weight","一级/常规/0.01个*1个(这是规格)")
                                .param("purchasesType","0")
                                .param("receivePlace","")
                                .param("receiveTime","2021-10-27")
                                .param("receiver","龙")
                                .param("remark","")
                                .param("state","0")
                                .accept(MediaType.APPLICATION_JSON)
                                .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


    /**
     * 校验预售数量
     * @throws Exception
     */
    @Test
    public void testCheckAdvanceQuantity() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/purchases/advance/check")
                        .param("id","1")
                        .param("sku","2274182870")
                        .param("areaNo","1")
                        .param("advQuantity","10")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


    /**
     * 校验退订预售单数量
     * @throws Exception
     */
    @Test
    public void testCheckBackQuantity() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/purchases/back/check")
                        .param("purchaseNo","2020022500170001")
                        .param("sku","2274182870")
                        .param("areaNo","1")
                        .param("advQuantity","10")
                        .param("backQuantity","1")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


}
