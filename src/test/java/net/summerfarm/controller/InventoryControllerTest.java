package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/3/12
 */
public class InventoryControllerTest extends BaseControllerTest {

    @Test
    public void selectBySpu() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/inventory/product")
                        .param("outdated","0")
                        .param("pdId","6")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void save() throws Exception {

        mvc.perform(
                MockMvcRequestBuilders.post("/inventory")
                        .param("weight","as")
                        .param("unit","ad")
                        .param("origin","ad")
                        .param("maturity","1qw")
                        .param("afterSaleQuantity","12")
                        .param("salePrice","12")
                        .param("costPrice","12")
                        .param("marketPrice","12")
                        .param("show","1")
                        .param("areaSkuVOS[0].areaName","杭州")
                        .param("areaSkuVOS[0].areaNo","1001")
                        .param("areaSkuVOS[0].originalPrice","12")
                        .param("areaSkuVOS[0].price","12")
                        .param("areaSkuVOS[0].salesMode","0")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void selectPriceBySku() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/inventory/select/price")
                .param("sku","668330443")
                .param("areaNo","1001")
                .accept(MediaType.APPLICATION_JSON)
                .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 查询已保存的工商名称
     * @throws Exception
     */
    @Test
    public void testSelectAll() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/inventory/businessAll")
                        .param("name","杭州鲜沐科技有限公司")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 查询已保存的门店发票信息
     * @throws Exception
     */
    @Test
    public void testAdminAddress() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/inventory/adminAddress")
                        .param("adminId","1334")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 天眼查查询信息
     * @throws Exception
     */
    @Test
    public void testSelectTianYanCha() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/inventory/selectTianYanCha")
                        .param("name","杭州鲜沐科技有限公司")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}