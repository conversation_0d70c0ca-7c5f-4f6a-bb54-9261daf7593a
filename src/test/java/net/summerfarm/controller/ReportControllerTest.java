package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.model.domain.AreaSku;
import net.summerfarm.model.input.InventoryReq;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class ReportControllerTest extends BaseControllerTest{

    @Test
    public void selectTotalGmv() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/report/gmv-total")
                        .param("areaNo","1001")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void selectGmvTrend() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/price-adjust/sku/1/10")
                        .param("priceStatus","2")
                        .param("areaNo","1")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void selectPriceData() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/report/sales-volume/1/10")
                        .param("type","month")
//                        .param("areaNo","1001")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void selectGmvAndCost() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/report/gmv-cost")
//                        .param("type","day")
//                        .param("areaNo","1001")
//                        .param("startDate", LocalDate.of(2018,11,01).toString())
//                        .param("endDate",LocalDate.now().toString())
                        .param("type","month")
//                        .param("storeNo","1")
                        .param("startDate",LocalDate.of(2018,8,1).toString())
                        .param("endDate",LocalDate.of(2018,11,30).toString())
                        .param("sku","648420144")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void selectVolumeDataBySku()throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/report/sales-volume")
//                        .param("type","day")
                        .param("type","day")
                        .param("areaNo","1001")
                        .param("startDate",LocalDate.of(2018,11,05).toString())
                        .param("endDate",LocalDate.of(2018,12,9).toString())
                        .param("sku","663785374")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void areaSkuRecord()throws Exception{
        InventoryReq inventoryReq = new InventoryReq();
        inventoryReq.setAfterSaleQuantity(23);
        inventoryReq.setBaseSaleQuantity(1);
        inventoryReq.setBaseSaleUnit(1);
        inventoryReq.setInvId((long)101);
        inventoryReq.setMaturity("");
        inventoryReq.setOrigin("美国");
        inventoryReq.setSku("653872426");
        inventoryReq.setUnit("盒");
        inventoryReq.setWeight("1_毛重2-5KG(分分分)");
        List<AreaSku> areaSkuVOS = new ArrayList<>();
        AreaSku areaSku = new AreaSku();
        areaSku.setAreaNo(1001);
        areaSku.setId(98);
        areaSku.setLadderPrice("[]");
        areaSku.setMType(0);
        areaSku.setOnSale(true);
        areaSku.setOriginalPrice(BigDecimal.valueOf(1000));
        areaSku.setSalesMode(0);
        areaSku.setShow(true);
        areaSku.setSku("653872426");
        areaSkuVOS.add(areaSku);
        inventoryReq.setAreaSkuVOS(areaSkuVOS);
        mvc.perform(
                MockMvcRequestBuilders.put("/inventory/653872426")
                        .accept(MediaType.APPLICATION_JSON)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSONObject.toJSONString(inventoryReq))
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
