package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

public class TaxRateControllerTest extends BaseControllerTest {

    @Test
    public void getTaxRateConfig() throws Exception {
        getTest("/tax_rate/config?pdId=24").getResponse().getContentAsString();
    }

    @Test
    public void saveTaxRateConfig() throws Exception {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.set("taxRateValue", "0.01");
        params.set("taxRateCode", "123879");
        postTest("/tax_rate/spu/24", params);
    }
}