package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.model.domain.Contact;
import net.summerfarm.model.domain.MerchantExt;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * @Classname MerchantController
 * @Description 客户测试类
 * @Date 2021/12/6 14:51
 * @Created by hx
 */
public class MerchantControllerTest extends BaseControllerTest {
    /**
     * 保存免邮日期
     * @throws Exception
     */
    @Test
    public void updateFreeday()throws Exception{
        MerchantExt merchantExt = new MerchantExt();
        merchantExt.setMId(1466L);
        merchantExt.setFreeDay("1,3");
        String json = JSONObject.toJSONString(merchantExt);
        mvc.perform(
                MockMvcRequestBuilders.post("/merchant/freeday")
                        .content(json)
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();

    }

    /**
     * 查看免邮日
     * @throws Exception
     */
    @Test
    public void select()throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/merchant/1/10")
                        .param("islock","0")
                        .param("mId","1466")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 查询配送周期
     */
    @Test
    public void selectDeliveryFrequent()throws Exception{
        Contact contact = new Contact();
        contact.setDeliveryFrequent("1,2");
        contact.setContactId(2L);
        contact.setArea("西湖区");
        contact.setCity("杭州市");
        String json = JSONObject.toJSONString(contact);
        mvc.perform(
                MockMvcRequestBuilders.post("/merchant/selectDeliveryFrequent")
                        .content(json)
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }




}
