package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 外部对接-订单同步
 * @createTime 2021年10月18日 14:17:00
 */
public class OrderOuterInfoControllerTest extends BaseControllerTest {

    /**
     * 查询列表数据
     * @throws Exception
     */
    @Test
    public void selectTest() throws Exception {
        getTest("/orderOuterInfo/1/10");
    }

    /**
     * 重新同步
     * @throws Exception
     */
    @Test
    public void placeOrderTest() throws Exception {
        String params="{\"placeOrderIds\":[1492,1455]}";
        postTest("/orderOuterInfo/placeOrder",params);
    }

    /**
     * 拉取AOL订单信息
     * @throws Exception
     */
    @Test
    public void pullOrderAOLTest() throws Exception {
        getTest("/orderOuterInfo/pullOrderAOL");
    }

    /**
     * 查询外部对接平台
     * @throws Exception
     */
    @Test
    public void selectOuterPlatformTest() throws Exception {
        getTest("/orderOuterInfo/selectOuterPlatform");
    }

    /**
     * 接收外部对接平台的订单数据
     * @throws Exception
     */
    @Test
    public void orderServicePlaceOrderTest() throws Exception {
        String params="{\n" +
                "    \"token\":\"12345678901234567890123456789012\",\n" +
                "    \"order_id\":\"123456064\",\n" +
                "    \"payment_time\":1635086354,\n" +
                "    \"store_sn\":\"WJ00009\",\n" +
                "    \"store_name\":\"【潘力】上海店\",\n" +
                "    \"ship_area\":\"浙江省 杭州市 余杭区\",\n" +
                "    \"ship_address\":\"未来科技城6-1-11\",\n" +
                "    \"ship_mobile\":\"13795438401\",\n" +
                "    \"ship_name\":\"潘力\",\n" +
                "    \"memo\":\"\",\n" +
                "    \"items\":[\n" +
                "        {\n" +
                "            \"oi_id\":3232,\n" +
                "            \"sn\":\"W210118\",\n" +
                "            \"nums\":1,\n" +
                "            \"name\":\"蘑菇头1\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        postTest("/api/orderService/placeOrder",params);
    }

    @Test
    public void testSendEmail() throws Exception {
        getTest("/orderOuterInfo/sendEmailAOL");
    }
}
