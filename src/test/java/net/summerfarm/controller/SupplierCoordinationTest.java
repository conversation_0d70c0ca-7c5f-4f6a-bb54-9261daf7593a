package net.summerfarm.controller;

import com.alibaba.fastjson.JSON;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.model.input.SupplierCoordinationInput;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/4/3 13:52
 */
public class SupplierCoordinationTest extends BaseControllerTest {


    /**
     * 供应商保存(支持协同配置)
     *
     * @throws Exception
     */
    @Test
    public void testSaveSupplier() throws Exception {
        String paramJson = "{\"material\":[],\"account\":\"\",\"accountBank\":\"\",\"address\":\"\",\"connectList\":[{\"phone\":\"***********\",\"name\":\"候卿\",\"position\":\"赶尸人\",\"id\":6273,\"adminName\":\"苏前坤\"}],\"id\":\"\",\"manager\":\"苏前坤\",\"selectWeek\":[],\"name\":\"勾莹\",\"payType\":\"\",\"accountName\":\"\",\"productArray\":\"\",\"categoryArray\":\"62/测试权限;184/测试的蔬菜\",\"deliveryFrequent\":\"\",\"settleType\":\"\",\"settleForm\":\"\",\"customStartDate\":\"\",\"remark\":\"\",\"invoice\":true,\"accountList\":[{\"payType\":2,\"accountBank\":\"\",\"account\":\"给现金\",\"accountName\":\"勾莹\",\"accountAscription\":\"\",\"showMsg1\":false,\"showMsg2\":false,\"showMsg3\":false}],\"supplierType\":1,\"supplierName\":\"\",\"taxNumber\":\"412728199505186667\",\"supplierContractList\":[],\"supplierFileList\":[{\"fileType\":5,\"materialType\":\"身份证\",\"fileUrl\":\"test/6xj4dgx1hku0f4m0e.JPG\",\"startDate\":\"2023-04-03\",\"valid\":1,\"endDate\":\"\"}],\"phone\":\"\",\"password\":\"\",\"scoreJson\":[{\"no\":1,\"score\":25,\"remark\":\"ce\"},{\"no\":2,\"score\":20,\"remark\":\"ce\"},{\"no\":3,\"score\":20,\"remark\":\"ce\"},{\"no\":4,\"score\":15,\"remark\":\"ce\"},{\"no\":5,\"score\":10,\"remark\":\"ce\"},{\"no\":6,\"score\":10,\"remark\":\"ce\"}],\"totalScore\":100}";
        mvc.perform(
                        MockMvcRequestBuilders.post("/supplier/save")
                                .headers(httpHeaders)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(paramJson)
                                .accept(MediaType.APPLICATION_JSON)
                ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


    /**
     * 获取供应商详情包括协同配置
     *
     * @throws Exception
     */
    @Test
    public void testGetSupplierDetail() throws Exception {
        mvc.perform(
                        MockMvcRequestBuilders.get("/supplier/detail/2899")
                                .headers(httpHeaders)
                                .accept(MediaType.APPLICATION_JSON_UTF8_VALUE)
                ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 更新供应商协同配置
     *
     * @throws Exception
     */
    @Test
    public void testUpdateSupplierCoordination() throws Exception {
        SupplierCoordinationInput supplierCoordinationInput = new SupplierCoordinationInput();
        supplierCoordinationInput.setSupplierId(2899L);
        supplierCoordinationInput.setPoCoordinationTab(1);
        supplierCoordinationInput.setReconciliationCoordinationTab(1);
        supplierCoordinationInput.setInvoiceCoordinationTab(1);
        supplierCoordinationInput.setInboundCoordinationTab(1);
        mvc.perform(
                        MockMvcRequestBuilders.post("/supplier/upsert/coordination")
                                .contentType(MediaType.APPLICATION_JSON)
                                .headers(httpHeaders)
                                .content(JSON.toJSONString(supplierCoordinationInput))
                                .accept(MediaType.APPLICATION_JSON_UTF8_VALUE)
                ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 查询供应商协同信息
     *
     * @throws Exception
     */
    @Test
    public void testQueryCoordination() throws Exception {
        mvc.perform(
                        MockMvcRequestBuilders.post("/supplier/query/coordination")
                                .headers(httpHeaders)
                                .param("supplierId", "2899")
                                .accept(MediaType.APPLICATION_JSON_UTF8_VALUE)
                ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


    /**
     * 采购订单直接发布(存在供应商协同配置)
     *
     * @throws Exception
     */
    @Test
    public void testSavePo() throws Exception {
        // 直接发布
//        String po ="{\"purchaseNo\":\"\",\"purchasePlace\":\"\",\"purchaser\":\"苏前坤\",\"receiver\":\"张小虎\",\"remark\":\"sqk测试\",\"purchaseTime\":\"2023-04-03\",\"state\":1,\"purchasesPlanResultVOS\":[{\"latestArrivalDate\":\"2023-04-03\",\"price\":50,\"checkReport\":\"\",\"qualityTime\":10,\"qualityTimeUnit\":\"\",\"purchaseNo\":\"\",\"purchasesPlans\":[{\"checkReport\":\"\",\"qualityDate\":\"\",\"productionDate\":\"\",\"inQuantity\":0,\"supplierId\":\"\",\"priceType\":0,\"latestArrivalDate\":\"2023-04-03\"}],\"quantity\":10,\"singlePlice\":5,\"sku\":\"5483773214\",\"title\":\"芜湖嘻嘻嘻\",\"weight\":\"0.01个*1个/二级/常规(这是规格)\",\"pdName\":\"芜湖嘻嘻嘻\",\"extType\":3,\"isSave\":false,\"isNew\":true,\"type\":0,\"advQuantity\":0,\"minPrice\":2,\"maxPrice\":2,\"priceType\":0,\"priceLapse\":false,\"supplierId\":2299,\"supplier\":\"cj测试\",\"showTip\":true,\"priceHint\":false,\"error\":false}],\"purchasesType\":0,\"arrangeTime\":\"\",\"arrangeRemark\":\"\",\"creatorId\":4648,\"deliveryTime\":\"\",\"tmsDistSiteName\":\"\",\"deliveryAddress\":\"\",\"tmsDistSiteId\":\"\",\"areaNo\":1,\"operatorId\":4648}";
        // 发给供应商确认
        String po = "{\"purchaseNo\":\"\",\"purchasePlace\":\"\",\"purchaser\":\"苏前坤\",\"receiver\":\"张小虎\",\"remark\":\"sqk测试\",\"purchaseTime\":\"2023-04-03\",\"state\":2,\"purchasesPlanResultVOS\":[{\"latestArrivalDate\":\"2023-04-03\",\"price\":50,\"checkReport\":\"\",\"qualityTime\":10,\"qualityTimeUnit\":\"\",\"purchaseNo\":\"\",\"purchasesPlans\":[{\"checkReport\":\"\",\"qualityDate\":\"\",\"productionDate\":\"\",\"inQuantity\":0,\"supplierId\":\"\",\"priceType\":0,\"latestArrivalDate\":\"2023-04-03\"}],\"quantity\":10,\"singlePlice\":5,\"sku\":\"5483773214\",\"title\":\"芜湖嘻嘻嘻\",\"weight\":\"0.01个*1个/二级/常规(这是规格)\",\"pdName\":\"芜湖嘻嘻嘻\",\"extType\":3,\"isSave\":false,\"isNew\":true,\"type\":0,\"advQuantity\":0,\"minPrice\":2,\"maxPrice\":2,\"priceType\":0,\"priceLapse\":false,\"supplierId\":2299,\"supplier\":\"cj测试\",\"showTip\":true,\"priceHint\":false,\"error\":false}],\"purchasesType\":0,\"arrangeTime\":\"\",\"arrangeRemark\":\"\",\"creatorId\":4648,\"deliveryTime\":\"\",\"tmsDistSiteName\":\"\",\"deliveryAddress\":\"\",\"tmsDistSiteId\":\"\",\"areaNo\":1,\"operatorId\":4648}";
        mvc.perform(
                        MockMvcRequestBuilders.post("/purchases")
                                .headers(httpHeaders)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(po)
                                .accept(MediaType.APPLICATION_JSON_UTF8_VALUE)
                ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


    @Test
    public void testReplaceConfirm() throws Exception {
        String po = "{\"purchaseId\":\"15190\",\"purchaseNo\":\"\",\"purchasePlace\":\"\",\"purchaser\":\"苏前坤\",\"receiver\":\"张小虎\",\"remark\":\"sqk测试\",\"purchaseTime\":\"2023-04-03\",\"state\":1,\"purchasesPlanResultVOS\":[{\"latestArrivalDate\":\"2023-04-03\",\"price\":50,\"checkReport\":\"\",\"qualityTime\":10,\"qualityTimeUnit\":\"\",\"purchaseNo\":\"\",\"purchasesPlans\":[{\"checkReport\":\"\",\"qualityDate\":\"\",\"productionDate\":\"\",\"inQuantity\":0,\"supplierId\":\"\",\"priceType\":0,\"latestArrivalDate\":\"2023-04-03\"}],\"quantity\":10,\"singlePlice\":5,\"sku\":\"5483773214\",\"title\":\"芜湖嘻嘻嘻\",\"weight\":\"0.01个*1个/二级/常规(这是规格)\",\"pdName\":\"芜湖嘻嘻嘻\",\"extType\":3,\"isSave\":false,\"isNew\":true,\"type\":0,\"advQuantity\":0,\"minPrice\":2,\"maxPrice\":2,\"priceType\":0,\"priceLapse\":false,\"supplierId\":2299,\"supplier\":\"cj测试\",\"showTip\":true,\"priceHint\":false,\"error\":false}],\"purchasesType\":0,\"arrangeTime\":\"\",\"arrangeRemark\":\"\",\"creatorId\":4648,\"deliveryTime\":\"\",\"tmsDistSiteName\":\"\",\"deliveryAddress\":\"\",\"tmsDistSiteId\":\"\",\"areaNo\":1,\"operatorId\":4648}\n";
        mvc.perform(
                        MockMvcRequestBuilders.post("/pms-service/po//upsert/replaceConfirm")
                                .headers(httpHeaders)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(po)
                                .accept(MediaType.APPLICATION_JSON_UTF8_VALUE)
                ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


    @Test
    public void testInboundCoordination() throws Exception {
        String po = "{\"addTime\":\"2023-04-03 14:56:32\",\"areaNo\":1,\"arrangeRemark\":\"\",\"creatorId\":4648,\"deliveryType\":1,\"id\":15189,\"logisticsPaymentType\":0,\"matchingProgress\":\"0\",\"processState\":0,\"purchaseNo\":\"20230403464884010\",\"purchaseTime\":\"2023-04-03\",\"purchaser\":\"苏前坤\",\"purchasesType\":0,\"receiver\":\"张小虎\",\"receiverName\":\"张小虎\",\"receiverPhone\":\"1\",\"remark\":\"sqk测试\",\"showOneClick\":1,\"skuNums\":1,\"state\":1,\"totalPrice\":50,\"totalQuantity\":10,\"totalWeight\":10,\"updateTime\":\"2023-04-03 15:30:07\",\"warehouseAddress\":\"浙江省杭州市西湖区龙章路6号\",\"warehouseName\":\"杭州总仓\",\"stockArrangeItemDetails\":[{\"areaNo\":1,\"arrangeQuantity\":10,\"checkReport\":\"\",\"firstLevelCategory\":\"鲜果\",\"id\":247489,\"inQuantity\":0,\"packing\":\"包\",\"pdName\":\"芜湖嘻嘻嘻\",\"pic\":\"sku-picture/uvg1rw3eeqcnjy2k.png\",\"planStatus\":1,\"price\":50,\"purchaseNo\":\"20230403464884010\",\"qualityTime\":10,\"qualityTimeUnit\":\"month\",\"quantity\":10,\"returnQuantity\":0,\"secondLevelCategory\":\"水果-鲜果牛奶-测试类目\",\"singlePlice\":5,\"sku\":\"5483773214\",\"storageArea\":\"冷冻\",\"supplier\":\"cj测试\",\"supplierId\":2299,\"title\":\"芜湖嘻嘻嘻\",\"type\":0,\"unit\":\"包\",\"unsubscribeQuantity\":0,\"volume\":\"0.01*0.01*0.01\",\"weight\":\"0.01个*1个/二级/常规(这是规格)\",\"weightNum\":\"1.00\",\"productionDate\":\"2023-04-03\",\"qualityDate\":\"2024-02-03\",\"mergeCol\":1,\"arrQuantity\":10}],\"arrangeTime\":\"2023-04-03\"}";
        mvc.perform(
                        MockMvcRequestBuilders.post("/stock-arrange")
                                .headers(httpHeaders)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(po)
                                .accept(MediaType.APPLICATION_JSON_UTF8_VALUE)
                ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
