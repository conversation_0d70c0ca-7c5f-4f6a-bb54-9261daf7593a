package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/20 11:48
 */
public class MallActivityControllerTest extends BaseControllerTest {

    /**
     * 展示特价活动及详情
     * @throws Exception
     */
    @Test
    public void selectOnSale() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/mall-activities/on-sale/1/10")
                        .param("areaNo","1001")
//                        .param("activityName","天")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 展示满减活动及详情
     * @throws Exception
     */
    @Test
    public void selectMarketRule() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/mall-activities/market-rule")
                        .param("areaNo","1001")
//                        .param("activityName","配置")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
