package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * SettlementController Tester.
 *
 * <AUTHOR>
 * @since <pre>10/22/2021</pre>
 * @version 1.0
 */
public class SettlementControllerTest  extends BaseControllerTest {

    @Before
    public void before() throws Exception {
    }

    @After
    public void after() throws Exception {
    }

    /**
     *
     * Method: select(@PathVariable int pageIndex, @PathVariable int pageSize, SettlementQuery query)
     *
     */
    @Test
    public void testSelect() throws Exception {
    //TODO: Test goes here...
    }

    /**
     *
     * Method: selectUnSettleByPurchaseNo(@PathVariable String purchaseNo)
     *
     */
    @Test
    public void testSelectUnSettleByPurchaseNo() throws Exception {
    //TODO: Test goes here...
    }

    /**
     * 未结算采购项通过供应商id查询可结算采购单信息
     * @throws Exception
     */
    @Test
    public void testSelectUnSettleBySupplierId() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/settlement/un-settle")
                        .param("supplierId","44")
                        .param("startTime","2021-10-18")
                        .param("endTime","2021-10-22")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     *
     * 创建结算单
     *
     */
    @Test
    public void testSave() throws Exception {
    //TODO: Test goes here...
        mvc.perform(
                MockMvcRequestBuilders.post("/settlement/save")
                        .param("detailList[0].accountingCost","1200")
                        .param("detailList[0].accountingQuantity","100")
                        .param("detailList[0].backCost","0")
                        .param("detailList[0].backQuantity","0")
                        .param("detailList[0].backStatus","0")
                        .param("detailList[0].checkReport","")
                        .param("detailList[0].extType","0")
                        .param("detailList[0].inPrice","0")
                        .param("detailList[0].inQuantity","0")
                        .param("detailList[0].marketPrice","10")
                        .param("detailList[0].matchingSchedule","0")
                        .param("detailList[0].planStatus","1")
                        .param("detailList[0].productionDate","2021-09-18")
                        .param("detailList[0].purchaseTime","2021-09-18")
                        .param("detailList[0].purchasesType","0")
                        .param("detailList[0].qualityDate","2021-09-28")
                        .param("detailList[0].quantity","100")
                        .param("detailList[0].settleFlag","0")
                        .param("detailList[0].settleForm","3")
                        .param("detailList[0].sku","866052253820")
                        .param("detailList[0].skuType","0")
                        .param("detailList[0].storeNo","1")
                        .param("detailList[0].supplier","222")
                        .param("detailList[0].supplierId","29")
                        .param("detailList[0].title","西柚")
                        .param("detailList[0].weight","6个*1箱/测试")
                        .param("detailList[0].purchasePlanId","1463")
                        .param("detailList[0].skuAmount","1200")
                        .param("detailList[0].type","1")
                        .param("recordList[0].amount","1200")
                        .param("recordList[0].expectedTime","2021-10-27 00:00:00")
                        .param("recordList[0].remark","500")
                        .param("supplierId","29")
                        .param("supplierName","222")
                        .param("account","*********")
                        .param("payType","1")
                        .param("pdType","2")
                        .param("totalAmount","1200")
                        .param("refundSettlementAmount","0")
                        .param("deductionAmount","0")
                        .param("accountName","11")
                        .param("accountBank","万达万达")
                        .param("accountAscription","王大王的")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     *
     * Method: close(Integer settlementId)
     *
     */
    @Test
    public void testClose() throws Exception {
    //TODO: Test goes here...
    }

    /**
     *
     * 结算单详情
     *
     */
    @Test
    public void testDetail() throws Exception {
    //TODO: Test goes here...
        mvc.perform(
                MockMvcRequestBuilders.get("/settlement/252")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();

    }

    /**
     *
     * Method: selectDetail(SettlementDetailVO settlementDetailVO)
     *
     */
    @Test
    public void testSelectDetail() throws Exception {
    //TODO: Test goes here...
    }

    /**
     *
     * Method: saveRecord(@Validated SettlementPaymentRecord instance)
     *
     */
    @Test
    public void testSaveRecord() throws Exception {
    //TODO: Test goes here...
    }

    /**
     *
     * Method: updateRemark(Integer recordId, String remark)
     *
     */
    @Test
    public void testUpdateRemark() throws Exception {
    //TODO: Test goes here...
    }

    /**
     *
     * Method: audit(Integer recordId, Integer auditFlag)
     *
     */
    @Test
    public void testAudit() throws Exception {
    //TODO: Test goes here...
    }

    /**
     *
     * Method: approve(Integer recordId, Integer approveFlag)
     *
     */
    @Test
    public void testApprove() throws Exception {
    //TODO: Test goes here...
    }

    /**
     *
     * Method: pay(Integer recordId, Integer auditFlag, String paymentVoucher)
     *
     */
    @Test
    public void testPay() throws Exception {
    //TODO: Test goes here...
    }

    /**
     *
     * Method: upVoucher(Integer recordId, String voucher)
     *
     */
    @Test
    public void testUpVoucher() throws Exception {
    //TODO: Test goes here...
    }

    /**
     *
     * Method: data()
     *
     */
    @Test
    public void testData() throws Exception {
    //TODO: Test goes here...
    }

    /**
     *
     * Method: export(@PathVariable Integer id)
     *
     */
    @Test
    public void testExportId() throws Exception {
    //TODO: Test goes here...
    }

    /**
     *
     * Method: export()
     *
     */
    @Test
    public void testExport() throws Exception {
        mvc.perform(
                        MockMvcRequestBuilders.get("/settlement/export/445/466")
                                .accept(MediaType.APPLICATION_JSON)
                                .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     *
     * 退款单列表查询
     *
     */
    @Test
    public void testSelectRefundSlip() throws Exception {
    //TODO: Test goes here...
        mvc.perform(
                MockMvcRequestBuilders.get("/settlement/selectRefundSlip/1/10")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 待退款退款单确认退款上传信息
     * @throws Exception
     */
    @Test
    public void testAdoptRefundSlip() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/settlement/adoptRefundSlip")
                        .param("remark","*********")
                        .param("id","*********")
                        .param("settlementId","*********")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 付款单撤回
     * @throws Exception
     */
    @Test
    public void testWithDraw() throws Exception {
        mvc.perform(
                        MockMvcRequestBuilders.post("/settlement/withdraw")
                                .accept(MediaType.APPLICATION_JSON)
                                .session(session)
                                .param("recordId","466")
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }




}
