package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.model.domain.Coupon;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.math.BigDecimal;
import java.util.Date;

public class CouponControllerTest extends BaseControllerTest {

    @Test
    public void selectPage()throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/coupon/all/1/10")
                        .param("status","1")
                        .param("name","1")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void save() throws Exception{
        Coupon coupon = new Coupon();
        coupon.setGrouping(1);
        coupon.setMoney(BigDecimal.ONE);
        coupon.setName("漳卅");
        coupon.setThreshold(new BigDecimal("1.01"));
        coupon.setType((byte)1);
        coupon.setAgioType(4);
        coupon.setVaildDate(new Date("2021-10-22 00:00:00"));

        String json = JSONObject.toJSONString(coupon);

        mvc.perform(
                MockMvcRequestBuilders.post("/coupon")
                        .content( json)
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();


    }

}
