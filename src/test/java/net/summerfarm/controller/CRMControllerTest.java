package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/18 14:47
 */
public class CRMControllerTest extends BaseControllerTest {

    /**
     * 查询拥有当前登录账号城市权限的BD列表
     */
    @Test
    public void selectBdForAreaList()throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/crm/area/bdList")
                        .param("areaNo","2750")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

}
