package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.model.domain.AdCodeMsg;
import net.summerfarm.model.vo.FenceVO;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * @Classname FenceControllerTest
 * @Description 围栏测试
 * @Date 2021/12/6 13:59
 * @Created by hx
 */
public class FenceControllerTest extends BaseControllerTest {

    /**
     * 保存
     * @throws Exception
     */
    @Test
    public void save()throws Exception{
        FenceVO fenceVO = new FenceVO();
        fenceVO.setNextDeliveryDate(LocalDate.now());
        fenceVO.setDeliveryFrequent("1,3");
        fenceVO.setFenceName("EW4");
        fenceVO.setStoreNo(1);
        fenceVO.setType(0);
        List<AdCodeMsg> adCodeMsgList = new ArrayList<>();
        AdCodeMsg adCodeMsg = new AdCodeMsg();
        adCodeMsg.setAdCode("410328");
        adCodeMsg.setProvince("河南");
        adCodeMsg.setCity("洛阳市");
        adCodeMsg.setArea("老城区");
        adCodeMsg.setLevel("district");
        adCodeMsgList.add(adCodeMsg);
        fenceVO.setAdCodes(adCodeMsgList);
        String json = JSONObject.toJSONString(fenceVO);
        mvc.perform(
                MockMvcRequestBuilders.post("/fence/save")
                        .content( json)
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();

    }

    /**
     * 修改
     * @throws Exception
     */
    @Test
    public void updateFence()throws Exception{
        FenceVO fenceVO = new FenceVO();
        fenceVO.setNextDeliveryDate(LocalDate.now());
        fenceVO.setDeliveryFrequent("1,3");
        fenceVO.setFenceName("EW4");
        fenceVO.setStoreNo(1);
        fenceVO.setType(0);
        fenceVO.setId(53);
        List<AdCodeMsg> adCodeMsgList = new ArrayList<>();
        AdCodeMsg adCodeMsg = new AdCodeMsg();
        adCodeMsg.setAdCode("410328");
        adCodeMsg.setProvince("河南");
        adCodeMsg.setCity("洛阳市");
        adCodeMsg.setArea("老城区");
        adCodeMsg.setLevel("district");
        adCodeMsgList.add(adCodeMsg);
        fenceVO.setAdCodes(adCodeMsgList);
        String json = JSONObject.toJSONString(fenceVO);
        mvc.perform(
                MockMvcRequestBuilders.post("/fence/updateFence")
                        .content( json)
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();

    }

    /**
     * 查看详情
     * @throws Exception
     */
    @Test
    public void detail()throws Exception{

        mvc.perform(
                MockMvcRequestBuilders.get("/fence/select/detail")
                        .param("id","53")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();

    }

    /**
     * 初始化
     * @throws Exception
     */
    @Test
    public void initFenceDelivery()throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/fence/initFenceDelivery")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();

    }

}
