package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.mapper.manage.MerchantMapper;
import net.summerfarm.model.domain.MerchantLeads;
import net.summerfarm.model.vo.MerchantVO;
import net.summerfarm.service.MerchantService;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2019-12-11
 */
public class MerchantLeadsControllerTest extends BaseControllerTest {

    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private MerchantService merchantService;

    @Test
    public void serviceTest() throws IOException {
        MerchantVO merchantVO = merchantMapper.selectMerchantByMid(12013L);
        merchantVO.setState(0);
        logger.info("开始处理店铺认领审核");
        merchantService.reviewMerchant(merchantVO.getmId().intValue(), merchantVO, true);
    }

    @Test
    public void select() throws Exception {
        getTest("/merchant-leads /1/10");
    }

    @Test
    public void save() throws Exception {
        MerchantLeads merchantLeads = new MerchantLeads();
        merchantLeads.setMname("喜茶西溪路店");
        merchantLeads.setMcontact("喜茶");
        merchantLeads.setPhone("18812122121");
        merchantLeads.setProvince("浙江省");
        merchantLeads.setCity("杭州市");
        merchantLeads.setArea("西湖区");
        merchantLeads.setAddress("文二西路");
        merchantLeads.setAreaNo(1001);
        merchantLeads.setAreaName("杭州");
        merchantLeads.setSize("单店");
        merchantLeads.setStatus(1);
        postTest("/merchant-leads", JSONObject.toJSONString(merchantLeads));
    }
}