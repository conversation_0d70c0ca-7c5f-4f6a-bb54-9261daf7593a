package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/4/26
 */
public class AreaStoreControllerTest extends BaseControllerTest {

    @Test
    public void updateLeadTime() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.post("/area-store/lead-time")
                        .session(session)
                        .param("sku","A002S01R001")
                        .param("areaNo","1")
                        .param("leadTime","5")
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void updatePurchaser() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.post("/area-store/purchaser")
                        .session(session)
                        .param("sku","A002S01R001")
                        .param("areaNo","1")
                        .param("adminId","3")
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void testUnsigned() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.post("/area-store/unsignedtest")
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}