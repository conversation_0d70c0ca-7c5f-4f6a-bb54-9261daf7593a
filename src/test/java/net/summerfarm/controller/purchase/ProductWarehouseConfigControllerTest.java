package net.summerfarm.controller.purchase;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.contexts.Global;
import net.summerfarm.module.scp.common.enums.SupplierReplenishmentConfigEnums;
import net.summerfarm.model.input.purchase.ProductWarehouseSaveConfigInput;
import net.summerfarm.model.input.purchase.ProductWarehouseSaveInput;
import net.summerfarm.model.input.purchase.ProductWarehouseSupplierSaveInput;
import net.summerfarm.model.input.purchase.SupplierReplenishmentSaveInput;
import org.junit.Test;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Collections;
import java.util.Map;

public class ProductWarehouseConfigControllerTest extends BaseControllerTest {

    private static String REQUEST_URL_PREFIX = "/productWarehouseConfig";

    @Test
    public void pageQueryProductConfigTest() throws Exception{
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.put("spuStatus", Collections.singletonList("1"));
        do {
            getTest(REQUEST_URL_PREFIX + "/1/10",params);
        } while (true);
    }

    @Test
    public void listSpuConfigDetailTest() throws Exception{
        do {
            getTest(REQUEST_URL_PREFIX + "/detail/3");
        } while (true);
    }


    @Test
    public void batchSaveConfigTest() throws Exception{
//        do {
            ProductWarehouseSaveInput saveInput = new ProductWarehouseSaveInput();
            ProductWarehouseSaveConfigInput saveConfigInput = new ProductWarehouseSaveConfigInput();
            saveConfigInput.setConfigId(1L);
            saveConfigInput.setPdId(3L);
            saveConfigInput.setWarehouseNo(1);
            saveConfigInput.setPurchaseType(1);
            saveConfigInput.setAdminId(1953);
            saveConfigInput.setAdminName("邹金鑫");

            ProductWarehouseSupplierSaveInput supplierSaveInput = new ProductWarehouseSupplierSaveInput();
            supplierSaveInput.setSupplierId(8);
            supplierSaveInput.setPrimaryFlag(Global.TRUE_FLAG);
            SupplierReplenishmentSaveInput replenishmentSaveInput = new SupplierReplenishmentSaveInput();
            replenishmentSaveInput.setReplenishmentMode(SupplierReplenishmentConfigEnums.ReplenishmentMode.IRREGULAR_INDEFINITE.getValue());
            replenishmentSaveInput.setPreDay(3);
            replenishmentSaveInput.setBacklogDay(1);
            replenishmentSaveInput.setSafeWaterLevel(10);
            supplierSaveInput.setReplenishmentConfigs(Lists.newArrayList(replenishmentSaveInput));

            ProductWarehouseSupplierSaveInput supplierSaveInput2 = new ProductWarehouseSupplierSaveInput();
            supplierSaveInput2.setSupplierId(9);
            supplierSaveInput2.setPrimaryFlag(Global.FALSE_FLAG);
            SupplierReplenishmentSaveInput replenishmentSaveInput1 = new SupplierReplenishmentSaveInput();
            replenishmentSaveInput1.setReplenishmentMode(SupplierReplenishmentConfigEnums.ReplenishmentMode.REGULAR_INDEFINITE.getValue());
            replenishmentSaveInput1.setPreDay(4);
            replenishmentSaveInput1.setOrderDate(1);
            replenishmentSaveInput1.setBacklogDay(1);
            replenishmentSaveInput1.setSafeWaterLevel(15);
            SupplierReplenishmentSaveInput replenishmentSaveInput2 = new SupplierReplenishmentSaveInput();
            replenishmentSaveInput2.setReplenishmentMode(SupplierReplenishmentConfigEnums.ReplenishmentMode.REGULAR_INDEFINITE.getValue());
            replenishmentSaveInput2.setPreDay(4);
            replenishmentSaveInput2.setOrderDate(4);
            replenishmentSaveInput2.setBacklogDay(1);
            replenishmentSaveInput2.setSafeWaterLevel(15);
            supplierSaveInput2.setReplenishmentConfigs(Lists.newArrayList(replenishmentSaveInput1,replenishmentSaveInput2));

            saveConfigInput.setSupplierInfos(Lists.newArrayList(supplierSaveInput, supplierSaveInput2));

            saveInput.setConfigList(Lists.newArrayList(saveConfigInput));
            postTest(REQUEST_URL_PREFIX, JSONObject.toJSONString(saveInput));
//        } while (true);
    }

    @Test
    public void manualAddProductConfigTest() throws Exception{
        Map<String, Object> map = Maps.newHashMap();
        do {
            postTest(REQUEST_URL_PREFIX +"/manual/3", JSONObject.toJSONString(map));
        } while (true);
    }

    @Test
    public void manualInitProductConfigTest() throws Exception{
        Map<String,Object> map = Maps.newHashMap();
//        do {
            postTest(REQUEST_URL_PREFIX + "/manual/init", JSONObject.toJSONString(map));
//        } while (true);
    }
}
