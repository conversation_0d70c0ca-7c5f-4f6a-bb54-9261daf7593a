package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/18 14:51
 */
public class OrderControllerTest extends BaseControllerTest {

    /**
     * CRM小程序：分页查询订单数据
     */
    @Test
    public void selectForCrmOrderList()throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/order/crm/1/10")
                        .param("status","3")
                        .param("areaNo","1001")
                        .param("endTime","2021-11-19 11:28:23")
                        .param("startTime","2021-06-18 11:28:23")
                        .param("type","1")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 订单详情页
     */
    @Test
    public void selectOrderDetails()throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.get("/order/01163720610249865")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
