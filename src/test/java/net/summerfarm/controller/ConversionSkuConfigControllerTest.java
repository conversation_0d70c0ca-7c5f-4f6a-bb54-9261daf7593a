package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.BaseControllerTest;
import net.summerfarm.model.domain.AdminDataPermission;
import net.summerfarm.model.domain.ConversionSkuConfig;
import net.summerfarm.model.vo.AdminVO;
import net.summerfarm.model.vo.RoleVO;
import org.junit.Test;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/10/25  16:22
 */
public class ConversionSkuConfigControllerTest extends BaseControllerTest {

   /**
   * 新增
   */
    @Test
    public void addConversionSkuConfig() throws Exception{

        ConversionSkuConfig params = new ConversionSkuConfig();
        params.setAdminId(123);
        params.setStatus(0);
        params.setInSku("5466723033");
        params.setOutSku("5466723522");
        params.setWarehouseNo(1);
        String s = JSONObject.toJSONString(params);

        mvc.perform(
                MockMvcRequestBuilders.post("/conversion/save")
                        .accept(MediaType.APPLICATION_JSON).contentType(APPLICATION_JSON_UTF8)
                        .param("inSku","5466723033")
                        .param("outSku","5466723522")
                        .param("warehouseNo","1")
                        .param("rates","1:2")
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
    * 查询
    */
    @Test
    public void selectConfigVO() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/conversion/select/1/10")
                        .accept(MediaType.APPLICATION_JSON).contentType(APPLICATION_JSON_UTF8)
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
    * 作废
    */
    @Test
    public void updateConfig() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.post("/conversion/update")
                        .accept(MediaType.APPLICATION_JSON).contentType(APPLICATION_JSON_UTF8)
                        .param("id","10")
                        .param("status", "1")
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


}
