package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

public class PurchasesBackControllerTest extends BaseControllerTest {


    /**
     * 查询退货单对应的退款单
     * @throws Exception
     */
    @Test
    public void testSelectRefund() throws Exception {
        mvc.perform(
                        MockMvcRequestBuilders.get("/purchases-back/refund")
                                .param("purchasesBackNo","0202110266334825")
                                .accept(MediaType.APPLICATION_JSON)
                                .session(session)
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }


    /**
     * 查询校验金额
     * @throws Exception
     */
    @Test
    public void testAmount() throws Exception {
        mvc.perform(
                        MockMvcRequestBuilders.get("/purchases-back/amount")
                                .param("purchasesBackNo","2021102793470008")
                                .accept(MediaType.APPLICATION_JSON)
                                .session(session)
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
