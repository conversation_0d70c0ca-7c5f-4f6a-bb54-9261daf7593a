package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

public class ProductStockControllerTest extends BaseControllerTest {


    /**
     * 查询库存详细信息
     * @throws Exception
     */
    @Test
    public void testDetail() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/product-stock/select/queryAreaSku")
                        .param("sku","5454250306")
                        .param("warehouseNo","1")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}
