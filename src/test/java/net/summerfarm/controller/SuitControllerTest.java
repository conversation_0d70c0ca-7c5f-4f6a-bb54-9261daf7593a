package net.summerfarm.controller;

import net.summerfarm.BaseControllerTest;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.charset.StandardCharsets;

/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/7/17
 */
public class SuitControllerTest extends BaseControllerTest {


    public static final MediaType APPLICATION_JSON_UTF8 = new MediaType(MediaType.APPLICATION_JSON.getType(), MediaType.APPLICATION_JSON.getSubtype(), StandardCharsets.UTF_8);


    @Test
    public void select() throws Exception{
        getTest("/sku/suit/1/10?areaNo=1001");
    }

    @Test
    public void selectDetail() throws Exception{
        getTest("/sku/suit/14");
    }


    @Test
    public void deleteSuitItem() throws Exception{
        mvc.perform(
                MockMvcRequestBuilders.post("/sku/suit/item/2")
                        .accept(MediaType.APPLICATION_JSON)
                        .session(session)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }
}