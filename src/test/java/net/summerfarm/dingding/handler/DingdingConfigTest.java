package net.summerfarm.dingding.handler;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2023/7/21 15:38
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class DingdingConfigTest {

    @Resource
    private DingdingConfig dingdingConfig;

    @Test
    public void getProcessCode() {
        String text = "PAYEE_AUDIT_CODE";
        String processCode = dingdingConfig.getProcessCode(text);
        System.out.println(text);
    }
}