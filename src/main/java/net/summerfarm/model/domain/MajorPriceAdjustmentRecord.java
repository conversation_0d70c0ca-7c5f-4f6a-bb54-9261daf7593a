package net.summerfarm.model.domain;

import net.summerfarm.model.vo.MajorPriceAutoUpdateVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class MajorPriceAdjustmentRecord {
    private Integer id;

    private Integer adminId;

    private Integer areaNo;

    private String sku;

    private Integer status;

    private BigDecimal interestRate;

    private BigDecimal fixedPrice;

    private BigDecimal originalCostPrice;

    private BigDecimal costPrice;

    private BigDecimal originalMarketPrice;

    private BigDecimal marketPrice;

    private Integer majorCycle;

    private String reason;

    private String createAdminName;

    private LocalDateTime createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getInterestRate() { return interestRate; }

    public void setInterestRate(BigDecimal interestRate) { this.interestRate = interestRate; }

    public BigDecimal getFixedPrice() {
        return fixedPrice;
    }

    public void setFixedPrice(BigDecimal fixedPrice) {
        this.fixedPrice = fixedPrice;
    }

    public BigDecimal getOriginalCostPrice() {
        return originalCostPrice;
    }

    public void setOriginalCostPrice(BigDecimal originalCostPrice) {
        this.originalCostPrice = originalCostPrice;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public BigDecimal getOriginalMarketPrice() {
        return originalMarketPrice;
    }

    public void setOriginalMarketPrice(BigDecimal originalMarketPrice) {
        this.originalMarketPrice = originalMarketPrice;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public Integer getMajorCycle() {
        return majorCycle;
    }

    public void setMajorCycle(Integer majorCycle) {
        this.majorCycle = majorCycle;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    public String getCreateAdminName() {
        return createAdminName;
    }

    public void setCreateAdminName(String createAdminName) {
        this.createAdminName = createAdminName == null ? null : createAdminName.trim();
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }


    public static MajorPriceAdjustmentRecord buildRecord(MajorPriceAutoUpdateVO majorPrice){
        MajorPriceAdjustmentRecord adjustmentRecord = new MajorPriceAdjustmentRecord();
        // 报价单添加自动调价记录
        adjustmentRecord.setAdminId(majorPrice.getAdminId());
        adjustmentRecord.setAreaNo(majorPrice.getAreaNo());
        adjustmentRecord.setSku(majorPrice.getSku());
        adjustmentRecord.setStatus(1);
        adjustmentRecord.setInterestRate(majorPrice.getInterestRate());
        adjustmentRecord.setFixedPrice(majorPrice.getFixedPrice());
        adjustmentRecord.setOriginalCostPrice(majorPrice.getCost());
        adjustmentRecord.setOriginalMarketPrice(majorPrice.getPrice());
        adjustmentRecord.setMajorCycle(majorPrice.getMajorCycle());
        adjustmentRecord.setReason("报价单自动调价");
        adjustmentRecord.setCreateAdminName("自动");
        adjustmentRecord.setCreateTime(LocalDateTime.now());
        return adjustmentRecord;
    }
}