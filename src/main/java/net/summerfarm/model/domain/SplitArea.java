package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.vo.SplitAreaVO;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2021/5/21  11:25 城市区域拆分
 */
@ApiModel(description = "城市区域拆分实体类")
@Data
public class SplitArea implements Serializable {

    private Integer id;

    /**
    * 添加时间
    */
    @ApiModelProperty(value = "添加时间")
    private LocalDateTime addTime;

    /**
    * 修改时间
    */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    /**
    * 被拆分城市编号
    */
    @ApiModelProperty(value = "被拆分城市编号")
    private Integer splitAreaNo;

    /**
    * 任务执行状态 0 待执行 1 已执行 2 取消
    */
    @ApiModelProperty(value = "任务执行状态 0 待执行 1 已执行 2 取消")
    private Integer status;

    /**
    * 拆分到城市编号
    */
    @ApiModelProperty(value = "拆分到城市编号")
    private Integer areaNo;

    /**
    * 任务执行时间
    */
    @ApiModelProperty(value = "任务执行时间")
    private LocalDate executeTime;


    public SplitArea(){}

    public SplitArea(SplitAreaVO splitAreaVO){
        this.splitAreaNo = splitAreaVO.getSplitAreaNo();
        this.areaNo = splitAreaVO.getAreaNo();
        this.status = splitAreaVO.getStatus();
        this.executeTime = splitAreaVO.getExecuteTime();
    }


}
