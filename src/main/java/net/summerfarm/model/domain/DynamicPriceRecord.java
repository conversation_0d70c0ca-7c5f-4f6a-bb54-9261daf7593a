package net.summerfarm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DynamicPriceRecord implements Serializable {
    
    private Long id;

    /**
     * sku任务id
     */
    private Long skuTaskId;

    /**
     * sku
     */
    private String sku;

    /**
     * 运营服务区域
     */
    private Integer areaNo;

    /**
     * 原售价
     */
    private BigDecimal oldSalePrice;

    /**
     * 原毛利率
     */
    private BigDecimal oldGrossProfitRate;

    /**
     * 预计售价
     */
    private BigDecimal newSalePrice;

    /**
     * 预计毛利率
     */
    private BigDecimal newGrossProfitRate;

    /**
     * 状态，0 失败，1 成功
     */
    private Integer status;

    /**
     * 成功失败的原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}