package net.summerfarm.model.domain.offline;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DynamicPriceStatistics implements Serializable {
    
    private Long id;

    /**
     * sku
     */
    private String sku;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 昨日点击量
     */
    private Integer uv;

    /**
     * 昨日点击转化率
     */
    private BigDecimal clickConversion;

    /**
     * 昨日销量
     */
    private Integer salesVolume;

    /**
     * 昨日是否有售罄过，0 否，1 是
     */
    private Integer sellOut;

    /**
     * 昨日小时销售速度，key-value，24对值，如：0100:0.2221,0200:0.5236
     */
    private String salesSpeedHour;

    /**
     * 昨日小时内是否有售罄过，key-value，24对值，0100:1,0200:0
     */
    private String sellOutHour;

    /**
     * 日期，如20230108
     */
    private Integer dateFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}