package net.summerfarm.model.domain.offline;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TemporaryInsuranceRisk implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * sku
     */
    private String sku;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 运营大区编号
     */
    private Integer largeAreaNo;

    /**
     * 同步时间标记(yyyyMMdd/yyyyMM)
     */
    private Integer dateFlag;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}