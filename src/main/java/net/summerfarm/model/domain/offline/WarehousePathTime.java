package net.summerfarm.model.domain.offline;

public class WarehousePathTime {
    private Long id;

    private Integer inWarehouseNo;

    private Integer outWarehouseNo;

    private Integer costTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getInWarehouseNo() {
        return inWarehouseNo;
    }

    public void setInWarehouseNo(Integer inWarehouseNo) {
        this.inWarehouseNo = inWarehouseNo;
    }

    public Integer getOutWarehouseNo() {
        return outWarehouseNo;
    }

    public void setOutWarehouseNo(Integer outWarehouseNo) {
        this.outWarehouseNo = outWarehouseNo;
    }

    public Integer getCostTime() {
        return costTime;
    }

    public void setCostTime(Integer costTime) {
        this.costTime = costTime;
    }
}