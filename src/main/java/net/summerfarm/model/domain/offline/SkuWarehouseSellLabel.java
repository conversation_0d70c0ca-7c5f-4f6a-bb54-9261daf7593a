package net.summerfarm.model.domain.offline;

import lombok.Data;

/**
 *
 * @describe sku仓售卖标签
 * @date 2023/04/07 17:58:10
 */
@Data
public class SkuWarehouseSellLabel {
    /**
     * 主键
     */
    private Long id;

    /**
     * sku
     */
    private String sku;

    /**
     * spu_id
     */
    private Long pdId;

    /**
     * 商品编号
     */
    private String spuNo;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * SKU+仓维度的ABC分档标签
     */
    private String levelLabel;
}