package net.summerfarm.model.domain.offline;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class WarehouseEstimatedConsumption {
    /**
     * id
     */
    private Long id;

    /**
     * spuID
     */
    private Integer pdId;

    /**
     * sku
     */
    private String skuId;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 日期
     */
    private LocalDateTime viewDate;

    /**
     * 新预估销量
     */
    private BigDecimal forecastSales;

    /**
     * 新预估调拨量
     */
    private BigDecimal forecastTransferOut;

    /**
     * 新预估消耗量
     */
    private BigDecimal forecastConsumption;
}