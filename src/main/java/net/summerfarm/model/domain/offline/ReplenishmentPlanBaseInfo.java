package net.summerfarm.model.domain.offline;

import lombok.Data;

/**
 *
 * @describe 补货计划基础信息,符合补货计划信息的
 * @date 2023/04/14 10:26:49
 */
@Data
public class ReplenishmentPlanBaseInfo {
    /**
     * 主键
     */
    private Long id;

    /**
     * sku
     */
    private String sku;

    /**
     * spu_id
     */
    private Long pdId;

    /**
     * 商品编号
     */
    private String spuNo;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 小SKU,原表中转入SKU
     */
    private String smallSku;

    /**
     * 比例 转出(大)SKU:转入(小)SKU 1:2
     */
    private String rates;

}