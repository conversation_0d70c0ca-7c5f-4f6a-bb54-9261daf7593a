package net.summerfarm.model.domain.bms;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2022/8/23
 */
@Data
public class BmsSettleAccount implements Serializable {

    /**
     * 结算明细单id
     */
    private Integer id;

    /**
     * 结算开始日期
     */
    private LocalDate deliveryStartDate;

    /**
     * 结算结束时间
     */
    private LocalDate deliveryEndDate;

    /**
     * 对账单id
     */
    private Integer reconciliationId;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 服务区/县
     */
    private String district;

    /**
     * 明细单状态 0待核对 1已核对
     */
    private Integer status;

    /**
     * 服务区域id
     */
    private Integer quotationAreaId;

    /**
     * 承运商id
     */
    private Integer carrierId;

    /**
     * 卸货费用
     */
    private BigDecimal dischargeAmount;

    /**
     * 业务类型
     * @see net.summerfarm.module.bms.common.constant.QuotationEnum
     */
    private String businessType;

    private Integer bidderId;

    private String bidderName;

    private Integer settleTargetId;

    private String settleTargetName;

    private String settleAccountNo;

    private String quotationAreaName;
}
