package net.summerfarm.model.domain.bms;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2022/8/18
 */
@Data
public class BmsDeliveryQuotationDetail implements Serializable {

    private Integer id;

    /**
     * 计算项id
     */
    private Integer bmsCalculationItemId;

    private String bmsCalculationItemName;

    /**
     * 报价单id
     */
    private Integer bmsDeliveryQuotationId;

    /**
     * 金额/点位
     */
    private BigDecimal amount;

    private String unit;
}
