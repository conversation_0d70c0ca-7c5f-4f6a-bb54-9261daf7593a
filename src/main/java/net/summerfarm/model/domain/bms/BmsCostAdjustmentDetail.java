package net.summerfarm.model.domain.bms;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2022/8/22
 */
@Data
@Accessors(chain = true)
public class BmsCostAdjustmentDetail implements Serializable {

    private Integer id;

    /**
     * 调整单id
     */
    private Integer costAdjustmentId;

    /**
     * 原金额
     */
    private BigDecimal oldAmount;

    /**
     * 调整后金额
     */
    private BigDecimal newAmount;

    /**
     * 备注
     */
    private String remake;

}
