package net.summerfarm.model.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * crm_manage_administrative_city
 * <AUTHOR>
@Data
public class CrmManageAdministrativeCity implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 区域id
     */
    private Integer mbId;

    /**
     * 行政城市名
     */
    private String administrativeCity;

    /**
     * 创建人id
     */
    private Integer creator;

    /**
     * 删除标识:0否1是
     */
    private Byte deleteFlag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}