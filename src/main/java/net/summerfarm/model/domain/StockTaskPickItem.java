package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2020/8/14  18:29
 */
@Data
public class StockTaskPickItem {


    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("添加时间")
    private LocalDateTime addTime;

    @ApiModelProperty("数量")
    private Integer amount;

    @ApiModelProperty("大客户id")
    private Integer adminId;

    @ApiModelProperty("大客户名称")
    private String adminName;

    @ApiModelProperty("类型 0 大客户-水果 1 其他")
    private Integer type;

    @ApiModelProperty("城配仓")
    private Integer outStoreNo;

    @ApiModelProperty("捡货仓库")
    private Integer storeNo;

    @ApiModelProperty("sku")
    private String sku;

    @ApiModelProperty("配送时间")
    private LocalDate deliveryTime;

    @ApiModelProperty("配送时间")
    private String  closeOrderTime;
}
