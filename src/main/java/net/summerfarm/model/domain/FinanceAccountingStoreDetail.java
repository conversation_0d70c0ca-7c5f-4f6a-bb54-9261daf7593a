package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * finance_accounting_store_detail
 * <AUTHOR> 2021-12-08
 */
@ApiModel(value="账期订单门店订单实体类")
@Data
public class FinanceAccountingStoreDetail implements Serializable {
    /**
     * 账期订单门店订单表id
     */
    @ApiModelProperty(value="账期订单门店订单表id")
    private Long id;

    /**
     * 账期订单门店表id
     */
    @ApiModelProperty(value="账期订单门店表id")
    private Long financeAccountingStoreId;

    /**
     * 售后总金额
     */
    @ApiModelProperty(value="售后总金额")
    private BigDecimal afterSaleAmount;

    /**
     * 配送费用
     */
    @ApiModelProperty(value="配送费用")
    private BigDecimal deliveryFee;

    /**
     * 实付价格
     */
    @ApiModelProperty(value="实付价格")
    private BigDecimal totalPrice;

    /**
     * 应收总金额
     */
    @ApiModelProperty(value="应收总金额")
    private BigDecimal totalAmountReceivable;

    /**
     * 订单编号
     */
    @ApiModelProperty(value="订单编号")
    private String orderNo;

    /**
     * 添加时间
     */
    @ApiModelProperty(value="添加时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    private String creator;

    @ApiModelProperty(value="超时加单费用")
    private BigDecimal outTimesFee;

    @ApiModelProperty(value="账单编号")
    private String billNumber;

    private static final long serialVersionUID = 1L;
}