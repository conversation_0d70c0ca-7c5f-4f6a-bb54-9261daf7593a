package net.summerfarm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class WillExpirePriceLog implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * sku编号
     */
    private String sku;

    /**
     * 临保天数
     */
    private Integer remainingExpiredDays;

    /**
     * 预警天数
     */
    private Integer warnDays;

    /**
     * 售罄率
     */
    private BigDecimal sellOutRate;

    /**
     * 周转率
     */
    private BigDecimal turnoverRate;

    /**
     * 临保对应普通商品售价
     */
    private BigDecimal salePrice;

    /**
     * 销售速度
     */
    private BigDecimal salesPace;

    /**
     * 折扣,举例：0.98表示98折
     */
    private BigDecimal discout;

    /**
     * 临保原价
     */
    private BigDecimal originalPrice;

    /**
     * 计算后的临保价
     */
    private BigDecimal calPrice;

    /**
     * 临保价变动原因，0 临保计算，1 转换入库
     * @see net.summerfarm.enums.WillExpirePriceChangeReasonEnum
     */
    private Integer reason;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}