package net.summerfarm.model.domain;

import net.summerfarm.common.util.validation.groups.Add;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Null;
import java.io.Serializable;



@ApiModel(value = "菜单权限")
@Data
public class MenuPurview implements Serializable {


    @ApiModelProperty(value = "菜单权限id")
    @Null(message = "param.illegal",groups = {Add.class})
    private Integer id;

    @ApiModelProperty(value = "父级菜单权限id A类菜单没有父id")
    private Integer parentId;

    @ApiModelProperty(value = "菜单权限名称")
    private String name;

    @ApiModelProperty(value = "菜单权限描述")
    private String description;

    @ApiModelProperty(value = "菜单级别 A B")
    private String type;

    @ApiModelProperty(value = "状态 0 可用  1 不可用")
    private Integer status;

    @ApiModelProperty(value = "所属模块")
    private String module;

}
