package net.summerfarm.model.domain;

import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR> ct
 * create at:  2020/1/9  11:37
 * 未被冻结的省心送配送计划
 */
@Data
public class TimingLockTime {

    private Integer id;

    /**
    * 类型 0 采购 1 调拨
    */
    private Integer type;

    /**
    * 状态  0 有效 1 失效
    */
    private Integer status;

    /**
    * 开始冻结时间
    */
    private LocalDate beginTime;

    /**
     * 结束冻结时间
     */
    private LocalDate endTime;

    /**
    * 省心送配送ID
    */
    private Integer deliveryPlanId;




}
