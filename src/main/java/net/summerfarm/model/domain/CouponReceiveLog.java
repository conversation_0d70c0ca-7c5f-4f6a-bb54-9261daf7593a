package net.summerfarm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * coupon_receive_log
 * <AUTHOR>
@Data
public class CouponReceiveLog implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 优惠劵id
     */
    private Integer couponId;

    /**
     * 发放设置id
     */
    private Integer couponSenderId;

    /**
     * 客户id
     */
    private String mId;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}