package net.summerfarm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * carrier_quotation
 * <AUTHOR>
@Data
public class CarrierQuotation implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 承运商id
     */
    private Long carrierId;

    /**
     * 服务区域
     */
    private String serviceArea;

    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * 0点位 1公里
     */
    private Integer type;

    /**
     * 起步价
     */
    private BigDecimal startPrice;

    /**
     * 起步点数
     */
    private Integer startPointNum;

    /**
     * 超点数
     */
    private BigDecimal exceedPointPrice;

    /**
     * 补贴价
     */
    private BigDecimal subsidyPrice;

    /**
     * 打印费
     */
    private BigDecimal printingFee;

    /**
     * 前置仓费
     */
    private BigDecimal frontWarehouseFee;

    /**
     * 公里价
     */
    private BigDecimal kmPrice;

    /**
     * 是否删除状态 1是 0否
     */
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}