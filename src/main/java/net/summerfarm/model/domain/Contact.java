package net.summerfarm.model.domain;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.facade.marketing.dto.DistributionRulesDTO;
import net.summerfarm.model.DTO.merchant.ContactAddressRemark;
import net.summerfarm.model.input.DistributionRulesInsertInput;
import net.summerfarm.model.vo.PoiVO;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@ApiModel(description = "联系人实体类")
public class Contact implements Serializable {
    @ApiModelProperty(value = "联系人id")
    private Long contactId;

    @ApiModelProperty(value = "商户id")
    @NotNull(message = "mId.null", groups = {Add.class})
    private Long mId;

    @ApiModelProperty(value = "联系人")
    @NotNull(message = "contact.null", groups = {Add.class})
    private String contact;

    @ApiModelProperty(value = "职位")
    private String position;

    @ApiModelProperty(value = "性别")
    private Boolean gender;

    @ApiModelProperty(value = "手机号")
    @NotNull(message = "phone.null", groups = {Add.class})
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "微信号")
    private String weixincode;

    @ApiModelProperty(value = "省份")
    @NotNull(message = "province.null", groups = {Add.class})
    private String province;

    @ApiModelProperty(value = "城市")
    @NotNull(message = "city.null", groups = {Add.class})
    private String city;

    @ApiModelProperty(value = "区域")
    @NotNull(message = "area.null", groups = {Add.class})
    private String area;

    @ApiModelProperty(value = "地址")
    @NotNull(message = "address.null", groups = {Add.class})
    private String address;

    @ApiModelProperty(value = "状态(1正常或审核通过、2删除、3待审核、4审核不通过)")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "配送车辆")
    private String deliveryCar;

    @ApiModelProperty(value = "1默认地址 与merchat中一致")
    private Integer isDefault;

    @ApiModelProperty(value = "经纬度")
    private String poiNote;

    @ApiModelProperty(value = "到仓库距离")
    private BigDecimal distance;

    @ApiModelProperty(value = "预排路线")
    private String path;

    @ApiModelProperty(value = "门牌号")
    private String houseNumber;

    @ApiModelProperty(value = "配送仓编号")
    private Integer storeNo;

    private PoiVO poi;

    @ApiModelProperty(value = "配送周期")
    private String deliveryFrequent;

    /**
     * 运营区域id
     */
    @Getter
    @Setter
    private Integer areaNo;
    /**
     * 运营区域名
     */
    @Getter
    @Setter
    private String areaName;

    /**
     * 是否修改
     */
    @Getter
    @Setter
    private Boolean modify;
    /**
     * 是否使用新地址
     */
    @Getter
    @Setter
    private Boolean useNew;

    /**
     * 首配日
     */
    @Getter
    @Setter
    private LocalDate nextDeliveryDate;

    /**
     * 运费规则
     */
    @Getter
    @Setter
    private String deliveryRule;

    /**
     * 运费
     */
    @Getter
    @Setter
    private BigDecimal deliveryFee;

    /**
     * 自定义地址备注
     */
    @Setter
    @Getter
    private ContactAddressRemark contactAddressRemark;

    /**
     * 地址备注
     */
    @Setter
    @Getter
    private String addressRemark;

    /**
     * 运费规则-新版
     */
    private DistributionRulesDTO distributionRulesDTO;

    /**
     * 新增运费规则-新版
     */
    private DistributionRulesInsertInput insertInput;

    /**
     * 周期方案 1周计算 2间隔计算
     */
    @Setter
    @Getter
    private Integer frequentMethod;

    /**
     * 周的配送周期 0每天 1周一 依次,多个逗号分隔
     */
    @Setter
    @Getter
    private String weekDeliveryFrequent;

    /**
     * 配送间隔周期
     */
    @Setter
    @Getter
    private Integer deliveryFrequentInterval;

    /**
     * 开始计算日期
     */
    @Setter
    @Getter
    private LocalDate beginCalculateDate;

    /**
     * 是否清空 1：是 2：否  默认为否
     */
    @Setter
    @Getter
    private Integer clearDelivery;

    /**
     * 指定城配仓标识 0未指定 1指定
     */
    @Setter
    @Getter
    private Boolean appointStoreNoFlag;

    @Override
    public String toString() {
        return "Contact{" +
                "contactId=" + contactId +
                ", mId=" + mId +
                ", contact='" + contact + '\'' +
                ", position='" + position + '\'' +
                ", gender=" + gender +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", weixincode='" + weixincode + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", area='" + area + '\'' +
                ", address='" + address + '\'' +
                ", status=" + status +
                ", remark='" + remark + '\'' +
                ", deliveryCar='" + deliveryCar + '\'' +
                ", isDefault=" + isDefault +
                ", poiNote='" + poiNote + '\'' +
                ", distance=" + distance +
                ", path='" + path + '\'' +
                ", houseNumber='" + houseNumber + '\'' +
                ", storeNo=" + storeNo +
                ", poi=" + poi +
                ", deliveryFrequent='" + deliveryFrequent + '\'' +
                ", areaNo=" + areaNo +
                ", areaName='" + areaName + '\'' +
                ", modify=" + modify +
                ", useNew=" + useNew +
                '}';
    }

    public String getDeliveryFrequent() {
        return deliveryFrequent;
    }

    public void setDeliveryFrequent(String deliveryFrequent) {
        this.deliveryFrequent = deliveryFrequent;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    public Long getmId() {
        return mId;
    }

    public String getPoiNote() {
        return poiNote;
    }

    public void setPoiNote(String poiNote) {
        this.poiNote = poiNote;
    }

    public BigDecimal getDistance() {
        return distance;
    }

    public void setDistance(BigDecimal distance) {
        this.distance = distance;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact == null ? null : contact.trim();
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position == null ? null : position.trim();
    }

    public Boolean getGender() {
        return gender;
    }

    public void setGender(Boolean gender) {
        this.gender = gender;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getWeixincode() {
        return weixincode;
    }

    public void setWeixincode(String weixincode) {
        this.weixincode = weixincode == null ? null : weixincode.trim();
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer /**/status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public String getDeliveryCar() {
        return deliveryCar;
    }

    public void setDeliveryCar(String deliveryCar) {
        this.deliveryCar = deliveryCar;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getHouseNumber() {
        return houseNumber;
    }

    public void setHouseNumber(String houseNumber) {
        this.houseNumber = houseNumber;
    }

    public PoiVO getPoi() {
        return poi;
    }

    public void setPoi(PoiVO poi) {
        this.poi = poi;
    }

    public Integer getStoreNo() {
        return storeNo;
    }

    public void setStoreNo(Integer storeNo) {
        this.storeNo = storeNo;
    }


    public void initAddrRemark(){
        if (StringUtils.isEmpty(addressRemark)){
            return;
        }
        ContactAddressRemark contactAddressRemark = JSONUtil.toBean(addressRemark, ContactAddressRemark.class);
        setContactAddressRemark(contactAddressRemark);
    }

    public DistributionRulesDTO getDistributionRulesDTO() {
        return distributionRulesDTO;
    }

    public void setDistributionRulesDTO(DistributionRulesDTO distributionRulesDTO) {
        this.distributionRulesDTO = distributionRulesDTO;
    }

    public DistributionRulesInsertInput getInsertInput() {
        return insertInput;
    }

    public void setInsertInput(DistributionRulesInsertInput insertInput) {
        this.insertInput = insertInput;
    }
}