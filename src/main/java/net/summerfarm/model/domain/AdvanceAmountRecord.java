package net.summerfarm.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2021/5/11  10:26
 *
 * 采购单使用金额记录
 */
@Data
public class AdvanceAmountRecord {

    private Integer id;

    /**
    * 采购单号
    */
    private String purchaseNo;

    /**
    * 总金额
    */
    private BigDecimal totalAmount;

    /**
    * 本次使用金额
    */
    private BigDecimal useAmount;

    /**
    * 供应商id
    */
    private Integer supplierId;

    /**
    * 供应商名称
    */
    private String supplierName;

    /**
    * 添加时间
    */
    private LocalDateTime addTime;

    /**
    * 修改时间
    */
    private LocalDateTime updateTime;


}
