package net.summerfarm.model.domain;

import lombok.Getter;
import lombok.Setter;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.validation.annotation.InRange;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import net.summerfarm.common.util.validation.groups.Add;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@ApiModel(description = "省心送实体类")
public class TimingRule implements Serializable {
    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "规则名称")
    private String name;

    @ApiModelProperty(value = "团购商品sku")
    @NotNull
    private String timingSku;

    @ApiModelProperty(value = "規則所屬城市")
    private Integer areaNo;

    @ApiModelProperty(value = "展示标记：0不展示，1展示")
    private Integer display;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "配送开始时间")
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate deliveryStart;

    @ApiModelProperty(value = "配送结束时间")
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate deliveryEnd;

    @ApiModelProperty(value = "规则描述")
    private String ruleInformation;

    @ApiModelProperty(value = "起送数量")
    private Integer deliveryUnit;

    @ApiModelProperty(value = "单次配送上限")
    private Integer deliveryUpperLimit;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "类型，0省心送，1预售")
    @InRange(rangeNums = {0,1})
    private Integer type;

    @ApiModelProperty(value = "排序")
    private Integer priority;

    @ApiModelProperty(value = "是否自动计算配送次数")
    private Boolean autoCalculate;

    @ApiModelProperty(value = "配送周期")
    private Integer deliveryPeriod;

    @ApiModelProperty(value = "开始配送类型：0、下一个配送日 1、指定日期")
    private Integer deliveryStartType;
    /**
     * 省心送门槛
     */
    private Integer threshold;

    /**
     * 下单日期+N的N值
     */
    @Getter
    @Setter
    private Integer plusDay;

    public TimingRule() {
    }

    public Integer getThreshold() {
        return threshold;
    }

    public void setThreshold(Integer threshold) {
        this.threshold = threshold;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getTimingSku() {
        return timingSku;
    }

    public void setTimingSku(String timingSku) {
        this.timingSku = timingSku == null ? null : timingSku.trim();
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public LocalDate getDeliveryStart() {
        return deliveryStart;
    }

    public void setDeliveryStart(LocalDate deliveryStart) {
        this.deliveryStart = deliveryStart;
    }

    public LocalDate getDeliveryEnd() {
        return deliveryEnd;
    }

    public void setDeliveryEnd(LocalDate deliveryEnd) {
        this.deliveryEnd = deliveryEnd;
    }

    public String getRuleInformation() {
        return ruleInformation;
    }

    public void setRuleInformation(String ruleInformation) {
        this.ruleInformation = ruleInformation == null ? null : ruleInformation.trim();
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDisplay() {
        return display;
    }

    public void setDisplay(Integer display) {
        this.display = display;
    }

    public Integer getDeliveryUnit() {
        return deliveryUnit;
    }

    public void setDeliveryUnit(Integer deliveryUnit) {
        this.deliveryUnit = deliveryUnit;
    }

    public Integer getDeliveryUpperLimit() {
        return deliveryUpperLimit;
    }

    public void setDeliveryUpperLimit(Integer deliveryUpperLimit) {
        this.deliveryUpperLimit = deliveryUpperLimit;
    }

    public Boolean getAutoCalculate() {
        return autoCalculate;
    }

    public void setAutoCalculate(Boolean autoCalculate) {
        this.autoCalculate = autoCalculate;
    }

    public Integer getDeliveryPeriod() {
        return deliveryPeriod;
    }

    public void setDeliveryPeriod(Integer deliveryPeriod) {
        this.deliveryPeriod = deliveryPeriod;
    }

    public Integer getDeliveryStartType() {
        return deliveryStartType;
    }

    public void setDeliveryStartType(Integer deliveryStartType) {
        this.deliveryStartType = deliveryStartType;
    }
}