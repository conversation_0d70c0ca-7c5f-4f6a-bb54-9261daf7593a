package net.summerfarm.model.domain;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class SupplierContract {
    private Long id;

    private Integer supplierId;

    private String fileUrl;
    //0审核中，1待上传合同，2已关闭，3已完成
    private Integer status;

    //0、账期结算 1、备用金结算 2、公对公现结 3、私对私现结
    private Integer settleForm;

    //0失效1生效
    private Integer valid;

    private LocalDate startDate;

    private LocalDate endDate;

    private Integer settleType;

    private LocalDate customStartDate;

    private Integer customCycle;

    private Integer creditDays;

    private String creator;

    private LocalDateTime createTime;

    private String updater;

    private LocalDateTime updateTime;


}