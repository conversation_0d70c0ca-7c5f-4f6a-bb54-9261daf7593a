package net.summerfarm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class LuckyDrawActivity implements Serializable {

    /**
     * primary key
     */
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 预热时间
     */
    private LocalDateTime preheatTime;

    /**
     * 规则说明-落地页ID
     */
    private String rule;

    /**
     * 活动类型  红包雨-1  每日抽奖-2
     */
    private Integer type;

    /**
     * 浮标图
     */
    private String buoyImage;

    /**
     * 预热背景图
     */
    private String preheatBackground;

    /**
     * 结束背景图
     */
    private String endBackground;

    /**
     * 未抽奖背景图
     */
    private String noDrawBackground;

    /**
     * 抽奖背景图
     */
    private String drawBackground;

    /**
     * 分享图
     */
    private String shardImage;

    /**
     * 分享标题
     */
    private String shardTitle;

    /**
     * 分享描述
     */
    private String shardRemake;

    /**
     * 是否失效 0-否  1-是
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}