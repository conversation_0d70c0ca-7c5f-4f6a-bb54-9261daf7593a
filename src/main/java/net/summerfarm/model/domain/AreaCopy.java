package net.summerfarm.model.domain;

import java.time.LocalDateTime;
import lombok.Data;

/**
    * 运营区域复制单
    */
@Data
public class AreaCopy {
    /**
    * 主键、自增
    */
    private Long id;

    /**
    * 源城市编号
    */
    private Integer sourceAreaNo;

    /**
    * 目标(应用)城市编号
    */
    private Integer targetAreaNo;

    /**
    * 状态:1-确认中(数据已生成) 2-执行中 3-已生效
    */
    private Integer status;

    /**
    * 创建人(admin.admin_id)
    */
    private Integer creator;

    /**
    * 确认人(admin.admin_id)
    */
    private Integer updater;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 修改时间
    */
    private LocalDateTime updateTime;
}