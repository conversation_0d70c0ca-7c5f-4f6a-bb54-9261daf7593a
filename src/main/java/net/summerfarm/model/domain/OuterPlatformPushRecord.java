package net.summerfarm.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 外部平台推送数据记录表
 * @createTime 2021年10月18日 17:27:00
 */
@Data
public class OuterPlatformPushRecord {
    private Long id;

    /**
     * 类型 1：上下架;2：价格更新;3：订单发货通知;4：订单签收通知
     */
    private Integer type;

    /**
     * sku
     */
    private String sku;

    /**
     * admin_id
     */
    private Integer areaNo;

    /**
     * 城市
     */
    private Integer adminId;

    /**
     * 0下架 1上架
     */
    private Integer onSale;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 鲜沐订单号
     */
    private String xmOrderNo;

    /**
     * 推送状态 1：成功; 2:失败
     */
    private Integer pushStatus;

    /**
     * 推送次数
     */
    private Integer pushTimes;

    /**
     * 请求内容
     */
    private String reqContent;

    /**
     * 返回内容
     */
    private String resContent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}