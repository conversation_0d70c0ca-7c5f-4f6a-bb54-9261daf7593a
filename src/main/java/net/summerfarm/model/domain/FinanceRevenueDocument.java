package net.summerfarm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * finance_revenue_document
 * <AUTHOR>
 */
@Data
public class FinanceRevenueDocument implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 手工码 1预购商品消费收货 2直接消费收货 3鲜沐卡消费收货 4省心送收货
     */
    private Integer manualCode;

    /**
     * 产品编号
     */
    private String sku;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 仓库编号（库存仓）
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 实付总价
     */
    private BigDecimal priceAmount;

    /**
     * 税率
     */
    private BigDecimal taxRateValue;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 单据类型：0 现结，1 账期
     */
    private Integer type;

    /**
     * 确认收入日期
     */
    private LocalDate revenueRecognition;

    /**
     * 账单编号
     */
    private String billNumber;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 超时加单
     */
    private BigDecimal outTimesFee;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 单位
     */
    private String unit;

    /**
     * 商品名称
     */
    private String pdName;

    private static final long serialVersionUID = 1L;
}