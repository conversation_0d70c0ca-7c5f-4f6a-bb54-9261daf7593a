package net.summerfarm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

public class Suit implements Serializable {
    private Integer id;

    private String suitName;

    /**
     * 组合包图片
     */
    private String suitPic;

    private LocalDateTime addtime;

    private LocalDateTime updatetime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSuitName() {
        return suitName;
    }

    public void setSuitName(String suitName) {
        this.suitName = suitName;
    }

    public LocalDateTime getAddtime() {
        return addtime;
    }

    public void setAddtime(LocalDateTime addtime) {
        this.addtime = addtime;
    }

    public LocalDateTime getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(LocalDateTime updatetime) {
        this.updatetime = updatetime;
    }

    public String getSuitPic() {
        return suitPic;
    }

    public void setSuitPic(String suitPic) {
        this.suitPic = suitPic;
    }
}