package net.summerfarm.model.domain.tms;

import java.time.LocalDateTime;

public class TmsDriver {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.phone
     *
     * @mbg.generated
     */
    private String phone;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.cooperation_cycle
     *
     * @mbg.generated
     */
    private Byte cooperationCycle;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.business_type
     *
     * @mbg.generated
     */
    private Byte businessType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.id_card
     *
     * @mbg.generated
     */
    private String idCard;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.id_card_front_pic
     *
     * @mbg.generated
     */
    private String idCardFrontPic;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.id_card_behind_pic
     *
     * @mbg.generated
     */
    private String idCardBehindPic;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.driver_pics
     *
     * @mbg.generated
     */
    private String driverPics;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.admin_id
     *
     * @mbg.generated
     */
    private Integer adminId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.create_time
     *
     * @mbg.generated
     */
    private LocalDateTime createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tms_driver.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    private Integer cityCarrierId;

    private Long baseUserId;

    public Long getBaseUserId() {
        return baseUserId;
    }

    public void setBaseUserId(Long baseUserId) {
        this.baseUserId = baseUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tms_driver.id
     *
     * @return the value of tms_driver.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tms_driver.id
     *
     * @param id the value for tms_driver.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tms_driver.name
     *
     * @return the value of tms_driver.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tms_driver.name
     *
     * @param name the value for tms_driver.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tms_driver.phone
     *
     * @return the value of tms_driver.phone
     *
     * @mbg.generated
     */
    public String getPhone() {
        return phone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tms_driver.phone
     *
     * @param phone the value for tms_driver.phone
     *
     * @mbg.generated
     */
    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tms_driver.cooperation_cycle
     *
     * @return the value of tms_driver.cooperation_cycle
     *
     * @mbg.generated
     */
    public Byte getCooperationCycle() {
        return cooperationCycle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tms_driver.cooperation_cycle
     *
     * @param cooperationCycle the value for tms_driver.cooperation_cycle
     *
     * @mbg.generated
     */
    public void setCooperationCycle(Byte cooperationCycle) {
        this.cooperationCycle = cooperationCycle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tms_driver.business_type
     *
     * @return the value of tms_driver.business_type
     *
     * @mbg.generated
     */
    public Byte getBusinessType() {
        return businessType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tms_driver.business_type
     *
     * @param businessType the value for tms_driver.business_type
     *
     * @mbg.generated
     */
    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tms_driver.id_card
     *
     * @return the value of tms_driver.id_card
     *
     * @mbg.generated
     */
    public String getIdCard() {
        return idCard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tms_driver.id_card
     *
     * @param idCard the value for tms_driver.id_card
     *
     * @mbg.generated
     */
    public void setIdCard(String idCard) {
        this.idCard = idCard == null ? null : idCard.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tms_driver.id_card_front_pic
     *
     * @return the value of tms_driver.id_card_front_pic
     *
     * @mbg.generated
     */
    public String getIdCardFrontPic() {
        return idCardFrontPic;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tms_driver.id_card_front_pic
     *
     * @param idCardFrontPic the value for tms_driver.id_card_front_pic
     *
     * @mbg.generated
     */
    public void setIdCardFrontPic(String idCardFrontPic) {
        this.idCardFrontPic = idCardFrontPic == null ? null : idCardFrontPic.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tms_driver.id_card_behind_pic
     *
     * @return the value of tms_driver.id_card_behind_pic
     *
     * @mbg.generated
     */
    public String getIdCardBehindPic() {
        return idCardBehindPic;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tms_driver.id_card_behind_pic
     *
     * @param idCardBehindPic the value for tms_driver.id_card_behind_pic
     *
     * @mbg.generated
     */
    public void setIdCardBehindPic(String idCardBehindPic) {
        this.idCardBehindPic = idCardBehindPic == null ? null : idCardBehindPic.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tms_driver.driver_pics
     *
     * @return the value of tms_driver.driver_pics
     *
     * @mbg.generated
     */
    public String getDriverPics() {
        return driverPics;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tms_driver.driver_pics
     *
     * @param driverPics the value for tms_driver.driver_pics
     *
     * @mbg.generated
     */
    public void setDriverPics(String driverPics) {
        this.driverPics = driverPics == null ? null : driverPics.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tms_driver.admin_id
     *
     * @return the value of tms_driver.admin_id
     *
     * @mbg.generated
     */
    public Integer getAdminId() {
        return adminId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tms_driver.admin_id
     *
     * @param adminId the value for tms_driver.admin_id
     *
     * @mbg.generated
     */
    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tms_driver.create_time
     *
     * @return the value of tms_driver.create_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tms_driver.create_time
     *
     * @param createTime the value for tms_driver.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tms_driver.update_time
     *
     * @return the value of tms_driver.update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tms_driver.update_time
     *
     * @param updateTime the value for tms_driver.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getCityCarrierId() {
        return cityCarrierId;
    }

    public void setCityCarrierId(Integer cityCarrierId) {
        this.cityCarrierId = cityCarrierId;
    }
}