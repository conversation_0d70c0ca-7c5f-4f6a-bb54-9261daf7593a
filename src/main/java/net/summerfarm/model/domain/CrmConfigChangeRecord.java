package net.summerfarm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * crm_config_change_record
 * <AUTHOR>
@Data
public class CrmConfigChangeRecord implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 操作类型：0、新增 1、修改 2、删除
     */
    private Integer type;

    /**
     * 主键ID
     */
    private Integer pmId;

    /**
     * 具体操作名称
     */
    private String handleName;
    /**
     * 新值
     */
    private String newValue;

    /**
     * 原值
     */
    private String oldValue;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}