package net.summerfarm.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2020/12/29  12:17
 */

@Data
public class AfterSaleDeliveryDetail {

    /**
     * 配送类型 配送
     */
    public static final Integer DELIVERY_TYPE = 0;


    /**
     * 配送类型 回收
     */
    public static final Integer RECOVERY_TYPE = 1;


    /**
     * 状态 生效
     */
    public static final Integer EFFECTIVE_STATUS = 1 ;

    private Integer id;
    /**
     *  '创建时间'
     */
    private LocalDateTime createTime;
    /**
     * '修改时间'
     */
    private LocalDateTime updateTime ;
    /**
     * '售后配送信息id'
     */
    private Integer asDeliveryPathId;
    /**
     * 'sku信息'
     */
    private String sku ;
    /**
     * '规格'
     */
    private String weight;
    /**
     * '数量'
     */
    private Integer quantity ;
    /**
     * 商品名称
     */
    private String  pdName;
    /**
     * 类型  0 配送 1 回收 '
     */
    private Integer type;
    /**
     *   '状态 0 失效 1 生效
     */
    private Integer status;

    /**
     *拦截状态 0 正常 1被拦截
     */
    private Integer interceptFlag;

    //拦截时间
    private LocalDateTime interceptTime;

    //完成排线-展示标识 0 展示 1不展示
    private Integer showFlag;
}

