package net.summerfarm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * gmv_month_data 销售日GMV
 * <AUTHOR> 2021.12.06
 */
@Data
public class GmvMonthData implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * BD
     */
    private Integer adminId;

    /**
     * 城市
     */
    private Integer areaNo;

    /**
     * 乳制品日gmv
     */
    private BigDecimal dairyGmv;

    /**
     * 水果+衍生品日gmv
     */
    private BigDecimal fruitGmv;

    /**
     * 日gmv时间
     */
    private LocalDateTime gmvTime;

    /**
     * 添加时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * BD姓名
     */
    private String realname;

    /**
     * 状态 0 存在，1 作废
     */
    private Integer status;

    /**
     * 日订单数
     */
    private Integer amount;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private String updater;

    private static final long serialVersionUID = 1L;
}