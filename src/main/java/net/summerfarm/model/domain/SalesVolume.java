package net.summerfarm.model.domain;

import com.aliyun.odps.data.Record;
import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;

import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2020/11/18  10:53
 */
@Data
public class SalesVolume {

    private Integer id ;

    /**
    * 添加时间
    */
    private LocalDateTime gmtCreate;

    /**
    * 修改时间
    */
    private LocalDateTime gmtModified;

    /**
    * sku
    */
    private String sku;
    /**
    * 预测销量
    */
    private Integer preSales;

    /**
    * 实际销量
    */
    private Integer trueSales;
    /**
    * 仓库编号
    */
    private Integer storeNo;

    /**
    * 大单值
    */
    private Integer bigDeal;

    /**
    * 日期
    */
    private LocalDate calculateTime;


    /**
    * 超出大单值数量
    */
    private Integer bigDealOrderCnt;

    public SalesVolume(){}

    public SalesVolume(Record record){
        this.sku = parseString(record.getString("sku"));
        this.storeNo = parseInt(Integer.valueOf(record.getString("store_no")));
        this.calculateTime = DateUtils.stringToLocalDate(parseString(record.getString("ds")));
        this.bigDeal = parseInt(Integer.valueOf(record.getString("bigdeal")));
        this.preSales = parseInt(Integer.valueOf(record.getString("pre_sales")));
        this.trueSales = parseInt(Integer.valueOf(record.getString("true_sales")));
        this.bigDealOrderCnt = parseInt(Integer.valueOf(record.getString("bigdeal_order_cnt")));
    }

    private static String parseString(Object obj) {
        return Objects.isNull(obj) ? StringUtils.EMPTY : String.valueOf(obj);
    }

    private static Integer parseInt(Object obj) {
        return Objects.isNull(obj) ? 0 : Integer.valueOf(String.valueOf(obj));
    }

}
