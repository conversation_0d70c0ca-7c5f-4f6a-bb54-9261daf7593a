package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * Created by wjd on 2017/12/20.
 */
@ApiModel(description = "账号变更实体类")
public class AccountChange implements Serializable {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "商户id")
    private Long mId;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "unionid")
    private String unionid;

    @ApiModelProperty(value = "旧手机号")
    private String oldPhone;

    @ApiModelProperty(value = "旧联系人")
    private String oldContact;

    @ApiModelProperty(value = "新手机号")
    private String newPhone;

    @ApiModelProperty(value = "新联系人")
    private String newContact;

    @ApiModelProperty(value = "商户名")
    private String mname;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "1待审核 2审核通过 3审核失败")
    private Integer status;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getOldPhone() {
        return oldPhone;
    }

    public void setOldPhone(String oldPhone) {
        this.oldPhone = oldPhone;
    }

    public String getOldContact() {
        return oldContact;
    }

    public void setOldContact(String oldContact) {
        this.oldContact = oldContact;
    }

    public String getNewPhone() {
        return newPhone;
    }

    public void setNewPhone(String newPhone) {
        this.newPhone = newPhone;
    }

    public String getNewContact() {
        return newContact;
    }

    public void setNewContact(String newContact) {
        this.newContact = newContact;
    }

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }
}
