package net.summerfarm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * merchant_update_record
 * <AUTHOR>
@Data
public class MerchantUpdateRecord implements Serializable {

    private static final long serialVersionUID = -656513687116878325L;

    /**
     * 主键，自增长
     */
    private Long id;

    /**
     * 门店对应merchant表内的m_id
     */
    private Long mId;

    /**
     * 0: 门店切换，1：门店新建，2：门店拉黑不可用，3：门店从黑名单中移除
     */
    private Integer changeType;

    /**
     * 门店切换前所属大客户admin_id,NULL切换前为其它类型;门店拉黑
     */
    private Long oldAdmin;

    /**
     * 门店切换后所属大客户admin_id,NULL切换后为其它类型;门店新建;门店从黑名单移除
     */
    private Long newAdmin;

    /**
     * 门店切换操作人，admin表内的admin_id
     */
    private Long creatorId;

    /**
     * 门店切换记录时间
     */
    private LocalDateTime createTime;
}