package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2021/5/7  10:19
 */
@ApiModel(description = "预购单信息")
@Data
public class AdvancePurchase {


    private Integer id;

    @ApiModelProperty(value = "添加时间")
    private LocalDateTime addTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    @ApiModelProperty(value = "申请总金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "可用金额")
    private BigDecimal useAmount;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "申请人id")
    private  Integer adminId;

    @ApiModelProperty(value = "打款凭证")
    private String certificate;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "结算方式关联id")
    private Integer supplierAccountId;


}
