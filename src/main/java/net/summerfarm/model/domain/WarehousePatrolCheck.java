package net.summerfarm.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2022/1/7  11:26
 */
@Data
public class WarehousePatrolCheck {

    private Integer id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
    * 库存仓编号
    */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 巡检时间
     */
    private LocalDateTime checkTime;

    /**
    * 类型
    */
    private Integer type;
}
