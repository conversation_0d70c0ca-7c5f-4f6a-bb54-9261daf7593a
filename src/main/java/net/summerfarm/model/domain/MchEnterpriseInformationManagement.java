package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * mch_enterprise_information_management
 * <AUTHOR>
 */
@ApiModel(value="net.summerfarm.model.domain.MchEnterpriseInformationManagement用户企业信息管理表")
@Data
public class MchEnterpriseInformationManagement implements Serializable {
    /**
     * 自增长主键
     */
    @ApiModelProperty(value="自增长主键")
    private Long id;

    /**
     * 取自merchant表中m_id
     */
    @ApiModelProperty(value="取自merchant表中m_id")
    private Long mId;

    /**
     * 企业工商名称
     */
    @ApiModelProperty(value="企业工商名称")
    private String invoiceTitle;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value="统一社会信用代码")
    private String taxNumber;

    /**
     * 0:生效中（默认), 1:(失效)
     */
    @ApiModelProperty(value="0:生效中（默认), 1:(失效)")
    private Byte validStatus;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 联系方式
     */
    @ApiModelProperty(value="联系方式")
    private String linkMethod;

    /**
     * 法人
     */
    @ApiModelProperty(value="法人")
    private String legalPersonName;

    /**
     * 验证 0:不需审核, 1:需要审核
     */
    @ApiModelProperty(value="验证 0:不需审核, 1:需要审核")
    private Integer verification;

    /**
     * 营业执照地址
     */
    @ApiModelProperty(value="营业执照地址")
    private String businessLicenseAddress;

    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    private String creator;

    /**
     * 修改人
     */
    @ApiModelProperty(value="修改人")
    private String updater;

    private static final long serialVersionUID = 1L;
}