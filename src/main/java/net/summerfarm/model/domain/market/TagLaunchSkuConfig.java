package net.summerfarm.model.domain.market;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TagLaunchSkuConfig implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 基本信息id
     */
    private Long infoId;

    /**
     * sku或者类目id
     */
    private String bizId;

    /**
     * 最后一次修改人id
     */
    private Integer updaterId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}