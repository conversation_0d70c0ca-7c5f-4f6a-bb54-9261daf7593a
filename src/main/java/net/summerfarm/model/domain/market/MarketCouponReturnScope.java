package net.summerfarm.model.domain.market;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/11/15 14:35
 * @PackageName:net.summerfarm.model.domain.market
 * @ClassName: MarketCouponReturnScope
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class MarketCouponReturnScope {

    /**
     * primary key
     */
    private Long id;

    /**
     * 卡券返券规则ID
     */
    private Long marketCouponReturnId;

    /**
     * 范围id （人群包：merchant_pool_info主键ID，运营城市：areaNo，运营大区：largeAreaNo）
     */
    private Long scopeId;

    /**
     * 活动范围类型，1 人群包，2 运营城市，3 运营大区
     */
    private Integer scopeType;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
}
