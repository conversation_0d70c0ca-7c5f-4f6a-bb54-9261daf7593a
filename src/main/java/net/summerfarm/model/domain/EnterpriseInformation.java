package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * enterprise_information
 * <AUTHOR> 2021/11/17
 */
@ApiModel(value="企业信息表")
@Data
public class EnterpriseInformation implements Serializable {

    private Long id;

    /**
     * 注册号
     */
    @ApiModelProperty(value="注册号")
    private String regNumber;

    /**
     * 经营状态
     */
    @ApiModelProperty(value="经营状态")
    private String regStatus;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value="统一社会信用代码")
    private String creditCode;

    /**
     * 成立日期
     */
    @ApiModelProperty(value="成立日期")
    private String estiblishTime;

    /**
     * 注册资本
     */
    @ApiModelProperty(value="注册资本")
    private String regCapital;

    /**
     * 公司类型 1-公司，2-香港公司，3-社会组织，4-律所，5-事业单位，6-基金会，9-新机构
     */
    @ApiModelProperty(value="公司类型 1-公司，2-香港公司，3-社会组织，4-律所，5-事业单位，6-基金会，9-新机构")
    private Short companyType;

    /**
     * 公司名
     */
    @ApiModelProperty(value="公司名")
    private String name;

    /**
     * 组织机构代码
     */
    @ApiModelProperty(value="组织机构代码")
    private String orgNumber;

    /**
     * 1-公司 2-人
     */
    @ApiModelProperty(value="1-公司 2-人")
    private Integer type;

    /**
     * 省份
     */
    @ApiModelProperty(value="省份")
    private String base;

    /**
     * 法人
     */
    @ApiModelProperty(value="法人")
    private String legalPersonName;

    /**
     * 匹配原因
     */
    @ApiModelProperty(value="匹配原因")
    private String matchType;

    /**
     * 添加时间
     */
    @ApiModelProperty(value="添加时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    private String creator;

    /**
     * 状态 0 存在，1 作废
     */
    @ApiModelProperty(value="状态 0 存在，1 作废")
    private Integer status;

    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间")
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @ApiModelProperty(value="修改人")
    private String updater;

    private static final long serialVersionUID = 1L;
}