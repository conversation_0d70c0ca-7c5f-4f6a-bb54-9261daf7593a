package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * products_property
 *
 * <AUTHOR>
@ApiModel(description = "商品属性")
@Data
public class ProductsProperty implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 属性名
     */
    @ApiModelProperty(value = "属性名")
    private String name;

    /**
     * 属性类型：0、关键属性 1、销售属性
     */
    @ApiModelProperty(value = "属性类型：0、关键属性 1、销售属性")
    private Integer type;

    /**
     * 格式类型：0、复合型 1、数字+单位 2、选择字符串 3、自定义字符串
     */
    @ApiModelProperty(value = "格式类型：0、复合型 1、数字+单位 2、选择字符串 3、自定义字符串")
    private Integer formatType;

    /**
     * 格式，根据类型不同处理，eg：
        0/1/2:["g","kg","箱"]
        3:["越南","海南","台湾"]
     */
    @ApiModelProperty(value = "格式，根据类型不同处理，eg：\n" +
            "        0/1/2:[\"g\",\"kg\",\"箱\"]\n" +
            "        3:[\"越南\",\"海南\",\"台湾\"]")
    private String formatStr;

    /**
     * 状态：0、失效 1、有效
     */
    @ApiModelProperty(value = "状态：0、失效 1、有效")
    private Integer status;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 类目id/pd id
     */
    private Integer mappingId;

    public String findMappingId() {
        if (Objects.isNull(mappingId)) {
            return null;
        }
        return mappingId.toString();
    }

    private static final long serialVersionUID = 1L;
}