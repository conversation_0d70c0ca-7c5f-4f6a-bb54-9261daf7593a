package net.summerfarm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * carrier_quotation_area
 * <AUTHOR>
@Data
public class CarrierQuotationArea implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 承运商报价单id
     */
    private Long carrierQuotationId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 县
     */
    private String county;

    private static final long serialVersionUID = 1L;
}