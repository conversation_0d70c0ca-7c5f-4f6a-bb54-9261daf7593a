package net.summerfarm.model.domain;

import lombok.Data;


/**
 * 发放记录详情
 */
@Data
public class MarketCouponSendDetail {

    private Long id;

    /**
     * 发放记录id
     */
    private Long sendId;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 状态 0：未发放 1：已发放 2：已撤回 3：已取消/发放失败
     */
    private Integer status;

    /**
     * 门店类型
     */
    private String size;

}
