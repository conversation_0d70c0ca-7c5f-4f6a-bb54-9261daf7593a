package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2022/2/22
 */
@Data
@ApiModel(description = "调拨单配置备份表")
public class StockAllocationConfigRecord implements Serializable {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "物流信息配置id")
    private Integer configId;

    @ApiModelProperty(value = "调拨单编号")
    private String listNo;

    @ApiModelProperty(value = "调出仓编号")
    private Integer warehouseNo;

    @ApiModelProperty(value = "调入仓编号")
    private Integer allocationWarehouseNo;

    @ApiModelProperty(value = "周期类型：0、天 1、周 2、每两周")
    private Integer cycleType;

    @ApiModelProperty(value = "配送时间：星期")
    private Integer logisticsTime;

    @ApiModelProperty(value = "是否次日达：0、是  1、不是 （空表示次日达）")
    private Integer nextDayArrive;

    @ApiModelProperty(value = "销售参与：0.不参与1.参与")
    private Integer salePartake;

    @ApiModelProperty(value = "采购参与：0.不参与1.参与")
    private Integer purchasePartake;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "销量预估天数")
    private Integer intervalDays;

    @ApiModelProperty(value = "需求销量开始时间")
    private LocalDateTime saleQuantityStart;

    @ApiModelProperty(value = "需求销量结束时间")
    private LocalDateTime saleQuantityEnd;
}
