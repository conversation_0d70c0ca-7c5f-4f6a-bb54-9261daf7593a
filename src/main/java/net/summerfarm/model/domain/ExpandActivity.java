package net.summerfarm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import net.summerfarm.enums.ExpandActivityStatusEnum;
import net.summerfarm.model.DTO.market.ExpandActivityConfigDTO;

/**
 * expand_activity
 * <AUTHOR>
@Data
public class ExpandActivity implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 状态 0停用，1启用
     */
    private Integer status;

    /**
     * 0指固定时间间隔到期，1长期有效
     */
    private Byte type;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 特惠有效期-单位：分钟
     */
    private Integer validityPeriod;

    /**
     * 触发条件-0首次，1每次
     */
    private Byte triggerTimes;

    /**
     * 触发条件-T+1有带配送的省心送计划0不勾选，1勾选
     */
    private Byte pendingDelivery;

    /**
     * 活动商品枚举-流失风险商品0不勾选，1勾选
     */
    private Byte churnRisk;

    /**
     * 活动商品枚举-召回商品0不勾选，1勾选
     */
    private Byte recall;

    /**
     * 活动商品枚举-拉新商品0不勾选，1勾选
     */
    private Byte pullNew;

    /**
     * 每件商品限购数量-单位：件
     */
    private Integer purchaseLimit;

    /**
     * 每件商品优惠幅度等于毛利的百分之X
     */
    private BigDecimal discountPercentage;

    /**
     * 每件商品最多优惠X元
     */
    private BigDecimal discount;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 备注
     */
    private String remark;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记,0未删除，1已删除
     */
    private Byte delFlag;

    private static final long serialVersionUID = 1L;

    public static ExpandActivity build(ExpandActivityConfigDTO configDto, String adminId) {
        ExpandActivity config = new ExpandActivity();
        config.setName(configDto.getName());
        config.setStatus(ExpandActivityStatusEnum.DISABLE.getId());
        config.setType(configDto.getType());
        config.setStartTime(configDto.getStartTime());
        config.setEndTime(configDto.getEndTime());
        config.setValidityPeriod(configDto.getValidityPeriod());
        config.setTriggerTimes(configDto.getTriggerTimes());
        config.setPendingDelivery(configDto.getPendingDelivery());
        config.setChurnRisk(configDto.getChurnRisk());
        config.setRecall(configDto.getRecall());
        config.setPullNew(configDto.getPullNew());
        config.setPurchaseLimit(configDto.getPurchaseLimit());
        config.setDiscountPercentage(configDto.getDiscountPercentage());
        config.setDiscount(configDto.getDiscount());
        config.setRemark(configDto.getRemark());
        config.setCreator(adminId);
        return config;
    }
}