package net.summerfarm.model.domain;

import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * invoice_merchant_relation
 * <AUTHOR>
@Data
public class InvoiceMerchantRelation implements Serializable {

    private static final long serialVersionUID = -3925507101967024542L;

    /**
     * 自增长主键
     */
    private Long id;

    /**
     * 取自invoice_config表内
     */
    private Long invoiceId;

    /**
     * 取自merchant表中m_id
     */
    private Long merchantId;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime updateTime;

    /**
     * 创建关联关系提交时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String update;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 状态 0 存在 1 作废
     */
    private Integer status;

}