package net.summerfarm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * crm_merchant_month_gmv
 * <AUTHOR>
@Data
public class CrmMerchantMonthGmv implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 商户名
     */
    private String merchantName;

    /**
     * 商户所在运营区域
     */
    private Integer areaNo;

    /**
     * 商户总gmv
     */
    private BigDecimal merchantTotalGmv;

    /**
     * 配送gmv
     */
    private BigDecimal distributionGmv;

    /**
     * 配送客单价
     */
    private BigDecimal deliveryUnitPrice;

    /**
     * 配送次数
     */
    private Integer distributionAmout;

    /**
     * 鲜果gmv
     */
    private BigDecimal fruitGmv;

    /**
     * 乳制品gmv
     */
    private BigDecimal dairyGmv;

    /**
     * 非乳制品gmv
     */
    private BigDecimal nonDairyGmv;

    /**
     * 自营品牌gmv
     */
    private BigDecimal brandGmv;

    /**
     * 核心客户标记:否(0),是(1)
     */
    private Byte coreMerchantTag;

    /**
     * 数据所在月份标记::yyyyMM
     */
    private Byte monthTag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}