package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR> 2021/07/09
 * 完成配送提醒
 */
@Data
public class CompleteDelivery {

    private Integer id;

    @ApiModelProperty(value = "物流中心编号（配送仓编号)")
    private Integer storeNo;

    @ApiModelProperty(value = "城市编号")
    private Integer areaNo;

    @ApiModelProperty(value = "配送完成时间")
    private String completeDeliveryTime;

    @ApiModelProperty(value = "状态 0 正常 1 暂停")
    private Integer status;

    @ApiModelProperty(value = "更新人")
    private String updater;

    @ApiModelProperty(value = "更新时间")
    private LocalDate updateTime;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private LocalDate createTime;

    @ApiModelProperty(value = "区域")
    private String region;

    @ApiModelProperty(value = "城市")
    private String city;

}
