package net.summerfarm.model.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * @Description
 * @Date 2023/2/6 15:58
 * @<AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StockTaskProcessOrderSkuDO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 出库单ID
     */
    private Long stockTaskProcessId;

    /**
     * 出库任务ID
     */
    private Long stockTaskId;

    /**
     * 外部订单号
     */
    private String outOrderNo;

    /**
     * sku编码
     */
    private String sku;

    /**
     * sku数量
     */
    private Integer quantity;

    /**
     * 采购批次
     */
    private String purchaseBatch;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Long gmtCreated;

    /**
     * 更新时间
     */
    private Long gmtModified;

    /**
     * 是否软删
     */
    private Integer isDeleted;

    /**
     * 最新版本号
     */
    private Integer lastVer;
}
