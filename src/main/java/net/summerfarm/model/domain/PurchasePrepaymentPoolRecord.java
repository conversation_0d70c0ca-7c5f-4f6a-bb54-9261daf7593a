package net.summerfarm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 预付池记录表(PurchasePrepaymentPoolRecord)实体类
 *
 * <AUTHOR>
 * @since 2022-01-19 17:51:18
 */
@Data
public class PurchasePrepaymentPoolRecord {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 供应商id
     */
    private Integer supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 来源单据id
     */
    private Long billId;
    /**
     * 来源类型 1、预付单 2、对账单 3、退款单
     */
    private Integer type;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
