package net.summerfarm.model.domain;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * sku绑定关系表
 */
@Data
public class InventoryBind {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * spuid(products.pd_id)
     */
    private Long pdId;

    /**
     * 商品编号
     */
    private String sku;

    /**
     * 绑定sku编号
     */
    private String bindSku;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}