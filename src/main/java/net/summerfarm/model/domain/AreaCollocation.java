package net.summerfarm.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 搭配购的应用城市
 */
@Data
public class AreaCollocation {

    private Integer id;

    /**
     * 搭配购id
     */
    private Integer collocationId;

    /**
     * 城市id
     */
    private Integer areaNo;

    /**
     * 城市名称
     */
    private String areaName;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     *更新人
     */
    private Integer updater;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
