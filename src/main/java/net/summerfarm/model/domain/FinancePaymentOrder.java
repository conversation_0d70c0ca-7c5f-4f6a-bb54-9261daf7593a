package net.summerfarm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 付款单表(FinancePaymentOrder)实体类
 * finance_payment_order
 * <AUTHOR>
 * @since 2022-01-19 17:42:37
 */
@Data
public class FinancePaymentOrder implements Serializable {
    private static final long serialVersionUID = -73780654287068462L;
    /**
     * 主键id
     */
    private Long id;
    /**
     * 来源单号 预付单/对账单id/打款单id
     */
    private Long additionalId;
    /**
     * 来源类型 1预付单 2 对账单 3 打款单
     */
    private Integer type;
    /**
     * 发票销售方id
     */
    private Integer supplierId;
    /**
     * 发票销售方名称
     */
    private String supplierName;
    /**
     * 付款金额
     */
    private BigDecimal amount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 付款单状态 0、已作废 1、待付款 2、已付款（已完成） 3、付款审核中(2022-07-25)
     */
    private Integer status;
    /**
     * delete_reason
     */
    private Integer deleteReason;
    /**
     * 发起人
     */
    private String creator;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    private String updater;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 申请时间
     */
    private LocalDateTime applicationTime;
    /**
     * 审批通过时间
     */
    private LocalDateTime approvalTime;
    /**
     * 付款凭证
     */
    private String paymentVoucher;

    public FinancePaymentOrder() {
    }

    public FinancePaymentOrder(Long id, Long additionalId, Integer type, BigDecimal amount, String remark, Integer status, String creator, LocalDateTime createTime, String updater, LocalDateTime updateTime, String paymentVoucher) {
        this.id = id;
        this.additionalId = additionalId;
        this.type = type;
        this.amount = amount;
        this.remark = remark;
        this.status = status;
        this.creator = creator;
        this.createTime = createTime;
        this.updater = updater;
        this.updateTime = updateTime;
        this.paymentVoucher = paymentVoucher;
    }
}
