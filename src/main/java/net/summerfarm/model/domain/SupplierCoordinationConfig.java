package net.summerfarm.model.domain;

import lombok.Builder;
import lombok.Data;

import javax.persistence.Access;
import java.time.LocalDateTime;

@Data
@Builder
public class SupplierCoordinationConfig {

    private Long id;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 采购订单是否协同
     */
    private Integer poCoordinationTab;

    /**
     * 入库单是否协同
     */
    private Integer inboundCoordinationTab;
    /**
     * 对账单是否协同
     */
    private Integer reconciliationCoordinationTab;

    /**
     * 发票是否协同
     */
    private Integer invoiceCoordinationTab;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新操作人
     */
    private String updater;

    /**
     * 创建人
     */
    private String creator;
}
