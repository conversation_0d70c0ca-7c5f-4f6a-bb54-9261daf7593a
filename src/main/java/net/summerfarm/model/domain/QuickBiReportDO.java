package net.summerfarm.model.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * quickBi数据报表信息
 * <AUTHOR>
 * @Create 2020-09-14
 */
public class QuickBiReportDO extends BaseDO {

    /** 报表的id */
    private String pageId;

    /** 阿里子账号 */
    private String aliyunId;

    /** 备注 */
    private String remark;

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAliyunId() {
        return aliyunId;
    }

    public void setAliyunId(String aliyunId) {
        this.aliyunId = aliyunId;
    }

    @Override
    public String toString() {
        return super.toString();
    }

}
