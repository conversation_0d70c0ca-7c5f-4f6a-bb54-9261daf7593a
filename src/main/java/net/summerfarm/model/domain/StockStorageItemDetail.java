package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel(description = "入库任务条目明细实体类")
public class StockStorageItemDetail implements Serializable {
    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "入库条目id")
    private Integer stockStorageItemId;

    @ApiModelProperty(value = "采购单号")
    private String purchaseNo;

    @ApiModelProperty(value = "实际调入数量")
    private Integer actualInQuantity;

    @ApiModelProperty(value = "保质期")
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate qualityDate;

    @ApiModelProperty(value = "入库数量")
    private Integer quantity;

    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate productionDate;

    @ApiModelProperty(value = "货位编号")
    private String glNo;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;

}