package net.summerfarm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class MarketRuleDetail implements Serializable {

    private Integer id;

    private Integer ruleId;


    private Integer categoryId;

    private String categoryName;

    private String sku;

    private String pdName;

    private String weight;

    /**
     * 返现门槛
     */
    private BigDecimal threshold;
}
