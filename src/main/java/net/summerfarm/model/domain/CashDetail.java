package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-06-28
 * @description
 */
@ApiModel(description = "提现审核实体类")
@Data
public class CashDetail extends Cash {
    /**
     * 商户名
     */
    @ApiModelProperty("商户名")
    private String mname;
    /**
     * 城市编号
     */
    private String areaNo;
    /**
     * 城市名
     */
    @ApiModelProperty("城市名")
    private String areaName;
    /**
     * 实际支付金额
     */
    @ApiModelProperty("订单总实付金额")
    private BigDecimal payAmount;

    /**
     * 子账号名称
     */
    @ApiModelProperty("子账号名称")
    private String accountName;
}
