package net.summerfarm.model.domain.plan;

import java.util.Date;

public class ProcessConfig {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.id
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.create_time
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.update_time
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.process_code
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private String processCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.process_type
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private Integer processType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.ding_code
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private String dingCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.feishu_code
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private String feishuCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.access_platform_type
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private String accessPlatformType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.create_user_id
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private Long createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.create_user_name
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private String createUserName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.update_user_id
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private Long updateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.update_user_name
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private String updateUserName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_config.remark
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    private String remark;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.id
     *
     * @return the value of process_config.id
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.id
     *
     * @param id the value for process_config.id
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.create_time
     *
     * @return the value of process_config.create_time
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.create_time
     *
     * @param createTime the value for process_config.create_time
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.update_time
     *
     * @return the value of process_config.update_time
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.update_time
     *
     * @param updateTime the value for process_config.update_time
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.process_code
     *
     * @return the value of process_config.process_code
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public String getProcessCode() {
        return processCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.process_code
     *
     * @param processCode the value for process_config.process_code
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setProcessCode(String processCode) {
        this.processCode = processCode == null ? null : processCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.process_type
     *
     * @return the value of process_config.process_type
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public Integer getProcessType() {
        return processType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.process_type
     *
     * @param processType the value for process_config.process_type
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setProcessType(Integer processType) {
        this.processType = processType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.ding_code
     *
     * @return the value of process_config.ding_code
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public String getDingCode() {
        return dingCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.ding_code
     *
     * @param dingCode the value for process_config.ding_code
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setDingCode(String dingCode) {
        this.dingCode = dingCode == null ? null : dingCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.feishu_code
     *
     * @return the value of process_config.feishu_code
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public String getFeishuCode() {
        return feishuCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.feishu_code
     *
     * @param feishuCode the value for process_config.feishu_code
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setFeishuCode(String feishuCode) {
        this.feishuCode = feishuCode == null ? null : feishuCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.access_platform_type
     *
     * @return the value of process_config.access_platform_type
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public String getAccessPlatformType() {
        return accessPlatformType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.access_platform_type
     *
     * @param accessPlatformType the value for process_config.access_platform_type
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setAccessPlatformType(String accessPlatformType) {
        this.accessPlatformType = accessPlatformType == null ? null : accessPlatformType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.create_user_id
     *
     * @return the value of process_config.create_user_id
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public Long getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.create_user_id
     *
     * @param createUserId the value for process_config.create_user_id
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.create_user_name
     *
     * @return the value of process_config.create_user_name
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public String getCreateUserName() {
        return createUserName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.create_user_name
     *
     * @param createUserName the value for process_config.create_user_name
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName == null ? null : createUserName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.update_user_id
     *
     * @return the value of process_config.update_user_id
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public Long getUpdateUserId() {
        return updateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.update_user_id
     *
     * @param updateUserId the value for process_config.update_user_id
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.update_user_name
     *
     * @return the value of process_config.update_user_name
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public String getUpdateUserName() {
        return updateUserName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.update_user_name
     *
     * @param updateUserName the value for process_config.update_user_name
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName == null ? null : updateUserName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column process_config.remark
     *
     * @return the value of process_config.remark
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column process_config.remark
     *
     * @param remark the value for process_config.remark
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }
}