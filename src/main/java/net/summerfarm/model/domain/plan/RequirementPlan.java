package net.summerfarm.model.domain.plan;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
public class RequirementPlan {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 需求计划编号
     */
    private String requirementPlanNo;

    /**
     * 需求名称
     */
    private String requirementName;

    /**
     * 冻结开间
     */
    private LocalDate replenishmentFreezeStartTime;

    /**
     * 冻结结间
     */
    private LocalDate replenishmentFreezeEndTime;

    /**
     * 计划开间
     */
    private LocalDate requirementStartTime;

    /**
     * 计划结间
     */
    private LocalDate requirementEndTime;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}