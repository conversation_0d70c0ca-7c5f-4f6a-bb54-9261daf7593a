package net.summerfarm.model.domain;

/**
 * <AUTHOR> ct
 * create at:  2020/9/22  15:36
 */

import lombok.Data;
import net.summerfarm.model.input.AOLOrderInput;
import java.time.LocalDateTime;

import java.time.LocalDate;
import java.util.List;

@Data
public class OrderOuterInfo {


    /**
    *
    */
    private Integer id;

    /**
     * AOL的门店id
     */
    private String mId;

    /**
     * AOL订单编号
     */
    private String orderNo;

    /**
    * 创建时间
    */
    private LocalDateTime gmtCreate;

    /**
     * 配送日期
     */
    private LocalDate deliveryDate;

    /**
     * 联系方式
     */
    private String mphone;

    /**
     * 规格
     */
    private String standard;
    /**
     * 单位
     */
    private String unit;

    /**
     * 名称
     */
    private String name;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 地址
     */
    private String detailedAddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * sku
     */
    private String sku;

    /**
     * 数量
     */
    private Integer amount;

    /**
    * 配送地址
    */
    private Long contactId;

    /**
    * 鲜沐订单号
    */
    private String xmOrderNo;

    /**
     * 配送状态
     */
    private Integer deliveryStatus;

    /**
     * 鲜沐mid
     */
    private Long xMId;

    /**
     * 订单同步状态
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 订单同步成功时间
     */
    private LocalDateTime orderSuccessTime;

    /**
     * 订单明细
     */
    private List<OrderOuterItem> orderOuterItemList;

    /**
     * 品牌名称
     */
    private String adminName;

    /**
     * 外部平台id
     */
    private Integer outerPlatformId;

    public  OrderOuterInfo(){

    }

    public OrderOuterInfo(AOLOrderInput aolOrderInput){
        this.mId = aolOrderInput.getMId();
        this.orderNo = aolOrderInput.getOrderNo();
        this.deliveryDate = aolOrderInput.getDeliveryDate();
        this.mphone = aolOrderInput.getMphone();
        this.province = aolOrderInput.getProvince();
        this.city = aolOrderInput.getCity();
        this.area = aolOrderInput.getArea();
        this.address = aolOrderInput.getAddress();
        this.remark = aolOrderInput.getRemark();
    }
}

