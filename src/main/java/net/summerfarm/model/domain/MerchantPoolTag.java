package net.summerfarm.model.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import net.summerfarm.enums.market.FieldTypeEnum;

/**
 * <AUTHOR>
 */
@Data
public class MerchantPoolTag implements Serializable {

    private Long id;

    /**
     * 规则标签名称,例：tag_long_date_last_buy_time
     */
    private String name;

    /**
     * 规则标签描述
     */
    private String description;

    /**
     * 关系符号,枚举值逗号分割
     */
    private String relationSymbol;

    /**
     * 规则字段类型,0 单值, 1 数组
     * @see FieldTypeEnum
     */
    private Integer fieldType;

    /**
     * 规则属性类型,0 基本属性,1 行为属性
     */
    private Integer type;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}