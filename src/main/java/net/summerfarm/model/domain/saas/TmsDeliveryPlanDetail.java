package net.summerfarm.model.domain.saas;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2022/5/12  15:52
 * 配送单详情
 */
@Data
public class TmsDeliveryPlanDetail {

    private Integer id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * tms配送单id
     */
    private Integer tmsDeliveryPlanId;

    /**
     * sku
     */
    private String  sku;

    /**
     * 数量
     */
    private Integer amount;


    /**
    * status
    */
    private Integer status;

    /**
     * 类型 配送
     */
    private Integer deliveryType;
}
