package net.summerfarm.model.domain.saas;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * saas_orders
 * <AUTHOR>
@Data
public class SaasOrders implements Serializable {
    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 品牌方名称
     */
    private String tenantName;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 订单时间
     */
    private Date orderTime;

    /**
     * 下单金额
     */
    private BigDecimal totalPrice;

    /**
     * 归属BD
     */
    private String belongDb;

    /**
     * 订单状态
     */
    private Integer status;

    private static final long serialVersionUID = 1L;
}