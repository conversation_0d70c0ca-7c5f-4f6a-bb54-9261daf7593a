package net.summerfarm.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR> ct
 * create at:  2019/8/20  11:31 AM
 * 库存金额成本表
 */
@Data
public class BiDayCost {

    /**
    * id自增
    */
    private Integer id;

    /**
    * sku
    */
    private String sku;

    /**
    * 仓库
    */
    private Integer storeNo;

    /**
    * 库存金额
    */
    private BigDecimal skuCost;

    /**
    * 日期
    */
    private LocalDate addDate;
}
