package net.summerfarm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * finance_invoice_partner
 * <AUTHOR>
 */
@Data
public class FinanceInvoicePartner implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 合作方id
     */
    private Integer supplierId;

    /**
     * 合作方类型 0、供应商 1、承运商
     */
    private Integer supplierType;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 发起人adminId
     */
    private Integer creatorAdminId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}