package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "用户生命周期实体类")
public class MerchantLifecycle implements Serializable {
    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "商户id")
    private Long mId;

    @ApiModelProperty(value = "生命周期，0新注册，1首单，2非稳，3稳定")
    private Byte lifecycle;

    @ApiModelProperty(value = "标签")
    private String tag;

    @ApiModelProperty(value = "距离下次采购时间间隔")
    private Integer purchasingCycleLeft;

    @ApiModelProperty(value = "优惠券失效时间")
    private Integer couponExpireDate;

    @ApiModelProperty(value = "上次下单时间")
    private Date lastOrderTime;

    @ApiModelProperty(value = "添加时间")
    private Date addTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public Byte getLifecycle() {
        return lifecycle;
    }

    public void setLifecycle(Byte lifecycle) {
        this.lifecycle = lifecycle;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag == null ? null : tag.trim();
    }

    public Integer getPurchasingCycleLeft() {
        return purchasingCycleLeft;
    }

    public void setPurchasingCycleLeft(Integer purchasingCycleLeft) {
        this.purchasingCycleLeft = purchasingCycleLeft;
    }

    public Integer getCouponExpireDate() {
        return couponExpireDate;
    }

    public void setCouponExpireDate(Integer couponExpireDate) {
        this.couponExpireDate = couponExpireDate;
    }

    public Date getLastOrderTime() {
        return lastOrderTime;
    }

    public void setLastOrderTime(Date lastOrderTime) {
        this.lastOrderTime = lastOrderTime;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

}