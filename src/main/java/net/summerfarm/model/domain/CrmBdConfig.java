package net.summerfarm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * crm_bd_config
 * <AUTHOR>
@Data
public class CrmBdConfig implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * bd id
     */
    private Integer adminId;

    /**
     * gmv基础值
     */
    private BigDecimal gmvBase;

    /**
     * gmc目标值
     */
    private BigDecimal gmvTarget;
    /**
     * 月活目标
     */
    private Integer monthOrderTarget;

    /**
     * 私海上限
     */
    private Integer privateSeaLimit;

    /**
     * 客情上限
     */
    private Integer quotaLimit;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;
    /**
     * 核心客户数基础值
     */
    private Integer coreMerchantAmount;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 所属行政城市
     */
    private String administrativeCity;

    private static final long serialVersionUID = 1L;
}