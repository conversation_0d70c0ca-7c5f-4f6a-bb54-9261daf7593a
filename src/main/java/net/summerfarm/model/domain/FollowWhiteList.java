package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> ct
 * create at:  2019/7/23  11:10 AM
 */
@Data
@ApiModel("用户白名单")
public class FollowWhiteList {

    //可用
    public static final Integer IS_WHITELIST  = 1;

    //不可用
    public static final Integer NO_WHITELIST = 0;

    /**
    * id
    */
    private Integer id;

    /**
     * 用户ID
     */
    private Long mId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 状态 0 不可用 1 可用
     */
    private Integer status;


}
