package net.summerfarm.model.domain;

import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class SkuMapping implements Serializable {

    @NotNull(message = "id.not.null",groups = {Update.class})
    private Integer id;

    @NotNull(message = "adminId.not.null",groups = {Add.class,Update.class})
    private Integer adminId;

    @NotNull(message = "sku.not.null",groups = {Add.class,Update.class})
    private String sku;

    private LocalDateTime addtime;

    private Integer status;

    @NotNull(message = "mapping.not.null",groups = {Add.class, Update.class})
    private String mapping;

    @NotNull(message = "mappingName.not.null",groups = {Add.class,Update.class})
    private String mappingName;

    /**
     * 影响大客户账单开关 0：关；1：开
     */
    private Integer kaBillSwitch;

    /**
     * 外部平台id
     */
    private Integer outerPlatformId;

}
