package net.summerfarm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class DistributionRules implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 规则类型  1：门店管理  2：品牌管理  3：服务区域
     */
    private Integer type;

    /**
     * 类型ID  服务区域、地址、品牌主键ID
     */
    private Long typeId;

    /**
     * 城市编号-针对品牌管理类型   0代表全部城市
     */
    private Integer areaNo;

    /**
     * 履约时效  0：T+1    1：T+N
     */
    private Integer ageing;

    /**
     * 商品类目  1：全部商品 2：乳制品商品  3：非乳制品商品
     */
    private Integer productType;

    /**
     * 门槛类型 1：金额  2：件数
     */
    private Integer sillType;

    /**
     * 件数  针对门槛类型为件数
     */
    private Integer number;

    /**
     * 金额 针对门槛类型为金额
     */
    private BigDecimal amount;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 快递费
     */
    private BigDecimal expressFee;

    /**
     * 创建人
     */
    private String creator;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}