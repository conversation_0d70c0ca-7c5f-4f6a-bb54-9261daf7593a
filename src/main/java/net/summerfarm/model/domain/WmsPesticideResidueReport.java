package net.summerfarm.model.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2022/4/27  15:19
 * 农药残留报告
 */
@Data
public class WmsPesticideResidueReport implements Serializable {

	/**
	* 状态 生效
	*/
	public static final Integer REPORT_STATUS_HAVE = 0;

	/**
	 * 失效
	 */
	public static final Integer REPORT_STATUS_VLI = 1;


	private Integer id;

	private LocalDateTime createTime;

	private LocalDateTime updateTime;
    /**
     * '批次'
     */
	private  String batch;
    /**
     * sku
     */
	private String sku;

	/**
	 * 状态处理
	 */
	private  Integer status;

    /**
     * '检测结果 0 合格 1 不合格'
     */
	private Integer detectionResult;

    /**
     * '抽样基数'
     */
	private BigDecimal  samplingBase ;
    /**
     * '抽样数'
     */
	private BigDecimal numberSamples;
    /**
     * '抑制率'
     */
	private BigDecimal inhibitionRate;

    /**
     * 图片
     */
	private String pictureUrl;


	/**
	* 保质期
	*/
	private LocalDate productionDate;


	public WmsPesticideResidueReport(){}

	public WmsPesticideResidueReport(String sku,String  batch,LocalDate productionDate){
		this.sku = sku;
		this.batch = batch;
		this.productionDate = productionDate;
	}


}
