package net.summerfarm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * admin_skin
 * <AUTHOR>
@Data
public class AdminSkin implements Serializable {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * admin表id
     */
    private Integer adminId;

    /**
     * 是否展示
     */
    private Boolean showFlag;

    /**
     * logo图片
     */
    private String logo;

    /**
     * 背景图
     */
    private String backgroundImage;

    /**
     * 创建日期
     */
    private LocalDateTime createTime;

    public AdminSkin() {
    }

    public AdminSkin(Integer adminId, Boolean showFlag, String logo, String backgroundImage, LocalDateTime createTime) {
        this.adminId = adminId;
        this.showFlag = showFlag;
        this.logo = logo;
        this.backgroundImage = backgroundImage;
        this.createTime = createTime;
    }

    private static final long serialVersionUID = 1L;
}