package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "出库任务条目明细实体类")
public class StockShipmentItemDetail implements Serializable {
    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "出库条目id")
    private Integer stockShipmentItemId;

    @ApiModelProperty(value = "采购单号")
    private String purchaseNo;

    @ApiModelProperty(value = "实际调出数量")
    private Integer actualOutQuantity;

    @ApiModelProperty(value = "保质期")
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate qualityDate;

    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate productionDate;

    @ApiModelProperty(value = "货位编号")
    private String glNo;

    @ApiModelProperty(value = "库位编码")
    private String cabinetCode;

}