package net.summerfarm.model.domain.easyexcel.bms;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2022/9/5
 */
@Data
public class BmsPaymentDocumentListExcel {

    /**
     * 打款单编号
     */
    @ExcelProperty(value = "打款单编号", index = 0)
    @ColumnWidth(20)
    private String paymentNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "承运商", index = 1)
    private String carrierName;

    @ColumnWidth(20)
//    @ExcelProperty(value = "打款金额", index = 2)
    private BigDecimal paymentAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "应付金额", index = 2)
    private BigDecimal totalAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "开票状态", index = 3)
    private String invoiceStatus;

    @ColumnWidth(20)
    @ExcelProperty(value = "打款状态", index = 4)
    private String paymentStatus;

    @ColumnWidth(30)
    @ExcelProperty(value = "任务生成时间", index = 5)
    private String createTime;
    @ColumnWidth(30)

    @ExcelProperty(value = "结算月份", index = 6)
    private String settleMonth;

    @ColumnWidth(15)
    @ExcelProperty(value = "城配仓名称", index = 7)
    private String storeName;


}
