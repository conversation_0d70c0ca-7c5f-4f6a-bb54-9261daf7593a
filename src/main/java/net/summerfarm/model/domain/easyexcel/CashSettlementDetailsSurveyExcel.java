package net.summerfarm.model.domain.easyexcel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * 现结明细概况sheeteasyexcel实体类
 */
@Data
@EqualsAndHashCode
public class CashSettlementDetailsSurveyExcel {

    @ColumnWidth(30)
    @ExcelProperty(value = "本期初", index = 0)
    private String startTime;

    @ColumnWidth(30)
    @ExcelProperty(value = "本期末", index = 1)
    private String endTime;

    @ColumnWidth(50)
    @ExcelProperty(value = "收入总额(不含税)", index = 2)
    private String totalRevenue;

    @ColumnWidth(50)
    @ExcelProperty(value = "收入的税额", index = 3)
    private String taxAmount;

    @ColumnWidth(50)
    @ExcelProperty(value = "含税收入", index = 4)
    private String taxIncludedIncome;

    @ColumnWidth(50)
    @ExcelProperty(value = "总成本", index = 5)
    private String totalCost;

    @ColumnWidth(50)
    @ExcelProperty(value = "售后总额", index = 6)
    private String totalAfterSales;

    @ColumnWidth(50)
    @ExcelProperty(value = "售后冲减收入", index = 7)
    private String afterSalesOffsetIncome;

    @ColumnWidth(50)
    @ExcelProperty(value = "售后税额", index = 8)
    private String afterSalesTax;

    @ColumnWidth(50)
    @ExcelProperty(value = "营销费用总额", index = 9)
    private String totalMarketingExpenses;

    @ColumnWidth(50)
    @ExcelProperty(value = "已收款未确认收入金额", index = 10)
    private String unrecognizedRevenueReceived;

}
