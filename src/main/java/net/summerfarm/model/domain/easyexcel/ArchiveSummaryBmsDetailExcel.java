package net.summerfarm.model.domain.easyexcel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @title: ArchiveSummaryBmsDetailExcel
 * BMS 票夹明细
 * @date 2022/8/3014:37
 */
@Data
@EqualsAndHashCode
public class ArchiveSummaryBmsDetailExcel {

    @ColumnWidth(50)
    @ExcelProperty(value = "发票号码", index = 0)
    private String invoiceNumber;

    @ColumnWidth(50)
    @ExcelProperty(value = "供应商", index = 1)
    private String supplier;

    @ColumnWidth(50)
    @ExcelProperty(value = "费用类型", index = 2)
    private String expenseType;

    @ColumnWidth(50)
    @ExcelProperty(value = "不含税金额", index = 3)
    private String excludingTax;

    @ColumnWidth(50)
    @ExcelProperty(value = "税额", index = 4)
    private String taxAmount;

    @ColumnWidth(50)
    @ExcelProperty(value = "税率", index = 5)
    private String taxRate;

    @ColumnWidth(50)
    @ExcelProperty(value = "含税金额", index = 6)
    private String includedTax;

    @ColumnWidth(50)
    @ExcelProperty(value = "区域", index = 7)
    private String area;

    @ColumnWidth(50)
    @ExcelProperty(value = "履约月份", index = 8)
    private String performanceMonth;

}
