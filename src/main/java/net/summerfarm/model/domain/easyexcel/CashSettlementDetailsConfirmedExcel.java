package net.summerfarm.model.domain.easyexcel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * 现结明细本期确认收入的订单明细sheeteasyexcel实体类
 */
@Data
@EqualsAndHashCode
public class CashSettlementDetailsConfirmedExcel {

    @ColumnWidth(50)
    @ExcelProperty(value = "订单编号", index = 0)
    private String orderNo;

    @ColumnWidth(50)
    @ExcelProperty(value = "所属省市", index = 1)
    private String city;

    @ColumnWidth(50)
    @ExcelProperty(value = "收入确认节点", index = 2)
    private String revenueRecognitionNode;

    @ColumnWidth(50)
    @ExcelProperty(value = "*单据编号", index = 3)
    private String documentNo;

    @ColumnWidth(50)
    @ExcelProperty(value = "支付类型", index = 4)
    private String paymentType;

    @ColumnWidth(50)
    @ExcelProperty(value = "门店名称", index = 5)
    private String mname;

    @ColumnWidth(50)
    @ExcelProperty(value = "大客户工商名称", index = 6)
    private String supplierName;

    @ColumnWidth(50)
    @ExcelProperty(value = "大客户品牌名称", index = 7)
    private String nameRemarks;

    @ColumnWidth(50)
    @ExcelProperty(value = "下单时间", index = 8)
    private String orderTime;

    @ColumnWidth(50)
    @ExcelProperty(value = "支付时间", index = 9)
    private String payTime;

    @ColumnWidth(50)
    @ExcelProperty(value = "银行收款时间", index = 10)
    private String bankCollectionTime;

    @ColumnWidth(50)
    @ExcelProperty(value = "计划配送时间", index = 11)
    private String planTime;

    @ColumnWidth(50)
    @ExcelProperty(value = "超时加单费用", index = 12)
    private String outTimesFee;

    @ColumnWidth(50)
    @ExcelProperty(value = "配送费", index = 13)
    private String deliveryFee;

    @ColumnWidth(50)
    @ExcelProperty(value = "☆确认收入时间", index = 14)
    private String revenueRecognitionTime;

    @ColumnWidth(50)
    @ExcelProperty(value = "确认收入时间的取值来源", index = 15)
    private String valueSource;

    @ColumnWidth(50)
    @ExcelProperty(value = "skuId", index = 16)
    private String sku;

    @ColumnWidth(50)
    @ExcelProperty(value = "商品名称", index = 17)
    private String pdName;

    @ColumnWidth(50)
    @ExcelProperty(value = "商品经营类型", index = 18)
    private String operationType;

    @ColumnWidth(50)
    @ExcelProperty(value = "税率", index = 19)
    private String taxRate;

    @ExcelProperty(value = "下单数量", index = 20)
    private String quantity;

    @ColumnWidth(50)
    @ExcelProperty(value = "实付总价", index = 21)
    private String priceAmount;

    @ColumnWidth(50)
    @ExcelProperty(value = "税额", index = 22)
    private String taxAmount;

    @ColumnWidth(50)
    @ExcelProperty(value = "确认收入金额", index = 23)
    private String recognizedRevenueAmount;

    @ColumnWidth(50)
    @ExcelProperty(value = "应付总价", index = 24)
    private String totalPricePayable;

    @ColumnWidth(50)
    @ExcelProperty(value = "营销费用", index = 25)
    private String marketingExpenses;

    @ColumnWidth(50)
    @ExcelProperty(value = "成本单价", index = 26)
    private String cost;

    @ColumnWidth(50)
    @ExcelProperty(value = "成本", index = 27)
    private String totalCost;
}
