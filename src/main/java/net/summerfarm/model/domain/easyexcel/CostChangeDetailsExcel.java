package net.summerfarm.model.domain.easyexcel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/8/7 20:46
 */
public class CostChangeDetailsExcel {

    @ColumnWidth(50)
    @ExcelProperty(value = "仓库名称", index = 0)
    private String warehouseName;

    @ColumnWidth(50)
    @ExcelProperty(value = "sku", index = 1)
    private String sku;

    @ColumnWidth(50)
    @ExcelProperty(value = "出库总数量", index = 2)
    private String outboundQuantity;

    @ColumnWidth(50)
    @ExcelProperty(value = "不含税金额", index = 3)
    private String amountExcludingTax;

    @ColumnWidth(50)
    @ExcelProperty(value = "产生时间", index = 4)
    private String confirmationDate;

    public String getConfirmationDate() {
        return confirmationDate;
    }

    public void setConfirmationDate(String confirmationDate) {
        this.confirmationDate = confirmationDate;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getOutboundQuantity() {
        return outboundQuantity;
    }

    public void setOutboundQuantity(String outboundQuantity) {
        this.outboundQuantity = outboundQuantity;
    }

    public String getAmountExcludingTax() {
        return amountExcludingTax;
    }

    public void setAmountExcludingTax(String amountExcludingTax) {
        this.amountExcludingTax = amountExcludingTax;
    }
}
