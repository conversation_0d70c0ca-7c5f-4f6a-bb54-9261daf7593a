package net.summerfarm.model.domain.easyexcel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @title: ArchiveSummaryInvoiceExcel
 * 归档票夹明细 发票信息
 * @date 2022/6/2014:26
 */
@Data
@EqualsAndHashCode
public class ArchiveSummaryInvoiceExcel {

    @ColumnWidth(50)
    @ExcelProperty(value = "开票日期", index = 0)
    private String billingDate;

    @ColumnWidth(50)
    @ExcelProperty(value = "供应商性质", index = 1)
    private String supplierType;

    @ColumnWidth(50)
    @ExcelProperty(value = "发票销售方", index = 2)
    private String supplierName;

    @ColumnWidth(50)
    @ExcelProperty(value = "税号/身份证号", index = 3)
    private String taxNumber;

    @ColumnWidth(50)
    @ExcelProperty(value = "发票代码", index = 4)
    private String invoiceCode;

    @ColumnWidth(50)
    @ExcelProperty(value = "发票号码", index = 5)
    private String invoiceNumber;

    @ColumnWidth(50)
    @ExcelProperty(value = "不含税金额", index = 6)
    private String excludingTax;

    @ColumnWidth(50)
    @ExcelProperty(value = "税额", index = 7)
    private String taxAmount;

    @ColumnWidth(50)
    @ExcelProperty(value = "含税金额", index = 8)
    private String includedTax;

    @ColumnWidth(50)
    @ExcelProperty(value = "票面平均税率", index = 9)
    private String taxRate;

    @ColumnWidth(50)
    @ExcelProperty(value = "实际可抵扣税率", index = 10)
    private String actualTaxRate;

    @ColumnWidth(50)
    @ExcelProperty(value = "实际可抵扣税额", index = 11)
    private String actualTaxAmount;

}
