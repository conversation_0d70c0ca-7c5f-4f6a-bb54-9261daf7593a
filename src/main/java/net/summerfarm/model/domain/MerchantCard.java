package net.summerfarm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel(description = "用户优惠卡实体类")
@Data
public class MerchantCard {

    private Integer id;

    @ApiModelProperty(value = "商户id")
    private Long mId;

    @ApiModelProperty(value = "优惠卡id")
    private Integer cardId;

    @ApiModelProperty(value = "优惠卡规则id")
    private Integer cardRuleId;

    @ApiModelProperty(value = "有效日期")
    private LocalDateTime vaildDate;

    @ApiModelProperty(value = "发送者")
    private String sender;

    private LocalDateTime addTime;

}
