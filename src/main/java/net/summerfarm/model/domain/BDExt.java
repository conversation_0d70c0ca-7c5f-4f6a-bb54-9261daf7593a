package net.summerfarm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class BDExt implements Serializable {

    private Integer id;

    private Integer adminId;


    private String adminName;


    private Integer areaNo;

    private String areaName;


    private Integer privateNum;


    private Integer gmvTarget;


    // 1正常 2删除
    private Integer status;

    private LocalDateTime addtime;

    /**
    * 客情额度上限
    */
    private BigDecimal privateQuota;

    /**
     * 每日计划数
     */
    private Integer visitTarget;
}

