package net.summerfarm.model.domain;

import net.summerfarm.common.util.DateUtils;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class InventoryRecord {

    private Integer id;

    private String sku;

    private String changeField;

    private String oldValue;

    private String newValue;

    private String recorder;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime addtime;

    public InventoryRecord(){
    }

    public InventoryRecord(String sku, String changeField, String oldValue, String newValue, String recorder, LocalDateTime addtime) {
        this.sku = sku;
        this.changeField = changeField;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.recorder = recorder;
        this.addtime = addtime;
    }
}
