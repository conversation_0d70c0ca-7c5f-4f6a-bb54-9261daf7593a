package net.summerfarm.model.domain.crm;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 销售组织信息
 */
@Data
public class CrmBdOrg {
    /**
     * primary key
     */
    private Long id;

    /**
     * bd id
     */
    private Long bdId;

    /**
     * bd 名称
     */
    private String bdName;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 上级名称
     */
    private String parentName;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 级别:4:bd;3:m1;2:m2;1:m3
     */
    private Integer rank;
}