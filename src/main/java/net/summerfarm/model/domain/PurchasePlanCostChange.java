package net.summerfarm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * purchase_plan_cost_change
 * <AUTHOR>
@Data
public class PurchasePlanCostChange implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 采购计划id
     */
    private Integer purchasePlanId;

    /**
     * 原总成本
     */
    private BigDecimal oldTotalCost;

    /**
     * 新总成本
     */
    private BigDecimal newTotalCost;

    /**
     * 审核状态 0、待审核 1、审核失败 2、审核通过
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}