package net.summerfarm.model.domain;

import net.summerfarm.common.util.validation.groups.Add;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;

@ApiModel(description = "角色实体类")
public class Role implements Serializable {

    @ApiModelProperty(value = "角色id")
    @Null(message = "param.illegal" ,groups = {Add.class})
    private Integer roleId;

    @ApiModelProperty(value = "角色名")
    @NotNull(message = "name.null" ,groups = {Add.class})
    private String rolename;

    @ApiModelProperty(value = "说明")
    private String remarks;

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public String getRolename() {
        return rolename;
    }

    public void setRolename(String rolename) {
        this.rolename = rolename == null ? null : rolename.trim();
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }
}