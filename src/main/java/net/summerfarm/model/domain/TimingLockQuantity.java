package net.summerfarm.model.domain;

import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR> ct
 * create at:  2020/1/6  17:21
 * 省心送冻结库存时间
 */
@Data
public class TimingLockQuantity {

    private Integer id;

    private String sku;
    /**
    * 类型 0 采购 1 调拨
    */
    private  Integer type;
    /**
    * 状态 0 失效 1 有效
    */
    private Integer status;
    /**
    * 开始冻结库存时间
    */
    private LocalDate beginTime;
    /**
    * 结束冻结库存时间
    */
    private LocalDate endTime;
}
