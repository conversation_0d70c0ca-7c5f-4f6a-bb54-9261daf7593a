package net.summerfarm.model.DTO;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/1/9
 */
@Data
public class DynamicPriceSkuRecordDTO implements Serializable {

    /**
     * 大区名称
     */
    private String largeAreaName;

    /**
     * 区域编号
     */
    private Integer areaNo;

    /**
     * 运营区域名称
     */
    private String areaName;

    /**
     * 原售价
     */
    private BigDecimal oldSalePrice;

    /**
     * 原毛利率
     */
    private BigDecimal oldGrossProfitRate;

    /**
     * 预计售价
     */
    private BigDecimal newSalePrice;

    /**
     * 预计毛利率
     */
    private BigDecimal newGrossProfitRate;

    /**
     * 状态，0 失败，1 成功
     */
    private Integer status;

    /**
     * 成功or失败的原因
     */
    private String reason;

}
