package net.summerfarm.model.DTO;

import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/10/31 19:01<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryPathNameDTO {
    private String mname;

    private Integer storeNo;

    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private Date endDate;

    private List<Long> contactIds;
}
