package net.summerfarm.model.DTO.market;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/1/16
 */
@Data
public class ActivitySkuImportDTO implements Serializable {

    @ColumnWidth(50)
    @ExcelProperty(value = "sku", index = 0)
    private String sku;


    /**
     * 活动价(限定 指定价形式)
     */
    @ColumnWidth(50)
    @ExcelProperty(value = "活动价", index = 1)
    private Integer unit;

    /**
     * 活动价(限定 指定价形式)
     */
    @ColumnWidth(50)
    @ExcelProperty(value = "活动价（默认指定价）", index = 2)
    private BigDecimal amount;

    /**
     * 活动库存
     */
    @ColumnWidth(50)
    @ExcelProperty(value = "活动库存", index = 3)
    private Integer actualQuantity;

    /**
     * 限购件数
     */
    @ColumnWidth(50)
    @ExcelProperty(value = "限购件数（为空则不限购）", index = 4)
    private Integer limitQuantity;
}
