package net.summerfarm.model.DTO.market;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/9/23 11:02
 * @PackageName:net.summerfarm.model.DTO.market
 * @ClassName: ExchangeItemImportDTO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class ExchangeItemImportDTO {

    /**
     * skuid
     */
    @ExcelProperty(value = "sku", index = 0)
    private String sku;

    /**
     * 排序，数字越大优先展示
     */
    @ExcelProperty(value = "排序值（数字越大优先展示）", index = 1)
    private Integer priority;

    /**
     * 活动价(限定 指定价形式)
     */
    @ExcelProperty(value = "活动价（指定价）", index = 2)
    private BigDecimal amount;
}
