package net.summerfarm.model.DTO.market.circle;

import java.io.Serializable;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2022/8/1
 */
@Data
public class TagDTO implements Serializable {

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签id
     */
    private Long tagId;

    /**
     * 标签字段,用于ES搜索
     */
    private String tagField;

    /**
     * 规则属性类型,0 基本属性,1 行为属性
     */
    private Integer type;

}
