package net.summerfarm.model.DTO.market;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 拼团列表查询参数
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2022-05-26
 */
@Data
public class PartnershipBuyConfigListQueryDTO {

    /**
     * 运营区域编号
     */
    private List<Integer> areaNos;

    /**
     * 状态 0-待生效 1-已生效 2-已失效
     */
    private Integer status;

    /**
     * 页码
     */
    @NotNull(message = "参数错误")
    @Min(value = 1, message = "参数错误")
    private Integer pageIndex;

    /**
     * 每页记录数
     */
    @NotNull(message = "参数错误")
    @Min(value = 1, message = "参数错误")
    private Integer pageSize;
}
