package net.summerfarm.model.DTO.market;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/11/18 16:46
 * @PackageName:net.summerfarm.model.DTO.market
 * @ClassName: MarketCouponReturnImportDTO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class MarketCouponReturnImportDTO {

    /**
     * skuid
     */
    @ExcelProperty(value = "sku", index = 0)
    private String sku;

    /**
     * 红包返现门槛价格
     */
    @ExcelProperty(value = "红包返现门槛价格", index = 1)
    private BigDecimal threshold;
}
