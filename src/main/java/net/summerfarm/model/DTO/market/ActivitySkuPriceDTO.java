package net.summerfarm.model.DTO.market;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/1/11
 */
@Data
public class ActivitySkuPriceDTO implements Serializable {

    /**
     * 运营服务大区
     */
    private String largeAreaName;

    /**
     * 运营服务城市
     */
    private String areaName;

    /**
     * 商品正价
     */
    private BigDecimal salePrice;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 活动价格
     */
    private BigDecimal activityPrice;

    /**
     * 活动阶梯价格
     */
    private List<ActivityLadderPriceDTO> activityLadderPriceList;

}
