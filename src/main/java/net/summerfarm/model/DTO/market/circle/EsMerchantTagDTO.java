package net.summerfarm.model.DTO.market.circle;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2022/8/12
 */
@Data
public class EsMerchantTagDTO implements Serializable {

    private Long mId;

    private Long largeAreaNo;

    private Long areaNo;

    private String mainBusiness;

    private String merchantType;

    private Date lastOrderTime;

    private Date registerTime;

    private List<String> buyFirstCateGory;

    private List<String> buyFourthCategory;

    /**
     * 多值类型的标签，value值类型可能为Long/String
     */
    private Map<String, List<Object>> listTagMap;

    /**
     * 单值类型的标签，value值类型可能为Long/String/Boolean
     */
    private Map<String, Object> tagMap;

}
