package net.summerfarm.model.DTO.market;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2022/12/9
 */
@Data
public class ActivitySkuQueryDTO implements Serializable {

    public ActivitySkuQueryDTO(String sku, Integer areaNo, BigDecimal activityPrice, BigDecimal originalPrice) {
        this.sku = sku;
        this.areaNo = areaNo;
        this.originalPrice = originalPrice;
        this.activityPrice = activityPrice;
    }

    private Long basicInfoId;

    private Long itemConfigId;

    private String sku;

    private Integer areaNo;

    private BigDecimal activityPrice;

    private BigDecimal originalPrice;


}
