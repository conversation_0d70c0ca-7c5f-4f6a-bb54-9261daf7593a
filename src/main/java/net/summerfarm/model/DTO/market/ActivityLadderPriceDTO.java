package net.summerfarm.model.DTO.market;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import net.summerfarm.common.valid.group.market.ActivityCreate;
import net.summerfarm.common.valid.group.market.SkuConfig;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/5/14 14:34
 */
@Data
public class ActivityLadderPriceDTO implements Serializable {

    /**
     * 阶梯数
     */
    private Integer unit;

    /**
     * 阶梯价格
     */
    private BigDecimal price;

    public static String initDefaultLadderPrice(BigDecimal price){
        List<ActivityLadderPriceDTO> result = new ArrayList<>();
        ActivityLadderPriceDTO dto = new ActivityLadderPriceDTO();
        dto.setUnit(1);
        dto.setPrice(price);
        result.add(dto);
        return JSON.toJSONString(result);
    }

}
