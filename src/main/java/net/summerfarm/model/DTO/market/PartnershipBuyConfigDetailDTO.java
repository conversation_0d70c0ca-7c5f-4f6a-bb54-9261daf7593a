package net.summerfarm.model.DTO.market;

import lombok.Data;
import net.summerfarm.model.DTO.AreaDTO;
import net.summerfarm.model.domain.PartnershipBuyConfig;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class PartnershipBuyConfigDetailDTO {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 拼团活动名称
     */
    private String partnershipBuyName;

    /**
     * 拼团活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 拼团活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 拼团人数
     */
    private Integer joinNum;

    /**
     * 拼团有效期(分钟数)
     */
    private Integer validTime;

    /**
     * 运营区域名称
     */
    private List<AreaDTO> areas;

    /**
     * 商品列表
     */
    private List<PartnershipBuySkuDTO> partnershipBuySkus;

    public static PartnershipBuyConfigDetailDTO build(PartnershipBuyConfig partnershipBuyConfig) {
        PartnershipBuyConfigDetailDTO detail = new PartnershipBuyConfigDetailDTO();
        detail.setId(partnershipBuyConfig.getId());
        detail.setPartnershipBuyName(partnershipBuyConfig.getPartnershipBuyName());
        detail.setStartTime(partnershipBuyConfig.getStartTime());
        detail.setEndTime(partnershipBuyConfig.getEndTime());
        detail.setJoinNum(partnershipBuyConfig.getJoinNum());
        detail.setValidTime(partnershipBuyConfig.getValidTime());
        return detail;
    }
}
