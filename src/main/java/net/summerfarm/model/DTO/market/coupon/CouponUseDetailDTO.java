package net.summerfarm.model.DTO.market.coupon;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/9/12
 */
@HeadFontStyle(fontHeightInPoints = 14)
@HeadRowHeight(25)
@ContentRowHeight(20)
@Data
public class CouponUseDetailDTO implements Serializable {

    /**
     * 商户名称
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "客户名称", index = 0)
    private String merchantName;

    /**
     * 手机号
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "手机号码", index = 1)
    private String phone;

    /**
     * 城市名
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "所在城市", index = 2)
    private String areaName;

    /**
     * 商户主营类型
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "主营类型", index = 3)
    private String type;

    /**
     * 商户跟进bd名
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "归属BD", index = 4)
    private String adminName;

    /**
     * 额度
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "优惠券面值", index = 5)
    private BigDecimal money;

    /**
     * 使用阈值
     */
    @ColumnWidth(12)
    @ExcelProperty(value = "使用条件", index = 6)
    private BigDecimal threshold;

    /**
     * 使用状态
     */
    @ColumnWidth(12)
    @ExcelProperty(value = "使用状态", index = 7)
    private String useStatus;

}
