package net.summerfarm.model.DTO.market;

import java.io.Serializable;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;

/**
 * @author: <EMAIL>
 * @create: 2022/12/5
 */
@Data
public class ActivityPageQueryDTO extends BasePageInput implements Serializable {

    /**
     * 创建者id
     */
    private Integer creatorId;

    /**
     * 活动id
     */
    private Long id;

    /**
     * 活动大区id
     */
    private Integer largeAreaNo;

    /**
     * 人群包id
     */
    private Long merchantPoolId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动状态，0 未生效，1 已生效，2 已失效
     */
    private Integer activityStatus;

    /**
     * 活动类型，0 特价活动，1 临保活动，2 换购，3 拓展购买，4 秒杀，5 多人拼团，6 满减，7 满返，8 预售，9 省心送
     */
    private Integer type;

    /**
     * sku和活动大区只能2选1查询，否则会慢sql
     */
    private String sku;

}
