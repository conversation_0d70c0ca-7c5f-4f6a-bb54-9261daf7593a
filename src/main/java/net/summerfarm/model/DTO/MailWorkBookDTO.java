package net.summerfarm.model.DTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.poi.ss.usermodel.Workbook;

/**
 * 邮件体workbook DTO
 * <AUTHOR>
 * @date 2023/01/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MailWorkBookDTO {

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 工作簿
     */
    private Workbook workbook;
}
