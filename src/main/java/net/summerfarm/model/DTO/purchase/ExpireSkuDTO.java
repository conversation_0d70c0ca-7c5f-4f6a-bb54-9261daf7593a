package net.summerfarm.model.DTO.purchase;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class ExpireSkuDTO {
    //采购单号
    @ColumnWidth(50)
    @ExcelProperty(value = "采购单号", index = 0)
    private String purchaseNo;
    //创建人
    @ColumnWidth(50)
    @ExcelProperty(value = "创建人", index = 1)
    private String purchaser;
    //SKU
    @ColumnWidth(50)
    @ExcelProperty(value = "SKU", index = 2)
    private String sku;
    //商品名称
    @ColumnWidth(50)
    @ExcelProperty(value = "商品名称", index = 3)
    private String pdName;
    //规格
    @ColumnWidth(50)
    @ExcelProperty(value = "规格", index = 4)
    private String weight;
    //供应商
    @ColumnWidth(50)
    @ExcelProperty(value = "供应商", index = 5)
    private String supplier;
    //最晚到货时间
    @ColumnWidth(50)
    @ExcelProperty(value = "最晚到货时间", index = 6)
    private String latestArrivalDate;
    //可预约数量
    @ColumnWidth(50)
    @ExcelProperty(value = "可预约数量", index = 7)
    private Integer arrangeQuantity;
    //创建人id
    @ExcelIgnore
    private Integer creatorId;

}
