package net.summerfarm.model.DTO.purchase;

import lombok.Data;
import net.summerfarm.module.scp.common.enums.SupplierReplenishmentConfigEnums;

import java.io.Serializable;

@Data
public class SupplierReplenishmentDTO implements Serializable {

    private static final long serialVersionUID = -5350748409780216663L;

    /**
     * 品仓供应商配置ID
     */
    private Long id;

    /**
     * SPU ID
     */
    private Long pdId;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 供应商ID
     */
    private Integer supplierId;

    /**
     * 补货模式 1:不定期不定量,2:定期不定量,3:不定期定量,4:定期定量
     *
     * @see SupplierReplenishmentConfigEnums.ReplenishmentMode
     */
    private Integer replenishmentMode;

    /**
     * 提前期
     */
    private Integer preDay;

    /**
     * 备货期
     */
    private Integer backlogDay;

    /**
     * 订单时间
     */
    private Integer orderDate;

    /**
     * 安全水位
     */
    private Integer safeWaterLevel;
}