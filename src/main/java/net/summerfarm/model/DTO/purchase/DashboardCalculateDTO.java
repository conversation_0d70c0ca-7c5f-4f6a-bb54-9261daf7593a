package net.summerfarm.model.DTO.purchase;

import lombok.Data;
import net.summerfarm.module.scp.common.enums.StockDashboardEnums;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class DashboardCalculateDTO implements Serializable {

    private static final long serialVersionUID = -3929565550423747341L;
    private Long id;

    /**
     * spuId
     */
    private Integer pdId;

    /**
     * sku
     */
    private String skuId;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 日期
     */
    private LocalDateTime viewDate;

    /**
     * 可用库存
     */
    private BigDecimal enabledQuantity;

    /**
     * 期初库存
     */
    private BigDecimal initQuantity;

    /**
     * 库存视图状态
     *  @see StockDashboardEnums.StockViewStatus
     * 补货视图的状态
     *  @see StockDashboardEnums.ReplenishmentViewStatus
     */
    private Integer status;

    /**
     * 在途库存
     */
    private Integer onWayQuantity;

    /**
     * 采购订单在途
     */
    private Integer onWayOrderQuantity;

    /**
     * 调入库存
     */
    private Integer transferInQuantity;

    /**
     * 在仓库存
     */
    private BigDecimal quantity;

//    /**
//     * 安全库存
//     */
//    private Integer safeQuantity;

//    /**
//     * 冻结库存
//     */
//    private Integer lockQuantity;

    /**
     * 安全水位
     */
    private Integer safeWaterLevel;

    /**
     * 预估销量
     */
    private BigDecimal forecastSales;
    /**
     * 预估调出量
     */
    private BigDecimal forecastTransferOut;
    /**
     * 总消耗量
     */
    private BigDecimal forecastConsumption;

    /**
     * 预估需求量
     */
    private BigDecimal estimatedDemandQuantity;

    /**
     * 调拨单订单口径在途
     */
    private BigDecimal transferOrderInQuantity;

    /**
     * 采购订单口径在途
     */
    private BigDecimal poOnWayQuantity;
}
