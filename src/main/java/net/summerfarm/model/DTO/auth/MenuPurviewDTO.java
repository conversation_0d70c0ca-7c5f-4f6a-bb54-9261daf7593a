package net.summerfarm.model.DTO.auth;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@Data
public class MenuPurviewDTO {
    /**
     * 权限id
     */
    private Long id;

    /**
     * 父权限Id
     */
    private Integer parentId;

    /**
     * 权限名称
     */
    @NotNull(message = "名称不能为空")
    @Length(max = 128, message = "名称不能超过128")
    private String menuName;
    private String purviewName;

    /**
     * 权限对应资源(URL)
     */
    @Length(max = 100, message = "权限URL长度不能超过100")
    private String url;

    /**
     * 权限描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer weight;

    /**
     * 新增类型 0 菜单 1权限 2按钮 3模块
     */
    private Integer type;

    /**
     * 关联权限
     */
    private List<String> sonPurviewList;

    /**
     * 5 oms 6 manage
     */
    @NotNull(message = "系统枚举不能为空")
    private Integer systemEnum;
    /**
     * 子菜单
     */
    private List<MenuPurviewDTO> childMenuPurviewVOS;
}
