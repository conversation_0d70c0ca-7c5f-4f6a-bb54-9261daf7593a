package net.summerfarm.model.DTO;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 活动黑名单
 * @date 2023/6/19 18:49:29
 */
@Data
public class TimingWhiteImportDTO {
    /**
     * 省心送订单ID
     */
    @ExcelProperty(value = "省心送订单ID", index = 0)
    private String orderNo;

    /**
     * 延期天数
     */
    @ExcelProperty(value = "延期天数", index = 1)
    private String daysDeferred;
}
