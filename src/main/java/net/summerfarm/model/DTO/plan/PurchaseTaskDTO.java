package net.summerfarm.model.DTO.plan;

import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Date 2022/12/6 16:31
 */

@Data
public class PurchaseTaskDTO {

    private Long id;
    /**
     * 最晚到货时间
     */
    private LocalDate latestArrivalDate;
    /**
     * 采购时间
     */
    private LocalDate purchaseTime;
    /**
     * 采购数量
     */
    private Integer quantity;
    /**
     * sku
     */
    private String sku;
    /**
     * 供应商id
     */
    private Integer supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 采购人名称
     */
    private String purchaserName;

    /**
     * 采购人id
     */
    private Integer purchaserId;

}
