package net.summerfarm.model.DTO;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import net.summerfarm.model.vo.PurchasesInStoreVO;
import net.summerfarm.model.vo.StoreRecordVO;

import java.util.List;

@ApiModel(description = "出入库批量操作")
@Data
public class StoreRecordBatchDTO extends StoreRecordVO {

    private List<StoreRecordDTO> storeRecordList;

    private List<PurchasesInStoreVO> purchasesInStoreVOS;

}
