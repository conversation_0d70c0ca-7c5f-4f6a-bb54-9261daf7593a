package net.summerfarm.model.DTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.StoreRecord;
import net.summerfarm.model.vo.WarehouseBatchProveRecordVO;

/**
 * @Package: net.summerfarm.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/11/10
 */
@ApiModel(description = "出入库记录VO")
@Data
public class StoreRecordDTO extends StoreRecord {

    @ApiModelProperty(value = "是否货检")
    private int isInspect;

    /**
     * 证件信息
     */
    private WarehouseBatchProveRecordVO batchProve;
}
