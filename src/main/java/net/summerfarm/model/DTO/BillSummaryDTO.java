package net.summerfarm.model.DTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title: BillSummaryDTO
 * @date 2022/2/1016:09
 */
@Data
public class BillSummaryDTO {

    @ApiModelProperty(value = "门店名称")
    private String mname;

    @ApiModelProperty(value = "结算方式")
    private String direct;

    @ApiModelProperty(value = "送货地址")
    private String address;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "下单时间")
    private LocalDateTime orderTime;

    @ApiModelProperty(value = "配送日期")
    private String deliveryTime;

    @ApiModelProperty(value = "司机送达时间")
    private String finishTime;

    @ApiModelProperty(value = "订单状态")
    private String status;

    @ApiModelProperty(value = "商品名称")
    private String pdName;

    @ApiModelProperty(value = "鲜沐sku")
    private String sku;

    @ApiModelProperty(value = "映射商品名称")
    private String mappingName;

    @ApiModelProperty(value = "映射sku")
    private String mapping;

    @ApiModelProperty(value = "规格")
    private String weight;

    @ApiModelProperty(value = "性质")
    private String skuType;

    @ApiModelProperty(value = "是否预付")
    private String prepayInventory;

    @ApiModelProperty(value = "商品数量")
    private String amount;

    @ApiModelProperty(value = "应付单价")
    private String originalPrice;

    @ApiModelProperty(value = "商品实付单价")
    private String salePrice;

    @ApiModelProperty(value = "商品实付总价")
    private String totalPrice;

    @ApiModelProperty(value = "售后金额")
    private String handleNum;

    @ApiModelProperty(value = "价格策略")
    private String rebateNumber;

    @ApiModelProperty(value = "返点金额")
    private String returnPoint;

    @ApiModelProperty(value = "配送费")
    private String deliveryFee;

    @ApiModelProperty(value = "调整总额")
    private String price;

    @ApiModelProperty(value = "应付总额")
    private String total;

    @ApiModelProperty(value = "备注")
    private String remark;

}
