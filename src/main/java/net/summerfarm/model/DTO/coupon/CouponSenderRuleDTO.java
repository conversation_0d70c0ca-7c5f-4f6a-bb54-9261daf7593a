package net.summerfarm.model.DTO.coupon;

import lombok.Data;
import net.summerfarm.enums.market.activity.ScopeTypeEnum;

import java.io.Serializable;

@Data
public class CouponSenderRuleDTO implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 发放设置ID
     */
    private Integer couponSenderId;

    /**
     * 发放设置状态
     */
    private Integer status;

    /**
     * 发放设置名称
     */
    private String couponSenderName;

    /**
     * 范围ID
     */
    private Long scopeId;

    /**
     * 范围类型 1-圈人 2-城市 3-运营大区
     * @see ScopeTypeEnum
     */
    private Integer scopeType;

    /**
     * 范围名称
     */
    private String scopeName;

    private static final long serialVersionUID = 1L;
}