package net.summerfarm.model.DTO.inventory;

import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.xianmu.common.input.BasePageInput;

/**
 * @author: <EMAIL>
 * @create: 2023/1/9
 */
@Data
public class WhiteListSkuDTO extends BasePageInput implements Serializable {

    @NotBlank(groups = {Add.class},message = "sku不能为空")
    /**
     * sku
     */
    private String sku;

    /**
     * 类目类型，0 鲜果 1 非鲜果
     */
    private Integer categoryType;

    @NotEmpty(groups = {Add.class, Update.class},message = "必须添加仓库")
    /**
     * 库存仓编号集合
     */
    private List<Integer> warehouseNos;

    /**
     * 商品名称
     */
    private String pdName;

}
