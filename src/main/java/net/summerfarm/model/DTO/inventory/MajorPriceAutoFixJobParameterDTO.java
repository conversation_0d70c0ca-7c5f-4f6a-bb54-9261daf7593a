package net.summerfarm.model.DTO.inventory;

import java.io.Serializable;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2024/3/7
 */
@Data
public class MajorPriceAutoFixJobParameterDTO implements Serializable {

    /**
     * 报价周期： 0周报价, 1半月报价, 2月报价，3日报价
     */
    private Integer majorCycle;

    /**
     * 偏移量
     */
    private Integer offset;

    /**
     * 一页的数量
     */
    private Integer limit;


}
