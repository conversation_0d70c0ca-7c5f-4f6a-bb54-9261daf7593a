package net.summerfarm.model.DTO.inventory;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

@Data
public class SamePropertyInventoryQueryDTO {

    /**
     * pd_id
     */
    private Integer pdId;

    /**
     * 规格
     */
    private String unit;

    /**
     * 性质 0-自营 1-代仓
     */
    private Integer type;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    private Integer subType;

    /**
     * 商品性质 2-临保 5-破袋
     */
    @NotNull(message = "请选择商品类型(仅支持临保或破袋)")
    @Pattern(regexp = "[25]", message = "仅支持临保或破袋商品")
    private Integer extType;

    /**
     * 销售属性组数据
     */
    private List<SkuPropertyValueDTO> saleValueList;
}
