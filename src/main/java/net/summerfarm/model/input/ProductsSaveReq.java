package net.summerfarm.model.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.Products;
import net.summerfarm.model.domain.ProductsProperty;
import net.summerfarm.model.domain.ProductsPropertyValue;
import net.summerfarm.model.vo.InventoryVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Package: net.summerfarm.model.vo.req
 * @Description: spu复杂对象
 * @author: <EMAIL>
 * @Date: 2017/9/1
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductsSaveReq extends Products implements Serializable{

    @ApiModelProperty("关键属性值")
    private List<ProductsPropertyValue> keyValueList;

    @ApiModelProperty("销售属性")
    private List<ProductsProperty> salePropertyList;

    @ApiModelProperty("销售属性值")
    private List<ProductsPropertyValue> saleValueList;

    private List<InventoryVO> skuList;

    /**
     * 审核标识：null、仅更新数据 0、拒绝 1、通过
     */
    private Boolean auditFlag;

    /**
     * 外部申请id
     */
    private Long outerApplicationId;

    /**
     * 税率 1%传递 0.01
     */
    BigDecimal taxRateValue;
    /**
     * 税率码
     */
    String taxRateCode;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 自动审核通过
     */
    private boolean autoAudit = false;

}
