package net.summerfarm.model.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Package: net.summerfarm.model.input
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/4/26
 */
@ApiModel(description = "消息Input类")
public class MsgInput {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "管理员id")
    private Integer adminId;

    @ApiModelProperty(value = "0接收人，1cc")
    private Integer recvType;

    @ApiModelProperty(value = "消息处理状态：0暂不处理,1已处理")
    private Integer status;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "消息模板id")
    private Integer msgTemplateId;

    @ApiModelProperty(value = "消息唯一标识")
    private String keyword;
    @ApiModelProperty(value = "消息唯一标识")
    private String endTime;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Integer getMsgTemplateId() {
        return msgTemplateId;
    }

    public void setMsgTemplateId(Integer msgTemplateId) {
        this.msgTemplateId = msgTemplateId;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public Integer getRecvType() {
        return recvType;
    }

    public void setRecvType(Integer recvType) {
        this.recvType = recvType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
