package net.summerfarm.model.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import net.summerfarm.common.util.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import net.summerfarm.enums.StockTaskMailPushStatusEnum;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel(description = "出入库任务查询条件")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StockTaskReq {
    @ApiModelProperty(value = "出入库：0入库、1出库",required = true)
    private Integer inOrOut;

    @ApiModelProperty(value = "城市编号")
    private Integer areaNo;

    @ApiModelProperty(value = "到货仓")
    private Integer inStoreNo;

    @ApiModelProperty(value = "出入库类型")
    private Integer type;

    @ApiModelProperty(value = "任务状态")
    private Integer state;

    @ApiModelProperty(value = "订单编号")
    private String taskNo;

    @ApiModelProperty(value = "商品名称")
    private String pdName;

    @ApiModelProperty(value = "任务编号")
    private Integer stockTaskId;

    private String sku;

    @ApiModelProperty(value = "出入库类型，多选用,隔开")
    private String types;

    @ApiModelProperty(value = "捡货任务类型")
    private Integer pickType;

    @ApiModelProperty(value = "捡货任务类型")
    private Integer categoryType;

    private List<Integer> typeList;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime addtime;

    private String closeOrderTime;

    @ApiModelProperty(value = "维度")
    private Integer dimension;

    @ApiModelProperty(value = "入库进度")
    private Integer processState;
    /**
    * 配送仓编号
    */
    private Integer storeNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 供应商id
     */
    private Integer pdId;

    @ApiModelProperty(value = "预约单状态list,需分隔符隔开")
    private String stockArrangeState;

    @ApiModelProperty(value = "任务状态")
    private List<Integer> states;

    /**
     * 邮件推送状态
     * @see StockTaskMailPushStatusEnum
     */
    private Integer mailPushState;

    /**
     * 生成起始时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startAddTime;

    /**
     * 生成结束时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endAddTime;

    /**
     * 租户ID 1-鲜沐
     */
    private Long tenantId;

    public boolean isFromSaas() {
        return fromSaas;
    }

    public void setFromSaas(boolean fromSaas) {
        this.fromSaas = fromSaas;
    }

    private boolean fromSaas;

    /**
     * skuId
     */
    private String saasSkuId;

    /**
     * 任务ID集合
     */
    private List<Integer> stockTaskIds;

    /**
     * 仓库列表
     */
    private List<Integer> warehouseNoList;

    /**
     * pdId查询sku列表
     */
    private List<String> skusByPdId;

    public List<Integer> getWarehouseNoList() {
        return warehouseNoList;
    }

    public void setWarehouseNoList(List<Integer> warehouseNoList) {
        this.warehouseNoList = warehouseNoList;
    }
    public void setSkusByPdId(List<String> skusByPdId) {
        this.skusByPdId = skusByPdId;
    }

    public List<String> getSkusByPdId(){
        return skusByPdId;
    }

    public Integer getInOrOut() {
        return inOrOut;
    }

    public void setInOrOut(Integer inOrOut) {
        this.inOrOut = inOrOut;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Integer getInStoreNo() {
        return inStoreNo;
    }

    public void setInStoreNo(Integer inStoreNo) {
        this.inStoreNo = inStoreNo;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName;
    }

    public Integer getStockTaskId() {
        return stockTaskId;
    }

    public void setStockTaskId(Integer stockTaskId) {
        this.stockTaskId = stockTaskId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getTypes() {
        return types;
    }

    public void setTypes(String types) {
        this.types = types;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public List<Integer> getTypeList() {
        return typeList;
    }

    public void setTypeList(List<Integer> typeList) {
        this.typeList = typeList;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public LocalDateTime getAddtime() {
        return addtime;
    }

    public void setAddtime(LocalDateTime addtime) {
        this.addtime = addtime;
    }

    public Integer getPickType() {
        return pickType;
    }

    public void setPickType(Integer pickType) {
        this.pickType = pickType;
    }


    public Integer getCategoryType() {
        return categoryType;
    }

    public void setCategoryType(Integer categoryType) {
        this.categoryType = categoryType;
    }

    public String getCloseOrderTime() {
        return closeOrderTime;
    }

    public void setCloseOrderTime(String closeOrderTime) {
        this.closeOrderTime = closeOrderTime;
    }

    public Integer getStoreNo() {
        return storeNo;
    }

    public void setStoreNo(Integer storeNo) {
        this.storeNo = storeNo;
    }

    public Integer getDimensiono() {
        return dimension;
    }

    public void setDimension(Integer dimension) {
        this.dimension = dimension;
    }

    public Integer getDimension() {
        return dimension;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getPdId() {
        return pdId;
    }

    public void setPdId(Integer pdId) {
        this.pdId = pdId;
    }

    public Integer getProcessState() {
        return processState;
    }

    public void setProcessState(Integer processState) {
        this.processState = processState;
    }

    public String getStockArrangeState() {
        return stockArrangeState;
    }

    public void setStockArrangeState(String stockArrangeState) {
        this.stockArrangeState = stockArrangeState;
    }

    public List<Integer> getStates() {
        return states;
    }

    public void setStates(List<Integer> states) {
        this.states = states;
    }

    public Integer getMailPushState() {
        return mailPushState;
    }

    public void setMailPushState(Integer mailPushState) {
        this.mailPushState = mailPushState;
    }

    public LocalDateTime getStartAddTime() {
        return startAddTime;
    }

    public void setStartAddTime(LocalDateTime startAddTime) {
        this.startAddTime = startAddTime;
    }

    public LocalDateTime getEndAddTime() {
        return endAddTime;
    }

    public void setEndAddTime(LocalDateTime endAddTime) {
        this.endAddTime = endAddTime;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getSaasSkuId() {
        return saasSkuId;
    }

    public void setSaasSkuId(String saasSkuId) {
        this.saasSkuId = saasSkuId;
    }

    public List<Integer> getStockTaskIds() {
        return stockTaskIds;
    }

    public void setStockTaskIds(List<Integer> stockTaskIds) {
        this.stockTaskIds = stockTaskIds;
    }
}
