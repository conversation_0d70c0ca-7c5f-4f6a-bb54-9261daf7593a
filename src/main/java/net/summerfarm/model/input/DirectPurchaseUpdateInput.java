package net.summerfarm.model.input;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.vo.PurchasesVO;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description: 直发采购单编辑入参
 * @Date: 2020/11/22 9:48
 * @Author: <EMAIL>
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class DirectPurchaseUpdateInput extends PurchasesVO {

    private static final long serialVersionUID = -5839393367154609254L;


    /**
     * 发货时间，校验应不早于下结算单时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDate sendTime;

    /**
     * 填入的物流信息
     */
    private String logisticsInfo;


}


