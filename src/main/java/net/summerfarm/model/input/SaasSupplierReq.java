package net.summerfarm.model.input;

import lombok.Data;

import java.util.List;


@Data
public class SaasSupplierReq {

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 供应商名称
     */
    private String name;

    /**
     * 供应商类型
     */
    private Integer supplierType;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 供应商id列表
     */
    private List<Integer> supplierIds;

    /**
     * 供应商id
     */
    private Integer supplierId;

}
