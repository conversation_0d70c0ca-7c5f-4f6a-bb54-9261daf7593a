package net.summerfarm.model.input;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@ApiModel(description = "保存区域配置")
@Data
public class SaveManageAreaInput implements Serializable {

    private static final long serialVersionUID = -7104516112244419337L;

    private Integer id;
    /**
     * 城市负责人
     */
    @NotNull
    private Integer manageAdminId;
    /**
     * 区域负责人
     */
    @NotNull
    private Integer parentAdminId;
    /**
     * 总负责人
     */
    @NotNull
    private Integer departmentAdminId;
    /**
     * 区域名称
     */
    @NotBlank
    @Size(max = 20)
    private String zoneName;
    /**
     * 下属城市no集合
     */
    @NotNull
    private List<Integer> subCity;

    /**
     * 所属行政城市
     */
    @NotNull
    private List<String> administrativeCitys;
}
