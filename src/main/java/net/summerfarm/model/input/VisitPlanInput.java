package net.summerfarm.model.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/6 15:43
 */
@Data
public class VisitPlanInput {

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 城市编号即可
     */
    private List<Integer> areaNos;

    /**
     * 客户名称
     */
    private String mname;

    /**
     * 拜访人id
     */
    private Integer adminId;

    /**
     * 状态 0 待拜访 1已拜访,2取消,4系统取消
     */
    private Integer status;

    /**
     * 期待拜访时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime expectedTime;

    /**
     * 开始时间*
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;


}
