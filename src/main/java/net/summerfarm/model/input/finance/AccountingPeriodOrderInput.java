package net.summerfarm.model.input.finance;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import net.xianmu.common.input.BasePageInput;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2023/3/30 18:30
 */
@Data
public class AccountingPeriodOrderInput extends BasePageInput {
    /**
     * id
     */
    private Long id;
    /**
     * 区域编号
     */
    private Integer areaNo;

    /**
     * 账单编号
     */
    private String billNumber;

    /**
     * 企业工商名称
     */
    private String invoiceTitle;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 品牌名称
     */
    private String nameRemakes;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 所属销售/地推人员
     */
    private String salerName;

    /**
     * 排序方式 0:最后更新时间（账单确认时间） 1 账单生成日 2 最后调整分摊时间
     */
    private Integer sortBy;

    /**
     * 账单状态 1 待确定 2 待审核 3 待收款 4 已收款 5 待开票
     * 待确定 type = 0 customerConfirmStatus = 0
     * 待审核 type = 0 customerConfirmStatus = 1 financialAudit = 0
     * 待付款 type = 1 customerConfirmStatus = 1 financialAudit = 1
     * 已付款 type = 1 customerConfirmStatus = 1 financialAudit = 1 receiptStatus = 2
     */
    private Integer periodOrderType;

    /**
     * 账单生成开始时间
     */
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate startTime;

    /**
     * 账单结束时间
     */
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate endTime;

    /**
     * 导出类型： 0:账单概览;1:账单明细；
     */
    private Integer exportType;

    /**
     * 客户类型
     * @see net.summerfarm.enums.finance.AccountingPeriodCustomerType
     */
    private Integer customerType;

    /**
     * 财务审核状态  0-未审核；1-审核通过；2-驳回
     */
    private Integer financialAudit;

    /**
     * 备注
     */
    private String remark;

    /**
     * 销售id
     */
    private Integer salerId;

    /**
     * 门店id
     */
    private Integer mId;

    /**
     * 入口类型1为crm 其它为后台
     */
    private Integer reqType;

    /**
     * 多个代下单类型
     */
    private String periodOrderTypeStr;

    /**
     * 是否是总览
     */
    private Boolean isOverview;

    /**
     * 客户确认状态；0-客户待确认；1-客户已确认；
     */
    private Integer customerConfirmStatus;

    /**
     * 开票状态 0:未开票,1:部分开票,2:已开票
     */
    private Integer invoiceStatus;

    /**
     * 核销状态 0-未收款；1-部分收款；2-已收款
     */
    private Integer receiptStatus;

    /**
     * 账单状态： 0、待确认 1、已确认
     */
    private Integer type;
}
