package net.summerfarm.model.input;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/9/21  16:04
 * AOL接口订单数据
 */
@Data
public class AOLOrderInput {

    /**
    * AOL的门店id
    */
    private String mId;

    /**
    * AOL订单编号
    */
    private String orderNo;

    /**
    * 配送日期
    */
    private LocalDate deliveryDate;

    /**
    * 联系方式
    */
    private String mphone;

    /**
    * 省
    */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
    * 详细地址
    */
    private String address;


    /**
    * 备注
    */
    private String remark;

    /**
    * 鲜沐mID
    */
    private Long xMid;
    /**
    * 订单详情
    */
    private List<AOLOrderItemInput> orderItems;

    /**
     * 数量
     */
    private Integer amount;

}
