package net.summerfarm.model.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 人工批量发劵
 * @date 2023/7/25 18:02:32
 */
@Data
public class CouponSendTemplateIssueReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 发放客户-针对手动
     */
    private List<Long> mIdList;

    /**
     * 卡劵ID
     */
    @NotNull(message = "卡劵ID不能为空！")
    private Long couponId;

    /**
     * 申请理由-大于10块钱需要申请理由
     */
    @NotBlank(message = "申请理由不能为空！")
    private String reason;

    /**
     * 是否发送短信
     */
    @NotNull(message = "是否发送短信不能为空！")
    private Boolean sendMsg;

    /**
     * 发放客户 0-手动  1-圈人
     */
    @NotNull(message = "发放客户不能为空！")
    private Integer sendClient;

    /**
     * 客户过滤：现结大客户 0-否  1-是
     */
    @NotNull(message = "客户过滤：现结大客户不能为空！")
    private Integer cashCustomer;

    /**
     * 客户过滤：账期大客户  0-否 1-是
     */
    @NotNull(message = "客户过滤：账期大客户不能为空！")
    private Integer accountCustomer;

    /**
     * 发放类型 0-立即发放  1-定时发放
     */
    @NotNull(message = "发放类型不能为空！")
    private Integer sendType;

    /**
     * 定时发放时间
     */
    private LocalDateTime releaseTime;

    /**
     * 发放客户-针对圈人
     */
    private CouponSenderRuleReq couponSenderRuleDTO;
}
