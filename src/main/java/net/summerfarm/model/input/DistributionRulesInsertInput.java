package net.summerfarm.model.input;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project marketing-center
 * @description 运费规则
 * @date 2023/9/27 11:24:44
 */
@Data
public class DistributionRulesInsertInput implements Serializable {

    private static final long serialVersionUID = 7152268151997601589L;

    /**
     * 类型ID  服务区域、地址、品牌主键ID
     */
    @NotNull(message = "类型ID不能为空！")
    private Long typeId;

    /**
     * 规则类型  1：门店管理  2：品牌管理  3：服务区域
     */
    @NotNull(message = "规则类型不能为空！")
    private Integer type;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 运费规则
     */
    @NotEmpty(message = "运费规则不能为空！")
    @Valid
    private List<RulesInput> rulesInputs;

    @Data
    public static class RulesInput implements Serializable {

        private static final long serialVersionUID = 7252268151997601589L;

        /**
         * 城市编号-针对品牌管理类型   0代表全部城市
         */
        private Integer areaNo;

        /**
         * 履约时效  0：T+1    1：T+N
         */
        @NotNull(message = "履约时效不能为空！")
        private Integer ageing;

        /**
         * 配送费
         */
        @NotNull(message = "配送费不能为空！")
        private BigDecimal deliveryFee;

        /**
         * 快递费
         */
        @NotNull(message = "快递费不能为空！")
        private BigDecimal expressFee;

        /**
         * 条件
         */
        private List<ConditionsInput> conditionsInputs;
    }

    @Data
    public static class ConditionsInput implements Serializable {

        private static final long serialVersionUID = 7242268151997601589L;

        /**
         * 商品类目  1：全部商品 2：乳制品商品  3：非乳制品商品
         */
        private Integer productType;

        /**
         * 门槛类型 1：金额  2：件数
         */
        private Integer sillType;

        /**
         * 件数  针对门槛类型为件数
         */
        private Integer number;

        /**
         * 金额 针对门槛类型为金额
         */
        private BigDecimal amount;
    }
}
