package net.summerfarm.model.input;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 活动黑名单列表
 * @date 2023/6/19 17:11:00
 */
@Data
public class ActivityBlackAndWhiteListPageQuery extends BasePageInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 库存仓名称
     */
    private String warehouseName;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * skuID
     */
    private String sku;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 城市名称
     */
    private String areaName;

    /**
     * 类型 1-黑名单  2-白名单
     */
    private Integer type;
}
