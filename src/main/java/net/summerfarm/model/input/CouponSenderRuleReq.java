package net.summerfarm.model.input;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description
 * @date 2023/8/29 16:54:17
 */
@Data
public class CouponSenderRuleReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 范围id集合
     */
    @NotEmpty(message = "范围ID不能为空！")
    private List<Long> scopeIds;


    /**
     * 活动范围类型，1 人群包，2 运营城市，3 运营大区 4-品牌客户
     */
    @NotNull(message = "活动范围类型不能为空！")
    private Integer scopeType;
}
