package net.summerfarm.model.input.item;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Date 2024/7/29 14:49
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PopProductPublishInput {
    /**
     * 类目信息
     */
    @NotNull(message = "类目不能为空")
    private Long categoryId;
    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String pdName;
    /**
     * 级别
     */
    @NotBlank(message = "级别不能为空")
    private String level;
    /**
     * 商品单位
     */
    @NotBlank(message = "商品单位不能为空")
    private String unit;
    /**
     * 规格
     */
    @NotBlank(message = "规格不能为空")
    private String specification;
    /**
     * 果规
     */
    @NotBlank(message = "果规不能为空")
    private String fruitSize;
    /**
     * 长（厘米）
     */
    @NotNull(message = "长不能为空")
    private BigDecimal length;
    /**
     * 宽（厘米）
     */
    @NotNull(message = "宽不能为空")
    private BigDecimal width;
    /**
     * 高（厘米）
     */
    @NotNull(message = "高不能为空")
    private BigDecimal high;
    /**
     * 毛重（KG）
     */
    @NotNull(message = "重量不能为空")
    private BigDecimal weightNum;
    /**
     * 净重
     */
    @NotNull(message = "净重不能为空")
    private BigDecimal netWeightNum;
    /**
     * 净重单位
     */
    @NotBlank(message = "净重单位不能为空")
    private String netWeightNumUnit;
    /**
     * 售后规则
     */
    @NotBlank(message = "售后规则不能为空")
    private String afterSaleRuleDetail;
    /**
     * 买手id
     */
    @NotNull(message = "买手不能为空")
    private Long buyerId;
    /**
     * 买手名称
     */
    private String buyerName;
    /**
     * 供应商id
     */
    @NotNull(message = "供应商不能为空")
    private Long supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 橱窗图
     */
    @NotNull(message = "橱窗图不能为空")
    private String picturePath;
    /**
     * 详情图
     */
    @Size(max = 10, message = "详情图最多10张")
    private List<String> detailPictureList;
    /**
     * 视频
     */
    @NotNull(message = "视频不能为空")
    private String videoUrl;
    /**
     * 产地
     */
    @NotNull(message = "产地不能为空")
    private String origin;
    /**
     * 是否国产
     */
    @NotNull(message = "是否国产不能为空")
    private Integer isDomestic;
    /**
     * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
     */
    @NotNull(message = "报价类型不能为空")
    private Integer quoteType;

    /**
     * 外部商品编码
     */
    private String externalSkuCode;

    /**
     * 创建来源
     */
    private PopProductPublishSourceInput sourceInput;
}
