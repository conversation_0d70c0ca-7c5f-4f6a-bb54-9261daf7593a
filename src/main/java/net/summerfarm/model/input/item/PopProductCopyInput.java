package net.summerfarm.model.input.item;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @Date 2025/1/10 13:55
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PopProductCopyInput {

    /**
     * sku编码
     */
    @NotBlank(message = "popSku不能为空")
    private String popSku;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String pdName;

    /**
     * 果规
     */
    @NotBlank(message = "果规不能为空")
    private String fruitSize;

    /**
     * 叶子类目
     */
    @NotNull(message = "请选择类目")
    private Long categoryId;

    /**
     * 售后规则
     */
    private String afterSaleRuleDetail;

    /**
     * 橱窗图
     */
    private String picturePath;
    /**
     * 详情图
     */
    @Size(max = 10, message = "详情图最多10张")
    private List<String> detailPictureList;

    /**
     * 毛利率
     */
    private BigDecimal interestRate;

    /**
     * 关键属性列表
     */
    private List<ProductsPropertyValueInput> productsPropertyValueList;

    /**
     * sku属性列表
     */
    private List<ProductsPropertyValueInput> skuPropertyValueList;
}
