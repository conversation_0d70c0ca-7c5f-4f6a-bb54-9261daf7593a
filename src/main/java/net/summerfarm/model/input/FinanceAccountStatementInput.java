package net.summerfarm.model.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.FinanceAccountStatement;
import net.summerfarm.model.vo.FinanceAccountStatementDetailVO;

import java.util.List;

/**
 * <AUTHOR>
 * @title: FinanceAccountStatementInput
 * @date 2022/2/1813:49
 */
@Data
public class FinanceAccountStatementInput extends FinanceAccountStatement {


    @ApiModelProperty(value="审核人")
    private String auditor;

    @ApiModelProperty(value="审批人")
    private String approver;

    @ApiModelProperty(value="sku")
    private String sku;

    @ApiModelProperty(value="入库单号")
    private Integer receiptNo;

    private Integer supplierAccountId;

    @ApiModelProperty(value="退货单号")
    private Integer returnOrderNo;

    @ApiModelProperty(value="成本调整 1 仅看成本调整")
    private Integer costAdjustment;

    @ApiModelProperty(value="对账单详情数据")
    private List<FinanceAccountStatementDetailVO> financeAccountStatementDetailVOS;

    @ApiModelProperty(value="对账单ids")
    private List<Long> financeAccountStatementIds;

    @ApiModelProperty(value="出入库类型 56、采购退货任务 11、采购入库任务")
    private Integer type;

    @ApiModelProperty(value="商品名称")
    private String pdName;

    @ApiModelProperty(value="商品名称")
    private List<String> pdNames;

    @ApiModelProperty(value="采购单号")
    private String purchaseNo;

    @ApiModelProperty(value="预付单ids")
    private String ids;

    @ApiModelProperty(value="realName")
    private String audit;

    @ApiModelProperty(value="adminId")
    private Integer auditId;

    @ApiModelProperty(value="钉钉id")
    private String userId;

}
