package net.summerfarm.model.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.model.domain.Inventory;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/11/7  13:39
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AreaStoreQueryInput {

    /**
     * size
     */
    private Integer pageSize;

    /**
     * nu,
     */
    private Integer pageNum;

    /**
     * sku
     */
    private List<Inventory> skuList;

    /**
     * 仓库
     */
    private List<Long> warehouseNoList;

    /**
     * 是否售罄
     */
    private Boolean saleOut;
}
