package net.summerfarm.model.input;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 红包雨/每日抽奖活动
 * @date 2023/5/12 15:42:40
 */
@Data
public class LuckDrawActivityReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空！")
    private Long id;
}
