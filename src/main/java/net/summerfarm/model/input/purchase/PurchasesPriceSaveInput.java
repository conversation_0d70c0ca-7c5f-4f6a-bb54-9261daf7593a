package net.summerfarm.model.input.purchase;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class PurchasesPriceSaveInput {

    /** 供应商ID **/
    @NotNull
    private Long supplierId;

    /** skuList **/
    @NotEmpty
    private List<PurchasesPriceInput> skuList;

    private Long tenantId;
    private String source;

}
