package net.summerfarm.model.input.purchase;

import lombok.Data;

import java.util.List;

@Data
public class ProductWarehouseSupplierQueryInput {

    /**
     * 品仓供应商配置ID
     */
    private Long id;

    /**
     * 品仓供应商配置ID 列表
     */
    private List<Long> ids;

    /**
     * SPU id
     */
    private Long pdId;

    /**
     * SPU id列表
     */
    private List<Long> pdIds;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 仓库编号列表
     */
    private List<Integer> warehouseNos;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 供应商ID列表
     */
    private List<Integer> supplierIds;

    /**
     * 配置是否完成标记0:未完成 1:已完成
     */
    private Integer completeFlag;

    /**
     * 首选供应商
     */
    private Integer primaryFlag;
}
