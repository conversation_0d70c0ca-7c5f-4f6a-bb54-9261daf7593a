package net.summerfarm.model.input.purchase;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ProductWarehouseSaveConfigInput {

    /**
     * 配置ID
     */
    @NotNull(message = "配置ID不能为空")
    private Long configId;

    /**
     * SPU_ID
     */
    @NotNull(message = "pdId不能为空")
    private Long pdId;

    /**
     * 库存仓编码
     */
    @NotNull(message = "warehouseNo不能为空")
    private Integer warehouseNo;

    /**
     * 采购类型 1: 直采 0: 非直采
     * {@link net.summerfarm.module.scp.common.enums.ProductWarehouseConfigEnums.PurchaseType}
     */
    @NotNull(message = "purchaseType不能为空")
    private Integer purchaseType;

    /**
     * 采购ID
     */
    @NotNull(message = "adminId不能为空")
    private Integer adminId;

    /**
     * 采购名称
     */
    @NotEmpty(message = "adminName不能为空")
    private String adminName;

    /**
     * 计划员名称
     */
    @NotEmpty(message = "计划员名称不能为空")
    private String plannerName;

    /**
     * 计划员id
     */
    @NotEmpty(message = "计划员Id不能为空")
    private Integer plannerId;

    /**
     * 供应商配置
     */
    private List<ProductWarehouseSupplierSaveInput> supplierInfos;
}
