package net.summerfarm.model.input.purchase;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class PurchasesPriceTimeInput {

    /** 供应商ID **/
    @NotNull(message = "供应商id不能为空")
    private Long supplierId;

    /** sku集合 **/
    @NotEmpty(message = "sku不能为空")
    private List<String> skuList;






}
