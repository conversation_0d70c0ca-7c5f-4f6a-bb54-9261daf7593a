package net.summerfarm.model.input.purchase;

import lombok.Data;
import net.summerfarm.enums.pms.ReplenishmentOrderEnums;

import java.util.List;

@Data
public class ReplenishmentRelationUpdateInput {

    /**
     * 补货单ID
     */
    private List<Long> orderIds;

    /**
     * 操作人Id
     */
    private Integer operatorId;

    /**
     * 原状态
     * @see ReplenishmentOrderEnums.OrderStatus
     */
    private Integer originalStatus;

    /**
     * 目标状态
     *  @see ReplenishmentOrderEnums.OrderStatus
     */
    private Integer targetStatus;

    /**
     * 创建时间,防止第二天关闭第一天的单子
     */
    private Integer createDate;

    /**
     * 关联操作类型：1:采购单
     * @see ReplenishmentOrderEnums.RelationType
     */
    private Integer relationType;

    /**
     * 关联操作id
     */
    private String relationId;
}
