package net.summerfarm.model.input.purchase;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

@Data
public class PurchasesPriceQueryInput extends BasePageInput {

    /**
     * 商品名称
     */
    private String pdName;

    /** saasSku **/
    private String saasSkuId;

    /** 供应商ID **/
    private Long supplierId;

    /** 日期 **/
    private String queryDate;


    /** 导出查询条件json **/
    private String paramJson;

    /** 状态0待生效1生效中2已失效 **/
    private Integer status;

    private Long tenantId;
    private String source;

    private String sku;

    private List<String> skuList;

}
