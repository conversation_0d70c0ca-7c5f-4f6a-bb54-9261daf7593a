package net.summerfarm.model.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.PurchaseInvoice;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @title: 采购发票查询
 * @date 2022/1/2016:08
 */
@Data
public class PurchaseInvoiceQuery extends PurchaseInvoice {

    @ApiModelProperty(value="开票开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startTime;

    @ApiModelProperty(value="开票结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTime;

    @ApiModelProperty(value="采购负责人(可能为供应商管理人)")
    private String purchaser;

    @ApiModelProperty(value="提交时间；匹配完成时间；归档时间。")
    private LocalDateTime updateTime;

    @ApiModelProperty(value="发票销售方")
    private String supplierName;

    @ApiModelProperty(value="备注")
    private String remakes;

    @ApiModelProperty(value="发票编码")
    private String invoiceCodes;

    /**
     * 发票代码或号码搜索关键词
     */
    private String invoiceSearchKey;

    @ApiModelProperty(value="采购发票id")
    private Integer purchaseInvoiceId;

    @ApiModelProperty(value="上传的文件地址")
    private List<String> fileAdd;

    @ApiModelProperty(value="1 提交匹配 2 保存 3 删除")
    private Integer writeType;

    @ApiModelProperty(value="实际可抵扣税额")
    private BigDecimal actualTaxAmount;

    @ApiModelProperty(value="状态：0 待提交，1 可匹配，2 匹配完成-待归档，3 已归档")
    private Integer status;

    @ApiModelProperty(value="供应商工商信息（税号字段/身份证号）")
    private String taxNumber;

    @ApiModelProperty(value="提交发票时间")
    private LocalDateTime submissionTime;

    @ApiModelProperty(value="匹配完成时间")
    private LocalDateTime matchCompletionTime;

    @ApiModelProperty(value="复核归档时间")
    private LocalDateTime reviewFilingTime;

    @ApiModelProperty(value="提交发票人")
    private String submissionTimeMan;

    @ApiModelProperty(value="匹配完成人")
    private String matchCompletionTimeMan;

    @ApiModelProperty(value="复核归档人")
    private String reviewFilingTimeMan;

    @ApiModelProperty(value="归档月份")
    private String fileTime;

    @ApiModelProperty(value="开票日期(开始)")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate billingDateStart;

    @ApiModelProperty(value="开票日期（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate billingDateEnd;

    @ApiModelProperty(value="有无发票附件：0 有；1 无")
    private Integer invoiceAttachment;

    @ApiModelProperty(value="发票代码")
    private String invoiceCode;

    @ApiModelProperty(value="发票号码")
    private String invoiceNumber;

    @ApiModelProperty(value="提交时间；匹配完成时间；归档时间。（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate updateTimeStart;

    @ApiModelProperty(value="提交时间；匹配完成时间；归档时间。（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate updateTimeEnd;

    @ApiModelProperty(value="排序，1 开票日期； 2 最后操作时间； 提交时间；匹配完成时间；归档时间")
    private Integer orderType;

}
