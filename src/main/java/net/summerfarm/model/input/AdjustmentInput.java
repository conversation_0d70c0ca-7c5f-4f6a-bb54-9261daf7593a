package net.summerfarm.model.input;

import lombok.Data;
import net.summerfarm.model.domain.Adjustment;
import net.summerfarm.model.domain.AdjustmentDetail;
import net.summerfarm.model.vo.AdjustmentDetailVO;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * @description
 * <AUTHOR>
 * @date 2021/12/4 13:59
 */
@Data
public class AdjustmentInput extends Adjustment {

    /**
     * 所属品牌adminId
     */
    private String adminId;

    /**
     * 所含门店的mId
     */
    private Long mId;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 0 审批拒绝 1 审批通过
     */
    private Integer flag;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * sku
     */
    private String sku;

    /**
     * 收货手机号
     */
    private String phone;

    /**
     * 调整单详情
     */
    private List<AdjustmentDetail> detailList;

    /**
     * 订单编号集合
     */
    private Set<String> orderNoSet;

    /**
     * 门店名称
     */
    private String mname;
}
