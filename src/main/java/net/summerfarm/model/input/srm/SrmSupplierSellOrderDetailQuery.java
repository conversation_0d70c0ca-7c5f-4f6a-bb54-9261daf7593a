package net.summerfarm.model.input.srm;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class SrmSupplierSellOrderDetailQuery {

    /**
     * 采购单号
     */
    @NotBlank(message = "采购单号不能为空！")
    private String purchaseNo;
    /**
     * 供应商id
     */
    @NotNull(message = "供应商id不能为空！")
    private Long supplierId;
}
