package net.summerfarm.model.input;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.model.domain.Supplier;
import net.summerfarm.model.domain.SupplierAccount;
import net.summerfarm.model.domain.SupplierConnect;
import net.summerfarm.model.domain.SupplierRelatedFile;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


@ApiModel(description = "SAAS供应商新增")
@Data
public class SaasSupplierInput {

    private Integer id;

    @NotNull(message = "供应商名称不能为空")
    private String supplierName;

    @NotNull(message = "供应商类型不能为空")
    private Integer supplierType;

    private String taxNum;

    private String categoryArray;

    private String remark;

    private List<SupplierConnect> connectList;
    private List<SupplierRelatedFile> fileList;
    private List<SupplierAccount> accountList;

    private String creator;

    private Long tenantId;

}
