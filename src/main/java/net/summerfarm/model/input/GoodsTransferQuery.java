package net.summerfarm.model.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.validation.annotation.InRange;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.model.domain.GoodsLocationDetail;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/4/8  13:15
 */
@Data
public class GoodsTransferQuery {


    private String  sku;

    private String pdName;

    private String  weight;

    private Integer totalQuantity;

    @ApiModelProperty(value = "存储区域")
    @InRange(rangeNums = {0, 1, 2, 3, 4}, groups = {Add.class, Update.class})  //0，未分类，1：冷冻，2：冷藏，3：恒温，4：顶汇大流通
    private Integer storageLocation;

    private List<GoodsLocationDetail> goodsLocationDetails;

    /** 存储区域 */
    private String storageArea;

    /** 包装 */
    private String packing;
}
