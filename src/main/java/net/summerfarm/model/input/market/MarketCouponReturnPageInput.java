package net.summerfarm.model.input.market;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/11/15 14:59
 * @PackageName:net.summerfarm.model.input.market
 * @ClassName: MarketCouponReturnPageInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class MarketCouponReturnPageInput extends BasePageInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 返券类型 2-红包返现
     */
    private Integer type;

    /**
     * 规则状态 0-已停用 1-未生效 2-生效中 3-已失效
     */
    private Integer ruleStatus;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建人
     */
    private String creatorName;
}
