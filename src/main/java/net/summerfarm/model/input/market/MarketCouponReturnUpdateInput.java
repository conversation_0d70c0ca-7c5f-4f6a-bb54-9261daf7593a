package net.summerfarm.model.input.market;

import lombok.Data;
import net.summerfarm.model.DTO.market.MarketRuleDetailDTO;
import net.summerfarm.model.DTO.market.MarketRuleScopeDTO;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/15 15:34
 * @PackageName:net.summerfarm.model.input.market
 * @ClassName: MarketCouponReturnUpdateInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class MarketCouponReturnUpdateInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @NotNull(message = "id不能为空")
    private Integer id;

    /**
     * 规则名称
     */
    @NotNull(message = "规则名称不能为空")
    private String name;

    /**
     * 返券类型 2-红包返现
     */
    @NotNull(message = "返券类型不能为空")
    private Integer type;

    /**
     * 规则类型 1-确认收货后
     */
    @NotNull(message = "规则类型不能为空")
    private Integer couponRule;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    /**
     * 优惠券返现规则明细
     */
    private List<MarketRuleDetailDTO> marketRuleDetailDTOS;

    /**
     * 优惠券返现规则适用范围
     */
    @NotEmpty(message = "优惠券返现规则适用范围不能为空")
    @Valid
    private List<MarketRuleScopeDTO> marketRuleScopeDTOS;
}
