package net.summerfarm.model.input.market;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/11/15 15:55
 * @PackageName:net.summerfarm.model.input.market
 * @ClassName: MarketCouponReturnDetailUploadInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class MarketCouponReturnDetailUploadInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件链接
     */
    @NotBlank(message = "文件链接不能为空！")
    private String key;
}
