package net.summerfarm.model.input;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 红包雨/每日抽奖活动
 * @date 2023/5/12 15:42:40
 */
@Data
public class LuckDrawActivityBasicInsertReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空！")
    private String name;

    /**
     * 活动开始时间
     */
    @NotNull(message = "活动开始时间不能为空！")
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @NotNull(message = "活动结束时间不能为空！")
    private LocalDateTime endTime;

    /**
     * 预热时间
     */
    private LocalDateTime preheatTime;

    /**
     * 规则说明-落地页ID
     */
    @NotBlank(message = "规则说明不能为空！")
    private String rule;

    /**
     * 活动类型  红包雨-1  每日抽奖-2
     */
    @NotNull(message = "活动类型不能为空！")
    private Integer type;

    /**
     * 浮标图
     */
    private String buoyImage;

    /**
     * 预热背景图
     */
    @NotBlank(message = "预热背景图不能为空！")
    private String preheatBackground;

    /**
     * 结束背景图
     */
    @NotBlank(message = "结束背景图不能为空！")
    private String endBackground;

    /**
     * 未抽奖背景图
     */
    @NotBlank(message = "未抽奖背景图不能为空！")
    private String noDrawBackground;

    /**
     * 已抽奖背景图
     */
    @NotBlank(message = "已抽奖背景图不能为空！")
    private String drawBackground;

    /**
     * 分享图
     */
    private String shardImage;

    /**
     * 分享标题
     */
    private String shardTitle;

    /**
     * 分享描述
     */
    private String shardRemake;

    /**
     * 奖项
     */
    @NotEmpty(message = "奖项不能为空！")
    @Valid
    private List<LuckyDrawActivityEquityPackageInsertReq> packageInsertReq;
}
