package net.summerfarm.model.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: StockTaskWalletsInput
 * @date 2022/1/2711:26
 */
@Data
public class StockTaskWalletsInput {

    @ApiModelProperty(value="票夹id")
    private Long walletsId;

    @ApiModelProperty(value="采购单编号")
    private String purchaseNo;

    @ApiModelProperty(value="sku")
    private String sku;

    @ApiModelProperty(value="税收分类编码")
    private String taxRateCode;

    @ApiModelProperty(value="商品名称")
    private String pdName;

    @ApiModelProperty(value="对账单号")
    private Long financeAccountStatementId;

    @ApiModelProperty(value="出入库单号")
    private Integer stockTaskProcessId;

    @ApiModelProperty(value="出入库类型 56、采购退货任务 11、采购入库任务")
    private Integer type;

    @ApiModelProperty(value="出入库时间排序 1正序 2 倒序")
    private Integer sequence;

    @ApiModelProperty(value="状态：2 待复核，3 已归档")
    private Integer status;

}
