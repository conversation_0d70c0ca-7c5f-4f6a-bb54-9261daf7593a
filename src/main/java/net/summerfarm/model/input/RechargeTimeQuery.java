package net.summerfarm.model.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title: RechargeTimeQuery
 * @date 2021/9/8 16:54
 */
@Data
public class RechargeTimeQuery {

    @ApiModelProperty(value = "查询时间")
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime nowTime;

    @ApiModelProperty(value = "查询时间七天前")
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime lastWork;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "环比开始时间")
    private String ratioStartTime;

    @ApiModelProperty(value = "环比结束时间")
    private String ratioEndTime;

    @ApiModelProperty(value = "同比开始时间")
    private String yearOnYearStartTime;

    @ApiModelProperty(value = "同比结束时间")
    private String yearOnYearEndTime;


}
