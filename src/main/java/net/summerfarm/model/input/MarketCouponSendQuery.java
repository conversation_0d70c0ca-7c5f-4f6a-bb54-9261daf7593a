package net.summerfarm.model.input;

import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MarketCouponSendQuery {
    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 发放状态  0-未发放 1-部分发放 2-已发放  3-已取消  4-已撤回
     */
    private Integer sendStatus;

    private BigDecimal money;

    private String couponName;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private Date startTime;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private Date endTime;
}
