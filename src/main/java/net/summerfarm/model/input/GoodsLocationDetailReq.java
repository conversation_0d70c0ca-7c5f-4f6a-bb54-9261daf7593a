package net.summerfarm.model.input;

import lombok.Data;
import net.summerfarm.model.domain.GoodsDetailRecord;
import net.summerfarm.model.vo.GoodsLocationDetailVO;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/3/25  16:32
 */
@Data
public class GoodsLocationDetailReq {

    /**
     * sku编号
     */
    private String sku;

    /**
    * 商品名称
    */
    private String pdName;

    /**
    * 规格
    */
    private String weight;

    /**
    * 调整信息
    */
    private List<GoodsDetailRecord> goodsDetailRecords;
}
