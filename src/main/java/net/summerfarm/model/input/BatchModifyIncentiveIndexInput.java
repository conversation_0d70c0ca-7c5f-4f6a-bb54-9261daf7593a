package net.summerfarm.model.input;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class BatchModifyIncentiveIndexInput implements Serializable {
    private static final long serialVersionUID = 7278239482691636689L;

    /**
     * 主键、自增
     */
    private List<Integer> id;

    /**
     * bd id
     */
    private Integer adminId;

    /**
     * gmv基础值
     */
    private BigDecimal gmvBase;

    /**
     * 类型
     */
    private Integer type;

    /**
     * gmc目标值
     */
    private BigDecimal gmvTarget;

    /**
     * 月活目标
     */
    private Integer monthOrderTarget;

    /**
     * 私海上限
     */
    private Integer privateSeaLimit;

    /**
     * 客情上限
     */
    private Integer quotaLimit;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 核心客户数基础值
     */
    private Integer coreMerchantAmount;

    private LocalDateTime createTime;


}
