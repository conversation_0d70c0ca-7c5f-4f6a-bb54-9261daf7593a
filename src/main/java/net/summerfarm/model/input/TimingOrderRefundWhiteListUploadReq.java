package net.summerfarm.model.input;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 黑白名单上传
 * @date 2023/6/20 18:02:44
 */
@Data
public class TimingOrderRefundWhiteListUploadReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件链接
     */
    @NotBlank(message = "文件链接不能为空！")
    private String ossUrl;
}
