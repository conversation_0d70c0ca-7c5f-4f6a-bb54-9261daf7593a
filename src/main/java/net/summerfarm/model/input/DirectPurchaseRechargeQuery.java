package net.summerfarm.model.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.DirectPurchaseRecharge;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * @Description: 收款申请记录查询VO
 * @Date: 2020/11/17 11:25
 * @Author: <EMAIL>
 */
@Data
public class DirectPurchaseRechargeQuery extends DirectPurchaseRecharge {

    private static final long serialVersionUID = -7189441974801202785L;
    /**
     * 店铺编号对应的店铺名，入参模糊搜索
     */
    @ApiModelProperty(value = "店铺编号对应的店铺名，入参模糊搜索")
    private String mname;

    /**
     * 查询开始日期时间
     */
    @ApiModelProperty(value = "查询开始日期时间")
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    /**
     * 查询结束日期时间
     */
    @ApiModelProperty(value = "查询结束日期时间")
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;

    /**
     * 入参查询有可用金额的和无可用金额的标识：0：无可用余额， 1：有可用余额
     */
    @ApiModelProperty(value = "入参查询有可用金额的和无可用金额的标识：0：无可用余额， 1：有可用余额")
    private Integer leftFlag;

}


