package net.summerfarm.model.bo;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.enums.BatchSetDeliveryPlanEnum;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: 批量设置配送计划请求实体类
 * date: 2022/5/11 11:26
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SetDeliveryPlanResult implements Serializable {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 客户名称
     */
    private String merchantName;

    /**
     * 客户手机号
     */
    private String phone;

    /**
     * 下单时间
     */
    private LocalDateTime orderTime;

    /**
     * 配送时间
     */
    private LocalDate deliveryDate;

    /**
     * 配送数量
     */
    private Integer deliveryAmount;

    /**
     * 配送地址
     */
    private String deliveryAddress;

    /**
     * 处理结果
     */
    private String result;

    /**
     * 失败原因
     */
    private String reason;

    public SetDeliveryPlanResult(String orderNo, String merchantName, String phone, LocalDateTime orderTime){
        this.orderNo = orderNo;
        this.merchantName = merchantName;
        this.phone = phone;
        this.orderTime = orderTime;
    }

    public void handle(BatchSetDeliveryPlanEnum resultEnum){
        if (BatchSetDeliveryPlanEnum.SUCCESS.equals(resultEnum)){
            this.result = BatchSetDeliveryPlanEnum.SUCCESS.getMsg();
        }else {
            this.reason = resultEnum.getMsg();
            this.result = BatchSetDeliveryPlanEnum.FAIL.getMsg();
            this.deliveryDate = null;
            this.deliveryAmount = null;
            this.deliveryAddress = null;
        }
    }


}
