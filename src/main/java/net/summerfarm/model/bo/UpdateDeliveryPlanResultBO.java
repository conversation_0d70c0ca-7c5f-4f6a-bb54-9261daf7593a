package net.summerfarm.model.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 批量设置配送计划请求实体类
 * date: 2022/5/11 14:26
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateDeliveryPlanResultBO implements Serializable {

    /**
     * 文件下载记录ID
     */
    private String uId;

    /**
     * 批量设置发货计划结果集合
     */
    private List<SetDeliveryPlanResult> results;

}
