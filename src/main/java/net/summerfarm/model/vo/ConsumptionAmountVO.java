package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title: ConsumptionAmountVO
 * @date 2021/9/9 16:10
 */
@Data
public class ConsumptionAmountVO {

    @ApiModelProperty(value = "消费金额")
    private BigDecimal consumptionAmount;

    @ApiModelProperty(value = "消费模块订单数")
    private Integer consumptionOrders;

    @ApiModelProperty(value = "消费模块单均金额")
    private Integer consumptionAverage;

    @ApiModelProperty(value = "消费模块环比")
    private BigDecimal consumptionRatio;

    @ApiModelProperty(value = "消费模块同比")
    private BigDecimal consumptionYearOnYear;
}
