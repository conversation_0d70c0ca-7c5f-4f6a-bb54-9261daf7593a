package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(description = "按件奖励")
@Data
public class CrmCommissionSkuVo implements Serializable {
    private static final long serialVersionUID = -5130583018444061851L;

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 区域名称
     */

    private String zoneName;

    /**
     * sku
     */
    private String sku;

    /**
     * sku Id
     */
    private Integer skuId;
    /**
     * sku+规格
     */
    private String weight;


    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 商品id
     */

    private Integer productId;


    /**
     * 提成金额
     */
    private BigDecimal reward;

    /**
     * sku商品性质
     */
    private Integer extType;

}
