package net.summerfarm.model.vo.saas;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: <br/>
 * date: 2022/8/24 13:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class OutsideAfterSaleVO implements Serializable {

    /**
     * sku数量
     */
    private String sku;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 售后单号
     */
    private String orderNo;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

}
