package net.summerfarm.model.vo.saas;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2022/5/16  19:41
 * 外部订单(订单项)取消配送
 */
@Data
public class OutsideRefundVO {

    /**
     * 品牌id
     */
    private Integer tenantId;

    /**
     * 商户id
     */
    private Integer storeId;

    /**
     * 外部订单号
     */
    private String orderNo;

    /**
     * 退单号
     */
    private String refundNo;

    /**
     * 退单sku详情信息
     */
    private List<OutsideRefundItemVO> refundItemVOS;
}
