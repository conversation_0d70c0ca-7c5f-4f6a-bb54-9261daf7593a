package net.summerfarm.model.vo.saas;

import io.swagger.models.auth.In;
import lombok.Data;

/**
 * <AUTHOR> ct
 * create at:  2022/5/13  18:48
 */
@Data
public class OutsideOrderItemVO {

    /**
     * Inventory表 invId (sku不存在则必填,和sku同时存在则使用sku)
     */
    private Integer skuId;

    /**
     * sku (skuId不存在则必填)
     */
    private String sku;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 配送类型
     */
    private Integer deliveryType;

}
