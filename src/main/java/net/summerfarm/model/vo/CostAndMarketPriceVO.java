package net.summerfarm.model.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Package: net.summerfarm.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/11/15
 */
public class CostAndMarketPriceVO {

    private BigDecimal costPrice;

    private BigDecimal originalCostPrice;

    private BigDecimal marketPrice;

    private BigDecimal originalMarketPrice;

    private LocalDateTime addTime;

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public BigDecimal getOriginalCostPrice() {
        return originalCostPrice;
    }

    public void setOriginalCostPrice(BigDecimal originalCostPrice) {
        this.originalCostPrice = originalCostPrice;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public BigDecimal getOriginalMarketPrice() {
        return originalMarketPrice;
    }

    public void setOriginalMarketPrice(BigDecimal originalMarketPrice) {
        this.originalMarketPrice = originalMarketPrice;
    }

    public LocalDateTime getAddTime() {
        return addTime;
    }

    public void setAddTime(LocalDateTime addTime) {
        this.addTime = addTime;
    }
}
