package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.SampleApply;
import net.summerfarm.model.domain.SampleSku;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/4/26  11:08
 */
@ApiModel(description = "样品申请vo")
@Data
public class SampleApplyVO extends SampleApply {


    private String areaName;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区域")
    private String area;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "门牌号")
    private String houseNumber;

    List<SampleSku> sampleSkuList;
}
