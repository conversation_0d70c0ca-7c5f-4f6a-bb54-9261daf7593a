package net.summerfarm.model.vo.finance;

import lombok.Data;
import net.summerfarm.model.domain.FinanceAccountingPeriodOrder;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/3/31 13:36
 */
@Data
public class FinanceAccountingPeriodOrderResult extends FinanceAccountingPeriodOrder {
    /**
     * 调整金额
     */
    private BigDecimal adjustmentAmount;

    /**
     * 应收金额
     */
    private BigDecimal receivableAmount;

    /**
     * 账单状态 1 待确定 2 待审核 3 待收款 4 已收款 5 待开票
     * 待确定 type = 0 customerConfirmStatus = 0
     * 待审核 type = 0 customerConfirmStatus = 1 financialAudit = 0
     * 待付款 type = 1 customerConfirmStatus = 1 financialAudit = 1
     * 已付款 type = 1 customerConfirmStatus = 1 financialAudit = 1 receiptStatus = 2
     */
    private Integer periodOrderType;

    /**
     * 已开票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 开票状态 0:待开票;1:部分开票;2:全额开票
     */
    private Integer invoiceStatus;

    private String billToPay;
}
