package net.summerfarm.model.vo.finance;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 账单明细excel-账单概览
 *
 * <AUTHOR>
 * @Date 2023/4/17 11:40
 */
@Data
@EqualsAndHashCode
public class FinancePeriodOverviewExportVO {
    /**
     * 账单开始结束日期
     */
    private String billCycle;

    /**
     * 出账日
     */
    private String billGenerationTime;

    /**
     * 账单编号
     */
    private String billNo;

    /**
     * 品牌名称
     */
    @ExcelProperty("品牌名称")
    private String nameRemakes;

    /**
     * 客户类型 品牌；单店
     */
    private String customerType;

    /**
     * 工商名称
     */
    private String invoiceTitle;

    /**
     * 应收金额
     */
    private BigDecimal receivableAmount;

    /**
     * 总应收金额
     */
    private BigDecimal totalReceivableAmount;

    /**
     * 实付总价
     */
    private BigDecimal totalPrice;

    /**
     * 售后金额
     */
    private BigDecimal afterSaleAmount;

    /**
     * 配送费用
     */
    private BigDecimal deliveryFee;

    /**
     * 超时加单
     */
    private BigDecimal outTimesFee;

    /**
     * 调整金额
     */
    private BigDecimal adjustmentAmount;
}
