package net.summerfarm.model.vo.finance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 账单明细excel-订单明细
 *
 * <AUTHOR>
 * @Date 2023/4/17 11:47
 */
@Data
@Accessors(chain = true)
public class FinancePeriodOrderItemExportVO {
    /**
     * 门店名称
     */
    private String mname;

    private Long mId;

    /**
     * 商品二级类型
     */
    private Integer subType;

    /**
     * 似乎是价格策略？
     */
    private Integer rebateType;

    /**
     * 似乎是返点金额？
     */
    private BigDecimal rebateNumber;

    /**
     * 下单类型
     */
    private String orderType;

    /**
     * 地址
     */
    private String address;

    /**
     * 订单编号
     */
    private String orderNo;

    private Long orderItemId;

    /**
     * 下单时间
     */
    private String orderTime;

    /**
     * 司机送达时间
     */
    private String finishTime;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 商品名字
     */
    private String pdName;

    /**
     * sku
     */
    private String sku;

    /**
     * 规格
     */
    private String weight;

    /**
     * 性质
     */
    private String property;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 应付单价
     */
    private BigDecimal copeUnitPrice;

    /**
     * 实付单价
     */
    private BigDecimal actualUnitPrice;

    /**
     * 实付总价
     */
    private BigDecimal actualPaidPrice;

    /**
     * 售后金额
     */
    private BigDecimal afterSaleAmount;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 调整金额
     */
    private BigDecimal adjustmentAmount;

    /**
     * 应付总额
     */
    private BigDecimal copeTotalAmount;

    /**
     * 订单备注
     */
    private String orderRemark;

    /**
     * 价格策略
     */
    private String priceStrategy;

    /**
     * 返点金额
     */
    private BigDecimal rebateAmount;
}
