package net.summerfarm.model.vo.finance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单概览excel
 *
 * <AUTHOR>
 * @Date 2023/4/18 12:57
 */
@Data
@Accessors(chain = true)
public class FinanceReceivableOverviewExportVO {
    /**
     * 账单编号
     */
    private String billNo;

    /**
     * 账单起止日
     */
    private String billCycle;

    /**
     * 客户名称
     */
    private String nameRemakes;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 工商名称
     */
    private String invoiceTitle;

    /**
     * 客户标签
     */
    private String customerTag;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 负责销售
     */
    private String salerName;

    /**
     * 应收金额
     */
    private BigDecimal receivableAmount;

    /**
     * 已收款金额
     */
    private BigDecimal writeOffAmount;

    /**
     * 未收款金额
     */
    private BigDecimal unWriteOffAmount;

    /**
     * 账单生成时间
     */
    private String billGenerationTime;

    /**
     * 付款周期
     */
    private String paymentCycle;

    /**
     * 到期日期
     */
    private String dueDate;

    /**
     * 是否逾期
     */
    private String isOverdue;

    /**
     * 逾期天数
     */
    private Integer daysOverdue;

    /**
     * 账单是否确认
     */
    private String billConfirmFlag;

    /**
     * 发票日期
     */
    private String invoiceDate;

    /**
     * 发票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 是否全额开票?
     */
    private String isFullInvoiced;

    /**
     * 收款状态
     */
    private String receiptStatus;
}
