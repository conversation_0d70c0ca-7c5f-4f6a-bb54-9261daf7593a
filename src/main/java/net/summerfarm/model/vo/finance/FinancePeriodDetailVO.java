package net.summerfarm.model.vo.finance;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/7/6 16:49
 */
@Data
public class FinancePeriodDetailVO {
    /**
     * 客户名称
     */
    private String nameRemakes;

    /**
     * 销售名称
     */
    private String salerName;

    /**
     * 客户类型
     */
    private Integer customerType;

    /**
     * 未确认金额总计
     */
    private BigDecimal unconfirmedAmount;

    /**
     * 待开票账单金额
     */
    private BigDecimal notBilledAmount;

    /**
     * 待核销金额总计
     */
    private BigDecimal receivableAmount;
}
