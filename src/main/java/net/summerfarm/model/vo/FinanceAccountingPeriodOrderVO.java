package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.FinanceAccountingPeriodOrder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title: FinanceAccountingPeriodOrderVO
 * @date 2021/12/914:36
 */
@ApiModel(value="账期订单实体类VO")
@Data
public class FinanceAccountingPeriodOrderVO extends FinanceAccountingPeriodOrder {

    @ApiModelProperty(value="最后调整时间")
    private LocalDateTime finalAdjustmentTime;

    @ApiModelProperty(value="应收总金额")
    private BigDecimal totalAmountReceivable;

    @ApiModelProperty(value="调整总金额")
    private BigDecimal total;

    @ApiModelProperty(value="条数")
    private Integer size;

    /**
     * 可调整实付金额
     */
    private BigDecimal useableMoney;

    /**
     * 可调整运费金额
     */
    private BigDecimal useableDeliveryFee;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 1 已确认的账单 2 有部分订单项在审批中
     */
    private Integer flag;

    /**
     * 调整实付金额
     */
    private BigDecimal adjustPrice;

    /**
     * 调整运费金额
     */
    private BigDecimal adjustDelivery;

    /**
     * 应收金额 = 实付 + 运费 - 售后 + 调整
     */
    private BigDecimal receivableMoney;

    /**
     * 账单核销状态
     */
    private Byte writeOffStatus;

    /**
     * 门店id
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String mname;

    @ApiModelProperty("票到付款 0 是 1 否")
    private Integer billToPay;

    /**
     * 待确认账单
     */
    private Integer confirmedBill;

    /**
     * 待核销账单
     */
    private Integer waitWriteOffBill;

    /**
     * 账单总数
     */
    private Integer billQuantity;

    /**
     * 待确认金额
     */
    private BigDecimal confirmedAmount;

    /**
     * 待核销金额
     */
    private BigDecimal waitWriteOffAmount;


    /**
     * 帐单数总计
     */
    private Integer billQuantityAmount;

    /**
     * 未确认账单
     */
    private Integer confirmedBillQuantity;

    /**
     * 待确认金额总计
     */
    private BigDecimal confirmedAmountTotal;

    /**
     * 待核销金额总计
     */
    private BigDecimal waitWriteOffAmountTotal;

    /**
     * 账单状态 1 待确定 2 待审核 3 待收款 4 已收款
     * 待确定 type = 0 customerConfirmStatus = 0
     * 待审核 type = 0 customerConfirmStatus = 1 financialAudit = 0
     * 待付款 type = 1 customerConfirmStatus = 1 financialAudit = 1
     * 已付款 type = 1 customerConfirmStatus = 1 financialAudit = 1 receiptStatus = 2
     */
    private Integer periodOrderType;
}
