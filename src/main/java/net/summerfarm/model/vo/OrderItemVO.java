package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.DTO.OrderItemInfoDTO;
import net.summerfarm.model.domain.DeliveryPathReturnSku;
import net.summerfarm.model.domain.DeliveryPathShortSku;
import net.summerfarm.model.domain.OrderItem;
import net.summerfarm.model.domain.TmsWithoutCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Package: net.summerfarm.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/9/12
 */
@ApiModel(description = "订单详情VO")
public class OrderItemVO extends OrderItem{

    @ApiModelProperty(value = "售卖价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "商户名称")
    private String mname;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "下单时间")
    private LocalDateTime orderTime;

    @ApiModelProperty(value = "配送结束时间")
    private LocalDateTime finishTime;

    @ApiModelProperty(value = "城市编号")
    private Integer areaNo;

    private String mapping;

    private String mappingName;

    private String suitName;

    public Integer suitAmount;

    private Integer sumOrders;

    private Integer sumQuantity;

    @ApiModelProperty(value = "配送时间")
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate deliveryTime;

    @ApiModelProperty(value = "订单备注")
    private String remark;

    @ApiModelProperty("喜茶订单号")
    private String htOrderCode;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDate startTime;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDate endTime;


    private Long mId;

    private Integer direct;

    @ApiModelProperty("sku类型")
    private Integer type;

    @ApiModelProperty("配送费")
    private BigDecimal deliveryFee;

    @ApiModelProperty("sku 类型")
    private Integer skuType;

    @ApiModelProperty("备注")
    private String nameRemakes;

    private String category;

    private String parentCategory;
    @ApiModelProperty("送货地址")
    private String address;

    @ApiModelProperty("预约商品数量")
    private Integer prePayAmount;


    @ApiModelProperty("类目类型")
    private Integer categoryType;

    @ApiModelProperty("sku配送类型  0 配送  1 回收")
    private Integer orderItemDeliveryType = 0;

    @ApiModelProperty("缺货信息")
    private DeliveryPathShortSku deliveryPathShortSku;

    @ApiModelProperty("拒收信息")
    @Setter
    @Getter
    private DeliveryPathReturnSku deliveryPathReturnSku;

    @ApiModelProperty("下单区域的sku所属仓对应的代仓或自己所属仓库")
    private Integer skuStoreNo;

    private Integer orderType;

    private Integer extType;

    private Integer afterSaleNum;

    /**
     * 订单项金额
     */
    private BigDecimal itemAmount;

    /**
     * 调整实付的金额
     */
    private BigDecimal adjustPrice;

    /**
     * 店铺等级
     */
    private Integer grade;

    /**
     * 客户类型
     */
    private Integer customerType;

    /**
    * 扫条码信息
    */
    private String codes;

    /**
    * 有货无码信息
    */
    private TmsWithoutCode tmsWithoutCode;

    /**
     * 联系方式
     */
    private String phone;

    /**
     * 库存仓
     */
    private Integer warehouseNo;

    public LocalDateTime getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(LocalDateTime finishTime) {
        this.finishTime = finishTime;
    }

    /**
    * 批次号
    */
    private String batchs;

    /**
     * 拦截数量
     */
    private Integer interceptNum;

    /**
     * 拦截时间
     */
    private LocalDateTime interceptTime;

    /**
     * sku头图
     */
    private String skuPic;

    private Long accountId;

    private Long pdId;

    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    private Integer subType;

    /**
     * 商品快照信息
     */
    private OrderItemInfoDTO orderItemInfoDTO;

    /**
     * 包装类型：0单品 1包裹
     */
    private Integer packType;

    /**
     * 买手名称(花名)
     */
    private String buyerName;

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public Integer getAfterSaleNum() {
        return afterSaleNum;
    }

    public void setAfterSaleNum(Integer afterSaleNum) {
        this.afterSaleNum = afterSaleNum;
    }

    public OrderItemVO() {
    }

    public OrderItemVO(String pdName, int amount, BigDecimal salePrice) {
        this.pdName = pdName;
        this.amount = amount;
        this.salePrice = salePrice;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }

    @Override
    public String getOrderNo() {
        return orderNo;
    }

    @Override
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDateTime getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(LocalDateTime orderTime) {
        this.orderTime = orderTime;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public String getMapping() {
        return mapping;
    }

    public void setMapping(String mapping) {
        this.mapping = mapping;
    }

    public String getMappingName() {
        return mappingName;
    }

    public void setMappingName(String mappingName) {
        this.mappingName = mappingName;
    }

    public Integer getSumOrders() {
        return sumOrders;
    }

    public void setSumOrders(Integer sumOrders) {
        this.sumOrders = sumOrders;
    }

    public Integer getSumQuantity() {
        return sumQuantity;
    }

    public void setSumQuantity(Integer sumQuantity) {
        this.sumQuantity = sumQuantity;
    }

    @Override
    public String toString() {
        return "OrderItemVO{" +
                "salePrice=" + salePrice +
                ", unit='" + unit + '\'' +
                ", mname='" + mname + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", orderTime=" + orderTime +
                ", id=" + id +
                ", sku='" + sku + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", amount=" + amount +
                ", price=" + price +
                ", pdName='" + pdName + '\'' +
                ", maturity='" + maturity + '\'' +
                ", weight='" + weight + '\'' +
                ", categoryId=" + categoryId +
                ", picturePath='" + picturePath + '\'' +
                ", addTime=" + addTime +
                ", storageLocation=" + storageLocation +
                ", _share=" + _share +
                '}';
    }

    public String getSuitName() {
        return suitName;
    }

    public void setSuitName(String suitName) {
        this.suitName = suitName;
    }

    public Integer getSuitAmount() {
        return suitAmount;
    }

    public void setSuitAmount(Integer suitAmount) {
        this.suitAmount = suitAmount;
    }


    public LocalDate getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDate startTime) {
        this.startTime = startTime;
    }

    public LocalDate getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDate endTime) {
        this.endTime = endTime;
    }


    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public Integer getDirect() {
        return direct;
    }

    public void setDirect(Integer direct) {
        this.direct = direct;
    }

    public LocalDate getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(LocalDate deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public BigDecimal getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(BigDecimal deliveryFee) {
        this.deliveryFee = deliveryFee;
    }

    public Integer getSkuType() {
        return skuType;
    }

    public void setSkuType(Integer skuType) {
        this.skuType = skuType;
    }

    public String getNameRemakes() {
        return nameRemakes;
    }

    public void setNameRemakes(String nameRemakes) {
        this.nameRemakes = nameRemakes;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getParentCategory() {
        return parentCategory;
    }

    public void setParentCategory(String parentCategory) {
        this.parentCategory = parentCategory;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getPrePayAmount() {
        return prePayAmount;
    }

    public void setPrePayAmount(Integer prePayAmount) {
        this.prePayAmount = prePayAmount;
    }

    public Integer getCategoryType() {
        return categoryType;
    }

    public void setCategoryType(Integer categoryType) {
        this.categoryType = categoryType;
    }

    public Integer getOrderItemDeliveryType() {
        return orderItemDeliveryType;
    }

    public void setOrderItemDeliveryType(Integer orderItemDeliveryType) {
        this.orderItemDeliveryType = orderItemDeliveryType;
    }

    public DeliveryPathShortSku getDeliveryPathShortSku() {
        return deliveryPathShortSku;
    }

    public void setDeliveryPathShortSku(DeliveryPathShortSku deliveryPathShortSku) {
        this.deliveryPathShortSku = deliveryPathShortSku;
    }

    public Integer getSkuStoreNo() {
        return skuStoreNo;
    }

    public void setSkuStoreNo(Integer skuStoreNo) {
        this.skuStoreNo = skuStoreNo;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getExtType() {
        return extType;
    }

    public void setExtType(Integer extType) {
        this.extType = extType;
    }

    public BigDecimal getItemAmount() {
        return itemAmount;
    }

    public void setItemAmount(BigDecimal itemAmount) {
        this.itemAmount = itemAmount;
    }

    public BigDecimal getAdjustPrice() {
        return adjustPrice;
    }

    public void setAdjustPrice(BigDecimal adjustPrice) {
        this.adjustPrice = adjustPrice;
    }

    public String getBatchs() {
        return batchs;
    }

    public void setBatchs(String batchs) {
        this.batchs = batchs;
    }

    public String getCodes() {
        return codes;
    }

    public void setCodes(String codes) {
        this.codes = codes;
    }

    public TmsWithoutCode getTmsWithoutCode() {
        return tmsWithoutCode;
    }

    public void setTmsWithoutCode(TmsWithoutCode tmsWithoutCode) {
        this.tmsWithoutCode = tmsWithoutCode;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
    public Integer getInterceptNum() {
        return interceptNum;
    }

    public void setInterceptNum(Integer interceptNum) {
        this.interceptNum = interceptNum;
    }

    public String getHtOrderCode() {
        return htOrderCode;
    }
    public void setHtOrderCode(String htOrderCode) {
        this.htOrderCode = htOrderCode;
    }

    public Integer getWarehouseNo() {
        return warehouseNo;
    }

    public void setWarehouseNo(Integer warehouseNo) {
        this.warehouseNo = warehouseNo;
    }

    public LocalDateTime getInterceptTime() {
        return interceptTime;
    }

    public void setInterceptTime(LocalDateTime interceptTime) {
        this.interceptTime = interceptTime;
    }

    public String getSkuPic() {
        return skuPic;
    }

    public void setSkuPic(String skuPic) {
        this.skuPic = skuPic;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getPdId() {
        return pdId;
    }

    public void setPdId(Long pdId) {
        this.pdId = pdId;
    }

    public Integer getSubType() {
        return subType;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }

    public OrderItemInfoDTO getOrderItemInfoDTO() {
        return orderItemInfoDTO;
    }

    public void setOrderItemInfoDTO(OrderItemInfoDTO orderItemInfoDTO) {
        this.orderItemInfoDTO = orderItemInfoDTO;
    }

    public DeliveryPathReturnSku getDeliveryPathReturnSku() {
        return deliveryPathReturnSku;
    }

    public void setDeliveryPathReturnSku(DeliveryPathReturnSku deliveryPathReturnSku) {
        this.deliveryPathReturnSku = deliveryPathReturnSku;
    }

    public Integer getPackType() {
        return packType;
    }

    public void setPackType(Integer packType) {
        this.packType = packType;
    }
}
