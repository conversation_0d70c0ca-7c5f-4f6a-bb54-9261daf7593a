package net.summerfarm.model.vo;

import net.summerfarm.model.domain.AreaSuit;
import net.summerfarm.model.domain.Suit;

import java.util.List;

/**
 * @Package: net.summerfarm.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/7/21
 */
public class SuitDetailVO extends Suit {

    private List<SuitItemVO> suitItemVOs;

    private List<AreaSuit> areaSuits;

    public SuitDetailVO() {
        super();
    }

    public SuitDetailVO(Suit suit, List<SuitItemVO> suitItemVOS, List<AreaSuit> areaSuits) {
        setId(suit.getId());
        setSuitName(suit.getSuitName());
        setAddtime(suit.getAddtime());
        setUpdatetime(suit.getUpdatetime());
        setSuitPic(suit.getSuitPic());
        this.suitItemVOs = suitItemVOS;
        this.areaSuits =areaSuits;
    }

    public List<AreaSuit> getAreaSuits() {
        return areaSuits;
    }

    public void setAreaSuits(List<AreaSuit> areaSuits) {
        this.areaSuits = areaSuits;
    }

    public List<SuitItemVO> getSuitItemVOs() {
        return suitItemVOs;
    }

    public void setSuitItemVOs(List<SuitItemVO> suitItemVOs) {
        this.suitItemVOs = suitItemVOs;
    }
}
