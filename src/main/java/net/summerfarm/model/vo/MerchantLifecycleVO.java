package net.summerfarm.model.vo;

import net.summerfarm.model.domain.Contact;
import net.summerfarm.model.domain.MerchantLifecycle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * @Package: net.summerfarm.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/3/9
 */
@ApiModel(description = "用户生命周期VO类")
public class MerchantLifecycleVO extends MerchantLifecycle {

    @ApiModelProperty(value = "管理员id")
    private Integer adminId;

    @ApiModelProperty(value = "管理员名称")
    private String adminName;

    @ApiModelProperty(value = "商户名称")
    private String mname;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "联系人")
    private String mcontact;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "上次跟进时间")
    private Date lastFollowUpTime;

    @ApiModelProperty(value = "城市编号")
    private Integer areaNo;

    @ApiModelProperty(value = "联系人列表")
    private List<Contact> contactList;

    @ApiModelProperty(value = "等级")
    private Integer grade;

    @ApiModelProperty(value = "开始时间")
    private LocalDate startDate;

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }

    public Date getLastFollowUpTime() {
        return lastFollowUpTime;
    }

    public void setLastFollowUpTime(Date lastFollowUpTime) {
        this.lastFollowUpTime = lastFollowUpTime;
    }

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMcontact() {
        return mcontact;
    }

    public void setMcontact(String mcontact) {
        this.mcontact = mcontact;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public List<Contact> getContactList() {
        return contactList;
    }

    public void setContactList(List<Contact> contactList) {
        this.contactList = contactList;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }
}
