package net.summerfarm.model.vo.plan;

import lombok.Data;
import net.summerfarm.module.scp.common.enums.ReplenishmentPlanEnums;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Date 2022/12/6 16:16
 */

@Data
public class ReplenishmentPlanVO {



    private Long id;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 补货冻结束时间
     */
    private String replenishmentFreezeEndTime;
    /**
     * 补货冻结开始时间
     */
    private String replenishmentFreezeStartTime;
    /**
     * 补货编号
     */
    private String replenishmentPlanNo;
    /**
     * 需求结束时间
     */
    private String requirementEndTime;
    /**
     * 需求编号
     */
    private String requirementPlanNo;
    /**
     * 需求开始时间
     */
    private String requirementStartTime;
    /**
     * 0:计划中 5:已完成
     * @see ReplenishmentPlanEnums.Status
     */
    private Integer status;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
