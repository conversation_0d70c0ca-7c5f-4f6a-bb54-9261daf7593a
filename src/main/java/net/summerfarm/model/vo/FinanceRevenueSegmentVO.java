package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.model.domain.FinanceRevenueSegment;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @title: FinanceRevenueSegmentVO
 * @date 2022/3/2917:40
 */
@Data
public class FinanceRevenueSegmentVO extends FinanceRevenueSegment {

    /**
     * 收入（现结+账期）
     */
    private BigDecimal income;
    /**
     * 成本（现结+账期）
     */
    private BigDecimal cost;

    /**
     * 收入同比
     */
    private BigDecimal cashSettlementYearOnYear;

    /**
     * 收入环比
     */
    private BigDecimal cashSettlementMonthOnMonth;

    /**
     * 账期比例值
     */
    private BigDecimal accountProportionalValue;

    /**
     * 预估账期收入
     */
    private BigDecimal accountEstimatedIncome;

    private LocalDate dayTime;
}
