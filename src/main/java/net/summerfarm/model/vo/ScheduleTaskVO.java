package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.domain.ScheduleTask;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-11-02
 * @description
 */
@Data
public class ScheduleTaskVO extends ScheduleTask {
    //下次执行时间
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime nextExecTime;
}
