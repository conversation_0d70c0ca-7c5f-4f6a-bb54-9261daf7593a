package net.summerfarm.model.vo;

import io.swagger.models.auth.In;
import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.domain.BatchProve;
import net.summerfarm.model.domain.WmsPesticideResidueReport;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.Date;

/**
 * <AUTHOR> ct
 * create at:  2021/10/21  15:11
 */
@Data
public class BatchProveVO extends BatchProve {

    /**
    * 批次
    */
    private String batch;

    /**
    * 数量
    */
    private Integer quantity;

    /**
    * 是否存在报告  0 没有 1 有
    */
    private Integer haveRedisReport;

    /**
    * 农残留报告
    */
    private WmsPesticideResidueReportVO residueReport;

    /**
     * 生产日期
     */
    @DateTimeFormat(pattern = DateUtils.SPECIFIC_DATE)
    LocalDate productionDate;
}
