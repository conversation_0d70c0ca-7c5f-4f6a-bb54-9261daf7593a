package net.summerfarm.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.model.domain.MerchantUpdateRecord;

import java.time.LocalDateTime;

/**
 * @Description: 门店转化记录VO
 * @Date: 2020/12/8 13:03
 * @Author: <EMAIL>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantUpdateRecordVO extends MerchantUpdateRecord {
    private static final long serialVersionUID = 4646053030594330699L;

    /**
     * 查询的开始时间
     */
    private LocalDateTime startTime;

    /**
     * 查询的结束时间
     */
    private LocalDateTime endTime;

}


