package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.model.domain.StockTaskItemDetail;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class StockTaskItemDetailVO extends StockTaskItemDetail {

    private String pdName;

    /**
     * 转入sku规格
     */
    private String weight;

    /**
    * 转出sku名称
    */
    private String rollOutPdName;

    /**
    * 转出sku规格
    */
    private String rollOutWeight;
    /**
    * 任务编号
    */
    private Integer stockTaskId;
    /**
    * 批次对应数量
    */
    private Integer storeQuantity;
    /**
     * 出入库仓库编号
     */
    private Integer warehouseNo;
    /**
     * 出入库类型
     */
    private Integer type;
    /**
     * 期望入库时间
     */
    private LocalDateTime expectTime;
    /**
     * 期望入库时间
     */
    private LocalDateTime addTime;
    /**
     * 是否次日达:1否,0或空是次日达
     */
    private Integer nextDayArrive;
    /**
     *货损类型
     */
    private String reasonType;
    /**
     * 货损原因
     */
    private String reason;

    /**
     * 成本
     */
    private BigDecimal cost;
    /**
     * 出库数量
     */
    private Integer outQuantity;

    private String remark;

    /**
     * 调入仓
     */
    private Integer inStore;

    /**
     * 调出仓
     */
    private Integer outStore;

















}
