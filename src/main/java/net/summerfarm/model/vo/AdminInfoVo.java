package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class AdminInfoVo implements Serializable {

    private Integer adminId;
    /**
     * 姓名
     */
    private String adminName;
    /**
     * 指定商品销量
     */
    private Integer desGoodsSales;
    /**
     * 单件指定商品奖励金额
     */
    private Integer desGoodsMoney;
    /**
     * 指定商品GMV
     */
    private BigDecimal desGoodsSalesGmv;
    /**
     * 总GMV
     */
    private BigDecimal totalGmv;
    /**
     * GMV目标
     */
    private String gmvCurrentProportion;
    /**
     * crm:gmv完成量
     */
    private String gmvTargetProportion;
    /**
     * 大客户GMV
     */
    private BigDecimal vipGmv;
    /**
     * 上月大客户GMV
     */
    private BigDecimal lastMonthVipGmv;
    /**
     * 大客户门店数
     */
    private Integer vipNum;
    /**
     * 大客户下单门店数
     */
    private Integer vipOrderNum;
    /**
     * 大客户未下单门店数
     */
    private Integer vipNotOrderNum;
    /**
     * 单店GMV
     */
    private BigDecimal singleShopGmv;
    /**
     * 上月单店GMV
     */
    private BigDecimal lastMonthSingleShopGmv;
    /**
     * 单店客户数
     */
    private Integer singleShopNum;
    /**
     * 单店下单客户数
     */
    private Integer singleShopOrderNum;
    /**
     * 单店未下单客户数
     */
    private Integer singleShopNotOrderNum;
    /**
     * 品牌客户数
     */
    private Integer brandNum;
    /**
     * 活跃客户数
     */
    private Integer monthLiving;
    /**
     * 活跃客户数目标
     */
    private String monthLivingProportion ;
    /**
     * 拜访数(不去重)
     */
    private Integer visitTotal;
    /**
     * 拜访数(去重)
     */
    private Integer visitNum;
    /**
     * 新客户数
     */
    private Integer newAdminNum;
    /**
     * 新客户下单数
     */
    private Integer newAdminOrderNum;
    /**
     * 私海
     */
    private String privateSeaProportion;
    /**
     * 客情
     */
    private String guestSentimentProportion;
    /**
     * 当前GMV
     */
    private BigDecimal gmvCurrent;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = DateUtils.MID_DATE_FORMAT)
    private LocalDate updateTime;
    /**
     * 是否被锁定
     */
    private Integer isDisabled;
    /**
     * gmv基础值
     */
    private String gmvTarget;
    /**
     * 本月核心客户数
     */
    private Integer coreMerchantNum;
    /**
     * 上月核心客户数
     */
    private Integer lastCoreMerchantNum;
    /**
     * 核心客户净增长数
     */
    private Integer coreMerchantGrowNum;
    /**
     * 距离下一牌级差值
     */
    private Integer gradeDifference;
    /**
     * 鲜果GMV
     */
    private BigDecimal fruitGmv;
    /**
     * 乳制品gmv
     */
    private BigDecimal dairyGmv;
    /**
     * 非乳制品gmv
     */
    private BigDecimal nonDairyGmv;
    /**
     * 自有品牌gmv
     */
    private BigDecimal brandGmv;
    /**
     * 绩效
     */
    private BigDecimal performance;
    /**
     * 绩效
     */
    private String performanceStr;
    /**
     * GMV奖励值
     */
    private BigDecimal gmvGoalProportion;
    /**
     * 普通拜访数
     */
    private Integer ordinaryNum;
    /**
     * 普通上门拜访数
     */
    private Integer dropInVisitNum;
    /**
     * 有效拜访数
     */
    private Integer efficientNum;
    /**
     * 价值拜访数
     */
    private Integer worthNum;
    /**
     * 大客户总业绩
     */
    private BigDecimal merchantTotalGmv;
    /**
     * 大客户账期总业绩
     */
    private BigDecimal creditPaidGmv;
    /**
     * 大客户现结总业绩
     */
    private BigDecimal cashSettlementGmv;
    /**
     * 总业绩(除安佳56217)
     */
    private BigDecimal merchantTotalGmvEx;
    /**
     * 账期总业绩(除安佳56217)
     */
    private BigDecimal creditPaidGmvEx;
    /**
     * 现结总业绩(除安佳56217)
     */
    private BigDecimal cashSettlementGmvEx;
    /**
     * 品类提成
     */
    private BigDecimal categoryAward;
    /**
     * 核心客户净增长数牌级
     */
    private BigDecimal coreMerchantCardLevel;

}
