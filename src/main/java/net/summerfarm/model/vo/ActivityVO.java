package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.Activity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-06-30
 * @description
 */
@ApiModel(description = "活动vo")
@Data
public class ActivityVO extends Activity {
    /**
     * 城市编号
     */
    @ApiModelProperty(value = "城市编号")
    private List<Integer> areaNoList;

    /**
     * sku
     */
    @ApiModelProperty(value = "sku")
    private String sku;
}
