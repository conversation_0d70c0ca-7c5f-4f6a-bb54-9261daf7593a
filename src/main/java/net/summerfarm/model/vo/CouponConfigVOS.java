package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: CouponConfigVOS
 * @date 2021/9/6 10:34
 */
@Data
public class CouponConfigVOS {

    @ApiModelProperty(value = "优惠券")
    List<CouponConfigVO> couponConfigVOS;

    @ApiModelProperty(value = "单店充送开关0 开启，1 关闭")
    Integer singleSwitch;

    @ApiModelProperty(value = "品牌总开关，开启（0）则为根据品牌自己的充送开关是否充送，关闭（1）则为所有品牌不充送")
    Integer brandSwitch;

    @ApiModelProperty(value = "自定义购卡金额，0 开启，1 关闭")
    Integer customAmount;

}
