package net.summerfarm.model.vo;

import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.domain.PurchasesBack;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.purchase.SaasSku;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PurchasesBackVO extends PurchasesBack implements SaasSku {

    @ApiModelProperty(value = "出库任务状态")
    private Integer stockState;

    /**
     * 商品名称
     */
    private String pdName;

    private String purchaser;

    /**
     * SKU 编号
     */
    private String sku;
    /**
     * saas SKU ID
     */
    private Long saasSkuId;

    private String saasSkuIdStr;

    /**
     * saas custom sku code
     */
    private String saasCustomSkuCode;

    /**
     * 用户id
     */
    private Integer adminId;

    /**
     * 用户名称
     */
    private String adminName;

    /**
     * 采购单号
     */
    private String purchasesNo;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime updatetime;

    /**
     * 采购单SKU列表
     */
    private List<PurchasesBackDetailVO> purchasesBackDetailVOS;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 供应商名称
     */
    private String supplier;

    /**
     * 供应商ID
     */
    private Integer supplierId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     saas账户名称（手机号）
     *
     */
    private String tenantName;

    /**
     * 仓库编号
     **/
    private Integer warehouseNo;
    /**
     * 仓库类型
     **/
    private Integer warehouseType;
    /**
     * 仓库类型名称
     **/
    private String warehouseTypeName;

    /**
     * 服务商
     **/
    private String serviceProviderName;

    /**
     * 仓库列表
     **/
    private List<Long> warehouseNoList;
    /**
     * 供应商ID 列表
     */
    private List<Integer> supplierIdList;
    /**
     * json参数
     */
    private String paramJson;

    private Integer pageIndex = 1;

    private Integer pageSize = 10;

}
