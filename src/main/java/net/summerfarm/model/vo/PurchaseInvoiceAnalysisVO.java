package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.PurchaseInvoiceAnalysis;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @title: PurchaseInvoiceAnalysisVO
 * @date 2021/8/722:00
 */
@Data
public class PurchaseInvoiceAnalysisVO extends PurchaseInvoiceAnalysis {

    @ApiModelProperty(value="含税金额总计")
    private BigDecimal includedTaxSum;

    private List<PurchaseInvoiceAnalysis> purchaseInvoiceAnalyses;

}
