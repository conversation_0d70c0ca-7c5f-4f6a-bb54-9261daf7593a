package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.enums.MerchantLabelTypeEnum;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 商户标签
 * @date 2023/4/11 15:08:16
 */
@Data
public class MerchantLabelVO implements Serializable {

    /**
     * primary key
     */
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 售后状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 标签类型  MerchantLabelTypeEnum
     * @see MerchantLabelTypeEnum
     */
    private Integer type;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Integer auditor;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;

}
