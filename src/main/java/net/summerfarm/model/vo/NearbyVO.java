package net.summerfarm.model.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-01-06
 * @description
 */
@Data
public class NearbyVO {
    private String mId;

    private String esId;

    private Long contactId;

    private String mname;

    private String contact;

    private String province;

    private String city;

    private String area;

    private String address;

    private String phone;

    private PoiVO poi;

    /**
     * 是否是公海客户
     */
    private Boolean openSeaFlag;

    /**
     * 是否是我的客户
     */
    private Boolean myMerchant;

    /**
     * 是否注册
     */
    private Boolean registerFlag;

    /**
     * 是否生成地推码
     */
    private Boolean leadFlag;

    /**
     * 距离（单位：米）
     */
    private Integer distance;

    private String ischain;

    private String cuisine_type;

    /**
     * 相关度评分
     */
    private transient Float score;
}
