package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.DiscountCard;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/5/20  14:17
 */
@Data
public class DiscountCardVO extends DiscountCard {

    /**
    * 适用sku
    */
    @ApiModelProperty(value = "适用sku")
    private String skus;

    /**
    * sku详情
    */
    @ApiModelProperty(value = "sku详情")
    private List<DiscountCardAvailableVO> discountCardAvailableVOS;
}
