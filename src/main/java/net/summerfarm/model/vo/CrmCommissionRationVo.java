package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(description = "gmv返参")
@Data
public class CrmCommissionRationVo implements Serializable {
    private static final long serialVersionUID = -3660169800595675461L;

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 区域名称
     */
    private String zoneName;

    /**
     *  低于-低于部分目标
     */
    private BigDecimal lowerBelow;

    /**
     * 低于-完成部分目标
     */
    private BigDecimal lowerFinish;

    /**
     * 低于-超出部分目标
     */
    private BigDecimal lowerBeyond;

    /**
     * 低于-新销售加成
     */
    private BigDecimal lowerNewBd;

    /**
     * 低于-新城市加成
     */
    private BigDecimal lowerNewArea;


    /**
     * 基础-低于部分目标
     */
    private BigDecimal baseBelow;

    /**
     * 基础-完成部分目标
     */
    private BigDecimal baseFinish;

    /**
     * 基础-超出部分目标
     */
    private BigDecimal baseBeyond;

    /**
     * 基础-新销售加成
     */
    private BigDecimal baseNewBd;

    /**
     * 基础-新城市加成
     */
    private BigDecimal baseNewArea;

    /**
     * 超出-低于部分目标
     */
    private BigDecimal excessBelow;

    /**
     * 超出-完成部分目标
     */
    private BigDecimal excessFinish;

    /**
     * 超出-超出目标
     */
    private BigDecimal excessBeyond;

    /**
     * 超出-新销售加成
     */
    private BigDecimal excessNewBd;

    /**
     * 超出-新城市加成
     */
    private BigDecimal excessNewArea;

}
