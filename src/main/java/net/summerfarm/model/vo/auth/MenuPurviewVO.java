package net.summerfarm.model.vo.auth;

import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@Data
public class MenuPurviewVO {
    /**
     * 权限id
     */
    private Long id;

    /**
     * 父权限Id
     */
    private Integer parentId;

    /**
     * 权限名称
     */
    private String menuName;

    /**
     * 权限对应资源(URL)
     */
    private String url;

    /**
     * 权限描述
     */
    private String description;

    /**
     * 关联权限
     */
    private String sonPurview;

    /**
     * 系统来源，0：srm 1:tms 2:admin 3:crm 4:cosfomanage 5:cosfo
     */
    private Byte systemOrigin;

    /**
     * 0 菜单 1模块 2按钮
     */
    private Byte type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后操作人ID
     */
    private String lastUpdater;

    /**
     * 最后更改时间
     */
    private Date updateTime;

    /**
     * 权限 名称
     */
    private String purviewName;
    /**
     * 0 默认 1超级管理员的权限
     */
    private Integer defaultType;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 关联权限
     */
    private List<String> sonPurviewList;

    /**
     * 子菜单
     */
    private List<MenuPurviewVO> childMenuPurviewVOS;


    public String getSonPurview() {
        if (CollectionUtils.isEmpty(sonPurviewList)) {
            return "";
        }
        return sonPurviewList.stream().collect(Collectors.joining(";"));
    }

    public String getMenuName() {
        if (StringUtils.isEmpty(menuName)){
            return purviewName;
        }
        return menuName;
    }
}
