package net.summerfarm.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/6/7 14:45
 */
@Data
public class BillInfoVo {
    /**
     * 账单周期
     */
    private String billCycle;

    /**
     * 账单id
     */
    private Integer billId;

    /**
     * 账单编号
     */
    private String billNo;

    /**
     * 应收金额
     */
    private BigDecimal receivableAmount;

    /**
     * 未核销金额
     */
    private BigDecimal unWriteOffAmount;

    /**
     * 已核销金额
     */
    private BigDecimal writeOffAmount;

    /**
     * 核销状态
     */
    private Integer receiptStatus;

}
