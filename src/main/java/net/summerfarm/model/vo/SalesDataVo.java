package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/16 16:27
 */
@Data
public class SalesDataVo {

    /**
     * 上月gmv
     */
    private BigDecimal lastMonthGmv;
    /**
     * 本月gmv
     */
    private BigDecimal currentMonthGmv;
    /**
     * 拉新数
     */
    private Integer introducingNewReward;
    /**
     * 总月活
     */
    private Integer monthLiving;
    /**
     * 私海月活数
     */
    private Integer privateMonthLiving;
    /**
     * 公海月活数
     */
    private Integer openMonthLiving;
    /**
     * 本月核心客户数
     */
    private Integer coreMerchantNum;
    /**
     * 上月核心客户数
     */
    private Integer lastMonthCoreMerchantNum;
    /**
     * 私海客户数
     */
    private Integer privateMerchant;
    /**
     * 私海当月未下单数
     */
    private Integer privateMerchantNoOrderNum;
    /**
     * 公海客户数
     */
    private Integer openMerchant;
    /**
     * 公海当月未下单数
     */
    private Integer openMerchantNoOrderNum;
    /**
     * 数据更新时间
     */
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate dataUpdateTime;
    /**
     * 类目:2乳制品,3非乳制品,4水果
     */
    private Integer categoryType;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 商品id
     */
    private Long pdId;
    /**
     * 商品名称
     */
    private String pdName;
    /**
     * 拜访数
     */
    private Integer visitNum;
    /**
     * 陪访数
     */
    private Integer escortNum;
    /**
     * 公海倒闭客户数
     */
    private Integer operateMerchantNum;

    public SalesDataVo() {
        this.lastMonthGmv = BigDecimal.ZERO;
        this.currentMonthGmv = BigDecimal.ZERO;
        this.introducingNewReward = 0;
        this.monthLiving = 0;
        this.privateMonthLiving = 0;
        this.openMonthLiving = 0;
        this.coreMerchantNum = 0;
        this.lastMonthCoreMerchantNum = 0;
        this.privateMerchant = 0;
        this.privateMerchantNoOrderNum = 0;
        this.openMerchant = 0;
        this.openMerchantNoOrderNum = 0;
    }
}
