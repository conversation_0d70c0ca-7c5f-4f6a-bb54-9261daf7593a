package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.model.DTO.AreaDTO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-07-10
 * @description
 */
@Data
public class MarketRuleVO {
    private Integer areaNo;
    private String sku;
    private Integer type;

    private Integer id;

    private String name;

    private String detail;

    private String showName;

    private Date startTime;

    private Date endTime;

    private String ruleDetail;

    // 0 生效中、1 未生效、2 已停用
    private Integer status;

    private Integer categoryId;

    // 0不支持省心送 1 支持省心送
    private Integer supportType;

    private List<AreaDTO> areaDTOList;

    private List<MarketRuleDetailVO> marketRuleDetailList;

    /**
     * 返券规则  1-确认收货后（默认）  2-支付完成后
     */
    private Integer couponRule;
}
