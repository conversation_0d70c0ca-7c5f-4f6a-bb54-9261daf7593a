package net.summerfarm.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description
 * @Date 2025/1/10 14:03
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PopProductCopyVO implements Serializable {

    private static final long serialVersionUID = 4035009303176704104L;

    /**
     * sku
     */
    private String sku;

    /**
     * pdNo
     */
    private String pdNo;

    /**
     * pdId
     */
    private Long pdId;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 规格
     */
    private String weight;

    /**
     * 重量
     */
    private BigDecimal weightNum;

    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 全路径类目名称
     */
    private String allPathCategoryName;

}
