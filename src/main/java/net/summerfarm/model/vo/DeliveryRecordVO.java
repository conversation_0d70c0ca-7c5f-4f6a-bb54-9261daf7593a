package net.summerfarm.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description: 配送记录业务类
 * date: 2022/5/12 17:10
 *
 * <AUTHOR>
 */
@Data
public class DeliveryRecordVO implements Serializable {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 配送数量汇总
     */
    private Integer sumQuantity;

    /**
     * 配送时间拼串
     */
    private String deliveryTimeStr;

}
