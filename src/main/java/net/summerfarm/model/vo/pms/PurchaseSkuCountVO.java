package net.summerfarm.model.vo.pms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chenjie
 * @date : 2023-03-06 12:03
 * @describe :
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseSkuCountVO {
    private String purchaseNo;
    private String sku;
    /** 实收数量 **/
    private Integer actCount;
    /** 未到数量 **/
    private Integer notArriveCount;
    /** 退货数量 **/
    private Integer inputBackCount;
    /** 退订数量 **/
    private Integer notInputBackCount;

}
