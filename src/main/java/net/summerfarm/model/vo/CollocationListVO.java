package net.summerfarm.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 搭配购列表
 */
@Data
public class CollocationListVO {
    /**
     * 搭配购包id
     */
    private Integer id;

    /**
     * 搭配包名称
     */
    private String collocationName;

    /**
     * 搭配包状态 0：禁用 1：启用
     */
    private Integer status;

    /**
     * 开启城市数
     */
    private Integer openAreaNum;

    /**
     * 包下sku数
     */
    private Integer skuNum;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
