package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.common.excel.utils.ExcelCheckResult;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.Products;
import net.summerfarm.model.domain.ProductsPropertyValue;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-02-25
 * @description
 */
@Data
public class ProductsCheckVO extends ExcelCheckResult {
    /**
     * 校验标识
     */
    private Boolean checkPass;
    /**
     * 是否需要处理spu
     */
    private Boolean productsHandleFlag;
    /**
     * spu信息
     */
    private Products products;
    /**
     * 关键属性
     */
    private List<ProductsPropertyValue> keyPropertyValue;
    /**
     * 是否需要处理sku
     */
    private Boolean inventoryHandleFlag;
    /**
     * sku信息
     */
    private Inventory inventory;
    /**
     * 销售属性
     */
    private List<ProductsPropertyValue> salePropertyValue;
}
