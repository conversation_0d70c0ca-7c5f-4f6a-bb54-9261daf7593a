package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.domain.Expense;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> xiang
 * create at:  2021-11-12
 */
@Data
public class ExpenseVO extends Expense {

    @ApiModelProperty(value = "报销明细")
    private List<ExpenseDetailVO> expenseDetails;

    @ApiModelProperty(value = "城配仓名称")
    private String storeName;

    @ApiModelProperty(value = "配送时间(支持配送时间段搜索)")
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate deliveryEndTime;

    @ApiModelProperty(value = "接收报销类型(多选筛选)")
    private String types;

    @ApiModelProperty(value = "报销类型(多选筛选)")
    private List<String> typeList;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;



}
