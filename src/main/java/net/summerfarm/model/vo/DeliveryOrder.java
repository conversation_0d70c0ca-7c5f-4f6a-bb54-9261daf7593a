package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.validation.annotation.InRange;

import java.util.Map;

@Data
public class DeliveryOrder {

    private String path;

    private String sku;

    private Integer amount;

    private String pdName;

    private String weight;

    //总数量
    private Integer total;

    //每条路线数量
    private Map<String,Integer> pathQuantity;

    @ApiModelProperty(value = "sku类型 0 自营 1 代仓")
    private Integer skuType;

    @ApiModelProperty(value = "大客户名称备注")
    private String nameRemakes;

    @ApiModelProperty(value = "存储区域:0：未分类，1：冷冻，2：冷藏，3：恒温，4：顶汇大流通")
    @InRange(rangeNums = {0, 1, 2, 3, 4})  //0：未分类，1：冷冻，2：冷藏，3：恒温，4：顶汇大流通
    public Integer storageLocation;

    @ApiModelProperty(value = "单位")
    private String unit;

    private Integer extType;

    @ApiModelProperty(value = "类目类型")
    private Integer categoryType;
}
