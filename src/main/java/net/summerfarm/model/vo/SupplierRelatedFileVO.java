package net.summerfarm.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SupplierRelatedFileVO implements Serializable {

    private static final long serialVersionUID=1L;

    private Long id;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 材料类型：1营业执照2生产许可证3经营许可证4开户许可证5身份证
     */
    private Integer fileType;

    /**
     * 材料七牛云url地址
     */
    private String fileUrl;

    /**
     * 开始日期
     */
    private LocalDateTime startDate;

    /**
     * 结束日期（永久为null）
     */
    private LocalDateTime endDate;




}
