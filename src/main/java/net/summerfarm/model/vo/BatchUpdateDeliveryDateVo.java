package net.summerfarm.model.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * Description: <br/>
 * date: 2022/4/19 16:11<br/>
 *
 * <AUTHOR> />
 */
@Data
public class BatchUpdateDeliveryDateVo implements Serializable {
    private Integer id;
    //OrderStatusEnum 3,6需要释放库存
    private Integer status;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate deliveryTime;

    private String orderNo;

    private Integer type;

    private Integer contactId;

    private Integer fenceId;
    //新修改的日期
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate newDeliveryTime;

    private LocalDate oldDeliveryTime;

    //客户经理电话
    private String phone;

    //商户电话
    private String merchantPhone;

    //商户openid
    private String openid;

    //联系人电话
    private String sendPhone;
}
