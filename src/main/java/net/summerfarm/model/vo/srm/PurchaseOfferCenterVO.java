package net.summerfarm.model.vo.srm;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PurchaseOfferCenterVO implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 报价单详情id
     */
    private Long id;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 规格
     */
    private String weight;

    /**
     * sku
     */
    private String sku;

    /**
     * 图片
     */
    private String pic;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 阶梯价
     */
    private List<SrmSupplierOfferDetailStepPriceVo> stepPriceVoList;

    /**
     * 生效时间
     */
    private LocalDateTime startTime;

    /**
     * 失效时间
     */
    private LocalDateTime endTime;

    /**
     * 状态，0：待生效，1：生效中，2：待报价
     */
    private Integer status;













}
