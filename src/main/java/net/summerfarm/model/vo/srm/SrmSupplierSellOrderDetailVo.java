package net.summerfarm.model.vo.srm;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SrmSupplierSellOrderDetailVo {
    /**
     * 采购单编号
     */
    private String purchaseNo;

    /**
     * 状态-1已作废,1已发布,2待确认
     */
    private Integer state;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 采购员
     */
    private String purchaser;

    /**
     * 仓库地址
     */
    private String warehouseAddress;

    /**
     * 订单日期
     */
    private LocalDateTime orderDate;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 销售订单项列表
     */
    private List<SrmSupplierSellOrderDetailItemVo> orderItemVoList;

    /**
     * 是否能发货
     */
    private Boolean shipStatus;


}
