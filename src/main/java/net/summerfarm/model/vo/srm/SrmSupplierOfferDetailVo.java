package net.summerfarm.model.vo.srm;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SrmSupplierOfferDetailVo implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 报价单id
     */
    private Long offerId;

    /**
     * 状态0待生效1生效中2已失效3已关闭
     */
    private Integer status;

    /**
     * 是否审核中0否1是
     */
    private Boolean isAudit;

    /**
     * 开始时间
     */
    private LocalDate startTime;

    /**
     * 结束时间
     */
    private LocalDate endTime;

    /**
     * 价格区间
     */
    private String priceInterval;

    /**
     * 阶梯价
     */
    private List<SrmSupplierOfferDetailStepPriceVo> stepPriceVoList;

//    /**
//     * 商品名称
//     */
//    private String pdName;
//
//    /**
//     * 仓库名称
//     */
//    private String warehouseName;
//
//    /**
//     * 仓库地址
//     */
//    private String warehouseAddress;
//
//    /**
//     * 图片
//     */
//    private String pic;
//
//    /**
//     * 规格
//     */
//    private String weight;





}
