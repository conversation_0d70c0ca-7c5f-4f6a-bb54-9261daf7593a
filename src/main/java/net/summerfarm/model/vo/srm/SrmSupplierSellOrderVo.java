package net.summerfarm.model.vo.srm;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class SrmSupplierSellOrderVo {
    /**
     * 采购单id
     */
    private String purchaseId;

    /**
     * 采购单编号
     */
    private String purchaseNo;

    /**
     * 商品项
     */
    private Integer skuNums;

    /**
     * 商品数量
     */
    private Integer totalQuantity;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 订单日期
     */
    private LocalDate orderDate;

    /**
     * 总价格
     */
    private BigDecimal totalPrice;

    /**
     * 是否可发货0不可1可
     */
    private Integer shipStatus;


}
