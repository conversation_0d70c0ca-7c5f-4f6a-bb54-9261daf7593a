package net.summerfarm.model.vo.purchase;

import lombok.Data;

@Data
public class ProductWarehouseConfigVO {

    /**
     * SPUID
     */
    private Long pdId;

    /**
     * SPUNo
     */
    private String pdNo;

    /**
     * SPU名称
     */
    private String pdName;

    /**
     * SPU图片地址
     */
    private String picUrl;

    /**
     * 带配置仓数
     */
    private Integer waitConfigWarehouse;

    /**
     * 负责人名称","隔开
     */
    private String adminName;

    /**
     * 计划员名称,多个用","隔开
     */
    private String plannerName;

}