package net.summerfarm.model.vo.purchase;

import lombok.Data;
import net.summerfarm.module.scp.common.enums.SupplierReplenishmentConfigEnums;

import java.time.LocalDateTime;

@Data
public class SupplierReplenishmentVO {

    /**
     * SPU_ID
     */
    private Long pdId;

    /**
     * 库存仓编码
     */
    private Integer warehouseNo;

    /**
     * 供应商ID
     */
    private Integer supplierId;

    /**
     * 补货模式 1:不定期不定量,2:定期不定量,3:不定期定量,4:定期定量
     * {@link SupplierReplenishmentConfigEnums.ReplenishmentMode}
     */
    private Integer replenishmentMode;

    /**
     * 提前期
     */
    private Integer preDay;

    /**
     * 备货期
     */
    private Integer backlogDay;

    /**
     * 订货时间周一到周日,1~7
     */
    private Integer orderDate;

    /**
     * 安全水位
     */
    private Integer safeWaterLevel;
}