package net.summerfarm.model.vo.purchase;

import lombok.Data;
import net.summerfarm.model.domain.purchase.SaasSku;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PurchasesPriceAmountVO {

    /**
     * saasSkuId
     */
    private Long saasSkuId;

    /**
     * sku
     */
    private String sku;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 税率
     */
    private BigDecimal taxRate;






}