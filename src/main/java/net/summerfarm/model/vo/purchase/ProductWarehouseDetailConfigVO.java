package net.summerfarm.model.vo.purchase;

import lombok.Data;

import java.util.List;

@Data
public class ProductWarehouseDetailConfigVO {

    /**
     * 配置ID
     */
    private Long configId;

    /**
     * SPU_ID
     */
    private Long pdId;

    /**
     * 库存仓编码
     */
    private Integer warehouseNo;

    /**
     * 库存仓名称
     */
    private String warehouseName;
    /**
     * 采购类型 1: 直采 2: 非直采
     * @see net.summerfarm.module.scp.common.enums.ProductWarehouseConfigEnums.PurchaseType
     */
    private Integer purchaseType;

    /**
     * 采购员ID
     */
    private Integer adminId;

    /**
     * 采购员名称
     */
    private String adminName;

    /**
     * 计划员名称
     */
    private String plannerName;

    /**
     * 计划员id
     */
    private Long plannerId;

    /**
     * 供应商ID
     */
    private Integer supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 首选标记(默认供应商) 0: 否 1: 是
     * @see net.summerfarm.contexts.Global#FALSE_FLAG
     * @see net.summerfarm.contexts.Global#TRUE_FLAG
     */
    private Integer primaryFlag;

    /**
     * 补货配置
     */
    private List<SupplierReplenishmentVO> replenishmentConfigs;

}
