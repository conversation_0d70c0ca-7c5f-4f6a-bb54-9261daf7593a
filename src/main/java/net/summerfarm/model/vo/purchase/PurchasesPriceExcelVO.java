package net.summerfarm.model.vo.purchase;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import lombok.Data;
import net.summerfarm.model.domain.purchase.SaasSku;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class PurchasesPriceExcelVO{

    /** saasSkuId **/
    @ExcelProperty(value = "SKU", index = 0)
    private Long saasSkuId;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "货品名称", index = 1)
    private String pdName;

    /**
     * 规格
     */
    @ExcelProperty(value = "规格", index = 2)
    private String weight;

    /**
     * 单位
     */
    @ExcelProperty(value = "规格单位", index = 3)
    private String unit;

    /**
     * 供应商
     */
    @ExcelProperty(value = "供应商", index = 4)
    private String supplierName;

    /** 含税价 **/
    @ExcelProperty(value = "含税价", index = 5)
    private BigDecimal price;
    /** 税率 **/
    @ExcelProperty(value = "税率", index = 6)
    private String taxRate;
    /**  税额  **/
    @ExcelProperty(value = "税额", index = 7)
    private BigDecimal taxPrice;
    /** 生效时间 **/
    @ExcelProperty(value = "生效时间", index = 8)
    private String startTime;
    /** 失效时间 **/
    @ExcelProperty(value = "失效时间", index = 9)
    private String endTime;

    /** 状态0待生效1生效中2已失效3已关闭 **/
    @ExcelProperty(value = "状态", index = 10)
    private String status;





}