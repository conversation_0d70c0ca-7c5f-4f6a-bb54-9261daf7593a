package net.summerfarm.model.vo.purchase;

import lombok.Data;
import net.summerfarm.module.scp.common.enums.StockDashboardEnums;

import java.math.BigDecimal;

@Data
public class SkuStockInfoVO {

    /**
     * sku
     */
    private String skuId;

    /**
     * 名称
     */
    private String skuName;

    /**
     * 规格
     */
    private String standard;

    /**
     * 图片地址
     */
    private String picUrl;

    /**
     * 库存视图状态0:计算中 1:充足2:售罄
     *
     * @see StockDashboardEnums.StockViewStatus
     */
    private Integer stockViewStatus;

    /**
     * 可用库存
     */
    private Integer enabledQuantity;

    /**
     * 采购在途库存
     */
    private Integer onWayQuantity;

    /**
     * 调拨在途库存
     */
    private Integer transferInInQuantity;

    /**
     * 安全库存
     */
    private Integer safeQuantity;

    /**
     * 冻结库存
     */
    private Integer lockQuantity;

    /**
     * 仓库库存
     */
    private Integer quantity;

    /**
     * 虚拟库存
     */
    private Integer onlineQuantity;

    /**
     * 采购订单在途
     */
    private Integer onWayOrderQuantity;

    /**
     * 采购在途
     */
    private Integer onWayAllQuantity;


    /**
     * 可用库存使用天数
     */
    private BigDecimal doc;


    /**
     * 在途可用库存
     */
    private BigDecimal onWayDoc;


}
