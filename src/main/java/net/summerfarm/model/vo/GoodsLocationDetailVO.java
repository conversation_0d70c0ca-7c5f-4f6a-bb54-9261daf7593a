package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.GoodsDetailRecord;
import net.summerfarm.model.domain.GoodsLocationDetail;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/3/25  14:36
 */
@Data
public class GoodsLocationDetailVO extends GoodsLocationDetail {

    /**
    * 采购时间
    */
    @ApiModelProperty(value = "采购时间")
    private LocalDate purchaseTime;
    /**
    * 生产日期
    */
    @ApiModelProperty(value = "生产日期")
    private LocalDate productionDate;

    /**
    * 总数量 - 销售冻结数量
    */
    @ApiModelProperty(value = "总数量 - 销售冻结数量")
    private Integer approveQuantity;
    /**
    * 货位转移
    */
    @ApiModelProperty(value = "货位转移")
    private List<GoodsDetailRecord> goodsDetailRecords;

    private String supplier;

    private Integer storeNo;

    private String weight;

    private String pdName;

    /** 储存区域 */
    @ApiModelProperty(value = "储存区域")
    private String storageArea;

    /** 包装 */
    @ApiModelProperty(value = "包装")
    private String packing;

    /**
     * 采购单总成本
     */
    private BigDecimal totalCost;

    /**
     * 采购单单个成本
     */
    private BigDecimal singleCost;

    /**
     * 实际数量
     */
    private Integer storeQuantity;
}
