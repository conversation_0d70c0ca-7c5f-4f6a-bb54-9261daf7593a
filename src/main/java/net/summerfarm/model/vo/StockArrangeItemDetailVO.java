package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.DTO.BatchProveDTO;
import net.summerfarm.model.domain.StockArrangeItemDetail;
import net.summerfarm.model.domain.purchase.SaasSku;

import java.io.Serializable;

@Data
public class StockArrangeItemDetailVO extends StockArrangeItemDetail implements SaasSku,Serializable {

    @ApiModelProperty(value = "是否填充货检")
    private Integer isInspect;

    @ApiModelProperty(value = "商品名称")
    private String pdName;

    @ApiModelProperty(value = "规格")
    private String weight;

    @ApiModelProperty(value = "类型 0 自营 1 代仓")
    private Integer type;

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "供应商Id")
    private Integer supplierId;

    @ApiModelProperty(value = "保质期时长")
    private Integer qualityTime;

    @ApiModelProperty(value = "保质期时长单位")
    private String qualityTimeUnit;

    @ApiModelProperty(value = "包装方式")
    private String packing;

    @ApiModelProperty(value = "剩余预约总量")
    private Integer remainQuantity;

    @ApiModelProperty(value = "预约总数量")
    private Integer totalQuantity;

    @ApiModelProperty(value = "储存区域")
    private String storageArea;

    @ApiModelProperty(value = "sku状态")
    private Integer status;

    @ApiModelProperty(value = "商品名称")
    private String title;

    @ApiModelProperty(value = "采购单编号")
    private String purchaseNo;

    @ApiModelProperty(value = "采购数量")
    private Integer purchaseQuantity;

    @ApiModelProperty(value = "采购计划id")
    private Integer purchasePlanId;

    @ApiModelProperty(value = "预约单条目预约数量")
    private Integer arrivalQuantity;

    @ApiModelProperty(value = "预约单条目实到数量")
    private Integer actualQuantity;

    @ApiModelProperty(value = "单个sku可预约数量")
    private Integer arrangeQuantity;

    @ApiModelProperty(value = "单个效期(stock_arrange_item_detail)实到数量")
    private Integer receiveQuantity;

    @ApiModelProperty(value = "存储区域")
    private Integer storageLocation;

    @ApiModelProperty(value = "证件信息")
    private BatchProveDTO batchProveDTO;

    @ApiModelProperty(value = "是否有证件，0没有，1有")
    private Integer proveStatus;

    private String weightNum;

    private String volume;
    private String pic;
    private String checkReport;
    /** 单位 **/
    private String unit;
    private Long saasSkuId;

    /**
     * saas custom sku code
     */
    private String saasCustomSkuCode;

    /** 四证 **/
    private WarehouseBatchProveRecordVO proveRecord;



}
