package net.summerfarm.model.vo.bms;

import lombok.Data;
import net.summerfarm.model.domain.bms.BmsDeliveryReconciliation;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/8/29
 */
@Data
public class BmsDeliveryReconciliationVO extends BmsDeliveryReconciliation {

    private String serviceArea;

    private String carrierName;

    private String paymentNo;

    private String storeName;

    private List<BmsDeliveryReconciliationAmountVO> amountVO;

    /**
     * 是否存在正在审批的调整单
     */
    private Integer haveExamineTask;

    /**
     * 调整单状态
     */
    private Integer adjustmentStatus;
    /**
     * 配送日期
     */
    private LocalDate deliveryDate;
    /**
     * 发起人
     */
    private String creatorName;
    /**
     * 最近操作人
     */
    private String updaterName;
}
