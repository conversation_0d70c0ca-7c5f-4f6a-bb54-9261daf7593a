package net.summerfarm.model.vo;

import net.summerfarm.model.domain.PurchasesConfig;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

public class PurchasesConfigVO extends PurchasesConfig {

    private Integer categoryId;

    private String category;

    private String pdName;

    private String weight;

    private Boolean onSale;

    private Integer quantity;

    private Integer lockQuantity;

    private Integer safeQuantity;

    private Integer roadQuantity;

    @ApiModelProperty(value = "30天平均销量")
    private BigDecimal thirtyAvgQuantity;

    @ApiModelProperty(value = "7天平均销量")
    private BigDecimal sevenAvgQuantity;

    @ApiModelProperty(value = "7天销量")
    private Integer sevenQuantity;

    @ApiModelProperty(value = "上个7天销量")
    private Integer lastSevenQuantity;

    @ApiModelProperty(value = "30天周转天数")
    private Integer turnOverDay;

    @ApiModelProperty(value = "未冻结省心送数量")
    private Integer unLockQuantity;

    @ApiModelProperty(value = "待消耗预付数量")
    private Integer unUsePrepayQuantity;

    private String nameRemakes;

    private List<Integer> areaNos;

    private Integer parentCategoryId;

    private Integer grandCategoryId;

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public Boolean getOnSale() {
        return onSale;
    }

    public void setOnSale(Boolean onSale) {
        this.onSale = onSale;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getLockQuantity() {
        return lockQuantity;
    }

    public void setLockQuantity(Integer lockQuantity) {
        this.lockQuantity = lockQuantity;
    }

    public Integer getSafeQuantity() {
        return safeQuantity;
    }

    public void setSafeQuantity(Integer safeQuantity) {
        this.safeQuantity = safeQuantity;
    }

    public Integer getRoadQuantity() {
        return roadQuantity;
    }

    public void setRoadQuantity(Integer roadQuantity) {
        this.roadQuantity = roadQuantity;
    }

    public List<Integer> getAreaNos() {
        return areaNos;
    }

    public void setAreaNos(List<Integer> areaNos) {
        this.areaNos = areaNos;
    }

    public Integer getTurnOverDay() {
        return turnOverDay;
    }

    public void setTurnOverDay(Integer turnOverDay) {
        this.turnOverDay = turnOverDay;
    }

    public Integer getUnLockQuantity() {
        return unLockQuantity;
    }

    public void setUnLockQuantity(Integer unLockQuantity) {
        this.unLockQuantity = unLockQuantity;
    }

    public BigDecimal getThirtyAvgQuantity() {
        return thirtyAvgQuantity;
    }

    public void setThirtyAvgQuantity(BigDecimal thirtyAvgQuantity) {
        this.thirtyAvgQuantity = thirtyAvgQuantity;
    }

    public BigDecimal getSevenAvgQuantity() {
        return sevenAvgQuantity;
    }

    public void setSevenAvgQuantity(BigDecimal sevenAvgQuantity) {
        this.sevenAvgQuantity = sevenAvgQuantity;
    }

    public Integer getSevenQuantity() {
        return sevenQuantity;
    }

    public void setSevenQuantity(Integer sevenQuantity) {
        this.sevenQuantity = sevenQuantity;
    }

    public Integer getLastSevenQuantity() {
        return lastSevenQuantity;
    }

    public void setLastSevenQuantity(Integer lastSevenQuantity) {
        this.lastSevenQuantity = lastSevenQuantity;
    }

    public String getNameRemakes() {
        return nameRemakes;
    }

    public void setNameRemakes(String nameRemakes) {
        this.nameRemakes = nameRemakes;
    }

    public Integer getUnUsePrepayQuantity() {
        return unUsePrepayQuantity;
    }

    public void setUnUsePrepayQuantity(Integer unUsePrepayQuantity) {
        this.unUsePrepayQuantity = unUsePrepayQuantity;
    }

    public Integer getParentCategoryId() {
        return parentCategoryId;
    }

    public void setParentCategoryId(Integer parentCategoryId) {
        this.parentCategoryId = parentCategoryId;
    }

    public Integer getGrandCategoryId() {
        return grandCategoryId;
    }

    public void setGrandCategoryId(Integer grandCategoryId) {
        this.grandCategoryId = grandCategoryId;
    }
}
