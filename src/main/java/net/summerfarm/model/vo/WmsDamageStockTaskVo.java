package net.summerfarm.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.domain.WmsDamageStockItem;
import net.summerfarm.model.domain.WmsDamageStockTask;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Classname WmsDamageStockTaskVo
 * @Description 货损出库任务Vo
 * @Date 2022/2/23 11:31
 * @Created by hx
 */
@Data
public class WmsDamageStockTaskVo extends WmsDamageStockTask {
    /**
     * 库存仓名称
     */
    private String warehouseName;
    /**
     * sku
     */
    private String sku;
    /**
     * 货损出库详情集合
     */
    private List<WmsDamageStockItem> wmsDamageStockItems;
    /**
     * 商品id
     */
    private Integer pdId;

    /**
     * 开始时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;


    /**
     * 类型列表
     */
    private List<Integer> typeList;

    /**
     * 创建人
     */
    private String creater;

    /**
     * saas skuId
     */
    private Long saasSkuId;
}
