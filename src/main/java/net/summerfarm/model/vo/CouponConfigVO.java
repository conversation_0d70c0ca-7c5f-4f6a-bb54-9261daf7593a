package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.CouponConfig;
import net.summerfarm.model.domain.CouponConfigDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @title: CouponConfigVO
 * @date 2021/9/6 10:30
 */
@Data
public class CouponConfigVO extends CouponConfig {

    @ApiModelProperty(value = "优惠券每档详情")
    List<CouponConfigDetail> couponConfigDetails;

}
