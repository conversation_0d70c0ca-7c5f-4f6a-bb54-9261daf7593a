package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.model.domain.StockTask;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class StockTaskResult extends StockTask {
    private List list; //放任意格式数据

    private List<AfterSaleOrderVO> afterSaleOrders;

    public List getList() {
        return list;
    }

    // 订单备注 店铺名称
    private String mname;

    // 客户名称
    private String orderRemark;

    public void setList(List list) {
        this.list = list;
    }

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }

    public String getOrderRemark() {
        return orderRemark;
    }

    public void setOrderRemark(String orderRemark) {
        this.orderRemark = orderRemark;
    }

    public List<AfterSaleOrderVO> getAfterSaleOrders() {
        return afterSaleOrders;
    }

    public void setAfterSaleOrders(List<AfterSaleOrderVO> afterSaleOrders) {
        this.afterSaleOrders = afterSaleOrders;
    }
    //订单编号
    //private String taskNo;
    //售后订单编号
    private String afterSaleOrderNo;
    //配送时间
    private LocalDateTime sendTime;
    //回收时间
    private LocalDateTime giveTime;
    //回收状态
    private Integer giveState;
    //回收司机
    private String driverName;
    //联系方式
    private String driverPhone;
    // 任务类型 1、退货 2、拒收 3、拦截
    private Integer taskType;



    //缺货类型
    private String lackType;
    //照片凭证
    private String pic;
    //缺货备注原因
    private String lackRemark;

    /**
     * 库存仓编号
     */
    private String warehouseName;

    /**
     * 城配仓编号
     */
    private String storeName;

    /**
     * 是否精细化管理
     */
    private Boolean openCabinetManagement;
}
