package net.summerfarm.model.vo;


import lombok.Data;
import net.summerfarm.model.domain.GoodsTransferItem;
import net.summerfarm.model.domain.GoodsTransferItemDetail;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/4/8  12:53
 */
@Data
public class GoodsTransferItemVO extends GoodsTransferItem {

    private List<GoodsTransferItemDetail> goodsTransferItemDetails ;

    /** 存储区域 */
    private String storageArea;

    /** 包装 */
    private String packing;

    private Integer extType;
}
