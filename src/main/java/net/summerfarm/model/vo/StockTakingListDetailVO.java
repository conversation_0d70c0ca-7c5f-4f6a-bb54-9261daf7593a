package net.summerfarm.model.vo;

import net.summerfarm.model.domain.StockTakingListDetail;

public class StockTakingListDetailVO extends StockTakingListDetail {

    private Integer storeQuantity;

    private Integer areaNo;

    private String nameRemakes;

    /** 储存区域 */
    private String storageArea;

    /** 包装 */
    private String packing;

    /** 一级类目，鲜果、非鲜果 */
    private String firstLevelCategory;

    /** 二级类目 */
    private String secondLevelCategory;

    private Integer extType;

    /**
    * 打印数量
    */
    private Integer printNumber;

    /**
     * 类目类型
     */
    private Integer canPrint;

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Integer getStoreQuantity() {
        return storeQuantity;
    }

    public void setStoreQuantity(Integer storeQuantity) {
        this.storeQuantity = storeQuantity;
    }

    public String getNameRemakes() {
        return nameRemakes;
    }

    public void setNameRemakes(String nameRemakes) {
        this.nameRemakes = nameRemakes;
    }

    public String getStorageArea() {
        return this.storageArea;
    }

    public void setStorageArea(String storageArea) {
        this.storageArea = storageArea;
    }

    public String getPacking() {
        return this.packing;
    }

    public void setPacking(String packing) {
        this.packing = packing;
    }

    public String getFirstLevelCategory() {
        return this.firstLevelCategory;
    }

    public void setFirstLevelCategory(String firstLevelCategory) {
        this.firstLevelCategory = firstLevelCategory;
    }

    public String getSecondLevelCategory() {
        return this.secondLevelCategory;
    }

    public void setSecondLevelCategory(String secondLevelCategory) {
        this.secondLevelCategory = secondLevelCategory;
    }

    public Integer getExtType() {
        return extType;
    }

    public void setExtType(Integer extType) {
        this.extType = extType;
    }

    public Integer getPrintNumber() {
        return printNumber;
    }

    public void setPrintNumber(Integer printNumber) {
        this.printNumber = printNumber;
    }

    public Integer getCanPrint() {
        return canPrint;
    }

    public void setCanPrint(Integer canPrint) {
        this.canPrint = canPrint;
    }
}
