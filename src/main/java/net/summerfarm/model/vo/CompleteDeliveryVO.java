package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.CompleteDelivery;
import net.summerfarm.model.domain.CompleteDeliveryCustomer;

import java.util.List;

/**
 * <AUTHOR> 2021/07/09
 */
@Data
public class CompleteDeliveryVO extends CompleteDelivery {

    @ApiModelProperty(value = "大客户ids")
    private List<Integer> adminIds;

    @ApiModelProperty(value = "城市编号")
    private List<Integer> areaNos;

    @ApiModelProperty(value = "城配仓名称")
    private String storeName;

    @ApiModelProperty(value = "城市名称")
    private String areaName;

    @ApiModelProperty(value = "大客户名称备注")
    private String nameRemakes;

    @ApiModelProperty(value = "大客户")
    private List<CompleteDeliveryCustomer> completeDeliveryCustomers;

    @ApiModelProperty(value = "会员名称")
    private String mname;

    @ApiModelProperty(value = "客户类型")
    private String size;

    @ApiModelProperty(value = "区域编码集合")
    private List<String> adCodes;

    @ApiModelProperty(value = "行政区域")
    private List<AreaAdCodeVO> areaAdCodeList;

}
