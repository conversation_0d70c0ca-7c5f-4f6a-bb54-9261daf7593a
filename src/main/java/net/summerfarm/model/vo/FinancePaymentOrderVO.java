package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.model.domain.FinancePaymentOrder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @description 预付单VO
 * <AUTHOR>
 * @date 2022/1/21 11:29
 */
@Data
public class FinancePaymentOrderVO extends FinancePaymentOrder {

    /**
     * 预付单id
     */
    private Long purchaseAdvancedOrderId;

    /**
     * 对账单id
     */
    private Long statementsId;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 供应商
     */
    private String supplierName;

    /**
     * 付款方式 1.银行卡 2.现金
     */
    private Integer payType;

    /**
     * 银行卡归属地
     */
    private String accountAscription;

    /**
     * 开户银行
     */
    private String accountBank;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账号
     */
    private String account;

    public FinancePaymentOrderVO() {
    }

    public FinancePaymentOrderVO(Long id, Long additionalId, Integer type, BigDecimal amount, String remark, Integer status, String creator, LocalDateTime createTime, String updater, LocalDateTime updateTime, String paymentVoucher) {
        super(id, additionalId, type, amount, remark, status, creator, createTime, updater, updateTime, paymentVoucher);
    }

}
