package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.tms.dist.dto.DistOrderDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 订单配送详情
 * @date 2022/8/30 16:07
 * @Version 1.0
 */
@Data
public class OrderShippingDetails{

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 库存仓名称
     */
    private Set<String> warehouseNames;

    /**
     * 司机名称
     */
    private String driver;
    /**
     * 司机电话
     */
    private String driverPhone;
    /**
     * 配送状态，10：未签收，20：已签收，30：签收异常
     */
    private Integer status;
    /**
     * 签收照片
     */
    private String signInPic;
    /**
     * 配送照片签收面单
     */
    private String singInSignPic;
    /**
     * 配送照片货物照片
     */
    private String singInProductPic;
    /**
     * 完成配送时间
     */
    private LocalDateTime finishTime;

    /**
     * 签收备注
     */
    private String signInRemark;
}
