package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.PrepayInventory;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-07-21
 * @description
 */
@Data
@ApiModel(description = "大客户锁量实体")
public class PrepayInventoryVO extends PrepayInventory {
    @ApiModelProperty(value = "规格")
    private String weight;

    @ApiModelProperty(value = "商品名称")
    private String pdName;

    @ApiModelProperty(value = "客户名称")
    private String realname;

    private Integer extType;
}
