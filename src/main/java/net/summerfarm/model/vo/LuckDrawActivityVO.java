package net.summerfarm.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 红包雨/每日活动
 * @date 2023/5/12 15:44:34
 */
@Data
public class LuckDrawActivityVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 预热时间
     */
    private LocalDateTime preheatTime;

    /**
     * 活动类型  红包雨-1  每日抽奖-2
     */
    private Integer type;

    /**
     * 活动类型  红包雨-1  每日抽奖-2
     */
    private String typeName;

    /**
     * 活动状态 1-未开始  2-预热中  3-进行中  4-已结束
     */
    private Integer status;

    /**
     * 规则说明-落地页ID
     */
    private String rule;

    /**
     * 规则说明-落地页ID
     */
    private String landPageName;


    /**
     * 浮标图
     */
    private String buoyImage;

    /**
     * 预热背景图
     */
    private String preheatBackground;

    /**
     * 结束背景图
     */
    private String endBackground;

    /**
     * 未抽奖背景图
     */
    private String noDrawBackground;

    /**
     * 抽奖背景图
     */
    private String drawBackground;

    /**
     * 分享图
     */
    private String shardImage;

    /**
     * 分享标题
     */
    private String shardTitle;

    /**
     * 分享描述
     */
    private String shardRemake;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 活动奖项
     */
    private List<LuckyDrawActivityEquityPackageVO> equityPackageVOs;
}
