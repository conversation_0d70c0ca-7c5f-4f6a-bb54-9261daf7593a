package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Description: <br/>
 * date: 2022/4/1 14:53<br/>
 *
 * <AUTHOR> />
 */
@Data
public class RejectSaleNeedStockVo {

    private String sku;

    private Integer amount;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "出库仓")
    private Integer areaNo;

    @ApiModelProperty(value = "城配仓")
    private Integer outStoreNo;

    private Long mId;


    /**
     * 获取订单号和仓库号
     * @return
     */
    public String getOrderNoStoreNo(){
        return this.getOrderNo()+this.getAreaNo();
    }



}
