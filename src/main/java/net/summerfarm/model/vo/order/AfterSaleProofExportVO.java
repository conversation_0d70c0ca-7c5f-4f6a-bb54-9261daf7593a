package net.summerfarm.model.vo.order;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import lombok.Data;
import lombok.experimental.Accessors;
import net.summerfarm.common.excel.utils.ExcelUrlConverterUtil;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@HeadFontStyle(fontHeightInPoints = 12)
@ContentFontStyle(fontHeightInPoints = 10)
@ColumnWidth(15)
@HeadRowHeight(30)
@ExcelIgnoreUnannotated
public class AfterSaleProofExportVO {

    /**
     * 订单编号
     */
    @ColumnWidth(23)
    @ExcelProperty(value = "售后单编号")
    private String afterSaleNo;

    /**
     * 司机送达时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "售后单创建时间")
    private String createTime;

    /**
     * 售后凭证
     */
    @ExcelProperty(value = "售后凭证" ,converter = ExcelUrlConverterUtil.class)
    private List<URL> proof;


}
