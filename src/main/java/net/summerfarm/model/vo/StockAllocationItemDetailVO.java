package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.StockAllocationItemDetail;

@ApiModel(description = "调拨条目明细VO")
@Data
public class StockAllocationItemDetailVO extends StockAllocationItemDetail {

    @ApiModelProperty(value = "sku")
    private String sku;

    /**
     * 证件信息
     */
    WarehouseBatchProveRecordVO batchProve;
}
