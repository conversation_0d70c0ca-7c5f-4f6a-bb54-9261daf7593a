package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.FinanceAccountingStore;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title: FinanceAccountingStoreVO
 * @date 2021/12/9 14:46
 */
@Data
public class FinanceAccountingStoreVO extends FinanceAccountingStore {

    @ApiModelProperty(value="运营服务区域名称")
    private String areaName;

    @ApiModelProperty(value="账单编号")
    private String billNumber;

    @ApiModelProperty(value="调整总金额")
    private BigDecimal storeTotal;

    @ApiModelProperty(value="门店应收总金额")
    private BigDecimal storeTotalAmount;

    /**
     * 账单生成时间
     */
    private LocalDateTime billGenerationTime;

}
