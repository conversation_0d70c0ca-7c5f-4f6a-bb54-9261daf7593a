package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.model.domain.PriceAdjustmentRuleSection;

import java.io.Serializable;
import java.util.List;

@Data
public class PriceAdjustRuleVo implements Serializable {
    private static final long serialVersionUID = -6954246965366517309L;

    private List<Integer> areaNo;

//    private List<Integer> warehouseNo;

//    private List<Integer> storeNo;

    private List<PriceAdjustmentRuleSection> priceAdjustmentRuleSections;

}
