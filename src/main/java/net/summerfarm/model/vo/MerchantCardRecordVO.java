package net.summerfarm.model.vo;

import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.domain.MerchantCardRecord;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MerchantCardRecordVO extends MerchantCardRecord {

    private BigDecimal totalPrice;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime orderTime;

    private BigDecimal deliveryFee;

}
