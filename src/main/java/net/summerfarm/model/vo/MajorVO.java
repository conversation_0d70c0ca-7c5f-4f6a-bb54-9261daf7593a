package net.summerfarm.model.vo;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;

public class MajorVO implements Serializable{
    private List<MajorDataCountVO> majorDataCountVOS;
    private List<HashMap> dataTrend;
    private List<MajorSkuDataVO> majorSkuDataVOS;
    private LocalDate createTime;

    public List<MajorDataCountVO> getMajorDataCountVOS() {
        return majorDataCountVOS;
    }

    public void setMajorDataCountVOS(List<MajorDataCountVO> majorDataCountVOS) {
        this.majorDataCountVOS = majorDataCountVOS;
    }

    public List<HashMap> getDataTrend() {
        return dataTrend;
    }

    public void setDataTrend(List<HashMap> dataTrend) {
        this.dataTrend = dataTrend;
    }

    public List<MajorSkuDataVO> getMajorSkuDataVOS() {
        return majorSkuDataVOS;
    }

    public void setMajorSkuDataVOS(List<MajorSkuDataVO> majorSkuDataVOS) {
        this.majorSkuDataVOS = majorSkuDataVOS;
    }

    public LocalDate getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDate createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "MajorVO{" +
                "majorDataCountVOS=" + majorDataCountVOS +
                ", dataTrend=" + dataTrend +
                ", majorSkuDataVOS=" + majorSkuDataVOS +
                ", createTime=" + createTime +
                '}';
    }
}
