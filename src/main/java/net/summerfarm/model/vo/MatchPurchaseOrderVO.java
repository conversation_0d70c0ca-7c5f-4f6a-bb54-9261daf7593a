package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.MatchPurchaseOrder;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @title: MatchPurchaseOrderVO
 * @date 2021/8/417:20
 */
@Data
public class MatchPurchaseOrderVO extends MatchPurchaseOrder {

    @ApiModelProperty(value="匹配总金额")
    private BigDecimal matchAmounts;

    @ApiModelProperty(value = "sku规格")
    private String weight;

    @ApiModelProperty(value = "商品名称")
    private String pdName;

    @ApiModelProperty(value="发票代码")
    private String invoiceCode;

    @ApiModelProperty(value="发票号码")
    private String invoiceNumber;

    @ApiModelProperty(value="采购日期")
    private LocalDate purchaseTime;

    @ApiModelProperty(value="匹配进度")
    private String matchingSchedule;

    @ApiModelProperty(value="采购负责人")
    private String purchaser;

    @ApiModelProperty(value="待匹配金额")
    private BigDecimal waitMatch;

    @ApiModelProperty(value="待匹配总额")
    private BigDecimal waitMatchAmount;

    @ApiModelProperty(value="采购单数量")
    private Integer purchasesSum;

    @ApiModelProperty(value="sku数量")
    private Integer skuSum;

    @ApiModelProperty(value="匹配分拆次数")
    private Integer splitSum;

    @ApiModelProperty(value="实际可抵扣税额")
    private BigDecimal actualTaxAmount;
}
