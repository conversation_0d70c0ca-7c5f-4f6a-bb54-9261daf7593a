package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title: BalanceRefundAmountVO
 * @date 2021/9/9 16:12
 */
@Data
public class BalanceRefundAmountVO {
    @ApiModelProperty(value = "余额退还金额")
    private BigDecimal balanceRefundAmount;

    @ApiModelProperty(value = "余额退还金额模块门店数")
    private Integer balanceRefundAmountStore;

    @ApiModelProperty(value = "余额退还金额模块门店均金额")
    private Integer balanceRefundAmountAverage;

    @ApiModelProperty(value = "余额退还金额模块环比")
    private BigDecimal balanceRefundRatio;

    @ApiModelProperty(value = "余额退还金额模块同比")
    private BigDecimal balanceRefundYearOnYear;
}
