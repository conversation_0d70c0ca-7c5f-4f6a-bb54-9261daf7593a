package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.model.domain.InvoiceConfig;

import java.util.List;

/**
 * @Description:
 * @Date: 2021/1/21 14:58
 * @Author: <EMAIL>
 */

@Data
public class InvoiceConfigVO  extends InvoiceConfig {
    private static final long serialVersionUID = 5038623509015185178L;

    /**
     * 已关联的门店列表id
     */
    @ApiModelProperty(value = "已关联的门店列表id")
    private List<Long> linkedIdsList;


    /**
     *用于标识是新增还是添加（0：新增， 1：更新）
     */
    @ApiModelProperty(value = "用于标识是新增还是添加（0：新增， 1：更新）")
    private Integer character;

    /**
     *已关联的对应的门店关系列表
     */
    @ApiModelProperty(value = "已关联的对应的门店关系列表")
    private List<InvoiceMerchantRelationVO> linkedList;

    /**
     * 已关联的门店的个数
     */
    @ApiModelProperty(value = "已关联的门店的个数")
    private Integer  merchantCount;

    /**
     * 对应id的入参别名
     */
    @ApiModelProperty(value = "对应id的入参别名")
    private Long invoiceConfigId;

    @ApiModelProperty(value = "品牌抬头")
    private String oldInvoiceTitle;

    @ApiModelProperty(value = "品牌税号")
    private String oldTaxNumber;

    /**
     * 入口标识 0门店列表 非0其它
     */
    private Integer entrance;
}


