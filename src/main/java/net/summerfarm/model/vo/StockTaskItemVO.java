package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.domain.StockTaskItem;
import net.summerfarm.model.domain.purchase.SaasSku;
import net.summerfarm.model.domain.wms.StockTaskItemCabinetOccupyDO;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class StockTaskItemVO extends StockTaskItem implements SaasSku {
    private String pdName;

    private String weight;

    private String pack;

    private String unit;

    private Integer status;

    @ApiModelProperty(value = "大客户名称备注")
    private String nameRemakes;

    private Integer categoryId;

    private Integer pdId;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;

    private Integer areaNo;

    private Integer outStoreNo;

    private List<StockTaskItemDetailVO> stockTaskItemDetailVOS;

    private Integer taskState;

    private LocalDateTime finishTime;

    private Integer storageLocation;

    @ApiModelProperty(value = "sku类型 0 自营 1 代仓")
    private Integer skuType;

    /**
    * sku出库数量总计
    */
    private Integer detailQuantity;

    private Integer categoryType;

    /** 储存区域 */
    private String storageArea;

    /** 包装 */
    private String packing;

    /** 一级类目，鲜果、非鲜果 */
    private String firstLevelCategory;

    /** 二级类目 */
    private String secondLevelCategory;

    /**
     * 商品性质
     */
    private Integer extType;

    private Integer characters;

    private String supplier;

    @ApiModelProperty(value = "重量")
    private BigDecimal weightNum;

    private String category;

    private String volume;

    private Long saasSkuId;

    public String getCabinetCode() {
        return cabinetCode;
    }

    public void setCabinetCode(String cabinetCode) {
        this.cabinetCode = cabinetCode;
    }

    public Long getCabinetId() {
        return cabinetId;
    }

    public void setCabinetId(Long cabinetId) {
        this.cabinetId = cabinetId;
    }

    /**
     * 库位编码
     */
    private String cabinetCode;

    /**
     * 库位ID
     */
    private Long cabinetId;

    /**
     * saas custom sku code
     */
    private String saasCustomSkuCode;

    /**
     * 采购退货库位锁定明细
     */
    @ApiModelProperty(value = "采购退货库位锁定明细")
    private List<StockTaskItemCabinetOccupyDO> cabinetOccupyDOList;

    public Long getSaasSkuId() {
        return saasSkuId;
    }

    public void setSaasSkuId(Long saasSkuId) {
        this.saasSkuId = saasSkuId;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getSaasCustomSkuCode() {
        return saasCustomSkuCode;
    }

    public void setSaasCustomSkuCode(String saasCustomSkuCode) {
        this.saasCustomSkuCode = saasCustomSkuCode;
    }

    public Integer getCharacters() {
        return characters;
    }

    public void setCharacters(Integer characters) {
        this.characters = characters;
    }

    public Integer getSkuType() {
        return skuType;
    }

    public void setSkuType(Integer skuType) {
        this.skuType = skuType;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getPack() {
        return pack;
    }

    public void setPack(String pack) {
        this.pack = pack;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<StockTaskItemDetailVO> getStockTaskItemDetailVOS() {
        return stockTaskItemDetailVOS;
    }

    public void setStockTaskItemDetailVOS(List<StockTaskItemDetailVO> stockTaskItemDetailVOS) {
        this.stockTaskItemDetailVOS = stockTaskItemDetailVOS;
    }

    public String getNameRemakes() {
        return nameRemakes;
    }

    public void setNameRemakes(String nameRemakes) {
        this.nameRemakes = nameRemakes;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getPdId() {
        return pdId;
    }

    public void setPdId(Integer pdId) {
        this.pdId = pdId;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Integer getOutStoreNo() {
        return outStoreNo;
    }

    public void setOutStoreNo(Integer outStoreNo) {
        this.outStoreNo = outStoreNo;
    }

    public Integer getTaskState() {
        return taskState;
    }

    public void setTaskState(Integer taskState) {
        this.taskState = taskState;
    }

    public LocalDateTime getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(LocalDateTime finishTime) {
        this.finishTime = finishTime;
    }

    public Integer getStorageLocation() {
        return storageLocation;
    }

    public void setStorageLocation(Integer storageLocation) {
        this.storageLocation = storageLocation;
    }

    public Integer getDetailQuantity() {
        return detailQuantity;
    }

    public void setDetailQuantity(Integer detailQuantity) {
        this.detailQuantity = detailQuantity;
    }

    public Integer getCategoryType() {
        return categoryType;
    }

    public void setCategoryType(Integer categoryType) {
        this.categoryType = categoryType;
    }

    public String getStorageArea() {
        return this.storageArea;
    }

    public void setStorageArea(String storageArea) {
        this.storageArea = storageArea;
    }

    public String getPacking() {
        return this.packing;
    }

    public void setPacking(String packing) {
        this.packing = packing;
    }

    public String getFirstLevelCategory() {
        return this.firstLevelCategory;
    }

    public void setFirstLevelCategory(String firstLevelCategory) {
        this.firstLevelCategory = firstLevelCategory;
    }

    public String getSecondLevelCategory() {
        return this.secondLevelCategory;
    }

    public void setSecondLevelCategory(String secondLevelCategory) {
        this.secondLevelCategory = secondLevelCategory;
    }

    public Integer getExtType() {
        return extType;
    }

    public void setExtType(Integer extType) {
        this.extType = extType;
    }

    public BigDecimal getWeightNum() {
        return weightNum;
    }

    public void setWeightNum(BigDecimal weightNum) {
        this.weightNum = weightNum;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }
}
