package net.summerfarm.model.vo;

import net.summerfarm.common.util.DateUtils;
import net.summerfarm.model.domain.StockTaking;
import net.summerfarm.model.domain.StockTakingItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel(description = "盘点任务VO")
public class StockTakingVO extends StockTaking {

    @ApiModelProperty(value = "出入库任务id")
    private Integer stockTaskId;

    @ApiModelProperty(value = "任务状态")
    private Integer state;

    private String pdName;

    @ApiModelProperty(value = "预计完成时间")
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime expectTime;

    @ApiModelProperty(value = "任务发起时间")
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime addtime;

    private Integer areaNo;

    private String nameRemakes;

    private Integer dimension;

    private List<StockTakingItem> stockTakingItems;

    /**
     * 库存仓名称
     */
    private String warehouseName;


    public List<StockTakingItem> getStockTakingItems() {
        return stockTakingItems;
    }

    public void setStockTakingItems(List<StockTakingItem> stockTakingItems) {
        this.stockTakingItems = stockTakingItems;
    }

    public Integer getStockTaskId() {
        return stockTaskId;
    }

    public void setStockTaskId(Integer stockTaskId) {
        this.stockTaskId = stockTaskId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName;
    }

    public LocalDateTime getExpectTime() {
        return expectTime;
    }

    public void setExpectTime(LocalDateTime expectTime) {
        this.expectTime = expectTime;
    }

    public LocalDateTime getAddtime() {
        return addtime;
    }

    public void setAddtime(LocalDateTime addtime) {
        this.addtime = addtime;
    }

    @Override
    public Integer getAreaNo() {
        return areaNo;
    }

    @Override
    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public String getNameRemakes() {
        return nameRemakes;
    }

    public void setNameRemakes(String nameRemakes) {
        this.nameRemakes = nameRemakes;
    }

    public Integer getDimension() {
        return dimension;
    }

    public void setDimension(Integer dimension) {
        this.dimension = dimension;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }
}
