package net.summerfarm.model.vo;

import net.summerfarm.enums.SkuTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * @Package: net.summerfarm.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/6/19
 */
public class SKUVO {

    private String sku;

    private String pdName;

    private String weight;

    private String maturity;

    private String baseSaleUnit;

    private String baseSaleQuantity;

    private Integer pdId;

    /**
     * spuId
     */
    private String pdNo;

    /**
     * 品牌
     */
    private String brand;

    private Integer quantity;

    private String category;

    private Integer qualityTime;

    private String qualityTimeUnit;

    private String volume;

    private BigDecimal weightNum;
    private Integer adminId;

    private Integer characters;

    private String nameRemakes;

    private Integer categoryId;

    /** 储存区域 */
    private String storageArea;

    /** 包装 */
    private String packing;

    //sku头图
    private String skuPic;

    //spu头图
    private  String spuPic;

    /**
     * 类目类型
     **/
    private Integer categoryType;

    private Integer type;
    /**
     * 二级商品类型
     */
    @Getter
    @Setter
    private Integer subType;

    private Integer isDomestic;

    /**
     * 其他信息
     */
    public String getOtherAttribute() {
        String skuTypeStr = "";
        // 自营还是代仓
        if (SkuTypeEnum.SELF_SUPPORT.getId().equals(type)) {
            skuTypeStr = SkuTypeEnum.SELF_SUPPORT.getStatus();
        } else if (SkuTypeEnum.SUBSTITUTE_WAREHOUSE.getId().equals(type)) {
            skuTypeStr = SkuTypeEnum.SUBSTITUTE_WAREHOUSE.getStatus();
        }
        // 包装方式 packing
        // 国产还是进口
        String origin = Objects.equals(NumberUtils.INTEGER_ZERO, isDomestic) ? "进口" : "国产";
        return StringUtils.joinWith("/", packing, skuTypeStr, origin);
    }


    public String getBaseSaleUnit() {
        return baseSaleUnit;
    }

    public void setBaseSaleUnit(String baseSaleUnit) {
        this.baseSaleUnit = baseSaleUnit;
    }

    public String getBaseSaleQuantity() {
        return baseSaleQuantity;
    }

    public void setBaseSaleQuantity(String baseSaleQuantity) {
        this.baseSaleQuantity = baseSaleQuantity;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getMaturity() {
        return maturity;
    }

    public void setMaturity(String maturity) {
        this.maturity = maturity;
    }

    public Integer getPdId() {
        return pdId;
    }

    public void setPdId(Integer pdId) {
        this.pdId = pdId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Integer getQualityTime() {
        return qualityTime;
    }

    public void setQualityTime(Integer qualityTime) {
        this.qualityTime = qualityTime;
    }

    public String getQualityTimeUnit() {
        return qualityTimeUnit;
    }

    public void setQualityTimeUnit(String qualityTimeUnit) {
        this.qualityTimeUnit = qualityTimeUnit;
    }

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }

    public BigDecimal getWeightNum() {
        return weightNum;
    }

    public void setWeightNum(BigDecimal weightNum) {
        this.weightNum = weightNum;
    }

    public Integer getAdminId() {
        return adminId;
    }

    public void setAdminId(Integer adminId) {
        this.adminId = adminId;
    }

    public Integer getCharacters() {
        return characters;
    }

    public void setCharacters(Integer characters) {
        this.characters = characters;
    }

    public String getNameRemakes() {
        return nameRemakes;
    }

    public void setNameRemakes(String nameRemakes) {
        this.nameRemakes = nameRemakes;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getStorageArea() {
        return this.storageArea;
    }

    public void setStorageArea(String storageArea) {
        this.storageArea = storageArea;
    }

    public String getPacking() {
        return this.packing;
    }

    public void setPacking(String packing) {
        this.packing = packing;
    }

    public String getSkuPic() {
        return skuPic;
    }

    public void setSkuPic(String skuPic) {
        this.skuPic = skuPic;
    }

    public String getSpuPic() {
        return spuPic;
    }

    public void setSpuPic(String spuPic) {
        this.spuPic = spuPic;
    }

    public Integer getCategoryType() {
        return categoryType;
    }

    public void setCategoryType(Integer categoryType) {
        this.categoryType = categoryType;
    }

    public String getPdNo() {
        return pdNo;
    }

    public void setPdNo(String pdNo) {
        this.pdNo = pdNo;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
