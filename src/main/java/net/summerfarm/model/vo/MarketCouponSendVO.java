package net.summerfarm.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MarketCouponSendVO {

    private Long id;

    /**
     * 优惠券id
     */
    private Long couponId;

    private String couponType;

    private String couponName;

    private BigDecimal money;

    private BigDecimal threshold;

    private LocalDateTime validDate;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 发送人
     */
    private String senderName;

    /**
     * 审核人
     */
    private String auditName;

    /**
     * 发放理由
     */
    private String reason;

    /**
     * 发放日期
     */
    private LocalDateTime createTime;

    /**
     * 发放状态  0-未发放 1-部分发放 2-已发放  3-已取消  4-已撤回 5-发放中（针对后端需要）
     */
    private Integer sendStatus;

    /**
     * 发放类型 0-立即发放  1-定时发放
     */
    private Integer sendType;

    /**
     * 定时发放时间
     */
    private LocalDateTime releaseTime;

    /**
     * 实际发放时间
     */
    private LocalDateTime actualReleaseTime;

}
