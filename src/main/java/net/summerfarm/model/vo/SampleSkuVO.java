package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.model.domain.SampleSku;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2022/5/26
 */
@Data
public class SampleSkuVO extends SampleSku {
    /**
     * 店铺等级
     */
    private Integer grade;
    /**
     * 店铺id
     */
    private Long mId;
    /**
     * 店铺名称
     */
    private String mname;
    /**
     * 联系方式
     */
    private String phone;
    //仓库
    private Integer areaNo;
    //城配仓
    private Integer storeNo;
    //联系人
    private Integer contactId;
    //配送时间
    private LocalDate deliveryTime;
}
