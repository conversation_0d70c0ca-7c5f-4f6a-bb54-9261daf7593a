package net.summerfarm.model.vo;

import lombok.Data;
import net.summerfarm.model.domain.ExchangeItemConfig;
import net.summerfarm.model.domain.ExchangeScopeConfig;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 换购活动范围VO
 * @date 2022/9/14 15:16
 * @Version 1.0
 */
@Data
public class ExchangeScopeConfigVO extends ExchangeScopeConfig {
    /**
     * 运营服务区域名称
     */
    private String areaName;

    /**
     * 人群包名称
     */
    private String merchantName;

    /**
     * sku配置
     */
    private List<ExchangeItemConfig> exchangeItemConfigList;
}
