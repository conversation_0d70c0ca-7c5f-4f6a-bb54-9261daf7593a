package net.summerfarm.model.vo;

import cn.hutool.core.collection.CollectionUtil;
import java.util.Comparator;
import java.util.Objects;
import lombok.Data;
import net.summerfarm.common.util.validation.annotation.InRange;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.ProductCategoryTypeEnum;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.purchase.SaasSku;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 * @Package: net.summerfarm.model.vo
 * @Description: sku展示对象
 * @author: <EMAIL>
 * @Date: 2016/7/22
 */
@Data
public class InventoryVO extends Inventory implements SaasSku, Serializable {

    private String skuCode;

    private String productName;

    private Boolean share;

    private Integer saleCount;

    private Integer alertInventory;

    private Integer safeInventory;

    private Boolean onSale;

    private Boolean show;

    private Integer priority;

    private String picturePath;

    private String variety;     //品种

    @InRange(rangeNums = {0,1,2,3,4})  //0：未分类，1：冷冻，2：冷藏，3：恒温，4：顶汇大流通
    private Integer storageLocation;

    private String ladderPrice;

    private String pdNo;

    private List<AreaSkuVO> areaSkuVOS;

    private List<AreaPurchaseInfoVO> areaPurchaseInfoVOS;

    private Integer mType;

    private Integer quantity;

    private Integer stockNum;

    private Integer areaNo;

    private Integer storeNo;

    private Integer type;

    private String pdName;

    private Integer categoryId;

    private Integer parentCategoryId;

    private List<Integer> categoryIdList;

    private List<Integer> parentCategoryIdList;

    private Integer categoryType;

    private String categoryName;
    /**
    * 大客户名称备注
    */
    private String nameRemakes;

    private List<TrustStoreVO> trustStoreVOS;

    /**
     * 城市仓的托管中心仓编号
     */
    private Integer trustStoreNo;

    private Integer pdPriority;

    private List<ProductsPropertyValueVO> saleValueList;

    private Integer warnTime;

    private String realName;

    private String spuCreateRemark;

    /**
     * 上新状态：0、待审核 1、上新完成 2、上新失败
     */
    private Integer createStatus;

    /**
     * 保质期时长
     */
    private Integer qualityTime;

    /**
     * 保质期时长单位
     */
    private String qualityTimeUnit;

    /**
     * 储存区域
     */
    private String storageMethod;

    /**
     * 当前sku在所有城市的最高售价
     */
    private BigDecimal maxPrice;

    /**
     * 当前sku在所有城市的最低售价
     */
    private BigDecimal minPrice;

    /**
     * 销量
     */
    private Integer saleQuantity;

    /**
     * 调出仓销量
     */
    private Integer outSaleQuantity;

    /**
     * 调入仓销量
     */
    private Integer inSaleQuantity;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 调出可用库存
     */
    private Integer outAvailableQuantity;

    /**
     * 调入可用库存
     */
    private Integer inAvailableQuantity;

    /**
     * 绑定SKU
     */
    private String bindSku;

    /**
     * 0、不展示平均价 1、展示平均价
     */
    private Integer averagePriceFlag;

    /**
     * 外部申请id
     */
    private Long outerApplicationItemId;

    /**
     * sku标签
     */
    private List<ProductLabelValueVo> productLabelValueVos;


    /** saasSkuId **/
    private Long saasSkuId;

    /**
     * saas custom sku code
     */
    private String saasCustomSkuCode;

    /**
     * 保质期类型
     */
    private Integer qualityTimeType;
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 特价金额
     */
    private BigDecimal activityPrice;

    /**
     * 销售属性，和商城一致
     */
    private List<ProductsPropertyValueVO> keyValueList;

    /**
     * 同商城搜索页商品名称展示一致
     */
    private String mallSkuName;

    /**
     * 同商城搜索页商品图片展示一致
     */
    private String mallSkuPic;

    /**
     * 详情图
     */
    private String detailPicture;

    public InventoryVO sortKeyValueList() {
        if (CollectionUtil.isNotEmpty(keyValueList)) {
            //属性排序处理
            if (Objects.equals(categoryType, ProductCategoryTypeEnum.FRUIT.getCode())) {
                keyValueList.sort(
                        Comparator.comparing(ProductsPropertyValueVO::getName, Comparator.comparing(
                                Global.FRUIT_PROPERTY_SORT::indexOf)));
            } else {
                keyValueList.sort(Comparator.comparing(ProductsPropertyValueVO::getName,
                        Comparator.comparing(Global.NON_FRUIT_PROPERTY_SORT::indexOf)));
            }
        }
        return this;
    }

    /**
     * 优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
     * @return
     */
    public InventoryVO resetSkuName() {
        if (StringUtils.isBlank(getSkuName())) {
            mallSkuName = productName + " " + getWeight();
        } else {
            mallSkuName = getSkuName();
        }
        return this;
    }

    /**
     * 优先取SKU头图，如果为空，则兜底为SPU橱窗图
     * @return
     */
    public InventoryVO resetSkuPic() {
        if (StringUtils.isBlank(getSkuPic())) {
            mallSkuPic = picturePath;
        } else {
            mallSkuPic = getSkuPic();
        }
        return this;
    }

    /**
     * 优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
     * && 优先取SKU头图，如果为空，则兜底为SPU橱窗图
     * @return
     */
    public InventoryVO resetSkuNameAndSkuPic() {
        resetSkuName();
        resetSkuPic();
        return this;
    }
}
