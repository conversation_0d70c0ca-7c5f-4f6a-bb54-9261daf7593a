package net.summerfarm.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * Description: <br/>
 * date: 2022/5/11 16:23<br/>
 *
 * <AUTHOR> />
 */
@Data
public class GetAfterDataVO implements Serializable {
    private Integer id;
    private Integer afterSaleDeliveryDetailId;

    //类型 0 配送 1 回收
    private Integer type;

    private String sku;

    private Integer quantity;

    private String afterSaleNo;

    //仓库
    private Integer areaNo;
    //城配仓
    private Integer storeNo;
    //联系人
    private Integer contactId;
    //配送时间
    private LocalDate deliveryTime;
    //售后处理方式,0返券，1补发，2退款
    private Integer handleType;
    //订单号
    private String orderNo;
}
