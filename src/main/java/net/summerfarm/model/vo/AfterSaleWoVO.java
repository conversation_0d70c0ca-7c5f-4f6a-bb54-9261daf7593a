package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.model.domain.AfterSaleWo;
import net.summerfarm.model.domain.AfterSaleWoProcess;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-07-29
 * @description
 */
@ApiModel(description = "售后工单")
@Data
public class AfterSaleWoVO extends AfterSaleWo {
    @ApiModelProperty(value = "工单跟进记录")
    private List<AfterSaleWoProcess> processList;
}
