package net.summerfarm.model.vo;

import com.google.common.collect.Lists;
import lombok.Data;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliveryOrderDTO;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.enums.DeliveryOrderStatusEnum;
import net.summerfarm.tms.enums.DistOrderStatusEnum;

import java.time.LocalDateTime;


/**
 * Description:配送详情响应类
 * date: 2022/8/29 18:35
 *
 * <AUTHOR>
 */
@Data
public class DeliveryDetailVO {

    private Integer deliveryPlanId;
    /**
     * 配送类型，0:配送，1：自提
     */
    private Integer deliveryType;
    /**
     * 配送状态，0：待检货，1：配送中，2：配送完成
     */
    private Integer pathStatus;
    private LocalDateTime finishTime;
    /**
     * 0:正常，1：异常
     */
    private Integer signForStatus;
    private String deliveryPic;
    private Integer storeNo;
    private String storeName;
    private String driver;
    private String driverPhone;
    /**
     * 拦截状态,0:正常,1:被拦截
     */
    private Integer interceptFlag;

    public DistOrderDTO convertDTO(){
        DistOrderDTO distOrderDTO = new DistOrderDTO();
        distOrderDTO.setDistId(this.deliveryPlanId.longValue());
        if (this.pathStatus == null){
            distOrderDTO.setStatus(DistOrderStatusEnum.TO_BE_WIRED.getCode());
            distOrderDTO.setStatusDesc(DistOrderStatusEnum.TO_BE_WIRED.getName());
        }else if (this.pathStatus == 0){
            distOrderDTO.setStatus(DistOrderStatusEnum.TO_BE_PICKED.getCode());
            distOrderDTO.setStatusDesc(DistOrderStatusEnum.TO_BE_PICKED.getName());
        }else if (this.pathStatus == 1){
            distOrderDTO.setStatus(DistOrderStatusEnum.IN_DELIVERY.getCode());
            distOrderDTO.setStatusDesc(DistOrderStatusEnum.IN_DELIVERY.getName());
        }else {
            distOrderDTO.setStatus(DistOrderStatusEnum.COMPLETE_DELIVERY.getCode());
            distOrderDTO.setStatusDesc(DistOrderStatusEnum.COMPLETE_DELIVERY.getName());
        }
        distOrderDTO.setBeginSiteName(this.storeName);
        distOrderDTO.setOutBusinessNo(String.valueOf(this.storeNo));
        distOrderDTO.setRealArrivalTime(this.finishTime);

        DeliveryBatchDTO deliveryBatchDTO = new DeliveryBatchDTO();
        deliveryBatchDTO.setDriver(this.driver);
        deliveryBatchDTO.setDriverPhone(this.driverPhone);
        distOrderDTO.setDeliveryBatchList(Lists.newArrayList(deliveryBatchDTO));

        DeliveryOrderDTO deliveryOrderDTO = new DeliveryOrderDTO();
        if (this.signForStatus == null){
            deliveryOrderDTO.setStatus(DeliveryOrderStatusEnum.NO_SIGN.getCode());
            deliveryOrderDTO.setStatusDesc(DeliveryOrderStatusEnum.NO_SIGN.getName());
        }else if (this.signForStatus == 0){
            deliveryOrderDTO.setStatus(DeliveryOrderStatusEnum.SIGN_SUC.getCode());
            deliveryOrderDTO.setStatusDesc(DeliveryOrderStatusEnum.SIGN_SUC.getName());
        }else {
            deliveryOrderDTO.setStatus(DeliveryOrderStatusEnum.SIGN_ERROR.getCode());
            deliveryOrderDTO.setStatusDesc(DeliveryOrderStatusEnum.SIGN_ERROR.getName());
        }
        deliveryOrderDTO.setSignInPic(this.deliveryPic);
        distOrderDTO.setDeliveryOrderList(Lists.newArrayList(deliveryOrderDTO));
        return distOrderDTO;
    }
}
