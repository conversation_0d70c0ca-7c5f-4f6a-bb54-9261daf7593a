package net.summerfarm.model.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import net.summerfarm.model.domain.SupplierAccount;
import net.summerfarm.model.domain.SupplierConnect;
import net.summerfarm.model.domain.SupplierRelatedFile;
import net.summerfarm.model.input.SupplierFileReq;

import javax.validation.constraints.NotNull;
import java.util.List;


@ApiModel(description = "SAAS供应商新增")
@Data
public class SaasSupplierDetailVO {

    private Integer id;

    private String supplierName;

    private Integer supplierType;

    private String taxNum;

    private String categoryArray;

    private String remark;

    private List<SupplierConnect> connectList;
    private List<SupplierFileReq> fileList;
    private List<SupplierAccount> accountList;


}
