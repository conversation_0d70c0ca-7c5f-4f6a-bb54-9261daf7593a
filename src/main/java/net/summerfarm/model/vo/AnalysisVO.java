package net.summerfarm.model.vo;

import java.io.Serializable;

/**
 * @Package: net.summerfarm.model.vo
 * @Description: 接口分析VO
 * @author: <EMAIL>
 * @Date: 2017/1/20
 */
public class AnalysisVO implements Serializable {

    private String date;

    private String actionName;

    private int pv;

    private int uv;


    public AnalysisVO() {
    }

    public AnalysisVO(String date, String actionName, int pv, int uv) {
        this.date = date;
        this.actionName = actionName;
        this.pv = pv;
        this.uv = uv;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public int getPv() {
        return pv;
    }

    public void setPv(int pv) {
        this.pv = pv;
    }

    public int getUv() {
        return uv;
    }

    public void setUv(int uv) {
        this.uv = uv;
    }
}
