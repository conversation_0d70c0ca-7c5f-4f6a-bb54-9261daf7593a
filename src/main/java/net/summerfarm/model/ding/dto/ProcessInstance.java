package net.summerfarm.model.ding.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProcessInstance implements Serializable {

    private static final long serialVersionUID = -2730972678171196698L;

    @JSONField(name = "call_back_tag")
    String callBackTag;

    @JSONField(name = "event_time")
    Long eventTime;

    @JSONField(name = "bpms_instance_change")
    BpmsInstanceChange bpmsInstanceChange;

    @JSONField(name = "bpms_task_change")
    BpmsTaskChange bpmsTaskChange;
}
