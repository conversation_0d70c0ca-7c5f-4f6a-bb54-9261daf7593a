package net.summerfarm.model.ding.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BpmsInstanceChange implements Serializable {
    private static final long serialVersionUID = -8914721772842542854L;

    @JSONField(name = "corpid")
    String corpId;

    BpmsCallBackData bpmsCallBackData;
}
