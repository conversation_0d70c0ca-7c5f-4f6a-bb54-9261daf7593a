package net.summerfarm.model.converter;

import net.summerfarm.model.DTO.market.malltag.MallTagInfoInput;
import net.summerfarm.model.DTO.market.malltag.MallTagPageRespDTO;
import net.summerfarm.model.domain.market.TagLaunchInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @author: <EMAIL>
 * @create: 2023/4/24
 */
@Mapper
public interface TagLaunchConverter {

    TagLaunchConverter INSTANCE = Mappers.getMapper(TagLaunchConverter.class);

    /**
     * 标签基础信息dto转换成info
     *
     * @param infoDTO
     * @return
     */
    TagLaunchInfo dtoToInfo(MallTagInfoInput infoDTO);

    /**
     * 标签基础信息info转换成dto
     * @param tagLaunchInfo
     * @return
     */
    MallTagInfoInput infoToDTO(TagLaunchInfo tagLaunchInfo);

    /**
     * 标签基础信息info转换成respDTO
     * @param launchInfo
     * @return
     */
    MallTagPageRespDTO infoToRespDTO(TagLaunchInfo launchInfo);

}
