package net.summerfarm.model.converter.market;

import net.summerfarm.model.domain.Banner;
import net.summerfarm.model.vo.BannerVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @author: <EMAIL>
 * @create: 2023/6/7
 */
@Mapper
public interface BannerConverter {

    BannerConverter INSTANCE = Mappers.getMapper(BannerConverter.class);

    /**
     * BannerVO转Banner
     *
     * @param bannerVO
     * @return
     */
    Banner voToBanner(BannerVO bannerVO);

}
