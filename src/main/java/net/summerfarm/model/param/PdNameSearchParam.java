package net.summerfarm.model.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/** 商品搜索入参 **/
@Data
public class PdNameSearchParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 商品名称 **/
    private String pdName;
    /** 货品创建类型 **/
    @NotEmpty
    private List<Integer> createTypeList;

    private Integer pageIndex = 1;
    private Integer pageSize = 20;
}
