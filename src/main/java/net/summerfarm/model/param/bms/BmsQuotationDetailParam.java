package net.summerfarm.model.param.bms;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2022/8/19
 */
@Data
public class BmsQuotationDetailParam {

    private Integer id;

    /**
     * 报价清单名称
     */
    @NotNull
    private String quoteName;
    /**
     * 类型 0系统报价 1自定义报价
     */
    @NotNull
    private Integer type;

    private String unit;

    /**
     * 来源类型 0报价详情 1 计算模型
     */
    @NotNull
    private Integer sourceType;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
