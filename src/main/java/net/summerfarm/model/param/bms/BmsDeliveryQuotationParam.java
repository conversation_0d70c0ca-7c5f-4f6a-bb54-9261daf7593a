package net.summerfarm.model.param.bms;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.model.domain.CarrierQuotationArea;
import net.summerfarm.model.domain.bms.BmsDeliveryQuotationDetail;
import net.summerfarm.model.vo.bms.BmsDeliveryQuoteCalculateCostVO;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/8/18
 */
@Data
public class BmsDeliveryQuotationParam {

    /**
     * 报价单id
     */
    private Integer id;

    /**
     * 承运商id
     */
    @NotNull(groups = {Add.class}, message = "承运商id不能为空")
    private Integer carrierId;

    /**
     * 承运商名称
     */
    private String carrierName;

    /**
     * 服务区域
     */
    @NotNull(groups = {Add.class}, message = "服务区域不能为空")
    private String serviceArea;

    /**
     * 服务区域id
     */
    private Integer serviceAreaId;

    /**
     * 服务城配仓
     */
//    @NotNull(groups = {Add.class}, message = "服务城配仓不能为空")
    private Integer storeNo;

    /**
     * 城配仓
     */
    private String storeName;

    /**
     * 报价类型 0内区 1外区
     */
//    @NotNull(groups = {Add.class}, message = "报价类型不能为空")
    private Integer quotaType;

    /**
     * 服务区域信息
     */
    List<CarrierQuotationArea> quotationAreas;

    /**
     * 状态-1 作废 0正常
     */
    private Integer status;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新人id
     */
    private Integer lastUpdaterId;

    /**
     * 报价详情
     */
//    @NotNull
    private List<BmsDeliveryQuotationDetail> quotationDetails;

    /**
     * 计费模型
     */
//    @NotNull
    private List<BmsDeliveryQuoteCalculateCostVO> calculateCosts;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 报价形式0择优报价，1组合报价
     */
//    @NotNull
    private Integer quotaForm;

    private String businessType;
}
