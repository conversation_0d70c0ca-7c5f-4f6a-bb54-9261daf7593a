package net.summerfarm.model.param.srm;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SrmSupplierOfferDetailStepPriceParam implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 报价单详情id
     */
    private Long offerDetailId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 价格
     */
    private BigDecimal price;

}
