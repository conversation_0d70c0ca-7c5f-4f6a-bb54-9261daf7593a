package net.summerfarm.model.param.srm;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class SrmSupplierSellOrderOperateParam {
    /**
     * 操作类型，0确认，1拒绝
     */
    @NotNull(message = "操作类型不能为空！")
    private Integer operate;
    /**
     * 采购单号
     */
    @NotBlank(message = "采购单号不能为空！")
    private String purchaseNo;
    /**
     * 操作人id
     */
    @NotNull(message = "操作人id不能为空！")
    private Integer operatorId;
    /**
     * 操作人类型0供应商1采购员
     */
    @NotNull(message = "操作人类型不能为空！")
    private Integer operatorType;
    /**
     * 理由
     */
    private String reason;
}
