package net.summerfarm.model.builder.coupon;

import net.summerfarm.enums.coupon.CouponSenderSetupStatusEnum;
import net.summerfarm.model.DTO.coupon.CouponSenderSetupListDTO;
import net.summerfarm.model.domain.CouponSenderSetup;

/**
 * CouponSenderSetupListDTO构造器
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021/11/23
 */
public class CouponSenderSetupListDTOBuilder {

    /**
     * 基于CouponSenderSetup构造CouponSenderSetupListDTO对象
     * @param couponSenderSetup 源数据对象
     * @return CouponSenderSetupListDTO对象
     */
    public static CouponSenderSetupListDTO build(CouponSenderSetup couponSenderSetup){
        CouponSenderSetupListDTO couponSenderSetupListDTO = new CouponSenderSetupListDTO();
        couponSenderSetupListDTO.setId(couponSenderSetup.getId());
        couponSenderSetupListDTO.setName(couponSenderSetup.getName());
        couponSenderSetupListDTO.setStartTime(couponSenderSetup.getStartTime());
        couponSenderSetupListDTO.setEndTime(couponSenderSetup.getEndTime());
        couponSenderSetupListDTO.setType(couponSenderSetup.getType());
        couponSenderSetupListDTO.setSenderType(couponSenderSetup.getSenderType());
        couponSenderSetupListDTO.setStatus(CouponSenderSetupStatusEnum.getViewStatus(couponSenderSetup.getStatus()));
        couponSenderSetupListDTO.setCreator(couponSenderSetup.getCreator());
        couponSenderSetupListDTO.setCreateTime(couponSenderSetup.getCreateTime());
        couponSenderSetupListDTO.setUpdater(couponSenderSetup.getUpdater());
        couponSenderSetupListDTO.setUpdateTime(couponSenderSetup.getUpdateTime());
        return couponSenderSetupListDTO;
    }
}
