package net.summerfarm.model.builder.coupon;

import lombok.Data;
import net.summerfarm.enums.coupon.CouponSenderSetupSenderTypeEnum;
import net.summerfarm.model.domain.CouponSenderSetup;
import net.summerfarm.model.input.CouponSenderSetupSaveReq;

import java.time.LocalDateTime;

import static net.summerfarm.enums.coupon.CouponSenderSetupStatusEnum.*;

/**
 * CouponSenderSetup对象构造器
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021/11/23
 */
@Data
public class CouponSenderSetupBuilder {

    /**
     * 基于CouponSenderSetupVo对象构建CouponSenderSetup对象
     * @param couponSenderSetupVo CouponSenderSetupSaveReq
     * @param adminId 操作人id
     * @return CouponSenderSetup对象
     */
    public static CouponSenderSetup build(CouponSenderSetupSaveReq couponSenderSetupVo, Integer adminId){
        CouponSenderSetup couponSenderSetup = new CouponSenderSetup();
        couponSenderSetup.setUpdater(adminId);
        couponSenderSetup.setUpdateTime(LocalDateTime.now());
        couponSenderSetup.setName(couponSenderSetupVo.getName());
        couponSenderSetup.setType(couponSenderSetupVo.getType());
        couponSenderSetup.setStartTime(couponSenderSetupVo.getStartTime());
        couponSenderSetup.setEndTime(couponSenderSetupVo.getEndTime());
        couponSenderSetup.setSenderType(couponSenderSetupVo.getSenderType());
        LocalDateTime startTime = couponSenderSetupVo.getStartTime();
        LocalDateTime endTime = couponSenderSetup.getEndTime();
        LocalDateTime nowTime = LocalDateTime.now();
        int status = NOT_IN_EFFECT.getStatus();
        if(couponSenderSetupVo.getSenderType() == CouponSenderSetupSenderTypeEnum.USER_RECEIVE.getType()){
            if(nowTime.isAfter(endTime)){
                status = INVALID_FOR_AUTO.getStatus();
            }else if(nowTime.isAfter(startTime) && nowTime.isBefore(endTime)){
                status = IN_EFFECT.getStatus();
            }
        }else{
            status = IN_EFFECT.getStatus();
        }
        couponSenderSetup.setStatus(status);
        return couponSenderSetup;
    }

}
