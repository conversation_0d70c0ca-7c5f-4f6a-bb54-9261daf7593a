package net.summerfarm.model.builder.coupon;

import lombok.Data;
import net.summerfarm.enums.CirclePeopleRelationTypeEnum;
import net.summerfarm.model.domain.CirclePeopleRelation;

import java.time.LocalDateTime;

/**
 * CirclePeopleRelation构造器
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021/11/23
 */
@Data
public class CirclePeopleRelationBuilder {

    public static CirclePeopleRelation build(Integer typeId, Integer ruleId,
                                             Integer adminId){
        CirclePeopleRelation circlePeopleRelation = new CirclePeopleRelation();
        circlePeopleRelation.setType(CirclePeopleRelationTypeEnum.COUPON_TYPE.getType());
        circlePeopleRelation.setTypeId(typeId);
        circlePeopleRelation.setRuleId(ruleId);
        circlePeopleRelation.setCreator(adminId);
        circlePeopleRelation.setCreateTime(LocalDateTime.now());
        return circlePeopleRelation;
    }

}
