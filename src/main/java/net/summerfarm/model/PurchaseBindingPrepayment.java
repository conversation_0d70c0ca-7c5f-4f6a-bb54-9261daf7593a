package net.summerfarm.model;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @description 供应商采购绑定预付款实体
 * <AUTHOR>
 * @date 2022/1/16 20:53
 */
@Data
public class PurchaseBindingPrepayment {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 预付单id
     */
    private Long purchaseAdvancedOrderId;

    /**
     * 本次预付金额
     */
    private BigDecimal advanceAmount;

    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 绑定状态
     */
    private Integer bindingStatus;

    /**
     * 发起人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 供应商id
     */
    private Integer supplierId;

    public PurchaseBindingPrepayment() {
    }

    public PurchaseBindingPrepayment(Long purchaseAdvancedOrderId, String purchaseNo, BigDecimal advanceAmount, Integer bindingStatus, String creator, Integer supplierId) {
        this.purchaseAdvancedOrderId = purchaseAdvancedOrderId;
        this.purchaseNo = purchaseNo;
        this.advanceAmount = advanceAmount;
        this.bindingStatus = bindingStatus;
        this.creator = creator;
        this.supplierId = supplierId;
    }
}
