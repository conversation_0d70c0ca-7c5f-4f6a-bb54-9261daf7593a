package net.summerfarm.model;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class GroupBuyConfigListDTO {

    /**
     * 已失效
     */
    public static final int EXPIRED = 0;

    /**
     * 生效中
     */
    public static final int EFFECTIVE = 1;

    /**
     * 待生效
     */
    public static final int WAIT_EFFECTIVE = 2;

    /**
     * 活动编号
     */
    private Long id;

    /**
     * 开放城市
     */
    private Integer areaNo;

    /**
     * 团购名称
     */
    private String groupBuyName;

    /**
     * 团购截止时间
     */
    private LocalDateTime endTime;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 状态：0、失效 1、生效中 2、待生效
     */
    private Integer status;

    /**
     * 成团量
     */
    private Integer leastNumber;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 运营区域名称
     */
    private String areaName;

    /**
     * 实际结束时间
     */
    private LocalDateTime actualEndTime;
}
