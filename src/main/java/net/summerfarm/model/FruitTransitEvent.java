package net.summerfarm.model;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 货品成本变更事件
 *
 * <AUTHOR>
 */
@Data
public class FruitTransitEvent {
    /**
     * 来源单号
     */
    private String outOrderNo;
    /**
     * 单据类型，1：采购预约
     */
    private Integer outOrderType;
    /**
     * 仓库编号
     */
    private Integer warehouseNo;
    /**
     * sku编码
     */
    private String sku;
    /**
     * 批次号，采购时为采购单号
     */
    private String batchNo;
    /**
     * 在途可售成本
     */
    private BigDecimal cost;

}
