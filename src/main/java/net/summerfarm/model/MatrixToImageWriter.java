package net.summerfarm.model;


import java.awt.image.BufferedImage;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;

import javax.imageio.ImageIO;

import java.util.Hashtable;

import com.google.zxing.common.BitMatrix;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;


/**
 * <AUTHOR>
 * @create 2022/4/27
 */
public class MatrixToImageWriter {
    private static final int BLACK = 0xFF000000;
    private static final int WHITE = 0xFFFFFFFF;

    private MatrixToImageWriter() {
    }

    public static BufferedImage toBufferedImage(BitMatrix matrix) {
        int width = matrix.getWidth();
        int height = matrix.getHeight();
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height - 50; y++) {
                image.setRGB(x, y, matrix.get(x, y + 50) ? BLACK : WHITE);
            }
        }
        return image;
    }


    public static BufferedImage writeToFile(BitMatrix matrix, String format)
            throws IOException {
        BufferedImage image = toBufferedImage(matrix);
        return image;
    }


    public static void writeToStream(BitMatrix matrix, String format, OutputStream stream)
            throws IOException {
        BufferedImage image = toBufferedImage(matrix);
        if (!ImageIO.write(image, format, stream)) {
            throw new IOException("Could not write an image of format " + format);
        }
    }


//    public static void main(String[] args) throws Exception {
//        String text = "http://www.baidu.com"; // 二维码内容
//        int width = 300; // 二维码图片宽度
//        int height = 300; // 二维码图片高度
//        String format = "jpg";// 二维码的图片格式
//
//        Hashtable<EncodeHintType, String> hints = new Hashtable<EncodeHintType, String>();
//        hints.put(EncodeHintType.CHARACTER_SET, "utf-8"); // 内容所使用字符集编码
//
//        BitMatrix bitMatrix = new MultiFormatWriter().encode(text,
//                BarcodeFormat.QR_CODE, width, height, hints);
//        // 生成二维码
//        File outputFile = new File("d:" + File.separator + "new.jpg");
//        MatrixToImageWriter.writeToFile(bitMatrix, format, outputFile);
//    }

}
