package net.summerfarm.provider.impl;

import com.cosfo.summerfarm.model.SummerfarmResult;
import com.cosfo.summerfarm.model.dto.*;
import com.cosfo.summerfarm.model.dto.supplyprice.SummerfarmSkuMallPriceDTO;
import com.cosfo.summerfarm.model.dto.supplyprice.SummerfarmSkuPriceInfoDTO;
import com.cosfo.summerfarm.model.dto.supplyprice.SummerfarmSkuSupplyStatusDTO;
import com.cosfo.summerfarm.model.input.*;
import com.cosfo.summerfarm.model.input.supplyprice.SummerfarmSkuMallPriceInput;
import com.cosfo.summerfarm.model.input.supplyprice.SummerfarmSkuPriceInfoInput;
import com.cosfo.summerfarm.model.input.supplyprice.SummerfarmSkuSupplyStatusInput;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.biz.finance.util.ExceptionUtil;
import net.summerfarm.enums.ProductsEnum;
import net.summerfarm.provider.converter.SummerFarmOrderConverter;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.SortByEnum;
import net.summerfarm.manage.client.saas.SaasInterfaceServiceProvider;
import net.summerfarm.manage.client.saas.dto.MinShelfLifePurchaseBatchDTO;
import net.summerfarm.manage.client.saas.dto.PurchaseBatchInventoryDTO;
import net.summerfarm.manage.client.saas.dto.SkuQuantityChangeRecordDTO;
import net.summerfarm.manage.client.saas.req.*;
import net.summerfarm.manage.client.saas.resp.*;
import net.summerfarm.manage.client.saas.req.BatchQueryMinShelfLifePurchaseBatchReq.PurchaseBatchQueryDTO;
import net.summerfarm.model.DTO.CategoryTaxRateDTO;
import net.summerfarm.model.ShelfLifePurchaseBatch;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.StoreRecord;
import net.summerfarm.model.input.LastStoreRecordPageQuery;
import net.summerfarm.model.input.QuantityChangeRecordPageQuery;
import net.summerfarm.model.input.StockChangeRecordQuery;
import net.summerfarm.model.vo.QuantityChangeRecordVO;
import net.summerfarm.model.vo.StoreRecordVO;
import net.summerfarm.provider.converter.InventoryConvert;
import net.summerfarm.provider.converter.SkuQuantityChangeRecordConvert;
import net.summerfarm.provider.converter.StoreRecordConvert;
import net.summerfarm.service.InventoryService;
import net.summerfarm.service.QuantityRecordService;
import net.summerfarm.service.StoreRecordService;
import net.summerfarm.service.TaxRateService;
import net.summerfarm.service.saas.OutSideProductService;
import net.summerfarm.service.saas.OutsideOrderService;
import net.summerfarm.warehouse.model.vo.WarehouseStorageCenterVO;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import net.summerfarm.model.DTO.CategoryDTO;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/22
 */
@DubboService
@Component
@Slf4j
public class SaasInterfaceServiceProviderImpl implements SaasInterfaceServiceProvider {
    @Resource
    private OutsideOrderService outsideOrderService;
    @Resource
    private OutSideProductService outSideProductService;
    @Resource
    private WarehouseStorageService storageService;
    @Resource
    private StoreRecordService storeRecordService;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private QuantityRecordService quantityRecordService;
    @Resource
    private TaxRateService taxRateService;

    @Override
    public DubboResponse<LocalDate> queryDeliveryTime(SummerFarmDeliveryReq data) {
        SummerfarmDeliveryInput input = new SummerfarmDeliveryInput();
        BeanUtils.copyProperties(data, input);
        SummerfarmResult summerfarmResult = outsideOrderService.queryDeliveryTime(input);
        if(summerfarmResult.isSuccess()){
            LocalDate deliveryTime = (LocalDate) summerfarmResult.getData();
            return DubboResponse.getOK(deliveryTime);
        }else {
            return DubboResponse.getError(summerfarmResult.getCode(), summerfarmResult.getMsg());
        }
    }

    @Override
    public DubboResponse<List<SummerFarmStockResp>> queryAreaStoreQuantityList(SummerFarmStockReq skuData) {
        SummerfarmStockInput summerfarmStockInput = new SummerfarmStockInput();
        BeanUtils.copyProperties(skuData, summerfarmStockInput);
        summerfarmStockInput.setSkuIdList(skuData.getSkuIdList());
        SummerfarmResult summerfarmResult = outsideOrderService.queryAreaStoreQuantityList(summerfarmStockInput);
        if(summerfarmResult.isSuccess()){
            List<SummerfarmStockDTO> summerfarmResultData = (List<SummerfarmStockDTO>) summerfarmResult.getData();
            List<SummerFarmStockResp> summerFarmStockResps = new ArrayList<>();
            if(!CollectionUtils.isEmpty(summerfarmResultData)){
                summerFarmStockResps = summerfarmResultData.stream().map(summerfarmStockDto -> {
                    SummerFarmStockResp summerFarmStockResp = new SummerFarmStockResp();
                    BeanUtils.copyProperties(summerfarmStockDto, summerFarmStockResp);
                    return summerFarmStockResp;
                }).collect(Collectors.toList());
            }
            return DubboResponse.getOK(summerFarmStockResps);
        }else {
            return DubboResponse.getError(summerfarmResult.getCode(), summerfarmResult.getMsg());
        }
    }

    @Override
    public DubboResponse<List<SummerFarmSkuResp>> getSkuInfo(SummerFarmSkuReq input) {
        SummerfarmSkuInput summerfarmSkuInput = new SummerfarmSkuInput();
        BeanUtils.copyProperties(input, summerfarmSkuInput);
        SummerfarmResult summerfarmResult = outsideOrderService.getSkuInfo(summerfarmSkuInput);
        if(summerfarmResult.isSuccess()){
            List<SummerfarmSkuDTO> summerfarmSkuDtos = (List<SummerfarmSkuDTO>) summerfarmResult.getData();
            List<SummerFarmSkuResp> list= new ArrayList<>();
            if(!CollectionUtils.isEmpty(summerfarmSkuDtos)){
                list = summerfarmSkuDtos.stream().map(summerfarmSkuDto -> {
                    SummerFarmSkuResp summerFarmSkuResp = new SummerFarmSkuResp();
                    BeanUtils.copyProperties(summerfarmSkuDto, summerFarmSkuResp);
                    return summerFarmSkuResp;
                }).collect(Collectors.toList());
            }
            return DubboResponse.getOK(list);
        }else {
            return DubboResponse.getError(summerfarmResult.getCode(), summerfarmResult.getMsg());
        }
    }

    @Override
    public DubboResponse<SummerFarmSynchronizedSkuResp> queryNeedSynchronizedSku(SummerFarmSynchronizedSkuReq input) {
        SummerfarmSkuInput summerfarmSkuInput = new SummerfarmSkuInput();
        BeanUtils.copyProperties(input, summerfarmSkuInput);
        SummerfarmResult summerfarmResult = outsideOrderService.queryNeedSynchronizedSku(summerfarmSkuInput);
        SummerFarmSynchronizedSkuResp summerFarmSynchronizedSkuResp = new SummerFarmSynchronizedSkuResp();
        if(summerfarmResult.isSuccess()){
            SummerfarmSynchronizedSkuDTO summerfarmSynchronizedSkuDto  = (SummerfarmSynchronizedSkuDTO) summerfarmResult.getData();
            BeanUtils.copyProperties(summerfarmSynchronizedSkuDto,summerFarmSynchronizedSkuResp);
            return DubboResponse.getOK(summerFarmSynchronizedSkuResp);
        }else {
            return DubboResponse.getError(summerfarmResult.getCode(), summerfarmResult.getMsg());
        }
    }

    @Override
    public DubboResponse<List<SummerFarmSkuPriceInfoResp>> queryAdminSkuPricingInfo(List<SummerFarmSkuPriceInfoReq> summerFarmSkuPriceInfoReqs) {
        List<SummerFarmSkuPriceInfoResp> resultList = new ArrayList<>();
        if(CollectionUtils.isEmpty(summerFarmSkuPriceInfoReqs)){
            return DubboResponse.getOK(resultList);
        }

        List<SummerfarmSkuPriceInfoInput> summerfarmSkuPriceInfoInputs = summerFarmSkuPriceInfoReqs.stream().map(summerFarmSkuPriceInfoReq -> {
            SummerfarmSkuPriceInfoInput summerfarmSkuPriceInfoInput = new SummerfarmSkuPriceInfoInput();
            BeanUtils.copyProperties(summerFarmSkuPriceInfoReq, summerfarmSkuPriceInfoInput);
            return summerfarmSkuPriceInfoInput;
        }).collect(Collectors.toList());
        SummerfarmResult summerfarmResult = outsideOrderService.queryAdminSkuPricingInfo(summerfarmSkuPriceInfoInputs);
        List<SummerFarmSkuPriceInfoResp> summerFarmSkuPriceInfoResps = new ArrayList<>();
        if(summerfarmResult.isSuccess()){
            List<SummerfarmSkuPriceInfoDTO> summerfarmResultData = (List<SummerfarmSkuPriceInfoDTO>) summerfarmResult.getData();
            summerFarmSkuPriceInfoResps = summerfarmResultData.stream().map(summerfarmSkuPriceInfoDto -> {
                SummerFarmSkuPriceInfoResp summerFarmSkuPriceInfoResp = new SummerFarmSkuPriceInfoResp();
                BeanUtils.copyProperties(summerfarmSkuPriceInfoDto, summerFarmSkuPriceInfoResp);
                return summerFarmSkuPriceInfoResp;
            }).collect(Collectors.toList());
            return DubboResponse.getOK(summerFarmSkuPriceInfoResps);
        }else {
            return DubboResponse.getError(summerfarmResult.getCode(), summerfarmResult.getMsg());
        }
    }

    @Override
    public DubboResponse<List<SummerFarmSkuMallPriceResp>> querySkuMallPrice(SummerFarmSkuMallPriceReq input) {
        SummerfarmSkuMallPriceInput summerfarmSkuMallPriceInput = new SummerfarmSkuMallPriceInput();
        BeanUtils.copyProperties(input, summerfarmSkuMallPriceInput);
        SummerfarmResult summerfarmResult = outsideOrderService.querySkuMallPrice(summerfarmSkuMallPriceInput);
        List<SummerFarmSkuMallPriceResp> results = new ArrayList<>();
        if(summerfarmResult.isSuccess()){
            List<SummerfarmSkuMallPriceDTO> summerfarmSkuMallPriceDtos = (List<SummerfarmSkuMallPriceDTO>) summerfarmResult.getData();
            results = summerfarmSkuMallPriceDtos.stream().map(summerfarmSkuMallPriceDto -> {
                SummerFarmSkuMallPriceResp summerFarmSkuMallPriceResp = new SummerFarmSkuMallPriceResp();
                BeanUtils.copyProperties(summerfarmSkuMallPriceDto, summerFarmSkuMallPriceResp);
                return summerFarmSkuMallPriceResp;
            }).collect(Collectors.toList());
            return DubboResponse.getOK(results);
        }else {
            return DubboResponse.getError(summerfarmResult.getCode(), summerfarmResult.getMsg());
        }
    }

    @Override
    public DubboResponse<List<SummerFarmOrderWarehouseAddressResp>> queryOrderItemWarehouseAddress(SummerFarmOrderWarehouseAddressReq input) {
        SummerfarmOrderInput summerfarmOrderInput = new SummerfarmOrderInput();
        BeanUtils.copyProperties(input, summerfarmOrderInput);
        SummerfarmResult summerfarmResult = outsideOrderService.queryOrderItemWarehouseAddress(summerfarmOrderInput);
        List<SummerFarmOrderWarehouseAddressResp> summerFarmOrderWarehouseAddressResps = new ArrayList<>();
        if(summerfarmResult.isSuccess()){
            List<SummerfarmSelfOrderDTO> summerfarmSelfOrderDtos = (List<SummerfarmSelfOrderDTO>) summerfarmResult.getData();
            summerFarmOrderWarehouseAddressResps = summerfarmSelfOrderDtos.stream().map(summerfarmSelfOrderDto -> {
                SummerFarmOrderWarehouseAddressResp summerFarmOrderWarehouseAddressResp = new SummerFarmOrderWarehouseAddressResp();
                BeanUtils.copyProperties(summerfarmSelfOrderDto, summerFarmOrderWarehouseAddressResp);
                return summerFarmOrderWarehouseAddressResp;
            }).collect(Collectors.toList());
            return DubboResponse.getOK(summerFarmOrderWarehouseAddressResps);
        }else {
            return DubboResponse.getError(summerfarmResult.getCode(), summerfarmResult.getMsg());
        }
    }

    @Override
    public DubboResponse<List<SummerFarmSkuSupplyStatusResp>> queryCitySupplyStatus(List<SummerFarmSkuSupplyStatusReq> input) {
        log.info("接口迁移wms后,记录调用 SaasInterfaceServiceProviderImpl-queryCitySupplyStatus");
        if(CollectionUtils.isEmpty(input)){
            return DubboResponse.getOK(new ArrayList<>());
        }

        List<SummerfarmSkuSupplyStatusInput> summerfarmSkuSupplyStatusInputs = input.stream().map(summerFarmSkuSupplyStatusReq -> {
            SummerfarmSkuSupplyStatusInput summerfarmSkuSupplyStatusInput = new SummerfarmSkuSupplyStatusInput();
            BeanUtils.copyProperties(summerFarmSkuSupplyStatusReq, summerfarmSkuSupplyStatusInput);
            return summerfarmSkuSupplyStatusInput;
        }).collect(Collectors.toList());
        SummerfarmResult summerfarmResult = outsideOrderService.queryCitySupplyStatus(summerfarmSkuSupplyStatusInputs);
        List<SummerFarmSkuSupplyStatusResp> resultList = new ArrayList<>();
        if(summerfarmResult.isSuccess()){
            List<SummerfarmSkuSupplyStatusDTO> summerfarmResultData = (List<SummerfarmSkuSupplyStatusDTO>) summerfarmResult.getData();
            resultList = summerfarmResultData.stream().map(summerfarmSkuSupplyStatusDto -> {
                SummerFarmSkuSupplyStatusResp summerFarmSkuSupplyStatusResp = new SummerFarmSkuSupplyStatusResp();
                BeanUtils.copyProperties(summerfarmSkuSupplyStatusDto, summerFarmSkuSupplyStatusResp);
                return summerFarmSkuSupplyStatusResp;
            }).collect(Collectors.toList());
            return DubboResponse.getOK(resultList);
        }else {
            return DubboResponse.getError(summerfarmResult.getCode(), summerfarmResult.getMsg());
        }
    }

    @Override
    public DubboResponse<PageInfo<SummerFarmAgentSkuWarehouseDataResp>> queryAreaStoreQuantity(SummerFarmAgentSkuWarehouseDataReq dataInput) {
        log.info("接口迁移wms后,记录调用 SaasInterfaceServiceProviderImpl-queryAreaStoreQuantity");
        SummerfarmAgentSkuWarehouseDataInput summerfarmAgentSkuWarehouseDataInput = new SummerfarmAgentSkuWarehouseDataInput();
        BeanUtils.copyProperties(dataInput, summerfarmAgentSkuWarehouseDataInput);
        SummerfarmResult summerfarmResult = outsideOrderService.queryAreaStoreQuantity(summerfarmAgentSkuWarehouseDataInput);
        if(summerfarmResult.isSuccess()){
            PageInfo<SummerfarmAgentSkuWarehouseDataDTO> pageInfo = (PageInfo<SummerfarmAgentSkuWarehouseDataDTO>) summerfarmResult.getData();
            List<SummerfarmAgentSkuWarehouseDataDTO> list = pageInfo.getList();
            List<SummerFarmAgentSkuWarehouseDataResp> summerFarmAgentSkuWarehouseDataResps = list.stream().map(summerfarmAgentSkuWarehouseDataDto -> {
                SummerFarmAgentSkuWarehouseDataResp summerFarmAgentSkuWarehouseDataResp = new SummerFarmAgentSkuWarehouseDataResp();
                BeanUtils.copyProperties(summerfarmAgentSkuWarehouseDataDto, summerFarmAgentSkuWarehouseDataResp);
                return summerFarmAgentSkuWarehouseDataResp;
            }).collect(Collectors.toList());
            PageInfo result = new PageInfo();
            BeanUtils.copyProperties(pageInfo, result);
            result.setList(summerFarmAgentSkuWarehouseDataResps);
            return DubboResponse.getOK(result);
        }else {
            return DubboResponse.getError(summerfarmResult.getCode(), summerfarmResult.getMsg());
        }
    }

    @Override
    public DubboResponse<Boolean> orderTurnSelf(SummerFarmOrderTurnSelfReq input) {
        log.info("接口迁移wms后,记录调用 SaasInterfaceServiceProviderImpl-orderTurnSelf");
        SummerfarmOrderInput summerfarmOrderInput = SummerFarmOrderConverter.convertSummerFarmOrderTurnSelfReq2Input(input);
        SummerfarmResult summerfarmResult = outsideOrderService.orderTurnSelf(summerfarmOrderInput);
        if(summerfarmResult.isSuccess()){
            return DubboResponse.getOK();
        }else {
            return DubboResponse.getError(summerfarmResult.getCode(), summerfarmResult.getMsg());
        }
    }

    @Override
    public DubboResponse<List<CategoryResp>> queryCategory(Set<Long> ids) {
        SummerfarmResult summerfarmResult = outsideOrderService.queryCategory(ids);
        Map<Integer, BigDecimal> taxRateMap = taxRateService.selectTaxRateByCategoryList(ids).stream().collect(Collectors.toMap(CategoryTaxRateDTO::getCategoryId, CategoryTaxRateDTO::getTaxRate, (x1, x2) -> x2));
        List<CategoryResp> resps = new ArrayList<>();
        if(summerfarmResult.isSuccess()){
            List<CategoryDTO> dtos = (List<CategoryDTO>) summerfarmResult.getData();
            resps = dtos.stream().map(dto -> {
                CategoryResp resp = new CategoryResp();
                BeanUtils.copyProperties(dto, resp);
                resp.setTaxRateValue(taxRateMap.get(resp.getId()));
                return resp;
            }).collect(Collectors.toList());
            return DubboResponse.getOK(resps);
        }else {
            return DubboResponse.getError(summerfarmResult.getCode(), summerfarmResult.getMsg());
        }
    }

    @Override
    public DubboResponse<List<SummerFarmWarehouseInfoResp>> queryWarehouseInfo() {
        List<WarehouseStorageCenterVO> warehouseStorageCenterVOS = storageService.selectAllForSummerFarm(null, 1);
        List<SummerFarmWarehouseInfoResp> summerFarmWarehouseInfoResps = new ArrayList<>();
        if(!CollectionUtils.isEmpty(warehouseStorageCenterVOS)){
            summerFarmWarehouseInfoResps = warehouseStorageCenterVOS.stream().map(item -> {
                SummerFarmWarehouseInfoResp summerFarmWarehouseInfoResp = new SummerFarmWarehouseInfoResp();
                summerFarmWarehouseInfoResp.setId(item.getWarehouseNo().longValue());
                summerFarmWarehouseInfoResp.setName(item.getWarehouseName());
                return summerFarmWarehouseInfoResp;
            }).collect(Collectors.toList());
        }

        return DubboResponse.getOK(summerFarmWarehouseInfoResps);
    }

    @Override
    public DubboResponse<PageInfo<StockChangeRecordResp>> pageQueryStockChangeRecord(StockChangeRecordQueryReq stockChangeRecordQueryReq) {
        log.info("接口迁移wms后,记录调用 SaasInterfaceServiceProviderImpl-pageQueryStockChangeRecord");
        // 参数校验
        this.paramCheck(stockChangeRecordQueryReq);
        if (CollectionUtils.isEmpty(stockChangeRecordQueryReq.getTenantSkuIdList())) {
            return DubboResponse.getOK(new PageInfo<>());
        }
        // 请求
        StockChangeRecordQuery query = StoreRecordConvert.convertStockChangeRecordQuery(stockChangeRecordQueryReq);
        PageInfo<StoreRecordVO> pageResult = storeRecordService.pageQueryStoreRecord(query);
        List<StockChangeRecordResp> resultList = pageResult.getList().stream()
                .map(StoreRecordConvert::convertStockChangeRecordResp).collect(Collectors.toList());
        // 返回
        PageInfo<StockChangeRecordResp> data = new PageInfo<>();
        data.setPageNum(pageResult.getPageNum());
        data.setPageSize(pageResult.getPageSize());
        data.setTotal(pageResult.getTotal());
        data.setList(resultList);
        return DubboResponse.getOK(data);
    }

    @Override
    public DubboResponse<PageQueryPurchaseBatchInventoryResp> pageQueryPurchaseBatchInventory(PageQueryPurchaseBatchInventoryReq request) {
        log.info("接口迁移wms后,记录调用 SaasInterfaceServiceProviderImpl-pageQueryPurchaseBatchInventory");
        // 参数校验、构建
        LastStoreRecordPageQuery pageQuery = this.checkAndBuildStoreRecordQuery(request);
        PageInfo<ShelfLifePurchaseBatch> pageInfo = storeRecordService.pageQueryLastStoreRecord(pageQuery);
        List<PurchaseBatchInventoryDTO> inventoryList = pageInfo.getList().stream()
                .filter(Objects::nonNull).map(StoreRecordConvert::convertPurchaseBatchInventoryDTO).collect(Collectors.toList());
        PageInfo<PurchaseBatchInventoryDTO> pageResult = new PageInfo<>();
        pageResult.setPageNum(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        pageResult.setTotal(pageInfo.getTotal());
        pageResult.setList(inventoryList);
        PageQueryPurchaseBatchInventoryResp resp = PageQueryPurchaseBatchInventoryResp.builder()
                .pageResult(pageResult)
                .build();
        return DubboResponse.getOK(resp);
    }

    @Override
    public DubboResponse<BatchQueryMinShelfLifePurchaseBatchResp> batchQueryMinShelfLifePurchaseBatch(BatchQueryMinShelfLifePurchaseBatchReq request) {
        log.info("接口迁移wms后,记录调用 SaasInterfaceServiceProviderImpl-batchQueryMinShelfLifePurchaseBatch");
        // 参数校验
        List<PurchaseBatchQueryDTO> queryDTOList = request.getQueryDTOList();
        if (CollectionUtils.isEmpty(queryDTOList)) {
            return DubboResponse.getOK(BatchQueryMinShelfLifePurchaseBatchResp.builder()
                            .batchDTOList(Lists.newArrayList()).build());
        }
        ExceptionUtil.checkAndThrow(queryDTOList.size() <= 50, "查询数量限制50条");
        List<Long> skuIdList = queryDTOList.stream().map(PurchaseBatchQueryDTO::getSkuId).distinct().collect(Collectors.toList());
        List<Integer> warehouseNoList = queryDTOList.stream().map(PurchaseBatchQueryDTO::getWarehouseNo).distinct().collect(Collectors.toList());
        List<ShelfLifePurchaseBatch> batchList = storeRecordService.batchQueryMinShelfLifePurchaseBatch(warehouseNoList, skuIdList);
        List<MinShelfLifePurchaseBatchDTO> batchDTOList = this.filterBatchList(batchList, queryDTOList);
        BatchQueryMinShelfLifePurchaseBatchResp resp = BatchQueryMinShelfLifePurchaseBatchResp.builder()
                .batchDTOList(batchDTOList).build();
        return DubboResponse.getOK(resp);
    }

    @Override
    public DubboResponse<PageQuerySkuQuantityChangeRecordResp> pageQuerySkuQuantityChangeRecord(PageQuerySkuQuantityChangeRecordReq request) {
        log.info("接口迁移wms后,记录调用 SaasInterfaceServiceProviderImpl-pageQuerySkuQuantityChangeRecord");
        // 参数校验、构建
        QuantityChangeRecordPageQuery pageQuery = this.checkAndBuildQuantityChangeRecordQuery(request);
        PageInfo<QuantityChangeRecordVO> pageInfo = quantityRecordService.pageQueryQuantityChangeRecord(pageQuery);
        PageInfo<SkuQuantityChangeRecordDTO> pageResult = new PageInfo<>();
        List<QuantityChangeRecordVO> recordVOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(recordVOList)) {
            return DubboResponse.getOK(PageQuerySkuQuantityChangeRecordResp.builder()
                    .changeRecordPageResult(new PageInfo<>())
                    .build());
        }
        List<SkuQuantityChangeRecordDTO> recordList = recordVOList.stream()
                .map(SkuQuantityChangeRecordConvert::convertChangeRecordDTO).collect(Collectors.toList());
        pageResult.setPageNum(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        pageResult.setTotal(pageInfo.getTotal());
        pageResult.setList(recordList);
        return DubboResponse.getOK(PageQuerySkuQuantityChangeRecordResp.builder()
                .changeRecordPageResult(pageResult)
                .build());
    }

    @Override
    public DubboResponse<ApplyAgentSkuResp> applyAgentSku(ApplyAgentSkuReq request) {
        // 参数校验
        Long skuId = request.getSkuId();
        Integer createType = request.getCreateType();
        ExceptionUtil.checkAndThrow(Objects.nonNull(skuId) && skuId > 0, "必传参数缺失-skuId");
        ExceptionUtil.checkAndThrow(Objects.nonNull(createType) && createType > 0, "必传参数缺失-createType");
        Inventory inventory = inventoryService.selectById(skuId);
        if (Objects.isNull(inventory)) {
            return DubboResponse.getDefaultError("未查询到该货品信息");
        }
        if (createType.toString().equals(inventory.getCreateType())
                && ProductsEnum.AuditStatus.FAIL.ordinal() != inventory.getAuditStatus()) {
            return DubboResponse.getDefaultError("代仓类型一致，且未审核失败，无需重复申请代仓");
        }
        inventoryService.editAgentCreateType(inventory.getSku(), createType.toString());
        return DubboResponse.getOK(
                ApplyAgentSkuResp.builder()
                        .applyResult(true).build());
    }

    @Override
    public DubboResponse<ApplyAgentSkuResp> cancelAgentSku(ApplyAgentSkuReq request) {
        // 参数校验
        Long skuId = request.getSkuId();
        ExceptionUtil.checkAndThrow(Objects.nonNull(skuId) && skuId > 0, "必传参数缺失-skuId");

        inventoryService.cancelAgentSku(request.getSkuId());
        return DubboResponse.getOK(
            ApplyAgentSkuResp.builder()
                .applyResult(true).build());
    }

    @Override
    public DubboResponse<List<Long>> querySkuIdsByAdminId(Integer integer) {
        return DubboResponse.getOK(inventoryService.querySkuIdsByAdminId(integer));
    }

    private QuantityChangeRecordPageQuery checkAndBuildQuantityChangeRecordQuery(PageQuerySkuQuantityChangeRecordReq request) {
        // 参数校验
        Integer pageIndex = request.getPageIndex();
        Integer pageSize = request.getPageSize();
        Integer warehouseNo = request.getWarehouseNo();
        List<Long> skuIdList = request.getPermissionSkuIdList();
        ExceptionUtil.checkAndThrow(Objects.nonNull(warehouseNo) && warehouseNo > 0, "请选择仓库");
        ExceptionUtil.checkAndThrow(Objects.nonNull(pageIndex) && pageIndex > 0, "分页参数缺失-pageIndex");
        ExceptionUtil.checkAndThrow(Objects.nonNull(pageSize) && pageSize > 0, "分页参数缺失-pageSize");
        ExceptionUtil.checkAndThrow(!CollectionUtils.isEmpty(skuIdList), "该租户下无货品数据");
        // 参数组装
        return QuantityChangeRecordPageQuery.builder()
                .pageIndex(pageIndex)
                .pageSize(pageSize)
                .skuId(request.getSkuId())
                .permissionSkuIdList(request.getPermissionSkuIdList())
                .warehouseNo(request.getWarehouseNo())
                .pdName(request.getPdName())
                .stockTypeList(request.getStockTypeList())
                .changeTypeNameList(request.getChangeTypeNameList())
                .startTime(request.getStartTime())
                .endTime(request.getEndTime())
                .tenantId(request.getTenantId()).build();
    }

    private List<MinShelfLifePurchaseBatchDTO> filterBatchList(List<ShelfLifePurchaseBatch> batchList
            , List<PurchaseBatchQueryDTO> queryDTOList) {
        List<MinShelfLifePurchaseBatchDTO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(batchList)) {
            return result;
        }
        Map<String, ShelfLifePurchaseBatch> batchMap = batchList.stream().collect(
                Collectors.toMap(batch -> this.generateKey(batch.getWarehouseNo(), batch.getSkuId()), Function.identity(), (a, b) -> a));
        queryDTOList.forEach(queryDTO -> {
            String key = this.generateKey(queryDTO.getWarehouseNo(), queryDTO.getSkuId());
            ShelfLifePurchaseBatch batch = batchMap.get(key);
            if (Objects.nonNull(batch)) {
                result.add(MinShelfLifePurchaseBatchDTO.builder()
                                .skuId(batch.getSkuId())
                                .sku(batch.getSku())
                                .warehouseNo(batch.getWarehouseNo())
                                .batch(batch.getBatch())
                                .leftShelfLife(batch.getLeftShelfLife())
                                .build());
            }
        });
        return result;
    }

    private String generateKey(Object... args) {
        StringBuilder key = new StringBuilder();
        for (Object arg : args) {
            key.append(arg).append("-");
        }
        return key.toString();
    }

    private void paramCheck(StockChangeRecordQueryReq stockChangeRecordQueryReq) {
        Integer pageNum = stockChangeRecordQueryReq.getPageNum();
        Integer pageSize = stockChangeRecordQueryReq.getPageSize();
        Integer inStoreType = stockChangeRecordQueryReq.getInStoreType();
        Integer outStoreType = stockChangeRecordQueryReq.getOutStoreType();
        ExceptionUtil.checkAndThrow(Objects.nonNull(pageNum) && pageNum > 0, "必传参数缺失-pageNum");
        ExceptionUtil.checkAndThrow(Objects.nonNull(pageSize) && pageSize > 0, "必传参数缺失-pageSize");
        ExceptionUtil.checkAndThrow(Lists.newArrayList(inStoreType, outStoreType).contains(null), "不支持同时选择出库和入库");
        // 不支持的出入库类型

    }

    @Override
    public DubboResponse<List<SummerfarmProductInfoResp>> batchQuerySkuInfo(SummerFarmSkuReq summerFarmSkuReq) {
        if (Objects.isNull(summerFarmSkuReq) || CollectionUtils.isEmpty(summerFarmSkuReq.getSkuIds())) {
            return DubboResponse.getDefaultError("查询商品参数错误");
        }
        List<Inventory> inventories = inventoryService.selectByIds(summerFarmSkuReq.getSkuIds());
        if (CollectionUtils.isEmpty(inventories)) {
            return DubboResponse.getOK();
        }
        return DubboResponse.getOK(inventories.stream().map(InventoryConvert::inventory2SummerfarmProductInfoResp).collect(Collectors.toList()));
    }

    private LastStoreRecordPageQuery checkAndBuildStoreRecordQuery(PageQueryPurchaseBatchInventoryReq request) {
        // 参数校验
        Long skuId = request.getSkuId();
        Integer warehouseNo = request.getWarehouseNo();
        Integer pageNum = request.getPageIndex();
        Integer pageSize = request.getPageSize();
        ExceptionUtil.checkAndThrow(Objects.nonNull(pageNum) && pageNum > 0, "分页参数缺失");
        ExceptionUtil.checkAndThrow(Objects.nonNull(pageSize) && pageSize > 0, "分页参数缺失");
        ExceptionUtil.checkAndThrow(Objects.nonNull(skuId) && skuId >0, "必传参数缺失-skuId");
        ExceptionUtil.checkAndThrow(Objects.nonNull(warehouseNo) && warehouseNo > 0, "必传参数缺失-库存仓");
        Inventory inventory = inventoryService.selectById(skuId);
        ExceptionUtil.checkAndThrow(Objects.nonNull(inventory), String.format("未查询到商品sku信息 %s", skuId));
        // 排序字段构建
        String orderBy = request.getQuantityDateSort();
        Integer qualityDateOrderBy = Objects.nonNull(orderBy) && "desc".equals(orderBy) ?
                SortByEnum.DESC.getType() : SortByEnum.ASC.getType();
        // 构建查询对象
        return LastStoreRecordPageQuery.builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .sku(inventory.getSku())
                .warehouseNo(warehouseNo)
                .shelfLifeOrderBy(qualityDateOrderBy).build();
    }
}
