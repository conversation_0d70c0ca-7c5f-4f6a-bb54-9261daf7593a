package net.summerfarm.provider.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.inventory.InventoryProvider;
import net.summerfarm.manage.client.inventory.dto.res.InventoryResDTO;
import net.summerfarm.mapper.manage.AdminMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.model.DTO.purchase.SkuBaseInfoDTO;
import net.summerfarm.model.domain.Admin;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

/**
 * @author: <EMAIL>
 * @create: 2023/1/10
 */
@DubboService
@Component
@Slf4j
public class InventoryProviderImpl implements InventoryProvider {

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private AdminMapper adminMapper;

    /**
     * 最大限制
     */
    public static final int LIMIT_SIZE = 50;

    @Override
    public DubboResponse<List<InventoryResDTO>> batchQueryBySkus(List<String> skus) {
        try {
            if (CollectionUtil.isEmpty(skus) || skus.size() > LIMIT_SIZE) {
                log.warn("查询sku不能为空,skus:{},或者查询数量超过最大限制:{}", skus, LIMIT_SIZE);
                return DubboResponse.getDefaultError("sku不能为空或者已超最大数量限制");
            }
            List<InventoryResDTO> list = Lists.newArrayList();
            List<SkuBaseInfoDTO> skuBaseInfoDTOS = inventoryMapper.selectSkuBaseInfosBySku(skus);
            List<Integer> adminIds = skuBaseInfoDTOS.stream().filter(x -> x.getAdminId() != null)
                    .map(x -> x.getAdminId()).distinct().collect(
                            Collectors.toList());
            Map<Integer, String> adminMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(adminIds)) {
                List<Admin> admins = adminMapper.selectByAdminIds(adminIds);
                adminMap = admins.stream().collect(
                        Collectors.toMap(x -> x.getAdminId(), x -> x.getRealname(), (a, b) -> a));
            }

            for (SkuBaseInfoDTO infoDTO : skuBaseInfoDTOS) {
                InventoryResDTO inventoryResDTO = InventoryResDTO.builder().skuId(infoDTO.getInvId())
                        .sku(infoDTO.getSku()).pdId(infoDTO.getPdId()).pdName(infoDTO.getPdName())
                        .picturePath(infoDTO.getPicturePath()).extType(infoDTO.getExtType())
                        .type(infoDTO.getPdAttribute())
                        .unit(infoDTO.getPackaging()).categoryId(infoDTO.getCategoryId())
                        .adminId(infoDTO.getAdminId())
                        .volume(infoDTO.getVolume()).weight(infoDTO.getWeight())
                        .weightNum(infoDTO.getWeightNum())
                        .build();
                if (infoDTO.getAdminId() != null) {
                    inventoryResDTO.setAdminName(adminMap.get(infoDTO.getAdminId()));
                }
                list.add(inventoryResDTO);
            }
            return DubboResponse.getOK(list);
        } catch (Exception e) {
            log.error("批量查询sku信息异常,cause:{}", Throwables.getStackTraceAsString(e));
            return DubboResponse.getDefaultError("批量查询sku信息异常");
        }
    }
}
