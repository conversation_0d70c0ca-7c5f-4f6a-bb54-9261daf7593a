package net.summerfarm.provider.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.manage.client.products.CategoryProvider;
import net.summerfarm.manage.client.products.dto.req.CategoryReqDTO;
import net.summerfarm.manage.client.products.dto.res.CategoryResDTO;
import net.summerfarm.mapper.manage.CategoryMapper;
import net.xianmu.common.exception.error.code.ParamsErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-06-04
 * @description
 */
@DubboService
@Component
@Slf4j
public class CategoryProviderImpl implements CategoryProvider {

    @Resource
    private CategoryMapper categoryMapper;

    @Override
    public DubboResponse<PageInfo<CategoryResDTO>> pageQueryCategory(CategoryReqDTO categoryReqDTO) {
        if (Objects.isNull(categoryReqDTO.getPageNum()) ||
        Objects.isNull(categoryReqDTO.getPageSize()) ||
                categoryReqDTO.getPageSize() > 200 ){
            return DubboResponse.getDefaultError("参数异常或查询超过限制数量");
        }
        try {
            PageHelper.startPage((int)categoryReqDTO.getPageNum(),(int)categoryReqDTO.getPageSize());
            List<CategoryResDTO> categoryResDTOList = categoryMapper.selectPageByCategoryIds(categoryReqDTO.getCategoryIds());
            return DubboResponse.getOK(PageInfoHelper.createPageInfo(categoryResDTOList));
        } catch (Exception e) {
            log.error("CategoryProviderImpl.pageQueryCategory,批量查询类目异常,cause:{}", Throwables.getStackTraceAsString(e));
            return DubboResponse.getDefaultError("类目查询异常");
        }
    }
}
