package net.summerfarm.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.merchant.MerchantProvider;
import net.summerfarm.manage.client.merchant.dto.MerchantDto;
import net.summerfarm.manage.client.merchant.req.QueryMerchantReq;
import net.summerfarm.manage.client.merchant.resp.QueryMerchantResp;
import net.summerfarm.mapper.manage.MerchantMapper;
import net.summerfarm.model.domain.Merchant;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@DubboService
public class MerchantProviderImpl implements MerchantProvider {
    @Resource
    MerchantMapper merchantMapper;

    @Override
    public DubboResponse<QueryMerchantResp> queryMerchantDto(QueryMerchantReq queryMerchantVO) {
        QueryMerchantResp resp = new QueryMerchantResp();
        if (CollectionUtils.isEmpty(queryMerchantVO.getMIds())) {
            resp.setMerchantDtoList(new ArrayList<>());
            return DubboResponse.getOK(resp);
        }
        List<MerchantDto> collect = merchantMapper.listByMIds(queryMerchantVO.getMIds()).stream().map(
                this::merge
        ).collect(Collectors.toList());
        resp.setMerchantDtoList(collect);
        return DubboResponse.getOK(resp);
    }

    private MerchantDto merge(Merchant merchant){
        MerchantDto merchantVO = new MerchantDto();
        merchantVO.setAdminId(merchant.getAdminId());
        merchantVO.setAddress(merchant.getAddress());
        merchantVO.setArea(merchant.getArea());
        merchantVO.setAreaNo(merchant.getAreaNo());
        merchantVO.setCity(merchant.getCity());
        merchantVO.setMId(merchant.getmId());
        merchantVO.setCluePool(merchant.getCluePool());
        merchantVO.setAuditTime(merchant.getAuditTime());
        merchantVO.setAuditUser(merchant.getAuditUser());
        merchantVO.setLastOrderTime(merchant.getLastOrderTime());
        merchantVO.setMname(merchant.getMname());
        merchantVO.setPhone(merchant.getPhone());
        merchantVO.setMcontact(merchant.getMcontact());
        merchantVO.setPoiNote(merchant.getPoiNote());
        merchantVO.setSkuShow(merchant.getSkuShow());
        merchantVO.setRegisterTime(merchant.getRegisterTime());
        merchantVO.setSize(merchant.getSize());
        merchantVO.setInvitecode(merchant.getInvitecode());
        merchantVO.setInviterChannelCode(merchant.getInviterChannelCode());
        return merchantVO;
    }
}
