package net.summerfarm.provider.impl;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.dingding.handler.MerchantSituationHandler;
import net.summerfarm.manage.client.merchant.req.MerchantSituationAutoAgreeReq;
import net.summerfarm.manage.client.merchant.resp.MerchantSituationAutoAgreeResp;
import net.summerfarm.manage.client.merchant.MerchantSituationManageProvider;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@DubboService
public class MerchantSituationManageProviderImpl implements MerchantSituationManageProvider {

    @Resource
    private MerchantSituationHandler merchantSituationHandler;

    @Override
    public DubboResponse<MerchantSituationAutoAgreeResp> autoAgree(MerchantSituationAutoAgreeReq merchantSituationAutoAgreeReq) {
        try {
            log.info("客情自动审批开始, 客情id:{}", merchantSituationAutoAgreeReq.getMerchantSituationId());
            if (merchantSituationAutoAgreeReq.getMerchantSituationId() == null){
                return DubboResponse.getDefaultError("客情id不能为空");
            }

            DingdingResultBO dingdingResultBO = new DingdingResultBO();
            dingdingResultBO.setBizId(merchantSituationAutoAgreeReq.getMerchantSituationId());
            merchantSituationHandler.agree(dingdingResultBO);
            return DubboResponse.getOK();
        } catch (Throwable e) {
            log.error("客情自动审批异常, 客情id:{}", merchantSituationAutoAgreeReq.getMerchantSituationId(), e);
            return DubboResponse.getDefaultError("客情自动审批异常");
        }
    }
}
