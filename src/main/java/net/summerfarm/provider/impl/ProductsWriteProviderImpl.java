package net.summerfarm.provider.impl;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.products.ProductsWriteProvider;
import net.summerfarm.manage.client.products.dto.req.UpdateSpuOutdatedReqDTO;
import net.summerfarm.mapper.manage.ProductsMapper;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-05-24
 * @description
 */
@Slf4j
@DubboService
@Component
public class ProductsWriteProviderImpl implements ProductsWriteProvider {

    private static final long MAX_LIMIT = 50;
    @Resource
    private ProductsMapper productsMapper;
    @Override
    public DubboResponse updateSpuOutdated(List<UpdateSpuOutdatedReqDTO> spuOutdatedReqDTOS) {
        log.info("updateSkuOutdated 入参:{}", spuOutdatedReqDTOS);
        if (CollectionUtil.isEmpty(spuOutdatedReqDTOS) || spuOutdatedReqDTOS.size() > MAX_LIMIT) {
            return DubboResponse.getDefaultError("商品数量限制");
        }
        productsMapper.batchUpdateOutdated(spuOutdatedReqDTOS);
        return DubboResponse.getOK(Boolean.TRUE);
    }
}
