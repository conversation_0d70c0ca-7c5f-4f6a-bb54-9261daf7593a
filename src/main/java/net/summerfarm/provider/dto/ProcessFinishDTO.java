package net.summerfarm.provider.dto;

import lombok.Data;
import net.summerfarm.enums.BizTypeEnum;

import java.io.Serializable;
import java.util.List;

/**
 * 钉钉审批工作流-流程实例参数
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021-12-21
 */
@Data
public class ProcessFinishDTO implements Serializable {

    private static final long serialVersionUID = 3942571683638396386L;

    private String url;

    private String processInstanceId;

    private Integer newProcessStatus;
}
