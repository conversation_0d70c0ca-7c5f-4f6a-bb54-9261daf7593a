package net.summerfarm.provider.converter;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.summerfarm.enums.SMSType;
import net.summerfarm.manage.client.msg.enums.MsgType;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum MsgTypeConvert {
    NOTIFY(MsgType.NOTIFY.getCode(), SMSType.NOTIFY),
    MARKET(MsgType.MARKET.getCode(), SMSType.MARKET),
    ;

    public static SMSType convert(Integer param) {
        return Arrays.stream(MsgTypeConvert.values())
                .filter(o -> o.getMsgType().equals(param))
                .findFirst()
                .get()
                .getSmsType();
    }

    /**
     * @see MsgType
     */
    Integer msgType;
    SMSType smsType;
}
