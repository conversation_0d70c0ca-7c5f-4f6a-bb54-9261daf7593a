package net.summerfarm.provider.converter;

import net.summerfarm.manage.client.admin.dto.AdminDTO;
import net.summerfarm.model.domain.Admin;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AdminConvert {
    AdminConvert INSTANCE = Mappers.getMapper(AdminConvert.class);

    @Mappings({
            @Mapping(target = "realName", source = "realname")
    })
    AdminDTO convert(Admin admin);
}
