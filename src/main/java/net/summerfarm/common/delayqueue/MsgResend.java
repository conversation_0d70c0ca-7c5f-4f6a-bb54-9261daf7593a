package net.summerfarm.common.delayqueue;

import java.time.LocalDateTime;

public class Msg<PERSON><PERSON>nd extends DelayQueueItem {

    private String keyword;

    private Integer msgBodyId;

    public MsgResend(String id) {
        super(id);
    }

    public MsgResend(String id, LocalDateTime createTime,Long waitTime,String keyword,Integer msgBodyId){
        super(id,createTime,waitTime);
        this.keyword=keyword;
        this.msgBodyId=msgBodyId;
    }

    public Integer getMsgBodyId() {
        return msgBodyId;
    }

    public void setMsgBodyId(Integer msgBodyId) {
        this.msgBodyId = msgBodyId;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
}
