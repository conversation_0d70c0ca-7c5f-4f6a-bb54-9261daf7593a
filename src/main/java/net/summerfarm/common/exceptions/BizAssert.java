package net.summerfarm.common.exceptions;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import net.xianmu.common.exception.BizException;

import java.util.Collection;
import java.util.Objects;

/**
 * 业务断言
 */
public class BizAssert {

    public static void notNull(Object obj, String message) {
        if (Objects.isNull(obj)) {
            throw new BizException(message);
        }
    }

    public static void notNull(Object obj, ErrorCode errorCode) {
        if (Objects.isNull(obj)) {
            throw new BizException(errorCode.getCode(), errorCode.getMessage());
        }
    }

    public static void notBlank(String str, String message) {
        if (StrUtil.isBlank(str)) {
            throw new BizException(message);
        }
    }

    public static void notBlank(String str, ErrorCode errorCode) {
        if (StrUtil.isBlank(str)) {
            throw new BizException(errorCode.getCode(), errorCode.getMessage());
        }
    }

    public static void notEmpty(Collection<?> collection, String message) {
        if (CollUtil.isEmpty(collection)) {
            throw new BizException(message);
        }
    }

    public static void notEmpty(Collection<?> collection, ErrorCode errorCode) {
        if (CollUtil.isEmpty(collection)) {
            throw new BizException(errorCode.getCode(), errorCode.getMessage());
        }
    }

    public static void isTrue(boolean flag, ErrorCode errorCode) {
        if (!flag) {
            throw new BizException(errorCode.getCode(), errorCode.getMessage());
        }
    }

    public static void isTrue(boolean flag, String message) {
        if (!flag) {
            throw new BizException(message);
        }
    }

    public static void isEmpty(Collection<?> collection, String message) {
        if (CollUtil.isNotEmpty(collection)) {
            throw new BizException(message);
        }
    }
}
