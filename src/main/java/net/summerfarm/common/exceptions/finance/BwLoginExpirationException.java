package net.summerfarm.common.exceptions.finance;

import lombok.Data;
import net.xianmu.common.exception.BizException;

/**
 * @description: 百旺登录失效异常
 * @author: <PERSON>
 * @date: 2024-05-15
 **/
@Data
public class BwLoginExpirationException extends BizException {

    private static final long serialVersionUID = 1L;

    /**
     * 登录账号
     */
    private String loginName;

    /**
     * 密码
     */
    private String password;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 区号
     */
    private String areaCode;

    public BwLoginExpirationException() {
        super();
    }

    public BwLoginExpirationException(String message) {
        super(message);
    }

    public BwLoginExpirationException(String message, String loginName, String password, String taxNumber, String areaCode) {
        super(message);
        this.loginName = loginName;
        this.password = password;
        this.taxNumber = taxNumber;
        this.areaCode = areaCode;
    }
}
