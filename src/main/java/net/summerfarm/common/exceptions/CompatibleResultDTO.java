package net.summerfarm.common.exceptions;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.summerfarm.common.AjaxResult;
import net.xianmu.common.result.ResultStatusEnum;

/**
 * 兼容返回类型，包含前后两个版本返回信息
 *
 * <AUTHOR>
 * @date 2022/9/7  17:29
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CompatibleResultDTO<T> extends AjaxResult<T> {
    /**
     * 响应status，参考http状态码
     */
    private Integer status;
    /**
     * 错误码、开发排查用、非必填
     */
    private String errCode;
    /**
     * 响应msg，前端展示用
     */
    private String msg;

    public CompatibleResultDTO(AjaxResult<T> ajaxResult) {
        this.setCode(ajaxResult.getCode());
        this.setMsg(ajaxResult.getMsg());
        this.setData(ajaxResult.getData());
        this.status = ResultStatusEnum.SERVER_ERROR.getStatus();
        this.msg = ResultStatusEnum.SERVER_ERROR.getMsg();
        this.errCode = ajaxResult.getCode();
    }
}
