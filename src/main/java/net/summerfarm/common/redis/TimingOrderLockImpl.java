package net.summerfarm.common.redis;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Repository
public class TimingOrderLockImpl implements TimingOrderLock {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    // Redis删除主键脚本
    @Resource(name = "delLockScript")
    private RedisScript<Boolean> delLockScript;

    // 锁主键标识
    private static final String KEY_ID = "manage:LockId:timingOrder";

    // 锁定过期时间
    private final Integer expireTime = 30 * 60;

    // 获取锁期间休眠时间
    private final Long sleepTime = 1000L;

    // 锁主键格式 (当前只有一个格式)
    public static final MessageFormat KEY_FORMAT = new MessageFormat("manage:LockId:timingOrder:{0}");

    @Resource
    private RedisLockUtil redisLockUtil;

    /**
     * 下一标识
     *
     * @return 锁标识
     */
    @Override
    public Long nextId() {
        return redisTemplate.opsForValue().increment(KEY_ID, 1L);
    }

    /**
     * 尝试锁定
     *
     * @param lockKey 需要锁定对象的标识
     * @param lockId  锁标识
     * @param timeout 超时时间
     * @param unit    超时单位
     * @return
     */
    @Override
    public Boolean tryLock(String lockKey, Long lockId, Long timeout, TimeUnit unit) {
        // 初始化
        final String key = KEY_FORMAT.format(new String[]{lockKey});
        final String value = lockId.toString();
        return redisLockUtil.tryLock(key,value,timeout,unit,expireTime,sleepTime);
    }

    /**
     * 直接锁定
     *
     * @param lockKey 需要锁定对象的标识
     * @param lockId  锁标识
     * @return
     */
    @Override
    public Boolean lock(String lockKey, Long lockId) {
        // 初始化
        final String key = KEY_FORMAT.format(new String[]{lockKey});
        final String value = lockId.toString();

        return redisLockUtil.lock(key,value,sleepTime);
    }

    /**
     * 释放锁定
     *
     * @param lockKey 需要锁定对象的标识
     * @param lockId  锁标识
     * @return
     */
    @Override
    public Boolean unLock(String lockKey, Long lockId) {
        // 初始化
        List<String> keyList = new ArrayList<>();
        String value = lockId.toString();

        //添加主键
        keyList.add(KEY_FORMAT.format(new String[]{lockKey}));

        //执行脚本
        return redisTemplate.execute(delLockScript, keyList, value);
    }
}
