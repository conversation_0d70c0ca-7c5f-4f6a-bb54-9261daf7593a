package net.summerfarm.common.util;

import com.github.pagehelper.PageHelper;
import lombok.Data;
import net.xianmu.common.exception.BizException;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 循环调用分页查询接口迭代器
 * 1)需要设置起始页和每页条数(默认起始页1,每页20条)
 * 2)不需要关心循环跳出,满足条件的所有数据遍历完之后结束
 * 3)支持设置导出数量限制
 * 4)支持超出限制异常处理
 *
 * <AUTHOR>
 * @Description
 * @Date create in 2023/4/25 14:50
 */
public class IteratorFunc {


    public static final Integer DEFAULT_PAGE_NO = 1;
    public static final Integer DEFAULT_PAGE_SIZE = 50;


    @Data
    public static class Page {

        /**
         * 页码
         */
        private Integer pageNo;

        /**
         * 每页多少条
         */
        private Integer pageSize;

    }

    @FunctionalInterface
    public interface LoadFunc<T extends Page, R> {

        /**
         * 分页查询
         *
         * @param t 入参
         * @return 分页列表
         */
        List<R> load(T t);
    }

    public static class PageIterator<P, T extends Page, R> implements Iterator<R> {
        private int pageNo = DEFAULT_PAGE_NO;
        private int pageSize = DEFAULT_PAGE_SIZE;
        private static final int MAX_SIZE = 100;
        private int maxHitSize = 10000;
        private int currentHitSize = 0;
        private boolean isLimitHit = false;
        private int currentIndex;
        private boolean hasMore = true;
        private boolean isThrowMsg = false;
        private String throwMsg;
        private List<R> list;
        private final T t;
        private final P p;
        private final LoadFunc<T, R> loadFunc;
        private final Function<P, List<R>> function;

        public PageIterator(T t, LoadFunc<T, R> loadFunc) {
            Objects.requireNonNull(loadFunc);
            assert t != null;
            assert t.getPageNo() != null;
            assert t.getPageSize() != null;
            if (t.getPageNo() < 1) {
                t.setPageNo(1);
            }
            this.pageNo = t.getPageNo();
            this.pageSize = t.getPageSize();
            if (pageSize > MAX_SIZE) {
                pageSize = MAX_SIZE;
            }
            this.t = t;
            this.p = null;
            this.loadFunc = loadFunc;
            this.function = null;
        }

        public PageIterator(P p, Function<P, List<R>> function) {
            this.function = function;
            Objects.requireNonNull(function);
            assert p != null;
            this.t = null;
            this.p = p;
            this.loadFunc = null;
        }

        @Override
        public boolean hasNext() {
            if (list != null && list.size() > currentIndex) {
                return true;
            }
            // 当前的数据已经加载完毕，尝试加载下一批
            if (!hasMore) {
                return false;
            }
            if (t != null) {
                list = loadFunc.load(t);
            } else {
                list = function.apply(p);
            }
            if (list == null || list.isEmpty()) {
                // 没有加载到数据，结束
                return false;
            }
            if (list.size() < pageSize) {
                // 返回条数小于限制条数，表示没有更多的数据可以加载
                hasMore = false;
            }
            currentIndex = 0;
            if (t != null) {
                t.setPageNo(++pageNo);
            }
            return true;
        }

        @Override
        public R next() {
            return list.get(currentIndex++);
        }


        public boolean hasNextPage() {
            // 当前的数据已经加载完毕，尝试加载下一批
            if (!hasMore) {
                return false;
            }
            PageHelper.startPage(pageNo, pageSize);
            if (t != null) {
                list = loadFunc.load(t);
            } else {
                list = function.apply(p);
            }
            if (list == null || list.isEmpty()) {
                // 没有加载到数据，结束
                return false;
            }
            if (list.size() < pageSize) {
                // 返回条数小于限制条数，表示没有更多的数据可以加载
                hasMore = false;
            }
            ++pageNo;
            if (t != null) {
                t.setPageNo(pageNo);
            }
            return true;
        }

        /**
         * 当页数据
         *
         * @param action
         */
        public void nextPage(Consumer<List<R>> action) {
            Objects.requireNonNull(action);
            while (hasNextPage()) {
                if (isLimitHit) {
                    currentHitSize += list.size();
                    if (currentHitSize == maxHitSize) {
                        action.accept(list);
                        return;
                    } else if (currentHitSize > maxHitSize) {
                        if (isThrowMsg) {
                            throw new BizException(throwMsg);
                        }
                        int endIndex = maxHitSize - (currentHitSize - list.size());
                        action.accept(list.subList(0, endIndex));
                        return;
                    }
                }
                action.accept(list);
            }
        }

        /**
         * 设置分页参数
         *
         * @param pageNo   起始页
         * @param pageSize 每页条数
         * @return 当页数据
         */
        public PageIterator<P, T, R> setPage(Integer pageNo, Integer pageSize) {
            this.pageNo = pageNo;
            this.pageSize = pageSize;
            return this;
        }

        /**
         * 迭代条数限制
         *
         * @param maxRows 最大遍历多少条数据
         * @return
         */
        public PageIterator<P, T, R> limit(Integer maxRows) {
            this.isLimitHit = true;
            this.maxHitSize = maxRows;
            return this;
        }

        /**
         * 超过限制抛出异常提示
         *
         * @return
         */
        public PageIterator<P, T, R> throwTooMuchMsg() {
            this.isThrowMsg = true;
            throwMsg = "最多导出" + maxHitSize + "条数据";
            return this;
        }

    }

    public static List<Integer> doSelectPage(Page pageParam) {
        int startIndex = (pageParam.getPageNo() - 1) * pageParam.getPageSize();
        int endIndex = startIndex + pageParam.getPageSize();
        List<Integer> nums = new ArrayList<>();
        nums.add(2);
        nums.add(8);
        nums.add(7);
        nums.add(9);
        nums.add(6);
        nums.add(4);
        nums.add(5);
        nums.add(11);
        nums.add(23);
        nums.add(33);
        nums.add(64);
        nums.add(65);
        if (startIndex > nums.size()) {
            return new ArrayList<>();
        }
        if (endIndex > nums.size()) {
            endIndex = nums.size();
        }
        return nums.subList(startIndex, endIndex);
    }

    /**
     * 循环调用分页遍历
     * <p>
     * 注:入参两种姿势
     * 1、继承 PageParam
     * 2、setPageParam
     * </p>
     *
     * @param args
     */
    public static void main(String[] args) {
        Page pageParam = new Page();
        pageParam.setPageNo(1);
        pageParam.setPageSize(20);
        // 1、继承PageParam
        new IteratorFunc.PageIterator<>(pageParam, IteratorFunc::doSelectPage).nextPage(currentPage -> {
            // do something

        });
        // 2、手动设置分页参数
        new IteratorFunc.PageIterator<>(pageParam, IteratorFunc::doSelectPage).setPage(1, 20).nextPage(currentPage -> {
            // do something
        });
    }
}