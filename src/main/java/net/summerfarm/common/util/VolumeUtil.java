package net.summerfarm.common.util;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import net.summerfarm.contexts.RegConstant;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * @desc
 * <AUTHOR>
 * @Date 2022/11/10 16:56
 **/
public class VolumeUtil {

    /**
     * 获取体积字符串 ex:1*1*1
     *
     * <AUTHOR>
     * @Date 2022/11/17 11:42
     * @param length 长 单位cm
     * @param width 宽 单位cm
     * @param high 高 单位cm
     * @return java.lang.String 单位米
     **/
    public static String getVolumeStr(BigDecimal length, BigDecimal width, BigDecimal high) {
        // 单位转换
        length = length.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
        width = width.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
        high = high.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
        return Joiner.on("*").join(length.toString(), width.toString(), high.toString());
    }

    /**
     * 计算体积
     *
     * <AUTHOR>
     * @Date 2022/11/14 19:07
     * @param volumeStr 体积
     * @return java.lang.String
     **/
    public static String calcVolume(String volumeStr) {
        // 校验空
        if (StringUtils.isEmpty(volumeStr)) {
            return StringUtils.EMPTY;
        }
        // 体积格式校验
        if (!volumeStr.matches(RegConstant.VOLUME_REG)) {
            return StringUtils.EMPTY;
        }
        List<String> strList = Splitter.on("*").splitToList(volumeStr);
        BigDecimal length = new BigDecimal(strList.get(0)).multiply(BigDecimal.valueOf(100));
        BigDecimal width = new BigDecimal(strList.get(1)).multiply(BigDecimal.valueOf(100));
        BigDecimal high = new BigDecimal(strList.get(2)).multiply(BigDecimal.valueOf(100));
        BigDecimal volume = length.multiply(width).multiply(high);
        return volume.setScale(0,BigDecimal.ROUND_HALF_UP).toString();
    }

    /**
     * 获取长度
     *
     * <AUTHOR>
     * @Date 2022/11/30 11:53
     * @param volumeStr 体积
     **/
    public static String getLength(String volumeStr) {
        // 校验空
        if (StringUtils.isEmpty(volumeStr) || !volumeStr.matches(RegConstant.VOLUME_REG)) {
            return StringUtils.EMPTY;
        }
        List<String> strList = Splitter.on("*").splitToList(volumeStr);
        return new BigDecimal(strList.get(0)).multiply(BigDecimal.valueOf(100))
                .setScale(0, BigDecimal.ROUND_HALF_UP).toPlainString();
    }

    /**
     * 获取宽度
     *
     * <AUTHOR>
     * @Date 2022/11/30 11:53
     * @param volumeStr 体积
     **/
    public static String getWidth(String volumeStr) {
        // 校验空
        if (StringUtils.isEmpty(volumeStr) || !volumeStr.matches(RegConstant.VOLUME_REG)) {
            return StringUtils.EMPTY;
        }
        List<String> strList = Splitter.on("*").splitToList(volumeStr);
        return new BigDecimal(strList.get(1)).multiply(BigDecimal.valueOf(100))
                .setScale(0, BigDecimal.ROUND_HALF_UP).toPlainString();
    }

    /**
     * 获取高度
     *
     * <AUTHOR>
     * @Date 2022/11/30 11:53
     * @param volumeStr 体积
     **/
    public static String getHigh(String volumeStr) {
        // 校验空
        if (StringUtils.isEmpty(volumeStr) || !volumeStr.matches(RegConstant.VOLUME_REG)) {
            return StringUtils.EMPTY;
        }
        List<String> strList = Splitter.on("*").splitToList(volumeStr);
        return new BigDecimal(strList.get(2)).multiply(BigDecimal.valueOf(100))
                .setScale(0, BigDecimal.ROUND_HALF_UP).toPlainString();
    }

}
