package net.summerfarm.common.util;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.exceptions.DefaultServiceException;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Package: com.manageSystem.common.utils
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/8/25
 */
public class PageInfoHelperManual {

    public static PageInfo createPageInfo(List data) {
        return (data != null && !data.isEmpty()) ? new PageInfo(data) : null;
    }

    /**
     * pagehelper   手动分页
     *
     * @param currentPage 当前页
     * @param pageSize
     * @param list
     * @param <T>
     * @return
     */
    public static <T> PageInfo<T> manualPage(int currentPage, int pageSize, List<T> list) {
        int total = list.size();
        if (total > pageSize) {
            int toIndex = pageSize * currentPage;
            if (toIndex > total) {
                toIndex = total;
            }
            int fromIndex = pageSize * (currentPage - 1);

            if (fromIndex <= toIndex) {
                list = list.subList(fromIndex, toIndex);
            } else {
                list = null;
            }
        }
        Page<T> page = new Page<>(currentPage, pageSize);
        if (!CollectionUtils.isEmpty(list)) {
            page.addAll(list);
        }
        page.setPages((total + pageSize - 1) / pageSize);
        page.setTotal(total);

        return new PageInfo<>(page);
    }

    /**
     * pageInfo类型转换
     *
     * @param pageInfoPO 当前pageInfo对象
     * @return 转换之后的pageInfo对象
     */
    public static <P, V> PageInfo<V> pageInfoConvert(PageInfo<P> pageInfoPO) {
        PageInfo<V> pageInfo = new PageInfo();
        pageInfo.setEndRow(pageInfoPO.getEndRow());
        pageInfo.setPageSize(pageInfoPO.getPageSize());
        pageInfo.setSize(pageInfoPO.getSize());
        pageInfo.setOrderBy(pageInfoPO.getOrderBy());
        pageInfo.setStartRow(pageInfoPO.getStartRow());
        pageInfo.setPageNum(pageInfoPO.getPageNum());
        pageInfo.setTotal(pageInfoPO.getTotal());
        pageInfo.setPages(pageInfoPO.getPages());
        pageInfo.setFirstPage(pageInfoPO.getFirstPage());
        pageInfo.setPrePage(pageInfoPO.getPrePage());
        pageInfo.setNextPage(pageInfoPO.getNextPage());
        pageInfo.setLastPage(pageInfoPO.getLastPage());
        pageInfo.setIsFirstPage(pageInfoPO.isIsFirstPage());
        pageInfo.setIsLastPage(pageInfoPO.isIsLastPage());
        pageInfo.setHasPreviousPage(pageInfoPO.isHasPreviousPage());
        pageInfo.setHasNextPage(pageInfoPO.isHasNextPage());
        pageInfo.setNavigatePages(pageInfoPO.getNavigatePages());
        pageInfo.setNavigatepageNums(pageInfoPO.getNavigatepageNums());
        return pageInfo;
    }

    /**
     * pageInfo类型转换
     *
     * @param pageInfoPO 当前pageInfo对象
     * @return 转换之后的pageInfo对象
     */
    public static <P, V> PageInfo<V> pageInfoConvert(PageInfo<P> pageInfoPO, Class<V> targetClazz) {
        PageInfo<V> pageInfo = pageInfoConvert(pageInfoPO);

        List<V> targetList = pageInfoPO.getList().stream().map(e -> {
            try {
                V v = targetClazz.getDeclaredConstructor().newInstance();
                BeanUtils.copyProperties(e, v);
                return v;
            } catch (Exception exception) {
                throw new DefaultServiceException("convertPageInfo cause exception", exception);
            }
        }).collect(Collectors.toList());
        pageInfo.setList(targetList);
        return pageInfo;
    }
}
