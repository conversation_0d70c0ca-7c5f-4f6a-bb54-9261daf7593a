package net.summerfarm.common.util;

import net.summerfarm.common.redis.DistributedLocker;
import net.xianmu.common.exception.error.code.ErrorCode;
import org.redisson.api.RLock;

/**
 * @desc
 * <AUTHOR>
 * @Date 2022/11/10 13:46
 **/
public class RedissonLockUtil {

    private static DistributedLocker redissonLock;

    public static void setLocker(DistributedLocker lock) {
        RedissonLockUtil.redissonLock = lock;
    }

    /**
     * 加锁
     *
     * @param lockKey   key
     * @param leaseTime 续期时间 -1则触发watch dag自动续期
     * @return org.redisson.api.RLock
     * <AUTHOR>
     * @Date 2022/11/10 13:53
     **/
    public static RLock lock(String lockKey, int leaseTime) {
        return redissonLock.lock(lockKey, leaseTime);
    }

    /**
     * 加锁
     *
     * @param lockKey
     * @param leaseTime
     * @return
     */
    public static RLock tryLock(String lockKey, int leaseTime) {
        return redissonLock.tryLock(lockKey, leaseTime, null);
    }

    /**
     * 加锁
     *
     * @param lockKey
     * @param leaseTime
     * @return
     */
    public static RLock tryLock(String lockKey, int leaseTime, ErrorCode errorCode) {
        return redissonLock.tryLock(lockKey, leaseTime, errorCode);
    }
    /**
     * 加锁
     *
     * @param lockKey
     * @param leaseTime
     * @return
     */
    public static RLock tryLock(String lockKey, int waitTime, int leaseTime) {
        return redissonLock.tryLock(lockKey, waitTime, leaseTime, null);
    }
}
