package net.summerfarm.common.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import net.summerfarm.common.exceptions.BizException;
import net.summerfarm.common.exceptions.ErrorCode;
import net.summerfarm.model.domain.SkuBatchCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class CheckHelper {
    /**
     * 校验对象是否为空，抛出异常
     *
     * @param item 对象
     */
    public static <T extends Object> void isNull(T item, ErrorCode errorCode) {
        if (Objects.isNull(item)) {
            throw new BizException(errorCode);
        }
    }

    /**
     * 校验对象是否为空，抛出异常
     * 自定义message
     *
     * @param item 对象
     */
    public static <T extends Object> void isNull(T item, ErrorCode errorCode, String message) {
        if (Objects.isNull(item)) {
            throw new BizException(errorCode.getCode(), message);
        }
    }

    /**
     * 判空，返回默认值
     */
    public static <T extends Object> String stringIfNull(T item) {
        return Objects.isNull(item) ? "" : item.toString();
    }

    /**
     * 判空，返回默认值
     *
     * @Param item 整数的字符串
     */
    public static Integer stringIfEmpty(String item) {
        return StringUtils.isEmpty(item) || StringUtils.equals(item, "null") ? 0 : Integer.valueOf(item);
    }

    /**
     * 判断集合是否为空，toMap
     */
    public static <T, S, R> Map<S, R> toMapIfNotNull(Collection<T> list, Function<T, S> function1, Function<T, R> function2) {
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return list.stream().collect(Collectors.toMap(function1, function2, (o1, o2) -> o2));
    }

    /**
     * 判断集合是否为空，groupBy
     */
    public static <T, S> Map<S, List<T>> groupByIfNotNull(Collection<T> list, Function<T, S> function) {
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return list.stream().collect(Collectors.groupingBy(function));
    }

    /**
     * 判断集合是否为空，toList
     */
    public static <T, S> List<S> toListIfNotNull(Collection<T> list, Function<T, S> function) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(function).distinct().collect(Collectors.toList());
    }

    /**
     * 判断集合是否为空，toSet
     */
    public static <T, S> Set<S> toSetIfNotNull(Collection<T> list, Function<T, S> function) {
        if (CollectionUtils.isEmpty(list)) {
            return Sets.newHashSet();
        }
        return list.stream().map(function).collect(Collectors.toSet());
    }
}
