package net.summerfarm.common.util;

import static net.summerfarm.enums.market.CircleOperatorEnum.getById;

import com.google.common.collect.Lists;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.BizException;
import net.summerfarm.enums.market.CircleOperatorEnum;
import net.summerfarm.enums.market.EsFieldTypeEnum;
import net.summerfarm.model.DTO.market.circle.RuleDTO;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

/**
 * @author: <EMAIL>
 * @create: 2022/8/9
 */
@Slf4j
public final class EsTagFieldUtil {

    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 解析ES字段名，获取字段类型
     *
     * @param field
     * @return
     */
    public static String parseEsFieldType(String field) {
        boolean prefixTag = field.startsWith("tag_");
        //非tag_前缀的字段名称，直接跳过处理
        if (!prefixTag) {
            return StringUtils.EMPTY;
        }
        boolean prefixTagLongDate = field.startsWith("tag_long_date_");
        if (prefixTagLongDate) {
            return EsFieldTypeEnum.LONG_DATE.getValue();
        }
        int secondIndex = field.indexOf("_", 4);
        String esFieldType = field.substring(4, secondIndex);
        return esFieldType;
    }

    /**
     * 是否需要做反义处理，举例：未购买 => 购买；通过第三个分割字段是否为not来判断；
     *
     * @param field
     * @return
     * @apiNote tag_keyword_not_buy_first_category 实际需要反转成 tag_keyword_buy_first_category去查询ES
     */
    public static boolean isAntonymField(String field) {
        boolean prefixTag = field.startsWith("tag_");
        //非tag_前缀的字段名称，直接跳过处理
        if (!prefixTag) {
            return false;
        }
        String[] split = field.split("_");
        if (split.length > 3 && Objects.equals(split[2], "not")) {
            return true;
        }
        return false;
    }

    /**
     * 获取转换后的时间区间值，约定第一个为开始时间，第二个为结束时间
     *
     * @param ruleDTO
     * @return
     * @throws ParseException
     */
    public static List<Long> parseDateType(RuleDTO ruleDTO) {
        Integer operatorId = ruleDTO.getOperatorId();
        String tagValue = ruleDTO.getTagValue();
        String[] date = tagValue.split(",");
        if (date.length > NumberUtils.INTEGER_TWO) {
            return Lists.newArrayList();
        }
        List<Long> list = Lists.newArrayList();
        try {
            //绝对时间点区间
            if (Objects.equals(CircleOperatorEnum.DATE_ABSOLUTE_BETWEEN.getOperatorId(),
                    operatorId)) {
                //直接解析转换成时间,需要约定开始时间和结束时间必须传一个
                // 如果只有开始时间，结束时间传2099-01-01 00:00:00
                // 如果只有结束时间，最小值传1900-01-01 00:00:00
                if (date[0] == null) {
                    date[0] = SDF.format("1900-01-01 00:00:00");
                }
                if (date[1] == null) {
                    date[1] = SDF.format("2099-01-01 00:00:00");
                }
                list.add(DateUtil.parseStartDate(SDF.parse(date[0]), 0).getTime());
                list.add(DateUtil.parseEndDate(SDF.parse(date[1]), 0).getTime());
                return list;
            }

            Date now = new Date();
            //相对时间区间
            if (Objects.equals(CircleOperatorEnum.DATE_RELATIVE_BETWEEN.getOperatorId(),
                    operatorId)) {
                //需要将天数转换成绝对时间
                list.add(DateUtil.parseStartDate(now, -Long.parseLong(date[1])).getTime());
                list.add(DateUtil.parseEndDate(now, -Long.parseLong(date[0])).getTime());
                return list;
            }

            //相对时间点-之内
            if (Objects.equals(CircleOperatorEnum.DATE_RELATIVE_IN.getOperatorId(), operatorId)) {
                //需要将天数转换成绝对时间
                list.add(DateUtil.parseStartDate(now, -Long.parseLong(date[0])).getTime());
                list.add(DateUtil.parseEndDate(now, 0).getTime());
                return list;
            }

            //相对时间点-之前
            if (Objects.equals(CircleOperatorEnum.DATE_RELATIVE_BEFORE.getOperatorId(),
                    operatorId)) {
                //需要将天数转换成绝对时间
                list.add(SDF.parse("1950-01-01 00:00:00").getTime());
                list.add(DateUtil.parseEndDate(now, -Long.parseLong(date[0])).getTime());
                return list;
            }
            return list;
        } catch (Exception e) {
            throw new BizException("处理时间规则异常", e);
        }
    }

    /**
     * 处理数值区间
     *
     * @param ruleDTO
     * @return
     */
    public static QueryBuilder parseLongInterval(RuleDTO ruleDTO) {
        QueryBuilder queryBuilder = null;
        Integer operatorId = ruleDTO.getOperatorId();
        String tagValue = ruleDTO.getTagValue();
        String tagName = ruleDTO.getTagName();
        String[] num = tagValue.split(",");
        if (num.length > NumberUtils.INTEGER_TWO) {
            return null;
        }
        switch (getById(operatorId)) {
            case GT:
                queryBuilder = QueryBuilders.rangeQuery(tagName).gt(num[0]);
                break;
            case LT:
                queryBuilder = QueryBuilders.rangeQuery(tagName).lt(num[0]);
                break;
            case GTE:
                queryBuilder = QueryBuilders.rangeQuery(tagName).gte(num[0]);
                break;
            case LTE:
                queryBuilder = QueryBuilders.rangeQuery(tagName).lte(num[0]);
                break;
            case NUM_L_OPEN_R_OPEN:
                queryBuilder = QueryBuilders.rangeQuery(tagName).gt(num[0]).lt(num[1]);
                break;
            case NUM_L_CLOSE_R_OPEN:
                queryBuilder = QueryBuilders.rangeQuery(tagName).gte(num[0]).lt(num[1]);
                break;
            case NUM_L_CLOSE_R_CLOSE:
                queryBuilder = QueryBuilders.rangeQuery(tagName).gte(num[0]).lte(num[1]);
                break;
            case NUM_L_OPEN_R_CLOSE:
                queryBuilder = QueryBuilders.rangeQuery(tagName).gt(num[0]).lte(num[1]);
                break;
            default:
        }
        return queryBuilder;
    }

}
