package net.summerfarm.common.util;

import net.summerfarm.enums.PrepayInventoryEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-08-26
 * @description
 */
public class SkuUtil {
    /**
     * sku信息
     *
     * @param extType 商品类型
     * @return 字符串
     */
    public static String getExtTypeStr(Integer extType) {
        if (Objects.equals(1, extType)) {
            return "（活动）";
        } else if (Objects.equals(2, extType)) {
            return "（临保）";
        }
        return "";
    }
}
