package net.summerfarm.common.util;

import org.redisson.executor.CronExpression;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-10-30
 * @description
 */
public class CronUtils {
    /**
     * 返回一个布尔值代表一个给定的Cron表达式的有效性
     *
     * @param cronExpression Cron表达式
     * @return 表达式是否有效
     */
    public static boolean isValid(String cronExpression) {
        return CronExpression.isValidExpression(cronExpression);
    }

    /**
     * 返回一个字符串值,表示该消息无效Cron表达式给出有效性
     *
     * @param cronExpression Cron表达式
     * @return 无效时返回表达式错误描述,如果有效返回null
     */
    public static String getInvalidMessage(String cronExpression) {
        try {
            new CronExpression(cronExpression);
            return null;
        } catch (Exception e) {
            if(e instanceof IllegalArgumentException){
                return e.getMessage();
            }
            throw e;
        }
    }

    /**
     * 返回下一个执行时间根据给定的Cron表达式
     *
     * @param cronExpression Cron表达式
     * @return 下次Cron表达式执行时间
     */
    public static LocalDateTime getNextExecTime(String cronExpression) {
        CronExpression cron = new CronExpression(cronExpression);
        Date next = cron.getNextValidTimeAfter(new Date());
        return DateUtils.date2LocalDateTime(next);
    }

    /**
     * 返回指定直接之后的下一个执行时间根据给定的Cron表达式
     *
     * @param cronExpression Cron表达式
     * @param afterTime 指定时间
     * @return 下次Cron表达式执行时间
     */
    public static LocalDateTime getNextExecTime(String cronExpression, LocalDateTime afterTime) {
        CronExpression cron = new CronExpression(cronExpression);
        Date next = cron.getNextValidTimeAfter(DateUtils.localDateTime2Date(afterTime));
        return DateUtils.date2LocalDateTime(next);
    }
}
