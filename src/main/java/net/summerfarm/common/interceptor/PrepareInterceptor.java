package net.summerfarm.common.interceptor;

import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.util.JdbcConstants;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.sqlsource.PageDynamicSqlSource;
import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.ReflectUtils;
import net.summerfarm.common.util.SaasThreadLocalUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.mapper.srm.SrmSupplierUserMapper;
import net.summerfarm.service.AdminDataPermissionService;
import net.xianmu.authentication.client.dto.ShiroUser;
import org.apache.ibatis.executor.statement.RoutingStatementHandler;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.scripting.xmltags.DynamicSqlSource;
import org.apache.ibatis.scripting.xmltags.MixedSqlNode;
import org.apache.ibatis.scripting.xmltags.SqlNode;
import org.apache.ibatis.scripting.xmltags.StaticTextSqlNode;
import org.apache.ibatis.session.Configuration;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Package: net.summerfarm.common.interceptor
 * @Description: mybatis拦截器, 在sql执行前生效 ，当升级到spring5.x的时候可以用AOP做,拦截Mapper
 * @author: <EMAIL>
 * @Date: 2018/9/28
 */
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class PrepareInterceptor implements Interceptor {
    @Lazy
    @Autowired
    SrmSupplierUserMapper srmSupplierUserMapper;
    @Lazy
    @Resource
    private AdminDataPermissionService adminDataPermissionService;
    @Resource
    private RedisTemplate redisTemplate;

    @Override
    public Object intercept(Invocation invocation) throws Exception {
        RoutingStatementHandler handler = (RoutingStatementHandler) invocation.getTarget();
        StatementHandler delegate = (StatementHandler) ReflectUtils.getFieldValue(handler, "delegate");
        MappedStatement mappedStatement = (MappedStatement) ReflectUtils.getFieldValue(delegate, "mappedStatement");
        //获取注解
        RequiresDataPermission requiresDataPermission = getAnnotation(mappedStatement.getId());
        // 接下来类似DataInterceptor中的逻辑
        if (requiresDataPermission == null) {  //没有注解则直接通过
            if (mappedStatement.getId().contains("_COUNT") && mappedStatement.getSqlSource() instanceof PageDynamicSqlSource) {
                int index = mappedStatement.getId().indexOf("_COUNT");
                requiresDataPermission = getAnnotation(mappedStatement.getId().substring(0, index));
                if (requiresDataPermission == null) {
                    return invocation.proceed();
                }
            } else {
                return invocation.proceed();
            }
        }

        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();

        //未登录则直接放行
        if (user == null) {
            return invocation.proceed();
        }
        //todo srm用户后续可设置默认角色/权限
        //SRM用户直接放行
        if (!StringUtils.isEmail(user.getUsername())){
            return invocation.proceed();
        }
        // todo saas请求放行
        if(SaasThreadLocalUtil.isSaasRequest()){
            return invocation.proceed();
        }
        // 超管直接放行
        Set<Integer> roleIds = new HashSet<>(user.getRoleIds());
        for (Integer roleId : roleIds) {
                if (roleId == 1) {
                    return invocation.proceed();
                }
            }
        // 数据权限类型：仓、服务区域
        Set<Integer> dataPermission = new HashSet<>();
        String dataPermissionKey = String.format("auth-data-permission:%s", user.getBizUserId());
        if (redisTemplate.hasKey(dataPermissionKey)){
            String dataPermissionStr = (String) redisTemplate.opsForValue().get(dataPermissionKey);
            dataPermission = JSONObject.parseObject(dataPermissionStr, HashSet.class);
        }

        //如果拥有所有仓库的数据权限则放行
        if (CollectionUtils.isEmpty(dataPermission)) {
            throw new DefaultServiceException("无数据授权");
        }

        // 0代表拥有所有数据权限
        if (dataPermission.contains(0)) {
            return invocation.proceed();
        }

        //拼接过滤条件
        String condition = "";
        String originalParam = requiresDataPermission.originalField();
        if (dataPermission.size() > 1) {
            condition = originalParam + " in ('" + dataPermission.stream().map(String::valueOf).collect(Collectors.joining("', '")) + "')";
        } else {
            condition = originalParam + " = '" + dataPermission.iterator().next() + "'";
        }

        //处理原始sql
        String sql;
        SqlSource sqlSource = mappedStatement.getSqlSource();
        if (sqlSource instanceof PageDynamicSqlSource && Global.threadLocal.get().containsKey(mappedStatement.getId())) {
            List buff = (List) Global.threadLocal.get().get(mappedStatement.getId());
            Configuration configuration = (Configuration) buff.get(0);
            SqlNode rootSqlNode = (SqlNode) buff.get(1);
            Object parameterObject = buff.get(2);

            //修改原始sql
            DynamicSqlSource dynamicSqlSource = new DynamicSqlSource(configuration, rootSqlNode);
            BoundSql boundSql = dynamicSqlSource.getBoundSql(parameterObject);
            String permissionSql = SQLUtils.addCondition(boundSql.getSql(), condition, JdbcConstants.MYSQL);
            List<SqlNode> sqlNodeList = new ArrayList<>();
            sqlNodeList.add(new StaticTextSqlNode(permissionSql));
            SqlNode newRootSqlNode = new MixedSqlNode(sqlNodeList);
            PageDynamicSqlSource pageDynamicSqlSource = new PageDynamicSqlSource(dynamicSqlSource);

            //处理分页数据
            ReflectUtils.setFieldValue(pageDynamicSqlSource, "rootSqlNode", newRootSqlNode);
            BoundSql boundSql1 = pageDynamicSqlSource.getBoundSql(parameterObject);

            sql = boundSql1.getSql();
        } else {
            sql = SQLUtils.addCondition(delegate.getBoundSql().getSql(), condition, JdbcConstants.MYSQL);
        }

        BoundSql boundSql = delegate.getBoundSql();
        ReflectUtils.setFieldValue(boundSql, "sql", sql);

        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        if (target instanceof StatementHandler) {
            return Plugin.wrap(target, this);
        }
        return target;
    }

    @Override
    public void setProperties(Properties properties) {

    }

    /**
     * 获取方法中的注解
     *
     * @param methondName
     * @throws ClassNotFoundException
     */
    public static RequiresDataPermission getAnnotation(String methondName) throws ClassNotFoundException {
        String id = methondName;
        String className = id.substring(0, id.lastIndexOf("."));
        String methodName = id.substring(id.lastIndexOf(".") + 1);
        final Class clazz = Class.forName(className);
        final Method[] method = clazz.getMethods();
        for (Method me : method) {
            if (me.getName().equals(methodName) && me.isAnnotationPresent(RequiresDataPermission.class)) {
                return me.getAnnotation(RequiresDataPermission.class);
            }
        }
        return null;
    }
}
