package net.summerfarm.common.base;

import net.summerfarm.facade.SaasTenantFacade;
import net.summerfarm.model.DTO.SaasTokenInfoDTO;
import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.authentication.controller.AuthBaseController;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.user.UserBase;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


/**
 * 获取用户基础信息Controller
 *
 * <AUTHOR>
 * @date 2023/3/20  13:46
 */
@Controller
public class BaseController extends AuthBaseController {
    @Resource
    SaasTenantFacade saasTenantFacade;
    @Resource
    private HttpServletRequest request;

    /**
     * 获取用户业务userName
     *
     * @return 鲜沐体系：adminId，tms\srm体系：userAuthId，SaaS manage：tenantId
     */
    public String getBizUserName() {
        UserBase userBase = getUserBase();
        if (userBase.getSystemOrigin().equals(SystemOriginEnum.ADMIN.type)) {
            return getUserBase().getNickname();
        }
        if (userBase.getSystemOrigin().equals(SystemOriginEnum.COSFO_MANAGE.type)) {
            String token = request.getHeader("token");
            SaasTokenInfoDTO saasTokenInfoDTO = saasTenantFacade.getXmTmpToken(token);
            return saasTokenInfoDTO == null ? "" : saasTokenInfoDTO.getTenantAccountName();
        }
        return getUserBase().getNickname();
    }

    public Integer getAdminId(){
        UserBase userBase = getUserBase();
        if (userBase.getSystemOrigin().equals(SystemOriginEnum.COSFO_MANAGE.type)) {
            String token = request.getHeader("token");
            SaasTokenInfoDTO saasTokenInfoDTO = saasTenantFacade.getXmTmpToken(token);
            return saasTokenInfoDTO == null ? null: saasTokenInfoDTO.getAdminId().intValue();
        }
        return getUserBase().getBizUserId();
    }

    public String getTenantName(){
        UserBase userBase = getUserBase();
        if (SystemOriginEnum.COSFO_MANAGE.type.equals(userBase.getSystemOrigin())) {
            String token = request.getHeader("token");
            SaasTokenInfoDTO saasTokenInfoDTO = saasTenantFacade.getXmTmpToken(token);
            return saasTokenInfoDTO == null ? null: saasTokenInfoDTO.getTenantName();
        }
        return null;
    }


    public Long getAuthId() {
        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        return user.getId();
    }

    public String getPhone() {
        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        return user.getPhone();
    }


    public UserBase getUserBase(){
        return  super.getUserBase();
    }
}
