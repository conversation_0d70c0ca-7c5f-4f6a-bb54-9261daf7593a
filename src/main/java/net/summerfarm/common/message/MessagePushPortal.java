package net.summerfarm.common.message;

import net.summerfarm.contexts.Global;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;

/**
 * @Package: net.summerfarm.common.message
 * @Description: 消息发送
 * @author: <EMAIL>
 * @Date: 2016/8/24
 */
public class MessagePushPortal implements MessagePortal {

    private static final Logger logger = LoggerFactory.getLogger(MessagePushPortal.class);

    private static MessagePushPortal messagePushPortal;

    private MessagePushPortal() {

    }

    public static MessagePushPortal getInstance() {
        if (messagePushPortal == null) {
            messagePushPortal = new MessagePushPortal();
        }
        return messagePushPortal;
    }

    @Override
    public String send(String receiver, String content) {
        String result = "";
        BufferedReader in = null;
        try {
            String urlNameString = "http://h5." + Global.TOP_DOMAIN_NAME + "/register/RegisterApprovedMsg?mId="+receiver;

            URL url = new URL(urlNameString);
            //打开连接
            URLConnection connection =url.openConnection();
            //设置通用请求
            connection.setRequestProperty("accept","*/*");
//            connection.setRequestProperty("connection","Keep-Alive");
//            connection.setRequestProperty("user-agen","");

            //建立实际的连接
            connection.connect();
            in = new BufferedReader(new InputStreamReader(
                    connection.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine())!= null) {
                result +=line;
            }

        } catch (Exception e) {
            logger.error("发送消息请求出现异常",e);
            e.printStackTrace();
        } finally {
            try {
                if (in !=null) {
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        logger.info("发送消息接口返回数据为：{}", result);
        return result;
    }

    @Override
    public String result(int messageId) {
        return null;
    }
}
