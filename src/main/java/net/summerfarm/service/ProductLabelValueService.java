package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.vo.ProductLabelValueVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProductLabelValueService {

    /**
     * 矫正数据赋予默认值等
     * @param productLabelValues
     * @param sku
     */
    void correctedData(List<ProductLabelValueVo> productLabelValues,String sku);

    /**
     * 查询所有标签
     * @return
     */
    AjaxResult selectAll();

    /**
     * 是否补货标签
     * @param sku
     * @return
     */
    boolean isReplenishmentLabel(String sku);

    /**
     * 查询预售商品
     *
     * @param sku
     * @return
     */
    List<String> queryForSaleSku(List<String> sku);




}
