package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.BatchLackToCondemnDTO;
import net.summerfarm.model.DTO.LackApprovedDTO;
import net.summerfarm.model.DTO.LackGoodsApprovedDTO;
import net.summerfarm.model.DTO.LackToCondemnDTO;
import net.summerfarm.model.domain.DeliveryPathShortSku;
import net.summerfarm.model.domain.TmsLackGoodsApproved;
import net.summerfarm.model.input.LackGoodsMsg;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PathVariable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/5/26 14:51<br/>
 *
 * <AUTHOR> />
 */
@Repository
public interface TmsLackGoodsApprovedService {
    /**
     * 生成缺货核准数据
     * @param lackGoodsMsg
     */
    void createLackGoodsApproved(LackGoodsMsg lackGoodsMsg);

    /**
     * 缺货核准查询
     * @param pageIndex
     * @param pageSize
     * @param lackGoodsApprovedDTO
     * @return
     */
    AjaxResult getDataList(int pageIndex, int pageSize,LackGoodsApprovedDTO lackGoodsApprovedDTO);

    /**
     * 缺货核准
     * @param lackApprovedDTO
     * @return
     */
    AjaxResult lackApproved(LackApprovedDTO lackApprovedDTO);

    /**
     * 缺货判责
     * @param lackToCondemnDTO
     * @return
     */
    AjaxResult lackToCondemn(LackToCondemnDTO lackToCondemnDTO);

    /**
     * 批量判责
     * @param batchLackToCondemnDTO
     * @return
     */
    AjaxResult batchLackToCondemn(BatchLackToCondemnDTO batchLackToCondemnDTO);

    /**
     * Excel导出
     * @param lackGoodsApprovedDTO
     * @param response
     * @return
     */
    void exportExcel(LackGoodsApprovedDTO lackGoodsApprovedDTO, HttpServletResponse response) throws IOException;

    /**
     * 获取缺货核准数据详情
     * @param id
     * @return
     */
    AjaxResult detail(Long id);
}
