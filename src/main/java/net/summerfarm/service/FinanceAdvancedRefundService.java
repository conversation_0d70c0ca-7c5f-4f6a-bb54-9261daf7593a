package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.FinanceAdvancedRefund;

import java.math.BigDecimal;

/**
 * @description 退款单Service
 * <AUTHOR>
 * @date 2022/1/21 15:21
 */
public interface FinanceAdvancedRefundService {

    /**
     * 新建退款单
     * 对预付池金额和预付池往来记录充值 总金额- 绑定金额不变 未绑定金额-
     * @param supplierId
     * @param refundAmount
     * @return
     */
    AjaxResult saveRefund(Integer supplierId, BigDecimal refundAmount);

    /**
     * 退款单列表查询
     * @param pageIndex
     * @param pageSize
     * @param financeAdvancedRefund
     * @return
     */
    AjaxResult listAll(int pageIndex, int pageSize, FinanceAdvancedRefund financeAdvancedRefund);

    /**
     * 更新退款单 上传凭证或者作废
     * @param refund
     * @return
     */
    AjaxResult updateRefund(FinanceAdvancedRefund refund);
}
