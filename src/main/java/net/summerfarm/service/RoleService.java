package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.Role;
import org.springframework.validation.BindingResult;

/**
 * @Package: net.summerfarm.service
 * @Description: 角色业务
 * @author: <EMAIL>
 * @Date: 2016/7/24
 */
public interface RoleService {

//    /**
//     * 查询角色详情（角色信息，角色权限）
//     * @param roleId
//     * @return
//    AjaxResult selectPurviews(Integer roleId);
//
//    *//**
//     * 新增角色
//     * @param record
//     * @param bindingResult
//     * @return
//     *//*
//    AjaxResult save(Role record, BindingResult bindingResult);
//
//    *//**
//     * 授权
//     * @param roleId
//     * @param purviews
//     * @return
//     *//*
//    AjaxResult grantPurview(Integer roleId, String purviews);
//
//    *//**
//     * 查询所有角色
//     * @return
//     *//*
//    AjaxResult selectAll();
//
//    AjaxResult update(Role record, int roleId);*/

}
