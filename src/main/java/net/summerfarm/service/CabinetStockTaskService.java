package net.summerfarm.service;

import java.time.LocalDate;

/**
 * @Description
 * @Date 2023/5/16 15:09
 * @<AUTHOR>
 */
public interface CabinetStockTaskService {

    /**
     * 根据城配仓创建波次出库任务
     *
     * <AUTHOR>
     * @date 2023/5/17 16:22
     * @param storeNo 城配仓
     * @return java.util.List<java.lang.Integer>
     */
    void createWaveSaleOutTaskByStoreNo(Integer warehouseNo, Integer storeNo, LocalDate execDate);

}
