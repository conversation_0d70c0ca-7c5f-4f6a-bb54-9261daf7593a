package net.summerfarm.service.purchase;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.plan.PurchaseTaskDTO;
import net.summerfarm.model.input.purchase.PurchaseTaskChangeRemarkInput;
import net.summerfarm.model.input.purchase.ReplenishmentCloseOrderInput;
import net.summerfarm.model.input.purchase.ReplenishmentOrderEditInput;
import net.summerfarm.model.input.purchase.ReplenishmentOrderQueryInput;
import net.summerfarm.model.vo.purchase.ReplenishmentOrderVO;

import java.util.List;

public interface ReplenishmentOrderService {

    AjaxResult closeReplenishmentOrderTask(Integer createDate);

}
