package net.summerfarm.service.purchase;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.CurrentStockDTO;
import net.summerfarm.model.DTO.plan.SkuImportDTO;
import net.summerfarm.model.DTO.purchase.DocDTO;
import net.summerfarm.model.input.purchase.StockDashboardQueryInput;
import net.summerfarm.model.vo.purchase.ReplenishmentDashboardFutureVO;
import net.summerfarm.model.vo.StockHistorySalesVO;
import net.summerfarm.model.vo.purchase.SkuStockInfoVO;
import net.summerfarm.model.vo.purchase.StockDashboardFutureVO;
import net.summerfarm.model.vo.purchase.StockDashboardHistoryVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

public interface StockDashboardService {

    AjaxResult<List<StockDashboardHistoryVO>> queryDashboardHistory(StockDashboardQueryInput queryInput);

    List<StockHistorySalesVO> queryStockHistorySalesHistory(StockDashboardQueryInput queryInput);

    AjaxResult<List<StockDashboardFutureVO>> queryStockDashboardFuture(StockDashboardQueryInput queryInput);

     List<BigDecimal> getEstimatedDemandQuantity(StockDashboardQueryInput input);

     Boolean isViewDayExists(SkuImportDTO skuImportDTO);
}
