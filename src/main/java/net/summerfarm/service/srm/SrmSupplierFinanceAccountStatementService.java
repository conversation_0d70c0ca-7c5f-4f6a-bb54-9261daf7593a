package net.summerfarm.service.srm;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.FinanceAccountStatement;
import net.summerfarm.model.domain.srm.SrmSupplierFinanceAccountStatement;
import net.summerfarm.model.domain.srm.SrmSupplierUser;
import net.summerfarm.model.input.srm.SrmSupplierFinanceAccountStatementQuery;
import net.summerfarm.model.vo.FinanceAccountStatementVO;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @title: SrmSupplierFinanceAccountStatementService
 * @date 2022/9/1411:11
 */
public interface SrmSupplierFinanceAccountStatementService {

    /**
     * 供应商对账单列表
     *
     * @param srmSupplierFinanceAccountStatementQuery
     * @return
     */
    AjaxResult queryList(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery);

    /**
     * 供应商对账单详情
     *
     * @param srmSupplierFinanceAccountStatement
     * @return
     */
    AjaxResult queryDetail(SrmSupplierFinanceAccountStatement srmSupplierFinanceAccountStatement);

    /**
     * SRM对账单详情（出入库单详情）
     * @param srmSupplierFinanceAccountStatementQuery
     * @return
     */
    AjaxResult purchaseQueryDetail(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery);

    /**
     * SRM对账单详情(出入库单详情汇总)
     * @param srmSupplierFinanceAccountStatementQuery
     * @return
     */
    AjaxResult purchaseQueryDetails(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery);

    /**
     * SRM供应商待匹配的发票信息
     * @param srmSupplierFinanceAccountStatementQuery
     * @return
     */
    AjaxResult queryInvoiceList(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery);

    /**
     * SRM对账单匹配的发票信息
     * @param srmSupplierFinanceAccountStatementQuery
     * @return
     */
    AjaxResult queryDetailInvoice(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery);

    /**
     * SRM对账单和发票匹配
     * @param srmSupplierFinanceAccountStatementQuery
     * @return
     */
    AjaxResult matchAccount(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery);

    /**
     * SRM供应商确认对账单
     * @param srmSupplierFinanceAccountStatementQuery
     * @return
     */
    AjaxResult supplierConfirm(SrmSupplierFinanceAccountStatementQuery srmSupplierFinanceAccountStatementQuery);

    /**
     * 采购待确认对账单
     * @param id
     * @return
     */
    String purchaserConfirm(Long id);

    /**
     * 改变srm对账单状态
     * @param id
     */
    void updateSrmAccount(Long id);

    /**
     * 改变srm对账单状态
     * @param id
     */
    void updateSrmAccountBack(Long id);

    /**
     * 改变srm对账单状态
     * @param id
     */
    void cancelSrmAccountBack(Long id);

    /**
     * 改变srm对账单状态
     * @param id
     */
    void srmAccountCompleted(Long id);

    /**
     * 改变srm对账单状态
     * @param id
     */
    void waitSrmAccountBack(Long id);

    /**
     * 生成srm对账单数据
     * @param financeAccountStatement
     */
    void upsertSrmSupplierAccount(FinanceAccountStatement financeAccountStatement);

    /**
     * 发送公众号通知
     * @param financeAccountStatement
     */
    void upsertSrmWechatMessage(FinanceAccountStatement financeAccountStatement);

    /**
     * 复核通过发送公众号通知
     * @param financeAccountStatement
     */
    void addSrmWechatMessage(FinanceAccountStatementVO financeAccountStatement);

    /**
     * 对账单付款审核失败微信公众号通知
     * @param id
     */
    void paymentFailSrmWechatMessage(Long id);

    /**
     * 撤销公众号通知
     * @param financeAccountStatement
     */
    void cancelSrmWechatMessage(FinanceAccountStatement financeAccountStatement);

    /**
     * 发送srm公众号通知给供应商
     * @param id
     */
    void sendWechatMessage(Long id);

    /**
     * 预付单微信公众号消息推送
     *
     * @param id 预付单号
     */
    void prepayReceipt(Long id);
}
