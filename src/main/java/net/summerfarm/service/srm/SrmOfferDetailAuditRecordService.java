package net.summerfarm.service.srm;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.srm.SrmSupplierOfferDetailAuditRecord;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
public interface SrmOfferDetailAuditRecordService {
    /**
     * 查询是否存在审核中的报价详情
     * @param offerDetailId 报价详情id
     * @return 结果
     */
    boolean existByOfferDetailId(Long offerDetailId);

    /**
     * 保存审核详情
     * @param srmSupplierOfferDetailAuditRecord 报价详情id
     * @return 结果
     */
    AjaxResult save(SrmSupplierOfferDetailAuditRecord srmSupplierOfferDetailAuditRecord);
}
