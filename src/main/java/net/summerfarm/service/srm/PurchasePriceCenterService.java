package net.summerfarm.service.srm;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.PriceCentreDTO;
import net.summerfarm.model.bo.PriceCentreBO;

/**
 * Description:采购价格中心业务接口层
 * date: 2022/6/15 14:55
 *
 * <AUTHOR>
 */
public interface PurchasePriceCenterService {
    /**
     * 根据条件分页查询价格中心列表
     * @param pageIndex 页数
     * @param pageSize 每页大小
     * @param priceCentreBO 价格中心业务实体
     * @return 价格中心列表
     */
    AjaxResult selectPriceCenter(int pageIndex, int pageSize, PriceCentreBO priceCentreBO);
}
