package net.summerfarm.service.impl;

import com.google.common.collect.Iterators;
import com.google.common.collect.Lists;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.common.util.RequestHolder;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.CornerMarkType;
import net.summerfarm.enums.ModelEnum;
import net.summerfarm.enums.StockStatus;
import net.summerfarm.mapper.manage.AdminMapper;
import net.summerfarm.mapper.manage.AreaStoreMapper;
import net.summerfarm.mapper.manage.SalePredictionConfigMapper;
import net.summerfarm.mapper.manage.StockMapper;
import net.summerfarm.model.domain.AdminSkuMapping;
import net.summerfarm.model.domain.SalePredictionConfig;
import net.summerfarm.model.domain.StockBoard;
import net.summerfarm.model.input.AdminSkuMappingInput;
import net.summerfarm.model.input.StockBoardInput;
import net.summerfarm.model.input.purchase.StockDashboardQueryInput;
import net.summerfarm.model.vo.StockBoardVo;
import net.summerfarm.model.vo.StockHistorySalesVO;
import net.summerfarm.module.pms.facade.ofc.OfcSalesFacade;
import net.summerfarm.module.pms.model.vo.OfcSalesVO;
import net.summerfarm.service.StockService;
import net.summerfarm.service.common.CommonConfigQueryService;
import net.summerfarm.service.purchase.StockDashboardService;
import org.apache.dubbo.common.utils.NamedThreadFactory;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * @Description: 库存看板业务层
 * @author: qingjun.miao
 * @Date: 2021/09/14
 */
@Service
public class StockServiceImpl implements StockService {

    private static final Logger logger = LoggerFactory.getLogger(StockServiceImpl.class);

    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private SalePredictionConfigMapper salePredictionConfigMapper;
    @Resource
    private StockMapper stockMapper;
    @Resource
    private RedisTemplate<String,String> redisTemplate;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private StockDashboardService stockDashboardService;

    @Resource
    private CommonConfigQueryService commonConfigQueryService;
    @Resource
    private OfcSalesFacade ofcSalesFacade;


    @Override
    public AjaxResult updateWarningQuantity(String sku, Integer warehouseNo, Integer warningInventory) {
        areaStoreMapper.updateWarningQuantity(sku,warehouseNo,warningInventory);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectConfig(Integer type) {
        SalePredictionConfig config=salePredictionConfigMapper.selectByType(type);
        return AjaxResult.getOK(config);
    }

    @Override
    public AjaxResult predictionRule(SalePredictionConfig config) {
        SalePredictionConfig con = salePredictionConfigMapper.selectByType(config.getType());
        if (Objects.isNull(con)){
            salePredictionConfigMapper.insert(config);
        }else{
            salePredictionConfigMapper.updateByType(config);
        }
        return AjaxResult.getOK();
    }

    private static final ExecutorService SELECT_KANBAN_EXECUTOR = new ThreadPoolExecutor(1, 10, 10, TimeUnit.MINUTES, new ArrayBlockingQueue<>(1000),
        new NamedThreadFactory("kanban_executor_"), new CallerRunsPolicy());

    @Override
    public AjaxResult selectKanban(StockBoardInput stockBoardInput) {

        //根据大客户查询出已有的报价单中的sku
        if (!StringUtils.isBlank(stockBoardInput.getAdminIdStr())){
            List<String> skuList = adminMapper.selectSkuByAdminIdStr(stockBoardInput);
            if (!CollectionUtils.isEmpty(skuList)) {
                String adminSkuStr = skuList.toString().substring(1, skuList.toString().length() - 1);
                stockBoardInput.setAdminSkuStr(adminSkuStr);
            }else{
                return AjaxResult.getOK(new StockBoardVo());
            }
        }
        List<StockBoard> boardList = stockMapper.selectKanban(stockBoardInput);

        if (Objects.isNull(boardList) || boardList.size() == 0){
            return AjaxResult.getOK(new StockBoardVo());
        }
        Map<String, StockHistorySalesVO> historySalesMap = doGetHistorySalesMap(boardList);
        Map<String, Integer> realTimeSalesMap = doGetRealTimeSalesMap(boardList);
        int total = boardList.size();
        int threadSize = total > NumberUtils.INTEGER_TWO_HUNDREDS ? NumberUtils.INTEGER_TWO_HUNDREDS : NumberUtils.INTEGER_FIFTY;

        List<Future<StockBoardVo>> tasks = new ArrayList<>();
        Iterators.partition(boardList.iterator(), threadSize).forEachRemaining(callList->{
            tasks.add(SELECT_KANBAN_EXECUTOR.submit(() -> handle(callList, stockBoardInput, historySalesMap, realTimeSalesMap)));
        });

        AtomicReference<StockBoardVo> res = new AtomicReference<>(new StockBoardVo());
        try {
            tasks.forEach(el -> {
                try {
                    StockBoardVo handle = el.get();
                    res.set(handleResult(res.get(), handle));
                } catch (Exception e) {
                    logger.info("stockBoardInput process error:{}", stockBoardInput, e);
                }
            });
        } catch (Exception e) {
            logger.info("stockBoardInput处理失败:{}", stockBoardInput, e);
            return AjaxResult.getError(e.getMessage());
        }
        return AjaxResult.getOK(res.get());
    }
    /** 获取实时销量数据 **/
    private Map<String, Integer> doGetRealTimeSalesMap(List<StockBoard> boardList) {
        Map<String, Integer> result;
        List<OfcSalesVO> salesVOList = Lists.newArrayList();
        List<List<Integer>> warehousePartitionList = Lists.partition( boardList.stream().map(StockBoard::getWarehouseNo).distinct().collect(Collectors.toList()), 50);
        List<List<String>> skuPartitionList = Lists.partition(boardList.stream().map(StockBoard::getSku).distinct().collect(Collectors.toList()), 50);
        for (List<Integer> warehouseList : warehousePartitionList) {
            for (List<String> skuList : skuPartitionList) {
                salesVOList.addAll(ofcSalesFacade.getRealTimeSales(warehouseList, skuList));
            }
        }
        result = salesVOList.stream().collect(Collectors.toMap(x -> saleSkuWarehouseNoKey(x.getSku(), x.getWarehouseNo()), OfcSalesVO::getSalesQuantity, (x1, x2) -> x2));
        return result;
    }

    /** 获取历史销量数据 **/
    private Map<String, StockHistorySalesVO> doGetHistorySalesMap(List<StockBoard> boardList) {
        Map<String, StockHistorySalesVO> result;
        List<StockHistorySalesVO> stockHistorySalesVOList = Lists.newArrayList();
        Map<Integer, List<StockBoard>> boardDataMap = boardList.stream().collect(Collectors.groupingBy(StockBoard::getWarehouseNo));
        boardDataMap.forEach((warehouseNo,dataList)->{
            List<List<String>> totalSkuList = Lists.partition(dataList.stream().map(StockBoard::getSku).collect(Collectors.toList()), 500);
            for (List<String> skuList : totalSkuList) {
                StockDashboardQueryInput queryInput = new StockDashboardQueryInput();
                queryInput.setWarehouseNo(warehouseNo);
                queryInput.setSkuIds(skuList);
                queryInput.setStartDate(LocalDateTime.now().minusDays(7).with(LocalTime.MIN));
                queryInput.setEndDate(LocalDateTime.now().minusDays(1).with(LocalTime.MAX));
                stockHistorySalesVOList.addAll(stockDashboardService.queryStockHistorySalesHistory(queryInput));
            }

        });
        result = stockHistorySalesVOList.stream().collect(Collectors.toMap(x -> saleSkuWarehouseNoKey(x.getSku(), x.getWarehouseNo()), y -> y, (x1, x2) -> x2));
        return result;
    }

    private StockBoardVo handleResult(StockBoardVo boardVo, StockBoardVo handle) {
        boardVo.setAdequateCount(boardVo.getAdequateCount() + handle.getAdequateCount());
        boardVo.setMaySoldoutCount(boardVo.getMaySoldoutCount() + handle.getMaySoldoutCount());
        boardVo.setSoonSoldOutCount(boardVo.getSoonSoldOutCount() + handle.getSoonSoldOutCount());
        boardVo.setSoldoutCount(boardVo.getSoldoutCount() + handle.getSoldoutCount());
        boardVo.setWarningCount(boardVo.getWarningCount() + handle.getWarningCount());
        boardVo.getStockBoardList().addAll(handle.getStockBoardList());
        boardVo.setTotal(boardVo.getStockBoardList().size());
        return boardVo;
    }

    private StockBoardVo handle(List<StockBoard> boardList, StockBoardInput stockBoardInput, Map<String, StockHistorySalesVO> historySalesMap, Map<String, Integer> realTimeSalesMap){
        //新老品的筛选
        if (stockBoardInput.getIsNew() != null){
            boardList= checkNew(boardList,stockBoardInput.getIsNew());
        }

        //库存状态的筛选
        if (stockBoardInput.getStatusStr() != null){
            boardList=checkStockStatus(boardList,stockBoardInput.getStatusStr());
        }

        SalePredictionConfig config = salePredictionConfigMapper.selectByType(ModelEnum.MODEL.getType());
        StockBoardVo boardVo = new StockBoardVo();
        Integer adequateCount = 0;
        Integer maySoldOutCount = 0;
        Integer soonSoldOutCount = 0;
        Integer soldOutCount = 0;
        Integer warningCount = 0;
        CopyOnWriteArrayList<StockBoard> list= new CopyOnWriteArrayList<>();
        list.addAll(boardList);
        Iterator<StockBoard> iterator = list.iterator();
        while (iterator.hasNext()) {
            StockBoard el = iterator.next();
            //角标处理
            if (Objects.equals(el.getCornerMark(),0)){
                if (isOld(el.getSku(),el.getWarehouseNo())){
                    el.setCornerMark(CornerMarkType.NONE.getType());
                }else{
                    el.setCornerMark(CornerMarkType.NEW.getType());
                }
            }
            //获取过去七天和未来七天的销量情况
            StockBoard recentSales = getRecentSales(el, stockBoardInput.getIsFilter(), historySalesMap);
            if (Objects.isNull(recentSales)){
                list.remove(el);
                continue;
            }
            //实时销量从OFC口径获取
            String realTimeSalesKey = saleSkuWarehouseNoKey(recentSales.getSku(), recentSales.getWarehouseNo());
            recentSales.setRealTimeSalesQuantity(realTimeSalesMap.getOrDefault(realTimeSalesKey, 0));
            getIncrementSales(el);

            Integer incrementSales = getIncrementSales(el);
            Integer quantity = areaStoreMapper.selectAvailableQuantity(el.getSku(),el.getWarehouseNo());
            if (quantity>incrementSales*config.getK2()){
                el.setStatus(StockStatus.ADEQUATE.getStatus());
                adequateCount++;
            }else if (quantity > incrementSales*config.getK3() && quantity <= incrementSales*config.getK2()){
                el.setStatus(StockStatus.MAY_SOLD_OUT.getStatus());
                maySoldOutCount++;
            }else if (quantity <= incrementSales * config.getK3() && quantity > 0){
                el.setStatus(StockStatus.SOON_SOLD_OUT.getStatus());
                soonSoldOutCount++;
            }else{
                el.setStatus(StockStatus.SOLD_OUT.getStatus());
                soldOutCount++;
            }
            if (Objects.nonNull(el.getWarningQuantity()) && el.getWarningQuantity() > quantity){
                warningCount++;
            }
        }

        boardVo.setAdequateCount(adequateCount);
        boardVo.setMaySoldoutCount(maySoldOutCount);
        boardVo.setSoonSoldOutCount(soonSoldOutCount);
        boardVo.setSoldoutCount(soldOutCount);
        boardVo.setWarningCount(warningCount);
        boardVo.setStockBoardList(list);
        return boardVo;
    }


    private List<StockBoard> checkStockStatus(List<StockBoard> boardList, String status) {
        List<StockBoard> resList = new ArrayList<>(boardList.size());
        SalePredictionConfig config = salePredictionConfigMapper.selectByType(ModelEnum.MODEL.getType());
        boardList.forEach(el->{
            Integer incrementSales = getIncrementSales(el);
            Integer quantity = areaStoreMapper.selectAvailableQuantity(el.getSku(), el.getWarehouseNo());
            String[] statusStr = status.split(StringUtils.SEPARATING_SYMBOL);
            for (String s : statusStr) {
                if (s.equals(StockStatus.ADEQUATE.getStatus().toString())){
                    if (quantity > incrementSales * config.getK2()){
                        resList.add(el);
                    }
                }else if (s.equals(StockStatus.MAY_SOLD_OUT.getStatus().toString())){
                    if (quantity > incrementSales * config.getK3() && quantity <= incrementSales * config.getK2()){
                        resList.add(el);
                    }
                }else if (s.equals(StockStatus.SOON_SOLD_OUT.getStatus().toString())){
                    if (quantity<=incrementSales * config.getK3() && quantity>0){
                        resList.add(el);
                    }
                }else if (s.equals(StockStatus.SOLD_OUT.getStatus().toString())){
                    if (quantity <= 0){
                        resList.add(el);
                    }
                }
            }
        });
        return resList;
    }

    private List<StockBoard> checkNew(List<StockBoard> boardList,Integer isNew) {
        List<StockBoard> newList = new ArrayList(boardList.size());
        List<StockBoard> oldList = new ArrayList(boardList.size());
        boardList.forEach(el->{
            Boolean flag = isOld(el.getSku(),el.getWarehouseNo());
            if (flag){
                oldList.add(el);
            }else{
                newList.add(el);
            }
        });
        if (Objects.equals(isNew,1)){
            return newList;
        }else{
            return oldList;
        }
    }

    @Override
    public Boolean isOld(String sku, Integer warehouseNo) {
        return stockMapper.selectIsNew(sku,warehouseNo);
    }

    @Override
    public Integer getIncrementSales(StockBoard board) {
        LocalDateTime now = LocalDateTime.now();
        Integer currentSales = stockMapper.getCurrentSales(board.getSku(),board.getWarehouseNo(), DateUtils.localDateTimeToStringThree(now));
        board.setCurrentSales(currentSales);
        //简易计算
        SalePredictionConfig config = salePredictionConfigMapper.selectByType(ModelEnum.EASY.getType());
        Integer historySalesOne = board.getHistorySalesOne();
        Double easyPredictionSales = historySalesOne.doubleValue() * config.getK1();
        board.setEasyIncrement(easyPredictionSales.intValue() - currentSales < NumberUtils.INTEGER_ZERO ? NumberUtils.INTEGER_ZERO : easyPredictionSales.intValue() - currentSales);
        //模型运算
        double a = NumberUtils.DOUBLE_ONE_POINT_TWO;
        double b = NumberUtils.DOUBLE_ONE;
        if (now.getHour() > NumberUtils.INTEGER_FOURTEEN) {
            double temp = NumberUtils.INTEGER_ONE - Math.pow((now.getHour() - NumberUtils.INTEGER_FOURTEEN) / NumberUtils.DOUBLE_NINE , NumberUtils.DOUBLE_ZERO_POINT_SIX);
            a = Math.pow(NumberUtils.DOUBLE_ONE_POINT_TWO, temp);
        }
        if (now.getDayOfWeek().getValue() == NumberUtils.INTEGER_SEVEN) {
            b = NumberUtils.DOUBLE_ZERO_POINT_NINE;
        }
        Integer yesterdaySales = stockMapper.getNextSales(board.getSku(), board.getWarehouseNo(), DateUtils.localDateTimeToStringThree(now.minusDays(NumberUtils.INTEGER_ONE)));
        Integer beforeYesterdaySales = stockMapper.getNextSales(board.getSku(), board.getWarehouseNo(), DateUtils.localDateTimeToStringThree(now.minusDays(NumberUtils.INTEGER_TWO)));
        Double modelPredictionSales = a * currentSales + b * (NumberUtils.DOUBLE_ZERO_POINT_SIX_SEVEN * yesterdaySales + NumberUtils.DOUBLE_ZERO_POINT_TWO_FIVE * beforeYesterdaySales);
        board.setModelIncrement(modelPredictionSales.intValue() - currentSales < NumberUtils.INTEGER_ZERO ? NumberUtils.INTEGER_ZERO : modelPredictionSales.intValue() - currentSales);
        return board.getModelIncrement();
    }

    public String saleSkuWarehouseNoKey(String sku ,Integer warehouseNo) {
        return warehouseNo + "-" + sku;
    }

    @Override
    public StockBoard getRecentSales(StockBoard board,Boolean isFilter,Map<String, StockHistorySalesVO> salesMap){
        String key = saleSkuWarehouseNoKey(board.getSku(), board.getWarehouseNo());
        StockHistorySalesVO salesVO = salesMap.get(key);
        if (isHistorySalesAllZero(salesVO) && isFilter && isOld(board.getSku(),board.getWarehouseNo())){
            return null;
        }else{
            if (!Objects.isNull(salesVO)) {
                board.setHistorySalesOne(Optional.ofNullable(salesVO.getHistorySalesOne()).orElse(BigDecimal.ZERO).intValue());
                board.setHistorySalesTwo(Optional.ofNullable(salesVO.getHistorySalesTwo()).orElse(BigDecimal.ZERO).intValue());
                board.setHistorySalesThree(Optional.ofNullable(salesVO.getHistorySalesThree()).orElse(BigDecimal.ZERO).intValue());
                board.setHistorySalesFour(Optional.ofNullable(salesVO.getHistorySalesFour()).orElse(BigDecimal.ZERO).intValue());
                board.setHistorySalesFive(Optional.ofNullable(salesVO.getHistorySalesFive()).orElse(BigDecimal.ZERO).intValue());
                board.setHistorySalesSix(Optional.ofNullable(salesVO.getHistorySalesSix()).orElse(BigDecimal.ZERO).intValue());
                board.setHistorySalesSeven(Optional.ofNullable(salesVO.getHistorySalesSeven()).orElse(BigDecimal.ZERO).intValue());
            }
        }
        return board;
    }

    /** 判断历史销量是否都为0 **/
    private boolean isHistorySalesAllZero(StockHistorySalesVO salesVO) {
        if (salesVO == null) {
            return true;
        }
        if (salesVO.getHistorySalesOne() != null && salesVO.getHistorySalesOne().compareTo(BigDecimal.ZERO) > 0) {
            return false;
        }
        if (salesVO.getHistorySalesTwo() != null && salesVO.getHistorySalesTwo().compareTo(BigDecimal.ZERO) > 0) {
            return false;
        }
        if (salesVO.getHistorySalesThree() != null && salesVO.getHistorySalesThree().compareTo(BigDecimal.ZERO) > 0) {
            return false;
        }
        if (salesVO.getHistorySalesFour() != null && salesVO.getHistorySalesFour().compareTo(BigDecimal.ZERO) > 0) {
            return false;
        }
        if (salesVO.getHistorySalesFive() != null && salesVO.getHistorySalesFive().compareTo(BigDecimal.ZERO) > 0) {
            return false;
        }
        if (salesVO.getHistorySalesSix() != null && salesVO.getHistorySalesSix().compareTo(BigDecimal.ZERO) > 0) {
            return false;
        }
        if (salesVO.getHistorySalesSeven() != null && salesVO.getHistorySalesSeven().compareTo(BigDecimal.ZERO) > 0) {
            return false;
        }
        return true;
    }

    @Override
    public AjaxResult selectSkuList(Integer adminId) {
        List<AdminSkuMapping> list = stockMapper.selectSkuList(adminId);
        if (CollectionUtils.isEmpty(list)){
            return AjaxResult.getOK();
        }
        String skuStr = list.stream().map(AdminSkuMapping::getSku).collect(Collectors.joining(StringUtils.SEPARATING_SYMBOL));
        return AjaxResult.getOK(skuStr);
    }

    @Override
    public void deleteSalesRecord() {
        String dateTime = DateUtils.localDateTimeToStringThree(LocalDateTime.now().minusDays(6));
        stockMapper.deleteSalesRecord(dateTime);
    }

    @Override
    public void export(List<StockBoard> boardList) {
        if (CollectionUtils.isEmpty(boardList)){
            return;
        }
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();

        Row head = sheet.createRow(0);
        head.createCell(0).setCellValue("基础信息");
        head.createCell(4).setCellValue("库存情况");
        head.createCell(10).setCellValue("今日销量");
        head.createCell(11).setCellValue("历史销量");

        Row title = sheet.createRow(1);
        title.createCell(0).setCellValue("商品名称");
        title.createCell(1).setCellValue("SKU");
        title.createCell(2).setCellValue("规格");
        title.createCell(3).setCellValue("所属仓库");
        title.createCell(4).setCellValue("仓库库存");
        title.createCell(5).setCellValue("冻结库存");
        title.createCell(6).setCellValue("可用库存");
        title.createCell(7).setCellValue("安全库存");
        title.createCell(8).setCellValue("预留库存");
        title.createCell(9).setCellValue("在途库存");
        title.createCell(10).setCellValue(DateUtils.localDateToString(LocalDate.now(),"MM-dd"));
        title.createCell(11).setCellValue(DateUtils.localDateToString(LocalDate.now().minusDays(7L),"MM-dd"));
        title.createCell(12).setCellValue(DateUtils.localDateToString(LocalDate.now().minusDays(6L),"MM-dd"));
        title.createCell(13).setCellValue(DateUtils.localDateToString(LocalDate.now().minusDays(5L),"MM-dd"));
        title.createCell(14).setCellValue(DateUtils.localDateToString(LocalDate.now().minusDays(4L),"MM-dd"));
        title.createCell(15).setCellValue(DateUtils.localDateToString(LocalDate.now().minusDays(3L),"MM-dd"));
        title.createCell(16).setCellValue(DateUtils.localDateToString(LocalDate.now().minusDays(2L),"MM-dd"));
        title.createCell(17).setCellValue(DateUtils.localDateToString(LocalDate.now().minusDays(1L),"MM-dd"));

        int index = 2;
        for (StockBoard board:boardList){
            Row row = sheet.createRow(index);
            row.createCell(0).setCellValue(board.getPdName());
            row.createCell(1).setCellValue(board.getSku());
            row.createCell(2).setCellValue(Optional.ofNullable(board.getWeight()).orElse(""));
            row.createCell(3).setCellValue(board.getWarehouseName());
            row.createCell(4).setCellValue(board.getQuantity());
            row.createCell(5).setCellValue(board.getFrozenQuantity());
            row.createCell(6).setCellValue(board.getAvailableQuantity());
            row.createCell(7).setCellValue(board.getSafeQuantity());
            row.createCell(8).setCellValue(board.getLockQuantity());
            row.createCell(9).setCellValue(board.getRoadQuantity());
            row.createCell(10).setCellValue(board.getRealTimeSalesQuantity());
            row.createCell(11).setCellValue(board.getHistorySalesOne());
            row.createCell(12).setCellValue(board.getHistorySalesTwo());
            row.createCell(13).setCellValue(board.getHistorySalesThree());
            row.createCell(14).setCellValue(board.getHistorySalesFour());
            row.createCell(15).setCellValue(board.getHistorySalesFive());
            row.createCell(16).setCellValue(board.getHistorySalesSix());
            row.createCell(17).setCellValue(board.getHistorySalesSeven());
            index ++;
        }


        //导出excel
        String fileName = "商品库存"+DateUtils.localDateTimeToStringTwo(LocalDate.now())+".xls";
        try {
            ExcelUtils.outputExcel(workbook, fileName, RequestHolder.getResponse());
        } catch (IOException e) {
            logger.error(Global.collectExceptionStackMsg(e));
            throw new DefaultServiceException("导出异常");
        }
    }

    @Override
    public AjaxResult updateMapping(AdminSkuMappingInput input) {
        List<AdminSkuMapping> list = stockMapper.selectSkuList(input.getAdminId());
        String[] skuStr = input.getSkuStr().split(StringUtils.SEPARATING_SYMBOL);
        if (CollectionUtils.isEmpty(list)){
            //保存管理员映射
            for (String sku : skuStr) {
                stockMapper.insert(input.getAdminId(),sku);
            }
            return AjaxResult.getOK();
        }
        for (String sku : skuStr) {
            if (list.stream().noneMatch(el -> el.getSku().equals(sku))){
                stockMapper.insert(input.getAdminId(),sku);
            }
        }

        for (AdminSkuMapping mapping : list) {
            if (Arrays.stream(skuStr).noneMatch(el -> el.equals(mapping.getSku()))){
                stockMapper.delete(input.getAdminId(),mapping.getSku());
            }
        }
        return AjaxResult.getOK();
    }
}
