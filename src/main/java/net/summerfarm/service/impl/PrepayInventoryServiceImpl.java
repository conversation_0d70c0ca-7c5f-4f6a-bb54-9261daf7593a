package net.summerfarm.service.impl;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;
import java.util.concurrent.TimeUnit;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.DtsUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.DtsModelTypeEnum;
import net.summerfarm.enums.PrepayInventoryEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.domain.MajorRebate;
import net.summerfarm.model.domain.PrepayInventory;
import net.summerfarm.model.domain.PrepayInventoryRecord;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.PrepayInventoryVO;
import net.summerfarm.model.vo.ProductVO;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.PrepayInventoryService;
import net.summerfarm.task.AsyncTaskService;
import net.summerfarm.task.MailUtil;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.apache.dubbo.common.utils.NamedThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-07-21
 * @description
 */
@Service
public class PrepayInventoryServiceImpl extends BaseService implements PrepayInventoryService {
    @Resource
    private PrepayInventoryMapper prepayInventoryMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private MailUtil mailUtil;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private MajorRebateMapper majorRebateMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private AsyncTaskService asyncTaskService;

    private final static Logger logger = LoggerFactory.getLogger(PrepayInventoryServiceImpl.class);

    /**
     * status字段
     */
    private static final String STATUS = "status";

    /**
     * id字段
     */
    private static final String ID = "id";

    @Override
    public AjaxResult select(int pageIndex, int pageSize, Integer adminId, Integer status, String sku, String realname, Integer[] statusArr) {
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(pageIndex, pageSize, () -> {
            List<PrepayInventoryVO> list = prepayInventoryMapper.selectByAdminId(adminId, status, sku, realname, statusArr);
            return list;
        }));
    }

    @Override
    public AjaxResult insert(PrepayInventory instance) {
        instance.setStatus(PrepayInventoryEnum.Status.WAIT_AUDIT.ordinal());
        instance.setCreator(getAdminName());
        instance.setCreateTime(LocalDateTime.now());
        prepayInventoryMapper.insertSelective(instance);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult update(PrepayInventory instance) {
        PrepayInventory old = prepayInventoryMapper.selectByPrimaryKey(instance.getId());
        //只有待生效和生效中的可以修改
        if (PrepayInventoryEnum.Status.WAIT_EFFECT.ordinal() != old.getStatus() && PrepayInventoryEnum.Status.IN_EFFECT.ordinal() != old.getStatus()) {
            return AjaxResult.getErrorWithMsg("当前记录不可修改");
        }

        //只能修改成已失效
        if (instance.getStatus() != null
                && !old.getStatus().equals(instance.getStatus())
                && PrepayInventoryEnum.Status.LOSE_EFFECT.ordinal() != instance.getStatus()) {
            return AjaxResult.getErrorWithMsg("只能修改为已失效状态");
        }

        PrepayInventory update = new PrepayInventory();
        update.setId(instance.getId());
        update.setUsableType(instance.getUsableType());
        update.setDeliveryCycle(instance.getDeliveryCycle());
        update.setStatus(instance.getStatus());
        update.setUpdater(getAdminName());
        if (old.getStatus() != PrepayInventoryEnum.Status.LOSE_EFFECT.ordinal()
                && instance.getStatus() == PrepayInventoryEnum.Status.LOSE_EFFECT.ordinal()) {
            update.setLoseEffectTime(LocalDateTime.now());
        }
        prepayInventoryMapper.updateByIdWithDeliveryCycle(update);

        return AjaxResult.getOK();
    }

    @Override
    @XmLock(waitTime = 1000 * 60, key = "(prepayInventoryService.audit):{id}")
    public AjaxResult audit(Integer id, Boolean flag) {
        PrepayInventory old = prepayInventoryMapper.selectByPrimaryKey(id);
        if (PrepayInventoryEnum.Status.WAIT_AUDIT.ordinal() != old.getStatus()) {
            return AjaxResult.getErrorWithMsg("当前数据不可审核");
        }

        PrepayInventory update = new PrepayInventory();
        update.setId(id);
        update.setAuditor(getAdminName());
        update.setAuditTime(LocalDateTime.now());
        if (flag) {
            //若无生效中的数据，当前数据立即生效
            List<PrepayInventory> list = prepayInventoryMapper.selectInEffectList(old.getAdminId(), old.getSku());
            if(CollectionUtils.isEmpty(list)){
                update.setStatus(PrepayInventoryEnum.Status.IN_EFFECT.ordinal());
            } else {
                update.setEffectTime(LocalDateTime.now());
                update.setStatus(PrepayInventoryEnum.Status.WAIT_EFFECT.ordinal());
            }
        } else {
            update.setStatus(PrepayInventoryEnum.Status.FAIL.ordinal());
        }
        prepayInventoryMapper.updateByPrimaryKeySelective(update);

        return AjaxResult.getOK();
    }

    @Override
    public void sendExpireMail(LocalDate date) {
        logger.info("预付商品到期邮件提醒开始发送");
        LocalDate calcDate = date.plusDays(5);
        List<PrepayInventory> expireList = prepayInventoryMapper.selectExpireAtDate(calcDate);
        for (PrepayInventory pi : expireList) {
            Admin admin = adminMapper.selectByPrimaryKey(pi.getAdminId());
            if (admin == null) {
                continue;
            }

            List<Admin> receiveList = new ArrayList<>();
            //运营
            if (admin.getOperateId() != null) {
                Admin operator = adminMapper.selectByAdminId(admin.getOperateId());
                if (operator != null) {
                    receiveList.add(operator);
                }
            }
            //BD
            receiveList.addAll(adminMapper.selectBdListByKaAdminId(pi.getAdminId()));

            if (!CollectionUtils.isEmpty(receiveList)) {
                InventoryVO vo = inventoryMapper.selectSkuType(pi.getSku());

                String title = "五天达到配送结束周期";

                String[] to = receiveList.stream().map(Admin::getUsername).toArray(String[]::new);

                String body = "hi,\n" +
                        admin.getRealname() + "," + vo.getSku() + "-" + vo.getPdName() + "-" + vo.getWeight()
                        + "，最后配送日为" + (calcDate.getMonthValue() + 1) + "月" + "-" + calcDate.getDayOfMonth()
                        + "日，剩余五天达到约定时间，请及时提醒客户。\n";

                mailUtil.sendMail(title, body, to, null);
            }
        }

        logger.info("预付商品到期邮件提醒发送结束，共{}条", expireList.size());
    }

    @Override
    @Transactional(rollbackOn = RuntimeException.class)
    @XmLock(waitTime = 1000 * 60, key = "(PrepayInventoryService.increase):{sku}:{orderNo}")
    public boolean increase(Integer adminId, String sku, String orderNo, int amount) {
        List<PrepayInventoryRecord> recordList = prepayInventoryRecordMapper.selectRecordList(orderNo, sku, true);
        if (CollectionUtils.isEmpty(recordList)) {
            return false;
        }

        //可退数量校验
        int usableTotalAmount = recordList.stream().filter(PrepayInventoryRecord::getValid).mapToInt(PrepayInventoryRecord::getAmount).sum();
        if (usableTotalAmount < amount) {
            logger.info("预付商品返还失败，订单：{}，sku：{}，返还数量：{}，实际可返数量：{}", orderNo, sku, amount, usableTotalAmount);
            return false;
        }

        List<Integer> prepayInventoryIds = recordList.stream().map(PrepayInventoryRecord::getPrepayInventoryId).collect(Collectors.toList());
        List<PrepayInventory> prepayList = prepayInventoryMapper.selectPrepayInventoryListById(prepayInventoryIds);
        Map<Integer,PrepayInventory>  prePayMap = prepayList.stream().collect(Collectors.toMap(PrepayInventory::getId, Function.identity()));
        //返还时需要优先返还后生效的（查询时已排序）
        for (PrepayInventoryRecord record : recordList) {
            PrepayInventory pi = prePayMap.get(record.getPrepayInventoryId());
            PrepayInventoryRecord update = new PrepayInventoryRecord();
            update.setId(record.getId());

            PrepayInventory updatePi = new PrepayInventory();
            updatePi.setId(pi.getId());
            updatePi.setUpdater(getAdminName());
            updatePi.setUpdateTime(LocalDateTime.now());
            //已完成状态退数量，状态修改为生效中
            if (pi.getStatus() == PrepayInventoryEnum.Status.FINISH.ordinal()) {
                updatePi.setStatus(PrepayInventoryEnum.Status.IN_EFFECT.ordinal());
            }
            //如果当前记录足够返还，修改记录使用数量
            if (record.getAmount() > amount) {
                prepayInventoryMapper.updateUsedAmount(updatePi,amount);

                record.setAmount(record.getAmount() - amount);
                prepayInventoryRecordMapper.updateByPrimaryKeySelective(record);

                logger.info("预付商品返还，订单：{}，sku：{}，预付id：{}，使用数量：{}，返还数量：{}", orderNo, sku, record.getPrepayInventoryId(), record.getAmount(), amount);
                break;
            } //当前记录不够返还，修改为失效状态
            else {
                prepayInventoryMapper.updateUsedAmount(updatePi,record.getAmount());

                update.setValid(false);
                prepayInventoryRecordMapper.updateByPrimaryKeySelective(update);

                logger.info("预付商品返还，订单：{}，sku：{}，预付id：{}，使用数量：{}，返还数量：{}", orderNo, sku, record.getPrepayInventoryId(), record.getAmount(), record.getAmount());

                amount -= record.getAmount();
                if (amount == 0) {
                    break;
                }
            }
        }

        return true;
    }

    private static final ExecutorService MESSAGE_SENDER_EXECUTOR = new ThreadPoolExecutor(1, 1, 10, TimeUnit.MINUTES, new ArrayBlockingQueue<>(1000),
        new NamedThreadFactory("prepare_sender_"), new CallerRunsPolicy());

    @Override
    public void sendDecreaseMessage(DtsModel dtsModel) {
        List<Map<String, String>> oldDataList = dtsModel.getOld();
        for (int i = 0; i < oldDataList.size(); i++) {
            String id = DtsUtils.searchChangeId(dtsModel, i, STATUS, ID);
            if (Objects.isNull(id)) {
                continue;
            }
            PrepayInventory prepayInventory = prepayInventoryMapper.selectByPrimaryKey(Integer.valueOf(id));
            if (Objects.isNull(prepayInventory)) {
                return;
            }
            if (!Objects.equals(prepayInventory.getStatus(), PrepayInventoryEnum.Status.FINISH.ordinal())) {
                logger.info("该预付配置信息状态非已完成，无需处理");
                return;
            }

            Integer adminId = prepayInventory.getAdminId();
            String sku = prepayInventory.getSku();
            List<MajorRebate> list = majorRebateMapper.selectValidSkuList(adminId, sku);
            if (!CollectionUtils.isEmpty(list)) {
                logger.info("发送预付商品消耗完毕私信，adminId：{}，sku：{}，合同ID：{}", adminId, sku, prepayInventory.getId());
                MESSAGE_SENDER_EXECUTOR.execute(() -> sendMessage(prepayInventory));
            }
        }
    }

    private void sendMessage(PrepayInventory pi) {
        Admin admin = adminMapper.selectByPrimaryKey(pi.getAdminId());
        List<Admin> receiveList = new ArrayList<>();
        //运营
        if (admin.getOperateId() != null) {
            Admin operator = adminMapper.selectByPrimaryKey(admin.getOperateId());
            if (operator != null) {
                receiveList.add(operator);
            }
        }
        //BD
        List<Admin> admins = adminMapper.selectBdListByKaAdminId(pi.getAdminId());
        if(!CollectionUtils.isEmpty(admins)){
            receiveList.addAll(admins);
        }
        Integer[] to = receiveList.stream().map(Admin::getAdminId).toArray(Integer[]::new);
        ProductVO vo = productsMapper.queryBySku(pi.getSku());
        String message = admin.getRealname() + "，" + pi.getSku() + "-" + vo.getPdName() + "-" + vo.getWeight()
                + "合同单价为" + (pi.getPrepayPrice().divide(BigDecimal.valueOf(pi.getPrepayAmount()), BigDecimal.ROUND_DOWN)).setScale(2, BigDecimal.ROUND_DOWN)
                + "元的合同已消耗完成，请注意是否要重新设置返点金额。";
        asyncTaskService.sendDingTalkMsgToAdmin(to, message);
    }
}
