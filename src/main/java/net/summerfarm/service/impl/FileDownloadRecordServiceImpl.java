package net.summerfarm.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;

import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.downloadCenter.bean.FileDownloadRecordBO;
import net.summerfarm.downloadCenter.bean.FileDownloadRecordDTO;
import net.summerfarm.downloadCenter.bean.FileDownloadRecordResp;
import net.summerfarm.downloadCenter.bean.UploadFileDTO;
import net.summerfarm.downloadCenter.handler.DownloadHandler;
import net.summerfarm.enums.FileDownloadRecordEnum;
import net.summerfarm.enums.DownloadCenterEnum;
import net.summerfarm.facade.DownloadHandlerFactory;
import net.summerfarm.mapper.manage.FileDownloadRecordMapper;
import net.summerfarm.model.domain.FileDownloadRecord;
import net.summerfarm.service.FileDownloadRecordService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.oss.common.constants.OssConstants;
import net.xianmu.oss.common.util.OssGetUtil;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.helper.XianMuOssHelper;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import javax.annotation.Resource;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/12 11:45
 */
@Slf4j
@Service
public class FileDownloadRecordServiceImpl extends BaseService implements FileDownloadRecordService {

    @Resource
    private FileDownloadRecordMapper fileDownloadRecordMapper;
    @Resource
    private MqProducer mqProducer;

    @Override
    public AjaxResult select(Integer pageIndex, Integer pageSize, Integer adminId) {
        PageHelper.startPage(pageIndex,pageSize);
        List<FileDownloadRecord> recordList = fileDownloadRecordMapper.selectByAdminId(adminId, BaseConstant.XIANMU, BaseConstant.XIANMU_TENANT_ID);
        for (FileDownloadRecord fileDownloadRecord : recordList) {
            Integer expirationTime = fileDownloadRecord.getExpirationTime();
            //七牛云或者无过期时间数据跳过或者不为上传成功状态
            if (Objects.equals(fileDownloadRecord.getOssType(), DownloadCenterEnum.Types.QI_NIU.getValue())
                    || expirationTime == null
                    || !Objects.equals(fileDownloadRecord.getStatus(), DownloadCenterEnum.Status.SUCCESS.getValue())) {
                continue;
            }
            //创建时间加上过期日期是否大于当前时间 大于当前时间过期数据
            boolean before = fileDownloadRecord.getCreateTime().plusDays(expirationTime).isBefore(LocalDateTime.now());
            if (before) {
                fileDownloadRecord.setStatus(DownloadCenterEnum.Status.EXPIRED.getValue());
            }
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(recordList));
    }

    @Override
    public AjaxResult list(Integer pageIndex, Integer pageSize, Integer type, LocalDate startTime) {
        PageHelper.startPage(pageIndex,pageSize);
        List<FileDownloadRecord> fileDownloadRecords = fileDownloadRecordMapper.selectByType(type, startTime,BaseConstant.XIANMU,BaseConstant.XIANMU_TENANT_ID);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(fileDownloadRecords));
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public AjaxResult deleteAll() {
        fileDownloadRecordMapper.deleteAll(getAdminId());
        return AjaxResult.getOK();
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public AjaxResult delete(Long id) {
        fileDownloadRecordMapper.delete(id);
        return AjaxResult.getOK();
    }

    @Override
    public CommonResult<String> download(Long id) {
        //查询该id的存储地址
        FileDownloadRecord downloadRecord = fileDownloadRecordMapper.selectById(id);
        if (Objects.equals(downloadRecord.getOssType(), DownloadCenterEnum.Types.QI_NIU.getValue())) {
            //七牛云存储的文件无法在oss中找到
            return CommonResult.fail(ResultStatusEnum.ACCEPTED, "该文件下载错误，请确认。");
        }
        //创建时间加上过期日期是否早于当前时间 早于当前时间过期数据
        Integer expirationTime = downloadRecord.getExpirationTime();
        if (expirationTime != null){
            boolean before = downloadRecord.getCreateTime().plusDays(expirationTime).isBefore(LocalDateTime.now());
            if (before) {
                downloadRecord.setStatus(DownloadCenterEnum.Status.EXPIRED.getValue());
            }
        }
        if (Objects.equals(downloadRecord.getStatus(), DownloadCenterEnum.Status.EXPIRED.getValue())) {
            return CommonResult.fail(ResultStatusEnum.ACCEPTED, "该文件已过期，请确认。");
        }
        //有效时间十分钟
        String url = XianMuOssHelper.generateUrl(downloadRecord.getFileNameAddress());
        return CommonResult.ok(url);
    }

    @Override
    public void updateExpiredData() {
        List<FileDownloadRecord> fileDownloadRecords = fileDownloadRecordMapper.select();
        for (FileDownloadRecord fileDownloadRecord : fileDownloadRecords) {
            if (fileDownloadRecord.getCreateTime().plusDays(fileDownloadRecord.getExpirationTime()).isBefore(LocalDateTime.now())) {
                fileDownloadRecordMapper.expireDownloadRecords(fileDownloadRecord.getId());
            }
        }
    }

    @Override
    public FileDownloadRecordResp startUpload(FileDownloadRecordDTO fileDownloadRecordDTO) {
        log.info("下载中心请求参数：{}", JSON.toJSONString(fileDownloadRecordDTO));

        String fileName = fileDownloadRecordDTO.getFileName();
        FileDownloadRecordEnum bizEnum = fileDownloadRecordDTO.getBizEnum();
        Object results = fileDownloadRecordDTO.getUploadObj();
        OSSExpiredLabelEnum ossExpiredLabelEnum = fileDownloadRecordDTO.getOssExpiredLabelEnum();
        Integer operatorId = fileDownloadRecordDTO.getOperatorId();

        if (StrUtil.isBlank(fileName) || bizEnum == null || ossExpiredLabelEnum == null){
            throw new ParamsException("参数异常");
        }
        DownloadHandler downloadHandler = DownloadHandlerFactory.getHandleInstance(bizEnum);
        if (downloadHandler == null){
            throw new BizException("缺少对应业务的DownloadHandler实现");
        }

        //插入待上传的文件下载记录
        FileDownloadRecord record = new FileDownloadRecord();
        record.setFileName(fileName);
        record.setStatus(DownloadCenterEnum.Status.WAIT_UPLOAD.getValue());
        record.setType(bizEnum.ordinal());
        record.setCreateTime(LocalDateTime.now());
        record.setAdminId(operatorId);
        record.setExpirationTime(ossExpiredLabelEnum.getExpireDay());
        record.setOssType(DownloadCenterEnum.Types.ALI.getValue());
        String uId = UUID.randomUUID().toString();
        record.setUId(uId);
        fileDownloadRecordMapper.insert(record);

        FileDownloadRecordBO fileDownloadRecordBO = new FileDownloadRecordBO(uId, results, bizEnum);
        mqProducer.send("download_center", "upload_oss", fileDownloadRecordBO);
        return new FileDownloadRecordResp(uId);
    }

    @Override
    public FileDownloadRecordResp uploadStorage(UploadFileDTO uploadFileDTO) {
        String uid = uploadFileDTO.getUid();
        byte[] bytes = uploadFileDTO.getBytes();

        FileDownloadRecordResp resp = new FileDownloadRecordResp(uid);
        if (StrUtil.isBlank(uid) || bytes == null || bytes.length == 0){
            logger.error("startUpload.参数异常,uid:{}", uid);
            return resp;
        }
        FileDownloadRecord fileDownloadRecord = fileDownloadRecordMapper.selectByUid(uid);
        if(fileDownloadRecord == null){
            logger.error("startUpload.未查询到文件下载记录,uid:{}", uid);
            return resp;
        }
        //获取文件期限
        Integer expirationTime = fileDownloadRecord.getExpirationTime();
        OSSExpiredLabelEnum ossExpiredLabelEnum = OSSExpiredLabelEnum.getLabelByExpireDay(expirationTime);

        String fileName = fileDownloadRecord.getFileName();
        try {
            //上传文件至阿里云
            String pathAndFileName = "downloadCenter" + OssConstants.DIR_SEPARATOR + fileName;
            OssUploadResult uploadResult = OssUploadUtil.upload(pathAndFileName, bytes, ossExpiredLabelEnum);
            resp.setOssAccessAddress(uploadResult.getObjectOssKey());
            fileDownloadRecord.setStatus(DownloadCenterEnum.Status.SUCCESS.getValue());
            fileDownloadRecord.setFileNameAddress(uploadResult.getObjectOssKey());
            fileDownloadRecordMapper.updateById(fileDownloadRecord);
        }catch (Throwable e){
            logger.error("startUpload.异步上传外部存储异常,uid:{}", uid, e);
            fileDownloadRecord.setStatus(DownloadCenterEnum.Status.FAIL.getValue());
            fileDownloadRecordMapper.updateById(fileDownloadRecord);
        }
        return resp;
    }

}
