package net.summerfarm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.*;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.CirclePeopleRelationTypeEnum;
import net.summerfarm.enums.SeriesTypeEnum;
import net.summerfarm.mapper.manage.CirclePeopleRelationMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.manage.StrictSelectionMapper;
import net.summerfarm.model.domain.CirclePeopleRelation;
import net.summerfarm.model.domain.SeriesOfSku;
import net.summerfarm.model.domain.StrictSelection;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.SeriesOfAreaVO;
import net.summerfarm.model.vo.SeriesOfSkuVO;
import net.summerfarm.model.vo.StrictSelectionVO;
import net.summerfarm.service.CirclePeopleService;
import net.summerfarm.service.SeriesService;
import net.summerfarm.service.StrictSelectionService;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-04-07
 * @description
 */
@Service
public class StrictSelectionServiceImpl extends BaseService implements StrictSelectionService {
    @Resource
    private StrictSelectionMapper strictSelectionMapper;
    @Resource
    private SeriesService seriesService;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private CirclePeopleRelationMapper circlePeopleRelationMapper;

    @Resource
    private CirclePeopleService circlePeopleService;

    @Override
    public PageInfo<? extends StrictSelection> selectPage(int pageIndex, int pageSize, StrictSelectionVO query) {
        PageHelper.startPage(pageIndex, pageSize);
        List<StrictSelection> list = strictSelectionMapper.selectByModel(query);
        List<StrictSelectionVO> vos = list.stream().map(el -> {
            StrictSelectionVO vo = new StrictSelectionVO(el);
            String nameOrId = circlePeopleRelationMapper.selectNameOrIdByActId(el.getId(), CirclePeopleRelationTypeEnum.SELECTION_TYPE.getType());
            vo.setNameOrId(nameOrId);
            //城市信息
            List<SeriesOfAreaVO> seriesOfArea = seriesService.queryAreaSeries(SeriesTypeEnum.SELECTION, el.getId());
            List<String> areaNameList = seriesOfArea.stream().map(SeriesOfAreaVO::getAreaName).collect(Collectors.toList());
            vo.setAreaNameList(areaNameList);

            //状态
            LocalDateTime now = LocalDateTime.now();
            vo.setStatus(1);
            if(vo.getStartTime().isAfter(now)){
                vo.setStatus(0);
            } else if(vo.getEndTime().isBefore(now)){
                vo.setStatus(2);
            }

            return vo;
        }).collect(Collectors.toList());

        return PageInfoHelper.createPageInfo(vos);
    }

    @Override
    public StrictSelectionVO selectById(Integer id) {
        StrictSelection selection = strictSelectionMapper.selectByPrimaryKey(id);
        if(selection == null){
            throw new DefaultServiceException(ResultConstant.PARAM_FAULT);
        }

        StrictSelectionVO vo = new StrictSelectionVO(selection);
        //城市信息
        List<SeriesOfAreaVO> seriesOfArea = seriesService.queryAreaSeries(SeriesTypeEnum.SELECTION, selection.getId());
        vo.setSeriesAreaList(seriesOfArea);
        //sku信息
        List<SeriesOfSkuVO> seriesOfSku = seriesService.querySkuSeries(SeriesTypeEnum.SELECTION, selection.getId());
        vo.setSeriesSkuList(seriesOfSku);
        String nameOrId = circlePeopleRelationMapper.selectNameOrIdByActId(id, CirclePeopleRelationTypeEnum.SELECTION_TYPE.getType());
        vo.setNameOrId(nameOrId);

        return vo;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String addStrictSelection(StrictSelectionVO instance) {
        String info = paramCheck(instance);

        instance.setCreator(getAdminId());
        strictSelectionMapper.insertSelective(instance);

        //城市信息处理
        seriesService.handleAreaNoSeries(SeriesTypeEnum.SELECTION, instance.getId(), instance.getSeriesAreaList());

        //sku信息处理
        seriesService.handleSkuSeries(SeriesTypeEnum.SELECTION, instance.getId(), instance.getSeriesSkuList());

        //自动排序
        if(instance.getAutoSort()){
            seriesService.autoUpdateSort(instance.getId());
        }
        //圈人
        if(!Objects.equals(instance.getRuleId(),null)){
            CirclePeopleRelation circlePeopleRelation = new CirclePeopleRelation();
            circlePeopleRelation.setType(CirclePeopleRelationTypeEnum.SELECTION_TYPE.getType());
            circlePeopleRelation.setTypeId(instance.getId());
            circlePeopleRelation.setRuleId(instance.getRuleId());
            circlePeopleRelation.setCreator(getAdminId());
            circlePeopleRelation.setCreateTime(LocalDateTime.now());
            circlePeopleRelationMapper.insertSelective(circlePeopleRelation);
        }
        return info;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String updateStrictSelection(StrictSelectionVO instance) {
        String info = paramCheck(instance);

        instance.setUpdater(getAdminId());
        strictSelectionMapper.updateByPrimaryKeySelective(instance);

        //城市信息处理
        seriesService.handleAreaNoSeries(SeriesTypeEnum.SELECTION, instance.getId(), instance.getSeriesAreaList());

        //sku信息处理
        seriesService.handleSkuSeries(SeriesTypeEnum.SELECTION, instance.getId(), instance.getSeriesSkuList());

        //自动排序
        if(instance.getAutoSort()){
            seriesService.autoUpdateSort(instance.getId());
        }
        if(!Objects.equals(instance.getRuleId(),null)){
            circlePeopleRelationMapper.deletByTypeId(instance.getId(),CirclePeopleRelationTypeEnum.SELECTION_TYPE.getType() );
            CirclePeopleRelation circlePeopleRelation = new CirclePeopleRelation();
            circlePeopleRelation.setType(CirclePeopleRelationTypeEnum.SELECTION_TYPE.getType());
            circlePeopleRelation.setTypeId(instance.getId());
            circlePeopleRelation.setRuleId(instance.getRuleId());
            circlePeopleRelation.setCreator(getAdminId());
            circlePeopleRelation.setCreateTime(LocalDateTime.now());
            circlePeopleRelationMapper.insertSelective(circlePeopleRelation);
        }else{
            circlePeopleRelationMapper.deletByTypeId(instance.getId(),CirclePeopleRelationTypeEnum.SELECTION_TYPE.getType() );
        }
        return info;
    }

    @Override
    public void getTemplate() {
        CommonFileUtils.exportFile(RequestHolder.getResponse(), Global.TEMPLATE_DIR, "鲜沐严选模板.xls");
    }

    @Override
    public AjaxResult upload(Integer id, MultipartFile file) {
        Workbook workbook;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            List<SeriesOfSkuVO> voList = new ArrayList<>();
            for (int i = 1; i < sheet.getLastRowNum(); i++) {
                Integer sort = null;
                Row row = sheet.getRow(i);
                String sku = ExcelUtils.getCellValueStr(row.getCell(0));
                Cell cell = row.getCell(1);
                cell.setCellType(CellType.STRING);
                String sv = cell.getStringCellValue();
                if(StringUtils.isNotBlank(sv)){
                    sort = Integer.valueOf(sv);
                }

                InventoryVO inventoryVO = inventoryMapper.selectSkuType(sku);
                if(inventoryVO == null){
                    throw new DefaultServiceException("导入异常，sku：" + sku + "不存在");
                }

                SeriesOfSkuVO vo = new SeriesOfSkuVO();
                vo.setSeriesType(SeriesTypeEnum.SELECTION.getType());
                vo.setSeriesId(id);
                vo.setSku(sku);
                if(StringUtils.isNotBlank(sv)){
                    vo.setSort(sort);
                }
                vo.setPdName(inventoryVO.getPdName());
                vo.setWeight(inventoryVO.getWeight());
                voList.add(vo);
            }
            voList.sort(Comparator.comparing(SeriesOfSku::getSort));

            return AjaxResult.getOK(voList);
        } catch (IOException e) {
            throw new DefaultServiceException("导入异常");
        }
    }

    @Override
    public void autoUpdateSort() {
        logger.info("鲜沐严选排序自动更新开始");

        StrictSelectionVO query = new StrictSelectionVO();
        query.setAutoSort(true);
        List<StrictSelection> selectionList = strictSelectionMapper.selectByModel(query);

        for (StrictSelection selection : selectionList) {
            seriesService.autoUpdateSort(selection.getId());
        }

        logger.info("鲜沐严选排序自动更新结束");
    }

    @Override
    public String checkSelection(StrictSelectionVO instance) {
        List<SeriesOfAreaVO> seriesAreaList = instance.getSeriesAreaList();
        List<Integer> collect = seriesAreaList.stream().map(el -> el.getAreaNo()).collect(Collectors.toList());
        List<String> mechants = new ArrayList<>();
        if(instance.getRuleId()!=null){
            List<Integer> currentStrict = strictSelectionMapper.selectCurrentStrict(instance.getId(),collect,instance.getStartTime(), instance.getEndTime());
            for (Integer integer : currentStrict) {
                List<String> sameMids = circlePeopleService.checkParmas(integer, instance.getRuleId(), CirclePeopleRelationTypeEnum.SELECTION_TYPE.getType());
                if (sameMids.size() == 0) {
                    continue;
                } else {
                    mechants.addAll(sameMids);
                }
            }
        }
        if (mechants.size() != 0) {
            List<String> collect1 = mechants.stream().distinct().collect(Collectors.toList());
            return String.valueOf(collect1.size());
        }
        return null;
    }

    private String paramCheck(StrictSelectionVO instance) {

        if (!instance.getStartTime().isBefore(instance.getEndTime())) {
            throw new DefaultServiceException("结束时间不可小于等于开始时间");
        }

        //重复城市校验
        StrictSelectionVO vo = new StrictSelectionVO();
        vo.setStatus(0);
        List<StrictSelection> selections = strictSelectionMapper.selectByModel(vo);
        vo.setStatus(1);
        selections.addAll(strictSelectionMapper.selectByModel(vo));

        StringBuffer selectionIds = new StringBuffer();
        List<Integer> ids = new LinkedList<>();
        List<String> mechants = new ArrayList<>();
        List<String> areaName = new ArrayList<>();
        for (StrictSelection selection : selections) {
            if (Objects.equals(instance.getId(), selection.getId())) {
                continue;
            }
            //校验时间是否有交集 有交集 会报错
            if (DateUtils.overlapped(DateUtils.buildSlot(instance.getStartTime(), instance.getEndTime()),
                    DateUtils.buildSlot(selection.getStartTime(), selection.getEndTime()))) {
                List<SeriesOfAreaVO> areaVOS = seriesService.queryAreaSeries(SeriesTypeEnum.SELECTION, selection.getId());
                areaVOS.forEach(el -> {
                    if (instance.getSeriesAreaList().stream().anyMatch(oo -> Objects.equals(oo.getAreaNo(), el.getAreaNo()))) {
                        areaName.add(el.getAreaName());
                    }
                    if (!CollectionUtils.isEmpty(areaName)) {
                        throw new DefaultServiceException("只可创建完全相同时间、完全不同时间的活动");
                    }
                });
            }
        }
        List<SeriesOfAreaVO> seriesAreaList = instance.getSeriesAreaList();
        List<Integer> collect = seriesAreaList.stream().map(el -> el.getAreaNo()).collect(Collectors.toList());
        if(instance.getRuleId()!=null){
            int i = circlePeopleRelationMapper.selectExist(instance.getId(), instance.getRuleId(),collect,
                    CirclePeopleRelationTypeEnum.SELECTION_TYPE.getType(), instance.getStartTime(), instance.getEndTime());
            if (i > 0) {
                throw new DefaultServiceException("当前时间段,已存在该圈人规则严选");
            }
        }else{
            //取当前城市，所以生效的严选
            List<Integer> currentStrict = strictSelectionMapper.selectCurrentStrict(instance.getId(),collect,instance.getStartTime(), instance.getEndTime());
            if(!CollectionUtils.isEmpty(currentStrict)){
                int i = circlePeopleRelationMapper.selectByTypeIds(currentStrict, CirclePeopleRelationTypeEnum.SELECTION_TYPE.getType());
                if(currentStrict.size() > i){
                    throw new DefaultServiceException("当前时间段,已存在无圈人规则严选");
                }
            }
        }
//        if(instance.getRuleId()!=null){
//            List<Integer> currentStrict = strictSelectionMapper.selectCurrentStrict(instance.getId(),collect,instance.getStartTime(), instance.getEndTime());
//            for (Integer integer : currentStrict) {
//                List<String> sameMids = circlePeopleService.checkParmas(integer, instance.getRuleId(), CirclePeopleRelationTypeEnum.SELECTION_TYPE.getType());
//                if (sameMids.size() == 0) {
//                    continue;
//                } else {
//                    mechants.addAll(sameMids);
//                    selectionIds.append("严选" + integer + " ");
//                    ids.add(integer);
//                    continue;
//                }
//            }
//        }
//
//        if (selectionIds.length() != 0) {
//            List<String> collects = mechants.stream().distinct().collect(Collectors.toList());
//            String endMechants = collects.stream().collect(Collectors.joining(","));
//            StrictSelection info = strictSelectionMapper.selectFirstTime(ids);
//            return "「" + endMechants + "」同时存在「" + selectionIds + "」中，则对应客户只可见「严选" + info.getId() + "」场次";
//        }
        return null;
    }
}



