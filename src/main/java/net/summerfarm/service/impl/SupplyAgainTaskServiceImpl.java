package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.*;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.*;
import net.summerfarm.facade.wms.CabinetInventoryFacade;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.DTO.MailWorkBookDTO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.wms.StockTaskItemCabinetOccupyDO;
import net.summerfarm.model.domain.wms.StockTaskProcessLogisticsOrderDO;
import net.summerfarm.model.domain.wms.StockTaskWaveConfig;
import net.summerfarm.model.input.StockTaskSaleOutReq;
import net.summerfarm.model.param.StockTaskProcessLogisticsOrder;
import net.summerfarm.model.vo.StockTaskItemDetailVO;
import net.summerfarm.model.vo.StockTaskItemVO;
import net.summerfarm.model.vo.StockTaskResult;
import net.summerfarm.module.wms.biz.factory.StockTaskItemDetailUpdateFactory;
import net.summerfarm.module.wms.biz.service.StockTaskOrderCancelService;
import net.summerfarm.module.wms.infrastructure.repository.StockTaskItemCabinetOccupyRepository;
import net.summerfarm.module.wms.infrastructure.repository.StockTaskWaveRepository;
import net.summerfarm.service.*;
import net.summerfarm.service.tms.TmsTrunkService;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetInventoryOccupyReduceReqDTO;
import net.summerfarm.wms.inventory.resp.cabinetInventory.CabinetInventoryOccupyReduceBatchRespDTO;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static net.summerfarm.contexts.Global.*;

/**
 * <AUTHOR> ct
 * create at:  2020/12/11  14:20
 */
@Service
public class SupplyAgainTaskServiceImpl extends BaseService implements SupplyAgainTaskService, StockTaskStrategy {



    @Resource
    StockTaskMapper stockTaskMapper;
    @Resource
    StockTaskItemMapper stockTaskItemMapper;
    @Resource
    StoreRecordMapper storeRecordMapper;
    @Resource
    StockTaskItemDetailMapper stockTaskItemDetailMapper;
    @Resource
    StockTaskProcessMapper stockTaskProcessMapper;
    @Resource
    BaseService baseService;
    @Resource
    AreaStoreService areaStoreService;
    @Resource
    WMSBuilderService wmsBuilderService;
    @Resource
    QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    PurchasesConfigService purchasesConfigService;
    @Resource
    StockTaskProcessDetailMapper stockTaskProcessDetailMapper;
    @Resource
    AreaStoreMapper areaStoreMapper;
    @Resource
    private TmsTrunkService tmsTrunkService;

    private SupplyAgainTaskService selfService;

    @Resource
    private StockTaskProcessLogisticsOrderMapper logisticsOrderMapper;

    @Resource
    private StockTaskMsgService stockTaskMsgService;

    @Resource
    private StockTaskProcessOrderSkuService stockTaskProcessOrderSkuService;

    @Resource
    private SkuConvertService skuConvertService;
    @Resource
    private WarehouseConfigService warehouseConfigService;
    @Resource
    private StockTaskItemCabinetOccupyRepository stockTaskItemCabinetOccupyRepository;
    @Resource
    private CabinetInventoryFacade cabinetInventoryFacade;
    @Resource
    private StockTaskOrderCancelService stockTaskOrderCancelService;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    private StockTaskWaveRepository stockTaskWaveRepository;

    @PostConstruct
    private void setSelf() {
        selfService = getContext().getBean(SupplyAgainTaskService.class);
    }

    protected final Logger logger = LoggerFactory.getLogger(SupplyAgainTaskServiceImpl.class);

    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    @Override
    public AjaxResult inOutStore(Integer type, String data) {

        if (!Objects.equals(StoreRecordType.SUPPLY_AGAIN_TASK.getId(),type)) {
            return AjaxResult.getErrorWithMsg("出库类型错误!");
        }
        List<StockTaskSaleOutReq> stockTaskSaleOutReqs = JSONObject.parseArray(data, StockTaskSaleOutReq.class);

        if (CollectionUtils.isEmpty(stockTaskSaleOutReqs)) {
            throw new DefaultServiceException("参数有误!");
        }

        StockTask task = stockTaskMapper.selectByPrimaryKey(stockTaskSaleOutReqs.get(0).getStockTaskId());
        if (task == null) {
            throw new DefaultServiceException("参数有误!");
        }
        if (task.getState().equals(StockTaskState.FINISH.getId())) {
            throw new DefaultServiceException("已完成的任务不可再次操作!");
        }

        if (OptionFlagUtil.hasValue(task.getOptionFlag(), OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode())) {
            StockTaskWaveConfig waveConfig = stockTaskWaveRepository.queryWaveConfig(task.getAreaNo(), task.getOutStoreNo());
            WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsService.selectByStoreNo(task.getOutStoreNo());
            if (waveConfig != null &&
                    net.summerfarm.common.util.StringUtils.isNotBlank(waveConfig.getWaveTime()) &&
                    warehouseLogisticsCenter != null &&
                    net.summerfarm.common.util.StringUtils.isNotBlank(warehouseLogisticsCenter.getCloseTime())) {
                LocalTime nowTime = LocalTime.now();
                LocalTime closeTime = LocalTime.parse(warehouseLogisticsCenter.getCloseTime());
                LocalTime closePlus10Time = closeTime.plusMinutes(10);
                LocalTime waveTime = LocalTime.parse(waveConfig.getWaveTime());
                if (waveTime.isAfter(closeTime)) {
                    logger.error("出库波次配置异常，波次时间在截单时间后 {} {} \n1", waveTime, closeTime);
                    return AjaxResult.getErrorWithMsg("出库波次配置异常，波次时间在截单时间后");
                }
                // 当前时间在波次前
                if (nowTime.isAfter(waveTime) && nowTime.isBefore(closePlus10Time)) {
                    return AjaxResult.getErrorWithMsg("请在截单时间" + closeTime + "过10分钟后操作波次出库任务!");
                }

                // 初始化单据占用
                stockTaskOrderCancelService.handleStockTaskWaveOccupyInit(task.getId());
            }
        }

        AjaxResult<Boolean> result = tmsTrunkService.haveTrunkMsg(task.getId());
        if(!result.getData()){
            throw new DefaultServiceException("未选择车辆");
        }
        String adminName = baseService.getAdminName();
        Long tenantId = SaasThreadLocalUtil.getTenantId();
        if (Objects.nonNull(tenantId) && tenantId > BaseConstant.XIANMU_TENANT_ID) {
            adminName = SaasThreadLocalUtil.getTenantAccountName();
        }

        //校验出库数据
        stockTaskSaleOutReqs.stream().forEach(x ->{
            List<StockTaskItemDetailVO> stockTaskItemDetailVOS = x.getStockTaskItemDetailVOS();
            if(!CollectionUtils.isEmpty(stockTaskItemDetailVOS)){
                stockTaskItemDetailVOS.stream().forEach(y ->{
                    Integer quantity = y.getQuantity();
                    if(quantity < 0){
                        throw new DefaultServiceException("出库数量不能小于0");
                    }
                });
            }
        });

        //新增出库单
        StockTaskProcess process = new StockTaskProcess();
        process.setStockTaskId(stockTaskSaleOutReqs.get(0).getStockTaskId());
        process.setAddtime(LocalDateTime.now());
        process.setRecorder(adminName);
        process.setTenantId(SaasThreadLocalUtil.getTenantId());
        stockTaskProcessMapper.insert(process);
        // 新增物流信息
        if (SaasThreadLocalUtil.isSaasRequest()) {
            StockTaskProcessLogisticsOrder logisticsOrder = stockTaskSaleOutReqs.get(0).getLogisticsOrder();
            StockTaskProcessLogisticsOrderDO logisticsOrderDO = StockTaskProcessLogisticsOrderDO.builder()
                    .stockTaskProcessId(process.getId().longValue())
                    .deliveryType(logisticsOrder.getDeliveryType())
                    .deliveryOrderNo(logisticsOrder.getDeliveryOrderNo())
                    .company(logisticsOrder.getCompany())
                    .remark(logisticsOrder.getRemark())
                    .creator(process.getRecorder())
                    .operator(process.getRecorder())
                    .gmtCreated(new Date())
                    .isDeleted(0)
                    .lastVer(1)
                    .build();
            logisticsOrderMapper.insert(logisticsOrderDO);
        }

        //排序
        stockTaskSaleOutReqs.sort(Comparator.comparing(StockTaskSaleOutReq::getSku));
        for (StockTaskSaleOutReq req : stockTaskSaleOutReqs) {
            selfService.handleStockTaskSaleOutReq(req,adminName,task,process.getId());
        }

        boolean close = closeStockTask(stockTaskSaleOutReqs.get(0).getStockTaskId());
        StockTask update = new StockTask();
        update.setId(stockTaskSaleOutReqs.get(0).getStockTaskId());
        StockTaskItem select = new StockTaskItem();
        select.setStockTaskId(stockTaskSaleOutReqs.get(0).getStockTaskId());
        List<StockTaskItem> items = stockTaskItemMapper.select(select);
        update.setState(StockTaskState.PART_IN_OUT.getId());
        if(close){
            update.setState(StockTaskState.FINISH.getId());
            tmsTrunkService.sendFinishStock(stockTaskSaleOutReqs.get(0).getStockTaskId());
            logger.info("任务编号:" + stockTaskSaleOutReqs.get(0).getStockTaskId() + "关闭");
        }
        update.setUpdatetime(LocalDateTime.now());
        stockTaskMapper.update(update);
        items.sort(Comparator.comparing(StockTaskItem::getSku));
        for (StockTaskItem item : items) { //修改sku状态、采购预警
          areaStoreService.updateAreaStoreStatus(stockTaskSaleOutReqs.get(0).getAreaNo(), item.getSku());
          purchasesConfigService.msgArrival(stockTaskSaleOutReqs.get(0).getAreaNo(), item.getSku());
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                stockTaskMsgService.handleStockTaskSkuOutMsg(process.getId());
            }
        });
        logger.info("{}添加库存记录:{}", adminName, type);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    public void closeTask(StockTask stockTask) {
        StockTaskItem select = new StockTaskItem();
        select.setStockTaskId(stockTask.getId());
        StockTask queryStockTask = stockTaskMapper.selectByPrimaryKey(stockTask.getId());
        Integer areaNo = queryStockTask.getAreaNo();
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        List<StockTaskItem> stockTaskItems = stockTaskItemMapper.select(select);
        if (!CollectionUtils.isEmpty(stockTaskItems)) {
            //排序
            stockTaskItems.sort(Comparator.comparing(StockTaskItem::getSku));
            for (StockTaskItem item : stockTaskItems) {
                //校验所有补货任务是否出库,否将未出的冻结解除并更新虚拟库存
                String sku = item.getSku();
                if(item.getQuantity() > item.getActualQuantity()){
                    Integer quantity = item.getQuantity() - item.getActualQuantity();
                    updateStock(sku,quantity,areaNo,stockTask.getId() + "", recordMap);
                }
                areaStoreService.updateAreaStoreStatus(stockTask.getAreaNo(), item.getSku());
            }
        }
        quantityChangeRecordService.insertRecord(recordMap);

        // 释放订单冻结剩余库位库存
        stockTaskOrderCancelService.handleCabinetReleaseByStockTaskIdFinish(stockTask.getId(), getAdminName());
    }

    @Override
    public void stockTaskDetailDownload(StockTask stockTask) {

    }

    /**
     * 生成excel表
     *
     * @param areaNo   库存仓
     * @param type     出库任务类型
     * @param taskList 出库任务列表
     * @return 生成的excel(文件名 + 文件实体)
     */
    @Override
    public List<MailWorkBookDTO> generateExcel(Integer areaNo, Integer type, List<StockTask> taskList) {
        // 返回的结果list
        List<MailWorkBookDTO> resultList = new ArrayList<>();
        // 根据不同出库任务的规则做最细粒度的拆分
        Map<String, List<StockTask>> tasks = taskList.stream()
                .collect(groupingBy(a -> a.getOutStoreNo() + SEPARATING_SYMBOL +
                        LocalDateTime.of(a.getExpectTime().toLocalDate(), LocalTime.MIN).toInstant(ZoneOffset.ofHours(8)).toEpochMilli()));
        for (Map.Entry<String, List<StockTask>> entry : tasks.entrySet()) {
            Workbook workbook = new HSSFWorkbook();
            Sheet loadingSheet = workbook.createSheet("销售出库");
            List<StockTask> taskValue = entry.getValue();
            // 查询出库任务对应的item信息
            List<Integer> taskIdList = taskValue.stream().map(StockTask::getId).collect(Collectors.toList());
            List<StockTaskItemVO> itemVOList = stockTaskItemMapper.selectByStockTaskIdList(taskIdList);
            if (!CollectionUtils.isEmpty(itemVOList)) {
                wmsBuilderService.batchBuildWMSInfo(itemVOList);
                itemVOList = wmsBuilderService.sortedFruitPriority(itemVOList);
            }

            // 设置实际出库数量
            itemVOList.forEach(item -> {
                List<StockTaskItemDetailVO> stockTaskItemDetailVOS = item.getStockTaskItemDetailVOS();
                int quantity = 0;
                if (!CollectionUtils.isEmpty(stockTaskItemDetailVOS)) {
                    quantity = stockTaskItemDetailVOS.stream().mapToInt(StockTaskItemDetailVO::getQuantity).sum();
                }
                item.setDetailQuantity(quantity);
            });

            // 合并SKU信息
            List<StockTaskItemVO> itemVOS = new ArrayList<>();
            if (!CollectionUtils.isEmpty(itemVOList)) {
                itemVOS = new ArrayList<>(itemVOList.stream().collect(Collectors.toMap(StockTaskItem::getSku, a -> a, (o1, o2) -> {
                    o1.setQuantity(o1.getQuantity() + o2.getQuantity());
                    o1.setDetailQuantity(o1.getDetailQuantity() + o2.getDetailQuantity());
                    return o1;
                })).values());
            }
            // 加载SKU信息
            loadingGoods(loadingSheet, type, taskValue, itemVOS);
            // 生成文件名
            StockTask stockTask = taskValue.get(0);
            String typeName = StockTaskType.getNameById(type);
            String fileName = stockTask.getOutStoreNo() == null ?
                    Global.warehouseMap.get(stockTask.getAreaNo())
                            + typeName
                            + WHITE_SPACE
                            + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                            + SEPARATING_SYMBOL
                            + System.currentTimeMillis()
                            + ".xls" :
                        Global.warehouseMap.get(stockTask.getAreaNo())
                                + "至" + Global.storeMap.get(stockTask.getOutStoreNo())
                                + typeName
                                +WHITE_SPACE
                                + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                                + SEPARATING_SYMBOL
                                + System.currentTimeMillis()
                                + ".xls";
            MailWorkBookDTO mailWorkBookDTO = new MailWorkBookDTO(fileName, workbook);
            resultList.add(mailWorkBookDTO);
        }
        return resultList;
    }

    /**
     * 录入出库任务SKU信息
     * @param loadingSheet 装货单sheet
     * @param type 出库任务类型
     * @param stockTaskList 出库任务list
     * @param itemVOS 出库任务条目list
     */
    private void loadingGoods(Sheet loadingSheet, Integer type, List<StockTask> stockTaskList, List<StockTaskItemVO> itemVOS) {
        int rowIndex = 0;
        Row first = loadingSheet.createRow(rowIndex++);
        first.createCell(0).setCellValue(StockTaskType.getNameById(type) + ":");
        StringBuilder ids = new StringBuilder();
        stockTaskList.forEach(st -> {
            ids.append(st.getId()).append(SEPARATING_SYMBOL);
        });
        ids.deleteCharAt(ids.length() -1);
        first.createCell(1).setCellValue(ids.toString());

        Row second = loadingSheet.createRow(rowIndex++);
        second.createCell(0).setCellValue("出库仓：");
        second.createCell(1).setCellValue(Global.warehouseMap.get(stockTaskList.get(0).getAreaNo()));
        second.createCell(2).setCellValue("城配仓：");
        second.createCell(3).setCellValue(Global.storeMap.get(stockTaskList.get(0).getOutStoreNo()));

        Row three = loadingSheet.createRow(rowIndex++);
        three.createCell(0).setCellValue("预计出库时间：");
        three.createCell(1).setCellValue(Objects.isNull(stockTaskList.get(0).getExpectTime()) ? " " : stockTaskList.get(0).getExpectTime().format(DateTimeFormatter.ofPattern(DateUtils.LONG_DATE_FORMAT)));

        Row four = loadingSheet.createRow(rowIndex++);
        four.createCell(0).setCellValue("实际出库时间：");
        four.createCell(1).setCellValue(stockTaskList.get(0).getUpdatetime() != null ? stockTaskList.get(0).getUpdatetime().format(DateTimeFormatter.ofPattern(DateUtils.LONG_DATE_FORMAT)) : "");

        //中间空一行
        rowIndex++;
        Row title = loadingSheet.createRow(rowIndex++);
        String[] titleNames = new String[]{"一级类目", "二级类目", "sku", "商品名称", "规格", "商品归属", "储存区域", "包装", "应出数量", "实发数量"};
        for (int i = 0; i < titleNames.length; i++) {
            title.createCell(i).setCellValue(titleNames[i]);
        }
        int index = rowIndex;
        for (StockTaskItemVO itemVO : itemVOS) {
            Row row = loadingSheet.createRow(index);
            row.createCell(0).setCellValue(itemVO.getFirstLevelCategory());
            row.createCell(1).setCellValue(itemVO.getSecondLevelCategory());
            row.createCell(2).setCellValue(itemVO.getSku());
            row.createCell(3).setCellValue(itemVO.getPdName() + SkuUtil.getExtTypeStr(itemVO.getExtType()));
            row.createCell(4).setCellValue(itemVO.getWeight());
            String productsType = net.summerfarm.common.util.StringUtils.productType(itemVO.getSkuType(), itemVO.getNameRemakes());
            row.createCell(5).setCellValue(productsType);
            row.createCell(6).setCellValue(itemVO.getStorageArea());
            row.createCell(7).setCellValue(itemVO.getUnit());
            row.createCell(8).setCellValue(itemVO.getQuantity());
            row.createCell(9).setCellValue(itemVO.getDetailQuantity());
            index++;
        }
    }

    @Override
    public void stockTaskBatchDetailDownload(List<StockTask> stockTaskList) {

    }

    @Override
    public AjaxResult stockTaskDetail(StockTaskResult result) {
        List<StockTaskItemVO> stockTaskItemVOS = stockTaskItemMapper.selectByStockTaskId(result.getId());
        // build sku的一些WMS用的外部信息
        wmsBuilderService.batchBuildWMSInfo(stockTaskItemVOS);
        // saas端转换skuId
        skuConvertService.setSaasSkuIdForList(stockTaskItemVOS);
        result.setList(stockTaskItemVOS);

        boolean openCabinetManagement = warehouseConfigService.openCabinetManagement(result.getAreaNo());
        result.setOpenCabinetManagement(openCabinetManagement);
        if (openCabinetManagement){
            List<String> skuCodeList = stockTaskItemVOS.stream()
                    .map(StockTaskItemVO::getSku)
                    .distinct()
                    .collect(Collectors.toList());

            List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOS = stockTaskItemCabinetOccupyRepository
                    .selectListByStockTaskIdOccupyed(result.getId(), skuCodeList);
            Map<String, List<StockTaskItemCabinetOccupyDO>> skuQualityDateOrderMap = stockTaskItemCabinetOccupyDOS.stream()
                    .collect(Collectors.groupingBy(s -> s.getSku()));

            for (StockTaskItemVO stockTaskItemVO : stockTaskItemVOS) {
                List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOList = skuQualityDateOrderMap.get(
                        stockTaskItemVO.getSku());
                if (!CollectionUtils.isEmpty(stockTaskItemCabinetOccupyDOList)){
                    stockTaskItemVO.setCabinetOccupyDOList(stockTaskItemCabinetOccupyDOList);
                }
            }
        }
        return AjaxResult.getOK(result);
    }

    @Override
    public AjaxResult abnormalInStore(AbnormalRecord record, Integer type) {
        return null;
    }

    @Override
    public AjaxResult manualCloseStockTask(Integer stockTaskId,String closeReason, Integer type) {
        return null;
    }


    /**
    * 校验库存在库批次信息
    */
    public List<StockTaskItemDetailVO> checkItemDetail(List<StockTaskItemDetailVO> stockTaskItemDetails,String sku,Integer areaNo){
        HashMap<String, StockTaskItemDetailVO> map = new HashMap<>();
        stockTaskItemDetails.forEach(detail -> {
            LocalDate qualityDate = detail.getQualityDate();
            String listNo = detail.getListNo();
            Integer quantity = detail.getQuantity();
            String str = listNo + "-" + qualityDate;
            if(!StringUtils.isEmpty(map.get(str))){
                StockTaskItemDetailVO itemDetail = map.get(str);
                Integer actualOutQuantity = itemDetail.getQuantity();
                itemDetail.setQuantity(actualOutQuantity + quantity);
                map.put(str,itemDetail);
            } else {
                StockTaskItemDetailVO itemDetail = new StockTaskItemDetailVO();
                itemDetail.setQualityDate(qualityDate);
                itemDetail.setQuantity(quantity);
                itemDetail.setSku(detail.getSku());
                itemDetail.setListNo(listNo);
                map.put(str,itemDetail);
            }
        });
        List<StockTaskItemDetailVO> collect = map.values().stream().collect(Collectors.toList());
        collect.forEach(x ->{
            StoreRecord select = new StoreRecord();
            select.setBatch(x.getListNo());
            select.setSku(sku);
            select.setAreaNo(areaNo);
            select.setQualityDate(x.getQualityDate());
            StoreRecord lasted = storeRecordMapper.selectOne(select);
            if (lasted != null) {
                if (x.getQuantity() > lasted.getStoreQuantity()) {
                    throw new DefaultServiceException("sku:" + sku + "此批次库存仅剩：" + x.getListNo() + "-"
                            + x.getQualityDate() + "-" + lasted.getStoreQuantity());
                }
            }

        });
        return collect;
    }

    /**
    * 生成库存记录
    */
    private StoreRecord createNewStoreRecord(StoreRecord lasted,Integer type,Integer quantity,String remark,String adminName){
        StoreRecord storeRecord = new StoreRecord();
        storeRecord.setBatch(lasted.getBatch());
        storeRecord.setSku(lasted.getSku());
        storeRecord.setType(type);
        storeRecord.setQuantity(quantity);
        storeRecord.setUnit(lasted.getUnit());
        storeRecord.setRecorder(adminName);
        storeRecord.setRemark(remark);
        storeRecord.setUpdateTime(new Date());
        storeRecord.setQualityDate(lasted.getQualityDate());
        storeRecord.setAreaNo(lasted.getAreaNo());
        storeRecord.setProductionDate(lasted.getProductionDate());
        storeRecord.setStoreQuantity(lasted.getStoreQuantity() - quantity);
        storeRecord.setCost(lasted.getCost());
        storeRecord.setTenantId(lasted.getTenantId());
        return storeRecord;
    }

    /**
    * 出库详情
    */
    private StockTaskItemDetail createItemDetail(StockTaskItemDetail old,Integer id,String sku,Integer quantity){
        StockTaskItemDetail insert = new StockTaskItemDetail();
        insert.setCabinetCode(old.getCabinetCode());
        insert.setListNo(old.getListNo());
        insert.setStockTaskItemId(id);
        insert.setSku(sku);
        insert.setQualityDate(old.getQualityDate());
        insert.setQuantity(quantity);
        insert.setProductionDate(old.getProductionDate());
        insert.setRemark(old.getRemark());
        insert.setOutStoreQuantity(quantity);
        insert.setShouldQuantity(quantity);
        return insert;
    }

    /**
    * 是否完成出库
    */
    public boolean closeStockTask(Integer stockTaskId) {
        StockTaskItem select = new StockTaskItem();
        select.setStockTaskId(stockTaskId);
        List<StockTaskItem> items = stockTaskItemMapper.select(select);
        return items.stream().allMatch(item -> Objects.equals(item.getQuantity(), item.getActualQuantity()));
    }

    /**
    * 减冻结 加虚拟
    */
    public void updateStock(String sku, Integer quantity, Integer areaNo, String recordNo, Map<String, QuantityChangeRecord> recordMap) {

        //加虚拟库存
        areaStoreService.updateOnlineStockByStoreNo(true, quantity, sku, areaNo, SaleStockChangeTypeEnum.RECOVER_OUT,recordNo, recordMap, NumberUtils.INTEGER_ZERO);
        //减冻结库存
        areaStoreService.updateLockStockByStoreNo(-quantity, sku, areaNo, SaleStockChangeTypeEnum.RECOVER_OUT,recordNo, recordMap);

    }

    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    public void handleStockTaskSaleOutReq(StockTaskSaleOutReq req,String adminName,StockTask stockTask,Integer processId){

        List<StockTaskItemDetailVO> stockTaskItemDetails = req.getStockTaskItemDetailVOS();

        if (CollectionUtils.isEmpty(stockTaskItemDetails)) {
            return;
        }

        String sku = req.getSku();
        Integer type = stockTask.getType();
        Integer outQuantity = stockTaskItemDetails.stream().mapToInt(StockTaskItemDetail::getQuantity).sum(); //即将出(入)库数量
        StockTaskItem selectKey = new StockTaskItem();
        selectKey.setSku(req.getSku());
        selectKey.setStockTaskId(req.getStockTaskId());
        List<StockTaskItem> stockTaskItems = stockTaskItemMapper.select(selectKey);
        if (CollectionUtils.isEmpty(stockTaskItems)) {
            throw new DefaultServiceException("参数有误!");
        }
        StockTaskItem item = stockTaskItems.get(0);
        Integer areaNo = req.getAreaNo();
        if (outQuantity + item.getActualQuantity() > item.getQuantity()) {
            throw new DefaultServiceException("sku:" + req.getSku() + "出库数量大于任务数量");
        }
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        //出入库单详情
        List<StockTaskProcessDetail> processDetails = new ArrayList<>();

        Boolean openCabinetManagement = warehouseConfigService.openCabinetManagement(stockTask.getAreaNo());
        Map<String, List<StockTaskItemCabinetOccupyDO>> skuQualityDateOrderMap = new HashMap<>();
        if (openCabinetManagement){
            List<String> skuCodeList = stockTaskItemDetails.stream()
                    .map(StockTaskItemDetail::getSku)
                    .distinct()
                    .collect(Collectors.toList());

            List<StockTaskItemCabinetOccupyDO> stockTaskItemCabinetOccupyDOS = stockTaskItemCabinetOccupyRepository
                    .selectListByStockTaskIdOccupyed(stockTask.getId(), skuCodeList);
            skuQualityDateOrderMap = stockTaskItemCabinetOccupyDOS.stream()
                    .collect(Collectors.groupingBy(s -> s.getSku() + "_" + DateUtil.formatYmdDate(s.getQualityDate())));
        }

        if (openCabinetManagement) {
            for (StockTaskItemDetail stockTaskItemDetail : stockTaskItemDetails) {
                List<StockTaskItemCabinetOccupyDO> skuOccupyList = skuQualityDateOrderMap.get(
                        req.getSku() + "_" + DateUtil.formatYmdDate(stockTaskItemDetail.getQualityDate()));
                if (CollectionUtils.isEmpty(skuOccupyList)) {
                    throw new DefaultServiceException("sku:" + req.getSku() + "没有锁定库位库存，请稍后进行重试");
                }
                if (stockTaskItemDetail.getQuantity() <= 0) {
                    throw new DefaultServiceException("sku:" + req.getSku() + "请求出库数量小于等于0");
                }

                // 精细化仓批次、库位库存处理
                CabinetInventoryOccupyReduceReqDTO occupyReduceReqDTO = StockTaskItemDetailUpdateFactory.newInstance(stockTask, stockTaskItemDetail, getAdminName());
                List<CabinetInventoryOccupyReduceBatchRespDTO> occupyReduceBatchRespDTOList = cabinetInventoryFacade.occupyReduceCabinetInventory(occupyReduceReqDTO);

                // 根据库位信息查询批次库存信息
                List<StockTaskItemDetail> batchItemDetailList = StockTaskItemDetailUpdateFactory.matchStoreRecordQuantity(
                        stockTaskItemDetail, occupyReduceBatchRespDTOList);
                batchItemDetailList.forEach(batchItemDetail ->
                        handleStoreRecord(req, adminName, type, areaNo, stockTaskItemDetail,
                                batchItemDetail.getQuantity(), batchItemDetail.getListNo())
                );

                // 处理库位占用更新
                stockTaskItemCabinetOccupyRepository.handlerCabinetOccupyItem(skuOccupyList, occupyReduceBatchRespDTOList);

                for (CabinetInventoryOccupyReduceBatchRespDTO cabinetInventoryOccupyReduceBatchRespDTO : occupyReduceBatchRespDTOList) {
                    //出库单详情
                    StockTaskProcessDetail processDetail = new StockTaskProcessDetail();
                    processDetail.setSku(req.getSku());
                    processDetail.setListNo(cabinetInventoryOccupyReduceBatchRespDTO.getBatchNo());
                    processDetail.setQuantity(cabinetInventoryOccupyReduceBatchRespDTO.getOccupyReduceQuantity());
                    processDetail.setQualityDate(DateUtil.toLocalDate(cabinetInventoryOccupyReduceBatchRespDTO.getQualityDate()));
                    processDetail.setProductionDate(DateUtil.toLocalDate(cabinetInventoryOccupyReduceBatchRespDTO.getProduceDate()));
                    processDetail.setRemark(stockTaskItemDetail.getRemark());
                    processDetail.setStockTaskProcessId(processId);
                    processDetails.add(processDetail);
                }
            }
        } else {
            for (StockTaskItemDetail stockTaskItemDetail : stockTaskItemDetails) {
                Integer quantity = stockTaskItemDetail.getQuantity(); // 出库数量
                handleStoreRecord(req, adminName, type, areaNo, stockTaskItemDetail, quantity,
                        stockTaskItemDetail.getListNo());


                //出库单详情
                StockTaskProcessDetail processDetail = new StockTaskProcessDetail();
                processDetail.setSku(req.getSku());
                processDetail.setListNo(stockTaskItemDetail.getListNo());
                processDetail.setQuantity(quantity);
                processDetail.setQualityDate(stockTaskItemDetail.getQualityDate());
                processDetail.setProductionDate(stockTaskItemDetail.getProductionDate());
                processDetail.setRemark(stockTaskItemDetail.getRemark());
                processDetail.setStockTaskProcessId(processId);
                processDetails.add(processDetail);
            }
        }

        for (StockTaskItemDetail stockTaskItemDetail : stockTaskItemDetails) {

            Integer quantity = stockTaskItemDetail.getQuantity(); // 出库数量

            //更新出库任务条目详情
            StockTaskItemDetail selectKeys = new StockTaskItemDetail();
            selectKeys.setCabinetCode(stockTaskItemDetail.getCabinetCode());
            selectKeys.setListNo(stockTaskItemDetail.getListNo());
            selectKeys.setStockTaskItemId(req.getId());
            selectKeys.setSku(req.getSku());
            selectKeys.setQualityDate(stockTaskItemDetail.getQualityDate());

            StockTaskItemDetail record = stockTaskItemDetailMapper.selectOne(selectKeys);
            //新增详情
            if (record == null) {
                StockTaskItemDetail insert = createItemDetail(stockTaskItemDetail, req.getId(), req.getSku(), quantity);
                stockTaskItemDetailMapper.insert(insert);
                //修改详情
            } else {
                StockTaskItemDetail update = new StockTaskItemDetail();
                update.setId(record.getId());
                update.setQuantity(record.getQuantity() + quantity);
                update.setOutStoreQuantity(quantity + record.getOutStoreQuantity());
                stockTaskItemDetailMapper.update(update);
            }
        }
        //更新已出库数量
        StockTaskItem update = new StockTaskItem();
        update.setId(item.getId());
        update.setActualQuantity(outQuantity + item.getActualQuantity());
        stockTaskItemMapper.update(update);
        // 波次出库任务不操作冻结库存，可能存在销售未到货退款释放冻结情况
        if (!OptionFlagUtil.hasValue(stockTask.getOptionFlag(), OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode())) {
            // 释放冻结库存
            areaStoreService.updateLockStockByWarehouseNo(-outQuantity, req.getSku(), stockTask.getOutStoreNo(), stockTask.getAreaNo(), SaleStockChangeTypeEnum.RECOVER_OUT, req.getStockTaskId() + "", recordMap);
        } else {
            stockTaskOrderCancelService.handleStockTaskWaveOccupyReduce(
                    stockTask.getId(), sku, outQuantity);
        }
        //同步仓库库存
        areaStoreService.updateStoreStockByWarehouseNo(-outQuantity, req.getSku(), stockTask.getAreaNo(), SaleStockChangeTypeEnum.RECOVER_OUT, SaleStockChangeTypeEnum.RECOVER_OUT.ordinal(), req.getStockTaskId() + "", recordMap );
        //插入数据
        quantityChangeRecordService.insertRecord(recordMap);

        // 批次生成出库单
        stockTaskProcessDetailMapper.insertBatch(processDetails);
        // 出库单订单明细处理&回写任务订单明细数量
        ProcessOrderSkuSaveCmd saveCmd = ProcessOrderSkuSaveCmd.builder()
                .stockTaskProcessId(processId)
                .sku(sku)
                .quantity(outQuantity).build();
        stockTaskProcessOrderSkuService.saveStockTaskProcessOrderSku(saveCmd);

    }

    private void handleStoreRecord(StockTaskSaleOutReq req, String adminName, Integer type, Integer areaNo,
                                   StockTaskItemDetail stockTaskItemDetail, Integer quantity, String listNo) {
        StoreRecord select = new StoreRecord();
        select.setBatch(listNo);
        select.setSku(req.getSku());
        select.setAreaNo(areaNo);
        select.setQualityDate(stockTaskItemDetail.getQualityDate());
        String remark = stockTaskItemDetail.getRemark();
        StoreRecord lasted = storeRecordMapper.selectOne(select);
        if (lasted != null) {
            if (quantity > lasted.getStoreQuantity()) {
                throw new DefaultServiceException("sku:" + req.getSku() + "此批次库存仅剩：" + stockTaskItemDetail.getListNo() + "-"
                        + stockTaskItemDetail.getQualityDate() + "-" + lasted.getStoreQuantity());
            }
        } else {
            throw new DefaultServiceException("当前仓不存在采购批次" + stockTaskItemDetail.getListNo() + "保质期为" + stockTaskItemDetail.getQualityDate() + "的" + req.getSku());
        }

        //新增库存变化记录
        StoreRecord storeRecord = createNewStoreRecord(lasted, type, quantity, remark, adminName);
        storeRecordMapper.insert(storeRecord);
    }
}
