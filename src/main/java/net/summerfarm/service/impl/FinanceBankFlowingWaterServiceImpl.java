package net.summerfarm.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import io.swagger.models.auth.In;
import net.summerfarm.biz.finance.config.FinanceConfig;
import net.summerfarm.cmb.common.utils.ChinaMerchantsBankHandler;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.qiNiu.Auth;
import net.summerfarm.common.util.qiNiu.UploadTokenFactory;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.QiNiuConstant;
import net.summerfarm.enums.CommonNumbersEnum;
import net.summerfarm.enums.FileDownloadRecordEnum;
import net.summerfarm.enums.FinanceBankFlowingWaterEnum;
import net.summerfarm.enums.FinanceReceiptPayTypeEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.DTO.BillOfChinaMerchantsBankDTO;
import net.summerfarm.model.domain.FileDownloadRecord;
import net.summerfarm.model.domain.FinanceBankFlowingWater;
import net.summerfarm.model.domain.FinanceBankFlowingWaterConfig;
import net.summerfarm.model.domain.RechargePic;
import net.summerfarm.model.domain.easyexcel.CashSettlementMainTableExcel;
import net.summerfarm.model.domain.easyexcel.CashSettlementSubTableExcel;
import net.summerfarm.model.domain.easyexcel.FinanceBankFlowingWaterExcel;
import net.summerfarm.model.input.FinanceBankFlowingWaterInput;
import net.summerfarm.model.input.RechargeQuery;
import net.summerfarm.model.vo.*;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;

import net.summerfarm.service.FinanceBankFlowingWaterConfigService;
import net.summerfarm.service.FinanceBankFlowingWaterService;
import net.xianmu.bms.client.receipt.provider.FinanceReceiptQueryProvider;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @title: FinanceBankFlowingWaterServiceImpl
 * @date 2022/6/2811:07
 */
@Service
public class FinanceBankFlowingWaterServiceImpl extends BaseService implements FinanceBankFlowingWaterService {

    @Resource
    private FinanceBankFlowingWaterMapper financeBankFlowingWaterMapper;

    @Resource
    private FinanceReceiptMapper financeReceiptMapper;

    @Resource
    private RechargeMapper rechargeMapper;

    @Resource
    private RechargePicMapper rechargePicMapper;

    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    private FinanceReceiptBillMapper financeReceiptBillMapper;

    @Resource
    private FileDownloadRecordMapper fileDownloadRecordMapper;

    @Resource
    private FinanceAccountingPeriodOrderMapper financeAccountingPeriodOrderMapper;

    @Resource
    private FinanceBankFlowingWaterConfigMapper financeBankFlowingWaterConfigMapper;

    @Resource
    private FinanceBankFlowingWaterConfigService financeBankFlowingWaterConfigService;

    @DubboReference
    private FinanceReceiptQueryProvider receiptQueryProvider;

    @Autowired
    MqProducer mqProducer;
    @Resource
    private FinanceConfig financeConfig;


    public static final String SUCCESS = "SUCCESS";

    @Override
    public AjaxResult list(int pageIndex, int pageSize, FinanceBankFlowingWaterInput financeBankFlowingWaterInput) {
        //状态多选
        if (!ObjectUtils.isEmpty(financeBankFlowingWaterInput.getClaimStatusList())) {
            String[] split = financeBankFlowingWaterInput.getClaimStatusList().split(",");
            List<Integer> list = new ArrayList<>();
            for (String str : split) {
                list.add(Integer.valueOf(str));
            }
            financeBankFlowingWaterInput.setStatusList(list);
        }
        financeBankFlowingWaterInput.setTransactionPayerQueryBlacklist(financeConfig.getTransactionPayerQueryBlacklist());
        PageHelper.startPage(pageIndex, pageSize);
        List<FinanceBankFlowingWaterVO> financeBankFlowingWaterList = financeBankFlowingWaterMapper.selectList(financeBankFlowingWaterInput);
        for (FinanceBankFlowingWaterVO flowing : financeBankFlowingWaterList) {
            DubboResponse<BigDecimal> receiptAmount = receiptQueryProvider.selectClaimAmountByFlowingWaterId(flowing.getId(), Boolean.TRUE);
            if (receiptAmount.isSuccess()){
                flowing.setAmountToBeCollected(flowing.getTransactionAmount().subtract(receiptAmount.getData()));
            }
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(financeBankFlowingWaterList));
    }

    @Override
    public AjaxResult cancel(Long id) {
        FinanceBankFlowingWater financeBankFlowingWater = financeBankFlowingWaterMapper.selectByPrimaryKey(id);
        if (!Objects.equals(financeBankFlowingWater.getClaimStatus(), FinanceBankFlowingWaterEnum.TO_BE_CLAIMED.ordinal())) {
            //如果不是待认领的流水不可撤销
            return AjaxResult.getErrorWithMsg("待认领的流水才可撤销");
        }
        FinanceBankFlowingWater flowingWater = new FinanceBankFlowingWater();
        flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.RESCINDED.ordinal());
        flowingWater.setId(id);
        flowingWater.setUpdater(getAdminName());
        financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult detail(Long id, Integer payType) {
        HashMap result = new HashMap(16);
        FinanceBankFlowingWater financeBankFlowingWater = financeBankFlowingWaterMapper.selectByPrimaryKey(id);
        result.put("financeBankFlowingWater",financeBankFlowingWater);
        //账单类型展示核销单
        if (Objects.equals(payType, FinanceReceiptPayTypeEnum.BILL.ordinal())) {
            List<FinanceReceiptVO> financeReceiptList = financeReceiptMapper.claimVerification(id);
            for (FinanceReceiptVO financeReceiptVO : financeReceiptList) {
                FinanceReceiptBillVO queryAmount = new FinanceReceiptBillVO();
                queryAmount.setFinanceReceiptId(financeReceiptVO.getId());
                FinanceReceiptBillVO receiptBillAmount = financeReceiptBillMapper.countReceiptBillAmount(queryAmount);
                if (ObjectUtils.isEmpty(receiptBillAmount)){
                    continue;
                }
                financeReceiptVO.setWriteOffAmount(receiptBillAmount.getWrittenOffAmount());
                financeReceiptVO.setOtherAmount(receiptBillAmount.getOtherAmount());
                List<FinanceReceiptBillVO> financeReceiptBilList = financeReceiptBillMapper.selectByReceiptIdList(financeReceiptVO.getId());
                BigDecimal totalOtherAmount = BigDecimal.ZERO;
                for (FinanceReceiptBillVO receiptBill : financeReceiptBilList) {
                    FinanceAccountingPeriodOrderVO periodOrder = financeAccountingPeriodOrderMapper.selectByIdInfo(receiptBill.getFinanceOrderId());
                    // 收款单-账单未核销金额 = 应收总金额-核销金额
                    receiptBill.setUnWrittenOffAmount(receiptBill.getReceivableAmount().subtract(periodOrder.getWriteOffAmount()));
                    // 其他收款总金额
                    totalOtherAmount = totalOtherAmount.add(receiptBill.getOtherAmount());
                }
                financeReceiptVO.setOtherAmount(totalOtherAmount);
                financeReceiptVO.setBillList(financeReceiptBilList);
                financeReceiptVO.setBillList(financeReceiptBilList);
            }
            //已经认领的金额（待核销和已核销）
            BigDecimal receivedAmount = financeReceiptMapper.selectByMoney(id);
            //待认领金额
            BigDecimal amountToBeCollected = financeBankFlowingWater.getTransactionAmount().subtract(receivedAmount);
            result.put("amountToBeCollected",amountToBeCollected);
            result.put("detail",financeReceiptList);
            result.put("financeBankFlowingWater",financeBankFlowingWater);
            return AjaxResult.getOK(result);
        }
        //鲜沐卡类型展示充值记录
        List<RechargeVO> rechargeList = rechargeMapper.selectWaterList(id);
        for (RechargeVO rechargeVO : rechargeList) {
            MerchantVO merchantVO = merchantMapper.selectMerchantByMid(rechargeVO.getmId());
            rechargeVO.setPhone(merchantVO.getPhone());
            rechargeVO.setMname(merchantVO.getMname());
            List<String> list = rechargePicMapper.selectById(rechargeVO.getId());
            rechargeVO.setPic(list);
        }
        //已经认领的金额（审核中和已经成功的充值记录）
        BigDecimal waterMoney = rechargeMapper.selectWater(id);
        //可认领总额
        BigDecimal amountToBeCollected = financeBankFlowingWater.getTransactionAmount().subtract(waterMoney);
        result.put("amountToBeCollected",amountToBeCollected);
        result.put("detail",rechargeList);
        result.put("financeBankFlowingWater",financeBankFlowingWater);
        return AjaxResult.getOK(result);
    }

    @Override
    public AjaxResult download(FinanceBankFlowingWaterInput financeBankFlowingWaterInput) {

        String fileName = getAdminName() + "收款单流水认领压缩文件" + System.currentTimeMillis() + ".zip";
        //下载中心数据建立
        insert(fileName);

        SpringUtil.getBean(FinanceBankFlowingWaterService.class).downloadMessage(fileName, financeBankFlowingWaterInput);

        return AjaxResult.getOK();
    }

    /**
     * 下载中心插入数据
     */
    private void insert(String fileName) {
        //下载中心留下下载中标记
        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
        fileDownloadRecord.setFileName(fileName);
        fileDownloadRecord.setStatus(CommonNumbersEnum.ZERO.getNumber());
        fileDownloadRecord.setAdminId(getAdminId());
        fileDownloadRecord.setType(FileDownloadRecordEnum.DOWNLOAD_CMB_COLLECTION_FLOW.ordinal());
        fileDownloadRecord.setCreateTime(LocalDateTime.now());
        fileDownloadRecordMapper.insert(fileDownloadRecord);
    }

    @Override
    @Async("downloadExecutor")
    public void downloadMessage(String fileName, FinanceBankFlowingWaterInput financeBankFlowingWaterInput) {
        logger.info("流水下载获取到download参数, financeBankFlowingWaterInput {}", financeBankFlowingWaterInput);
        if (ObjectUtils.isEmpty(financeBankFlowingWaterInput)) {
            logger.info("下载数据为空");
            return;
        }

        //根据文件名获得token
        Map<String, String> data = UploadTokenFactory.createToken(fileName, QiNiuConstant.DEFAULT_EXPIRES);

        if (ObjectUtils.isEmpty(data)) {
            return;
        }

        //状态多选
        if (!ObjectUtils.isEmpty(financeBankFlowingWaterInput.getClaimStatusList())) {
            String[] split = financeBankFlowingWaterInput.getClaimStatusList().split(",");
            List<Integer> list = new ArrayList<>();
            for (String str : split) {
                list.add(Integer.valueOf(str));
            }
            financeBankFlowingWaterInput.setStatusList(list);
        }
        List<FinanceBankFlowingWaterVO> financeBankFlowingWaterList = financeBankFlowingWaterMapper.selectList(financeBankFlowingWaterInput);
        logger.info("流水线下载, financeBankFlowingWaterList数据 {}", financeBankFlowingWaterList.size());

        try {
            downloadExcel(financeBankFlowingWaterList, fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * easyexcel构建
     * @param financeBankFlowingWaterList
     * @param fileName
     * @throws IOException
     */
    private void downloadExcel(List<FinanceBankFlowingWaterVO> financeBankFlowingWaterList, String fileName) throws IOException {

        //构造一个带指定 Region 对象的配置类
        Configuration configuration = new Configuration(Region.region0());
        //...其他参数参考类注释
        UploadManager uploadManager = new UploadManager(configuration);
        //...生成上传凭证，然后准备上传
        String accessKey = QiNiuConstant.ACCESS_KEY;
        String secretKey = QiNiuConstant.SECRET_KEY;
        String bucket = QiNiuConstant.DEFAULT_BUCKET;

        //上传文件至七牛云
        AjaxResult result = null;

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bytes = null;
        ZipOutputStream zos = new ZipOutputStream(byteArrayOutputStream);

        //压缩文件
        try {

            String name = getAdminName() + "收款单流水认领压缩文件" + System.currentTimeMillis() + ".xls";

            //构建一个excel对象,这里注意type要是xls不能是xlsx,否则下面的写入后流会关闭,导致报错
            ExcelWriter excelWriter = EasyExcel.write().excelType(ExcelTypeEnum.XLS).build();

            //构建一个sheet页
            WriteSheet firstSheet = EasyExcel.writerSheet("收款流水").build();
            WriteTable writeTableOne = EasyExcel.writerTable(0).head(FinanceBankFlowingWaterExcel.class).needHead(Boolean.TRUE).build();
            detailDownload(excelWriter,firstSheet,writeTableOne,financeBankFlowingWaterList);

            Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();

            ZipEntry zipEntry = new ZipEntry(name);
            zos.putNextEntry(zipEntry);
            workbook.write(zos);
            zos.closeEntry();

        } catch (IOException e) {
            logger.info("七牛云上传失败");
        } finally {
            zos.close();
            byteArrayOutputStream.close();
        }
        //上传文件信息
        bytes = byteArrayOutputStream.toByteArray();
        if (bytes.length < 1) {
            return;
        }
        Auth auth = Auth.create(accessKey, secretKey);
        String upToken = auth.uploadToken(bucket);
        Response response = null;
        try {
            response = uploadManager.put(bytes, fileName, upToken);
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
        } catch (QiniuException e) {
            logger.info("七牛云上传失败");
            result = AjaxResult.getError();
        }

        if (ObjectUtils.isEmpty(result)) {
            result = AjaxResult.getOK();
        }

        //上传成功或者失败修改状态
        update(result, fileName);
    }

    /**
     * 修改下载中心插入七牛云信息状态
     */
    private void update(AjaxResult result, String fileName) {

        //成功则修改状态为上传成功
        if (Objects.equals(result.getCode(), SUCCESS)) {
            FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
            fileDownloadRecord.setFileName(fileName);
            fileDownloadRecord.setStatus(CommonNumbersEnum.ONE.getNumber());
            fileDownloadRecordMapper.updateFileName(fileDownloadRecord);
            return;
        }
        //失败则修改状态为上传失败
        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
        fileDownloadRecord.setFileName(fileName);
        fileDownloadRecord.setStatus(CommonNumbersEnum.TWO.getNumber());
        fileDownloadRecordMapper.updateFileName(fileDownloadRecord);

    }

    /**
     * 组装数据
     *
     * @param excelWriter
     * @param firstSheet
     * @param writeTableOne
     * @param financeBankFlowingWaterList
     */
    private void detailDownload(ExcelWriter excelWriter, WriteSheet firstSheet, WriteTable writeTableOne, List<FinanceBankFlowingWaterVO> financeBankFlowingWaterList) {

        List<FinanceBankFlowingWaterExcel> financeBankFlowingWaterExcels = new ArrayList<>(16);
        for (FinanceBankFlowingWaterVO financeBankFlowingWaterVO : financeBankFlowingWaterList) {
            FinanceBankFlowingWaterExcel financeBankFlowingWaterExcel = new FinanceBankFlowingWaterExcel();
            financeBankFlowingWaterExcel.setAccountNumber(financeBankFlowingWaterVO.getAccountNumber());
            financeBankFlowingWaterExcel.setTradingDay(financeBankFlowingWaterVO.getTradingDay());
            financeBankFlowingWaterExcel.setTransactionAmount(financeBankFlowingWaterVO.getTransactionAmount().toString());
            financeBankFlowingWaterExcel.setAbstractText(financeBankFlowingWaterVO.getAbstractText());
            financeBankFlowingWaterExcel.setSerialNumber(financeBankFlowingWaterVO.getSerialNumber());
            financeBankFlowingWaterExcel.setUserName(financeBankFlowingWaterVO.getUserName());
            financeBankFlowingWaterExcels.add(financeBankFlowingWaterExcel);
        }
        excelWriter.write(financeBankFlowingWaterExcels, firstSheet, writeTableOne);
    }

    @Override
    public void bankFlow() {
        String toDay = DateUtils.localDateToString(LocalDate.now(), BaseDateUtils.MID_DATE_FORMAT);
        List<String> cmbBankFlowAccounts = financeConfig.getCmbBankFlowAccounts();
        for (String bankNo : cmbBankFlowAccounts) {
            //测试数据"********"
            FinanceBankFlowingWaterConfig financeBankFlowingWaterConfig = financeBankFlowingWaterConfigMapper.selectByLastSerialNumber(toDay,bankNo);
            //今日该接口增量查询次数 从0开始
            int num = 0;
            if (!ObjectUtils.isEmpty(financeBankFlowingWaterConfig)) {
                num = num + financeBankFlowingWaterConfig.getLastSerialNumber() + 1;
            }

            String randomNum = String.valueOf(System.currentTimeMillis());
            try {
                //获取接口类型 uid 今天的一个调用接口数量 银行分行行号 今日该接口增量查询次数
                String bankWater = ChinaMerchantsBankHandler.selectBankWater(ChinaMerchantsBankHandler.FUNCODE, ChinaMerchantsBankHandler.UID, randomNum, ChinaMerchantsBankHandler.BANK_NUMBER, bankNo, String.valueOf(num));
                logger.info(bankWater);
                //为空数据查询没有或者出错
                if (StringUtils.isEmpty(bankWater)) {
                    return;
                }

                financeBankFlowingWaterConfigService.assemblyHeaderInformation(bankWater, toDay, randomNum,bankNo);

            } catch (Exception e) {
                logger.info("招银流水查询异常");
                logger.error("招银流水查询异常", e);
            }
        }
    }
}
