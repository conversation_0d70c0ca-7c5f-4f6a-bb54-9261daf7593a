package net.summerfarm.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.MsgTemplate;
import net.summerfarm.service.MsgHistoryService;
import org.springframework.stereotype.Service;

/**
 * @Package: net.summerfarm.service.impl
 * @Description: 消息记录
 * @author: <EMAIL>
 * @Date: 2016/8/29
 */
@Service
public class MsgHistoryServiceImpl implements MsgHistoryService {
    @Override
    public AjaxResult send(MsgTemplate msgTemplate, int modeId, String receiver) {
        return null;
    }
}
