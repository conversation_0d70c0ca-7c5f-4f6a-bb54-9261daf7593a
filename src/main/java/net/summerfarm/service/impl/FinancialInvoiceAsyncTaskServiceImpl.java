package net.summerfarm.service.impl;

import net.summerfarm.mapper.FinancialInvoiceAsyncTaskMapper;
import net.summerfarm.model.domain.FinancialInvoiceAsyncTask;
import net.summerfarm.service.FinancialInvoiceAsyncTaskService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2024-07-19
 **/
@Service
public class FinancialInvoiceAsyncTaskServiceImpl implements FinancialInvoiceAsyncTaskService {

    @Resource
    private FinancialInvoiceAsyncTaskMapper financialInvoiceAsyncTaskMapper;

    @Override
    public void updateByPrimaryKeySelective(FinancialInvoiceAsyncTask financialInvoiceAsyncTask) {
        if (financialInvoiceAsyncTask == null) {
            return;
        }
        if (financialInvoiceAsyncTask.getId() == null) {
            return;
        }
        financialInvoiceAsyncTaskMapper.updateByPrimaryKeySelective(financialInvoiceAsyncTask);
    }
}
