package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.CityGroupMapper;
import net.summerfarm.model.DTO.AreaDTO;
import net.summerfarm.model.domain.CityGroup;
import net.summerfarm.model.input.CityGroupAddReq;
import net.summerfarm.model.vo.CityGroupVO;
import net.summerfarm.service.CityGroupService;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CityGroupServiceImpl implements CityGroupService {

    @Resource
    private CityGroupMapper cityGroupMapper;
    @Resource
    private BaseService baseService;
    @Resource
    private AreaMapper areaMapper;


    @Override
    public AjaxResult save(CityGroupAddReq cityGroupAddReq) {
        if (Objects.isNull(cityGroupAddReq)){
            return AjaxResult.getOK();
        }
        CityGroup cityGroup = new CityGroup();
        if (Objects.isNull(cityGroupAddReq.getId())){
            // 新增
            cityGroup.setName(cityGroupAddReq.getName());
            if (!CollectionUtils.isEmpty(cityGroupAddReq.getAreaDTOList())){
                cityGroup.setContent(JSON.toJSONString(cityGroupAddReq.getAreaDTOList()));
            }
            cityGroup.setCreator(baseService.getAdminId());
            try {
                cityGroupMapper.insert(cityGroup);
            }catch (DuplicateKeyException e){
                throw new DefaultServiceException("城市组名称不能重复！");
            }
        }else {
            // 修改
            cityGroup.setId(cityGroupAddReq.getId());
            cityGroup.setName(cityGroupAddReq.getName());
            if (!CollectionUtils.isEmpty(cityGroupAddReq.getAreaDTOList())){
                cityGroup.setContent(JSON.toJSONString(cityGroupAddReq.getAreaDTOList()));
            }
            cityGroup.setUpdater(baseService.getAdminId());
            cityGroupMapper.update(cityGroup);
        }


        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult delete(Integer id) {
        cityGroupMapper.deleteById(id);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult list() {
        List<CityGroup> cityGroupList = cityGroupMapper.list();
        if (CollectionUtils.isEmpty(cityGroupList)){
            return AjaxResult.getOK();
        }
        List<Integer> validAreaNoList = areaMapper.selectAreaNo();
        List<CityGroupVO> cityGroupVOList = new ArrayList<>();
        for (CityGroup cityGroup : cityGroupList) {
            CityGroupVO cityGroupVO = new CityGroupVO();
            cityGroupVO.setId(cityGroup.getId());
            cityGroupVO.setName(cityGroup.getName());
            List<AreaDTO> areaDTOS = JSON.parseArray(cityGroup.getContent(), AreaDTO.class);
            if (CollectionUtil.isNotEmpty(areaDTOS)) {
                areaDTOS = areaDTOS.stream().filter(x -> validAreaNoList.contains(x.getAreaNo())).distinct().collect(
                        Collectors.toList());
            }
            cityGroupVO.setAreaDTOList(areaDTOS);
            cityGroupVOList.add(cityGroupVO);
        }
        return AjaxResult.getOK(cityGroupVOList);
    }
}
