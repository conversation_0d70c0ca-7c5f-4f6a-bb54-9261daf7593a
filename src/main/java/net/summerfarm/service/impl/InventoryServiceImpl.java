package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.odps.Instance;
import com.aliyun.odps.OdpsException;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.task.SQLTask;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import com.cosfo.summerfarm.enums.CreateTypeEnums;
import com.cosfo.manage.client.product.req.SummerFarmSynchronizedSkuReq;
import com.cosfo.summerfarm.model.dto.SummerFarmSkuMsgDTO;
import com.cosfo.summerfarm.model.dto.SummerfarmSynchronizedSkuDTO;
import com.cosfo.summerfarm.model.dto.product.SummerfarmProductAuditResultDTO;
import com.cosfo.summerfarm.mq.SummerfarmMQTopic;
import com.cosfo.summerfarm.mq.msg.SummerfarmMqTag;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgModel;
import com.cosfo.summerfarm.mq.msg.SummerfarmMsgType;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.client.provider.DownloadCenterProvider;
import net.summerfarm.common.client.req.DownloadCenterInitReq;
import net.summerfarm.common.client.req.DownloadCenterUploadReq;
import net.summerfarm.common.client.resp.DownloadCenterResp;
import net.summerfarm.common.constant.ManagerConstant;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.executor.ExecutorFactory;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.qiNiu.UploadTokenFactory;
import net.summerfarm.config.OdpsConfig;
import net.summerfarm.contexts.*;
import net.summerfarm.facade.ProductRepository;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.downloadCenter.bean.FileDownloadRecordDTO;
import net.summerfarm.enums.*;
import net.summerfarm.facade.SaasCategoryFacade;
import net.summerfarm.facade.goods.GoodsFacade;
import net.summerfarm.goods.client.req.GoodsCodeInputReq;
import net.summerfarm.goods.client.req.XmSyncSkuReq;
import net.summerfarm.mall.client.provider.ActiveSkuProvider;
import net.summerfarm.mall.client.resp.ActiveSkuResp;
import net.summerfarm.mapper.ProductLabelValueMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.model.DTO.AreaStorageDTO;
import net.summerfarm.model.DTO.SkuCapacityDTO;
import net.summerfarm.model.DTO.inventory.SamePropertyInventoryQueryDTO;
import net.summerfarm.model.DTO.inventory.SkuPropertyInfoDTO;
import net.summerfarm.model.DTO.inventory.SkuPropertyValueDTO;
import net.summerfarm.model.bo.StoreRecordExportBO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.AdminSkuMappingInput;
import net.summerfarm.model.input.AreaSkuQuery;
import net.summerfarm.model.input.InventoryReq;
import net.summerfarm.model.input.MajorPriceInput;
import net.summerfarm.model.param.ProductSearchParam;
import net.summerfarm.model.vo.*;
import net.summerfarm.module.Product;
import net.summerfarm.module.wms.infrastructure.facade.TenantQueryFacade;
import net.summerfarm.module.wms.infrastructure.repository.WarehouseReadRepository;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.repository.AreaStoreRepository;
import net.summerfarm.repository.StoreRecordRepository;
import net.summerfarm.repository.price.CycleInventoryCostRepository;
import net.summerfarm.service.*;
import net.summerfarm.service.integration.ProductServiceIntegration;
import net.summerfarm.service.stockTransfer.enums.StorageLocationEnum;
import net.summerfarm.warehouse.mapper.WarehouseInventoryMappingMapper;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ExceptionUtil;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.enums.ConfigValueEnum.CONSIGNMENT_NO_WAREHOUSE_ENABLE;
import static net.summerfarm.enums.InventoryExtTypeEnum.BROKEN_BAG;
import static net.summerfarm.enums.InventoryExtTypeEnum.TEMPORARY_INSURANCE;
import static net.summerfarm.enums.InventorySubTypeEnum.CONSIGNMENT_NO_WAREHOUSE;

/**
 * @Package: net.summerfarm.service.impl
 * @Description:库存管理业务实现
 * @author: <EMAIL>
 * @Date: 2016/7/20
 */
@Service
public class InventoryServiceImpl extends BaseService implements InventoryService {
    private static final Logger logger = LoggerFactory.getLogger(InventoryService.class);

    @Resource
    private CycleInventoryCostRepository cycleInventoryCostRepository;
    @Resource
    private ProductLabelValueService productLabelValueService;
    @Resource
    private ProductLabelValueMapper productLabelValueMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private ProductRepository productRepository;
    @DubboReference
    private DownloadCenterProvider downloadCenterProvider;
    @Resource
    private MajorPriceMapper majorPriceMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private SupplierMapper supplierMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private InventoryRecordMapper inventoryRecordMapper;
    @Resource
    private ProductsPropertyService productsPropertyService;
    @Resource
    private WMSBuilderService wmsBuilderService;
    @Resource
    private DaySaleRankMapper daySaleRankMapper;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private AreaStoreRepository areaStoreRepository;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;

    @Autowired
    ProductsService productsService;
    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private DingTalkService dingTalkService;
    @Resource
    private FenceService fenceService;
    @Resource
    private ProductsPropertyValueMapper productsPropertyValueMapper;
    @Resource
    private WarehouseInventoryMappingMapper warehouseInventoryMappingMapper;
    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Resource
    private StoreRecordRepository storeRecordRepository;
    @Resource
    private AreaSkuService areaSkuService;
    @Resource
    private CycleInventoryCostMapper cycleInventoryCostMapper;
    @Resource
    private FenceMapper fenceMapper;
    @Resource
    private WarehouseInventoryMappingMapper inventoryMappingMapper;
    @Resource
    private StockAllocationCategoryConfigMapper stockAllocationCategoryConfigMapper;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    private WarehouseStockExtService warehouseStockExtService;
    @Resource
    private AdminMapper adminMapper;

    @Resource
    private InventoryBindMapper inventoryBindMapper;

    @Resource
    private CategoryMapper categoryMapper;

    @Resource
    private CrmBdConfigMapper crmBdConfigMapper;

    private InventoryService inventoryService;

    @Resource
    private MqProducer mqProducer;

    @Resource
    private ProductServiceIntegration productServiceIntegration;

    @Resource
    private SkuConvertService skuConvertService;

    @Resource
    private FileDownloadRecordService fileDownloadRecordService;

    @DubboReference
    private ActiveSkuProvider activeSkuProvider;

    @Resource
    private WarehouseReadRepository warehouseReadRepository;

    @Resource
    private SaasCategoryFacade saasCategoryFacade;

    @Resource
    private TenantQueryFacade tenantQueryFacade;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private GoodsFacade goodsFacade;

    @PostConstruct
    public void setInventoryService() {
        inventoryService = getContext().getBean(InventoryService.class);
    }

    //sku状态字段
    private static final String STATUS = "audit_status";
    //id字段
    private static final String ID = "inv_id";
    //上新成功
    private static final Integer SUCCESS = 1;
    /**
     * 监听类型
     */
    private static final String UPDATE = "UPDATE";
    /**
     * 体积
     */
    private static final String VOLUME = "volume";
    /**
     * 重量
     */
    private static final String WEIGHT_NUM = "weight_num";

    private static final ExecutorService EXECUTOR_SERVICE = new ThreadPoolExecutor(2, 8,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), new cn.hutool.core.thread.NamedThreadFactory("productOp-", false),
            new ThreadPoolExecutor.CallerRunsPolicy());


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult delete(String[] skus) {
        if (inventoryMapper.deleteAll(skus) > 0) {
            //修改所有售卖信息为不上架、不展示
            areaSkuMapper.closeShowAndSale(skus, getAdminName());
            return AjaxResult.getOK();
        } else {
            return AjaxResult.getError(ResultConstant.DELETE_NOT_EXIST);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult save(InventoryReq inventoryReq) {
        //校验代仓sku所属大客户
        if (Objects.equals(inventoryReq.getType(), 1)) {
            if (inventoryReq.getAdminId() == null) {
                return AjaxResult.getErrorWithMsg("代仓商品必须有所属大客户");
            }
        }

        //体积
        if (StringUtils.isNotBlank(inventoryReq.getVolume())) {
            if (!inventoryReq.getVolume().matches(RegConstant.VOLUME_REG)) {
                return AjaxResult.getErrorWithMsg("体积字段格式不正确");
            }
        }

        AjaxResult ajaxResult = checkSubType(inventoryReq.getSubType());
        if (!ajaxResult.isSuccess()) {
            return ajaxResult;
        }

        //校验是否所有销售属性都填写
        List<ProductsProperty> salePropertyList = productsPropertyService.selectSalePropertyByPdId(inventoryReq.getPdId());
        for (ProductsProperty property : salePropertyList) {
            boolean flag = inventoryReq.getSaleValueList()
                    .stream()
                    .noneMatch(val -> Objects.equals(val.getProductsPropertyId(), property.getId()));
            if (flag) {
                return AjaxResult.getErrorWithMsg("请填写销售属性：" + property.getName());
            }
        }
        //校验销售属性值格式
        List<ProductsProperty> wrongKeyPropertyList = productsPropertyService.checkValueFormat(inventoryReq.getSaleValueList());
        if (!CollectionUtils.isEmpty(wrongKeyPropertyList)) {
            logger.warn("销售属性填写格式错误:{}", wrongKeyPropertyList);
            return AjaxResult.getErrorWithMsg("销售属性填写格式错误：" + wrongKeyPropertyList.get(0).getName());
        }

        //校验是否有相同销售属性sku
        List<String> skuList = productsPropertyService.equalsSkuBySaleProperty(inventoryReq.getSku(), inventoryReq.getPdId(), inventoryReq.getExtType(), inventoryReq.getSaleValueList(), inventoryReq.getType(), inventoryReq.getAdminId(), inventoryReq.getCreateType());
        if (!CollectionUtils.isEmpty(skuList)) {
            return AjaxResult.getErrorWithMsg("有相同SKU，SKU编码为：" + skuList.get(0));
        }

        AjaxResult checkResult = checkBindSkuForSave(inventoryReq);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }

        //sku编号=1.	所属SPU编号+3位随机数
        Products products = productsMapper.selectByPdId(inventoryReq.getPdId());

//        String sku = createSkuNo(products.getPdNo());
        String sku = goodsFacade.takeSkuCode(GoodsCodeInputReq.builder()
                .spu(products.getPdNo()).build()).getSku();

        //sku数据保存
        inventoryReq.setSku(sku);
        inventoryReq.setCreator(getAdminId());
        inventoryReq.setVideoInfo(inventoryReq.getVideoUrl (),getAdminName ());
        int result = inventoryMapper.insertSelective(inventoryReq);
        if (result != 1) {
            throw new DefaultServiceException(ResultConstant.SAVE_FAIL);
        }

        //销售属性值保存
        productsPropertyService.addSalePropertyValue(sku, inventoryReq.getSaleValueList());

        //新建sku初始化area_store信息
        areaStoreMapper.initAfterCreateSku(sku, BaseConstant.XIANMU_TENANT_ID);
        warehouseStockExtService.initWarehouseStockExt(sku);
        // 如果当前sku为破袋或临保类型sku，需要插入绑定关系记录
        createBindSku(inventoryReq, sku);
        StringBuffer msg = new StringBuffer();
        if (inventoryReq.getExtType() != null && Objects.equals(inventoryReq.getExtType(), 1)) {
            msg.append(products.getPdName())
                    .append("为活动，该商品上新不会自动上新，有问题请联系产品经理");
        }

        HashMap<String, String> map = new HashMap<>();
        map.put("sku", sku);
        map.put("msg", msg.toString());
        return AjaxResult.getOK(map);
    }

    public AjaxResult checkSubType(Integer subType) {
        ExceptionUtil.Params.checkAndThrow(Objects.nonNull(subType), "商品性质不能为空");
        // 是否支持新增代销不入仓sku
        if (ObjectUtil.equal(subType, CONSIGNMENT_NO_WAREHOUSE.getSubType())) {
            Config config = configMapper.selectOne(CONSIGNMENT_NO_WAREHOUSE_ENABLE.getKey());
            if (config != null && Boolean.parseBoolean(config.getValue())) {
                return AjaxResult.getErrorWithMsg("暂不支持新增代销不入仓商品");
            }
        }
        return AjaxResult.getOK();
    }

    @Override
    public Integer selectMinSaleUnit(String sku) {
        if(StringUtils.isBlank(sku)) {
            logger.error("sku:{}不存在!", sku);
            throw new BizException("sku不存在!");
        }
        Inventory inventory = new Inventory();
        inventory.setSku(sku);
        Inventory inventoryVO = inventoryMapper.selectOne(inventory);
        if(inventoryVO == null) {
            logger.error("sku:{}不存在!", sku);
            throw new BizException("sku不存在!");
        }
        return inventoryVO.getBaseSaleUnit() * inventoryVO.getBaseSaleQuantity();
    }

    /**
     * 创建临保/破袋品与常规品的绑定关系
     *
     * @param inventoryReq 创建的商品信息
     * @param sku          临保或破袋品SKU
     */
    private void createBindSku(InventoryReq inventoryReq, String sku) {
        Integer extType = inventoryReq.getExtType();
        if (!Objects.equals(TEMPORARY_INSURANCE.type(), extType) && !Objects.equals(BROKEN_BAG.type(), extType)) {
            return;
        }

        String bindSku = inventoryReq.getBindSku();
        Long pdId = inventoryReq.getPdId();
        InventoryBind inventoryBind = inventoryBindMapper.selectByPdIdAndSkuAndBindSku(pdId, sku, bindSku);
        if (inventoryBind != null) {
            logger.info("当前两个sku已存在绑定关系，不继续建立绑定关系,sku:{}, bindSku:{}", sku, bindSku);
            return;
        }

        inventoryBind = new InventoryBind();
        inventoryBind.setPdId(pdId);
        inventoryBind.setSku(sku);
        inventoryBind.setBindSku(bindSku);

        inventoryBindMapper.insertSelective(inventoryBind);
    }

    private AjaxResult checkBindSkuForSave(InventoryReq inventoryReq) {
        // 如果是非临保破袋的sku直接返回ok的结果，不继续后续校验
        if (!isAllowBindSkuExtType(inventoryReq)) {
            return AjaxResult.getOK();
        }
        return checkBindSku(inventoryReq, true);
    }

    private boolean isAllowBindSkuExtType(InventoryReq inventoryReq) {
        Integer extType = inventoryReq.getExtType();
        return Objects.equals(TEMPORARY_INSURANCE.type(), extType) ||
                Objects.equals(BROKEN_BAG.type(), extType);
    }

    private AjaxResult checkBindSku(InventoryReq inventoryReq, boolean isNewAdd) {
        // 校验当前是否临保、破袋商品，如果是的话，必须要选择绑定的sku
        String bindSku = inventoryReq.getBindSku();
        if (StringUtils.isEmpty(bindSku)) {
            return AjaxResult.getErrorWithMsg("请选择同包装同销售属性组同性质的常规SKU绑定");
        }

        InventoryBind inventoryBind = inventoryBindMapper.selectByBindSkuAndExtType(inventoryReq.getPdId(),
                bindSku, inventoryReq.getExtType());
        if (isNewAdd && inventoryBind != null) {
            return AjaxResult.getErrorWithMsg("当前常规品SKU已被绑定，请重新选择常规品SKU进行绑定");
        }

        if (!isNewAdd && inventoryBind != null && !inventoryBind.getSku().equals(inventoryReq.getSku())) {
            return AjaxResult.getErrorWithMsg("当前常规品SKU已被绑定，请重新选择常规品SKU进行绑定");
        }

        Inventory inventory = new Inventory();
        inventory.setSku(bindSku);
        inventory = inventoryMapper.selectOne(inventory);
        if (inventory == null) {
            return AjaxResult.getErrorWithMsg("绑定的常规SKU不存在");
        }
        Integer extType = inventory.getExtType();
        if (extType != null && !Objects.equals(extType, InventoryExtTypeEnum.NORMAL.type())) {
            return AjaxResult.getErrorWithMsg("绑定的SKU非常规SKU");
        }
        Integer type = inventory.getType();
        if (type != null && !Objects.equals(type, inventoryReq.getType())) {
            return AjaxResult.getErrorWithMsg("绑定的常规SKU性质不匹配");
        }
        String unit = inventory.getUnit();
        if (!StringUtils.isEmpty(unit) && !Objects.equals(unit, inventoryReq.getUnit())) {
            return AjaxResult.getErrorWithMsg("绑定的常规SKU包装不匹配");
        }
        Long pdId = inventoryReq.getPdId();
        List<SkuPropertyInfoDTO> skuPropertyInfoDTOS = productsPropertyValueMapper.selectByPdIdAndSkusAndPropertyType(pdId.intValue(),
                Lists.newArrayList(bindSku), ProductsPropertTypeEnum.SALE_PROPERTY.getType());
        SkuPropertyInfoDTO skuPropertyInfoDTO = skuPropertyInfoDTOS.get(0);
        List<SkuPropertyValueDTO> skuPropertyValues = skuPropertyInfoDTO.getSkuPropertyValues();
        List<String> bindSkus = getCanBindSkus(pdId.intValue(), type, unit, inventoryReq.getExtType(), skuPropertyValues, Lists.newArrayList(inventory));
        if (isNewAdd && CollectionUtils.isEmpty(bindSkus)) {
            return AjaxResult.getErrorWithMsg("无可关联的常规SKU，请检查后重试");
        }

        if (!isNewAdd && inventoryBind == null && CollectionUtils.isEmpty(bindSkus)) {
            return AjaxResult.getErrorWithMsg("无可关联的常规SKU，请检查后重试");
        }

        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult copy(InventoryReq inventoryReq) {
        //校验代仓sku所属大客户
        if (Objects.equals(inventoryReq.getType(), 1)) {
            if (inventoryReq.getAdminId() == null) {
                return AjaxResult.getErrorWithMsg("代仓商品必须有所属大客户");
            }
        }

        //体积
        if (StringUtils.isNotBlank(inventoryReq.getVolume())) {
            if (!inventoryReq.getVolume().matches(RegConstant.VOLUME_REG)) {
                return AjaxResult.getErrorWithMsg("体积字段格式不正确");
            }
        }

        AjaxResult ajaxResult = checkSubType(inventoryReq.getSubType());
        if (!ajaxResult.isSuccess()) {
            return ajaxResult;
        }

        //校验是否所有销售属性都填写
        List<ProductsProperty> salePropertyList = productsPropertyService.selectSalePropertyByPdId(inventoryReq.getPdId());
        for (ProductsProperty property : salePropertyList) {
            boolean flag = inventoryReq.getSaleValueList()
                    .stream()
                    .noneMatch(val -> Objects.equals(val.getProductsPropertyId(), property.getId()));
            if (flag) {
                return AjaxResult.getErrorWithMsg("请填写销售属性：" + property.getName());
            }
        }
        //校验销售属性值格式
        List<ProductsProperty> wrongKeyPropertyList = productsPropertyService.checkValueFormat(inventoryReq.getSaleValueList());
        if (!CollectionUtils.isEmpty(wrongKeyPropertyList)) {
            logger.warn("销售属性填写格式错误:{}", wrongKeyPropertyList);
            return AjaxResult.getErrorWithMsg("销售属性填写格式错误：" + wrongKeyPropertyList.get(0).getName());
        }

        //校验是否有相同销售属性sku
        List<String> skuList = productsPropertyService.equalsSkuBySaleProperty(inventoryReq.getSku(), inventoryReq.getPdId(), inventoryReq.getExtType(), inventoryReq.getSaleValueList(), inventoryReq.getType(), inventoryReq.getAdminId(), inventoryReq.getCreateType());
        if (!CollectionUtils.isEmpty(skuList)) {
            return AjaxResult.getErrorWithMsg("有相同SKU，SKU编码为：" + skuList.get(0));
        }

        AjaxResult checkResult = checkBindSkuForSave(inventoryReq);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }

        //sku编号=1.	所属SPU编号+3位随机数
        Products products = productsMapper.selectByPdId(inventoryReq.getPdId());
//        String sku = createSkuNo(products.getPdNo());
        String sku = goodsFacade.takeSkuCode(GoodsCodeInputReq.builder()
                .spu(products.getPdNo()).build()).getSku();

        //sku数据保存
        inventoryReq.setSku(sku);
        inventoryReq.setCreator(getAdminId());
        inventoryReq.setVideoInfo(inventoryReq.getVideoUrl (),getAdminName ());
        int result = inventoryMapper.insertSelective(inventoryReq);
        if (result != 1) {
            throw new DefaultServiceException(ResultConstant.SAVE_FAIL);
        }

        //销售属性值保存
        productsPropertyService.addSalePropertyValue(sku, inventoryReq.getSaleValueList());

        //新建sku初始化area_store信息
        areaStoreMapper.initAfterCreateSku(sku, BaseConstant.XIANMU_TENANT_ID);
        warehouseStockExtService.initWarehouseStockExt(sku);
        // 如果当前sku为破袋或临保类型sku，需要插入绑定关系记录
        createBindSku(inventoryReq, sku);
        // 保存城市售卖信息
        List<AreaSkuVO> areaSkuVOList = inventoryReq.getAreaSkuVOList();
        if (!CollectionUtils.isEmpty(areaSkuVOList)) {
            areaSkuVOList.forEach(e -> e.setSku(sku));
            areaSkuService.batchHandle(areaSkuVOList);
        }


        StringBuffer msg = new StringBuffer();
        if (inventoryReq.getExtType() != null && Objects.equals(inventoryReq.getExtType(), 1)) {
            msg.append(products.getPdName())
                    .append("为活动，该商品上新不会自动上新，有问题请联系产品经理");
        }

        HashMap<String, String> map = new HashMap<>();
        map.put("sku", sku);
        map.put("msg", msg.toString());
        return AjaxResult.getOK(map);
    }

    @Override
    public AjaxResult uploadIntroduction(String sku, int picName) {
        if (picName != 1 && picName != 2 && picName != 3 && picName != 4 && picName != 5) {
            return AjaxResult.getError();
        }
        Map<String, Object> selectKeys = new HashMap<>();
        selectKeys.put("sku", sku);
        int num = inventoryMapper.count(selectKeys);
        if (num != 1) {
            return AjaxResult.getError(ResultConstant.RECORD_NOT_EXIST);
        }

        Map<String, String> data = UploadTokenFactory.createToken("INVENTORY_INTRODUCTION", sku + "/" + picName, QiNiuConstant.DEFAULT_EXPIRES);
        if (data == null) {
            return AjaxResult.getError(ResultConstant.UPLOAD_TYPE_NOT_EXIST);
        }
        return AjaxResult.getOK(data);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult update(String sku, InventoryReq record) {
        //体积校验
        if (StringUtils.isNotBlank(record.getVolume())) {
            if (!record.getVolume().matches(RegConstant.VOLUME_REG)) {
                return AjaxResult.getErrorWithMsg("体积字段格式不正确");
            }
        }

        //销售属性格式校验
        List<ProductsProperty> wrongPropertyList = productsPropertyService.updateCheckValueFormat(record.getSaleValueList());
        if (!CollectionUtils.isEmpty(wrongPropertyList)) {
            logger.warn("销售属性填写格式错误:{}", wrongPropertyList);
            return AjaxResult.getErrorWithMsg("销售属性填写格式错误：" + wrongPropertyList.get(0).getName());
        }
        Inventory query = new Inventory();
        query.setSku(record.getSku());
        Inventory old = inventoryMapper.selectOne(query);
        //审核通过不允许修改
        if (!ProductsEnum.Outdated.CREATING.getCode().equals(old.getOutdated())
                && Objects.nonNull(old.getAdminId())) {
            if (!old.getAdminId().equals(record.getAdminId())) {
                return AjaxResult.getErrorWithMsg("代仓所属不允许修改");
            }
            List<TenantResp> tenantRespList = tenantQueryFacade.listSaasTenant();
            List<Long> saasAdminIdList = tenantRespList.stream().map(TenantResp::getAdminId).collect(Collectors.toList());
            // 帆台所属代仓商品不允许修改
            if (saasAdminIdList.contains(old.getAdminId().longValue())) {
                return AjaxResult.getErrorWithMsg("帆台所属代仓商品不允许修改");
            }
        }
        Integer extType = record.getExtType();
        if (Objects.equals(TEMPORARY_INSURANCE.type(), extType) || Objects.equals(BROKEN_BAG.type(), extType)) {
            String bindSku = record.getBindSku();
            if (StringUtils.isEmpty(bindSku)) {
                return AjaxResult.getErrorWithMsg("请选择一个常规SKU进行绑定");
            }
            InventoryBind inventoryBind = inventoryBindMapper.selectOneByPdIdAndSku(record.getPdId(), bindSku);
            if (inventoryBind != null && !bindSku.equals(inventoryBind.getBindSku())) {
                return AjaxResult.getErrorWithMsg("绑定SKU不允许修改");
            }
            AjaxResult ajaxResult = checkBindSku(record, false);
            if (!ajaxResult.isSuccess()) {
                return ajaxResult;
            }
        }

        //记录销售属性修改
        List<ProductsPropertyValueVO> valueList = productsPropertyService.selectSaleValueBySku(sku);
        for (ProductsPropertyValueVO valueVO : valueList) {
            for (ProductsPropertyValue value : record.getSaleValueList()) {
                if (Objects.equals(valueVO.getProductsPropertyId(), value.getProductsPropertyId())) {
                    if (!Objects.equals(valueVO.getProductsPropertyValue(), value.getProductsPropertyValue())) {
                        InventoryRecord inventoryRecord = new InventoryRecord(sku, valueVO.getName(), valueVO.getProductsPropertyValue(), value.getProductsPropertyValue(), getAdminName(), LocalDateTime.now());
                        inventoryRecordMapper.insert(inventoryRecord);
                    }

                    break;
                }
            }
        }

        //审核状态
        record.setAuditStatus(null);
        if (record.getAuditFlag() != null) {
            //是否能够审核

            if (!ProductsEnum.Outdated.CREATING.getCode().equals(old.getOutdated())) {
                return AjaxResult.getErrorWithMsg("当前SKU不可审核");
            }
            record.setAuditor(getAdminId());
            record.setAuditTime(LocalDateTime.now());
            if (record.getAuditFlag()) {
                record.setOutdated(ProductsEnum.Outdated.VALID.getCode());
                record.setAuditStatus(ProductsEnum.AuditStatus.SUCCESS.ordinal());
                record.setRefuseReason("");
            } else {
                record.setAuditStatus(ProductsEnum.AuditStatus.FAIL.ordinal());
            }
        }
        logger.info("updateSpuAndSku Operator:{},:{}", getAdminOperator(), JSON.toJSONString(record));
        //更新
        int rs = inventoryMapper.update(record);
        if (rs != 1) {
            throw new DefaultServiceException(ResultConstant.UPDATE_FAILED);
        }
        //修改标签
        List<ProductLabelValueVo> productLabelValueVos = record.getProductLabelValueVos();
        if (!CollectionUtils.isEmpty(productLabelValueVos)) {
            for (ProductLabelValueVo productLabelValueVo : productLabelValueVos) {
                productLabelValueVo.setUpdateTime(LocalDateTime.now());
                productLabelValueMapper.updateById(productLabelValueVo);
            }
        }
        //更新销售属性
        if (!CollectionUtils.isEmpty(record.getSaleValueList())) {
            productsPropertyService.addSalePropertyValue(sku, record.getSaleValueList());
        }

        // 临保/破袋与常规SKU建立绑定关系
        if (Objects.equals(TEMPORARY_INSURANCE.type(), extType) || Objects.equals(BROKEN_BAG.type(), extType)) {
            createBindSku(record, record.getSku());
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult approvedSaasAgent(String sku,String volume) {
        Inventory query = new Inventory();
        query.setSku(sku);
        Inventory oldSku = inventoryMapper.selectOne(query);
        //体积校验
        if(StringUtils.isBlank(volume)){
            return AjaxResult.getErrorWithMsg("体积不可为空!");
        }
        if (!volume.matches(ManagerConstant.VOLUME_PATTERN)) {
            return AjaxResult.getErrorWithMsg("体积字段格式不正确");
        }

        //审核状态
        oldSku.setAuditStatus(null);
        //是否能够审核
        if (!ProductsEnum.Outdated.CREATING.getCode().equals(oldSku.getOutdated())) {
            return AjaxResult.getErrorWithMsg("当前SKU不可审核");
        }
        oldSku.setAuditor(getAdminId());
        oldSku.setAuditTime(LocalDateTime.now());
        oldSku.setOutdated(ProductsEnum.Outdated.VALID.getCode());
        oldSku.setAuditStatus(ProductsEnum.AuditStatus.SUCCESS.ordinal());
        oldSku.setRefuseReason("");
        logger.info("updateSpuAndSku Operator:{},:{}", getAdminOperator(), JSON.toJSONString(oldSku));
        //更新
        int rs = inventoryMapper.update(oldSku);
        //对应spu也改为有效
        productsMapper.updateByPrimaryKeySelective(Products.builder().pdId(oldSku.getPdId()).outdated(ProductsEnum.Outdated.VALID.getCode()).build());
        if (rs != 1) {
            throw new DefaultServiceException(ResultConstant.UPDATE_FAILED);
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult vaild(String sku, Integer pdId) {
        Set<Integer> dataPermission = getDataPermission();
        if (dataPermission.contains(0) || isSA()) {
            return AjaxResult.getOK();
        }
        AreaSkuVO select = new AreaSkuVO();
        select.setSku(sku);
        select.setOnSale(true);
        select.setPdId(pdId);
        List<AreaSkuVO> areaSkuVOS = areaSkuMapper.selectVOs(select);
        if (!CollectionUtils.isEmpty(areaSkuVOS)) {
            //查询权限中不包含的仓库
            List<Integer> storeNos = areaSkuVOS.stream()
                    .map(AreaSkuVO::getParentNo)
                    .distinct()
                    .filter(o -> !dataPermission.contains(o))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(storeNos)) {
                StringBuffer returnStr = new StringBuffer();
                for (Integer storeNo : storeNos) {
                    returnStr.append(Global.warehouseMap.get(storeNo)).append(",");
                }
                returnStr.append("有城市正在售卖,是否确定修改信息");
                return AjaxResult.getError("SHOW_INFO", returnStr.toString());
            }
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult select(int pageIndex, int pageSize, InventoryReq selectKeys, String orderBy) {
        PageHelper.startPage(pageIndex, pageSize);
        List<InventoryVO> inventories = inventoryMapper.select(selectKeys);
        // 非省心送券类型查询特价
        if (selectKeys.getActivityScope() != null && !selectKeys.getActivityScope().equals(CouponEnum.ActivityScope.TIMING_DELIVERY.ordinal())) {
            for (InventoryVO inventory : inventories) {
                if (selectKeys.getmId() != null) {
                    DubboResponse<ActiveSkuResp> activeSkuDetail = activeSkuProvider.getActiveSkuDetail(inventory.getSku(), inventory.getAreaNo(), Long.valueOf(selectKeys.getmId()));
                    if (activeSkuDetail.isSuccess() && activeSkuDetail.getData() != null) {
                        inventory.setSalePrice(activeSkuDetail.getData().getActivityPrice());
                        inventory.setActivityPrice(activeSkuDetail.getData().getActivityPrice());
                    }
                }
            }
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(inventories));
    }

    @Override
    public AjaxResult selectSkuList(InventoryReq selectKeys) {
        if (selectKeys.getStoreNo() == null) {
            return AjaxResult.getErrorWithMsg("请先选择仓库");
        }

        // 任何一个城市上架即为上架, 所有城市下架即为下架。。。 只能把业务杂糅进SQL
        List<InventoryVO> result = inventoryMapper.selectSkuList(selectKeys);
        if (selectKeys.getOnSale() != null && !CollectionUtils.isEmpty(result)) {
            if (selectKeys.getOnSale()) {
                result = result.stream().filter(inventoryVO -> Objects.equals(inventoryVO.getOnSale(), Boolean.TRUE)).collect(Collectors.toList());
            } else {
                result = result.stream().filter(inventoryVO -> Objects.equals(inventoryVO.getOnSale(), Boolean.FALSE)).collect(Collectors.toList());
            }
        }
        return AjaxResult.getOK(result);
    }

    @Override
    public AjaxResult selectBySpu(InventoryReq selectKeys) {
        if (selectKeys == null || selectKeys.getPdId() == null) {
            return AjaxResult.getErrorWithMsg("参数不能为空");
        }
        List<InventoryVO> inventories = inventoryMapper.selectVOBySpu(selectKeys);
        for (InventoryVO vo : inventories) {
            List<AreaSkuVO> areaSkuVOS = areaSkuMapper.selectVOList(vo.getSku(), selectKeys.getAreaNo());
            vo.setAreaSkuVOS(areaSkuVOS);

            //销售属性值
            List<ProductsPropertyValueVO> saleValueList = productsPropertyService.selectSaleValueBySku(vo.getSku());
            Integer extType = vo.getExtType();
            if (extType != null && (Objects.equals(extType, TEMPORARY_INSURANCE.type()) || Objects.equals(extType, BROKEN_BAG.type()))) {
                InventoryBind inventoryBind = inventoryBindMapper.selectOneByPdIdAndSku(vo.getPdId(), vo.getSku());
                if (inventoryBind != null) {
                    vo.setBindSku(inventoryBind.getBindSku());
                }
            }
            vo.setSaleValueList(saleValueList);

            //上新状态判断
            if (Objects.equals(-1, vo.getOutdated()) && Objects.equals(0, vo.getAuditStatus())) {
                vo.setCreateStatus(0);
            } else if (Objects.equals(0, vo.getOutdated()) && Objects.equals(1, vo.getAuditStatus())) {
                vo.setCreateStatus(1);
            } else if (Objects.equals(-1, vo.getOutdated()) && Objects.equals(2, vo.getAuditStatus())) {
                vo.setCreateStatus(2);
            } else if (Objects.equals(1, vo.getOutdated()) && Objects.equals(1, vo.getAuditStatus())) {
                // outdated 状态为1 就是回收站sku数据
                vo.setCreateStatus(3);
            }
        }
        ProductsVO spu = new ProductsVO();
        if (selectKeys.getPdId() != null) {
            spu = productsMapper.selectByPdId(selectKeys.getPdId());

            //上新状态判断
            if (Objects.equals(-1, spu.getOutdated()) && Objects.equals(0, spu.getAuditStatus())) {
                spu.setCreateStatus(0);
            } else if (Objects.equals(1, spu.getOutdated()) && Objects.equals(1, spu.getAuditStatus())) {
                spu.setCreateStatus(1);
            } else if (Objects.equals(-1, spu.getOutdated()) && Objects.equals(2, spu.getAuditStatus())) {
                spu.setCreateStatus(2);
            }

            //审批状态处理
            if (Objects.equals(selectKeys.getOutdated(), -1) && !CollectionUtils.isEmpty(inventories)) {
                spu.setAuditStatus(inventories.get(0).getAuditStatus());
            }

            //关键属性值
            List<ProductsPropertyValueVO> valueList = productsPropertyService.selectKeyValueByPdId(spu.getPdId());
            spu.setKeyValueList(valueList);

            //销售属性
            List<ProductsProperty> propertyList = productsPropertyService.selectSalePropertyByPdId(spu.getPdId());
            spu.setSalePropertyList(propertyList);
        }


        Map map = new HashMap();
        map.put("spu", spu);
        map.put("skulist", inventories);
        return AjaxResult.getOK(map);

    }


    @Override
    public AjaxResult selectStoreStock(int pageIndex, int pageSize, StockVO selectKeys, Long tenantId) {

        // 处理上下架状态sku搜索条件
        if (selectKeys.getOnSale() != null) {
            List<Integer> storeNoList = warehouseInventoryMappingMapper.selectAreaNo(selectKeys.getAreaNo());
            if (CollectionUtils.isEmpty(storeNoList)) {
                return AjaxResult.getErrorWithMsg("暂无配送仓使用该仓库存，无法根据上下架筛选");
            }
            if (Objects.equals(selectKeys.getOnSale(), true)) {
                selectKeys.setOnSaleSkus(areaSkuMapper.selectSku(selectKeys.getOnSale(), storeNoList, selectKeys.getAreaNo()));
            } else if (Objects.equals(selectKeys.getOnSale(), false)) {
                List<String> grounding = areaSkuMapper.selectSku(true, storeNoList, selectKeys.getAreaNo());
                List<String> notGrounding = areaSkuMapper.selectSku(false, storeNoList, selectKeys.getAreaNo());
                notGrounding.removeAll(grounding);
                selectKeys.setOnSaleSkus(notGrounding);
            }
        }

        // 仓库没选，按照租户隔离权限
        if (selectKeys.getAreaNo() == null && CollectionUtils.isEmpty(selectKeys.getAreaNoList())) {
            selectKeys.setAreaNoList(
                    warehouseReadRepository.queryWarehouseCodeListByTenantId(tenantId)
            );
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<StockVO> stockVOs = inventoryMapper.selectStoreStock(selectKeys);
        wmsBuilderService.batchBuildWMSInfo(stockVOs);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(stockVOs));
    }

    @Override
    public Long storeStockDownload(StockVO selectKeys, Integer hasInventory, Long tenantId, HttpServletResponse response) {

        // 处理上下架状态sku搜索条件
        if (selectKeys.getOnSale() != null) {
            List<Integer> storeNoList = warehouseInventoryMappingMapper.selectAreaNo(selectKeys.getAreaNo());
            if (CollectionUtils.isEmpty(storeNoList)) {
                throw new BizException("暂无配送仓使用该仓库存，无法根据上下架筛选");
            }
            if (Objects.equals(selectKeys.getOnSale(), true)) {
                selectKeys.setOnSaleSkus(areaSkuMapper.selectSku(selectKeys.getOnSale(), storeNoList, selectKeys.getAreaNo()));
            } else if (Objects.equals(selectKeys.getOnSale(), false)) {
                List<String> grounding = areaSkuMapper.selectSku(true, storeNoList, selectKeys.getAreaNo());
                List<String> notGrounding = areaSkuMapper.selectSku(false, storeNoList, selectKeys.getAreaNo());
                notGrounding.removeAll(grounding);
                selectKeys.setOnSaleSkus(notGrounding);
            }
        }
        List<String> skusByPdId = Lists.newArrayList();
        if (Objects.nonNull(selectKeys.getPdId())) {
            Map<String, Product> productMap = productRepository.mapProductsByPdIdsOnlyGoods(selectKeys.getWarehouseNo(),
                    Lists.newArrayList(selectKeys.getPdId().longValue()));
            if (productMap.isEmpty() && Objects.nonNull(selectKeys.getPdId())) {
                throw new BizException("货品中心查询不到该商品");
            }
            skusByPdId = Lists.newArrayList(productMap.keySet());
        }
        DownloadCenterInitReq downloadCenterInitReq = new DownloadCenterInitReq();
        downloadCenterInitReq.setAdminId(Objects.isNull(selectKeys.getAdminId()) ? null : selectKeys.getAdminId().longValue());
        // 表里初始化的type
        downloadCenterInitReq.setBizType(150);
        downloadCenterInitReq.setFileName("出入库操作数据导出.xls");
        downloadCenterInitReq.setFileExpiredDay(DownloadCenterEnum.FileExpiredDayEnum.THREE_DAY);
        DubboResponse<DownloadCenterResp> downloadCenterRespDubboResponse = downloadCenterProvider.initRecord(downloadCenterInitReq);
        if (!downloadCenterRespDubboResponse.isSuccess()) {
            throw new BizException("出入库数据导出异常");
        }
        DownloadCenterResp data = downloadCenterRespDubboResponse.getData();
        if (Objects.isNull(data)) {
            throw new BizException("出入库数据导出异常");
        }
        List<String> finalSkusByPdId = skusByPdId;
        ExecutorFactory.generateExcelExecutor.execute(() -> {
            try {
                download(selectKeys, hasInventory, response, finalSkusByPdId, data.getResId());
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                //上传文件到下载中心
                DownloadCenterUploadReq downloadCenterUploadReq = new DownloadCenterUploadReq();
                downloadCenterUploadReq.setResId(data.getResId());
                downloadCenterUploadReq.setFileName("出入库操作数据导出.xls");
                downloadCenterUploadReq.setBizStatus(DownloadCenterEnum.BizStatusEnum.FAILED);
                downloadCenterProvider.uploadFile(downloadCenterUploadReq);
            }
        });


        return data.getResId();
    }

    public void download(StockVO selectKeys, Integer hasInventory, HttpServletResponse response, List<String> skusByPdId, Long resId) {
        Workbook workbook = new HSSFWorkbook();
        File file = null;
        FileOutputStream out = null;
        try {

            if (Objects.isNull(selectKeys.getAreaNo())) {
                throw new ParamsException("请选择库存仓");
            }

            Sheet sheet1 = workbook.createSheet("批次数据");
            Row title1 = sheet1.createRow(0);
            title1.createCell(0).setCellValue("仓库");
            title1.createCell(1).setCellValue("一级类目");
            title1.createCell(2).setCellValue("二级类目");
            title1.createCell(3).setCellValue("商品编号");
            title1.createCell(4).setCellValue("商品名称");
            title1.createCell(5).setCellValue("规格");
            title1.createCell(6).setCellValue("存储区域");
            title1.createCell(7).setCellValue("包装");
            title1.createCell(8).setCellValue("商品归属");
            title1.createCell(9).setCellValue("进口/国产");
            title1.createCell(10).setCellValue("仓库库存");
            title1.createCell(11).setCellValue("批次");
            title1.createCell(12).setCellValue("生产日期");
            title1.createCell(13).setCellValue("保质期");
            title1.createCell(14).setCellValue("批次库存");
            title1.createCell(15).setCellValue("安全库存");
            title1.createCell(16).setCellValue("冻结库存");
            title1.createCell(17).setCellValue("在途库存");
            title1.createCell(18).setCellValue("可用库存");
            title1.createCell(19).setCellValue("上架状态");

            Integer sheet1RowNum = NumberUtils.INTEGER_ONE;

            Sheet sheet2 = workbook.createSheet("仓库数据");
            Row title2 = sheet2.createRow(0);
            title2.createCell(0).setCellValue("仓库");
            title2.createCell(1).setCellValue("一级类目");
            title2.createCell(2).setCellValue("二级类目");
            title2.createCell(3).setCellValue("商品编号");
            title2.createCell(4).setCellValue("商品名称");
            title2.createCell(5).setCellValue("规格");
            title2.createCell(6).setCellValue("存储区域");
            title2.createCell(7).setCellValue("包装");
            title2.createCell(8).setCellValue("商品归属");
            title2.createCell(9).setCellValue("进口/国产");
            title2.createCell(10).setCellValue("仓库库存");
            title2.createCell(11).setCellValue("安全库存");
            title2.createCell(12).setCellValue("冻结库存");
            title2.createCell(13).setCellValue("在途库存");
            title2.createCell(14).setCellValue("可用库存");
            title2.createCell(15).setCellValue("上架状态");
            Integer sheet2RowNum = 1;


            Integer pageNum = 1;
            Integer pageSize = 500;

            Integer count = inventoryMapper.countStoreStock(selectKeys, hasInventory, skusByPdId);
            Integer page = count / pageSize + 1;

            for (; pageNum <= page; pageNum++) {

                PageHelper.startPage(pageNum, pageSize);
                List<StockVO> stockVOs = inventoryMapper.selectStoreStockEx(selectKeys, hasInventory, skusByPdId);
                if (stockVOs.size() == 0) {
                    break;
                }
                Map<Integer, List<StockVO>> map = stockVOs.stream().collect(Collectors.groupingBy(StockVO::getAreaNo));
                map.forEach((areaNo, list) -> {
                    List<String> skus = list.stream().map(StockVO::getSku).collect(Collectors.toList());
                    Map<String, Product> productMap = productRepository.mapProductsBySkusOnlyGoods(areaNo.longValue(), skus);
                    list.forEach(item -> {
                        Product product = productMap.getOrDefault(item.getSku(), Product.builder().build());
                        item.setProductName(product.getPdName());
                        item.setWeight(product.getSpecification());
                        item.setExtType(product.getSkuExeType());
                        item.setSkuType(product.getSkuType());
                        item.setIsDomestic(product.getIsDomestic());
                        item.setPdId(Objects.isNull(product.getId()) ? null : product.getId().intValue());
                        item.setPicturePath(product.getPic());
                        item.setStorageLocation(product.getStorageLocation());
                        item.setNameRemakes(product.getBelong());
                        item.setFirstLevelCategory(Objects.equals(product.getCategoryType(), Category.FRUIT_TYPE) ? "鲜果" : "非鲜果");
                        item.setStorageArea(StorageLocationEnum.getDescByCode(product.getTemperature()));
                        item.setPacking(product.getPackaging());
                        item.setSku(product.getSku());
                        item.setSecondLevelCategory(product.queryFirstCategoryName() + product.querySecondCategoryName()
                                + product.queryThirdCategoryName());
                    });
                });

                // sheet1写入批次数据
                if (!CollectionUtils.isEmpty(stockVOs)) {
                    stockVOs = wmsBuilderService.sortedFruitPriority(stockVOs);

                    List<Integer> warehouseCodeList = stockVOs.stream().map(StockVO::getAreaNo).distinct().collect(Collectors.toList());
                    List<String> skuCodeList = stockVOs.stream().map(StockVO::getSku).distinct().collect(Collectors.toList());

                    AreaSkuQuery areaSkuQuery = new AreaSkuQuery();
                    areaSkuQuery.setSkuList(skuCodeList);
                    areaSkuQuery.setOnSale(Boolean.TRUE);
                    areaSkuQuery.setWarehouseNo(selectKeys.getAreaNo());

                    List<AreaSkuVO> areaSkuVOS = areaSkuMapper.selectByKeysGroupByAreaNoAndStoreNo(areaSkuQuery);
                    Set<String> onSaleSet = new HashSet<>();
                    if (!CollectionUtils.isEmpty(areaSkuVOS)) {
                        for (AreaSkuVO areaSkuVO : areaSkuVOS) {
                            String key = areaSkuVO.getSku() + ":" + areaSkuVO.getParentNo();
                            onSaleSet.add(key);
                        }
                    }

                    Map<String, AreaStore> areaStoreMap = areaStoreRepository.mapByWarehouseNoAndSkuCodeList(
                            warehouseCodeList, skuCodeList);

                    Map<String, List<StoreRecord>> storeRecordMap = storeRecordRepository.mapByWarehouseNoAndSkuCodeList(
                            warehouseCodeList, skuCodeList
                    );
                    //控制行数
                    for (int i = 0; i < stockVOs.size(); i++) {
                        Row row = sheet1.createRow(sheet1RowNum);
                        StockVO stockVO = stockVOs.get(i);
                        //获取批次信息
                        List<StoreRecord> storeRecords = storeRecordMap.get(
                                storeRecordRepository.mapKeyByWarehouseNoAndSkuCodeList(
                                        stockVO.getAreaNo(), stockVO.getSku())
                        );

                        //获取批次 > 0
                        List<StoreRecord> storeRecordList = CollectionUtils.isEmpty(storeRecords) ? new ArrayList<>() :
                                storeRecords.stream()
                                        .filter(x -> x.getStoreQuantity() > NumberUtils.INTEGER_ZERO)
                                        .collect(Collectors.toList());

                        int size = storeRecordList.size() > 0 ? storeRecordList.size() : 1;

                        if (!Objects.equals(size, NumberUtils.INTEGER_ONE)) {
                            mergedRegion(sheet1, sheet1RowNum, size, 20);
                        }
                        for (StoreRecord record : storeRecordList) {
                            Row sheetRow = sheet1.createRow(sheet1RowNum);
                            String productionDate = Objects.isNull(record.getProductionDate()) ? "" : record.getProductionDate().toString();
                            String qualityDate = Objects.isNull(record.getQualityDate()) ? "" : record.getQualityDate().toString();
                            sheetRow.createCell(11).setCellValue(record.getBatch());
                            sheetRow.createCell(12).setCellValue(productionDate);
                            sheetRow.createCell(13).setCellValue(qualityDate);
                            sheetRow.createCell(14).setCellValue(record.getStoreQuantity().toString());
                            sheet1RowNum++;
                        }
                        //为空的也要展示

                        // 查询仓库库存
                        AreaStore areaStore = areaStoreMap.get(
                                areaStoreRepository.mapKeyByWarehouseNoAndSkuCodeList(
                                        stockVO.getAreaNo(), stockVO.getSku()));

                        row.createCell(0).setCellValue(Global.warehouseMap.get(stockVO.getAreaNo()));
                        row.createCell(1).setCellValue(stockVO.getFirstLevelCategory());
                        row.createCell(2).setCellValue(stockVO.getSecondLevelCategory());
                        row.createCell(3).setCellValue(stockVO.getSku());
                        if (stockVO.getNameRemakes() != null) {
                            row.createCell(4).setCellValue(stockVO.getProductName() + "-" + stockVO.getNameRemakes());
                        } else {
                            row.createCell(4).setCellValue(stockVO.getProductName());
                        }
                        row.createCell(5).setCellValue(stockVO.getWeight());
                        row.createCell(6).setCellValue(stockVO.getStorageLocation() == null ? "无" : StorageLocation.getTypeById(stockVO.getStorageLocation()));
                        row.createCell(7).setCellValue(stockVO.getPacking());

                        // 自营还是代仓
                        if (SkuTypeEnum.SELF_SUPPORT.getId().equals(stockVO.getSkuType())) {
                            row.createCell(8).setCellValue(SkuTypeEnum.SELF_SUPPORT.getStatus());
                        } else if (SkuTypeEnum.SUBSTITUTE_WAREHOUSE.getId().equals(stockVO.getSkuType())) {
                            row.createCell(8).setCellValue(SkuTypeEnum.SUBSTITUTE_WAREHOUSE.getStatus());
                        } else {
                            row.createCell(8).setCellValue("");
                        }

                        row.createCell(9).setCellValue(Objects.equals(NumberUtils.INTEGER_ZERO, stockVO.getIsDomestic()) ? "进口" : "国产");
                        row.createCell(10).setCellValue(Objects.isNull(areaStore) ? "" : areaStore.getQuantity().toString());
                        //无可用批次展示0
                        if (CollectionUtils.isEmpty(storeRecordList)) {
                            sheet1RowNum++;
                            row.createCell(14).setCellValue(NumberUtils.INTEGER_ZERO);
                        }
                        row.createCell(15).setCellValue(stockVO.getSafeQuantity());
                        row.createCell(16).setCellValue(stockVO.getLockQuantity());
                        row.createCell(17).setCellValue(stockVO.getRoadQuantity());
                        row.createCell(18).setCellValue(stockVO.getStoreQuantity() - stockVO.getLockQuantity() - stockVO.getSafeQuantity());
                        String key = stockVO.getSku() + ":" + stockVO.getAreaNo();
                        row.createCell(19).setCellValue(onSaleSet.contains(key) ? "上架中" : "未上架");

                    }

                    // sheet写入仓库数据 拼装 仓库库存数据
                    storeSheet(workbook, stockVOs, onSaleSet, sheet2, sheet2RowNum);
                    sheet2RowNum += stockVOs.size();
                }
            }

            String fileName = System.getProperty("java.io.tmpdir") + File.separator
                    + "数据" + ".xls";
            file = new File(fileName);
            out = new FileOutputStream(file);
            // excel输出到文件
            workbook.write(out);
            // 上传文件到Oss
            OssUploadResult ossUploadResult = OssUploadUtil.uploadExpireThreeDay(fileName, file);
            DownloadCenterUploadReq downloadCenterUploadReq = new DownloadCenterUploadReq();
            downloadCenterUploadReq.setResId(resId);
            downloadCenterUploadReq.setFileName("出入库操作数据导出.xls");
            downloadCenterUploadReq.setBizStatus(DownloadCenterEnum.BizStatusEnum.SUCCESS);
            downloadCenterUploadReq.setFilePath(ossUploadResult.getObjectOssKey());
            downloadCenterProvider.uploadFile(downloadCenterUploadReq);
//            ExcelUtils.outputExcel(workbook, "数据.xls", response);
        } catch (IOException e) {
            logger.error("storeStockDownload exception", e);
            throw new ProviderException("导出异常");
        } finally {
            try {
                workbook.close();
                if (Objects.nonNull(file)) {
                    file.deleteOnExit();
                }
                if (Objects.nonNull(out)) {
                    out.close();
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public void exportStoreStock(StockVO selectKeys) {
        StoreRecordExportBO storeRecordExportBO = new StoreRecordExportBO("", selectKeys);
        FileDownloadRecordDTO fileDownloadRecordDTO = FileDownloadRecordDTO.builder().fileName("数据.xls").bizEnum(FileDownloadRecordEnum.STORE_RECORD)
                .uploadObj(storeRecordExportBO).ossExpiredLabelEnum(OSSExpiredLabelEnum.THREE_DAY).operatorId(getAdminId()).build();
        fileDownloadRecordService.startUpload(fileDownloadRecordDTO);
    }

    @Override
    public AjaxResult pdNamesMatch(String pdName) {
        Products selectKey = new Products();
        selectKey.setPdName(pdName);
        return AjaxResult.getOK(productsMapper.selectList(selectKey));
    }


    @Override
    public AjaxResult selectQuantityNotEmpty(InventoryReq selectKeys) {
        if (StringUtils.isBlank(selectKeys.getPhone())) {
            return AjaxResult.getErrorWithMsg("请传入正确的参数");
        }

        HashMap map = new HashMap();
        map.put("phone", selectKeys.getPhone());
        if (!(isBD() || isSA() || isAreaSA())) {
            map.put("adminId", getAdminId());
        }

        Merchant merchant = merchantMapper.selectOne(map);
        if (merchant == null) {
            return AjaxResult.getErrorWithMsg("该店铺不存在");
        }

        String param = "mId=" + merchant.getmId();
        try {
            if (StringUtils.isNotBlank(selectKeys.getPdName())) {
                //前端用pdName传参
                param = param + "&queryStr=" + URLEncoder.encode(selectKeys.getPdName(), StandardCharsets.UTF_8.name());
            }
        } catch (UnsupportedEncodingException e) {
            logger.error("转码异常：", e);
            throw new DefaultServiceException();
        }

        String result = cn.hutool.http.HttpUtil.get(Global.HOME_PRODUCT + "?" + param);
//        String result = HttpUtil.SendGET(Global.HOME_PRODUCT, param);
        logger.info("result:" + result);

        return JSON.parseObject(result, AjaxResult.class);
    }

    @Override
    public AjaxResult selectList(InventoryReq selectKeys) {
        selectKeys.setOutdated(ProductsEnum.Outdated.VALID.getCode());
        List<Inventory> inventories = inventoryMapper.selectList(selectKeys);
        return AjaxResult.getOK(inventories);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult recovery(String sku) {
        Inventory selectKeys = new Inventory();
        selectKeys.setSku(sku);
        Inventory inventory = inventoryMapper.selectOne(selectKeys);
        ProductsVO productsVO = productsMapper.selectByPdId(inventory.getPdId());
        if (productsVO.getCategoryId() == null) {
            return AjaxResult.getErrorWithMsg("类目缺失，请先编辑完善类目信息");
        }
        //恢复spu
        Products updateKeys = new Products();
        updateKeys.setPdId(inventory.getPdId());
        updateKeys.setOutdated(ProductsEnum.Outdated.VALID.getCode());
        productsMapper.updateByPrimaryKeySelective(updateKeys);
        //回复sku
        selectKeys.setOutdated(ProductsEnum.Outdated.VALID.getCode());
        inventoryMapper.update(selectKeys);
        return AjaxResult.getOK();
    }

    public ArrayList<String> pdNameArrayToList(String productArray) {
        ArrayList<String> pdNames = new ArrayList<>();
        if (!StringUtils.isEmpty(productArray)) {
            String[] pds = productArray.split(";");
            for (String pd : pds) {
                String[] pdName = pd.split("/");
                if (pdName.length == 2) {
                    pdNames.add(pdName[1]);
                }
            }
        }
        return pdNames;
    }

    @Override
    public AjaxResult selectAreaSku(Integer areaNo, String sku) {
        InventoryReq inventoryReq = new InventoryReq();
        inventoryReq.setAreaNo(areaNo);
        inventoryReq.setSku(sku);
        List<InventoryVO> inventoryVOs = inventoryMapper.selectBySpu(inventoryReq);
        if (!CollectionUtils.isEmpty(inventoryVOs)) {
            InventoryVO inventoryVO = inventoryVOs.get(0);
            if (!CollectionUtils.isEmpty(inventoryVO.getAreaSkuVOS())) {
                return AjaxResult.getOK(inventoryVO.getAreaSkuVOS().get(0));
            }
        }
        return AjaxResult.getError("FAIL", "对应城市不存在此SKU");
    }

    @Override
    public AjaxResult selectPriceBySku(String sku, Integer areaNo) {
        if (!StringUtils.isNotBlank(sku)) {
            return AjaxResult.getErrorWithMsg("请先输入sku!");
        }
        if (areaNo == null) {
            return AjaxResult.getErrorWithMsg("请先输入城市!");
        }
        MajorPriceInput majorPriceInput = majorPriceMapper.selectSalePrice(sku, areaNo);
        if (majorPriceInput == null) {
            Area area = areaMapper.selectByAreaNo(areaNo);
            if (area == null) {
                throw new DefaultServiceException(ResultConstant.PARAM_FAULT);
            }
            return AjaxResult.getError(ResultConstant.RECORD_NOT_EXIST, area.getAreaName() + "没有此商品信息!");
        }
        return AjaxResult.getOK(majorPriceInput);
    }

    @Override
    public AjaxResult selectByAdminIdSkusGroup(Integer adminId) {
        List<SKUVO> skus = inventoryMapper.selectSkusByType(0, null);
        List<SKUVO> skuvos = inventoryMapper.selectSkusByType(1, adminId);
        skus.addAll(skuvos);
        return skusGroup(skus);
    }

    @Override
    public AjaxResult selectByAdminIdSkus(Integer adminId) {
        //查询自营和所属大客户的代仓商品
        List<SKUVO> skus = inventoryMapper.selectSkusByType(0, null);
        List<SKUVO> skuvos = inventoryMapper.selectSkusByType(1, adminId);
        skus.addAll(skuvos);
        return AjaxResult.getOK(skus);
    }

    private AjaxResult skusGroup(List<SKUVO> skus) {

        List<SKUGroupVO> skuGroupVOS = new LinkedList<>();

        if (CollectionUtils.isEmpty(skus)) {
            return AjaxResult.getOK(skuGroupVOS);
        }
        List<Supplier> suppliers = supplierMapper.selectAll();

        // key对应productName value对应供应商
        HashMap<String, List<Supplier>> pdNameSuppliers = new HashMap<>();
        for (Supplier supplier : suppliers) {
            List<String> pdNames = pdNameArrayToList(supplier.getProductArray());
            for (String pdName : pdNames) {
                if (pdNameSuppliers.containsKey(pdName)) {
                    pdNameSuppliers.get(pdName).add(supplier);
                } else {
                    ArrayList<Supplier> supplierArrayList = new ArrayList<>();
                    supplierArrayList.add(supplier);
                    pdNameSuppliers.put(pdName, supplierArrayList);
                }
            }
        }
        Set<String> pdNames = skus.stream().map(SKUVO::getPdName).collect(Collectors.toSet());
        for (String pdName : pdNames) {
            SKUGroupVO skuGroupVO = new SKUGroupVO();
            skuGroupVOS.add(skuGroupVO);
            List<SKUVO> skuvos = new LinkedList<>();
            skuGroupVO.setPdName(pdName);
            skuGroupVO.setSkuvos(skuvos);
            Iterator<SKUVO> iterator = skus.iterator();
            while (iterator.hasNext()) {
                SKUVO skuvo = iterator.next();
                if (pdName.equals(skuvo.getPdName())) {
                    skuvo.setWeight(skuvo.getWeight());
                    skuvos.add(skuvo);
                    skuGroupVO.setPdId(skuvo.getPdId());
                    skuGroupVO.setSpuPic(skuvo.getSpuPic());
                    skuGroupVO.setBrand(skuvo.getBrand());
                    skuGroupVO.setPdNo(skuvo.getPdNo());
                }
            }
            if (pdNameSuppliers.containsKey(pdName)) {
                skuGroupVO.setSuppliers(pdNameSuppliers.get(pdName));
            }
        }
        return AjaxResult.getOK(skuGroupVOS);
    }

    @Override
    public AjaxResult selectSku(String sku) {
        InventoryVO inventoryVO = inventoryMapper.selectSkuType(sku);
        return AjaxResult.getOK(inventoryVO);
    }

    @Override
    public AjaxResult selectSampleSku(Integer areaNo, String queryStr) {
        // 获取城市所在总仓,先根据仓获取所有的sku
        Area area = areaMapper.queryByAreaNo(areaNo);
        Integer parentNo = fenceService.selectStoreNoByAreaNo(areaNo);

        List<InventoryVO> inventoryVOS = inventoryMapper.selectSampleSkuByArea(parentNo, queryStr);
        //不存在样品sku,返回一个空List
        if (Objects.isNull(inventoryVOS) || CollectionUtil.isEmpty(inventoryVOS)) {
            inventoryVOS = new ArrayList<>(1);
        }
        //对存在托管仓的情况下，更新库存，有更好的实现？待优化
        inventoryVOS.forEach(item -> {
            if (Objects.nonNull(item.getTrustStoreNo()) && Objects.nonNull(item.getSku())) {
                AreaStore query = new AreaStore();
                query.setAreaNo(item.getTrustStoreNo());
                query.setSku(item.getSku());
                AreaStore areaStore = areaStoreMapper.selectWithOutDataPermission(query);
                if (Objects.nonNull(areaStore) && Objects.nonNull(areaStore.getOnlineQuantity())) {
                    item.setQuantity(areaStore.getOnlineQuantity());
                }
            }
        });
        return AjaxResult.getOK(inventoryVOS);
    }

    @Override
    public InventoryWMSInfo queryWMSInfo(String sku) {
        InventoryWMSInfo info = inventoryMapper.selectSkuWMSInfo(sku);
        if (Objects.isNull(info)) {
            return null;
        }
        info.setFirstLevelCategory(Objects.equals(info.getCategoryType(), Category.FRUIT_TYPE) ? "鲜果" : "非鲜果");
        info.setStorageArea(StorageLocation.getTypeById(info.getStorageLocation()));
        return info;
    }

    @Override
    public Map<String, InventoryWMSInfo> batchQueryWMSInfo(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Maps.newHashMap();
        }
        skuList = skuList.stream().distinct().collect(Collectors.toList());
        List<InventoryWMSInfo> infoList = inventoryMapper.batchSelectSkuWMSInfo(skuList);

        return infoList.stream().map(info -> {
            info.setFirstLevelCategory(Objects.equals(info.getCategoryType(), Category.FRUIT_TYPE) ? "鲜果" : "非鲜果");
            info.setStorageArea(StorageLocation.getTypeById(info.getStorageLocation()));
            return info;
        }).collect(Collectors.toMap(InventoryWMSInfo::getSku, Function.identity()));
    }

    @Override
    public AjaxResult isNotDirectStore(String sku, Integer adminId) {
        if (StringUtils.isBlank(sku) || StringUtils.isBlank(adminId)) {
            throw new DefaultServiceException("sku或adminId不能为空！");
        }
        Inventory inventory = inventoryMapper.queryBySku(sku);
        if (Objects.isNull(inventory) || (Objects.deepEquals(inventory.getType(), 1) && !Objects.deepEquals(inventory.getAdminId(), adminId))) {
            throw new DefaultServiceException("该大客户不存在对应的sku");
        }
        Boolean flag = Objects.deepEquals(inventory.getType(), 1);
        return AjaxResult.getOK(flag);
    }

    /**
     * 日热销商品信息同步
     */
    @Transactional
    @Override
    public void syncSkuDaySaleRank() {
        String ds;
        String querySql;
        ds = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        querySql = "select area_no, sku, rank_id, brand_name, sales from  summerfarm_ds.dws_trd_sku_rank_di " +
                "WHERE ds = '" + ds + "' order by area_no,brand_name,rank_id asc;";
       /* querySql = "select area_no, sku, rank_id, brand_name from  summerfarm_ds.dws_trd_sku_rank_di" +
                " WHERE ds = '20210223'  order by area_no, brand_name, rank_id asc;";*/

        logger.info("商品销售排名查询sql：{}", querySql);

        try {
            Instance instance = SQLTask.run(OdpsConfig.ODPS, querySql);
            instance.waitForSuccess();
            List<Record> recordList = SQLTask.getResult(instance);
            List<DaySaleRank> daySaleRanks = recordList.stream().map(this::transferRecordToVo).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(daySaleRanks)) {
                logger.info("InventoryServiceImpl[]syncSkuDaySaleRank[]daySaleRanks is empty querySql:{}", querySql);
                return;
            }
            //先把原有的表清空
            daySaleRankMapper.deleteAll();
            //再获取到的新的内容插入到表内
            daySaleRankMapper.insertBatch(daySaleRanks);

        } catch (OdpsException e) {
            logger.error("同步每日热销数据异常：", e);
        }

    }

    @Override
    public AjaxResult selectSkuInfo() {
        List<InventoryVO> inventoryVOS = inventoryMapper.selectSkuInfo();
        inventoryVOS.stream().forEach(el -> {
            String weight = el.getWeight();
            if (!StringUtils.isEmpty(weight) && weight.length() > 2) {
                el.setWeight(weight.substring(2));
            } else {
                el.setWeight(weight);
            }
        });
        return AjaxResult.getOK(inventoryVOS);
    }

    @Override
    public AjaxResult selectBySpuInfo() {
        //查询出spu
        List<SKUGroupVO> skuGroupVOList = productsMapper.selectGroupBySpu();
        return AjaxResult.getOK(skuGroupVOList);
    }

    @Override
    public AjaxResult skuQuery(InventoryVO query) {
        List<InventoryVO> list = inventoryMapper.skuQuery(query);
        return AjaxResult.getOK(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult purchaseCreateSku(InventoryReq record) {
        //参数校验
        if (StringUtils.isBlank(record.getUnit())) {
            return AjaxResult.getErrorWithMsg("请填写SKU包装");
        }
        if (record.getType() == null) {
            return AjaxResult.getErrorWithMsg("请填写SKU性质");
        }
        if (StringUtils.isBlank(record.getVolume())) {
            return AjaxResult.getErrorWithMsg("请填写SKU体积");
        }
        if (StringUtils.isBlank(record.getWeightNum())) {
            return AjaxResult.getErrorWithMsg("请填写SKU重量");
        }
        if (StringUtils.isBlank(record.getCreateRemark())) {
            return AjaxResult.getErrorWithMsg("请填写SKU上新备注");
        }
        AjaxResult ajaxResult = checkSubType(record.getSubType());
        if (!ajaxResult.isSuccess()) {
            return ajaxResult;
        }

        //校验销售属性值格式
        List<ProductsProperty> wrongKeyPropertyList = productsPropertyService.checkValueFormat(record.getSaleValueList());
        if (!CollectionUtils.isEmpty(wrongKeyPropertyList)) {
            logger.warn("销售属性填写格式错误:{}", wrongKeyPropertyList);
            return AjaxResult.getErrorWithMsg("销售属性填写格式错误：" + wrongKeyPropertyList.get(0).getName());
        }

        //校验是否有相同销售属性sku
        List<String> skuList = productsPropertyService.equalsSkuBySaleProperty(record.getSku(), record.getPdId(), record.getExtType(), record.getSaleValueList(), record.getType(), record.getAdminId(), record.getCreateType());
        if (!CollectionUtils.isEmpty(skuList)) {
            return AjaxResult.getErrorWithMsg("有相同SKU，SKU编码为：" + skuList.get(0));
        }


//        String sku = createSkuNo(record.getPdNo());
        String sku = goodsFacade.takeSkuCode(GoodsCodeInputReq.builder()
                .spu(record.getPdNo()).build()).getSku();
        record.setSku(sku);
        record.setCreator(Objects.isNull(record.getCreator()) ? getAdminId() : record.getCreator());
        String createType = record.getCreateType();
        if (CreateTypeEnums.SELF.getType().toString().equals(createType)) {
            // 自营品无需审核
            record.setOutdated(ProductsEnum.Outdated.VALID.getCode());
            record.setAuditStatus(ProductsEnum.AuditStatus.SUCCESS.ordinal());
        } else {
            record.setOutdated(ProductsEnum.Outdated.CREATING.getCode());
            record.setAuditStatus(ProductsEnum.AuditStatus.WAIT_AUDIT.ordinal());
        }
        record.setTaskType(1);
        record.setVideoInfo(record.getVideoUrl (),getAdminName ());
        int result = inventoryMapper.insertSelective(record);
        if (result != 1) {
            throw new DefaultServiceException(ResultConstant.SAVE_FAIL);
        }

        if (!CollectionUtils.isEmpty(record.getProductLabelValueVos())) {
            productLabelValueService.correctedData(record.getProductLabelValueVos(), sku);
        }

        //销售属性值保存
        productsPropertyService.addSalePropertyValue(sku, record.getSaleValueList());

        //新建sku初始化area_store信息
        // areaStoreMapper.initAfterCreateSaasSku(sku, record.getTenantId());
        Long tenantId = record.getTenantId();
        if (Objects.nonNull(tenantId) && tenantId > BaseConstant.XIANMU_TENANT_ID) {
            if (CreateTypeEnums.SELF.getType().toString().equals(record.getCreateType())) {
                areaStoreMapper.initAfterCreateSaasSelfSku(sku, record.getTenantId());
            } else if (CreateTypeEnums.SELF_AND_AGENT.getType().toString().equals(record.getCreateType())) {
                areaStoreMapper.initAfterCreateSaasSelfSku(sku, record.getTenantId());
                areaStoreMapper.initAfterApplySaasAgentSku(sku, BaseConstant.XIANMU_TENANT_ID);
            } else {
                return AjaxResult.getError(String.format("saas自营货品不支持的crateType:%s", record.getCreateType()));
            }
        } else {
            // 鲜沐初始化
            areaStoreMapper.initAfterApplySaasAgentSku(sku, BaseConstant.XIANMU_TENANT_ID);
        }


        warehouseStockExtService.initWarehouseStockExt(sku);

        HashMap<String, String> map = new HashMap<>();
        map.put("sku", sku);
        map.put("skuId", String.valueOf(record.getInvId()));
        map.put("msg", "");
        return AjaxResult.getOK(map);
    }

    @Override
    public AjaxResult auditFail(String sku, String refuseReason) {
        Inventory query = new Inventory();
        query.setSku(sku);
        Inventory inventory = inventoryMapper.selectOne(query);
        if (inventory == null) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        boolean creating = ProductsEnum.Outdated.CREATING.getCode().equals(inventory.getOutdated());
        boolean inAudit = Objects.equals(ProductsEnum.AuditStatus.WAIT_AUDIT.ordinal(), inventory.getAuditStatus());
        if (!(creating && inAudit)) {
            return AjaxResult.getErrorWithMsg("当前商品上新不可操作忽略");
        }

        Inventory update = new Inventory();
        update.setSku(sku);
        update.setAuditStatus(ProductsEnum.AuditStatus.FAIL.ordinal());
        update.setAuditTime(LocalDateTime.now());
        update.setAuditor(getAdminId());
        update.setRefuseReason(refuseReason);
        inventoryMapper.update(update);
        return AjaxResult.getOK();
    }

    /**
     * 将查询到的数据转化
     *
     * @param record
     */
    private DaySaleRank transferRecordToVo(Record record) {
        DaySaleRank daySaleRank = new DaySaleRank();
        daySaleRank.setAreaNo(Integer.valueOf(String.valueOf(record.get("area_no"))));
        daySaleRank.setSku(String.valueOf(record.get("sku")));
        daySaleRank.setRankId(Integer.valueOf(String.valueOf(record.get("rank_id"))));
        daySaleRank.setBrandName(String.valueOf(record.get("brand_name")));
        daySaleRank.setSales(Integer.valueOf(String.valueOf(record.get("sales"))));
        return daySaleRank;
    }

    /**
     * 生成规则由货品中心收口
     **/
    private String createSkuNo(String pdNo) {
        String sku = pdNo + StringUtils.getRandomNumber(3);
        //sku编号查重
        Map countKey = new HashMap();
        int repeat = 1;
        while (repeat > 0) {
            sku = pdNo + StringUtils.getRandomNumber(3);
            countKey.put("sku", sku);
            repeat = inventoryMapper.count(countKey);
        }
        return sku;
    }

    /**
     * 发送审核通知消息
     *
     * @param auditFlag     审核标识
     * @param auditor       审核人
     * @param createAdminId createAdminId
     * @param sku           sku
     */
    private void sendAuditMsg(boolean auditFlag, String auditor, Integer createAdminId, String sku) {
        EXECUTOR_SERVICE.execute(() -> {
            if (createAdminId == null) {
                return;
            }
                    /*AdminAuthExtend authExtend = adminAuthExtendRepository.selectByAdminId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), createAdminId);
                    if (authExtend == null) {
                        logger.warn("上新审核结果通知失败，无钉钉授权信息，adminId：{}", createAdminId);
                        return;
                    }*/
            Inventory query = new Inventory();
            query.setSku(sku);
            Inventory inventory = inventoryMapper.selectOne(query);
            Products products = productsMapper.selectByPrimaryKey(inventory.getPdId());
            String title = "【商品上新结果】";
            String content = "###### " + title + "\n" +
                    "> ###### ：" + (auditFlag ? "恭喜！" : "抱歉！") + "您提交的 " + products.getPdName()
                    + " 规格为 " + inventory.getWeight()
                    + (auditFlag ? " 上新完成；" : (" 上新失败；请联系 @" + auditor + " 处理")) + "\n" +
                    "> ###### 商品名称：" + products.getPdName() + "\n" +
                    "> ###### SKU编码：" + inventory.getSku() + "\n" +
                    "> ###### SKU规格：" + inventory.getWeight() + "\n" +
                    "> ###### 处理人：" + auditor + "\n";

            DingTalkMsgReceiverIdBO dingTalkMsgReceiverIdBO = new DingTalkMsgReceiverIdBO();
            dingTalkMsgReceiverIdBO.setReceiverIdList(Collections.singletonList(createAdminId.longValue()));
            dingTalkMsgReceiverIdBO.setMsgType(DingTalkMsgTypeEnum.MARKDOWN.getType());
            dingTalkMsgReceiverIdBO.setTitle(title);
            dingTalkMsgReceiverIdBO.setText(content);
            try {
                dingTalkMsgSender.sendMessageWithFeiShu(dingTalkMsgReceiverIdBO);
            } catch (Exception e) {
                logger.info("【商品上新结果】发送飞书消息失败,param:{},e:{}", JSON.toJSONString(dingTalkMsgReceiverIdBO), e);
            }
        });
    }

    @Override
    public PageInfo<InventoryVO> productSearch(ProductSearchParam productSearchParam) {
        int pageIndex = productSearchParam.getPageIndex();
        int pageSize = productSearchParam.getPageSize();
        String queryStr = productSearchParam.getQueryStr();
        String saasSkuId = productSearchParam.getSaasSkuId();
        List<Integer> createTypeList = productSearchParam.getCreateTypeList();
        Integer warehouseNo = productSearchParam.getWarehouseNo();
        Integer samplePool = productSearchParam.getSamplePool();
        Integer categoryId = productSearchParam.getCategoryId();
        List<String> skuList = CollectionUtil.isEmpty(productSearchParam.getSkuList()) ? Lists.newArrayList() : productSearchParam.getSkuList();
        if (saasSkuId != null) {
            String sku = skuConvertService.saasSkuId2Sku(saasSkuId);
            if (StringUtils.isEmpty(sku)) {
                return new PageInfo<>();
            } else {
                skuList.add(sku);
            }
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<InventoryVO> inventories;
        if (SaasThreadLocalUtil.isSaasRequest()) {
            List<String> categorySkuList = Lists.newArrayList();
            if (categoryId != null) {
                categorySkuList.addAll(saasCategoryFacade.querySkuBySaasCategoryId(categoryId));
            }
            skuList = doBuildListByCategoryList(skuList, categorySkuList);
            inventories = inventoryMapper.saasMatchSkuOrName(queryStr, skuList, SaasThreadLocalUtil.getAdminId(), createTypeList);
        } else {
            // 多个sku用逗号隔开
            if (StringUtils.isNotBlank(queryStr) && queryStr.contains(Global.SEPARATING_SYMBOL)) {
                skuList.addAll(Arrays.asList(queryStr.split(Global.SEPARATING_SYMBOL)));
            }
            inventories = inventoryMapper.matchSkuOrName(queryStr, skuList, samplePool);
        }
        if (CollectionUtils.isNotEmpty(inventories)) {
            List<Long> pdIds = inventories.stream().map(x -> x.getPdId()).filter(x -> x != null).distinct()
                    .collect(Collectors.toList());
            //保持saas原逻辑,由于上面逻辑中没有获取pdId,所以不查询销售属性
            if (CollectionUtil.isNotEmpty(pdIds)) {
                List<Integer> categoryIds = inventories.stream().map(x -> x.getCategoryId()).distinct()
                        .collect(Collectors.toList());
                List<ProductsPropertyValueVO> propertyValueVOList = productsPropertyValueMapper.listByPdIds(
                        pdIds);
                Map<Integer, List<ProductsPropertyValueVO>> propertyMap = propertyValueVOList.stream()
                        .collect(Collectors.groupingBy(ProductsPropertyValueVO::getPdId));
                List<Category> categories = categoryMapper.selectTypeByIds(categoryIds);
                Map<Integer, Integer> categoryTypeMap = categories.stream()
                        .collect(Collectors.toMap(x -> x.getId(), x -> x.getType(), (a, b) -> a));
                for (InventoryVO inventory : inventories) {
                    if (inventory.getPdId() == null) {
                        continue;
                    }
                    inventory.setCategoryType(categoryTypeMap.get(inventory.getCategoryId()));
                    //销售属性
                    List<ProductsPropertyValueVO> keyValueList = propertyMap.get(
                            inventory.getPdId().intValue());
                    inventory.setKeyValueList(keyValueList);
                    inventory.sortKeyValueList();
                    inventory.resetSkuNameAndSkuPic();
                }
            }
            if (warehouseNo != null) {
                List<String> skus = inventories.stream()
                        .map(InventoryVO::getSku).collect(Collectors.toList());
                List<SkuAreaPriceVO> skuAreaPriceVOS = areaSkuService.selectByWarehouseNoAndSkus(warehouseNo, skus);
                List<SkuStockDO> skuStockDOS = areaStoreMapper.selectBySkus(warehouseNo, skus);
                Map<String, Integer> skuNumMap = skuStockDOS.stream().collect(Collectors.toMap(SkuStockDO::getSku, SkuStockDO::getStockNum, (o1, o2) -> o1));
                inventories.forEach(inventoryVO -> inventoryVO.setStockNum(skuNumMap.getOrDefault(inventoryVO.getSku(), 0)));
                if (CollectionUtils.isNotEmpty(skuAreaPriceVOS)) {
                    for (InventoryVO inventory : inventories) {
                        skuAreaPriceVOS.stream()
                                .filter(skuAreaPriceVO -> inventory.getSku().equals(skuAreaPriceVO.getSku()))
                                .findFirst()
                                .ifPresent(skuAreaPriceVO -> {
                                    inventory.setMaxPrice(skuAreaPriceVO.getMaxPrice());
                                    inventory.setMinPrice(skuAreaPriceVO.getMinPrice());
                                });
                    }
                }
            }
        }
        skuConvertService.setSaasSkuIdForList(inventories);
        return PageInfoHelper.createPageInfo(inventories);
    }

    /**
     * 1.sku和类目都不为空时取交集
     * 2.sku为空，类目不为空 取类目
     * 3.类目为空，sku不为空 取sku
     **/
    private List<String> doBuildListByCategoryList(List<String> skuList, List<String> categorySkuList) {
        if (CollectionUtils.isNotEmpty(skuList) && CollectionUtils.isNotEmpty(categorySkuList)) {
            skuList = (List<String>) CollectionUtils.intersection(skuList, categorySkuList);
            if (CollectionUtils.isEmpty(skuList)) {
                skuList.add("null");
            }
        }
        if (CollectionUtils.isEmpty(skuList) && CollectionUtils.isNotEmpty(categorySkuList)) {
            skuList.addAll(categorySkuList);
        }

        return skuList;
    }

    @Override
    public AjaxResult selectBySkuStr(AdminSkuMappingInput input) {
        if (Objects.isNull(input) || StringUtils.isBlank(input.getSkuStr())) {
            return null;
        }
        List<InventoryVO> inventoryVOS = inventoryMapper.selectBySkuStr(input);
        return AjaxResult.getOK(inventoryVOS);
    }

    @Override
    public AjaxResult selectArea(String sku) {
        List<AreaStorageDTO> areaStorageDTOS = inventoryMapper.selectAreaNoAndStorage(sku);
        if (CollectionUtils.isEmpty(areaStorageDTOS)) {
            return AjaxResult.getOK();
        }

        Map<Integer, List<AreaStorageDTO>> listMap = areaStorageDTOS.stream()
                .collect(Collectors.groupingBy(AreaStorageDTO::getWarehouseNo));
        List<AreaStorageVO> areaStorageVOList = new ArrayList<>();

        for (Map.Entry<Integer, List<AreaStorageDTO>> entry : listMap.entrySet()) {
            AreaStorageVO areaStorageVO = new AreaStorageVO();
            areaStorageVO.setWarehouseNo(entry.getKey());
            areaStorageVO.setWarehouseName(entry.getValue().get(0).getWarehouseName());
            // 设置库存仓下子城市
            List<AreaStorage> children = new ArrayList<>();
            List<AreaStorageDTO> areaStorageDTOList = entry.getValue();
            for (AreaStorageDTO areaStorageDTO : areaStorageDTOList) {
                AreaStorage areaStorage = new AreaStorage();
                areaStorage.setAreaNo(areaStorageDTO.getAreaNo());
                areaStorage.setAreaName(areaStorageDTO.getAreaName());
                areaStorage.setWarehouseNo(areaStorageDTO.getWarehouseNo());
                children.add(areaStorage);
            }
            areaStorageVO.setChildren(children);
            areaStorageVOList.add(areaStorageVO);
        }
        return AjaxResult.getOK(areaStorageVOList);
    }

    @Override
    public List<String> selectByCondition(InventoryReq selectKeys) {
        List<String> skus = areaSkuMapper.selectByWarehouseNo(selectKeys);
        logger.info("定品数量" + skus.size());
        return skus;
    }

    @Override
    @Deprecated
    public AjaxResult match2SkuOrName(int pageIndex, int pageSize, String queryStr, Integer warehouseNo) {
        PageHelper.startPage(pageIndex, pageSize);
        // 备货中采购只查看负责的商品
        List<String> skus = stockAllocationCategoryConfigMapper.selectByAdminId(getAdminId());
        List<InventoryVO> inventories = inventoryMapper.match2SkuOrName(queryStr, skus);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(inventories));
    }

    @Override
    public BigDecimal selectCostPrice(String sku, Integer areaNo) {
        Fence fence = new Fence();
        fence.setAreaNo(areaNo);
        List<Fence> fences = fenceMapper.selectFence(fence);
        if (CollectionUtils.isEmpty(fences)) {
            logger.info("未获取到对应围栏信息{}", areaNo);
            return BigDecimal.ZERO;
        }
        Integer storeNo = fences.get(0).getStoreNo();
        WarehouseInventoryMapping warehouseInventoryMapping = inventoryMappingMapper.selectByUniqueIndex(storeNo, sku);
        if (warehouseInventoryMapping == null) {
            logger.info("sku{},不在城配仓下{}", sku, storeNo);
            return BigDecimal.ZERO;
        }
        CycleInventoryCost cycleInventoryCost = cycleInventoryCostRepository.selectBySku(sku, warehouseInventoryMapping.getWarehouseNo());
        if (cycleInventoryCost == null) {
            logger.info("未查询到成本价,sku{},warehouseNo:{}", sku, warehouseInventoryMapping.getWarehouseNo());
            return BigDecimal.ZERO;
        }
        return cycleInventoryCost.getFirstCycleCost();
    }

    /**
     * 合并单元格
     *
     * @param sheet
     * @param index
     * @param length
     * @param size
     */
    private void mergedRegion(Sheet sheet, Integer index, Integer size, Integer length) {
        for (int i = 0; i < length; i++) {
            //不合并
            if (i > 10 && i < 15) {
                continue;
            }
            sheet.addMergedRegion(new CellRangeAddress(index, index + size - 1, i, i));
        }
    }

    /**
     * excel 拼装仓库数据
     *
     * @param workbook
     * @param stockVOs  下载数据
     * @param onSaleSet 上架sku
     */
    private void storeSheet(Workbook workbook, List<StockVO> stockVOs, Set<String> onSaleSet, Sheet sheet, Integer sheet2RowNum) {

        for (int i = 0; i < stockVOs.size(); i++) {
            Row row = sheet.createRow(sheet2RowNum);

            StockVO stockVO = stockVOs.get(i);
            row.createCell(0).setCellValue(Global.warehouseMap.get(stockVO.getAreaNo()));
            row.createCell(1).setCellValue(stockVO.getFirstLevelCategory());
            row.createCell(2).setCellValue(stockVO.getSecondLevelCategory());
            row.createCell(3).setCellValue(stockVO.getSku());
            if (stockVO.getNameRemakes() != null) {
                row.createCell(4).setCellValue(stockVO.getProductName() + "-" + stockVO.getNameRemakes());
            } else {
                row.createCell(4).setCellValue(stockVO.getProductName());
            }
            row.createCell(5).setCellValue(stockVO.getWeight());
            row.createCell(6).setCellValue(stockVO.getStorageLocation() == null ? "无" : StorageLocation.getTypeById(stockVO.getStorageLocation()));
            row.createCell(7).setCellValue(stockVO.getPacking());
            // 自营还是代仓
            if (SkuTypeEnum.SELF_SUPPORT.getId().equals(stockVO.getSkuType())) {
                row.createCell(8).setCellValue(SkuTypeEnum.SELF_SUPPORT.getStatus());
            } else if (SkuTypeEnum.SUBSTITUTE_WAREHOUSE.getId().equals(stockVO.getSkuType())) {
                row.createCell(8).setCellValue(SkuTypeEnum.SUBSTITUTE_WAREHOUSE.getStatus());
            } else {
                row.createCell(8).setCellValue("");
            }
            row.createCell(9).setCellValue(Objects.equals(NumberUtils.INTEGER_ZERO, stockVO.getIsDomestic()) ? "进口" : "国产");
            row.createCell(10).setCellValue(stockVO.getStoreQuantity());
            row.createCell(11).setCellValue(stockVO.getSafeQuantity());
            row.createCell(12).setCellValue(stockVO.getLockQuantity());
            row.createCell(13).setCellValue(stockVO.getRoadQuantity());
            row.createCell(14).setCellValue(stockVO.getStoreQuantity() - stockVO.getLockQuantity() - stockVO.getSafeQuantity());
            String key = stockVO.getSku() + ":" + stockVO.getAreaNo();
            row.createCell(15).setCellValue(onSaleSet.contains(key) ? "上架中" : "未上架");

            sheet2RowNum++;
        }
    }

    @Override
    public void handleAuditResult(DtsModel dtsModel) {
        List<Map<String, String>> oldDataList = dtsModel.getOld();
        for (int i = 0; i < oldDataList.size(); i++) {
            String id = DtsUtils.searchChangeId(dtsModel, i, STATUS, ID);
            if (Objects.isNull(id)) {
                continue;
            }
            Long inventoryId = Long.parseLong(id);
            Inventory inventory = inventoryMapper.selectByPrimaryKey(inventoryId);

            // 处理代仓申请
            if (Objects.equals(Integer.valueOf(inventory.getCreateType()), ProductsEnum.CreateType.FAN_TAI_AGENT.ordinal())) {
                SummerfarmProductAuditResultDTO summerfarmProductAuditResultDTO = new SummerfarmProductAuditResultDTO();
                summerfarmProductAuditResultDTO.setSkuId(inventoryId);
                summerfarmProductAuditResultDTO.setAuditResult(inventory.getAuditStatus());
                summerfarmProductAuditResultDTO.setRefuseReason(inventory.getRefuseReason());
                summerfarmProductAuditResultDTO.setAuditTime(inventory.getAuditTime());
                notifyAuditResultToSaaS(summerfarmProductAuditResultDTO);
            }

            Integer auditorAdminId = inventory.getAuditor();
            Admin auditor = adminMapper.selectByPrimaryKey(auditorAdminId);
            if (!Objects.isNull(auditor)) {
                sendAuditMsg(Objects.equals(inventory.getAuditStatus(), SUCCESS) ? Boolean.TRUE : Boolean.FALSE, auditor.getRealname(), inventory.getCreator(), inventory.getSku());
            }
        }
    }

    /**
     * 通知SaaS商品的审核结果
     *
     * @param summerfarmProductAuditResultDTO
     */
    private void notifyAuditResultToSaaS(SummerfarmProductAuditResultDTO summerfarmProductAuditResultDTO) {
        SummerfarmMsgModel msgModel = new SummerfarmMsgModel();
        msgModel.setMsgType(SummerfarmMsgType.PRODUCT_AGENT_APPLY_RESULT);
        msgModel.setMsgData(summerfarmProductAuditResultDTO);
        logger.info("发送异步消息通知SaaS，消息体:{}", JSON.toJSONString(msgModel));
        mqProducer.send(SummerfarmMQTopic.SAAS_MANAGE, null, JSON.toJSONString(msgModel));
    }

    @Override
    public List<String> querySamePropertyInventory(SamePropertyInventoryQueryDTO samePropertyInventoryQueryDTO) {
        Integer pdId = samePropertyInventoryQueryDTO.getPdId();
        Integer type = samePropertyInventoryQueryDTO.getType();
        String unit = samePropertyInventoryQueryDTO.getUnit();
        Integer extType = samePropertyInventoryQueryDTO.getExtType();
        List<SkuPropertyValueDTO> saleValueList = samePropertyInventoryQueryDTO.getSaleValueList();
        List<Inventory> inventories = inventoryMapper.selectByPdIdAndExtType(pdId, InventoryExtTypeEnum.NORMAL.type());
        if (CollectionUtils.isEmpty(inventories)) {
            return new ArrayList<>();
        }
        return getCanBindSkus(pdId, type, unit, extType, saleValueList, inventories);
    }

    /**
     * 获取可绑定的sku列表数据
     *
     * @param pdId          pdId
     * @param type          商品性质
     * @param unit          包装
     * @param saleValueList 销售属性组
     * @param inventories   商品数据
     * @return 可绑定的sku列表
     */
    @Override
    public List<String> getCanBindSkus(Integer pdId, Integer type, String unit,
                                       Integer extType,
                                       List<SkuPropertyValueDTO> saleValueList,
                                       List<Inventory> inventories) {
        List<String> skus = inventories.stream()
                .map(Inventory::getSku).collect(Collectors.toList());
        List<SkuPropertyInfoDTO> productsPropertyValues =
                productsPropertyValueMapper.selectByPdIdAndSkusAndPropertyType(pdId, skus, ProductsPropertTypeEnum.SALE_PROPERTY.getType());
        if (CollectionUtils.isEmpty(productsPropertyValues)) {
            return new ArrayList<>();
        }
        // 查询已绑定的inventory数据
        List<InventoryBind> inventoryBinds = inventoryBindMapper.selectByPdIdAndExtType(pdId.longValue(), extType);
        // 已被绑定的sku
        Set<String> bindSkus = new HashSet<>();
        if (CollectionUtils.isNotEmpty(inventoryBinds)) {
            bindSkus = inventoryBinds.stream().map(InventoryBind::getBindSku).collect(Collectors.toSet());
        }
        List<String> canBindSkus = new ArrayList<>();
        // 执行属性比较，找到完全符合条件的可绑定的sku
        for (SkuPropertyInfoDTO productsPropertyValue : productsPropertyValues) {
            String sku = productsPropertyValue.getSku();
            // 如果当前sku已被绑定，当前sku不能够继续被绑定
            if (bindSkus.contains(sku)) {
                continue;
            }
            // 比较包装和性质
            List<Inventory> sameTypeAndUnitInv = inventories.stream()
                    .filter(item -> compareTypeAndUnit(item, type, unit))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sameTypeAndUnitInv)) {
                continue;
            }
            List<SkuPropertyValueDTO> skuPropertyValues = productsPropertyValue.getSkuPropertyValues();
            boolean isCanBind = true;
            for (SkuPropertyValueDTO skuPropertyValue : skuPropertyValues) {
                Integer productsPropertyId = skuPropertyValue.getProductsPropertyId();
                // 判断是否有同一项属性配置
                Optional<SkuPropertyValueDTO> optionalProductsPropertyValueDTO =
                        saleValueList.stream().filter(item -> Objects.equals(productsPropertyId, item.getProductsPropertyId()))
                                .findFirst();
                if (!optionalProductsPropertyValueDTO.isPresent()) {
                    isCanBind = false;
                    break;
                }
                // 判断同一项属性的属性值是否相等
                SkuPropertyValueDTO productsPropertyValueDTO = optionalProductsPropertyValueDTO.get();
                if (!Objects.equals(productsPropertyValueDTO.getProductsPropertyValue(), skuPropertyValue.getProductsPropertyValue())) {
                    isCanBind = false;
                    break;
                }
            }
            if (isCanBind) {
                canBindSkus.add(sku);
            }
        }
        return canBindSkus;
    }

    private boolean compareTypeAndUnit(Inventory inventory, Integer type, String unit) {
        Integer curType = inventory.getType();
        String curUnit = inventory.getUnit();
        return Objects.equals(curType, type) && Objects.equals(curUnit, unit);
    }

    @Override
    public void initBindTask() {
        logger.info("开始执行初始化绑定关系任务");
        int pageIndex = 1;
        int pageSize = 30;
        List<Integer> extTypes = Lists.newArrayList(TEMPORARY_INSURANCE.type(),
                BROKEN_BAG.type());
        PageHelper.startPage(pageIndex, pageSize);
        List<Inventory> inventories = inventoryMapper.selectByExtTypes(extTypes);
        Set<String> noCanBindSku = new HashSet<>();
        Set<String> multiCanBindSku = new HashSet<>();
        while (CollectionUtils.isNotEmpty(inventories)) {
            for (Inventory inventory : inventories) {
                inventoryService.initBind(inventory, noCanBindSku, multiCanBindSku);
            }
            pageIndex = pageIndex + 1;
            PageHelper.startPage(pageIndex, pageSize);
            inventories = inventoryMapper.selectByExtTypes(extTypes);
        }
        if (CollectionUtils.isNotEmpty(noCanBindSku)) {
            logger.info("无法建立绑定关系的SKU:{}", JSON.toJSONString(noCanBindSku));
        }
        if (CollectionUtils.isNotEmpty(multiCanBindSku)) {
            logger.info("多匹配绑定关系的SKU:{}", JSON.toJSONString(multiCanBindSku));
        }
        logger.info("执行初始化绑定关系任务结束");
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void initBind(Inventory inventory, Set<String> noCanBindSku, Set<String> multiCanBindSku) {
        try {
            Long pdId = inventory.getPdId();
            String sku = inventory.getSku();
            List<SkuPropertyValueDTO> saleValues = productsPropertyValueMapper.selectByPdIdAndSkuAndPropertyType(pdId.intValue(), sku,
                    ProductsPropertTypeEnum.SALE_PROPERTY.getType());
            SamePropertyInventoryQueryDTO samePropertyInventoryQueryDTO = new SamePropertyInventoryQueryDTO();
            samePropertyInventoryQueryDTO.setPdId(pdId.intValue());
            samePropertyInventoryQueryDTO.setUnit(inventory.getUnit());
            samePropertyInventoryQueryDTO.setType(inventory.getType());
            samePropertyInventoryQueryDTO.setSaleValueList(saleValues);
            List<String> canBindSku = querySamePropertyInventory(samePropertyInventoryQueryDTO);
            if (CollectionUtils.isEmpty(canBindSku)) {
                noCanBindSku.add(sku);
                return;
            }
            if (canBindSku.size() > 1) {
                multiCanBindSku.add(sku);
                return;
            }
            String bindSku = canBindSku.get(0);
            // 判断是否已经存在绑定关系 当前sku已经存在绑定关系，则更新绑定关系
            InventoryBind inventoryBind = inventoryBindMapper.selectByBindSkuAndExtType(pdId, sku, inventory.getExtType());
            if (inventoryBind != null) {
                inventoryBind.setPdId(pdId);
                inventoryBind.setSku(sku);
                inventoryBind.setBindSku(bindSku);
                inventoryBindMapper.updateBindSkuById(inventoryBind.getId(), bindSku);
            } else {
                InventoryReq inventoryReq = new InventoryReq();
                inventoryReq.setPdId(pdId);
                inventoryReq.setExtType(inventory.getExtType());
                inventoryReq.setSku(sku);
                inventoryReq.setBindSku(bindSku);
                createBindSku(inventoryReq, sku);
            }
        } catch (Exception e) {
            logger.error("创建绑定关系异常, sku:{}", inventory.getSku(), e);
        }
    }

    @Override
    public Inventory selectById(Long id) {
        id = Optional.ofNullable(id).orElse(NumberUtils.LONG_ZERO);
        Inventory query = new Inventory();
        query.setInvId(id);
        return inventoryMapper.selectOne(query);
    }

    @Override
    public List<Inventory> selectByIds(List<Long> invIds) {

        return inventoryMapper.selectByIds(invIds);
    }

    @Override
    public AjaxResult querySku(String skuName, Integer id) {
        List<QuerySkuVo> info = crmBdConfigMapper.querySku(skuName, id);
        return AjaxResult.getOK(info);
    }

    @Override
    public String getCategory(List<String> skus) {
        List<InventoryWMSInfo> inventoryWMSInfos = inventoryMapper.batchSelectSkuWMSInfo(skus);
        if (org.springframework.util.CollectionUtils.isEmpty(inventoryWMSInfos)) {
            return null;
        }
        List<Integer> category = inventoryWMSInfos.stream().map(InventoryWMSInfo::getCategoryType).collect(Collectors.toList());
        if (category.stream().allMatch(o -> Objects.equals(o, Category.FRUIT_TYPE))) {
            return FruitCategoryEnum.FRESH.getDesc();
        } else if (category.stream().noneMatch(o -> Objects.equals(o, Category.FRUIT_TYPE))) {
            return FruitCategoryEnum.FRESH_NOT.getDesc();
        } else {
            return FruitCategoryEnum.OTHER.getDesc();
        }
    }

    @Override
    public BigDecimal getWeightNums(List<SkuCapacityDTO> skuCapacityDTOList) {
        BigDecimal skuCapacity = BigDecimal.ZERO;
        if (CollectionUtil.isEmpty(skuCapacityDTOList)) {
            return skuCapacity;
        }
        List<String> skuWeights = skuCapacityDTOList.stream().map(SkuCapacityDTO::getSku).collect(Collectors.toList());
        List<InventoryVO> inventoryVOS = inventoryMapper.queryWeightNumBySkus(skuWeights);
        for (SkuCapacityDTO skuCapacityDTO : skuCapacityDTOList) {
            for (InventoryVO inventoryVO : inventoryVOS) {
                if (skuCapacityDTO.getSku().equals(inventoryVO.getSku())) {
                    skuCapacity = skuCapacity.add(inventoryVO.getWeightNum().multiply(new BigDecimal(skuCapacityDTO.getQuantity())));
                }
            }
        }
        skuCapacity = skuCapacity.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(1000));
        return skuCapacity;
    }

    @Override
    public AjaxResult selectBySkuMsg(String sku) {
        InventoryVO inventoryVO = inventoryMapper.selectBySkuMsg(sku);
        return AjaxResult.getOK(inventoryVO);
    }


    @Override
    public Integer selectSkuVisible(String sku) {
        return inventoryMapper.selectSkuVisible(sku);
    }

    @Override
    public List<SummerfarmSynchronizedSkuDTO> queryNeedSynchronizedSkuInfo(String startTime, String endTime) {
        // 查询昨日新增的sku
        List<SummerfarmSynchronizedSkuDTO> summerfarmSynchronizedSkuDTOS = inventoryMapper.queryNeedSynchronizedSkuInfo(startTime, endTime);
        return summerfarmSynchronizedSkuDTOS;
    }

    @Override
    public SummerfarmSynchronizedSkuDTO queryNeedSynchronizedSkuInfoBySkuId(Long skuId) {
        SummerfarmSynchronizedSkuDTO summerfarmSynchronizedSkuDTOS = inventoryMapper.queryNeedSynchronizedSkuInfoBySkuId(skuId);
        return summerfarmSynchronizedSkuDTOS;
    }

    @Override
    public List<Inventory> selectByPdIdAndAdminId(Integer spuId, Integer adminId) {
        return inventoryMapper.selectByPdIdAndAdminId(spuId, adminId);
    }

    /**
     * 根据skuList搜索 sku等商品信息
     *
     * @param * @Param skuList:
     * @date 2023/3/1 15:52
     */
    @Override
    public List<InventorySkuVO> selectSkuInfoBySkuList(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return null;
        }
        return inventoryMapper.selectSkuInfoBySkuList(skuList);
    }

    /**
     * 根据skuList搜索
     *
     * @param * @Param skuList:
     * @date 2023/3/1 15:52
     */
    @Override
    public List<Inventory> selectBySkuList(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return null;
        }
        return inventoryMapper.selectBySkuList(skuList);
    }

    @Override
    public Inventory selectBySku(String sku) {
        if (Objects.isNull(sku)) {
            return null;
        }
        return inventoryMapper.selectOneBySku(sku);
    }

    @Override
    public void handleMsgNotice(DtsModel dtsModel) {
        logger.info("receive inventory data, dtsModel:{}", JSONUtil.toJsonStr(dtsModel));
        if (!Objects.equals(dtsModel.getType(), UPDATE)) {
            return;
        }
        try {
            this.sendInventoryBaseInfoMsg(dtsModel);
        } catch (Exception e) {
            logger.error("处理商品消息失败 dtsModel:{}", JSONUtil.toJsonStr(dtsModel), e);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editAgentCreateType(String sku, String createType) {
        Inventory record = new Inventory();
        record.setSku(sku);
        record.setCreateType(createType);
        record.setSubType(InventorySubTypeEnum.AGENT_WAREHOUSE.getSubType());
        if (CreateTypeEnums.SELF_AND_AGENT.getType().toString().equals(createType)) {
            record.setOutdated(ProductsEnum.Outdated.CREATING.getCode());
            record.setAuditStatus(ProductsEnum.AuditStatus.WAIT_AUDIT.ordinal());
            record.setTaskType(1);
        }
        inventoryMapper.update(record);
        if (CreateTypeEnums.SELF_AND_AGENT.getType().toString().equals(createType)) {
            Inventory inventory = inventoryMapper.selectOneBySku(sku);
            Products products = new Products();
            products.setPdId(inventory.getPdId());
            products.setCreateType(CreateTypeEnums.SELF_AND_AGENT.getType());
            productsMapper.updateByPrimaryKeySelective(products);
            productsService.insertResponsible(products.getPdId());
            List<AreaStore> areaStoreList = areaStoreMapper.selectListBySkuAndTenantId(sku, BaseConstant.XIANMU_TENANT_ID);
            if (CollectionUtils.isEmpty(areaStoreList)) {
                try {
                    areaStoreMapper.initAfterApplySaasAgentSku(sku, BaseConstant.XIANMU_TENANT_ID);
                } catch (Exception e) {
                    logger.error("代仓申请 initAfterApplySaasAgentSku fail, sku:{}, tenantId:{}", sku, BaseConstant.XIANMU_TENANT_ID, e);
                }

            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelAgentSku(Long skuId) {
        Inventory inventory = inventoryService.selectById(skuId);
        if (Objects.isNull(inventory)) {
            throw new BizException("未查询到该货品信息");
        }

        if (!CreateTypeEnums.SELF_AND_AGENT.getType().toString().equals(inventory.getCreateType())) {
            throw new BizException("只有代仓品才可取消!");
        }
        if (ProductsEnum.AuditStatus.WAIT_AUDIT.ordinal() != inventory.getAuditStatus()) {
            throw new BizException("只有待审核才可取消！");
        }

        // 将申请代仓时的字段改为原来的状态
        inventory.setCreateType(CreateTypeEnums.SELF.getType().toString());
        inventory.setOutdated(ProductsEnum.Outdated.VALID.getCode());
        inventory.setAuditStatus(ProductsEnum.AuditStatus.SUCCESS.ordinal());
        inventory.setTaskType(0);
        inventoryMapper.update(inventory);

        Products products = new Products();
        products.setPdId(inventory.getPdId());
        products.setCreateType(CreateTypeEnums.SELF.getType());
        productsMapper.updateByPrimaryKeySelective(products);
    }

    @Override
    public List<SummerFarmSynchronizedSkuReq> queryNeedSyncSkuList(List<Long> pdIds, List<Long> skuIds) {
        List<SummerFarmSynchronizedSkuReq> syncSkuList = inventoryMapper.queryNeedSyncSkuList(pdIds, skuIds);
        return syncSkuList;
    }

    @Override
    public List<XmSyncSkuReq> queryNeedSyncSkuListForGoods(List<Long> pdIds, List<Long> skuIds) {
        List<XmSyncSkuReq> syncSkuList = inventoryMapper.queryNeedSyncSkuListForGoods(pdIds, skuIds);
        return syncSkuList;
    }

    @Override
    public List<Long> querySkuIdsByAdminId(Integer adminId) {
        return inventoryMapper.querySkuIdsByAdminId(adminId);
    }

    @Override
    public AjaxResult queryAllSku(InventoryReq selectKeys) {
        if (Objects.isNull(selectKeys) || Objects.isNull(selectKeys.getSku())) {
            throw new BizException("sku不能为空");
        }
        List<SKUVO> skuvoList = inventoryMapper.queryAllSku(selectKeys.getSku());
        return AjaxResult.getOK(skuvoList);
    }

    private void sendInventoryBaseInfoMsg(DtsModel dtsModel) {
        if (Objects.isNull(dtsModel)) {
            return;
        }
        List<Map<String, String>> oldList = dtsModel.getOld();
        List<Map<String, String>> dataList = dtsModel.getData();
        for (Map<String, String> oldMap : oldList) {
            String volume = oldMap.get(VOLUME);
            String weightNum = oldMap.get(WEIGHT_NUM);
            if (StringUtils.isBlank(volume) && StringUtils.isBlank(weightNum)) {
                return;
            }
            int index = oldList.indexOf(oldMap);
            Map<String, String> dataMap = dataList.get(index);
            String id = dataMap.get(ID);
            Inventory inventory = inventoryMapper.selectByPrimaryKey(Long.valueOf(id));
            if (Objects.isNull(inventory)) {
                logger.error("sendInventoryBaseInfoMsg 未查询到sku信息， skuId:{}", Long.valueOf(id));
                return;
            }
            // 只发送代仓品数据
            if (!Objects.equals(inventory.getCreateType(), String.valueOf(ProductsEnum.CreateType.FAN_TAI_AGENT.ordinal()))) {
                return;
            }
            // 发送消息
            SummerFarmSkuMsgDTO msgBody = SummerFarmSkuMsgDTO.builder()
                    .skuId(inventory.getInvId())
                    .volume(inventory.getVolume())
                    .weightNum(inventory.getWeightNum())
                    .build();
            SummerfarmMsgModel msgModel = new SummerfarmMsgModel();
            msgModel.setMsgType(SummerfarmMsgType.AGENT_SKU_BASE_INFO_SYNC);
            msgModel.setMsgData(msgBody);
            mqProducer.send(SummerfarmMQTopic.SUMMERFARM_TO_COSFO_MANAGE, SummerfarmMqTag.INVENTORY_SYNC, msgModel);

        }
    }
}

