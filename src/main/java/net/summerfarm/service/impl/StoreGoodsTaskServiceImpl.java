package net.summerfarm.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.netty.util.internal.StringUtil;
import net.summerfarm.biz.finance.util.ExceptionUtil;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.controller.convert.StoreGoodsTaskConvert;
import net.summerfarm.enums.*;
import net.summerfarm.mapper.GoodsTaskWhiteListMapper;
import net.summerfarm.mapper.StoreGoodsTaskMapper;
import net.summerfarm.mapper.manage.AreaStoreMapper;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.manage.ProductsMapper;
import net.summerfarm.model.DTO.GoodsCheckTaskDO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.GoodsCheckTaskPageQuery;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.ProductsVO;
import net.summerfarm.model.vo.StoreStockVO;
import net.summerfarm.service.StoreGoodsTaskService;
import net.summerfarm.service.stockTransfer.enums.StorageLocationEnum;
import net.summerfarm.warehouse.enums.WarehouseStorageCenterEnum;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.model.vo.WarehouseStorageCenterVO;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.xianmu.common.exception.ProviderException;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @desc 库内货品任务校验服务
 *
 * @Date 2022/11/7 15:29
 **/
@Service
public class StoreGoodsTaskServiceImpl implements StoreGoodsTaskService {

    private final static Logger logger = LoggerFactory.getLogger(StoreGoodsTaskServiceImpl.class);

    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private StoreGoodsTaskMapper storeGoodsTaskMapper;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private GoodsTaskWhiteListMapper goodsTaskWhiteListMapper;
    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    private ConfigMapper configMapper;

    @Override
    public Long createGoodsCheckTask(GoodsCheckTaskDO createDO) {
        // 任务状态为待完成的任务无需重复发起任务
        List<StoreGoodsTaskPO> taskPOList = storeGoodsTaskMapper.selectBySkuAndStatus(createDO.getSku(), GoodsCheckTaskStatusEnum.PROCESSING.getType());
        ExceptionUtil.checkAndThrow(CollectionUtils.isEmpty(taskPOList), String.format("sku:%s存在未完成的任务，无需再次发起任务", createDO.getSku()));
        // 业务参数校验,商品信息
        Inventory inventory = inventoryMapper.selectOneBySku(createDO.getSku());
        ExceptionUtil.checkAndThrow(Objects.nonNull(inventory), String.format("商品sku信息不存在, sku:%s", createDO.getSku()));
        ProductsVO productsVO = productsMapper.selectByPdId(inventory.getPdId());
        ExceptionUtil.checkAndThrow(Objects.nonNull(productsVO), String.format("商品spu信息不存在, pdId:%s", productsVO.getPdId()));
        // 聚合商品信息
        assembleGoodsCheckTask(createDO, inventory, productsVO);
        StoreGoodsTaskPO taskPO = StoreGoodsTaskConvert.convertTaskPO(createDO);
        storeGoodsTaskMapper.insert(taskPO);
        return taskPO.getId();
    }

    @Override
    public void createGoodsCheckTaskOfDay() {
        logger.info("每日巡检创建商品校验任务开始");
        // 捞取符合条件的商品信息
        List<Inventory> weightNumList = inventoryMapper.selectByMinWeightNum(BigDecimal.valueOf(50));
        List<Inventory> volumeList = inventoryMapper.selectByMinVolume(BigDecimal.valueOf(1));
        List<String> skuList = Lists.newArrayList();
        skuList.addAll(weightNumList.stream().map(Inventory::getSku).collect(Collectors.toList()));
        skuList.addAll(weightNumList.stream().map(Inventory::getSku).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(skuList)) {
            logger.info("无符合校验数据的商品信息，任务结束");
            return;
        }
        skuList = skuList.stream().distinct().collect(Collectors.toList());
        logger.info("weightNumList:{}, volumeList:{}, skuList:{}",
                JSONUtil.toJsonStr(weightNumList), JSONUtil.toJsonStr(volumeList), JSONUtil.toJsonStr(skuList));
        // 查询有库存的sku
        List<String> inStockList = areaStoreMapper.selectInStockBySkuList(skuList);
        // 过滤任务状态为待完成sku
        List<String> processList = storeGoodsTaskMapper.selectBySkuListAndStatus(inStockList, GoodsCheckTaskStatusEnum.PROCESSING.getType());
        inStockList.removeAll(processList);
        // 过滤白名单商品
        List<GoodsTaskWhiteListPO> whiteListPOList = goodsTaskWhiteListMapper.selectListByPushStatus(GoodsCheckTaskPushEnum.CLOSE.getType());
        inStockList.removeAll(whiteListPOList.stream().map(GoodsTaskWhiteListPO::getSku).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(inStockList)) {
            logger.info("无符合校验数据的商品信息，任务结束");
            return;
        }
        // 根据sku查询库存最多的仓，生成任务单 opt:待观察，查询结果数据太大，内存打爆
        List<AreaStore> areaStoreList = areaStoreMapper.selectBySkuList(inStockList);
        logger.info("过滤非鲜沐仓前数据：{}", JSON.toJSONString(areaStoreList));
        // 过滤非鲜沐仓
        areaStoreList = areaStoreList.stream().filter(center -> BaseConstant.XIANMU_TENANT_ID.equals(center.getTenantId())).collect(Collectors.toList());
        logger.info("过滤非鲜沐仓后数据：{}", JSON.toJSONString(areaStoreList));
        Map<String, List<AreaStore>> storeMap = areaStoreList.stream().collect(Collectors.groupingBy(AreaStore::getSku));
        // 需要过滤仓数据
        List<Integer> filterWarehouseList = Lists.newArrayList();
        List<WarehouseStorageCenter> centerList = warehouseStorageService.selectByWarehouseNoList(
                areaStoreList.stream().map(AreaStore::getAreaNo).distinct().collect(Collectors.toList()));
        centerList = centerList.stream().filter(center -> BaseConstant.XIANMU_TENANT_ID.equals(center.getTenantId())).collect(Collectors.toList());
        List<Integer> closeList = centerList.stream().filter(center -> center.getStatus() == 0)
                .map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList());
        // 过滤非本部仓
        List<Integer> notInternalList = centerList.stream().filter(center -> WarehouseStorageCenterEnum.Type.INTERNAL_AREA.ordinal() != center.getType())
                .map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList());
        filterWarehouseList.addAll(closeList);
        filterWarehouseList.addAll(notInternalList);
        logger.info("sku集合：{}", JSON.toJSONString(storeMap));
        storeMap.forEach((sku, storeList) -> {
            // 过滤测试仓
            Config config = configMapper.selectOne("testWarehouse");
            if (Objects.nonNull(config) && !StringUtils.isEmpty(config.getValue())) {
                filterWarehouseList.addAll(JSONUtil.parseArray(config.getValue()).toList(Integer.class));
            }
            List<Integer> finalTestWarehouseList = filterWarehouseList;
            AreaStore areaStore = storeList.stream()
                    .filter(store -> !finalTestWarehouseList.contains(store.getAreaNo()))
                    .max(Comparator.comparingInt(AreaStore::getQuantity)).get();
            if (areaStore.getQuantity() <= 0) {
                logger.info("sku:{}在各个仓库均无库存，无需创建校验任务", sku);
                return;
            }
            // 插入任务
            GoodsCheckTaskDO taskDO = GoodsCheckTaskDO.builder()
                    .taskSource(GoodsCheckTaskSourceEnum.TIMED.getType())
                    .taskStatus(GoodsCheckTaskStatusEnum.PROCESSING.getType())
                    .warehouseNo(areaStore.getAreaNo())
                    .sku(sku)
                    .pushStatus(GoodsCheckTaskPushEnum.OPEN.getType())
                    .creator("系统-每日巡检")
                    .operator("系统-每日巡检")
                    .build();
            try {
                createGoodsCheckTask(taskDO);
                logger.info("每日巡检任务生成结果：{}", JSON.toJSONString(taskDO));
            } catch (Exception e) {
                logger.error("创建商品校验任务, 执行参数:{}", JSONUtil.toJsonStr(taskDO), e);
            }

        });
        logger.info("每日巡检创建商品校验任务结束, 共生成任务{}条，sku列表{}", storeMap.keySet().size(), JSONUtil.toJsonStr(storeMap.keySet()));
    }

    @Override
    public void createGoodsCheckTaskOfMonth() {
        logger.info("每月推送创建商品校验任务开始");

        List<String> failResult = Lists.newArrayList();
        // 查询库存大于0的所有sku数据
        List<String> skuList = areaStoreMapper.selectInStock();
        // 查询已经生成任务的sku数据，筛选需要处理的sku数据
        List<StoreGoodsTaskPO> taskPOList = storeGoodsTaskMapper.selectBySkuList(skuList);
        List<String> existSkuList = taskPOList.stream().map(StoreGoodsTaskPO::getSku).distinct().collect(Collectors.toList());
        skuList.removeAll(existSkuList);
        if (CollectionUtils.isEmpty(skuList)) {
            logger.info("无符合校验数据的商品信息，任务结束");
            return;
        }
        logger.info("需要创建任务的sku列表：{}", JSONUtil.toJsonStr(skuList));
        // 查询仓库列表
        Config capacityConfig = configMapper.selectOne("warehouseCapacity");
        ExceptionUtil.checkAndThrow(Objects.nonNull(capacityConfig), "未查询到仓库容量配置项，请确认数据配置");
        Integer capacity = Integer.parseInt(capacityConfig.getValue());
        Map<Integer, Integer> taskMap = this.initWarehouseData();
        for (Map.Entry<Integer, Integer> entry : taskMap.entrySet()) {
            if (CollectionUtils.isEmpty(skuList)) {
                logger.info("所有sku已分配完毕，任务结束");
                break;
            }
            Integer warehouseNo = entry.getKey();
            Integer useCapacity = entry.getValue();
            Iterator<String> iterator = skuList.iterator();
            logger.info("{}号仓开始分配校验任务", warehouseNo);
            while (iterator.hasNext()) {
                String sku = iterator.next();
                // 判断sku在仓内是否有库存
                AreaStore areaStore = areaStoreMapper.selectByStoreNoAndSku(warehouseNo, sku);
                if (Objects.isNull(areaStore) || !BaseConstant.XIANMU_TENANT_ID.equals(areaStore.getTenantId())) {
                    logger.info("{}号仓非鲜沐仓，过滤", warehouseNo);
                    continue;
                }
                if (areaStore.getQuantity() <= 0) {
                    logger.info("sku:{}在{}仓无库存记录，无需生成校验任务", sku, warehouseNo);
                    continue;
                }
                // 根据sku+warehouse查询是否生成过任务
                List<StoreGoodsTaskPO> goodsTaskPOList = storeGoodsTaskMapper.selectBySkuAndWarehouseNo(sku, warehouseNo);
                if (CollectionUtils.isEmpty(goodsTaskPOList)) {
                    // 判断useCapacity
                    if (useCapacity.equals(capacity)) {
                        // 结束循环
                        break;
                    }
                    // useCapacity+1
                    useCapacity = useCapacity + 1;
                    // 创建任务
                    try {
                        GoodsCheckTaskDO taskDO = GoodsCheckTaskDO.builder()
                                .taskSource(GoodsCheckTaskSourceEnum.TIMED.getType())
                                .taskStatus(GoodsCheckTaskStatusEnum.PROCESSING.getType())
                                .warehouseNo(warehouseNo)
                                .sku(sku)
                                .pushStatus(GoodsCheckTaskPushEnum.OPEN.getType())
                                .creator("系统-每月推送")
                                .operator("系统-每月推送")
                                .build();
                        createGoodsCheckTask(taskDO);
                        logger.info("每月推送任务生成结果：{}", JSON.toJSONString(taskDO));
                    } catch (Exception e) {
                        logger.warn("任务创建失败 失败原因：{}, 任务数据 sku:{} 仓:{}", e.getMessage(), sku, warehouseNo);
                        failResult.add(sku);
                        useCapacity = useCapacity - 1;
                    }

                    // 剔除sku
                    iterator.remove();
                }

            }
        }

        logger.info("每月推送创建商品校验任务结束, 异常数据 sku：{}", JSONUtil.toJsonStr(failResult));
    }

    @Override
    public void createGoodsCheckTaskOfNewGoods() {
        logger.info("每日推送新品创建商品校验任务开始");
        // 根据上新状态查询商品sku列表 状态为上新成功&&创建时间在15天内
        List<Inventory> inventoryList = inventoryMapper.selectNewSku(0, 1);
        if (CollectionUtils.isEmpty(inventoryList)) {
            logger.info("没有需要处理的新品数据");
            return;
        }
        List<String> newSkuList = inventoryList.stream().map(Inventory::getSku).collect(Collectors.toList());
        // 判断sku是否存在待完成任务，待完成任务不再重复生成
        List<StoreGoodsTaskPO> processingTaskList = storeGoodsTaskMapper.selectByStatus(GoodsCheckTaskStatusEnum.PROCESSING.getType());
        List<String> processingSkuList = processingTaskList.stream().map(StoreGoodsTaskPO::getSku).distinct().collect(Collectors.toList());
        newSkuList.removeAll(processingSkuList);
        if (CollectionUtils.isEmpty(newSkuList)) {
            logger.info("没有需要处理的新品数据");
            return;
        }
        // 判断是否生成过任务，已经生成过任务则不会再生成
        List<StoreGoodsTaskPO> taskPOList = storeGoodsTaskMapper.selectBySkuList(newSkuList);
        List<String> existSkuList = taskPOList.stream().map(StoreGoodsTaskPO::getSku).distinct().collect(Collectors.toList());
        newSkuList.removeAll(existSkuList);
        if (CollectionUtils.isEmpty(newSkuList)) {
            logger.info("没有需要处理的新品数据");
            return;
        }
        // 查询商品库存大于0的数据，给库存最多的仓生成任务
        List<AreaStore> areaStoreList = areaStoreMapper.selectBySkuList(newSkuList);
        logger.info("新品校验过滤非鲜沐仓前数据：{}", JSON.toJSONString(areaStoreList));
        // 过滤非鲜沐仓
        areaStoreList = areaStoreList.stream().filter(center -> BaseConstant.XIANMU_TENANT_ID.equals(center.getTenantId())).collect(Collectors.toList());
        logger.info("新品校验过滤非鲜沐仓后数据：{}", JSON.toJSONString(areaStoreList));
        Map<String, List<AreaStore>> storeMap = areaStoreList.stream().collect(Collectors.groupingBy(AreaStore::getSku));
        List<Integer> testWarehouseList = Lists.newArrayList();
        List<WarehouseStorageCenter> centerList = warehouseStorageService.selectByWarehouseNoList(
                areaStoreList.stream().map(AreaStore::getAreaNo).distinct().collect(Collectors.toList()));
        // 只查询鲜沐的仓
        centerList = centerList.stream().filter(center -> BaseConstant.XIANMU_TENANT_ID.equals(center.getTenantId())).collect(Collectors.toList());
        List<Integer> closeList = centerList.stream().filter(center -> center.getStatus() == 0)
                .map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList());
        List<Integer> notInternalList = centerList.stream().filter(center -> WarehouseStorageCenterEnum.Type.INTERNAL_AREA.ordinal() != center.getType())
                .map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList());
        testWarehouseList.addAll(closeList);
        testWarehouseList.addAll(notInternalList);
        logger.info("新品校验sku集合：{}", JSON.toJSONString(storeMap));
        storeMap.forEach((sku, storeList) -> {
            // 过滤测试仓
            Config config = configMapper.selectOne("testWarehouse");
            if (Objects.nonNull(config) && !StringUtils.isEmpty(config.getValue())) {
                testWarehouseList.addAll(JSONUtil.parseArray(config.getValue()).toList(Integer.class));
            }
            AreaStore areaStore = storeList.stream()
                    .filter(store -> !testWarehouseList.contains(store.getAreaNo()))
                    .max(Comparator.comparingInt(AreaStore::getQuantity)).get();
            if (areaStore.getQuantity() <= 0) {
                logger.info("sku:{}在各个仓库均无库存，无需创建校验任务", sku);
                return;
            }
            // 插入任务
            GoodsCheckTaskDO taskDO = GoodsCheckTaskDO.builder()
                    .taskSource(GoodsCheckTaskSourceEnum.TIMED.getType())
                    .taskStatus(GoodsCheckTaskStatusEnum.PROCESSING.getType())
                    .warehouseNo(areaStore.getAreaNo())
                    .sku(sku)
                    .pushStatus(GoodsCheckTaskPushEnum.OPEN.getType())
                    .creator("系统-新品校验")
                    .operator("系统-新品校验")
                    .build();
            try {
                createGoodsCheckTask(taskDO);
                logger.info("新品校验任务生成结果：{}", JSON.toJSONString(taskDO));
            } catch (Exception e) {
                logger.error("创建商品校验任务, 执行参数:{}", JSONUtil.toJsonStr(taskDO), e);
            }

        });
        logger.info("每日推送新品创建商品校验任务结束, 共生成任务{}条，sku列表{}", storeMap.keySet().size(), JSONUtil.toJsonStr(storeMap.keySet()));
    }

    @Override
    public void exportGoodsCheckTask(GoodsCheckTaskPageQuery query) {
        List<StoreGoodsTaskPO> taskPOList = storeGoodsTaskMapper.selectList(query);
        ExceptionUtil.checkAndThrow(!CollectionUtils.isEmpty(taskPOList), "excel为空文件");
        List<Integer> warehouseNoList = taskPOList.stream()
                .map(StoreGoodsTaskPO::getWarehouseNo).distinct().collect(Collectors.toList());
        List<WarehouseStorageCenter> centerList = warehouseStorageService.selectByWarehouseNoList(warehouseNoList);
        Map<Integer, WarehouseStorageCenter> centerMap = centerList.stream()
                .collect(Collectors.toMap(WarehouseStorageCenter::getWarehouseNo, Function.identity(), (a, b) -> a));

        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("任务编码");
        title.createCell(1).setCellValue("仓库");
        title.createCell(2).setCellValue("sku");
        title.createCell(3).setCellValue("商品名称");
        title.createCell(4).setCellValue("规格");
        title.createCell(5).setCellValue("储存区域");
        title.createCell(6).setCellValue("长(cm)");
        title.createCell(7).setCellValue("宽(cm)");
        title.createCell(8).setCellValue("高(cm)");
        title.createCell(9).setCellValue("重量(kg)");
        title.createCell(10).setCellValue("包装");
        title.createCell(11).setCellValue("进口/国产");
        title.createCell(12).setCellValue("存储温区");
        title.createCell(13).setCellValue("保质期类型（固定时长/到期时间）");
        title.createCell(14).setCellValue("保质期时长");
        title.createCell(15).setCellValue("任务状态");
        title.createCell(16).setCellValue("生成时间");

        AtomicInteger index = new AtomicInteger(1);
        taskPOList.forEach(taskPO -> {
            Row row = sheet.createRow(index.get());
            WarehouseStorageCenter center = centerMap.get(taskPO.getWarehouseNo());
            row.createCell(0).setCellValue(taskPO.getId());
            row.createCell(1).setCellValue(Objects.nonNull(center) ? center.getWarehouseName() : StringUtil.EMPTY_STRING);
            row.createCell(2).setCellValue(taskPO.getSku());
            row.createCell(3).setCellValue(Optional.ofNullable(taskPO.getSkuName()).orElse(StringUtil.EMPTY_STRING));
            row.createCell(4).setCellValue(taskPO.getWeight());
            row.createCell(5).setCellValue(StorageLocationEnum.convert(taskPO.getStorageLocation()));
            row.createCell(6).setCellValue(Optional.ofNullable(VolumeUtil.getLength(taskPO.getVolume())).orElse(StringUtil.EMPTY_STRING));
            row.createCell(7).setCellValue(Optional.ofNullable(VolumeUtil.getWidth(taskPO.getVolume())).orElse(StringUtil.EMPTY_STRING));
            row.createCell(8).setCellValue(Optional.ofNullable(VolumeUtil.getHigh(taskPO.getVolume())).orElse(StringUtil.EMPTY_STRING));
            row.createCell(9).setCellValue(Objects.nonNull(taskPO.getWeightNum()) ? taskPO.getWeightNum().toPlainString() : StringUtil.EMPTY_STRING);
            row.createCell(10).setCellValue(taskPO.getUnit());
            row.createCell(11).setCellValue(IsDomesticEnum.getDescByCode(taskPO.getIsDomestic()));
            row.createCell(12).setCellValue(StorageLocation.getTypeById(taskPO.getStorageLocation()));
            row.createCell(13).setCellValue(QualityTimeTypeEnum.getDescByCode(taskPO.getQualityTimeType()));
            row.createCell(14).setCellValue(taskPO.getQualityTime() + QualityTimeUnitEnum.getDescByCode(taskPO.getQualityTimeUnit()));
            row.createCell(15).setCellValue(GoodsCheckTaskStatusEnum.getDesc(taskPO.getTaskStatus()));
            row.createCell(16).setCellValue(DateUtil.formatDateYmdhms(taskPO.getGmtCreated()));
            index.getAndIncrement();
        });
        String fileName = "库内商品校验任务" + ".xls";
        try {
            ExcelUtils.outputExcel(workbook, fileName, RequestHolder.getResponse());
        } catch (IOException e) {
            throw new ProviderException("导出库内商品校验任务异常");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editGoodsCheckTask(GoodsCheckTaskDO taskDO) {
        // 校验任务是否存在、已被更新
        StoreGoodsTaskPO taskPO = storeGoodsTaskMapper.selectByPrimaryKey(taskDO.getId());
        ExceptionUtil.checkAndThrow(Objects.nonNull(taskPO), "任务单不存在，请刷新后操作");
        ExceptionUtil.checkAndThrow(GoodsCheckTaskStatusEnum.PROCESSING.getType()
                .equals(taskPO.getTaskStatus()), "任务单已完成，请勿重复操作");
        // 更新任务表商品体积 重量 任务状态 推送状态
        StoreGoodsTaskPO storeGoodsTaskPO = StoreGoodsTaskPO.builder()
                .id(taskDO.getId())
                .volume(taskDO.getVolume())
                .weightNum(taskDO.getWeightNum())
                .pushStatus(taskDO.getPushStatus())
                .taskStatus(GoodsCheckTaskStatusEnum.FINISH.getType())
                .operator(taskDO.getOperator())
                .gmtModified(System.currentTimeMillis())
                .unit(taskDO.getUnit())
                .isDomestic(taskDO.getIsDomestic())
                .storageLocation(taskDO.getStorageLocation())
                .qualityTime(taskDO.getQualityTime())
                .qualityTimeUnit(taskDO.getQualityTimeUnit())
                .qualityTimeType(taskDO.getQualityTimeType())
                .picUrl(taskDO.getPicUrl())
                .build();
        storeGoodsTaskMapper.updateByPrimaryKeySelective(storeGoodsTaskPO);
        // 更新商品sku表商品体积 重量
        Inventory inventory = new Inventory();
        inventory.setSku(taskPO.getSku());
        inventory.setVolume(taskDO.getVolume());
        inventory.setWeightNum(taskDO.getWeightNum());
        logger.info("editGoodsCheckTask ,:{}",JSON.toJSONString(inventory));
        inventoryMapper.update(inventory);
        // 修改白名单中sku的推送状态
        GoodsTaskWhiteListPO whiteListPO = goodsTaskWhiteListMapper.selectBySku(taskPO.getSku());
        FunctionalUtil.isTureOrFalse(Objects.isNull(whiteListPO)).hand(()->{
            GoodsTaskWhiteListPO insertPO = GoodsTaskWhiteListPO.builder()
                    .sku(taskPO.getSku())
                    .pushStatus(taskDO.getPushStatus())
                    .creator(taskDO.getOperator())
                    .operator(taskDO.getOperator())
                    .gmtCreated(System.currentTimeMillis())
                    .gmtModified(System.currentTimeMillis())
                    .build();
            goodsTaskWhiteListMapper.insert(insertPO);
        }, () -> {
            GoodsTaskWhiteListPO updatePO = GoodsTaskWhiteListPO.builder()
                    .sku(taskPO.getSku())
                    .pushStatus(taskDO.getPushStatus())
                    .operator(taskDO.getOperator())
                    .gmtModified(System.currentTimeMillis())
                    .build();
            goodsTaskWhiteListMapper.updateBySkuSelective(updatePO);
        });
        // 核对信息变更发送钉钉通知
        sendGoodsCheckDingTalkMsg(taskPO, storeGoodsTaskPO);
        return Boolean.TRUE;
    }

    @Override
    public PageInfo<StoreGoodsTaskPO> pageQueryGoodsCheckTask(GoodsCheckTaskPageQuery query) {
        PageHelper.startPage(query.getPageIndex(), query.getPageSize());
        return PageInfoHelper.createPageInfo(storeGoodsTaskMapper.selectList(query));
    }

    @Override
    public List<StoreStockVO> selectInStockListBySku(String sku) {
        List<StoreStockVO> result = Lists.newArrayList();
        List<AreaStore> areaStoreList = areaStoreMapper.selectInStockListBySku(sku);
        areaStoreList = areaStoreList.stream().filter(areaStore -> areaStore.getQuantity() > 0)
                .sorted(Comparator.comparing(AreaStore::getQuantity).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areaStoreList)) {
            return result;
        }
        // 聚合库存名称信息
        List<WarehouseStorageCenter> storageCenterList = warehouseStorageService.selectByWarehouseNoList(
                areaStoreList.stream().map(AreaStore::getAreaNo).collect(Collectors.toList()));
        Map<Integer, WarehouseStorageCenter> centerMap = storageCenterList.stream()
                .filter(center -> center.getStatus() == 1)
                .collect(Collectors.toMap(WarehouseStorageCenter::getWarehouseNo, Function.identity(), (a, b) -> a));
        areaStoreList.forEach(areaStore -> {
            WarehouseStorageCenter center = centerMap.get(areaStore.getAreaNo());
            if (Objects.isNull(center)) {
                return;
            }
            StoreStockVO storeStockVO = StoreStockVO.builder()
                    .sku(areaStore.getSku())
                    .warehouseNo(areaStore.getAreaNo())
                    .warehouseName(Optional.ofNullable(center.getWarehouseName()).orElse(StringUtil.EMPTY_STRING))
                    .stockQuantity(areaStore.getQuantity())
                    .build();
            result.add(storeStockVO);
        });
        return result;
    }



    private void assembleGoodsCheckTask(GoodsCheckTaskDO createDO, Inventory inventory, ProductsVO productsVO) {
        createDO.setTaskSource(null != createDO.getTaskSource() ? createDO.getTaskSource() : GoodsCheckTaskSourceEnum.MANUAL.getType());
        createDO.setTaskStatus(GoodsCheckTaskStatusEnum.PROCESSING.getType());
        createDO.setPdId(productsVO.getPdId());
        createDO.setSku(inventory.getSku());
        createDO.setSkuName(Optional.ofNullable(productsVO.getPdName()).orElse(StringUtil.EMPTY_STRING));
        createDO.setWeight(Optional.ofNullable(inventory.getWeight()).orElse(StringUtil.EMPTY_STRING));
        createDO.setStorageLocation(Optional.ofNullable(productsVO.getStorageLocation()).orElse(0));
        createDO.setVolume(Optional.ofNullable(inventory.getVolume()).orElse(StringUtil.EMPTY_STRING));
        createDO.setWeightNum(Optional.ofNullable(inventory.getWeightNum()).orElse(BigDecimal.ZERO));
        createDO.setCreator(createDO.getOperator());
        createDO.setPushStatus(createDO.getPushStatus());
        createDO.setOperator(createDO.getOperator());
        createDO.setUnit(inventory.getUnit());
        createDO.setIsDomestic(inventory.getIsDomestic());
        createDO.setStorageLocation(productsVO.getStorageLocation());
        createDO.setQualityTime(productsVO.getQualityTime());
        createDO.setQualityTimeUnit(productsVO.getQualityTimeUnit());
        createDO.setQualityTimeType(productsVO.getQualityTimeType());
    }

    private Map<Integer, Integer> initWarehouseData() {
        Map<Integer, Integer> result = Maps.newLinkedHashMap();
        // 查询仓库数据，根据ID排序
        List<WarehouseStorageCenterVO> centerVOList = warehouseStorageService.selectAllForSummerFarm(
                WarehouseStorageCenterEnum.Type.INTERNAL_AREA.ordinal(), WarehouseStorageCenterEnum.Status.VALID.ordinal());
        centerVOList.sort(Comparator.comparing(WarehouseStorageCenterVO::getWarehouseNo));
        // 过滤测试仓
        List<Integer> testWarehouseList = Lists.newArrayList();
        Config config = configMapper.selectOne("testWarehouse");
        if (Objects.nonNull(config) && !StringUtils.isEmpty(config.getValue())) {
            testWarehouseList = JSONUtil.parseArray(config.getValue()).toList(Integer.class);
        }
        List<Integer> finalTestWarehouseList = testWarehouseList;
        centerVOList.stream().filter(x -> !finalTestWarehouseList.contains(x.getWarehouseNo())).forEach(centerVO -> {
            result.put(centerVO.getWarehouseNo(), 0);
        });
        return result;
    }

    /**
     * 发送钉钉消息
     * @param queryTask
     * @param editTask
     * @return
     */
    private void sendGoodsCheckDingTalkMsg(StoreGoodsTaskPO queryTask, StoreGoodsTaskPO editTask) {
        try {
            if(!compareData(queryTask, editTask)) {
                Config config = configMapper.selectOne("goodsCheckTaskRobotUrl");
                if (null == config || StringUtils.isEmpty(config.getValue())) {
                    logger.error("未获取到钉钉通知url，key：goodsCheckTaskUrl");
                    return;
                }
                WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNo(queryTask.getWarehouseNo());
                if (null == warehouseStorageCenter) {
                    logger.error("未获取到仓库信息，warehouseNo：{}", queryTask.getWarehouseNo());
                    return;
                }
                DingTalkRobotUtil.sendMsgAndAtAll(DingTalkRobotUtil.MARKDOWN, config.getValue(), () -> buildGoodsCheckDingTalkMsg(queryTask, editTask, warehouseStorageCenter));
            }
        } catch (Exception e) {
            logger.error("商品校验任务提交钉钉通知发送失败，goodsCheckTaskId：{}", queryTask.getId(), e);
        }
    }

    /**
     * 校验部分属性值是否相同
     * @param queryTask
     * @param editTask
     * @return
     */
    private Boolean compareData(StoreGoodsTaskPO queryTask, StoreGoodsTaskPO editTask) {
        if (null == queryTask.getUnit() || !queryTask.getUnit().equals(editTask.getUnit())) {
            return false;
        }
        if (null == queryTask.getStorageLocation() || !queryTask.getStorageLocation().equals(editTask.getStorageLocation())) {
            return false;
        }
        if (null == queryTask.getIsDomestic() || !queryTask.getIsDomestic().equals(editTask.getIsDomestic())) {
            return false;
        }
        if (null == queryTask.getQualityTimeType() || !queryTask.getQualityTimeType().equals(editTask.getQualityTimeType())) {
            return false;
        }
        if (null == queryTask.getQualityTime() || !queryTask.getQualityTime().equals(editTask.getQualityTime())) {
            return false;
        }
        if (null == queryTask.getQualityTimeUnit() || !queryTask.getQualityTimeUnit().equals(editTask.getQualityTimeUnit())) {
            return false;
        }
        return true;
    }

    /**
     * 构建发送钉钉通知消息体
     * @param queryTask
     * @param editTask
     * @return
     */
    private Map<String, String> buildGoodsCheckDingTalkMsg(StoreGoodsTaskPO queryTask, StoreGoodsTaskPO editTask, WarehouseStorageCenter warehouseStorageCenter) {
        String title = "商品信息核对提醒";
        StringBuilder text = new StringBuilder("### " + title + "\n");
        text.append("#### ").append("提报仓：").append(warehouseStorageCenter.getWarehouseName()).append("\n")
                .append("#### ").append("商品名称：").append(queryTask.getSkuName()).append("\n")
                .append("#### ").append("sku：").append(queryTask.getSku()).append("\n")
                .append("#### ").append("规格：").append(queryTask.getWeight()).append("\n")
                .append("#### ").append("核对信息").append("\n")
                .append("#### ").append("包装：").append(queryTask.getUnit()).append(org.apache.commons.lang3.StringUtils.equals(queryTask.getUnit(), editTask.getUnit()) ? "" : "，核对后：" + editTask.getUnit()).append("\n")
                .append("#### ").append("存储温区：").append(StorageLocationEnum.getDescByCode(queryTask.getStorageLocation())).append(null != queryTask.getStorageLocation() && queryTask.getStorageLocation().equals(editTask.getStorageLocation()) ? "" : "，核对后：" + StorageLocationEnum.getDescByCode(editTask.getStorageLocation())).append("\n")
                .append("#### ").append("进口/国产：").append(IsDomesticEnum.getDescByCode(queryTask.getIsDomestic())).append(null != queryTask.getIsDomestic() && queryTask.getIsDomestic().equals(editTask.getIsDomestic()) ? "" : "，核对后：" + IsDomesticEnum.getDescByCode(editTask.getIsDomestic())).append("\n")
                .append("#### ").append("保质期类型：").append(QualityTimeTypeEnum.getDescByCode(queryTask.getQualityTimeType())).append(null != queryTask.getQualityTimeType() && queryTask.getQualityTimeType().equals(editTask.getQualityTimeType()) ? "" : "，核对后：" + QualityTimeTypeEnum.getDescByCode(editTask.getQualityTimeType())).append("\n")
                .append("#### ").append("保质期时长：").append(queryTask.getQualityTime()).append(QualityTimeUnitEnum.getDescByCode(queryTask.getQualityTimeUnit())).append(null != queryTask.getQualityTime() && queryTask.getQualityTime().equals(editTask.getQualityTime()) && !StringUtils.isEmpty(queryTask.getQualityTimeUnit()) && queryTask.getQualityTimeUnit().equals(editTask.getQualityTimeUnit()) ? "" : "，核对后：" + editTask.getQualityTime() + QualityTimeUnitEnum.getDescByCode(editTask.getQualityTimeUnit())).append("\n");
        if (!StringUtils.isEmpty(editTask.getPicUrl())) {
            String[] split = editTask.getPicUrl().split(",");
            text.append("#### ").append("核对照片：\n");
            for (String picUrl : split) {
                text.append("![](").append(picUrl).append(")\n");
            }
        }
        Map<String, String> md = new HashMap<>();
        md.put("title", title);
        md.put("text", text.toString());
        return md;
    }

}
