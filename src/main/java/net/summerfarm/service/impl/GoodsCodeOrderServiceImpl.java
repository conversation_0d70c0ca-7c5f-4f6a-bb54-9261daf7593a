package net.summerfarm.service.impl;

import net.summerfarm.mapper.manage.GoodsCodeOrderMapper;
import net.summerfarm.model.domain.GoodsCodeOrder;
import net.summerfarm.model.vo.GoodsCodeOrderVO;
import net.summerfarm.service.GoodsCodeOrderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/12/13  14:29
 */
@Service
public class GoodsCodeOrderServiceImpl implements GoodsCodeOrderService {

    @Resource
    private GoodsCodeOrderMapper goodsCodeOrderMapper;

    @Override
    public List<GoodsCodeOrder> queryGoodsCodeOrder(GoodsCodeOrder goodsCodeOrder) {
        List<GoodsCodeOrder> goodsCodeOrders = goodsCodeOrderMapper.queryCodeMsg(goodsCodeOrder);
        return goodsCodeOrders;
    }

    @Override
    public List<GoodsCodeOrder> queryGoodsCode(String onlyCode) {
        GoodsCodeOrder goodsCodeOrder = new GoodsCodeOrder();
        goodsCodeOrder.setOnlyCode(onlyCode);
        List<GoodsCodeOrder> goodsCodeOrders = goodsCodeOrderMapper.queryCodeMsg(goodsCodeOrder);
        return goodsCodeOrders;
    }

    @Override
    public List<GoodsCodeOrderVO> queryGoodsCodeOrderVO(GoodsCodeOrder goodsCodeOrder) {
        return goodsCodeOrderMapper.queryGoodsCodeOrderVO(goodsCodeOrder);
    }

    @Override
    public List<GoodsCodeOrderVO> querySaasGoodsCodeOrderVO(GoodsCodeOrder goodsCodeOrder) {
        return goodsCodeOrderMapper.querySaasGoodsCodeOrderVO(goodsCodeOrder);
    }
}
