package net.summerfarm.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.biz.finance.util.ExceptionUtil;
import net.summerfarm.biz.stockTransfer.constants.TransferExportConstant;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.dao.stockTransfer.StockTransferDAO;
import net.summerfarm.dao.stockTransfer.dataobject.StockTransferDO;
import net.summerfarm.enums.*;
import net.summerfarm.mapper.WmsBatchFrozenMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.wms.WarehouseCostBatchDAO;
import net.summerfarm.mapper.wms.WarehouseProduceBatchDAO;
import net.summerfarm.model.DTO.StoreRecordDTO;
import net.summerfarm.model.ShelfLifePurchaseBatch;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.wms.CostProduceBatchMappingDO;
import net.summerfarm.model.input.LastStoreRecordPageQuery;
import net.summerfarm.model.input.StockChangeRecordQuery;
import net.summerfarm.model.input.StoreRecordAllocationQueryDTO;
import net.summerfarm.model.param.StoreRecordQueryVO;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.PurchasesBackDetailVO;
import net.summerfarm.model.vo.StoreRecordBatchQuantityVO;
import net.summerfarm.model.vo.StoreRecordVO;
import net.summerfarm.model.vo.pms.PurchaseStoreRecordVO;
import net.summerfarm.model.vo.pms.converter.PurchaseStoreRecordVOConverter;
import net.summerfarm.module.scp.model.input.AllocationWarningQueryDTO;
import net.summerfarm.module.scp.model.vo.AllocationWarningVO;
import net.summerfarm.module.wms.infrastructure.repository.WarehouseReadRepository;
import net.summerfarm.module.wms.model.converter.StoreRecordQueryVOConverter;
import net.summerfarm.provider.converter.StoreRecordConvert;
import net.summerfarm.service.*;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Package: net.summerfarm.service.impl
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/11/10
 */
@Service
public class StoreRecordServiceImpl extends BaseService implements StoreRecordService {

    private static final Logger logger = LoggerFactory.getLogger(StoreRecordService.class);

    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Lazy
    @Resource
    private AreaStoreService areaStoreService;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private StockTaskMapper stockTaskMapper;
    @Resource
    private StockTaskItemMapper stockTaskItemMapper;
    @Resource
    private StockTaskItemDetailMapper stockTaskItemDetailMapper;
    @Resource
    private StockTaskProcessMapper stockTaskProcessMapper;
    @Resource
    private StockTaskProcessDetailMapper stockTaskProcessDetailMapper;

    @Lazy
    @Resource
    private PurchasesConfigService purchasesConfigService;
    @Lazy
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    private PurchasesBackDetailMapper purchasesBackDetailMapper;

    @Resource
    private WMSBuilderService wmsBuilderService;
    @Resource
    private WmsBatchFrozenMapper wmsBatchFrozenMapper;

    @Resource
    private StockTransferDAO stockTransferDAO;

    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    private WarehouseProduceBatchDAO warehouseProduceBatchDAO;
    @Resource
    private WarehouseCostBatchDAO warehouseCostBatchDAO;
    @Resource
    private SkuConvertService skuConvertService;
    @Resource
    private WarehouseReadRepository warehouseReadRepository;
    @Resource
    private PurchasesService purchasesService;


    @Override
    public AjaxResult<PageInfo<StoreRecordVO>> selectStoreRecord(int pageIndex, int pageSize, StoreRecordVO selectKeys, Long tenantId) {

        StoreRecordQueryVO storeRecordQueryVO = StoreRecordQueryVOConverter.INSTANCE.convert(selectKeys);
        // 仓库没选，按照租户隔离权限
        if (storeRecordQueryVO.getAreaNo() == null
            && BaseConstant.XIANMU_TENANT_ID.equals(tenantId)) {
            storeRecordQueryVO.setAreaNoList(
                    warehouseReadRepository.queryWarehouseCodeListByTenantId(tenantId)
            );
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<StoreRecordVO> storeRecords = storeRecordMapper.select(storeRecordQueryVO);
        storeRecords.forEach(storeRecord -> {
            try {
                Integer adminId = Integer.valueOf(storeRecord.getRecorder());
                String adminName = getAdminName(adminId);
                storeRecord.setRecorder(adminName);
            } catch (Exception e) {
                logger.info("老数据兼容");
            }
        });
        wmsBuilderService.batchBuildWMSInfo(storeRecords);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(storeRecords));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @XmLock(waitTime = 1000 * 60, key = "(addStoreRecord):{storeRecordVO.sku}")
    public AjaxResult save(StoreRecordVO storeRecordVO, List<StoreRecordDTO> storeRecordList) {
//        List<StoreRecordDTO> storeRecordList = storeRecordVO.getStoreRecordList();
        if (CollectionUtils.isEmpty(storeRecordList)) {
            return AjaxResult.getErrorWithMsg("库存记录新增有误！");
        }

        //采购单入库(批量操作)
        if (storeRecordVO.getIsPurchase() == 1) {
            throw new DefaultServiceException("请于wms中进行采购单入库操作");
        } else {
            StoreRecord storeRecord = storeRecordList.get(0);
            if (storeRecord.getType() == StoreRecordType.PURCHASE_IN.getId()) {
                throw new DefaultServiceException("请于wms中进行采购单入库操作");
            }
            if (storeRecord.getType() == StoreRecordType.DAMAGE_OUT.getId()) {
                throw new DefaultServiceException("请于wms中进行货损出库操作");
            }
            if (storeRecord.getType() == StoreRecordType.AFTER_SALE_IN.getId()) {
                throw new DefaultServiceException("请于wms中进行退货入库操作");
            }

            storeRecord.setAreaNo(storeRecordVO.getAreaNo());
            StoreRecord lasted = storeRecordMapper.selectOne(storeRecord);

            int quantity = storeRecord.getType() < 50 ? storeRecord.getQuantity() : -storeRecord.getQuantity();//此处会更新为负数
            int storeQuantity = quantity;
            if (lasted != null) {
                storeQuantity += lasted.getStoreQuantity();
                storeRecord.setCost(lasted.getCost());
                storeRecord.setProductionDate(lasted.getProductionDate());
                storeRecord.setTenantId(lasted.getTenantId());
            } else {
                if (storeRecord.getType() == StoreRecordType.AFTER_SALE_IN.getId()) {
                    throw new DefaultServiceException("当前仓不存在采购批次" + storeRecord.getBatch() + "保质期为" + storeRecord.getQualityDate() + "的" + storeRecord.getSku());
                } else if (storeRecord.getType() == StoreRecordType.STORE_ALLOCATION_IN.getId()) {
                    StoreRecordVO selectKey = new StoreRecordVO();
                    selectKey.setBatch(storeRecord.getBatch());
                    selectKey.setSku(storeRecord.getSku());
                    selectKey.setQualityDate(storeRecord.getQualityDate());
                    StoreRecord record = storeRecordMapper.selectList(selectKey).get(0);
                    storeRecord.setCost(record.getCost());
                    storeRecord.setProductionDate(record.getProductionDate());
                    storeRecord.setTenantId(record.getTenantId());
                }
            }

            if (storeQuantity < 0) {
                if (lasted != null) {
                    throw new DefaultServiceException(
                            "sku:" + storeRecord.getSku() + "此批次库存仅剩：" + storeRecord.getBatch() + "-" + storeRecord.getQualityDate() + "-" + lasted.getStoreQuantity());
                } else {
                    throw new DefaultServiceException("当前仓不存在采购批次" + storeRecord.getBatch() + "保质期为" + storeRecord.getQualityDate() + "的" + storeRecord.getSku());
                }
            }
            storeRecord.setStoreQuantity(storeQuantity);
            storeRecord.setRecorder(getAdminName());
            storeRecord.setUpdateTime(new Date());
            storeRecordMapper.insert(storeRecord);

            Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
            //销售出库
            if (storeRecord.getType() == StoreRecordType.SALE_OUT.getId()) {
                throw new DefaultServiceException("请于wms中进行销售出库操作");
//                areaStoreService.updateLockStockByStoreNo(quantity, storeRecord.getSku(), storeRecord.getAreaNo(), "销售出库");
            } else {
                if (StoreRecordType.STORE_ALLOCATION_OUT.getId() != storeRecord.getType()
                        && StoreRecordType.STORE_ALLOCATION_IN.getId() != storeRecord.getType()
                        && StoreRecordType.RETURN_IN.getId() != storeRecord.getType()
                        && StoreRecordType.STORE_ALLOCATION_NOT_IN.getId() != storeRecord.getType()) {
                    //虚拟库存同步
                    AreaStore areaStore = areaStoreMapper.selectOne(new AreaStore(storeRecord.getAreaNo(), storeRecord.getSku()));
                    if (areaStore.getSync() == 1) {
                        areaStoreService.updateOnlineStockByStoreNo(true, quantity, storeRecord.getSku(), storeRecord.getAreaNo(), StoreRecordType.getById(storeRecord.getType()), null, recordMap, NumberUtils.INTEGER_ONE);
                    }
                }
            }

            //同步仓库库存 要在 虚拟库存后 否则会出错
            areaStoreService.updateStoreStockByWarehouseNo(quantity, storeRecord.getSku(), storeRecord.getAreaNo(), StoreRecordType.getById(storeRecord.getType()), storeRecord.getType(), null, recordMap);
            quantityChangeRecordService.insertRecord(recordMap);

            if (storeRecord.getType() == StoreRecordType.DEMO_OUT.getId()) { //出样出库,自动生成出入库任务
                StockTask stockTask = new StockTask(null, storeRecord.getAreaNo(), storeRecord.getType(), LocalDateTime.now(), StockTaskState.FINISH);
                stockTaskMapper.insert(stockTask);
                StockTaskItem stockTaskItem = new StockTaskItem(stockTask.getId(), storeRecord.getSku(), storeRecord.getQuantity(), storeRecord.getQuantity());
                stockTaskItemMapper.insert(stockTaskItem);
                StockTaskItemDetail detail = new StockTaskItemDetail(stockTaskItem.getId(), storeRecord.getSku(), storeRecord.getBatch(), storeRecord.getQualityDate(), storeRecord.getQuantity(), storeRecord.getRemark());
                detail.setProductionDate(lasted.getProductionDate());//NOSONAR
                stockTaskItemDetailMapper.insert(detail);
                StockTaskProcess process = new StockTaskProcess(stockTask.getId(), LocalDateTime.now(), getAdminName());
                stockTaskProcessMapper.insert(process);
                StockTaskProcessDetail processDetail = new StockTaskProcessDetail(process.getId(), null, storeRecord.getSku(), storeRecord.getBatch(), storeRecord.getQualityDate(), storeRecord.getQuantity(), lasted.getProductionDate());
                stockTaskProcessDetailMapper.insert(processDetail);

                purchasesConfigService.msgArrival(storeRecord.getAreaNo(), storeRecord.getSku()); //预警通知
            }
            logger.info(getAdminName() + "添加库存记录：" + storeRecord.getType());
        }
        return AjaxResult.getOK();

    }

    @Override
    public AjaxResult selectBatchList(StoreRecordVO storeRecord) {

        List<StoreRecordVO> storeRecordVOS = storeRecordMapper.selectBatchList(storeRecord);
        PurchasesBackDetailVO query = new PurchasesBackDetailVO();
        Integer totalOutQuantity;
        for (StoreRecordVO sr : storeRecordVOS) {
            query.setQualityDate(sr.getQualityDate());
            query.setSku(sr.getSku());
            query.setAreaNo(sr.getAreaNo());
            query.setStatus(PurchasesBackStatus.SUCCESS.ordinal());
            query.setBatch(sr.getBatch());
            query.setType(PurchasesBackTypeEnum.HAS_IN_BACK.ordinal());
            query.setActualOutQuantity(NumberUtils.INTEGER_ZERO);
            query.setPic(sr.getPic());
            totalOutQuantity = purchasesBackDetailMapper.selectTotalQuantity(query);
            //剩余批次数量减去已退货成功但是未出库的数量
            sr.setStoreQuantity(sr.getStoreQuantity() - totalOutQuantity);
        }

        List<PurchaseStoreRecordVO> purchaseStoreRecordVOList = PurchaseStoreRecordVOConverter
                .INSTANCE.convertList(storeRecordVOS);
        skuConvertService.setSaasSkuIdForList(purchaseStoreRecordVOList);
        purchaseStoreRecordVOList.forEach(x->x.setSupplierId(purchasesService.getSupplierIdByBatchAndSku(x.getBatch(),x.getSku())));
        return AjaxResult.getOK(purchaseStoreRecordVOList);
    }

    @Override
    public List<StoreRecord> selectLasted(StoreRecordVO storeRecordVO) {
        List<StoreRecord> result;
        if (storeRecordVO.getStoreQuantityMin() == null) {
            storeRecordVO.setStoreQuantityMin(1);
        }
        result = storeRecordMapper.selectLasted(storeRecordVO);
        // 临保过滤
        boolean isTransfer = false;
        if (Objects.nonNull(storeRecordVO.getStockTaskId())) {
            StockTransferDO stockTransferDO = stockTransferDAO.selectById(storeRecordVO.getStockTaskId().longValue());
            if (Objects.nonNull(stockTransferDO) && StringUtils.equals(stockTransferDO.getRemark(), TransferExportConstant.NEAR_SHELF_LIFE)) {
                isTransfer = true;
            }
        }
        if (isTransfer) {
            InventoryVO inventory = inventoryMapper.selectInventoryVOBySku(storeRecordVO.getSku());
            LocalDate now = LocalDate.now();
            result = result.stream()
                    .filter(item -> now.plusDays(3L).isBefore(item.getQualityDate()))
                    .filter(item -> {
                        if (inventory == null || inventory.getWarnTime() == null) {
                            return false;
                        }
                        return !now.plusDays(inventory.getWarnTime()).isBefore(item.getQualityDate());
                    })
                    .collect(Collectors.toList());
        }
//        for (StoreRecord storeRecord : result) {
//            int num = wmsBatchFrozenMapper.selectByDetail(storeRecord.getBatch(), storeRecord.getSku(), storeRecord.getAreaNo(), storeRecord.getQualityDate());
//            // 校验扣减采购退货批次
//            PurchasesBackDetailVO backDetailVO = new PurchasesBackDetailVO();
//            backDetailVO.setBatch(storeRecord.getBatch());
//            backDetailVO.setSku(storeRecord.getSku());
//            backDetailVO.setStatus(PurchasesBackStatus.SUCCESS.ordinal());
//            backDetailVO.setType(PurchasesBackTypeEnum.HAS_IN_BACK.ordinal());
//            backDetailVO.setQualityDate(storeRecord.getQualityDate());
//            backDetailVO.setAreaNo(storeRecord.getAreaNo());
//            int backLockBatchQuantity = purchasesBackDetailMapper.selectLockBatch(backDetailVO);
//            storeRecord.setStoreQuantity(storeRecord.getStoreQuantity() - num - backLockBatchQuantity);
//        }
        return result;
    }


    @Override
    public AjaxResult ok() {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult ok2() {
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectLastedPurchase(StoreRecordVO record) {

        //获取采购价>0最新采购批次
        StoreRecord storeRecord = storeRecordMapper.selectPurchaseType(record);
        if (Objects.isNull(storeRecord)) {
            return AjaxResult.getOK();
        }
        //获取批次采购价
        BigDecimal cost = storeRecordMapper.selectPurchaseCost(storeRecord);
        return AjaxResult.getOK(cost);
    }

    @Override
    public AjaxResult updateStoreRecordQuantity(Integer id, Integer quantity, Integer type) {
        if (Objects.equals(NumberUtils.INTEGER_ZERO, type)) {
            storeRecordMapper.updateStoreQuantity(id, quantity);
        }
        return AjaxResult.getOK();
    }

    public  List<StoreRecordBatchQuantityVO> queryStoreRecordQuantity(StoreRecordAllocationQueryDTO storeRecordAllocationQueryDTO) {
            StoreRecordVO storeRecordVO = new StoreRecordVO();
            storeRecordVO.setSku(storeRecordAllocationQueryDTO.getSku());
            storeRecordVO.setAreaNo(storeRecordAllocationQueryDTO.getWarehouseNo());
            List<StoreRecord> storeRecords = selectLasted(storeRecordVO);
            if (CollectionUtils.isEmpty(storeRecords)) {
                return Lists.newArrayList();
            }
            List<StoreRecordBatchQuantityVO> storeRecordBatchQuantityVOList = Lists.newArrayList();
            storeRecords.forEach(o -> {
                Optional<StoreRecordBatchQuantityVO> storeRecordBatchQuantityVOptional = storeRecordBatchQuantityVOList.stream().filter(p -> p.getProductionDate().isEqual(o.getProductionDate()) && p.getQualityDate().isEqual(o.getQualityDate())).findFirst();
                if (storeRecordBatchQuantityVOptional.isPresent()) {
                    StoreRecordBatchQuantityVO storeRecordBatchQuantityVO = storeRecordBatchQuantityVOptional.get();
                    Integer quantity = storeRecordBatchQuantityVO.getQuantity();
                    storeRecordBatchQuantityVO.setQuantity(quantity + o.getStoreQuantity());
                } else {
                    StoreRecordBatchQuantityVO storeRecordBatchQuantityVO = new StoreRecordBatchQuantityVO();
                    storeRecordBatchQuantityVO.setProductionDate(o.getProductionDate());
                    storeRecordBatchQuantityVO.setQualityDate(o.getQualityDate());
                    storeRecordBatchQuantityVO.setQuantity(o.getStoreQuantity());
                    storeRecordBatchQuantityVOList.add(storeRecordBatchQuantityVO);
                }
            });
            List<StoreRecordBatchQuantityVO> sortStoreRecordBatchQuantityVO = storeRecordBatchQuantityVOList.stream().sorted(Comparator.comparing(StoreRecordBatchQuantityVO::getProductionDate)).collect(Collectors.toList());
            return sortStoreRecordBatchQuantityVO;
    }

    public List<AllocationWarningVO> queryAllocationWarning(AllocationWarningQueryDTO allocationWarningQueryDTO) {
        ArrayList<AllocationWarningVO> allocationWarningVOS = Lists.newArrayList();
        for (String sku : allocationWarningQueryDTO.getSku()) {
            StoreRecordAllocationQueryDTO storeRecordAllocationQueryDTO = new StoreRecordAllocationQueryDTO();
            storeRecordAllocationQueryDTO.setSku(sku);
            storeRecordAllocationQueryDTO.setWarehouseNo(allocationWarningQueryDTO.getWarehouseNo());
            List<StoreRecordBatchQuantityVO> storeRecordBatchQuantityVOS = queryStoreRecordQuantity(storeRecordAllocationQueryDTO);
            AllocationWarningVO allocationWarningVO = new AllocationWarningVO();
            allocationWarningVO.setSku(sku);
            boolean warning=false;
            for(StoreRecordBatchQuantityVO storeRecordBatchQuantityVOList: storeRecordBatchQuantityVOS){
                long betweenTotal = DateUtil.between(Date.from(storeRecordBatchQuantityVOList.getProductionDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()), Date.from(storeRecordBatchQuantityVOList.getQualityDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()), DateUnit.DAY);
                long betweenNow = DateUtil.between(DateUtil.beginOfDay(new Date()), Date.from(storeRecordBatchQuantityVOList.getQualityDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()), DateUnit.DAY);
                Integer warningDay = null; 
                if (betweenTotal >= AllocationWarnDayEnum.YEAR_NUMBER.getDays()) {
                    warningDay = 2 * AllocationWarnDayEnum.MONTH_NUMBER.getDays();
                } else if (betweenTotal >= AllocationWarnDayEnum.HALF_YEAR_NUMBER.getDays()) {
                    warningDay = AllocationWarnDayEnum.TEN_MORE_MONTH_NUMBER.getDays();
                } else if (betweenTotal >= AllocationWarnDayEnum.QUARTER_NUMBER.getDays()) {
                    warningDay = 3 * AllocationWarnDayEnum.WEEK_NUMBER.getDays();
                } else if (betweenTotal >= AllocationWarnDayEnum.MONTH_NUMBER.getDays()) {
                    warningDay = 2 * AllocationWarnDayEnum.WEEK_NUMBER.getDays();
                } else if (betweenTotal>=AllocationWarnDayEnum.TEN_NUMBER.getDays()){
                    warningDay = AllocationWarnDayEnum.WEEK_NUMBER.getDays();
                }
                if (warningDay==null||betweenNow <= warningDay) {
                    warning=true;
                }
            }
            allocationWarningVO.setWarning(warning);
            allocationWarningVOS.add(allocationWarningVO);
        }
        return allocationWarningVOS;

    }

    @Override
    public PageInfo<StoreRecordVO> pageQueryStoreRecord(StockChangeRecordQuery query) {
        List<Inventory> inventoryList = inventoryService.selectByIds(query.getTenantSkuIdList());
        if (CollectionUtils.isEmpty(inventoryList)) {
            return new PageInfo<>();
        }
        String sku = null;
        if (Objects.nonNull(query.getSkuId())) {
            Inventory inventory = inventoryService.selectById(query.getSkuId());
            if (Objects.isNull(inventory)) {
                return new PageInfo<>();
            }
            sku = inventory.getSku();
        }
        query.setSku(sku);
        AjaxResult<PageInfo<StoreRecordVO>> ajaxResult = this.selectStoreRecord(query.getPageNum(), query.getPageSize(),
                this.buildStoreRecordVO(query), null);
        if (!ajaxResult.isSuccess()) {
            logger.error("invoke selectStoreRecord error request:{}, msg:{}", JSONUtil.toJsonStr(this.buildStoreRecordVO(query)), ajaxResult.getMsg());
            ExceptionUtil.wrapAndThrow(String.format("库存变更记录查询失败,%s, %s", ajaxResult.getCode(), ajaxResult.getMsg()));
        }
        PageInfo<StoreRecordVO> pageInfo = ajaxResult.getData();
        this.assembleStoreRecord(pageInfo);
        return pageInfo;
    }

    @Override
    public PageInfo<ShelfLifePurchaseBatch> pageQueryLastStoreRecord(LastStoreRecordPageQuery pageQuery) {
        Page<CostProduceBatchMappingDO> page = PageHelper.startPage(pageQuery.getPageNum(), pageQuery.getPageSize());
        List<CostProduceBatchMappingDO> costProduceBatchList = warehouseCostBatchDAO.selectListByWarehouseNoAndSku(pageQuery.getWarehouseNo(), pageQuery.getSku(), pageQuery.getShelfLifeOrderBy());
        if (CollectionUtils.isEmpty(costProduceBatchList)) {
            return new PageInfo<>(Lists.newArrayList());
        }
        List<ShelfLifePurchaseBatch> batchList = costProduceBatchList.stream()
                .filter(Objects::nonNull).map(StoreRecordConvert::convertShelfLifePurchaseBatch).collect(Collectors.toList());
        PageInfo<ShelfLifePurchaseBatch> pageResult = new PageInfo<>();
        pageResult.setPageNum(page.getPageNum());
        pageResult.setPageSize(page.getPageSize());
        pageResult.setTotal(page.getTotal());
        pageResult.setList(batchList);
        return pageResult;
    }

    @Override
    public List<ShelfLifePurchaseBatch> batchQueryMinShelfLifePurchaseBatch(List<Integer> warehouseNoList, List<Long> skuIdList) {
        List<Inventory> inventoryList = inventoryService.selectByIds(skuIdList);
        if (CollectionUtils.isEmpty(skuIdList) || CollectionUtils.isEmpty(warehouseNoList)) {
            return Lists.newArrayList();
        }
        List<String> skuList = inventoryList.stream().map(Inventory::getSku).collect(Collectors.toList());
        List<CostProduceBatchMappingDO> costBatchDOList = warehouseCostBatchDAO.selectListByWareNoAndSkuList(warehouseNoList, skuList);
        List<ShelfLifePurchaseBatch> result = Lists.newArrayList();
        Map<String, Inventory> inventoryMap = inventoryList.stream().collect(Collectors.toMap(Inventory::getSku, Function.identity(), (a, b) -> a));
        costBatchDOList.forEach(costBatchDO -> {
            Inventory inventory = inventoryMap.get(costBatchDO.getSku());
            if (Objects.isNull(inventory)) {
                logger.warn("未查询到sku信息, sku:{}", costBatchDO.getSku());
                return;
            }
            LocalDate shelfLive = net.summerfarm.common.util.DateUtil.toLocalDate(costBatchDO.getShelfLife());
            ShelfLifePurchaseBatch batch = ShelfLifePurchaseBatch.builder()
                    .skuId(inventory.getInvId())
                    .sku(costBatchDO.getSku())
                    .warehouseNo(costBatchDO.getWarehouseNo())
                    .batch(costBatchDO.getPurchaseNo())
                    .leftShelfLife(net.summerfarm.common.util.DateUtil.getBetweenDay(LocalDate.now(), shelfLive).intValue())
                    .build();
            result.add(batch);
        });
        return result;
    }

    private void assembleStoreRecord(PageInfo<StoreRecordVO> pageInfo) {
        List<StoreRecordVO> storeRecordVOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(storeRecordVOList)) {
            return;
        }
        // 库存仓信息
        List<Integer> warehouseNoList = storeRecordVOList.stream().map(StoreRecordVO::getAreaNo)
                .distinct().collect(Collectors.toList());
        List<WarehouseStorageCenter> centerList = warehouseStorageService.selectByWarehouseNoList(warehouseNoList);
        Map<Integer, WarehouseStorageCenter> warehouseMap = centerList.stream()
                .collect(Collectors.toMap(WarehouseStorageCenter::getWarehouseNo, Function.identity(), (o1, o2) -> o1));
        storeRecordVOList.forEach(storeRecordVO -> {
            WarehouseStorageCenter center = warehouseMap.get(storeRecordVO.getAreaNo());
            storeRecordVO.setAreaName(Objects.nonNull(center) ? center.getWarehouseName() : "");
            storeRecordVO.setWarehouseTenantId(Objects.nonNull(center) ? center.getTenantId() : null);
        });

    }

    private StoreRecordVO buildStoreRecordVO(StockChangeRecordQuery query) {
        List<Inventory> inventoryList = inventoryService.selectByIds(query.getTenantSkuIdList());
        Integer inStoreType = query.getInStoreType();
        Integer outStoreType = query.getOutStoreType();
        StoreRecordVO storeRecordVO = new StoreRecordVO();
        storeRecordVO.setAreaNo(query.getWarehouseNo());
        storeRecordVO.setType(Optional.ofNullable(inStoreType).orElse(outStoreType));
        storeRecordVO.setPdName(query.getGoodsName());
        storeRecordVO.setSku(query.getSku());
        storeRecordVO.setStartDate(query.getStartDate());
        storeRecordVO.setEndDate(query.getEndDate());
        storeRecordVO.setSkuList(inventoryList.stream().map(Inventory::getSku).collect(Collectors.toList()));
        return storeRecordVO;
    }

}
