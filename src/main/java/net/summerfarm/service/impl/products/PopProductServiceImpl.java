package net.summerfarm.service.impl.products;

import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.biz.finance.util.ExceptionUtil;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.TransactionUtil;
import net.summerfarm.goods.client.enums.ProductConfigEnum;
import net.summerfarm.mapper.product.ProductConfigMapper;
import net.summerfarm.model.ProductConfig;
import net.summerfarm.model.bo.ProductSaveResultBO;
import net.summerfarm.model.input.ProductsSaveReq;
import net.summerfarm.service.ProductsService;
import net.summerfarm.service.impl.ExternalProductMappingHandler;
import net.summerfarm.service.impl.ProductConfigHandler;
import net.summerfarm.service.item.command.PopProductCopyCommand;
import net.summerfarm.service.item.command.PopProductPublishCommand;
import net.summerfarm.service.item.convert.PopProductFactory;
import net.summerfarm.service.item.resp.PopProductCopyResp;
import net.summerfarm.service.item.resp.PopProductPublishResp;
import net.summerfarm.service.products.PopProductService;
import net.summerfarm.validator.InventoryValidator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description
 * @Date 2025/1/16 17:15
 * @<AUTHOR>
 */
@Service
public class PopProductServiceImpl implements PopProductService {

    @Resource
    private InventoryValidator inventoryValidator;
    @Resource
    private PopProductFactory popProductFactory;
    @Resource
    private ProductsService productsService;
    @Resource
    private ProductConfigMapper productConfigMapper;
    @Resource
    private ExternalProductMappingHandler externalProductMappingHandler;
    @Resource
    private ProductConfigHandler productConfigHandler;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PopProductCopyResp copyPopProduct(PopProductCopyCommand popProductCopyCommand) {
        // 校验pop商品信息
        inventoryValidator.checkCopyPopProduct(popProductCopyCommand);
        // 构建商品创建对象
        ProductsSaveReq saveReq = popProductFactory.buildNotInWarehouseProduct(popProductCopyCommand);
        // 创建商品
        AjaxResult<ProductSaveResultBO> ajaxResult = productsService.purchaseAddProduct(saveReq);
        ExceptionUtil.checkAndThrow(ajaxResult.isSuccess(), ajaxResult.getMsg());
        List<String> skuList = ajaxResult.getData().getSkuList();
        ExceptionUtil.checkAndThrow(!CollectionUtils.isEmpty(skuList), "复制并创建代销不入仓sku失败");
        String sku = skuList.get(0);
        // 保存pop复制商品来源关系
        ProductConfig productConfig = ProductConfig.builder()
                .configType(ProductConfigEnum.ProductConfigTypeEnum.SOURCE_POP_COPY.getValue())
                .configKey(sku)
                .configValue(popProductCopyCommand.getPopSku())
                .creator(popProductCopyCommand.getOperator())
                .operator(popProductCopyCommand.getOperator()).build();
        productConfigMapper.insert(productConfig);
        // 保存商品毛利
        if (Objects.nonNull(popProductCopyCommand.getInterestRate())) {
            ProductConfig interestRateConfig = ProductConfig.builder()
                    .configType(ProductConfigEnum.ProductConfigTypeEnum.PRODUCT_INTEREST_RATE.getValue())
                    .configKey(sku)
                    .configValue(popProductCopyCommand.getInterestRate().toPlainString())
                    .creator(popProductCopyCommand.getOperator())
                    .operator(popProductCopyCommand.getOperator()).build();
            productConfigMapper.insert(interestRateConfig);
        }

        // 组装返回结果
        return popProductFactory.buildPopProductCopyResp(skuList.get(0));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PopProductPublishResp publishPopProduct(PopProductPublishCommand publishCommand) {
        ProductsSaveReq saveReq = popProductFactory.buildPopProduct(publishCommand);
        AjaxResult<ProductSaveResultBO> ajaxResult = productsService.purchaseAddProduct(saveReq);
        ExceptionUtil.checkAndThrow(ajaxResult.isSuccess(), ajaxResult.getMsg());
        Long pdId = ajaxResult.getData().getPdId();
        String pdNo = ajaxResult.getData().getPdNo();

        List<String> skuList = ajaxResult.getData().getSkuList();
        // 处理商品配置信息
        productConfigHandler.handleProductConfig(publishCommand, pdNo, skuList);

        // 保存外部品绑定关系

        if (StringUtils.isNotBlank(publishCommand.getExternalSkuCode())
                && CollectionUtil.isNotEmpty(skuList)) {
            // pop商品 只会创建一个sku
            externalProductMappingHandler.handleExternalProductMapping(skuList.get(0), publishCommand.getExternalSkuCode());
        }
        return PopProductPublishResp.builder()
                .pdId(pdId).pdNo(pdNo).build();
    }

}
