package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.aliyun.odps.data.Record;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.RequestHolder;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.dao.PurchasePredictionDAO;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.FruitPurchasePredictionReq;
import net.summerfarm.model.param.FruitPurchaseRemindModel;
import net.summerfarm.model.vo.FruitPurchasesPredictionVO;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.PurchasesPlanVO;
import net.summerfarm.service.DingTalkService;
import net.summerfarm.service.PurchasePredictionService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Create 2020-11-03
 */
@Service
public class PurchasePredictionServiceImpl implements PurchasePredictionService {

    private static final Logger logger = LoggerFactory.getLogger(PurchasePredictionServiceImpl.class);

//    static {
//        fruitTopCategoryId = Global.isProduct() ? 87 : 6;
//    }

    /** 水果采购异常告警阈值 **/
    private static final double FRUIT_PRUCHASE_REMIND_THRESHOLD = 0.3d;

    /** 水果采购告警机器人 */
    private static final String FRUIT_PURCHASE_ROBOT_CONFIG = "fruitPurchaseRobotUrl";

    private static final String LINE_BREAK = System.getProperty("line.separator");

    /** 水果的一级类目 */
    private static Integer fruitTopCategoryId;


    @Autowired
    private PurchasePredictionDAO purchasePredictionDAO;

    @Autowired
    private AreaSkuMapper areaSkuMapper;

    @Autowired
    private AreaMapper areaMapper;

    @Autowired
    private PurchasesPlanMapper purchasesPlanMapper;

    @Autowired
    private CategoryMapper categoryMapper;

    @Autowired
    private ProductsMapper productsMapper;

    @Autowired
    private InventoryMapper inventoryMapper;

    @Resource
    private FruitPurchasePredictionMapper fruitPurchasePredictionMapper;

    @Autowired
    private DingTalkService dingTalkService;

    @Autowired
    private ConfigMapper configMapper;

    @Resource
    private AreaStoreMapper areaStoreMapper;

    @Override
    public AjaxResult listFruitData(Integer pageIndex, Integer pageSize, FruitPurchasePredictionReq req) {

        // 先根据上下架状态查找sku
        if (Objects.nonNull(req.getOnSale())) {
            Set<String> onSaleSku = searchStoreOnSaleSku(req.getOnSale(), req.getStoreNo());
            if (CollectionUtils.isEmpty(onSaleSku)) {
                return AjaxResult.getOK(PageInfoHelper.createPageInfo(Lists.newArrayList()));
            }
            req.setOnSaleSkus(onSaleSku);
        }

        // 上架的sku
        Set<String> onSaleSkuSet = searchStoreOnSaleSku(true, req.getStoreNo());

        PageInfo pageInfo = PageInfoHelper.createPageInfo(pageIndex, pageSize,
                () -> fruitPurchasePredictionMapper.selectiveQuery(req));

        List<FruitPurchasesPredictionVO> pageList = Lists.transform(pageInfo.getList(),
                fpp -> transformVO((FruitPurchasePredictionDO)fpp, onSaleSkuSet));
        pageInfo.setList(pageList);

        return AjaxResult.getOK(pageInfo);
    }

    /**
     * 转换成VO
     * @param fpp
     * @param onSaleSkuSet
     * @return
     */
    private FruitPurchasesPredictionVO transformVO(FruitPurchasePredictionDO fpp, Set<String> onSaleSkuSet){
        FruitPurchasesPredictionVO result = new FruitPurchasesPredictionVO();
        BeanUtils.copyProperties(fpp, result);
        result.setSalesRate(fpp.getSalesRate()+"%");
        result.setOnSale(onSaleSkuSet.contains(result.getSku()) ? "上架" : "下架");
        result.setWeight(fpp.getWeight());

        return result;
    }

    private static String parseString(Object obj) {
        return Objects.isNull(obj) ? StringUtils.EMPTY : String.valueOf(obj);
    }

    private static Integer parseInt(Object obj) {
        return Objects.isNull(obj) ? 0 : Integer.valueOf(String.valueOf(obj));
    }

    private static Double parseDouble(Object obj) {
        String purchaser = String.valueOf(obj);
        if (StringUtils.equalsIgnoreCase("\\N", purchaser)) {
            return 0.00;
        }
        return Objects.isNull(obj) ? 0.00 : Double.valueOf(String.valueOf(obj));
    }

    /**
     * 查找仓库里上/下架状态的sku
     * 在全部城市下架才算是下架，若有城市上架则算是上架
     *
     * @param onSale false-下架 true-上架
     * @param storeNo 仓库的编号
     * @return
     */
    private Set<String> searchStoreOnSaleSku(Boolean onSale, Integer storeNo){
        // 查找仓库下所有城市的sku
        List<String> areaSkuList = areaSkuMapper.selectSkuInStore(onSale, storeNo);
        return Sets.newHashSet(areaSkuList);
    }




    @Override
    public void exportFruitData(FruitPurchasePredictionReq req) {

        String fileName = createFileName(req.getStoreNo());

        if (Objects.nonNull(req.getOnSale())) {
            Set<String> onSaleSku = searchStoreOnSaleSku(req.getOnSale(), req.getStoreNo());
            if (CollectionUtils.isEmpty(onSaleSku)) {
                onSaleSku = Sets.newHashSet(FruitPurchasePredictionReq.INVALID_SKU);
            }
            req.setOnSaleSkus(onSaleSku);
        }

        List<FruitPurchasesPredictionVO> rows = Lists.transform(fruitPurchasePredictionMapper.selectiveQuery(req),
                li -> transformVO(li, searchStoreOnSaleSku(true, req.getStoreNo())));
        if (CollectionUtils.isEmpty(rows)) {
            // 没有数据就给一个空表头。。。。
            rows = Lists.newArrayList(new FruitPurchasesPredictionVO());
        }

        HttpServletResponse response = RequestHolder.getResponse();
        try (ServletOutputStream out = response.getOutputStream(); ExcelWriter writer = ExcelUtil.getWriter()){

            writeTitleAlias(writer);
            writer.passCurrentRow();
            writer.write(rows);
            ExcelUtils.setResponseHeader(response, fileName);
            writer.flush(out);

        } catch (IOException e) {
            logger.error("Export fruit purchase prediction data error!", e);
        }
    }


    @Override
    public void fruitPurchaseRemind() {

        // 1.获取水果的sku
        Set<String> skuIds = getFruitSku();
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }


        // 2.昨日sku采购量
        Map<String, Integer> yesterdayPurchase = calcPurchaseQuantity(
                LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MIN),
                LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MAX),
                skuIds);

        // 3.今日sku采购量 0点-10点的数据
        Map<String, Integer> todayPurchase = calcPurchaseQuantity(
                LocalDateTime.of(LocalDate.now(), LocalTime.MIN),
                LocalDateTime.of(LocalDate.now(), LocalTime.of(10, 0, 0)),
                skuIds);

        // 4.两天sku合集，比较采购差值，小于阈值的发送采购提醒
        List<FruitPurchaseRemindModel> remindList = calcPurchaseRemind(yesterdayPurchase, todayPurchase);
        if (CollectionUtils.isEmpty(remindList)) {
            return;
        }

        // 5.发送告警
        sendFruitPurchaseRemind(remindList);
        for (FruitPurchaseRemindModel fruitPurchaseRemindModel : remindList) {
            logger.info("remindList:{}",fruitPurchaseRemindModel.toString());
        }

    }

    @Override
    public void syncFruitData() {

        logger.info("水果采购预测数据开始同步。。。。。。");

        try{
            // 拉取数据
            List<Record> records = purchasePredictionDAO.listTodayFruitData();
            if (CollectionUtil.isEmpty(records)) {
                return;
            }

            // 同步到数据库
            List<FruitPurchasePredictionDO> list = Lists.transform(records, this::transfromDO);
            List<List<FruitPurchasePredictionDO>> allList = CollectionUtil.split(list, 300);
            for (List<FruitPurchasePredictionDO> subList : allList) {
                fruitPurchasePredictionMapper.insertBatch(subList);
                Thread.sleep(500);
            }

            // 最后确认一次
            int c1 = purchasePredictionDAO.countTodayFruitData();
            int c2 = fruitPurchasePredictionMapper.countTodayData();
            if (c1 != c2) {
                logger.error("同步水果采购数据数量不一致！opds数量:{}，同步数量:{}", c1, c2);
                sendSyncFruitFailMsg(c1, c2);
            }
        } catch (Exception e) {
            logger.error("同步水果采购数据异常!", e);
        }
        logger.info("水果采购预测数据同步结束。。。。。。");
    }

    /**
     * 失败提醒
     * @param c1
     * @param c2
     */
    private void sendSyncFruitFailMsg(int c1, int c2){
        try {
            String url = configMapper.selectOne("ScheduleTaskRobotUrl").getValue();
            Map<String, String> msg = Maps.newHashMap();
            msg.put("title", "水果采购预测数据同步异常提醒");
            StringBuilder text = new StringBuilder();
            text.append("#### 水果采购预测数据同步异常提醒").append(LINE_BREAK)
                .append("> ##### odps数量:").append(c1).append("，同步数量:").append(c2);
            msg.put("text", text.toString());
            DingTalkRobotUtil.sendMarkDownMsg(url, () -> msg, null);
        } catch (Exception e){
            logger.error("PurchasePredictionServiceImpl.sendSyncFruitFailMsg error!", e);
        }
    }

    /**
     * 表格title设置
     * @param writer
     */
    private void writeTitleAlias(ExcelWriter writer) {
        LocalDate now = LocalDate.now();
        writer.addHeaderAlias("sku", "sku");
        writer.addHeaderAlias("pdName", "商品名称");
        writer.addHeaderAlias("weight", "规格");
        writer.addHeaderAlias("onSale", "状态");
        writer.addHeaderAlias("salesPrediction",DateUtils.localDateTimeToStringTwo(now));
        writer.addHeaderAlias("preSaleSecondDay", DateUtils.localDateTimeToStringTwo(now.plusDays(1)));
        writer.addHeaderAlias("preSaleThirdDay" ,DateUtils.localDateTimeToStringTwo(now.plusDays(2)));
        writer.addHeaderAlias("preSaleFourthDay", DateUtils.localDateTimeToStringTwo(now.plusDays(3)));
        writer.addHeaderAlias("preSaleFifthDay", DateUtils.localDateTimeToStringTwo(now.plusDays(4)));
        writer.addHeaderAlias("preSaleSixthDay", DateUtils.localDateTimeToStringTwo(now.plusDays(5)));
        writer.addHeaderAlias("preSaleSeventhDay", DateUtils.localDateTimeToStringTwo(now.plusDays(6)));
        writer.addHeaderAlias("quantity", "今日剩余仓库库存");
        writer.addHeaderAlias("vipAvgSales", "大客户近三天平均销量");
        writer.addHeaderAlias("singleAvgSales", "单店近三天平均销量");
        writer.addHeaderAlias("salesRate", "销量同比增长率");
        writer.addHeaderAlias("purchaser", "采购负责人");
        writer.writeCellValue(4,0,"七天销量预测");
        writer.writeCellValue(0,0,"sku");
        writer.writeCellValue(1,0,"商品名称");
        writer.writeCellValue(2,0,"规格");
        writer.writeCellValue(3,0,"状态");
        writer.writeCellValue(4,0,"七天销量预测");
        writer.writeCellValue(11,0,"今日剩余仓库库存");
        writer.writeCellValue(12,0,"大客户三天平均销量");
        writer.writeCellValue(13,0,"单店近三天平均销量");
        writer.writeCellValue(14,0,"可用库销量同比增长率存");
        writer.writeCellValue(15,0,"采购负责人");
        writer.setColumnWidth(12,16);
        writer.setColumnWidth(13,16);
        writer.setColumnWidth(11,16);
        Sheet sheet = writer.getSheet();
        int nums = 0;
        while (nums < 4){
            sheet.addMergedRegion(new CellRangeAddress(0,1,nums,nums));
                nums++;
        }
        sheet.addMergedRegion(new CellRangeAddress(0,0,4,10));
        nums = 11;
        while (nums < 17){
            sheet.addMergedRegion(new CellRangeAddress(0,1,nums,nums));
            nums++;
        }

    }

    /**
     * 生成文件名
     * @param storeNo
     * @return
     */
    private String createFileName(Integer storeNo) {
        String localDate = DateUtils.tranfLocalDate(LocalDate.now(), "yyyy-MM-dd");
        Area area = areaMapper.queryByAreaNo(storeNo);
        String storeName = Objects.nonNull(area) ? area.getAreaName() : StringUtils.EMPTY;
        return localDate + storeName + "水果采购预测.xls";
    }


    /**
     * 转成DO对象
     * @param record
     * @return
     */
    private FruitPurchasePredictionDO transfromDO(Record record) {
        FruitPurchasePredictionDO fpp = new FruitPurchasePredictionDO();
        fpp.setPredictionDate(LocalDate.now());
        fpp.setAreaNo(parseInt(record.get("store_no")));
        fpp.setSku(parseString(record.get("sku")));
        fpp.setPdName(parseString(record.get("spu_name")));
        fpp.setWeight(parseString(record.get("weight")));
        fpp.setQuantity(parseInt(record.get("available_stock")));
        fpp.setSalesPrediction(parseInt(record.get("pre_sales_1d")));
        fpp.setPreSaleSecondDay(parseInt(record.get("pre_sales_2d")));
        fpp.setPreSaleThirdDay(parseInt(record.get("pre_sales_3d")));
        fpp.setPreSaleFourthDay(parseInt(record.get("pre_sales_4d")));
        fpp.setPreSaleFifthDay(parseInt(record.get("pre_sales_5d")));
        fpp.setPreSaleSixthDay(parseInt(record.get("pre_sales_6d")));
        fpp.setPreSaleSeventhDay(parseInt(record.get("pre_sales_7d")));
        fpp.setVipAvgSales(parseInt(record.get("vip_avg_sales_3d")));
        fpp.setSingleAvgSales(parseInt(record.get("single_avg_sales_3d")));
        //废弃
        fpp.setPurchaseReference(0);
        fpp.setRecentSevenDaysSales(0);
        fpp.setRecentThreeDaysSales(0);
        fpp.setWorkDaySalesBM(0);
        fpp.setWorkDaySalesSS(0);
        fpp.setSalesRate(parseDouble(record.get("sales_rate")));
        String purchaser = String.valueOf(record.get("purchaser"));
        if (!StringUtils.equalsIgnoreCase("\\N", purchaser)) {
            fpp.setPurchaser(purchaser);
        }
        return fpp;
    }


    /**
     * 获取所有水果的sku
     */
    private Set<String> getFruitSku(){
        Set<Integer> fruitCategoryIds = categoryMapper.selectByParentId(Global.isProduct() ? 87 : 6)
                .stream().map(Category::getId).collect(Collectors.toSet());
        List<Products> productList = productsMapper.selectByCategoryIds(fruitCategoryIds);
        Set<Long> productIds = productList.stream().map(Products::getPdId).collect(Collectors.toSet());
        List<Inventory> inventoryList = inventoryMapper.selectByPdIds(productIds);
        Set<String> skuIds = inventoryList.stream().map(Inventory::getSku).collect(Collectors.toSet());
        return skuIds;
    }


    /**
     * 计算给定时间内sku的采购量,分仓库
     * @param startTime
     * @param endTime
     * @param skuSet
     * @return key->sku+areaNo value->采购总量
     */
    private Map<String, Integer> calcPurchaseQuantity(LocalDateTime startTime, LocalDateTime endTime, Set<String> skuSet) {

        Map<String, Integer> result = Maps.newHashMap();

        List<PurchasesPlanVO> purchasesPlans = purchasesPlanMapper.selectValidByAddTimeAndSku(startTime, endTime, skuSet);
        if (CollectionUtils.isEmpty(purchasesPlans)) {
            return result;
        }
        Map<Integer, List<PurchasesPlanVO>> purchasesPlansByAreaNo = purchasesPlans.stream().collect(Collectors.groupingBy(PurchasesPlanVO::getAreaNo));
        Set<Integer> areaNos = purchasesPlansByAreaNo.keySet();
        for (Integer areaNo : areaNos) {
            Map<String, List<PurchasesPlanVO>> purchasesPlanBySkuMap = purchasesPlansByAreaNo.get(areaNo).stream().collect(Collectors.groupingBy(PurchasesPlanVO::getSku));
            Set<String> skus = purchasesPlanBySkuMap.keySet();
            for (String sku : skus) {
                List<PurchasesPlanVO> purchasesPlan= purchasesPlanBySkuMap.get(sku);
                int sum = purchasesPlan.stream().mapToInt(PurchasesPlan::getQuantity).sum();
                String skuArea = sku + "," + areaNo;
                result.put(skuArea,sum);
            }
        }

        return result;
    }


    /**
     * 计算采购异常提醒数据
     * 该sku当日采购量低于前一天采购量的30%即为异常
     *
     * @param yesterdayPurchase
     * @param todayPurchase
     * @return
     */
    private List<FruitPurchaseRemindModel> calcPurchaseRemind(Map<String, Integer> yesterdayPurchase,
                                      Map<String, Integer> todayPurchase){

        List<FruitPurchaseRemindModel> result =  Lists.newArrayList();

        Set<String> allSkuArea = Sets.newHashSet(CollectionUtil.addAll(Sets.newHashSet(yesterdayPurchase.keySet()),
                Sets.newHashSet(todayPurchase.keySet())));
        if (CollectionUtils.isEmpty(allSkuArea)) {
            return Lists.newArrayList();
        }
        for (String skuArea : allSkuArea) {
            Integer yPurchase = Optional.ofNullable(yesterdayPurchase.get(skuArea)).orElse(0);
            Integer tPurchase = Optional.ofNullable(todayPurchase.get(skuArea)).orElse(0);

            String[] skuAndArea = skuArea.split(",");
            String sku = skuAndArea[0];
            Integer areaNo = parseInt(skuAndArea[1]);
            Area area = areaMapper.queryByAreaNo(areaNo);
            String areaName = Objects.isNull(area.getAreaName()) ? " " : area.getAreaName();

            AreaStore query = new AreaStore();
            query.setAreaNo(areaNo);
            query.setSku(sku);
            Integer skuLeftAmount = null;
            AreaStore areaStore = areaStoreMapper.selectWithOutDataPermission(query);
            if(Objects.nonNull(areaStore)){
                skuLeftAmount = Optional.ofNullable(areaStore.getQuantity()).orElse(0);
            }

            if (Objects.equals(tPurchase, 0) && !Objects.equals(yPurchase, 0)) {
                result.add(new FruitPurchaseRemindModel(sku, tPurchase, 1d,areaName,skuLeftAmount));
            } else if(!Objects.equals(yPurchase,0)){
                Double todayRate =  (1.0d - (double)tPurchase/yPurchase);
                if (todayRate > FRUIT_PRUCHASE_REMIND_THRESHOLD) {
                    result.add(new FruitPurchaseRemindModel(sku, tPurchase, todayRate,areaName,skuLeftAmount));
                }
            }




        }

        if (CollectionUtils.isNotEmpty(result)) {
            buildPdName(result);
        }

        return result;
    }

    /**
     * 构建商品名称
     * @param remindList
     */
    private void buildPdName(List<FruitPurchaseRemindModel> remindList) {
        List<InventoryVO> inventoryList = inventoryMapper.queryPdNameBySkus(
                remindList.stream().map(FruitPurchaseRemindModel::getSku).collect(Collectors.toSet()));
        Map<String, String> skuPdNameMap = inventoryList.stream()
                .collect(Collectors.toMap(InventoryVO::getSku, InventoryVO::getProductName));
        remindList.forEach(el ->
                el.setPdName(Optional.ofNullable(skuPdNameMap.get(el.getSku())).orElse(StringUtils.EMPTY)));
    }


    /**
     * 发送水果采购提醒
     * @param remindList
     */
    private void sendFruitPurchaseRemind(List<FruitPurchaseRemindModel> remindList){

        String url = configMapper.selectOne(FRUIT_PURCHASE_ROBOT_CONFIG).getValue();

        StringBuilder text = new StringBuilder();

        NumberFormat pf = NumberFormat.getPercentInstance();
        pf.setMinimumIntegerDigits(1);
        pf.setMaximumFractionDigits(2);
        pf.setGroupingUsed(false);

        for(FruitPurchaseRemindModel fpr : remindList){
            text.append(fpr.getSkuAreaName()).append(fpr.getSku()).append(fpr.getPdName()).append("今日采购量").append(fpr.getTodayPurchase())
                    .append("件，低于上次采购量的").append(pf.format(fpr.getTodayPurchaseRate())).append("，当前剩余库存量")
                    .append(fpr.getSkuLeftAmount()).append(LINE_BREAK);
        }
        text.append("请确定是否需要调整大客户预留库存！");

        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setIsAtAll(true);
        dingTalkService.dingTalkRobotTxtAT(url, text.toString(), at);
    }


}
