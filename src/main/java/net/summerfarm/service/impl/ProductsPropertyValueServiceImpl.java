package net.summerfarm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.mapper.manage.ProductsPropertyValueMapper;
import net.summerfarm.model.domain.ProductsPropertyValue;
import net.summerfarm.model.domain.ProductsPropertyValueInfo;
import net.summerfarm.service.ProductsPropertyValueService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/9 14:25
 */
@Service
public class ProductsPropertyValueServiceImpl implements ProductsPropertyValueService {

    @Resource
    private ProductsPropertyValueMapper productsPropertyValueMapper;

    @Override
    public ProductsPropertyValue selectByPdIdAndPropertyId(Long pdId, Long propertyId) {
        return productsPropertyValueMapper.selectByPdIdAndPropertyId(pdId, propertyId);
    }

    @Override
    public List<ProductsPropertyValue> selectByPdIds(List<Long> pdIds) {
        if (CollectionUtils.isEmpty(pdIds)) {
            return Lists.newArrayList();
        }
        return productsPropertyValueMapper.selectByPdIds(pdIds);
    }

    @Override
    public List<ProductsPropertyValueInfo>  selectByPdIdAndProductsPropertyIds(Long pdId, List<Long> propertyIds) {
        if (CollectionUtils.isEmpty(propertyIds) || Objects.isNull(pdId)){
            return null;
        }
        return productsPropertyValueMapper.selectByPdIdAndProductsPropertyIds(pdId,propertyIds);
    }

    @Override
    public List<ProductsPropertyValueInfo> selectByPdIdAndSkuAndProductsPropertyIds(Long pdId, String sku, List<Long> propertyIds) {
        if (CollectionUtils.isEmpty(propertyIds) || Objects.isNull(pdId)){
            return null;
        }
        return productsPropertyValueMapper.selectByPdIdAndSkuAndProductsPropertyIds(pdId, sku, propertyIds);
    }

    @Override
    public PageInfo<String> queryContainsGSku(int pageIndex, int pageSize) {
        PageHelper.startPage(pageIndex, pageSize);
        List<String> list = productsPropertyValueMapper.queryContainsGSku();
        return new PageInfo<>(list);
    }
}
