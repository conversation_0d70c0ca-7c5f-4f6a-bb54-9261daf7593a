package net.summerfarm.service.impl;

import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.ResultConstant;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.model.domain.BusinessMission;
import net.summerfarm.mapper.manage.BusinessMissionMapper;
import net.summerfarm.service.BusinessMissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Package: net.summerfarm.service.impl
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/3/23
 */
@Service
public class BusinessMissionServiceImpl extends BaseService implements BusinessMissionService {

    private static final Logger logger = LoggerFactory.getLogger(BusinessMissionService.class);


    @Resource
    private BusinessMissionMapper businessMissionMapper;

    @Override
    public AjaxResult select(int pageIndex, int pageSize, BusinessMission selectKeys) {
        PageHelper.startPage(pageIndex, pageSize);
        List<BusinessMission> businessMissions = businessMissionMapper.select(selectKeys);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(businessMissions));
    }

    @Override
    public AjaxResult update(BusinessMission businessMission) {
        BusinessMission record = businessMissionMapper.selectByPrimaryKey(businessMission.getId());
        if (record == null) {
            return AjaxResult.getError(ResultConstant.RECORD_NOT_EXIST);
        }
        logger.info("管理员{}修改了{}城市gmv目标{}", getAdminId(),businessMission.getAreaNo(), businessMission.getId());
        businessMission.setUpdateTime(new Date());
        businessMissionMapper.updateByPrimaryKey(businessMission);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult save(BusinessMission businessMission) {
        List<BusinessMission> businessMissions =
                businessMissionMapper.select(new BusinessMission(businessMission.getYear(), businessMission.getMonth(), businessMission.getAreaNo()));
        if (!CollectionUtils.isEmpty(businessMissions)) {
            return AjaxResult.getError(ResultConstant.RECORD_EXIST);
        }
        logger.info("管理员{}新增了{}城市{}年{}月gmv目标", getAdminId(), businessMission.getAreaNo(),businessMission.getYear(), businessMission.getMonth());
        businessMission.setUpdateTime(new Date());
        businessMissionMapper.insert(businessMission);
        return AjaxResult.getOK();
    }
}
