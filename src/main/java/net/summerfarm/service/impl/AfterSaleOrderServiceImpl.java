package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.client.provider.DownloadCenterProvider;
import net.summerfarm.common.client.req.DownloadCenterInitReq;
import net.summerfarm.common.client.req.DownloadCenterUploadReq;
import net.summerfarm.common.client.resp.DownloadCenterResp;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.executor.ExecutorFactory;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.contexts.WholeOrderAfterSaleOrder;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.*;
import net.summerfarm.facade.mall.AfterSaleFacade;
import net.summerfarm.facade.ofc.OfcAfterSaleAfterDeliveryFacade;
import net.summerfarm.facade.ofc.OfcLogisticsQueryFacade;
import net.summerfarm.facade.ofc.OfcQueryFacade;
import net.summerfarm.facade.ofc.dto.OrderFulfillmentInfo;
import net.summerfarm.facade.tms.TmsDeliverySiteQueryStandardFacade;
import net.summerfarm.facade.tms.TmsDistOrderQueryStandardFacade;
import net.summerfarm.facade.wms.AreaStoreFacade;
import net.summerfarm.facade.wms.dto.AreaStoreUnLockReq;
import net.summerfarm.facade.wms.dto.OrderUnLockSkuDetailReqDTO;
import net.summerfarm.facade.wnc.DeliveryFenceQueryFacade;
import net.summerfarm.facade.wnc.dto.AreaQueryRes;
import net.summerfarm.facade.wnc.dto.FenceCloseTimeInput;
import net.summerfarm.mall.client.req.afterSale.CalcAfterSaleCouponReq;
import net.summerfarm.mall.client.req.afterSale.DeliveryStatusQueryReq;
import net.summerfarm.mall.client.req.afterSale.HandleTypeQueryReq;
import net.summerfarm.mall.client.resp.ExecutableAfterSaleResp;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.AfterSaleProofExportInput;
import net.summerfarm.model.vo.*;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.req.QueryAfterSaleAfterDeliveryReq;
import net.summerfarm.ofc.client.resp.afterSale.AfterSaleDeliveryInfoResp;
import net.summerfarm.ofc.client.resp.afterSale.AfterSaleDeliveryItemInfoResp;
import net.summerfarm.service.*;
import net.summerfarm.service.order.OrderRelationService;
import net.summerfarm.task.AsyncOrderTaskService;
import net.summerfarm.tms.client.dist.req.standard.DistOrderBatchQueryStandardReq;
import net.summerfarm.tms.client.dist.req.standard.DistOrderQueryStandardReq;
import net.summerfarm.tms.client.dist.resp.standard.DistOrderStandardResp;
import net.summerfarm.warehouse.mapper.WarehouseLogisticsCenterMapper;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * @Package: net.summerfarm.service.impl
 * @Description: 售后
 * @author: <EMAIL>
 * @Date: 2017/7/10
 */
@Service
@Slf4j
public class AfterSaleOrderServiceImpl extends BaseService implements AfterSaleOrderService {
    private final static List<Short> deliveryPlanNoInterceptStatus = Arrays.asList ((short)2, (short)3, (short)6, (short)8);

    private static final Logger logger = LoggerFactory.getLogger(AfterSaleOrderService.class);
    @Resource
    private CompleteDeliveryMapper completeDeliveryMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private CouponMapper couponMapper;
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private SuitMapper suitMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private StockTaskMapper stockTaskMapper;
    @Resource
    private StockTaskItemMapper stockTaskItemMapper;
    @Resource
    private OrdersCouponMapper ordersCouponMapper;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Resource
    private DeliveryPathMapper deliveryPathMapper;
    @Resource
    AreaStoreService areaStoreService;
    @Resource
    QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    private MarketCouponSendDetailMapper marketCouponSendDetailMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private WarehouseLogisticsCenterMapper warehouseLogisticsCenterMapper;
    @Lazy
    @Resource
    private WholeOrderAfterSaleOrder wholeOrderAfterSaleOrder;
    @Resource
    private TmsDeliverySiteQueryStandardFacade tmsDeliverySiteQueryStandardFacade;
    @Resource
    private TmsDistOrderQueryStandardFacade tmsDistOrderQueryStandardFacade;
    @Resource
    private AfterSaleFacade afterSaleFacade;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private DeliveryFenceQueryFacade deliveryFenceQueryFacade;

    @Autowired
    MqProducer mqProducer;

    @DubboReference
    private DownloadCenterProvider downloadCenterProvider;

    @Resource
    private RedisTemplate<String,String> redisTemplate;

    @Resource
    private AsyncOrderTaskService asyncOrderTaskService;

    @Resource
    private OfcAfterSaleAfterDeliveryFacade ofcAfterSaleAfterDeliveryFacade;
    @Resource
    private AreaStoreFacade areaStoreFacade;
    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private OrderRelationService ofcRelationService;
    @Resource
    private OrderPreferentialMapper orderPreferentialMapper;

    @Resource
    private ConfigService configService;

    @Resource
    private FollowUpRelationMapper followUpRelationMapper;

    @Resource
    private DingTalkMsgSender dingTalkMsgSender;

    @Autowired
    private OfcLogisticsQueryFacade ofcLogisticsQueryFacade;

    /**
     * 订单拦截通知群机器人Config
     */
    private static final String REFUND_FAIL_NOTIFY = "refund_fail_notify";
    @Override
    public AjaxResult selectPage(int pageIndex, int pageSize, AfterSaleOrderVO selectKeys) {
        String orderby = " t.id ";
        if (selectKeys.getStatus() == null || selectKeys.getStatus() == 2 || selectKeys.getStatus() == 3) {
            orderby = " t.id desc";
        }

        String mPhone = selectKeys.getPhone();
        if (StringUtils.isNotBlank(mPhone)){

            // 手机号存在就先走手机号的查询
            List<Long> mIdList = merchantMapper.selectByPhone(mPhone, null);
            // 未检测到数据直接返回
            if (CollectionUtil.isEmpty(mIdList)){
                return AjaxResult.getOK(PageInfoHelper.createPageInfo(Collections.emptyList()));
            }
            selectKeys.setMIdList(mIdList);
        }

        PageHelper.startPage(pageIndex, pageSize).setOrderBy(orderby);
        List<AfterSaleOrderVO> afterSaleOrders = afterSaleOrderMapper.selectByOneProof(selectKeys);
        if (!afterSaleOrders.isEmpty()) {
            List<String> ids = afterSaleOrders.stream().map(AfterSaleOrder::getAfterSaleOrderNo).collect(Collectors.toList());
            Map<String, List<AfterSaleProof>> groupBy = afterSaleProofMapper.selectList(ids).stream().collect(Collectors.groupingBy(AfterSaleProof::getAfterSaleOrderNo));

            List<DeliveryPlan> deliveryPlans = queryDeliveryPlan (afterSaleOrders);
            Map<String, DeliveryPlan> orderNoMap = Collections.emptyMap ();
            Map<Integer, DeliveryPlan> idMap = Collections.emptyMap ();
            Map<Integer, WarehouseLogisticsCenter> warehouseLogisticsCenterMap = Collections.emptyMap ();
            if(CollectionUtil.isNotEmpty (deliveryPlans)) {
                 orderNoMap = deliveryPlans.stream ().collect (Collectors.toMap (DeliveryPlan::getOrderNo, e -> e, (existing, replacement) -> existing));
                 idMap = deliveryPlans.stream ().collect (Collectors.toMap (DeliveryPlan::getId, e -> e, (existing, replacement) -> existing));
                //查询结单时间
                /*Set<Integer> storeNos = deliveryPlans.stream ().map (DeliveryPlan::getOrderStoreNo).collect (Collectors.toSet ());
                if(CollectionUtil.isNotEmpty (storeNos)) {
                    warehouseLogisticsCenterMap = warehouseLogisticsCenterMapper.selectByStoreNos (storeNos).stream ().collect (Collectors.toMap (WarehouseLogisticsCenter::getStoreNo, e -> e, (existing, replacement) -> existing));
                }*/
            }
            //获取DistOrderStandardMap
            Map<String, DistOrderStandardResp> longDistOrderStandardRespMap = queryDistOrderStandardMap (afterSaleOrders, orderNoMap, idMap);


            //获取订单详细信息
            for (AfterSaleOrderVO afterSaleOrder : afterSaleOrders) {
                DeliveryPlan deliveryPlan = afterSaleOrder.getDeliveryId () == null ? orderNoMap.get (afterSaleOrder.getOrderNo ()) : idMap.get (afterSaleOrder.getDeliveryId ());
                handleVOV2(afterSaleOrder, groupBy.get(afterSaleOrder.getAfterSaleOrderNo()),deliveryPlan,longDistOrderStandardRespMap,warehouseLogisticsCenterMap);
            }
        }
        //过滤原订单配送类型
        if (selectKeys.getOrderDeliveryStatus() != null){
            afterSaleOrders = afterSaleOrders.stream().filter(e -> selectKeys.getOrderDeliveryStatus().equals(e.getOrderDeliveryStatus())).collect(Collectors.toList());
        }
        PageInfo<AfterSaleOrderVO> pageInfo = PageInfoHelper.createPageInfo(afterSaleOrders);
        return AjaxResult.getOK(pageInfo);
    }

    private Map<String,DistOrderStandardResp> queryDistOrderStandardMap(List<AfterSaleOrderVO> afterSaleOrders, Map<String, DeliveryPlan> orderNoMap, Map<Integer, DeliveryPlan> idMap) {
        List<DistOrderQueryStandardReq> distOrderQueryStandardReqs = new ArrayList<> ();
        for (AfterSaleOrderVO afterSaleOrder : afterSaleOrders) {
            DeliveryPlan deliveryPlan = afterSaleOrder.getDeliveryId () == null ? orderNoMap.get (afterSaleOrder.getOrderNo ()) : idMap.get (afterSaleOrder.getDeliveryId ());
            if(deliveryPlan != null && deliveryPlan.getInterceptFlag () == 1){
                DistOrderQueryStandardReq queryStandardReq = new DistOrderQueryStandardReq();
                queryStandardReq.setOuterOrderId(afterSaleOrder.getOrderNo());
                if (Objects.equals(afterSaleOrder.getOrderType (),OrderTypeEnum.TIMING.getId())) {
                    queryStandardReq.setSource(TmsDistOrderTypeSourceEnum.SAVE_MIND.getCode());
                } else {
                    queryStandardReq.setSource(TmsDistOrderTypeSourceEnum.getDistOrderSourceByOrderType(afterSaleOrder.getOrderType()));
                }
                queryStandardReq.setOuterContactId(String.valueOf(deliveryPlan.getContactId()));
                queryStandardReq.setExpectBeginTime(deliveryPlan.getDeliveryTime().atTime(00,00,00,00));
                distOrderQueryStandardReqs.add (queryStandardReq);
            }
        }
        if (CollectionUtil.isNotEmpty(distOrderQueryStandardReqs)) {
            DistOrderBatchQueryStandardReq queryStandardReq = new DistOrderBatchQueryStandardReq();
            queryStandardReq.setDistOrderQueryStandardReqs (distOrderQueryStandardReqs);
            List<DistOrderStandardResp> distOrderStandardResps = tmsDistOrderQueryStandardFacade.queryDistOrderDetailList (queryStandardReq);
            if (CollectionUtil.isNotEmpty(distOrderStandardResps)) {
                return distOrderStandardResps.stream ().collect (Collectors.toMap (DistOrderStandardResp::getOutContactId, e -> e, (existing, replacement) -> existing));
            }
        }
        return Collections.emptyMap ();
    }

    private List<DeliveryPlan> queryDeliveryPlan(List<AfterSaleOrderVO> afterSaleOrders) {
        List<DeliveryPlan> deliveryPlans = new ArrayList<> ();
        List<String> orderNos = afterSaleOrders.stream().map(AfterSaleOrder::getOrderNo).collect(Collectors.toList());
        List<DeliveryPlan> deliveryPlansByOrderNos = deliveryPlanMapper.listDeliveryPlanByOrderNoList (orderNos);
        log.info("deliveryPlan is null,return; deliveryPlansByOrderNos={}",deliveryPlansByOrderNos);
        if(CollectionUtil.isNotEmpty (deliveryPlansByOrderNos)) {
            deliveryPlansByOrderNos = deliveryPlansByOrderNos.stream ().filter (deliveryPlan -> deliveryPlanNoInterceptStatus.contains (deliveryPlan.getStatus ())).collect (Collectors.toList ());
            log.info("deliveryPlan is null,return; after status fliter,deliveryPlansByOrderNos={}",deliveryPlansByOrderNos);
            if(CollectionUtil.isNotEmpty (deliveryPlansByOrderNos)) {
                deliveryPlans.addAll (deliveryPlansByOrderNos);
            }
        }
        List<Integer> deliveryIds = afterSaleOrders.stream ().filter (e -> e.getDeliveryId () != null).map (AfterSaleOrderVO::getDeliveryId).collect (Collectors.toList ());
        if(CollectionUtil.isNotEmpty (deliveryIds)) {
            List<DeliveryPlan> deliveryPlansByIds = deliveryPlanMapper.selectByIds (deliveryIds);
            log.info("deliveryPlan is null,return; deliveryPlansByIds={}",deliveryPlansByIds);
            if(CollectionUtil.isNotEmpty (deliveryPlansByIds)) {
                deliveryPlans.addAll (deliveryPlansByIds);
            }
        }
        return deliveryPlans;
    }


    @Override
    public AjaxResult selectExport(AfterSaleOrderVO selectKeys) {
        List<AfterSaleOrderVO> info = afterSaleOrderMapper.selectExport(selectKeys);
        if (CollectionUtils.isEmpty(info)) {
            throw new DefaultServiceException("暂无数据");
        }
        if (selectKeys.getStartTime() == null || selectKeys.getEndTime() == null) {
            throw new DefaultServiceException("申请时间必填");
        }
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("订单编号");
        title.createCell(1).setCellValue("客户名称");
        title.createCell(2).setCellValue("售后时间");
        title.createCell(3).setCellValue("province");
        title.createCell(4).setCellValue("city");
        title.createCell(5).setCellValue("area");
        title.createCell(6).setCellValue("sku");
        title.createCell(7).setCellValue("spu");
        title.createCell(8).setCellValue("实付单价");
        title.createCell(9).setCellValue("sku原价");
        title.createCell(10).setCellValue("购买数量");
        title.createCell(11).setCellValue("售后数量");
        title.createCell(12).setCellValue("售后金额");
        title.createCell(13).setCellValue("客服备注");
        title.createCell(14).setCellValue("售后原因");
        title.createCell(15).setCellValue("售后类型");
        title.createCell(16).setCellValue("补偿类型");
        title.createCell(17).setCellValue("售后订单类型");
        title.createCell(18).setCellValue("补偿方式");
        title.createCell(19).setCellValue("客户类型");
        title.createCell(20).setCellValue("审批人");
        title.createCell(21).setCellValue("审批时间");
        title.createCell(22).setCellValue("原订单配送状态");
        int index = 1;
        if (info.size() > 0) {
            for (AfterSaleOrderVO afterSaleOrderVO : info) {
                Row row = sheet.createRow(index);
                row.createCell(0).setCellValue(afterSaleOrderVO.getOrderNo());
                row.createCell(1).setCellValue(afterSaleOrderVO.getMname());
                row.createCell(2).setCellValue(afterSaleOrderVO.getAddTime() == null ? null : afterSaleOrderVO.getAddTime().toString());
                row.createCell(3).setCellValue(afterSaleOrderVO.getProvince());
                row.createCell(4).setCellValue(afterSaleOrderVO.getCity());
                row.createCell(5).setCellValue(afterSaleOrderVO.getArea());
                row.createCell(6).setCellValue(afterSaleOrderVO.getSku());
                row.createCell(7).setCellValue(afterSaleOrderVO.getSpu());
                row.createCell(8).setCellValue(afterSaleOrderVO.getPrice() == null ? null : afterSaleOrderVO.getPrice().toString());
                row.createCell(9).setCellValue(afterSaleOrderVO.getOriginalPrice() == null ? null : afterSaleOrderVO.getOriginalPrice().toString());
                row.createCell(10).setCellValue(afterSaleOrderVO.getAmount());
                row.createCell(11).setCellValue(afterSaleOrderVO.getQuantity() == null ? null : afterSaleOrderVO.getQuantity().toString());
                row.createCell(12).setCellValue(afterSaleOrderVO.getHandleNum() == null ? null : afterSaleOrderVO.getHandleNum().toString());
                row.createCell(13).setCellValue(afterSaleOrderVO.getHandleRemark());
                row.createCell(14).setCellValue(afterSaleOrderVO.getAfterSaleType());
                row.createCell(15).setCellValue(afterSaleOrderVO.getAfterSaleSituation());
                row.createCell(16).setCellValue(afterSaleOrderVO.getCompensateType());
                row.createCell(17).setCellValue(afterSaleOrderVO.getAfterSaleOrderType());
                row.createCell(18).setCellValue(afterSaleOrderVO.getCompensateMode());
                row.createCell(19).setCellValue(afterSaleOrderVO.getmSize());
                row.createCell(20).setCellValue(afterSaleOrderVO.getAuditer());
                row.createCell(21).setCellValue(afterSaleOrderVO.getAuditetime() == null ? null : afterSaleOrderVO.getAuditetime().toString());
                row.createCell(22).setCellValue(afterSaleOrderVO.getOrderStautsName());
                index++;
            }
        }
        String date = "";
        if (selectKeys.getEndTime() != null) {
            date = DateUtils.tranfLocalDate(selectKeys.getStartTime().toLocalDate(), "yyyyMMdd") + "-" + DateUtils.tranfLocalDate(selectKeys.getEndTime().toLocalDate(), "yyyyMMdd");
        }
        String fileName = "售后订单" + date + ".xls";
        try {
            ExcelUtils.outputExcel(workbook, fileName, RequestHolder.getResponse());
        } catch (IOException e) {
            logger.error(Global.collectExceptionStackMsg(e));
            throw new DefaultServiceException("导出异常");
        }
        return null;
    }

    @Override
    public AjaxResult selectOrderAll(AfterSaleOrderVO selectKeys) {
        List<AfterSaleOrderVO> afterSaleOrderVOs = afterSaleOrderMapper.selectByOneProof(selectKeys);
        BigDecimal bigDecimal = ordersMapper.selectCouponDeliveryFeeByOrderNo(selectKeys.getOrderNo());
        if (bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0) {
            bigDecimal = orderPreferentialMapper.selectDeliveryFeeAmountByOrderNo(selectKeys.getOrderNo());
        }
        if (!CollectionUtils.isEmpty(afterSaleOrderVOs)) {
            for (AfterSaleOrderVO o : afterSaleOrderVOs) {
                //设置原订单配送状态
                OrderVO orderVO = ordersMapper.selectByOrderyNo(o.getOrderNo());
                if (ObjectUtils.isEmpty(orderVO)){
                    return AjaxResult.getErrorWithMsg("暂无权限，请确认后重试");
                }

                List<DeliveryPlanVO> deliveryPlanVOS1 = deliveryPlanMapper.selectByOrderNo(selectKeys.getOrderNo());
                if (!CollectionUtils.isEmpty(deliveryPlanVOS1)){
                    DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS1.get(0);
                    Contact contact = contactMapper.selectByPrimaryKey(deliveryPlanVO.getContactId());
                    String date = completeDeliveryMapper.selectCompleteDeliveryTime(contact.getCity(), contact.getArea());
                    if (org.springframework.util.StringUtils.isEmpty(date)){
                        //为空默认为中午12点预计完成时间
                        date = LocalTime.of(12, 0).toString();
                    }
                    DateTimeFormatter fmt = DateTimeFormatter.ofPattern("HH:mm");
                    LocalTime completeDeliveryTime = LocalTime.parse(date, fmt);
                    //进行拼接
                    LocalDateTime start = deliveryPlanVO.getDeliveryTime().atTime(completeDeliveryTime);
                    start = start.plusHours(48L);
                    //判断是否为超时售后
                    o.setIsOverTimeAfter(o.getAddTime().isAfter(start));
                }
                if (o.getDeliveryFee() != null && o.getDeliveryFee().compareTo(BigDecimal.ZERO) > 0){
                    //此处判空是因为之前创建order_delivery_record这个表没有处理秒杀的，所以查询为空，秒杀没有券使用，所以为0即可避免空指针
                    if (null == bigDecimal){
                        bigDecimal = BigDecimal.ZERO;
                    }
                    BigDecimal actualDeliveryFee = o.getDeliveryFee().subtract(bigDecimal);
                    o.setActualDeliveryFee(actualDeliveryFee);
                }
                //查询对应的退款信息
                RefundVO refund = refundMapper.selectByAfterSaleOrderNo(o.getAfterSaleOrderNo());
                o.setRefundVO(refund);

                //查询回收/补发单信息 改成dubbo调用
                //List<AfterSaleDeliveryPathVO> afterSaleDeliveryPathVOS = afterSaleDeliveryPathMapper.selectByAfterNo(o.getAfterSaleOrderNo());
                AfterSaleDeliveryPath path = afterSaleDeliveryPathMapper.selectPathByNo(o.getAfterSaleOrderNo());
                List<AfterSaleDeliveryItemInfoResp> itemInfoResps = null;
                QueryAfterSaleAfterDeliveryReq queryAfterSaleAfterDeliveryReq = new QueryAfterSaleAfterDeliveryReq();
                queryAfterSaleAfterDeliveryReq.setAfterSaleOrderNo(o.getAfterSaleOrderNo());
                queryAfterSaleAfterDeliveryReq.setNeedSourceFulfillmentOrder(Boolean.FALSE);
                AfterSaleDeliveryInfoResp afterSaleDeliveryInfo = ofcAfterSaleAfterDeliveryFacade.queryAfterSaleDeliveryInfo(queryAfterSaleAfterDeliveryReq);
                if (Objects.nonNull(afterSaleDeliveryInfo) && !CollectionUtils.isEmpty(afterSaleDeliveryInfo.getDetailList())) {
                    itemInfoResps = afterSaleDeliveryInfo.getDetailList();
                    List<AfterSaleDeliveryPathVO> afterSaleDeliveryPathVOS = new ArrayList<>(itemInfoResps.size());
                    itemInfoResps.stream().forEach(e -> {
                        AfterSaleDeliveryPathVO afterSaleDeliveryPathVO = new AfterSaleDeliveryPathVO();
                        afterSaleDeliveryPathVO.setFinishTime(afterSaleDeliveryInfo.getFinishTime());
                        afterSaleDeliveryPathVO.setSignForStatus(e.getStatus());
                        afterSaleDeliveryPathVO.setRemark(e.getRemark());
                        afterSaleDeliveryPathVO.setType(e.getType());
                        afterSaleDeliveryPathVO.setDeliveryTime(afterSaleDeliveryInfo.getFulfillmentTime());
                        if (e.getType() == 1 && e.getItemRecycleResp() != null){
                            afterSaleDeliveryPathVO.setRecyclePics(e.getItemRecycleResp().getRecyclePics());
                            afterSaleDeliveryPathVO.setReasonType(e.getItemRecycleResp().getReasonType());
                            afterSaleDeliveryPathVO.setReasonTypeDesc(e.getItemRecycleResp().getReasonTypeDesc());
                            if (!CollectionUtils.isEmpty(e.getItemRecycleResp().getAfterSaleNoList())){
                                afterSaleDeliveryPathVO.setAfterSaleNoList(e.getItemRecycleResp().getAfterSaleNoList());
                            }
                        }

                        afterSaleDeliveryPathVO.setPlanReceiptCount(e.getPlanReceiptCount());
                        afterSaleDeliveryPathVO.setRealReceiptCount(e.getRealReceiptCount());
                        if (Objects.nonNull(path)) {
                            afterSaleDeliveryPathVO.setStatus(path.getStatus());
                        }
                        afterSaleDeliveryPathVOS.add(afterSaleDeliveryPathVO);
                    });
                    o.setAfterSaleDeliveryPathVO(afterSaleDeliveryPathVOS);
                }

                //查询支付方式
                List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.selectDescOne(o.getAfterSaleOrderNo());
                handleVO(o, afterSaleProofs);
                o.setAfterSaleProofList(afterSaleProofs);
                AfterSaleProof afterSaleProof = afterSaleProofs.get(0);
                if (o.getDeliveryed() == 0 && o.getSuitId() > 0) {
                    o.setSuitName(suitMapper.selectByPrimaryKey(o.getSuitId()).getSuitName());
                }
                String afterSaleOrderNo = o.getAfterSaleOrderNo();
                //获取售后单信息

                if (path != null) {
                    //获取配送信息
                    DistOrderQueryStandardReq queryStandardReq = new DistOrderQueryStandardReq();
                    queryStandardReq.setOuterOrderId(afterSaleOrderNo);
                    queryStandardReq.setSource(TmsDistOrderTypeSourceEnum.getDistOrderAfterSaleSourceByOrderType(orderVO.getType()));
                    queryStandardReq.setOuterContactId(String.valueOf(path.getConcatId()));
                    queryStandardReq.setExpectBeginTime(path.getDeliveryTime().atTime(00,00,00,00));
                    DistOrderStandardResp distOrderDetail = tmsDistOrderQueryStandardFacade.queryDistOrderDetail(queryStandardReq);
                    //DeliveryPath deliveryPath = deliveryPathMapper.selectOne(path.getOutStoreNo(), path.getDeliveryTime(), path.getConcatId());

                    //为空 没有配送
                    if (distOrderDetail != null) {

                        //设置完成配送时间
                        if (!CollectionUtils.isEmpty(o.getAfterSaleDeliveryPathVO())) {
                            o.getAfterSaleDeliveryPathVO().stream().forEach(e -> {
                                if (Objects.equals(e.getStatus(), 3)) {
                                    e.setFinishTime(distOrderDetail.getRealArrivalTime());
                                }
                            });
                        }

                        // 回收 配送 获取回收缺货信息
                        if (path.getType() != 0) {
                            //配送sku信息 改调用dubbo
                            //List<DeliveryPathShortSku> shortSkus = afterSaleDeliveryPathMapper.afterSaleByNo(afterSaleOrderNo, 1);

                            if (!CollectionUtils.isEmpty(itemInfoResps)) {
                                List<DeliveryPathShortSku> shortSkus = new ArrayList<>();
                                itemInfoResps.stream().filter(e -> Objects.equals(e.getType(), 1)).forEach(e -> {
                                    DeliveryPathShortSku deliveryPathShortSku = new DeliveryPathShortSku();
                                    deliveryPathShortSku.setType(e.getType());
                                    deliveryPathShortSku.setSku(e.getOutItemId());
                                    deliveryPathShortSku.setRemark(e.getRemark());
                                    deliveryPathShortSku.setShortCnt(e.getShortCount());
                                    shortSkus.add(deliveryPathShortSku);
                                });
                                if (!CollectionUtils.isEmpty(shortSkus)) {
                                    o.setDeliveryPathShortSkuList(shortSkus);
                                }
                            }

                            //配送 获取配送缺货信息
                        } else {
                            //List<DeliveryPathShortSku> shortSkus = afterSaleDeliveryPathMapper.afterSaleByNo(afterSaleOrderNo, 0);
                            if (!CollectionUtils.isEmpty(itemInfoResps)) {
                                List<DeliveryPathShortSku> shortSkus = new ArrayList<>();
                                itemInfoResps.stream().filter(e -> Objects.equals(e.getType(), 0)).forEach(e -> {
                                    DeliveryPathShortSku deliveryPathShortSku = new DeliveryPathShortSku();
                                    deliveryPathShortSku.setType(e.getType());
                                    deliveryPathShortSku.setSku(e.getOutItemId());
                                    deliveryPathShortSku.setRemark(e.getRemark());
                                    deliveryPathShortSku.setShortCnt(e.getShortCount());
                                    shortSkus.add(deliveryPathShortSku);
                                });
                                if (!CollectionUtils.isEmpty(shortSkus)) {
                                    o.setDeliveryPathShortSkuList(shortSkus);
                                }
                            }
                        }

                        if (distOrderDetail.getStatus() == null){
                            o.setDeliveryStatus(DeliveryStatusEnum.TO_BE_WIRED.getStatus());
                        }else {
                            o.setDeliveryStatus(TmsDistOrderStatusEnum.getDistOrderStatus(distOrderDetail.getStatus()));
                        }
                    } else {
                        o.setDeliveryStatus(DeliveryStatusEnum.TO_BE_WIRED.getStatus());
                    }

                    //换货获取对应的换货sku信息
                    if (Objects.equals(afterSaleProof.getHandleType(), AfterSaleHandleType.EXCHANGE_GOODS.getType()) ||
                            Objects.equals(afterSaleProof.getHandleType(), AfterSaleHandleType.REFUND_ENTRY_BILL.getType()) ||
                            Objects.equals(afterSaleProof.getHandleType(), AfterSaleHandleType.REFUND_GOODS.getType()) ) {
                        List<ExchangeGoods> exchangeGoods = afterSaleDeliveryPathMapper.selectExchangeGoods(afterSaleOrderNo);
                        if (!CollectionUtils.isEmpty(exchangeGoods)) {
                            o.setExchangeGoodList(exchangeGoods);
                        }
                    }
                }

                if (o.getSku()!= null){
                    InventoryVO inventoryVO = inventoryMapper.selectSkuAfterSale(o.getSku());
                    ProductsVO products = productsMapper.selectByPdId(inventoryVO.getPdId());
                    o.setAllRefundType(products.getRefundType());

                    if (!ObjectUtils.isEmpty(inventoryVO) && inventoryVO.getCategoryType() != null){
                        if (inventoryVO.getCategoryType() == 4){
                            o.setSkuCategoryType(true);
                        }else {
                            o.setSkuCategoryType(false);
                        }
                    }
                }
                // 补充售后单的物流信息
                OrderFulfillmentInfo orderFulfillmentInfo = ofcLogisticsQueryFacade.queryAfterSaleOrderLogisticsInfo(o.getAfterSaleOrderNo());
                if (orderFulfillmentInfo != null){
                    o.setOrderFulfillmentType(orderFulfillmentInfo.getOrderFulfillmentType());
                    o.setLogisticsInfoList(orderFulfillmentInfo.getLogisticsInfoList());
                }
            }
        }
        return AjaxResult.getOK(afterSaleOrderVOs);
    }


    @Override
    public AjaxResult pre(String sku) {
        AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        Inventory selectKeys = new Inventory();
        selectKeys.setSku(sku);
        Inventory inventory = inventoryMapper.selectOne(selectKeys);
        ProductsVO products = productsMapper.selectByPdId(inventory.getPdId());
        afterSaleOrderVO.setAfterSaleQuantity(inventory.getAfterSaleQuantity());
        afterSaleOrderVO.setAfterSaleTime(products.getAfterSaleTime());
        afterSaleOrderVO.setAfterSaleType(products.getAfterSaleType());
        // 售后单位从inventory表获取
        afterSaleOrderVO.setAfterSaleUnit(inventory.getAfterSaleUnit());
        afterSaleOrderVO.setCategoryId(products.getCategoryId());
        afterSaleOrderVO.setRefundType(products.getRefundType());
        return AjaxResult.getOK(afterSaleOrderVO);
    }

    @Override
    public AjaxResult calcAfterSaleCoupon(String orderNo, String sku, Integer expectQuantity, int suitId, int deliveryed,
                                          Integer type, Integer deliveryId, Integer handleType, String afterSaleOrderNo) {
        CalcAfterSaleCouponReq calcAfterSaleCouponReq = new CalcAfterSaleCouponReq();
        calcAfterSaleCouponReq.setOrderNo(orderNo);
        calcAfterSaleCouponReq.setSku(sku);
        calcAfterSaleCouponReq.setQuantity(expectQuantity);
        calcAfterSaleCouponReq.setSuitId(suitId);
        calcAfterSaleCouponReq.setDeliveryed(deliveryed);
        calcAfterSaleCouponReq.setHandleType(handleType);
        calcAfterSaleCouponReq.setType(type);
        calcAfterSaleCouponReq.setDeliveryId(deliveryId);
        calcAfterSaleCouponReq.setAfterSaleOrderNo(afterSaleOrderNo);
        return AjaxResult.getOK(afterSaleFacade.afterSaleCoupon(calcAfterSaleCouponReq));
    }


    public AfterSaleProof conventAfterSaleProof(AfterSaleOrderVO afterSaleOrderVO) {
        AfterSaleProof afterSaleProof = new AfterSaleProof();
        afterSaleProof.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        afterSaleProof.setProofPic(afterSaleOrderVO.getProofPic());
        afterSaleProof.setHandleType(afterSaleOrderVO.getHandleType());
        afterSaleProof.setHandleNum(afterSaleOrderVO.getHandleNum());
        afterSaleProof.setHandler(afterSaleOrderVO.getHandler());
        afterSaleProof.setAuditer(afterSaleOrderVO.getAuditer());
        afterSaleProof.setApplyRemark(afterSaleOrderVO.getApplyRemark());
        afterSaleProof.setQuantity(afterSaleOrderVO.getQuantity());
        afterSaleProof.setHandleRemark(afterSaleOrderVO.getHandleRemark());
        afterSaleProof.setAfterSaleType(afterSaleOrderVO.getAfterSaleType());
        afterSaleProof.setRefundType(afterSaleOrderVO.getRefundType());
        afterSaleProof.setStatus(afterSaleOrderVO.getStatus());
        afterSaleProof.setHandletime(LocalDateTime.now());
        return afterSaleProof;
    }
    @Override
    public AjaxResult  save(AfterSaleOrderVO afterSaleOrderVO) {
        //测试日志
        logger.info("售后发起AfterSaleOrderVO："+JSON.toJSONString(afterSaleOrderVO));
        String orderNo = afterSaleOrderVO.getOrderNo();
        Orders orders = ordersMapper.selectByOrderyNo(afterSaleOrderVO.getOrderNo());

        //POP订单校验定责比例
        if (Objects.equals(orders.getType(), OrderTypeEnum.POP.getId()) && StringUtils.isBlank(afterSaleOrderVO.getSnapshot())) {
            return AjaxResult.getError("POP订单定责比例不能为空！");
        }
        if (Objects.equals(orders.getType(), OrderTypeEnum.POP.getId()) && StringUtils.isBlank(afterSaleOrderVO.getApplySecondaryRemark())) {
            return AjaxResult.getError("售后分类不能为空！");
        }

        //查询大客户预付商品信息
        List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectRecordList(orderNo, afterSaleOrderVO.getSku(), true);

        InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(afterSaleOrderVO.getSku());
        //换货，补发售后金额为0 元  拦截退单、拦截录入账单的省心送为0元
        boolean exGoodsRefund = true;
        if (Arrays.asList(new Integer[]{6, 7,11,12,13,14}).contains(afterSaleOrderVO.getHandleType())) {
            exGoodsRefund = false;
        }
        if (exGoodsRefund) {
            if (CollectionUtils.isEmpty(prepayInventoryRecords) && Objects.equals(inventoryVO.getType(), 0) && (afterSaleOrderVO.getHandleNum() == null || afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) <= 0)) {
                return AjaxResult.getErrorWithMsg("售后金额不能小于零");
            }
        }

        BigDecimal totalPrice = orders.getTotalPrice();
        // 获取使用的优惠券信息
        OrderRelation orderRelation =  ofcRelationService.selectByOrderNo(orderNo);
        String selectOrderNo = orderNo;
        if(!ObjectUtils.isEmpty(orderRelation)){
            selectOrderNo = orderRelation.getMasterOrderNo();
        }
        List<MerchantCouponVO> merchantCouponVOS = ordersCouponMapper.select(selectOrderNo);
        //使用售后券 且处理方式为返券 总金额 = 订单实付 + 券金额
        if (Objects.equals(afterSaleOrderVO.getHandleType(), AfterSaleHandleType.COUPON.getType()) &&
                !CollectionUtils.isEmpty(merchantCouponVOS)) {
            for (MerchantCouponVO merchantCouponVO : merchantCouponVOS){
                Integer grouping = merchantCouponVO.getGrouping();
                //校验券类型 和券金额
                if (Objects.equals(grouping, 1) && merchantCouponVO.getMoney() != null) {
                    BigDecimal money = merchantCouponVO.getMoney();
                    totalPrice = totalPrice.add(money);
                }
            }
        }
        BigDecimal originHandleNum = afterSaleOrderVO.getHandleNum();
        if (afterSaleOrderVO.getRecoveryNum() != null) {
            originHandleNum = originHandleNum.subtract(afterSaleOrderVO.getRecoveryNum());
        }
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        List<AfterSaleOrderVO> afterSaleOrders = selectByOrderNo(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getDeliveryId());

        if (exGoodsRefund) {
            //已售后的数量
            BigDecimal handleNum = BigDecimal.ZERO;
            //提交售后的金额
            BigDecimal afterHandleNum = BigDecimal.ZERO;
            for (AfterSaleOrderVO check : afterSaleOrders) {
                if (Objects.equals(check.getDeliveryId(), deliveryId)) {
                    handleNum = handleNum.add(check.getHandleNum());
                }
                afterHandleNum = afterHandleNum.add(check.getHandleNum());

            }
            //售后金额大于可售后金额 提交报错
            if (totalPrice.subtract(afterHandleNum).compareTo(originHandleNum) < 0) {
                return AjaxResult.getError("REFUND", "发起售后的总金额大于订单金额");
            }

            if (afterSaleOrderVO.getType() == null || afterSaleOrderVO.getType() != 3) {
                if (handleNum.add(originHandleNum).compareTo(totalPrice) > 0) {
                    return AjaxResult.getError("TOTAL_REFUND", "退款金额异常");
                }
            }

        }
        //查询已提交的售后订单

        afterSaleOrderVO.setHandler(getAdminName());
        afterSaleOrderVO.setmId(orders.getmId());
        afterSaleOrderVO.setAddTime(LocalDateTime.now());
        afterSaleOrderVO.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        afterSaleOrderVO.setView(0);
        afterSaleOrderVO.setTimes(afterSaleOrders.size() + 1);
        afterSaleOrderVO.setDeliveryId(afterSaleOrderVO.getDeliveryId());
        afterSaleOrderVO.setAccountId(orders.getAccountId());
        afterSaleOrderVO.setApplyer(getAdminName());
        afterSaleOrderVO.setIsManage(true);

        //退货退款、退货录入账单 指定商品时候需要校验商品是否上架
        if (afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.REFUND_GOODS.getType()) ||
                afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.REFUND_ENTRY_BILL.getType())) {
            if (!CollectionUtils.isEmpty(afterSaleOrderVO.getExchangeGoodList()) && !afterSaleOrderVO.getSku().
                    equals(afterSaleOrderVO.getExchangeGoodList().get(0).getSku())) {
                ExchangeGoods exchangeGoods = afterSaleOrderVO.getExchangeGoodList().get(0);

                List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
                DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);
                AreaQueryRes areaQueryRes = deliveryFenceQueryFacade.queryAreaByAddress(deliveryPlanVO.getCity(), deliveryPlanVO.getArea());
                if (areaQueryRes == null || areaQueryRes.getAreaNo() == null) {
                    return AjaxResult.getErrorWithMsg("当前下单地址区域未开通配送服务");
                }
                AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(areaQueryRes.getAreaNo(), exchangeGoods.getSku());
                if (areaSku == null || !areaSku.getOnSale()) {
                    return AjaxResult.getErrorWithMsg("指定回收商品未在当前下单地址区域内上架");
                }
            }
        }

        //换货类型检查回收sku和下单sku是同一个情况下需要校验数量
        if (afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.EXCHANGE_GOODS.getType()) && !CollectionUtils.isEmpty(afterSaleOrderVO.getExchangeGoodList())
                && afterSaleOrderVO.getSku().equals(afterSaleOrderVO.getExchangeGoodList().get(0).getSku())) {
            Integer maxQuantity = afterSaleFacade.afterSaleManageCalculateQuantity(afterSaleOrderVO);
            if (maxQuantity < afterSaleOrderVO.getExchangeGoodList().get(0).getQuantity()) {
                return AjaxResult.getErrorWithMsg("回收商品数量超出可回收的商品数量，当前下单sku最多可回收数量为:" + maxQuantity +"件");
            }
        }

        logger.info("提交的订单："+JSON.toJSONString(afterSaleOrderVO));
        afterSaleFacade.afterSaleManageSave(afterSaleOrderVO);

        return AjaxResult.getOK();
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult handle(AfterSaleOrderVO afterSaleOrderVO) {
        //测试日志
        logger.info("审核AfterSaleOrderVO："+JSON.toJSONString(afterSaleOrderVO));
        AfterSaleOrderVO afterSaleOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        selectByAfterSaleOrder(afterSaleOrder);
        if (afterSaleOrder.getStatus() != 0) {
            return AjaxResult.getError("不正确的审核状态");
        }
        String refundType = afterSaleOrderVO.getRefundType();
        if (StringUtils.isEmpty(refundType)) {
            refundType = afterSaleOrder.getRefundType();
        }
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.select(afterSaleOrderVO.getAfterSaleOrderNo());
        if (CollectionUtils.isEmpty(afterSaleProofs)) {
            return AjaxResult.getError("售后单异常");
        }
        Orders orders = ordersMapper.selectByOrderyNo(afterSaleOrder.getOrderNo());
        BigDecimal totalPrice = orders.getTotalPrice();
        OrderRelation orderRelation =  ofcRelationService.selectByOrderNo(afterSaleOrder.getOrderNo());
        String selectOrderNo = afterSaleOrder.getOrderNo();
        if(!ObjectUtils.isEmpty(orderRelation)){
            selectOrderNo = orderRelation.getMasterOrderNo();
        }
        List<MerchantCouponVO> merchantCouponVOS = ordersCouponMapper.select(selectOrderNo);
        if (!CollectionUtils.isEmpty(merchantCouponVOS)) {
            for (MerchantCouponVO merchantCouponVO : merchantCouponVOS){
                Integer grouping = merchantCouponVO.getGrouping();
                if (Objects.equals(grouping, 1)) {
                    BigDecimal money = merchantCouponVO.getMoney();
                    totalPrice = totalPrice.add(money);
                }
            }
        }

        //POP订单校验定责比例
        if (Objects.equals(orders.getType(), OrderTypeEnum.POP.getId()) && StringUtils.isBlank(afterSaleOrderVO.getSnapshot())) {
            return AjaxResult.getError("POP订单定责比例不能为空！");
        }
        if (StringUtils.isBlank(afterSaleOrderVO.getHandleSecondaryRemark())) {
            return AjaxResult.getError("审核售后分类不能为空！");
        }
        Integer deliveryId = afterSaleOrder.getDeliveryId();

        //查询已提交的售后订单
        List<AfterSaleOrderVO> afterSaleOrders = selectByOrderNo(afterSaleOrder.getOrderNo(), deliveryId);
        //审核不通过不用做下面校验

        BigDecimal handleNum = BigDecimal.ZERO;
        Integer afterQuantity = 0;
        for (AfterSaleOrderVO check : afterSaleOrders) {
            if (check.getStatus() == 2 && Objects.equals(check.getDeliveryId(), deliveryId)&& !Arrays.asList(new Integer[]{11,12,13,14}).contains(check.getHandleType())) {
                afterQuantity = afterQuantity + check.getQuantity();
                handleNum = handleNum.add(check.getHandleNum());
            }
        }
        //省心送配送计划已到货售后，校验金额 , 将拦截单除去
        if (Objects.equals(orders.getType(), 1) && !Objects.equals(afterSaleOrderVO.getHandleType(), -1)&& !Arrays.asList(new Integer[]{11,12,13,14}).contains(afterSaleOrderVO.getHandleType())) {
            afterSaleOrder.setHandleNum(afterSaleOrderVO.getHandleNum());
            AjaxResult ajaxResult = timingCheckout(afterSaleOrder, afterQuantity, handleNum);
            if (!Objects.equals("SUCCESS", ajaxResult.getCode())) {
                return AjaxResult.getError("TOTAL_REFUND", ajaxResult.getMsg());
            }
        }

        //拒绝审核 不用校验金额
        if (handleNum.add(afterSaleOrderVO.getHandleNum()).compareTo(totalPrice) > 0 && !Objects.equals(afterSaleOrderVO.getHandleType(), -1)) {
            return AjaxResult.getError("TOTAL_REFUND", "退款金额异常");
        }
        afterSaleOrderVO.setOrderNo(afterSaleOrder.getOrderNo());
        afterSaleOrderVO.setSku(afterSaleOrder.getSku());
        afterSaleOrderVO.setHandler(getAdminName());

        //当售后单 审核失败时，发送私聊消息至对应 BD，若无BD 则无需发送飞书消息 -- 针对退货退款和退款类型
        try {
            sendFeiShuMsg(afterSaleOrderVO, afterSaleOrder);
        } catch (Exception e) {
            logger.warn("审核失败时，私聊消息至对应BD消息发送失败, cause:{}", e);
        }

        //商城提交审核
        afterSaleFacade.afterSaleHandle(afterSaleOrderVO);

        return AjaxResult.getOK();
    }


    private void handleBranch(AfterSaleOrderVO afterSaleOrderVO) {
        //修改售后记录
        AfterSaleOrder updateKeys = new AfterSaleOrder();
        //查询sku是否为代仓商品, 是售后可以为0元
        InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(afterSaleOrderVO.getSku());
        //-1拒绝，0返券，1补发，2退款',
        if (afterSaleOrderVO.getHandleType() == -1) {
            //记录
            logger.info("管理员：{}{}拒绝了{}售后申请", getAdminName(), getAdminId(), afterSaleOrderVO.getAfterSaleOrderNo());
            updateKeys.setStatus(AfterSaleOrderStatus.FAIL.getStatus());
            if ("凭证不符合要求,请重新上传照片".equals(afterSaleOrderVO.getHandleRemark())) {
                updateKeys.setStatus(AfterSaleOrderStatus.RE_COMMIT.getStatus());
            }
            //成功返券
        } else if (afterSaleOrderVO.getHandleType() == 0) {
            if ((afterSaleOrderVO.getHandleNum() == null || afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) <= 0) && Objects.equals(inventoryVO.getType(), 0)) {
                throw new DefaultServiceException("售后金额不能小于零");
            }
            logger.info("管理员：{}{}通过了{}售后申请", getAdminName(), getAdminId(), afterSaleOrderVO.getAfterSaleOrderNo());

            //修改售后记录
            updateKeys.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());
        }
        //退款
        else if (afterSaleOrderVO.getHandleType() == 2) {
            if ((afterSaleOrderVO.getHandleNum() == null || afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) <= 0) && Objects.equals(inventoryVO.getType(), 0)) {
                throw new DefaultServiceException("售后金额不能小于零");
            }
            logger.info("管理员：{}{}为{}发起了退款申请", getAdminName(), getAdminId(), afterSaleOrderVO.getAfterSaleOrderNo());
            //修改售后记录
            updateKeys.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());
            //查询历史退款记录
            OrderVO orderVO = ordersMapper.selectByOrderyNo(afterSaleOrderVO.getOrderNo());
            BigDecimal totalFen = orderVO.getTotalPrice().multiply(BigDecimal.valueOf(100));
            BigDecimal refundFen = afterSaleOrderVO.getHandleNum().multiply(BigDecimal.valueOf(100));

            List<Refund> refunds = refundMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
            String refundNo = afterSaleOrderVO.getOrderNo() + (refunds.size() + 1);
            Refund refund = new Refund(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getAfterSaleOrderNo(), refundNo, totalFen, refundFen);
            refund.setCouponId(afterSaleOrderVO.getCouponId());
            refundMapper.insertSelective(refund);
            //录入账单
        } else if (afterSaleOrderVO.getHandleType() == 3) {
            if ((afterSaleOrderVO.getHandleNum() == null || afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) <= 0) && Objects.equals(inventoryVO.getType(), 0)) {
                throw new DefaultServiceException("售后金额不能小于零");
            }
            updateKeys.setStatus(AfterSaleOrderStatus.IN_HAND.getStatus());
        } else {
            throw new DefaultServiceException("不正确的处理状态");
        }

        //只更改状态售后的
        updateKeys.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        updateKeys.setView(0);
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateKeys);
        //获取最后一个
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.select(afterSaleOrderVO.getAfterSaleOrderNo());
        //根据id 修改proof
        afterSaleOrderVO.setHandler(getAdminName());
        AfterSaleProof updateProof = conventAfterSaleProof(afterSaleOrderVO);
        updateProof.setStatus(updateKeys.getStatus());
        updateProof.setId(afterSaleProofs.get(afterSaleProofs.size() - 1).getId());
        updateProof.setAfterSaleType(afterSaleOrderVO.getAfterSaleType());
        updateProof.setRefundType(afterSaleOrderVO.getRefundType());
        //拒绝handler type不处理
        if (updateProof.getHandleType() == -1) {
            updateProof.setHandleType(null);
        }
        afterSaleProofMapper.updateById(updateProof);

        if ("凭证不符合要求,请重新上传照片".equals(afterSaleOrderVO.getHandleRemark())) {
            MQData mqData = new MQData();
            mqData.setType(MType.AFTER_SALE_ORDER_NO.name());
            mqData.setData(afterSaleOrderVO.getAfterSaleOrderNo());
            mqProducer.send(RocketMqMessageConstant.MANAGE_LIST,null,JSON.toJSONString(mqData));
        }
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult audit(String afterSaleOrderNo, int status, String auditRemark) throws InterruptedException {
        AfterSaleOrderVO afterSaleOrder = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        selectByAfterSaleOrder(afterSaleOrder);
        //处于待审核状态才可以进入下一步
        if (AfterSaleOrderStatus.IN_HAND.getStatus() != afterSaleOrder.getStatus()) {
            return AjaxResult.getError(ResultConstant.NOT_IN_HANDLE);
        }
        //获取最后一个
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.select(afterSaleOrderNo);
        //根据id 修改proof
        AfterSaleProof updateProof = new AfterSaleProof();
        updateProof.setAuditer(getAdminName());
        updateProof.setId(afterSaleProofs.get(afterSaleProofs.size() - 1).getId());

        logger.info("审批参数：{},{},{},{}",afterSaleOrder.getAfterSaleOrderNo(),status,auditRemark,getAdminName());
        afterSaleFacade.afterSaleAudit(afterSaleOrder.getAfterSaleOrderNo(),status,auditRemark,getAdminName());

        return AjaxResult.getOK();
    }


    /**
     * 售后单号:(mId +1111)反转后 + 商户售后次数
     *
     * @param mId
     * @return
     */
    private String createAfterSaleOrderNo(Long mId) {
        int afterSaleTimes = afterSaleOrderMapper.countByMId(mId);
        StringBuffer head = new StringBuffer(mId + 1111 + "");

        String mid = StringUtils.orderRandomNum();
        String tail = afterSaleTimes + 1 + "";
        while (tail.length() < 4) {
            tail = 0 + tail;
        }
        int monthValue = LocalDate.now().getMonthValue();
        String month = String.valueOf(monthValue);
        if (monthValue < 10) {
            month = "0" + month;
        }
        head.reverse().append(mid).append(month).append(tail);
        return head.toString();
    }

    @Override
    public AjaxResult timingAfterSaleOrder(String orderNo) {

        if (StringUtils.isEmpty(orderNo)) {
            return AjaxResult.getError();
        }
        Integer quantity = afterSaleProofMapper.selectTimingQuantity(orderNo);
        quantity = quantity == null ? 0 : quantity;

        return AjaxResult.getOK(quantity);
    }


    private Integer timingAfterSaleOrder(Integer deliveryId, String orderNo, String sku) {
        List<OrderItem> orderItems = orderItemMapper.selectList(orderNo, sku, 0);
        Integer totalQuantity = 0;
        if (deliveryId != null) {
            DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(deliveryId);
            totalQuantity = deliveryPlan.getQuantity();
        } else {
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orderNo);
            for (DeliveryPlanVO deliveryPlanVO : deliveryPlanVOS) {
                Integer quantity = deliveryPlanVO.getQuantity();
                totalQuantity = quantity == null ? totalQuantity : totalQuantity + quantity;
            }
            totalQuantity = orderItems.get(0).getAmount() - totalQuantity;
        }

        return totalQuantity;
    }


    /**
     * 取消售后
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult closeAfterSale(String afterSaleNo) {

        AfterSaleOrderVO afterSaleOrderVO = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleNo);
        selectByAfterSaleOrder(afterSaleOrderVO);
        AfterSaleDeliveryPath afterSaleDeliveryPath = afterSaleDeliveryPathMapper.selectPathByNo(afterSaleNo);
        if (Objects.equals(afterSaleOrderVO.getStatus(), 11)) {
            return AjaxResult.getErrorWithMsg("已取消，不可再关闭售后单");
        }
        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        if (orders == null) {
            return AjaxResult.getErrorWithMsg("订单不存在");
        }

        if (afterSaleDeliveryPath != null) {
            //关单时间判断
            LocalTime closeTime = ofcQueryFacade.queryCloseTime(afterSaleDeliveryPath.getConcatId(),
                    orders.getType().equals(OrderTypeEnum.POP.getId()) ? OfcOrderSourceEnum.POP_AFTER_SALE : OfcOrderSourceEnum.XM_AFTER_SALE);
            if (null == closeTime) {
                log.error("\n 关闭售后单失败，截单时间查询异常 >>> {} \n", afterSaleNo);
                return AjaxResult.getErrorWithMsg("售后单关单失败，截单时间查询异常");
            }
            // 售后单的可关闭时间 统一为 「配送日期减一天的截单时间」 （2025-05-12 修改）
            LocalDate deliveryDate = afterSaleDeliveryPath.getDeliveryTime().minusDays(1L);
            LocalDateTime closeDateTime = deliveryDate.atTime(closeTime);
            if(LocalDateTime.now().isAfter(closeDateTime)){
                log.info("关闭售后单失败，已超过截单时间 >>> {} >>> {}", closeDateTime, afterSaleNo);
                return AjaxResult.getErrorWithMsg(closeDateTime + "之后不可关闭当前售后单");
            }
        }
        logger.info("关闭凭证更新售后单号:" + afterSaleOrderVO.getAfterSaleOrderNo());
        //否者 就失败售后单
        AfterSaleOrder updateOrder = new AfterSaleOrder();
        updateOrder.setAfterSaleOrderNo(afterSaleNo);
        updateOrder.setCloser(getAdminName());
        updateOrder.setCloseTime(LocalDateTime.now());
        updateOrder.setStatus(11);
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateOrder);
        AfterSaleProof update = new AfterSaleProof();
        update.setAfterSaleOrderNo(afterSaleNo);
        update.setStatus(11);
        afterSaleProofMapper.updateByAfterSalePoof(update);
        //处理补发 退货退款 换货
        if (afterSaleDeliveryPath != null) {
            Integer deliveryPathId = afterSaleDeliveryPath.getId();
            //补发sku信息
            AfterSaleDeliveryDetail afterSaleDeliveryDetail = afterSaleDeliveryPathMapper.selectDetail(deliveryPathId);
            //返还冻结
            if (afterSaleDeliveryDetail != null) {

                String sku = afterSaleDeliveryDetail.getSku();
                Integer quantity = afterSaleDeliveryDetail.getQuantity();
                Integer outStoreNo = afterSaleDeliveryPath.getOutStoreNo();
                logger.info("取消售后信息 sku={},quantity={}", sku, quantity);
                /*Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
                //加虚拟
                areaStoreService.updateOnlineStockByStoreNo(true, quantity, sku, outStoreNo, SaleStockChangeTypeEnum.CANCEL_AFTER_ORDER, null, recordMap, NumberUtils.INTEGER_ZERO);
                //减冻结库存
                areaStoreService.updateLockStockByStoreNo(-quantity, sku, outStoreNo, SaleStockChangeTypeEnum.CANCEL_AFTER_ORDER, null, recordMap);
                quantityChangeRecordService.insertRecord(recordMap);*/

                //库存释放 新模型
                AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
                areaStoreUnLockReq.setContactId(afterSaleDeliveryPath.getConcatId());
                areaStoreUnLockReq.setOrderType(SaleStockChangeTypeEnum.CANCEL_AFTER_ORDER.getTypeName());
                areaStoreUnLockReq.setOrderNo(afterSaleNo);
                areaStoreUnLockReq.setIdempotentNo(afterSaleNo);
                areaStoreUnLockReq.setOperatorNo(afterSaleNo);
                areaStoreUnLockReq.setMerchantId(afterSaleOrderVO.getmId());
                areaStoreUnLockReq.setSource(TmsDistOrderTypeSourceEnum.getDistOrderAfterSaleSourceByOrderType(orders.getType()));
                areaStoreUnLockReq.setOperatorName(getAdminName());
                List<OrderUnLockSkuDetailReqDTO> orderReleaseSkuDetailReqDTOS = new ArrayList<>();
                OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
                orderUnLockSkuDetailReqDTO.setSkuCode(sku);
                orderUnLockSkuDetailReqDTO.setReleaseQuantity(quantity);
                orderReleaseSkuDetailReqDTOS.add(orderUnLockSkuDetailReqDTO);
                areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(orderReleaseSkuDetailReqDTOS);
                areaStoreFacade.storeUnLock(areaStoreUnLockReq);
            }
            //更新状态
            afterSaleDeliveryPathMapper.updateDeliveryPath(afterSaleNo, 0);
        }
        logger.info("关闭售后单操作者："+getAdminName()+"操作时间："+LocalDateTime.now());
        return AjaxResult.getOK();
    }


    private AjaxResult timingCheckout(AfterSaleOrderVO afterSaleOrder, int handleQuantity, BigDecimal handleNum) {

        Integer quantity = timingAfterSaleOrder(afterSaleOrder.getDeliveryId(), afterSaleOrder.getOrderNo(), afterSaleOrder.getSku());

        if (!Arrays.asList(new Integer[]{11,12}).contains(afterSaleOrder.getHandleType())){
            if (afterSaleOrder.getDeliveryId() == null && !Objects.equals(quantity, handleQuantity + afterSaleOrder.getQuantity()) ) {
                return AjaxResult.getError("TOTAL_REFUND", "当前未设置数量发生变更，请重新发起售后");
            }
        }


        List<OrderItem> orderItems = orderItemMapper.selectList(afterSaleOrder.getOrderNo(), afterSaleOrder.getSku(), 0);
        BigDecimal price = orderItems.get(0).getPrice();
        BigDecimal totalPrice = price.multiply(BigDecimal.valueOf(quantity));
        if (afterSaleOrder.getDeliveryId() != null && handleNum.add(afterSaleOrder.getHandleNum()).compareTo(totalPrice) > 0) {
            return AjaxResult.getError("TOTAL_REFUND", "超出可售后金额");
        }
        return AjaxResult.getOK();

    }

    /**
     * 查询订单售后信息
     */
    @Override
    public List<AfterSaleOrderVO> selectByOrderNo(String orderNo, Integer deliveryId) {
        AfterSaleOrder querySaleOrder = new AfterSaleOrder();
        querySaleOrder.setOrderNo(orderNo);
        querySaleOrder.setDeliveryId(deliveryId);
        //获取到订单sku所有的售后信息
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectByOrderNoNew(querySaleOrder);
        //获取到订单售后单详情信息
        List<AfterSaleProof> afterSaleProofs = afterSaleProofMapper.selectByOrderNo(orderNo, null);
        if (!CollectionUtils.isEmpty(afterSaleProofs) && !CollectionUtils.isEmpty(afterSaleOrderVOS)) {
            Map<String, List<AfterSaleProof>> saleProofMap = afterSaleProofs.stream().
                    collect(Collectors.groupingBy(AfterSaleProof::getAfterSaleOrderNo));
            //遍历售后单信息填充 数量信息
            afterSaleOrderVOS.stream().forEach(afterSaleOrderVO -> {
                List<AfterSaleProof> afterSaleProofList = saleProofMap.get(afterSaleOrderVO.getAfterSaleOrderNo());
                handleVO(afterSaleOrderVO, afterSaleProofList);
            });
        }
        return afterSaleOrderVOS;
    }
    private void handleVOV2(AfterSaleOrderVO afterSaleOrder, List<AfterSaleProof> result,DeliveryPlan deliveryPlan,Map<String, DistOrderStandardResp> distOrderDetailMap,Map<Integer, WarehouseLogisticsCenter> warehouseLogisticsCenterMap) {
        afterSaleOrder.setOrderSource(Objects.equals(afterSaleOrder.getOrderType(), OrderTypeEnum.POP.getId()) ?CommonStatus.YES.getCode() : CommonStatus.NO.getCode() );

        AfterSaleProof afterSaleProof;
        if (!CollectionUtils.isEmpty(result) && afterSaleOrder != null) {
            if (result.size() > 1) {
                List<AfterSaleProof> collect = result.stream().filter(x -> !Objects.equals(x.getStatus(), AfterSaleOrderStatus.RE_COMMIT.getStatus()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    afterSaleProof = collect.get(NumberUtils.INTEGER_ZERO);
                } else {
                    afterSaleProof = result.get(NumberUtils.INTEGER_ZERO);
                }
            } else {
                afterSaleProof = result.get(NumberUtils.INTEGER_ZERO);
            }
            afterSaleOrder.setAfterSaleMoney(afterSaleProof.getHandleNum ());
            afterSaleOrder.setRefundType(afterSaleProof.getRefundType());
            afterSaleOrder.setApplyer(afterSaleProof.getApplyer());
            afterSaleOrder.setRecoveryNum(afterSaleProof.getRecoveryNum());
            afterSaleOrder.setHandleType(afterSaleProof.getHandleType());
            afterSaleOrder.setQuantity(afterSaleProof.getQuantity());
            afterSaleOrder.setHandler(afterSaleProof.getHandler());
            afterSaleOrder.setProofPic(afterSaleProof.getProofPic());
            afterSaleOrder.setHandleRemark(afterSaleProof.getHandleRemark());
            afterSaleOrder.setExtraRemark(afterSaleProof.getExtraRemark());
            afterSaleOrder.setAfterSaleType(afterSaleProof.getAfterSaleType());
            afterSaleOrder.setAuditer(afterSaleProof.getAuditer());
            afterSaleOrder.setHandleNum(afterSaleProof.getHandleNum());
            afterSaleOrder.setStatus(afterSaleProof.getStatus());
            afterSaleOrder.setApplyRemark(afterSaleProof.getApplyRemark());
            afterSaleOrder.setUpdatetime(afterSaleProof.getUpdatetime());
            afterSaleOrder.setAuditeRemark(afterSaleProof.getAuditeRemark());
            afterSaleOrder.setProofVideo(afterSaleProof.getProofVideo());
            if (deliveryPlan == null){
                log.info("deliveryPlan is null,return; aftersaleno={}",afterSaleOrder.getAfterSaleOrderNo ());
                afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
                return;
            }
            if (deliveryPlan.getInterceptFlag() == 1 ){
                afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.INTERCEPT.getStatus());
            }else {
                DistOrderStandardResp distOrderDetail = distOrderDetailMap.get (String.valueOf (deliveryPlan.getContactId ()));
                if (distOrderDetail != null && Objects.nonNull(distOrderDetail.getStatus())){
                    afterSaleOrder.setOrderDeliveryStatus(TmsDistOrderStatusEnum.getDistOrderStatus(distOrderDetail.getStatus()));
                }else {
                    afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.TO_BE_WIRED.getStatus());
                }
            }

            //改成tms查询
            /*WarehouseLogisticsCenter wlc = warehouseLogisticsCenterMap.get (deliveryPlan.getOrderStoreNo ());
            if(wlc!=null) {
                //校验截单时间
                LocalDateTime orderTime = afterSaleOrder.getOrderTime ();
                String closeTime = wlc.getCloseTime ();
                LocalDateTime closeTimeParsed = LocalDateTime.parse (closeTime, DateTimeFormatter.ofPattern ("HH:mm:ss"));
                // 截单时间早于订单时间，订单时间加一天
                if (closeTimeParsed.toLocalTime ().isBefore (orderTime.toLocalTime ())) {
                    orderTime = orderTime.plusDays (1);
                }
                // 创建开始时间
                LocalDateTime startTime = LocalDateTime.parse (orderTime.toLocalDate () + " " + closeTime, DateTimeFormatter.ofPattern ("yyyy-MM-dd HH:mm:ss"));

                // 判断当前时间是否在开始时间之前
                if (LocalDateTime.now ().isBefore (startTime)) {
                    afterSaleOrder.setDeliveryStatus (DeliveryStatusEnum.NOT_YET.getStatus ());
                }
            }*/
        }
    }
    @Override
    public void handleVO(AfterSaleOrderVO afterSaleOrder, List<AfterSaleProof> result) {
        AfterSaleProof afterSaleProof;
        if (!CollectionUtils.isEmpty(result) && afterSaleOrder != null) {
            if (result.size() > 1) {
                List<AfterSaleProof> collect = result.stream().filter(x -> !Objects.equals(x.getStatus(), AfterSaleOrderStatus.RE_COMMIT.getStatus()))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    afterSaleProof = collect.get(NumberUtils.INTEGER_ZERO);
                } else {
                    afterSaleProof = result.get(NumberUtils.INTEGER_ZERO);
                }
            } else {
                afterSaleProof = result.get(NumberUtils.INTEGER_ZERO);
            }
            afterSaleOrder.setRefundType(afterSaleProof.getRefundType());
            afterSaleOrder.setApplyer(afterSaleProof.getApplyer());
            afterSaleOrder.setRecoveryNum(afterSaleProof.getRecoveryNum());
            afterSaleOrder.setHandleType(afterSaleProof.getHandleType());
            afterSaleOrder.setQuantity(afterSaleProof.getQuantity());
            afterSaleOrder.setHandler(afterSaleProof.getHandler());
            afterSaleOrder.setProofPic(afterSaleProof.getProofPic());
            afterSaleOrder.setHandleRemark(afterSaleProof.getHandleRemark());
            afterSaleOrder.setExtraRemark(afterSaleProof.getExtraRemark());
            afterSaleOrder.setAfterSaleType(afterSaleProof.getAfterSaleType());
            afterSaleOrder.setAuditer(afterSaleProof.getAuditer());
            afterSaleOrder.setHandleNum(afterSaleProof.getHandleNum());
            afterSaleOrder.setStatus(afterSaleProof.getStatus());
            afterSaleOrder.setApplyRemark(afterSaleProof.getApplyRemark());
            afterSaleOrder.setUpdatetime(afterSaleProof.getUpdatetime());
            afterSaleOrder.setAuditeRemark(afterSaleProof.getAuditeRemark());
            afterSaleOrder.setProofVideo(afterSaleProof.getProofVideo());
            //判断是否为省心送退款
            if (afterSaleOrder.getDeliveryId() != null){
                DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(afterSaleOrder.getDeliveryId());
                if (deliveryPlan == null){
                    return;
                }
                OrderVO orderVO = ordersMapper.selectByOrderyNo(deliveryPlan.getOrderNo());
                if (orderVO == null){
                    return;
                }
                if (deliveryPlan.getInterceptFlag() == 1 ){
                    afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.INTERCEPT.getStatus());
                }else {
                    DistOrderQueryStandardReq queryStandardReq = new DistOrderQueryStandardReq();
                    queryStandardReq.setOuterOrderId(deliveryPlan.getOrderNo());
                    if (Objects.equals(orderVO.getType(),OrderTypeEnum.TIMING.getId())) {
                        queryStandardReq.setSource(TmsDistOrderTypeSourceEnum.SAVE_MIND.getCode());
                    } else {
                        queryStandardReq.setSource(TmsDistOrderTypeSourceEnum.getDistOrderSourceByOrderType(orderVO.getType()));
                    }
                    queryStandardReq.setOuterContactId(String.valueOf(deliveryPlan.getContactId()));
                    queryStandardReq.setExpectBeginTime(deliveryPlan.getDeliveryTime().atTime(00,00,00,00));
                    DistOrderStandardResp distOrderDetail = tmsDistOrderQueryStandardFacade.queryDistOrderDetail(queryStandardReq);
                    //DeliveryPath deliveryPath = deliveryPathMapper.selectOne(deliveryPlan.getOrderStoreNo(),deliveryPlan.getDeliveryTime(),deliveryPlan.getContactId());
                    if (distOrderDetail != null && Objects.nonNull(distOrderDetail.getStatus())){
                        afterSaleOrder.setOrderDeliveryStatus(TmsDistOrderStatusEnum.getDistOrderStatus(distOrderDetail.getStatus()));
                    }else {
                        afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.TO_BE_WIRED.getStatus());
                    }
                }

                //校验截单时间 --改从tms查询
                /*WarehouseLogisticsCenter wlc = warehouseLogisticsCenterMapper.selectByStoreNo(deliveryPlan.getOrderStoreNo());
                Date orderTime = orderVO.getOrderTime();
                SimpleDateFormat hms = new SimpleDateFormat("HH:mm:ss");
                String closeTime = wlc.getCloseTime();
                String orderFormat = hms.format(orderTime);
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                SimpleDateFormat ds = new SimpleDateFormat("yyyy-MM-dd ");
                try {
                    Date orderHms = hms.parse(orderFormat);
                    Date closeHms = hms.parse(closeTime);
                    //如果超过截单时间，日期加一天
                    if(closeHms.compareTo(orderHms) <0){
                        Calendar   calendar = new GregorianCalendar();
                        calendar.setTime(orderTime);
                        calendar.add(Calendar.DATE,1); //把日期往后增加一天,整数  往后推,负数往前移动
                        orderTime=calendar.getTime();
                    }
                    Date startTime = df.parse(ds.format(orderTime) +wlc.getCloseTime());
                    Date date = new Date();
                    int i = date.compareTo(startTime);
                    if (i<0){
                        afterSaleOrder.setDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                }*/
            }else {
                //配置原订单配送状态
                List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNoIntercept(afterSaleOrder.getOrderNo());
                if(!CollectionUtils.isEmpty(deliveryPlanVOS)){
                    DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);
                    OrderVO orderVO = ordersMapper.selectByOrderyNo(deliveryPlanVO.getOrderNo());
                    if (orderVO == null){
                        return;
                    }
                    if (deliveryPlanVO.getInterceptFlag() == 1){
                        afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.INTERCEPT.getStatus());
                    }else {
                        DistOrderQueryStandardReq queryStandardReq = new DistOrderQueryStandardReq();
                        queryStandardReq.setOuterOrderId(afterSaleOrder.getOrderNo());
                        if (Objects.equals(orderVO.getType(),OrderTypeEnum.TIMING.getId())) {
                            queryStandardReq.setSource(TmsDistOrderTypeSourceEnum.SAVE_MIND.getCode());
                        } else {
                            queryStandardReq.setSource(TmsDistOrderTypeSourceEnum.getDistOrderSourceByOrderType(orderVO.getType()));
                        }
                        queryStandardReq.setOuterContactId(String.valueOf(deliveryPlanVO.getContactId()));
                        queryStandardReq.setExpectBeginTime(deliveryPlanVO.getDeliveryTime().atTime(00,00,00,00));
                        DistOrderStandardResp distOrderDetail = tmsDistOrderQueryStandardFacade.queryDistOrderDetail(queryStandardReq);
                        //DeliveryPath deliveryPath = deliveryPathMapper.selectByOrderNo(afterSaleOrder.getOrderNo());
                        if (distOrderDetail != null && Objects.nonNull(distOrderDetail.getStatus())){
                            afterSaleOrder.setOrderDeliveryStatus(TmsDistOrderStatusEnum.getDistOrderStatus(distOrderDetail.getStatus()));
                        }else {
                            afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.TO_BE_WIRED.getStatus());
                        }
                    }

                    //校验截单时间 -- 改从tms查询
                    /*WarehouseLogisticsCenter wlc = warehouseLogisticsCenterMapper.selectByStoreNo(deliveryPlanVO.getOrderStoreNo());
                    Date orderTime = orderVO.getOrderTime();
                    SimpleDateFormat hms = new SimpleDateFormat("HH:mm:ss");
                    String closeTime = wlc.getCloseTime();
                    String orderFormat = hms.format(orderTime);
                    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    SimpleDateFormat ds = new SimpleDateFormat("yyyy-MM-dd ");
                    try {
                        Date orderHms = hms.parse(orderFormat);
                        Date closeHms = hms.parse(closeTime);
                        //如果超过截单时间，日期加一天
                        if(closeHms.compareTo(orderHms) <0){
                            Calendar   calendar = new GregorianCalendar();
                            calendar.setTime(orderTime);
                            calendar.add(Calendar.DATE,1); //把日期往后增加一天,整数  往后推,负数往前移动
                            orderTime=calendar.getTime();
                        }
                        Date startTime = df.parse(ds.format(orderTime) +wlc.getCloseTime());
                        Date date = new Date();
                        int i = date.compareTo(startTime);
                        if (i<0){
                            afterSaleOrder.setDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
                        }
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }*/
                }else {
                    afterSaleOrder.setOrderDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
                }
            }
        }
    }

    @Override
    public AfterSaleOrderVO selectByAfterSaleOrder(AfterSaleOrderVO afterSaleOrder) {
        if (afterSaleOrder == null || StringUtils.isEmpty(afterSaleOrder.getAfterSaleOrderNo())) {
            return afterSaleOrder;
        }
        List<AfterSaleProof> afterSaleProofList = afterSaleProofMapper.select(afterSaleOrder.getAfterSaleOrderNo());
        handleVO(afterSaleOrder, afterSaleProofList);
        return afterSaleOrder;
    }

    @Override
    @Transactional
    public AjaxResult openAfterSaleOrder(String afterSaleOrderNo, String orderNo) {
        AfterSaleOrderVO afterSaleOrderVO = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        if (Objects.isNull(afterSaleOrderVO)) {
            return AjaxResult.getErrorWithMsg("未找到对应售后订单信息");
        }
        Integer status = afterSaleOrderVO.getStatus();
        Integer deliveryed = afterSaleOrderVO.getDeliveryed();
        Integer type = afterSaleOrderVO.getType();
        Long mId = afterSaleOrderVO.getmId();
        List<AfterSaleProof> afterSaleProofList = afterSaleProofMapper.select(afterSaleOrderNo);
        if (CollectionUtils.isEmpty(afterSaleProofList)) {
            return AjaxResult.getErrorWithMsg("未找到对应售后订单信息");
        }
        AfterSaleProof afterSaleProof = afterSaleProofList.get(afterSaleProofList.size() - NumberUtils.INTEGER_ONE);

        if (!Objects.equals(status, AfterSaleOrderStatus.SUCCESS.getStatus()) || !Objects.equals(deliveryed, NumberUtils.INTEGER_ONE)
                || !Objects.equals(afterSaleProof.getHandleType(), AfterSaleHandleType.COUPON.getType()) || !Objects.equals(type, 0)) {
            return AjaxResult.getErrorWithMsg("该售后单不支持重开");
        }
        Coupon coupon = couponMapper.selectByCode(Global.AFTER_SALE_COUPON_FRONT + afterSaleProof.getHandleNum());
        if (Objects.isNull(coupon)) {
            return AjaxResult.getErrorWithMsg("未找到对应券信息");
        }
        List<MerchantCoupon> merchantCoupons = merchantCouponMapper.selectByCouponId(mId, coupon.getId());
        if (CollectionUtils.isEmpty(merchantCoupons)) {
            return AjaxResult.getErrorWithMsg("售后券已使用，无法重开");
        }
        AfterSaleOrder updateSaleOrder = new AfterSaleOrder();
        updateSaleOrder.setAfterSaleOrderNo(afterSaleOrderNo);
        updateSaleOrder.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        afterSaleOrderMapper.updateByAfterSaleOrderNo(updateSaleOrder);
        AfterSaleProof updateSaleProof = new AfterSaleProof();
        updateSaleProof.setId(afterSaleProof.getId());
        updateSaleProof.setAfterSaleOrderNo(afterSaleOrderNo);
        updateSaleProof.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        afterSaleProofMapper.updateByAfterSalePoof(updateSaleProof);
        MerchantCoupon merchantCoupon = merchantCoupons.get(NumberUtils.INTEGER_ZERO);
        //此处不删除用户券，将券改成失效
        MerchantCoupon userCoupon = new MerchantCoupon();
        userCoupon.setId(merchantCoupon.getId());
        userCoupon.setVaildDate(DateUtils.localDateTimeToDate(LocalDateTime.now().minusMonths(1)));
        merchantCouponMapper.updateByPrimaryKeySelective(userCoupon);
        try {
            // 商户优惠券删除时,发放详情状态置为'已撤回'
            if (merchantCoupon.getSendId() != null) {
                marketCouponSendDetailMapper.updateStatusByMids(merchantCoupon.getSendId(), MarketCouponSendDetailStatusEnum.HAD_RECALL.getStatus(), Collections.singletonList(merchantCoupon.getmId()));
            }
        } catch (Exception e) {
            logger.error("发放记录详情状态更新异常");
        }
        return AjaxResult.getOK();
    }

    @Override
    public void autoAuditReplenishment(String type) {
        logger.info("AfterSaleOrderServiceImpl[]autoAuditReplenishment[]start[]type:{}", type);
        //查询售后信息
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectReplenishment();

        if (CollectionUtils.isEmpty(afterSaleOrderVOS)) {
            return;
        }

        for (AfterSaleOrderVO afterSaleOrderVO : afterSaleOrderVOS) {
            Long mId = afterSaleOrderVO.getmId();
            MerchantVO merchantVO = merchantMapper.selectMerchantByMid(mId);
            if (Objects.isNull(merchantVO)) {
                continue;
            }
            //根据类型过滤
            boolean isContinue = checkType(type, merchantVO);
            if (!isContinue) {
                continue;
            }

            //商城发起售后
            afterSaleOrderVO.setApplyer("系统自动发起");
            afterSaleOrderVO.setAuditer("系统自动发起");
            afterSaleOrderVO.setStatus(0);
            //商城提交审核
            afterSaleFacade.afterSaleHandle(afterSaleOrderVO);
        }
        return;
    }

    @Override
    public AjaxResult selectByAfterSale(String orderNo, String sku, Integer suitId) {
        boolean empty = checkAfterOrder(orderNo, sku, suitId);
        return AjaxResult.getOK(empty);
    }

    @Override
    public boolean checkAfterOrder(String orderNo, String sku, Integer suitId) {
        AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
        afterSaleOrder.setOrderNo(orderNo);
        afterSaleOrder.setSku(sku);
        afterSaleOrder.setSuitId(suitId);
        List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectReplenishmentByOrderNo(afterSaleOrder);
        return CollectionUtils.isEmpty(afterSaleOrderVOS);
    }

    /**
     * 根据类型执行截单信息
     */
    private boolean checkType(String type, MerchantVO merchantVO) {
        //单店或者非大客户提前截单类型
        if (Objects.equals(type, Global.SINGLE_STORE) && StringUtils.isEmpty(merchantVO.getCloseOrderTime())) {
            return Boolean.TRUE;
        }
        //大客户提前截单
        if (Objects.equals(type, Global.BIG_MERCHANT) && !StringUtils.isEmpty(merchantVO.getCloseOrderTime())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    @Override
    public AjaxResult checkStock(String orderNo, String sku, Integer deliveryId, Integer quantity) {
        //配送地址id
        Long contactId = getContactId(orderNo, deliveryId);
        if (ObjectUtils.isEmpty(contactId)) {
            return AjaxResult.getErrorWithMsg("未获取到订单配送计划");
        }
        Integer storeQuantity = queryStoreQuantity(sku, contactId);
        if (storeQuantity < quantity) {
            return AjaxResult.getErrorWithMsg("库存不足,下单失败");
        }
        return AjaxResult.getOK();
    }



    /**
     * 获取配送地址
     */
    public Long getContactId(String orderNo,Integer deliveryId){
        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orderNo);
        if (CollectionUtils.isEmpty(deliveryPlanVOS)) {
            return null;
        }
        Long contactId = deliveryPlanVOS.get(0).getContactId();
        //省心送
        if(deliveryId != null){
            DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(deliveryId);
            contactId = deliveryPlan.getContactId();
        }
        return contactId;
    }

    /**
     * 查询虚拟库存
     * @param sku
     * @param contactId
     * @return
     */
    private Integer queryStoreQuantity(String sku,Long contactId){

        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        Integer storeNo = contact.getStoreNo();
        AreaStore select = areaStoreMapper.selectByStoreNoAndSkuNew(storeNo, sku);
        Integer onlineQuantity = select.getOnlineQuantity();
        Integer reserveMaxQuantity = select.getReserveMaxQuantity();
        Integer reserveMinQuantity = select.getReserveMinQuantity();
        Integer reserveUseQuantity = select.getReserveUseQuantity();
        if(reserveMaxQuantity != null){
            Integer reserveQuantity = reserveMaxQuantity - reserveMinQuantity;
            onlineQuantity = reserveUseQuantity > reserveQuantity ? onlineQuantity - reserveMinQuantity : onlineQuantity - (reserveMaxQuantity - reserveUseQuantity);
        }
        return onlineQuantity;
    }


    @Override
    public AjaxResult deliveryStatus(String orderNo, Boolean isManage,Integer deliveryId) {
        DeliveryStatusQueryReq deliveryStatusQueryReq = new DeliveryStatusQueryReq();
        deliveryStatusQueryReq.setOrderNo(orderNo);
        deliveryStatusQueryReq.setIsManage(isManage);
        deliveryStatusQueryReq.setDeliveryId(deliveryId);
        ExecutableAfterSaleResp executableAfterSaleResp = afterSaleFacade.afterSaleDeliveryStatus(deliveryStatusQueryReq);
        return AjaxResult.getOK(executableAfterSaleResp);
    }

    @Override
    public AjaxResult getHandleType(String orderNo, String sku, Integer deliveryStatus, Boolean isManage) {
        HandleTypeQueryReq handleTypeQueryReq = new HandleTypeQueryReq();
        handleTypeQueryReq.setOrderNo(orderNo);
        handleTypeQueryReq.setIsManage(isManage);
        handleTypeQueryReq.setDeliveryed(deliveryStatus);
        handleTypeQueryReq.setSku(sku);
        ExecutableAfterSaleResp executableAfterSaleResp = afterSaleFacade.afterSaleGetHandleType(handleTypeQueryReq);
        return AjaxResult.getOK(executableAfterSaleResp);
    }

    @Override
    public AjaxResult getMaxQuantity(AfterSaleOrderVO afterSaleOrderVO) {
        logger.info("访问到了最大售后数量接口");
        Integer maxQuantity = afterSaleFacade.afterSaleManageCalculateQuantity(afterSaleOrderVO);
        return AjaxResult.getOK(maxQuantity);
    }

    @Override
    public AjaxResult getAfterSaleMoney(AfterSaleOrderVO afterSaleOrderVO) {
        logger.info("访问到了获取最大可售后金额接口");
        BigDecimal maxMoney = afterSaleFacade.afterSaleManageAfterSaleMoney(afterSaleOrderVO);
        return AjaxResult.getOK(maxMoney);
    }

    @Override
    public AjaxResult afterRefundShipping(String orderNo,Long mId) {
        //录入账单类型
        boolean billFlag = false;
        OrderVO orderVO = ordersMapper.selectOrderByOrderNo(orderNo);
        if (orderVO == null || orderVO.getType().equals(OrderTypeEnum.DIRECT.getId())){
            return AjaxResult.getOK();
        }
        BigDecimal deliveryFee = orderVO.getDeliveryFee();
        //查询运费券金额
        BigDecimal couponDeliveryFee = ordersMapper.selectCouponDeliveryFeeByOrderNo(orderNo);
        if (couponDeliveryFee == null || couponDeliveryFee.compareTo(BigDecimal.ZERO) == 0) {
            couponDeliveryFee = orderPreferentialMapper.selectDeliveryFeeAmountByOrderNo(orderNo);
        }
        if (orderVO.getDeliveryFee() == null){
            return AjaxResult.getOK();
        }
        if (couponDeliveryFee != null){
            deliveryFee = deliveryFee.subtract(couponDeliveryFee);
        }
        //没有运费不允许退
        if (deliveryFee.compareTo(BigDecimal.ZERO) <= 0){
            return AjaxResult.getOK();
        }
        //查询是否有对应退运费的操作
        Integer count = afterSaleOrderMapper.selectByDeliveryFeeCount(orderVO.getOrderNo());
        if (count > 0){
            return AjaxResult.getOK();
        }

        Merchant merchant = merchantMapper.selectByMId(mId);
        // 门店被删除之后这里不再支持退运费
        if (merchant == null) {
            return AjaxResult.getOK();
        }
        if (orderVO.getType().equals(OrderTypeEnum.HELP.getId()) || (merchant.getDirect() != null && merchant.getDirect().equals(1))) {
            billFlag = true;
        }
        if (billFlag){
            return AjaxResult.getOK(AfterSaleHandleType.RETURN_SHIPPING_BILL.getType());
        }

        return AjaxResult.getOK(AfterSaleHandleType.RETURN_SHIPPING.getType());
    }

    @Override
    public AjaxResult getAfterRate(AfterSaleOrderVO afterSaleOrderVO) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime beforeTime = now.minusDays(30);
        BigDecimal orderMoney = new BigDecimal(ordersMapper.selectMoneyByMId(afterSaleOrderVO.getmId(), now, beforeTime));
        BigDecimal afterMoney = afterSaleOrderMapper.selectMoneyByMid(afterSaleOrderVO.getmId(),now,beforeTime,null);
        BigDecimal notInStockAfterMoney = afterSaleOrderMapper.selectMoneyByMid(afterSaleOrderVO.getmId(),now,beforeTime,0);
        //创建实体类
        AfterSaleOrderVO vo = new AfterSaleOrderVO();
        vo.setOrderMoney(orderMoney.subtract(notInStockAfterMoney));
        vo.setAfterMoney(afterMoney.subtract(notInStockAfterMoney));
        if (vo.getOrderMoney().compareTo(BigDecimal.ZERO) == 0 || vo.getAfterMoney().compareTo(BigDecimal.ZERO) == 0){
            vo.setAfterRate(BigDecimal.ZERO);
        }else {
            BigDecimal divide = vo.getAfterMoney().divide(vo.getOrderMoney(),4,ROUND_HALF_UP);
            vo.setAfterRate(divide.multiply(new BigDecimal(100)));
        }
        return AjaxResult.getOK(vo);
    }

    @Override
    public void notifyRefundFailToDing(DtsModel dtsModel){
        logger.info("数据库监控退款："+JSON.toJSONString(dtsModel));
        Refund oldRefund = new Refund();
        //旧数据非3 新数据为3 则退款失败
        dtsModel.consumerOld(map ->{
            String stringStatus = map.get("status");
            if(StringUtils.isEmpty(stringStatus)){
                return;
            }
            Byte status = Byte.valueOf(stringStatus);
            oldRefund.setStatus(status);
        });
        Refund refund = new Refund();
        dtsModel.consumerData(map ->{
            Byte status = Byte.valueOf(map.get("status"));
            refund.setStatus(status);
            String orderNo = map.get("order_no");
            refund.setOrderNo(orderNo);
            refund.setErrCodeDes(map.get("err_code_des"));
            refund.setAfterSaleOrderNo(map.get("after_sale_order_no"));
        });
        //发起通知
        if (refund.getStatus()==RefundStatusEnum.FAIL.ordinal() && oldRefund.getStatus()!=RefundStatusEnum.FAIL.ordinal()){
            //查看发送群
            Config config = configMapper.selectOne(REFUND_FAIL_NOTIFY);
            //初始化map
            Map<String, String> md = new HashMap<>(2);
            //查看SKU名称
            md.put("title","【退款失败】你有一个退款单失败了");
            String sb ="退款失败订单如下\n"+
                    "> ##### 订单号："+refund.getOrderNo()+" \n" +
                    "> ##### 售后单号："+refund.getAfterSaleOrderNo()+" \n" +
                    "> ##### 退款失败原因："+refund.getErrCodeDes()+"\n" +
                    "> ##### 以上售后单请及时关注，可根据失败原因联系财务或者技术同学\n" ;
            md.put("text",sb);
            DingTalkRobotUtil.sendMsgAndAtAll(DingTalkRobotUtil.MARKDOWN,config.getValue(),() -> md);
        }

    }

    @Override
    public AjaxResult getSkuCategoryType(AfterSaleOrderVO afterSaleOrderVO) {
        InventoryVO inventoryVO = inventoryMapper.selectSkuType(afterSaleOrderVO.getSku());
        if (inventoryVO == null){
            return AjaxResult.getOK();
        }
        if (inventoryVO.getCategoryType() == null){
            return AjaxResult.getErrorWithMsg("该商品无相关类型");
        }
        if (inventoryVO.getCategoryType() == 4){
            return AjaxResult.getOK(true);
        }
        return AjaxResult.getOK(false);
    }

    @Override
    public CommonResult<Void> afterSaleProofExport(AfterSaleProofExportInput input){
        Integer adminId = Optional.ofNullable(getAdminId()).orElse(-1);
        String key = "admin_after_sale_proof_download:" + adminId;
        if (redisTemplate.hasKey(key)) {//NOSONAR
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"订单导出中，请勿重复操作");
        }
        if (input.getAfterSaleOrderStatus() == 1){
            return CommonResult.fail(ResultStatusEnum.NOT_FOUND,"拦截售后不可导出");
        }
        redisTemplate.opsForValue().set(key, "" + adminId, 10, TimeUnit.MINUTES);

        //文件名
        StringBuilder fileName = new StringBuilder();
        if (null != input.getStartTime()) {
            fileName.append(cn.hutool.core.date.DateUtil.format(input.getStartTime(),"yyyyMMdd"));
            fileName.append("-");
        }
        if (null != input.getEndTime()) {
            fileName.append(cn.hutool.core.date.DateUtil.format(input.getEndTime(),"yyyyMMdd"));
        }
        String finalName = "售后凭证" + fileName.toString() + ".html";

        DownloadCenterInitReq downloadCenterInitReq = new DownloadCenterInitReq();
        downloadCenterInitReq.setBizType(36);
        downloadCenterInitReq.setFileName(finalName);
        downloadCenterInitReq.setAdminId(Long.valueOf(adminId));
        downloadCenterInitReq.setFileExpiredDay(DownloadCenterEnum.FileExpiredDayEnum.THREE_DAY);
        downloadCenterInitReq.setBizId(UUID.fastUUID().toString());
        DubboResponse<DownloadCenterResp> downloadCenterRespDubboResponse = downloadCenterProvider.initRecord(downloadCenterInitReq);
        if (!downloadCenterRespDubboResponse.isSuccess() || Objects.isNull(downloadCenterRespDubboResponse.getData().getResId())) {
            throw new BizException("文件初始化失败!");
        }
        Long resId = downloadCenterRespDubboResponse.getData().getResId();

        ExecutorFactory.generateExcelExecutor.execute(() -> {
            try {
                logger.info("开始导出售后凭证:{}, finalName:{}, adminId:{}", input, finalName, adminId);
                asyncOrderTaskService.asyncGenerateProofExcel(input, resId, finalName);
                logger.info("售后凭证导出完成了！input:{}, resId:{}, finalName:{}", input, resId, finalName);
            } catch (Exception e) {
                logger.error("售后凭证导出失败了:{}", input, e);
                // 上传文件到下载中心
                DownloadCenterUploadReq downloadCenterUploadReq = new DownloadCenterUploadReq();
                downloadCenterUploadReq.setResId(resId);
                downloadCenterUploadReq.setFileName(finalName);
                downloadCenterUploadReq.setBizStatus(DownloadCenterEnum.BizStatusEnum.FAILED);
                DubboResponse<Boolean> uploadResult = downloadCenterProvider.uploadFile(downloadCenterUploadReq);
            } finally {
                redisTemplate.delete(key);
            }
        });
        return CommonResult.ok();
    }

    /**
    * @description 当售后单 审核失败时，发送私聊消息至对应 BD，若无BD 则无需发送飞书消息 -- 针对返券和退款以及退货退款类型
    * @params [afterSaleOrderVO, afterSaleOrder]
    * @return void
    * <AUTHOR>
    * @date  2024/11/7 15:34
    */
    private void sendFeiShuMsg(AfterSaleOrderVO afterSaleOrderVO, AfterSaleOrderVO afterSaleOrder) {
        if (Objects.equals(afterSaleOrderVO.getHandleType(), -1) && (afterSaleOrder.getHandleType().equals(AfterSaleHandleType.REFUND_GOODS.getType())
                || afterSaleOrder.getHandleType().equals(AfterSaleHandleType.REFUND.getType()) || afterSaleOrder.getHandleType().equals(AfterSaleHandleType.COUPON.getType()))) {
            Merchant merchant = merchantMapper.selectByMId(afterSaleOrder.getmId());
            FollowUpRelation followUpRelation = followUpRelationMapper.selectNotReassignByMid(afterSaleOrder.getmId().intValue());
            if (merchant != null && merchant.getSize().equals(MerchantSizeEnum.SINGGLE_STORE.getValue()) && followUpRelation != null) {
                String  title = "您有一个客户的售后单审核失败";
                StringBuilder text = new StringBuilder(title).append("\n");
                text.append("> ###### 服务类型：").append(AfterSaleHandleType.getAfterSaleHandleType(afterSaleOrder.getHandleType()).getDescription()).append("\n")
                        .append("> ###### 客户名称：").append(merchant.getMname()).append("\n")
                        .append("> ###### 订单号：").append(afterSaleOrder.getOrderNo()).append("\n")
                        .append("> ###### 售后单号：").append(afterSaleOrder.getAfterSaleOrderNo()).append("\n")
                        .append("> ###### 审核备注：").append(afterSaleOrderVO.getHandleRemark()).append("\n")
                        .append("> ###### 如有疑问请联系客服");
                DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.MARKDOWN.getType(), null, title, text.toString());
                DingTalkMsgReceiverIdBO fushuMsgBO = new DingTalkMsgReceiverIdBO(dingTalkMsgBO);
                fushuMsgBO.setReceiverIdList(Collections.singletonList(followUpRelation.getAdminId().longValue()));
                dingTalkMsgSender.sendMessageWithFeiShu(fushuMsgBO);
            }
        }
    }
}
