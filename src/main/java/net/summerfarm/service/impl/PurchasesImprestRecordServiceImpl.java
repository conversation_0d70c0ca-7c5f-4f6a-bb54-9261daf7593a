package net.summerfarm.service.impl;


import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiProcessinstanceCreateRequest;
import com.dingtalk.api.response.OapiProcessinstanceCreateResponse;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.DingTalkUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.Global;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.mapper.manage.PurchasesImprestMapper;
import net.summerfarm.mapper.manage.PurchasesImprestRecordMapper;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.PurchasesImprest;
import net.summerfarm.model.domain.PurchasesImprestRecord;
import net.summerfarm.service.AdminService;
import net.summerfarm.service.PurchasesImprestRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2020/3/6  16:43
 */
@Service
public class PurchasesImprestRecordServiceImpl  extends BaseService implements PurchasesImprestRecordService {

    @Resource
    PurchasesImprestRecordMapper purchasesImprestRecordMapper;
    @Resource
    PurchasesImprestMapper purchasesImprestMapper;
    @Resource
    ConfigMapper configMapper;
    @Resource
    AdminService adminService;

    @Override
    public AjaxResult insertPurchasesImprestRecord(PurchasesImprestRecord purchasesImprestRecord) {

        //查询是否存在没有审核的变更单
        List<PurchasesImprestRecord> records = purchasesImprestRecordMapper.queryRecord();
        if(!CollectionUtils.isEmpty(records)){
            return AjaxResult.getErrorWithParam("存在为审核的变更单");
        }
        PurchasesImprest purchasesImprest = purchasesImprestMapper.queryImprest();
        purchasesImprestRecord.setCreateTime(new Date());
        purchasesImprestRecord.setCreateId(getAdminId());
        purchasesImprestRecord.setCreateName(getAdminName());
        purchasesImprestRecord.setOriginAmount(purchasesImprest.getAmount());
        purchasesImprestRecord.setStatus(PurchasesImprestRecord.STATUS_NEW);
        purchasesImprestRecordMapper.createPurchasesRecord(purchasesImprestRecord);
        Integer id = purchasesImprestRecord.getId();

        createDingTalk(id,getAdminName(),purchasesImprest.getAmount(),purchasesImprestRecord.getNewAmount());
        return AjaxResult.getOK();

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public  AjaxResult purchasesImprestHandle(Integer id, Integer status) {
        //查询审核单状态
        PurchasesImprestRecord record = purchasesImprestRecordMapper.queryById(id);
        if(!Objects.equals(record.getStatus(),PurchasesImprestRecord.STATUS_NEW)){
            return AjaxResult.getErrorWithParam("该变更已被审核");
        }


        PurchasesImprestRecord updateRecord = new PurchasesImprestRecord();
        updateRecord.setStatus(status);
        updateRecord.setId(id);
        updateRecord.setExamineId(getAdminId());
        updateRecord.setExamineName(getAdminName());
        updateRecord.setExamineTime(new Date());

        //通过 修改备用金额
        if(Objects.equals(status,PurchasesImprestRecord.STATUS_PASS)){
            PurchasesImprest updateImprest = new PurchasesImprest();
            updateImprest.setAmount(record.getNewAmount());
            updateImprest.setUpdateTime(new Date());
            updateImprest.setId(record.getImprestId());
            purchasesImprestMapper.updateImprest(updateImprest);
        }
        purchasesImprestRecordMapper.updateRecord(updateRecord);

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectPurchasesImprest(int pageIndex, int pageSize,PurchasesImprestRecord purchasesImprestRecord){
        PageHelper.startPage(pageIndex, pageSize);
        List<PurchasesImprestRecord> records = purchasesImprestRecordMapper.queryByRecord(purchasesImprestRecord);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(records));
    }

    @Override
    public AjaxResult selectDetail(Integer id){
        return AjaxResult.getOK(purchasesImprestRecordMapper.queryById(id));
    }

    @Override
    public AjaxResult selectImprest() {
        return AjaxResult.getOK(purchasesImprestMapper.queryImprest());
    }


    private void createDingTalk(Integer id, String name, BigDecimal originAmount,BigDecimal newAmount){

        try {
            DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/create");
            OapiProcessinstanceCreateRequest request = new OapiProcessinstanceCreateRequest();
            request.setAgentId(Long.valueOf(Global.AGENT_ID));
            request.setProcessCode(Global.IMPREST_CODE);
            Config originator = configMapper.selectOne("originator");
            //默认为系统工作人
            request.setOriginatorUserId("5957642744985517013");
            if(originator != null && originator.getValue() != null){
                request.setOriginatorUserId(originator.getValue());
            }
            request.setDeptId(-1L);
            List<OapiProcessinstanceCreateRequest.FormComponentValueVo> formComponentValues = new ArrayList<OapiProcessinstanceCreateRequest.FormComponentValueVo>();
            OapiProcessinstanceCreateRequest.FormComponentValueVo vo0 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
            vo0.setName("备用金变更ID");
            vo0.setValue(String.valueOf(id));
            OapiProcessinstanceCreateRequest.FormComponentValueVo vo1 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
            vo1.setName("发起人");
            vo1.setValue( name + "提交了备用金金额修改,请审核");

            OapiProcessinstanceCreateRequest.FormComponentValueVo vo2 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
            vo2.setName("说明");
            vo2.setValue("将原有备用金"+originAmount.toString()+"元,修改为"+newAmount.toString()+"元");
            formComponentValues.add(vo0);
            formComponentValues.add(vo1);
            formComponentValues.add(vo2);
            request.setFormComponentValues(formComponentValues);
            String accessToken = DingTalkUtils.init().getToken();
            OapiProcessinstanceCreateResponse response = client.execute(request,accessToken);
            if(!response.isSuccess()){
                logger.info("创建安全库存释放审批失败 err={}",response.getMessage());
            }
        } catch (Exception e) {
            logger.info("创建安全库存释放审批失败 err={}",e.getMessage());
        }
        return;
    }

}
