package net.summerfarm.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.CategoryTypeEnum;
import net.summerfarm.enums.StandardTypeEnum;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.input.pms.ProveQueryPmsInput;
import net.summerfarm.model.param.BatchAreaQueryParam;
import net.summerfarm.model.param.BatchProveParam;
import net.summerfarm.model.vo.*;
import net.summerfarm.model.vo.pms.ProveBatchPmsVO;
import net.summerfarm.service.BatchProveService;
import net.summerfarm.warehouse.mapper.WarehouseTakeStandardMapper;
import net.summerfarm.warehouse.model.domain.WarehouseTakeStandard;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR> ct
 * create at:  2021/10/21  14:39
 */
@Service
public class BatchProveServiceImpl extends BaseService implements BatchProveService {
    @Resource
    AreaStoreMapper areaStoreMapper;
    @Resource
    InventoryMapper inventoryMapper;
    @Resource
    private WarehouseBatchProveRecordMapper warehouseBatchProveRecordMapper;
    @Resource
    private WarehouseTakeStandardMapper warehouseTakeStandardMapper;
    @Resource
    private StoreRecordMapper storeRecordMapper;

    private static final Logger logger = LoggerFactory.getLogger(BatchProveServiceImpl.class);


    @Override
    public AjaxResult selectList(Integer pageSize, Integer pageIndex, BatchProveParam param) {
        // 根据仓查询有批次的sku信息
        List<String> skuList = storeRecordMapper.selectSkuListByWarehouseNo(param.getWarehouseNo());
        if (CollectionUtils.isEmpty(skuList)) {
            return AjaxResult.getOK(PageInfoHelper.createPageInfo(Lists.newArrayList()));
        }

        BatchAreaQueryParam batchAreaQueryParam = new BatchAreaQueryParam();
        batchAreaQueryParam.setBatchSkuList(skuList);
        batchAreaQueryParam.setPdId(param.getPdId());
        batchAreaQueryParam.setWarehouseNo(param.getWarehouseNo());
        batchAreaQueryParam.setPdName(param.getPdName());
        batchAreaQueryParam.setSku(param.getSku());
        batchAreaQueryParam.setWeightNumIsNull(param.getWeightNumIsNull());
        batchAreaQueryParam.setVolumeIsNull(param.getVolumeIsNull());

        PageHelper.startPage(pageIndex, pageSize);
        List<AreaStoreVO> areaStoreVOS = areaStoreMapper.selectByBatchProveParam(batchAreaQueryParam);
        areaStoreVOS.forEach(item -> {
            item.setFirstCategory(Objects.equals(4, item.getFirstCategoryType()) ? "鲜果" : "非鲜果");
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(item.getWeight())) {
                String[] split = item.getWeight().split("/");
                item.setSpecification(split[0]);
            }
        });
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(areaStoreVOS));
    }

    @Override
    public AjaxResult selectBatchProveList(Integer pageSize, Integer pageIndex, BatchProveParam batchProve) {

        if (Objects.isNull(batchProve.getWarehouseNo()) || StringUtils.isEmpty(batchProve.getSku())) {
            AjaxResult.getErrorWithMsg("仓库编号和sku不能为空");
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<WarehouseBatchProveRecordVO> storeRecords = warehouseBatchProveRecordMapper.selectInBatchProve(batchProve);

        List<WarehouseBatchProveRecordVO> batchProveVos = warehouseBatchProveRecordMapper.selectByBatchAndSku(batchProve);

        storeRecords.forEach(storeRecord -> {
            List<WarehouseBatchProveRecordVO> proveRecordVos = batchProveVos.stream().filter(vo -> storeRecord.getBatch().equals(vo.getBatch()) && storeRecord.getProductionDate().equals(vo.getProductionDate())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(proveRecordVos)) {
                WarehouseBatchProveRecordVO recordVO = proveRecordVos.get(NumberUtils.INTEGER_ZERO);
                storeRecord.setCustomsDeclarationCertificate(recordVO.getCustomsDeclarationCertificate()).setQualityInspectionReport(recordVO.getQualityInspectionReport()).setSupervisionWarehouseCertificate(recordVO.getSupervisionWarehouseCertificate()).setNucleicAcidDetection(recordVO.getNucleicAcidDetection()).setDisinfectionCertificate(recordVO.getDisinfectionCertificate());

                WmsPesticideResidueReportVO residueReport = new WmsPesticideResidueReportVO();
                residueReport.setDetectionResult(recordVO.getDetectionResult());
                residueReport.setInhibitionRate(recordVO.getInhibitionRate());
                residueReport.setNumberSamples(recordVO.getNumberSamples());
                residueReport.setSamplingBase(recordVO.getSamplingBase());
                residueReport.setPesticideResiduePictures(recordVO.getPesticideResiduePictures());
                storeRecord.setResidueReport(residueReport);
                if (!StringUtils.isEmpty(recordVO.getPesticideResiduePictures()) && !Objects.isNull(recordVO.getDetectionResult())) {
                    storeRecord.setHaveRedisReport(NumberUtils.INTEGER_ONE);
                } else {
                    storeRecord.setHaveRedisReport(NumberUtils.INTEGER_ZERO);
                }
            }
        });

        return AjaxResult.getOK(PageInfoHelper.createPageInfo(storeRecords));
    }

    /**
     * @param sku
     * @param warehouseNo
     * @return 证件状态0无须 1未上传
     */
    @Override
    public Integer getProveStatus(String sku, Integer warehouseNo) {
        InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(sku);
        Integer standardType = getIStandardType(inventoryVO.getCategoryType(), inventoryVO.getIsDomestic());
        if (Objects.isNull(standardType)) {
            return NumberUtils.INTEGER_ZERO;
        }
        List<WarehouseTakeStandard> list = warehouseTakeStandardMapper.selectByCondition(inventoryVO.getStorageLocation(), standardType, warehouseNo);
        if (CollectionUtils.isEmpty(list)) {
            return NumberUtils.INTEGER_ZERO;
        }
        return NumberUtils.INTEGER_ONE;
    }

    @Override
    public List<Integer> selectBatchProveStandard(Integer warehouseNo, String sku) {
        InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(sku);
        Integer standardType = getIStandardType(inventoryVO.getCategoryType(), inventoryVO.getIsDomestic());
        List<WarehouseTakeStandard> list = warehouseTakeStandardMapper.selectByCondition(inventoryVO.getStorageLocation(), standardType, warehouseNo);
        return list.stream().map(WarehouseTakeStandard::getProveType).collect(Collectors.toList());
    }

    /** 批量获取sku配置数据 **/
    @Override
    public List<ProveBatchPmsVO> selectBatchProveStandardBatch(ProveQueryPmsInput input) {
        List<ProveBatchPmsVO> result = Lists.newArrayList();
        Integer warehouseNo = input.getWarehouseNo();
        for (String sku : input.getSkuList()) {
            result.add(ProveBatchPmsVO.builder()
                    .sku(sku)
                    .configList(selectBatchProveStandard(warehouseNo, sku))
                    .build());

        }
        return result;
    }

    /**
     * 查询进口鲜果类型
     *
     * @param categoryType
     * @param isDomestic
     * @return
     */
    private Integer getIStandardType(Integer categoryType, Integer isDomestic) {
        Integer standardType;
        // 鲜果
        if (Objects.equals(CategoryTypeEnum.FRUIT.getType(), categoryType)) {
            // 进口
            if (Objects.equals(NumberUtils.INTEGER_ZERO, isDomestic)) {
                standardType = StandardTypeEnum.NON_DOMESTIC_FRUITS.getId();
            } else {
                standardType = StandardTypeEnum.DOMESTIC_FRUITS.getId();
            }
            // 非鲜果
        } else {
            // 进口
            if (Objects.equals(NumberUtils.INTEGER_ZERO, isDomestic)) {
                standardType = StandardTypeEnum.NON_DOMESTIC_NOT_FRUITS.getId();
            } else {
                standardType = StandardTypeEnum.DOMESTIC_NOT_FRUITS.getId();
            }
        }
        return standardType;
    }
}
