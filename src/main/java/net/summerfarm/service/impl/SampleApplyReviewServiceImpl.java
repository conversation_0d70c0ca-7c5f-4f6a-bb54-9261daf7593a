package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.SaleStockChangeTypeEnum;
import net.summerfarm.facade.ofc.OfcQueryFacade;
import net.summerfarm.facade.tms.TmsDeliveryRuleFacade;
import net.summerfarm.facade.wms.AreaStoreFacade;
import net.summerfarm.facade.wms.dto.AreaStoreLockReq;
import net.summerfarm.facade.wms.dto.AreaStoreLockRes;
import net.summerfarm.facade.wms.dto.OrderLockSkuDetailReqDTO;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.Merchant;
import net.summerfarm.model.domain.SampleApply;
import net.summerfarm.model.domain.SampleApplyReview;
import net.summerfarm.model.domain.SampleSku;
import net.summerfarm.model.vo.SampleApplyReviewVO;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.summerfarm.service.*;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.wnc.client.enums.SourceEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class SampleApplyReviewServiceImpl implements SampleApplyReviewService {

    private static final Logger logger = LoggerFactory.getLogger(SampleApplyReviewServiceImpl.class);

    @Resource
    private SampleApplyReviewMapper sampleApplyReviewMapper;
    @Resource
    private BaseService baseService;
    @Resource
    private SampleApplyMapper sampleApplyMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private SampleSkuMapper sampleSkuMapper;

    @Resource
    private AreaService areaService;
    @Resource
    private AreaStoreService areaStoreService;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;

    @Resource
    private SampleApplyService sampleApplyService;
    @Resource
    private TmsDeliveryRuleFacade tmsDeliveryRuleFacade;
    @Resource
    DeliverPlanRemarkSnapshotService deliverPlanRemarkSnapshotService;

    @Resource
    private AreaStoreFacade areaStoreFacade;

    @Resource
    private OfcQueryFacade ofcQueryFacade;

    /**
     * 分页查询
     *
     * @param pageIndex
     * @param pageSize
     * @param sampleApply 样品申请
     * @return
     */
    @Override
    public AjaxResult selectSampleApplyReview(int pageIndex, int pageSize, SampleApply sampleApply) {
        // BD只能看到自己的申请
        if(baseService.isBD()){
            // 同时，不是销售主管、区域主管、超管
            if(!(baseService.isSaleSA() || baseService.isAreaSA() || baseService.isSA())){
                sampleApply.setBdId(baseService.getAdminId());
            }
        }
        PageHelper.startPage(pageIndex, pageSize);
        List<SampleApplyReviewVO> sampleApplyList = sampleApplyReviewMapper.selectByBdIds(sampleApply.getMName()
                ,sampleApply.getStatus(),sampleApply.getAreaNo(),sampleApply.getBdId());
        // 获取商品信息
        for (SampleApplyReviewVO sampleApplyReviewVO : sampleApplyList) {
            List<SampleSku> sampleSkus = sampleSkuMapper.selectBySampleId(sampleApplyReviewVO.getSampleId());
            sampleApplyReviewVO.setSampleSkuList(sampleSkus);
            if (StringUtils.isNotBlank(sampleApplyReviewVO.getBdName())) {
                sampleApplyReviewVO.setCreateName(sampleApplyReviewVO.getBdName());
            }
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(sampleApplyList));
    }

    /**
     * 查询样品申请详情
     * @param sampleApplyId 样品申请Id
     * @return
     */
    @Override
    public AjaxResult selectSampleApplyReviewVOById(Integer sampleApplyId) {
        if(ObjectUtil.isNull(sampleApplyId)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        SampleApplyReviewVO sampleApplyReviewVO = sampleApplyReviewMapper.selectSampleApplyReviewVOById(sampleApplyId);
        List<SampleSku> sampleSkus = sampleSkuMapper.selectBySampleId(sampleApplyReviewVO.getSampleId());
        sampleApplyReviewVO.setSampleSkuList(sampleSkus);
        deliverPlanRemarkSnapshotService.mergeSampleApplyReviewVOSnapshot(sampleApplyReviewVO);
        return AjaxResult.getOK(sampleApplyReviewVO);
    }

    /**
     * 样品申请审核
     * @param sampleApplyReview 样品申请审核信息
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult sampleApplyReview(SampleApplyReview sampleApplyReview){
        if(ObjectUtil.isNull(sampleApplyReview)){
            return AjaxResult.getError("无审核信息");
        }
        if(Objects.nonNull(sampleApplyReviewMapper.isReview(sampleApplyReview.getSampleId(),null))){
            return AjaxResult.getError("该样品申请已进行过审核操作,请稍后到样品审核列表查看");
        }
        // 更新数据
        sampleApplyReview.setReviewId(baseService.getAdminId());
        sampleApplyReview.setReviewName(baseService.getAdminName());
        sampleApplyReview.setAuditTime(new Date());
        // 不通过、储存不通过的信息，关闭样品申请
        if(Objects.equals(sampleApplyReview.getStatus(),1)){
            sampleApplyReviewMapper.insertSelective(sampleApplyReview);
            sampleApplyMapper.closeSampleApply(sampleApplyReview.getSampleId());
            return AjaxResult.getOK();
        }
        // 查询样品申请的所有信息
        SampleApplyReviewVO sampleApplyReviewVO = sampleApplyReviewMapper.selectSampleApplyReviewVOById(sampleApplyReview.getSampleId());
        // 判断是否正在切仓
        Merchant merchant = merchantMapper.selectByPrimaryKey(sampleApplyReviewVO.getMId());
        Integer areaNo = merchant.getAreaNo();
        if(areaService.inChange(areaNo,  null, merchant.getmId())){
            return AjaxResult.getError("切仓中，该功能暂时无法使用");
        }
        // 加入库存信息
        List<SampleSku> sampleSkus = sampleSkuMapper.selectBySampleId(sampleApplyReviewVO.getSampleId());
        sampleApplyReviewVO.setSampleSkuList(sampleSkus);
        // 检查库存信息
        StringBuffer checkSampleStock = sampleApplyService.checkSampleStock(sampleApplyReviewVO.getSampleSkuList(), sampleApplyReviewVO.getContactId());
        if(checkSampleStock.length() > 0){
            return AjaxResult.getError(ResultConstant.DEFAULT_FAILED,checkSampleStock.append("无可用库存").toString());
        }
        // 已经审核已通过，库存信息充足，储存样品申请审核信息
        sampleApplyReviewMapper.insertSelective(sampleApplyReview);
        // 将样品申请信息更新，将修改时间、申请状态，配送时间更新
        sampleApplyReviewVO.setUpdateTime(new Date());
        sampleApplyReviewVO.setStatus(0);
        /*LocalDate deliveryDate = tmsDeliveryRuleFacade.queryCloudDeliveryDate(DeliveryRuleQueryInput.builder()
                .source(SourceEnum.XM_SAMPLE_APPLY)
                .merchantId(merchant.getmId())
                .contactId((long) sampleApplyReviewVO.getContactId())
                .orderTime(LocalDateTime.now())
                .build());*/
        //改成调用ofc获取配送日期
        List<String> skuList = sampleSkus.stream().map(SampleSku::getSku).collect(Collectors.toList());
        DeliveryDateQueryResp dateQueryResp = ofcQueryFacade.queryDeliveryDate(LocalDateTime.now(),merchant.getmId(),Long.valueOf(sampleApplyReviewVO.getContactId()),null,null, OfcOrderSourceEnum.XM_SAMPLE_APPLY, skuList);
        sampleApplyReviewVO.setDeliveryTime(dateQueryResp.getDeliveryDate());
        SampleApply sa = new SampleApply();
        BeanUtils.copyProperties(sampleApplyReviewVO,sa);
        sampleApplyMapper.updateSampleApply(sa);

        //库存扣减 老模型替换
        /*List<SampleSku> sampleSkuList = sampleApplyReviewVO.getSampleSkuList();
        Integer storeNo = sampleApplyReviewVO.getStoreNo();
        // key:storeNo  value:stockTaskItem(出入库任务)
        HashMap<Integer, List<StockTaskItem>> map = new HashMap<>(16);
        HashMap<String, Integer> warehouseSkuMap = new HashMap<>(16);
        sampleSkuList.forEach(sampleSku -> {
            String sku = sampleSku.getSku();
            //获取库存使用仓
            WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, sku);
            //出入库任务
            StockTaskItem stockTaskItem = new StockTaskItem();
            stockTaskItem.setQuantity(sampleSku.getAmount());
            stockTaskItem.setSku(sampleSku.getSku());
            warehouseSkuMap.put(sku,mapping.getWarehouseNo());
            // 相同仓库的存在同一个key内
            map.merge(storeNo, Lists.newArrayList(stockTaskItem), ListUtils::union);
        });

        Map<String, QuantityChangeRecord> recordMap = new HashMap<>(16);
        map.forEach((x,taskItems) ->{
            taskItems.forEach(taskItem -> {
                //减虚拟库存
                areaStoreService.updateOnlineStockByStoreNo(true, -taskItem.getQuantity(), taskItem.getSku(), x,
                        SaleStockChangeTypeEnum.DEMO_OUT,null, recordMap, NumberUtils.INTEGER_ZERO);
                //加锁定库存
                areaStoreService.updateLockStockByStoreNo(taskItem.getQuantity(), taskItem.getSku(), x,
                        SaleStockChangeTypeEnum.DEMO_OUT,null, recordMap);
                Integer warehouseNo = warehouseSkuMap.get(x);
                areaStoreService.updateAreaStoreStatus(warehouseNo, taskItem.getSku());
            });
        });
        quantityChangeRecordService.insertRecord(recordMap);*/

        //库存扣减 改用新模型
        AreaStoreLockReq storeLockReq = new AreaStoreLockReq();
        storeLockReq.setOrderNo(String.valueOf(sampleApplyReview.getSampleId()));
        storeLockReq.setOperatorNo(String.valueOf(sampleApplyReview.getSampleId()));
        storeLockReq.setIdempotentNo(String.valueOf(sampleApplyReview.getSampleId()));
        storeLockReq.setOrderType(SaleStockChangeTypeEnum.DEMO_OUT.getTypeName());
        storeLockReq.setContactId(sampleApplyReviewVO.getContactId().longValue());
        storeLockReq.setMerchantId(sampleApplyReviewVO.getMId());
        storeLockReq.setSource(SourceEnum.XM_SAMPLE_APPLY.getValue());
        List<OrderLockSkuDetailReqDTO> orderLockSkuDetailReqDTOS = new ArrayList<>();
        sampleSkus.stream().forEach(e -> {
            OrderLockSkuDetailReqDTO orderLockSkuDetailReqDTO = new OrderLockSkuDetailReqDTO();
            orderLockSkuDetailReqDTO.setSkuCode(e.getSku());
            orderLockSkuDetailReqDTO.setOccupyQuantity(e.getAmount());
            orderLockSkuDetailReqDTOS.add(orderLockSkuDetailReqDTO);
        });
        storeLockReq.setOrderLockSkuDetailReqDTOS(orderLockSkuDetailReqDTOS);
        AreaStoreLockRes areaStoreLockRes = areaStoreFacade.storeLockWithOutException(storeLockReq);
        //库存不足封装不足sku明细到审核备注里面
        if (!ObjectUtils.isEmpty(areaStoreLockRes) && !CollectionUtils.isEmpty(areaStoreLockRes.getOrderNotLockSkuDetailResDTOList())){
            SampleApply notLockSampleApply = new SampleApply();
            notLockSampleApply.setSampleId(sampleApplyReview.getSampleId());
            StringBuffer sb = new StringBuffer();
            areaStoreLockRes.getOrderNotLockSkuDetailResDTOList().forEach(e -> {
                sb.append(e.getSkuCode()).append("库存不足，最大可用库存为:").append(e.getMaxOccupyQuantity()).append(",");
            });
            notLockSampleApply.setRemark(sb.toString());
            sampleApplyMapper.updateSampleApply(notLockSampleApply);
        }
        return AjaxResult.getOK();
    }

    /**
     * 定时任务，关闭三天前的样品申请
     */
    @Override
    public void closeSampleApplyReview() {
        logger.info("开始关闭三天前样品申请");
        LocalDateTime of = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime endTime  = of.minusDays(3);
        // 获取所有endTime未关闭的样品申请ids
        this.closeSampleApply(endTime);
        logger.info("已关闭三天前样品申请");
    }

    /**
     * 定时任务，月底关闭所有样品申请
     */
    @Override
    public void closeMonthSampleApplyReview() {
        logger.info("开始关闭样品申请");
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        this.closeSampleApply(endTime);
        logger.info("已关闭样品申请");
    }

    // 获取所有endTime未关闭的样品申请ids
    private void closeSampleApply(LocalDateTime endTime) {
        List<Integer> ids = sampleApplyMapper.querySituationListTime(endTime);
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        // 关闭申请
        SampleApplyReview sampleApplyReview = new SampleApplyReview();
        sampleApplyReview.setReviewName("系统审核");
        sampleApplyReview.setReviewRemark("超时关闭");
        sampleApplyReview.setStatus(1);
        sampleApplyReview.setAuditTime(new Date());
        for (Integer id : ids) {
            sampleApplyMapper.closeSampleApply(id);
            sampleApplyReview.setSampleId(id);
            sampleApplyReviewMapper.insertSelective(sampleApplyReview);
        }
    }
}
