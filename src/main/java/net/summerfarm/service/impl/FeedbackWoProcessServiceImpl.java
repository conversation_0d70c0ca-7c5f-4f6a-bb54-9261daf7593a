package net.summerfarm.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.FeedbackWoStatusEnum;
import net.summerfarm.enums.FeedbackWoTypeEnum;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.mapper.manage.FeedbackWoMapper;
import net.summerfarm.mapper.manage.FeedbackWoProcessMapper;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.FeedbackWo;
import net.summerfarm.model.vo.FeedbackWoProcessVO;
import net.summerfarm.model.vo.FeedbackWoVO;
import net.summerfarm.service.FeedbackWoProcessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 反馈工单处理记录
 * @Date: 2020/12/4 17:13
 * @Author: <EMAIL>
 */
@Service
public class FeedbackWoProcessServiceImpl  extends BaseService implements FeedbackWoProcessService  {

    @Resource
    FeedbackWoProcessMapper feedbackWoProcessMapper;

    @Resource
    FeedbackWoMapper feedbackWoMapper;

    @Resource
    ConfigMapper configMapper;

    private static final Logger logger = LoggerFactory.getLogger(FeedbackWoProcessServiceImpl.class);


    @Override
    public AjaxResult feedbackWoProcessList(Long woId) {

        if(Objects.isNull(woId)){
            throw new DefaultServiceException("查询的工单号不为null！");
        }

        List<FeedbackWoProcessVO> feedbackWoProcessList = feedbackWoProcessMapper.selectByWoId(woId);
        return AjaxResult.getOK(feedbackWoProcessList);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult save(FeedbackWoProcessVO insertContent) {
        Long woId = insertContent.getWoId();
        FeedbackWoVO query = new FeedbackWoVO();
        query.setId(woId);
        List<FeedbackWoVO> feedbackWoVOList = feedbackWoMapper.selectBySelectKeys(query);
        FeedbackWoVO feedbackWo = feedbackWoVOList.get(0);

        if(Objects.isNull(feedbackWo)){
            throw new DefaultServiceException("对应的工单不存在！");
        }

        if(Objects.deepEquals(feedbackWo.getWoStatus(),FeedbackWoStatusEnum.SETTLED.getStatus())){
            throw new DefaultServiceException("已解决了反馈不可添加处理回复！");
        }

        //未完结的存入工单记录则要求填入的内容不为空
        if(!Objects.deepEquals(insertContent.getFinished(),1) && StringUtils.isBlank(insertContent.getContent())){
            throw new DefaultServiceException("回复或反馈的处理内容不能为空！");
        }

        FeedbackWo updateContent = new FeedbackWo();
        updateContent.setId(woId);
        Integer creatorId = getAdminId();
        insertContent.setCreatorId(creatorId.longValue());


        //若是工单状态为待解决的，则第一次保存则需要改"为解决中"
        if(Objects.deepEquals(feedbackWo.getWoStatus(),FeedbackWoStatusEnum.UNSETTLED.getStatus())){
            updateContent.setWoStatus(FeedbackWoStatusEnum.SETTLING.getStatus());
        }

        //若finish = 1,则更新工单为已解决，若是大客户则把插入的内容修改为"已解决"
        if(Objects.deepEquals(insertContent.getFinished(),FeedbackWoProcessVO.FINISHED)){
           updateContent.setWoStatus(FeedbackWoStatusEnum.SETTLED.getStatus());
            if(Objects.deepEquals(feedbackWo.getWoType(), FeedbackWoTypeEnum.KA_TYPE.getWoType())){
                //还要校验点击已解决的人是否为发起者
                if(!Objects.deepEquals(feedbackWo.getCreatorId(),getAdminId().longValue())){
                    return AjaxResult.getError(ResultConstant.OPERATOR_IS_NOT_CREATOR,"只有发起问题者可以确认已解决哦");
                }
                insertContent.setContent("已解决");
            }
        }

        feedbackWoProcessMapper.insertSelective(insertContent);
        logger.info("{}跟进了某个工单id{}:是否结束标识{}，填入内容为{}",getAdminName(),insertContent.getWoId(),insertContent.getFinished(),insertContent.getContent());

        if(Objects.nonNull(updateContent.getWoStatus())){
            feedbackWoMapper.updateByPrimaryKeySelective(updateContent);
            logger.info("{}更新了单号{}的状态为{}",getAdminName(),updateContent.getId(),updateContent.getWoStatus());
        }
        //todo:工单通知废弃 ,有需要再加
        //当为大客户时查询对应的工单的条目数，并钉钉消息提醒
//        if(Objects.deepEquals(feedbackWo.getWoType(), FeedbackWoTypeEnum.KA_TYPE.getWoType())){
//            List<String> contentArray = new ArrayList<>(2);
//            //按时间顺序查询出来只取两条内容
//            List<FeedbackWoProcessVO> feedbackWoProcessList = feedbackWoProcessMapper.selectByWoId(woId);
//            //当只有一条时，要显示问题描述，多条时只显示最近的两条
//            if (feedbackWoProcessList.size() == 1){
//                contentArray.add(0, feedbackWoProcessList.get(0).getContent());
//                contentArray.add(1,feedbackWo.getDescription());
//            }else if (feedbackWoProcessList.size() > 1){
//                 contentArray.add(0,feedbackWoProcessList.get(0).getContent());
//                 contentArray.add(1,feedbackWoProcessList.get(1).getContent());
//            }else{
//                throw new DefaultServiceException("反馈历史处理数据异常！");
//            }
//            //发钉钉消息：
//            Map<String, String> md = new HashMap<>(2);
//            Config reserveMinRobotUrl = configMapper.selectOne("reserveMinRobotUrl");
//            String title = feedbackWo.getCreatorName() + "发起的" + feedbackWo.getNameRemakes() + "的问题反馈，" + getAdminName() + "已跟进";
//
//            md.put("title", title);
//            String sb =
//                    title + "\n" +
//                    "> ##### 详情说明: \n" +
//                    "> ##### 本次内容：" + contentArray.get(0) + "\n" +
//                    "> ##### 上次内容：" + contentArray.get(1) + "\n" ;
//            md.put("text", sb);
//            DingTalkRobotUtil.sendMsgAndAtAll(DingTalkRobotUtil.MARKDOWN, reserveMinRobotUrl.getValue(), () -> md);
//        }


        return AjaxResult.getOK();
    }
}


