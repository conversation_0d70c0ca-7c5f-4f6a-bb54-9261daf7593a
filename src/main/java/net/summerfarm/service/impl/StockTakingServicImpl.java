package net.summerfarm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.constant.dingding.ProcessInstanceBizTypeEnum;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.BeanCopyUtil;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.MathUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.dingding.bo.DingdingFormBO;
import net.summerfarm.dingding.bo.ProcessCreateResultBO;
import net.summerfarm.dingding.bo.ProcessInstanceCreateBO;
import net.summerfarm.dingding.exception.DingdingProcessException;
import net.summerfarm.dingding.service.DingdingProcessInstanceService;
import net.summerfarm.enums.*;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.mapper.WmsDamageStockTaskMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.DTO.MailWorkBookDTO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.service.*;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jetbrains.annotations.Nullable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.contexts.Global.*;

/**
 * Created by wjd on 2017/9/27.
 */
@Service
public class StockTakingServicImpl extends BaseService implements StockTakingService, StockTaskStrategy {
    @Resource
    private StockTakingMapper stockTakingMapper;

    @Resource
    private StockTakingDetailMapper stockTakingDetailMapper;

    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Lazy
    @Resource
    private AreaStoreService areaStoreService;

    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Lazy
    @Resource
    private MsgAdminService msgAdminService;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private StockTakingListMapper stockTakingListMapper;
    @Resource
    private StockTakingListDetailMapper stockTakingListDetailMapper;
    @Resource
    private StockTakingItemMapper stockTakingItemMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private StockTaskMapper stockTaskMapper;
    @Lazy
    @Resource
    private PurchasesConfigService purchasesConfigService;
    @Lazy
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    private WMSBuilderService wmsBuilderService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    DingTalkService dingTalkService;
    @Resource
    private SkuBatchCodeService skuBatchCodeService;
    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    private DingdingProcessInstanceService dingdingProcessInstanceService;
    @Resource
    private WmsDamageStockTaskMapper wmsDamageStockTaskMapper;
    @Resource
    private WarehouseStockExtService warehouseStockExtService;
    @Resource
    private AdminService adminService;

//    @Override
//    public AjaxResult select(int pageIndex, int pageSize, StockTaking stockTaking) {
//        PageHelper.startPage(pageIndex, pageSize);
//        List<StockTaking> list = stockTakingMapper.select(stockTaking);
//        return AjaxResult.getOK(new PageInfo<>(list));
//    }

    @Override
    public AjaxResult stockTakingDownload(Integer areaNo, HttpServletResponse response) {
        if (areaNo == null) {
            return AjaxResult.getErrorWithMsg("请先选择仓库编号");
        }
        Area area = areaMapper.selectByAreaNo(areaNo);
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet("仓库盘点数据");
        sheet.setColumnWidth(0, 3500);
        sheet.setColumnWidth(1, 3500);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 5000);
        sheet.setColumnWidth(4, 4000);
        sheet.setColumnWidth(5, 2000);
        sheet.setColumnWidth(6, 2000);


        Row firstRow = sheet.createRow(0);
        firstRow.createCell(0).setCellValue(area.getAreaName());
        firstRow.createCell(1).setCellValue(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日HH时mm分ss秒")));

        Row title = sheet.createRow(1);
        title.createCell(0).setCellValue("商品名称");
        title.createCell(1).setCellValue("商品编码");
        title.createCell(2).setCellValue("规格");
        title.createCell(3).setCellValue("采购批次");
        title.createCell(4).setCellValue("保质期");
        title.createCell(5).setCellValue("仓库");
        title.createCell(6).setCellValue("批次库存");

        List<StockTakingDetail> detailList = stockTakingDetailMapper.selectAll(areaNo);
        if (!CollectionUtils.isEmpty(detailList)) {

            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            int rowIndex = 2;//当前行号
            Map<String, List<StockTakingDetail>> detailMap = detailList.stream().collect(Collectors.groupingBy(StockTakingDetail::getSkuName)); //根据商品名称分组
            Set<String> keySet = detailMap.keySet();
            for (String key : keySet) {
                List<StockTakingDetail> details = detailMap.get(key);
                if (details.size() > 1) {
                    CellRangeAddress cellRangeAddress = new CellRangeAddress(rowIndex, rowIndex + details.size() - 1, 0, 0);
                    sheet.addMergedRegion(cellRangeAddress);
                }
                for (StockTakingDetail detail : details) {
                    Row row = sheet.createRow(rowIndex);
                    Cell cell1 = row.createCell(0);
                    cell1.setCellStyle(cellStyle);
                    cell1.setCellValue(detail.getSkuName());
                    row.createCell(1).setCellValue(detail.getSku());
                    row.createCell(2).setCellValue(detail.getWeight());
                    row.createCell(3).setCellValue(detail.getBatch());
                    row.createCell(4).setCellValue(detail.getQualityDate() == null ? "无" : detail.getQualityDate().format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT)));
                    row.createCell(5).setCellValue(detail.getAreaQuantity());
                    row.createCell(6).setCellValue(detail.getQuantity());
                    rowIndex++;
                }
            }
        }
        try {
            ExcelUtils.outputExcel(workbook, "库存盘点数据.xls", response);
        } catch (IOException e) {
            throw new DefaultServiceException("数据导出异常");
        }
        return null;
    }


    @Override
    public AjaxResult selectOneSkuList(int pageIndex, int pageSize, StockTakingDetail detail) {
        PageHelper.startPage(pageIndex, pageSize);
        List<StockTakingDetail> list = stockTakingDetailMapper.select(detail);
        StockTaking stockTaking = stockTakingMapper.selectById(detail.getTakingId());
        for (StockTakingDetail ss : list) {
            if (ss.getBatch() != null) {
                AreaStore areaStore = areaStoreMapper.selectOne(new AreaStore(stockTaking.getAreaNo(), ss.getSku()));
                ss.setAreaQuantity(areaStore.getQuantity());
                StoreRecord select = new StoreRecord();
                select.setBatch(ss.getBatch());
                select.setSku(ss.getSku());
                select.setQualityDate(ss.getQualityDate());
                select.setAreaNo(stockTaking.getAreaNo());
                StoreRecord result = storeRecordMapper.selectOne(select);
                ss.setQuantity(result.getStoreQuantity());
            }

        }

        return AjaxResult.getOK(new PageInfo<>(list));
    }

    @Override
    public AjaxResult select(int pageIndex, int pageSize, StockTakingVO stockTakingVO) {
        PageHelper.startPage(pageIndex, pageSize);
        List<StockTakingVO> stockTakingVOS = stockTakingMapper.select(stockTakingVO);
        stockTakingVOS.forEach(vo -> {
            String warehouseName =  Global.warehouseMap.getOrDefault(vo.getAreaNo(),"");
            vo.setWarehouseName(warehouseName);
        });
        return AjaxResult.getOK(new PageInfo<>(stockTakingVOS));
    }

    @Override
    public AjaxResult selectStockTakingDetail(Integer id, String pdName, String sku, Integer state, Boolean diff) {
        List<StockTakingItemVO> list = new ArrayList<>();
        //未盘点sku
        StockTaking stockTaking = stockTakingMapper.selectById(id);
        if (state == null || state == 0) {
            List<StockTakingItemVO> unTakingItems = getTakingItemVO(id,pdName,sku,stockTaking.getAreaNo());
            if (!CollectionUtils.isEmpty(unTakingItems)) {
                list.addAll(unTakingItems);
            }
        }
        //已盘点sku
        if (state == null || state == 1) {
            List<StockTakingItemVO> takingItems = stockTakingItemMapper.selectTakingItem(id, pdName, sku, diff);
            if (!CollectionUtils.isEmpty(takingItems)) {
                list.addAll(takingItems);
                //todo
            }
        }
        return AjaxResult.getOK(list);
    }

    @Override
    public AjaxResult selectList(int pageIndex, int pageSize, StockTakingListVO stockTakingListVO) {
        PageHelper.startPage(pageIndex, pageSize);
        List<StockTakingListVO> stockTakingListVOS = stockTakingListMapper.select(stockTakingListVO);
        return AjaxResult.getOK(new PageInfo<>(stockTakingListVOS));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult stockTakingListCreate(StockTakingVO stockTakingVO) {
        List<StockTakingItem> items = stockTakingVO.getStockTakingItems();
        if (CollectionUtils.isEmpty(items)) {
            throw new DefaultServiceException("请先选择要盘点的sku");
        }
        StockTakingList insert = new StockTakingList();
        insert.setTakingId(stockTakingVO.getId());
        insert.setAdminId(getAdminId());
        insert.setStatus(0);
        insert.setAddtime(LocalDateTime.now());
        insert.setUpdatetime(LocalDateTime.now());
        stockTakingListMapper.insert(insert);

        List<StockTakingListDetail> insertBatch = new ArrayList<>();
        for (StockTakingItem item : items) {
            StockTakingItem record = stockTakingItemMapper.selectByPrimaryKey(item.getId());
            if (record.getState() != 0) {
                throw new DefaultServiceException("sku:" + item.getSku() + "在盘点中");
            }

            AreaStore selectKey = new AreaStore(stockTakingVO.getAreaNo(),item.getSku());
            AreaStore areaStore = areaStoreMapper.selectOne(selectKey);
            WarehouseStockExt warehouseStockExt = new WarehouseStockExt( stockTakingVO.getAreaNo(),item.getSku());
            WarehouseStockExt  stockExt = warehouseStockExtService.selectWarehouseStockExt(warehouseStockExt);
            int damageNum = wmsDamageStockTaskMapper.selectByCondition(item.getSku(),stockTakingVO.getAreaNo());
            if (damageNum > 0) {
                throw new DefaultServiceException("sku:" + item.getSku() + "在货损出库中");
            }
            if (Objects.equals(stockExt.getStatus(),AreaStoreStatusEnum.OUT_IN.ordinal())) {
                throw new DefaultServiceException("sku:" + item.getSku() + "在出入库中");
            } else if (Objects.equals(stockExt.getStatus(),AreaStoreStatusEnum.CHECK_INVENTORY.ordinal())) {
                throw new DefaultServiceException("sku:" + item.getSku() + "在盘点中");
            }

            //修改当前sku的盘点状态
            StockTakingItem update = new StockTakingItem();
            update.setId(item.getId());
            update.setState(1);
            update.setStoreQuantity(areaStore.getQuantity());
            stockTakingItemMapper.updateByPrimary(update);

            StoreRecordVO select = new StoreRecordVO();
            select.setSku(item.getSku());
            select.setAreaNo(stockTakingVO.getAreaNo());
            select.setStoreQuantityMin(1);
            List<StoreRecord> storeRecords = storeRecordMapper.selectLasted(select);
            InventoryVO inventoryVO = inventoryMapper.selectSkuType(item.getSku());
            //生成信息
            if (!CollectionUtils.isEmpty(storeRecords)) {
                //嘉兴仓特殊处理
                for (StoreRecord storeRecord : storeRecords) {
                    StockTakingListDetail detail = new StockTakingListDetail();
                    detail.setStockTakingListId(insert.getId());
                    detail.setSku(inventoryVO.getSku());
                    detail.setWeight(inventoryVO.getWeight());
                    detail.setPdName(inventoryVO.getPdName());
                    detail.setQuantity(storeRecord.getStoreQuantity());
                    detail.setQualityDate(storeRecord.getQualityDate());
                    detail.setBatch(storeRecord.getBatch());
                    detail.setProductionDate(storeRecord.getProductionDate());
                    insertBatch.add(detail);
                }
            } else { //没有剩余库存大于0的批次，默认选第一条批次

                select.setStoreQuantityMin(null);
                StoreRecord storeRecord = storeRecordMapper.selectLasted(select).get(0);
                StockTakingListDetail detail = new StockTakingListDetail();
                detail.setStockTakingListId(insert.getId());
                detail.setSku(inventoryVO.getSku());
                detail.setWeight(inventoryVO.getWeight());
                detail.setPdName(inventoryVO.getPdName());
                detail.setQuantity(storeRecord.getStoreQuantity());
                detail.setQualityDate(storeRecord.getQualityDate());
                detail.setBatch(storeRecord.getBatch());
                detail.setProductionDate(storeRecord.getProductionDate());
                insertBatch.add(detail);
            }
        }
        if (!CollectionUtils.isEmpty(insertBatch)) {
            stockTakingListDetailMapper.insertBatch(insertBatch);
        }
        for (StockTakingItem item : items) { //修改sku状态
            areaStoreService.updateAreaStoreStatus(stockTakingVO.getAreaNo(), item.getSku());
        }
        StockTask stockTask = stockTaskMapper.selectbyTerm(stockTakingVO.getId());
        if (stockTask != null) {
            return AjaxResult.getOK(stockTask.getId());
        }
        return AjaxResult.getOK(stockTakingVO.getId());
    }

    @Override
    public AjaxResult selectListDetail(Integer id) {
        Map result = new HashMap();
        StockTakingListVO stockTakingListVO = stockTakingListMapper.selectByPrimaryKey(id);
        result.put("info",stockTakingListVO);
        List<StockTakingListDetailVO> listDetailVOS = stockTakingListDetailMapper.selectByPrimaryKey(id);
        wmsBuilderService.batchBuildWMSInfo(listDetailVOS);
        if (!CollectionUtils.isEmpty(listDetailVOS)){
            listDetailVOS.forEach(x -> {
                SkuBatchCode skuBatchCode = new SkuBatchCode(x.getSku(),x.getBatch(),x.getProductionDate());
                skuBatchCode.setQualityDate(x.getQualityDate());
                SkuBatchCode queryBatchCode = skuBatchCodeService.selectSkuBatchCod(skuBatchCode);
                if(!Objects.isNull(queryBatchCode)){
                    x.setPrintNumber(queryBatchCode.getPrintNumber());
                }
                //是否可以打印条码 类型为水果
                InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(x.getSku());
                Integer canPrint = !Objects.isNull(inventoryVO) && Objects.equals(inventoryVO.getCategoryType(),CategoryTypeEnum.FRUIT.getType())
                        ? CodePrintEnum.CAN_PRINT.ordinal() : CodePrintEnum.NOT_CAN_PRINT.ordinal() ;
                x.setCanPrint(canPrint);
            });
            Map<String, List<StockTakingListDetailVO>> collect = listDetailVOS.stream().collect(Collectors.groupingBy(StockTakingListDetailVO::getSku));
            result.put("list",collect);
        }
        return AjaxResult.getOK(result);
    }

    @Override
    public AjaxResult listDetailDownload(Integer id, HttpServletResponse response) {
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet("盘点单数据");
        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("一级类目");
        title.createCell(1).setCellValue("二级类目");
        title.createCell(2).setCellValue("sku");
        title.createCell(3).setCellValue("商品名称");
        title.createCell(4).setCellValue("规格");
        title.createCell(5).setCellValue("储存区域");
        title.createCell(6).setCellValue("包装");
        title.createCell(7).setCellValue("仓库库存");
        title.createCell(8).setCellValue("采购批次");
        title.createCell(9).setCellValue("保质期");
        title.createCell(10).setCellValue("批次库存");
        title.createCell(11).setCellValue("盘点库存");
        title.createCell(12).setCellValue("原因");

        List<StockTakingListDetailVO> stockTakingListDetailVOS = stockTakingListDetailMapper.selectByPrimaryKey(id);
        wmsBuilderService.batchBuildWMSInfo(stockTakingListDetailVOS);

        if (!CollectionUtils.isEmpty(stockTakingListDetailVOS)) {
            Map<String, List<StockTakingListDetailVO>> collect = stockTakingListDetailVOS.stream().collect(Collectors.groupingBy(o -> o.getSku()));
            int index = 1;
            Set<String> set = collect.keySet();
            for (String key : set) {
                List<StockTakingListDetailVO> values = collect.get(key);
                if (values.size() > 1) {

                    CellRangeAddress firstCategoryMerge = new CellRangeAddress(index, index + values.size() - 1, 0, 0);
                    sheet.addMergedRegion(firstCategoryMerge);

                    CellRangeAddress secondCategoryMerge = new CellRangeAddress(index, index + values.size() - 1, 1, 1);
                    sheet.addMergedRegion(secondCategoryMerge);

                    CellRangeAddress skuMerge = new CellRangeAddress(index, index + values.size() - 1, 2, 2);
                    sheet.addMergedRegion(skuMerge);

                    CellRangeAddress pdNameMerge = new CellRangeAddress(index, index + values.size() - 1, 3, 3);
                    sheet.addMergedRegion(pdNameMerge);

                    CellRangeAddress weightMerge = new CellRangeAddress(index, index + values.size() - 1, 4, 4);
                    sheet.addMergedRegion(weightMerge);

                    CellRangeAddress storageMerge = new CellRangeAddress(index, index + values.size() - 1, 5, 5);
                    sheet.addMergedRegion(storageMerge);

                    CellRangeAddress packingMerge = new CellRangeAddress(index, index + values.size() - 1, 6, 6);
                    sheet.addMergedRegion(packingMerge);

                    CellRangeAddress storeQuantityMerge = new CellRangeAddress(index, index + values.size() - 1, 7, 7);
                    sheet.addMergedRegion(storeQuantityMerge);
                }
                for (StockTakingListDetailVO vo : values) {
                    Row row = sheet.createRow(index);
                    row.createCell(0).setCellValue(vo.getFirstLevelCategory());
                    row.createCell(1).setCellValue(vo.getSecondLevelCategory());
                    row.createCell(2).setCellValue(vo.getSku());
                    row.createCell(3).setCellValue(vo.getPdName());
                    row.createCell(4).setCellValue(vo.getWeight());
                    row.createCell(5).setCellValue(vo.getStorageArea());
                    row.createCell(6).setCellValue(vo.getPacking());
                    row.createCell(7).setCellValue(vo.getStoreQuantity());
                    row.createCell(8).setCellValue(vo.getBatch());
                    row.createCell(9).setCellValue(vo.getQualityDate() == null ? "无" : vo.getQualityDate().toString());
                     row.createCell(10).setCellValue(vo.getQuantity());
                    if (vo.getRealQuantity() != null) {
                        row.createCell(11).setCellValue(vo.getRealQuantity());
                    }
                    row.createCell(12).setCellValue(vo.getReason());
                    index++;
                }
            }
        }
        try {
            ExcelUtils.outputExcel(workbook, "盘点单.xls", response);
        } catch (IOException e) {
            logger.error(Global.collectExceptionStackMsg(e));
            throw new DefaultServiceException("导出失败!");
        }
        return null;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult stockTakingListDetailDelete(StockTakingListDetail stockTakingListDetail) {
        StockTakingListDetail select = new StockTakingListDetail();
        select.setStockTakingListId(stockTakingListDetail.getStockTakingListId());
        select.setSku(stockTakingListDetail.getSku());
        List<StockTakingListDetail> stockTakingListDetails = stockTakingListDetailMapper.select(select);
        if (!CollectionUtils.isEmpty(stockTakingListDetails)) {
            for (StockTakingListDetail detail : stockTakingListDetails) {
                stockTakingListDetailMapper.delete(detail.getId());
            }
        }

        StockTakingListVO stockTakingListVO = stockTakingListMapper.selectByPrimaryKey(stockTakingListDetails.get(0).getStockTakingListId());
        // 更新盘点任务sku状态
        stockTakingItemMapper.updateState(stockTakingListVO.getTakingId(), stockTakingListDetail.getSku());

        List<StockTakingListDetailVO> stockTakingListDetailVOS = stockTakingListDetailMapper.selectByPrimaryKey(stockTakingListDetail.getStockTakingListId());
        if (CollectionUtils.isEmpty(stockTakingListDetailVOS)) { //所有sku都被移除时，删除盘点单
            stockTakingListMapper.delete(stockTakingListDetail.getStockTakingListId());
        } else {
            StockTakingList update = new StockTakingList();
            update.setId(stockTakingListDetail.getStockTakingListId());
            update.setUpdatetime(LocalDateTime.now());
            stockTakingListMapper.updateByPrimaryKey(update);
        }

        //更新sku状态
        areaStoreService.updateAreaStoreStatus(stockTakingListVO.getAreaNo(), stockTakingListDetail.getSku());
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult stockTakingListUpdate(StockTakingListVO stockTakingListVO) {
        StockTakingListVO record = stockTakingListMapper.selectByPrimaryKey(stockTakingListVO.getId());
        if (record.getStatus() != 0) {
            throw new DefaultServiceException("只有盘点中的盘点单才能修改");
        }
        List<StockTakingListDetailVO> stockTakingListDetailVOS = stockTakingListVO.getStockTakingListDetailVOS();
        Set<String> repeatBatchCheck = new HashSet<>();

        if (Objects.equals(NumberUtils.INTEGER_TWO, stockTakingListVO.getDimension())) {
            Map<String, List<StockTakingListDetail>> check = stockTakingListDetailVOS.stream().collect(Collectors.groupingBy(StockTakingListDetail::getBatch));
            check.forEach((batch, detail) -> {
                int quantity = detail.stream().mapToInt(StockTakingListDetail::getQuantity).sum();
                int realQuantity = detail.stream().mapToInt(StockTakingListDetail::getRealQuantity).sum();
                if (!Objects.equals(quantity, realQuantity)) {
                    throw new DefaultServiceException("一个批次中盘点库存与批次库存总数不符");
                }
            });
        }

        //新增的盘点项
        List<StockTakingListDetailVO> insertList = stockTakingListDetailVOS.stream().filter(o -> o.getId() == null).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(insertList)) {
            List<StockTakingListDetail> insertBatch = new ArrayList<>();
            for (StockTakingListDetailVO detailVO : insertList) {
                Integer areaNo = stockTakingListVO.getAreaNo();
                String key = "sku:" + detailVO.getSku() + "batch:" + detailVO.getBatch() + "qualityDate:" + detailVO.getQualityDate();
                if (repeatBatchCheck.contains(key)) {
                    throw new DefaultServiceException("请勿重复盘点sku:" + detailVO.getSku() + "批次:" + detailVO.getBatch() + "保质期:" + detailVO.getQualityDate());
                }
                repeatBatchCheck.add(key);

                StockTakingListDetail insert = new StockTakingListDetail();

                insert.setReason(detailVO.getReason());
                insert.setBatch(detailVO.getBatch());
                insert.setSku(detailVO.getSku());
                insert.setQualityDate(detailVO.getQualityDate());
                insert.setProductionDate(detailVO.getProductionDate());
                insert.setStockTakingListId(stockTakingListVO.getId());
                InventoryVO inventoryVO = inventoryMapper.selectSkuType(detailVO.getSku());
                insert.setPdName(inventoryVO.getPdName());
                insert.setWeight(inventoryVO.getWeight());
                insert.setRealQuantity(detailVO.getRealQuantity());
                insert.setReasonType(detailVO.getReasonType());
                if (!Objects.equals(NumberUtils.INTEGER_TWO, stockTakingListVO.getDimension())&&!detailVO.getBatch().matches("01[0-9]{14}")) { //非新增批次
                    StoreRecord select = new StoreRecord();
                    select.setSku(detailVO.getSku());
                    select.setBatch(detailVO.getBatch());
                    select.setQualityDate(detailVO.getQualityDate());
                    select.setAreaNo(stockTakingListVO.getAreaNo());
                    StoreRecord storeRecord = storeRecordMapper.selectOne(select);
                    if (storeRecord == null) {
                        throw new DefaultServiceException("sku:" + detailVO.getSku() + "保质期:" + detailVO.getQualityDate() + "批次:" + detailVO.getBatch() + "在当前仓不存在");
                    }
                    insert.setQuantity(storeRecord.getStoreQuantity());
                }

                if (Objects.equals(NumberUtils.INTEGER_TWO, stockTakingListVO.getDimension())) {
                    BigDecimal cost = getCost(detailVO);
//                    if (Objects.isNull(cost)) {
//                        throw new DefaultServiceException("sku:" + detailVO.getSku() + "保质期:" + detailVO.getQualityDate() + "批次:" + detailVO.getBatch() + "无单件成本");
//                    }
                    StoreRecord input = new StoreRecord(detailVO.getBatch(), detailVO.getSku(), StoreRecordType.BATCH_TRIM.getId(), detailVO.getRealQuantity(), null,
                            getAdminName(), detailVO.getReason(), new Date(), stockTakingListVO.getAreaNo(), detailVO.getQualityDate(), detailVO.getProductionDate(),
                            detailVO.getRealQuantity(), cost
                            , BaseConstant.XIANMU_TENANT_ID);
                    storeRecordMapper.insert(input);
                }
                insertBatch.add(insert);
            }
            stockTakingListDetailMapper.insertBatch(insertBatch);
        }

        //修改的盘点项
        List<StockTakingListDetailVO> updateList = stockTakingListDetailVOS.stream().filter(o -> o.getId() != null).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(updateList)) {
            for (StockTakingListDetailVO detailVO : updateList) {

                String key = "sku:" + detailVO.getSku() + "batch:" + detailVO.getBatch() + "qualityDate:" + detailVO.getQualityDate();
                if (repeatBatchCheck.contains(key)) {
                    throw new DefaultServiceException("请勿重复盘点sku:" + detailVO.getSku() + "批次:" + detailVO.getBatch() + "保质期:" + detailVO.getQualityDate());
                }
                repeatBatchCheck.add(key);

                StockTakingListDetail update = new StockTakingListDetail();
                update.setId(detailVO.getId());
                update.setRealQuantity(detailVO.getRealQuantity());
                update.setReason(detailVO.getReason());
                update.setReasonType(detailVO.getReasonType());
                stockTakingListDetailMapper.updateByPrimaryKey(update);

                if (Objects.equals(NumberUtils.INTEGER_TWO, stockTakingListVO.getDimension())) {
                    StoreRecord select = new StoreRecord();
                    select.setSku(detailVO.getSku());
                    select.setBatch(detailVO.getBatch());
                    select.setQualityDate(detailVO.getQualityDate());
                    select.setAreaNo(stockTakingListVO.getAreaNo());
                    StoreRecord storeRecord = storeRecordMapper.selectOne(select);
                    if (StringUtils.isBlank( detailVO.getRealQuantity())){
                        throw new DefaultServiceException("参数错误");
                    }
                    if (storeRecord.getStoreQuantity() < detailVO.getRealQuantity()) {
                        StoreRecord input = new StoreRecord(detailVO.getBatch(), detailVO.getSku(), StoreRecordType.BATCH_TRIM.getId(), detailVO.getRealQuantity() - storeRecord.getStoreQuantity(), null,
                                getAdminName(), detailVO.getReason(), new Date(), stockTakingListVO.getAreaNo(), detailVO.getQualityDate(), detailVO.getProductionDate(),
                                detailVO.getRealQuantity(), storeRecord.getCost()
                            , BaseConstant.XIANMU_TENANT_ID);
                        storeRecordMapper.insert(input);
                    }else if (storeRecord.getStoreQuantity() > detailVO.getRealQuantity()){
                        StoreRecord output = new StoreRecord(detailVO.getBatch(), detailVO.getSku(), StoreRecordType.BATCH_TRIM.getId(), storeRecord.getStoreQuantity() - detailVO.getRealQuantity(), null,
                                getAdminName(), detailVO.getReason(), new Date(), stockTakingListVO.getAreaNo(), detailVO.getQualityDate(), detailVO.getProductionDate(),
                                detailVO.getRealQuantity(), storeRecord.getCost()
                            , BaseConstant.XIANMU_TENANT_ID);
                        storeRecordMapper.insert(output);
                    }
                }
            }
        }
        //金额大于500时走审核,小于500时,走自动通过
        StoreRecord selectStore = new StoreRecord();
        Map<String, List<StockTakingListDetail>> skuStockMap = stockTakingListDetailVOS.stream().collect(Collectors.groupingBy(StockTakingListDetail::getSku));
        Boolean flag = false;
        StringBuilder msg = new StringBuilder();
        //是否需要自动审核
        List<StoreRecord> lockStoreRecords = new LinkedList<>();
        Set<String> sReasonSet = new HashSet<>();
        Set<String> sReasonTypeSet = new HashSet<>();
        Set<String> dReasonSet = new HashSet<>();
        Set<String> dReasonTypeSet = new HashSet<>();
        //批次盘点时，不计算
        if (!Objects.equals(NumberUtils.INTEGER_TWO, stockTakingListVO.getDimension())) {
            for (String sku : skuStockMap.keySet()) {
                //盘盈盘亏金额
                BigDecimal amount = BigDecimal.ZERO;
                //盘盈金额
                BigDecimal sAmount = BigDecimal.ZERO;
                //盘亏金额
                BigDecimal dAmount = BigDecimal.ZERO;
                //盈利数量
                int sNum = 0;
                //亏损数量
                int dNum = 0;
                for (StockTakingListDetail detail : skuStockMap.get(sku)) {
                    //添加批次批次库存默认为0
                    if (Objects.isNull(detail.getQuantity())){
                        detail.setQuantity(NumberUtils.INTEGER_ZERO);
                    }
                    selectStore.setSku(detail.getSku());
                    selectStore.setBatch(detail.getBatch());
                    selectStore.setAreaNo(stockTakingListVO.getAreaNo());
                    selectStore.setQualityDate(detail.getQualityDate());
                    StoreRecord selectOne = storeRecordMapper.selectOne(selectStore);
                    if (detail.getRealQuantity() > detail.getQuantity()) {
                        amount = amount.add(BigDecimal.valueOf(detail.getRealQuantity() - detail.getQuantity()).multiply(Objects.isNull(selectOne) ? BigDecimal.ZERO : selectOne.getCost()));
                        sAmount = sAmount.add(BigDecimal.valueOf(detail.getRealQuantity() - detail.getQuantity()).multiply(Objects.isNull(selectOne) ? BigDecimal.ZERO : selectOne.getCost()));
                        sNum += detail.getRealQuantity() - detail.getQuantity();
                        // 拼接盘盈原因
                        if (Objects.equals(NumberUtils.INTEGER_ONE,detail.getReasonType())){
                            sReasonSet.add(detail.getReason());
                        }else{
                            sReasonTypeSet.add(detail.getReason());
                        }
                    }
                    if (detail.getRealQuantity() < detail.getQuantity()) {
                        amount = amount.add(BigDecimal.valueOf(detail.getRealQuantity() - detail.getQuantity()  ).multiply(Objects.isNull(selectOne) ? BigDecimal.ZERO : selectOne.getCost()));
                        dAmount = dAmount.add(BigDecimal.valueOf(detail.getQuantity() - detail.getRealQuantity() ).multiply(Objects.isNull(selectOne) ? BigDecimal.ZERO : selectOne.getCost()));
                        dNum += detail.getQuantity() - detail.getRealQuantity();
                        // 拼接盘亏原因
                        if (Objects.equals(NumberUtils.INTEGER_ONE,detail.getReasonType())){
                            dReasonSet.add(detail.getReason());
                        }else{
                            dReasonTypeSet.add(detail.getReason());
                        }
                    }

                }
                //当有货损时,组装库存冻结SKU,以及数量
                if (dNum > 0) {
                    StoreRecord storeRecord = new StoreRecord();
                    storeRecord.setAreaNo(stockTakingListVO.getAreaNo());
                    storeRecord.setQuantity(dNum);
                    storeRecord.setSku(sku);
                    lockStoreRecords.add(storeRecord);
                }
                //大于500时，需要自动审核
                if(amount.abs().compareTo(new BigDecimal("500"))!=-1){
                    flag = true;
                }
                InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(sku);
                if(sNum>0){
                    msg.append(sku).append(inventoryVO.getPdName()).append("盘盈").append(sNum).append("件,盘盈金额总计").append(sAmount).append("元。").append("\n");
                    msg.append("原因:");
                    StringJoiner joiner = new StringJoiner(SEPARATING_SYMBOL);
                    sReasonSet.forEach(joiner::add);
                    sReasonTypeSet.forEach(joiner::add);
                    msg.append(joiner.toString()).append("\n");
                }

                if(dNum>0){
                    msg.append(sku).append(inventoryVO.getPdName()).append("盘亏").append(dNum).append("件,盘亏金额总计").append(dAmount).append("元。").append("\n");
                    msg.append("原因:");
                    StringJoiner joiner = new StringJoiner(SEPARATING_SYMBOL);
                    dReasonSet.forEach(joiner::add);
                    dReasonTypeSet.forEach(joiner::add);
                    msg.append(joiner.toString()).append("\n");
                }

            }
        }

        if (stockTakingListVO.getProcess() == 1) { //提交盘点单,进入审核状态
            boolean sign = checkList(stockTakingListVO);
            List<StockTakingItem> items = stockTakingItemMapper.selectByStockTakingListId(stockTakingListVO.getId());
            StockTakingList update = new StockTakingList();
            update.setId(stockTakingListVO.getId());
            if (sign) { //库存数量无出入，绕过审核
                update.setStatus(2);
                stockTakingListMapper.updateByPrimaryKey(update);

                for (StockTakingItem item : items) {
                    StockTakingItem itemUpdate = new StockTakingItem();
                    itemUpdate.setId(item.getId());
                    itemUpdate.setState(3);
                    stockTakingItemMapper.updateByPrimary(itemUpdate);
                    areaStoreService.updateAreaStoreStatus(stockTakingListVO.getAreaNo(), item.getSku());
                }

                closeStockTaking(record.getTakingId(), record.getStockTakingNo());
            } else { //进入审核中
                update.setStatus(1);
                stockTakingListMapper.updateByPrimaryKey(update);

                for (StockTakingItem item : items) {
                    StockTakingItem itemUpdate = new StockTakingItem();
                    itemUpdate.setId(item.getId());
                    itemUpdate.setState(2);
                    stockTakingItemMapper.updateByPrimary(itemUpdate);
                }
                //增加钉钉人工审核
                if(flag){
                    try {
                        WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNo(stockTakingListVO.getAreaNo());
                        //库存冻结
                        for (StoreRecord lockStoreRecord : lockStoreRecords) {
                            //添加冻结
                            Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
                            //1.虚拟库存
                            areaStoreService.updateOnlineStockByStoreNo(true, -lockStoreRecord.getQuantity(), lockStoreRecord.getSku(), lockStoreRecord.getAreaNo(), OtherStockChangeTypeEnum.STOCK_TAKING_OUT, null, recordMap, org.apache.commons.lang3.math.NumberUtils.INTEGER_ONE);
                            //2.加冻结库存
                            areaStoreService.updateLockStockByWarehouseNo(lockStoreRecord.getQuantity(), lockStoreRecord.getSku(), null, lockStoreRecord.getAreaNo(), OtherStockChangeTypeEnum.STOCK_TAKING_OUT, null, recordMap);
                            quantityChangeRecordService.insertRecord(recordMap);
                        }
                        // 扣减完后发送审批,避免审批重复
                        createStockTakingProcess(stockTakingListVO.getId(),warehouseStorageCenter.getWarehouseName(),msg.toString());
                    }catch (Exception e){
                        logger.error("盘盈&盘亏增加钉钉审核失败,id:{}",stockTakingListVO.getId());
                        throw new DefaultServiceException("盘盈&盘亏增加钉钉审核失败"+e.getMessage());
                    }
                }else{ //直接通过
                    this.stockTakingListHandler(stockTakingListVO.getId(),NumberUtils.INTEGER_TWO,null,null,null);
                }
            }
        }
        return AjaxResult.getOK();
    }

    /**
     * 创建钉钉审批
     * @param id
     * @param warehouseName
     * @param msg
     * @return
     */
    private String createStockTakingProcess(Integer id, String warehouseName, String msg) throws DingdingProcessException {
        // 审批业务实例
        ProcessInstanceCreateBO processInstanceCreateBO = new ProcessInstanceCreateBO();
        processInstanceCreateBO.setBizTypeEnum(ProcessInstanceBizTypeEnum.STOCKTAKING_AUDUT);
        // 发起人adminId
        processInstanceCreateBO.setAdminId(super.getAdminId());
        // 业务数据id 用于标识
        processInstanceCreateBO.setBizId(id.longValue());
        // 审批表单参数
        List<DingdingFormBO> dingForms = new ArrayList<>(3);
        DingdingFormBO df1 = new DingdingFormBO();
        df1.setFormName("任务编号");
        df1.setFormValue(id.toString());
        dingForms.add(df1);
        DingdingFormBO df2 = new DingdingFormBO();
        df2.setFormName("仓库名称");
        df2.setFormValue(warehouseName);
        dingForms.add(df2);
        DingdingFormBO df3 = new DingdingFormBO();
        df3.setFormName("操作时间");
        df3.setFormValue(DateUtils.localDateTimeToString(LocalDateTime.now()));
        dingForms.add(df3);
        DingdingFormBO df4 = new DingdingFormBO();
        df4.setFormName("操作人");
        df4.setFormValue(getAdminName());
        dingForms.add(df4);
        DingdingFormBO df5 = new DingdingFormBO();
        df5.setFormName("内容");
        df5.setFormValue(msg);
        dingForms.add(df5);
        processInstanceCreateBO.setDingdingForms(dingForms);
        ProcessCreateResultBO processInstance = dingdingProcessInstanceService.createProcessInstance(processInstanceCreateBO);
        if(processInstance.isSuccess()){
            return Global.SUCCESS_FLAG;
        }
        return processInstance.getMessage();
    }

    @Nullable
    private BigDecimal getCost(StockTakingListDetailVO detailVO) {
        StoreRecord select = new StoreRecord();
        select.setSku(detailVO.getSku());
        select.setBatch(detailVO.getBatch());
        BigDecimal cost = storeRecordMapper.selectPurchaseCost(select);
        if (Objects.isNull(cost)) {
        // 该批次非采购批次
            cost = storeRecordMapper.selectBatchCost(select);
        }
        return cost;
    }

    /**
     * 检查盘点单是否可以进入审核状态
     */
    @Transactional(propagation = Propagation.REQUIRED)
    public boolean checkList(StockTakingListVO stockTakingListVO) {
        StockTakingListDetail select = new StockTakingListDetail();
        select.setStockTakingListId(stockTakingListVO.getId());
        List<StockTakingListDetail> stockTakingListDetails = stockTakingListDetailMapper.select(select);
        boolean sign = true;

        for (StockTakingListDetail detail : stockTakingListDetails) {
            if (detail.getRealQuantity() == null) {
                throw new DefaultServiceException("sku:" + detail.getSku() + "批次:" + detail.getBatch() + "保质期:" + detail.getQualityDate() + "未填写盘点数量");
            }
            if (detail.getQuantity() == null) {
                sign = false;
            } else if (!detail.getQuantity().equals(detail.getRealQuantity())) {
                sign = false;
            }
        }
        if (Objects.equals(NumberUtils.INTEGER_TWO, stockTakingListVO.getDimension())) {
            Set<String> skus = stockTakingListVO.getStockTakingListDetailVOS().stream().map(StockTakingListDetailVO::getSku).collect(Collectors.toSet());
            skus.forEach(sku -> {
                List<StockTakingListDetailVO> detailVOS = stockTakingListVO.getStockTakingListDetailVOS().stream().filter(vo -> sku.equals(vo.getSku())).collect(Collectors.toList());
                int totalQuantity = 0;
                for (StockTakingListDetailVO detail : detailVOS) {
                    totalQuantity += detail.getRealQuantity();
                }
                StockTakingListDetailVO vo = detailVOS.get(0);
                if (totalQuantity >vo.getStoreQuantity()) {
                    throw new DefaultServiceException("sku:" + sku + "盘点库存总数大于仓库库存");
                } else if (totalQuantity < vo.getStoreQuantity()) {
                    throw new DefaultServiceException("sku:" + sku + "盘点库存总数小于仓库库存");
                }
            });
            sign=true;
        }
        return sign;
    }

    @Override
    public AjaxResult stockTakingListSelect(Integer id, String pdName, String sku, Boolean diff) {
        List<StockTakingListDetailVO> stockTakingListDetailVOS = stockTakingListDetailMapper.selectList(id, pdName, sku, diff);
        if (!CollectionUtils.isEmpty(stockTakingListDetailVOS)) {
            Map<String, List<StockTakingListDetailVO>> collect = stockTakingListDetailVOS.stream().collect(Collectors.groupingBy(StockTakingListDetailVO::getSku));
            return AjaxResult.getOK(collect);
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult stockTakingListHandler(Integer id, Integer status, String remark,Integer types,Integer auditId) {
        StockTakingListVO record = stockTakingListMapper.selectByPrimaryKey(id);

        StockTask stockTask = stockTaskMapper.selectByStockTakingListId(id);
        if (record.getStatus() != 1) {
            return AjaxResult.getErrorWithMsg("审核中的盘点单才能进行审核");
        }
        StockTakingList update = new StockTakingList();
        update.setId(record.getId());
        update.setStatus(status);
        update.setRemark(remark);
        update.setUpdatetime(LocalDateTime.now());
        update.setHandler(getAdminId());
        stockTakingListMapper.updateByPrimaryKey(update);

        List<StockTakingItem> items = stockTakingItemMapper.selectByStockTakingListId(id);
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        StockTakingListDetail select = new StockTakingListDetail();
        select.setStockTakingListId(id);
        List<StockTakingListDetail> details = stockTakingListDetailMapper.select(select);
        if (status == 2) { //审核通过
            //key:sku , value:盘盈金额，盘亏金额
            Map<String, String> skuAmountMap = new HashMap<>();
            StoreRecord selectStore = new StoreRecord();
            //sku维度区分盘盈盘亏
            Map<String, List<StockTakingListDetail>> skuStockMap = details.stream().collect(Collectors.groupingBy(StockTakingListDetail::getSku));
            skuStockMap.forEach((sku, list) -> {
                //盘盈金额
                BigDecimal sAmount = BigDecimal.ZERO;
                //盘亏金额
                BigDecimal lAmount = BigDecimal.ZERO;
                for (StockTakingListDetail detail : list) {
                    //添加批次批次库存默认为0
                    if (Objects.isNull(detail.getQuantity())){
                        detail.setQuantity(NumberUtils.INTEGER_ZERO);
                    }
                    selectStore.setSku(detail.getSku());
                    selectStore.setBatch(detail.getBatch());
                    selectStore.setAreaNo(stockTask.getAreaNo());
                    selectStore.setQualityDate(detail.getQualityDate());
                    StoreRecord selectOne = storeRecordMapper.selectOne(selectStore);
                    if (detail.getRealQuantity() > detail.getQuantity()) {
                        sAmount = sAmount.add(BigDecimal.valueOf(detail.getRealQuantity() - detail.getQuantity()).multiply(Objects.isNull(selectOne) ? BigDecimal.ZERO : selectOne.getCost()));
                    }
                    if (detail.getRealQuantity() < detail.getQuantity()) {
                        lAmount = lAmount.add(BigDecimal.valueOf(detail.getQuantity() - detail.getRealQuantity()).multiply(Objects.isNull(selectOne) ? BigDecimal.ZERO : selectOne.getCost()));
                    }
                }
                skuAmountMap.put(sku, sAmount + SEPARATING_SYMBOL + lAmount);
            });
            details.sort(Comparator.comparing(StockTakingListDetail::getSku));
            for (StockTakingListDetail detail : details) {

                Integer diff;
                String sku = detail.getSku();
                if (detail.getQuantity() == null) { //盘盈新批次
                    diff = detail.getRealQuantity();
                } else {
                    diff = detail.getRealQuantity() - detail.getQuantity();
                }
                if (!Objects.equals(NumberUtils.INTEGER_ZERO, diff)) { //库存有出入
                    StoreRecordType type = diff < 0 ? StoreRecordType.STOCK_TAKING_OUT : StoreRecordType.STOCK_TAKING_IN;
                    //2.修改虚拟库存
                    if ((diff > NumberUtils.INTEGER_ZERO && Objects.equals(types, NumberUtils.INTEGER_ZERO)) || Objects.isNull(types)) {
                        areaStoreService.updateOnlineStockByStoreNo(true, diff, detail.getSku(), record.getAreaNo(), OtherStockChangeTypeEnum.STOCK_TAKING, stockTask.getId() + "", recordMap, NumberUtils.INTEGER_ONE);
                    }
                    //数量为盘亏时，修改冻结库存
                    if (diff < 0 && Objects.equals(types,NumberUtils.INTEGER_ZERO)) {
                        areaStoreService.updateLockStockByWarehouseNo(diff, sku, null, record.getAreaNo(), OtherStockChangeTypeEnum.STOCK_TAKING_OUT, null, recordMap);
                    }
                    //1.当前仓总库存修改（跟2中 位置调换会出bug）
                    areaStoreService.updateStoreStockByWarehouseNo(diff, detail.getSku(), record.getAreaNo(), type, type.getId(), id + "", recordMap);
                    StoreRecord storeRecordSelect = new StoreRecord();
                    storeRecordSelect.setBatch(detail.getBatch());
                    storeRecordSelect.setSku(detail.getSku());
                    storeRecordSelect.setQualityDate(detail.getQualityDate());
                    storeRecordSelect.setAreaNo(record.getAreaNo());
                    StoreRecord storeRecord = storeRecordMapper.selectOne(storeRecordSelect);

                    StoreRecord insert = new StoreRecord();
                    insert.setBatch(detail.getBatch());
                    insert.setSku(detail.getSku());
                    insert.setQualityDate(detail.getQualityDate());
                    insert.setAreaNo(record.getAreaNo());
                    insert.setType(type.getId());
                    insert.setQuantity(Math.abs(diff));
                    Admin admin = adminService.select(record.getAdminId());
                    insert.setRecorder(admin.getRealname());
                    if (!StringUtils.isEmpty(detail.getReason())) {
                        insert.setRemark("库存盘点:" + detail.getReason());
                    }
                    insert.setUpdateTime(new Date());
                    insert.setStoreQuantity(detail.getRealQuantity());
                    if (detail.getQuantity() == null) { //原数量为空,为新增批次
                        //新增库存数据
                        insert.setCost(BigDecimal.ZERO);
                        insert.setProductionDate(detail.getProductionDate());
                    } else {
                        insert.setCost(Objects.isNull(storeRecord) ? BigDecimal.ZERO : storeRecord.getCost());
                        insert.setProductionDate(Objects.isNull(storeRecord) ? detail.getProductionDate() : storeRecord.getProductionDate());
                    }
                    insert.setTenantId(Objects.isNull(storeRecord) ? BaseConstant.XIANMU_TENANT_ID : storeRecord.getTenantId());
                    storeRecordMapper.insert(insert);
                    //4uan修正发消息
                    String keyword = insert.getAreaNo() + Global.SEPARATING_SYMBOL + insert.getSku();
                    String mainInfo = detail.getPdName() + ":" + getAdminName() + "把" + Global.warehouseMap.get(insert.getAreaNo()) + "-" + insert.getSku() + "库存" + diff;
                    msgAdminService.handleMsg(6, null, mainInfo, keyword, null, null, SMSType.NOTIFY);
                }
                //生成码信息
                SkuBatchCode skuBatchCode = new SkuBatchCode(detail.getSku(),detail.getBatch(),detail.getProductionDate());
                skuBatchCode.setQualityDate(detail.getQualityDate());
                skuBatchCodeService.createBatchCode(skuBatchCode,record.getAreaNo());
            }

            for (StockTakingItem item : items) {
                StockTakingItem itemUpdate = new StockTakingItem();
                itemUpdate.setId(item.getId());
                itemUpdate.setState(3);
                stockTakingItemMapper.updateByPrimary(itemUpdate);
                areaStoreService.updateAreaStoreStatus(record.getAreaNo(), item.getSku());
                purchasesConfigService.msgArrival(record.getAreaNo(), item.getSku()); //预警消息
            }

            closeStockTaking(record.getTakingId(), record.getStockTakingNo());
            try {
                if (!CollectionUtils.isEmpty(skuAmountMap)) {
                    // 封装盘点钉钉盘点消息通知
                    StringJoiner surplusMsg = packDingTalkMsg(record, skuAmountMap, SURPLUS);
                    if (StringUtils.isNotBlank(surplusMsg.toString())) {
                        dingTalkService.sendCargoDamageMsg(stockTask.getId(), SURPLUS, surplusMsg,auditId);
                    }

                    StringJoiner lossMsg = packDingTalkMsg(record, skuAmountMap, LOSS);
                    if (StringUtils.isNotBlank(lossMsg.toString())) {
                        dingTalkService.sendCargoDamageMsg(stockTask.getId(), LOSS, lossMsg,auditId);
                    }
                }
            } catch (Exception e) {
                logger.error("任务编号{}:钉钉提醒异常:{},信息：{}", stockTask.getId(),e.getMessage(),e.getCause());
            }

        } else if (status == 3) { //审核未通过
            for (StockTakingListDetail detail : details) {
                Integer diff;
//                String sku = detail.getSku();
                if (detail.getQuantity() == null) { //盘盈新批次
                    diff = detail.getRealQuantity();
                } else {
                    diff = detail.getRealQuantity() - detail.getQuantity();
                }
                //2.修改虚拟库存
                if (diff < NumberUtils.INTEGER_ZERO) {
                    areaStoreService.updateOnlineStockByStoreNo(true, -diff, detail.getSku(), record.getAreaNo(), OtherStockChangeTypeEnum.STOCK_TAKING, id + "", recordMap, NumberUtils.INTEGER_ONE);
                    areaStoreService.updateLockStockByWarehouseNo(diff, detail.getSku(), null, record.getAreaNo(), OtherStockChangeTypeEnum.STOCK_TAKING_OUT, null, recordMap);
                }
            }
            for (StockTakingItem item : items) {
                StockTakingItem itemUpdate = new StockTakingItem();
                itemUpdate.setId(item.getId());
                itemUpdate.setState(3);
                stockTakingItemMapper.updateByPrimary(itemUpdate);
                areaStoreService.updateAreaStoreStatus(record.getAreaNo(), item.getSku());
            }
        }
        quantityChangeRecordService.insertRecord(recordMap);
        return AjaxResult.getOK();
    }

    /**
     * 封装盘盈盘亏消息内容
     * @param record
     * @param skuAmountMap  value:盘盈金额，盘亏金额
     */
    private StringJoiner packDingTalkMsg(StockTakingListVO record, Map<String, String> skuAmountMap,String type) {
        StringJoiner joiner = new StringJoiner("\n > ###### ");

        Iterator<String> iterator = skuAmountMap.keySet().iterator();
        List<StockTakingListDetail> details = stockTakingListDetailMapper.selectByTakingId(record.getTakingId());
        while (iterator.hasNext()) {
            String sku = iterator.next();
            String amount = skuAmountMap.get(sku);
            if (SURPLUS.equals(type) && BigDecimal.ZERO.compareTo(new BigDecimal(amount.split(SEPARATING_SYMBOL)[NumberUtils.INTEGER_ZERO])) < 0) {
                InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(sku);
                //封装盘点原因
                StringJoiner reason = new StringJoiner(SEPARATING_SYMBOL);
                List<StockTakingListDetail> list = details.stream().filter(stockTakingListDetail -> sku.equals(stockTakingListDetail.getSku())).collect(Collectors.toList());
                int quantity = NumberUtils.INTEGER_ZERO;
                if (!CollectionUtils.isEmpty(list)) {
                    for (StockTakingListDetail detail : list) {
                        if (Objects.isNull(detail.getQuantity())) {
                            detail.setQuantity(NumberUtils.INTEGER_ZERO);
                        }
                        if (detail.getRealQuantity() > detail.getQuantity()) {
                            reason.add(detail.getReason());
                            quantity += detail.getRealQuantity() - detail.getQuantity();
                        }
                    }
                }
                String msg = "sku:" + sku + SEPARATING_SYMBOL + inventoryVO.getPdName() + SURPLUS + quantity + "件金额共计" + new BigDecimal(amount.split(SEPARATING_SYMBOL)[NumberUtils.INTEGER_ZERO]).toString() + "元";
                joiner.add(msg);
                joiner.add("盘点原因:" + reason.toString());
            }
            if (LOSS.equals(type) && BigDecimal.ZERO.compareTo(new BigDecimal(amount.split(SEPARATING_SYMBOL)[NumberUtils.INTEGER_ONE])) < 0) {
                int quantity = NumberUtils.INTEGER_ZERO;
                //封装盘点原因
                StringJoiner reason = new StringJoiner(SEPARATING_SYMBOL);
                List<StockTakingListDetail> list = details.stream().filter(stockTakingListDetail -> sku.equals(stockTakingListDetail.getSku())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(list)) {
                    for (StockTakingListDetail detail : list) {
                        if (detail.getRealQuantity() < detail.getQuantity()) {
                            reason.add(detail.getReason());
                            quantity += detail.getQuantity() - detail.getRealQuantity();
                        }
                    }
                }
                InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(sku);
                String msg = "sku:" + sku + SEPARATING_SYMBOL + inventoryVO.getPdName() + LOSS + quantity + "件金额共计" + new BigDecimal(amount.split(SEPARATING_SYMBOL)[NumberUtils.INTEGER_ONE]).toString() + "元";
                joiner.add(msg);
                joiner.add("盘点原因:" + reason.toString());

            }
        }

        return joiner;
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public void closeStockTaking(Integer takingId, String stockTakingNo) {
        StockTakingItem select = new StockTakingItem();
        select.setTakingId(takingId);
        List<StockTakingItem> records = stockTakingItemMapper.selectUnFinishSku(select);

        StockTaking updateTaking = new StockTaking();
        updateTaking.setId(takingId);

        StockTask updateTask = new StockTask();
        updateTask.setTaskNo(stockTakingNo);
        updateTask.setUpdatetime(LocalDateTime.now());

        if (CollectionUtils.isEmpty(records)) { //全部盘点完成
            updateTaking.setStatus(2);
            updateTask.setState(2);
            stockTakingMapper.updateByPrimary(updateTaking);
            stockTaskMapper.updateByTaskNo(updateTask);
        } else { //部分完成
            updateTaking.setStatus(1);
            updateTask.setState(1);
            stockTakingMapper.updateByPrimary(updateTaking);
            stockTaskMapper.updateByTaskNo(updateTask);
        }
    }


    /**
     * 创建新的批次号（与采购单无关联）
     * 盘盈批次号16位：01 + 年月日 + 6位随机数
     *
     * @return
     */
    @Override
    public String createNewPurchasesNo() {
        StringBuffer purchasesNo = new StringBuffer();
        purchasesNo.append("01");
        purchasesNo.append(LocalDate.now().toString().replaceAll("-", ""));
        boolean loop = true;
        while (loop) {
            String randomNumber = StringUtils.getRandomNumber(6);
            Integer count = stockTakingListDetailMapper.countPurchasesNo(purchasesNo + randomNumber);
            if (count == null || count == 0) {
                loop = false;
                purchasesNo.append(randomNumber);
            }
        }
        return purchasesNo.toString();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult areaStoreStatusInit(Integer areaNo, String sku) {
        AreaStore select = new AreaStore();
        select.setAreaNo(areaNo);
        select.setSku(sku);
        List<AreaStore> areaStores = areaStoreMapper.selectList(select);
        if (!CollectionUtils.isEmpty(areaStores)) {
            for (AreaStore areaStore : areaStores) {
                areaStoreService.updateAreaStoreStatus(areaStore.getAreaNo(), areaStore.getSku());
            }
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult finishStockTask(int id) {
        StockTask select = stockTaskMapper.selectByPrimaryKey(id);
        if (Objects.equals(NumberUtils.INTEGER_TWO,select.getState())){
            throw new DefaultServiceException("该任务已完成！");
        }

        //修改任务状态
        StockTask update = new StockTask();
        update.setId(id);
        update.setState(NumberUtils.INTEGER_TWO);
        update.setUpdatetime(LocalDateTime.now());
        stockTaskMapper.update(update);

        //修改盘点单及盘点条目状态
        StockTaking stockTaking = new StockTaking();
        stockTaking.setStockTakingNo(select.getTaskNo());
        stockTaking.setStatus(StockTakingStatusEnum.SUCCESS.getStatus());
        stockTaking.setUpdateTime(LocalDateTime.now());
        stockTaking.setRemark(getAdminName()+"完成操作");
        stockTakingMapper.updateByTaskNo(stockTaking);

        StockTaking selectOne = stockTakingMapper.selectOne(select.getTaskNo());
        if (null!=selectOne){
            StockTakingItem stockTakingItem = new StockTakingItem();
            stockTakingItem.setTakingId(selectOne.getId());
            stockTakingItem.setState(StockTakingStatusEnum.CLOSE.getStatus());
            stockTakingItemMapper.updateByTakingId(stockTakingItem);
        }

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult closeStockTaking(int id) {

        StockTakingListVO select = stockTakingListMapper.selectByPrimaryKey(id);
        if (Objects.isNull(select.getTakingId())){
            throw new DefaultServiceException("盘点单编号出错了！");
        }
        StockTaking taking = stockTakingMapper.selectById(select.getTakingId());
        if (Objects.isNull(taking.getAreaNo())){
            throw new DefaultServiceException("盘点单归属仓库出错了！");
        }

        //修改盘点单状态
        StockTakingList stockTakingList = new StockTakingList();
        stockTakingList.setId(id);
        stockTakingList.setStatus(StockTakingStatusEnum.CLOSE.getStatus());
        stockTakingList.setUpdatetime(LocalDateTime.now());
        stockTakingList.setUpdater(getAdminName());
        stockTakingListMapper.updateByTakingId(stockTakingList);

        //修改盘点单条目状态
        StockTakingItem stockTakingItem = new StockTakingItem();
        stockTakingItem.setTakingId(select.getTakingId());
        stockTakingItem.setState(StockTakingStatusEnum.CLOSE.getStatus());
        stockTakingItemMapper.updateByTakingId(stockTakingItem);

        //修改盘点单实例状态
        StockTaking stockTaking = new StockTaking();
        stockTaking.setId(select.getTakingId());
        stockTaking.setStatus(StockTakingStatusEnum.SUCCESS.getStatus());
        stockTaking.setUpdateTime(LocalDateTime.now());
        stockTakingMapper.updateByPrimary(stockTaking);

        //修改盘点单下sku状态
        List<StockTakingItem> items = stockTakingItemMapper.selectByTakingId(select.getTakingId());
        items.sort(Comparator.comparing(StockTakingItem::getSku));
        items.forEach(el->{
            WarehouseStockExt updateStockExt = new WarehouseStockExt(taking.getAreaNo(), el.getSku());
            updateStockExt.setStatus(AreaStoreStatusEnum.NORMAL.ordinal());
            warehouseStockExtService.updateStatus(updateStockExt);
         });

        return AjaxResult.getOK();
    }


    /**
     * 盘点出入库入口在此 {@link StockTakingService#stockTakingListUpdate(StockTakingListVO)}
     */
    @Override
    public AjaxResult inOutStore(Integer type, String data) {
        return AjaxResult.getError();
    }

    @Override
    public void closeTask(StockTask stockTask) {
        // do nothing
    }

    /**
     * 盘点任务导出入口在此：{@link StockTakingService#stockTakingDownload(Integer, HttpServletResponse)}
     */
    @Override
    public void stockTaskDetailDownload(StockTask stockTask) {
    }

    /**
     * 生成excel表
     *
     * @param areaNo   库存仓
     * @param type     出库任务类型
     * @param taskList 出库任务列表
     * @return 生成的excel(文件名 + 文件实体)
     */
    @Override
    public List<MailWorkBookDTO> generateExcel(Integer areaNo, Integer type, List<StockTask> taskList) {
        return null;
    }

    @Override
    public void stockTaskBatchDetailDownload(List<StockTask> stockTaskList) {

    }

    @Override
    public AjaxResult stockTaskDetail(StockTaskResult result) {
        StockTaking stockTaking = stockTakingMapper.selectByTakingNo(result.getTaskNo());
        List<StockTakingItemVO> list = new ArrayList<>();

        //未盘点sku
        List<StockTakingItemVO> unTakingItems = getTakingItemVO(stockTaking.getId(), null, null,stockTaking.getAreaNo());
        if (!CollectionUtils.isEmpty(unTakingItems)) {
            list.addAll(unTakingItems);
        }
        //已盘点sku
        List<StockTakingItemVO> takingItems = stockTakingItemMapper.selectTakingItem(stockTaking.getId(), null, null, null);
        if (!CollectionUtils.isEmpty(takingItems)) {

            takingItems.forEach(item->{
                List<StockTakingListDetail> stockTakingListDetails = item.getStockTakingListDetails();
                HashMap<String, StockTakingListDetail> detailHashMap = new HashMap<>();
                //存在货位则合并数据
                List<StockTakingListDetail> collect =
                        stockTakingListDetails.stream().filter(x -> !StringUtils.isEmpty(x.getGlNo())).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(collect)){
                    collect.stream().forEach(x ->{
                        String purchaseNo = x.getBatch();
                        LocalDate qualityDate = x.getQualityDate();
                        String key = purchaseNo + qualityDate;

                        StockTakingListDetail itemDetail = detailHashMap.get(key);
                        if(!Objects.isNull(itemDetail)){
                            MathUtil.sumQuantity(itemDetail.getRealQuantity(),x.getRealQuantity());
                            MathUtil.sumQuantity(itemDetail.getQuantity(),x.getQuantity());
                        } else {
                            detailHashMap.put(key,x);
                        }

                    });
                    item.setStockTakingListDetails(new ArrayList<>(detailHashMap.values()));
                }
            });
            list.addAll(takingItems);
        }
        // WMS info 处理
        wmsBuilderService.batchBuildWMSInfo(list);
        result.setList(list);
        return AjaxResult.getOK(result);
    }

    @Override
    public AjaxResult abnormalInStore(AbnormalRecord record, Integer type) {
        return null;
    }

    @Override
    public AjaxResult manualCloseStockTask(Integer stockTaskId,String closeReason, Integer type) {
        return null;
    }


    /**
     * 获取剩余批次，以及仓库库存信息
     * @param id
     * @param warehouseNo
     * @param pdName
     * @param sku
     * @return
     */
    private List<StockTakingItemVO> getTakingItemVO(Integer id,String pdName,String sku,Integer warehouseNo){

        //获取未盘点数据
        List<StockTakingItemVO> stockTakingItemVOS = stockTakingItemMapper.selectUnTakingItem(id, pdName, sku);

        //遍历获取批次数据信息
        for (StockTakingItemVO stockTakingItemVO : stockTakingItemVOS) {
            String querySku = stockTakingItemVO.getSku();
            //获取sku信息
            InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(querySku);
            AreaStore areaStoreKey = new AreaStore(warehouseNo, querySku);
            //获取仓库库存信息
            AreaStore areaStore = areaStoreMapper.selectWithOutDataPermission(areaStoreKey);
            //获取状态信息
            WarehouseStockExt warehouseStockExt = new WarehouseStockExt(warehouseNo, querySku);
            WarehouseStockExt  stockExt = warehouseStockExtService.selectWarehouseStockExt(warehouseStockExt);
            //获取剩余批次信息 库存数据 > 0
            List<StoreRecordVO> storeRecordVOS;
            storeRecordVOS = storeRecordMapper.selectLeftInStock(warehouseNo, querySku);

            //批次信息处理
            if(!CollectionUtils.isEmpty(storeRecordVOS)){
                List<StoreRecordVO> collect = storeRecordVOS.stream().filter(x -> x.getStoreQuantity() > 0).collect(Collectors.toList());
                //拼装数据
                List<StockTakingListDetail> details = BeanCopyUtil.copyListProperties(collect, StockTakingListDetail::new);
                stockTakingItemVO.setStockTakingListDetails(details);
            }

            stockTakingItemVO.setStoreQuantity(areaStore.getQuantity());
            stockTakingItemVO.setStatus(stockExt.getStatus());

            stockTakingItemVO.setNameRemakes(inventoryVO.getNameRemakes());
            stockTakingItemVO.setStorageLocation(inventoryVO.getStorageLocation());
            stockTakingItemVO.setExtType(inventoryVO.getExtType());
            stockTakingItemVO.setSkuType(inventoryVO.getType());
        }
        return stockTakingItemVOS;
    }


}
