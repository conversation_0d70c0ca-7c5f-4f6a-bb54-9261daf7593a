package net.summerfarm.service.impl;

import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.mapper.manage.MerchantLifecycleMapper;
import net.summerfarm.model.vo.FollowWhiteListVO;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.mapper.manage.FollowWhiteListMapper;
import net.summerfarm.model.domain.FollowWhiteList;
import net.summerfarm.service.FollowWhiteListService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ct
 * create at:  2019/7/23  1:49 PM
 */
@Service("followWhiteListService")
public class FollowWhiteListServiceImpl implements FollowWhiteListService {

    public static final Integer IS_WHITELIST  = 1;

    @Resource
    private FollowWhiteListMapper followWhiteListMapper;
    @Resource
    private MerchantLifecycleMapper merchantLifecycleMapper;



    @Override
    public AjaxResult save(FollowWhiteList followWhiteList) {
        if(followWhiteList == null || followWhiteList.getMId() == null){
            return  AjaxResult.getError("参数错误");
        }
        followWhiteList.setStatus(IS_WHITELIST);
        FollowWhiteList queryFollowWhite = followWhiteListMapper.queryFollowWhiteListOne(followWhiteList.getMId());
        if(queryFollowWhite != null){
          return  AjaxResult.getError("该用户已在白名单内");
        }
        followWhiteList.setGmtCreate(new Date());
        followWhiteList.setGmtModified(new Date());
        Integer integer = followWhiteListMapper.insertFollowWhite(followWhiteList);
        if(integer > 0){
            return AjaxResult.getOK();
        }
        return AjaxResult.getError();

    }

    @Override
    public AjaxResult update(Long mId) {
        FollowWhiteList queryFollowWhite = followWhiteListMapper.queryFollowWhiteListOne(mId);

        if(Objects.isNull(queryFollowWhite)){
            return AjaxResult.getError("该用户不在白名单内");
        }
        queryFollowWhite.setStatus(0);
        Integer integer = followWhiteListMapper.updateFollowWhite(queryFollowWhite);
        if(integer > 0){
            return AjaxResult.getOK();
        }
        return AjaxResult.getError();
    }

    @Override
    public AjaxResult queryWhiteList(int pageIndex, int pageSize, FollowWhiteListVO followWhiteListVO) {
        PageHelper.startPage(pageIndex, pageSize);
        List<FollowWhiteListVO> followWhiteLists = followWhiteListMapper.queryFollowWhiteList(followWhiteListVO);
        // 获取生命周期
        for (FollowWhiteListVO followWhiteList : followWhiteLists) {
            Integer integer = merchantLifecycleMapper.selectLast(followWhiteList.getMId());
            Integer lifeCycle = Objects.isNull(integer) ? NumberUtils.INTEGER_ZERO : integer;
            followWhiteList.setLifecycle(lifeCycle);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(followWhiteLists));
    }

    @Override
    public AjaxResult deleteWhiteList(Long mId) {
        if(mId == null){
            return AjaxResult.getError("mId不能为空");
        }
        FollowWhiteList queryFollowWhite = followWhiteListMapper.queryFollowWhiteListOne(mId);

        if(queryFollowWhite == null){
            AjaxResult.getError("该用户不在白名单内");
        }
        Integer integer = followWhiteListMapper.deleteFollowWhite(mId);
        if(integer > 0){
            return AjaxResult.getOK();
        }
        return AjaxResult.getError();
    }


}
