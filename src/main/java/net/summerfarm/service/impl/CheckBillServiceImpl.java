package net.summerfarm.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.PayChannelEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.Statement;
import net.summerfarm.model.vo.CompanyAccountVO;
import net.summerfarm.service.CheckBillService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.RoundingMode.HALF_UP;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CheckBillServiceImpl implements CheckBillService {
    public static final String STATEMENT_URL = "statementUrl";
    public static final String WX_ACCOUNT_IDS = "wxAccountIds";
    public static final String CMB_ACCOUNT_IDS = "cmbAccountIds";
    public static final String WX_PAY = "微信支付";
    public static final String MIN_PAY = "小程序支付";
    public static final String XIAN_CARD_PAY = "鲜沐卡";

    private final RestTemplate client = new RestTemplate();

    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private RefundMapper refundMapper;

    @Resource
    private PaymentMapper paymentMapper;

    @Resource
    private CompanyAccountMapper companyAccountMapper;

    @Resource
    private ConfigMapper configMapper;


    @Override
    public String checkBill() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("#### ")
                .append(" <font color=#0089FF>@所有人</font> 昨日对帐通知\n");

        // 微信帐号对帐
        Config config = configMapper.selectOne(WX_ACCOUNT_IDS);
        List<Integer> wxAccountIds = Arrays.stream(config.getValue().split(","))
                .map(Integer::valueOf).collect(Collectors.toList());
        log.info("微信支付公司id:{}", wxAccountIds);
        for (Integer accountId : wxAccountIds) {
            Statement localStatement = getLocalStatement(PayChannelEnum.WEIX.getDesc(), accountId);
            Statement remoteStatement = getRemoteStatement(PayChannelEnum.WEIX.getDesc(), accountId);
            CompanyAccountVO accountVO = companyAccountMapper.selectByPrimaryKey(accountId);
            buildPayMsg(stringBuilder, PayChannelEnum.WEIX, localStatement, remoteStatement, accountVO);
        }

        // 招行银行对账
        Config config1 = configMapper.selectOne(CMB_ACCOUNT_IDS);
        List<Integer> cmbAccountIds = Arrays.stream(config1.getValue().split(","))
                .map(Integer::valueOf).collect(Collectors.toList());
        log.info("招行支付公司id:{}", cmbAccountIds);
        for (Integer accountId : cmbAccountIds) {
            Statement localStatement = getLocalStatement(PayChannelEnum.CMB.getDesc(), accountId);
            Statement remoteStatement = getRemoteStatement(PayChannelEnum.CMB.getDesc(), accountId);
            CompanyAccountVO accountVO = companyAccountMapper.selectByPrimaryKey(accountId);
            buildPayMsg(stringBuilder, PayChannelEnum.CMB, localStatement, remoteStatement, accountVO);

        }
        buildXianmuCardMsg(stringBuilder, getLocalStatement(XIAN_CARD_PAY, null));
        sendMsgToRobot(stringBuilder);
        return stringBuilder.toString();
    }

    /**
     * 发送消息到钉钉
     *
     * @param stringBuilder 字符串
     */
    private void sendMsgToRobot(StringBuilder stringBuilder) {
        Config config = configMapper.selectOne(Global.CHECK_BILL_ROBOT_URL);
        String url = config.getValue();
        Map<String, String> jsonObject = new HashMap<>();
        jsonObject.put("title", "昨日对账");
        jsonObject.put("text", stringBuilder.toString());
        DingTalkRobotUtil.sendMsgAndAtAll(DingTalkRobotUtil.MARKDOWN, url, () -> jsonObject);
    }

    /**
     * 构建对账消息
     *
     * @param stringBuilder   消息体
     * @param cmb             支付类型
     * @param localStatement  本地账单
     * @param remoteStatement 远程账单
     * @param accountVO       帐号
     */
    private void buildPayMsg(StringBuilder stringBuilder, PayChannelEnum cmb, Statement localStatement, Statement remoteStatement, CompanyAccountVO accountVO) {
        stringBuilder
                .append(" > ###### ")
                .append(accountVO.getCompanyName())
                .append("通过")
                .append(cmb.getDesc())
                .append("\n >> ###### 订单金额：")
                .append(localStatement.getOrderAmount().setScale(2, HALF_UP))
                .append("元，")
                .append("\n >> ###### 本地应收：")
                .append(localStatement.getPayAmount().setScale(2, HALF_UP))
                .append("元，")
                .append("\n >> ###### 实收：")
                .append(remoteStatement.getPayAmount().setScale(2, HALF_UP));
        if (remoteStatement.getPayAmount().compareTo(localStatement.getPayAmount()) != 0) {
            stringBuilder.append("元，")
                    .append("\n>> ###### 实收差额：")
                    .append(localStatement.getPayAmount().subtract(remoteStatement.getPayAmount()).setScale(2, HALF_UP));
        }
        stringBuilder.append("元;");
        stringBuilder.append("\n>> ###### 应退款：")
                .append(localStatement.getRefundAmount().setScale(2, HALF_UP))
                .append("元，")
                .append("\n>> ###### 实退款：")
                .append(remoteStatement.getRefundAmount().setScale(2, HALF_UP));
        if (remoteStatement.getRefundAmount().compareTo(localStatement.getRefundAmount()) != 0) {
            stringBuilder.append("元，")
                    .append("\n>> ###### 退款差额：")
                    .append(localStatement.getRefundAmount().subtract(remoteStatement.getRefundAmount()).setScale(2, HALF_UP));
        }
        stringBuilder.append("元。\n\n");
    }

    /**
     * 构建鲜沐卡对账消息
     *
     * @param stringBuilder  消息体
     * @param localStatement 本地账单
     */
    private void buildXianmuCardMsg(StringBuilder stringBuilder, Statement localStatement) {
        stringBuilder
                .append("> ###### 通过鲜沐卡")
                .append("\n>> ###### 订单金额：")
                .append(localStatement.getOrderAmount())
                .append("元，")
                .append("\n>> ###### 收入：")
                .append(localStatement.getPayAmount())
                .append("元;")
                .append("\n>> ###### 退款：")
                .append(localStatement.getRefundAmount().setScale(2, HALF_UP))
                .append("元。\n\n");
    }

    /**
     * 获取本地账单
     *
     * @param payType   支付类型
     * @param accountId 支付帐号
     * @return 账单
     */
    private Statement getLocalStatement(String payType, Integer accountId) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(1).with(LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.now().minusDays(1).with(LocalTime.MAX);
        BigDecimal orderAmount;
        BigDecimal payAmount;
        BigDecimal refundAmount;
        if (PayChannelEnum.WEIX.getDesc().equals(payType)) {
            List<String> payTypes = Arrays.asList(WX_PAY, MIN_PAY);
            orderAmount = ordersMapper.selectTotalAmount(startTime, endTime, payTypes, accountId);
            payAmount = paymentMapper.selectPayAmount(startTime, endTime, payTypes, accountId);
            refundAmount = refundMapper.selectRefundAmount(startTime, endTime, payTypes, accountId);
        } else {
            orderAmount = ordersMapper.selectTotalAmount(startTime, endTime, Collections.singletonList(payType), accountId);
            payAmount = paymentMapper.selectPayAmount(startTime, endTime, Collections.singletonList(payType), accountId);
            refundAmount = refundMapper.selectRefundAmount(startTime, endTime, Collections.singletonList(payType), accountId);

        }
        Statement statement = new Statement();
        statement.setOrderAmount(orderAmount == null ? BigDecimal.ZERO : orderAmount);
        statement.setPayAmount(payAmount == null ? BigDecimal.ZERO : payAmount);
        statement.setRefundAmount(refundAmount == null ? BigDecimal.ZERO : refundAmount);
        log.info("getLocalStatement:{}", statement);
        return statement;
    }

    /**
     * 获取远程账单
     *
     * @param payType   支付类型
     * @param accountId 支付帐号
     * @return 账单
     */
    private Statement getRemoteStatement(String payType, Integer accountId) {
        Statement statement = new Statement();
        String date = getYesterdayDateStr();

        String url = configMapper.selectOne(STATEMENT_URL).getValue();
        String urlTemplate = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam("date", date)
                .queryParam("accountId", accountId)
                .queryParam("payType", payType)
                .encode()
                .toUriString();
        AjaxResult res = client.getForObject(urlTemplate, AjaxResult.class);
        if (res != null && res.getData() != null && AjaxResult.DEFAULT_SUCCESS.equals(res.getCode())) {
            Map<String, Double> data = (Map<String, Double>) res.getData();
            statement.setPayAmount(BigDecimal.valueOf(data.get("payAmount")));
            statement.setRefundAmount(BigDecimal.valueOf(data.get("refundAmount")));
        } else {
            log.error("getRemoteStatement failed!, res:{}, url:{}", res, urlTemplate);
            statement.setPayAmount(BigDecimal.ZERO);
            statement.setRefundAmount(BigDecimal.ZERO);
        }

        log.info("getRemoteStatement:{}", statement);
        return statement;
    }

    @NotNull
    private String getYesterdayDateStr() {
        return LocalDate.now().minusDays(1).format(DateTimeFormatter.BASIC_ISO_DATE);
    }

}
