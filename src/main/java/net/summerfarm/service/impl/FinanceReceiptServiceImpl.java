package net.summerfarm.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.*;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.ReceiptBillConfirmInfo;
import net.summerfarm.model.input.ReceiptBillConfirmInput;
import net.summerfarm.model.vo.BillInfoVo;
import net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO;
import net.summerfarm.model.vo.FinanceReceiptBillVO;
import net.summerfarm.model.vo.FinanceReceiptVO;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.AccountingPeriodOrderService;
import net.summerfarm.service.DingTalkService;
import net.summerfarm.service.FinanceReceiptService;
import net.summerfarm.service.RechargeService;
import net.summerfarm.service.finance.FinanceAccountingPeriodOrderService;
import net.summerfarm.service.finance.converter.ReceiptConverter;
import net.xianmu.bms.client.receipt.provider.FinanceReceiptCommandProvider;
import net.xianmu.bms.client.receipt.provider.FinanceReceiptQueryProvider;
import net.xianmu.bms.client.receipt.req.SaveReceiptReq;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2021年12月25日 20:21:00
 */
@Service
public class FinanceReceiptServiceImpl extends BaseService implements FinanceReceiptService {

    @Resource
    private FinanceReceiptMapper financeReceiptMapper;

    @Resource
    private FinanceReceiptBillMapper financeReceiptBillMapper;

    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;
    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;

    @Resource
    private DingTalkService dingTalkService;

    @Resource
    private FinanceAccountingPeriodOrderMapper financeAccountingPeriodOrderMapper;

    @Resource
    private FinanceReceiptBillFlowingWaterMapper financeReceiptBillFlowingWaterMapper;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private AccountingPeriodOrderService accountingPeriodOrderService;

    @Resource
    private FinanceAccountingPeriodOrderService periodOrderService;

    @Resource
    private DingTalkMsgSender dingTalkMsgSender;

    @Resource
    private FinanceBankFlowingWaterMapper financeBankFlowingWaterMapper;

    @Resource
    private FinanceReceiptVoucherMapper financeReceiptVoucherMapper;

    @DubboReference
    private FinanceReceiptCommandProvider receiptProvider;

    @Resource
    private FinanceReceiptQueryProvider receiptQueryProvider;

    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    private RechargeService rechargeService;

    /**
     * status字段
     */
    private static final String STATUS = "write_off_status";

    /**
     * id字段
     */
    private static final String ID = "id";

    @Override
    public AjaxResult listWriteOff(Integer pageIndex, Integer pageSize, FinanceReceiptVO financeReceiptVO) {
        PageHelper.startPage(pageIndex, pageSize);
        List<FinanceReceiptVO> financeReceiptList = financeReceiptMapper.listWriteOff(financeReceiptVO);

        for (FinanceReceiptVO financeReceipt : financeReceiptList) {
            if (Objects.equals(financeReceipt.getPayType(), FinanceReceiptPayTypeEnum.BILL.ordinal())) {
                // 查询收款单核销金额、其他金额
                FinanceReceiptBillVO queryAmount = new FinanceReceiptBillVO();
                queryAmount.setFinanceReceiptId(financeReceipt.getId());
                FinanceReceiptBillVO receiptBillAmount = financeReceiptBillMapper.countReceiptBillAmount(queryAmount);
                if (ObjectUtils.isEmpty(receiptBillAmount)) {
                    continue;
                }
                financeReceipt.setWriteOffAmount(receiptBillAmount.getWrittenOffAmount());
                financeReceipt.setOtherAmount(receiptBillAmount.getOtherAmount());
                if (ObjectUtils.isEmpty(financeReceipt.getReceiptAmount())) {
                    financeReceipt.setReceiptAmount(financeReceipt.getWriteOffAmount().subtract(financeReceipt.getOtherAmount()));
                }
            }
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(financeReceiptList));
    }

    @Override
    public AjaxResult detailWriteOff(Long id) {
        FinanceReceiptVO financeReceipt = financeReceiptMapper.selectByIdInfo(id);
        List<FinanceReceiptBillVO> financeReceiptBillList = financeReceiptBillMapper.selectByReceiptIdList(id);
        if (Objects.equals(financeReceipt.getPayType(), FinanceReceiptPayTypeEnum.BILL.ordinal())) {
            BigDecimal totalOtherAmount = BigDecimal.ZERO;
            for (FinanceReceiptBillVO receiptBill : financeReceiptBillList) {
                FinanceAccountingPeriodOrderVO periodOrder = financeAccountingPeriodOrderMapper.selectByIdInfo(receiptBill.getFinanceOrderId());
                // 收款单-账单未核销金额 = 应收总金额-核销金额
                receiptBill.setUnWrittenOffAmount(receiptBill.getReceivableAmount().subtract(periodOrder.getWriteOffAmount()));
                // 其他收款总金额
                totalOtherAmount = totalOtherAmount.add(receiptBill.getOtherAmount());
            }
            financeReceipt.setOtherAmount(totalOtherAmount);
        }
        Map<String, Object> resultMap = new HashMap<>(3);
        resultMap.put("receiptDetail", financeReceipt);
        resultMap.put("billList", financeReceiptBillList);
        if (!ObjectUtils.isEmpty(financeReceipt.getFinanceBankFlowingWaterId())) {
            FinanceBankFlowingWater flowingWater = financeBankFlowingWaterMapper.selectByPrimaryKey(financeReceipt.getFinanceBankFlowingWaterId());
            resultMap.put("flowingWater", flowingWater);
        }
        return AjaxResult.getOK(resultMap);
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public AjaxResult confirmWriteOff(FinanceReceiptVO financeReceiptVO) {

        // 收款单-账单信息
        FinanceReceiptBill financeReceiptBill = null;
        for (FinanceReceiptBillVO receiptBill : financeReceiptVO.getBillList()) {

            // 查询收款单账单信息
            FinanceReceiptBill queryReceiptBill = financeReceiptBillMapper.selectByPrimaryKey(receiptBill.getBillId());
            FinanceAccountingPeriodOrder queryPeriodOrder = financeAccountingPeriodOrderMapper.selectByIdInfo(queryReceiptBill.getFinanceOrderId());
            // 账单本次核销金额=账单收款金额+账单其他收款金额
            BigDecimal writtenOffAmount = queryReceiptBill.getReceiptAmount().add(queryReceiptBill.getOtherAmount());

            // 更新账期订单账单信息
            FinanceAccountingPeriodOrder periodOrder = new FinanceAccountingPeriodOrder();
            periodOrder.setId(queryReceiptBill.getFinanceOrderId());
            periodOrder.setWriteOffAmount(queryPeriodOrder.getWriteOffAmount().add(writtenOffAmount));
            // 账期订单账单-未核销金额=应收总额-已核销金额
            BigDecimal unWrittenOffAmount = (queryReceiptBill.getReceivableAmount().subtract(periodOrder.getWriteOffAmount()));
            if (unWrittenOffAmount.compareTo(new BigDecimal(BigInteger.ZERO)) == 0) {
                periodOrder.setReceiptStatus(ReceiptStatusEnum.RECEIVED.getId());
            } else {
                periodOrder.setReceiptStatus(ReceiptStatusEnum.PARTIAL_COLLECTION.getId());
            }
            financeAccountingPeriodOrderMapper.updateByPrimaryKeySelective(periodOrder);

            // 保存账期订单账单核销记录
            FinanceReceiptBillFlowingWater flowingWater = new FinanceReceiptBillFlowingWater();
            flowingWater.setFinanceReceiptId(financeReceiptVO.getId());
            flowingWater.setFinanceOrderId(queryReceiptBill.getFinanceOrderId());
            flowingWater.setType(ReceiptWriteOffStatusEnum.WRITTEN_OFF.getId());
            flowingWater.setReceivableAmount(queryReceiptBill.getReceivableAmount());
            flowingWater.setUnWrittenOffAmount(unWrittenOffAmount);
            flowingWater.setReceiptAmount(queryReceiptBill.getReceiptAmount().negate());
            if (Objects.nonNull(receiptBill.getOtherAmount())) {
                flowingWater.setOtherAmount(receiptBill.getOtherAmount().negate());
            }
            flowingWater.setCreator(getAdminName());
            financeReceiptBillFlowingWaterMapper.insertSelective(flowingWater);
        }

        // 更新收款单信息
        FinanceReceipt financeReceipt = new FinanceReceipt();
        financeReceipt.setId(financeReceiptVO.getId());
        financeReceipt.setWriteOffStatus(ReceiptWriteOffStatusEnum.WRITTEN_OFF.getId());
        financeReceipt.setUpdater(getAdminName());
        financeReceiptMapper.updateByPrimaryKeySelective(financeReceipt);

        //查询收款单信息
        FinanceReceiptVO queryReceipt = financeReceiptMapper.selectByIdInfo(financeReceiptVO.getId());

        //查询对应收款流水的状态是否需要修改
        if (!ObjectUtils.isEmpty(queryReceipt.getFinanceBankFlowingWaterId())) {
            FinanceBankFlowingWater financeBankFlowingWater = financeBankFlowingWaterMapper.selectByPrimaryKey(queryReceipt.getFinanceBankFlowingWaterId());
            //给销售发送审批通过的消息
            sendOfferMessage(queryReceipt, financeBankFlowingWater.getTradingTime());
            BigDecimal receivedAmount = financeReceiptMapper.selectByMoney(queryReceipt.getFinanceBankFlowingWaterId());
            FinanceBankFlowingWater flowingWater = new FinanceBankFlowingWater();
            flowingWater.setId(queryReceipt.getFinanceBankFlowingWaterId());
            flowingWater.setUpdater(getAdminName());
            if (receivedAmount.compareTo(BigDecimal.ZERO) > 0 && !Objects.equals(financeBankFlowingWater.getTransactionAmount().compareTo(receivedAmount), 0)) {
                //改为部分认领
                flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.PARTIAL_CLAIM.ordinal());
                financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);
                return AjaxResult.getOK();
            }
            if (receivedAmount.compareTo(BigDecimal.ZERO) > 0 && Objects.equals(financeBankFlowingWater.getTransactionAmount().compareTo(receivedAmount), 0)) {
                //改为全部认领
                flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.CLAIM_ALL.ordinal());
                financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);
                return AjaxResult.getOK();
            }
            //改为待认领
            flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.TO_BE_CLAIMED.ordinal());
            flowingWater.setPayType(null);
            financeBankFlowingWaterMapper.updateByPrimaryKey(flowingWater);
            return AjaxResult.getOK();
        }
        return AjaxResult.getOK();
    }


    /**
     * 核销单确认审核通知
     *
     * @param queryReceipt
     * @param localDateTime
     */
    private void sendOfferMessage(FinanceReceiptVO queryReceipt, String localDateTime) {
        //查询所属销售的钉钉对应信息
        if (queryReceipt.getSalerId()==null){
            return;
        }
        String result = Objects.equals(queryReceipt.getWriteOffStatus(), ReceiptWriteOffStatusEnum.WRITTEN_OFF.getId()) ? "通过" : Objects.equals(queryReceipt.getWriteOffStatus(), ReceiptWriteOffStatusEnum.RESCINDED.getId()) ? "撤销" : "驳回";
        StringBuilder content = new StringBuilder();

        String title = "核销单确认审核通知";
        StringBuilder text = new StringBuilder("##### " + title + "\n");
        text.append("> ###### 客户名称：").append(queryReceipt.getNameRemakes()).append("\n");
        text.append("> ###### 打款时间：").append(localDateTime).append("\n");
        text.append("> ###### 认领时间：").append(BaseDateUtils.localDateTimeToString(queryReceipt.getCreateTime())).append("\n");
        text.append("> ###### 审核人：").append(queryReceipt.getUpdater()).append("\n");
        text.append("> ###### 审核时间：").append(queryReceipt.getUpdateTime()).append("\n");
        text.append("> ###### 审核结果：").append(result).append("\n");
        text.append("> ###### 备注：").append(queryReceipt.getRemarks()).append("\n");
        text.append(content);
        DingTalkMsgReceiverIdBO msgBO =new DingTalkMsgReceiverIdBO();
        msgBO.setMsgType(DingTalkMsgTypeEnum.MARKDOWN.getType());
        msgBO.setText(text.toString());
        msgBO.setTitle(title);
        msgBO.setReceiverIdList(Collections.singletonList(Long.valueOf(queryReceipt.getSalerId())));
        dingTalkMsgSender.sendMessageWithFeiShu(msgBO);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult cancel(Long id) {
        fallbackCancel(id, ReceiptWriteOffStatusEnum.RESCINDED.getId());
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult cancel(List<ReceiptBillConfirmInfo> inputList) {
        for (ReceiptBillConfirmInfo input:inputList){
            FinanceAccountingPeriodOrderVO period = financeAccountingPeriodOrderMapper.selectByBillNumber(input.getSourceNo());
            // 账单本次核销金额:账单收款金额+账单其他收款金额
            BigDecimal writeOffAmount = period.getWriteOffAmount().subtract(input.getReceiptAmount());
            period.setWriteOffAmount(writeOffAmount);

        }
        return null;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult reject(Long id) {
        fallbackCancel(id, ReceiptWriteOffStatusEnum.REJECT.getId());
        return AjaxResult.getOK();
    }

    /**
     * 核销单撤销&核销
     *
     * @param id
     * @param writeOffStatus
     */
    private void fallbackCancel(Long id, Byte writeOffStatus) {
        FinanceReceiptVO queryReceipt = financeReceiptMapper.selectByIdInfo(id);
        List<FinanceReceiptBillVO> queryReceiptBillList = financeReceiptBillMapper.selectByReceiptIdList(id);
        for (FinanceReceiptBillVO queryReceiptBill : queryReceiptBillList) {
            // 查询收款单-账期订单账单
            FinanceAccountingPeriodOrderVO queryPeriodOrder = financeAccountingPeriodOrderMapper.selectByIdInfo(queryReceiptBill.getFinanceOrderId());
            FinanceReceiptBillFlowingWater flowingWater = new FinanceReceiptBillFlowingWater();

            if (ReceiptWriteOffStatusEnum.WRITTEN_OFF.getId().equals(queryReceipt.getWriteOffStatus())) {
                // 账单本次核销金额:账单收款金额+账单其他收款金额
                BigDecimal writtenOffAmount = queryReceiptBill.getReceiptAmount().add(queryReceiptBill.getOtherAmount());

                // 更新账期订单账单
                FinanceAccountingPeriodOrder periodOrder = new FinanceAccountingPeriodOrder();
                periodOrder.setId(queryReceiptBill.getFinanceOrderId());
                periodOrder.setWriteOffAmount(queryPeriodOrder.getWriteOffAmount().subtract(writtenOffAmount));
                if (periodOrder.getWriteOffAmount().compareTo(new BigDecimal(BigInteger.ZERO)) == 0) {
                    periodOrder.setReceiptStatus(ReceiptStatusEnum.UN_COLLECTED.getId());
                } else {
                    periodOrder.setReceiptStatus(ReceiptStatusEnum.PARTIAL_COLLECTION.getId());
                }
                financeAccountingPeriodOrderMapper.updateByPrimaryKeySelective(periodOrder);
                flowingWater.setUnWrittenOffAmount(queryReceiptBill.getReceivableAmount().subtract(periodOrder.getWriteOffAmount()));
            } else {
                flowingWater.setUnWrittenOffAmount(queryReceiptBill.getReceivableAmount().subtract(queryPeriodOrder.getWriteOffAmount()));
            }

            // 保存账期订单账单核销记录
            flowingWater.setFinanceReceiptId(queryReceiptBill.getFinanceReceiptId());
            flowingWater.setFinanceOrderId(queryReceiptBill.getFinanceOrderId());
            flowingWater.setType(writeOffStatus);
            flowingWater.setReceivableAmount(queryReceiptBill.getReceivableAmount());
            flowingWater.setReceiptAmount(queryReceiptBill.getReceiptAmount());
            flowingWater.setOtherAmount(queryReceiptBill.getOtherAmount());
            flowingWater.setCreator(getAdminName());
            financeReceiptBillFlowingWaterMapper.insertSelective(flowingWater);
        }
        // 更新收款单信息
        FinanceReceipt updateFinanceReceipt = new FinanceReceipt();
        updateFinanceReceipt.setWriteOffStatus(writeOffStatus);
        updateFinanceReceipt.setId(id);
        updateFinanceReceipt.setUpdater(getAdminName());
        financeReceiptMapper.updateByPrimaryKeySelective(updateFinanceReceipt);

        if (!ObjectUtils.isEmpty(queryReceipt.getFinanceBankFlowingWaterId())) {
            BigDecimal receivedAmount = financeReceiptMapper.selectByMoney(queryReceipt.getFinanceBankFlowingWaterId());
            FinanceBankFlowingWater financeBankFlowingWater = financeBankFlowingWaterMapper.selectByPrimaryKey(queryReceipt.getFinanceBankFlowingWaterId());
            //给销售发送审批驳回/撤销的消息
            sendOfferMessage(queryReceipt, financeBankFlowingWater.getTradingTime());
            FinanceBankFlowingWater flowingWater = new FinanceBankFlowingWater();
            flowingWater.setId(queryReceipt.getFinanceBankFlowingWaterId());
            flowingWater.setUpdater(getAdminName());
            if (receivedAmount.compareTo(BigDecimal.ZERO) > 0) {
                //改为部分认领
                flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.PARTIAL_CLAIM.ordinal());
                financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);
            } else {
                //改为待认领
                flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.TO_BE_CLAIMED.ordinal());
                flowingWater.setPayType(null);
                financeBankFlowingWaterMapper.updateByPrimaryKey(flowingWater);
            }
        }
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult saveReceipt(FinanceReceiptVO financeReceiptVOS) {

        //查询改收款流水是否已经被认领
        FinanceBankFlowingWater bankFlowingWater = financeBankFlowingWaterMapper.selectByPrimaryKey(financeReceiptVOS.getFinanceBankFlowingWaterId());
        if (!ObjectUtils.isEmpty(bankFlowingWater.getPayType()) && !Objects.equals(bankFlowingWater.getPayType(), financeReceiptVOS.getPayType())) {
            return AjaxResult.getErrorWithMsg("该收款流水已经有过认领，与当前认领类型不同,请认领相应的类型");
        }

        if (Objects.equals(bankFlowingWater.getClaimStatus(), FinanceBankFlowingWaterEnum.TO_BE_CLAIMED.ordinal())) {
            //未被认领更改收款流水生成的收款类型
            FinanceBankFlowingWater financeBankFlowingWater = new FinanceBankFlowingWater();
            financeBankFlowingWater.setId(financeReceiptVOS.getFinanceBankFlowingWaterId());
            financeBankFlowingWater.setPayType(0);
            financeBankFlowingWaterMapper.updateByPrimaryKeySelective(financeBankFlowingWater);
        }


        String key = "finance_receipt" + financeReceiptVOS.getFinanceReceiptVOList().get(0).getAdminId() + financeReceiptVOS.getFinanceReceiptVOList().get(0).getInvoiceId() + financeReceiptVOS.getFinanceReceiptVOList().get(0).getSalerId();
        Boolean tryLock = redisTemplate.opsForValue().setIfAbsent(key, key, 30, TimeUnit.SECONDS);
        if (!tryLock) {
            return AjaxResult.getErrorWithMsg("新增收款单操作频繁，请稍后重试!");
        }

        for (FinanceReceiptVO financeReceiptVO : financeReceiptVOS.getFinanceReceiptVOList()) {
            // 保存收款单
            financeReceiptVO.setBillNumber(financeReceiptVO.getBillList().size());
            financeReceiptVO.setCreatorId(getAdminId().longValue());
            financeReceiptVO.setCreator(getAdminName());
            financeReceiptVO.setPayType(financeReceiptVO.getPayType());
            if (ObjectUtils.isEmpty(financeReceiptVO.getReceiptAmount())) {
                BigDecimal reduce = financeReceiptVO.getBillList().stream().map(FinanceReceiptBillVO::getReceiptAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                financeReceiptVO.setReceiptAmount(reduce);
            }
            financeReceiptVO.setFinanceBankFlowingWaterId(financeReceiptVOS.getFinanceBankFlowingWaterId());
            financeReceiptMapper.insertSelective(financeReceiptVO);

            //打款日期
            FinanceReceiptVoucher financeReceiptVoucher = new FinanceReceiptVoucher();
            financeReceiptVoucher.setFinanceReceiptId(financeReceiptVO.getId());
            financeReceiptVoucher.setReceiptAmount(financeReceiptVO.getReceiptAmount());
            LocalDateTime customerPaymentTime = LocalDateTime.of(LocalDate.parse(bankFlowingWater.getTradingDay()), LocalTime.parse(bankFlowingWater.getTradingTime()));
            financeReceiptVoucher.setCustomerPaymentTime(customerPaymentTime);
            financeReceiptVoucherMapper.insert(financeReceiptVoucher);

            FinanceReceiptBill financeReceiptBill = null;
            for (FinanceReceiptBillVO bill : financeReceiptVO.getBillList()) {
                FinanceAccountingPeriodOrderVO periodOrder = financeAccountingPeriodOrderMapper.selectByIdInfo(bill.getBillId());
                FinanceAccountingPeriodOrderVO financeAccountingPeriodOrder = accountingPeriodOrderService.selectUnWrittenOffAmount(periodOrder);
                if (financeAccountingPeriodOrder.getUnWrittenOffAmount().compareTo(bill.getReceiptAmount()) == -1) {
                    return AjaxResult.getError("账期（" + periodOrder.getBillNumber() + "）收款金额不能大于未核销金额");
                }

                // 保存收款单-账单
                financeReceiptBill = new FinanceReceiptBill();
                financeReceiptBill.setFinanceOrderId(bill.getBillId());
                financeReceiptBill.setFinanceReceiptId(financeReceiptVO.getId());
                financeReceiptBill.setReceiptAmount(bill.getReceiptAmount());
                financeReceiptBill.setReceivableAmount(bill.getTotalAmountReceivable());
                financeReceiptBillMapper.insertSelective(financeReceiptBill);

                // 保存账期订单账单核销记录
                FinanceReceiptBillFlowingWater flowingWater = new FinanceReceiptBillFlowingWater();
                flowingWater.setFinanceReceiptId(financeReceiptVO.getId());
                flowingWater.setFinanceOrderId(bill.getBillId());
                flowingWater.setType((byte) ReceiptWriteOffStatusEnum.NOT_WRITTEN_OFF.getId());
                flowingWater.setReceivableAmount(financeAccountingPeriodOrder.getTotalAmountReceivable());
                flowingWater.setUnWrittenOffAmount(financeAccountingPeriodOrder.getUnWrittenOffAmount());
                flowingWater.setReceiptAmount(bill.getReceiptAmount().negate());
                flowingWater.setCreator(getAdminName());
                financeReceiptBillFlowingWaterMapper.insertSelective(flowingWater);
            }

        }
        BigDecimal receivedAmount = financeReceiptMapper.selectByMoney(financeReceiptVOS.getFinanceBankFlowingWaterId());
        FinanceBankFlowingWater flowingWater = new FinanceBankFlowingWater();
        flowingWater.setId(financeReceiptVOS.getFinanceBankFlowingWaterId());
        flowingWater.setUpdater(getAdminName());
        if (receivedAmount.compareTo(BigDecimal.ZERO) > 0 && Objects.equals(bankFlowingWater.getClaimStatus(), FinanceBankFlowingWaterEnum.TO_BE_CLAIMED.ordinal())) {
            //改为部分认领
            flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.PARTIAL_CLAIM.ordinal());
            financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);
        } else if (receivedAmount.compareTo(bankFlowingWater.getTransactionAmount()) == 0) {
            //改为全部认领
            flowingWater.setClaimStatus(FinanceBankFlowingWaterEnum.CLAIM_ALL.ordinal());
            financeBankFlowingWaterMapper.updateByPrimaryKeySelective(flowingWater);
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult detailsBillReceipt(Long id) {
        FinanceReceiptBillVO financeReceiptBillVO = new FinanceReceiptBillVO();
        // 账单应收总金额
        BigDecimal receivableAmount = BigDecimal.ZERO;
        // 账单已收款金额
        BigDecimal writtenOffAmount = BigDecimal.ZERO;
        // 账单未收款金额
        BigDecimal unWrittenOffAmount = BigDecimal.ZERO;

        // 根据账单id查询收款单的账单信息
        FinanceReceiptBillVO queryFinanceReceiptBill = new FinanceReceiptBillVO();
        queryFinanceReceiptBill.setFinanceOrderId(id);
        queryFinanceReceiptBill.setWriteOffStatus(ReceiptWriteOffStatusEnum.WRITTEN_OFF.getId());
        List<FinanceReceiptBillVO> financeReceiptBillList = financeReceiptBillMapper.selectByOrderIdList(queryFinanceReceiptBill);
        for (FinanceReceiptBillVO financeReceiptBill : financeReceiptBillList) {
            receivableAmount = financeReceiptBill.getReceivableAmount();
            writtenOffAmount = writtenOffAmount.add(financeReceiptBill.getWrittenOffAmount());
        }
        // 未收款金额
        unWrittenOffAmount = receivableAmount.subtract(writtenOffAmount);

        financeReceiptBillVO.setReceivableAmount(receivableAmount);
        financeReceiptBillVO.setWrittenOffAmount(writtenOffAmount);
        financeReceiptBillVO.setUnWrittenOffAmount(unWrittenOffAmount);
        financeReceiptBillVO.setReceiptList(financeReceiptBillList);
        return AjaxResult.getOK(financeReceiptBillVO);
    }

    @Override
    public AjaxResult billReceipt(Long id, Long financeBankFlowingWaterId) {

        FinanceReceiptVO financeReceiptList = financeReceiptMapper.selectByIdInfo(id);

        FinanceReceiptBillVO queryAmount = new FinanceReceiptBillVO();
        queryAmount.setFinanceReceiptId(financeReceiptList.getId());
        FinanceReceiptBillVO receiptBillAmount = financeReceiptBillMapper.countReceiptBillAmount(queryAmount);
        financeReceiptList.setWriteOffAmount(receiptBillAmount.getWrittenOffAmount());
        financeReceiptList.setOtherAmount(receiptBillAmount.getOtherAmount());
        List<FinanceReceiptBillVO> financeReceiptBilList = financeReceiptBillMapper.selectByReceiptIdList(financeReceiptList.getId());
        BigDecimal totalOtherAmount = BigDecimal.ZERO;
        for (FinanceReceiptBillVO receiptBill : financeReceiptBilList) {
            FinanceAccountingPeriodOrderVO periodOrder = financeAccountingPeriodOrderMapper.selectByIdInfo(receiptBill.getFinanceOrderId());
            // 收款单-账单未核销金额 = 应收总金额-核销金额
            receiptBill.setUnWrittenOffAmount(receiptBill.getReceivableAmount().subtract(periodOrder.getWriteOffAmount()));
            // 其他收款总金额
            totalOtherAmount = totalOtherAmount.add(receiptBill.getOtherAmount());
        }
        financeReceiptList.setOtherAmount(totalOtherAmount);
        financeReceiptList.setBillList(financeReceiptBilList);

        List<FinanceReceiptVO> financeReceiptVOList = new ArrayList<>();
        financeReceiptVOList.add(financeReceiptList);


        HashMap result = new HashMap(16);
        FinanceBankFlowingWater financeBankFlowingWater = financeBankFlowingWaterMapper.selectByPrimaryKey(financeBankFlowingWaterId);
        if (!ObjectUtils.isEmpty(financeBankFlowingWater)) {

            //已经认领的金额（待核销和已核销）
            BigDecimal receivedAmount = financeReceiptMapper.selectByMoney(financeBankFlowingWaterId);
            //待认领金额
            BigDecimal amountToBeCollected = financeBankFlowingWater.getTransactionAmount().subtract(receivedAmount);
            result.put("amountToBeCollected", amountToBeCollected);
            result.put("financeBankFlowingWater", financeBankFlowingWater);
        }

        result.put("detail", financeReceiptVOList);
        return AjaxResult.getOK(result);
    }

    @Override
    public void writeOffTimeoutTask() {
    }

    @Override
    public void sendConfirmMessage(DtsModel dtsModel) {

    }

    @Override
    public void claimBill(SaveReceiptInput input) {
        for (SaveReceiptDetailInput detailInput : input.getClaimList()) {
            for (SaveReceiptListInput detail : detailInput.getReceiptList()) {
                BigDecimal unWrittenOffAmount = periodOrderService.selectUnWrittenOffAmount(detail.getSourceNo());
                if(detail.getReceiptAmount().compareTo(unWrittenOffAmount) == 1){
                    throw new BizException("账期（" + detail.getSourceNo() + "）收款金额不能大于未核销金额");
                }
            }
        }
        saveReceipt(input);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditBill(ReceiptBillConfirmInput info) {
        for (ReceiptBillConfirmInfo input : info.getReceiptBillDetailEntities()) {
            FinanceAccountingPeriodOrder periodOrder = financeAccountingPeriodOrderMapper.selectByBillNumber(input.getSourceNo());
            if (periodOrder == null) {
                continue;
            }
            if (ReceiptWriteOffStatusEnum.WRITTEN_OFF.getId().equals(info.getWriteOffStatus())) {
                // 更新账期订单账单信息
                periodOrder.setWriteOffAmount(periodOrder.getWriteOffAmount().add(input.getReceiptAmount()));
                periodOrder.setReceiptStatus(getReceiptStatus(input.getSourceNo(), periodOrder.getWriteOffAmount()));
                periodOrder.setUpdater(info.getAuditor());
                periodOrder.setUpdateTime(info.getAuditTime());
            } else if (ReceiptWriteOffStatusEnum.REJECT.getId().equals(info.getWriteOffStatus())) {
                // 更新账单状态
                periodOrder.setReceiptStatus(getReceiptStatus(input.getSourceNo(), periodOrder.getWriteOffAmount()));
                //给销售发送审批驳回/撤销的消息
                sendAuditMessage(info);
            } else if (ReceiptWriteOffStatusEnum.RESCINDED.getId().equals(info.getWriteOffStatus())) {
                // 更新账期订单账单
                periodOrder.setWriteOffAmount(periodOrder.getWriteOffAmount().subtract(input.getReceiptAmount()));
                // 撤销账单收款金额为负数
                periodOrder.setReceiptStatus(getReceiptStatus(input.getSourceNo(), periodOrder.getWriteOffAmount()));
                //给销售发送审批驳回/撤销的消息
                sendAuditMessage(info);
            }
            financeAccountingPeriodOrderMapper.updateByPrimaryKeySelective(periodOrder);
            sendConfirmMessage(info);
        }
    }

    /**
     * 获取核销状态
     *
     * @param writeOffAmount 核销金额
     * @param billNo         账单编号
     * @return byte
     */
    public byte getReceiptStatus(String billNo, BigDecimal writeOffAmount) {
        // 剩余待认领金额 = 应收-已核销
        BigDecimal unWrittenOffAmount = periodOrderService.getReceivablesAmount(billNo).subtract(writeOffAmount);
        int flag = unWrittenOffAmount.compareTo(BigDecimal.ZERO);
        logger.info("核销状态更新:writeOffAmount:{},unWrittenOffAmount{},flag:{}",writeOffAmount,unWrittenOffAmount,flag);
        // 待认领金额为负数即为超额认领
        if (flag < 0) {
            return ReceiptStatusEnum.OVERAGE.getId();
        } else if (flag == 0) {
            return ReceiptStatusEnum.RECEIVED.getId();
            // 如果已认领金额为0 即未认领
        } else if (writeOffAmount.compareTo(BigDecimal.ZERO) == 0) {
            return ReceiptStatusEnum.UN_COLLECTED.getId();
        } else {
            return ReceiptStatusEnum.PARTIAL_COLLECTION.getId();
        }
    }

    public void sendConfirmMessage(ReceiptBillConfirmInput info) {
        if (!ReceiptWriteOffStatusEnum.WRITTEN_OFF.getId().equals(info.getWriteOffStatus())){
            return;
        }
        StringBuilder content = new StringBuilder();
        List<String> billNos = info.getReceiptBillDetailEntities().stream().map(ReceiptBillConfirmInfo::getSourceNo).collect(Collectors.toList());
        List<BillInfoVo> billInfoVos = periodOrderService.selectByBillNos(billNos);
        for (BillInfoVo billInfo : billInfoVos) {
            content.append("> ###### 账单时间：").append(billInfo.getBillCycle()).append("\n");
            if (ReceiptStatusEnum.RECEIVED.getId().equals(billInfo.getReceiptStatus())) {
                content.append("> ###### 当前账单状态为「已收款」。").append("\n");
            } else {
                content.append("> ###### 当前账单状态为「部分收款」，账单未收款金额为").append(billInfo.getUnWriteOffAmount()).append("元，请关注。").append("\n");
            }
        }

        String title = "对账单核销通知";
        StringBuilder text = new StringBuilder("##### " + title + "\n");
        text.append("> ###### 客户名称：").append(info.getNameRemakes()).append("\n");
        text.append("> ###### 工商名称：").append(info.getInvoiceTitle()).append("\n");
        text.append(content);

        DingTalkMsgReceiverIdBO msgBO = new DingTalkMsgReceiverIdBO();
        msgBO.setMsgType(DingTalkMsgTypeEnum.MARKDOWN.getType());
        msgBO.setText(text.toString());
        msgBO.setTitle(title);
        msgBO.setReceiverIdList(Collections.singletonList(Long.valueOf(info.getCreatorId())));
        dingTalkMsgSender.sendMessageWithFeiShu(msgBO);
    }

    /**
     * 核销单确认审核通知
     *
     * @param queryReceipt
     */
    private void sendAuditMessage(ReceiptBillConfirmInput queryReceipt) {
        //查询所属销售的钉钉对应信息
        String result = Objects.equals(queryReceipt.getWriteOffStatus(), ReceiptWriteOffStatusEnum.WRITTEN_OFF.getId()) ? "通过" : Objects.equals(queryReceipt.getWriteOffStatus(), ReceiptWriteOffStatusEnum.RESCINDED.getId()) ? "撤销" : "驳回";
        StringBuilder content = new StringBuilder();

        String title = "核销单确认审核通知";
        StringBuilder text = new StringBuilder("##### " + title + "\n");
        text.append("> ###### 客户名称：").append(queryReceipt.getNameRemakes()).append("\n");
        text.append("> ###### 打款时间：").append(queryReceipt.getTradingTime()).append("\n");
        text.append("> ###### 认领时间：").append(BaseDateUtils.localDateTimeToString(queryReceipt.getCreateTime())).append("\n");
        text.append("> ###### 审核人：").append(queryReceipt.getAuditor()).append("\n");
        text.append("> ###### 审核时间：").append(queryReceipt.getAuditTime()).append("\n");
        text.append("> ###### 审核结果：").append(result).append("\n");
        text.append("> ###### 备注：").append(queryReceipt.getRemarks()).append("\n");
        text.append(content);

        DingTalkMsgReceiverIdBO msgBO = new DingTalkMsgReceiverIdBO();
        msgBO.setMsgType(DingTalkMsgTypeEnum.MARKDOWN.getType());
        msgBO.setText(text.toString());
        msgBO.setTitle(title);
        msgBO.setReceiverIdList(Collections.singletonList(Long.valueOf(queryReceipt.getSalerId())));
        dingTalkMsgSender.sendMessageWithFeiShu(msgBO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void claimRecharge(SaveReceiptInput saveReceiptInput) {
        for (SaveReceiptDetailInput input : saveReceiptInput.getClaimList()) {
            Merchant merchant = merchantMapper.selectByPrimaryKey(input.getAdminId());
            String mname = merchant.getMname();
            if (ObjectUtils.isEmpty(merchant)){
                throw new BizException("门店不存在");
            }
            boolean isBigCustomer = "大客户".equals(merchant.getSize()) && !ObjectUtils.isEmpty(merchant.getDirect()) && Objects.equals(merchant.getDirect(), 1);
            if (isBigCustomer){
                throw new BizException("账期门店客户" + mname + "无充值功能!");
            }

            List<SaveReceiptListInput> receiptList = new ArrayList<>();
            SaveReceiptListInput receiptListInput = new SaveReceiptListInput();
            receiptListInput.setReceiptAmount(input.getReceiptAmount());
            receiptList.add(receiptListInput);
            input.setReceiptList(receiptList);
            // 生成充值单
            rechargeService.rechargeCardPurchase(input,saveReceiptInput.getFlowingWaterId());

        }
        saveReceipt(saveReceiptInput);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditRecharge(ReceiptBillConfirmInput input) {
        logger.info("管理员{}审批了{}充值申请，审批结果为{}", input.getAuditor(), JSONUtil.toJsonStr(input.getReceiptBillDetailEntities()), input.getWriteOffStatus());
        rechargeService.handle(input);

    }

    public void saveReceipt(SaveReceiptInput saveReceiptInput) {
        SaveReceiptReq req = ReceiptConverter.toReceiptReq(saveReceiptInput);
        req.setCreator(getAdminName());
        req.setCreatorId(getAdminId());
        DubboResponse<Void> res = null;
        if (saveReceiptInput.getAuditFlag() != null && saveReceiptInput.getAuditFlag()) {
            res = receiptProvider.saveAndAuditReceipt(req);
        } else {
            res = receiptProvider.saveReceipt(req);
        }
        if (!res.isSuccess()) {
            throw new BizException(res.getMsg());
        }
    }
}
