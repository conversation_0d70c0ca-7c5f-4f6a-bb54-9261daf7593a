package net.summerfarm.service.impl;


import cn.hutool.core.text.StrPool;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.taobao.api.ApiException;
import com.taobao.api.internal.toplink.embedded.websocket.util.StringUtil;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.client.req.process.ProcessAutoAuditInstanceRequest;
import net.summerfarm.common.constant.dingding.ProcessInstanceBizTypeEnum;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.DingTalkUtils;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.common.util.SpringContextUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.common.util.dingtalk.DingTalkUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.crm.client.dto.CrmBdOrgDTO;
import net.summerfarm.crm.client.provider.BdOrgQueryProvider;
import net.summerfarm.dingding.bo.*;
import net.summerfarm.dingding.enums.DingTalkAuditEnum;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.exception.DingdingProcessException;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.dingding.service.DingdingProcessInstanceService;
import net.summerfarm.enums.AdminAuthExtendEnum;
import net.summerfarm.enums.BizTypeEnum;
import net.summerfarm.enums.CommonStatus;
import net.summerfarm.enums.DirectPurchaseRechargeStatus;
import net.summerfarm.enums.OtherStockChangeTypeEnum;
import net.summerfarm.facade.process.ProcessFacade;
import net.summerfarm.mapper.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.model.DTO.LeaderInfoDTO;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.MerchantCancelReq;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.MerchantCancelVO;
import net.summerfarm.model.vo.MerchantVO;
import net.summerfarm.model.vo.OrderVO;
import net.summerfarm.service.*;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.model.vo.WarehouseInventoryMappingVO;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static net.summerfarm.enums.BdAreaConfigEnum.SaleRank.*;

/**
 * <AUTHOR> ct
 * create at:  2020/2/27  09:58
 */
@Service
public class DingTalkServiceImpl extends BaseService implements DingTalkService {
    @Resource
    ConfigMapper configMapper;
    @Resource
    AreaStoreMapper areaStoreMapper;
    @Resource
    AreaMapper areaMapper;
    @Resource
    InventoryMapper inventoryMapper;
    @Resource
    OrdersMapper ordersMapper;
    @Resource
    @Lazy
    AdminService adminService;
    @Resource
    @Lazy
    AreaStoreService areaStoreService;
    @Resource
    QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    PurchasesConfigService purchasesConfigService;
    @Resource
    DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    AdminAuthExtendMapper adminAuthExtendMapper;
    @Resource
    AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    PurchasesImprestMapper purchasesImprestMapper;
    @Resource
    PurchasesImprestRecordMapper purchasesImprestRecordMapper;
    @Resource
    PCMaxThresholdMapper pcMaxThresholdMapper;
    @Lazy
    @Resource
    private PurchasePlanCostChangeService purchasePlanCostChangeService;
    @Lazy
    @Resource
    private PurchasesBackService purchasesBackService;
    @Resource
    DirectPurchaseRechargeMapper directPurchaseRechargeMapper;
    @Lazy
    @Resource
    DirectPurchaseRechargeRecordService directPurchaseRechargeRecordService;
    @Resource
    AdminMapper adminMapper;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    DepartmentDetailsMapper departmentDetailsMapper;
    @Resource
    DepartmentStaffMapper departmentStaffMapper;
    @Resource
    FenceService fenceService;
    @Resource
    BaseService baseService;
    @Resource
    StockTaskMapper stockTaskMapper;
    @Resource
    private  CrmManageBdMapper crmManageBdMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    private FollowUpRelationService followUpRelationService;
    @Lazy
    @Resource
    private MerchantService merchantService;
    @Resource
    private DingdingProcessInstanceService dingdingProcessInstanceService;
    @Resource
    private ManageAreaService manageAreaService;
    @Resource
    private ProcessFacade processFacade;
    @DubboReference
    private BdOrgQueryProvider bdOrgQueryProvider;
    @Resource
    private MerchantCancelMapper merchantCancelMapper;

    private static final Logger logger = LoggerFactory.getLogger(DingTalkService.class);

    private static final String DEFAULT_SUBMITTER = "defaultFeiShuSubmitter";


    @Override
    public void createProcessInstance(String sku,Integer storeNo) {

        AreaStore areaStore = new AreaStore();
        areaStore.setAreaNo(storeNo);
        areaStore.setSku(sku);
        AreaStore queryStore = areaStoreMapper.selectOne(areaStore);
        InventoryVO inventoryVO = inventoryMapper.selectSkuType(sku);
        WarehouseStorageCenter center = warehouseStorageService.selectByWarehouseNo(storeNo);
        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/create");
        OapiProcessinstanceCreateRequest request = new OapiProcessinstanceCreateRequest();
        request.setAgentId(Long.valueOf(Global.AGENT_ID));
        request.setProcessCode(Global.STORE_QUANTITY_CODE);
        Config originator = configMapper.selectOne("originator");
        //默认为系统工作人
        request.setOriginatorUserId("5957642744985517013");
        if(originator != null && originator.getValue() != null){
            request.setOriginatorUserId(originator.getValue());
        }
        request.setDeptId(-1L);
        List<OapiProcessinstanceCreateRequest.FormComponentValueVo> formComponentValues = new ArrayList<OapiProcessinstanceCreateRequest.FormComponentValueVo>();
        OapiProcessinstanceCreateRequest.FormComponentValueVo vo0 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        vo0.setName("仓库编号");
        vo0.setValue(String.valueOf(center.getWarehouseNo()));
        OapiProcessinstanceCreateRequest.FormComponentValueVo vo1 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        vo1.setName("仓库");
        vo1.setValue(center.getWarehouseName());
        OapiProcessinstanceCreateRequest.FormComponentValueVo vo2 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        vo2.setName("商品信息");
        StringBuffer value = new StringBuffer();
        value.append(sku)
                .append("-")
                .append(inventoryVO.getPdName())
                .append("-")
                .append(inventoryVO.getWeight())
                .append("当前安全库存")
                .append(queryStore.getSafeQuantity())
                .append("件");
        vo2.setValue(value.toString());
        OapiProcessinstanceCreateRequest.FormComponentValueVo vo3 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        vo3.setName("安全库存释放数量");
        vo3.setValue("0");
        OapiProcessinstanceCreateRequest.FormComponentValueVo vo4 = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        vo4.setName("说明");
        vo4.setValue("以下商品虚拟库存不足，请查看是否可释放安全库存，可释放 0<填写数量<="+queryStore.getSafeQuantity());
        formComponentValues.add(vo0);
        formComponentValues.add(vo1);
        formComponentValues.add(vo4);
        formComponentValues.add(vo2);
        formComponentValues.add(vo3);

        request.setFormComponentValues(formComponentValues);
        String accessToken = DingTalkUtils.init().getToken();

        try {
            OapiProcessinstanceCreateResponse response = client.execute(request,accessToken);
            response.isSuccess();
        } catch (ApiException e) {
            logger.info("创建安全库存释放审批失败 err={}",e.getErrMsg());
        }
        return;
    }

    /**
     * 系统人发起应收账款审核审批实例 title,rechargeNum,picturePaths,remark为入参内容
     * @param title
     * @param rechargeNum
     * @param picturePaths
     * @param remark
     */
    @Override
    public void createAccountReceivableProcessInstance(String title, Double rechargeNum, JSONArray picturePaths, String remark, Integer rechargeId) {

        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/create");
        OapiProcessinstanceCreateRequest request = new OapiProcessinstanceCreateRequest();
        request.setAgentId(Long.valueOf(Global.AGENT_ID));
        request.setProcessCode(Global.ACCOUNT_RECEIVABLE);
        //给组件填入值
        List<OapiProcessinstanceCreateRequest.FormComponentValueVo> formComponentValues = new ArrayList<OapiProcessinstanceCreateRequest.FormComponentValueVo>();
        OapiProcessinstanceCreateRequest.FormComponentValueVo titleComponent = new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        titleComponent.setName("应收账款审批");
        titleComponent.setValue(title);
        OapiProcessinstanceCreateRequest.FormComponentValueVo  rechargeNoComponent= new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        rechargeNoComponent.setName("充值编号");
        rechargeNoComponent.setValue(rechargeId.toString());
        OapiProcessinstanceCreateRequest.FormComponentValueVo  rechargeNumComponent= new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        rechargeNumComponent.setName("收款金额");
        rechargeNumComponent.setValue(rechargeNum.toString());
        OapiProcessinstanceCreateRequest.FormComponentValueVo  picturePathsComponent= new OapiProcessinstanceCreateRequest.FormComponentValueVo();
        picturePathsComponent.setName("凭证");
        picturePathsComponent.setValue(picturePaths.toJSONString(0));
        if(Objects.nonNull(remark)){
            OapiProcessinstanceCreateRequest.FormComponentValueVo  remarkComponent= new OapiProcessinstanceCreateRequest.FormComponentValueVo();
            remarkComponent.setName("备注");
            remarkComponent.setValue(remark);
            formComponentValues.add(remarkComponent);
        }
        formComponentValues.add(titleComponent);
        formComponentValues.add(rechargeNoComponent);
        formComponentValues.add(rechargeNumComponent);
        formComponentValues.add(picturePathsComponent);

        request.setFormComponentValues(formComponentValues);
        Config originator = configMapper.selectOne("originator");
        //默认为系统工作人
        request.setOriginatorUserId("5957642744985517013");
        if(originator != null && originator.getValue() != null){
            request.setOriginatorUserId(originator.getValue());
        }
        request.setDeptId(-1L);

        String accessToken = DingTalkUtils.init().getToken();
        try {
            OapiProcessinstanceCreateResponse response = client.execute(request,accessToken);
            response.isSuccess();
        } catch (ApiException e) {
            logger.info("创建应收账款审批实例失败 err={}",e.getErrMsg());
        }

    }

    @Override
    public Map<String, String>  DingTalkSyncProcess(String signature,String timestamp,String nonce,JSONObject json) {
        try {

            DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Global.TOKEN,Global.EN_AES_KEY,Global.CORPID);
            //获取从encrypt解密出来的明文
            String encrypt = json.getString("encrypt");
            String plainText = dingTalkEncryptor.getDecryptMsg(signature, timestamp, nonce, encrypt);
            JSONObject jsonplainText = JSONObject.parseObject(plainText);
            String eventType = jsonplainText.getString("EventType");
            String type = jsonplainText.getString("type");
            String result =  jsonplainText.getString("result");
            //钉钉消息回调
            String res = "success";
            String ticket ="bpms_task_change";
            //审批流且审批结束
            logger.info("jsonplainText={}",jsonplainText);
            if(Objects.equals(eventType,ticket) && Objects.equals(type,"finish")){
                String processCode = jsonplainText.getString("processCode");
                String processInstanceId = jsonplainText.getString("processInstanceId");

                //同意
                if(Objects.equals(result,"agree")){
                    //备用金审核
                    if(Objects.equals(processCode,Global.IMPREST_CODE)){
                        imprestRecord(processInstanceId,PurchasesImprestRecord.STATUS_PASS);
                    }
                    if(Objects.equals(processCode,Global.STORE_QUANTITY_CODE)){
                        storeUpdateQuantity(processInstanceId);
                    }
                }
                //拒绝
                if(Objects.equals(result,"refuse")){
                    //备用金审核
                    if(Objects.equals(processCode,Global.IMPREST_CODE)){
                        imprestRecord(processInstanceId,PurchasesImprestRecord.STATUS_CLOSE);
                    }
                }

                if(Objects.equals(processCode, Global.COST_CHANGE_CODE)){
                    purchasePlanCostChangeService.handleDingTalkAudit(processInstanceId);
                } else if (Objects.equals(processCode, Global.PURCHASE_BACK_CODE)){
                    purchasesBackService.handleDingTalkAudit(processInstanceId);
                }

                //若是回调中的processCode为应收款的审核Code，则进入到审核处理中
                if(Objects.equals(processCode,Global.ACCOUNT_RECEIVABLE)){
                    rechargeHandle(processInstanceId);
                }
            }
            Map<String, String> jsonMap = null;

            // jsonMap是需要返回给钉钉服务器的加密数据包
            jsonMap = dingTalkEncryptor.getEncryptedMap(res, System.currentTimeMillis(), DingTalkUtil.getRandomStr(8));

            return jsonMap;
        } catch (Exception e) {
            logger.info("回调处理失败"+e.getMessage());
        }
        return null;
    }

    @Override
    public void addSyncEvent() {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/call_back/register_call_back");
        OapiCallBackRegisterCallBackRequest request = new OapiCallBackRegisterCallBackRequest();
        request.setUrl("https://devadmin." + Global.TOP_DOMAIN_NAME + "/admin/dingTalkSync");
        request.setAesKey(Global.EN_AES_KEY);
        request.setToken(Global.TOKEN);
        request.setCallBackTag(Arrays.asList("bpms_task_change"));
        String accessToken = DingTalkUtils.init().getToken();
        try {
            OapiCallBackRegisterCallBackResponse response = client.execute(request,accessToken);
            if(!response.isSuccess()){
                logger.info("新增事件失败 msg ={}",response.getErrmsg());
            }
        } catch (ApiException e) {
            logger.info("新增事件失败 errMsg={}",e.getErrMsg());
        }
    }

    /**
     * 处理仓库库存调整
     * @param processInstanceId
     */
    private void storeUpdateQuantity( String processInstanceId){

        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/get");
        OapiProcessinstanceGetRequest request = new OapiProcessinstanceGetRequest();
        request.setProcessInstanceId(processInstanceId);
        String accessToken = DingTalkUtils.init().getToken();
        try {
            OapiProcessinstanceGetResponse response = client.execute(request,accessToken);
            String result = response.getProcessInstance().getResult();
            if(response.isSuccess() && Objects.equals(result,"agree")){
                OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = response.getProcessInstance();
                List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstance.getFormComponentValues();
                OapiProcessinstanceGetResponse.TaskTopVo taskTopVo = null;
                List<OapiProcessinstanceGetResponse.TaskTopVo> tasks = processInstance.getTasks();
                for (OapiProcessinstanceGetResponse.TaskTopVo task : tasks) {
                    if(Objects.equals(task.getTaskResult(),"AGREE")){
                        taskTopVo = task;
                    }
                }
                if(taskTopVo == null){
                    logger.info("没有审批人");
                    return;
                }
                //获取仓库信息,改动数量,sku信息
                String areaName = "";
                String sku = "";
                Integer quantity = 0;
                String skuVO = "";

                for (OapiProcessinstanceGetResponse.FormComponentValueVo formComponentValue : formComponentValues) {

                    if(Objects.equals(formComponentValue.getName(),"仓库")){
                        areaName = formComponentValue.getValue();
                    }
                    if(Objects.equals(formComponentValue.getName(),"商品信息")){
                        String value = formComponentValue.getValue();
                        String[] split = value.split("-");
                        sku = split[0];
                        skuVO =formComponentValue.getValue();

                    }
                    if(Objects.equals(formComponentValue.getName(),"安全库存释放数量")){
                        try {
                            quantity = Integer.valueOf(formComponentValue.getValue());
                        } catch (Exception e) {
                            DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.TXT.getType(), taskTopVo.getUserid(), "安全库存释放数量只能是整数");
                            dingTalkMsgSender.sendMessage(dingTalkMsgBO);
                        }
                    }
                }

                Area area = areaMapper.selectByAreaName(areaName);
                AreaStore areaStore = new AreaStore();
                areaStore.setSku(sku);
                areaStore.setAreaNo(area.getAreaNo());
                AreaStore queryAreaStore = areaStoreMapper.selectOne(areaStore);
                Integer safeQuantity = queryAreaStore.getSafeQuantity();
                if(safeQuantity < 0){
                    return;
                }
                //更改数量小于0 或大于安全库存发送消息给审批的bd
                if(quantity <= 0 || quantity > safeQuantity ){

                    String userid = taskTopVo.getUserid();
                    String msg = "您提交的" + skuVO + ",释放安全库存数量为0，若实际需要释放，请进入后台操作";
                    if(quantity > safeQuantity){
                        msg = "您提交的" + skuVO + ",释放安全库存数量大于实际安全库存，操作失败，请重新进入后台操作";
                    }
                    DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.TXT.getType(), userid, msg);
                    dingTalkMsgSender.sendMessage(dingTalkMsgBO);
                    return;
                }
                quantity = safeQuantity - quantity;
                Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
                areaStoreService.updateSafeStockByStoreNo(quantity, sku, area.getAreaNo(), OtherStockChangeTypeEnum.SAFE_STOCK_CHANGE,null, recordMap);
                AdminAuthExtend adminAuthExtend = adminAuthExtendRepository.selectByUserId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), taskTopVo.getUserid());
                if(adminAuthExtend != null){
                    Admin select = adminService.select(adminAuthExtend.getAdminId());
                    if (!CollectionUtils.isEmpty(recordMap) && select != null) {
                        String realName = select.getRealname();
                        recordMap.forEach((key, record) -> {
                            record.setRecorder(realName);
                        });
                    }
                }
                quantityChangeRecordService.insertRecord(recordMap);
                purchasesConfigService.msgArrival(area.getAreaNo(),sku);
                logger.info("成功修改安全库存，审批人={}",taskTopVo.getUserid());
            }

        } catch (Exception e) {
            logger.info(e.getMessage());
        }
    }

    @Override
    public void sendBigOrder(String orderNo,String sku,String dpId){
        OrderVO orderVO = ordersMapper.selectByOrderyNo(orderNo);
        if (StringUtils.isEmpty(orderVO)){
            logger.info("订单:{}省心送冻结大单消息发送失败,未找到该订单",orderNo);
            return;
        }
        String mname = orderVO.getMname();
        Area area = areaMapper.selectByAreaNo(orderVO.getAreaNo());
        Integer storeNo = fenceService.selectStoreNoByAreaNo(area.getAreaNo());
        WarehouseInventoryMappingVO mappingVO = warehouseInventoryService.selectVoByUniqueIndex(storeNo, sku);
        String userIdList = null;

        StringBuffer msg = new StringBuffer("大单提醒:");
        if(StringUtils.isEmpty(sku)){
            return;
        }
        String[] dpIds = dpId.split(",");
        int i = 1;
        boolean sendMsg = false;
        for (String id : dpIds) {
            if (StringUtils.isEmpty(id)){
                logger.info("订单:{}省心送冻结大单消息发送失败,dpId为空",orderNo);
                return;
            }
            Integer deliverPlanId = NumberUtils.INTEGER_ZERO;
            try {
                deliverPlanId = Integer.valueOf(id);
            } catch (Exception e) {
                logger.info("订单:{}省心送冻结大单消息发送失败,dpId为{}",orderNo,id);
            }
            PCMaxThreshold pcMaxThreshold = pcMaxThresholdMapper.selectOneBySoreNo(mappingVO.getWarehouseNo(), sku);
            DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(deliverPlanId);
            if(deliveryPlan != null && pcMaxThreshold != null && deliveryPlan.getQuantity() > pcMaxThreshold.getThreshold()){
                InventoryVO inventoryVO = inventoryMapper.selectSkuType(sku);
                msg.append(i+".")
                        .append(mname + "-")
                        .append(orderNo + "-")
                        .append(inventoryVO.getPdName() + "-")
                        .append(inventoryVO.getWeight())
                        .append(",配送计划数量为" + deliveryPlan.getQuantity() + ",")
                        .append("配送时间:")
                        .append(deliveryPlan.getDeliveryTime())
                        .append(",剩余虚拟库存为");

                AreaStore areaStore = new AreaStore();
                areaStore.setAreaNo(mappingVO.getWarehouseNo());
                areaStore.setSku(sku);
                AreaStore queryAreaStore = areaStoreMapper.selectOne(areaStore);
                Integer onlineQuantity = queryAreaStore.getOnlineQuantity();
                msg.append(onlineQuantity + ";")
                        .append("\n");
                i++;
                sendMsg =true;
            }

        }

        if(Objects.equals(orderVO.getAreaNo(),1001)){
            userIdList = configMapper.selectOne(area.getAreaName()).getValue();
        }
        if(!Objects.equals(orderVO.getAreaNo(),1001)){
            Config config = configMapper.selectOne(mappingVO.getWarehouseName());
            if(config == null){
                return;
            }
            userIdList = config.getValue();
        }
        if(sendMsg){
            if (null == userIdList){
                return;
            }
            DingTalkMsgReceiverIdBO dingTalkMsgBO = new DingTalkMsgReceiverIdBO();
            dingTalkMsgBO.setReceiverIdList(Collections.singletonList(Long.valueOf(userIdList)));
            dingTalkMsgBO.setContent(msg.toString());
            dingTalkMsgBO.setMsgType(DingTalkMsgTypeEnum.TXT.getType());
            dingTalkMsgSender.sendMessageWithFeiShu(dingTalkMsgBO);
        }
        return;
    }

    @Override
    public void updateUrl(){
        DingTalkClient  client = new DefaultDingTalkClient("https://oapi.dingtalk.com/call_back/update_call_back");
        OapiCallBackUpdateCallBackRequest request = new OapiCallBackUpdateCallBackRequest();
        request.setUrl("https://admin." + Global.TOP_DOMAIN_NAME + "/admin/dingTalkSync");
        request.setAesKey(Global.EN_AES_KEY);
        request.setToken(Global.TOKEN);
        request.setCallBackTag(Arrays.asList("bpms_task_change"));
        String accessToken = DingTalkUtils.init().getToken();
        try {
            OapiCallBackUpdateCallBackResponse response = client.execute(request,accessToken);
            if(response.isSuccess()){
                logger.info("钉钉回调事件更新成功");
            }
        } catch (ApiException e) {
            logger.info("钉钉回调更新失败 err={}",e.getErrMsg());
        }
    }

    @Override
    public void dingTalkRobotLink(DingTalkLinkMsg dingTalkLinkMsg) {
        DingTalkClient client = new DefaultDingTalkClient(dingTalkLinkMsg.getRobotUrl());
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("link");
        OapiRobotSendRequest.Link link = new OapiRobotSendRequest.Link();
        link.setText(dingTalkLinkMsg.getTitle());
        link.setTitle(dingTalkLinkMsg.getText());
        link.setPicUrl("");
        link.setMessageUrl(dingTalkLinkMsg.getMessageUrl());
        request.setLink(link);
        try {
            OapiRobotSendResponse response = client.execute(request);
            if(!response.isSuccess()){
                logger.info("发送失败 e ={}",response.getErrmsg());
            }
        } catch (ApiException e) {

            logger.info("发送失败 e ={}",e.getErrMsg());
        }
    }


    private void imprestRecord(String processInstanceId,Integer status){
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/get");
        OapiProcessinstanceGetRequest request = new OapiProcessinstanceGetRequest();
        request.setProcessInstanceId(processInstanceId);
        String accessToken = DingTalkUtils.init().getToken();
        try {
            OapiProcessinstanceGetResponse response = client.execute(request,accessToken);
            Integer updateId = 0;
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = response.getProcessInstance();
            List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstance.getFormComponentValues();
            //获取仓库信息,改动数量,sku信息
            for (OapiProcessinstanceGetResponse.FormComponentValueVo formComponentValue : formComponentValues) {
                if(Objects.equals(formComponentValue.getName(),"备用金变更ID")){
                    updateId = Integer.valueOf(formComponentValue.getValue());
                }
            }
            OapiProcessinstanceGetResponse.TaskTopVo taskTopVo = null;
            List<OapiProcessinstanceGetResponse.TaskTopVo> tasks = processInstance.getTasks();
            for (OapiProcessinstanceGetResponse.TaskTopVo task : tasks) {
                if(Objects.equals(task.getTaskResult(),"AGREE") || Objects.equals(task.getTaskResult(),"REFUSE")){
                    taskTopVo = task;
                }
            }
            if(taskTopVo == null){
                logger.info("备用金审核没有审批人");
            }
            PurchasesImprestRecord record = purchasesImprestRecordMapper.queryById(updateId);
            if(!Objects.equals(record.getStatus(),PurchasesImprestRecord.STATUS_NEW)){
                return;
            }
            AdminAuthExtend adminAuthExtend = null;
            if(taskTopVo != null){
                adminAuthExtend = adminAuthExtendRepository.selectByUserId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), taskTopVo.getUserid());

            }
            Integer adminId = 0;
            String adminName = "钉钉审批";
            if(adminAuthExtend != null){
                Admin select = adminService.select(adminAuthExtend.getAdminId());
                adminId = select.getAdminId();
                adminName = select.getRealname();
            }
            PurchasesImprestRecord updateRecord = new PurchasesImprestRecord();
            updateRecord.setStatus(status);
            updateRecord.setId(updateId);
            updateRecord.setExamineId(adminId);
            updateRecord.setExamineName(adminName);
            updateRecord.setExamineTime(new Date());
            BigDecimal newAmount = record.getNewAmount();
            Integer imprestId = record.getImprestId();

            //通过 修改备用金额
            if(Objects.equals(status,PurchasesImprestRecord.STATUS_PASS)){
                PurchasesImprest updateImprest = new PurchasesImprest();
                updateImprest.setAmount(newAmount);
                updateImprest.setUpdateTime(new Date());
                updateImprest.setId(imprestId);
                purchasesImprestMapper.updateImprest(updateImprest);
            }
            purchasesImprestRecordMapper.updateRecord(updateRecord);

            return ;

        } catch (Exception e) {
            logger.info(e.getMessage());
        }
    }

    @Override
    public void dingTalkRobotTxtAT(String url, String txt, OapiRobotSendRequest.At at) {
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent(txt);
        request.setText(text);
        if (at != null) {
            request.setAt(at);
        }
        try {
            OapiRobotSendResponse response = client.execute(request);
        } catch (ApiException e) {
            logger.info("发送失败 e ={}",e.getErrMsg());
        }
    }

    @Override
    public void getDeptMsg(){
        setDeptStatus();
        List<Long> list = new ArrayList<>();
        list.add(1L);
        getDeptIdList(list);
    }

    /**
     * 重置部门表状态
     */
    private void setDeptStatus(){
        departmentDetailsMapper.updateDeptStatus();
    }

    /**
     * 获取子部门ID列表
     */
    @Override
    public void getDeptIdList(List<Long> plist) {
        for (Long pid : plist) {
            try {
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsubid");
                OapiV2DepartmentListsubidRequest req = new OapiV2DepartmentListsubidRequest();
                req.setDeptId(pid);
                String accessToken = DingTalkUtils.init().getToken();
                OapiV2DepartmentListsubidResponse rsp = client.execute(req, accessToken);
                List<Long> sList = rsp.getResult().getDeptIdList();
                for (Long aLong : sList) {
                    getDeptDetail(aLong);
                }
                if (sList.size() > 0){
                    getDeptIdList(sList);
                }
//                //如果子部门列表为空，说明此时的部门为最小分支，去获得部门员工详情信息
//                else if(CollectionUtils.isEmpty(sList)){
//                    getStaffDetails(pid);
//                }
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void getStatus() {
        departmentStaffMapper.updateByStaffStatus();
    }

    /**
     * 获取部门详情
     */
    @Override
    public void getDeptDetail(Long id) {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/get");
            OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
            req.setDeptId(id);
            String accessToken = DingTalkUtils.init().getToken();
            OapiV2DepartmentGetResponse rsp = client.execute(req, accessToken);
            OapiV2DepartmentGetResponse.DeptGetResponse result = rsp.getResult();
            //查询该部门id在数据库中是否存在，存在则更新数据，不存在则插入数据
            List<DepartmentDetails> departmentDet = departmentDetailsMapper.selectByName(id);
            DepartmentDetails departmentDetails = new DepartmentDetails();
            departmentDetails.setDeptId(result.getDeptId());
            departmentDetails.setName(result.getName());
            departmentDetails.setParentId(result.getParentId());
            departmentDetails.setCreateTime(new Date());
            if (departmentDet.size()>0){
                departmentDetails.setUpdateTime(new Date());
                departmentDetailsMapper.update(departmentDetails);
            }else if (CollectionUtils.isEmpty(departmentDet)){
                departmentDetailsMapper.insert(departmentDetails);
            }
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取部门员工详情信息
     * @param id
     */
    @Override
    public void getStaffDetails(Long id) {
        logger.info("*******************" + id);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        req.setDeptId(id);
        req.setCursor(0L);
        req.setSize(100L);
        String accessToken = DingTalkUtils.init().getToken();
        try {
            OapiV2UserListResponse rsp = client.execute(req, accessToken);
            List<OapiV2UserListResponse.ListUserResponse> list = rsp.getResult().getList();
            if (!CollectionUtils.isEmpty(list)){
                list.forEach(staffList -> {
                    String unionid = staffList.getUnionid();
                    DepartmentStaff departmentStaff = new DepartmentStaff();
                    departmentStaff.setDeptId(id);
                    departmentStaff.setUserId(staffList.getUserid());
                    departmentStaff.setUnionId(unionid);
                    departmentStaff.setName(staffList.getName());
                    departmentStaff.setMobile(staffList.getMobile());
                    departmentStaff.setLeader(staffList.getLeader());
                    departmentStaff.setStatus("1");
                    departmentStaff.setCreateTime(new Date());
                    //取出数据库数据与遍历数据对比
                    logger.info("id={},unionid={},name={}",id,unionid,staffList.getName());
                    List<DepartmentStaff> departmentStaffs = departmentStaffMapper.selectByStaff(id,unionid);
                    //List uniqueList =departmentStaffs.stream().distinct().collect(Collectors.toList());
                    logger.info("------size:" + departmentStaffs.size());
                    if (departmentStaffs.size() > 0){
                        departmentStaff.setUpdateTime(new Date());
                        departmentStaffMapper.updateByStaff(departmentStaff);
                    } else if (CollectionUtils.isEmpty(departmentStaffs)){
                        logger.info("******" + departmentStaffs.size());
                        //如果新数据在数据库中没有，则插入新数据
                        departmentStaffMapper.insert(departmentStaff);
                    }
                });}
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    /**
     * 员工信息录入
     */
    @Override
    public void getDeptById() {
        //查询部门id，通过遍历，调用部门id获取部门员工信息
        List<DepartmentDetails> departmentDetails = departmentDetailsMapper.selectByDept();
        departmentDetails.forEach(list->{
            logger.info(String.valueOf(list.getDeptId()));
            getStaffDetails(list.getDeptId());
        });
    }

    @Override
    public void getDingTalk() {
        getStatus();
        getDeptById();
    }

    @Override
    public void rechargeHandle(String processInstanceId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/get");
        OapiProcessinstanceGetRequest request = new OapiProcessinstanceGetRequest();
        request.setProcessInstanceId(processInstanceId);
        String accessToken = DingTalkUtils.init().getToken();

        try{
            OapiProcessinstanceGetResponse response = client.execute(request,accessToken);
            if(!Objects.equals(response.getErrcode(),0L)){
                logger.info("{}获取的response状态异常{}",response.getProcessInstance(),response.getErrcode());
            }
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = response.getProcessInstance();

            //该审批的实例的状态，只在为status = "COMPLETED" 时进行操作
            String status = processInstance.getStatus();
            if(Objects.deepEquals(status,"COMPLETED")){
                //从组件中获取rechargeId
                List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues =processInstance.getFormComponentValues();
                List<OapiProcessinstanceGetResponse.FormComponentValueVo> componentValuesList = formComponentValues.stream()
                        .filter(item -> Objects.deepEquals(item.getName(), "充值编号") && Objects.deepEquals(item.getComponentType(),"NumberField"))
                        .collect(Collectors.toList());
                if(!Objects.equals(componentValuesList.size(),1)){
                    logger.info("从componentValuesList={}获取充值编号内的值异常！", componentValuesList);
                }
                String rechargeIdStr = componentValuesList.get(0).getValue();
                Long rechargeId = Long.parseLong(rechargeIdStr);
                //审批结果
                Integer updateStatus = null;
                String result = processInstance.getResult();

                switch (result){
                    case "agree" :   updateStatus = DirectPurchaseRechargeStatus.SUCCESS.getStatus(); break;
                    case "refuse" :  updateStatus = DirectPurchaseRechargeStatus.FAIL.getStatus(); break;
                    default: updateStatus = DirectPurchaseRechargeStatus.WAIT_HANDLE.getStatus();
                }

                //获取审批人信息
                List<OapiProcessinstanceGetResponse.TaskTopVo> tasks = processInstance.getTasks();

                //或签：在审核通过后，有多个人，就有多少个task所以或签的审批，先筛选出与结果相符合的task,然后取endtime最早的一个
                List<OapiProcessinstanceGetResponse.TaskTopVo> taskTopList = tasks.stream().filter(item -> Objects.nonNull(item.getFinishTime()) && Objects.deepEquals(result.toLowerCase(), item.getTaskResult().toLowerCase()))
                        .collect(Collectors.toList());
                taskTopList.sort(Comparator.comparing(OapiProcessinstanceGetResponse.TaskTopVo::getFinishTime));
                String userid = taskTopList.get(0).getUserid();
                AdminAuthExtend adminAuthExtend = adminAuthExtendRepository.selectByUserId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), userid);
                String handler = "钉钉审批";
                if(Objects.nonNull(adminAuthExtend) ){
                    Integer adminId = adminAuthExtend.getAdminId();
                    Admin adminHandler = adminMapper.selectByPrimaryKey(adminId);
                    if(Objects.nonNull(adminHandler)){
                        handler = adminHandler.getRealname();
                    }
                }
                if(Objects.deepEquals(handler,"钉钉审批")){
                    logger.info("审核人不在后台系统表adminAuthExtend或是admin表中记录");
                }

                DirectPurchaseRecharge query = directPurchaseRechargeMapper.selectByPrimaryKey(rechargeId);
                if(Objects.isNull(query)){
                    logger.info("根据rechargeId查询出的query为NUll");
                }
                //当未发生过审核时在处理充值审核内容
                if(Objects.nonNull(query) && Objects.equals(query.getStatus(),DirectPurchaseRechargeStatus.WAIT_HANDLE.getStatus())){
                    DirectPurchaseRecharge updateRecharge = new DirectPurchaseRecharge();
                    updateRecharge.setProcessingtime(LocalDateTime.now());
                    updateRecharge.setHandler(handler);
                    updateRecharge.setId(rechargeId);
                    updateRecharge.setStatus(updateStatus);
                    directPurchaseRechargeMapper.updateByPrimaryKeySelective(updateRecharge);
                    logger.info("在钉钉流中{}对编号{}的审核结果是status:{}",handler,rechargeIdStr,updateStatus);
                    Map<String, String> userInfoMap = directPurchaseRechargeRecordService.changeChargeStatus(query, updateStatus);
                    //通知申请人
                    String title = userInfoMap.get("title");
                    String userId = userInfoMap.get("userId");
                    if(Objects.nonNull(title) && Objects.nonNull(userId)){
                        DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.TXT.getType(), userId, title);
                        dingTalkMsgSender.sendMessage(dingTalkMsgBO);
                    }else{
                        logger.info("在后台系统中无对应的申请人");
                    }
                }else{
                    logger.info("该审批已在后台审批过了");
                }
            }
        }catch (Exception exception) {
            logger.info("在获取{}后，处理过程中失败",processInstanceId);
        }

    }


    @Override
    public AjaxResult getByIdStaffDetails(Long id) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        req.setDeptId(id);
        req.setCursor(0L);
        req.setSize(100L);
        String accessToken = DingTalkUtils.init().getToken();
        try {
            OapiV2UserListResponse rsp = client.execute(req, accessToken);
            List<OapiV2UserListResponse.ListUserResponse> list = rsp.getResult().getList();
            return AjaxResult.getOK(list);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return AjaxResult.getOK("null");
    }

    @Override
    public String getManagerAndTitle(String userId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userId);
        req.setLanguage("zh_CN");
        String accessToken = DingTalkUtils.init().getToken();
        OapiV2UserGetResponse rsp = null;
        String res="";
        try {
            rsp = client.execute(req, accessToken);
            res = rsp.getResult().getTitle() + StringUtils.SEPARATING_SYMBOL + rsp.getResult().getManagerUserid();
            logger.info("钉钉获取直系主管和职位为{}",res);
            return res;
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void sendCargoDamageMsg(Integer taskId, String type, StringJoiner msg,Integer auditId) {

        if (StringUtils.isEmpty(msg)) {
            return;
        }

        StockTask stockTask = stockTaskMapper.selectByPrimaryKey(taskId);
        String adminName = Objects.isNull(auditId) ? baseService.getAdminName() : adminMapper.selectByAdminId(auditId).getRealname();
        Config config = configMapper.selectOne("cargo_damage_url");
        WarehouseStorageCenter center = warehouseStorageService.selectByWarehouseNo(stockTask.getAreaNo());

        logger.info("开始发送金额提醒");

        Map<String, String> md = new HashMap<>(2);
        md.put("title", type + "预警提醒");
        String sb = "#### " + type + "预警提醒 \n" +
                "> ###### 任务编号：" + taskId + "\n" +
                "> ###### 仓库名称：" + center.getWarehouseName() + "\n" +
                "> ###### 类型：" + type + "\n" +
                "> ###### 时间：" + LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtils.DEFAULT_DATE_FORMAT)) + "\n" +
                "> ###### 操作人：" + adminName + "\n" +
                "> ###### 内容：\n" +
                "> ###### " + msg.toString() + "\n" +
                "> ###### 请进入WMS系统搜索查看 "; // <font color=#0089FF>@所有人</font>
        md.put("text", sb);
        DingTalkRobotUtil.sendMsgAndAtAll(DingTalkRobotUtil.MARKDOWN, config.getValue(), () -> md);

    }

    @Override
    public LeaderInfoDTO getManagerInfo(String userId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userId);
        req.setLanguage("zh_CN");
        String accessToken = DingTalkUtils.init().getToken();
        OapiV2UserGetResponse rsp = null;
        LeaderInfoDTO leaderInfoDTO = new LeaderInfoDTO();
        try {
            rsp = client.execute(req, accessToken);
            String title = rsp.getResult().getTitle();
            String managerId = rsp.getResult().getManagerUserid();
            leaderInfoDTO.setTitle(title);
            leaderInfoDTO.setManagerId(managerId);
            logger.info("钉钉获取用户：{}的直系主管为:{},职位为:{}", userId, managerId, title);
            return leaderInfoDTO;
        } catch (Exception e) {
            logger.error("钉钉获取用户：{}的直系主管失败", e);
        }
        return null;
    }

    @Override
    public void createMerchantCancel(Long id) {
        logger.info("DingTalkServiceImpl[]createMerchantCancel[]start[]id:{}", id);
        ProcessInstanceCreateBO processInstanceCreateBO = new ProcessInstanceCreateBO();

        //查询门店记录详情
        MerchantCancel merchantCancel = merchantCancelMapper.selectByPrimaryKeyForMaster(id);
        if (Objects.isNull(merchantCancel)) {
            logger.warn("DingTalkServiceImpl[]createMerchantCancel[]error[]merchant cancel not exist id:{}", id);
            throw new BizException("门店注销记录不存在！");
        }

        //门店信息详情
        AjaxResult ajaxResult = merchantService.selectOne(merchantCancel.getMId());
        MerchantVO merchantVO = (MerchantVO) ajaxResult.getData();
        if (Objects.isNull(merchantVO)) {
            logger.error("DingTalkServiceImpl[]createMerchantCancel[]error[]merchant not exist id:{}", id);
            return;
        }

        //地推人员信息
        FollowUpRelation upRelation = followUpRelationService.getInfoByMid(merchantVO.getmId());

        //拼接用户注册地址
        StringBuilder address = new StringBuilder();
        address.append(merchantVO.getProvince()).append(merchantVO.getCity())
                .append(merchantVO.getArea()).append(merchantVO.getAddress());

        //获取运营区域信息
        Area area = areaMapper.selectByAreaNo(merchantVO.getAreaNo());

        //1、发送至当前门店的对应BD以及BD的主管M1并抄送M2    2、若当前门店无BD时，则发送至当前注册城市的 M1 并抄送 M2
        StringBuilder approvers = new StringBuilder();
        String ccList = null;
        String adminRealName;
        if (Objects.isNull(upRelation) || upRelation.getReassign()) {
            DubboResponse<List<CrmBdOrgDTO>> resp = bdOrgQueryProvider.selectBdOrgCity(merchantVO.getProvince(), merchantVO.getCity(), merchantVO.getArea(), Boolean.FALSE);
            if (!resp.isSuccess()) {
                logger.info("获取主管失败:{}", JSONUtil.toJsonStr(resp));
                throw new BizException("获取主管失败");
            }
            /*if (resp.getData().isEmpty()){
                logger.info("{}直属上级为空",upRelation.getAdminId());
            }*/
            for (CrmBdOrgDTO org : resp.getData()) {
                if (CITY_MANAGER == org.getRank()) {
                    approvers.append(org.getBdId());
                } else if (AREA_MANAGER == org.getRank()) {
                    ccList = String.valueOf(org.getBdId());
                }
            }
            //地推人员
            adminRealName = "默认邀请码";
        } else {
            approvers.append(upRelation.getAdminId());
            DubboResponse<List<CrmBdOrgDTO>> resp = bdOrgQueryProvider.selectBdOrgByBdId(upRelation.getAdminId(), Boolean.FALSE);
            if (!resp.isSuccess()) {
                logger.info("获取主管失败:{}", JSONUtil.toJsonStr(resp));
                throw new BizException("获取主管失败");
            }
            if (resp.getData().isEmpty()){
                logger.info("{}直属上级为空",upRelation.getAdminId());
            }
            for (CrmBdOrgDTO org : resp.getData()) {
                if (CITY_MANAGER == org.getRank()) {
                    approvers.append(StrPool.COMMA).append(org.getBdId());
                } else if (AREA_MANAGER == org.getRank()) {
                    ccList = String.valueOf(org.getBdId());
                }
            }
            adminRealName = upRelation.getAdminName();
        }

        if (StringUtils.isBlank(approvers)) {
            logger.error("DingTalkServiceImpl[]createMerchantCancel[]error[]approvers is null id:{}", id);
            return;
        }

        //创建审批表单
        List<DingdingFormBO> fromList = new ArrayList<>(11);
        fromList.add(new DingdingFormBO("标题", "门店注销申请"));
        fromList.add(new DingdingFormBO("运营区域", Objects.isNull(area) ? merchantVO.getArea() : area.getAreaName()));
        fromList.add(new DingdingFormBO("门店名称", merchantVO.getMname()));
        fromList.add(new DingdingFormBO("门店ID", String.valueOf(merchantVO.getmId())));
        fromList.add(new DingdingFormBO("注销原因", merchantCancel.getRemake()));
        fromList.add(new DingdingFormBO("联系人", merchantVO.getMcontact()));
        fromList.add(new DingdingFormBO("手机号", merchantVO.getPhone()));
        fromList.add(new DingdingFormBO("注册地址", address.toString()));
        fromList.add(new DingdingFormBO("地推人员", adminRealName));
        fromList.add(new DingdingFormBO("门店类型", merchantVO.getSize()));
        fromList.add(new DingdingFormBO("备注", "当前注销将于24小时后完成注销，如有需要可联系客户了解详细原因"));

        Config config = configMapper.selectOne(DEFAULT_SUBMITTER);
        if (Objects.isNull(config)) {
            logger.error("DingTalkServiceImpl[]createMerchantCancel[]error[]config is null id:{}", id);
            return;
        }
        processInstanceCreateBO.setAdminId(Integer.valueOf(config.getValue()));
        //组装审批信息
        processInstanceCreateBO.setBizId(id);
        processInstanceCreateBO.setApprovers(approvers.toString());
        processInstanceCreateBO.setCcList(ccList);

        processInstanceCreateBO.setBizTypeEnum(ProcessInstanceBizTypeEnum.MERCHANT_CANCEL_AUDIT);
        processInstanceCreateBO.setDingdingForms(fromList);
        try {
            //发起审批
            dingdingProcessInstanceService.createProcessInstance(processInstanceCreateBO);

            //改成飞书逻辑
            ProcessAutoAuditInstanceRequest request = new ProcessAutoAuditInstanceRequest();

            //添加睡眠3s --防止人员离职了 立即审批失败
            Thread.sleep(3000);
            request.setBizId(id);
            request.setProcessType(ProcessInstanceBizTypeEnum.MERCHANT_CANCEL_AUDIT.getBizType());
            request.setResult(DingTalkAuditEnum.AGREE.getValue());
            processFacade.autoAudit(request);

            //获取审批详情--老的钉钉逻辑
            //GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult processInfo = dingdingProcessInstanceService.getProcessInfo(processInstance.getProcessInstanceId());
            //自动审批--老的钉钉逻辑
            /*processInfo.getTasks().stream().forEach(e -> {
                ProcessAuditBO processAuditBO = new ProcessAuditBO();
                processAuditBO.setRemark("自动通过，仅提醒");
                processAuditBO.setResult(DingTalkAuditEnum.AGREE.getValue());
                processAuditBO.setProcessInstanceId(processInstance.getProcessInstanceId());
                processAuditBO.setTaskId(e.getTaskId());
                processAuditBO.setActionerUserId(e.getUserId());
                dingdingProcessInstanceService.processAudit(processAuditBO);
            });*/
        } catch (DingdingProcessException | InterruptedException e) {
            logger.error("DingTalkServiceImpl[]createMerchantCancel[]createProcessInstance[]error[]cause:{}", e.getMessage(), e);
            throw new BizException("门店注销发起审批异常:" + e.getMessage());
        }
        logger.info("DingTalkServiceImpl[]createMerchantCancel[]end[]id:{}", id);
    }
}
