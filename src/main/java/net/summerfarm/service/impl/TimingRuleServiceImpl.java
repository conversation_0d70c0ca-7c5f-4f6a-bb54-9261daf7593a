package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.BeanCopyUtil;
import net.summerfarm.common.util.DateUtil;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.TimingRuleEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.DTO.AreaInfoDTO;
import net.summerfarm.model.DTO.TimingOrderRefundWhiteListDTO;
import net.summerfarm.model.DTO.TimingWhiteImportDTO;
import net.summerfarm.model.bo.price.PriceInfoBO;
import net.summerfarm.model.converter.TimingDeliveryConverter;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.TimingOrderRefundWhiteListDeleteReq;
import net.summerfarm.model.input.TimingOrderRefundWhiteListInitReq;
import net.summerfarm.model.input.TimingOrderRefundWhiteListInsertReq;
import net.summerfarm.model.input.TimingOrderRefundWhiteListPageQuery;
import net.summerfarm.model.input.TimingOrderRefundWhiteListUploadReq;
import net.summerfarm.model.input.market.timing.BatchUpdateInput;
import net.summerfarm.model.vo.OrderVO;
import net.summerfarm.model.vo.TimingOrderVO;
import net.summerfarm.model.vo.TimingRuleVO;
import net.summerfarm.repository.price.CycleInventoryCostRepository;
import net.summerfarm.service.PriceService;
import net.summerfarm.service.TimingRuleService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.oss.common.util.OssGetUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * @Package: net.summerfarm.service.impl
 * @Description: 省心送规则业务
 * @author: <EMAIL>
 * @Date: 2017/2/6
 */
@Slf4j
@Service
public class TimingRuleServiceImpl extends BaseService implements TimingRuleService {
    @Resource
    private TimingRuleMapper timingRuleMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private CycleInventoryCostRepository cycleInventoryCostRepository;

    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private CycleInventoryCostMapper cycleInventoryCostMapper;
    @Resource
    private PriceService priceService;

    @Resource
    private TimingOrderRefundWhiteListMapper timingOrderRefundWhiteListMapper;

    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private OrderItemMapper orderItemMapper;

    @Resource
    private TimingOrderRefundRecordMapper timingOrderRefundRecordMapper;

    @Resource
    private TimingOrderRefundTimeMapper timingOrderRefundTimeMapper;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private TimingOrderMapper timingOrderMapper;

    /**
     * 最大N值
     */
    private static final Integer MAX_PLUS_DAY = 20;

    /**
     * 配送周期
     */
    private static final Integer MAX_PERIOD_DAY = 91;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Integer> saveTimingRule(TimingRuleVO timingRuleVO) {
        //基础校验
        checkBefore(timingRuleVO);
        //配送周期强制设置为91天
        if (timingRuleVO.getDeliveryPeriod() == null) {
            timingRuleVO.setDeliveryPeriod(MAX_PERIOD_DAY);
        }

        //areaNos可能来源于城市分组，其中可能存在失效的城市
        List<Integer> areaNos = timingRuleVO.getAreaNoList();
        String timingSku = timingRuleVO.getTimingSku();
        //先查询是否有开启中的省心送
        List<TimingRule> timingRules = timingRuleMapper.listByAreaNos(areaNos,
                timingSku, timingRuleVO.getType());
        List<AreaInfoDTO> areaInfoList = areaMapper.listByAreaNos(areaNos);
        Map<Integer, String> areaMap = areaInfoList.stream()
                .collect(Collectors.toMap(AreaInfoDTO::getAreaNo, AreaInfoDTO::getAreaName));
        if (CollectionUtil.isNotEmpty(timingRules)) {
            String repeatAreaNames = timingRules.stream().map(x -> areaMap.get(x.getAreaNo()))
                    .collect(Collectors.joining(","));
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, repeatAreaNames + " 已展示该商品该类型省心送,同一城市,同一商品,同一类型只可展示一条");
        }

        List<AreaSku> areaSkus = areaSkuMapper.selectBySkuAndAreaNos(timingSku, areaNos);

        List<Integer> filterAreaNos = areaSkus.stream().map(x -> x.getAreaNo()).distinct()
                .collect(Collectors.toList());
        List<Integer> copyAreaNos = Lists.newArrayList(areaNos);
        copyAreaNos.removeAll(filterAreaNos);
        //过滤掉失效的运营城市
        List<Integer> inValidAreaNos = copyAreaNos.stream().filter(x -> areaMap.get(x) == null)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(inValidAreaNos)) {
            copyAreaNos.removeAll(inValidAreaNos);
            log.warn("创建省心送商品,已过滤失效的运营城市,areaNos:{}", inValidAreaNos);
        }
        if (CollectionUtil.isNotEmpty(copyAreaNos)) {
            String noPriceAreaNames = copyAreaNos.stream().map(x -> areaMap.get(x))
                    .collect(Collectors.joining(","));
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, noPriceAreaNames + "没有设置本商品价格");
        }
        //批量保存省心送规则
        timingRuleMapper.insertBatch(timingRuleVO);
        return CommonResult.ok(timingRuleVO.getId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updateTimingRule(TimingRuleVO timingRuleVO) {
        TimingRuleVO oldTimingRule = timingRuleMapper.selectById(timingRuleVO.getId());
        if (oldTimingRule == null) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST);
        }
        //基础校验
        checkBefore(timingRuleVO);
        checkAreaTimingRuleUniqueness(timingRuleVO);

        timingRuleMapper.updateByPrimaryKey(timingRuleVO);
        return CommonResult.ok(Boolean.TRUE);
    }

    @Override
    public CommonResult<PageInfo<TimingRuleVO>> selectTimingRule(TimingRuleVO selectKeys) {
        PageHelper.startPage(selectKeys.getPageIndex(),selectKeys.getPageSize());
        List<TimingRuleVO> timingRules = timingRuleMapper.select(selectKeys);
        return CommonResult.ok(PageInfoHelper.createPageInfo(timingRules));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updatePriority(Integer id,Integer priority){
        timingRuleMapper.updatePriority(id,priority);
        return CommonResult.ok(Boolean.TRUE);
    }

    @Override
    public CommonResult<TimingRuleVO> selectDetail(Integer id) {
        TimingRuleVO timingRuleVO = timingRuleMapper.selectById(id);
        // 查询城市售卖数据
        AreaSku areaSku = areaSkuMapper.selectByAreaNoAndSku(timingRuleVO.getAreaNo(), timingRuleVO.getTimingSku());
        if(Objects.nonNull(areaSku)){
            PriceInfoBO priceInfo = priceService.getNormalPrice(areaSku);
            timingRuleVO.setPrice(priceInfo.getPrice());
        }

        Integer warehouseNo = inventoryMapper.selectWarehouseNo(timingRuleVO.getTimingSku(), timingRuleVO.getAreaNo());
        if (Objects.nonNull(warehouseNo)){
            CycleInventoryCost cycleInventoryCost = cycleInventoryCostRepository.selectBySku(timingRuleVO.getTimingSku(), warehouseNo);
            if (Objects.nonNull(cycleInventoryCost)){
                timingRuleVO.setCostPrice(cycleInventoryCost.getFirstCycleCost());
            }
        }
        return CommonResult.ok(timingRuleVO);
    }

    private void checkAreaTimingRuleUniqueness(TimingRuleVO timingRuleVO){
        TimingRuleVO timingRuleQuery = new TimingRuleVO();
        BeanCopyUtil.copyProperties(timingRuleVO,timingRuleQuery);
        timingRuleQuery.setDisplay(TimingRuleEnum.DisplayStatus.DISPLAY.ordinal());
        List<TimingRule> timingRules  = timingRuleMapper.selectTimingRuleList(timingRuleQuery);
        if (CollectionUtil.isNotEmpty(timingRules)){
            throw new BizException(timingRuleVO.getAreaName() + "已展示该商品该类型省心送,同一城市,同一商品,同一类型只可展示一条");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updateDisPlay(String timingRuleIdsStr ,Integer disPlay){
        if(StringUtils.isEmpty(timingRuleIdsStr)){
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "没有选中的省心送");
        }
        List<Integer> ruleIds = Arrays.asList(timingRuleIdsStr.split(Global.SEPARATING_SYMBOL))
                .stream().map(Integer::valueOf).collect(Collectors.toList());

        List<TimingRule> timingRules = timingRuleMapper.listByIds(ruleIds);
        checkNotExist(ruleIds, timingRules);

        Map<Integer, String> areaMap = Maps.newHashMap();
        //如果是批量开启的需要做下面的校验
        if (Objects.equals(disPlay, TimingRuleEnum.DisplayStatus.DISPLAY.ordinal())) {
            Map<String, List<TimingRule>> timingRuleMap = timingRules.stream()
                    .collect(Collectors.groupingBy(x -> x.getTimingSku() + ":" + x.getAreaNo() + ":" + x.getType()));
            String repeatRuleIds = timingRuleMap.entrySet().stream().filter(x -> x.getValue().size() > 1)
                    .findFirst().map(x -> x.getValue().stream().map(t -> t.getId().toString())
                            .collect(Collectors.joining(","))).orElse(null);
            if (StringUtils.isNotBlank(repeatRuleIds)) {
                throw new BizException("不能同时开启：" + repeatRuleIds);
            }
            List<Integer> areaNos = timingRules.stream().map(x -> x.getAreaNo()).distinct()
                    .collect(Collectors.toList());
            List<AreaInfoDTO> areaInfoList = areaMapper.listByAreaNos(areaNos);
            areaMap = areaInfoList.stream()
                    .collect(Collectors.toMap(AreaInfoDTO::getAreaNo, AreaInfoDTO::getAreaName));
        }

        for (TimingRule rule : timingRules) {
            if (Objects.equals(TimingRuleEnum.DisplayStatus.DISPLAY.ordinal(),disPlay)){
                TimingRuleVO timingRuleVO = new TimingRuleVO();
                timingRuleVO.setTimingSku(rule.getTimingSku());
                timingRuleVO.setAreaNo(rule.getAreaNo());
                timingRuleVO.setType(rule.getType());
                timingRuleVO.setId(rule.getId());
                timingRuleVO.setAreaName(areaMap.get(rule.getAreaNo()));
                // 校验是否重复展示
                checkAreaTimingRuleUniqueness(timingRuleVO);
            }
        }

        //修改省心送
        for (Integer ruleId : ruleIds) {
            TimingRule updateTimingRule = new TimingRule();
            updateTimingRule.setId(ruleId);
            updateTimingRule.setDisplay(disPlay);
            updateTimingRule.setUpdateTime(LocalDateTime.now());
            timingRuleMapper.updateByPrimaryKey(updateTimingRule);
        }
        return CommonResult.ok(Boolean.TRUE);
    }

    private void checkNotExist(List<Integer> ruleIds, List<TimingRule> timingRules) {
        if (CollectionUtil.isEmpty(timingRules)) {
            throw new BizException("所选省心送都不存在");
        }
        if (!Objects.equals(timingRules.size(), ruleIds.size())) {
            List<Integer> idList = timingRules.stream().map(x -> x.getId())
                    .collect(Collectors.toList());
            List<Integer> ids = ruleIds.stream().filter(x -> !idList.contains(x))
                    .collect(Collectors.toList());
            throw new BizException("省心送:" + ids + "不存在");
        }
    }

    @Override
    public CommonResult<List<TimingRuleVO>> existAreaName(TimingRule timingRuleQuery) {
        // 仅检索已展示的区域
        timingRuleQuery.setDisplay(TimingRuleEnum.DisplayStatus.DISPLAY.ordinal());
        // 已配置的区域
        List<TimingRuleVO> configuredArea = timingRuleMapper.selectConfiguredAreaBySku(timingRuleQuery);
        return CommonResult.ok(configuredArea);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Boolean> batchUpdate(BatchUpdateInput input) {
        //根据ids
        List<Integer> ruleIds = input.getIds();
        List<TimingRule> timingRules = timingRuleMapper.listByIds(ruleIds);
        checkNotExist(ruleIds, timingRules);
        long count = timingRules.stream().map(x -> x.getType()).distinct().count();
        if (count > 1) {
            throw new BizException("仅支持对单一类型进行批量修改");
        }
        //展示的case下需要校验
        if (Objects.equals(input.getDisplay(), TimingRuleEnum.DisplayStatus.DISPLAY.ordinal())) {
            Map<Integer, Integer> areaCountMap = timingRules.stream()
                    .collect(Collectors.toMap(x -> x.getAreaNo(), x -> 1, (a, b) -> a + b));
            //添加的数据重复校验
            List<Integer> repeatAreaNos = areaCountMap.entrySet().stream()
                    .filter(entry -> entry.getValue() > 1).map(entry -> entry.getKey())
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(repeatAreaNos)) {
                List<AreaInfoDTO> areaInfoList = areaMapper.listByAreaNos(repeatAreaNos);
                if (CollectionUtil.isNotEmpty(areaInfoList)) {
                    String repeatAreaNames = areaInfoList.stream().map(x -> x.getAreaName())
                            .collect(Collectors.joining(","));
                    throw new BizException("不允许添加重复的城市：" + repeatAreaNames);
                }
            }
            //再判断是否有重复的城市已经开启中了
            //先查询是否有开启中的省心送
            Set<Integer> areaNos = areaCountMap.keySet();
            List<TimingRule> allTimingRules = timingRuleMapper.listByAreaNos(Lists.newArrayList(areaNos), input.getTimingSku(), input.getType());
            //剔除本次选中的
            List<TimingRule> repeatRules = allTimingRules.stream()
                    .filter(x -> !ruleIds.contains(x.getId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(repeatRules)) {
                List<AreaInfoDTO> areaInfoList = areaMapper.listByAreaNos(areaNos);
                if (CollectionUtil.isNotEmpty(areaInfoList)) {
                    Map<Integer, String> areaMap = areaInfoList.stream()
                            .collect(Collectors.toMap(AreaInfoDTO::getAreaNo, AreaInfoDTO::getAreaName));
                    String repeatAreaNames = repeatRules.stream().map(x -> areaMap.get(x.getAreaNo()))
                            .collect(Collectors.joining(","));
                    throw new BizException(repeatAreaNames + " 已展示该商品该类型省心送,同一城市,同一商品,同一类型只可展示一条");
                }
            }
        }

        //校验配送周期大于N值
        TimingRule timingRule = TimingDeliveryConverter.INSTANCE.inputToRule(input);
        checkBefore(timingRule);
        //批量更新
        for (Integer ruleId : ruleIds) {
            timingRule.setId(ruleId);
            timingRuleMapper.updateByPrimaryKey(timingRule);
        }

        return CommonResult.ok(Boolean.TRUE);
    }

    @Override
    public PageInfo<TimingOrderRefundWhiteListDTO> pageWhiteList(TimingOrderRefundWhiteListPageQuery input) {
        PageInfo<TimingOrderRefundWhiteListDTO> dtoPageInfo = PageInfoHelper.createPageInfo(
                input.getPageIndex(), input.getPageSize(), () -> {
                List<TimingOrderRefundWhiteListDTO> whiteListDTOS = timingOrderRefundWhiteListMapper.page(input);
                    if (CollectionUtil.isEmpty(whiteListDTOS)) {
                        return Collections.emptyList();
                    }
                return whiteListDTOS;
                });
        return dtoPageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertWhiteList(TimingOrderRefundWhiteListInsertReq input) {
        String orderNo = input.getOrderNo();
        Integer daysDeferred = input.getDaysDeferred();
        Long id = input.getId();

        Orders orders =  ordersMapper.queryByOrderNo(orderNo);
        if (ObjectUtils.isEmpty(orders)){
            throw new BizException("订单ID:" + orderNo + "不存在");
        }

        if (Objects.isNull(id)){
            //数据重复（即订单ID已存在）
            Boolean aBoolean = timingOrderRefundWhiteListMapper.selectByOrderNo(orderNo);
            if (aBoolean) {
                throw new BizException("订单ID:" + orderNo + "已存在");
            }
        }

        //id存在则修改 否则为新增
        TimingOrderRefundWhiteList timingOrderRefundWhiteList = null;
        if (Objects.nonNull(id)) {
            timingOrderRefundWhiteList = timingOrderRefundWhiteListMapper.selectByPrimaryKey(id);
            if (Objects.isNull(timingOrderRefundWhiteList)) {
                throw new BizException("当前记录不存在！");
            }

            if (!timingOrderRefundWhiteList.getOrderNo().equals(orderNo)){
                throw new BizException("订单ID:" + orderNo + "已存在");
            }
        }

        if (null == daysDeferred || daysDeferred < 0 || daysDeferred > 99999){
            throw new BizException("延期天数格式异常");
        }

        //校验订单ID是否存在
        List<TimingOrderVO> timingOrderVOS = ordersMapper.selectTimingOrderInfosNew(Collections.singleton(orderNo));
        if (CollectionUtil.isEmpty(timingOrderVOS)) {
            throw new BizException("订单ID:" + orderNo + "不存在");
        }
        TimingOrderVO timingOrderVO = timingOrderVOS.get(0);

        if (Objects.isNull(timingOrderRefundWhiteList)) {
            timingOrderRefundWhiteList = new TimingOrderRefundWhiteList();
        }

        timingOrderRefundWhiteList.setOperator(getAdminName());
        timingOrderRefundWhiteList.setOrderNo(orderNo);
        timingOrderRefundWhiteList.setDaysDeferred(daysDeferred);
        timingOrderRefundWhiteList.setOrderTime(timingOrderVO.getOrderTime());
        timingOrderRefundWhiteList.setMname(timingOrderVO.getMerchantName());
        timingOrderRefundWhiteList.setSku(timingOrderVO.getSku());
        timingOrderRefundWhiteList.setPdName(timingOrderVO.getPdName());

        if (Objects.isNull(id)) {
            timingOrderRefundWhiteListMapper.insertSelective(timingOrderRefundWhiteList);
        } else {
            timingOrderRefundWhiteList.setId(id);
            timingOrderRefundWhiteListMapper.updateByPrimaryKeySelective(timingOrderRefundWhiteList);
        }
        //删除弹窗记录重新弹窗
        timingOrderRefundRecordMapper.batchDeleteRecordList(Collections.singletonList(orderNo));
        //更新用户退款时间和可配置时间
        updateOrderRefundTime(Collections.singletonList(timingOrderRefundWhiteList),daysDeferred);
        return Boolean.TRUE;
    }

    private void updateOrderRefundTime(List<TimingOrderRefundWhiteList> timingOrderRefundWhiteLists,Integer daysDeferred){
        if (CollectionUtils.isEmpty(timingOrderRefundWhiteLists)){
            return;
        }
        Config config = configMapper.selectOne("TIMING_REFUND_DEFAULT_TIME");
        Map<String,LocalDateTime> orderTimeMap = timingOrderRefundWhiteLists.stream().collect(Collectors.toMap(TimingOrderRefundWhiteList::getOrderNo,TimingOrderRefundWhiteList::getOrderTime));
        List<String> orderNoList = timingOrderRefundWhiteLists.stream().map(TimingOrderRefundWhiteList::getOrderNo).collect(Collectors.toList());
        //更新省心送订单退款时间
        List<TimingOrderRefundTime> timingOrderRefundTimes = timingOrderRefundTimeMapper.selectByOrderNos(orderNoList);
        if (!CollectionUtils.isEmpty(timingOrderRefundTimes)){
            for (TimingOrderRefundTime orderRefundTime : timingOrderRefundTimes){
                LocalDateTime orderTime = orderTimeMap.get(orderRefundTime.getOrderNo());
                LocalDate refundTime = orderTime.toLocalDate().plusDays(Integer.parseInt(config.getValue())).plusDays(daysDeferred);
                orderRefundTime.setRefundTime(refundTime);
            }
            int pageSize = 200;
            for (int i = 0; i * pageSize < timingOrderRefundTimes.size(); i++) {
                List<TimingOrderRefundTime> refundTimeLists = timingOrderRefundTimes
                        .subList(pageSize * i, Math.min(pageSize * (i + 1), timingOrderRefundTimes.size()));
                timingOrderRefundTimeMapper.batchUpdateByPrimaryKey(refundTimeLists);
            }
        }

        //更新用户可配置时间
        List<TimingOrder>  timingOrderList = timingOrderMapper.selectByOrderNos(orderNoList);
        if (!CollectionUtils.isEmpty(timingOrderList)){
            for (TimingOrder timingOrder : timingOrderList){
                LocalDateTime orderTime = orderTimeMap.get(timingOrder.getOrderNo());
                LocalDate refundTime = orderTime.toLocalDate().plusDays(Integer.parseInt(config.getValue())).plusDays(daysDeferred);
                timingOrder.setDeliveryEndTime(DateUtil.toDate(refundTime));
            }
            int pageSize = 200;
            for (int i = 0; i * pageSize < timingOrderList.size(); i++) {
                List<TimingOrder> timingOrders = timingOrderList
                        .subList(pageSize * i, Math.min(pageSize * (i + 1), timingOrderList.size()));
                timingOrderMapper.batchUpdateByPrimaryKey(timingOrders);
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteWhiteList(TimingOrderRefundWhiteListDeleteReq input) {
        List<TimingOrderRefundWhiteList> orderRefundWhiteLists = timingOrderRefundWhiteListMapper.selectByIds(input.getIds());
        int i = timingOrderRefundWhiteListMapper.batchDeleteWhiteList(input.getIds());
        //更新用户退款时间
        updateOrderRefundTime(orderRefundWhiteLists,NumberUtils.INTEGER_ZERO);
        List<String> orderNos = orderRefundWhiteLists.stream().map(TimingOrderRefundWhiteList::getOrderNo).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderNos)){
            //删除弹窗记录重新弹窗
            timingOrderRefundRecordMapper.batchDeleteRecordList(orderNos);
        }
        return i > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> uploadWhiteList(TimingOrderRefundWhiteListUploadReq input) {
        //记录错误数据类型
        Map<String, Object> result = new HashMap<>();

        //解析文件数据
        InputStream inputStream = OssGetUtil.getInputStream(input.getOssUrl());
        List<TimingWhiteImportDTO> timingWhiteImportDTOS;
        try {
            timingWhiteImportDTOS = EasyExcel.read(inputStream, TimingWhiteImportDTO.class, null).doReadAllSync();
        } catch (Exception e) {
            throw new BizException("上传省心送不自动退名单解析异常！");
        }

        if (CollectionUtil.isEmpty(timingWhiteImportDTOS)) {
            throw new BizException("上传省心送不自动退名单数据不能为空！");
        }

        List<String> repeat = new ArrayList<>();
        List<String> dataError = new ArrayList<>();
        List<String> notExist = new ArrayList<>();
        List<String> unnecessary = new ArrayList<>();
        List<String> successList = new ArrayList<>();
        TimingOrderRefundWhiteList timingOrderRefundWhiteList;
        List<TimingOrderRefundWhiteList> timingOrderRefundWhiteLists = new ArrayList<>();

        //批量查询订单信息
        Set<String> orderNos = timingWhiteImportDTOS.stream().map(TimingWhiteImportDTO::getOrderNo).collect(Collectors.toSet());
        List<TimingOrderVO> timingOrderVOS = ordersMapper.selectTimingOrderInfosNew(orderNos);
        Map<String, List<TimingOrderVO>> listMap = timingOrderVOS.stream().collect(Collectors.groupingBy(TimingOrderVO::getOrderNo));

        //批量查询重复数据信息
        List<TimingOrderRefundWhiteList> whiteListList = timingOrderRefundWhiteListMapper.selectByOrderNos(new ArrayList<>(orderNos));
        Map<String, TimingOrderRefundWhiteList> whiteListMap = whiteListList.stream()
                .collect(Collectors.toMap(TimingOrderRefundWhiteList::getOrderNo, Function.identity(), (o, n) -> o));

        for (int i = 0; i < timingWhiteImportDTOS.size(); i++) {
            TimingWhiteImportDTO timingWhiteImportDTO = timingWhiteImportDTOS.get(i);
            String orderNo = timingWhiteImportDTO.getOrderNo();
            String daysDeferred = timingWhiteImportDTO.getDaysDeferred();
            if (StringUtils.isBlank(orderNo)){
                continue;
            }

            if (i >= 1000) {
                int j = i + 1;
                unnecessary.add("单次新增最大仅支持1000条，剩余部分请重新上传，第"+ j + "条订单ID:" + orderNo);
                continue;
            }

            if (null == daysDeferred || !daysDeferred.matches("^\\d+$")){
                dataError.add(orderNo);
                continue;
            }

            if (Integer.parseInt(daysDeferred) > 99999 || Integer.parseInt(daysDeferred) == 0){
                dataError.add(orderNo);
                continue;
            }

            //数据重复（即订单ID已存在）
            if (CollectionUtil.isNotEmpty(whiteListMap) && Objects.nonNull(whiteListMap.get(orderNo))) {
                repeat.add(orderNo);
                continue;
            }

            //校验订单ID是否存在
            if (CollectionUtil.isEmpty(listMap) || CollectionUtil.isEmpty(listMap.get(orderNo))) {
                notExist.add(orderNo);
                continue;
            }

            if (successList.contains(orderNo)){
                repeat.add(orderNo);
                continue;
            }

            //组装数据
            List<TimingOrderVO> orderVOList = listMap.get(orderNo);
            TimingOrderVO timingOrderVO = orderVOList.get(0);
            timingOrderRefundWhiteList = new TimingOrderRefundWhiteList();
            timingOrderRefundWhiteList.setOperator(getAdminName());
            timingOrderRefundWhiteList.setOrderNo(orderNo);
            timingOrderRefundWhiteList.setDaysDeferred(Integer.valueOf(daysDeferred));
            timingOrderRefundWhiteList.setOrderTime(timingOrderVO.getOrderTime());
            timingOrderRefundWhiteList.setMname(timingOrderVO.getMerchantName());
            timingOrderRefundWhiteList.setSku(timingOrderVO.getSku());
            timingOrderRefundWhiteList.setPdName(timingOrderVO.getPdName());
            timingOrderRefundWhiteLists.add(timingOrderRefundWhiteList);
            successList.add(orderNo);
        }

        //批量删除的订单号
        List<String> deleteOrderNos = new ArrayList<>();
        Map<Integer,List<TimingOrderRefundWhiteList>> refreshWhiteMap = null;
        //批量插入数据
        if (CollectionUtil.isNotEmpty(timingOrderRefundWhiteLists)) {
            int pageSize = 500;
            for (int i = 0; i * pageSize < timingOrderRefundWhiteLists.size(); i++) {
                List<TimingOrderRefundWhiteList> orderRefundWhiteLists = timingOrderRefundWhiteLists
                        .subList(pageSize * i, Math.min(pageSize * (i + 1), timingOrderRefundWhiteLists.size()));
                timingOrderRefundWhiteListMapper.batchInsert(orderRefundWhiteLists);
            }
            //删除弹窗记录重新弹窗
            deleteOrderNos = timingOrderRefundWhiteLists.stream().map(TimingOrderRefundWhiteList::getOrderNo).collect(Collectors.toList());
            timingOrderRefundRecordMapper.batchDeleteRecordList(deleteOrderNos);
            //分组更新用户退款时间
            refreshWhiteMap = timingOrderRefundWhiteLists.stream().collect(Collectors.groupingBy(TimingOrderRefundWhiteList::getDaysDeferred));
        }
        //更新用户退款时间
        if (null != refreshWhiteMap){
            for(Integer daysDeferred:refreshWhiteMap.keySet()){
                updateOrderRefundTime(refreshWhiteMap.get(daysDeferred),daysDeferred);
            }
        }

        result.put("dataError", dataError);
        result.put("repeat", repeat);
        result.put("notExist", notExist);
        result.put("unnecessary", unnecessary);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> initWhiteList(TimingOrderRefundWhiteListInitReq input) {
        Map<String, Object> map = new HashMap<>();
        Set<String> repeat = new HashSet<>();
        List<String> success = new ArrayList<>();
        List<String> notExist = new ArrayList<>();
        List<Long> delete = new ArrayList<>();

        List<TimingOrderRefundWhiteList> orderRefundWhiteLists;
        if (Objects.nonNull(input) && CollectionUtil.isNotEmpty(input.getOrderNos())) {
            orderRefundWhiteLists = timingOrderRefundWhiteListMapper.selectByOrderNos(input.getOrderNos());
        } else {
            orderRefundWhiteLists = timingOrderRefundWhiteListMapper.selectAll();
        }

        if (CollectionUtil.isEmpty(orderRefundWhiteLists)) {
            return map;
        }

        Set<String> orderNos = orderRefundWhiteLists.stream().map(TimingOrderRefundWhiteList::getOrderNo).collect(Collectors.toSet());
        List<TimingOrderVO> timingOrderVOS = ordersMapper.selectTimingOrderInfosNew(orderNos);
        Map<String, List<TimingOrderVO>> listMap = timingOrderVOS.stream().collect(Collectors.groupingBy(TimingOrderVO::getOrderNo));

        for (TimingOrderRefundWhiteList orderRefundWhiteList : orderRefundWhiteLists) {
            String orderNo = orderRefundWhiteList.getOrderNo();
            if (repeat.contains(orderNo)) {
                delete.add(orderRefundWhiteList.getId());
                continue;
            }
            List<TimingOrderVO> orderVOList = listMap.get(orderNo);
            if (CollectionUtil.isEmpty(orderVOList)) {
                delete.add(orderRefundWhiteList.getId());
                notExist.add(orderNo);
                continue;
            }

            //组装数据
            TimingOrderVO timingOrderVO = orderVOList.get(0);
            orderRefundWhiteList.setOperator(getAdminName());
            orderRefundWhiteList.setOrderTime(timingOrderVO.getOrderTime());
            orderRefundWhiteList.setMname(timingOrderVO.getMerchantName());
            orderRefundWhiteList.setSku(timingOrderVO.getSku());
            orderRefundWhiteList.setPdName(timingOrderVO.getPdName());
            repeat.add(orderNo);
            success.add(orderNo);
        }

        int pageSize = 500;
        for (int i = 0; i * pageSize < orderRefundWhiteLists.size(); i++) {
            List<TimingOrderRefundWhiteList> refundWhiteLists = orderRefundWhiteLists
                    .subList(pageSize * i, Math.min(pageSize * (i + 1), orderRefundWhiteLists.size()));
            timingOrderRefundWhiteListMapper.batchUpdate(refundWhiteLists);
        }

        //删除重复数据
        if (CollectionUtil.isNotEmpty(delete)) {
            timingOrderRefundWhiteListMapper.batchDeleteWhiteList(delete);
        }

        map.put("delete", delete);
        map.put("success", success);
        map.put("notExist", notExist);
        return map;
    }

    /**
     * 基础校验
     * @param timingRule
     */
    private void checkBefore(TimingRule timingRule) {
        if (Objects.equals(TimingRuleEnum.DeliveryStartType.APPOINT.getCode(), timingRule.getDeliveryStartType())){
            if (Objects.isNull(timingRule.getDeliveryStart()) || timingRule.getDeliveryStart().isBefore(LocalDate.now())) {
                throw new BizException("开始配送时间不可为空或不能在当前时间之前！");
            }
        }
        if (Objects.equals(TimingRuleEnum.DeliveryStartType.AFTER_ORDER.getCode(), timingRule.getDeliveryStartType())){
            Integer plusDay = timingRule.getPlusDay();
            if (Objects.isNull(plusDay) || plusDay > MAX_PLUS_DAY || plusDay < 1) {
                throw new BizException("N值请设置1~20范围内的数字");
            }

            //目前校验是用当前时间+最晚配送周期 假如小于下单日期+N的这个N
            if (timingRule.getDeliveryPeriod() != null && timingRule.getDeliveryPeriod() <= timingRule.getPlusDay()) {
                throw new BizException("配送周期不能小于或等于下单日期+N的N值！");
            }
        }
        if (timingRule.getDeliveryPeriod() != null && timingRule.getDeliveryPeriod() > MAX_PERIOD_DAY) {
            throw new BizException("配送周期暂不能超过91天！");
        }
    }

}
