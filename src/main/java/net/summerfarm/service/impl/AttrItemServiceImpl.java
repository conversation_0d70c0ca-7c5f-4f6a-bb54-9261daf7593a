package net.summerfarm.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.AttrItem;
import net.summerfarm.mapper.manage.AttrItemMapper;
import net.summerfarm.service.AttrItemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Package: net.summerfarm.service.impl
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/8/3
 */
@Service
public class AttrItemServiceImpl implements AttrItemService {

    @Resource
    private AttrItemMapper attrItemMapper;

    @Override
    public AjaxResult select(Integer attrId) {
        AttrItem selectKeys = new AttrItem();
        selectKeys.getAttrId();
        List<AttrItem> products = attrItemMapper.select(selectKeys);
        return AjaxResult.getOK(products);
    }
}
