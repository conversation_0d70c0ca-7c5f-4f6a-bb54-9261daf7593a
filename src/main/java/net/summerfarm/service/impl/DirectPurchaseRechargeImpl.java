package net.summerfarm.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.DtsUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.Global;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.DirectPurchaseRechargeStatus;
import net.summerfarm.enums.OrderReceivableStatusEnum;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.DirectPurchaseOrderPaymentInput;
import net.summerfarm.model.input.DirectPurchaseRechargeQuery;
import net.summerfarm.model.vo.DirectPurchaseOrderVO;
import net.summerfarm.model.vo.DirectPurchaseRechargeRecordVO;
import net.summerfarm.model.vo.DirectPurchaseRechargeVO;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.DingTalkService;
import net.summerfarm.service.DirectPurchaseRechargeRecordService;
import net.summerfarm.service.DirectPurchaseRechargeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Description: 直发采购业务充值审核
 * @Date: 2020/11/17 10:58
 * @Author: <EMAIL>
 */
@Service
public class DirectPurchaseRechargeImpl extends BaseService implements DirectPurchaseRechargeService {

    @Resource
    private DirectPurchaseRechargeMapper directPurchaseRechargeMapper;
    @Resource
    private DirectPurchaseRechargeRecordMapper directPurchaseRechargeRecordMapper;
    @Resource
    private DirectPurchaseRechargeRecordService directPurchaseRechargeRecordService;
    @Resource
    private DingTalkService dingTalkService;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;

    /**
     * status字段
     */
    private static final String STATUS = "status";

    /**
     * id字段
     */
    private static final String ID = "id";

    private static final Logger logger = LoggerFactory.getLogger(DirectPurchaseRecharge.class);

    @Override
    public AjaxResult rechargeList(int pageIndex, int pageSize, DirectPurchaseRechargeQuery selectKeys) {

        List<DirectPurchaseRechargeVO> rechargeList = new ArrayList<>(2);

        PageHelper.startPage(pageIndex,pageSize);
        rechargeList = directPurchaseRechargeMapper.selectByKeys(selectKeys);

        return AjaxResult.getOK(PageInfoHelper.createPageInfo(rechargeList));
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult save(DirectPurchaseRecharge insertContent) {
        insertContent.setAddtime(LocalDateTime.now());
        insertContent.setApplicant(getAdminName());
        directPurchaseRechargeMapper.insertSelective(insertContent);
        logger.info("管理员{}发起了给用户{}提交直发采购充值{}的审核",getAdminName(),insertContent.getMId(),insertContent.getRechargeNum());

        String picturePaths = insertContent.getPicturePaths();
        if (Objects.isNull(picturePaths)) {
            return AjaxResult.getErrorWithMsg("图片传入不能为空");
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult rechargeUpdate(DirectPurchaseRecharge updateContent) {
        DirectPurchaseRecharge query = directPurchaseRechargeMapper.selectByPrimaryKey(updateContent.getId());
        if(Objects.isNull(query) || Objects.isNull(updateContent.getRemark())){
            throw new DefaultServiceException("参数有误!");
        }
        directPurchaseRechargeMapper.updateByPrimaryKeySelective(updateContent);
        logger.info("管理员{}更新id:{}的记录remark:{}",getAdminName(),updateContent.getId(),updateContent.getRemark());
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult rechargeHandle(DirectPurchaseRecharge updateContent) {
        //验证id是否存在,并更新status的值
        DirectPurchaseRecharge query = directPurchaseRechargeMapper.selectByPrimaryKey(updateContent.getId());
        Integer insertStatus = updateContent.getStatus();
        updateContent.setHandler(getAdminName());
        updateContent.setProcessingtime(LocalDateTime.now());
        Integer status = query.getStatus();
        if(Objects.isNull(query) || Objects.isNull(status)){
            throw new DefaultServiceException("参数有误!");
        }
        if(!Objects.deepEquals(status,DirectPurchaseRechargeStatus.WAIT_HANDLE.getStatus())){
            throw  new DefaultServiceException("已审核过!");
        }

        directPurchaseRechargeMapper.updateByPrimaryKeySelective(updateContent);
        logger.info("处理人{}对编号{}的审核结果是status:{}",getAdminName(),updateContent.getId(),updateContent.getStatus());
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectBymId(Long mId) {
        if(Objects.isNull(mId)){
            throw  new DefaultServiceException("mId is null");
        }
        List<DirectPurchaseOrderVO> directPurchaseOrderVO = directPurchaseRechargeMapper.selectBymId(mId);

        return  AjaxResult.getOK(directPurchaseOrderVO);

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult orderPay(DirectPurchaseOrderPaymentInput input) {
        //采购单号是否为直发采购订单的单号,是否为应付部分付款，还是应付未付款；应付已付款的抛异常
        String orderNo = input.getOrderNo();
        Long[] recordIds = input.getRecordIds();
        Orders orderVO = ordersMapper.queryByOrderNo(orderNo);
        if(Objects.isNull(orderVO)){
            throw new DefaultServiceException("入参订单号错误");
        }
        Long mId = orderVO.getmId();
        Integer orderType = orderVO.getType();
        Integer receivableStatus = orderVO.getReceivableStatus();
        //应付总价
        BigDecimal totalPrice = orderVO.getTotalPrice();
        if(!Objects.deepEquals(orderType, OrderTypeEnum.DIRECT.getId())){
            throw new DefaultServiceException("订单号不是直发采购订单号");
        }
        if(Objects.deepEquals(receivableStatus, OrderReceivableStatusEnum.PAYED_RECEIVE.getId())){
            throw new DefaultServiceException("订单不可匹配！");
        }

        //可扣款总金额：若是部分付款则等于应付总价 - 部分已付金额；若是未付款则等于应付总价
        BigDecimal unpayAmount =  BigDecimal.ZERO;
        if(Objects.deepEquals(receivableStatus,OrderReceivableStatusEnum.NOPAY_RECEIVE.getId())){
            unpayAmount = totalPrice;
        }

        if(Objects.deepEquals(receivableStatus,OrderReceivableStatusEnum.PARTPAY_RECEIVE.getId())){
            List<DirectPurchaseOrderVO> rechargeList = directPurchaseRechargeMapper.selectByorderNo(orderNo);
            BigDecimal payedAmount =  BigDecimal.ZERO;
            if( ! CollectionUtils.isEmpty(rechargeList)){
                for (DirectPurchaseOrderVO item : rechargeList) {
                    payedAmount = payedAmount.add(item.getDifferenceAmount());
                }
            }
            unpayAmount = totalPrice.subtract(payedAmount);

        }
        BigDecimal initUnPayAmount = new BigDecimal(unpayAmount.toString());
        // 将 ids -> 对应的 List<DirectPurchaseRechargeRecord> 根据 recharge_id 排好序，依次扣除 ,并金额相加
        DirectPurchaseRechargeRecordVO insertContent = new DirectPurchaseRechargeRecordVO();
        insertContent.setMId(mId);
        insertContent.setType(0);
        insertContent.setOrderNo(orderNo);
        insertContent.setOperator(getAdminName());
        List<Long> idList = Arrays.asList(recordIds);
        List<DirectPurchaseRechargeRecord> records = directPurchaseRechargeRecordMapper.selectByIds(idList);
        BigDecimal payingAmount =  BigDecimal.ZERO;

        if(CollectionUtils.isEmpty(records)){
            throw new DefaultServiceException("参数错误");
        }
        //匹配扣款原则：根据已更新recharge_id的顺序依次排序，1：不够扣款，2.扣款刚好， 3.有余额
        for (DirectPurchaseRechargeRecord record : records) {
            if(record.getLeftAmount().compareTo(BigDecimal.ZERO) == 0){
                throw new DefaultServiceException("选择可用金额错误");
            }
            BigDecimal differenceAmount =  BigDecimal.ZERO;
            //判断unpayAmount 是否大于 leftAmout
            BigDecimal difference = unpayAmount.subtract(record.getLeftAmount());
            if(difference.compareTo(BigDecimal.ZERO) >= 0){
                differenceAmount = record.getLeftAmount();
            } else if(difference.compareTo(BigDecimal.ZERO) < 0){
                differenceAmount = unpayAmount;
            }
            insertContent.setRechargeId(record.getRechargeId());
            insertContent.setDifferenceAmount(differenceAmount);
            directPurchaseRechargeRecordService.insert(insertContent);
            unpayAmount = unpayAmount.subtract(differenceAmount);
            payingAmount = payingAmount.add(differenceAmount);
            if(difference.compareTo(BigDecimal.ZERO) <= 0){
                break;
            }
        }
        // 比较已扣除金额 和 本次应扣除金额 ： 等于，则将订单应收状态变为全部支付， 小于则应收状态为部分支付
        int flag = payingAmount.compareTo(initUnPayAmount);
        Orders orders = new Orders();
        orders.setOrderNo(orderNo);
        if(Objects.deepEquals(flag,0)){
            //扣款已全清，变订单应收状态为全部支付
            orders.setReceivableStatus(OrderReceivableStatusEnum.PAYED_RECEIVE.getId());
        }
        if(Objects.deepEquals(flag,-1)){
            //变订单应收状态为部分支付
            orders.setReceivableStatus(OrderReceivableStatusEnum.PARTPAY_RECEIVE.getId());
        }
        ordersMapper.update(orders);
        return AjaxResult.getOK();
    }

    @Override
    public void sendSaveMessage(Long id) {
        DirectPurchaseRecharge directPurchaseRecharge = directPurchaseRechargeMapper.selectByPrimaryKey(id);
        Long mId = directPurchaseRecharge.getMId();
        Merchant merchant = merchantMapper.selectByMId(mId);
        //财务群钉钉群内机器人发送消息@指定工作人员，financeRobotUrl:为财务群里机器人
        Config financeRobotUrl = configMapper.selectOne("financeRobotUrl");
        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        ArrayList<String> phoneList = new ArrayList<>();
        StringBuffer title = new StringBuffer(directPurchaseRecharge.getApplicant())
                .append("提交了")
                .append(merchant.getMname())
                .append("收款申请，请审核");

        //通知特定审批人，手机号码：李笑庭 18736899959；罗玮栋 13732212120
        phoneList.add("18736899959");
        phoneList.add("13732212120");

        //非生产条件下则指定的审批人在测试群里通知指定测试人
        if (!Global.isProduct()) {
            phoneList.clear();
            phoneList.add("17681653200");
            phoneList.add("15669093476");
        }

        at.setAtMobiles(phoneList);
        dingTalkService.dingTalkRobotTxtAT(financeRobotUrl.getValue(), title.toString(), at);

        //将要审核的内容取出，并创建钉钉审批工作流
        String picturePaths = directPurchaseRecharge.getPicturePaths();
        if (Objects.isNull(picturePaths)) {
            return;
        }
        String[] oldPictures = directPurchaseRecharge.getPicturePaths().split(",");
        List<String> pictures = new ArrayList<>(1);
        for (String picture : oldPictures) {
            picture = Global.RECEIVABLE_PICTURE_PATH_PREFIX + picture;
            pictures.add(picture);
        }
        JSONArray jsonArray = JSONUtil.parseArray(pictures);
        dingTalkService.createAccountReceivableProcessInstance(title.toString(), directPurchaseRecharge.getRechargeNum().doubleValue(), jsonArray, directPurchaseRecharge.getRemark(), directPurchaseRecharge.getRechargeId());
    }

    @Override
    public void sendAuditMessage(DtsModel dtsModel) {
        List<Map<String, String>> oldDataList = dtsModel.getOld();
        for (int i = 0; i < oldDataList.size(); i++) {
            String id = DtsUtils.searchChangeId(dtsModel, i, STATUS, ID);
            if (Objects.isNull(id)) {
                continue;
            }
            DirectPurchaseRecharge query = directPurchaseRechargeMapper.selectByPrimaryKey(Long.parseLong(id));
            //更新资金动账记录并获取申请人信息用于发送钉钉消息
            try {
                Map<String, String> handleMap = directPurchaseRechargeRecordService.changeChargeStatus(query, query.getStatus());
                String title = handleMap.get("title");
                String userId = handleMap.get("userId");
                if (Objects.nonNull(title) && Objects.nonNull(userId)) {
                    DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.TXT.getType(), userId, title);
                    dingTalkMsgSender.sendMessage(dingTalkMsgBO);
                }
            } catch (DefaultServiceException e) {
                logger.error(e.getMessage());
            }
        }
    }
}


