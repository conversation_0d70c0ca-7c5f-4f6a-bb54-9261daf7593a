package net.summerfarm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.RequestHolder;
import net.summerfarm.common.util.SkuUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.*;
import net.summerfarm.facade.tms.TmsDeliveryPathFacade;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.saas.TmsDeliveryPlanMapper;
import net.summerfarm.model.DTO.MailWorkBookDTO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.saas.TmsDeliveryPlan;
import net.summerfarm.model.input.StockTaskSaleOutReq;
import net.summerfarm.model.vo.*;
import net.summerfarm.module.pms.model.vo.AllocationOrderItemEntityVO;
import net.summerfarm.service.*;
import net.summerfarm.service.saas.OutsideOrderService;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.wms.instore.dto.req.StockStorageCreateReqDTO;
import net.summerfarm.wms.instore.dto.req.StockStorageItemCreateDTO;
import net.summerfarm.wms.instore.enums.StockStorageTypeEnums;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static java.util.stream.Collectors.groupingBy;
import static net.summerfarm.contexts.Global.SEPARATING_SYMBOL;

/**
 * Description: <br/>
 * date: 2022/3/31 17:53<br/>
 *
 * <AUTHOR> />
 */
@Service
public class ReturnRejectWarehousingServiceImpl extends BaseService implements ReturnRejectWarehousingService, StockTaskStrategy {

    @Resource
    private StockTaskMapper stockTaskMapper;
    @Resource
    private StockTaskItemMapper stockTaskItemMapper;
    @Resource
    private StockStorageItemMapper stockStorageItemMapper;
    @Resource
    private StoreRecordMapper storeRecordMapper;
   /* @Resource
    private StockTaskItemDetailMapper stockTaskItemDetailMapper;*/
    @Resource
    private StockStorageItemDetailMapper stockStorageItemDetailMapper;
    @Lazy
    @Resource
    private AreaStoreService areaStoreService;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Lazy
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    private StockTaskProcessMapper stockTaskProcessMapper;
    @Resource
    private StockTaskProcessDetailMapper stockTaskProcessDetailMapper;
    @Lazy
    @Resource
    private PurchasesConfigService purchasesConfigService;
    @Resource
    private WMSBuilderService wmsBuilderService;
    @Resource
    private StockTaskItemDetailMapper stockTaskItemDetailMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private DeliveryPathShortSkuMapper deliveryPathShortSkuMapper;
    @Resource
    private TmsDeliveryPlanMapper tmsDeliveryPlanMapper;

    private ReturnRejectWarehousingService selfService;

    @Autowired
    private AfterSaleOrderMapper afterSaleOrderMapper;

    @Resource
    private OutsideOrderService outsideOrderService;

    @Resource
    private DeliveryPathMapper  deliveryPathMapper;
    @Resource
    private  DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    @Lazy
    private  StockTaskService stockTaskService;
    @Resource
    private TmsDeliveryPathFacade tmsDeliveryPathFacade;

    @PostConstruct
    private void setSelf() {
        selfService = getContext().getBean(ReturnRejectWarehousingService.class);
    }

    @Override
    public AjaxResult inOutStore(Integer type, String data) {
        if (!Arrays.asList(new Integer[]{StoreRecordType.AFTER_SALE_IN_NEW.getId(), StoreRecordType.RETURN_IN.getId()}).contains(type)) {
            return AjaxResult.getErrorWithMsg("入库类型错误!");
        }
        List<StockTaskSaleOutReq> stockTaskSaleOutReqs = JSONObject.parseArray(data, StockTaskSaleOutReq.class);

        if (CollectionUtils.isEmpty(stockTaskSaleOutReqs)) {
            throw new DefaultServiceException("参数有误!");
        }

        StockTask task = stockTaskMapper.selectByPrimaryKey(stockTaskSaleOutReqs.get(0).getStockTaskId());
        if (task == null) {
            throw new DefaultServiceException("参数有误!");
        }
        if (task.getState().equals(StockTaskState.FINISH.getId())) {
            throw new DefaultServiceException("已完成的任务不可再次操作!");
        }

        //货位信息
        Integer goodAreaNo =  task.getAreaNo();
        HashMap<String, String> amountMap = new HashMap<>();

        //校验出入库数据
        stockTaskSaleOutReqs.stream().forEach(x ->{
            List<StockTaskItemDetailVO> stockTaskItemDetailVOS = x.getStockTaskItemDetailVOS();
            if(!CollectionUtils.isEmpty(stockTaskItemDetailVOS)){
                stockTaskItemDetailVOS.stream().forEach(y ->{
                    Integer quantity = y.getQuantity();
                    if(quantity < 0){
                        throw new DefaultServiceException("出入库数量不能小于0");
                    }
                });
            }
        });

        //新增入库单
        StockTaskProcess process = new StockTaskProcess();
        process.setStockTaskId(stockTaskSaleOutReqs.get(0).getStockTaskId());
        process.setAddtime(LocalDateTime.now());
        process.setRecorder(getAdminName());
        stockTaskProcessMapper.insert(process);
        //排序
        stockTaskSaleOutReqs.sort(Comparator.comparing(StockTaskSaleOutReq::getSku));
        //skuItem出入库操作
        stockTaskSaleOutReqs.forEach(req -> selfService.skuInOutStore(req,task,amountMap,process.getId()));

        boolean close = closeStockTask(stockTaskSaleOutReqs.get(0).getStockTaskId());
        StockTask update = new StockTask();
        update.setId(stockTaskSaleOutReqs.get(0).getStockTaskId());
        StockStorageItem select = new StockStorageItem();
        select.setStockTaskId(stockTaskSaleOutReqs.get(0).getStockTaskId());
        //List<StockTaskItem> items = stockTaskItemMapper.select(select);
        List<StockStorageItem> items = stockStorageItemMapper.selectByStockStorageItem(select);
        items.sort(Comparator.comparing(StockStorageItem::getSku));
        //关闭
        if (close) {
            update.setState(StockTaskState.FINISH.getId());
            update.setProcessState(StockTaskState.FINISH.getId());
            logger.info("任务编号:" + stockTaskSaleOutReqs.get(0).getStockTaskId() + "关闭");
        } else {
            update.setState(StockTaskState.PART_IN_OUT.getId());
            update.setProcessState(StockTaskState.PART_IN_OUT.getId());
        }
        update.setUpdatetime(LocalDateTime.now());
        update.setAdminId(getAdminId());
        stockTaskMapper.update(update);
        //应出 = 实出 修改状态
        for (StockStorageItem item : items) {
            areaStoreService.updateAreaStoreStatus(goodAreaNo, item.getSku());
            purchasesConfigService.msgArrival(task.getOutStoreNo(), item.getSku());
        }

        logger.info("{}添加库存记录:{}", getAdminName(), type);
        return AjaxResult.getOK();
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    public void closeTask(StockTask stockTask) {
        StockStorageItem select = new StockStorageItem();
        select.setStockTaskId(stockTask.getId());
        List<StockStorageItem> stockStorageItems = stockStorageItemMapper.selectByStockStorageItem(select);
        stockStorageItems.sort(Comparator.comparing(StockStorageItem::getSku));
        if (!CollectionUtils.isEmpty(stockStorageItems)) {
            for (StockStorageItem item : stockStorageItems) {
                areaStoreService.updateAreaStoreStatus(stockTask.getAreaNo(), item.getSku());
            }
        }

    }

    @Override
    public void stockTaskDetailDownload(StockTask stockTask) {
        Workbook workbook = new HSSFWorkbook();
        Sheet loadingSheet = workbook.createSheet("装货单");
        Sheet sheet = workbook.createSheet("捡货单");
        int rowIndex = 0;
        Row first = sheet.createRow(rowIndex++);

        String nameById = StockTaskType.getNameById(stockTask.getType());
        first.createCell(0).setCellValue(nameById +":");
        first.createCell(1).setCellValue(stockTask.getId());

        Row second = sheet.createRow(rowIndex++);
        second.createCell(0).setCellValue("出库仓：");
        Integer warehouseNo = stockTask.getAreaNo();
        second.createCell(1).setCellValue(Global.warehouseMap.get(stockTask.getAreaNo()));
        second.createCell(2).setCellValue("城配仓：");
        second.createCell(3).setCellValue(Global.storeMap.get(stockTask.getOutStoreNo()));

        Row three = sheet.createRow(rowIndex++);
        three.createCell(0).setCellValue("预计出库时间：");
        LocalDateTime expectTime = Objects.isNull(stockTask.getExpectTime()) ? LocalDateTime.now() : stockTask.getExpectTime();
        three.createCell(1).setCellValue(expectTime.format(DateTimeFormatter.ofPattern(DateUtils.LONG_DATE_FORMAT)));

        Row four = sheet.createRow(rowIndex++);
        four.createCell(0).setCellValue("实际出库时间：");
        four.createCell(1).setCellValue(stockTask.getUpdatetime() != null ? stockTask.getUpdatetime().format(DateTimeFormatter.ofPattern(DateUtils.LONG_DATE_FORMAT)) : "");

        rowIndex++; //中间空一行
        Row title = sheet.createRow(rowIndex++);
        String[] titleNames = null;

        titleNames = new String[]{"类目类型", "类目名称", "sku", "商品名称", "规格", "商品归属", "储存区域", "包装",  "应出数量", "批次", "生产日期", "保质期", "实发数量"};

        for (int i = 0; i< titleNames.length; i++) {
            title.createCell(i).setCellValue(titleNames[i]);
        }

        int index = rowIndex;
        List<StockTaskStorageItemVO> itemVOS = stockStorageItemMapper.selectByStockTaskId(stockTask.getId());

        if (!CollectionUtils.isEmpty(itemVOS)) {

            wmsBuilderService.batchBuildWMSInfo(itemVOS);
            itemVOS = wmsBuilderService.sortedFruitPriority(itemVOS);

            for (StockTaskStorageItemVO itemVO : itemVOS) {
                List<StockTaskItemDetailVO> itemDetailVOS = itemVO.getStockTaskItemDetailVOS();

                if (!CollectionUtils.isEmpty(itemDetailVOS)) {
                    if (itemDetailVOS.size() > 1) {
                        sheet.addMergedRegion(new CellRangeAddress(index, index + itemDetailVOS.size() - 1, 0, 0));
                        sheet.addMergedRegion(new CellRangeAddress(index, index + itemDetailVOS.size() - 1, 1, 1));
                        sheet.addMergedRegion(new CellRangeAddress(index, index + itemDetailVOS.size() - 1, 2, 2));
                        sheet.addMergedRegion(new CellRangeAddress(index, index + itemDetailVOS.size() - 1, 3, 3));
                        sheet.addMergedRegion(new CellRangeAddress(index, index + itemDetailVOS.size() - 1, 4, 4));
                        sheet.addMergedRegion(new CellRangeAddress(index, index + itemDetailVOS.size() - 1, 5, 5));
                    }
                    for (StockTaskItemDetailVO detailVO : itemDetailVOS) {
                        Row row = sheet.createRow(index);

                        // sku 信息
                        row.createCell(0).setCellValue(itemVO.getFirstLevelCategory());
                        row.createCell(1).setCellValue(itemVO.getSecondLevelCategory());
                        row.createCell(2).setCellValue(itemVO.getSku());
                        row.createCell(3).setCellValue(itemVO.getPdName() + SkuUtil.getExtTypeStr(itemVO.getExtType()));
                        row.createCell(4).setCellValue(itemVO.getWeight());
                        String productsType = StringUtils.productType(itemVO.getSkuType(),itemVO.getNameRemakes());
                        row.createCell(5).setCellValue(productsType);
                        row.createCell(6).setCellValue(itemVO.getStorageArea());
                        row.createCell(7).setCellValue(itemVO.getUnit());
                        row.createCell(8).setCellValue(itemVO.getQuantity());
                        // 详细信息
                        row.createCell(9).setCellValue(detailVO.getGlNo());
                        row.createCell(10).setCellValue(detailVO.getListNo());
                        row.createCell(11).setCellValue(detailVO.getProductionDate() != null ? detailVO.getProductionDate().toString() : "无");
                        row.createCell(12).setCellValue(detailVO.getQualityDate() != null ? detailVO.getQualityDate().toString() : "无");
                        row.createCell(12).setCellValue(detailVO.getShouldQuantity() != null ? detailVO.getShouldQuantity() : 0);
                        row.createCell(14).setCellValue(detailVO.getQuantity());


                        index++;
                        rowIndex++;
                    }

                } else {
                    Row row = sheet.createRow(index);
                    row.createCell(0).setCellValue(itemVO.getFirstLevelCategory());
                    row.createCell(1).setCellValue(itemVO.getSecondLevelCategory());
                    row.createCell(2).setCellValue(itemVO.getSku());
                    row.createCell(3).setCellValue(itemVO.getPdName() + SkuUtil.getExtTypeStr(itemVO.getExtType()));
                    row.createCell(4).setCellValue(itemVO.getWeight());
                    String productsType = StringUtils.productType(itemVO.getSkuType(),itemVO.getNameRemakes());
                    row.createCell(5).setCellValue(productsType);
                    row.createCell(6).setCellValue(itemVO.getStorageArea());
                    row.createCell(7).setCellValue(itemVO.getUnit());
                    row.createCell(8).setCellValue(itemVO.getQuantity());
                    index++;
                    rowIndex++;
                }
            }

        }
        try {
            String fileName = stockTask.getOutStoreNo() == null ? LocalDate.now() + Global.warehouseMap.get(stockTask.getAreaNo()) + ".xls" :
                    LocalDate.now() + Global.warehouseMap.get(stockTask.getAreaNo()) + "至" + Global.storeMap.get(stockTask.getOutStoreNo()) + ".xls";
            ExcelUtils.outputExcel(workbook, fileName, RequestHolder.getResponse());
        } catch (IOException e) {
            logger.error(Global.collectExceptionStackMsg(e));
            throw new DefaultServiceException("导出异常");
        }
    }

    /**
     * 生成excel表
     *
     * @param areaNo   库存仓
     * @param type     出库任务类型
     * @param taskList 出库任务列表
     * @return 生成的excel(文件名 + 文件实体)
     */
    @Override
    public List<MailWorkBookDTO> generateExcel(Integer areaNo, Integer type, List<StockTask> taskList) {
        return null;
    }

    @Override
    public void stockTaskBatchDetailDownload(List<StockTask> stockTaskList) {

    }

    @Override
    public AjaxResult stockTaskDetail(StockTaskResult result) {
        OrderAndSendVo orderAndSendVO = new OrderAndSendVo();
        //重构走新表
        //查询订单和配送信息
        Boolean isSaasOrder = Boolean.FALSE;
        OrderVO orderVO = ordersMapper.selectByOrderyNo(result.getTaskNo());
        if (!Objects.isNull(orderVO)) {
            result.setMname(orderVO.getMname());
            result.setOrderRemark(orderVO.getRemark());
        }
        TmsDeliveryPlan tmsDeliveryPlan = tmsDeliveryPlanMapper.selectAfterByOrderNo(result.getTaskNo());
        if(Objects.nonNull(tmsDeliveryPlan)){
            isSaasOrder = Boolean.TRUE;
        }
        LocalDateTime expectTime = result.getExpectTime();

        // todo saas订单处理 ，重构后记录信息，现在只能这样处理
        if(Objects.nonNull(expectTime) && isSaasOrder){
            LocalDate deliveryTime = expectTime.toLocalDate();
            DeliveryPath queryPath = new DeliveryPath();
            queryPath.setDeliveryTime(deliveryTime);
            queryPath.setContactId(Long.valueOf(tmsDeliveryPlan.getContactId()));
            queryPath.setStoreNo(tmsDeliveryPlan.getStoreNo());
            orderAndSendVO = deliveryPathMapper.selectSaasByContact(queryPath);
        }

        if(result.getType() == StockTaskType.AFTER_SALE_IN_NEW.getId() && !isSaasOrder){
            //退货入库
            List<OrderAndSendVo> orderAndSendAboutInfo = stockTaskMapper.getOrderAndSendAboutInfo(result);
            if(CollectionUtils.isEmpty(orderAndSendAboutInfo)){
                //是手动添加的退货入库
                StockTask stockTask = stockTaskMapper.selectByPrimaryKey(result.getId());
                orderAndSendAboutInfo = new ArrayList<>();
                OrderAndSendVo orderAndSendVo = new OrderAndSendVo();
                orderAndSendVo.setMismatchReason(stockTask.getMismatchReason());
                orderAndSendAboutInfo.add(orderAndSendVo);
            }
            orderAndSendVO.stockTaskDetailSetFied(orderAndSendAboutInfo);
        }
        if(result.getType() == StockTaskType.REJECT_SALE_IN.getId()){
            //拒收入库
            List<OrderAndSendVo> rejectOrderAndSendAboutInfo = stockTaskMapper.getRejectOrderAndSendAboutInfo(result);
            orderAndSendVO.stockTaskDetailSetFied(rejectOrderAndSendAboutInfo);
        }

        if(orderAndSendVO != null){
            BeanUtils.copyProperties(orderAndSendVO, result);
        }

        List<StockTaskStorageItemVO> stockTaskStorageItemVOS = stockStorageItemMapper.selectByStockTaskId(result.getId());
        if(result.getType() == StockTaskType.AFTER_SALE_IN_NEW.getId()){
            Integer deliveryPathId = orderAndSendVO.getDeliveryPathId();
            //设置回收状态值
            initRecycleState(deliveryPathId,stockTaskStorageItemVOS);
        }
        //设置售后原因
        setRemarkValue(stockTaskStorageItemVOS,result.getType());
        // build sku的一些WMS用的外部信息
        wmsBuilderService.batchBuildWMSInfo(stockTaskStorageItemVOS);

        result.setList(stockTaskStorageItemVOS);

        return AjaxResult.getOK(result);
    }

    @Override
    public AjaxResult abnormalInStore(AbnormalRecord record, Integer type) {
        return null;
    }

    @Override
    public AjaxResult manualCloseStockTask(Integer stockTaskId,String closeReason, Integer type) {
        return null;
    }

    private void setRemarkValue(List<StockTaskStorageItemVO> stockTaskStorageItemVOS, Integer afterType) {
        AfterSaleRemarkType[] values = AfterSaleRemarkType.values();
        for (StockTaskStorageItemVO stockTaskStorageItemVO : stockTaskStorageItemVOS) {
            String taskNo = stockTaskStorageItemVO.getTaskNo();
            String sku = stockTaskStorageItemVO.getSku();
            StringBuilder sb = new StringBuilder();
            if(StringUtils.isNotBlank(taskNo) && StringUtils.isNotBlank(sku)){
                new ArrayList<>();
                //订单号查询售后原因
                List<Integer> afterSaleRemarkTypeList = afterSaleOrderMapper.getAfterSaleRemarkTypeList(taskNo,sku,afterType);
                for (Integer type : afterSaleRemarkTypeList) {
                    Arrays.stream(values).forEach(typeEnum ->{
                        if(typeEnum.getType() == type){
                            sb.append(typeEnum.getDescription()).append(";");
                        }
                    });
                }
            }
            stockTaskStorageItemVO.setRemark(sb.toString());
        }
    }

    /**
     * 设置回收状态值和异常原因
     * @param deliveryPathId
     * @param stockTaskStorageItemVOS
     */
    private void initRecycleState(Integer deliveryPathId, List<StockTaskStorageItemVO> stockTaskStorageItemVOS) {
        if(deliveryPathId != null){
            stockTaskStorageItemVOS.stream().forEach(stockTaskStorageItem ->{
                List<DeliveryPathShortSku> deliveryPathShortSkus = deliveryPathShortSkuMapper.getDataByDPIdAndSku(deliveryPathId,stockTaskStorageItem.getSku());
                if(CollectionUtils.isEmpty(deliveryPathShortSkus)){
                    //正常
                    stockTaskStorageItem.setRecycleState(0);
                }else{
                    //异常
                    stockTaskStorageItem.setRecycleState(1);
                    for (DeliveryPathShortSku pathShortSkus : deliveryPathShortSkus) {
                        if(StringUtils.isNotBlank(pathShortSkus.getRemark())){
                            stockTaskStorageItem.setExceptionReason(pathShortSkus.getRemark());
                        }
                    }
                }
            });
        }
    }

    public boolean closeStockTask(Integer stockTaskId) {
        StockStorageItem select = new StockStorageItem();
        select.setStockTaskId(stockTaskId);
        //List<StockTaskItem> items = stockTaskItemMapper.select(select);
        List<StockStorageItem> items = stockStorageItemMapper.selectByStockStorageItem(select);
        return items.stream().allMatch(item -> Objects.equals(item.getQuantity(), item.getActualQuantity()));
    }

    private void loadingGoods(Sheet loadingSheet,StockTask stockTask,List<StockTaskStorageItemVO> itemVOS){
        itemVOS.forEach(item ->{
            List<StockTaskItemDetailVO> stockTaskItemDetailVOS = item.getStockTaskItemDetailVOS();
            Integer quantity = 0;
            if(!CollectionUtils.isEmpty(stockTaskItemDetailVOS)){
                quantity = stockTaskItemDetailVOS.stream().mapToInt(StockTaskItemDetailVO::getQuantity).sum();
            }
            item.setDetailQuantity(quantity);
        });
        int rowIndex = 0;
        Row first = loadingSheet.createRow(rowIndex++);
        first.createCell(0).setCellValue("销售出库：");
        first.createCell(1).setCellValue(stockTask.getId());

        Row second = loadingSheet.createRow(rowIndex++);
        second.createCell(0).setCellValue("出库仓：");
        second.createCell(1).setCellValue(Global.warehouseMap.get(stockTask.getAreaNo()));
        second.createCell(2).setCellValue("城配仓：");
        second.createCell(3).setCellValue(Global.storeMap.get(stockTask.getOutStoreNo()));

        Row three = loadingSheet.createRow(rowIndex++);
        three.createCell(0).setCellValue("预计出库时间：");
        three.createCell(1).setCellValue(stockTask.getExpectTime().format(DateTimeFormatter.ofPattern(DateUtils.LONG_DATE_FORMAT)));

        Row four = loadingSheet.createRow(rowIndex++);
        four.createCell(0).setCellValue("实际出库时间：");
        four.createCell(1).setCellValue(stockTask.getUpdatetime() != null ? stockTask.getUpdatetime().format(DateTimeFormatter.ofPattern(DateUtils.LONG_DATE_FORMAT)) : "");

        rowIndex++; //中间空一行
        Row title = loadingSheet.createRow(rowIndex++);
        String[] titleNames = new String[]{"一级类目", "二级类目", "sku", "商品名称", "规格", "商品归属","储存区域", "包装", "应出数量","实发数量"};
        for (int i = 0; i< titleNames.length; i++) {
            title.createCell(i).setCellValue(titleNames[i]);
        }
        int index = rowIndex;
        for (StockTaskStorageItemVO itemVO : itemVOS) {
            Row row = loadingSheet.createRow(index);
            //这儿是写死的类目
            row.createCell(0).setCellValue(itemVO.getFirstLevelCategory());
            row.createCell(1).setCellValue(itemVO.getSecondLevelCategory());
            row.createCell(2).setCellValue(itemVO.getSku());
            row.createCell(3).setCellValue(itemVO.getPdName() + SkuUtil.getExtTypeStr(itemVO.getExtType()));
            row.createCell(4).setCellValue(itemVO.getWeight());
            String productsType = StringUtils.productType(itemVO.getSkuType(),itemVO.getNameRemakes());
            row.createCell(5).setCellValue(productsType);
            row.createCell(6).setCellValue(itemVO.getStorageArea());
            row.createCell(7).setCellValue(itemVO.getUnit());
            row.createCell(8).setCellValue(itemVO.getQuantity());
            row.createCell(9).setCellValue(itemVO.getDetailQuantity());
            index++;
        }
    }


    /**
     * sku 条目 出入库操作
     * @param req
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void skuInOutStore(StockTaskSaleOutReq req ,StockTask task,HashMap<String,String> amountMap,Integer processId){

        List<StockTaskItemDetailVO> stockTaskItemDetails = req.getStockTaskItemDetailVOS();
        if (CollectionUtils.isEmpty(stockTaskItemDetails)) {
            return;
        }
        Integer type = task.getType();
        Integer areaNo =  task.getAreaNo();
        String sku = req.getSku();
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();

        //数据校验
        StockStorageItem item = checkItemDetail(task,stockTaskItemDetails,sku);
        //操作出库信息
        List<StockTaskProcessDetail> processDetails = new ArrayList<>();
        //即将出(入)库数量
        Integer outQuantity = stockTaskItemDetails.stream().mapToInt(StockTaskItemDetail::getQuantity).sum();
        //数量校验
        if (outQuantity + item.getActualQuantity() > item.getQuantity()) {
            throw new DefaultServiceException("sku:" + req.getSku() + "入库数量大于任务数量");
        }

        BigDecimal totalAmount = BigDecimal.ZERO;
        int totalOutQuantity = NumberUtils.INTEGER_ZERO;

        for (StockTaskItemDetail stockTaskItemDetail : stockTaskItemDetails) {
            // 出(入)库数量
            Integer quantity = stockTaskItemDetail.getQuantity();
            totalOutQuantity += quantity;
            //处理批次信息
            selfService.handleStoreRecord(task,stockTaskItemDetail,totalAmount,sku);
            //更新出库任务条目详情
            StockStorageItemDetail record  = selfService.handItemDetail(req,stockTaskItemDetail);
            //出库单详情
            StockTaskProcessDetail processDetail = new StockTaskProcessDetail();
            processDetail.setItemId(req.getId());
            processDetail.setGlNo(stockTaskItemDetail.getGlNo());
            processDetail.setSku(req.getSku());
            processDetail.setListNo(stockTaskItemDetail.getListNo());
            processDetail.setQuantity(quantity);
            processDetail.setQualityDate(stockTaskItemDetail.getQualityDate());
            processDetail.setProductionDate(stockTaskItemDetail.getProductionDate());
            processDetail.setRemark(stockTaskItemDetail.getRemark());
            processDetails.add(processDetail);
        }

        //更新已出库数量
        StockStorageItem update = new StockStorageItem();
        update.setId(item.getId());
        update.setActualQuantity(outQuantity + item.getActualQuantity());
        stockStorageItemMapper.update(update);
        amountMap.put(req.getSku(), totalAmount.toString()+ SEPARATING_SYMBOL+totalOutQuantity);
        //库存数据处理
        if (type.equals(StoreRecordType.RETURN_IN.getId()) || type.equals(StoreRecordType.AFTER_SALE_IN_NEW.getId())) {
            AreaStore areaStore = areaStoreMapper.selectOne(new AreaStore(areaNo, sku));
            if (areaStore.getSync() == 1) {
                if(type == StoreRecordType.AFTER_SALE_IN_NEW.getId()){
                    areaStoreService.updateOnlineStockByStoreNo(true, outQuantity, sku, areaNo, OtherStockChangeTypeEnum.AFTER_SALE_IN, req.getStockTaskId() + "", recordMap, NumberUtils.INTEGER_ONE);
                }
                if(type == StoreRecordType.RETURN_IN.getId()){
                    areaStoreService.updateOnlineStockByStoreNo(true, outQuantity, sku, areaNo, OtherStockChangeTypeEnum.RETURN_IN, req.getStockTaskId() + "", recordMap, NumberUtils.INTEGER_ONE);
                }
            }
            if(type == StoreRecordType.AFTER_SALE_IN_NEW.getId()){
                areaStoreService.updateStoreStockByWarehouseNo(outQuantity, sku, areaNo, OtherStockChangeTypeEnum.AFTER_SALE_IN, StoreRecordType.AFTER_SALE_IN_NEW.getId(), req.getStockTaskId() + "", recordMap);
            }
            if(type == StoreRecordType.RETURN_IN.getId()){
                areaStoreService.updateStoreStockByWarehouseNo(outQuantity, sku, areaNo, OtherStockChangeTypeEnum.RETURN_IN, StoreRecordType.RETURN_IN.getId(), req.getStockTaskId() + "", recordMap);
            }
        }
        quantityChangeRecordService.insertRecord(recordMap);
        //插入出库单详情列表
        processDetails.stream().forEach(o -> {
            o.setStockTaskProcessId(processId);
            stockTaskProcessDetailMapper.insert(o);
        });
    }


    /**
     * 数据校验
     */
    public StockStorageItem checkItemDetail(StockTask stockTask,List<StockTaskItemDetailVO> stockTaskItemDetails,String sku){

        StockStorageItem selectKey = new StockStorageItem();
        selectKey.setSku(sku);
        selectKey.setStockTaskId(stockTask.getId());
        //List<StockTaskItem> stockTaskItems = stockTaskItemMapper.select(selectKey);
        List<StockStorageItem> stockStorageItems = stockStorageItemMapper.selectByStockStorageItem(selectKey);
        if (CollectionUtils.isEmpty(stockStorageItems)) {
            throw new DefaultServiceException("参数有误!");
        }
        return stockStorageItems.get(0);
    }


    /**
     * 批次数据处理
     * @param stockTask
     * @param stockTaskItemDetail
     * @param totalAmount
     * @param sku
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void handleStoreRecord(StockTask stockTask,StockTaskItemDetail stockTaskItemDetail,BigDecimal totalAmount,String sku){

        Integer quantity = stockTaskItemDetail.getQuantity(); // 出(入)库数量
        Integer areaNo = stockTask.getAreaNo();
        Integer type = stockTask.getType();

        StoreRecord select = new StoreRecord();
        select.setBatch(stockTaskItemDetail.getListNo());
        select.setSku(sku);
        select.setAreaNo(areaNo);
        select.setQualityDate(stockTaskItemDetail.getQualityDate());
        StoreRecord lasted = storeRecordMapper.selectOne(select);
        if (lasted == null) {
            throw new DefaultServiceException("当前仓不存在采购批次" + stockTaskItemDetail.getListNo() + "保质期为" + stockTaskItemDetail.getQualityDate() + "的" + sku);
        }

        //新增库存变化记录
        StoreRecord storeRecord = new StoreRecord();
        storeRecord.setBatch(lasted.getBatch());
        storeRecord.setSku(lasted.getSku());
        storeRecord.setType(type);
        storeRecord.setQuantity(quantity);
        storeRecord.setUnit(lasted.getUnit());
        storeRecord.setRecorder(getAdminName());
        storeRecord.setRemark(stockTaskItemDetail.getRemark());
        storeRecord.setUpdateTime(new Date());
        storeRecord.setQualityDate(lasted.getQualityDate());
        storeRecord.setAreaNo(lasted.getAreaNo());
        storeRecord.setProductionDate(lasted.getProductionDate());
        if (type.equals(StoreRecordType.RETURN_IN.getId()) || type.equals(StoreRecordType.AFTER_SALE_IN_NEW.getId()) ) {
            storeRecord.setStoreQuantity(lasted.getStoreQuantity() + quantity);
        }

        storeRecord.setCost(lasted.getCost());
        storeRecord.setTenantId(lasted.getTenantId());
        storeRecordMapper.insert(storeRecord);
    }


    /**
     * 条目信息处理
     * @param req
     * @param stockTaskItemDetail
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public StockStorageItemDetail handItemDetail(StockTaskSaleOutReq req, StockTaskItemDetail stockTaskItemDetail){

        //更新出库任务条目详情
        StockStorageItemDetail selectKeys = new StockStorageItemDetail();
        Integer quantity = stockTaskItemDetail.getQuantity();
        selectKeys.setStockStorageItemId(req.getId());
        selectKeys.setQualityDate(stockTaskItemDetail.getQualityDate());
        selectKeys.setPurchaseNo(stockTaskItemDetail.getListNo());
        StockStorageItemDetail record = stockStorageItemDetailMapper.selectOne(selectKeys);
        //为空说明是新增条目信息
        if (Objects.isNull(record)) {
            StockStorageItemDetail stockStorageItemDetail = new StockStorageItemDetail();
            stockStorageItemDetail.setStockStorageItemId(req.getId());
            stockStorageItemDetail.setGlNo(stockTaskItemDetail.getGlNo());
            stockStorageItemDetail.setQualityDate(stockTaskItemDetail.getQualityDate());
            stockStorageItemDetail.setActualInQuantity(quantity);
            stockStorageItemDetail.setPurchaseNo(stockTaskItemDetail.getListNo());
            stockStorageItemDetail.setProductionDate(stockTaskItemDetail.getProductionDate());

            stockStorageItemDetailMapper.insert(stockStorageItemDetail);
            record= new StockStorageItemDetail();
            record.setId(stockStorageItemDetail.getId());
            record.setStockStorageItemId(req.getId());
        } else {

            StockStorageItemDetail stockStorageItemDetail = new StockStorageItemDetail();
            stockStorageItemDetail.setId(record.getId());
            stockStorageItemDetail.setActualInQuantity(record.getActualInQuantity()+quantity);
            stockStorageItemDetailMapper.updateByPrimaryKey(stockStorageItemDetail);
            record.setId(stockStorageItemDetail.getId());
        }
        return record;
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public AjaxResult batchStorage(List<StockTaskSaleOutReq> stockTaskSaleOutReqList) {
        //校验数据
        cheakQuantity(stockTaskSaleOutReqList);
        for (StockTaskSaleOutReq stockTaskSaleOutReq : stockTaskSaleOutReqList) {
            Map<String, Integer> stockTaskSkuMap = stockTaskSaleOutReq.getStockTaskSkuMap();
            String sku = stockTaskSaleOutReq.getSku();
            //相同sku可以存在不同的批次和保质期，sku和批次和保质期是唯一
            List<StockTaskItemDetailVO> stockTaskItemDetailVOS = stockTaskSaleOutReq.getStockTaskItemDetailVOS();

            //根据stockTask排序（由远到近入），进行入库
            Set<String> stockTask = stockTaskSkuMap.keySet();
            TreeSet<String> stockTaskTreeSet = new TreeSet<>(stockTask);

            for (String stockTaskId : stockTaskTreeSet) {
                //一个入库任务需要的sku数量
                Integer needSkuQuantity = stockTaskSkuMap.get(stockTaskId);

                ArrayList<StockTaskItemDetailVO> stockTaskItemDetailVoList = new ArrayList<>();

                //构造入库明细
                createStockTaskItemDetailVoList(stockTaskItemDetailVOS,stockTaskItemDetailVoList,needSkuQuantity);
                if(stockTaskItemDetailVoList.size() == 0){
                    break;
                }
                //根据sku和taskId查询需要入库的条目
                StockStorageItem stockStorageItem = stockStorageItemMapper.selectItemByTaskIdAndSku(Integer.parseInt(stockTaskId),sku);
                //组装请求报文
                StockTaskSaleOutReq req = new StockTaskSaleOutReq();
                BeanUtil.copyProperties(stockTaskSaleOutReq, req);
                req.setId(stockStorageItem.getId());
                req.setQuantity(stockStorageItem.getQuantity());
                req.setStockTaskId(Integer.parseInt(stockTaskId));
                req.setStockTaskItemDetailVOS(stockTaskItemDetailVoList);
                req.setStockTaskSkuMap(null);

                StockTask stockTaskInfo = stockTaskMapper.selectByPrimaryKey(Integer.parseInt(stockTaskId));
                //入库操作
                inOutStore(stockTaskInfo.getType(), JSON.toJSONString(Arrays.asList(req)));
            }

        }

        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createInOutStore(String orderNo, String sku, Integer quantity,Integer storeNo,String afterSaleOrderNo) {
        logger.info("入库消息接收 orderNo={},sku={},quantity={},storeNo={},afterSaleOrderNo={}",orderNo,sku,quantity,storeNo,afterSaleOrderNo);
        if(StringUtils.isEmpty(orderNo) || StringUtils.isEmpty(sku)||StringUtils.isEmpty(afterSaleOrderNo)  || Objects.isNull(quantity)||Objects.isNull(storeNo)){
            logger.info("任务生成不能为空 orderNo={},sku={},quantity={},storeNo={},afterSaleOrderNo={}",orderNo,sku,quantity,storeNo,afterSaleOrderNo);
            return;
        }
        AfterSaleOrderVO afterSaleOrderVO = afterSaleOrderMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        if (afterSaleOrderVO.getStatus() != AfterSaleOrderStatus.SUCCESS.getStatus()){
            return;
        }
         WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, sku);
        Integer warehouseNo = mapping.getWarehouseNo();
        StockTask stockTask = new StockTask();
        stockTask.saleNeedStockSet(orderNo,warehouseNo,storeNo, StoreRecordType.AFTER_SALE_IN_NEW.getId(), TaskTypeEnum.returnGoods.getCode());
        stockTaskMapper.insert(stockTask);
        AllocationOrderItemEntityVO item = new AllocationOrderItemEntityVO();
        item.setSku(sku);
        item.setOutQuantity(quantity);
        areaStoreService.updateAreaStoreStatus(warehouseNo, sku);
        InventoryVO inventoryVO = inventoryMapper.selectSkuType(sku);
        StockStorageCreateReqDTO createReqDTO = new StockStorageCreateReqDTO();
        createReqDTO.setInWarehouseNo(warehouseNo);
        createReqDTO.setSourceNo(orderNo);
        createReqDTO.setType(StockStorageTypeEnums.AFTER_SALE_IN_NEW.getId());
        ArrayList<StockStorageItemCreateDTO> itemCreateDTOS = new ArrayList<>();

        Integer categoryType = inventoryVO.getCategoryType();
        StockStorageItemCreateDTO itemCreateDTO = StockStorageItemCreateDTO.builder()
             .sku(inventoryVO.getSku())
             .pdName(inventoryVO.getPdName())
             .quantity(quantity)
             .packaging(inventoryVO.getUnit())
             .categoryType(categoryType)
             .specification(inventoryVO.getWeight())
             .skuType(inventoryVO.getType())
             .build();
        itemCreateDTOS.add(itemCreateDTO);
         createReqDTO.setStorageItemCreateDTOList(itemCreateDTOS);
        //发送消息 生成任务信息
        createReqDTO.setAddStore(Boolean.FALSE);
        createReqDTO.setStockTaskId(stockTask.getId().longValue());
        createReqDTO.setExpectTime(LocalDateTime.now());
        stockTaskService.sendCrateInStore(createReqDTO);
     }

    /**
     * 构造入库明细
     * @param stockTaskItemDetailVOS
     * @param stockTaskItemDetailVoList
     * @param needSkuQuantity
     */
    private void createStockTaskItemDetailVoList(List<StockTaskItemDetailVO> stockTaskItemDetailVOS, ArrayList<StockTaskItemDetailVO> stockTaskItemDetailVoList, Integer needSkuQuantity) {
        for (StockTaskItemDetailVO stockTaskItemDetailVO : stockTaskItemDetailVOS) {
            Integer quantity = stockTaskItemDetailVO.getQuantity();
            if(quantity == 0){
                continue;
            }
            Integer nextQuantity = quantity - needSkuQuantity;

            //数量够用
            if(nextQuantity >= 0){
                StockTaskItemDetailVO stockTaskItemDetail = new StockTaskItemDetailVO();
                BeanUtil.copyProperties(stockTaskItemDetailVO, stockTaskItemDetail);
                stockTaskItemDetail.setQuantity(needSkuQuantity);
                stockTaskItemDetailVoList.add(stockTaskItemDetail);

                stockTaskItemDetailVO.setQuantity(nextQuantity);
                break;
            }else if(nextQuantity < 0){
                //数量存在数差
                StockTaskItemDetailVO stockTaskItemDetail = new StockTaskItemDetailVO();
                BeanUtil.copyProperties(stockTaskItemDetailVO, stockTaskItemDetail);
                stockTaskItemDetail.setQuantity(quantity);
                stockTaskItemDetailVoList.add(stockTaskItemDetail);

                stockTaskItemDetailVO.setQuantity(0);
            }
        }

    }

    /**
     * 校验批量入库数据
     * @param stockTaskSaleOutReqList
     */
    private void cheakQuantity(List<StockTaskSaleOutReq> stockTaskSaleOutReqList) {
        for (StockTaskSaleOutReq stockTaskSaleOutReq : stockTaskSaleOutReqList) {
            //校验数据是否正确
            Map<String, Integer> stockTaskSkuMap = stockTaskSaleOutReq.getStockTaskSkuMap();
            //需要入的数量
            Integer quantity = stockTaskSaleOutReq.getQuantity();
            String sku = stockTaskSaleOutReq.getSku();
            Set<String> stockTaskIdSet = stockTaskSkuMap.keySet();

            //获取数量
            List<StockTaskStorageItemVO> stockTaskStorageItemVOS = stockStorageItemMapper.selectSkuQuantity(stockTaskIdSet,sku);
            int skuQuantity = 0;
            int skuActualQuantity = 0;

            for (StockTaskStorageItemVO stockTaskStorageItemVO : stockTaskStorageItemVOS) {
                Integer quantity1 = stockTaskStorageItemVO.getQuantity();
                Integer actualQuantity = stockTaskStorageItemVO.getActualQuantity();

                skuQuantity += quantity1;
                skuActualQuantity += actualQuantity;
            }

            int needQuantity = skuQuantity - skuActualQuantity;
            if(quantity != needQuantity){
                throw new DefaultServiceException("数据存在变动，请重新入");
            }
        }
    }
}
