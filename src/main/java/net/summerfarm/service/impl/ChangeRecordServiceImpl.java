package net.summerfarm.service.impl;

import net.summerfarm.common.base.BaseService;
import net.summerfarm.contexts.Global;
import net.summerfarm.mapper.manage.CrmConfigChangeRecordMapper;
import net.summerfarm.model.domain.CrmConfigChangeRecord;
import net.summerfarm.service.ChangeRecordService;
import net.summerfarm.task.MailUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ChangeRecordServiceImpl extends BaseService implements ChangeRecordService {

    @Resource
    CrmConfigChangeRecordMapper crmConfigChangeRecordMapper;
    @Resource
    MailUtil mailUtil;

    @Override
    public void insertRecord(Integer pmid,String oldValue,String newValue,String handleName,int type) {
        Integer adminId = getAdminId();
        CrmConfigChangeRecord crmConfigChangeRecord = new CrmConfigChangeRecord();
        crmConfigChangeRecord.setCreator(adminId);
        crmConfigChangeRecord.setCreateTime(LocalDateTime.now());
        crmConfigChangeRecord.setHandleName(handleName);
        crmConfigChangeRecord.setType(type);
        crmConfigChangeRecord.setPmId(pmid);
        crmConfigChangeRecord.setOldValue(oldValue);
        crmConfigChangeRecord.setNewValue(newValue);
        crmConfigChangeRecordMapper.insert(crmConfigChangeRecord);
    }

    @Override
    public void queryRecord() {
        LocalDate localDate = LocalDate.now();
        //取昨天四点
        LocalDateTime startTime = localDate.plusDays(-1).atTime(16, 00, 00);
        //今天四点
        LocalDateTime endTime = localDate.atTime(16, 00, 00);
        List<CrmConfigChangeRecord> crmConfigChangeRecords =  crmConfigChangeRecordMapper.selectByTime(startTime,endTime);
        //如果为空的话，不发送邮件
        if(crmConfigChangeRecords.isEmpty()){
            return;
        }
        //先根据区域分类
        Map<String, List<CrmConfigChangeRecord>> collectHandleNameMaps = crmConfigChangeRecords.stream().collect(Collectors.groupingBy(s -> s.getHandleName()));
        StringBuffer info = new StringBuffer("<html><head></head><body><style> p {text-indent:2em; line-height:28px;} </style>");
        for (String handleName : collectHandleNameMaps.keySet()) {
            List<CrmConfigChangeRecord> crmConfigChangeRecords1 = collectHandleNameMaps.get(handleName);
            //根据类型分类
            Map<Integer, List<CrmConfigChangeRecord>> collectTypeMaps = crmConfigChangeRecords1.stream().collect(Collectors.groupingBy(s -> s.getType()));
            for (Integer integer : collectTypeMaps.keySet()) {

                List<CrmConfigChangeRecord> configChangeRecords = collectTypeMaps.get(integer);
                if(Objects.equals(integer,0)){
                    info.append("<h4>"+handleName+"新增:"+"</h4>");
                    System.out.println(handleName+"新增:"+"\r\n");
                    for (CrmConfigChangeRecord configChangeRecord : configChangeRecords) {
                        info.append("<p>"+configChangeRecord.getNewValue()+"。</p>");
                        System.out.println("  "+handleName+"已新增: "+configChangeRecord.getNewValue());
                    }
                }
                if(Objects.equals(integer,1)){
                    System.out.println(handleName+"修改:");
                    info.append("<h4>"+handleName+"修改:"+"</h4>");
                    for (CrmConfigChangeRecord configChangeRecord : configChangeRecords) {
                        info.append("<p>"+handleName+"原数据为"+configChangeRecord.getOldValue()+"，现改为"+configChangeRecord.getNewValue()+"。</p>");
                        System.out.println("  "+handleName+"原("+configChangeRecord.getOldValue()+")现改为("+configChangeRecord.getNewValue()+")");
                    }
                }
                if(Objects.equals(integer,2)){
                    System.out.println(handleName+"删除:");
                    info.append("<h4>"+handleName+"删除:"+"</h4>");
                    for (CrmConfigChangeRecord configChangeRecord : configChangeRecords) {
                        info.append("<p>"+configChangeRecord.getOldValue()+"。</p>");
                        System.out.println("  "+handleName+"已删除: "+configChangeRecord.getOldValue());
                    }
                }
            }
        }
        info.append("</body></html>");
        String[] to = new String[]{"<EMAIL>"};
        try {
            if (Global.isProduct()) {
                mailUtil.sendMailHtml("佣金机制调整",info.toString(),to,null);
            }
        } catch (MessagingException e) {
            e.printStackTrace();
        }
    }
}
