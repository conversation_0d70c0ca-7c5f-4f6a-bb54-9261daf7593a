package net.summerfarm.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.enums.AdjustmentAverageStatusEnum;
import net.summerfarm.enums.AdjustmentStatus;
import net.summerfarm.enums.AdjustmentType;
import net.summerfarm.enums.ReceiptStatusEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.offline.FinanceBillRevenueDetailsMapper;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.model.input.AdjustmentInput;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.summerfarm.service.AdjustmentService;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static net.summerfarm.enums.AdjustmentAverageStatusEnum.HAS_AVERAGE;


/**
 * <AUTHOR>
 * @description 调整单业务层
 * @date 2021/12/4 13:47
 */
@Slf4j
@Service
public class AdjustmentServiceImpl extends BaseService implements AdjustmentService {

    @Resource
    private AdjustmentMapper adjustmentMapper;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private AdjustmentDetailMapper adjustmentDetailMapper;

    @Resource
    private OrderItemMapper orderItemMapper;

    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private FinanceAccountingPeriodOrderMapper periodOrderMapper;

    @Resource
    private FinanceAccountingStoreDetailMapper storeDetailMapper;

    @Resource
    private FinanceAccountingPeriodOrderMapper financeAccountingPeriodOrderMapper;

    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;


    @Autowired
    MqProducer mqProducer;

    /**
     * 0
     */
    public static final int ZERO = 0;

    /**
     * 1
     */
    public static final int ONE = 1;

    /**
     * 2
     */
    public static final int TWO = 2;

    @Resource
    private PlatformTransactionManager transactionManager;

    @Override
    public AjaxResult select(int pageIndex, int pageSize, AdjustmentInput input) {
        PageHelper.startPage(pageIndex, pageSize);
        List<AdjustmentVO> adjustmentVOS = adjustmentMapper.selectAll(input);
        for (AdjustmentVO adjustmentVO : adjustmentVOS) {
            if (adjustmentVO.getSourceType() == 0) {
                FinanceAccountingPeriodOrderVO financeAccountingPeriodOrderVO = financeAccountingPeriodOrderMapper.selectByBillNumber(adjustmentVO.getBillNumber());
                financeAccountingPeriodOrderVO = Optional.ofNullable(financeAccountingPeriodOrderVO).orElse(new FinanceAccountingPeriodOrderVO());
                adjustmentVO.setNameRemakes(financeAccountingPeriodOrderVO.getNameRemakes());
            }
            Admin creator = adminMapper.selectByPrimaryKey(adjustmentVO.getCreator());
            if (creator != null) {
                adjustmentVO.setCreatorName(creator.getRealname());
            }
            adjustmentVO.setAfterAdjustPrice(NumberUtil.add(adjustmentVO.getReceivableMoney(), (adjustmentVO.getTotal())));
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(adjustmentVOS));
    }

    @Override
    public AjaxResult selectTotalAmount(AdjustmentInput input) {
        BigDecimal totalAmount = adjustmentMapper.selectTotalAmount(input);
        return AjaxResult.getOK(Objects.isNull(totalAmount) ? BigDecimal.ZERO : totalAmount);
    }

    @Override
    public AjaxResult selectDetail(String adjustNo) {
        AdjustmentVO adjustmentVO = adjustmentMapper.selectByNo(adjustNo);
        Admin creator = adminMapper.selectByPrimaryKey(adjustmentVO.getCreator());
        adjustmentVO.setCreatorName(creator.getRealname());
        if (Objects.nonNull(adjustmentVO.getApprover())) {
            Admin approver = adminMapper.selectByPrimaryKey(adjustmentVO.getApprover());
            adjustmentVO.setApproverName(approver.getRealname());
        }
        return AjaxResult.getOK(adjustmentVO);
    }

//    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult approve(AdjustmentInput input) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        Adjustment adjustment = adjustmentMapper.selectByPrimaryKey(input.getId());
        if (!Objects.equals(AdjustmentStatus.WAIT.ordinal(), adjustment.getStatus())) {
            return AjaxResult.getErrorWithMsg("调整单待审批状态下才可以进行审批");
        }
        try {
            adjustment.setStatus(input.getFlag());
            adjustment.setNote(input.getNote());
            adjustment.setApprover(getAdminId());
            adjustment.setApproveTime(LocalDateTime.now());
            adjustmentMapper.updateSelective(adjustment);
            if (!adjustment.getType().equals(AdjustmentType.ALL.ordinal())) {
                updatePeriodReceiptStatus(adjustment.getBillNumber());
            }
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            return AjaxResult.getErrorWithMsg("审批失败");
        }

        // 全部订单发送mq拆分
        if (adjustment.getType().equals(AdjustmentType.ALL.ordinal())) {
            MQData mqData = new MQData(MType.APPORTION_ADJUSTMENT.name());
            JSONObject msgJson = new JSONObject();
            msgJson.put("adjustNo", adjustment.getAdjustNo());
            msgJson.put("billNumber", adjustment.getBillNumber());
            String productMsg = msgJson.toJSONString();
            mqData.setData(productMsg);
            mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
        }
        return AjaxResult.getOK();
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult saveAdjustment(AdjustmentInput input) {
        AdjustmentInput query = new AdjustmentInput();
        query.setStatus(AdjustmentStatus.WAIT.ordinal());
        query.setBillNumber(input.getBillNumber());
        List<Adjustment> adjustmentList = adjustmentMapper.select(query);
        if (!CollectionUtils.isEmpty(adjustmentList)) {
            long count = adjustmentList.stream().filter(el -> Objects.equals(el.getType(), AdjustmentType.ALL.ordinal()) && Objects.equals(el.getStatus(), AdjustmentStatus.WAIT.ordinal())).count();
            if (count > 0) {
                return AjaxResult.getErrorWithMsg("该账单有部分订单，处于待审批状态的调整单中，请完成审批后，再进行发起");
            }
            if (Objects.equals(AdjustmentType.PART.ordinal(), input.getType())) {
                query.setType(AdjustmentType.PART.ordinal());
                AdjustmentDetail select = new AdjustmentDetail();
                for (AdjustmentDetail detail : input.getDetailList()) {
                    if (Objects.nonNull(detail.getOrderItemId())) {
                        select.setOrderItemId(detail.getOrderItemId());
                        List<Adjustment> partList = adjustmentMapper.selectByDetail(select, AdjustmentStatus.WAIT.ordinal());
                        if (!CollectionUtils.isEmpty(partList)) {
                            return AjaxResult.getErrorWithMsg("该账单有部分订单，处于待审批状态的调整单中，请完成审批后，再进行发起");
                        }
                        continue;
                    }
                    select.setOrderNo(detail.getOrderNo());
                    List<Adjustment> partList = adjustmentMapper.selectByDetail(detail, AdjustmentStatus.WAIT.ordinal());
                    if (!CollectionUtils.isEmpty(partList)) {
                        return AjaxResult.getErrorWithMsg("该账单有部分订单，处于待审批状态的调整单中，请完成审批后，再进行发起");
                    }
                }
            }
        }

        // 对所有订单、部分订单的额度进行校验
        check(input);

        String adjustNo = createAdjustNo();
        Adjustment insert = new Adjustment(adjustNo, input.getBillNumber(), input.getType(), input.getTotalPrice(), input.getTotalDelivery(), input.getTotal(), input.getReceivableMoney(), input.getType(), AdjustmentStatus.WAIT.ordinal(), input.getAttachment(), input.getReason(), getAdminId());
        adjustmentMapper.insertSelective(insert);

        List<AdjustmentDetail> detailList = input.getDetailList();
        if (Objects.equals(AdjustmentType.PART.ordinal(), input.getType().intValue())) {
            for (AdjustmentDetail detail : detailList) {
                detail.setAdjustNo(adjustNo);
                if (StringUtils.isBlank(detail.getOrderNo())) {
                    detail.setMId(orderItemMapper.selectMidByItemId(detail.getOrderItemId()));
                } else {
                    detail.setMId(ordersMapper.selectMidByOrderNo(detail.getOrderNo()));
                }
            }
            adjustmentDetailMapper.insertBatch(detailList);
        }
        logger.info("管理员{}新增了调整单{}", getAdminName(), adjustNo);
        return AjaxResult.getOK();
    }

    private AjaxResult check(AdjustmentInput input) {
        //全部订单方式校验额度
        FinanceAccountingPeriodOrderVO periodOrder = financeAccountingPeriodOrderMapper.selectByBillNumber(input.getBillNumber());
        if (BigDecimal.ZERO.compareTo(input.getTotalPrice()) > 0 && Objects.equals(AdjustmentType.ALL.ordinal(), input.getType())) {
            BigDecimal maxAdjustPrice = periodOrder.getTotalPrice().subtract(periodOrder.getDeliveryFee()).subtract(periodOrder.getAfterSaleAmount()).add(periodOrder.getAdjustPrice());
            if (BigDecimal.ZERO.compareTo(maxAdjustPrice.add(input.getTotalPrice())) > 0) {
                return AjaxResult.getErrorWithMsg("该调整单的可调整实付额度小于本次的调整金额");
            }
        }

        if (BigDecimal.ZERO.compareTo(input.getTotalDelivery()) > 0 && Objects.equals(AdjustmentType.ALL.ordinal(), input.getType())) {
            BigDecimal maxAdjustDelivery = periodOrder.getDeliveryFee().subtract(periodOrder.getAdjustDelivery());
            if (BigDecimal.ZERO.compareTo(maxAdjustDelivery.add(input.getTotalDelivery())) > 0) {
                return AjaxResult.getErrorWithMsg("该调整单的可调整运费额度小于本次的调整金额");
            }
        }

        //部分订单方式额度校验
        if (Objects.equals(AdjustmentType.PART.ordinal(), input.getType())) {
            for (AdjustmentDetail detail : input.getDetailList()) {
                if (Objects.nonNull(detail.getOrderNo()) && BigDecimal.ZERO.compareTo(detail.getAdjustDelivery()) > 0) {
                    BigDecimal successDelivery = adjustmentDetailMapper.selectSuccessNumByOrderNo(detail.getOrderNo());
                    BigDecimal deliverFee = ordersMapper.selectDeliveryFee(detail.getOrderNo());
                    BigDecimal maxAdjustDelivery = successDelivery.add(deliverFee);
                    if (BigDecimal.ZERO.compareTo(detail.getAdjustDelivery().add(maxAdjustDelivery)) > 0) {
                        return AjaxResult.getErrorWithMsg(detail.getOrderNo() + "运费超出可调整运费额度");
                    }
                    continue;
                }
                if (Objects.nonNull(detail.getOrderItemId()) && BigDecimal.ZERO.compareTo(detail.getAdjustPrice()) > 0) {
                    BigDecimal successPrice = adjustmentDetailMapper.selectSuccessNumByOrderItem(detail.getOrderItemId());
                    BigDecimal afterSaleAmount = afterSaleProofMapper.selectSuccessNumByOrderItem(detail.getOrderNo(), detail.getSku());
                    OrderItem orderItem = orderItemMapper.selectByPrimaryKey(detail.getOrderItemId());
                    BigDecimal itemAmount = orderItem.getPrice().multiply(BigDecimal.valueOf(orderItem.getAmount()));
                    BigDecimal maxAdjustPrice = itemAmount.subtract(afterSaleAmount).add(successPrice);
                    if (BigDecimal.ZERO.compareTo(detail.getAdjustPrice().add(maxAdjustPrice)) > 0) {
                        return AjaxResult.getErrorWithMsg(detail.getSku() + "实付超出可调整实付额度");
                    }
                }
            }
        }
        return null;
    }

    /**
     * 创建调整单号 日期加上今天第多少个调整单
     *
     * @return
     */
    private String createAdjustNo() {
        LocalDate today = LocalDate.now();
        String replaceAll = today.toString().replaceAll("-", "");
        Map<String, String> countKeys = new HashMap(NumberUtils.INTEGER_ONE);
        countKeys.put("fuzzyAdjustNo", replaceAll);
        Integer num = adjustmentMapper.count(countKeys) + 1;
        return replaceAll + StringUtils.strFormat(num.toString(), 3);
    }


    /**
     * 分摊调整单
     *
     * @param adjustment 调整单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void apportionAdjustment(Adjustment adjustment) {
        String adjustNo = adjustment.getAdjustNo();
        String billNumber = adjustment.getBillNumber();
        log.info("分摊调整单开始，调整单号：{}，账单编号:{}", adjustNo, billNumber);
        BigDecimal adjustPriceSum = adjustmentMapper.selectPaidOrDeliveryFeeByBillNo(billNumber, adjustNo, true);
        BigDecimal adjustDeliverSum = adjustmentMapper.selectPaidOrDeliveryFeeByBillNo(billNumber, adjustNo, false);

        //查询出账单下的所有订单
        List<String> orderNoList = storeDetailMapper.selectOrderNoByBillNumber(adjustment.getBillNumber());
        List<OrderVO> orderVOList = ordersMapper.selectWithItem(orderNoList);
        BigDecimal deliveryCount = BigDecimal.valueOf(orderVOList.size());
        BigDecimal totalPrice = BigDecimal.ZERO;
        Integer itemCountSum = 0;
        //数据统计
        for (OrderVO orderVO : orderVOList) {
            //处理有过调整的订单运费
            BigDecimal successDelivery = adjustmentDetailMapper.selectSuccessNumByOrderNo(orderVO.getOrderNo());
            orderVO.setDeliveryFee(orderVO.getDeliveryFee().add(successDelivery));
            itemCountSum += orderVO.getItemCount();
            if (adjustDeliverSum.compareTo(BigDecimal.ZERO) < 0 && orderVO.getDeliveryFee().compareTo(BigDecimal.ZERO) <= 0) {
                deliveryCount = deliveryCount.subtract(BigDecimal.ONE);
            }
            for (OrderItemVO itemVO : orderVO.getOrderItemVOs()) {
                //处理调整过后的实付金额
                BigDecimal successAdjustPrice = adjustmentDetailMapper.selectSuccessNumByOrderItem(itemVO.getId());
                BigDecimal successAfterSale = afterSaleProofMapper.selectSuccessNumByOrderItem(orderVO.getOrderNo(), itemVO.getSku());
                itemVO.setItemAmount(itemVO.getItemAmount().subtract(successAfterSale).add(successAdjustPrice));
                totalPrice = totalPrice.add(itemVO.getItemAmount());
            }
        }

        int deliveryFlag = deliveryCount.intValue();
        List<AdjustmentDetail> insertDetailList = new ArrayList<>();
        BigDecimal priceSum = BigDecimal.ZERO;
        BigDecimal deliverySum = BigDecimal.ZERO;
        for (OrderVO orderVO : orderVOList) {
            if (CollectionUtils.isEmpty(orderVO.getOrderItemVOs())) {
                return;
            }
            //调整总运费大于0或者总运费小于0并且运费也大于0才去构建运费明细对象
            boolean conditionOne = adjustDeliverSum.compareTo(BigDecimal.ZERO) > ZERO;
            boolean conditionTwo = adjustDeliverSum.compareTo(BigDecimal.ZERO) < ZERO && orderVO.getDeliveryFee().compareTo(BigDecimal.ZERO) > ZERO;
            if (conditionOne || conditionTwo) {
                AdjustmentDetail deliveryDetail = new AdjustmentDetail();
                //构建调整运费
                deliveryDetail.setAdjustNo(adjustment.getAdjustNo());
                deliveryDetail.setAdjustPrice(BigDecimal.ZERO);
                deliveryDetail.setOrderNo(orderVO.getOrderNo());
                deliveryDetail.setMId(orderVO.getmId());
                BigDecimal adjustDelivery = adjustDeliverSum.divide(deliveryCount, TWO, BigDecimal.ROUND_HALF_UP);
                if (deliveryFlag == ONE) {
                    deliveryDetail.setAdjustDelivery(adjustDeliverSum.subtract(deliverySum));
                } else {
                    deliveryDetail.setAdjustDelivery(adjustDelivery);
                }
                deliverySum = deliverySum.add(adjustDelivery);
                deliveryFlag--;
                insertDetailList.add(deliveryDetail);
            }
            //如果有调整实付金额  构建调整实付明细
            if (adjustPriceSum.compareTo(BigDecimal.ZERO) != ZERO) {
                List<OrderItemVO> orderItemVOs = orderVO.getOrderItemVOs();
                for (OrderItemVO itemVO : orderItemVOs) {
                    AdjustmentDetail priceDetail = new AdjustmentDetail();
                    priceDetail.setAdjustNo(adjustment.getAdjustNo());
                    priceDetail.setOrderItemId(itemVO.getId());
                    priceDetail.setSku(itemVO.getSku());
                    priceDetail.setMId(orderVO.getmId());
                    priceDetail.setAdjustDelivery(BigDecimal.ZERO);
                    //处理调整实付金额
                    BigDecimal adjustPrice = itemVO.getItemAmount().multiply(adjustPriceSum).divide(totalPrice, TWO, BigDecimal.ROUND_HALF_UP);
                    if (itemCountSum == ONE) {
                        priceDetail.setAdjustPrice(adjustPriceSum.subtract(priceSum));
                    } else {
                        priceDetail.setAdjustPrice(adjustPrice);
                    }
                    priceSum = priceSum.add(adjustPrice);
                    itemCountSum--;
                    insertDetailList.add(priceDetail);
                }
            }
        }
        if (!CollectionUtils.isEmpty(insertDetailList)) {
            adjustmentDetailMapper.insertBatch(insertDetailList);
        }
        adjustment.setAverageFlag(HAS_AVERAGE.ordinal());
        adjustmentMapper.updateAverageFlag(adjustment);
        updatePeriodReceiptStatus(adjustment.getBillNumber());
        log.info("调整单完毕，调整单号：{}，账单编号:{}", adjustNo, billNumber);
    }


    /**
     * 账单收款后调整判断账单状态
     *
     * @param billNo 账单编号
     */
    public void updatePeriodReceiptStatus(String billNo){
        FinanceAccountingPeriodOrderVO period = periodOrderMapper.selectByBillNumber(billNo);
        // 未收款
        if (period.getWriteOffAmount().compareTo(BigDecimal.ZERO)==0){
            return;
        }
        BigDecimal adjustment = adjustmentMapper.selectAdjustment(billNo);
        byte receiptStatus;
        // 订单实付-调整-售后=账单应收
        if (period.getTotalPrice().add(adjustment).subtract(period.getAfterSaleAmount()).compareTo(period.getWriteOffAmount()) < 0) {
            receiptStatus = ReceiptStatusEnum.OVERAGE.getId();
        } else if (period.getTotalPrice().add(adjustment).subtract(period.getAfterSaleAmount()).compareTo(period.getWriteOffAmount()) == 0) {
            receiptStatus = ReceiptStatusEnum.RECEIVED.getId();
        } else {
            receiptStatus = ReceiptStatusEnum.PARTIAL_COLLECTION.getId();
        }
        period.setReceiptStatus(receiptStatus);
        periodOrderMapper.updateByPrimaryKeySelective(period);
    }
}
