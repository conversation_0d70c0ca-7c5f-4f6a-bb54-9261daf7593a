package net.summerfarm.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.enums.CommonStatus;
import net.summerfarm.enums.DiscountTypeEnum;
import net.summerfarm.enums.InventoryExtTypeEnum;
import net.summerfarm.enums.StoreRecordType;
import net.summerfarm.enums.WillExpirePriceChangeReasonEnum;
import net.summerfarm.enums.market.activity.ActivityTagEnum;
import net.summerfarm.enums.market.activity.ActivityTypeEnum;
import net.summerfarm.enums.market.activity.AdjustTypeEnum;
import net.summerfarm.enums.market.activity.GoodSelectWayEnum;
import net.summerfarm.enums.market.activity.ScopeTypeEnum;
import net.summerfarm.facade.SaleInventoryFacade;
import net.summerfarm.facade.WarehouseSkuAreaNoQueryFacade;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.offline.StockSalesVolumeMapper;
import net.summerfarm.model.DTO.WillExpiredDiscountNotifyDTO;
import net.summerfarm.model.DTO.market.ActivityItemScopeDTO;
import net.summerfarm.model.DTO.market.ActivityLadderConfigDTO;
import net.summerfarm.model.DTO.market.ActivityLadderPriceDTO;
import net.summerfarm.model.DTO.market.ActivityScopeQueryDTO;
import net.summerfarm.model.DTO.market.ActivitySkuQueryDTO;
import net.summerfarm.model.bo.price.PriceInfoBO;
import net.summerfarm.model.domain.Activity;
import net.summerfarm.model.domain.ActivitySku;
import net.summerfarm.model.domain.Area;
import net.summerfarm.model.domain.AreaSku;
import net.summerfarm.model.domain.AreaStore;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.Quota;
import net.summerfarm.model.domain.StockSkuStatistics;
import net.summerfarm.model.domain.StoreRecord;
import net.summerfarm.model.domain.WillExpirePriceLog;
import net.summerfarm.model.domain.market.ActivityBasicInfo;
import net.summerfarm.model.domain.market.ActivityItemConfig;
import net.summerfarm.model.domain.market.ActivitySceneConfig;
import net.summerfarm.model.domain.market.ActivityScopeConfig;
import net.summerfarm.model.domain.market.ActivitySkuDetail;
import net.summerfarm.model.domain.market.ActivitySkuPrice;
import net.summerfarm.model.vo.AreaSkuVO;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.ProductVO;
import net.summerfarm.model.vo.StoreRecordVO;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.ActivityNewService;
import net.summerfarm.service.ActivityService;
import net.summerfarm.service.FenceService;
import net.summerfarm.service.PriceService;
import net.summerfarm.service.ProductsPropertyService;
import net.summerfarm.service.helper.AreaStoreServiceHelper;
import net.summerfarm.warehouse.mapper.WarehouseStorageCenterMapper;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.wms.saleinventory.dto.req.QueryWarehouseSkuInventoryReq;
import net.summerfarm.wms.saleinventory.dto.res.WarehouseSkuInventoryDetailResDTO;
import net.summerfarm.wms.saleinventory.dto.res.WarehouseSkuInventoryResp;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoDataReq;
import net.summerfarm.wnc.client.req.WarehouseBySkuAreaNoQueryReq;
import net.summerfarm.wnc.client.resp.WarehouseBySkuAreaNoResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * Created by wjd on 2017/9/25.
 */
@Service("activityService")
public class ActivityServiceImpl extends BaseService implements ActivityService {
    public static final double MAX_F = 0.2;
    @Resource
    private StockSalesVolumeMapper stockSalesVolumeMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private ProductsPropertyService productsPropertyService;
    @Resource
    private ActivityMapper activityMapper;
    @Resource
    private ActivitySkuMapper activitySkuMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private FenceService fenceService;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private PriceService priceService;
    @Resource
    private WillExpirePriceLogMapper willExpirePriceLogMapper;
    @Resource
    private AreaStoreServiceHelper areaStoreServiceHelper;
    @Resource
    private WarehouseStorageCenterMapper warehouseStorageCenterMapper;

    @Resource
    private ActivityBasicInfoMapper activityBasicInfoMapper;

    @Resource
    private ActivitySceneConfigMapper activitySceneConfigMapper;

    @Resource
    private ActivitySkuDetailMapper activitySkuDetailMapper;

    @Resource
    private ActivityScopeConfigMapper activityScopeConfigMapper;

    @Resource
    private ActivitySkuPriceMapper activitySkuPriceMapper;

    @Resource
    private ActivityItemConfigMapper activityItemConfigMapper;

    @Resource
    private ActivityNewService activityNewService;

    @Resource
    private AreaStoreMapper areaStoreMapper;

    @Resource
    private WarehouseSkuAreaNoQueryFacade warehouseSkuAreaNoQueryFacade;

    @Resource
    private SaleInventoryFacade saleInventoryFacade;


    private static String DISCOUNT_MSG_TEMPLATE = "【临保折扣监控】{0} {1} {2}在 {3} 的最新折扣为{4}折，最新临保价为¥{5}，上期折扣为{6}折，请检查是否合理";

    private static String DISCOUNT_EXCEPTION_KEY = "discountExceptionRobotUrl";

    private static final Logger logger = LoggerFactory.getLogger(ActivityServiceImpl.class);

    @Override
    public void nearExpiredSkuActivity() {
        logger.info("临保活动生成开始--");
        //判断并初始化活动1
        this.initActivity();

        //查看 所有临保品的SKU且在售卖的SKU
        List<AreaSkuVO> areaSkuVOS = areaSkuMapper.listNearExpiredSku();

        //确定到库存仓
        for (AreaSkuVO areaSkuVO : areaSkuVOS) {
            Integer areaNo = areaSkuVO.getAreaNo();
            try {
                Integer warehouseNo = fenceService.selectWarehouseNo(areaNo, areaSkuVO.getSku());
                areaSkuVO.setWarehouseNo(warehouseNo);
            } catch (Exception e) {
                logger.error("获取库存仓异常, areaNo:{}, sku:{}", areaNo, areaSkuVO.getSku());
            }

        }

        List<AreaSkuVO> skuVOList = areaSkuVOS.stream().filter(x -> x.getWarehouseNo() != null)
                .collect(Collectors.toList());

//        List<AreaSkuVO> nearExpiredSku = areaSkuMapper.selectNearExpiredSku(AreaSkuEnum.EXT_TYPE_EXPIRED.getExtType(), null, null);

        this.sendDiscountNotification(skuVOList);

    }

    /**
     * 生成临保池
     * @param areaSkuVO         sku售卖信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void generateExpiredActivity(AreaSkuVO areaSkuVO,WillExpiredDiscountNotifyDTO discountNotify) {
        logger.info("开始生成临保池， areaSkuVO：{}， discountNotify：{}", JSON.toJSONString(areaSkuVO), JSON.toJSONString(discountNotify));
        //重新构建areaSkuVO
        InventoryVO inventoryVO = inventoryMapper.selectInventoryBySku(areaSkuVO.getSku());
        areaSkuVO.setWarnTime(inventoryVO.getWarnTime());
        areaSkuVO.setPicturePath(inventoryVO.getPicturePath());
        areaSkuVO.setPdName(inventoryVO.getPdName());

        Integer storeNo = null;
        //查看商品保质期（不同城市取不同商品保质期）
        storeNo = fenceService.selectStoreNoByAreaNo(areaSkuVO.getAreaNo());
        WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, areaSkuVO.getSku());
        //关联不到库存仓时，跳过
        if (Objects.isNull(mapping)) {
            logger.error("ActivityServiceImpl[]generateExpiredActivity[]selectByUniqueIndex[]mapping[]empty! areaSkuVO:{}, storeNo:{}", JSON.toJSONString(areaSkuVO), storeNo);
            return;
        }
        StoreRecord storeRecord = storeRecordMapper.selectQualityDate(areaSkuVO.getSku(), mapping.getWarehouseNo());
        //当为空，或者保质期为空时,跳过
        if (Objects.isNull(storeRecord) || Objects.isNull(storeRecord.getQualityDate())) {
            logger.error("ActivityServiceImpl[]generateExpiredActivity[]storeRecord[]empty! areaSkuVO:{}, mapping:{}", JSON.toJSONString(areaSkuVO), JSON.toJSONString(mapping));
            return;
        }
        //判断该SKU是否参加了非临保活动
        //新营销活动处理
        checkAndDeleteSku(areaSkuVO.getSku(), areaSkuVO.getAreaNo());

        BigDecimal price = BigDecimal.ZERO;
        PriceInfoBO priceInfo = priceService.getNormalPrice(areaSkuVO);
        BigDecimal originalPrice = priceInfo.getPrice();

        Quota quota = new Quota();
        AreaStore areaStore = areaStoreMapper.selectByStoreNoAndSkuNew(storeNo, areaSkuVO.getSku());
        areaSkuVO.setQuantity(Optional.ofNullable(areaStore.getQuantity()).orElse(0));
        areaSkuVO.setSafeQuantity(Optional.ofNullable(areaStore.getSafeQuantity()).orElse(0));
        areaSkuVO.setLockQuantity(Optional.ofNullable(areaStore.getLockQuantity()).orElse(0));
        List<String> normalSkus = fillQuotaAndGetNormalSkus(areaSkuVO, originalPrice, quota);
        logger.info("准备计算临保活动价, sku={}, areaNo:{}", areaSkuVO.getSku(), areaSkuVO.getAreaNo());
        price = calPrice(quota, normalSkus, areaSkuVO);
        //判断该城市活动是否有临保活动
        //临保活动是到运营城市维度的
        ActivityBasicInfo basicInfo = activityScopeConfigMapper.selectExpiredActivity(
                Long.valueOf(areaSkuVO.getAreaNo()));
        if (basicInfo == null) {
            logger.error("ActivityServiceImpl[]generateExpiredActivity[]selectExpiredActivity[]basicInfo[]empty! areaSkuVO:{}", JSON.toJSONString(areaSkuVO));
            return;
        }

        //过滤关闭自动定价商品--不自动计算价格
        ActivityItemConfig itemConfig = activityItemConfigMapper.getByInfoId(basicInfo.getId());
        if (Objects.isNull(itemConfig)) {
            logger.error("ActivityServiceImpl[]generateExpiredActivity[]itemConfig[]empty! basicInfo:{}", JSON.toJSONString(basicInfo));
            return;
        }
        ActivitySkuDetail activitySkuDetail = activitySkuDetailMapper.selectBySku(itemConfig.getId(), areaSkuVO.getSku());
        if (Objects.nonNull(activitySkuDetail) && Objects.equals(activitySkuDetail.getAutoPrice(), CommonStatus.NO.getCode())) {
            logger.info("关闭自动定价过滤, sku={}, activitySkuDetail:{}", areaSkuVO.getSku(), JSON.toJSONString(activitySkuDetail));
            return;
        }

        //保存计算参数，获取上一次计算参数，如果存在并发操作还是会影响本次折扣差值计算的
        WillExpirePriceLog lastPriceLog = willExpirePriceLogMapper.getLastBySkuAndArea(areaSkuVO.getAreaNo(), areaSkuVO.getSku());

        //保存计算参数
        WillExpirePriceLog willExpirePriceLog = quota.convertToWillExpirePriceLog();
        willExpirePriceLog.setSku(areaSkuVO.getSku());
        willExpirePriceLog.setAreaNo(areaSkuVO.getAreaNo());
        willExpirePriceLog.setReason(WillExpirePriceChangeReasonEnum.CALCULATE.getCode());
        willExpirePriceLog.setCalPrice(price);
        willExpirePriceLogMapper.insertSelective(willExpirePriceLog);

        //折扣变化小于等于 -1.5（-1.6视为小于）则触发临保折扣监控钉钉通知
        if (lastPriceLog != null && lastPriceLog.getDiscout().subtract(quota.getDiscount()).compareTo(BigDecimal.valueOf(0.16)) > 0) {
            discountNotify.setIsTrigger(true);
            logger.info("触发临保折扣监控钉钉通知, sku={}, areaNo:{}, warehouseNo:{}", areaSkuVO.getSku(), areaSkuVO.getAreaNo(),areaSkuVO.getWarehouseNo());
            if (null == discountNotify.getMaxOldDiscount() || discountNotify.getMaxOldDiscount().compareTo(lastPriceLog.getDiscout()) < 0){
                discountNotify.setMaxOldDiscount(lastPriceLog.getDiscout());
            }
            if (null == discountNotify.getMinOldDiscount() || discountNotify.getMinOldDiscount().compareTo(lastPriceLog.getDiscout()) > 0){
                discountNotify.setMinOldDiscount(lastPriceLog.getDiscout());
            }
            if (null == discountNotify.getMaxDiscount() || discountNotify.getMaxDiscount().compareTo(quota.getDiscount()) < 0){
                discountNotify.setMaxDiscount(quota.getDiscount());
            }
            if (null == discountNotify.getMinDiscount() || discountNotify.getMinDiscount().compareTo(quota.getDiscount()) > 0){
                discountNotify.setMinDiscount(quota.getDiscount());
            }
            if (null == discountNotify.getMaxCalPrice() || discountNotify.getMaxCalPrice().compareTo(price) < 0){
                discountNotify.setMaxCalPrice(price);
            }
            if (null == discountNotify.getMinCalPrice() || discountNotify.getMinCalPrice().compareTo(price) > 0){
                discountNotify.setMinCalPrice(price);
            }
        }

        //生成活动
//        generateMethod(activityVO, areaSkuVO, price, originalPrice);

        //更新临保活动sku的价格
        logger.info("开始更新临保活动sku的价格，sku:{}, areaNo:{}, price:{}, originalPrice:{}", areaSkuVO.getSku(), areaSkuVO.getAreaNo(), price, originalPrice);
        ActivitySkuQueryDTO skuQueryDTO = new ActivitySkuQueryDTO(areaSkuVO.getSku(), areaSkuVO.getAreaNo(), price, originalPrice);
        skuQueryDTO.setBasicInfoId(basicInfo.getId());
        updateActivityPrice(skuQueryDTO);


    }

    /**
     * 校验并删除特价活动中的sku
     * @param sku
     * @param areaNo
     */
    private void checkAndDeleteSku(String sku, Integer areaNo) {
        //获取大区
        Area area = areaMapper.selectByAreaNo(areaNo);
        Integer largeAreaNo = area.getLargeAreaNo();

        //找到大区或者运营城市的活动，不管活动是否生效，都先删除掉，类目、标签的需要使用的时候排除
        List<ActivityScopeQueryDTO> scopeList = Lists.newArrayList();
        scopeList.add(new ActivityScopeQueryDTO(Long.valueOf(areaNo), ScopeTypeEnum.AREA.getCode()));
        scopeList.add(new ActivityScopeQueryDTO(Long.valueOf(largeAreaNo), ScopeTypeEnum.LARGE_AREA.getCode()));
        List<ActivityItemScopeDTO> configs = activityBasicInfoMapper.listByScope(scopeList,
                ActivityTypeEnum.SPECIAL_PRICE.getCode(), null);
        if (CollectionUtil.isEmpty(configs)) {
            return;
        }
        List<Long> itemConfigIds = configs.stream().map(x -> x.getItemConfigId()).collect(Collectors.toList());
        List<ActivitySkuDetail> skuDetailList = activitySkuDetailMapper.listByItemConfigs(itemConfigIds, sku);
        if (CollectionUtil.isEmpty(skuDetailList)) {
            return;
        }
        List<Long> ids = skuDetailList.stream().map(x -> x.getId()).collect(Collectors.toList());
        logger.info("处理非临保活动的sku，detail_id:{}", JSON.toJSONString(ids));
        activitySkuDetailMapper.updateDelFlagBatch(ids);
    }

    /**
     * 计算参数并获取所有常规sku
     * @param areaSkuVO
     * @param originalPrice
     * @param quota
     * @return
     */
    private List<String> fillQuotaAndGetNormalSkus(AreaSkuVO areaSkuVO, BigDecimal originalPrice, Quota quota) {
        quota.setOriginalPrice(originalPrice);
        quota.setWarnDays(BigDecimal.valueOf(Optional.ofNullable(areaSkuVO.getWarnTime())
                .orElse(0)
        ));

        //查询sku有剩余批次（已兼容使用中心仓的库存情况）
        quota.setRemainingExpiredDays(BigDecimal.ZERO);
        List<StoreRecordVO> recordList = storeRecordMapper.selectQuantityBySkuAndWarehouseNo(areaSkuVO.getSku(), areaSkuVO.getWarehouseNo());
        recordList = recordList.stream()
                .filter(Objects::nonNull)
                .filter(el -> el.getQualityDate() != null)
                .filter(el -> Objects.equals(areaSkuVO.getWarehouseNo(), el.getAreaNo()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(recordList) && recordList.stream().noneMatch(el -> el.getQualityDate() == null)) {
            recordList.sort(Comparator.comparing(StoreRecordVO::getQualityDate));
            LocalDate qualityDate = recordList.get(0).getQualityDate();
            Duration.between(LocalDateTime.of(LocalDate.now(), LocalTime.MIN), LocalDateTime.of(qualityDate, LocalTime.MIN));
            long days = qualityDate.toEpochDay() - LocalDate.now().toEpochDay();
            quota.setRemainingExpiredDays(BigDecimal.valueOf(days));
        }
        List<String> normalSkus = productsPropertyService.getNormalSku((long) areaSkuVO.getPdId(), areaSkuVO.getSku());
        fillQuota(normalSkus, quota, areaSkuVO);

        //原有逻辑--此临保SKU在对应城市（T - 1）天销量
        long totalQuantity = ordersMapper.selectTotalQuantity(LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MIN),
                LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MAX), areaSkuVO.getAreaNo(), areaSkuVO.getSku()
        );

        //新改逻辑--此临保SKU所在库存仓（T - 1）天销量
        StockSkuStatistics stockSkuStatistics = stockSalesVolumeMapper.selectBySkuAndWerhouseNo(areaSkuVO.getWarehouseNo(), areaSkuVO.getSku());
        if (Objects.nonNull(stockSkuStatistics) && stockSkuStatistics.getQuantity() > 0) {
            logger.info("ActivityServiceImpl[]fillQuotaAndGetNormalSkus[]selectBySkuAndWerhouseNo[]stockSkuStatistics:{}", JSON.toJSONString(stockSkuStatistics));
            totalQuantity = stockSkuStatistics.getQuantity().longValue();
        }

        int q = areaSkuVO.getQuantity() - areaSkuVO.getSafeQuantity() - areaSkuVO.getLockQuantity();
        // 如果q <= 0， F取最大值
        quota.setSalesPace(q > 0 ? BigDecimal.valueOf(totalQuantity).divide(BigDecimal.valueOf(q), 4, RoundingMode.HALF_UP) : BigDecimal.valueOf(MAX_F));
        logger.info("ActivityServiceImpl[]fillQuotaAndGetNormalSkus[]quota[]areaSkuVO:{}, quota:{}, q:{}, totalQuantity:{}", JSON.toJSONString(areaSkuVO), JSON.toJSONString(quota), q, totalQuantity);
        // 如果 q <= 0 且销量<0取 -1，不参与计算
        if (q <= 0 && totalQuantity == 0) {
            quota.setSalesPace(BigDecimal.valueOf(-1));
        }
        return normalSkus;
    }

    private void fillQuota(List<String> normalSkus, Quota quota, AreaSkuVO areaSku) {
        if (normalSkus == null || normalSkus.isEmpty()) {
            quota.setSellOutRate(BigDecimal.ZERO);
            quota.setTurnoverRate(BigDecimal.ZERO);
        } else {
            List<StockSkuStatistics> list = stockSalesVolumeMapper.selectByDateAndSku(areaSku.getWarehouseNo(), normalSkus);
            quota.setSellOutRate(avg(list.stream().map(StockSkuStatistics::getDurationRate14d).filter(Objects::nonNull).collect(Collectors.toList())));
            quota.setTurnoverRate(avg(list.stream().map(StockSkuStatistics::getTurnoverRate7d)
                    .filter(Objects::nonNull)
                    .map(i -> {
                        if (i.compareTo(BigDecimal.valueOf(7)) > 0) {
                            return BigDecimal.valueOf(7);
                        } else {
                            return i;
                        }
                    })
                    .collect(Collectors.toList())));
        }
    }

    private BigDecimal avg(List<BigDecimal> nums) {
        if(nums.isEmpty()){
            return BigDecimal.ZERO;
        }
        return nums.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(nums.size()), 4, RoundingMode.HALF_UP);
    }

    /**
     * 计算临保价,公式见https://summerfarm.yuque.com/tech-xe1hi/ohf6w4/hy8ggq
     * @param q 参数
     * @param normalSkus 普通sku
     * @param areaSkuVO 区域
     * @return 价格
     */
    private BigDecimal calPrice(Quota q, List<String> normalSkus, AreaSkuVO areaSkuVO) {
        Integer areaNo = areaSkuVO.getAreaNo();

        // e取所有常规sku原价的平均值，如果不存在取50
        BigDecimal normalSalePrice = BigDecimal.valueOf(50);
        int count = 0;
        BigDecimal totalPrice = BigDecimal.ZERO;

        //原来E--取此城市临保SKU对应常规SKU（同品名、规格、口味）的原价的平均值，当此城市临保SKU对应常规SKU（同品名、规格、口味）的原价的平均值为空值时，将售价置为50
        /*for(String normalSku: normalSkus){
            AreaSku sku = areaSkuMapper.selectByAreaNoAndSku(areaNo, normalSku);
            if(sku != null){
                BigDecimal originalPrice = priceService.getNormalPrice(sku).getPrice();
                if (originalPrice != null) {
                    count++;
                    totalPrice = totalPrice.add(originalPrice);
                }
            }
        }*/

        //现在E--取临保SKU所在库存仓对应城市（测试城市除外）所有常规SKU（同品名、规格、口味）的原价的平均值即可。如果按前述逻辑计算出空值时，给售价赋值为50
        WarehouseBySkuAreaNoQueryReq queryReq;
        WarehouseBySkuAreaNoDataReq skuAreaNoDataReq;
        QueryWarehouseSkuInventoryReq skuInventoryReq;
        for(String normalSku: normalSkus){
            //获取sku对应的所有城市区域信息
            List<AreaSkuVO> areaSkuVOS = areaSkuMapper.selectAllBySkuAndOnSale(normalSku, CommonStatus.YES.getCode());
            if (CollectionUtils.isEmpty(areaSkuVOS)) {
                continue;
            }
            List<AreaSkuVO> areaSkuVOCollect = areaSkuVOS.stream().filter(e -> Objects.equals(e.getLargeAreaNo(), areaSkuVO.getLargeAreaNo())).collect(Collectors.toList());
            List<Integer> areaNoList = areaSkuVOCollect.stream().map(AreaSkuVO::getAreaNo).collect(Collectors.toList());;

            //根据大区下面的城市编号获取库存仓
            queryReq = new WarehouseBySkuAreaNoQueryReq();
            skuAreaNoDataReq = new WarehouseBySkuAreaNoDataReq();
            skuAreaNoDataReq.setSku(normalSku);
            skuAreaNoDataReq.setAreaNoList(areaNoList);
            queryReq.setAreaSkuList(Collections.singletonList(skuAreaNoDataReq));
            List<WarehouseBySkuAreaNoResp> queryBySkuAreNo = warehouseSkuAreaNoQueryFacade.queryBySkuAreNo(queryReq);
            if (CollectionUtils.isEmpty(queryBySkuAreNo)) {
                logger.error("ActivityServiceImpl[]calPrice[]queryBySkuAreNo[]empty areaSkuVO:{},queryReq:{}", JSON.toJSONString(areaSkuVO), JSON.toJSONString(queryReq));
                continue;
            }

            //判断大区库存仓是否同一个 同一个运营城市下面对应多个库存仓取第一个 假如是同一个判断是否仅有一个库存仓有库存 是-继续往下走 否-钉钉消息提醒并剔除sku（大区维度剔除）
            Map<Integer, List<WarehouseBySkuAreaNoResp>> areaGroup = queryBySkuAreNo.stream()
                    .collect(Collectors.groupingBy(WarehouseBySkuAreaNoResp::getAreaNo));
            Set<Integer> warehouseNoSet = new HashSet<>();
            for (Integer area : areaGroup.keySet()) {
                List<WarehouseBySkuAreaNoResp> warehouseBySkuAreaNoResps = areaGroup.get(area);
                if (CollectionUtils.isEmpty(warehouseBySkuAreaNoResps)) {
                    continue;
                }
                warehouseNoSet.add(warehouseBySkuAreaNoResps.get(0).getWarehouseNo());
            }

            if (warehouseNoSet.size() > 1) {
                //判断是否仅有一个仓有库存
                skuInventoryReq = new QueryWarehouseSkuInventoryReq();
                skuInventoryReq.setTenantId(1L);
                skuInventoryReq.setWarehouseNoList(new ArrayList<>(warehouseNoSet));
                skuInventoryReq.setSkuCode(normalSku);
                WarehouseSkuInventoryResp skuInventory = saleInventoryFacade.queryWarehouseSkuInventory(skuInventoryReq);
                if (Objects.isNull(skuInventory) || CollectionUtils.isEmpty(skuInventory.getWarehouseSkuInventoryDetailResDTOS())) {
                    logger.error("ActivityServiceImpl[]calPrice[]queryWarehouseSkuInventory[]empty areaSkuVO:{},skuInventoryReq:{}", JSON.toJSONString(areaSkuVO), JSON.toJSONString(skuInventoryReq));
                    continue;
                }

                //判断是否仅有一个库存仓有库存
                List<WarehouseSkuInventoryDetailResDTO> detailResDTOList = skuInventory.getWarehouseSkuInventoryDetailResDTOS()
                        .stream().filter(e -> e.getAvailableQuantity() > 0).collect(Collectors.toList());
                if (detailResDTOList.size() != 1) {
                    logger.error("ActivityServiceImpl[]calPrice[]queryWarehouseSkuInventory[]warehouseNo empty or more than one areaSkuVO:{}, sku:{}", JSON.toJSONString(areaSkuVO), JSON.toJSONString(normalSku));
                    //todo 并发送群消息提醒
                    continue;
                }
            }

            //获取大区下面价格进行运算
            List<AreaSku> areaSkus = areaSkuMapper.selectSkuAreaPrice(normalSku, areaNoList);
            if (CollectionUtils.isEmpty(areaSkus)) {
                logger.error("ActivityNewServiceImpl[]calPrice[]queryWarehouseSkuInventory[]selectSkuAreaPrice[]empty areaSkuVO:{}, sku:{}", JSON.toJSONString(areaSkuVO), JSON.toJSONString(normalSku));
                continue;
            }
            BigDecimal reduce = areaSkus.stream().map(AreaSku::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            totalPrice = totalPrice.add(reduce);
            count += areaSkus.size();
        }
        if(count > 0){
            normalSalePrice = totalPrice.divide(BigDecimal.valueOf(count), 4, RoundingMode.HALF_UP);
        }

        logger.info("ActivityNewServiceImpl[]calPrice[]areaSkuVO:{},normalSkus:{},normalSalePrice:{},count:{},totalPrice:{}", JSON.toJSONString(areaSkuVO), JSON.toJSONString(normalSkus), normalSalePrice, count, totalPrice);
        q.setNormalSalePrice(normalSalePrice);
        if (q.getNormalSalePrice().compareTo(BigDecimal.valueOf(500)) > 0) {
            q.setNormalSalePrice(BigDecimal.valueOf(500));
        }
        if (q.getSalesPace().compareTo(BigDecimal.valueOf(MAX_F)) > 0) {
            q.setSalesPace(BigDecimal.valueOf(MAX_F));
        }

        BigDecimal rate;
        if (q.getOriginalPrice().compareTo(BigDecimal.valueOf(9.9)) < 0) {
            q.setDiscount(BigDecimal.ONE);
            logger.info("原价<9.9 所以price={}, quota ={}, areaNo={}", q.getOriginalPrice(), q, areaNo);
            return q.getOriginalPrice();
        } else if (q.getNormalSalePrice().compareTo(BigDecimal.valueOf(9.9)) < 0 || q.getWarnDays().compareTo(BigDecimal.ZERO) == 0 || q.getRemainingExpiredDays().compareTo(BigDecimal.ZERO) <= 0) {
            rate = BigDecimal.ONE;
        } else {
            rate = q.getRemainingExpiredDays().divide(q.getWarnDays(), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(10))
                    .add(q.getSellOutRate().multiply(BigDecimal.valueOf(2)))
                    .add(q.getTurnoverRate().divide(BigDecimal.valueOf(7), 4, RoundingMode.HALF_UP))
                    .add(q.getNormalSalePrice().divide(BigDecimal.valueOf(200), 4, RoundingMode.HALF_UP));

            if (q.getSalesPace().compareTo(BigDecimal.valueOf(-1)) != 0) {
                rate = rate.add(q.getSalesPace().multiply(q.getSalesPace()).multiply(BigDecimal.valueOf(-66.6))
                        .add(q.getSalesPace().multiply(BigDecimal.valueOf(25.43)))
                        .subtract(BigDecimal.valueOf(1.5))
                );
            }
            rate = rate.divide(BigDecimal.valueOf(10), 4,RoundingMode.HALF_UP);
        }
        logger.info("ActivityServiceImpl[]calPrice[]rate[]areaSkuVO:{}, rate:{}", JSON.toJSONString(areaSkuVO), rate);
        //当原有的折扣率rate大于等于1时会变为9.9折
        if (rate.compareTo(BigDecimal.ONE) >= 0) {
            rate = BigDecimal.valueOf(0.99);
        }
        if (rate.compareTo(BigDecimal.valueOf(0.1)) < 0) {
            rate = BigDecimal.valueOf(0.1);
        }
        BigDecimal price = rate.multiply(q.getOriginalPrice()).setScale(2, RoundingMode.HALF_UP);
        if (price.compareTo(BigDecimal.valueOf(9.9)) < 0) {
            price = BigDecimal.valueOf(9.9);
        }
        q.setDiscount(rate);
        logger.info("ActivityServiceImpl[]calPrice[]queryBySkuAreNo[]areaSkuVO:{}, rate:{}, quota:{}, price:{}, areaNo:{}", JSON.toJSONString(areaSkuVO), rate, q, price, areaNo);
        return price;
    }

    /**
     * 初始化临保城市活动
     */
    private void initActivity() {
        //取出所有已设活动,但城市关闭的活动
        List<ActivityScopeConfig> scopeConfigs = activityScopeConfigMapper.selectAllExpiredScope(null);
        if (CollectionUtil.isEmpty(scopeConfigs)) {
            return;
        }
        List<Integer> areaNos = scopeConfigs.stream().filter(x -> x.getScopeId() != null).map(x -> Integer.valueOf(x.getScopeId().toString()))
                .collect(Collectors.toList());
        Map<Long, ActivityScopeConfig> scopeConfigMap = scopeConfigs.stream()
                .collect(Collectors.toMap(x -> x.getScopeId(), Function.identity()));
        //判断这些城市是否关闭
        List<Area> areas = areaMapper.selectAreaNos(areaNos);
        List<Integer> closedAreaNos = areas.stream().filter(x -> !x.getStatus()).map(t -> t.getAreaNo())
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(closedAreaNos)) {
            for (Integer closedAreaNo : closedAreaNos) {
                //关闭该城市的临保活动
                ActivityScopeConfig scopeConfig = scopeConfigMap.get(closedAreaNo);
                if (scopeConfig == null) {
                    continue;
                }
                Long basicInfoId = scopeConfig.getBasicInfoId();
                try {
                    // 按城市关闭开关，商品不做处理
                    activityNewService.openDown(basicInfoId, 0);
                } catch (Exception e) {
                    logger.error("运营区域：{}关闭临保活动失败, 活动id:{}", closedAreaNo, basicInfoId);
                }

            }
        }

        //找到所有还未开放的城市
        List<Integer> areaNoAll = areaMapper.selectAreaNo();
        areaNoAll.removeAll(areaNos);

        if (CollectionUtil.isNotEmpty(areaNoAll)) {
            for (Integer areaNo : areaNoAll) {
                ((ActivityService)AopContext.currentProxy()).createNewExpiredActivity(areaNo);
            }

        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void createNewExpiredActivity(Integer areaNo) {
        //系统创建临保活动，但是没有商品明细，临保商品需要后续添加
        ActivityBasicInfo basicInfo = new ActivityBasicInfo();
        Area area = areaMapper.selectByAreaNo(areaNo);
        basicInfo.setName("临保活动-" + area.getAreaName());
        basicInfo.setType(ActivityTypeEnum.NEAR_EXPIRED.getCode());
        basicInfo.setTag(ActivityTagEnum.NEAR_EXPIRED_CLEARANCE.getCode());
        basicInfo.setRemark("系统创建");
        basicInfo.setIsPermanent(1);
        basicInfo.setStatus(1);
        basicInfo.setCreatorId(0);
        activityBasicInfoMapper.insertSelective(basicInfo);

        Long basicInfoId = basicInfo.getId();
        ActivitySceneConfig sceneConfig = new ActivitySceneConfig();
        sceneConfig.setBasicInfoId(basicInfoId);
        sceneConfig.setPlatform(0);
        activitySceneConfigMapper.insertSelective(sceneConfig);

        ActivityItemConfig itemConfig = new ActivityItemConfig();
        itemConfig.setPricingType(0);
        itemConfig.setGoodSelectWay(GoodSelectWayEnum.SKU.getCode());
        itemConfig.setBasicInfoId(basicInfoId);
        activityItemConfigMapper.insertSelective(itemConfig);

        ActivityScopeConfig scopeConfig = new ActivityScopeConfig();
        scopeConfig.setBasicInfoId(basicInfoId);
        scopeConfig.setScopeId(areaNo.longValue());
        scopeConfig.setScopeType(ScopeTypeEnum.AREA.getCode());
        activityScopeConfigMapper.insertSelective(scopeConfig);
        logger.info("运营区域：{}的临保活动创建成功", areaNo);
    }

    /**
     * 临保活动，一个城市对应一个活动
     * @param skuQueryDTO
     */
    private void updateActivityPrice(ActivitySkuQueryDTO skuQueryDTO) {
        Long basicInfoId = skuQueryDTO.getBasicInfoId();
        String sku = skuQueryDTO.getSku();
        ActivityItemConfig itemConfig = activityItemConfigMapper.getByInfoId(basicInfoId);
        ActivitySkuDetail skuDetail = activitySkuDetailMapper.selectBySku(
                itemConfig.getId(), sku);
        //没有就新增，有就更新
        if (skuDetail == null) {
            skuDetail = new ActivitySkuDetail();
            skuDetail.setSku(sku);
            skuDetail.setItemConfigId(itemConfig.getId());
            skuDetail.setAdjustType(AdjustTypeEnum.FIXED_PRICE.getCode());
            skuDetail.setAmount(skuQueryDTO.getActivityPrice());
            skuDetail.setRoundingMode(0);
            skuDetail.setPlanQuantity(100000);
            skuDetail.setActualQuantity(100000);
            skuDetail.setLadderConfig(ActivityLadderConfigDTO.initDefaultLadderConfig(skuQueryDTO.getActivityPrice(), 0, AdjustTypeEnum.FIXED_PRICE.getCode()));
            activitySkuDetailMapper.insertSelective(skuDetail);
            ActivitySkuPrice skuPrice = new ActivitySkuPrice();
            skuPrice.setSkuDetailId(skuDetail.getId());
            skuPrice.setBasicInfoId(basicInfoId);
            skuPrice.setSku(sku);
            skuPrice.setAreaNo(skuQueryDTO.getAreaNo());
            skuPrice.setActivityPrice(skuQueryDTO.getActivityPrice());
            skuPrice.setLadderPrice(ActivityLadderPriceDTO.initDefaultLadderPrice(skuQueryDTO.getActivityPrice()));
            skuPrice.setSalePrice(skuQueryDTO.getOriginalPrice());
            skuPrice.setUpdaterId(0);
            activitySkuPriceMapper.insertSelective(skuPrice);
        } else {
            skuDetail.setAmount(skuQueryDTO.getActivityPrice());
            skuDetail.setLadderConfig(ActivityLadderConfigDTO.initDefaultLadderConfig(skuQueryDTO.getActivityPrice(), 0, AdjustTypeEnum.FIXED_PRICE.getCode()));
            activitySkuDetailMapper.updateByPrimaryKeySelective(skuDetail);
            ActivitySkuPrice skuPrice = new ActivitySkuPrice();
            skuPrice.setSkuDetailId(skuDetail.getId());
            skuPrice.setBasicInfoId(basicInfoId);
            skuPrice.setSku(sku);
            skuPrice.setAreaNo(skuQueryDTO.getAreaNo());
            skuPrice.setActivityPrice(skuQueryDTO.getActivityPrice());
            skuPrice.setLadderPrice(ActivityLadderPriceDTO.initDefaultLadderPrice(skuQueryDTO.getActivityPrice()));
            skuPrice.setSalePrice(skuQueryDTO.getOriginalPrice());
            activitySkuPriceMapper.updatePriceSelective(skuPrice);
        }
    }



    @Override
    public void handleWillExpireSkuStockTransfer(DtsModel dtsModel) {
        Map<String, String> newData = dtsModel.getData().get(0);
        String sku = newData.get("sku");
        if (StringUtils.isBlank(sku)) {
            logger.warn("【临保品恢复原价】临保sku不存在,dtsModel:{}", JSON.toJSONString(dtsModel));
            return;
        }

        Inventory inventory = inventoryMapper.selectOneBySku(sku);
        String typeName = newData.get("type_name");
        //判断是否临保库存转换入库、采购单入库或者调拨入库
        if (!(StoreRecordType.expiredRestorePrice(typeName)
                && Objects.equals(inventory.getExtType(), InventoryExtTypeEnum.TEMPORARY_INSURANCE.type()))) {
            return;
        }
        Integer warehouseNo = Integer.valueOf(newData.get("area_no"));
        logger.info("【临保品恢复原价】临保品sku={}执行:{},warehouseNo:{}", sku, typeName, warehouseNo);
        // 获取需要调整价格的城市运营区域
        Set<Integer> areaNos = areaStoreServiceHelper.getPriceAdjustAreaNos(warehouseNo, sku);
        if (CollectionUtil.isEmpty(areaNos)) {
            logger.warn("【临保品恢复原价】当前临保sku在库存仓不存在需要调整价格的城市运营区域,sku:{}, warehouseNo:{}", sku, warehouseNo);
            return;
        }
        //批量查询城市临保活动
//        List<Activity> activities = activityMapper.listActIdsByExtType(1, areaNos);

        List<ActivityScopeConfig> scopeConfigs = activityScopeConfigMapper.selectAllExpiredScope(areaNos);
        if (CollectionUtil.isEmpty(scopeConfigs)) {
            logger.warn("【临保品恢复原价】区域内临保活动不存在, sku:{}, areaNos:{}", sku, areaNos);
            return;
        }
        areaNos = scopeConfigs.stream().distinct().map(x -> x.getScopeId().intValue()).collect(Collectors.toSet());
        //批量获取临保sku在临保活动区域的信息
        Map<Integer, AreaSku> areaSkuMap = areaSkuMapper.selectBySkuAndAreaNos(sku, areaNos).stream().collect(Collectors.toMap(AreaSku::getAreaNo, Function.identity()));
        if (CollectionUtil.isEmpty(areaSkuMap)) {
            return;
        }
        logger.info("【临保品恢复原价】临保品sku={}执行:{}，变更临保价=临保原价", sku, typeName);
        for (ActivityScopeConfig scopeConfig : scopeConfigs) {
            AreaSku areaSku = areaSkuMap.get(scopeConfig.getScopeId().intValue());
            if (areaSku == null) {
                continue;
            }
            //避免处理失败导致无法继续处理后续的数据
            try {
                updateActivityPrice(scopeConfig, areaSku);
            } catch (Exception e) {
                logger.warn("【临保品恢复原价】临保入库操作:{}，跳过处理，sku={},activityId={}", typeName, sku, scopeConfig.getBasicInfoId());
            }
        }

    }

    @Override
    public void sendDiscountNotification(List<AreaSkuVO> areaSkuList) {
        logger.info("待处理的临保数据areaSkuList ：{}", JSON.toJSONString(areaSkuList));
        Map<String,List<AreaSkuVO>> groupMap = new HashMap<>(16);
        areaSkuList.forEach(e->{
            String strKey = e.getWarehouseNo() + "_" + e.getSku();
            if (!groupMap.containsKey(strKey)){
                List<AreaSkuVO> skuGroupList = new ArrayList<>();
                skuGroupList.add(e);
                groupMap.put(strKey,skuGroupList);
            }else {
                groupMap.get(strKey).add(e);
            }
        });
        for (Map.Entry<String,List<AreaSkuVO>> entry : groupMap.entrySet()){
            WillExpiredDiscountNotifyDTO discountNotify = new WillExpiredDiscountNotifyDTO(null,null,null,null,null,null,false);
            for (AreaSkuVO areaSkuVO : entry.getValue()) {
                //生成临保活动池
                try {
                    ((ActivityService)AopContext.currentProxy()).generateExpiredActivity(areaSkuVO,discountNotify);
                } catch (Exception e) {
                    discountNotify.setIsTrigger(false);
                    logger.error("转换临保价出错:{}", areaSkuVO, e);
                }
            }
            if (discountNotify.getIsTrigger()){
                String[] spiltList = entry.getKey().split("_");
                Integer warehouseNo = Integer.valueOf(spiltList[0]);
                String sku = spiltList[1];
                ProductVO product = productsMapper.queryProductBySku(sku);
                AreaSkuVO areaSkuVO = new AreaSkuVO();
                areaSkuVO.setWarehouseNo(warehouseNo);
                areaSkuVO.setPdName(product.getPdName());
                areaSkuVO.setWeight(product.getWeight());
                areaSkuVO.setSku(sku);
                sendDiscountExceptionNotification(areaSkuVO,discountNotify);
            }
        }
    }

    /**
     * 变更临保价=原价，并记录
     * @param scopeConfig
     * @param areaSku
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateActivityPrice(ActivityScopeConfig scopeConfig, AreaSku areaSku) {
        String sku = areaSku.getSku();
        Long basicInfoId = scopeConfig.getBasicInfoId();
        //临保sku可能当前还没有在临保活动中
        List<ActivitySkuDetail> skuDetailList = activitySkuDetailMapper.listByBasicInfoIds(
                Lists.newArrayList(basicInfoId), sku);
        if (CollectionUtil.isEmpty(skuDetailList)) {
            logger.warn("【临保品恢复原价】临保sku={}不在临保活动id={}内,areaNo={}", sku, basicInfoId, areaSku.getAreaNo());
            return;
        }
        ActivitySkuDetail skuDetail = skuDetailList.get(0);
        //修改临保活动内的指定价的价格
        skuDetail.setAmount(areaSku.getPrice());
        skuDetail.setLadderConfig(ActivityLadderConfigDTO.initDefaultLadderConfig(areaSku.getPrice(), 0, AdjustTypeEnum.FIXED_PRICE.getCode()));
        activitySkuDetailMapper.updateByPrimaryKeySelective(skuDetail);
        //处理临保价格
        ActivitySkuPrice activitySkuPrice = activitySkuPriceMapper.selectByDetailId(
                skuDetail.getId(), sku, areaSku.getAreaNo());
        if (activitySkuPrice == null) {
            logger.warn("【临保品恢复原价】临保sku={}在临保活动id={}内活动价不存在,areaNo={}", sku, basicInfoId, areaSku.getAreaNo());
            ActivitySkuPrice skuPrice = new ActivitySkuPrice();
            skuPrice.setSkuDetailId(skuDetail.getId());
            skuPrice.setBasicInfoId(basicInfoId);
            skuPrice.setSku(sku);
            skuPrice.setAreaNo(areaSku.getAreaNo());
            skuPrice.setActivityPrice(areaSku.getPrice());
            skuPrice.setLadderPrice(ActivityLadderPriceDTO.initDefaultLadderPrice(areaSku.getPrice()));
            skuPrice.setSalePrice(areaSku.getPrice());
            skuPrice.setUpdaterId(0);
            activitySkuPriceMapper.insertSelective(skuPrice);
        } else {
            activitySkuPrice.setSalePrice(areaSku.getPrice());
            activitySkuPrice.setActivityPrice(areaSku.getPrice());
            activitySkuPrice.setLadderPrice(ActivityLadderPriceDTO.initDefaultLadderPrice(areaSku.getPrice()));
            activitySkuPrice.setUpdaterId(0);
            activitySkuPriceMapper.updatePriceSelective(activitySkuPrice);
        }

        WillExpirePriceLog priceLog = new WillExpirePriceLog();
        priceLog.setSku(sku);
        priceLog.setAreaNo(scopeConfig.getScopeId().intValue());
        priceLog.setCalPrice(areaSku.getPrice());
        priceLog.setOriginalPrice(areaSku.getPrice());
        //临保价等于当前临保原价，折扣为10折
        priceLog.setDiscout(BigDecimal.valueOf(1));
        priceLog.setReason(WillExpirePriceChangeReasonEnum.TRANSFER_TO_STORE.getCode());
        willExpirePriceLogMapper.insertSelective(priceLog);
        logger.info("【临保品恢复原价】临保sku={}在临保活动id={}内,areaNo={},已执行恢复原价={}", sku, basicInfoId, areaSku.getAreaNo(), areaSku.getPrice());
    }

    private void sendDiscountExceptionNotification(AreaSkuVO areaSkuVO, WillExpiredDiscountNotifyDTO discountNotify) {
        WarehouseStorageCenter storageCenter = warehouseStorageCenterMapper.selectByWarehouseNo(areaSkuVO.getWarehouseNo());
        String newDiscount = this.bigDecimalToString(discountNotify.getMaxDiscount(),discountNotify.getMinDiscount(),DiscountTypeEnum.NEW_DISCOUNT.getType());
        String newCalPrice = this.bigDecimalToString(discountNotify.getMaxCalPrice(),discountNotify.getMinCalPrice(),DiscountTypeEnum.NEW_CAL_PRICE.getType());
        String oldDiscount = this.bigDecimalToString(discountNotify.getMaxOldDiscount(),discountNotify.getMinOldDiscount(),DiscountTypeEnum.OLD_DISCOUNT.getType());
        String content = MessageFormat.format(DISCOUNT_MSG_TEMPLATE, areaSkuVO.getPdName(), areaSkuVO.getWeight(),
                areaSkuVO.getSku(), storageCenter.getWarehouseName(), newDiscount, newCalPrice, oldDiscount);
        logger.info("临保折扣监控, content={}",content);
        Map<String, String> msg = new HashMap<>();
        msg.put("title", "【临保折扣监控】");
        msg.put("text", content);
        Config config = configMapper.selectOne(DISCOUNT_EXCEPTION_KEY);
        DingTalkRobotUtil.sendMarkDownMsg(config.getValue(), () -> msg, null);
    }

    private String bigDecimalToString(BigDecimal maxData,BigDecimal minData,Integer type){
        String splicing = null;
        if (type.equals(DiscountTypeEnum.NEW_DISCOUNT.getType())){
            if (maxData.compareTo(minData) == 0){
                splicing = maxData.multiply(BigDecimal.valueOf(10)).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
            }else {
                splicing = minData.multiply(BigDecimal.valueOf(10)).setScale(1, BigDecimal.ROUND_HALF_UP).toString() + "-" + maxData.multiply(BigDecimal.valueOf(10)).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
            }
        }else if (type.equals(DiscountTypeEnum.NEW_CAL_PRICE.getType())){
            if (maxData.compareTo(minData) == 0){
                splicing = maxData.toString();
            }else {
                splicing = minData.toString() + "-" + maxData.toString();
            }
        }else if (type.equals(DiscountTypeEnum.OLD_DISCOUNT.getType())){
            if (maxData.compareTo(minData) == 0){
                splicing = maxData.multiply(BigDecimal.valueOf(10)).setScale(1).toString();
            }else {
                splicing = minData.multiply(BigDecimal.valueOf(10)).setScale(1).toString() + "-" + maxData.multiply(BigDecimal.valueOf(10)).setScale(1).toString();
            }
        }
        return splicing;
    }

    /**
     * 临保活动自动迁移
     */
    @Override
    public void transferExpired(Integer areaNo) {
        List<Activity> activities = activityMapper.listByExtType();
        //按areaNo维度迁移创建临保活动
        for (Activity activity : activities) {
            try {
                if (areaNo != null) {
                    if (Objects.equals(areaNo, activity.getAreaNo())) {
                        ((ActivityService)AopContext.currentProxy()).createNewActivity(activity);
                        logger.info("【新营销活动】临保活动:{}迁移成功", activity.getName());
                    }
                } else {
                    ((ActivityService)AopContext.currentProxy()).createNewActivity(activity);
                    logger.info("【新营销活动】临保活动:{}迁移成功", activity.getName());
                }
            } catch (Exception e) {
                logger.error("【新营销活动】临保活动:{}迁移失败, cause:{}", activity.getId(), Throwables.getStackTraceAsString(e));
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createNewActivity(Activity activity) {
        ActivityBasicInfo basicInfo = new ActivityBasicInfo();
        Area area = areaMapper.selectByAreaNo(activity.getAreaNo());
        basicInfo.setName("临保活动-" + area.getAreaName());
        basicInfo.setType(ActivityTypeEnum.NEAR_EXPIRED.getCode());
        basicInfo.setTag(ActivityTagEnum.NEAR_EXPIRED_CLEARANCE.getCode());
        basicInfo.setRemark("系统创建");
        basicInfo.setIsPermanent(1);
        basicInfo.setStatus(1);
        basicInfo.setCreatorId(0);
        activityBasicInfoMapper.insertSelective(basicInfo);

        Long basicInfoId = basicInfo.getId();
        ActivitySceneConfig sceneConfig = new ActivitySceneConfig();
        sceneConfig.setBasicInfoId(basicInfoId);
        sceneConfig.setPlatform(0);
        activitySceneConfigMapper.insertSelective(sceneConfig);

        ActivityItemConfig itemConfig = new ActivityItemConfig();
        itemConfig.setPricingType(0);
        itemConfig.setGoodSelectWay(GoodSelectWayEnum.SKU.getCode());
        itemConfig.setBasicInfoId(basicInfoId);
        activityItemConfigMapper.insertSelective(itemConfig);
        Long itemConfigId = itemConfig.getId();
        List<ActivitySku> activitySkuList = activitySkuMapper.listByActivityId(activity.getId());
        for (ActivitySku activitySku : activitySkuList) {
            ActivitySkuDetail skuDetail = new ActivitySkuDetail();
            skuDetail.setSku(activitySku.getSku());
            skuDetail.setItemConfigId(itemConfigId);
            skuDetail.setAdjustType(AdjustTypeEnum.FIXED_PRICE.getCode());
            skuDetail.setRoundingMode(0);
            skuDetail.setAmount(activitySku.getActivityPrice());
            skuDetail.setPlanQuantity(100000);
            skuDetail.setActualQuantity(100000);
            skuDetail.setLadderConfig(ActivityLadderConfigDTO.initDefaultLadderConfig(activitySku.getActivityPrice(), 0, AdjustTypeEnum.FIXED_PRICE.getCode()));
            activitySkuDetailMapper.insertSelective(skuDetail);

            ActivitySkuPrice skuPrice = new ActivitySkuPrice();
            skuPrice.setSkuDetailId(skuDetail.getId());
            skuPrice.setBasicInfoId(basicInfoId);
            skuPrice.setSku(activitySku.getSku());
            skuPrice.setAreaNo(activity.getAreaNo());
            skuPrice.setActivityPrice(activitySku.getActivityPrice());
            skuPrice.setLadderPrice(ActivityLadderPriceDTO.initDefaultLadderPrice(activitySku.getActivityPrice()));
            skuPrice.setSalePrice(activitySku.getSalePrice());
            skuPrice.setUpdaterId(0);
            activitySkuPriceMapper.insertSelective(skuPrice);
        }

        ActivityScopeConfig scopeConfig = new ActivityScopeConfig();
        scopeConfig.setBasicInfoId(basicInfoId);
        scopeConfig.setScopeId(activity.getAreaNo().longValue());
        scopeConfig.setScopeType(ScopeTypeEnum.AREA.getCode());
        activityScopeConfigMapper.insertSelective(scopeConfig);
    }

}
