package net.summerfarm.service.impl;

import net.summerfarm.mapper.manage.StockTaskAbnormalRecordMapper;
import net.summerfarm.model.domain.StockTaskAbnormalRecord;
import net.summerfarm.service.StockTaskAbnormalRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/7/13
 */
@Service
public class StockTaskAbnormalRecordServiceImpl implements StockTaskAbnormalRecordService {

    @Resource
    private StockTaskAbnormalRecordMapper abnormalRecordMapper;

    @Override
    public List<StockTaskAbnormalRecord> select(Integer stockTaskId, String sku) {
        return abnormalRecordMapper.select(stockTaskId,sku);
    }

    @Override
    public void insert(StockTaskAbnormalRecord insert) {
        abnormalRecordMapper.insert(insert);
    }
}
