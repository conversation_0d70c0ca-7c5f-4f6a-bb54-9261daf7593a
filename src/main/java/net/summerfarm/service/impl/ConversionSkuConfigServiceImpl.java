package net.summerfarm.service.impl;

import com.aliyun.odps.Instance;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.task.SQLTask;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.util.CommonFileUtils;
import net.summerfarm.common.util.DateUtil;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.config.OdpsConfig;
import net.summerfarm.contexts.Global;
import net.summerfarm.mapper.manage.ConversionSkuQuantityMapper;
import net.summerfarm.mapper.offline.SkuSaleStatisticsMapper;
import net.summerfarm.model.domain.Category;
import net.summerfarm.model.domain.ConversionSkuQuantity;
import net.summerfarm.model.domain.offline.SkuSaleStatisticsPO;
import org.apache.commons.lang3.StringUtils;
import net.summerfarm.enums.CurrencyStatusEnum;
import net.summerfarm.mapper.manage.ConversionSkuConfigMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.model.domain.ConversionSkuConfig;
import net.summerfarm.model.vo.ConversionSkuConfigVO;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.service.ConversionSkuConfigService;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import org.apache.commons.lang3.math.NumberUtils;


import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ct
 * create at:  2021/10/19  14:12
 */
@Service
public class ConversionSkuConfigServiceImpl implements ConversionSkuConfigService {

    @Resource
    ConversionSkuConfigMapper conversionSkuConfigMapper;
    @Resource
    InventoryMapper inventoryMapper;
    @Resource
    WarehouseStorageService warehouseStorageService;
    @Resource
    BaseService baseService;
    @Resource
    ConversionSkuQuantityMapper conversionSkuQuantityMapper;
    @Resource
    private SkuSaleStatisticsMapper skuSaleStatisticsMapper;


    private static final String IS_NOT_NULL = "必填选项不能为空";

    private static final String SKU_SAVE = "转入sku已存在";

    private static final String OPPOSITE_SKU= "不能配置相反比例的sku";

    private static final String EXCEL_SKU_SAVE = "表格sku重复";

    private static final String SKU_NOT_SAVE = "sku不存在";

    private static final String SPU_NOT_LIKE = "转出、转入sku需为同一spu";

    private static final String RATE_NOT_NULL = "非鲜果转换比例必填";

    private static final String NOT_FRUIT_RATES_ERROR =  "非鲜果转换比例为1:N，请重新填写。";

    private static final String FRUIT_RATES_ERROR =  "鲜果转换比例为M:N，请重新填写。";

    private static final String IS_NOT_WAREHOUSE = "库存仓不存在";

    private static final String IS_LIKE_SKU = "转出sku和转入sku相同";

    /**
    * 非鲜果类目转换比例正则表达式
    */
    private static final String NOT_FRUIT_RATES = "^1:[1-9][0-9]*";
    /**
     * 鲜果类目转换比例正则表达式
     */
    private static final String FRUIT_RATES = "^[1-9][0-9]*:[1-9][0-9]*";

    protected final Logger logger = LoggerFactory.getLogger(getClass());



    @Override
    public AjaxResult selectConfigVO(Integer pageIndex, Integer pageSize,ConversionSkuConfigVO vo) {
        PageHelper.startPage(pageIndex, pageSize);
        List<ConversionSkuConfigVO> conversionSkuConfigVOS = conversionSkuConfigMapper.selectListConfigVO(vo);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(conversionSkuConfigVOS));
    }

    @Override
    public AjaxResult saveConfigVO(ConversionSkuConfig conversionSkuConfig) {
        //校验配置信息
        String errMsg = checkoutConfig(conversionSkuConfig);
        if(!StringUtils.isEmpty(errMsg)){
           return AjaxResult.getError(errMsg);
        }
        conversionSkuConfig.setAdminId(baseService.getAdminId());
        conversionSkuConfig.setStatus(CurrencyStatusEnum.EFFECTIVE.ordinal());
        conversionSkuConfigMapper.saveConfig(conversionSkuConfig);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult importExcel(MultipartFile file) {
        Workbook workbook = null;
        try {
            Integer adminId = baseService.getAdminId();
            String adminName = baseService.getAdminName();
            workbook = WorkbookFactory.create(file.getInputStream());
            ExcelUtils en = new ExcelUtils(workbook);
            List<Map<String, String>> allData = en.getMapData();
            List<ConversionSkuConfigVO> conversionSkuConfigVOS = new ArrayList<>();
            for (Map<String, String> allDatum : allData) {
                conversionSkuConfigVOS.add(tranfMsg(allDatum));
            }
            //校验重复,校验库存仓名称是否正确
            //根据库存仓编号分组处理
            //先过滤没有库存仓编号的数据
            List<ConversionSkuConfigVO> rightVOList = new ArrayList<>();
            Map<Integer, List<ConversionSkuConfigVO>> rightMap = new HashMap<>();
            List<ConversionSkuConfigVO> errVOList = conversionSkuConfigVOS.stream().filter(vo -> StringUtils.isEmpty(vo.getWarehouseName()) || StringUtils.isEmpty(vo.getInSku()) || StringUtils.isEmpty(vo.getOutSku())).collect(Collectors.toList());
            errVOList.forEach(err -> err.setErrorMsg(IS_NOT_NULL));
            //过滤错误信息
            List<ConversionSkuConfigVO> collect = conversionSkuConfigVOS.stream().filter(vo -> !errVOList.contains(vo)).collect(Collectors.toList());
            Map<String, List<ConversionSkuConfigVO>> nameMap = collect.stream().collect(Collectors.groupingBy(ConversionSkuConfigVO::getWarehouseName));
            //获取库存仓编号
            nameMap.forEach((warehouseName,voList) ->{
                WarehouseStorageCenter center = warehouseStorageService.selectByWarehouseNameForSummerfarm(warehouseName);
                if(Objects.isNull(center)){
                    voList.forEach( x -> x.setErrorMsg(IS_NOT_WAREHOUSE));
                    errVOList.addAll(voList);
                } else {
                    voList.forEach( x -> {
                        x.setWarehouseNo(center.getWarehouseNo());
                        x.setAdminId(adminId);
                        x.setAdminName(adminName);
                    });
                    rightMap.put(center.getWarehouseNo(),voList);
                }
            });
            rightMap.forEach((warehouseNo,voList) ->{
                Map<String, List<ConversionSkuConfigVO>> skuVOMap = voList.stream().collect(Collectors.groupingBy(ConversionSkuConfig::getInSku));
                //判断数据是否有重复
                skuVOMap.forEach((sku,configList) ->{
                    if(configList.size() > NumberUtils.INTEGER_ONE){
                        configList.forEach(x -> x.setErrorMsg(EXCEL_SKU_SAVE));
                        errVOList.addAll(configList);
                        //无重复校验信息后新增
                    } else {
                        ConversionSkuConfigVO conversionSkuConfigVO = configList.get(NumberUtils.INTEGER_ZERO);
                        String errMsg = checkoutConfig(conversionSkuConfigVO);
                        if(!StringUtils.isEmpty(errMsg)){
                            conversionSkuConfigVO.setErrorMsg(errMsg);
                            errVOList.add(conversionSkuConfigVO);
                        } else {
                            rightVOList.add(conversionSkuConfigVO);
                        }
                    }

                });
            });
            //批量插入
            if(!CollectionUtils.isEmpty(rightVOList)){
                conversionSkuConfigMapper.batchSaveConfig(rightVOList);
            }
            //异常数据导出
            if(!CollectionUtils.isEmpty(errVOList)){
                Workbook downWorkBook = errMsgDown(errVOList);
                Random random = new Random();
                int i = random.nextInt(1000);
                String fileType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
                String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtils.NUMBER_DATE_FORMAT));
                String fileName = "CONVERSION_SKU" + time + i + fileType;
                CommonFileUtils.generateExcelFile(Global.REPORT_DIR,fileName,downWorkBook);
                return AjaxResult.getOK(fileName);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult updateConfig(ConversionSkuConfig conversionSkuConfig) {
        conversionSkuConfig.setAdminId(baseService.getAdminId());
        conversionSkuConfigMapper.updateSaveConfig(conversionSkuConfig);
        return AjaxResult.getOK();
    }

    @Override
    public void templateDown(HttpServletResponse response) {

        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet("sheet1");
        Font titleFont = workbook.createFont();
        titleFont.setFontName("宋体");
        titleFont.setFontHeightInPoints((short) 12);
        titleFont.setBold(Boolean.TRUE);
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setFont(titleFont);

        sheet.setColumnWidth(0, ExcelUtils.getColumnWidth(18));
        sheet.setColumnWidth(1, ExcelUtils.getColumnWidth(18));
        sheet.setColumnWidth(2, ExcelUtils.getColumnWidth(18));
        sheet.setColumnWidth(3, ExcelUtils.getColumnWidth(18));
        sheet.setColumnWidth(4, ExcelUtils.getColumnWidth(32));

        Row title = sheet.createRow(0);
        String[] titleName = {"仓库名称", "转出sku", "转入sku","转入比例(sku为鲜果非必填)"};
        for (int i = 0; i < titleName.length; i++) {
            Cell cell = title.createCell(i);
            cell.setCellStyle(titleStyle);
            cell.setCellValue(titleName[i]);
        }
        Font cellFont = workbook.createFont();
        cellFont.setFontName("宋体");
        cellFont.setFontHeightInPoints((short) 11);
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(cellFont);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        try {
            ExcelUtils.outputExcel(workbook, "转换配置模版.xls", response);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return;
    }

    @Override
    public void dataSkuQuantity() {
        logger.info("开始更新sku转换销量信息");
        StringBuffer sql = new StringBuffer("select * from summerfarm_ds.app_sku_transfer_base_cnt_di where  ds = ");
        LocalDate plus = LocalDate.now().minusDays(1);
        String dateTime =  plus.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        sql.append(dateTime)
                .append(";");
        Set<ConversionSkuQuantity> skuQuantitySet = new HashSet<>();
        try {
            //获取数据
            Instance i = SQLTask.run(OdpsConfig.ODPS, sql.toString());
            i.waitForSuccess();
            List<Record> records = SQLTask.getResult(i);
            records.forEach(x -> createSkuQuantity(x,skuQuantitySet));
        } catch (Exception e) {
            logger.info("sku转换销量信息更新失败 e={}",e.getMessage());
            return;
        }
        ArrayList<ConversionSkuQuantity> quantityList = new ArrayList<>(skuQuantitySet);
        if (CollectionUtils.isEmpty(quantityList)) {
            logger.info("sku转换销量信息更新失败,获取数据为空");
            return;
        }
        conversionSkuQuantityMapper.saveBatchSkuQuantity(quantityList);

    }

    @Override
    public void syncSkuQuantity() {
        logger.info("开始更新sku转换销量信息");
        List<SkuSaleStatisticsPO> skuPOList = skuSaleStatisticsMapper
                .selectListByDateFlag(DateUtil.formatYmdWithOutSplitDate(LocalDate.now().minusDays(1)));
        if (CollectionUtils.isEmpty(skuPOList)) {
            logger.warn("未查询到转换sku配置销量数据 时间：{}", LocalDate.now().minusDays(1));
            return;
        }
        Set<ConversionSkuQuantity> skuQuantitySet = new HashSet<>();
        try {
            skuPOList.forEach(x -> assembleSkuQuantity(x,skuQuantitySet));
        } catch (Exception e) {
            logger.info("sku转换销量信息更新失败 e={}",e.getMessage());
            return;
        }
        ArrayList<ConversionSkuQuantity> quantityList = new ArrayList<>(skuQuantitySet);
        if (CollectionUtils.isEmpty(quantityList)) {
            logger.info("sku转换销量信息更新失败,获取数据为空");
            return;
        }
        conversionSkuQuantityMapper.saveBatchSkuQuantity(quantityList);
        logger.info("完成更新sku转换销量信息");

    }

    /**
     * 校验sku信息
     * @Author: ct
     * @param skuConfig 配置
     * @return
     **/
    private String checkoutConfig(ConversionSkuConfig skuConfig){
        StringJoiner errMsg = new StringJoiner(",");
        Integer warehouseNo = skuConfig.getWarehouseNo();
        String inSku = skuConfig.getInSku();
        String outSku = skuConfig.getOutSku();
        String rate = skuConfig.getRates();
        //库存仓,转入sku,转出sku,不能为空
        if(Objects.isNull(warehouseNo) || Objects.isNull(inSku) || Objects.isNull(outSku)){
            errMsg.add(IS_NOT_NULL);
        }
        InventoryVO inSkuInventoryVO = inventoryMapper.selectSkuType(inSku);
        InventoryVO outSkuInventoryVO = inventoryMapper.selectSkuType(outSku);
        //sku不存在
        if(Objects.isNull(inSkuInventoryVO) || Objects.isNull(outSkuInventoryVO)){
            errMsg.add(SKU_NOT_SAVE);
        } else {
            //转出、转入sku需为同一spu
            if(!Objects.equals(inSkuInventoryVO.getPdId(),outSkuInventoryVO.getPdId())){
                errMsg.add(SPU_NOT_LIKE);
            }
            // 非鲜果类目转换比例不能为空，且转换比例为1:N（N为正整数）
            if(!Objects.equals(inSkuInventoryVO.getCategoryType(), Category.FRUIT_TYPE)){
                if (StringUtils.isEmpty(rate)) {
                    errMsg.add(RATE_NOT_NULL);
                } else if(!rate.matches(NOT_FRUIT_RATES)) {
                    errMsg.add(NOT_FRUIT_RATES_ERROR);
                }
            } else if (!StringUtils.isEmpty(rate)) {
                // 鲜果类目转换比例可以为空，如果不为空时转换比例格式为M:N（M、N为正整数）
                if(!rate.matches(FRUIT_RATES)){
                    errMsg.add(FRUIT_RATES_ERROR);
                }
            }
        }
        //对比转入sku和转出sku是否一样
        if(Objects.equals(outSku,inSku)){
            errMsg.add(IS_LIKE_SKU);
        }
        if(!Objects.isNull(inSkuInventoryVO)) {
            skuConfig.setPdId(inSkuInventoryVO.getPdId());
        }
        //转入sku已存在
        ConversionSkuConfig queryConfig = new ConversionSkuConfig();
        queryConfig.setInSku(inSku);
        queryConfig.setWarehouseNo(warehouseNo);
        queryConfig.setStatus(CurrencyStatusEnum.EFFECTIVE.ordinal());
        List<ConversionSkuConfig> conversionSkuConfigs = conversionSkuConfigMapper.selectConfig(queryConfig);
        if(!CollectionUtils.isEmpty(conversionSkuConfigs)){
            errMsg.add(SKU_SAVE);
        }
        //不能配置转入转出相反比例
        queryConfig.setInSku(outSku);
        queryConfig.setOutSku(inSku);
        List<ConversionSkuConfig> configList = conversionSkuConfigMapper.selectConfig(queryConfig);
        if(!CollectionUtils.isEmpty(configList)){
            errMsg.add(OPPOSITE_SKU);
        }
        return errMsg.toString();
    }

    private ConversionSkuConfigVO tranfMsg(Map<String ,String> date){
        String warehouseName = StringUtils.trim(date.get("仓库名称"));
        String outSku = StringUtils.trim(date.get("转出sku"));
        String inSku = StringUtils.trim(date.get("转入sku"));
        String rate = StringUtils.trim(date.get("转入比例(sku为鲜果非必填)"));
        ConversionSkuConfigVO conversionSkuConfigVO = new ConversionSkuConfigVO();
        conversionSkuConfigVO.setWarehouseName(warehouseName);
        conversionSkuConfigVO.setRates(rate);
        conversionSkuConfigVO.setInSku(inSku);
        conversionSkuConfigVO.setOutSku(outSku);
        conversionSkuConfigVO.setStatus(CurrencyStatusEnum.EFFECTIVE.ordinal());
        return conversionSkuConfigVO;
    }

    /**
     *  导入异常数据导出
     * @Author: ct
     * @param errVOList 异常信息
     * @return
     **/
    private Workbook errMsgDown(List<ConversionSkuConfigVO> errVOList){

        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet("sheet1");
        int rowIndex = 1;

        Font titleFont = workbook.createFont();
        titleFont.setFontName("宋体");
        titleFont.setFontHeightInPoints((short) 12);
        titleFont.setBold(Boolean.TRUE);

        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setFont(titleFont);

        sheet.setColumnWidth(0, ExcelUtils.getColumnWidth(18));
        sheet.setColumnWidth(1, ExcelUtils.getColumnWidth(18));
        sheet.setColumnWidth(2, ExcelUtils.getColumnWidth(18));
        sheet.setColumnWidth(3, ExcelUtils.getColumnWidth(18));
        sheet.setColumnWidth(4, ExcelUtils.getColumnWidth(32));

        Row title = sheet.createRow(0);
        String[] titleName = {"仓库名称", "转出sku", "转入sku","转入比例(sku为鲜果非必填)","备注"};
        for (int i = 0; i < titleName.length; i++) {
            Cell cell = title.createCell(i);
            cell.setCellStyle(titleStyle);
            cell.setCellValue(titleName[i]);
        }
        Font cellFont = workbook.createFont();
        cellFont.setFontName("宋体");
        cellFont.setFontHeightInPoints((short) 11);
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(cellFont);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        if(!CollectionUtils.isEmpty(errVOList)){
            for (ConversionSkuConfigVO vo : errVOList) {
                Row row = sheet.createRow(rowIndex);
                row.createCell(0).setCellValue(vo.getWarehouseName());
                row.createCell(1).setCellValue(vo.getOutSku());
                row.createCell(2).setCellValue(vo.getInSku());
                row.createCell(3).setCellValue(vo.getRates());
                row.createCell(4).setCellValue(vo.getErrorMsg());
                rowIndex ++;
            }
        }
        return workbook;
    }

    /**
     * 转换对象
     * @param record
     * @param quantitySet
     */
    private  void createSkuQuantity(Record record , Set<ConversionSkuQuantity> quantitySet){
        Integer warehouseNo = parseInt(record.getString("rdc_k"));
        String inSku = parseString(record.getString("in_sku_k"));
        BigDecimal inSkuFifteen = parseBigDecimal(record.getString("in_sale_cnt_b15d"));
        BigDecimal inSkuSeven = parseBigDecimal(record.getString("in_sale_cnt_b7d"));
        BigDecimal inSkuSaleCnt = parseBigDecimal(record.getString("in_min_sale_cnt"));
        String  outSku = parseString(record.getString("out_sku_k"));
        BigDecimal outSkuFifteen = parseBigDecimal(record.getString("out_sale_cnt_b15d"));
        BigDecimal outSkuSeven = parseBigDecimal(record.getString("out_sale_cnt_b7d"));
        BigDecimal outSkuSaleCnt = parseBigDecimal(record.getString("out_min_sale_cnt"));
        ConversionSkuQuantity skuQuantity = new ConversionSkuQuantity(warehouseNo,inSku,inSkuFifteen,inSkuSeven,inSkuSaleCnt);
        LocalDate date = LocalDate.now().minusDays(NumberUtils.INTEGER_ONE);
        skuQuantity.setDate(date);
        ConversionSkuQuantity outSkuQuantity = new ConversionSkuQuantity(warehouseNo,outSku,outSkuFifteen,outSkuSeven,outSkuSaleCnt);
        outSkuQuantity.setDate(date);
        quantitySet.add(skuQuantity);
        quantitySet.add(outSkuQuantity);
    }

    private void assembleSkuQuantity(SkuSaleStatisticsPO record , Set<ConversionSkuQuantity> quantitySet) {
        Integer warehouseNo = record.getRdcK();
        String inSku = record.getInSkuK();
        BigDecimal inSkuFifteen = record.getInSaleCntB15d();
        BigDecimal inSkuSeven = record.getInSaleCntB7d();
        BigDecimal inSkuSaleCnt = record.getInMinSaleCnt();
        BigDecimal inMaxSaleSeven = record.getInMaxSaleSeven();

        String outSku = record.getOutSkuK();
        BigDecimal outSkuFifteen = record.getOutSaleCntB15d();
        BigDecimal outSkuSeven = record.getOutSaleCntB7d();
        BigDecimal outSkuSaleCnt = record.getOutMinSaleCnt();
        BigDecimal outMaxSaleSeven = record.getOutMaxSaleSeven();

        ConversionSkuQuantity skuQuantity = new ConversionSkuQuantity(warehouseNo,inSku,inSkuFifteen,inSkuSeven,inSkuSaleCnt, inMaxSaleSeven);
        LocalDate date = LocalDate.now().minusDays(NumberUtils.INTEGER_ONE);
        skuQuantity.setDate(date);
        ConversionSkuQuantity outSkuQuantity = new ConversionSkuQuantity(warehouseNo,outSku,outSkuFifteen,outSkuSeven,outSkuSaleCnt, outMaxSaleSeven);
        outSkuQuantity.setDate(date);
        quantitySet.add(skuQuantity);
        quantitySet.add(outSkuQuantity);
    }

    /**
     * String转换
     * @param obj
     * @return
     */
    private static String parseString(Object obj) {
        return Objects.isNull(obj) ? StringUtils.EMPTY : String.valueOf(obj);
    }

    /**
     * Integer转换
     * @param obj
     * @return
     */
    private static Integer parseInt(Object obj) {
        return Objects.isNull(obj) ? 0 : Integer.valueOf(String.valueOf(obj));
    }
    /**
     * BigDecimal转换
     * @param obj
     * @return
     */
    private static BigDecimal parseBigDecimal(Object obj) {
        return Objects.isNull(obj) ? BigDecimal.ZERO :BigDecimal.valueOf(Double.valueOf(String.valueOf(obj)));
    }

}
