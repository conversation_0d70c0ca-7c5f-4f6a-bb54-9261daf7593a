package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.mapper.manage.CategoryMapper;
import net.summerfarm.mapper.manage.ProductsMapper;
import net.summerfarm.mapper.manage.SalesDataMapper;
import net.summerfarm.model.domain.Category;
import net.summerfarm.model.domain.Products;
import net.summerfarm.model.domain.ProductsProperty;
import net.summerfarm.model.vo.CategoryVO;
import net.summerfarm.model.vo.SalesDataVo;
import net.summerfarm.service.CategoryService;
import net.summerfarm.service.ProductsPropertyService;
import net.summerfarm.task.AsyncTaskService;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Package: net.summerfarm.service.impl
 * @Description: 品类业务实现接口
 * @author: <EMAIL>
 * @Date: 2016/7/26
 */
@Service
public class CategoryServiceImpl implements CategoryService {

    @Resource
    private CategoryMapper categoryMapper;
    @Lazy
    @Resource
    private AsyncTaskService asyncTaskService;
    @Lazy
    @Resource
    private ProductsPropertyService productsPropertyService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private SalesDataMapper salesDataMapper;

    private static final String BACKGROUND_CATEGORY = "background_category";

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult save(CategoryVO record) {
        paramCheck(record);

        redisTemplate.delete(BACKGROUND_CATEGORY);

        categoryMapper.insertSelective(record);

        Optional.ofNullable(record.getCurrentPropertyList()).ifPresent(propertyList -> productsPropertyService.addKeyPropertyMapping(record.getId(), propertyList));

        redisTemplate.delete(BACKGROUND_CATEGORY);

        return AjaxResult.getOK(record);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult update(CategoryVO record) {
        if (record == null || record.getId() <= 0) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        paramCheck(record);

        redisTemplate.delete(BACKGROUND_CATEGORY);

        categoryMapper.updateByPrimaryKeySelective(record);

        Optional.ofNullable(record.getCurrentPropertyList()).ifPresent(propertyList -> productsPropertyService.addKeyPropertyMapping(record.getId(), propertyList));

        asyncTaskService.flushCategoryTree();
        redisTemplate.delete(BACKGROUND_CATEGORY);

        return AjaxResult.getOK();
    }


    @Override
    public AjaxResult select() {
        List<CategoryVO> categoryList = getCategoryTree();
        return AjaxResult.getOK(categoryList);
    }

    @Override
    public List<CategoryVO> getCategoryTree() {
        if (redisTemplate.hasKey(BACKGROUND_CATEGORY)) {
            String result = redisTemplate.opsForValue().get(BACKGROUND_CATEGORY);
            return JSONObject.parseArray(result, CategoryVO.class);
        }

        List<CategoryVO> result = new ArrayList<>();
        List<CategoryVO> treeNodes = categoryMapper.selectTreeNodes();
        Map<Integer, List<CategoryVO>> childListMap = new HashMap<>(32);
        treeNodes.stream()
                .filter(el -> {
                    if (el.getParentId() == null) {
                        result.add(el);
                        return false;
                    }
                    return true;
                })
                .forEach(el -> {
                    if (!childListMap.containsKey(el.getParentId())) {
                        childListMap.put(el.getParentId(), new ArrayList<>(8));
                    }
                    childListMap.get(el.getParentId()).add(el);
                });

        //一级类目
        result.stream()
                .forEach(el -> {
                    //二级目录
                    if (childListMap.containsKey(el.getId())) {
                        el.setCategoryList(childListMap.get(el.getId()));
                        if (!CollectionUtils.isEmpty(el.getCategoryList())) {
                            el.getCategoryList().forEach(child -> {
                                child.setParentId(el.getId());

                                //三级目录
                                if (childListMap.containsKey(child.getId())) {
                                    child.setCategoryList(childListMap.get(child.getId()));

                                    //关键属性
                                    if (!CollectionUtils.isEmpty(child.getCategoryList())) {
                                        child.getCategoryList().forEach(grand -> {
                                            grand.setParentId(child.getId());

                                            List<ProductsProperty> keyPropertyList = productsPropertyService.selectKeyPropertyByCategoryId(grand.getId());
                                            grand.setCurrentPropertyList(keyPropertyList);
                                        });
                                    }
                                }
                            });
                        }
                    }
                });

        redisTemplate.opsForValue().set(BACKGROUND_CATEGORY, JSONObject.toJSONString(result));

        return result;
    }

    /**
     * 查询三级所属类目名称
     *
     * @param id
     * @return List
     */
    @Override
    public Map<String, String> getThreeLevelCategory(Integer id) {
        Map<String, String> threeLevelCategory = categoryMapper.selectThreeLevelCategory(id);
        return threeLevelCategory;
    }

    private void paramCheck(CategoryVO record) {
        if (!CollectionUtils.isEmpty(record.getCurrentPropertyList()) && record.getCurrentPropertyList().size() > 10) {
            throw new DefaultServiceException("关键属性挂靠不可超过10个");
        }
        if (!StringUtils.isEmpty(record.getTaxRateValue()) && record.getTaxRateCode().length() > 19) {
            throw new DefaultServiceException("税率分类编码不可超过19位");
        }
        //类目名称校验
        boolean flag = categoryMapper.hasEqualsName(null, null, record.getCategory());
        if (flag) {
            throw new DefaultServiceException("类目名称已存在");
        }

    }

    @Override
    public void flushCache() {
        redisTemplate.delete(BACKGROUND_CATEGORY);
        getCategoryTree();
    }


    @Override
    public AjaxResult detail(Integer id,Integer level){
        CategoryVO categoryVO = categoryMapper.selectDetail(id);
        if (categoryVO == null){
            return AjaxResult.getOK();
        }
        if (Objects.equals(level,3)){
            List<ProductsProperty> keyPropertyList = productsPropertyService.selectKeyPropertyByCategoryId(id);
            categoryVO.setCurrentPropertyList(keyPropertyList);
        }
        return AjaxResult.getOK(categoryVO);
    }


    @Override
    public AjaxResult selectSkuCategory(int pageIndex, int pageSize, String name) {
        PageHelper.startPage(pageIndex, pageSize);
        List<SalesDataVo> salesDataVos = salesDataMapper.selectSkuCategory(name);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(salesDataVos));
    }

    @Override
    public AjaxResult delete(Integer id){
        Set<Integer> categoryIds = new LinkedHashSet<>();
        // 查询可以删除类目集合
        this.selectDeleteCategoryIds(id, categoryIds);
        boolean deleteFlag = this.judgeCategoryConnectProduct(categoryIds);
        if (deleteFlag){
            categoryMapper.delete(categoryIds);
            redisTemplate.delete(BACKGROUND_CATEGORY);
            return AjaxResult.getOK();
        }
        throw new DefaultServiceException("该类目或其子类目下已有sku挂靠,无法删除!");
    }

    /**
     * 递归查询没有关联spu的分类
     * @param id 分类id
     * @param categoryIds 没有关联sku分分类id集合
     * @return 没有关联sku分分类id集合
     */
    private Set<Integer> selectDeleteCategoryIds(Integer id,Set<Integer> categoryIds){
        // 查询当前分类的子集分类
        List<Category> categories = categoryMapper.selectByParentId(id);
        if (!CollectionUtils.isEmpty(categories)){
            Set<Integer> categoryIdList = categories.stream().map(Category::getId).collect(Collectors.toSet());
            categoryIds.addAll(categoryIdList);
            for (Category category : categories) {
                selectDeleteCategoryIds(category.getId(),categoryIds);
            }
        }
        categoryIds.add(id);
        return categoryIds;
    }

    /**
     * 判断类目是否关联了spu
     * @param categoryIds 没有关联spu的类目id集合
     * @return 没有关联spu的类目id集合
     */
    private boolean judgeCategoryConnectProduct(Set<Integer> categoryIds) {
        List<Products> products = productsMapper.selectByCategoryIdList(categoryIds);
        if (CollectionUtils.isEmpty(products)){
            return true;
        }
        return false;
    }

    @Override
    public CategoryVO selectDetailById(Integer id) {
        CategoryVO categoryVO = categoryMapper.selectDetail(id);
        return categoryVO;
    }

    @Override
    public List<Category> listAllThirdCategory() {
        List<Category> list = Lists.newArrayList();
        List<CategoryVO> categoryTree = getCategoryTree();
        for (CategoryVO categoryVO : categoryTree) {
            List<CategoryVO> secondCategory = categoryVO.getCategoryList();
            if (CollectionUtil.isEmpty(secondCategory)) {
                continue;
            }
            for (CategoryVO second : secondCategory) {
                List<CategoryVO> thirdCategory = second.getCategoryList();
                if (CollectionUtil.isEmpty(thirdCategory)) {
                    continue;
                }
                for (CategoryVO third : thirdCategory) {
                    Category category = new Category();
                    category.setCategory(third.getCategory());
                    category.setId(third.getId());
                    list.add(category);
                }
            }
        }
        return list;
    }

    @Override
    public List<Category> selectTypeByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }

        return categoryMapper.selectTypeByIds(ids);
    }

    @Override
    public Map<String, String> getSublevelCategoryIds(List<Integer> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return null;
        }

        Map<String, String> categoryMap = new HashMap<>();
        for (Integer categoryId : categoryIds) {
            //查询下一级
            List<Category> childrens = categoryMapper.selectChildrenId(categoryId);
            if (CollectionUtils.isEmpty(childrens)) {
                Category category = categoryMapper.selectByPrimaryKey(categoryId);
                if (Objects.nonNull(category)) {
                    categoryMap.put(String.valueOf(categoryId), category.getCategory());
                }
                continue;
            }

            //继续查询下一级
            List<Integer> childrenIds = childrens.stream().map(Category::getId).collect(Collectors.toList());
            List<Category> selectChildrenIdByList = categoryMapper.selectChildrenIdByList(childrenIds);
            if (CollectionUtils.isEmpty(selectChildrenIdByList)) {
                Map<String, String> childrenIdMap = childrens.stream().collect(Collectors.toMap(e -> String.valueOf(e.getId()), Category::getCategory, (p1, p2) -> p2));
                categoryMap.putAll(childrenIdMap);
            } else {

                //最后一级
                Map<String, String> childrenIdMap = selectChildrenIdByList.stream().collect(Collectors.toMap(e -> String.valueOf(e.getId()), Category::getCategory, (p1, p2) -> p2));
                categoryMap.putAll(childrenIdMap);
            }
        }
        return categoryMap;
    }
}
