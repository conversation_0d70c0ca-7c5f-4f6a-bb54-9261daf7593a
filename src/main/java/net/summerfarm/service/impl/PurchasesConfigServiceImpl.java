package net.summerfarm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.PreCutOffOrderUtil;
import net.summerfarm.common.util.ReflectUtils;
import net.summerfarm.common.util.RequestHolder;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.AreaTypeEnum;
import net.summerfarm.enums.SMSType;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.BiStockUpConfigVO;
import net.summerfarm.model.vo.BiStockUpVO;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.PurchasesConfigVO;
import net.summerfarm.service.FenceService;
import net.summerfarm.service.MsgAdminService;
import net.summerfarm.service.PurchaseCalculateService;
import net.summerfarm.service.PurchasesConfigService;
import net.summerfarm.task.AsyncTaskService;
import net.summerfarm.warehouse.enums.WarehouseStorageCenterEnum;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsConfig;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.model.vo.WarehouseStorageCenterVO;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
@Service
public class PurchasesConfigServiceImpl extends BaseService implements PurchasesConfigService {
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private PurchasesConfigMapper purchasesConfigMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private AdminMapper adminMapper;
    @Lazy
    @Resource
    private MsgAdminService msgAdminService;
    @Resource
    private BiStockUpMapper biStockUpMapper;
    @Resource
    private BiStockUpConfigMapper biStockUpConfigMapper;
    @Resource
    private PurchaseCalculateService purchaseCalculateService;
    @Lazy
    @Resource
    private AsyncTaskService asyncTaskService;
    @Resource
    private TimingRuleMapper timingRuleMapper;
    @Resource
    private BiPurchasesConfigMapper biPurchasesConfigMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private PrepayInventoryMapper prepayInventoryMapper;
    @Resource
    private PreCutOffOrderUtil preCutOffOrderUtil;
    @Resource
    private WarehouseStorageService storageService;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    private WarehouseLogisticsService logisticsService;
    @Resource
    private FenceService fenceService;

    public static final String UPDATE_PURCHASE ="updatePurchase";

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult save(PurchasesConfig purchasesConfig) {
        List<Integer> areaNos = validateDataPermissions(purchasesConfig.getType(), purchasesConfig.getAreaNo(), purchasesConfig.getAreaManageId());
        PurchasesConfig record = purchasesConfigMapper.selectOne(purchasesConfig);
        if (record != null){
            return AjaxResult.getErrorWithMsg("记录已存在,请勿重复添加");
        }
        boolean check = true;
        for (Integer areaNo: areaNos){
            AreaStore selectKey = new AreaStore();
            selectKey.setSku(purchasesConfig.getSku());
            selectKey.setAreaNo(areaNo);
            AreaStore areaStore = areaStoreMapper.selectOne(selectKey);
            if (areaStore != null){
                check = false;
                break;
            }
        }
        if (check){
            throw new DefaultServiceException("当前仓不存在此sku");
        }

        PurchasesConfig data = purchaseCalculateService.calcSafeLevelSingle(purchasesConfig);
        purchasesConfig.setSafeLevel(data.getSafeLevel());
        purchasesConfig.setCalcQuantity(data.getCalcQuantity());

        //判断状态
        purchasesConfig.setStatus(0);

        purchasesConfig.setAddtime(LocalDateTime.now());
        purchasesConfig.setUpdateTime(LocalDateTime.now());
        purchasesConfigMapper.insert(purchasesConfig);
        if(purchasesConfig.getAreaManageId() != null){
            throw new DefaultServiceException("分布仓暂不支持配置");
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult delete(Integer id) {
        PurchasesConfig record = purchasesConfigMapper.selectByPrimaryKey(id);
        if (record == null){
            return AjaxResult.getErrorWithMsg("记录不存在");
        }

        validateDataPermissions(record.getType(),record.getAreaNo(),record.getAreaManageId());
        Area area = new Area();
        Integer areaNo = record.getAreaNo();
        //todo
        List<Integer> integers = timingRuleMapper.selectBySku(record.getSku(), areaNo, new Date());
        WarehouseInventoryMapping inventoryMapping = warehouseInventoryService.selectByUniqueIndex(areaNo, record.getSku());
        List<WarehouseLogisticsConfig> logisticsConfigs = logisticsService.selectAllocationConfig(inventoryMapping.getWarehouseNo());

        if(!CollectionUtils.isEmpty(logisticsConfigs) && !CollectionUtils.isEmpty(integers)){
            return AjaxResult.getErrorWithMsg("该sku仍有省心送，该仓为不调拨，不可删除采购信息");
        }

        //触发调拨冻结库存 释放所有采购冻结库存 ,明天配送的不解冻
        compareTransfer(areaNo,record.getSku(),Long.valueOf(String.valueOf(record.getLeadTime())));

        int rs = purchasesConfigMapper.delete(id);
        if (rs == 1){
            return AjaxResult.getOK();
        }

        return AjaxResult.getErrorWithMsg("删除失败");
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult update(PurchasesConfig purchasesConfig) {
        PurchasesConfig data = purchaseCalculateService.calcSafeLevelSingle(purchasesConfig);
        purchasesConfig.setSafeLevel(data.getSafeLevel());
        purchasesConfig.setCalcQuantity(data.getCalcQuantity());
        //判断预警
        Integer status = checkStatus(purchasesConfig);
        purchasesConfig.setStatus(status);

        PurchasesConfig record = purchasesConfigMapper.selectByPrimaryKey(purchasesConfig.getId());
        if (status == 1 && !Objects.equals(record.getStatus(),status)){
            handleMsg(purchasesConfig);
        }
        purchasesConfig.setUpdateTime(LocalDateTime.now());
        purchasesConfigMapper.update(purchasesConfig);

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectList(int pageIndex, int pageSize, PurchasesConfigVO purchasesConfigVO) {
        PageHelper.startPage(pageIndex,pageSize);
        List<PurchasesConfigVO> purchasesConfigVOS = purchasesConfigMapper.selectVO(purchasesConfigVO);
        for (PurchasesConfigVO configVO : purchasesConfigVOS) {
            String name = configVO.getNameRemakes() != null ? configVO.getPdName()+"-"+configVO.getNameRemakes() : configVO.getPdName();
            configVO.setPdName(name);
        }
        return AjaxResult.getOK(new PageInfo<>(purchasesConfigVOS));
    }

    @Override
    public AjaxResult selectDataList(int pageIndex,int pageSize,PurchasesConfigVO purchasesConfigVO){
        List<Integer> areaNos = new ArrayList<>();
        areaNos.add(purchasesConfigVO.getAreaNo());
        purchasesConfigVO.setAreaNos(areaNos);
        Integer huaXiAreaNo = 24;
        PageHelper.startPage(pageIndex,pageSize);
        List<PurchasesConfigVO> purchasesConfigVOS = purchasesConfigMapper.selectVOList(purchasesConfigVO);
        if (!CollectionUtils.isEmpty(purchasesConfigVOS)){
            //查询所有本部仓（排除测试仓）和华西总仓 24
            List<WarehouseStorageCenterVO> centerList = storageService.selectAllForSummerFarm(WarehouseStorageCenterEnum.Type.INTERNAL_AREA.ordinal(), WarehouseStorageCenterEnum.Status.VALID.ordinal());
            List<Integer> ownAreaNos = centerList.stream()
                    .map(WarehouseStorageCenter::getWarehouseNo)
                    .filter(warehouseNo -> !Objects.equals(12, warehouseNo))
                    .filter(warehouseNo -> !Objects.equals(huaXiAreaNo, warehouseNo)).collect(Collectors.toList());

            Integer areaNo = purchasesConfigVO.getAreaNo();
            Integer type = purchasesConfigVO.getType();

            /**
             * 若是为华西总仓，则设置ownAreaNos只为华西总仓的
             */
            if(Objects.equals(huaXiAreaNo,areaNo)){
                ownAreaNos.clear();
                ownAreaNos.add(huaXiAreaNo);
            }
            for (PurchasesConfigVO vo : purchasesConfigVOS) {
                //本部仓库存
                if (Objects.equals(purchasesConfigVO.getType(), 0) && ! Objects.deepEquals(purchasesConfigVO.getAreaNo(),huaXiAreaNo)) {
                    areaNo = null;
                }else if(Objects.equals(purchasesConfigVO.getType(), 0) && Objects.deepEquals(purchasesConfigVO.getAreaNo(),huaXiAreaNo)){
                    areaNo = purchasesConfigVO.getAreaNo();
                } else {
                    //非本部仓使用其他仓库存，显示其他仓的库存数据
                    areaNo = purchasesConfigVO.getAreaNo();
                }

                AreaStore ownStore = areaStoreMapper.sumOwnQuantity(vo.getSku(), areaNo, type);

                // 计算为总仓的时候则需要排除华西总仓
                if(Objects.isNull(areaNo)){
                    AreaStore otherOwnStore = areaStoreMapper.sumOwnQuantity(vo.getSku(), huaXiAreaNo, 0);
                    if(Objects.nonNull(otherOwnStore)){
                        Integer quantity = ownStore.getQuantity();
                        Integer lockQuantity = ownStore.getLockQuantity();
                        Integer roadQuantity = ownStore.getRoadQuantity();
                        Integer safeQuantity = ownStore.getSafeQuantity();

                        ownStore.setQuantity(quantity-otherOwnStore.getQuantity());
                        ownStore.setLockQuantity(lockQuantity - otherOwnStore.getLockQuantity());
                        ownStore.setRoadQuantity(roadQuantity - otherOwnStore.getRoadQuantity());
                        ownStore.setSafeQuantity(safeQuantity - otherOwnStore.getSafeQuantity());
                    }
                }


                vo.setQuantity(ownStore.getQuantity());
                vo.setLockQuantity(ownStore.getLockQuantity());
                vo.setRoadQuantity(ownStore.getRoadQuantity());
                vo.setSafeQuantity(ownStore.getSafeQuantity());

                //销量数据
                BiPurchasesConfig biPurchasesConfig = biPurchasesConfigMapper.queryQuantity(purchasesConfigVO.getAreaNo(), vo.getSku());
                vo.setThirtyAvgQuantity(biPurchasesConfig.getThirtyAvgQuantity());
                vo.setSevenQuantity(biPurchasesConfig.getSevenQuantity());
                vo.setSevenAvgQuantity(biPurchasesConfig.getSevenAvgQuantity());
                vo.setLastSevenQuantity(biPurchasesConfig.getLastSevenQuantity());

                //未冻结省心送数量
                Integer unLockQuantity = ordersMapper.unLockQuantity(vo.getSku(), ownAreaNos);
                //已冻结未配送省心送数量
                Integer unDelivery = deliveryPlanMapper.lockUnDeliveryQuantity(vo.getSku(), LocalDate.now(), ownAreaNos);
                vo.setUnLockQuantity(Optional.ofNullable(unLockQuantity).orElse(0) + unDelivery);

                //待消耗预付数量
                Integer unUserPrepayQuantity = prepayInventoryMapper.unUseSkuQuantity(vo.getSku());
                vo.setUnUsePrepayQuantity(unUserPrepayQuantity);
            }
        }
        return AjaxResult.getOK(new PageInfo<>(purchasesConfigVOS));
    }

    @Override
    public void download(PurchasesConfigVO purchasesConfigVO) {
        List<Integer> areaNos = new ArrayList<>();
        areaNos.add(purchasesConfigVO.getAreaNo());
        purchasesConfigVO.setAreaNos(areaNos);
        Integer huaXiAreaNo = 24;
        List<PurchasesConfigVO> purchasesConfigVOS = purchasesConfigMapper.selectVOList(purchasesConfigVO);
        if (!CollectionUtils.isEmpty(purchasesConfigVOS)){
            //查询所有本部仓（排除测试仓）和华西总仓
            List<WarehouseStorageCenterVO> centerList = storageService.selectAllForSummerFarm(WarehouseStorageCenterEnum.Type.INTERNAL_AREA.ordinal(), WarehouseStorageCenterEnum.Status.VALID.ordinal());
            List<Integer> ownAreaNos = centerList.stream()
                    .map(WarehouseStorageCenter::getWarehouseNo)
                    .filter(warehouseNo -> !Objects.equals(12, warehouseNo))
                    .filter(warehouseNo -> !Objects.equals(huaXiAreaNo, warehouseNo)).collect(Collectors.toList());
            Integer areaNo = purchasesConfigVO.getAreaNo();
            Integer type = purchasesConfigVO.getType();

            /**
             * 若是为华西总仓，则设置ownAreaNos只为华西总仓的
             */
            if(Objects.equals(huaXiAreaNo,areaNo)){
                ownAreaNos.clear();
                ownAreaNos.add(huaXiAreaNo);
            }

            Area area = areaMapper.selectByAreaNo(purchasesConfigVO.getAreaNo());

            Workbook workbook = new HSSFWorkbook();
            Sheet sheet = workbook.createSheet(area.getAreaName() + "采购数量预测");

            Row title = sheet.createRow(0);
            title.createCell(0).setCellValue("仓库");
            title.createCell(1).setCellValue("sku");
            title.createCell(2).setCellValue("商品名称");
            title.createCell(3).setCellValue("规格");
            title.createCell(4).setCellValue("状态");
            title.createCell(5).setCellValue(type == 0 ? "本部仓" : "" + "可用库存");
            title.createCell(6).setCellValue(type == 0 ? "本部仓" : "" + "在途库存");
            title.createCell(7).setCellValue("安全水位");
            title.createCell(8).setCellValue("采购数量参考");
            title.createCell(9).setCellValue("近30天平均销量");
            title.createCell(10).setCellValue("近7天平均销量");
            title.createCell(11).setCellValue("未配送省心送数量");
            title.createCell(12).setCellValue("待消耗预付数量");

            int rowIndex = 1;
            for (PurchasesConfigVO vo : purchasesConfigVOS) {
                Row row = sheet.createRow(rowIndex);
                row.createCell(0).setCellValue(area.getAreaName());
                row.createCell(1).setCellValue(vo.getSku());
                row.createCell(2).setCellValue(vo.getPdName());
                row.createCell(3).setCellValue(vo.getWeight());
                row.createCell(4).setCellValue(vo.getStatus() == 0 ? "正常" : "采购预警");

                //本部仓库存
                if (Objects.equals(purchasesConfigVO.getType(), 0) && ! Objects.deepEquals(purchasesConfigVO.getAreaNo(),huaXiAreaNo)) {
                    areaNo = null;
                }else if(Objects.equals(purchasesConfigVO.getType(), 0) && Objects.deepEquals(purchasesConfigVO.getAreaNo(),huaXiAreaNo)){
                    areaNo = purchasesConfigVO.getAreaNo();
                } else {
                    //非本部仓使用其他仓库存，显示其他仓的库存数据
                    areaNo = purchasesConfigVO.getAreaNo();
                }

                AreaStore ownStore = areaStoreMapper.sumOwnQuantity(vo.getSku(), areaNo, type);

                // 计算为总仓的时候则需要排除华西总仓
                if(Objects.isNull(areaNo)){
                    AreaStore otherOwnStore = areaStoreMapper.sumOwnQuantity(vo.getSku(), huaXiAreaNo, 0);
                    if(Objects.nonNull(otherOwnStore)){
                        Integer quantity = ownStore.getQuantity();
                        Integer lockQuantity = ownStore.getLockQuantity();
                        Integer roadQuantity = ownStore.getRoadQuantity();
                        Integer safeQuantity = ownStore.getSafeQuantity();

                        ownStore.setQuantity(quantity-otherOwnStore.getQuantity());
                        ownStore.setLockQuantity(lockQuantity - otherOwnStore.getLockQuantity());
                        ownStore.setRoadQuantity(roadQuantity - otherOwnStore.getRoadQuantity());
                        ownStore.setSafeQuantity(safeQuantity - otherOwnStore.getSafeQuantity());
                    }
                }

                int usableQuantity = ownStore.getQuantity() - ownStore.getLockQuantity() - ownStore.getSafeQuantity();
                row.createCell(5).setCellValue(usableQuantity);
                row.createCell(6).setCellValue(ownStore.getRoadQuantity());
                row.createCell(7).setCellValue(vo.getSafeLevel());
                row.createCell(8).setCellValue(vo.getSafeLevel() - usableQuantity - ownStore.getRoadQuantity());

                //销量数据
                BiPurchasesConfig biPurchasesConfig = biPurchasesConfigMapper.queryQuantity(purchasesConfigVO.getAreaNo(), vo.getSku());
                row.createCell(9).setCellValue(biPurchasesConfig.getThirtyAvgQuantity().toString());
                row.createCell(10).setCellValue(biPurchasesConfig.getSevenAvgQuantity().toString());

                //未冻结省心送数量
                Integer unLockQuantity = ordersMapper.unLockQuantity(vo.getSku(), ownAreaNos);
                //已冻结未配送省心送数量
                Integer unDelivery = deliveryPlanMapper.lockUnDeliveryQuantity(vo.getSku(), LocalDate.now(), ownAreaNos);
                row.createCell(11).setCellValue(Optional.ofNullable(unLockQuantity).orElse(0) + unDelivery);

                //待消耗预付数量
                Integer unUserPrepayQuantity = prepayInventoryMapper.unUseSkuQuantity(vo.getSku());
                row.createCell(12).setCellValue(Optional.ofNullable(unUserPrepayQuantity).orElse(0));

                rowIndex ++;
            }

            try {
                ExcelUtils.outputExcel(workbook, area.getAreaName() + "采购数量预测.xls", RequestHolder.getResponse());
            } catch (IOException e) {
                logger.error(Global.collectExceptionStackMsg(e));
                throw new DefaultServiceException("导出失败!");
            }
        }
    }

    @Override
    public AjaxResult stockUpList(int pageIndex, int pageSize, BiStockUpVO biStockUpVO) {
        biStockUpVO.setAddDate(biStockUpVO.getAddDate() == null ? LocalDate.now() : biStockUpVO.getAddDate());
        PageHelper.startPage(pageIndex,pageSize);
        List<BiStockUpVO> biStockUpVOS = biStockUpMapper.select(biStockUpVO);
        return AjaxResult.getOK(new PageInfo<>(biStockUpVOS));
    }

    @Override
    public AjaxResult stockUpConfigList(int pageIndex, int pageSize, BiStockUpConfigVO biStockUpConfigVO) {
        PageHelper.startPage(pageIndex, pageSize);
        List<BiStockUpConfigVO> select = biStockUpConfigMapper.select(biStockUpConfigVO);
        return AjaxResult.getOK(new PageInfo<>(select));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult stockUpConfigSave(BiStockUpConfig stockUpConfig) {
        BiStockUpConfig biStockUpConfig = biStockUpConfigMapper.selectOne(stockUpConfig);
        if (biStockUpConfig != null) {
            return AjaxResult.getErrorWithMsg("该配置已存在,请勿重复添加");
        }
        stockUpConfig.setAddTime(LocalDateTime.now());
        biStockUpConfigMapper.insert(stockUpConfig);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult stockUpConfigDelete(Integer id) {
        biStockUpConfigMapper.delete(id);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult stockUpDownload(BiStockUpVO biStockUpVO) {
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        int rowIndex = 0;

        Row firstRow = sheet.createRow(rowIndex++);
        firstRow.createCell(0).setCellValue("调度仓:");
        firstRow.createCell(1).setCellValue(Global.warehouseMap.get(biStockUpVO.getAreaNo()));
        firstRow.createCell(3).setCellValue("预测日期:");
        firstRow.createCell(4).setCellValue(biStockUpVO.getAddDate() == null ? LocalDate.now().toString() : biStockUpVO.getAddDate().toString());

        String[] titles = {"sku编码", "商品名称", "规格", "7天平均销量", "备货量", "当前仓库库存"};
        Row titleRow = sheet.createRow(rowIndex++);
        for (int i = 0; i < titles.length; i++) {
            titleRow.createCell(i).setCellValue(titles[i]);
        }

        biStockUpVO.setAddDate(biStockUpVO.getAddDate() == null ? LocalDate.now() : biStockUpVO.getAddDate());
        List<BiStockUpVO> biStockUpVOS = biStockUpMapper.select(biStockUpVO);
        if (!CollectionUtils.isEmpty(biStockUpVOS)) {
            String[] fields = {"sku", "pdName", "weight", "sevenAvgQuantity", "stockUpQuantity", "storeQuantity"};
            Row row;
            for (BiStockUpVO vo: biStockUpVOS){
                row = sheet.createRow(rowIndex++);
                for (int i = 0; i < fields.length; i++) {
                    row.createCell(i).setCellValue(ReflectUtils.invokeGetMethod(BiStockUpVO.class, vo, fields[i]) + "");
                }
            }
        }

        try {
            ExcelUtils.outputExcel(workbook, "备货量数据.xls", RequestHolder.getResponse());
        } catch (IOException e) {
            logger.error(e.getMessage());
            throw new DefaultServiceException("数据导出异常");
        }
        return null;
    }

    public void handleMsg(PurchasesConfig purchasesConfig){
        String[] ccAdminIds = purchasesConfig.getCcAdminId().split(Global.SEPARATING_SYMBOL);
        InventoryVO inventoryVO = inventoryMapper.selectSkuType(purchasesConfig.getSku());
        for (String cc: ccAdminIds){
            String mainInfo = inventoryVO.getSku()+inventoryVO.getPdName()+"-" + inventoryVO.getWeight();
            Admin admin = adminMapper.selectByPrimaryKey(Integer.valueOf(cc));
            String keyword = admin.getAdminId()+Global.SEPARATING_SYMBOL+purchasesConfig.getType()+Global.SEPARATING_SYMBOL+purchasesConfig.getAreaNo()+
                    Global.SEPARATING_SYMBOL+purchasesConfig.getAreaManageId();
            msgAdminService.handleMsg(2,admin,mainInfo,keyword,null,null, SMSType.NOTIFY);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void msgArrival(Integer storeNo,String sku){
        WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, sku);
        if(mapping == null){
            return;
        }
        WarehouseStorageCenter storageCenter = storageService.selectByWarehouseNo(mapping.getWarehouseNo());

        PurchasesConfig select = new PurchasesConfig();
        select.setType(storageCenter.getType());
        select.setAreaManageId(storageCenter.getAreaManageId());
        select.setSku(sku);
        select.setAreaNo(storeNo);
        PurchasesConfig purchasesConfig = purchasesConfigMapper.selectOne(select);
        if (purchasesConfig == null){ //未配置预警信息
            return;
        }

        List<Integer> areaNos = fenceService.selectAreaNoByStoreNo(storeNo);
        AreaStore areaStoreSelect = new AreaStore();
        Integer quantity = 0;
        Integer lockQuantity = 0;
        Integer roadQuantity = 0;
        Integer safeQuantity = 0;

        for (Integer areaNo: areaNos){
            areaStoreSelect.setAreaNo(areaNo);
            areaStoreSelect.setSku(purchasesConfig.getSku());
            AreaStore areaStore = areaStoreMapper.selectOne(areaStoreSelect);
            if (areaStore != null){
                quantity += areaStore.getQuantity();
                lockQuantity += areaStore.getLockQuantity();
                roadQuantity += areaStore.getRoadQuantity();
                safeQuantity += areaStore.getSafeQuantity();
            }
        }

        if ((quantity-lockQuantity-safeQuantity+roadQuantity) < purchasesConfig.getSafeLevel()){
            if (purchasesConfig.getStatus() == 0){//未处于预警状态
                PurchasesConfig update = new PurchasesConfig();
                update.setId(purchasesConfig.getId());
                update.setStatus(1);
                update.setUpdateTime(LocalDateTime.now());
                purchasesConfigMapper.update(update);

//                handleMsg(purchasesConfig);
            }
        }else {
            PurchasesConfig update = new PurchasesConfig();
            update.setId(purchasesConfig.getId());
            update.setStatus(0);
            update.setUpdateTime(LocalDateTime.now());
            purchasesConfigMapper.update(update);
        }
    }

    /**
     * 判断预警状态
     */
    @Override
    public Integer checkStatus(PurchasesConfig purchasesConfig) {
        WarehouseStorageCenter query = new WarehouseStorageCenter();
        query.setType(purchasesConfig.getType());
        query.setWarehouseNo(purchasesConfig.getAreaNo());
        query.setAreaManageId(purchasesConfig.getAreaManageId());
        List<WarehouseStorageCenter> centerList = storageService.selectAllForSummerfarm(query);

        AreaStore selectKey = new AreaStore();
        Integer quantity = 0;
        Integer lockQuantity = 0;
        Integer roadQuantity = 0;
        Integer safeQuantity = 0;

        for (WarehouseStorageCenter center: centerList){
            selectKey.setAreaNo(center.getWarehouseNo());
            selectKey.setSku(purchasesConfig.getSku());
            AreaStore areaStore = areaStoreMapper.selectOne(selectKey);
            if (areaStore != null){
                quantity += areaStore.getQuantity();
                lockQuantity += areaStore.getLockQuantity();
                roadQuantity += areaStore.getRoadQuantity();
                safeQuantity += areaStore.getSafeQuantity();
            }
        }

        //可用库存+在途库存 >= 安全水位
        if ((quantity-lockQuantity-safeQuantity+roadQuantity) >= Optional.of(purchasesConfig.getSafeLevel()).orElse(0)){
            return 0;
        }
        return 1;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void configUpdate(List<PurchasesConfig> purchasesConfigs){
        if (!CollectionUtils.isEmpty(purchasesConfigs)){
            for (PurchasesConfig purchasesConfig: purchasesConfigs){
                update(purchasesConfig);
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void autoUpdate(){
        logger.info("自动更新采购信息开始"+ LocalDateTime.now());
        List<PurchasesConfig> purchasesConfigs = purchasesConfigMapper.select(null);
        if (!CollectionUtils.isEmpty(purchasesConfigs)){
            for (PurchasesConfig purchasesConfig: purchasesConfigs){
                purchasesConfigUpdate(purchasesConfig);
            }
        }
        logger.info("自动更新采购信息结束"+ LocalDateTime.now());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void warehouseTypeChange(WarehouseStorageCenter storageCenter, WarehouseStorageCenter oldStorageCenter) {
        List<Integer> deleteList = new ArrayList<>();
        List<PurchasesConfig> updateList = new ArrayList<>();
        List<PurchasesConfig> insertList = new ArrayList<>();
        //切换前是外部仓
        if (AreaTypeEnum.EXTERNAL_AREA.getType().equals(oldStorageCenter.getType())) {
            PurchasesConfig select = new PurchasesConfig();
            select.setType(AreaTypeEnum.EXTERNAL_AREA.getType());
            select.setAreaNo(oldStorageCenter.getWarehouseNo());
            List<PurchasesConfig> purchasesConfigRecords = purchasesConfigMapper.select(select);

            select.setType(storageCenter.getType());
            select.setAreaManageId(storageCenter.getAreaManageId());
            select.setAreaNo(null);
            List<PurchasesConfig> purchasesConfigs = purchasesConfigMapper.select(select);
            if (!CollectionUtils.isEmpty(purchasesConfigRecords)) {
                for (PurchasesConfig purchasesConfig : purchasesConfigRecords) {
                    deleteList.add(purchasesConfig.getId());

                    Map<String, PurchasesConfig> collect = new HashMap<>();
                    if (!CollectionUtils.isEmpty(purchasesConfigRecords)) {
                        collect = purchasesConfigs.stream().collect(Collectors.toMap(PurchasesConfig::getSku, PurchasesConfig -> PurchasesConfig));
                    }

                    if (!collect.containsKey(purchasesConfig.getSku())) {
                        PurchasesConfig insert = new PurchasesConfig();
                        insert.setType(storageCenter.getType());
                        insert.setAreaManageId(storageCenter.getAreaManageId());
                        insert.setSku(purchasesConfig.getSku());
                        insert.setLeadTime(purchasesConfig.getLeadTime());
                        insert.setSupplierId(purchasesConfig.getSupplierId());
                        insert.setStockRate(purchasesConfig.getStockRate());
                        insert.setCcAdminId(purchasesConfig.getCcAdminId());
                        insertList.add(purchasesConfigCreate(insert));
                    }
                }
                updateList.addAll(purchasesConfigs);
            }
        } //切换后是外部仓
        else if (AreaTypeEnum.EXTERNAL_AREA.getType().equals(storageCenter.getType())) {
            List<PurchasesConfig> purchasesConfigRecords = purchasesConfigMapper.selectBySomeKey(storageCenter.getType(), storageCenter.getAreaManageId(), storageCenter.getWarehouseNo());
            if (!CollectionUtils.isEmpty(purchasesConfigRecords)) {
                List<Integer> areaNos = new ArrayList<>();

                WarehouseStorageCenter query = new WarehouseStorageCenter();
                query.setType(storageCenter.getType());
                query.setAreaManageId(storageCenter.getAreaManageId());
                List<WarehouseStorageCenter> centerList = storageService.selectAllForSummerfarm(query);
                if (!CollectionUtils.isEmpty(centerList)) {
                    areaNos = centerList.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList());
                }

                for (PurchasesConfig purchasesConfig : purchasesConfigRecords) {
                    //只有当前仓有就删除，否则更新
                    if (!CollectionUtils.isEmpty(areaNos)) {
                        List<AreaStore> areaStores = areaStoreMapper.selectBySomeKey(purchasesConfig.getSku(), areaNos);
                        if (CollectionUtils.isEmpty(areaStores)) {
                            deleteList.add(purchasesConfig.getId());
                        } else {
                            updateList.add(purchasesConfig);
                        }
                    } else {
                        deleteList.add(purchasesConfig.getId());
                    }


                    PurchasesConfig insert = new PurchasesConfig();
                    insert.setType(storageCenter.getType());
                    insert.setAreaManageId(storageCenter.getAreaManageId());
                    insert.setSku(purchasesConfig.getSku());
                    insert.setLeadTime(purchasesConfig.getLeadTime());
                    insert.setSupplierId(purchasesConfig.getSupplierId());
                    insert.setStockRate(purchasesConfig.getStockRate());
                    insert.setCcAdminId(purchasesConfig.getCcAdminId());
                    insert.setAreaNo(storageCenter.getWarehouseNo());
                    insertList.add(purchasesConfigCreate(insert));
                }
            }
        } else {
            List<PurchasesConfig> purchasesConfigRecords = purchasesConfigMapper.selectBySomeKey(oldStorageCenter.getType(), oldStorageCenter.getAreaManageId(), oldStorageCenter.getWarehouseNo());
            PurchasesConfig select = new PurchasesConfig();
            select.setType(oldStorageCenter.getType());
            select.setAreaManageId(oldStorageCenter.getAreaManageId());
            List<PurchasesConfig> purchasesConfigs = purchasesConfigMapper.select(select);
            if (!CollectionUtils.isEmpty(purchasesConfigRecords)) {
                Map<String, PurchasesConfig> collect = new HashMap<>();
                if (!CollectionUtils.isEmpty(purchasesConfigs)) {
                    collect = purchasesConfigs.stream().collect(Collectors.toMap(PurchasesConfig::getSku, PurchasesConfig -> PurchasesConfig));
                }

                List<Integer> areaNos = new ArrayList<>();
                WarehouseStorageCenter query = new WarehouseStorageCenter();
                query.setType(oldStorageCenter.getType());
                query.setAreaManageId(oldStorageCenter.getAreaManageId());
                List<WarehouseStorageCenter> centerList = storageService.selectAllForSummerfarm(query);
                if (!CollectionUtils.isEmpty(centerList)) {
                    areaNos = centerList.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList());
                }

                for (PurchasesConfig purchasesConfig : purchasesConfigRecords) {

                    if (!collect.containsKey(purchasesConfig.getSku())) {
                        PurchasesConfig insert = new PurchasesConfig();
                        insert.setType(oldStorageCenter.getType());
                        insert.setAreaManageId(oldStorageCenter.getAreaManageId());
                        insert.setSku(purchasesConfig.getSku());
                        insert.setLeadTime(purchasesConfig.getLeadTime());
                        insert.setSupplierId(purchasesConfig.getSupplierId());
                        insert.setStockRate(purchasesConfig.getStockRate());
                        insert.setCcAdminId(purchasesConfig.getCcAdminId());
                        insertList.add(purchasesConfigCreate(insert));
                    }

                    //只有当前仓有就删除，否则更新
                    if (!CollectionUtils.isEmpty(areaNos)) {
                        List<AreaStore> areaStores = areaStoreMapper.selectBySomeKey(purchasesConfig.getSku(), areaNos);
                        if (CollectionUtils.isEmpty(areaStores)) {
                            deleteList.add(purchasesConfig.getId());
                        } else {
                            updateList.add(purchasesConfig);
                        }
                    } else {
                        deleteList.add(purchasesConfig.getId());
                    }
                }
            }
            updateList.addAll(purchasesConfigs);
        }


        if (!CollectionUtils.isEmpty(deleteList)) {
            purchasesConfigMapper.deleteBatch(deleteList);
        }

        if (!CollectionUtils.isEmpty(insertList)) {
            purchasesConfigMapper.insertBatch(insertList);
        }

        if (!CollectionUtils.isEmpty(updateList)) {
            for (PurchasesConfig config : updateList) {
                update(config);
            }
        }
    }

    @Override
    public PurchasesConfig selectPurchasesConfig(String sku, Integer storeNo) {
        PurchasesConfig purchasesConfig = new PurchasesConfig();
        purchasesConfig.setAreaNo(storeNo);
        purchasesConfig.setSku(sku);
        PurchasesConfig queryConfig = purchasesConfigMapper.selectOne(purchasesConfig);
        return queryConfig;
    }

    @Override
    public PurchasesConfig selectOnePurchasesConfig(String sku, Integer storeNo,Integer type) {
        PurchasesConfig purchasesConfig = new PurchasesConfig();
        purchasesConfig.setAreaNo(storeNo);
        purchasesConfig.setSku(sku);
        purchasesConfig.setType(type);
        PurchasesConfig queryConfig = purchasesConfigMapper.selectOne(purchasesConfig);
        return queryConfig;
    }


    public void purchasesConfigUpdate(PurchasesConfig purchasesConfig){
        PurchasesConfig data = purchaseCalculateService.calcSafeLevelSingle(purchasesConfig);
        Integer status = checkStatus(purchasesConfig);
        PurchasesConfig update = new PurchasesConfig();
        update.setId(purchasesConfig.getId());
        update.setStatus(status);
        update.setSafeLevel(data.getSafeLevel());
        update.setCalcQuantity(data.getCalcQuantity());
        update.setUpdateTime(LocalDateTime.now());
        purchasesConfigMapper.update(update);

//        if (status == 1 && purchasesConfig.getStatus() == 0){ //触发预警
//            handleMsg(purchasesConfig);
//        }
    }

    public PurchasesConfig purchasesConfigCreate(PurchasesConfig purchasesConfig){
        PurchasesConfig data = purchaseCalculateService.calcSafeLevelSingle(purchasesConfig);
        purchasesConfig.setSafeLevel(data.getSafeLevel());
        purchasesConfig.setCalcQuantity(data.getCalcQuantity());

        Integer status = checkStatus(purchasesConfig);
        purchasesConfig.setStatus(status);
//        if (status == 1){
//            handleMsg(purchasesConfig);
//        }
        return purchasesConfig;
    }


    /**
     * 校验数据权限
     */
    public List<Integer> validateDataPermissions(Integer type,Integer areaNo,Integer areaManageId){
        if (type == null){
            throw new DefaultServiceException("请先选择仓库类型");
        }
        if (AreaTypeEnum.EXTERNAL_AREA.getType().equals(type)){ //外部仓
            if (areaNo == null){
                throw new DefaultServiceException("外部仓需传仓库编号字段");
            }
        }else if (AreaTypeEnum.PARTNER_AREA.getType().equals(type)){ //合伙人仓
            if (areaManageId == null){
                throw new DefaultServiceException("合伙人仓需传所属合伙人字段");
            }
        } else if(AreaTypeEnum.INTERNAL_AREA.getType().equals(type)){
            areaNo = null;
        }
        List<WarehouseStorageCenter> storageCenters = warehouseStorageService.selectAllForSummerfarm(new WarehouseStorageCenter());

        return storageCenters.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList());
    }

    /**
    * 采购仓转为调拨仓
    */
    public void compareTransfer(Integer storeNo,String sku,Long leadTime){
        LocalDate localDate = LocalDate.now().plusDays(leadTime + 1);
        //获取下个周期
        LocalDate endDate = localDate.plusDays(Global.TI_MING_ALLOCATION);
        asyncTaskService.switchTimingOrderLock(sku,storeNo,localDate,endDate,UPDATE_PURCHASE);

    }
}
