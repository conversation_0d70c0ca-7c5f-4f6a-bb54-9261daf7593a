package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.enums.MerchantEnum;
import net.summerfarm.facade.WarehouseStorageQueryFacade;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.model.domain.Area;
import net.summerfarm.model.vo.AreaSkuVO;
import net.summerfarm.model.vo.AreaVO;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.service.AreaSkuPopService;
import net.summerfarm.service.AreaSkuService;
import net.summerfarm.service.WarehouseService;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.wnc.client.resp.WarehouseStorageCenterResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @author: xiaowk
 * @time: 2024/7/30 下午3:22
 */
@Slf4j
@Service
public class AreaSkuPopServiceImpl implements AreaSkuPopService {

    @Resource
    private AreaSkuService areaSkuService;

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;

    /**
     * [{"warehouseNo":479,"parentNo":185,"areaNo":29453,"openSale":1,"price":9999,"mType":1,"cornerStatus":1,"salesMode":0,"sku":"4831436333","seconds":0,"info":"","_index":0,"_rowKey":29453,"show":true}]
     *
     * @param sku
     */
    @Override
    public void popSkuInitAreaSku(String sku) {
        try {
            // 鲜果pop 运营区域
            Area query = new Area();
            query.setStatus(true);
            query.setBusinessLine(MerchantEnum.BusinessLineEnum.POP.getCode());
            List<AreaVO> areaList = areaMapper.selectListVO(query);
            if (CollectionUtils.isEmpty(areaList)) {
                log.error("鲜果pop 运营区域为空");
                return;
            }

            log.info("鲜果pop 运营区域areaNo={}", areaList.stream().map(AreaVO::getAreaNo).collect(Collectors.toList()));

            InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(sku);
            if (inventoryVO == null) {
                log.error("鲜果pop sku={} 信息不存在", sku);
                return;
            }

            //查询所有pop可以城配仓
            List<WarehouseStorageCenterResp> warehouseStorageCenterResps = warehouseStorageQueryFacade.queryAllPopWarehouseList();
            Set<Integer> warehouseNos = warehouseStorageCenterResps.stream().map(WarehouseStorageCenterResp::getWarehouseNo).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(warehouseNos)) {
                log.error("鲜果pop对应的仓库不存在\n");
                return;
            }

            for (AreaVO areaVO : areaList) {
                initAreaSku(sku, areaVO, inventoryVO, warehouseNos);
            }

        } catch (Exception e) {
            log.warn("初始化鲜果pop城市售卖信息错误，sku={}, ", sku, e);
        }
    }

    private void initAreaSku(String sku, AreaVO areaVO, InventoryVO inventoryVO, Set<Integer> warehouseNos){
        Integer areaNo = areaVO.getAreaNo();
        if (areaNo == null) {
            log.error("鲜果pop 默认运营区域areaNo为空");
            return;
        }

        //校验城市是否存在
        Area area = areaMapper.selectByAreaNo(areaNo);
        if (Objects.isNull(area)) {
            log.error("鲜果pop areaNo={}, 不存在", areaNo);
            return;
        }

        //假如是pop t+2 区域需要校验当前商品类目是否是黑名单类目
        if (dynamicConfig.getPopT2AreaNoList().contains(areaNo)) {
            List<Integer> thirdCategoryBlackList = dynamicConfig.getThirdCategoryBlackList();
            if (!CollectionUtils.isEmpty(thirdCategoryBlackList) && thirdCategoryBlackList.contains(inventoryVO.getCategoryId())) {
                log.warn("鲜果pop t+2区域：areaNo={}，城市对应的商品类目在黑名单中，自动过滤当前商品不进行同步，thirdCategoryBlackList={}", areaNo,
                        JSON.toJSONString(thirdCategoryBlackList));
                return;
            }
        }

        List<WarehouseStorageCenter> warehouseStorageCenterList = warehouseService.selectUsableStorageList(areaNo, sku);
        if (CollectionUtils.isEmpty(warehouseStorageCenterList)) {
            log.error("鲜果pop areaNo={}, 城市对应的库存仓不存在", areaNo);
            return;
        }

        List<AreaSkuVO> initAreaSkuList = Lists.newArrayList();

        for (WarehouseStorageCenter warehouseStorageCenter : warehouseStorageCenterList) {
            if (!warehouseNos.contains(warehouseStorageCenter.getWarehouseNo())) {
                log.error("鲜果pop areaNo={}, 城市对应的库存仓warehouseNo={} 不存在", areaNo, warehouseStorageCenter.getWarehouseNo());
                continue;
            }

            AreaSkuVO areaSkuVO = new AreaSkuVO();
            areaSkuVO.setAreaNo(areaNo);
            areaSkuVO.setSku(sku);
            areaSkuVO.setWarehouseNo(warehouseStorageCenter.getWarehouseNo());
            areaSkuVO.setParentNo(area.getParentNo());
            areaSkuVO.setSalesMode(0);
            areaSkuVO.setShow(true);
            // 2024-10-22 POP商城改成单店了, 默认不需要大客户专享
            areaSkuVO.setMType(0);
            areaSkuVO.setCornerStatus(1);
            areaSkuVO.setOpenSale(1);
            areaSkuVO.setPrice(new BigDecimal("9999"));

            initAreaSkuList.add(areaSkuVO);
        }

        log.info("初始化鲜果pop城市售卖信息请求参数，sku={}, initAreaSkuList={}", sku, JSON.toJSONString(initAreaSkuList));
        AjaxResult result = areaSkuService.batchHandle(initAreaSkuList);
        log.info("初始化鲜果pop城市售卖信息结果，sku={}, result={}", sku, JSON.toJSONString(result));
    }

}
