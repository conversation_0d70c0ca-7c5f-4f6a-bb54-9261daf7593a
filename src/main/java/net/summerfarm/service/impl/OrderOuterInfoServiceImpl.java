package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.Global;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.*;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;

import net.summerfarm.mq.constant.ProductMqConstant;
import net.summerfarm.mq.item.dto.MajorPricePushOuterMsgDTO;
import net.summerfarm.service.DingTalkService;
import net.summerfarm.service.FenceService;
import net.summerfarm.service.OrderOuterInfoService;
import net.summerfarm.task.MailUtil;
import net.summerfarm.task.quartz.JobManage;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 外部对接，订单同步服务
 * @createTime 2021年09月07日 14:00:00
 */
@Service
@Slf4j
public class OrderOuterInfoServiceImpl implements OrderOuterInfoService {

    @Resource
    private OrderOuterInfoMapper orderOuterInfoMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private DingTalkService dingTalkService;
    @Resource
    private MailUtil mailUtil;
    @Resource
    private MerchantOuterMapper merchantOuterMapper;
    @Resource
    private DeliveryPathMapper deliveryPathMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private TmsTaskMapper tmsTaskMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    private SkuMappingMapper skuMappingMapper;
    @Resource
    private MajorPriceMapper majorPriceMapper;
    @Resource
    private OuterPlatformMapper outerPlatformMapper;
    @Resource
    private OuterPlatformPushRecordMapper outerPlatformPushRecordMapper;
    @Resource
    private FenceService fenceService;
    @Resource
    private WarehouseLogisticsService logisticsService;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private JobManage jobManage;
    @Resource
    private Executor asyncServiceExecutor;

    @Autowired
    MqProducer mqProducer;

    /**
     * 分页查询
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @return
     */
    @Override
    public AjaxResult select(int pageIndex, int pageSize, OrderOuterInfoVo selectKeys) {
        PageHelper.startPage(pageIndex, pageSize);
        List<OrderOuterInfoVo> orderOuterInfoVos = orderOuterInfoMapper.selectOrderOuterInfo(selectKeys);
        //查询订单明细数据
        orderOuterInfoVos.forEach(orderOuterInfoVo -> {
            List<OrderOuterItem> orderOuterItemList = orderOuterInfoMapper.selectOrderOuterItem(orderOuterInfoVo.getOuterOrderNo(),orderOuterInfoVo.getOuterPlatformId());
            if(orderOuterItemList !=null && orderOuterItemList.size()>0) {
                orderOuterInfoVo.setPdName(orderOuterItemList.get(0).getPdName());
                orderOuterInfoVo.setXmSku(orderOuterItemList.get(0).getXmSku());
                orderOuterInfoVo.setAmount(orderOuterItemList.get(0).getAmount());
                if (orderOuterItemList.size() == 1) {
                    orderOuterInfoVo.setAllStatus(false);
                } else {
                    orderOuterInfoVo.setAllStatus(true);
                }
                orderOuterInfoVo.setOrderOuterItemList(orderOuterItemList);
            }
        });
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(orderOuterInfoVos));
    }

    /**
     * 重新同步
     * @param selectKeys
     * @return
     */
    @Override
    public AjaxResult placeOrder(OrderOuterInfoVo selectKeys) {
        log.info("重新同步开始");
        List<OrderOuterInfo> orders=orderOuterInfoMapper.selectPlaceOrder(selectKeys);
        //过滤非AOL订单
        orders=orders.stream().filter(order -> order.getOuterPlatformId() == 1).collect(Collectors.toList());
        //查询订单明细数据
        orders.forEach(placeOrder -> {
            List<OrderOuterItem> orderOuterItem = orderOuterInfoMapper.selectOrderOuterItem(placeOrder.getOrderNo(),placeOrder.getOuterPlatformId());
            placeOrder.setOrderOuterItemList(orderOuterItem);
        });
        //商城下单
        int successNumber=0;
        if(orders !=null && orders.size()>0) {
            successNumber = mallPlaceOrder(OuterPlatformEnum.AOL.getOuterPlatformId(), OuterPlatformMessageNoticeEnum.NO_NOTICE.getStatus(), orders);
        }
        String data="";
        if(orders.size()==1){
            if(successNumber==1) {
                data = "同步成功";
            }else{
                data = "同步失败";
            }
        }else{
            if(orders.size() == successNumber) {
                data = "成功"+successNumber+"条";
            }else{
                int failNumber=orders.size()-successNumber;
                data = "成功"+successNumber+"条，失败"+failNumber+"条";
            }
        }
        log.info("重新同步结束");
        return AjaxResult.getOK(data);
    }

    @Override
    public AjaxResult pullOrderAOL() {
        log.info("开始拉取AOL订单信息");
        //获取当天的日期的预计到货订单
        List<AOLOrderInput> aolOrderInputs = getAOLOrders();
        if(CollectionUtils.isEmpty(aolOrderInputs)){
            log.info("获取订单信息为空");
            Config config = configMapper.selectOne("AOlobotUrl");
            String value = config.getValue();
            dingTalkService.dingTalkRobotTxtAT(value,"没有获取到AOL订单",null);
            return AjaxResult.getOK();
        }
        //获取订单明细数据
        List<OrderOuterInfo> aolOrders = new ArrayList<>();
        List<OrderOuterItem> aolOrderItems = new ArrayList<>();
        for(AOLOrderInput aolOrderInput:aolOrderInputs){
            OrderOuterInfo aolOrder = new OrderOuterInfo(aolOrderInput);
            List<OrderOuterInfo> orderOuterInfo = orderOuterInfoMapper.queryOrderOuter(aolOrder.getOrderNo(),1);
            if (orderOuterInfo != null && orderOuterInfo.size() > 0) {
                log.info("AOL订单号:{}重复",aolOrder.getOrderNo());
                continue;
            }
            List<OrderOuterItem> orderOuterItemList = new ArrayList<>();
            List<AOLOrderItemInput> aolOrderItemInputs = aolOrderInput.getOrderItems();
            for (int i = 0; i < aolOrderItemInputs.size(); i++) {
                AOLOrderItemInput aolOrderItemInput = aolOrderItemInputs.get(i);
                OrderOuterItem aolOrderItem = new OrderOuterItem();
                aolOrderItem.setItemId(i+1);
                aolOrderItem.setSku(aolOrderItemInput.getSku());
                aolOrderItem.setAmount(aolOrderItemInput.getAmt());
                aolOrderItem.setStandard(aolOrderItemInput.getStandard());
                aolOrderItem.setUnit(aolOrderItemInput.getUnit());
                aolOrderItem.setName(aolOrderItemInput.getName());
                aolOrderItem.setOrderNo(aolOrderInput.getOrderNo());
                aolOrderItem.setOuterPlatformId(1);
                aolOrderItems.add(aolOrderItem);
                orderOuterItemList.add(aolOrderItem);
            }
            aolOrder.setOrderOuterItemList(orderOuterItemList);
            aolOrder.setOuterPlatformId(1);

            // 详细地址截取
            StringBuffer detailedAddress = new StringBuffer();
            if(StringUtils.isNotBlank(aolOrder.getProvince())){
                detailedAddress.append(aolOrder.getProvince());
            }
            if(StringUtils.isNotBlank(aolOrder.getCity())){
                detailedAddress.append(aolOrder.getCity());
            }
            if(StringUtils.isNotBlank(aolOrder.getArea())){
                detailedAddress.append(aolOrder.getArea());
            }
            if (detailedAddress.length() > 0 && StringUtils.isNotBlank(aolOrder.getAddress()) && aolOrder.getAddress().length() > detailedAddress.length()) {
                aolOrder.setDetailedAddress(aolOrder.getAddress().substring(detailedAddress.length(),aolOrder.getAddress().length()));
            }
            aolOrders.add(aolOrder);
        }
        //插入第三方订单信息
        if(aolOrders !=null && aolOrders.size()>0){
            orderOuterInfoMapper.insertList(aolOrders);
            orderOuterInfoMapper.insertItemList(aolOrderItems);
            //商城下单
            mallPlaceOrder(OuterPlatformEnum.AOL.getOuterPlatformId(),   OuterPlatformMessageNoticeEnum.NOTICE.getStatus(),aolOrders);
        }
        log.info("拉取AOL订单信息完成");
        return AjaxResult.getOK();
    }

    public int mallPlaceOrder(Integer outerPlatformId, Integer status, List<OrderOuterInfo> orderOuterInfos) {
        int successNumber=0;
        List<OrderOuterInfo> placeOrderErr = new ArrayList<>();
        HashMap<String, List<OrderOuterInfo>> errAOLOrderMap = new HashMap<>();
        List<OrderVO> orderVOS = checkoutAOLOrder(errAOLOrderMap, orderOuterInfos);
        //循环调用商城下单接口
        /*for (OrderVO orderVO : orderVOS) {
            JSONObject json = new JSONObject();
            json.put("outerPlatformId", orderVO.getOuterPlatformId());
            json.put("orderNo", orderVO.getOrderNo());
            json.put("remark", orderVO.getRemark());
            json.put("contactId", String.valueOf(orderVO.getContactId()));
            json.put("deliveryTime", orderVO.getDeliveryTime());
            json.put("orderItems", orderVO.getOrderItemVOs());
            String param = json.toJSONString();
            String result = HttpUtil.sendHttp(Global.AOL_HELP_ORDER, RequestMethod.POST, param, "application/json;charset=UTF-8");
            AjaxResult ajaxResult = JSON.parseObject(result, AjaxResult.class);
            if (!AjaxResult.isSuccess(ajaxResult)) {
                for (OrderOuterInfo orderOuterInfo : orderOuterInfos) {
                    if(orderOuterInfo.getOrderNo().equals(orderVO.getOrderNo())){
                        placeOrderErr.add(orderOuterInfo);
                        break;
                    }
                }
                orderOuterInfoMapper.updateFailureReason(orderVO.getOrderId().intValue(),ajaxResult.getMsg());
            }else {
                ++successNumber;
            }
        }*/
        if(!CollectionUtils.isEmpty(placeOrderErr)){
            errAOLOrderMap.put("商城下单异常",placeOrderErr);
        }
        //发送飞书消息
        if (OuterPlatformEnum.AOL.getOuterPlatformId().intValue() == outerPlatformId && OuterPlatformMessageNoticeEnum.NOTICE.getStatus().intValue() == status) {
            try{
                handleAOLOrderMap(errAOLOrderMap);
            }catch (Exception e){
                log.error("AOL通知异常", e);
            }
        }
        if (OuterPlatformEnum.JDSXC.getOuterPlatformId().intValue() == outerPlatformId && OuterPlatformMessageNoticeEnum.NOTICE.getStatus().intValue() == status) {
            try{
                handleJDOrderMap(errAOLOrderMap);
            }catch (Exception e){
                log.error("悸动烧仙草通知异常", e);
            }
        }

        //外部对接，外部实时推送的订单需要订单回调通知
        orderCallback(orderOuterInfos);
        return successNumber;
    }

    /**
     * 外部对接，外部实时推送的订单需要订单回调通知
     * @param orderOuterInfos
     */
    public void orderCallback(List<OrderOuterInfo> orderOuterInfos) {
        for (OrderOuterInfo orderOuterInfo : orderOuterInfos) {
            OuterPlatformVo outerPlatform = outerPlatformMapper.selectOuterPlatformById(orderOuterInfo.getOuterPlatformId());
            if(outerPlatform.getOrderCallBackSwitch() == 0){
                continue;
            }
            List<OrderOuterInfo> orderOuterInfoList = orderOuterInfoMapper.queryOrderOuter(orderOuterInfo.getOrderNo(), orderOuterInfo.getOuterPlatformId());
            OrderOuterInfo queryOrder = orderOuterInfoList.get(0);
            // 外部对接，悸动烧仙草成功订单不回调
            if(outerPlatform.getOuterPlatformId() == 2 && queryOrder.getStatus() == 1){
                continue;
            }
            JSONObject json = new JSONObject();
            // 判断悸动外部平台 下单失败按退货，其他外部平台按订单回调通知
            if (outerPlatform.getOuterPlatformId() == 2) {
                json.put("method", "returngoods");
            } else {
                json.put("method", "orderCallBack");
                json.put("order_status", queryOrder.getStatus());
            }
            String reason = "";
            if (queryOrder.getStatus() == 2) {
                reason = queryOrder.getFailureReason();
            }
            json.put("reason", reason);
            json.put("token", outerPlatform.getToken());
            json.put("order_id", queryOrder.getOrderNo());
            String param = json.toJSONString();
            log.info("外部对接-订单回调通知,请求数据：{}", param);
            String result = sendHttp(outerPlatform.getCallUrl(), param);
            log.info("外部对接-订单回调通知,返回数据：{}", result);
        }
    }

    //安佳发送邮件信息
    @Override
    public AjaxResult sendEmailAOL(){
        //获取今天发货的所有订单
        LocalDate now = LocalDate.now();
        //获取AOL安佳在线sku
        Config config = configMapper.selectOne("aj_online");
        String[] skus=config.getValue().replace("'","").split(",");
        List<MerchantOrderVO> merchantOrderVO = merchantOuterMapper.selectMerchantOrder(now,skus);
        List<MerchantOrderVO> merchantOrderList = new ArrayList<>();
        //获取对应关系
        for (MerchantOrderVO orderVO : merchantOrderVO) {
            Long contactId = orderVO.getContactId();
            LocalDate deliveryTime = orderVO.getDeliveryTime();
            Integer storeNo = orderVO.getStoreNo();
            DeliveryPath deliveryPath = deliveryPathMapper.selectOne(storeNo, deliveryTime, contactId);
            OrderOuterInfo resultInfo = orderOuterInfoMapper.queryInfoByXmOrderNo(orderVO.getOrderNo());
            //不是拉取的订单
            if(resultInfo == null){
                continue;
            }
            orderVO.setAolOrderNo(resultInfo.getOrderNo());
            orderVO.setAddTime(resultInfo.getGmtCreate().toLocalDate());
            List<OrderOuterItem> orderOuterItemList=orderOuterInfoMapper.selectOrderOuterItem(resultInfo.getOrderNo(),resultInfo.getOuterPlatformId());
            orderVO.setOrderOuterItemList(orderOuterItemList);
            //开始配送
            if(deliveryPath == null || deliveryPath.getPathStatus() == null || Objects.equals(deliveryPath.getPathStatus(),2)){
                continue;
            }
            OrderOuterInfo updateOrderOuter = new OrderOuterInfo();
            updateOrderOuter.setDeliveryStatus(1);
            updateOrderOuter.setId(resultInfo.getId());
            orderVO.setDeliveryStatus("配送中");
            orderVO.setDeliveryStartTime(LocalDateTime.now());
            //更新 对应关系并更新配送状态
            //更新对应关系及状态
            orderOuterInfoMapper.updateOrderOuter(updateOrderOuter);
            merchantOrderList.add(orderVO);
        }
        //发送消息 获取之前未配送完成的数据
        List<OrderOuterInfo> orderOuterInfos = orderOuterInfoMapper.selectByDeliveryStatus(now.minusDays(1));
        for (OrderOuterInfo oi : orderOuterInfos) {
            String orderNo = oi.getXmOrderNo();
            if(StringUtils.isEmpty(orderNo)){
                continue;
            }
            //找到订单信息
            OrderVO orderVO = ordersMapper.selectByOrderyNo(orderNo);
            //获取配送计划信息
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orderNo);
            if(CollectionUtils.isEmpty(deliveryPlanVOS)){
                continue;
            }
            DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);
            Contact contact = contactMapper.selectByPrimaryKey(deliveryPlanVO.getContactId());
            Integer storeNo = contact.getStoreNo();
            LocalDate deliveryTime = deliveryPlanVO.getDeliveryTime();
            Long contactId = deliveryPlanVO.getContactId();
            //获取配送单信息
            DeliveryPath deliveryPath = deliveryPathMapper.selectOne(storeNo, deliveryTime, contactId);

            if(deliveryPath != null){
                Integer pathStatus = deliveryPath.getPathStatus();
                String path = deliveryPath.getPath();
                TmsTask tmsTask = tmsTaskMapper.selectTmsTask(storeNo, deliveryTime,path);
                if(tmsTask == null || pathStatus == null){
                    continue;
                }
                MerchantOrderVO mt = new MerchantOrderVO();
                mt.setAolOrderNo(oi.getOrderNo());
                mt.setOrderNo(orderNo);
                mt.setMName(orderVO.getMname());
                mt.setDeliveryStartTime(tmsTask.getAddTime());
                mt.setAolOrderNo(oi.getOrderNo());
                mt.setAddTime(oi.getGmtCreate().toLocalDate());
                List<OrderOuterItem> orderOuterItemList=orderOuterInfoMapper.selectOrderOuterItem(oi.getOrderNo(),oi.getOuterPlatformId());
                mt.setOrderOuterItemList(orderOuterItemList);
                mt.setDeliveryTime(deliveryTime);
                OrderOuterInfo update = new OrderOuterInfo();

                if(pathStatus.intValue() == 0 || pathStatus.intValue() == 1 ){
                    //更新配送状态
                    mt.setDeliveryStatus("配送中");
                    update.setDeliveryStatus(1);
                    update.setId(oi.getId());
                } else if (pathStatus.intValue() == 2){
                    //更新配送状态
                    mt.setDeliveryStatus("配送完成");
                    mt.setFinishTime(deliveryPath.getFinishTime());
                    update.setDeliveryStatus(2);
                    update.setId(oi.getId());
                }
                merchantOrderList.add(mt);
                orderOuterInfoMapper.updateOrderOuter(update);
            }

        }

        if(merchantOrderList !=null && merchantOrderList.size()>0) {
            Map<String, Workbook> bodyPart = new HashMap<>();
            String deliveryDate = DateUtils.tranfLocalDate(now, DateUtils.DEFAULT_DATE_FORMAT);
            bodyPart.put(deliveryDate + "鲜沐科技有限公司开始配送信息.xls", handleOrderVO(merchantOrderList));
            try {
                String[] cc = new String[]{Global.AOL_DEVELOPER_GROUP};
                if (Global.isProduct()) {
                    cc = new String[]{Global.ALL_AOL_GROUP};
                }
                mailUtil.sendMail(deliveryDate + "杭州鲜沐科技有限公司开始配送信息", null, cc, null, bodyPart);
            } catch (Exception e) {
                log.error(Global.collectExceptionStackMsg(e));
            }
        }
        return AjaxResult.getOK();
    }

    //安佳晚上9点发送邮件信息
    @Override
    public AjaxResult sendEmailAOLFinish(){
        //获取今天发货的所有订单
        LocalDate now = LocalDate.now();
        List<MerchantOrderVO> sendMerchantList =new ArrayList<>();
        //获取AOL安佳在线sku
        Config config = configMapper.selectOne("aj_online");
        String[] skus=config.getValue().replace("'","").split(",");
        List<MerchantOrderVO> merchantOrderVO = merchantOuterMapper.selectMerchantOrder(now,skus);
        for (MerchantOrderVO orderVO : merchantOrderVO) {
            Long contactId = orderVO.getContactId();
            LocalDate deliveryTime = orderVO.getDeliveryTime();
            Integer storeNo = orderVO.getStoreNo();
            DeliveryPath deliveryPath = deliveryPathMapper.selectOne(storeNo, deliveryTime, contactId);

            //非配送完成
            if(deliveryPath == null || !Objects.equals(deliveryPath.getPathStatus(),2)){
                continue;
            }
            orderVO.setDeliveryStatus("配送完成");
            orderVO.setFinishTime(deliveryPath.getFinishTime());
            orderVO.setDeliveryStartTime(LocalDateTime.now());
            //获取aol订单信息
            OrderOuterInfo updateOrderOuter = new OrderOuterInfo();
            updateOrderOuter.setMId(orderVO.getOuterNo());
            updateOrderOuter.setDeliveryDate(orderVO.getDeliveryTime());
            //更新 对应关系并更新配送状态
            OrderOuterInfo orderOuterInfo = orderOuterInfoMapper.queryInfoByXmOrderNo(orderVO.getOrderNo());
            if(orderOuterInfo != null){
                //已经发送完成配送邮件
                orderVO.setAolOrderNo(orderOuterInfo.getOrderNo());
                orderVO.setAddTime(orderOuterInfo.getGmtCreate().toLocalDate());
                List<OrderOuterItem> orderOuterItemList=orderOuterInfoMapper.selectOrderOuterItem(orderOuterInfo.getOrderNo(),orderOuterInfo.getOuterPlatformId());
                orderVO.setOrderOuterItemList(orderOuterItemList);
                sendMerchantList.add(orderVO);
                //状态是完成
                updateOrderOuter.setDeliveryStatus(2);
                updateOrderOuter.setId(orderOuterInfo.getId());
                orderOuterInfoMapper.updateOrderOuter(updateOrderOuter);
            }

        }

        if(sendMerchantList !=null && sendMerchantList.size()>0) {
            Map<String, Workbook> bodyPart = new HashMap<>();
            String deliveryDate = DateUtils.tranfLocalDate(now, DateUtils.DEFAULT_DATE_FORMAT);
            bodyPart.put(deliveryDate + "鲜沐科技有限公司完成配送信息.xls", handleOrderVO(sendMerchantList));
            try {
                String[] cc = new String[]{Global.AOL_DEVELOPER_GROUP};
                if (Global.isProduct()) {
                    cc = new String[]{Global.ALL_AOL_GROUP};
                }
                mailUtil.sendMail(deliveryDate + "杭州鲜沐科技有限公司完成配送信息", null, cc, null, bodyPart);
            } catch (Exception e) {
                log.error("AOL配送邮件邮件错误，e={}", e.getMessage(), e);
                throw new DefaultServiceException(e);
            }
        }
        return AjaxResult.getOK();
    }

    /**
     * 获取AOL订单信息
     */
    private List<AOLOrderInput> getAOLOrders(){
        List<AOLOrderInput> aolOrderInputs=new ArrayList<>();
        HashMap<String, String> map = new HashMap<>();
        map.put("Content-Type","application/json");
        JSONObject json = new JSONObject();
        //获取开始时间
        Config aolStart = configMapper.selectOne("AOLStart");
        Config aolEnd = configMapper.selectOne("AOLEnd");
        Config aolStartDay = configMapper.selectOne("AOLStartDay");
        Config aolPullOrderUrl = configMapper.selectOne("AOLPullOrderUrl");
        Config aolStoreIds = configMapper.selectOne("AOLStoreId");
        String[] aolStoreId = aolStoreIds.getValue().split(",");
        for(int i=0;i<aolStoreId.length;i++) {
            LocalTime startTime = LocalTime.of(Integer.valueOf(aolStart.getValue()), 0, 0);
            LocalTime endTime = LocalTime.of(Integer.valueOf(aolEnd.getValue()), 0, 0);
            LocalDateTime localDateTimeLe = LocalDateTime.of(LocalDate.now(), endTime);
            LocalDateTime localDateTimeGt = LocalDateTime.of(LocalDate.now().minusDays(Long.parseLong(aolStartDay.getValue())), startTime);
            String deliverDateLe = DateUtils.localDateTimeToString(localDateTimeLe);
            String deliverDateGt = DateUtils.localDateTimeToString(localDateTimeGt);
            json.put("deliverDate_le", deliverDateLe);
            json.put("deliverDate_gt", deliverDateGt);
            json.put("storeId", aolStoreId[i]);
            String param = json.toJSONString();
            log.info("待发货订单请求地址：{},请求参数：{}", aolPullOrderUrl.getValue(), param);
            String result = HttpUtil.sendHttps(aolPullOrderUrl.getValue(), RequestMethod.POST, param, map);
            log.info("待发货订单返回参数：{}", result);
            List<AOLOrderInput> aolStoreOrders = JSONObject.parseArray(result, AOLOrderInput.class);
            aolOrderInputs.addAll(aolStoreOrders);
        }
        return aolOrderInputs;
    }

    /**
     * 校验订单信息
     */
    private List<OrderVO> checkoutAOLOrder(HashMap<String, List<OrderOuterInfo>> errAOLOrderMap, List<OrderOuterInfo> orderOuterInfoList) {
        //无用户信息
        List<OrderOuterInfo> noMerchantList = new ArrayList<>();
        //配送地址异常
        List<OrderOuterInfo> noContactList = new ArrayList<>();
        //sku不匹配
        List<OrderOuterInfo> noSkuList = new ArrayList<>();
        List<OrderVO> orderVOS = new ArrayList<>();
        flag:for (OrderOuterInfo orderOuterInfo : orderOuterInfoList) {
            OrderVO orderVO = new OrderVO();
            Integer outerPlatformId = orderOuterInfo.getOuterPlatformId();
            String outNo = orderOuterInfo.getMId();
            MerchantOuterDO merchantOuterInfo = merchantOuterMapper.selectByOuterNo(outNo,outerPlatformId);
            //校验用户信息
            if(merchantOuterInfo == null || merchantOuterInfo.getmId() == null){
                noMerchantList.add(orderOuterInfo);
                orderOuterInfoMapper.updateFailureReason(orderOuterInfo.getId(),"mid缺失");
                continue;
            }
            Long mId = merchantOuterInfo.getmId();
            //获取用户信息
            MerchantVO merchant = merchantMapper.selectMerchantByMid(mId);
            if(merchant == null){
                noMerchantList.add(orderOuterInfo);
                orderOuterInfoMapper.updateFailureReason(orderOuterInfo.getId(),"mid缺失");
                continue;
            }
            orderOuterInfo.setXMId(mId);

            //更新订单关联鲜沐门店
            OrderOuterInfo updateOrderOuterInfo = new OrderOuterInfo();
            updateOrderOuterInfo.setId(orderOuterInfo.getId());
            updateOrderOuterInfo.setXMId(orderOuterInfo.getXMId());
            orderOuterInfoMapper.updateOrderOuter(updateOrderOuterInfo);

            Admin admin=adminMapper.selectByPrimaryKey(merchant.getAdminId());
            if(admin == null) {
                noMerchantList.add(orderOuterInfo);
                orderOuterInfoMapper.updateFailureReason(orderOuterInfo.getId(),"未找到订单门店所属总部");
                continue;
            }
            orderOuterInfo.setAdminName(admin.getRealname());
            //外部平台商品sku与鲜沐sku映射
            List<OrderOuterItem> orderOuterItemList = orderOuterInfo.getOrderOuterItemList();
            List<OrderItemVO> orderItemVOs = new ArrayList<>();
            for (OrderOuterItem orderOuterItem : orderOuterItemList) {
                SkuMappingVO skuMapping=skuMappingMapper.selectMappingSku(merchant.getAdminId(),outerPlatformId,orderOuterItem.getSku());
                if(skuMapping == null){
                    noSkuList.add(orderOuterInfo);
                    orderOuterInfoMapper.updateFailureReason(orderOuterInfo.getId(),"sku未在鲜沐配置");
                    break flag;
                }
                //更新订单明细信息
                orderOuterItem.setXmSku(skuMapping.getSku());
                orderOuterItem.setPdName(skuMapping.getPdName());
                orderOuterInfoMapper.updateOrderOuterItem(orderOuterItem);
                OrderItemVO orderItemVO=new OrderItemVO();
                orderItemVO.setSku(orderOuterItem.getXmSku());
                orderItemVO.setAmount(orderOuterItem.getAmount());
                orderItemVOs.add(orderItemVO);
            }
            //校验配送地址
            List<Contact> contacts = contactMapper.selectByMid(mId,1);
            boolean same = false;
            for (Contact contact : contacts) {
                String aolAddress = orderOuterInfo.getAddress();
                String address = contact.getAddress();
                //aol地址为省市区+详细地址
                if(aolAddress.contains(address)){
                    same = true;
                    orderVO.setContactId(contact.getContactId());
                    break;
                }
            }
            if(!same){
                // 订单收货地址在门店收货地址中未找到，新增门店收货地址
                Contact contact = new Contact();
                contact.setCity(orderOuterInfo.getCity());
                contact.setArea(orderOuterInfo.getArea());
                try {
                    // 判断收货地址是否在运营服务范围内
                    Integer areaNo = fenceService.getAreaNo(merchant.getPoiNote(),contact);
                    if(Objects.isNull(areaNo)){
                        noContactList.add(orderOuterInfo);
                        orderOuterInfoMapper.updateFailureReason(orderOuterInfo.getId(),"该地址不在运营服务范围内");
                        saveContact(contact,merchant,orderOuterInfo,3,null);
                        continue;
                    }
                    Integer storeNo = fenceService.getStoreNo(merchant.getPoiNote(),contact);
                    if(Objects.isNull(storeNo)){
                        noContactList.add(orderOuterInfo);
                        orderOuterInfoMapper.updateFailureReason(orderOuterInfo.getId(),"该地址不在运营服务范围内");
                        saveContact(contact,merchant,orderOuterInfo,3,null);
                        continue;
                    }
                    JSONObject jsonObject = GaoDeUtil.getGeoCode(orderOuterInfo.getAddress());
                    log.info("高德API返回数据：{}", jsonObject.toJSONString());
                    if ("1".equals(jsonObject.getString("status")) && !Objects.equals(jsonObject.getInteger("count"), 0)) {
                        JSONArray geocodes = jsonObject.getJSONArray("geocodes");
                        JSONObject geocode = geocodes.getJSONObject(0);
                        String location = geocode.getString("location");
                        contact.setPoiNote(location);
                    } else {
                        noContactList.add(orderOuterInfo);
                        orderOuterInfoMapper.updateFailureReason(orderOuterInfo.getId(),"当前地址在高德api中无法定位");
                        saveContact(contact,merchant,orderOuterInfo,3,null);
                        continue;
                    }
                    // 新增收货地址
                    saveContact(contact,merchant,orderOuterInfo,1,storeNo);
                    orderVO.setContactId(contact.getContactId());
                }catch (Exception e){
                    noContactList.add(orderOuterInfo);
                    orderOuterInfoMapper.updateFailureReason(orderOuterInfo.getId(),"该地址不在运营服务范围内");
                    saveContact(contact,merchant,orderOuterInfo,3,null);
                    continue;
                }
            }
            //第三方订单号
            //校验订单信息成功，封装商城下单数据
            orderVO.setOrderNo(orderOuterInfo.getOrderNo());
            orderVO.setRemark(orderOuterInfo.getRemark());
            orderVO.setmId(mId);
            orderVO.setDeliveryTime(orderOuterInfo.getDeliveryDate());
            orderVO.setOrderId(Long.valueOf(orderOuterInfo.getId()));
            orderVO.setOrderItemVOs(orderItemVOs);
            orderVO.setOuterPlatformId(orderOuterInfo.getOuterPlatformId());
            orderVOS.add(orderVO);
        }
        errAOLOrderMap.put("用户信息异常",noMerchantList);
        errAOLOrderMap.put("配送地址异常",noContactList);
        errAOLOrderMap.put("sku信息异常",noSkuList);
        return orderVOS;
    }

    /**
     * 新增收货地址
     * @param contact
     * @param merchant
     * @param orderOuterInfo
     * @param status
     * @param storeNo
     */
    public void saveContact(Contact contact,Merchant merchant,OrderOuterInfo orderOuterInfo,Integer status,Integer storeNo){
        contact.setContact(merchant.getMcontact());
        contact.setmId(merchant.getmId());
        contact.setPhone(merchant.getPhone());
        contact.setProvince(orderOuterInfo.getProvince());
        contact.setCity(orderOuterInfo.getCity());
        contact.setArea(orderOuterInfo.getArea());
        contact.setAddress(orderOuterInfo.getDetailedAddress());
        contact.setStatus(status);
        if (status == 1) {
            BigDecimal distance = BigDecimal.ZERO;
            WarehouseLogisticsCenter logisticsCenter = logisticsService.selectByStoreNo(storeNo);

            logisticsCenter = Optional.ofNullable(logisticsCenter).orElse(new WarehouseLogisticsCenter());
            PoiVO logisticsCenterPoi = SplitUtils.string2poi(logisticsCenter.getPoiNote());
            PoiVO contactPoi = SplitUtils.string2poi(merchant.getPoiNote());

            if(Objects.nonNull(logisticsCenterPoi) && Objects.nonNull(contactPoi)){
                double distanceDou = DistanceUtil.getDistance(logisticsCenterPoi.getLon(),logisticsCenterPoi.getLat()
                        ,contactPoi.getLon(),contactPoi.getLat());
                distance = new BigDecimal(distanceDou);
            }
            contact.setDistance(distance);
            contact.setStoreNo(storeNo);
        }
        contactMapper.insertSelective(contact);
    }

    private void handleAOLOrderMap(HashMap<String, List<OrderOuterInfo>> errAOLOrderMap){
        StringBuffer msg = new StringBuffer();
        Config config = configMapper.selectOne("AOlobotUrl");
        String value = config.getValue();
        msg.append("AOL批量下单异常:").append("\n");
        StringBuffer err = new StringBuffer();
        errAOLOrderMap.forEach((type,orderOuterInfoList) -> {
            if(!CollectionUtils.isEmpty(orderOuterInfoList)){
                err.append(type).append(":").append("\n");
                for (OrderOuterInfo orderOuterInfo : orderOuterInfoList) {
                    err.append("AOL订单号-"+orderOuterInfo.getAdminName()+":")
                        .append(orderOuterInfo.getOrderNo())
                        .append(",鲜沐mid:")
                        .append(orderOuterInfo.getXMId());
                        for(OrderOuterItem orderOuterItem:orderOuterInfo.getOrderOuterItemList()){
                            err.append(",数量:")
                                .append(orderOuterItem.getAmount())
                                .append(",鲜沐sku:")
                                .append(orderOuterItem.getXmSku());
                        }
                    err.append("\n");
                }
                err.append("\n");
            }
        });
        if(StringUtil.isEmpty(err.toString())){
            dingTalkService.dingTalkRobotTxtAT(value,"AOL订单，全部生成完成",null);
            return;
        }
        msg.append(err);
        log.info(msg.toString());
        dingTalkService.dingTalkRobotTxtAT(value,msg.toString(),null);
        return ;
    }

    private void handleJDOrderMap(HashMap<String, List<OrderOuterInfo>> errAOLOrderMap){
        StringBuffer msg = new StringBuffer();
        Config config = configMapper.selectOne("OuterPlatformLobotUrl");
        Config messageNoticeAddressee = configMapper.selectOne("JDMessageNoticeAddressee");

        String value = config.getValue();
        msg.append("订单同步失败-悸动烧仙草:\n");
        StringBuffer err = new StringBuffer();
        errAOLOrderMap.forEach((type, orderOuterInfoList) -> {
            if (!CollectionUtils.isEmpty(orderOuterInfoList)) {
                err.append("失败原因：").append(type).append("\n");
                for (OrderOuterInfo orderOuterInfo : orderOuterInfoList) {
                    err.append("外部订单号:").append(orderOuterInfo.getOrderNo()).append("\n")
                            .append("下单时间：").append(DateUtils.localDateTimeToString(LocalDateTime.now())).append("\n")
                            .append("手机号：").append(orderOuterInfo.getMphone()).append("\n");
                }
            }
        });
        if(StringUtil.isEmpty(err.toString())){
            return;
        }

        msg.append(err);
        msg.append("有劳及时处理喔。路径：客户管理-品牌—外部对接");
        log.info(msg.toString());
        dingTalkService.dingTalkRobotTxtAT(value,msg.toString(),null);
        DingTalkMsgReceiverIdBO dingTalkMsgBO = new DingTalkMsgReceiverIdBO();
        dingTalkMsgBO.setReceiverIdList(Collections.singletonList(Long.valueOf(messageNoticeAddressee.getValue())));
        dingTalkMsgBO.setContent(msg.toString());
        dingTalkMsgBO.setMsgType(DingTalkMsgTypeEnum.TXT.getType());
        dingTalkMsgSender.sendMessageWithFeiShu(dingTalkMsgBO);
        return ;
    }

    private Workbook handleOrderVO(List<MerchantOrderVO> merchantOrderVOS){
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet("sheet1");
        int rowIndex = 1;

        Font titleFont = workbook.createFont();
        titleFont.setFontName("宋体");
        titleFont.setFontHeightInPoints((short) 12);
        titleFont.setBold(Boolean.TRUE);

        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setFont(titleFont);

        sheet.setColumnWidth(0, ExcelUtils.getColumnWidth(32));
        sheet.setColumnWidth(1, ExcelUtils.getColumnWidth(32));
        sheet.setColumnWidth(2, ExcelUtils.getColumnWidth(32));
        sheet.setColumnWidth(3, ExcelUtils.getColumnWidth(32));
        sheet.setColumnWidth(4, ExcelUtils.getColumnWidth(18));
        sheet.setColumnWidth(5, ExcelUtils.getColumnWidth(18));
        sheet.setColumnWidth(6, ExcelUtils.getColumnWidth(12));
        sheet.setColumnWidth(7, ExcelUtils.getColumnWidth(24));
        sheet.setColumnWidth(8, ExcelUtils.getColumnWidth(12));


        Row title = sheet.createRow(0);
        String[] titleName = {"鲜沐订单编号", "安佳订单编号", "客户名称","订单日期","开始配送时间", "完成配送时间", "配送状态","商品名称","订单数量"};
        for (int i = 0; i < titleName.length; i++) {
            Cell cell = title.createCell(i);
            cell.setCellStyle(titleStyle);
            cell.setCellValue(titleName[i]);
        }

        Font cellFont = workbook.createFont();
        cellFont.setFontName("宋体");
        cellFont.setFontHeightInPoints((short) 11);
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(cellFont);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        if(!CollectionUtils.isEmpty(merchantOrderVOS)){
            for (int i = 0; i < merchantOrderVOS.size(); i++) {
                MerchantOrderVO merchantOrderVO = merchantOrderVOS.get(i);
                Row row = sheet.createRow(rowIndex);
                row.createCell(0).setCellValue(merchantOrderVO.getOrderNo());
                row.createCell(1).setCellValue(merchantOrderVO.getAolOrderNo());
                row.createCell(2).setCellValue(merchantOrderVO.getMName());
                String addTime = DateUtils.tranfLocalDate(merchantOrderVO.getAddTime(), DateUtils.DEFAULT_DATE_FORMAT);
                row.createCell(3).setCellValue(addTime);
                String deliveryDate = DateUtils.tranfLocalDate(merchantOrderVO.getDeliveryTime(), DateUtils.DEFAULT_DATE_FORMAT);
                row.createCell(4).setCellValue(deliveryDate);
                if(merchantOrderVO.getFinishTime() != null){
                    String finish = DateUtils.localDateTimeToString(merchantOrderVO.getFinishTime());
                    row.createCell(5).setCellValue(finish);
                }
                row.createCell(6).setCellValue(merchantOrderVO.getDeliveryStatus());
                //订单明细
                List<OrderOuterItem> orderOuterItemList=merchantOrderVO.getOrderOuterItemList();
                for (int j=0;j<orderOuterItemList.size();j++) {
                    OrderOuterItem orderOuterItem=orderOuterItemList.get(j);
                    row.createCell(7).setCellValue(orderOuterItem.getName());
                    row.createCell(8).setCellValue(orderOuterItem.getAmount());
                    if(orderOuterItemList.size()>1){
                        row = sheet.createRow(rowIndex + 1 + j);
                    }
                }
                //合并单元格
                if(orderOuterItemList.size() > 1){
                    for (int j = 0; j < 7; j++) {
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex,rowIndex+orderOuterItemList.size() - 1 ,j,j));
                    }
                }
                row.setRowStyle(cellStyle);
                rowIndex = rowIndex + orderOuterItemList.size() ;
            }
        }
        return workbook;
    }

    /**
     * 查询外部对接平台
     * @return
     */
    @Override
    public AjaxResult selectOuterPlatform() {
        List<OuterPlatformVo> outerPlatformList=outerPlatformMapper.selectOuterPlatform();
        return AjaxResult.getOK(outerPlatformList);
    }

    /**
     * 向外部对接平台，推送门店信息
     * @param content
     */
    @Override
    public void pushStore(String content) {
        JSONObject contentJson = JSONObject.parseObject(content);
        String method = contentJson.getString("method");
        String outerNo = contentJson.getString("outerNo");
        Integer outerPlatformId = contentJson.getInteger("outerPlatformId");
        Integer areaNo = contentJson.getInteger("areaNo");
        // 根据外部平台id查询
        OuterPlatformVo outerPlatform = outerPlatformMapper.selectOuterPlatformById(outerPlatformId);
        if (outerPlatform.getPushStoreSwitch() == PushSwitchEnum.OPEN.getSwitchStatus().intValue()) {
            JSONObject json = new JSONObject();
            json.put("method", method);
            json.put("token", outerPlatform.getToken());
            json.put("store_sn", outerNo);
            json.put("city_code", areaNo);
            String param = json.toJSONString();
            log.info("外部对接-推送门店{},请求数据：{}", "storeadd".equals(method) ? "关联城市" : "删除", param);
            String result = sendHttp(outerPlatform.getCallUrl(), param);
            log.info("外部对接-推送门店{},返回数据：{}", "storeadd".equals(method) ? "关联城市" : "删除", result);
        }
    }

    /**
     * 向外部对接平台，初始化推送商品上下架、商品价格信息
     */
    @Override
    public void pushOnSaleInit() {
        List<SkuMappingVO> skuMappingList = skuMappingMapper.selectOuterPlatformSkuList();
        for (SkuMappingVO skuMapping : skuMappingList) {
            // 根据外部平台id、adminId查询关联了外部平台的门店
            List<MerchantOuterDOVO> merchantOuterList = merchantOuterMapper.selectByOuterMerchant(skuMapping.getOuterPlatformId(), skuMapping.getAdminId());
            // 根据城市分组，进行城市推送上下架
            Map<Integer, List<MerchantOuterDOVO>> merchantOuterMap= merchantOuterList.stream().collect(Collectors.groupingBy(MerchantOuterDOVO::getAreaNo));
            merchantOuterMap.forEach((areaNo, merchantOuterMapList) -> {
                mqPushOnSaleUpdatePrice(skuMapping.getSku(), areaNo, skuMapping.getAdminId(), PushTypeEnum.ONSALE.getPushType());
            });
        }
    }

    /**
     * 根据城市编号，查询实际库存数据
     * @param areaNo
     * @param sku
     * @return
     */
    public AreaStore selectAreaStore(Integer areaNo, String sku) {
        Area area = areaMapper.selectByAreaNo(areaNo);
        Integer storeNo = fenceService.selectStoreNoByAreaNo((area.getAreaNo()));
        WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, sku);
        AreaStore areaStore = areaStoreMapper.selectOneNoAuth(new AreaStore(mapping.getWarehouseNo(), sku));
//        AreaStore areaStore = areaStoreMapper.selectOne(new AreaStore(mapping.getWarehouseNo(), sku));
        return areaStore;
    }

    /**
     * 外部对接，推送商品上下架信息
     * @param url
     * @param token
     * @param sn
     * @param cityCode
     * @param status
     * @return
     */
    public OuterPlatformRes sendPushOnSale(String url, String token, String sn, Integer cityCode, Integer status) {
        OuterPlatformRes outerPlatformRes = new OuterPlatformRes();
        Integer pushStatus = 2;
        String result = "";
        JSONObject json = new JSONObject();
        json.put("method", "poshelf");
        json.put("token", token);
        json.put("sn", sn);
        json.put("city_code", cityCode);
        json.put("status", status == 1 ? status : 2);
        String param = json.toJSONString();
        log.info("外部对接-推送商品{},请求数据：{}", status == 1 ? "上架" : "下架", param);
        try {
            result = sendHttp(url, param);
            log.info("外部对接-推送商品{},返回数据：{}", status == 1 ? "上架" : "下架", result);
            if (StringUtils.isNotBlank(result)){
                outerPlatformRes = JSON.parseObject(result, OuterPlatformRes.class);
            }
            if (outerPlatformRes != null && OuterPlatformResEnum.SUCCESS.getRecode().equals(outerPlatformRes.getRecode())) {
                pushStatus = 1;
            }
        } catch (Exception e) {
            log.error("外部堆积-推送商品上下架信息异常", e);
        }
        outerPlatformRes.setPushStatus(pushStatus);
        outerPlatformRes.setReqContent(param);
        outerPlatformRes.setResContent(result);
        return outerPlatformRes;
    }

    /**
     * 保存商品上下架推送记录
     * @param type
     * @param sku
     * @param areaNo
     * @param adminId
     * @param onSale
     * @param pushStatus
     * @param reqContent
     * @param resContent
     */
    public void saveOnSaleRecord(Integer type, String sku, Integer areaNo, Integer adminId,
                                 Integer onSale, Integer pushStatus, String reqContent, String resContent) {
        OuterPlatformPushRecord outerPlatformPushRecord = new OuterPlatformPushRecord();
        outerPlatformPushRecord.setType(type);
        outerPlatformPushRecord.setSku(sku);
        outerPlatformPushRecord.setAreaNo(areaNo);
        outerPlatformPushRecord.setAdminId(adminId);
        outerPlatformPushRecord.setOnSale(onSale);
        outerPlatformPushRecord.setPushStatus(pushStatus);
        outerPlatformPushRecord.setReqContent(reqContent);
        outerPlatformPushRecord.setResContent(resContent);
        outerPlatformPushRecordMapper.insertSelective(outerPlatformPushRecord);
    }

    /**
     * 外部对接，推送商品价格信息
     * @param url
     * @param token
     * @param sn
     * @param cityCode
     * @param cityName
     * @param price
     * @return
     */
    public OuterPlatformRes sendPushUpdatePrice(String url, String token, String sn, Integer cityCode, String cityName, BigDecimal price) {
        OuterPlatformRes outerPlatformRes = new OuterPlatformRes();
        Integer pushStatus = 2;
        String result = "";
        JSONObject json = new JSONObject();
        json.put("method", "upprice");
        json.put("token", token);
        json.put("sn", sn);
        json.put("city_code", cityCode);
        json.put("city_name", cityName);
        json.put("price", price);
        String param = json.toJSONString();
        log.info("外部对接-推送商品价格,请求数据：{}", param);
        try {
            result = sendHttp(url, param);
            log.info("外部对接-推送商品价格,返回数据：{}", result);
            outerPlatformRes = JSON.parseObject(result, OuterPlatformRes.class);
            if (outerPlatformRes != null && OuterPlatformResEnum.SUCCESS.getRecode().equals(outerPlatformRes.getRecode())) {
                pushStatus = 1;
            }
        } catch (Exception e) {
            log.error("外部堆积-推送商品上下架信息异常", e);
        }
        outerPlatformRes.setPushStatus(pushStatus);
        outerPlatformRes.setReqContent(param);
        outerPlatformRes.setResContent(result);
        return outerPlatformRes;
    }

    /**
     * 保存商品价格推送记录
     * @param type
     * @param sku
     * @param areaNo
     * @param adminId
     * @param price
     * @param pushStatus
     * @param reqContent
     * @param resContent
     */
    public void saveUpdatePriceRecord(Integer type, String sku, Integer areaNo, Integer adminId,
                                      BigDecimal price, Integer pushStatus, String reqContent, String resContent) {
        OuterPlatformPushRecord outerPlatformPushRecord = new OuterPlatformPushRecord();
        outerPlatformPushRecord.setType(type);
        outerPlatformPushRecord.setSku(sku);
        outerPlatformPushRecord.setAreaNo(areaNo);
        outerPlatformPushRecord.setAdminId(adminId);
        outerPlatformPushRecord.setPrice(price);
        outerPlatformPushRecord.setPushStatus(pushStatus);
        outerPlatformPushRecord.setReqContent(reqContent);
        outerPlatformPushRecord.setResContent(resContent);
        outerPlatformPushRecordMapper.insertSelective(outerPlatformPushRecord);
    }

    /**
     * 向外部对接平台，推送商品上下架、商品价格信息
     * @param sku
     * @param areaNo
     * @param adminId
     * @param status
     */
    @Override
    public void pushOnSaleUpdatePrice(String sku,Integer areaNo,Integer adminId,Integer status) {
        log.info("外部对接，校验数据开始------------------sku：{}，地区：{}，adminId：{},状态：{}", sku, areaNo, adminId, status);

        Set<String> invalidTimeSet = new HashSet<>();

        List<SkuMappingVO> skuMappingList = checkNeedPush(sku, areaNo, adminId);
        if (CollectionUtils.isEmpty(skuMappingList)) {
            log.info("外部对接，校验数据结束，无需推送--sku：{}，地区：{}，adminId：{},状态：{}", sku, areaNo, adminId, status);
            return;
        }

        for (SkuMappingVO skuMapping : skuMappingList) {
            // 根据外部平台id查询
            OuterPlatformVo outerPlatform = outerPlatformMapper.selectOuterPlatformById(skuMapping.getOuterPlatformId());
            // 判断外部平台是否推送商品基础信息
            if (outerPlatform.getPushGoodsSwitch() == PushSwitchEnum.CLOSE.getSwitchStatus().intValue()) {
                continue;
            }
            boolean onSale = false;
            boolean updatePrice = false;
            boolean notOnsale = false;
            MajorPriceInput majorPrice = null;
            // 根据上下架类型、sku、城市、adminId查询最新推送记录
            OuterPlatformPushRecord onSaleRecord = selectRecordByLatestOne(PushRrecordTypeEnum.ONSALE_NOTONSALE.getPushRrecordType(), sku, areaNo, skuMapping.getAdminId());
            OuterPlatformPushRecord updatePriceRecord = selectRecordByLatestOne(PushRrecordTypeEnum.UPDATEPRICE.getPushRrecordType(), sku, areaNo, skuMapping.getAdminId());

            // 0：商品下架
            if (status == PushTypeEnum.NOTONSALE.getPushType().intValue()) {
                //  判断上次是否推送过上架，推送过上架才推送下架
                if (onSaleRecord != null && onSaleRecord.getOnSale() == PushTypeEnum.ONSALE.getPushType().intValue()) {
                    notOnsale = true;
                }
            }
            // 1：商品上架、2：更新价格、3：报价单定时生效、4：有库存商品上架
            if (status != PushTypeEnum.NOTONSALE.getPushType().intValue()) {
                // 根据adminId、账期、sku、地区、报价单状态生效中查询报价单
                MajorPriceInput majorPriceInput = new MajorPriceInput();
                majorPriceInput.setAdminId(skuMapping.getAdminId());
                majorPriceInput.setDirect(1);
                majorPriceInput.setSku(sku);
                majorPriceInput.setAreaNo(areaNo);
                majorPriceInput.setValidStatus(1);
                List<MajorPriceInput> majorPriceList = majorPriceMapper.selectMajorPrice(majorPriceInput);
                // 报价单为空不上架
                if (CollectionUtils.isEmpty(majorPriceList)) {
                    log.info("外部对接，校验数据结束，无报价单--sku：{}，地区：{}，adminId：{},状态：{}", sku, areaNo, adminId, status);
                    continue;
                }
                majorPrice = majorPriceList.get(0);

                // 报价单上架状态 未上架
                boolean existedOnsale = status !=  PushTypeEnum.ONSALE.getPushType().intValue() && status !=  PushTypeEnum.STOCK_ONSALE.getPushType().intValue() && !majorPrice.getOnSale();
                if (existedOnsale) {
                    log.info("外部对接，校验数据结束，城市未上架--sku：{}，地区：{}，adminId：{},状态：{}", sku, areaNo, adminId, status);
                    continue;
                }

                // 根据城市、sku查询库存
                AreaStore areaStore = selectAreaStore(areaNo, sku);
                boolean existedStock = status !=  PushTypeEnum.STOCK_ONSALE.getPushType().intValue() && (areaStore == null || areaStore.getOnlineQuantity() <= 0);
                if (existedStock) {
                    log.info("外部对接，校验数据结束，无库存--sku：{}，地区：{}，adminId：{},状态：{}", sku, areaNo, adminId, status);
                    continue;
                }
                // 报价单价格形式是商城价取商城销售价
                if (majorPrice.getPriceType() == 0) {
                    majorPrice.setPrice(majorPrice.getSalePrice());
                }

                // 没有上架记录直接推上下架、推价格
                if (onSaleRecord == null || onSaleRecord.getOnSale() == PushTypeEnum.NOTONSALE.getPushType().intValue()) {
                    onSale = true;
                    updatePrice = true;
                }
                if (!updatePrice) {
                    if (updatePriceRecord != null && updatePriceRecord.getPrice().compareTo(majorPrice.getPrice()) == 0) {
                        log.info("外部对接，校验数据结束，价格一样不推送--sku：{}，地区：{}，adminId：{},状态：{}", sku, areaNo, adminId, status);
                        continue;
                    }
                    updatePrice = true;
                }

                if (status ==  PushTypeEnum.MAJORPRICE_TASK.getPushType().intValue()) {
                    // 报价单生效后添加报价单失效时间
                    invalidTimeSet.add(DateUtils.localDateTimeToString(majorPrice.getInvalidTime()));
                }
            }
            if (onSale) {
                // 外部对接，推送商品上架
                OuterPlatformRes outerPlatformRes = sendPushOnSale(outerPlatform.getCallUrl(), outerPlatform.getToken(), skuMapping.getMapping(), areaNo, onSale ? PushTypeEnum.ONSALE.getPushType() : PushTypeEnum.NOTONSALE.getPushType());
                // 保存上下架推送记录
                saveOnSaleRecord(PushRrecordTypeEnum.ONSALE_NOTONSALE.getPushRrecordType(), sku, areaNo, skuMapping.getAdminId(), onSale ? PushTypeEnum.ONSALE.getPushType() : PushTypeEnum.NOTONSALE.getPushType() ,
                        outerPlatformRes.getPushStatus(), outerPlatformRes.getReqContent(), outerPlatformRes.getResContent());
            }
            if (updatePrice) {
                // 外部对接，推送商品价格
                OuterPlatformRes outerPlatformRes = sendPushUpdatePrice(outerPlatform.getCallUrl(), outerPlatform.getToken(), skuMapping.getMapping(), areaNo, majorPrice.getAreaName(), majorPrice.getPrice());
                // 保存商品价格推送记录
                saveUpdatePriceRecord(PushRrecordTypeEnum.UPDATEPRICE.getPushRrecordType(), sku, areaNo, skuMapping.getAdminId(), majorPrice.getPrice(),
                        outerPlatformRes.getPushStatus(), outerPlatformRes.getReqContent(), outerPlatformRes.getResContent());
            }
            if (notOnsale) {
                // 外部对接，推送商品下架
                OuterPlatformRes outerPlatformRes = sendPushOnSale(outerPlatform.getCallUrl(), outerPlatform.getToken(), skuMapping.getMapping(), areaNo, onSale ? PushTypeEnum.ONSALE.getPushType() : PushTypeEnum.NOTONSALE.getPushType());
                // 保存商品下架推送记录
                saveOnSaleRecord(PushRrecordTypeEnum.ONSALE_NOTONSALE.getPushRrecordType(), sku, areaNo, skuMapping.getAdminId(), onSale ? PushTypeEnum.ONSALE.getPushType() : PushTypeEnum.NOTONSALE.getPushType(),
                        outerPlatformRes.getPushStatus(), outerPlatformRes.getReqContent(), outerPlatformRes.getResContent());
            }
            log.info("外部对接，校验数据结束，------------------sku：{}，地区：{}，adminId：{},状态：{}", sku, areaNo, adminId, status);
        }
        // 报价单生效后添加定时失效任务
        if (!CollectionUtils.isEmpty(invalidTimeSet)) {
            invalidTimeSet.stream().forEach(invalidTime -> {
                //延时消息处理
                MajorPricePushOuterMsgDTO msgDTO = new MajorPricePushOuterMsgDTO();
                msgDTO.setType(2);
                msgDTO.setAdminId(adminId);
                msgDTO.setTime(invalidTime);
                mqProducer.sendStartDeliver(ProductMqConstant.PRICE_ADJUSTMENT, ProductMqConstant.TAG_PUSH_OUTER,JSON.toJSONString(msgDTO), DateUtils.stringToLocalDateTime(invalidTime));
            });
        }
    }

    /**
     * 检查商品是否需要推送
     * @param sku
     * @param areaNo
     * @param adminId
     * @return
     */
    public List<SkuMappingVO> checkNeedPush(String sku,Integer areaNo,Integer adminId){
        Set<Integer> areaNoSet = new HashSet<Integer>(16);

        // 根据鲜沐sku查询外部对接商品映射的sku、外部平台
        SkuMappingVO querySkuMapping = new SkuMappingVO();
        querySkuMapping.setSku(sku);
        if (adminId != null) {
            querySkuMapping.setAdminId(adminId);
        }

        List<SkuMappingVO> skuMappingList = skuMappingMapper.selectOuterPlatformSkuByCondition(querySkuMapping);
        if (CollectionUtils.isEmpty(skuMappingList)) {
            return null;
        }

        skuMappingList.forEach(skuMapping -> {
            // 根据外部平台id、adminId查询关联了外部平台的门店城市
            List<MerchantOuterDOVO> merchantOuterList = merchantOuterMapper.selectByOuterMerchant(skuMapping.getOuterPlatformId(), skuMapping.getAdminId());
            merchantOuterList.forEach(merchantOuter -> {
                areaNoSet.add(merchantOuter.getAreaNo());
            });
        });

        if (!areaNoSet.contains(areaNo)) {
            return null;
        }

        return skuMappingList;
    }

    /**
     *  根据类型、sku、城市、adminId查询最新推送记录
     * @param type
     * @param sku
     * @param areaNo
     * @param adminId
     * @return
     */
    public OuterPlatformPushRecord selectRecordByLatestOne(Integer type,String sku,Integer areaNo,Integer adminId){
        OuterPlatformPushRecordVO queryRecord = new OuterPlatformPushRecordVO();
        queryRecord.setType(type);
        queryRecord.setSku(sku);
        queryRecord.setAreaNo(areaNo);
        queryRecord.setAdminId(adminId);
        queryRecord.setPushStatus(PushRrecordStatusEnum.SUCCESS.getPushStatus());
        return outerPlatformPushRecordMapper.selectRecordByLatestOne(queryRecord);
    }

    /**
     * 向外部对接平台，推送订单发货通知
     * @param content
     */
    @Override
    public void pushDeliveryNotice(String content) {
        JSONObject contentJson = JSONObject.parseObject(content);
        String xmOrderNo = contentJson.getString("orderNo");
        Integer pushTimes = contentJson.getInteger("pushTimes");
        if (pushTimes == null) {
            pushTimes = 0;
        }
        Integer pushTimesMax = 3;
        // 根据鲜沐订单号查询外部订单信息
        OrderOuterInfo orderOuterInfo = orderOuterInfoMapper.queryInfoByXmOrderNo(xmOrderNo);
        if (orderOuterInfo == null){
            return;
        }
        // 根据外部平台id查询外部平台信息
        OuterPlatformVo outerPlatform = outerPlatformMapper.selectOuterPlatformById(orderOuterInfo.getOuterPlatformId());
        // 判断外部平台是否要推送订单发货通知
        if (outerPlatform.getPushOrderSwitch() == PushSwitchEnum.CLOSE.getSwitchStatus().intValue()) {
            return;
        }
        // 根据外部订单号、外部平台id查询外部订单明细信息
        List<OrderOuterItem> orderOuterItemList = orderOuterInfoMapper.selectOrderOuterItem(orderOuterInfo.getOrderNo(), outerPlatform.getOuterPlatformId());
        // 根据订单号查询明细
        List<OrderItemVO> orderItemList = orderItemMapper.selectOrderItemByOrderNo(xmOrderNo);
        List<Map> list = new ArrayList<>();
        BigDecimal totalPrice = new BigDecimal(0);
        for (OrderOuterItem orderOuterItem : orderOuterItemList) {
            Map<String, Object> map = new HashMap<>();
            map.put("oi_id", orderOuterItem.getItemId());
            map.put("sn", orderOuterItem.getSku());
            map.put("nums", orderOuterItem.getAmount());
            // 外部对接，悸动烧仙草平台才推送以下数据
            if (outerPlatform.getOuterPlatformId() == OuterPlatformEnum.JDSXC.getOuterPlatformId().intValue()) {
                for (OrderItemVO orderItem : orderItemList) {
                    if (orderItem.getSku().equals(orderOuterItem.getXmSku()) && orderItem.getAmount().equals(orderOuterItem.getAmount())) {
                        map.put("pprice", orderItem.getSalePrice());
                        totalPrice = totalPrice.add(orderItem.getSalePrice().multiply(BigDecimal.valueOf(orderItem.getAmount())));
                    }
                }
            }
            list.add(map);
        }
        JSONObject json = new JSONObject();
        json.put("method", "deliver");
        json.put("token", outerPlatform.getToken());
        json.put("order_id", orderOuterInfo.getOrderNo());
        json.put("memo", "");
        json.put("items", JSON.parseArray(JSON.toJSONString(list)));
        // 外部对接，悸动烧仙草平台才推送以下数据
        if (outerPlatform.getOuterPlatformId() == OuterPlatformEnum.JDSXC.getOuterPlatformId().intValue()) {
            json.put("logi_code", "XM");
            json.put("logi_no", xmOrderNo);
            json.put("pprice_amount", totalPrice);
        }
        String param = json.toJSONString();
        log.info("外部对接-推送订单发货通知,请求数据：{}", param);
        Integer pushStatus = PushRrecordStatusEnum.FAIL.getPushStatus();
        String result = "";
        try {
            result = sendHttp(outerPlatform.getCallUrl(), param);
            log.info("外部对接-推送订单发货通知,返回数据：{}", result);
            OuterPlatformRes outerPlatformRes = JSON.parseObject(result, OuterPlatformRes.class);
            if (outerPlatformRes != null && OuterPlatformResEnum.SUCCESS.getRecode().equals(outerPlatformRes.getRecode())) {
                pushStatus = PushRrecordStatusEnum.SUCCESS.getPushStatus();
                pushTimes = pushTimes + 1;
            } else {
                if (pushTimes <= pushTimesMax) {
                    pushTimes = pushTimes + 1;
                    /*ScheduleJob job = new ScheduleJob(xmOrderNo + "_" + pushTimes, JobGroupType.REPUSH_DELIVERY_NOTICE);
                    LocalDateTime repushTime = LocalDateTime.now().plusMinutes(5);
                    job.setCronExpression(repushTime.format(DateTimeFormatter.ofPattern(DateUtils.QUARTZ_DATE_FORMAT)));
                    jobManage.addJob(job);*/
                }
            }
        } catch (Exception e) {
            log.error("外部对接-推送订单发货通知异常", e);
        }
        OuterPlatformPushRecord pushRecord = outerPlatformPushRecordMapper.selectRecordByXmOrderNo(xmOrderNo, PushRrecordTypeEnum.DELIVERY_NOTICE.getPushRrecordType());
        if (pushRecord == null) {
            OuterPlatformPushRecord outerPlatformPushRecord = new OuterPlatformPushRecord();
            outerPlatformPushRecord.setType(PushRrecordTypeEnum.DELIVERY_NOTICE.getPushRrecordType());
            outerPlatformPushRecord.setXmOrderNo(xmOrderNo);
            outerPlatformPushRecord.setPushStatus(pushStatus);
            outerPlatformPushRecord.setPushTimes(pushTimes);
            outerPlatformPushRecord.setReqContent(param);
            outerPlatformPushRecord.setResContent(result);
            outerPlatformPushRecordMapper.insertSelective(outerPlatformPushRecord);
        } else {
            pushRecord.setPushStatus(pushStatus);
            pushRecord.setPushTimes(pushTimes);
            outerPlatformPushRecordMapper.updateByPrimaryKeySelective(pushRecord);
        }
    }

    /**
     * 向外部对接平台，推送订单收货通知
     * @param content
     */
    @Override
    public void pushReceiptNotice(String content) {
        JSONObject contentJson = JSONObject.parseObject(content);
        // 订单发货明细
        String orderItemLists = contentJson.getString("orderItemList");
        // 缺货sku
        String shortSkuLists = contentJson.getString("shortSkuList");
        Integer pushTimes = contentJson.getInteger("pushTimes");
        if (pushTimes == null) {
            pushTimes = 0;
        }
        Integer pushTimesMax = 3;
        if(StringUtils.isBlank(orderItemLists)){
            return;
        }
        List<OrderItemVO> orderItemData = JSON.parseArray(orderItemLists, OrderItemVO.class);
        List<OrderItemVO> orderItemList = orderItemData.stream().filter(el -> Objects.isNull(el.getOrderNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemList)) {
            return;
        }
        List<DeliveryPathShortSku> shortSkuList = null;
        if(StringUtils.isNotBlank(shortSkuLists)){
            shortSkuList = JSON.parseArray(shortSkuLists, DeliveryPathShortSku.class);
        }

        // 订单发货明细根据sku分组，每个sku的应发货数量
        List<OrderItemVO> orderItemSkuList = new ArrayList<>();
        orderItemList.stream().collect(Collectors.groupingBy(OrderItem::getSku)).forEach((sku, item) -> {
            int sum = item.stream().mapToInt(OrderItem::getAmount).sum();
            OrderItemVO orderItemVO = new OrderItemVO();
            orderItemVO.setSku(sku);
            orderItemVO.setAmount(sum);
            orderItemSkuList.add(orderItemVO);

        });

        // 根据缺货sku计算每个sku实发货数量
        for (OrderItemVO orderItem : orderItemSkuList) {
            for (DeliveryPathShortSku shortSku : shortSkuList) {
                if (!orderItem.getSku().equals(shortSku.getSku())) {
                    continue;
                }
                orderItem.setAmount(orderItem.getAmount() - (shortSku.getShortCnt() == null ? 0: shortSku.getShortCnt()));
            }
        }

        // 订单发货明细根据订单号分组,根据订单维度向外部对接平台推送订单收货通知
        List<OrderItemVO> orderList = new ArrayList<>();
        orderItemList.stream().collect(Collectors.groupingBy(OrderItem::getOrderNo)).forEach((orderNo, item) -> {
            OrderItemVO orderItemVO = new OrderItemVO();
            orderItemVO.setOrderNo(orderNo);
            orderList.add(orderItemVO);
        });

        for (OrderItemVO order : orderList) {
            // 根据鲜沐订单号查询外部订单信息
            OrderOuterInfo orderOuterInfo = orderOuterInfoMapper.queryInfoByXmOrderNo(order.getOrderNo());
            if (orderOuterInfo == null) {
                return;
            }
            // 根据外部平台id查询外部平台信息
            OuterPlatformVo outerPlatform = outerPlatformMapper.selectOuterPlatformById(orderOuterInfo.getOuterPlatformId());
            // 判断外部平台是否要推送订单发货通知
            if (outerPlatform.getPushOrderSwitch() == PushSwitchEnum.CLOSE.getSwitchStatus().intValue()) {
                return;
            }

            // 根据外部订单号、外部平台id查询外部订单明细信息
            List<OrderOuterItem> orderOuterItemList = orderOuterInfoMapper.selectOrderOuterItem(orderOuterInfo.getOrderNo(), outerPlatform.getOuterPlatformId());
            List<Map> signedOrderItems = new ArrayList<>();
            for (OrderItemVO orderItem : orderItemList) {
                if (!order.getOrderNo().equals(orderItem.getOrderNo())) {
                    continue;
                }
                // 明细签收数量
                Integer signedInquantity = 0;
                for (OrderItemVO orderItemsSku : orderItemSkuList) {
                    if (orderItemsSku.getSku().equals(orderItem.getSku())) {
                        if (orderItemsSku.getAmount() > 0) {
                            Integer orderItemsSkuAmount = orderItemsSku.getAmount();
                            // sku实际发货数量-订单明细应发货数量，大于等于0签收数量=应发货数量，否则签收数量=实际发货数量
                            orderItemsSku.setAmount(orderItemsSku.getAmount() - orderItem.getAmount());
                            if (orderItemsSku.getAmount() >= 0) {
                                signedInquantity = orderItem.getAmount();
                            } else {
                                signedInquantity = orderItemsSkuAmount;
                            }
                        }
                    }
                }
                // 明细签收数量大于0 才推送明细签收信息
                if (signedInquantity > 0) {
                    Integer itemId = null;
                    String outerSku = "";
                    for (OrderOuterItem orderOuterItem : orderOuterItemList) {
                        if (orderItem.getSku().equals(orderOuterItem.getXmSku())) {
                            itemId = orderOuterItem.getItemId();
                            outerSku = orderOuterItem.getSku();
                        }
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("oi_id", itemId);
                    map.put("sn", outerSku);
                    map.put("nums", signedInquantity);
                    signedOrderItems.add(map);
                }
            }
            JSONObject json = new JSONObject();
            json.put("method", "orderconfirm");
            json.put("token", outerPlatform.getToken());
            json.put("order_id", orderOuterInfo.getOrderNo());
            json.put("memo", "");
            json.put("items", JSON.parseArray(JSON.toJSONString(signedOrderItems)));
            String param = json.toJSONString();
            log.info("外部对接-推送门店确认签收通知,请求数据：{}", param);
            Integer pushStatus = PushRrecordStatusEnum.FAIL.getPushStatus();
            String result = "";
            try {
                result = sendHttp(outerPlatform.getCallUrl(), param);
                log.info("外部对接-推送门店确认签收通知,返回数据：{}", result);
                OuterPlatformRes outerPlatformRes = JSON.parseObject(result, OuterPlatformRes.class);
                if (outerPlatformRes != null && OuterPlatformResEnum.SUCCESS.getRecode().equals(outerPlatformRes.getRecode())) {
                    pushStatus = PushRrecordStatusEnum.SUCCESS.getPushStatus();
                    pushTimes = pushTimes + 1;
                } else {
                    if (pushTimes <= pushTimesMax) {
                        pushTimes = pushTimes + 1;
                        /*ScheduleJob job = new ScheduleJob(orderItemLists + "_" + orderItemLists + "_" + pushTimes, JobGroupType.REPUSH_RECEIPT_NOTICE);
                        LocalDateTime repushTime = LocalDateTime.now().plusMinutes(5);
                        job.setCronExpression(repushTime.format(DateTimeFormatter.ofPattern(DateUtils.QUARTZ_DATE_FORMAT)));
                        jobManage.addJob(job);*/
                    }
                }
            } catch (Exception e) {
                log.error("外部对接-推送订单发货通知异常", e);
            }
            OuterPlatformPushRecord pushRecord = outerPlatformPushRecordMapper.selectRecordByXmOrderNo(orderOuterInfo.getXmOrderNo(), PushRrecordTypeEnum.RECEIPT_NOTICE.getPushRrecordType());
            if (pushRecord == null) {
                OuterPlatformPushRecord outerPlatformPushRecord = new OuterPlatformPushRecord();
                outerPlatformPushRecord.setType(PushRrecordTypeEnum.RECEIPT_NOTICE.getPushRrecordType());
                outerPlatformPushRecord.setXmOrderNo(orderOuterInfo.getXmOrderNo());
                outerPlatformPushRecord.setPushStatus(pushStatus);
                outerPlatformPushRecord.setPushTimes(pushTimes);
                outerPlatformPushRecord.setReqContent(param);
                outerPlatformPushRecord.setResContent(result);
                outerPlatformPushRecordMapper.insertSelective(outerPlatformPushRecord);
            } else {
                pushRecord.setPushStatus(pushStatus);
                pushRecord.setPushTimes(pushTimes);
                outerPlatformPushRecordMapper.updateByPrimaryKeySelective(pushRecord);
            }
        }
    }

    /**
     * 发送http请求
     * @param urlStr 地址
     * @param param 请求内容
     * @return
     */
    public String sendHttp(String urlStr, String param) {
        String contentType = "application/json";
        return HttpUtil.sendHttp(urlStr, RequestMethod.POST, param, contentType);
    }

    /**
     * 报价单定时生效、失效；向外部对接平台，推送上下架、价格信息
     * @param type
     * @param adminId
     * @param time
     */
    @Override
    public void executeMajorPriceValidInvalidTime(Integer type, Integer adminId, String time) {
        // 根据adminId、账期、生效时间和失效时间查询报价单信息
        MajorPriceInput majorPriceInput = new MajorPriceInput();
        majorPriceInput.setAdminId(adminId);
        majorPriceInput.setDirect(1);
        // type 1生效时间 2失效时间
        if (type == PushTypeEnum.ONSALE.getPushType().intValue()) {
            majorPriceInput.setValidTime(DateUtils.stringToLocalDateTime(time));
        } else {
            majorPriceInput.setInvalidTime(DateUtils.stringToLocalDateTime(time));
        }
        List<MajorPriceInput> majorPriceList = majorPriceMapper.selectMajorPrice(majorPriceInput);

        for (MajorPriceInput majorPrice : majorPriceList) {
            // 生效时间 走报价单定时生效
            if (type == PushTypeEnum.ONSALE.getPushType().intValue()) {
                mqPushOnSaleUpdatePrice(majorPrice.getSku(), majorPrice.getAreaNo(), majorPrice.getAdminId(), PushTypeEnum.MAJORPRICE_TASK.getPushType());
                continue;
            }

            // 失效时间
            LocalDateTime invalidTime = DateUtils.stringToLocalDateTime(time);
            // 根据adminId、账期、sku、地区、生效时间查询是否有马上生效的报价单
            MajorPriceInput majorPriceSku = new MajorPriceInput();
            majorPriceSku.setAdminId(adminId);
            majorPriceSku.setDirect(1);
            majorPriceSku.setSku(majorPrice.getSku());
            majorPriceSku.setAreaNo(majorPrice.getAreaNo());
            // type 2失效时间，根据失效时间去查询生效时间大于等于失效时间的数据
            majorPriceSku.setValidTime(invalidTime);
            List<MajorPriceInput> majorPriceSkuList = majorPriceMapper.selectMajorPrice(majorPriceSku);

            if (CollectionUtils.isEmpty(majorPriceSkuList)) {
                mqPushOnSaleUpdatePrice(majorPrice.getSku(), majorPrice.getAreaNo(), majorPrice.getAdminId(), PushTypeEnum.NOTONSALE.getPushType());
                continue;
            }

            // 根据生效时间和失效时间差大于一分钟才推送下架，小于等生效任务直接推价格更新
            LocalDateTime validTime = majorPriceSkuList.get(0).getValidTime();
            Double d = DateUtils.calculateToDateTime(validTime, invalidTime, "minute");
            if (d > 1) {
                mqPushOnSaleUpdatePrice(majorPrice.getSku(), majorPrice.getAreaNo(), majorPrice.getAdminId(), PushTypeEnum.NOTONSALE.getPushType());
            }
        }
    }

    /**
     * 接收外部对接平台的订单数据
     * @param orderData
     * @return
     */
    @Override
    public String orderServicePlaceOrder(OuterPlatformOrderReq orderData){
        try {
            if (StringUtils.isBlank(orderData.getToken())) {
                return orderServicePlaceOrderRes("2000", "token不能为空");
            }
            if (StringUtils.isBlank(orderData.getOrder_id())) {
                return orderServicePlaceOrderRes("2000", "订单号不能为空");
            }
            if (StringUtils.isBlank(orderData.getStore_sn())) {
                return orderServicePlaceOrderRes("2000", "门店编号不能为空");
            }
            if (StringUtils.isBlank(orderData.getShip_area())) {
                return orderServicePlaceOrderRes("2000", "地区名称不能为空");
            }
            if (StringUtils.isBlank(orderData.getShip_address())) {
                return orderServicePlaceOrderRes("2000", "详细地址不能为空");
            }
            if (StringUtils.isBlank(orderData.getShip_mobile())) {
                return orderServicePlaceOrderRes("2000", "收货人电话不能为空");
            }
            if (StringUtils.isBlank(orderData.getShip_name())) {
                return orderServicePlaceOrderRes("2000", "收货人姓名不能为空");
            }
            if (CollectionUtils.isEmpty(orderData.getItems())) {
                return orderServicePlaceOrderRes("2000", "订单明细不能为空");
            }
            OuterPlatformVo outerPlatform = outerPlatformMapper.selectOuterPlatformByToken(orderData.getToken());
            if (outerPlatform == null) {
                return orderServicePlaceOrderRes("2000", "token错误");
            }
            List<OrderOuterInfo> orderOuterInfo = orderOuterInfoMapper.queryOrderOuter(orderData.getOrder_id(), outerPlatform.getOuterPlatformId());
            if (!CollectionUtils.isEmpty(orderOuterInfo)) {
                return orderServicePlaceOrderRes("2000", "订单号重复");
            }
            List<OrderOuterInfo> orderList = new ArrayList<>();
            OrderOuterInfo order = new OrderOuterInfo();
            order.setMId(orderData.getStore_sn());
            order.setOrderNo(orderData.getOrder_id());
            order.setMphone(orderData.getShip_mobile());
            StringBuffer address = new StringBuffer();
            String[] shipAreaArray = orderData.getShip_area().split(" ");
            if (shipAreaArray.length > 0) {
                order.setProvince(shipAreaArray[0]);
                address.append(order.getProvince());
            }
            if (shipAreaArray.length > 1) {
                order.setCity(shipAreaArray[1]);
                address.append(order.getCity());
            }
            if (shipAreaArray.length > 2) {
                order.setArea(shipAreaArray[2]);
                address.append(order.getArea());
            }
            address.append(orderData.getShip_address());
            order.setAddress(address.toString());
            order.setDetailedAddress(orderData.getShip_address());
            order.setRemark(orderData.getMemo());
            List<OrderOuterItem> orderItemList = new ArrayList<>();
            List<OuterPlatformOrderItemReq> orderItems = orderData.getItems();
            orderItems.forEach(orderItem -> {
                OrderOuterItem aolOrderItem = new OrderOuterItem();
                aolOrderItem.setItemId(orderItem.getOi_id());
                aolOrderItem.setSku(orderItem.getSn());
                aolOrderItem.setAmount(orderItem.getNums());
                aolOrderItem.setOrderNo(order.getOrderNo());
                aolOrderItem.setOuterPlatformId(outerPlatform.getOuterPlatformId());
                orderItemList.add(aolOrderItem);
            });
            order.setOrderOuterItemList(orderItemList);
            order.setOuterPlatformId(outerPlatform.getOuterPlatformId());
            orderList.add(order);
            //插入第三方订单信息
            if (orderList != null && orderList.size() > 0) {
                orderOuterInfoMapper.insertList(orderList);
                orderOuterInfoMapper.insertItemList(orderItemList);
            }
            asyncServiceExecutor.execute(() -> {
                //商城下单
                try {
                    mallPlaceOrder(OuterPlatformEnum.JDSXC.getOuterPlatformId(), OuterPlatformMessageNoticeEnum.NOTICE.getStatus(), orderList);
                }catch (Exception e){
                    log.error("外部对接，调用下单异常",e);
                    //异步下单异常，订单回调通知下单失败
                    orderCallback(orderList);
                }
            });
        }catch (Exception e){
            log.error("外部对接，接收订单数据异常",e);
            return orderServicePlaceOrderRes("2000","接收订单数据异常");
        }
        return orderServicePlaceOrderRes("1000","成功");
    }

    /**
     * 接收外部对接平台的订单数据返回信息
     * @param recode 应答码
     * @param remsg 返回描述
     * @return
     */
    public String orderServicePlaceOrderRes(String recode,String remsg){
        JSONObject res=new JSONObject();
        res.put("recode",recode);
        res.put("remsg",remsg);
        return JSON.toJSONString(res);
    }

    /**
     * 外部对接，商品上下架、价格推送mq封装
     * @param sku
     * @param areaNo
     * @param adminId
     * @param status
     */
    @Override
    public void mqPushOnSaleUpdatePrice(String sku,Integer areaNo,Integer adminId,Integer status){
        List<SkuMappingVO> skuMappingList = checkNeedPush(sku, areaNo, adminId);
        if (CollectionUtils.isEmpty(skuMappingList)) {
            log.info("外部对接，校验数据无需放入MQ，无需推送--sku：{}，地区：{}，adminId：{},状态：{}", sku, areaNo, adminId, status);
            return;
        }

        JSONObject msgJson = new JSONObject();
        msgJson.put("sku", sku);
        msgJson.put("areaNo", areaNo);
        msgJson.put("adminId", adminId);
        msgJson.put("status", status);
        String producerMsg = msgJson.toJSONString();
        MQData mqData = new MQData();
        mqData.setType(MType.MQ_PUSH_ONSALE_UPDATEPRICE.name());
        mqData.setData(producerMsg);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
    }

    /**
     * 外部对接，门店关联城市、删除门店推送mq封装
     * @param method
     * @param outerNo
     * @param outerPlatformId
     * @param areaNo
     */
    @Override
    public void mqPushStore(String method,String outerNo,Integer outerPlatformId,Integer areaNo){
        // 根据外部平台id查询
        OuterPlatformVo outerPlatform = outerPlatformMapper.selectOuterPlatformById(outerPlatformId);
        // 判断外部平台是否推送门店关联城市
        if (outerPlatform.getPushStoreSwitch() == PushSwitchEnum.CLOSE.getSwitchStatus().intValue()) {
            return;
        }
        // 外部对接，推送门店关联城市、删除门店
        JSONObject msgJson = new JSONObject();
        msgJson.put("method", method);
        msgJson.put("outerNo", outerNo);
        msgJson.put("outerPlatformId", outerPlatformId);
        msgJson.put("areaNo", areaNo);
        String producerMsg = msgJson.toJSONString();
        MQData mqData = new MQData();
        mqData.setType(MType.PUSH_STORE.name());
        mqData.setData(producerMsg);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
    }

    /**
     * 外部对接，根据商品映射推送上下架
     * @param status
     * @param sku
     * @param mapping
     * @param outerPlatformId
     * @param adminId
     */
    @Override
    public void skuMappingPushOnSaleUpdatePrice(Integer status, String sku, String mapping, Integer outerPlatformId, Integer adminId) {
        // 根据外部平台id、adminId查询关联了外部平台的门店
        List<MerchantOuterDOVO> merchantOuterList = merchantOuterMapper.selectByOuterMerchant(outerPlatformId, adminId);
        // 根据城市分组，进行城市推送上下架
        Map<Integer, List<MerchantOuterDOVO>> merchantOuterMap= merchantOuterList.stream().collect(Collectors.groupingBy(MerchantOuterDOVO::getAreaNo));
        merchantOuterMap.forEach((areaNo, merchantOuterMapList) -> {
            // 商品上架
            if (status == PushTypeEnum.ONSALE.getPushType().intValue()) {
                mqPushOnSaleUpdatePrice(sku, areaNo, adminId, status);
                return;
            }

            // 商品下架
            // 根据外部平台id查询
            OuterPlatformVo outerPlatform = outerPlatformMapper.selectOuterPlatformById(outerPlatformId);

            // 根据上下架类型、sku、城市、adminId查询最新推送记录
            OuterPlatformPushRecord onSaleRecord = selectRecordByLatestOne(PushRrecordTypeEnum.ONSALE_NOTONSALE.getPushRrecordType(), sku, areaNo, adminId);
            // 0：商品下架，判断上次是否推送过上架，推送过上架才推送下架
            if (onSaleRecord != null && onSaleRecord.getOnSale() == PushTypeEnum.ONSALE.getPushType().intValue()) {
                // 外部对接，推送商品下架
                OuterPlatformRes outerPlatformRes = sendPushOnSale(outerPlatform.getCallUrl(), outerPlatform.getToken(), mapping, areaNo, PushTypeEnum.NOTONSALE.getPushType());
                // 保存商品下架推送记录
                saveOnSaleRecord(PushRrecordTypeEnum.ONSALE_NOTONSALE.getPushRrecordType(), sku, areaNo, adminId, PushTypeEnum.NOTONSALE.getPushType(),
                        outerPlatformRes.getPushStatus(), outerPlatformRes.getReqContent(), outerPlatformRes.getResContent());
            }
        });
    }

    /**
     * 外部对接，订单汇总
     */
    @Override
    public void outerOrderReport() {
        Config config = configMapper.selectOne("OuterPlatformLobotUrl");
        // 获取当日订单
        LocalDate now = LocalDate.now();
        OrderOuterInfoVo orderOuterInfoVo = new OrderOuterInfoVo();
        orderOuterInfoVo.setDailyTime(now);
        List<OrderOuterInfo> orderOuterInfoList = orderOuterInfoMapper.outerOrderReport(orderOuterInfoVo);
        if (CollectionUtils.isEmpty(orderOuterInfoList)) {
            return;
        }

        Map<Integer, List<OrderOuterInfo>> statusOrderOuterInfoMap = orderOuterInfoList.stream().collect(Collectors.groupingBy(OrderOuterInfo::getStatus));
        StringBuffer msg = new StringBuffer();
        msg.append(now.getMonthValue() + "月" + now.getDayOfMonth() + "日悸动烧仙草订单同步日报：").append("\n");

        statusOrderOuterInfoMap.forEach((status, statusOrderOuterInfoList) -> {
            Set<String> merchantOuterSet = new HashSet<String>();
            BigDecimal totalPrice = new BigDecimal(0);

            for (OrderOuterInfo orderOuterInfo : statusOrderOuterInfoList) {
                Orders xmOrders = ordersMapper.queryByOrderNo(orderOuterInfo.getXmOrderNo());
                if (xmOrders != null) {
                    totalPrice=totalPrice.add(xmOrders.getTotalPrice());
                }
                merchantOuterSet.add(orderOuterInfo.getOuterPlatformId()+orderOuterInfo.getMId());
            }
            msg.append(status == 1 ? "成功：" : "失败：")
                    .append(merchantOuterSet.size() + "家门店，共支付" + statusOrderOuterInfoList.size() + "单，实付GMV" + totalPrice + "元；")
                    .append("\n");
        });
        log.info("订单日汇报：{}", msg.toString());
        dingTalkService.dingTalkRobotTxtAT(config.getValue(), msg.toString(), null);
    }

    /**
     * 定时任务,查询明日配送的订单
     */
    @Override
    public void orderDeliveryNotice() {
        List<OrderOuterInfoVo> deliveryNoticeList =orderOuterInfoMapper.orderDeliveryNoticeList(LocalDate.now().plusDays(1));
        for (OrderOuterInfoVo deliveryNotice : deliveryNoticeList) {
            // 外部对接，向外部平台推送发货通知
            OrderOuterInfo orderOuterInfo = orderOuterInfoMapper.queryInfoByXmOrderNo(deliveryNotice.getXmOrderNo());
            if (orderOuterInfo != null) {
                JSONObject msgJson = new JSONObject();
                msgJson.put("orderNo", deliveryNotice.getXmOrderNo());
                String producerMsg = msgJson.toJSONString();
                MQData mqData = new MQData();
                mqData.setType(MType.PUSH_DELIVERY_NOTICE.name());
                mqData.setData(producerMsg);
                mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
            }
        }
    }

}
