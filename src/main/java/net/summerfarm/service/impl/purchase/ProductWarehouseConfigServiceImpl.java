package net.summerfarm.service.impl.purchase;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.BizException;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.AreaTypeEnum;
import net.summerfarm.mapper.PurchaseProductWarehouseConfigMapper;
import net.summerfarm.mapper.PurchaseProductWarehouseSupplierConfigMapper;
import net.summerfarm.mapper.PurchaseSupplierReplenishmentConfigMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.manage.ProductsMapper;
import net.summerfarm.mapper.manage.StockAllocationCategoryConfigMapper;
import net.summerfarm.mapper.manage.SupplierMapper;
import net.summerfarm.model.DTO.WarehouseDTO;
import net.summerfarm.model.DTO.purchase.*;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.purchase.PurchaseProductWarehouseConfig;
import net.summerfarm.model.domain.purchase.PurchaseProductWarehouseSupplierConfig;
import net.summerfarm.model.domain.purchase.PurchaseSupplierReplenishmentConfig;
import net.summerfarm.model.input.purchase.*;
import net.summerfarm.model.vo.ProductsVO;
import net.summerfarm.model.vo.purchase.ProductWarehouseConfigVO;
import net.summerfarm.model.vo.purchase.ProductWarehouseDetailConfigVO;
import net.summerfarm.model.vo.purchase.SupplierReplenishmentVO;
import net.summerfarm.module.scp.common.enums.ProductWarehouseConfigEnums;
import net.summerfarm.module.scp.common.enums.SupplierReplenishmentConfigEnums;
import net.summerfarm.service.purchase.ProductWarehouseConfigService;
import net.summerfarm.service.purchase.ProductWarehousePlannerHandler;
import net.summerfarm.warehouse.enums.WarehouseLogisticsCenterStatus;
import net.summerfarm.warehouse.enums.WarehouseStorageCenterEnum;
import net.summerfarm.warehouse.mapper.WarehouseStorageCenterMapper;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.model.input.WarehouseStorageCenterInput;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductWarehouseConfigServiceImpl extends BaseService implements ProductWarehouseConfigService {

    @Autowired
    private PurchaseProductWarehouseConfigMapper productWarehouseConfigMapper;

    @Autowired
    private PurchaseProductWarehouseSupplierConfigMapper productWarehouseSupplierConfigMapper;

    @Autowired
    private PurchaseSupplierReplenishmentConfigMapper supplierReplenishmentConfigMapper;

    @Autowired
    private InventoryMapper inventoryMapper;

    @Autowired
    private ProductsMapper productsMapper;

    @Autowired
    private WarehouseStorageCenterMapper warehouseStorageCenterMapper;

    @Autowired
    private StockAllocationCategoryConfigMapper categoryConfigMapper;

    @Autowired
    private SupplierMapper supplierMapper;
    @Resource
    private ProductWarehousePlannerHandler productWarehousePlannerHandler;

    @Override
    public AjaxResult pageQueryProductConfig(int pageIndex, int pageSize, ProductWarehouseConfigInput input) {
        List<ProductWarehouseConfigVO> configVOList = Lists.newArrayList();

        // 1.将sku转成spu,没找到返回空数据
        if (StringUtils.isNotBlank(input.getSkuNo())) {
            Inventory inventory = inventoryMapper.selectOneBySku(input.getSkuNo());
            if (Objects.isNull(inventory) || inventory.getPdId() == null) {
                return AjaxResult.getOK(PageInfoHelper.createPageInfo(configVOList));
            } else {
                input.setPdId(inventory.getPdId());
            }
        }
        List<ProductMissWareHouseDTO> productMissWareHouseDTOS = null;
        if (input.getConfigNotComplete() != null && input.getConfigNotComplete()) {
            productMissWareHouseDTOS = productWarehouseSupplierConfigMapper.selectMissWarehouse(null);
            List<Long> pdIds = productMissWareHouseDTOS.stream().map(ProductMissWareHouseDTO::getPdId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(pdIds)) {
                return AjaxResult.getOK(new PageInfo<>());
            }
            input.setPdIds(pdIds);
        } else if (input.getConfigNotComplete() != null && !input.getConfigNotComplete()) {
            productMissWareHouseDTOS = productWarehouseSupplierConfigMapper.selectNoMissWarehouse(null);
            List<Long> pdIds = productMissWareHouseDTOS.stream().map(ProductMissWareHouseDTO::getPdId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(pdIds)) {
                return AjaxResult.getOK(new PageInfo<>());
            }
            input.setPdIds(pdIds);
        }
        // 2.拿条件进行分页查询
        Page<Object> page = PageHelper.startPage(pageIndex, pageSize);
        List<ProductBaseInfoDTO> productBaseInfoDTOS = productWarehouseConfigMapper.queryProductBaseConfigs(input);
        if (CollectionUtil.isNotEmpty(productBaseInfoDTOS)) {
            // 品仓配置pdId列表
            List<Long> pdIds = productBaseInfoDTOS.stream()
                    .map(ProductBaseInfoDTO::getPdId)
                    .distinct()
                    .collect(Collectors.toList());
            // 3.补齐缺失仓及负责人信息
            // 3.1 根据pd_id查出未配置
            List<ProductAdminsDTO> productAdminsDTOList = productWarehouseConfigMapper.queryAdminNames(pdIds);
            Map<Long, ProductAdminsDTO> productAdminsMap = productAdminsDTOList.stream().collect(Collectors.toMap(ProductAdminsDTO::getPdId, Function.identity()));
            // 3.2 根据pd_id查出未配置仓
            Map<Long, Integer> missConfigMap = new HashedMap();
            List<ProductMissWareHouseDTO> missConfigInfo;
            if (input.getConfigNotComplete() != null) {
                missConfigInfo = productMissWareHouseDTOS.stream().filter(o -> pdIds.contains(o.getPdId())).collect(Collectors.toList());
            } else {
                missConfigInfo = productWarehouseSupplierConfigMapper.selectMissWarehouse(pdIds);
            }
            if (CollectionUtil.isNotEmpty(missConfigInfo)) {
                missConfigMap = missConfigInfo.stream().collect(Collectors.toMap(ProductMissWareHouseDTO::getPdId, ProductMissWareHouseDTO::getWaitConfigWarehouse,
                        (o, n) -> o));
            }
            final Map<Long, Integer> finalMissConfigMap = missConfigMap;
            // 4.封装数据返回
            configVOList = productBaseInfoDTOS.stream().map(
                    info -> {
                        ProductWarehouseConfigVO warehouseConfigVO = new ProductWarehouseConfigVO();
                        warehouseConfigVO.setPdId(info.getPdId());
                        warehouseConfigVO.setPdName(info.getPdName());
                        warehouseConfigVO.setPdNo(info.getPdNo());
                        warehouseConfigVO.setPicUrl(info.getPicturePath());
                        warehouseConfigVO.setWaitConfigWarehouse(finalMissConfigMap.getOrDefault(info.getPdId(), 0));
                        ProductAdminsDTO productAdminsDTO = productAdminsMap.get(info.getPdId());
                        warehouseConfigVO.setAdminName(StrUtil.isEmpty(productAdminsDTO.getAdminNames()) ? StrUtil.EMPTY : productAdminsDTO.getAdminNames());
                        // 计划员
                        warehouseConfigVO.setPlannerName(StrUtil.isEmpty(productAdminsDTO.getPlannerNames()) ? StrUtil.EMPTY : productAdminsDTO.getPlannerNames());
                        return warehouseConfigVO;
                    }
            ).collect(Collectors.toList());
        }
        PageInfo pageInfo = new PageInfo(page);
        pageInfo.setList(configVOList);
        return AjaxResult.getOK(pageInfo);
    }

    @Override
    public AjaxResult<List<ProductWarehouseDetailConfigVO>> listSpuConfigDetail(Long pdId) {
        List<ProductWarehouseDetailConfigVO> configVOList = Lists.newArrayList();
        // 1. 查询品仓负责人相关信息
        List<ProductWarehouseConfigDTO> pdWarehouseConfigDTOS = productWarehouseConfigMapper.queryProductWarehouseConfig(pdId);
        if (CollectionUtil.isEmpty(pdWarehouseConfigDTOS)) {
            return AjaxResult.getOK(configVOList);
        }
        // 2. 查询品仓供应商相关信息
        List<ProductWarehouseSupplierDTO> pdWarehouseSupplierDTOS = productWarehouseSupplierConfigMapper.queryProductWarehouseSupplierConfig(pdId);
        // 3. 查询供应商品仓补货配置信息
        Map<String, List<SupplierReplenishmentDTO>> replenishmentMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(pdWarehouseSupplierDTOS)) {
            SupplierReplenishmentQueryInput queryInput = new SupplierReplenishmentQueryInput();
            queryInput.setPdId(pdId);
            List<SupplierReplenishmentDTO> supplierReplenishmentDTOS = supplierReplenishmentConfigMapper.querySupplierReplenishmentConfigs(queryInput);
            if (CollectionUtil.isNotEmpty(pdWarehouseConfigDTOS)) {
                replenishmentMap = supplierReplenishmentDTOS.stream().collect(Collectors.groupingBy(item -> supplierReplenishmentKey(item.getPdId(), item.getWarehouseNo(), item.getSupplierId())));
            }
        }
        final Map<String, List<SupplierReplenishmentDTO>> finalReplenishmentMap = replenishmentMap;
        // 4. 封装数据返回
        Map<String, ProductWarehouseConfigDTO> configMap = pdWarehouseConfigDTOS.stream().collect(
                Collectors.toMap(config -> productWarehouseKey(config.getPdId(), config.getWarehouseNo()), Function.identity(), (o, n) -> o));
        // 增加使用过的品仓key,目的与品仓配置取差集,最后去补齐没有供应商配置,但有品仓配置的数据
        final Set<String> usedKey = Sets.newHashSet();
        configVOList = pdWarehouseSupplierDTOS.stream().map(
                supplierDTO -> {
                    ProductWarehouseConfigDTO config = configMap.get(productWarehouseKey(pdId, supplierDTO.getWarehouseNo()));
                    usedKey.add(productWarehouseKey(pdId, supplierDTO.getWarehouseNo()));
                    List<SupplierReplenishmentDTO> supplierReplenishmentConfig = finalReplenishmentMap.containsKey(supplierReplenishmentKey(pdId, supplierDTO.getWarehouseNo(), supplierDTO.getSupplierId()))
                            ? finalReplenishmentMap.get(supplierReplenishmentKey(pdId, supplierDTO.getWarehouseNo(), supplierDTO.getSupplierId())) : null;
                    ProductWarehouseDetailConfigVO detailConfigVO = new ProductWarehouseDetailConfigVO();
                    detailConfigVO.setConfigId(config.getId());
                    detailConfigVO.setPdId(config.getPdId());
                    detailConfigVO.setWarehouseNo(config.getWarehouseNo());
                    detailConfigVO.setWarehouseName(config.getWarehouseName());
                    detailConfigVO.setPurchaseType(config.getPurchaseType());
                    detailConfigVO.setAdminId(config.getAdminId());
                    detailConfigVO.setAdminName(config.getAdminName());
                    detailConfigVO.setPlannerId(config.getPlannerId());
                    detailConfigVO.setPlannerName(config.getPlannerName());
                    detailConfigVO.setSupplierId(supplierDTO.getSupplierId());
                    detailConfigVO.setSupplierName(supplierDTO.getSupplierName());
                    detailConfigVO.setPrimaryFlag(supplierDTO.getPrimaryFlag());
                    if (supplierReplenishmentConfig != null) {
                        List<SupplierReplenishmentVO> replenishmentVOS = supplierReplenishmentConfig.stream().map(
                                replenishment -> {
                                    SupplierReplenishmentVO replenishmentVO = new SupplierReplenishmentVO();
                                    replenishmentVO.setPdId(replenishment.getPdId());
                                    replenishmentVO.setWarehouseNo(replenishment.getWarehouseNo());
                                    replenishmentVO.setSupplierId(replenishment.getSupplierId());
                                    replenishmentVO.setReplenishmentMode(replenishment.getReplenishmentMode());
                                    replenishmentVO.setPreDay(replenishment.getPreDay());
                                    replenishmentVO.setBacklogDay(replenishment.getBacklogDay());
                                    replenishmentVO.setOrderDate(replenishment.getOrderDate());
                                    replenishmentVO.setSafeWaterLevel(replenishment.getSafeWaterLevel());
                                    return replenishmentVO;
                                }
                        ).collect(Collectors.toList());
                        detailConfigVO.setReplenishmentConfigs(replenishmentVOS);
                    }
                    return detailConfigVO;
                }
        ).collect(Collectors.toList());

        // 使用过的key如果数量小于配置中的数据行,说明有配置没有供应商信息,将差集内容,增加到结尾数据返回
        if (usedKey.size() < configMap.size()) {
            Sets.SetView<String> needAppendKey = Sets.difference(configMap.keySet(), usedKey);
            for (String key : needAppendKey) {
                ProductWarehouseConfigDTO config = configMap.get(key);
                ProductWarehouseDetailConfigVO detailConfigVO = new ProductWarehouseDetailConfigVO();
                detailConfigVO.setConfigId(config.getId());
                detailConfigVO.setPdId(config.getPdId());
                detailConfigVO.setWarehouseNo(config.getWarehouseNo());
                detailConfigVO.setWarehouseName(config.getWarehouseName());
                detailConfigVO.setPurchaseType(config.getPurchaseType());
                detailConfigVO.setAdminId(config.getAdminId());
                detailConfigVO.setAdminName(config.getAdminName());
                detailConfigVO.setPlannerId(config.getPlannerId());
                detailConfigVO.setPlannerName(config.getPlannerName());
                configVOList.add(detailConfigVO);
            }
        }

        return AjaxResult.getOK(configVOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult batchSaveConfig(ProductWarehouseSaveInput productWarehouseSaveInput) {
        Admin currentUser = this.getCurrentUser();
        Integer adminId = currentUser.getAdminId();
        LocalDateTime now = LocalDateTime.now();
        // 定义需批量删除的关键字段
        List<Integer> warehouseNos = Lists.newArrayList();
        Long pdId = productWarehouseSaveInput.getConfigList().get(0).getPdId();
        // 定义最终需批量存储的数据
        List<PurchaseProductWarehouseConfig> configs = Lists.newArrayList();
        List<PurchaseProductWarehouseSupplierConfig> supplierConfigs = Lists.newArrayList();
        List<PurchaseSupplierReplenishmentConfig> replenishmentConfigs = Lists.newArrayList();
        productWarehouseSaveInput.getConfigList().forEach(item -> {
            warehouseNos.add(item.getWarehouseNo());
            // 1.组装品仓配置信息
            PurchaseProductWarehouseConfig config = new PurchaseProductWarehouseConfig();
            config.setPdId(item.getPdId());
            config.setWarehouseNo(item.getWarehouseNo());
            config.setPurchaseType(item.getPurchaseType());
            config.setAdminId(item.getAdminId());
            config.setAdminName(item.getAdminName());
            config.setPlannerId(item.getPlannerId());
            config.setPlannerName(item.getPlannerName());
            config.setCreateTime(now);
            config.setUpdateTime(now);
            config.setCreator(adminId);
            config.setUpdater(adminId);
            configs.add(config);
            // 2.组装供应商信息
            item.getSupplierInfos().forEach(supplier -> {
                PurchaseProductWarehouseSupplierConfig supplierConfig = new PurchaseProductWarehouseSupplierConfig();
                supplierConfig.setPdId(item.getPdId());
                supplierConfig.setWarehouseNo(item.getWarehouseNo());
                supplierConfig.setSupplierId(supplier.getSupplierId());
                supplierConfig.setCompleteFlag(Global.TRUE_FLAG);
                supplierConfig.setPrimaryFlag(supplier.getPrimaryFlag());
                supplierConfig.setCreateTime(now);
                supplierConfig.setUpdateTime(now);
                supplierConfig.setCreator(adminId);
                supplierConfig.setUpdater(adminId);
                supplierConfigs.add(supplierConfig);

                // 3.组装补货信息
                supplier.getReplenishmentConfigs().forEach(replenishment -> {
                    PurchaseSupplierReplenishmentConfig replenishmentConfig = new PurchaseSupplierReplenishmentConfig();
                    replenishmentConfig.setPdId(item.getPdId());
                    replenishmentConfig.setWarehouseNo(item.getWarehouseNo());
                    replenishmentConfig.setSupplierId(supplier.getSupplierId());
                    replenishmentConfig.setReplenishmentMode(replenishment.getReplenishmentMode());
                    replenishmentConfig.setPreDay(replenishment.getPreDay());
                    replenishmentConfig.setBacklogDay(replenishment.getBacklogDay());
                    replenishmentConfig.setOrderDate(replenishment.getOrderDate());
                    replenishmentConfig.setSafeWaterLevel(replenishment.getSafeWaterLevel());
                    replenishmentConfig.setCreateTime(now);
                    replenishmentConfig.setUpdateTime(now);
                    replenishmentConfig.setCreator(adminId);
                    replenishmentConfig.setUpdater(adminId);
                    replenishmentConfigs.add(replenishmentConfig);
                });
            });
        });
        // 4.根据pid及warehouseNo删除原有数据
        productWarehouseConfigMapper.deleteByPdIdAndWarehouseNo(pdId, warehouseNos);
        productWarehouseSupplierConfigMapper.deleteByPdIdAndWarehouseNo(pdId, warehouseNos);
        supplierReplenishmentConfigMapper.deleteByPdIdAndWarehouseNo(pdId, warehouseNos);
        // 5.批量插入
        if (CollectionUtil.isNotEmpty(configs)) {
            productWarehouseConfigMapper.insertBatch(configs);
        }
        if (CollectionUtil.isNotEmpty(supplierConfigs)) {
            productWarehouseSupplierConfigMapper.insertBatch(supplierConfigs);
        }
        if (CollectionUtil.isNotEmpty(replenishmentConfigs)) {
            supplierReplenishmentConfigMapper.insertBatch(replenishmentConfigs);
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean dealWarehouseChangeTask() {
        Boolean result = false;
        LocalDateTime now = LocalDateTime.now();
        // 1.查出更新时间为当前时间到前一天的库存仓, 分类为开放及未关闭两类
        WarehouseStorageCenterInput input = new WarehouseStorageCenterInput();
//        input.setUpdateStartTime(now.minusDays(1));
//        input.setUpdateEndTime(now);
        input.setStatus(WarehouseStorageCenterEnum.Status.VALID.ordinal());
        input.setType(AreaTypeEnum.INTERNAL_AREA.getType());
        List<WarehouseStorageCenter> warehouseStorageCenters = warehouseStorageCenterMapper.selectWarehouseInfosForSummerfarm(input);
        if (CollectionUtil.isEmpty(warehouseStorageCenters)) {
            return result;
        }
        Set<Integer> validWarehouseSet = warehouseStorageCenters.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toSet());
//        Set<Integer> validWarehouseSet = warehouseStorageCenters.stream().filter(warehouse -> warehouse.getStatus().equals(WarehouseStorageCenterEnum.Status.VALID.ordinal())).map(WarehouseStorageCenter :: getWarehouseNo).collect(Collectors.toSet());
//        Set<Integer> inValidWarehouseSet = warehouseStorageCenters.stream().filter(warehouse -> warehouse.getStatus().equals(WarehouseStorageCenterEnum.Status.IN_VALID.ordinal())).map(WarehouseStorageCenter :: getWarehouseNo).collect(Collectors.toSet());
        // 2.查出配置中存在的库存仓
        List<Integer> configWarehouseNos = productWarehouseConfigMapper.groupWarehouseNo();
        Set<Integer> configWarehouseSet = CollectionUtil.isEmpty(configWarehouseNos) ? Sets.newHashSet() : configWarehouseNos.stream().collect(Collectors.toSet());
        // 3.已有配置库存仓与开放库存仓取差集为需要新增的库存仓配置
        Sets.SetView<Integer> needInsertWarehouseSet = Sets.difference(validWarehouseSet, configWarehouseSet);
        if (needInsertWarehouseSet.size() > 0) {
            result = true;
        }
        // 3.1 已有配置库存仓中取一个仓作为条件
        if (CollectionUtil.isNotEmpty(configWarehouseNos)) {
            Integer copyWarehouseNo = configWarehouseNos.get(0);
            // 3.2 循环需要新增的库存仓配置
            for (Integer warehouseNo : needInsertWarehouseSet) {
                // 循环查出数据后为配置赋新值最后批量入库
                batchOperateProductWarehouseConfig(copyWarehouseNo, warehouseNo);
                batchOperateProductWarehouseSupplierConfig(copyWarehouseNo, warehouseNo);
                batchOperateSupplierReplenishmentConfig(copyWarehouseNo, warehouseNo);
            }
        }
        // 4.未开放库存仓与已配置库存仓取交集为需要删除的库存仓配置
//        Sets.SetView<Integer> needDeleteWarehouseSet = Sets.intersection(inValidWarehouseSet, configWarehouseSet);
        // 4.1删除三张配置表信息
//        if (CollectionUtil.isNotEmpty(needDeleteWarehouseSet)) {
//            productWarehouseConfigMapper.deleteByWarehouseNos(Lists.newArrayList(needDeleteWarehouseSet));
//            productWarehouseSupplierConfigMapper.deleteByWarehouseNos(Lists.newArrayList(needDeleteWarehouseSet));
//            supplierReplenishmentConfigMapper.deleteByWarehouseNos(Lists.newArrayList(needDeleteWarehouseSet));
//        }
        return result;
    }


    @Override
    @Async
    public void initWaterLevelAndBacklogDay() {
        Integer pageIndex = 1;
        Integer pageSize = 100;
        boolean hasNextPage;
        do {
            Page<Object> page = PageHelper.startPage(pageIndex, pageSize);
            ProductWarehouseSupplierQueryInput productWarehouseSupplierQueryInput = new ProductWarehouseSupplierQueryInput();
            productWarehouseSupplierQueryInput.setPrimaryFlag(Global.TRUE_FLAG);
            List<PurchaseProductWarehouseSupplierConfig> purchaseProductWarehouseSupplierConfigs = productWarehouseSupplierConfigMapper.selectByQueryInput(productWarehouseSupplierQueryInput);
            if (CollectionUtil.isNotEmpty(purchaseProductWarehouseSupplierConfigs)) {
                purchaseProductWarehouseSupplierConfigs.forEach(o -> {
                    ProductWarehouseQueryInput productWarehouseQueryInput = new ProductWarehouseQueryInput();
                    productWarehouseQueryInput.setWarehouseNo(o.getWarehouseNo());
                    productWarehouseQueryInput.setPdId(o.getPdId());
                    List<PurchaseProductWarehouseConfig> purchaseProductWarehouseConfigs = productWarehouseConfigMapper.selectByQueryInput(productWarehouseQueryInput);
                    for (PurchaseProductWarehouseConfig purchaseProductWarehouseConfig : purchaseProductWarehouseConfigs) {
                        if (!CollectionUtil.isEmpty(purchaseProductWarehouseSupplierConfigs)) {
                            PurchaseProductWarehouseSupplierConfig purchaseProductWarehouseSupplierConfig = purchaseProductWarehouseSupplierConfigs.stream().filter(purchaseProductWarehouse -> purchaseProductWarehouse.getPdId().equals(o.getPdId()) && purchaseProductWarehouse.getWarehouseNo().equals(o.getWarehouseNo())).findFirst().get();
                            SupplierReplenishmentQueryInput supplierReplenishmentQueryInput = new SupplierReplenishmentQueryInput();
                            supplierReplenishmentQueryInput.setSupplierId(purchaseProductWarehouseSupplierConfig.getSupplierId());
                            supplierReplenishmentQueryInput.setWarehouseNo(o.getWarehouseNo());
                            supplierReplenishmentQueryInput.setPdId(o.getPdId());
                            List<SupplierReplenishmentDTO> supplierReplenishmentDTOS = supplierReplenishmentConfigMapper.querySupplierReplenishmentConfigs(supplierReplenishmentQueryInput);
                            if (CollectionUtil.isNotEmpty(supplierReplenishmentDTOS)) {
                                SupplierReplenishmentDTO supplierReplenishmentDTO1 = supplierReplenishmentDTOS.get(0);
                                if (supplierReplenishmentDTO1.getReplenishmentMode().equals(SupplierReplenishmentConfigEnums.ReplenishmentMode.REGULAR_QUANTITATIVE.getValue())
                                        || supplierReplenishmentDTO1.getReplenishmentMode().equals(SupplierReplenishmentConfigEnums.ReplenishmentMode.REGULAR_INDEFINITE.getValue())) {
                                    Optional<SupplierReplenishmentDTO> supplierReplenishmentOptional = supplierReplenishmentDTOS.stream().filter(p -> p.getPreDay() != null).max(Comparator.comparingInt(SupplierReplenishmentDTO::getPreDay));
                                    if (supplierReplenishmentOptional.isPresent()) {
                                        SupplierReplenishmentDTO supplierReplenishmentDTO = supplierReplenishmentOptional.get();
                                        purchaseProductWarehouseConfig.setSafeWaterLevel(supplierReplenishmentDTO.getSafeWaterLevel());
                                        purchaseProductWarehouseConfig.setBacklogDay(supplierReplenishmentDTO.getPreDay());
                                        productWarehouseConfigMapper.updateByPrimaryKeySelective(purchaseProductWarehouseConfig);
                                    }
                                } else {
                                    purchaseProductWarehouseConfig.setSafeWaterLevel(supplierReplenishmentDTO1.getSafeWaterLevel());
                                    purchaseProductWarehouseConfig.setBacklogDay(supplierReplenishmentDTO1.getBacklogDay());
                                    productWarehouseConfigMapper.updateByPrimaryKeySelective(purchaseProductWarehouseConfig);
                                }
                            }
                        }
                    }
                });
            }
            PageInfo pageInfo = new PageInfo(page);
            hasNextPage = pageInfo.isHasNextPage();
            pageIndex++;
        }
        while (hasNextPage);
        log.info("处理完毕");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer manualAddProductConfig(Long pdId, Integer warehouseNo) {
        Integer result = 0;
        LocalDateTime now = LocalDateTime.now();
        // 1.最多需要新增的库存仓
        Set<Integer> warehouseNos = Sets.newHashSet();
        if (warehouseNo == null) {
            WarehouseStorageCenterInput input = new WarehouseStorageCenterInput();
            input.setStatus(WarehouseLogisticsCenterStatus.VALID.ordinal());
            List<WarehouseStorageCenter> warehouseStorageCenterVOS = warehouseStorageCenterMapper.selectWarehouseInfosForSummerfarm(input);
            if (CollectionUtil.isNotEmpty(warehouseStorageCenterVOS)) {
                warehouseNos = warehouseStorageCenterVOS.stream().map(WarehouseStorageCenter::getWarehouseNo).collect(Collectors.toList()).stream().collect(Collectors.toSet());
            }
        } else {
            warehouseNos.add(warehouseNo);
        }
        // 2.已有库存仓配置的数据
        Set<Integer> existWareHouse = Sets.newHashSet();
        List<Integer> existWareHouseList = productWarehouseConfigMapper.selectExistWareHouse(Lists.newArrayList(warehouseNos), pdId);
        if (CollectionUtil.isNotEmpty(existWareHouseList)) {
            existWareHouse = existWareHouseList.stream().collect(Collectors.toSet());
        }

        // 3.取差集去作为最终需要新增的库存仓
        List<Integer> needInsertWarehouse;
        if (existWareHouse.size() == 0) {
            needInsertWarehouse = Lists.newArrayList(warehouseNos);
        } else {
            needInsertWarehouse = Lists.newArrayList(Sets.difference(warehouseNos, existWareHouse));
        }
        // 4.组装新增数据
        ProductsVO productsVO = productsMapper.selectByPdId(pdId);
        // feat(自动生成补货计划)：处理品仓配置计划员
        Integer purchaserId = productsVO.getCreator();
        ProductWarehousePlannerHandler.Planner planner = productWarehousePlannerHandler.getPlannerByPurchaserId(purchaserId);
        List<PurchaseProductWarehouseConfig> configs = Lists.newArrayList();
        for (Integer warehouse : needInsertWarehouse) {
            PurchaseProductWarehouseConfig config = new PurchaseProductWarehouseConfig();
            config.setPdId(productsVO.getPdId());
            config.setWarehouseNo(warehouse);
            config.setPurchaseType(ProductWarehouseConfigEnums.PurchaseType.DIRECT_PURCHASE.getValue());
            config.setAdminId(productsVO.getCreator());
            config.setCreateTime(now);
            config.setUpdater(getAdminId());
            config.setUpdateTime(now);
            config.setAdminName(productsVO.getRealName());
            config.setCreator(getAdminId());
            if (Objects.nonNull(planner)) {
                config.setPlannerId(planner.getPlannerId());
                config.setPlannerName(planner.getPlannerName());
            }
            configs.add(config);
        }
        if (CollectionUtil.isNotEmpty(configs)) {
            result = productWarehouseConfigMapper.insertBatch(configs);
        }
        return result;
    }

    @Override
    public AjaxResult<List<SupplierBaseInfoDTO>> queryEnableSupplier(Long pdId, Integer warehouseNo) {
        List<SupplierBaseInfoDTO> supplierBaseInfoDTOS = Lists.newArrayList();
        ProductWarehouseSupplierQueryInput queryInput = new ProductWarehouseSupplierQueryInput();
        queryInput.setCompleteFlag(Global.TRUE_FLAG);
        queryInput.setPdId(pdId);
        queryInput.setWarehouseNo(warehouseNo);
        List<Integer> supplierIds = productWarehouseSupplierConfigMapper.selectByQueryInput(queryInput).stream().map(PurchaseProductWarehouseSupplierConfig::getSupplierId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(supplierIds)) {
            SupplierQueryInput input = new SupplierQueryInput();
            input.setIdList(supplierIds);
            supplierBaseInfoDTOS = supplierMapper.selectSupplierBaseInfoByInput(input);
        }
        return AjaxResult.getOK(supplierBaseInfoDTOS);
    }

    @Override
    public PurchaselatestArrivalDateDTO getAdvanceDayBySkuWarehouseAndSupplier(String sku, Integer warehouseNo, Integer supplierId) {
        PurchaselatestArrivalDateDTO purchaselatestArrivalDateDTO = new PurchaselatestArrivalDateDTO();
        Inventory inventory = inventoryMapper.selectOneBySku(sku);
        if (inventory == null) {
            throw new BizException("无法查找到该sku");
        }
        Long pdId = inventory.getPdId();
        PurchaseSupplierReplenishmentConfig purchaseSupplierReplenishmentConfig = supplierReplenishmentConfigMapper.getMaxPreDay(pdId, warehouseNo, supplierId);
        if (purchaseSupplierReplenishmentConfig == null || purchaseSupplierReplenishmentConfig.getPreDay() == null) {
            purchaselatestArrivalDateDTO.setCompleteAdvanceTime(false);
            purchaselatestArrivalDateDTO.setLatestArrivalDate(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
            return purchaselatestArrivalDateDTO;
        }
        LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).plusDays(purchaseSupplierReplenishmentConfig.getPreDay());
        purchaselatestArrivalDateDTO.setCompleteAdvanceTime(true);
        purchaselatestArrivalDateDTO.setLatestArrivalDate(localDateTime);
        return purchaselatestArrivalDateDTO;
    }

    @Override
    public void productWarehouseConfigUpdate(ProductWarehouseConfigUpsert productWarehouseConfigUpsert) {
        ProductWarehouseConfigUpdate productWarehouseConfigUpdate = new ProductWarehouseConfigUpdate();
        productWarehouseConfigUpdate.setAdminId(productWarehouseConfigUpsert.getPurchaserId());
        productWarehouseConfigUpdate.setAdminName(productWarehouseConfigUpsert.getPurchaserName());
        productWarehouseConfigUpdate.setPlannerName(productWarehouseConfigUpsert.getPlannerName());
        productWarehouseConfigUpdate.setPlannerId(productWarehouseConfigUpsert.getPlannerId());
        productWarehouseConfigUpdate.setUpdater(productWarehouseConfigUpsert.getOperatorId());
        List<Integer> warehouseNos = null;
        if (CollectionUtil.isNotEmpty(productWarehouseConfigUpsert.getWarehouseDTOList())) {
            warehouseNos = productWarehouseConfigUpsert.getWarehouseDTOList().stream().map(WarehouseDTO::getWarehouseNo).collect(Collectors.toList());
        }
        productWarehouseConfigMapper.updateByPdIdsAndWarehouseNos(productWarehouseConfigUpdate, productWarehouseConfigUpsert.getPdId(), warehouseNos);

    }

    private Boolean batchOperateProductWarehouseConfig(Integer copyWarehouseNo, Integer warehouseNo) {
        Boolean result = false;
        LocalDateTime now = LocalDateTime.now();
        Integer pageReturnSize;
        Integer startNum = 0;
        Integer pageSize = 100;
        Integer i = 0;
        do {
            List<PurchaseProductWarehouseConfig> configList = productWarehouseConfigMapper.selectConfigByWarehouseLimit(copyWarehouseNo, startNum, pageSize);
            if (CollectionUtil.isNotEmpty(configList)) {
                for (PurchaseProductWarehouseConfig config : configList) {
                    config.setId(null);
                    config.setWarehouseNo(warehouseNo);
                    config.setCreateTime(now);
                    config.setUpdater(Global.SYSTEM_ID);
                    config.setUpdateTime(now);
                    config.setCreator(Global.SYSTEM_ID);
                }
                i += productWarehouseConfigMapper.insertBatch(configList);

            }
            startNum = startNum + pageSize;
            pageReturnSize = configList.size();
        } while (pageReturnSize.equals(pageSize));
        return i > 0 ? true : result;
    }

    private Boolean batchOperateProductWarehouseSupplierConfig(Integer copyWarehouseNo, Integer warehouseNo) {
        Boolean result = false;
        LocalDateTime now = LocalDateTime.now();
        Integer pageReturnSize;
        Integer startNum = 0;
        Integer pageSize = 100;
        Integer i = 0;
        do {
            List<PurchaseProductWarehouseSupplierConfig> configList = productWarehouseSupplierConfigMapper.selectConfigByWarehouseLimit(copyWarehouseNo, startNum, pageSize);
            if (CollectionUtil.isNotEmpty(configList)) {
                for (PurchaseProductWarehouseSupplierConfig config : configList) {
                    config.setId(null);
                    config.setWarehouseNo(warehouseNo);
                    config.setCreateTime(now);
                    config.setUpdater(Global.SYSTEM_ID);
                    config.setUpdateTime(now);
                    config.setCreator(Global.SYSTEM_ID);
                }
                i += productWarehouseSupplierConfigMapper.insertBatch(configList);
            }
            startNum = startNum + pageSize;
            pageReturnSize = configList.size();
        } while (pageReturnSize.equals(pageSize));
        return i > 0 ? true : result;
    }

    private Boolean batchOperateSupplierReplenishmentConfig(Integer copyWarehouseNo, Integer warehouseNo) {
        Boolean result = false;
        LocalDateTime now = LocalDateTime.now();
        Integer pageReturnSize;
        Integer startNum = 0;
        Integer pageSize = 100;
        Integer i = 0;
        do {
            List<PurchaseSupplierReplenishmentConfig> configList = supplierReplenishmentConfigMapper.selectConfigByWarehouseLimit(copyWarehouseNo, startNum, pageSize);
            if (CollectionUtil.isNotEmpty(configList)) {
                for (PurchaseSupplierReplenishmentConfig config : configList) {
                    config.setId(null);
                    config.setWarehouseNo(warehouseNo);
                    config.setCreateTime(now);
                    config.setUpdater(Global.SYSTEM_ID);
                    config.setUpdateTime(now);
                    config.setCreator(Global.SYSTEM_ID);
                }
                i += supplierReplenishmentConfigMapper.insertBatch(configList);
            }
            startNum = startNum + pageSize;
            pageReturnSize = configList.size();
        } while (pageReturnSize.equals(pageSize));
        return i > 0 ? true : result;
    }

    private String productWarehouseKey(Long pdId, Integer warehouseNo) {
        return pdId + "-" + warehouseNo;
    }

    private String supplierReplenishmentKey(Long pdId, Integer warehouseNo, Integer supplierId) {
        return pdId + "-" + warehouseNo + "-" + supplierId;
    }

}
