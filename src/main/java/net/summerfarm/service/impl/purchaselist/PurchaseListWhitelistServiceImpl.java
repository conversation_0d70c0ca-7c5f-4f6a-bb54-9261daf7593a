package net.summerfarm.service.impl.purchaselist;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.mapper.manage.MerchantPoolInfoMapper;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.MerchantPoolInfo;
import net.summerfarm.model.input.PurchaseListWhitelistInput;
import net.summerfarm.model.vo.purchaselist.PurchaseListWhitelistVO;
import net.summerfarm.service.purchaselist.PurchaseListWhitelistService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PurchaseListWhitelistServiceImpl implements PurchaseListWhitelistService {

    private final static String PURCHASE_LIST_WHITELIST_CONFIG_CODE = "PURCHASE_LIST_WHITELIST";

    @Resource
    private ConfigMapper configMapper;
    @Resource
    private MerchantPoolInfoMapper merchantPoolInfoMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editPurchaseListWhitelist(PurchaseListWhitelistInput purchaseListWhitelistInput) {
        configMapper.updateValue(PURCHASE_LIST_WHITELIST_CONFIG_CODE, String.valueOf(purchaseListWhitelistInput.getGroupId()));
    }

    @Override
    public PurchaseListWhitelistVO getWhitelistGroup() {
        Config config = configMapper.selectOne(PURCHASE_LIST_WHITELIST_CONFIG_CODE);
        Long groupId = Long.valueOf(config.getValue());
        MerchantPoolInfo merchantPoolInfo = merchantPoolInfoMapper.selectById(groupId);
        PurchaseListWhitelistVO vo = new PurchaseListWhitelistVO();
        if (merchantPoolInfo != null) {
            vo.setGroupId(groupId);
            vo.setName(merchantPoolInfo.getName());
        }
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean delWhitelistGroup(Long groupId) {
        configMapper.updateValue(PURCHASE_LIST_WHITELIST_CONFIG_CODE, String.valueOf(0L));
        return true;
    }
}
