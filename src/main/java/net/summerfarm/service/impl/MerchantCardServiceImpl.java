package net.summerfarm.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.vo.MerchantCardRecordVO;
import net.summerfarm.model.vo.MerchantCardVO;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.mapper.manage.CardMapper;
import net.summerfarm.mapper.manage.CardRuleMapper;
import net.summerfarm.mapper.manage.MerchantCardMapper;
import net.summerfarm.mapper.manage.MerchantCardRecordMapper;
import net.summerfarm.model.domain.Card;
import net.summerfarm.model.domain.CardRule;
import net.summerfarm.model.domain.MerchantCard;
import net.summerfarm.model.domain.MerchantCardRecord;
import net.summerfarm.service.MerchantCardService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class MerchantCardServiceImpl implements MerchantCardService {

    @Resource
    private MerchantCardMapper merchantCardMapper;

    @Resource
    private CardMapper cardMapper;

    @Resource
    private CardRuleMapper cardRuleMapper;

    @Resource
    private MerchantCardRecordMapper merchantCardRecordMapper;

    @Override
    public AjaxResult select(int pageIndex, int pageSize, MerchantCardVO merchantCardVO) {
        PageHelper.startPage(pageIndex,pageSize);
        List<MerchantCardVO> merchantCardVOS = merchantCardMapper.selectVO(merchantCardVO);
        return AjaxResult.getOK(new PageInfo<>(merchantCardVOS));
    }

    @Override
    public AjaxResult selectDetail(Integer id) {
        JSONObject result = new JSONObject();
        MerchantCard merchantCard = merchantCardMapper.selectByPrimaryKey(id);
        result.put("merchantCard",merchantCard);

        Card card = cardMapper.selectByPrimaryKey(merchantCard.getCardId());
        result.put("card",card);

        CardRule cardRule = cardRuleMapper.selectByPrimaryKey(merchantCard.getCardRuleId());
        result.put("cardRule",cardRule);

        MerchantCardRecord select = new MerchantCardRecord();
        select.setMerchantCardId(id);
        List<MerchantCardRecordVO> merchantCardRecordVOS = merchantCardRecordMapper.selectVO(select);
        result.put("merchantCardRecordVOS",merchantCardRecordVOS);

        return AjaxResult.getOK(result);
    }


}
