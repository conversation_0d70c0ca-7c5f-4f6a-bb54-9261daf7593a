package net.summerfarm.service.impl;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.CommonStatus;
import net.summerfarm.enums.LuckDrawStatusEnum;
import net.summerfarm.enums.LuckDrawTypeEnum;
import net.summerfarm.facade.marketing.CmsFacade;
import net.summerfarm.facade.marketing.dto.CmsPageInfoDTO;
import net.summerfarm.mapper.manage.LandPageMapper;
import net.summerfarm.mapper.manage.LuckyDrawActivityEquityPackageMapper;
import net.summerfarm.mapper.manage.LuckyDrawActivityMapper;
import net.summerfarm.mapper.manage.LuckyDrawActivityPrizeMapper;
import net.summerfarm.model.domain.Coupon;
import net.summerfarm.model.domain.LandPage;
import net.summerfarm.model.domain.LuckyDrawActivity;
import net.summerfarm.model.domain.LuckyDrawActivityEquityPackage;
import net.summerfarm.model.domain.LuckyDrawActivityPrize;
import net.summerfarm.model.input.LuckDrawActivityBasicInsertReq;
import net.summerfarm.model.input.LuckDrawActivityBasicUpdateReq;
import net.summerfarm.model.input.LuckDrawActivityPageQuery;
import net.summerfarm.model.input.LuckDrawActivityReq;
import net.summerfarm.model.input.LuckyDrawActivityEquityPackageInsertReq;
import net.summerfarm.model.input.LuckyDrawActivityEquityPackageUpdateReq;
import net.summerfarm.model.vo.LandPageVO;
import net.summerfarm.model.vo.LuckDrawActivityVO;
import net.summerfarm.model.vo.LuckyDrawActivityEquityPackageVO;
import net.summerfarm.model.vo.LuckyDrawActivityPrizeVO;
import net.summerfarm.service.CouponService;
import net.summerfarm.service.LuckDrawActivityService;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 红包雨/每日抽奖活动
 * @date 2023/5/12 16:07:30
 */
@Service
@Slf4j
public class LuckDrawActivityServiceImpl extends BaseService implements LuckDrawActivityService {

    @Resource
    private LuckyDrawActivityMapper luckyDrawActivityMapper;

    @Resource
    private LuckyDrawActivityEquityPackageMapper luckyDrawActivityEquityPackageMapper;

    @Resource
    private LuckyDrawActivityPrizeMapper luckyDrawActivityPrizeMapper;

    @Resource
    private CouponService couponService;

    @Resource
    private LandPageMapper landPageMapper;

    @Resource
    private CmsFacade cmsFacade;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public LuckDrawActivityVO insertBasic(LuckDrawActivityBasicInsertReq luckDrawActivityInsertReq) {
        //校验入参
        checkInsert(luckDrawActivityInsertReq);

        //转成实体类
        LuckyDrawActivity newLuckyDrawActivity = extracted(luckDrawActivityInsertReq);

        //校验时间是否重叠
        checkTimeOverlap(newLuckyDrawActivity);

        //新增活动基本信息
        luckyDrawActivityMapper.insertSelective(newLuckyDrawActivity);

        //获取新增奖项信息
        Long activityId = newLuckyDrawActivity.getId();
        List<LuckyDrawActivityEquityPackageInsertReq> packageInsertReq = luckDrawActivityInsertReq.getPackageInsertReq();
        LuckyDrawActivityEquityPackage luckyDrawActivityEquityPackage;
        List<LuckyDrawActivityPrize> activityPrizeList;
        LuckyDrawActivityPrize luckyDrawActivityPrize;
        List<LuckyDrawActivityEquityPackageVO> equityPackageVOs = new ArrayList<>(packageInsertReq.size());
        LuckyDrawActivityEquityPackageVO luckyDrawActivityEquityPackageVO;

        //校验概率是否超过100%
        BigDecimal sumProbability = packageInsertReq.stream().map(LuckyDrawActivityEquityPackageInsertReq::getProbability)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (sumProbability.compareTo(new BigDecimal(100.0000)) > 0) {
            throw new BizException("新增奖项失败，奖项概率总和不能超过100%！");
        }

        //新增奖项信息和具体奖项信息
        for (LuckyDrawActivityEquityPackageInsertReq insertReq : packageInsertReq) {
            luckyDrawActivityEquityPackage = new LuckyDrawActivityEquityPackage();
            luckyDrawActivityEquityPackageVO = new LuckyDrawActivityEquityPackageVO();
            luckyDrawActivityEquityPackage.setActivityId(activityId);
            luckyDrawActivityEquityPackage.setName(insertReq.getName());

            //将概率转换成四位小数
            luckyDrawActivityEquityPackage.setProbability(insertReq.getProbability().multiply(new BigDecimal(0.01))
                    .setScale(4, BigDecimal.ROUND_HALF_UP));
            luckyDrawActivityEquityPackage.setQuantity(insertReq.getQuantity());
            luckyDrawActivityEquityPackage.setSurplusQuantity(insertReq.getQuantity());
            luckyDrawActivityEquityPackage.setCreateTime(LocalDateTime.now());
            luckyDrawActivityEquityPackageMapper.insertSelective(luckyDrawActivityEquityPackage);
            activityPrizeList = new ArrayList<>(insertReq.getCouponId().size());
            for (Long couponId : insertReq.getCouponId()) {
                luckyDrawActivityPrize = new LuckyDrawActivityPrize();
                luckyDrawActivityPrize.setActivityId(activityId);
                luckyDrawActivityPrize.setEquityPackageId(luckyDrawActivityEquityPackage.getId());
                luckyDrawActivityPrize.setCouponId(couponId);
                luckyDrawActivityPrize.setCreateTime(LocalDateTime.now());
                activityPrizeList.add(luckyDrawActivityPrize);
            }
            luckyDrawActivityPrizeMapper.batchInsert(activityPrizeList);

            //设置返回信息
            luckyDrawActivityEquityPackageVO.setId(luckyDrawActivityEquityPackage.getId());
            equityPackageVOs.add(luckyDrawActivityEquityPackageVO);
        }
        LuckDrawActivityVO luckDrawActivityVO = new LuckDrawActivityVO();
        luckDrawActivityVO.setEquityPackageVOs(equityPackageVOs);
        luckDrawActivityVO.setId(newLuckyDrawActivity.getId());
        return luckDrawActivityVO;
    }

    @Override
    public LuckDrawActivityVO detail(LuckDrawActivityReq luckDrawActivityReq) {
        Long id = luckDrawActivityReq.getId();

        //查询活动信息是否存在
        LuckyDrawActivity luckyDrawActivity = getInfo(id);
        if (Objects.isNull(luckyDrawActivity)) {
            throw new BizException("查询失败，该活动信息不存在！");
        }
        LuckDrawActivityVO luckDrawActivityVO = new LuckDrawActivityVO();

        //组装活动基本信息
        conversionToVO(luckyDrawActivity, luckDrawActivityVO);

        //落地页名称返回
        if (StringUtils.isNotBlank(luckyDrawActivity.getRule())) {
            CmsPageInfoDTO pageInfoDTO = cmsFacade.getBasicInfoById(
                    Long.valueOf(luckyDrawActivity.getRule()));
            luckDrawActivityVO.setLandPageName(Objects.nonNull(pageInfoDTO) ? pageInfoDTO.getName() : null);
        }

        //组装奖项信息
        List<LuckyDrawActivityEquityPackage> luckyDrawActivityEquityPackages = luckyDrawActivityEquityPackageMapper.selectByActivityId(id);
        if (CollectionUtils.isEmpty(luckyDrawActivityEquityPackages)) {
            return luckDrawActivityVO;
        }
        List<LuckyDrawActivityEquityPackageVO> luckyDrawActivityEquityPackageVOS = new ArrayList<>(luckyDrawActivityEquityPackages.size());
        LuckyDrawActivityEquityPackageVO luckyDrawActivityEquityPackageVO;
        for (LuckyDrawActivityEquityPackage luckyDrawActivityEquityPackage : luckyDrawActivityEquityPackages) {
            luckyDrawActivityEquityPackageVO = new LuckyDrawActivityEquityPackageVO();
            luckyDrawActivityEquityPackageVO.setActivityId(luckyDrawActivityEquityPackage.getActivityId());
            luckyDrawActivityEquityPackageVO.setId(luckyDrawActivityEquityPackage.getId());
            luckyDrawActivityEquityPackageVO.setName(luckyDrawActivityEquityPackage.getName());
            luckyDrawActivityEquityPackageVO.setProbability(luckyDrawActivityEquityPackage.getProbability().multiply(new BigDecimal(100)));
            luckyDrawActivityEquityPackageVO.setQuantity(luckyDrawActivityEquityPackage.getQuantity());
            luckyDrawActivityEquityPackageVO.setSurplusQuantity(luckyDrawActivityEquityPackage.getSurplusQuantity());

            //获取具体卡劵信息
            List<LuckyDrawActivityPrize> activityPrizeList = luckyDrawActivityPrizeMapper.selectByEquityPackageId(luckyDrawActivityEquityPackage.getId());
            if (CollectionUtils.isEmpty(activityPrizeList)) {
                continue;
            }
            List<LuckyDrawActivityPrizeVO> luckyDrawActivityPrizeVOS = new ArrayList<>(activityPrizeList.size());
            activityPrizeList.stream().forEach(e -> {
                LuckyDrawActivityPrizeVO luckyDrawActivityPrizeVO = new LuckyDrawActivityPrizeVO();
                luckyDrawActivityPrizeVO.setId(e.getId());
                luckyDrawActivityPrizeVO.setActivityId(e.getActivityId());
                luckyDrawActivityPrizeVO.setCouponId(e.getCouponId());
                luckyDrawActivityPrizeVO.setEquityPackageId(e.getEquityPackageId());

                //查询卡劵信息
                if (Objects.nonNull(e.getCouponId())) {
                    AjaxResult ajaxResult = couponService.selectOne(e.getCouponId().intValue());
                    luckyDrawActivityPrizeVO.setCoupon((Coupon) ajaxResult.getData());
                }
                luckyDrawActivityPrizeVOS.add(luckyDrawActivityPrizeVO);
            });
            luckyDrawActivityEquityPackageVO.setActivityPrizeVO(luckyDrawActivityPrizeVOS);
            luckyDrawActivityEquityPackageVOS.add(luckyDrawActivityEquityPackageVO);
        }
        luckDrawActivityVO.setEquityPackageVOs(luckyDrawActivityEquityPackageVOS);
        return luckDrawActivityVO;
    }

    @Override
    public PageInfo<LuckDrawActivityVO> page(LuckDrawActivityPageQuery luckDrawActivityPageQuery) {
        PageInfo<LuckDrawActivityVO> pageInfo = PageInfoHelper.createPageInfo(
                luckDrawActivityPageQuery.getPageIndex(), luckDrawActivityPageQuery.getPageSize(), () -> {
                    List<LuckDrawActivityVO> luckDrawActivityVOS = luckyDrawActivityMapper.getPage(luckDrawActivityPageQuery);
                    luckDrawActivityVOS.stream().forEach(e -> {
                        e.setTypeName(LuckDrawTypeEnum.getValueByKey(e.getType()));
                        LocalDateTime now = LocalDateTime.now();

                        //开始和预热时间一样 说明预热时间为空
                        if(Objects.equals(e.getStartTime(), e.getPreheatTime())
                                || e.getPreheatTime().isAfter(e.getStartTime())) {
                            e.setPreheatTime(null);
                        }

                        //返回活动状态 1-未开始  2-预热中  3-进行中  4-已结束
                        if (Objects.nonNull(e.getPreheatTime())) {
                            if (now.isBefore(e.getPreheatTime())) {
                                e.setStatus(LuckDrawStatusEnum.NOT_START.getCode());
                            } else if (now.isBefore(e.getStartTime()) && now.isAfter(e.getPreheatTime())) {
                                e.setStatus(LuckDrawStatusEnum.PREHEATING.getCode());
                            } else if (now.isBefore(e.getEndTime()) && now.isAfter(e.getStartTime())) {
                                e.setStatus(LuckDrawStatusEnum.UNDER_WAY.getCode());
                            } else if (now.isAfter(e.getEndTime())) {
                                e.setStatus(LuckDrawStatusEnum.FINISHED.getCode());
                            }
                        } else {
                            if (now.isBefore(e.getStartTime())) {
                                e.setStatus(LuckDrawStatusEnum.NOT_START.getCode());
                            } else if (now.isBefore(e.getEndTime()) && now.isAfter(e.getStartTime())) {
                                e.setStatus(LuckDrawStatusEnum.UNDER_WAY.getCode());
                            } else if (now.isAfter(e.getEndTime())) {
                                e.setStatus(LuckDrawStatusEnum.FINISHED.getCode());
                            }
                        }
                        if (Objects.nonNull(luckDrawActivityPageQuery.getStatus())) {
                            e.setStatus(luckDrawActivityPageQuery.getStatus());
                        }
                    });
                    return luckDrawActivityVOS;
                });
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBasic(LuckDrawActivityBasicUpdateReq luckDrawActivityUpdateReq) {
        //校验更新数据
        checkUpdate(luckDrawActivityUpdateReq);

        //查询活动信息是否存在
        Long activityId = luckDrawActivityUpdateReq.getId();
        LuckyDrawActivity luckyDrawActivity = getInfo(activityId);
        if (Objects.isNull(luckyDrawActivity)) {
            throw new BizException("更新活动失败，该活动信息不存在！");
        }

        //转成实体类
        LuckyDrawActivity newLuckyDrawActivity = extracted(luckDrawActivityUpdateReq);

        //编辑活动不能修改活动类型
        if (Objects.nonNull(luckDrawActivityUpdateReq.getType()) &&
                !Objects.equals(luckyDrawActivity.getType(), luckDrawActivityUpdateReq.getType())) {
            throw new BizException("更新活动失败，不能修改活动类型！");
        }
        newLuckyDrawActivity.setType(luckyDrawActivity.getType());

        //更新时间校验
        checkUpdateTime(luckDrawActivityUpdateReq, luckyDrawActivity, newLuckyDrawActivity);

        //预热时间为空则说明删除了预热时间-将预热时间设置为开始时间
        if (Objects.isNull(luckDrawActivityUpdateReq.getPreheatTime())) {
            newLuckyDrawActivity.setPreheatTime(Objects.nonNull(newLuckyDrawActivity.getStartTime())
                    ? newLuckyDrawActivity.getStartTime() : luckyDrawActivity.getStartTime());
        }
        luckyDrawActivityMapper.updateByPrimaryKeySelective(newLuckyDrawActivity);

        //获取修改奖项信息
        List<LuckyDrawActivityEquityPackageUpdateReq> packageUpdateReq = luckDrawActivityUpdateReq.getPackageUpdateReq();

        //获取需要删除的奖项信息
        List<LuckyDrawActivityEquityPackageUpdateReq> delete = packageUpdateReq.stream().filter(e -> Objects.equals(e.getIsDelete(), CommonStatus.YES.getCode())).collect(Collectors.toList());

        //活动进行中不支持删除
        LocalDateTime now = LocalDateTime.now();
        if (!CollectionUtils.isEmpty(delete) && now.isBefore(luckyDrawActivity.getEndTime())
                && now.isAfter(luckyDrawActivity.getStartTime())) {
            throw new BizException("更新活动失败，活动进行中不支持删除奖项信息！");
        }

        //修改（新增）奖项信息
        List<LuckyDrawActivityEquityPackageUpdateReq> update = packageUpdateReq.stream().filter(e -> !Objects.equals(e.getIsDelete(), CommonStatus.YES.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(update) ||update.size() > 10) {
            throw new BizException("更新活动失败，活动奖项信息不能为空或不能超过10个！");
        }

        //校验奖项名称不能重复
        Set<String> equityPackageNames = update.stream().map(LuckyDrawActivityEquityPackageUpdateReq::getName).collect(Collectors.toSet());
        if (equityPackageNames.size() != update.size()) {
            throw new BizException("更新活动失败，奖项名称不能重复！");
        }

        //校验概率是否超过100%
        BigDecimal sumProbability = update.stream().map(LuckyDrawActivityEquityPackageUpdateReq::getProbability)
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(4, BigDecimal.ROUND_HALF_UP);
        if (sumProbability.compareTo(new BigDecimal(100.0000)) > 0) {
            throw new BizException("更新活动失败，奖项概率总和不能超过100%！");
        }

        if (!CollectionUtils.isEmpty(update)) {
            //删除所有的具体奖品信息
            List<Long> equityPackageIds = update.stream().map(LuckyDrawActivityEquityPackageUpdateReq::getId).collect(Collectors.toList());
            luckyDrawActivityPrizeMapper.batchDeleteByEqutiyPackageId(equityPackageIds);

            LuckyDrawActivityEquityPackage luckyDrawActivityEquityPackage;
            for (LuckyDrawActivityEquityPackageUpdateReq updateReq : update) {
                luckyDrawActivityEquityPackage = new LuckyDrawActivityEquityPackage();
                luckyDrawActivityEquityPackage.setId(updateReq.getId());
                luckyDrawActivityEquityPackage.setName(updateReq.getName());

                //将概率转换为浮点数 * 0.01 并保留4位小数
                luckyDrawActivityEquityPackage.setProbability(updateReq.getProbability().multiply(new BigDecimal(0.01))
                        .setScale(4, BigDecimal.ROUND_HALF_UP));

                //校验奖项数量 修改奖项数量时，向下修改数字时，需要额外判断不能低于已发放数量（即原奖项数量-当前剩余数量）
                if (Objects.nonNull(updateReq.getQuantity()) && Objects.nonNull(updateReq.getId())) {
                    LuckyDrawActivityEquityPackage equityPackage = luckyDrawActivityEquityPackageMapper.selectByPrimaryKey(updateReq.getId());
                    if (Objects.isNull(equityPackage)) {
                        continue;
                    } else if (updateReq.getQuantity().compareTo(equityPackage.getQuantity()) < 0
                            && updateReq.getQuantity().compareTo(equityPackage.getSendQuantity()) < 0 ){
                        throw new BizException("更新活动失败，奖项名称：" + equityPackage.getName() + "的修改数量不能低于已发放的数量！");
                    }
                    if (updateReq.getQuantity().compareTo(equityPackage.getQuantity()) != 0) {
                        luckyDrawActivityEquityPackage.setQuantity(updateReq.getQuantity());
                    }
                    luckyDrawActivityEquityPackageMapper.updateByPrimaryKeySelective(luckyDrawActivityEquityPackage);
                } else {
                    luckyDrawActivityEquityPackage.setActivityId(activityId);
                    luckyDrawActivityEquityPackage.setQuantity(updateReq.getQuantity());
                    luckyDrawActivityEquityPackage.setSurplusQuantity(updateReq.getQuantity());
                    luckyDrawActivityEquityPackage.setCreateTime(LocalDateTime.now());
                    luckyDrawActivityEquityPackageMapper.insertSelective(luckyDrawActivityEquityPackage);
                    updateReq.setId(luckyDrawActivityEquityPackage.getId());
                }

                //新增具体卡劵信息
                List<LuckyDrawActivityPrize> activityPrizeList = new ArrayList<>(updateReq.getCouponId().size());
                updateReq.getCouponId().stream().forEach(couponId -> {
                    LuckyDrawActivityPrize luckyDrawActivityPrize = new LuckyDrawActivityPrize();
                    luckyDrawActivityPrize.setActivityId(activityId);
                    luckyDrawActivityPrize.setEquityPackageId(updateReq.getId());
                    luckyDrawActivityPrize.setCouponId(couponId);
                    luckyDrawActivityPrize.setCreateTime(LocalDateTime.now());
                    activityPrizeList.add(luckyDrawActivityPrize);
                });
                luckyDrawActivityPrizeMapper.batchInsert(activityPrizeList);
            }
        }

        //删除奖项信息
        if (!CollectionUtils.isEmpty(delete)) {
            List<Long> equityPackageIds = delete.stream().map(LuckyDrawActivityEquityPackageUpdateReq::getId).collect(Collectors.toList());
            luckyDrawActivityEquityPackageMapper.batchDeleteById(equityPackageIds);
            luckyDrawActivityPrizeMapper.batchDeleteByEqutiyPackageId(equityPackageIds);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(LuckDrawActivityReq luckDrawActivityReq) {
        //查询活动信息是否存在
        LuckyDrawActivity luckyDrawActivity = getInfo(luckDrawActivityReq.getId());
        if (Objects.isNull(luckyDrawActivity)) {
            throw new BizException("删除失败，该活动信息不存在！");
        }

        //仅对“未开始”、“已结束”活动提供删除操作
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(luckyDrawActivity.getPreheatTime()) && now.isBefore(luckyDrawActivity.getEndTime())) {
            throw new BizException("删除失败，预热中或进行中活动不能删除！");
        }

        LuckyDrawActivity newLuckyDrawActivity = new LuckyDrawActivity();
        newLuckyDrawActivity.setStatus(CommonStatus.YES.getCode());
        newLuckyDrawActivity.setId(luckDrawActivityReq.getId());
        newLuckyDrawActivity.setUpdater(getAdminName());
        int update = luckyDrawActivityMapper.updateByPrimaryKeySelective(newLuckyDrawActivity);
        return update > 0;
    }

    /**
     * @description: 转成实体类对象
     * @author: lzh
     * @date: 2023/5/15 14:34
     * @param: [luckDrawActivityInsertReq]
     * @return: net.summerfarm.model.domain.LuckyDrawActivity
     **/
    private LuckyDrawActivity extracted(LuckDrawActivityBasicInsertReq luckDrawActivityInsertReq) {
        LuckyDrawActivity luckyDrawActivity = new LuckyDrawActivity();
        luckyDrawActivity.setName(luckDrawActivityInsertReq.getName());
        luckyDrawActivity.setStartTime(luckDrawActivityInsertReq.getStartTime());
        luckyDrawActivity.setEndTime(luckDrawActivityInsertReq.getEndTime());
        luckyDrawActivity.setPreheatTime(luckDrawActivityInsertReq.getPreheatTime());

        //预热时间为空 就获取开始时间
        if (Objects.isNull(luckyDrawActivity.getPreheatTime())) {
            luckyDrawActivity.setPreheatTime(luckDrawActivityInsertReq.getStartTime());
        }
        luckyDrawActivity.setRule(luckDrawActivityInsertReq.getRule());
        luckyDrawActivity.setType(luckDrawActivityInsertReq.getType());
        luckyDrawActivity.setBuoyImage(luckDrawActivityInsertReq.getBuoyImage());
        luckyDrawActivity.setPreheatBackground(luckDrawActivityInsertReq.getPreheatBackground());
        luckyDrawActivity.setNoDrawBackground(luckDrawActivityInsertReq.getNoDrawBackground());
        luckyDrawActivity.setDrawBackground(luckDrawActivityInsertReq.getDrawBackground());
        luckyDrawActivity.setEndBackground(luckDrawActivityInsertReq.getEndBackground());
        luckyDrawActivity.setShardImage(luckDrawActivityInsertReq.getShardImage());
        luckyDrawActivity.setShardTitle(luckDrawActivityInsertReq.getShardTitle());
        luckyDrawActivity.setShardRemake(luckDrawActivityInsertReq.getShardRemake());
        luckyDrawActivity.setShardTitle(luckDrawActivityInsertReq.getShardTitle());
        luckyDrawActivity.setCreateTime(LocalDateTime.now());
        luckyDrawActivity.setCreator(getAdminName());
        return luckyDrawActivity;
    }

    /**
     * @description: 转成实体类对象
     * @author: lzh
     * @date: 2023/5/15 14:34
     * @param: [LuckDrawActivityBasicUpdateReq]
     * @return: net.summerfarm.model.domain.LuckyDrawActivity
     **/
    private LuckyDrawActivity extracted(LuckDrawActivityBasicUpdateReq luckDrawActivityBasicUpdateReq) {
        LuckyDrawActivity luckyDrawActivity = new LuckyDrawActivity();
        luckyDrawActivity.setId(luckDrawActivityBasicUpdateReq.getId());
        luckyDrawActivity.setName(luckDrawActivityBasicUpdateReq.getName());
        luckyDrawActivity.setRule(luckDrawActivityBasicUpdateReq.getRule());
        luckyDrawActivity.setBuoyImage(luckDrawActivityBasicUpdateReq.getBuoyImage());
        luckyDrawActivity.setPreheatBackground(luckDrawActivityBasicUpdateReq.getPreheatBackground());
        luckyDrawActivity.setEndBackground(luckDrawActivityBasicUpdateReq.getEndBackground());
        luckyDrawActivity.setNoDrawBackground(luckDrawActivityBasicUpdateReq.getNoDrawBackground());
        luckyDrawActivity.setDrawBackground(luckDrawActivityBasicUpdateReq.getDrawBackground());
        luckyDrawActivity.setShardImage(luckDrawActivityBasicUpdateReq.getShardImage());
        luckyDrawActivity.setShardTitle(luckDrawActivityBasicUpdateReq.getShardTitle());
        luckyDrawActivity.setShardRemake(luckDrawActivityBasicUpdateReq.getShardRemake());
        luckyDrawActivity.setShardTitle(luckDrawActivityBasicUpdateReq.getShardTitle());
        luckyDrawActivity.setUpdater(getAdminName());
        return luckyDrawActivity;
    }

    /**
     * @description: 查询活动基本信息
     * @author: lzh
     * @date: 2023/5/15 17:00
     * @param: [id]
     * @return: net.summerfarm.model.domain.LuckyDrawActivity
     **/
    private LuckyDrawActivity getInfo(Long id) {
        LuckyDrawActivity luckyDrawActivity = luckyDrawActivityMapper.selectByPrimaryKey(id);
        return luckyDrawActivity;
    }

    /**
     * @description: 实体类转换成VO
     * @author: lzh
     * @date: 2023/5/15 17:28
     * @param: [luckyDrawActivity, luckDrawActivityVO]
     * @return: void
     **/
    private void conversionToVO(LuckyDrawActivity luckyDrawActivity, LuckDrawActivityVO luckDrawActivityVO) {
        luckDrawActivityVO.setId(luckyDrawActivity.getId());
        luckDrawActivityVO.setName(luckyDrawActivity.getName());
        luckDrawActivityVO.setStartTime(luckyDrawActivity.getStartTime());
        luckDrawActivityVO.setEndTime(luckyDrawActivity.getEndTime());
        luckDrawActivityVO.setPreheatTime(luckyDrawActivity.getPreheatTime());

        //预热时间和开始时间一样 则置空
        if (Objects.equals(luckDrawActivityVO.getPreheatTime(), luckDrawActivityVO.getStartTime())
                || luckDrawActivityVO.getPreheatTime().isAfter(luckDrawActivityVO.getStartTime())) {
            luckDrawActivityVO.setPreheatTime(null);
        }
        luckDrawActivityVO.setType(luckyDrawActivity.getType());
        luckDrawActivityVO.setDrawBackground(luckyDrawActivity.getDrawBackground());
        luckDrawActivityVO.setBuoyImage(luckyDrawActivity.getBuoyImage());
        luckDrawActivityVO.setNoDrawBackground(luckyDrawActivity.getNoDrawBackground());
        luckDrawActivityVO.setEndBackground(luckyDrawActivity.getEndBackground());
        luckDrawActivityVO.setRule(luckyDrawActivity.getRule());
        luckDrawActivityVO.setPreheatBackground(luckyDrawActivity.getPreheatBackground());
        luckDrawActivityVO.setShardImage(luckyDrawActivity.getShardImage());
        luckDrawActivityVO.setShardTitle(luckyDrawActivity.getShardTitle());
        luckDrawActivityVO.setShardRemake(luckyDrawActivity.getShardRemake());

        //设置活动状态
        LocalDateTime now = LocalDateTime.now();
        if (Objects.nonNull(luckDrawActivityVO.getPreheatTime())) {
            if (now.isBefore(luckDrawActivityVO.getPreheatTime())) {
                luckDrawActivityVO.setStatus(LuckDrawStatusEnum.NOT_START.getCode());
            } else if (now.isBefore(luckDrawActivityVO.getStartTime()) && now.isAfter(luckDrawActivityVO.getPreheatTime())) {
                luckDrawActivityVO.setStatus(LuckDrawStatusEnum.PREHEATING.getCode());
            } else if (now.isBefore(luckDrawActivityVO.getEndTime()) && now.isAfter(luckDrawActivityVO.getStartTime())) {
                luckDrawActivityVO.setStatus(LuckDrawStatusEnum.UNDER_WAY.getCode());
            } else if (now.isAfter(luckDrawActivityVO.getEndTime())) {
                luckDrawActivityVO.setStatus(LuckDrawStatusEnum.FINISHED.getCode());
            }
        } else {
            if (now.isBefore(luckDrawActivityVO.getStartTime())) {
                luckDrawActivityVO.setStatus(LuckDrawStatusEnum.NOT_START.getCode());
            } else if (now.isBefore(luckDrawActivityVO.getEndTime()) && now.isAfter(luckDrawActivityVO.getStartTime())) {
                luckDrawActivityVO.setStatus(LuckDrawStatusEnum.UNDER_WAY.getCode());
            } else if (now.isAfter(luckDrawActivityVO.getEndTime())) {
                luckDrawActivityVO.setStatus(LuckDrawStatusEnum.FINISHED.getCode());
            }
        }
    }

    /**
     * @description: 校验时间是否重叠
     * @author: lzh
     * @date: 2023/5/16 11:14
     * @param: [newLuckyDrawActivity]
     * @return: void
     **/
    private void checkTimeOverlap(LuckyDrawActivity newLuckyDrawActivity) {
        //预热时间不存在-同类型的活动开始时间至结束时间不能重叠 正向判断有四种 取反只有两种
        //正向思维-1、参考时间段 包含 比较时间段  2、参考时间段 只包含 比较时间段结束时间  3、参考时间段 只包含 比较时间段开始时间  4、比较时间段 包含 参考时间段
        //逆向思维-1、比较时间段的结束时间在参考时间段的开始时间之前  2、比较时间段的开始时间在参考时间段的结束时间之后
        int count = luckyDrawActivityMapper.getCount(newLuckyDrawActivity);
        if (count > 0) {
            throw new BizException("当前时间段已存在同类型活动！");
        }
    }

    /**
     * @description: 更新活动信息时间字段校验
     * @author: lzh
     * @date: 2023/5/18 14:54
     * @param: [luckDrawActivityUpdateReq, luckyDrawActivity, newLuckyDrawActivity]
     * @return: void
     **/
    private void checkUpdateTime(LuckDrawActivityBasicUpdateReq luckDrawActivityUpdateReq, LuckyDrawActivity luckyDrawActivity, LuckyDrawActivity newLuckyDrawActivity) {
        LocalDateTime now = LocalDateTime.now();

        //未开始均可修改活动时间、预热时间
        if (now.isBefore(luckyDrawActivity.getPreheatTime())) {
            //假如既修改了预热时间又修改了开始时间 则已预热时间为准 否则开始时间为准
            if (Objects.nonNull(luckDrawActivityUpdateReq.getPreheatTime()) &&
                    !Objects.equals(luckyDrawActivity.getPreheatTime(), luckDrawActivityUpdateReq.getPreheatTime())) {
                if (now.isAfter(luckDrawActivityUpdateReq.getPreheatTime())) {
                    throw new BizException("更新活动失败，预热时间不能小于当前时间！");
                }
                newLuckyDrawActivity.setPreheatTime(luckDrawActivityUpdateReq.getPreheatTime());
                newLuckyDrawActivity.setStartTime(luckDrawActivityUpdateReq.getStartTime());
            } else if (Objects.nonNull(luckDrawActivityUpdateReq.getStartTime()) &&
                    !Objects.equals(luckyDrawActivity.getStartTime(), luckDrawActivityUpdateReq.getStartTime())){
                if (now.isAfter(luckDrawActivityUpdateReq.getStartTime())) {
                    throw new BizException("更新活动失败，开始时间不能小于当前时间！");
                }
                newLuckyDrawActivity.setPreheatTime(luckDrawActivityUpdateReq.getStartTime());
                newLuckyDrawActivity.setStartTime(luckDrawActivityUpdateReq.getStartTime());
            }
            if (Objects.nonNull(luckDrawActivityUpdateReq.getEndTime()) &&
                    !Objects.equals(luckyDrawActivity.getEndTime(), luckDrawActivityUpdateReq.getEndTime())) {
                if (now.isAfter(luckDrawActivityUpdateReq.getEndTime())) {
                    throw new BizException("更新活动失败，结束时间不能小于当前时间！");
                }
                newLuckyDrawActivity.setEndTime(luckDrawActivityUpdateReq.getEndTime());
            }

            //校验时间 假如预热时间为空则默认拿此数据之前的预热时间 反之假如结束时间为空则默认拿此数据之前的结束时间
            if (Objects.nonNull(newLuckyDrawActivity.getPreheatTime()) && Objects.nonNull(newLuckyDrawActivity.getEndTime())) {
                checkTimeOverlap(newLuckyDrawActivity);
            } else if (Objects.nonNull(newLuckyDrawActivity.getPreheatTime())) {
                newLuckyDrawActivity.setEndTime(luckyDrawActivity.getEndTime());
                checkTimeOverlap(newLuckyDrawActivity);
            } else if (Objects.nonNull(newLuckyDrawActivity.getEndTime())) {
                newLuckyDrawActivity.setPreheatTime(luckyDrawActivity.getPreheatTime());
                checkTimeOverlap(newLuckyDrawActivity);
            }
        }

        //预热中和进行中不能修预热时间、改活动开始时间-仅支持修改结束时间
        if (now.isAfter(luckyDrawActivity.getPreheatTime()) && now.isBefore(luckyDrawActivity.getEndTime())) {
            if (Objects.nonNull(luckDrawActivityUpdateReq.getPreheatTime()) &&
                    !Objects.equals(luckyDrawActivity.getPreheatTime(), luckDrawActivityUpdateReq.getPreheatTime())) {
                throw new BizException("更新活动失败，预热中和进行中不能修改预热时间！");
            }
            if (Objects.nonNull(luckDrawActivityUpdateReq.getStartTime()) &&
                    !Objects.equals(luckyDrawActivity.getStartTime(), luckDrawActivityUpdateReq.getStartTime())) {
                throw new BizException("更新活动失败，预热中和进行中不能修改开始时间！");
            }
            if (Objects.nonNull(luckDrawActivityUpdateReq.getEndTime()) &&
                    !Objects.equals(luckyDrawActivity.getEndTime(), luckDrawActivityUpdateReq.getEndTime())) {
                if (now.isAfter(luckDrawActivityUpdateReq.getEndTime())) {
                    throw new BizException("更新活动失败，结束时间不能小于当前时间！");
                }

                //校验时间 默认带上之前的预热时间
                newLuckyDrawActivity.setPreheatTime(luckyDrawActivity.getPreheatTime());
                newLuckyDrawActivity.setEndTime(luckDrawActivityUpdateReq.getEndTime());
                checkTimeOverlap(newLuckyDrawActivity);
            }
        }
    }

    /**
     * @description: 更新活动信息字段校验
     * @author: lzh
     * @date: 2023/5/18 14:53
     * @param: [luckDrawActivityUpdateReq]
     * @return: void
     **/
    private void checkUpdate(LuckDrawActivityBasicUpdateReq luckDrawActivityUpdateReq) {
        if (Objects.nonNull(luckDrawActivityUpdateReq.getPreheatTime())) {
            if (Objects.equals(luckDrawActivityUpdateReq.getPreheatTime(), luckDrawActivityUpdateReq.getStartTime())) {
                throw new BizException("更新活动失败，预热时间不能和开始时间相等！");
            }
            if (luckDrawActivityUpdateReq.getPreheatTime().isAfter(luckDrawActivityUpdateReq.getStartTime())) {
                throw new BizException("更新活动失败，预热时间不能大于开始时间！");
            }
        }
        if (luckDrawActivityUpdateReq.getStartTime().isAfter(luckDrawActivityUpdateReq.getEndTime())) {
            throw new BizException("更新活动失败，开始时间不能大于结束时间！");
        }
        if (Objects.equals(luckDrawActivityUpdateReq.getStartTime(), luckDrawActivityUpdateReq.getEndTime())) {
            throw new BizException("更新活动失败，开始时间不能等于结束时间！");
        }
    }

    /**
     * @description: 新增活动信息校验
     * @author: lzh
     * @date: 2023/5/18 14:56
     * @param: [luckDrawActivityInsertReq]
     * @return: void
     **/
    private void checkInsert(LuckDrawActivityBasicInsertReq luckDrawActivityInsertReq) {
        //校验时间数据
        if (Objects.nonNull(luckDrawActivityInsertReq.getPreheatTime())) {
            if (Objects.equals(luckDrawActivityInsertReq.getPreheatTime(), luckDrawActivityInsertReq.getStartTime())) {
                throw new BizException("新增活动失败，预热时间不能和开始时间相等！");
            }
            if (luckDrawActivityInsertReq.getPreheatTime().isAfter(luckDrawActivityInsertReq.getStartTime())) {
                throw new BizException("新增活动失败，预热时间不能大于开始时间！");
            }
            if (LocalDateTime.now().isAfter(luckDrawActivityInsertReq.getPreheatTime())) {
                throw new BizException("新增活动失败，预热时间不能小于当前时间！");
            }
        }
        if (Objects.equals(luckDrawActivityInsertReq.getStartTime(), luckDrawActivityInsertReq.getEndTime())) {
            throw new BizException("新增活动失败，开始时间不能等于结束时间！");
        }
        if (luckDrawActivityInsertReq.getStartTime().isAfter(luckDrawActivityInsertReq.getEndTime())) {
            throw new BizException("新增活动失败，开始时间不能大于结束时间！");
        }
        if (LocalDateTime.now().isAfter(luckDrawActivityInsertReq.getStartTime())) {
            throw new BizException("新增活动失败，开始时间不能小于当前时间！");
        }
        if (LocalDateTime.now().isAfter(luckDrawActivityInsertReq.getEndTime())) {
            throw new BizException("新增活动失败，结束时间不能小于当前时间！");
        }

        //每日抽奖的额外校验
        if (Objects.equals(luckDrawActivityInsertReq.getType(), LuckDrawTypeEnum.DAILY_DRAW.getCode())) {
            if (Objects.isNull(luckDrawActivityInsertReq.getBuoyImage())) {
                throw new BizException("新增活动失败，每日抽奖活动的浮标图不能为空！");
            }
            if (Objects.isNull(luckDrawActivityInsertReq.getShardTitle()) ||
                    Objects.isNull(luckDrawActivityInsertReq.getShardImage()) || Objects.isNull(luckDrawActivityInsertReq.getShardRemake())) {
                throw new BizException("新增活动失败，每日抽奖活动分享标题或分享图或分享描述不能为空！");
            }
        }

        //奖项数量不能超过10个
        if (luckDrawActivityInsertReq.getPackageInsertReq().size() > 10) {
            throw new BizException("新增活动失败，活动奖项信息不能超过10个！");
        }

        //奖项名称去重校验
        Set<String> equityPackageNames = luckDrawActivityInsertReq.getPackageInsertReq().stream().map(LuckyDrawActivityEquityPackageInsertReq::getName).collect(Collectors.toSet());
        if (equityPackageNames.size() != luckDrawActivityInsertReq.getPackageInsertReq().size()) {
            throw new BizException("新增活动失败，奖项名称不能重复！");
        }
        Long pageInfoId = Long.valueOf(luckDrawActivityInsertReq.getRule().trim());
        CmsPageInfoDTO pageInfoDTO = cmsFacade.getBasicInfoById(pageInfoId);
        if (pageInfoDTO == null) {
            throw new BizException("请输入正确的专题页ID");
        }
    }
}
