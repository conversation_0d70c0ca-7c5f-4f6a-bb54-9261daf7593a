package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.enums.SMSType;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.service.GroupBuyService;
import net.summerfarm.service.MsgAdminService;
import net.summerfarm.service.OrderService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
public class GroupBuyServiceImpl extends BaseService implements GroupBuyService {
    @Resource
    private GroupBuyInfoMapper groupBuyInfoMapper;
    @Resource
    private GroupBuyOrderRecordMapper groupBuyOrderRecordMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private OrderService orderService;
    @Resource
    private MsgAdminService msgAdminService;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private OrdersMapper ordersMapper;

    private GroupBuyService selfService;

    @Resource
    private GroupBuyConfigMapper groupBuyConfigMapper;

    @PostConstruct
    private void setSelf() {
        selfService = getContext().getBean(GroupBuyServiceImpl.class);
    }

    @Override
    public void groupBuyFinish() {
        log.info("团购截单任务执行开始...");
        LocalDateTime finishTime = LocalDateTime.now().minusHours(1);
        // 查询截止到当前时间前一个小时结束的活动 即 活动状态为1且结束团购活动结束时间小于等于当前时间前一个小时的活动
        List<GroupBuyConfig> configs = groupBuyConfigMapper.selectByActualEndTime(finishTime);
        if(CollectionUtils.isEmpty(configs)){
            log.info("截止到当前无结束的团购活动配置:{}", DateUtils.localDateTime2Date(finishTime));
            return;
        }
        for (GroupBuyConfig config : configs) {
            List<GroupBuyInfo> groupBuyInfoList = groupBuyInfoMapper.selectByGroupBuyConfigId(config.getId());
            if (CollectionUtils.isEmpty(groupBuyInfoList)) {
                log.info("当前团购活动配置下无团购场次,团id:{},时间:{}", config.getId(), DateUtils.localDateTime2Date(finishTime));
                groupBuyConfigMapper.updateStatusById(config.getId());
                continue;
            }

            // 记录已经处理的活动场次，每处理一个场次记录数+1，只有处理记录数
            List<Long> handleBuyId = new ArrayList<>();
            List<Long> failBuyId = new ArrayList<>();
            for (GroupBuyInfo buyInfo : groupBuyInfoList) {
                BigDecimal total = groupBuyOrderRecordMapper.sumGroupBuyInfo(buyInfo.getId());
                boolean successFlag = BigDecimal.valueOf(buyInfo.getLeastNumber()).compareTo(total) <= 0;
                log.info("团购截单任务详情，infoId：{}，是否成团：{}", buyInfo.getId(), successFlag);

                try {
                    selfService.finishGroupBuy(buyInfo, successFlag);
                    handleBuyId.add(buyInfo.getId());
                } catch (Exception e) {
                    log.error("场次成团处理异常,团id:{}", buyInfo.getId());
                    failBuyId.add(buyInfo.getId());
                }


                try {
                    //发送短信数据
                    List<GroupBuyOrderRecord> recordList = groupBuyOrderRecordMapper.selectByInfoId(buyInfo.getId());
                    for (GroupBuyOrderRecord record : recordList) {
                        String date = DateUtils.date2String(DateUtils.localDateTime2Date(record.getCreateTime()), "MM月dd日HH:mm:ss");
                        List<OrderItem> itemList = orderItemMapper.queryItemList(record.getOrderNo());
                        Set<String> nameSet = new HashSet<>();
                        for (OrderItem item : itemList) {
                            if (item.getSuitId() > 0) {
                                nameSet.add(item.getSuitName());
                            } else {
                                nameSet.add(item.getPdName());
                            }
                        }
                        Orders orders = ordersMapper.queryByOrderNo(record.getOrderNo());
                        String mon = orders.getTotalPrice().setScale(2, RoundingMode.HALF_UP).toString();
                        // 17L 代表拼团成功消息 15L代表拼团失败消息
                        msgAdminService.sms(successFlag ? 17L : 15L, Arrays.asList(record.getOrderNo(), date, String.join("、", nameSet), mon), record.getPhone(), SMSType.NOTIFY);
                    }
                } catch (Exception e) {
                    log.error("发送成团短信异常,团id:{}", buyInfo.getId(), e);
                }
            }
            // 如果处理的成团数与需要处理的成团数相等，则变更活动状态为失效
            if(handleBuyId.size() == groupBuyInfoList.size()){
                groupBuyConfigMapper.updateStatusById(config.getId());
            }
            if(CollectionUtils.isNotEmpty(failBuyId)){
                log.info("当前拼团下的所有成团场次未处理完成,未处理完成场次id:{}", JSON.toJSONString(failBuyId));
            }
        }
        log.info("团购截单任务执行结束...");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishGroupBuy(GroupBuyInfo buyInfo, boolean successFlag) {
        List<GroupBuyOrderRecord> recordList = groupBuyOrderRecordMapper.selectByInfoId(buyInfo.getId());
        for (GroupBuyOrderRecord orderRecord : recordList) {
            if (successFlag) {
                DeliveryPlan oldDeliveryPlan = deliveryPlanMapper.queryDeliveryTimeByOrderNo(orderRecord.getOrderNo());
                if (oldDeliveryPlan == null || oldDeliveryPlan.getDeliveryTime().isBefore(buyInfo.getDeliveryTime())){
                    log.info("团购订单{}已配送，不调整配送日期", orderRecord.getOrderNo());
                    continue;
                }

                DeliveryPlan deliveryPlan = new DeliveryPlan();
                deliveryPlan.setDeliveryTime(buyInfo.getDeliveryTime());
                deliveryPlan.setOrderNo(orderRecord.getOrderNo());
                deliveryPlanMapper.updateByorderNo(deliveryPlan);
            } else {
                orderService.closeOrder(orderRecord.getOrderNo());
            }
        }

        GroupBuyInfo update = new GroupBuyInfo();
        update.setId(buyInfo.getId());
        update.setStatus(successFlag ? 1 : 2);
        groupBuyInfoMapper.updateByPrimaryKeySelective(update);
    }
}
