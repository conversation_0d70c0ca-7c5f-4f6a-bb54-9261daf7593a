package net.summerfarm.service.impl;

import net.summerfarm.common.base.BaseService;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.AreaSkuMapper;
import net.summerfarm.mapper.manage.AreaSkuRecordMapper;
import net.summerfarm.mapper.manage.BiOnSaleTimeMapper;
import net.summerfarm.model.domain.Area;
import net.summerfarm.model.domain.AreaSku;
import net.summerfarm.model.domain.AreaSkuRecord;
import net.summerfarm.model.domain.BiOnSaleTime;
import net.summerfarm.model.vo.AreaSkuRecordVO;
import net.summerfarm.service.BiOnSaleTimeService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class BiOnSaleTimeServiceImpl extends BaseService implements BiOnSaleTimeService {
    @Resource
    private BiOnSaleTimeMapper biOnSaleTimeMapper;
    @Resource
    private AreaSkuRecordMapper areaSkuRecordMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private AreaMapper areaMapper;

    @Override
    public void update(LocalDate runDate) {
        logger.info("上架时长更新开始:{}", LocalDateTime.now());

        //前一天上架时长数据 ok
        LocalDateTime startTime = runDate.minusDays(1).atTime(0, 0);
        LocalDateTime endTime = runDate.atTime(0, 0);

        BiOnSaleTime select = new BiOnSaleTime();
        select.setAddDate(runDate.minusDays(2));
        Map<String, BiOnSaleTime> records = biOnSaleTimeMapper.selectList(select).stream().collect(Collectors.toMap(o -> o.getAreaNo() + ":" + o.getSku(), o -> o));

        //上下架记录
        AreaSkuRecordVO selectKey = new AreaSkuRecordVO();
        selectKey.setStartTime(startTime);
        selectKey.setEndTime(endTime);
        selectKey.setTypeName("上下架");
        List<AreaSkuRecord> areaSkuRecords = areaSkuRecordMapper.selectList(selectKey);
        Map<String, List<AreaSkuRecord>> areaSkuMap = new HashMap<>(areaSkuRecords.size());
        if (!CollectionUtils.isEmpty(areaSkuRecords)) {
            areaSkuMap = areaSkuRecords.stream().collect(Collectors.groupingBy(o -> o.getAreaNo() + ":" + o.getSku()));
        }

        //城市对应仓库
        Area query = new Area();
        query.setStatus(true);
        List<Area> areaList = areaMapper.selectAll(query);
        Map<Integer, Integer> areaToStoreMap = new HashMap<>(areaList.size());
        areaList.forEach(el -> areaToStoreMap.put(el.getAreaNo(), el.getLargeAreaNo()));

        //售罄记录
        AreaSkuRecordVO voQuery = new AreaSkuRecordVO();
        voQuery.setStartTime(startTime);
        voQuery.setEndTime(endTime);
        voQuery.setTypeName("售罄");
        List<AreaSkuRecord> areaSkuRecords2 = areaSkuRecordMapper.selectList(selectKey);
        Map<String, List<AreaSkuRecord>> storeRecordMap = areaSkuRecords2.stream().collect(Collectors.groupingBy(el -> el.getAreaNo().toString() + ":" + el.getSku()));

        //计算售卖时长
        List<AreaSku> areaSkus = areaSkuMapper.selectList(new AreaSku());
        List<BiOnSaleTime> insert = new ArrayList<>();
        for (AreaSku areaSku : areaSkus) {
            String key = areaSku.getAreaNo() + ":" + areaSku.getSku();
            BiOnSaleTime onSaleTime = records.get(key);
            List<AreaSkuRecord> areaSkuRecordList = areaSkuMap.get(key);

            //把售罄、补货数据转换为上下架数据（方便统一计算）
            Integer storeNo = areaToStoreMap.get(areaSku.getAreaNo());
            if(storeNo == null){
                continue;
            }
            List<AreaSkuRecord> tempList = storeRecordMap.get(storeNo.toString() + ":" + areaSku.getSku());
            if(!CollectionUtils.isEmpty(tempList)){
                    areaSkuRecordList.addAll(tempList);
            }

            BiOnSaleTime result = calOnSaleTime(areaSku, onSaleTime, areaSkuRecordList);
            insert.add(result);
        }

        insert.forEach(o -> biOnSaleTimeMapper.insert(o));
        logger.info("上架时长更新结束:{}", LocalDateTime.now());
    }

    private BiOnSaleTime calOnSaleTime(AreaSku areaSku, BiOnSaleTime record, List<AreaSkuRecord> records) {
        BiOnSaleTime insert = new BiOnSaleTime();
        insert.setSku(areaSku.getSku());
        insert.setAreaNo(areaSku.getAreaNo());
        insert.setAddDate(LocalDate.now().minusDays(1));
        insert.setResult(areaSku.getOnSale());

        Boolean onSale = record == null ? Boolean.FALSE : record.getResult();
        LocalTime startTime = LocalTime.of(0, 0);
        long onSaleTime = 0L;

        if (CollectionUtils.isEmpty(records)) {
            insert.setOnSaleTime(onSale ? BigDecimal.valueOf(24) : BigDecimal.ZERO);
            return insert;
        }

        records.sort((o1, o2) -> o1.getAddtime().isBefore(o2.getAddtime()) ? 1 : 0);
        for (AreaSkuRecord areaSkuRecord : records) {
            if (onSale && !areaSkuRecord.getResultStatus()) {
                //上架时长
                long onSaleSeconds = areaSkuRecord.getAddtime().toEpochSecond(ZoneOffset.MIN) - LocalDateTime.of(areaSkuRecord.getAddtime().toLocalDate(), startTime).toEpochSecond(ZoneOffset.MIN);
                onSaleTime += onSaleSeconds;
            } else if (!onSale && areaSkuRecord.getResultStatus()) {
                startTime = areaSkuRecord.getAddtime().toLocalTime();
            }
            onSale = areaSkuRecord.getResultStatus();
        }

        //最后一次记录是上架
        if (records.get(records.size() - 1).getResultStatus()) {
            long seconds = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).toEpochSecond(ZoneOffset.MIN) - LocalDateTime.of(LocalDate.now(), startTime).toEpochSecond(ZoneOffset.MIN);
            onSaleTime += seconds;
        }
        insert.setOnSaleTime(BigDecimal.valueOf(onSaleTime / 3600));
        return insert;
    }
}
