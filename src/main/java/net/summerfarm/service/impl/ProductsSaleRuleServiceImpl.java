package net.summerfarm.service.impl;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.mapper.manage.ProductsMapper;
import net.summerfarm.mapper.product.ProductsSaleRuleMapper;
import net.summerfarm.model.ProductsSaleRule;
import net.summerfarm.model.domain.Products;
import net.summerfarm.model.param.ProductsSaleRuleQueryParam;
import net.summerfarm.model.vo.ProductsSaleRuleVO;
import net.summerfarm.model.vo.ProductsVO;
import net.summerfarm.service.ProductsSaleRuleService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
*
* <AUTHOR>
* @date 2024-11-21 15:54:58
* @version 1.0
*
*/
@Service
@Slf4j
public class ProductsSaleRuleServiceImpl extends BaseService implements ProductsSaleRuleService {

    @Resource
    private ProductsSaleRuleMapper productsSaleRuleMapper;
    @Resource
    private ProductsMapper productsMapper;


    @Override
    public PageInfo<ProductsSaleRuleVO> getPage(ProductsSaleRuleQueryParam param) {
        int pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        int pageIndex = param.getPageIndex() == null ? 1 : param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<ProductsSaleRuleVO> select = productsSaleRuleMapper.getPage(param);
        return PageInfoHelper.createPageInfo(select);
    }

    @Override
    public int insert(ProductsSaleRule entity) {
        if(entity.getPdId() == null || entity.getBaseSaleQuantity() == null) {
            log.error("请求参数缺失.entity:{}", JSON.toJSONString(entity));
            throw new ParamsException("请求参数缺失!");
        }

        ProductsSaleRule productsSaleRule = productsSaleRuleMapper.selectByPdId(entity.getPdId());
        if(productsSaleRule != null) {
            log.error("该商品已存在起购规则！.entity:{}", JSON.toJSONString(entity));
            throw new BizException("该商品已存在起购规则！");
        }
        entity.setStatus(1);
        entity.setOperator(this.getAdminName());
        return productsSaleRuleMapper.insertSelective(entity);
    }


    public PageInfo<Products> selectPageForCreatePurchaseRule(int pageIndex, int pageSize, Products products) {
        PageHelper.startPage(pageIndex, pageSize);
        return PageInfoHelper.createPageInfo(productsMapper.selectPageForCreatePurchaseRule(products));
    }

    @Override
    public int update(ProductsSaleRule entity) {
        if(entity.getId() == null) {
            log.error("请求参数缺失.entity:{}", JSON.toJSONString(entity));
            throw new ParamsException("请求参数缺失!");
        }
        ProductsSaleRule updateRecord = new ProductsSaleRule();
        updateRecord.setStatus(entity.getStatus());
        updateRecord.setBaseSaleQuantity(entity.getBaseSaleQuantity());
        updateRecord.setId(entity.getId());
        updateRecord.setOperator(this.getAdminName());
        return productsSaleRuleMapper.updateSelectiveById(updateRecord);
    }

    @Override
    public int delete(Long id) {
        return productsSaleRuleMapper.remove(id);
    }
}