package net.summerfarm.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.*;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.DTO.ChargeBackDTO;
import net.summerfarm.model.DTO.OrderInterceptionDTO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.service.DeliveryPathService;
import net.summerfarm.service.OrderService;
import net.summerfarm.service.StockTaskService;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.dist.dto.DistOrderInterceptDTO;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.event.EventTypeEnum;
import net.summerfarm.tms.event.TmsDeliveryEvent;
import net.summerfarm.tms.message.DeliveryOrderMessage;
import net.xianmu.common.result.CommonResult;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> ct
 * create at:  2021/12/29  11:21
 */
@Service
@Slf4j
public class DeliveryPathServiceImpl implements DeliveryPathService {

    @Resource
    DeliveryPathMapper deliveryPathMapper;
    @Resource
    OrdersMapper ordersMapper;
    @Resource
    private RedisTemplate<String,String> redisTemplate;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Resource
    private SampleApplyMapper sampleApplyMapper;
    @Resource
    private SampleSkuMapper sampleSkuMapper;
    @Resource
    private DeliveryPathInterceptSkuMapper deliveryPathInterceptSkuMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    @Lazy
    private StockTaskService stockTaskService;
    @Resource
    private AfterSaleDeliveryDetailMapper afterSaleDeliveryDetailMapper;
    @Resource
    private TmsTaskMapper tmsTaskMapper;
    @Resource
    private OrderService orderService;
    @Resource
    private StockTaskMapper stockTaskMapper;

    @Autowired
    private MqProducer mqProducer;

    @Override
    public DeliveryPath selectByPrimaryKey(Integer id) {
        return deliveryPathMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<DeliveryPath> getListSelectByPrimaryKey(List<Integer> ids) {
        return deliveryPathMapper.getListSelectByPrimaryKey(ids);
    }

    @Override
    public Boolean tryLock(DeliveryPath record){
        String redisKey = getKey(record);
        String value = Objects.isNull(record.getId()) ? " " : record.getId().toString();
        Boolean tryLock = redisTemplate.opsForValue().setIfAbsent(redisKey, value, 30, TimeUnit.SECONDS);
        return tryLock;
    }

    @Override
    public void deleteLock(DeliveryPath record){
        String redisKey = getKey(record);
        redisTemplate.delete(redisKey);
    }

    /**
     * 获取key
     * @param record
     * @return
     */
    private String getKey(DeliveryPath record){

        //校验参数
        if(Objects.isNull(record) || Objects.isNull(record.getDeliveryTime())
                || Objects.isNull(record.getStoreNo()) ){
            throw new DefaultServiceException("无该路线信息");
        }
        LocalDate deliveryTime = record.getDeliveryTime();
        String timeToString = DateUtils.localDateToString(deliveryTime, DateUtils.DEFAULT_DATE_FORMAT);
        Integer storeNo = record.getStoreNo();
        //根据日期 城配仓 纬度加锁
        StringJoiner keyJoiner = new StringJoiner(Global.REDIS_DELIMITER);
        keyJoiner.add(Global.DELIVERY_PATH_UPDATE_KEY).add(timeToString).add(storeNo.toString());
        String redisKey = keyJoiner.toString();

        return redisKey;
    }

    /**
     * 单个和批量的订单拦截
     * @param orderNos 订单号集合
     * @param noHeartDeliveryTime 省心送单个需要传配送日期
     * @param flag 是否取消补发单和样品单，true取消，false不取消
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public AjaxResult sendOrderInterception(List<String> orderNos,LocalDate noHeartDeliveryTime,Boolean flag,Integer deliveryPlanId) {
        if(flag == null){
            throw new DefaultServiceException("flag不能为空");
        }
        if(CollectionUtils.isEmpty(orderNos)){
            throw new DefaultServiceException("订单号不能为空");
        }
        //拦截失败的订单
        ArrayList<String> errorOrderList = new ArrayList<>();
        //样品取消的id
        ArrayList<Integer> sampleIdList = new ArrayList();
        //补发取消的售后单号
        ArrayList<String> afterSaleOrderNoList = new ArrayList();

        //根据订单号查询需要拦截的订单信息
        List<OrderInfoVo> orderInfos = ordersMapper.getOrderInfoByOrderNos(orderNos,noHeartDeliveryTime,deliveryPlanId);
        //配送计划id为空，表示批量退单
        if(deliveryPlanId == null){
            //省心送订单只退当天的订单
            //把省心送订单配送日期不是今天的给排除掉
            Iterator<OrderInfoVo> orderIter = orderInfos.iterator();
            while(orderIter.hasNext()){
                OrderInfoVo order = orderIter.next();
                if(order.getType() == 1 && !order.getDeliveryTime().isEqual(LocalDate.now())){
                    String orderNo = order.getOrderNo();
                    if(!errorOrderList.contains(orderNo)){
                        errorOrderList.add(orderNo);
                    }
                    orderIter.remove();
                }
            }
        }
        //订单拦截截单时间和配送状态检查
        closeTimeSendStatusCheak(errorOrderList,orderInfos);
        //查询点位是否存在其他订单、补发，换货、退货退款、样品
        for (OrderInfoVo orderInfo : orderInfos) {
            LocalDate deliveryTime = orderInfo.getDeliveryTime();
            Integer contactId = orderInfo.getContactId();
            String orderNo = orderInfo.getOrderNo();
            Integer deliveryPathId = orderInfo.getDeliveryPathId();
            Integer orderDeliveryPlanId = orderInfo.getDeliveryPlanId();
            Integer pathStatus = orderInfo.getPathStatus();
            Integer storeNo = orderInfo.getStoreNo();
            Integer type = orderInfo.getType();

            List<GetOrderDataVO> deliveryPlanVos = deliveryPlanMapper.getOtherOrderDataByDTAndCIdAndONo(deliveryTime,contactId,orderNo);

            //拦截时间
            LocalDateTime interceptTime = LocalDateTime.now();
            //判断当前是否排线了，如果完成排线就展示，没有就不展示
            DeliveryPath deliveryPathInfo = deliveryPathMapper.selectOne(storeNo, deliveryTime, Long.parseLong(String.valueOf(contactId)));
            Integer showFlag = ShowFlagEnum.show.getCode();
            if(deliveryPathInfo == null || deliveryPathInfo.getPathStatus() == null){
                showFlag = ShowFlagEnum.noShow.getCode();
            }
            //设置订单的拦截状态
            if(orderDeliveryPlanId != null){
                DeliveryPlan deliveryPlan = new DeliveryPlan();
                deliveryPlan.setId(orderDeliveryPlanId);
                deliveryPlan.setInterceptFlag(InterceptFlagEnum.intercept.getCode());
                deliveryPlan.setInterceptTime(interceptTime);
                deliveryPlan.setShowFlag(showFlag);
                deliveryPlanMapper.updateById(deliveryPlan);
            }
            //将订单详情添加到拦截sku表中，并且生成拦截入库信息
            orderSkuDetailAddInterceptSku(orderNo,deliveryTime,contactId,storeNo,orderDeliveryPlanId,type,interceptTime);

            List<GetAfterDataVO> afterSaleDeliveryPathVos = new ArrayList<>();
            List<SampleSkuVO> sampleSkus = new ArrayList<>();
            if(flag){
                //查询售后
                afterSaleDeliveryPathVos = afterSaleDeliveryPathMapper.getAfterDataByDTAndCId(deliveryTime,contactId);
                //查询样品
                sampleSkus = sampleApplyMapper.getIdByDTAndCId(deliveryTime,contactId);
            }

            //样品需要取消，并且生成拦截入库信息
            List<Integer> sampleIds = cancelSample(sampleSkus, deliveryTime, contactId, storeNo,interceptTime,showFlag);
            if(!CollectionUtils.isEmpty(sampleIds)){
                sampleIdList.addAll(sampleIds);
            }
            //补发取消，并且生成拦截入库信息
            List<String> cancleAfterOrderNoList = cancleReissue(afterSaleDeliveryPathVos, deliveryTime, contactId, storeNo,interceptTime,showFlag);
            if(!CollectionUtils.isEmpty(cancleAfterOrderNoList)){
                afterSaleOrderNoList.addAll(cancleAfterOrderNoList);
            }

            //过滤只有回收的条件
            List<GetAfterDataVO> noRecycleAfterSaleDeliveryPaths = afterSaleDeliveryPathVos.stream().
                    filter(afterSaleDeliveryPath -> afterSaleDeliveryPath.getType() == AfterSaleTypeEnum.recycle.getCode()).collect(Collectors.toList());
            if(deliveryPlanVos.size() > 0 || noRecycleAfterSaleDeliveryPaths.size() > 0){
                //设置路线为部分拦截状态
                setDeliveryPathInterceptType(deliveryPathId,InterceptTypeEnum.partIntercept.getCode());
            }else{
                //设置全部拦截状态
                if(pathStatus != null){
                    //排线完成
                    setDeliveryPathInterceptType(deliveryPathId,InterceptTypeEnum.allIntercept.getCode());
                }else{
                    //过滤只有配送的数据
                    List<GetAfterDataVO> haveRecycleAfterSaleDeliveryPaths = afterSaleDeliveryPathVos.stream()
                            .filter(afterSaleDeliveryPath -> afterSaleDeliveryPath.getType() == AfterSaleTypeEnum.recycle.getCode()).collect(Collectors.toList());
                    //没有完成排线有回收任务不能删除，如果全部拦截，配送回收改为回收
                    if(pathStatus == null && haveRecycleAfterSaleDeliveryPaths.size() == 0){
                        deliveryPathMapper.deleteDeliveryPath(Arrays.asList(deliveryPathId));
                    }else{
                        DeliveryPath deliveryPath = new DeliveryPath();
                        deliveryPath.setId(deliveryPathId);
                        deliveryPath.setDeliveryType(AfterSaleTypeEnum.recycle.getCode());
                        deliveryPath.setInterceptType(InterceptTypeEnum.allIntercept.getCode());
                        deliveryPathMapper.updateByPrimaryKey(deliveryPath);
                    }
                }
            }
        }

        ArrayList<String> successOrderList = new ArrayList<>();
        SendOrderInterceptionVO sendOrderInterceptionVO = new SendOrderInterceptionVO();
        if(!CollectionUtils.isEmpty(orderInfos)){
            Set<String> orderSet = orderInfos.stream().map(OrderInfoVo::getOrderNo).collect(Collectors.toSet());
            for (String orderNo : orderSet) {
                successOrderList.add(orderNo);
            }
        }
        sendOrderInterceptionVO.setSuccessOrderList(successOrderList);
        sendOrderInterceptionVO.setErrorOrderList(errorOrderList);
        sendOrderInterceptionVO.setAfterSaleOrderNoList(afterSaleOrderNoList);
        sendOrderInterceptionVO.setSampleIdList(sampleIdList);
        return AjaxResult.getOK(sendOrderInterceptionVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Void> sendOrderInterceptionNew(List<String> orderNos, Boolean flag, String contactIdParam, LocalDate deliveryTimeParam, Integer interceptReasonCode, String interceptReason) {
        if(flag == null){
            throw new DefaultServiceException("flag不能为空");
        }
        if(CollectionUtils.isEmpty(orderNos)){
            throw new DefaultServiceException("订单号不能为空");
        }
        //拦截失败的订单
        ArrayList<OrderInfoVo> errorOrderList = new ArrayList<>();
        //样品取消的id
        ArrayList<SampleSkuVO> sampleIdList = new ArrayList();
        //补发取消的售后单号
        ArrayList<GetAfterDataVO> afterSaleOrderNoList = new ArrayList();
        Integer deliveryPlanId = null;
        if(StringUtils.isNotBlank(contactIdParam) && deliveryTimeParam != null){
            DeliveryPlan deliveryPlan = new DeliveryPlan();
            deliveryPlan.setDeliveryTime(deliveryTimeParam);
            deliveryPlan.setOrderNo(orderNos.get(0));
            deliveryPlan.setContactId(Long.parseLong(contactIdParam));
            DeliveryPlan deliveryPlanData = deliveryPlanMapper.selectOne(deliveryPlan);
            deliveryPlanId = deliveryPlanData.getId();
        }

        //根据订单号查询需要拦截的订单信息
        List<OrderInfoVo> orderInfos = ordersMapper.getOrderInfoByOrderNos(orderNos,null,deliveryPlanId);
        //配送计划id为空，表示批量退单
        if(deliveryPlanId == null){
            //省心送订单只退当天的订单
            //把省心送订单配送日期不是今天的给排除掉
            Iterator<OrderInfoVo> orderIter = orderInfos.iterator();
            while(orderIter.hasNext()){
                OrderInfoVo order = orderIter.next();
                if(order.getType() == 1 && !order.getDeliveryTime().isEqual(LocalDate.now())){
                    if(!errorOrderList.contains(order)){
                        errorOrderList.add(order);
                    }
                    orderIter.remove();
                }
            }
        }
        //订单拦截截单时间和配送状态检查
        closeTimeSendStatusCheakNew(errorOrderList,orderInfos);
        //查询点位是否存在其他订单、补发，换货、退货退款、样品
        for (OrderInfoVo orderInfo : orderInfos) {
            LocalDate deliveryTime = orderInfo.getDeliveryTime();
            Integer contactId = orderInfo.getContactId();
            String orderNo = orderInfo.getOrderNo();
            Integer deliveryPathId = orderInfo.getDeliveryPathId();
            Integer orderDeliveryPlanId = orderInfo.getDeliveryPlanId();
            Integer pathStatus = orderInfo.getPathStatus();
            Integer storeNo = orderInfo.getStoreNo();
            Integer type = orderInfo.getType();

            List<GetOrderDataVO> deliveryPlanVos = deliveryPlanMapper.getOtherOrderDataByDTAndCIdAndONo(deliveryTime,contactId,orderNo);

            //拦截时间
            LocalDateTime interceptTime = LocalDateTime.now();
            //判断当前是否排线了，如果完成排线就展示，没有就不展示
            DeliveryPath deliveryPathInfo = deliveryPathMapper.selectOne(storeNo, deliveryTime, Long.parseLong(String.valueOf(contactId)));
            Integer showFlag = ShowFlagEnum.show.getCode();
            if(deliveryPathInfo == null || deliveryPathInfo.getPathStatus() == null){
                showFlag = ShowFlagEnum.noShow.getCode();
            }
            //设置订单的拦截状态
            if(orderDeliveryPlanId != null){
                DeliveryPlan deliveryPlan = new DeliveryPlan();
                deliveryPlan.setId(orderDeliveryPlanId);
                deliveryPlan.setInterceptFlag(InterceptFlagEnum.intercept.getCode());
                deliveryPlan.setInterceptTime(interceptTime);
                deliveryPlan.setShowFlag(showFlag);
                deliveryPlanMapper.updateById(deliveryPlan);
            }
            //将订单详情添加到拦截sku表中，并且生成拦截入库信息
            orderSkuDetailAddInterceptSku(orderNo,deliveryTime,contactId,storeNo,orderDeliveryPlanId,type,interceptTime);

            List<GetAfterDataVO> afterSaleDeliveryPathVos = new ArrayList<>();
            List<SampleSkuVO> sampleSkus = new ArrayList<>();
            if(flag){
                //查询售后
                afterSaleDeliveryPathVos = afterSaleDeliveryPathMapper.getAfterDataByDTAndCId(deliveryTime,contactId);
                //查询样品
                sampleSkus = sampleApplyMapper.getIdByDTAndCId(deliveryTime,contactId);
            }

            //样品需要取消，并且生成拦截入库信息
            cancelSample(sampleSkus, deliveryTime, contactId, storeNo,interceptTime,showFlag);
            if(!CollectionUtils.isEmpty(sampleSkus)){
                sampleIdList.addAll(sampleSkus);
            }
            //补发取消，并且生成拦截入库信息
            cancleReissue(afterSaleDeliveryPathVos, deliveryTime, contactId, storeNo,interceptTime,showFlag);
            if(!CollectionUtils.isEmpty(afterSaleDeliveryPathVos)){
                afterSaleOrderNoList.addAll(afterSaleDeliveryPathVos);
            }

            //过滤只有回收的条件
            List<GetAfterDataVO> noRecycleAfterSaleDeliveryPaths = afterSaleDeliveryPathVos.stream().
                    filter(afterSaleDeliveryPath -> afterSaleDeliveryPath.getType() == AfterSaleTypeEnum.recycle.getCode()).collect(Collectors.toList());
            if(deliveryPlanVos.size() > 0 || noRecycleAfterSaleDeliveryPaths.size() > 0){
                //设置路线为部分拦截状态
                setDeliveryPathInterceptType(deliveryPathId,InterceptTypeEnum.partIntercept.getCode());
            }else{
                //设置全部拦截状态
                if(pathStatus != null){
                    //排线完成
                    setDeliveryPathInterceptType(deliveryPathId,InterceptTypeEnum.allIntercept.getCode());
                }else{
                    //过滤只有配送的数据
                    List<GetAfterDataVO> haveRecycleAfterSaleDeliveryPaths = afterSaleDeliveryPathVos.stream()
                            .filter(afterSaleDeliveryPath -> afterSaleDeliveryPath.getType() == AfterSaleTypeEnum.recycle.getCode()).collect(Collectors.toList());
                    //没有完成排线有回收任务不能删除，如果全部拦截，配送回收改为回收
                    if(pathStatus == null && haveRecycleAfterSaleDeliveryPaths.size() == 0){
                        deliveryPathMapper.deleteDeliveryPath(Arrays.asList(deliveryPathId));
                    }else{
                        DeliveryPath deliveryPath = new DeliveryPath();
                        deliveryPath.setId(deliveryPathId);
                        deliveryPath.setDeliveryType(AfterSaleTypeEnum.recycle.getCode());
                        deliveryPath.setInterceptType(InterceptTypeEnum.allIntercept.getCode());
                        deliveryPathMapper.updateByPrimaryKey(deliveryPath);
                    }
                }
            }
        }

        //组装信息
        List<DeliveryOrderMessage> deliveryOrderMessages = new ArrayList<>();
        //组装信息
        orderInfos.forEach(successOrder->{
            DeliveryOrderMessage deliveryOrderMessage = new DeliveryOrderMessage();
            deliveryOrderMessage.setOutOrderId(successOrder.getOrderNo());
            if(StringUtils.isNotBlank(contactIdParam) && deliveryTimeParam != null){
                deliveryOrderMessage.setSource(DistOrderSourceEnum.XM_MALL_TIMING.getCode());
                deliveryOrderMessage.setDeliveryTime(successOrder.getDeliveryTime().atStartOfDay());
                deliveryOrderMessage.setContactId(String.valueOf(successOrder.getContactId()));
            }else{
                deliveryOrderMessage.setSource(DistOrderSourceEnum.XM_MALL.getCode());
            }
            deliveryOrderMessage.setInterceptReason(interceptReason);
            deliveryOrderMessage.setInterceptReasonCode(interceptReasonCode);
            deliveryOrderMessages.add(deliveryOrderMessage);
        });

        afterSaleOrderNoList.forEach(afterSale->{
            DeliveryOrderMessage deliveryOrderMessage = new DeliveryOrderMessage();
            deliveryOrderMessage.setOutOrderId(afterSale.getAfterSaleNo());
            deliveryOrderMessage.setSource(DistOrderSourceEnum.XM_AFTER_SALE.getCode());
            deliveryOrderMessage.setInterceptReason(interceptReason);
            deliveryOrderMessage.setInterceptReasonCode(interceptReasonCode);

            deliveryOrderMessages.add(deliveryOrderMessage);
        });

        sampleIdList.forEach(sample->{
            DeliveryOrderMessage deliveryOrderMessage = new DeliveryOrderMessage();
            deliveryOrderMessage.setOutOrderId(String.valueOf(sample.getSampleId()));
            deliveryOrderMessage.setSource(DistOrderSourceEnum.XM_SAMPLE_APPLY.getCode());
            deliveryOrderMessage.setInterceptReason(interceptReason);
            deliveryOrderMessage.setInterceptReasonCode(interceptReasonCode);

            deliveryOrderMessages.add(deliveryOrderMessage);
        });

        TmsDeliveryEvent tmsDeliveryEvent = new TmsDeliveryEvent();
        tmsDeliveryEvent.setEventType(EventTypeEnum.INTERCEPT_DELIVERY.getCode());
        tmsDeliveryEvent.setEventTypeDesc(EventTypeEnum.INTERCEPT_DELIVERY.getName());
        tmsDeliveryEvent.setDeliveryOrderMessages(deliveryOrderMessages);
        mqProducer.send(MqConstants.Topic.TMS_DELIVERY,MqConstants.Tag.TMS_DELIVERY_EVENT,tmsDeliveryEvent);

        return CommonResult.ok();
    }

    @Override
    public CommonResult<List<DistOrderInterceptDTO>> queryOrderInterception(OrderInterceptionDTO orderInterceptionDTO) {
        List<String> orderNos = orderInterceptionDTO.getOrderNos();
        String contactIdParam = orderInterceptionDTO.getContactId();
        LocalDate deliveryTimeParam = orderInterceptionDTO.getDeliveryTime();
        boolean flag = orderInterceptionDTO.isCancelFlag();

        if(CollectionUtils.isEmpty(orderNos)){
            throw new DefaultServiceException("订单号不能为空");
        }
        //拦截失败的订单
        ArrayList<OrderInfoVo> errorOrderList = new ArrayList<>();
        //样品取消的id
        ArrayList<SampleSkuVO> sampleIdList = new ArrayList();
        //补发取消的售后单号
        ArrayList<GetAfterDataVO> afterSaleOrderNoList = new ArrayList();
        Integer deliveryPlanId = null;
        if(StringUtils.isNotBlank(contactIdParam) && deliveryTimeParam != null){
            DeliveryPlan deliveryPlan = new DeliveryPlan();
            deliveryPlan.setDeliveryTime(deliveryTimeParam);
            deliveryPlan.setOrderNo(orderNos.get(0));
            deliveryPlan.setContactId(Long.parseLong(contactIdParam));
            DeliveryPlan deliveryPlanData = deliveryPlanMapper.selectOne(deliveryPlan);
            deliveryPlanId = deliveryPlanData.getId();
        }
        //根据订单号查询需要拦截的订单信息
        List<OrderInfoVo> orderInfos = ordersMapper.getOrderInfoByOrderNos(orderNos,null,deliveryPlanId);
        //配送计划id为空，表示批量退单
        if(deliveryPlanId == null){
            //省心送订单只退当天的订单
            //把省心送订单配送日期不是今天的给排除掉
            Iterator<OrderInfoVo> orderIter = orderInfos.iterator();
            while(orderIter.hasNext()){
                OrderInfoVo order = orderIter.next();
                if(order.getType() == 1 && !order.getDeliveryTime().isEqual(LocalDate.now())){
                    if(!errorOrderList.contains(order)){
                        errorOrderList.add(order);
                    }
                    orderIter.remove();
                }
            }
        }
        //订单拦截截单时间和配送状态检查
        closeTimeSendStatusCheakNew(errorOrderList,orderInfos);
        //查询点位是否存在其他订单、补发，换货、退货退款、样品
        for (OrderInfoVo orderInfo : orderInfos) {
            LocalDate deliveryTime = orderInfo.getDeliveryTime();
            Integer contactId = orderInfo.getContactId();

            List<GetAfterDataVO> afterSaleDeliveryPathVos = new ArrayList<>();
            List<SampleSkuVO> sampleSkus = new ArrayList<>();
            if(flag){
                //查询售后
                afterSaleDeliveryPathVos = afterSaleDeliveryPathMapper.getAfterDataByDTAndCId(deliveryTime,contactId);
                //查询样品
                sampleSkus = sampleApplyMapper.getIdByDTAndCId(deliveryTime,contactId);
            }

            //样品需要取消，并且生成拦截入库信息
            if(!CollectionUtils.isEmpty(sampleSkus)){
                sampleIdList.addAll(sampleSkus);
            }
            //补发取消，并且生成拦截入库信息
            if(!CollectionUtils.isEmpty(afterSaleDeliveryPathVos)){
                afterSaleOrderNoList.addAll(afterSaleDeliveryPathVos);
            }
        }

        //组装信息
        List<DistOrderInterceptDTO> interceptDTOS = new ArrayList<>();
        orderInfos.forEach(successOrder->{
            DistOrderInterceptDTO distOrderInterceptDTO = new DistOrderInterceptDTO();
            distOrderInterceptDTO.setOutOrderId(successOrder.getOrderNo());
            if(StringUtils.isNotBlank(contactIdParam) && deliveryTimeParam != null){
                distOrderInterceptDTO.setSource(DistOrderSourceEnum.XM_MALL_TIMING);
            }else{
                distOrderInterceptDTO.setSource(DistOrderSourceEnum.XM_MALL);
            }
            distOrderInterceptDTO.setDeliveryTime(successOrder.getDeliveryTime());
            distOrderInterceptDTO.setState(0);
            distOrderInterceptDTO.setOuterContactId(String.valueOf(successOrder.getContactId()));

            interceptDTOS.add(distOrderInterceptDTO);
        });

        afterSaleOrderNoList.forEach(afterSale->{
            DistOrderInterceptDTO distOrderInterceptDTO = new DistOrderInterceptDTO();
            distOrderInterceptDTO.setOutOrderId(afterSale.getAfterSaleNo());
            distOrderInterceptDTO.setSource(DistOrderSourceEnum.XM_AFTER_SALE);
            distOrderInterceptDTO.setDeliveryTime(afterSale.getDeliveryTime());
            distOrderInterceptDTO.setState(0);
            distOrderInterceptDTO.setOuterContactId(String.valueOf(afterSale.getContactId()));

            interceptDTOS.add(distOrderInterceptDTO);
        });

        sampleIdList.forEach(sample->{
            DistOrderInterceptDTO distOrderInterceptDTO = new DistOrderInterceptDTO();
            distOrderInterceptDTO.setOutOrderId(String.valueOf(sample.getSampleId()));
            distOrderInterceptDTO.setSource(DistOrderSourceEnum.XM_SAMPLE_APPLY);
            distOrderInterceptDTO.setDeliveryTime(sample.getDeliveryTime());
            distOrderInterceptDTO.setState(0);
            distOrderInterceptDTO.setOuterContactId(String.valueOf(sample.getContactId()));

            interceptDTOS.add(distOrderInterceptDTO);
        });

        errorOrderList.forEach(errorOrder->{
            DistOrderInterceptDTO distOrderInterceptDTO = new DistOrderInterceptDTO();
            distOrderInterceptDTO.setOutOrderId(errorOrder.getOrderNo());
            if(StringUtils.isNotBlank(contactIdParam) && deliveryTimeParam != null){
                distOrderInterceptDTO.setSource(DistOrderSourceEnum.XM_MALL_TIMING);
            }else{
                distOrderInterceptDTO.setSource(DistOrderSourceEnum.XM_MALL);
            }
            distOrderInterceptDTO.setDeliveryTime(errorOrder.getDeliveryTime());
            distOrderInterceptDTO.setState(1);
            distOrderInterceptDTO.setFailedReason("当前时间在截单时间之前或配送状态已完成");
            distOrderInterceptDTO.setOuterContactId(String.valueOf(errorOrder.getContactId()));

            interceptDTOS.add(distOrderInterceptDTO);
        });

        List<DistOrderInterceptDTO> interceptDistinctDTOS = interceptDTOS.stream().distinct().collect(Collectors.toList());
        log.info("老模型manage查询订单拦截返回：{}",interceptDistinctDTOS);
        return CommonResult.ok(interceptDistinctDTOS);
    }

    /**
     * 补发的取消
     * @param afterSaleDeliveryPathVos
     * @param deliveryTime
     * @param contactId
     * @param storeNo
     * @param interceptTime
     * @param showFlag
     * @return
     */
    private List<String> cancleReissue(List<GetAfterDataVO> afterSaleDeliveryPathVos, LocalDate deliveryTime, Integer contactId, Integer storeNo, LocalDateTime interceptTime, Integer showFlag) {
        if(!CollectionUtils.isEmpty(afterSaleDeliveryPathVos)){
            //获取补发的数据集合
            List<GetAfterDataVO> reissueList = afterSaleDeliveryPathVos.stream()
                    .filter(afterSaleDeliveryPath -> AfterSaleHandleType.DELIVERY_GOODS.getType() == afterSaleDeliveryPath.getHandleType())
                    .collect(Collectors.toList());
            //更新补发的拦截标识
            List<Integer> afterSaleDeliveryDetailIdList = afterSaleDeliveryPathVos.stream().map(GetAfterDataVO::getAfterSaleDeliveryDetailId).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(afterSaleDeliveryDetailIdList)){
                afterSaleDeliveryDetailMapper.updateInterceptFlagByIdList(afterSaleDeliveryDetailIdList, InterceptFlagEnum.intercept.getCode(),interceptTime,showFlag);
            }
            //将拦截的数据插入到拦截表里面，并且生成拦截入库信息
            if(!CollectionUtils.isEmpty(reissueList)){
                for (GetAfterDataVO reissue : reissueList) {
                    String sku = reissue.getSku();
                    Integer quantity = reissue.getQuantity();
                    handlerDeliveryPathInterceptSku(sku,quantity,deliveryTime,contactId, interceptTime);
                }
                Map<String, List<GetAfterDataVO>> reissueMap = reissueList.stream().collect(Collectors.groupingBy(GetAfterDataVO::getOrderNo));
                Set<String> orderNoSet = reissueMap.keySet();
                for (String orderNo : orderNoSet) {
                    //拦截入库的任务生成
                    stockTaskService.stockTaskInterceptCreate(orderNo,null,storeNo,null,reissueMap.get(orderNo));
                }
            }

            List<String> afterSaleOrderNoList = reissueList.stream().map(GetAfterDataVO::getAfterSaleNo).collect(Collectors.toList());
            return afterSaleOrderNoList;
        }
        return null;
    }

    /**
     * 设置路线拦截状态
     * @param deliveryPathId
     * @param interceptType
     */
    private void setDeliveryPathInterceptType(Integer deliveryPathId, Integer interceptType) {
        if(deliveryPathId != null){
            DeliveryPath deliveryPath = new DeliveryPath();
            deliveryPath.setId(deliveryPathId);
            deliveryPath.setInterceptType(interceptType);
            deliveryPathMapper.updateByPrimaryKey(deliveryPath);
        }
    }

    /**
     * 将订单详情添加到拦截sku表中，并且生成拦截入库信息
     * @param orderNo
     * @param deliveryTime
     * @param contactId
     * @param storeNo
     * @param orderDeliveryPlanId
     * @param type
     * @param interceptTime
     */
    private void orderSkuDetailAddInterceptSku(String orderNo, LocalDate deliveryTime, Integer contactId, Integer storeNo,Integer orderDeliveryPlanId,Integer type,LocalDateTime interceptTime) {
        List<OrderItem> orderItems = new ArrayList<>();
        //省心送需要特殊处理
        if(1 == type){
            orderItems = orderItemMapper.queryItemListByOrderNoAndDPId(orderNo,orderDeliveryPlanId);
        }else{
            //根据订单编号查询订单详情信息
            orderItems = orderItemMapper.querySkuDetailItemList(orderNo);

            if(orderItems.size() > 1){
                //判断是否已经拦截过，因为一个订单可能存在多个配送计划
                int stockTaskNum = stockTaskMapper.selectByOrderNoAndTime(orderNo);
                if(stockTaskNum > 0){
                    return;
                }
            }
        }

        //去除退款的订单
        orderItems = orderItems.stream().filter(orderItem -> orderItem.getStatus() != OrderStatusEnum.DRAWBACK.getId()).collect(Collectors.toList());
        //将拦截的数据插入到拦截表里面，并且生成拦截入库信息
        if(!CollectionUtils.isEmpty(orderItems)){
            orderItems.stream().forEach(orderItem -> {
                handlerDeliveryPathInterceptSku(orderItem.getSku(),orderItem.getAmount(),deliveryTime,contactId,interceptTime);
            });
            //拦截入库的任务生成
            stockTaskService.stockTaskInterceptCreate(orderNo,orderItems,storeNo,null, null);
        }
    }

    /**
     * 取消样品逻辑
     * @param sampleSkus
     * @param deliveryTime
     * @param contactId
     * @param storeNo
     * @param interceptTime
     * @param showFlag
     * @return
     */
    private List<Integer> cancelSample(List<SampleSkuVO> sampleSkus, LocalDate deliveryTime, Integer contactId, Integer storeNo,
                                       LocalDateTime interceptTime, Integer showFlag) {
        if(sampleSkus.size() > 0){
            List<Integer> sampleIds = sampleSkus.stream().map(SampleSku::getId).collect(Collectors.toList());
            //更新拦截信息
            sampleSkuMapper.updateInterceptFlagByIdList(sampleIds, InterceptFlagEnum.intercept.getCode(),showFlag,interceptTime);
            //将拦截的数据插入到拦截表里面
            sampleSkus.stream().forEach(sampleSku ->{
                String sku = sampleSku.getSku();
                Integer amount = sampleSku.getAmount();
                handlerDeliveryPathInterceptSku(sku,amount,deliveryTime,contactId, interceptTime);
            });

            Map<Integer, List<SampleSkuVO>> sampleIdMap = sampleSkus.stream().collect(Collectors.groupingBy(SampleSkuVO::getSampleId));
            Set<Integer> sampleIdSet = sampleIdMap.keySet();
            for (Integer sampleId : sampleIdSet) {
                //拦截入库的任务生成
                stockTaskService.stockTaskInterceptCreate(null,null,storeNo,sampleIdMap.get(sampleId), null);
            }

            return sampleSkus.stream().map(SampleSku::getSampleId).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 处理拦截sku表数据逻辑
     * @param sku
     * @param amount
     * @param deliveryTime
     * @param contactId
     * @param interceptTime
     */
    private void handlerDeliveryPathInterceptSku(String sku, Integer amount, LocalDate deliveryTime, Integer contactId, LocalDateTime interceptTime) {

        //查询当前sku在这个点位是否已经有过拦截数据
        /*DeliveryPathInterceptSku dpInterceptSku = deliveryPathInterceptSkuMapper.selectByDTAndCIdAndSku(deliveryTime,contactId,sku);
        if(dpInterceptSku != null){
            DeliveryPathInterceptSku deliveryPathInterceptSku = new DeliveryPathInterceptSku();
            deliveryPathInterceptSku.setFiled(deliveryTime,contactId,sku,amount + dpInterceptSku.getQuantity());
            deliveryPathInterceptSku.setUpdateTime(LocalDateTime.now());

            deliveryPathInterceptSkuMapper.updateByPrimaryKey(deliveryPathInterceptSku);
        }else{*/
        DeliveryPathInterceptSku deliveryPathInterceptSku = new DeliveryPathInterceptSku();
        deliveryPathInterceptSku.setFiled(deliveryTime,contactId,sku,amount,interceptTime);
        deliveryPathInterceptSkuMapper.insert(deliveryPathInterceptSku);
        //}
    }

    /**
     * 订单拦截校验
     * @param errorOrderList
     * @param orderInfos
     */
    private void closeTimeSendStatusCheak(ArrayList<String> errorOrderList, List<OrderInfoVo> orderInfos) {
        DateTimeFormatter dfDate = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        Iterator<OrderInfoVo> orderInfoIterator = orderInfos.iterator();
        while(orderInfoIterator.hasNext()){
            OrderInfoVo orderInfo = orderInfoIterator.next();
            String closeTime = orderInfo.getCloseTime();
            LocalDateTime orderTime = orderInfo.getOrderTime();
            //给截单时间格式化为yyyy-MM-dd HH:mm:ss
            String closeDateTimeStr = orderTime.format(dfDate) + " " + closeTime;
            DateTime closeDateTime = DateUtil.parse(closeDateTimeStr);
            DateTime nowDate = DateUtil.date();

            //当前时间在截单时间之前和配送状态已完成表示拦截失败，移除数据，添加失败订单号
            if(!nowDate.after(closeDateTime) || orderInfo.getPathStatus() == PathStatusEnum.sendComplete.getCode()){
                errorOrderList.add(orderInfo.getOrderNo());
                orderInfoIterator.remove();
            }
        }
    }
    /**
     * 订单拦截校验
     * @param errorOrderList
     * @param orderInfos
     */
    private void closeTimeSendStatusCheakNew(ArrayList<OrderInfoVo> errorOrderList, List<OrderInfoVo> orderInfos) {
        DateTimeFormatter dfDate = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        Iterator<OrderInfoVo> orderInfoIterator = orderInfos.iterator();
        while(orderInfoIterator.hasNext()){
            OrderInfoVo orderInfo = orderInfoIterator.next();
            String closeTime = orderInfo.getCloseTime();
            LocalDateTime orderTime = orderInfo.getOrderTime();
            //给截单时间格式化为yyyy-MM-dd HH:mm:ss
            String closeDateTimeStr = orderTime.format(dfDate) + " " + closeTime;
            DateTime closeDateTime = DateUtil.parse(closeDateTimeStr);
            DateTime nowDate = DateUtil.date();

            //当前时间在截单时间之前和配送状态已完成表示拦截失败，移除数据，添加失败订单号
            if(!nowDate.after(closeDateTime) || orderInfo.getPathStatus() == PathStatusEnum.sendComplete.getCode()){
                errorOrderList.add(orderInfo);
                orderInfoIterator.remove();
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public AjaxResult chargeBack(ChargeBackDTO chargeBackDTO) {
        LocalDate deliveryTime = chargeBackDTO.getDeliveryTime();
        Integer contactId = chargeBackDTO.getContactId();
        Integer deliveryPathId = chargeBackDTO.getId();
        //判断是否是saas的数据
        DeliveryPathVO deliveryPathVO = deliveryPathMapper.selectById(deliveryPathId);
        if(deliveryPathVO.getBrandType() == 1){
            throw new DefaultServiceException("Saas订单不能拦截");
        }
        //判断有没有完成排线，完成排线给打回去
        Integer id = chargeBackDTO.getId();
        int count = tmsTaskMapper.selectTmsTaskByPathId(id);
        if(count > 0){
            throw new DefaultServiceException("已完成排线不能退单");
        }

        //查询相关的配送计划、售后、样品数量
        List<GetOrderDataVO> deliveryPlanVos = deliveryPlanMapper.getOtherOrderDataByDTAndCIdAndONo(deliveryTime, contactId, null);
        List<GetAfterDataVO> afterDataVos = afterSaleDeliveryPathMapper.getAfterDataByDTAndCId(deliveryTime, contactId);
        List<SampleSkuVO> sampleSkus = sampleApplyMapper.getIdByDTAndCId(deliveryTime,contactId);

        //修改配送计划、售后、样品数据全部设置为拦截状态，并插入拦截表
        setAllInterceptFlag(deliveryPlanVos,afterDataVos,sampleSkus);
        //没有回收任务的删除配送路线
        List<GetAfterDataVO> noRecycleAfterDataVos = afterDataVos.stream()
                .filter(afterDataVo -> afterDataVo.getType() != AfterSaleTypeEnum.send.getCode()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(noRecycleAfterDataVos)){
            deliveryPathMapper.deleteDeliveryPath(Arrays.asList(deliveryPathId));
        }else{
            //修改任务状态
            DeliveryPath deliveryPath = new DeliveryPath();
            deliveryPath.setId(deliveryPathId);
            deliveryPath.setDeliveryType(AfterSaleTypeEnum.recycle.getCode());
            deliveryPath.setInterceptType(InterceptTypeEnum.allIntercept.getCode());
            deliveryPathMapper.update(deliveryPath);
        }

        List<String> orderNoList = deliveryPlanVos.stream().map(GetOrderDataVO::getOrderNo).collect(Collectors.toList());
        List<Integer> sampleIdList = sampleSkus.stream().map(SampleSkuVO::getSampleId).collect(Collectors.toList());
        List<String> afterSaleNoList = afterDataVos.stream().map(GetAfterDataVO::getAfterSaleNo).collect(Collectors.toList());

       AjaxResult afterSaleOrder = orderService.createAfterSaleOrder(orderNoList, sampleIdList, (ArrayList<String>) afterSaleNoList, null, null, null);
        String code = afterSaleOrder.getCode();
        if(!"SUCCESS".equals(code)){
            throw new RuntimeException("商城处理失败");
        }
        return AjaxResult.getOK();
    }

    /**
     * 修改配送计划、售后、样品数据全部设置为拦截状态，并插入拦截表，生成拦截入库数据
     * @param deliveryPlanVos
     * @param afterDataVos
     * @param sampleSkus
     */
    private void setAllInterceptFlag(List<GetOrderDataVO> deliveryPlanVos, List<GetAfterDataVO> afterDataVos, List<SampleSkuVO> sampleSkus) {
        //拦截时间
        LocalDateTime interceptTime = LocalDateTime.now();
        if(!CollectionUtils.isEmpty(deliveryPlanVos)){
            deliveryPlanVos.stream().forEach(deliveryPlanVo ->{
                String orderNo = deliveryPlanVo.getOrderNo();
                LocalDate deliveryTime = deliveryPlanVo.getDeliveryTime();
                Integer contactId = deliveryPlanVo.getContactId();
                Integer storeNo = deliveryPlanVo.getStoreNo();
                Integer id = deliveryPlanVo.getId();
                Integer type = deliveryPlanVo.getType();
                //判断当前是否排线了，如果完成排线就展示，没有就不展示
                DeliveryPath deliveryPathInfo = deliveryPathMapper.selectOne(storeNo, deliveryTime, Long.parseLong(String.valueOf(contactId)));
                Integer showFlag = ShowFlagEnum.show.getCode();
                if(deliveryPathInfo == null || deliveryPathInfo.getPathStatus() == null){
                    showFlag = ShowFlagEnum.noShow.getCode();
                }

                //设置拦截状态
                if(id != null){
                    DeliveryPlan deliveryPlan = new DeliveryPlan();
                    deliveryPlan.setId(id);
                    deliveryPlan.setInterceptFlag(InterceptFlagEnum.intercept.getCode());
                    deliveryPlan.setInterceptTime(interceptTime);
                    deliveryPlan.setShowFlag(showFlag);
                    deliveryPlanMapper.updateById(deliveryPlan);
                }
                //将订单详情添加到拦截sku表中，并且生成拦截入库信息
                orderSkuDetailAddInterceptSku(orderNo,deliveryTime,contactId,storeNo,id,type,interceptTime);
            });
        }

        //售后需要特殊处理，因为存在回收
        if(!CollectionUtils.isEmpty(afterDataVos)){
            Stream<GetAfterDataVO> afterDataVOStream = afterDataVos.stream().filter(afterDataVo -> afterDataVo.getType() != AfterSaleTypeEnum.recycle.getCode());
            afterDataVOStream.forEach(afterDataVo ->{
                LocalDate deliveryTime = afterDataVo.getDeliveryTime();
                Integer contactId = afterDataVo.getContactId();
                Integer storeNo = afterDataVo.getStoreNo();
                Integer afterSaleDeliveryDetailId = afterDataVo.getAfterSaleDeliveryDetailId();

                //判断当前是否排线了，如果完成排线就展示，没有就不展示
                DeliveryPath deliveryPathInfo = deliveryPathMapper.selectOne(storeNo, deliveryTime, Long.parseLong(String.valueOf(contactId)));
                Integer showFlag = ShowFlagEnum.show.getCode();
                if(deliveryPathInfo.getPathStatus() == null){
                    showFlag = ShowFlagEnum.noShow.getCode();
                }
                //设置拦截状态
                if(afterSaleDeliveryDetailId != null){
                    AfterSaleDeliveryDetail afterSaleDeliveryDetail = new AfterSaleDeliveryDetail();
                    afterSaleDeliveryDetail.setId(afterSaleDeliveryDetailId);
                    afterSaleDeliveryDetail.setInterceptFlag(InterceptFlagEnum.intercept.getCode());
                    afterSaleDeliveryDetail.setInterceptTime(interceptTime);
                    afterSaleDeliveryDetail.setShowFlag(showFlag);
                    afterSaleDeliveryDetailMapper.updateById(afterSaleDeliveryDetail);
                }
                //将订单详情添加到拦截sku表中，并且生成拦截入库信息
                cancleReissue(Arrays.asList(afterDataVo),deliveryTime,contactId,storeNo, interceptTime, showFlag);
            });
        }

        if(!CollectionUtils.isEmpty(sampleSkus)){
            sampleSkus.stream().forEach(sampleSku ->{
                Integer id = sampleSku.getId();
                LocalDate deliveryTime = sampleSku.getDeliveryTime();
                Integer contactId = sampleSku.getContactId();
                Integer storeNo = sampleSku.getStoreNo();

                //判断当前是否排线了，如果完成排线就展示，没有就不展示
                DeliveryPath deliveryPathInfo = deliveryPathMapper.selectOne(storeNo, deliveryTime, Long.parseLong(String.valueOf(contactId)));
                Integer showFlag = ShowFlagEnum.show.getCode();
                if(deliveryPathInfo.getPathStatus() == null){
                    showFlag = ShowFlagEnum.noShow.getCode();
                }
                //设置拦截状态
                if(id != null){
                    SampleSku sampleSkuVo = new SampleSku();
                    sampleSkuVo.setId(id);
                    sampleSkuVo.setInterceptFlag(InterceptFlagEnum.intercept.getCode());
                    sampleSkuVo.setInterceptTime(interceptTime);
                    sampleSkuVo.setShowFlag(showFlag);
                    sampleSkuMapper.updateById(sampleSkuVo);
                }
                //将订单详情添加到拦截sku表中，并且生成拦截入库信息
                cancelSample(Arrays.asList(sampleSku),deliveryTime,contactId,storeNo, interceptTime, showFlag);
            });
        }
    }

    @Override
    public AjaxResult isHaveExchange(Integer pathId) {
        Integer num = deliveryPathMapper.selectExchangeNum(pathId);
        if(num > 0){
            return AjaxResult.getOK(true);
        }
        return AjaxResult.getOK(false);
    }

    @Override
    public List<DeliveryPathVO> selectDeliveryPath(DeliveryPath deliveryPath) {
        return deliveryPathMapper.selectDeliveryByStoreNo(deliveryPath);
    }

    @Override
    public List<DeliveryPathVO> selectDeliveryPathDetail(DeliveryPath deliveryPath) {
        return deliveryPathMapper.selectDeliveryPathByStoreNo(deliveryPath);
    }

    @Override
    public List<String> selectMatePathByDistricts(DeliveryPathVO pathVO) {
        return deliveryPathMapper.selectMatePathByDistricts(pathVO);
    }
}
