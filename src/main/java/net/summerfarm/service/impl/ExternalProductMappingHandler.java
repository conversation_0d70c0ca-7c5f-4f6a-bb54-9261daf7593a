package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.ExternalProductMappingEnum;
import net.summerfarm.mapper.product.ExternalProductMappingMapper;
import net.summerfarm.model.ExternalProductMapping;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description
 * @Date 2024/11/25 15:06
 * @<AUTHOR>
 */
@Slf4j
@Component
public class ExternalProductMappingHandler {

    @Resource
    private ExternalProductMappingMapper externalProductMappingMapper;

    public void handleExternalProductMapping(String xmSkuCode, String externalSkuCode) {
        try {
            if (StringUtils.isBlank(xmSkuCode) || StringUtils.isBlank(externalSkuCode)) {
                return;
            }
            ExternalProductMapping externalProductMapping = ExternalProductMapping.builder()
                    .type(ExternalProductMappingEnum.SKU.getType())
                    .internalValue(xmSkuCode)
                    .externalValue(externalSkuCode)
                    .build();
            externalProductMappingMapper.insertSelective(externalProductMapping);
        } catch (Exception e) {
            log.error("pop商品绑定外部平台品失败，不影响鲜沐商品创建，请及时确认数据 xmSkuCode:{}, externalSkuCode:{}", xmSkuCode, externalSkuCode, e);

        }

    }

}
