package net.summerfarm.service.impl;

import net.summerfarm.common.util.MathUtil;
import net.summerfarm.enums.StoreRecordType;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.BiOnSaleTime;
import net.summerfarm.model.domain.BiStockUp;
import net.summerfarm.model.vo.AreaStoreVO;
import net.summerfarm.model.vo.BiOnSaleTimeVO;
import net.summerfarm.model.vo.BiStockUpConfigVO;
import net.summerfarm.model.vo.DaySaleQuantityVO;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.service.BiStockUpService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BiStockUpServiceImpl extends BaseService implements BiStockUpService {

    @Resource
    private BiStockUpMapper biStockUpMapper;

    @Resource
    private BiStockUpConfigMapper biStockUpConfigMapper;

    @Resource
    private BiOnSaleTimeMapper biOnSaleTimeMapper;

    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private StoreRecordMapper storeRecordMapper;

    @Override
    public void stockUpUpdate() {
        logger.info("备货量数据更新开始:{}", LocalDateTime.now().toString());
        LocalDate endDate = LocalDate.now().minusDays(1);
        LocalDate startDate = LocalDate.now().minusDays(7);

        List<BiStockUpConfigVO> configs = biStockUpConfigMapper.selectAll();
        for (BiStockUpConfigVO config: configs) {

            BiStockUp insert = new BiStockUp();
            insert.setSku(config.getSku());
            insert.setAddDate(LocalDate.now());
            insert.setAreaNo(config.getAreaNo());

            // 当前中心仓对应的城配仓
            List<Integer> areaNos = config.getAreaStoreVOS().stream().map(AreaStoreVO::getAreaNo).collect(Collectors.toList());
            areaNos.add(config.getAreaNo());

            // 上架天数
            BigDecimal onSaleDay = calOnSaleDay(areaNos, config.getSku(), startDate, endDate);
            // 各种出库数据
            List<DaySaleQuantityVO> daySaleQuantityVOS = calQuantity(config.getSku(), areaNos, startDate, endDate);
            Integer sevenAvgQuantity = onSaleDay.compareTo(BigDecimal.ZERO) == 0 ? 0 :
                    BigDecimal.valueOf(daySaleQuantityVOS.stream().mapToInt(DaySaleQuantityVO::getAmount).sum()).divide(onSaleDay, 0, BigDecimal.ROUND_HALF_EVEN).intValue();
            insert.setSevenAvgQuantity(sevenAvgQuantity);

            // 七天平均转换出库量
            insert.setSevenOtherQuantity(daySaleQuantityVOS.stream().mapToInt(DaySaleQuantityVO::getOutQuantity).sum() / 7);

            Map<LocalDate, Integer> map = daySaleQuantityVOS.stream().collect(Collectors.toMap(DaySaleQuantityVO::getOrderDate, o -> {
                return o.getAmount() + o.getOutQuantity();
            }));

            List<BigDecimal> subValue = new ArrayList<>();
            for (int i = 0; i < endDate.toEpochDay() - startDate.toEpochDay(); i++) {
                Integer today = map.get(startDate.plusDays(i));
                Integer lastDay = map.get(startDate.plusDays(i + 1));
                subValue.add(BigDecimal.valueOf(today - lastDay));
            }
            Integer stdv = MathUtil.stDev(subValue).multiply(BigDecimal.valueOf(2)).intValue();
            Integer avgQuantity = daySaleQuantityVOS.stream().mapToInt(o -> o.getOutQuantity() + o.getAmount()).sum() / 7;
            insert.setStockUpQuantity(stdv + avgQuantity);
            biStockUpMapper.insert(insert);
        }
        logger.info("备货量数据更新结束:{}", LocalDateTime.now().toString());
    }

    // 计算上架天数
    @Deprecated
    private BigDecimal calOnSaleDay(List<Integer> areaNos, String sku, LocalDate startDate, LocalDate endDate) {
        BiOnSaleTimeVO select = new BiOnSaleTimeVO();
        select.setAreaNos(areaNos);
        select.setStartDate(startDate);
        select.setEndDate(endDate);
        select.setSku(sku);

        List<BiOnSaleTime> biOnSaleTimes = new ArrayList<>();

        if (!CollectionUtils.isEmpty(biOnSaleTimes)) {
            biOnSaleTimes = biOnSaleTimes.stream().filter(o -> o.getOnSaleTime().compareTo(BigDecimal.ZERO) == 1).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(biOnSaleTimes)) {
                //上架天数 = 所有城市的上架时常总和/24小时/上架城市数量
                BigDecimal sum = BigDecimal.valueOf(biOnSaleTimes.stream().mapToDouble(o -> o.getOnSaleTime().doubleValue()).sum());
                return sum.divide(BigDecimal.valueOf(biOnSaleTimes.size() * 24), 4, BigDecimal.ROUND_HALF_EVEN);
            }
        }

        return BigDecimal.ZERO;
    }

    // 计算 销量、转换出库、调拨出库、出样出库
    private List<DaySaleQuantityVO> calQuantity(String sku, List<Integer> areaNos, LocalDate startDate, LocalDate endDate) {

        /*ordersMapper.selectEveryDayQuantity(sku, areaNos, startDate, endDate)
                .stream()
                .collect(Collectors.toMap(DaySaleQuantityVO::getOrderDate, DaySaleQuantityVO::getAmount))*/
        Map<LocalDate, Integer> salesMap = new HashMap<>();

        List<Integer> types = Arrays.asList(StoreRecordType.TRANSFER_OUT.getId(), StoreRecordType.STORE_ALLOCATION_OUT.getId(), StoreRecordType.DEMO_OUT.getId());

        Map<LocalDate, Integer> outQuantityMap = biStockUpMapper.selectOutQuantity(sku, areaNos, types, startDate, endDate)
                .stream()
                .collect(Collectors.toMap(DaySaleQuantityVO::getOrderDate, DaySaleQuantityVO::getOutQuantity));

        List<DaySaleQuantityVO> result = new ArrayList<>();
        for (int i = 0; i <= endDate.toEpochDay() - startDate.toEpochDay(); i++) {
            LocalDate orderDate = startDate.plusDays(i);
            DaySaleQuantityVO daySaleQuantityVO = new DaySaleQuantityVO();
            daySaleQuantityVO.setOrderDate(orderDate);
            daySaleQuantityVO.setAmount(salesMap.get(orderDate) == null ? 0 : salesMap.get(orderDate));
            daySaleQuantityVO.setOutQuantity(outQuantityMap.get(orderDate) == null ? 0 : outQuantityMap.get(orderDate));
            result.add(daySaleQuantityVO);
        }
        return result;
    }


}
