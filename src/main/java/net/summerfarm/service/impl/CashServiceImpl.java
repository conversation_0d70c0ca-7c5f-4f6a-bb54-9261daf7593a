package net.summerfarm.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.enums.CashRecordType;
import net.summerfarm.enums.CashStatusEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.vo.CashVO;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.model.domain.Cash;
import net.summerfarm.model.domain.CashDetail;
import net.summerfarm.model.domain.CashRecord;
import net.summerfarm.model.domain.RedPack;
import net.summerfarm.service.CashService;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-06-28
 * @description 提现实现类
 */
@Service
public class CashServiceImpl extends BaseService implements CashService {
    @Resource
    private CashMapper cashMapper;
    @Resource
    private RedPackMapper redPackMapper;
    @Resource
    private CashRecordMapper cashRecordMapper;
    @Resource
    private AdminDataPermissionMapper permissionMapper;
    @Resource
    private MerchantSubAccountMapper merchantSubAccountMapper;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public Map<String, BigDecimal> selectCountAmount() {
        Map<String, BigDecimal> result = new HashMap<>(2);
        if (permissionMapper.hasAllDataPermission(getAdminId())) {
            result.put("unAuditAmount", cashMapper.countUnAuditAmount(null));
            result.put("sameDayAmount", cashMapper.countSameDayAmount(null));
            result.put("unApplyAmount", cashMapper.countUnApplyAmount(null));
            result.put("totalRpAmount", cashMapper.countTotalRpAmount(null));
        } else {
            result.put("unAuditAmount", cashMapper.countUnAuditAmount(getAdminId()));
            result.put("sameDayAmount", cashMapper.countSameDayAmount(getAdminId()));
            result.put("unApplyAmount", cashMapper.countUnApplyAmount(getAdminId()));
            result.put("totalRpAmount", cashMapper.countTotalRpAmount(getAdminId()));
        }
        return result;
    }

    @Override
    public AjaxResult select(int pageIndex, int pageSize, CashVO cashVO) {
        PageInfo<CashDetail> info = PageInfoHelper.createPageInfo(pageIndex, pageSize, () -> cashMapper.selectByCashVO(cashVO));
        return AjaxResult.getOK(info);
    }

    @Override
    public AjaxResult selectRecord(int pageIndex, int pageSize, CashVO cashVO) {
        PageInfo<CashDetail> info = PageInfoHelper.createPageInfo(pageIndex, pageSize, () -> cashMapper.selectRecordByCashVO(cashVO));
        return AjaxResult.getOK(info);
    }

    @Override
    public AjaxResult selectRedPackRecord(Integer id, Long accountId, Integer type) {
        List<RedPack> list;
        if (Objects.equals(type, 0)) {
            list = redPackMapper.selectByCashId(id);
        } else {
            list = redPackMapper.selectByMIdAndAccountId(id.longValue(), accountId);
        }
        return AjaxResult.getOK(list);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    @XmLock(waitTime = 1000 * 60, key = "(CashService.audit):{cashId}")
    public void audit(Integer cashId, Integer flag, String remark) {
        try {
            Cash cash = cashMapper.selectByPrimaryKey(cashId);
            if (Objects.equals(cash.getStatus(), CashStatusEnum.IN_AUDIT.getStatus())) {
                logger.info("提现id:{}，审核：{}，备注：{}", cashId, Objects.equals(1, flag) ? "通过" : "不通过", remark);
                cash.setAuditAdmin(getAdminId());
                cash.setAuditTime(new Date());
                cash.setAuditRemark(remark);
                if (Objects.equals(1, flag)) {
                    //审核通过
                    cash.setStatus(CashStatusEnum.IN_PAY.getStatus());
                } else {
                    //审核拒绝
                    cash.setStatus(CashStatusEnum.AUDIT_FAIL.getStatus());

                    //归还用户红包金额
                    returnRedPack(cash.getmId(), cash.getAccountId(), cash.getAmount(), cashId);
                }
                cashMapper.updateByPrimaryKeySelective(cash);
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.error("提现审核失败了, id:{}，审核：{}，备注：{}", cashId, flag, remark, e);
        }
    }

    /**
     * 退还用户红包金额
     *
     * @param mId    mId
     * @param accountId    accountId
     * @param amount 金额
     * @param cashId 提现ID
     */
    private void returnRedPack(Long mId, Long accountId, BigDecimal amount, Integer cashId) {
        CashRecord record = new CashRecord();
        record.setmId(mId);
        record.setAccountId(accountId);
        record.setAmount(amount);
        record.setType(CashRecordType.CASH_OUT_FAIL.getType());
        record.setRemark(CashRecordType.CASH_OUT_FAIL.getDescription());
        record.setBusinessId(cashId);
        record.setCreateTime(new Date());
        cashRecordMapper.insertSelective(record);

        merchantSubAccountMapper.increaseCashAmount(accountId, amount);
    }
}
