package net.summerfarm.service.impl;

import net.summerfarm.common.util.SnowflakeUtil;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.mapper.manage.MerchantMapper;
import net.summerfarm.mapper.manage.RechargeRecordMapper;
import net.summerfarm.model.domain.Merchant;
import net.summerfarm.model.domain.RechargeRecord;
import net.summerfarm.service.RechargeRecordService;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

@Service
public class RechargeRecordServiceImpl extends BaseService implements RechargeRecordService {

    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private RechargeRecordMapper rechargeRecordMapper;


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @XmLock(waitTime = 1000 * 60, key = "(RechargeRecordService.insert):{mId}")
    public String insert(Long mId, Integer type, String recordNo, BigDecimal amount) {
        Merchant merchant = merchantMapper.selectByPrimaryKey(mId);
        try{
            BigDecimal newAmount = merchant.getRechargeAmount().add(amount);
            if (newAmount.compareTo(BigDecimal.ZERO) == -1){
                throw new DefaultServiceException("客户"+merchant.getMname()+"余额不足!");
            }
            RechargeRecord insert = new RechargeRecord();
            insert.setmId(mId);
            insert.setType(type);
            insert.setRecordNo(recordNo);
            insert.setOldAmount(merchant.getRechargeAmount());
            insert.setNewAmount(newAmount);
            insert.setAddtime(LocalDateTime.now());

            String rechargeRecordNo = String.valueOf(SnowflakeUtil.nextId());
            RechargeRecord selectKey = new RechargeRecord();
            selectKey.setRechargeRecordNo(rechargeRecordNo);
            RechargeRecord record = rechargeRecordMapper.selectOne(selectKey);
            if (record != null){
                throw new DefaultServiceException("余额变动编号重复!");
            }
            insert.setRechargeRecordNo(rechargeRecordNo);
            rechargeRecordMapper.insert(insert);
            merchantMapper.updateRechargeAmount(amount,mId);
            logger.info("客户:{}余额变更成功,余额为：{}",merchant.getMname(),newAmount);
            return insert.getRechargeRecordNo();
        }catch (DataIntegrityViolationException e){
            throw new DefaultServiceException("客户"+merchant.getMname()+"余额不足!");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    @XmLock(waitTime = 1000 * 60, key = "(RechargeRecordService.update):{mId}")
    public String update(Long mId, Integer type, String recordNo, BigDecimal amount) {
        Merchant merchant = merchantMapper.selectByPrimaryKey(mId);
        try{
            BigDecimal newAmount = merchant.getRechargeAmount().subtract(amount);
            if (Objects.equals(newAmount.compareTo(BigDecimal.ZERO),-1)){
                throw new DefaultServiceException("客户" + merchant.getMname() + "余额不足!");
            }
            RechargeRecord update = new RechargeRecord();
            update.setmId(mId);
            update.setType(type);
            update.setRecordNo(recordNo);
            update.setOldAmount(merchant.getRechargeAmount());
            update.setNewAmount(newAmount);
            update.setAddtime(LocalDateTime.now());

            String rechargeRecordNo = String.valueOf(SnowflakeUtil.nextId());
            RechargeRecord selectKey = new RechargeRecord();
            selectKey.setRechargeRecordNo(rechargeRecordNo);
            RechargeRecord record = rechargeRecordMapper.selectOne(selectKey);
            if (!ObjectUtils.isEmpty(record)){
                throw new DefaultServiceException("余额变动编号重复!");
            }
            update.setRechargeRecordNo(rechargeRecordNo);
            rechargeRecordMapper.insert(update);
            merchantMapper.subtractRechargeAmount(amount,mId);
            logger.info("客户:{}余额变更成功,余额为：{}",merchant.getMname(),newAmount);
            return update.getRechargeRecordNo();
        }catch (DataIntegrityViolationException e){
            throw new DefaultServiceException("客户"+merchant.getMname()+"余额不足!");
        }
    }
}
