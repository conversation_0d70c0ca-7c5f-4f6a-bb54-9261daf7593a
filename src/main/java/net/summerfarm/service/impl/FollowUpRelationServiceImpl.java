package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.common.util.BeanCopyUtil;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.common.util.es.EsIndexContext;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.*;
import net.summerfarm.es.EsClientPoolUtil;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.offline.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.NearbyReq;
import net.summerfarm.model.input.PrivateSeaInput;
import net.summerfarm.model.vo.*;
import net.summerfarm.service.AreaService;
import net.summerfarm.service.FollowUpRelationService;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.GeoDistanceSortBuilder;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Package: net.summerfarm.service.impl
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/4/27
 */
@Service
public class FollowUpRelationServiceImpl extends BaseService implements FollowUpRelationService {

    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private BDExtMapper bdExtMapper;
    @Resource
    private FollowWhiteListMapper followWhiteListMapper;
    @Resource
    private MerchantCluePoolMapper merchantCluePoolMapper;
    @Resource
    private MerchantLeadsMapper merchantLeadsMapper;
    @Lazy
    @Resource
    private AreaService areaService;
    @Resource
    private MerchantMonthPurmoneyMapper merchantMonthPurmoneyMapper;
    @Resource
    private CrmRelationRecordMapper crmRelationRecordMapper;
    @Resource
    private CrmMerchantDayGmvMapper crmMerchantDayGmvMapper;
    @Resource
    private CrmMerchantMonthGmvMapper crmMerchantMonthGmvMapper;
    @Resource
    DataSynchronizationInformationMapper dataSynchronizationInformationMapper;
    @Resource
    private CrmMerchantDayLabelMapper crmMerchantDayLabelMapper;


    private final String BATCH_ADD = "批量操作";
    /**
     * 批量修改bd时,选择无跟进人,此时adminId传值为0
     */
    private final Integer NO_FOLLOW_UP = 0;
    /**
     * bd释放类型
     */
    private final Integer RELEASE_TYPE = 1;

    /**
     * 排序规则:倒计时降序
     */
    private final int DANGER_DAY_DESCENDING_ORDER = 3;
    /**
     * 排序规则:倒计时升序
     */
    private final int DANGER_DAY_ASCENDING_ORDER = 2;
    /**
     * 排序规则:gmv升序
     */
    private final int GMV_ASCENDING_ORDER = 0;


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult reassign(List<Long> mIds, Integer toAdminId, String reason,Integer type) {
        // 普通bd只可做私海释放操作，不可添加及修改
        if(Objects.nonNull(toAdminId)){
            if(!(super.isAreaSA() || super.isSA() || super.isSaleSA())){
                return AjaxResult.getError(ResultConstant.UNAUTHORIZED,"您无此权限，请联系管理员授权");
            }
            reason = this.BATCH_ADD;
        }
        for (Long mId : mIds) {
            //后台不可以跨白名单操作
            if(!Objects.equals(RELEASE_TYPE,type)){
                FollowWhiteList queryFollowWhite = followWhiteListMapper.queryFollowWhiteListOne(mId);
                if(Objects.nonNull(queryFollowWhite)){
                    return AjaxResult.getError("mId = " + mId +"为白名单客户，不可释放");
                }
            }
            reassignOne(mId, toAdminId, reason);
        }
        return AjaxResult.getOK();
    }

    @Transactional(rollbackFor = Exception.class)
    void reassignOne(Long mId, Integer toAdminId, String reason) {
        FollowUpRelation update = new FollowUpRelation(mId, toAdminId,null);
        update.setReason(reason);
        //toAdminId 为空或者为0 代表 释放BD
        if (Objects.isNull(toAdminId) || Objects.equals(NO_FOLLOW_UP,toAdminId)) {
            update.setReassign(true);
            update.setmId(mId);
        } else {
            //更换跟进bd
            check(mId, toAdminId);
            Admin admin = adminMapper.selectByPrimaryKey(toAdminId);
            update.setAdminName(admin.getRealname());
            update.setReason("主管分配");
            update.setReassign(false);
        }
        this.updateAndInsertFollow(update);
    }

    /**
     * 检查私海池是否已达上限
     * @param mId 商户id
     * @param toAdminId bd的id
     */
    private void check(Long mId, Integer toAdminId) {
        // 白名单,大客户不计算私海池空间
        int whiteNum = followWhiteListMapper.selectNumByBd(toAdminId,null);
        FollowUpRelationVO selectFollowVO = new FollowUpRelationVO();
        selectFollowVO.setReassign(false);
        selectFollowVO.setAdminId(toAdminId);
        selectFollowVO.setSize(MerchantSizeEnum.SINGGLE_STORE.getValue());
        List<FollowUpRelation> privateNum = followUpRelationMapper.selectByAreaNo(selectFollowVO);

        Merchant merchant = merchantMapper.selectByPrimaryKey(mId);
        BDExt selectBDExt = new BDExt();
        selectBDExt.setAdminId(toAdminId);
        selectBDExt.setAreaNo(merchant.getAreaNo());
        selectBDExt.setStatus(1);
        BDExt bdExt = bdExtMapper.selectOne(selectBDExt);
        if (Objects.isNull(bdExt)) {
            throw new DefaultServiceException("BD没有配置,请联系主管");
        }
        if(CollectionUtils.isEmpty(privateNum)){
            return;
        }
        boolean flag = bdExt.getPrivateNum() <= privateNum.size() - whiteNum;
        if (Objects.equals(MerchantSizeEnum.SINGGLE_STORE.getValue(),merchant.getSize()) && flag) {
            throw new DefaultServiceException("超过私海池上限");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult  bdAssign(List<Long> mIds) {
        for (Long mId : mIds) {
            check(mId, getAdminId());
            FollowWhiteList queryFollowWhite = followWhiteListMapper.queryFollowWhiteListOne(mId);
            if(queryFollowWhite != null){
                return AjaxResult.getErrorWithMsg(mId + "在白名单中,不能加入私海");
            }
            FollowUpRelation selectKeys = new FollowUpRelation();
            selectKeys.setmId(mId);
            FollowUpRelation record = followUpRelationMapper.selectOne(selectKeys);
            if (null != record && record.getReassign() != null && !record.getReassign()) {
                return AjaxResult.getErrorWithMsg(mId + "存在对应BD:" + record.getAdminName());
            }
            // 默认无新购买标签
            int followType = FollowUpRelationEnum.FollowType.FALSE.ordinal();
            LocalDateTime firstDay = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).with(TemporalAdjusters.firstDayOfMonth());
            LocalDateTime startTime = (null!=record && firstDay.isBefore(record.getReassignTime())) ? record.getReassignTime() : firstDay;
            int order = ordersMapper.count(startTime, LocalDateTime.now(), mId);
            int timingOrder = ordersMapper.countTimingNotDelivery(mId);
            FollowUpRelation followUpRelation = new FollowUpRelation(mId, getAdminId(), getAdminName());
            // 省心送订单数大于0
            if (timingOrder > 0) {
                followType = FollowUpRelationEnum.FollowType.CANCEL.ordinal();
                followUpRelation.setTimingFollowType(FollowUpRelationEnum.TimingFollowType.TRUE.ordinal());
            }
            // 订单数大于0
            if (order > 0) {
                followType = FollowUpRelationEnum.FollowType.TRUE.ordinal();
            }
            // 2024-12-16 去掉公海期间购买不能加入私海限制
//            if (followType == FollowUpRelationEnum.FollowType.TRUE.ordinal()) {
//                return AjaxResult.getErrorWithMsg(mId + "公海期间购买,不能加入私海");
//            }
            followUpRelation.setReason("主动选择");
            followUpRelation.setReassign(false);
            this.updateAndInsertFollow(followUpRelation);
        }
        return AjaxResult.getOK();
    }


    @Override
    public AjaxResult selectOpenSea(int pageIndex, int pageSize, MerchantVO select) {
        List<MerchantVO> merchantVOS = new ArrayList<>(pageSize);
        //公海池
        if (select.getState() == 0) {
            DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_GMV.getTableName());
            DataSynchronizationInformation labelThisDay = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_LABEL.getTableName());
            PageHelper.startPage(pageIndex, pageSize);
            PageInfo<MerchantVO> pageInfo;
            if(Objects.nonNull(select.getMerchantLabel())){
                // 处理查询条件
                String[] merchantLabel = select.getMerchantLabel().trim().split(Global.SEPARATING_SYMBOL);
                List<String> merchantLabelList = Arrays.asList(merchantLabel);
                select.setMerchantLabelList(merchantLabelList);
                // 获取满足条件的商户id,循环pageSize次
                merchantVOS = crmMerchantDayLabelMapper.selectMidListByInput(select, labelThisDay.getDateFlag());
                // 删除生产库与离线库数据不一致的数据
                List<MerchantVO> deleteMerchantInfoList = new ArrayList<>(pageSize);
                for (MerchantVO merchant : merchantVOS) {
                    select.setmId(merchant.getmId());
                    List<MerchantVO> merchantInfoList = this.queryMerchantInfoDetail(select,thisMonth);
                    if(CollectionUtil.isNotEmpty(merchantInfoList)){
                        BeanCopyUtil.copyProperties(merchantInfoList.get(0),merchant);
                    }else {
                        deleteMerchantInfoList.add(merchant);
                    }
                }
                merchantVOS.removeAll(deleteMerchantInfoList);
                // 处理贴上标签但是为私海的情况
                pageInfo = PageInfoHelper.createPageInfo(merchantVOS);
                if(CollectionUtil.isNotEmpty(merchantVOS)){
                    pageInfo.setIsLastPage(false);
                }
            }else {
                merchantVOS = this.queryMerchantInfoDetail(select,thisMonth);
                pageInfo = PageInfoHelper.createPageInfo(merchantVOS);
            }
            return AjaxResult.getOK(pageInfo);
           //客户动态 从公海分配出去的
        } else if (select.getState() == 1) {
            Integer adminId = null;
            if (!isSA()) {
                adminId = getAdminId();
            }
            PageHelper.startPage(pageIndex, pageSize);
            merchantVOS = merchantMapper.selectPrivateSea(Global.getStartTime(), select.getReason(), select.getMname(), adminId, null);
            return AjaxResult.getOK(PageInfoHelper.createPageInfo(merchantVOS));
            //每天新调入的bd 新掉入公海的
        } else if (select.getState() == 2) {
            PageHelper.startPage(pageIndex, pageSize);
            merchantVOS = merchantMapper.selectOpenSea(Global.getStartTime(), select.getReason(), select.getMname(), null, null,select.getArea(),select.getCity(),select.getProvince(),select.getAddress(),null,null);
            return AjaxResult.getOK(PageInfoHelper.createPageInfo(merchantVOS));
            //倒计时
        } else if (select.getState() == 3) {
            Integer adminId = getAdminId();
            PageHelper.startPage(pageIndex, pageSize);
            merchantVOS = merchantMapper.selectPrivateSea(null, null, select.getMname(), adminId, null);
            if (!CollectionUtils.isEmpty(merchantVOS)) {
                for (MerchantVO merchantVO : merchantVOS) {
                    this.notOrderOrFollow(merchantVO);
                }
            }
            return AjaxResult.getOK(PageInfoHelper.createPageInfo(merchantVOS));
        } else {
            return AjaxResult.getError();
        }

    }

    /**
     * 查询公海客户信息
     * @param select 查询条件
     * @param thisMonth 离线库数据最新更新时间
     * @return 公海客户信息
     */
    private List<MerchantVO> queryMerchantInfoDetail(MerchantVO select,DataSynchronizationInformation thisMonth) {
        select.setAddTime(DateUtils.getAtBeginningOfMonth());
        List<MerchantVO> merchantVOS = merchantMapper.selectOpenSeas(select);
        if(Objects.isNull(merchantVOS)){
            return Collections.emptyList();
        }
        merchantVOS.forEach(el->{
            PrivateSeaVO gmvDate = crmMerchantDayGmvMapper.selectByMid(el.getmId().intValue(), thisMonth.getDateFlag());
            el.setThisMonthGmv(BigDecimal.ZERO);
            if(Objects.nonNull(gmvDate)){
                el.setThisMonthGmv(gmvDate.getThisMonthGmv());
                el.setCoreMerchantTag(gmvDate.getCoreMerchantTag());
            }
            // 下单周期预警
            el.setReassign(select.getReassign());
            this.notOrderOrFollow(el);
            boolean orderCycleWarn = false;
            if(Objects.nonNull(el.getNotOrder())){
                int orderCycle = this.averageOrderCycle(el.getmId());
                if(orderCycle == 0){
                    orderCycle = el.getNotOrder();
                }
                orderCycleWarn =  el.getNotOrder() > orderCycle || el.getNotOrder() > 30;
            }
            el.setOrderCycleWarn(orderCycleWarn);
        });
        // 根据前台条件筛选本月下单周期预警用户
        if(!Objects.isNull(select.isOrderCycleWarn())){
            merchantVOS = merchantVOS.stream().filter(s -> Objects.equals(select.isOrderCycleWarn(), s.isOrderCycleWarn())).collect(Collectors.toList());
        }
        return merchantVOS;
    }

    /**
     * 计算拜访天数和未下单天数
     * @param merchantVO 商户信息,其中reassign为1表示请求来源为公海
     */
    @Override
    public void notOrderOrFollow(MerchantVO merchantVO) {
        List<ReleaseVO> releaseVOS;
        if(Objects.equals(merchantVO.getReassign(),1)){
            releaseVOS = followUpRelationMapper.getMerchaWithOpenSea(merchantVO.getmId());
        }else {
            releaseVOS = followUpRelationMapper.getMerchaWithBD(merchantVO.getmId());
        }
        // 设置未拜访天数与未下单天数
        if (!CollectionUtils.isEmpty(releaseVOS)) {
            LocalDate now = LocalDate.now();
            if(Objects.nonNull(releaseVOS.get(0).getFollowTime())){
                LocalDate orderTime = Global.getStartTimeByDate(releaseVOS.get(0).getOrderTime()).toLocalDate().plusDays(1);
                LocalDate followTime = Global.getStartTimeByDate(releaseVOS.get(0).getFollowTime()).toLocalDate().plusDays(1);
                Long notOrder = now.toEpochDay() - orderTime.toEpochDay();
                Long notFollow = now.toEpochDay() - followTime.toEpochDay();
                merchantVO.setNotOrder(notOrder >= 0 ? notOrder.intValue() : 0);
                merchantVO.setNotFollow(notFollow.intValue());
            }
        }
    }

    /**
     * 计算近三个月内客户平均下单周期
     * 客户订单间隔周期 -> 客户相邻两笔订单时间之差（取天数，订单发生当天不计入周期内），每日仅第一笔订单生效，结果四舍五入
     * @param mId 商户id
     * @return 客户平均下单周期
     */
    @Override
    public int averageOrderCycle(Long mId){
        LocalDateTime startTime = LocalDateTime.now().minusDays(90);
        List<Date> orderTimes = ordersMapper.selectOrderTimeByMId(mId, startTime);
        int cnt = 0;
        double sum = 0;
        double avg = 0;
        LocalDate ot1;
        LocalDate ot2;
        for (int i = orderTimes.size()-1; i > 0 ; i--) {
            ot1 = DateUtils.date2LocalDate(orderTimes.get(i));
            ot2 = DateUtils.date2LocalDate(orderTimes.get(i-1));
            if(ot2.toEpochDay() - ot1.toEpochDay() > 0){
                sum += ot2.toEpochDay() - ot1.toEpochDay() - 1;
                cnt++;
            }
        }
        if(cnt >= 1){
            avg = sum / cnt;
        }
        return Long.valueOf(Math.round(avg)).intValue();
    }

    @Resource
    private ConfigMapper configMapper;

    @Override
    public void autoRelease() {
        LocalDate now = LocalDate.now();
        //遍历所有客户判断
        List<ReleaseVO> releaseVOList = followUpRelationMapper.getMerchaWithBD(null);
        boolean tag = false;
        for (ReleaseVO releaseVO : releaseVOList) {
            LocalDate orderTime = Global.getStartTimeByDate(releaseVO.getOrderTime()).toLocalDate().plusDays(1);
            LocalDate followTime = Global.getStartTimeByDate(releaseVO.getFollowTime()).toLocalDate().plusDays(1);
            try {
                if (now.toEpochDay()-orderTime.toEpochDay() > 60) {
                    reassignOne(releaseVO.getmId(), null, "60天内未下单");
                    logger.info("{}60天未下单被自动释放,最近下单时间:{},最近拜访时间:{}",releaseVO.getmId(),releaseVO.getOrderTime(),releaseVO.getFollowTime());
                }
                if ((now.toEpochDay()-followTime.toEpochDay() > 15 && now.toEpochDay()-orderTime.toEpochDay() > 30)) {
                    reassignOne(releaseVO.getmId(), null, "30天内未下单且15天内未拜访");
                    logger.info("{}30天未下单且15天未拜访被自动释放,最近下单时间:{},最近拜访时间:{}",releaseVO.getmId(),releaseVO.getOrderTime(),releaseVO.getFollowTime());
                }
            } catch (Exception e) {
                logger.error("自动释放用户失败,失败用户id为:{},栈信息:{}",releaseVO.getmId(),e.getMessage());
                tag = true;
            }
        }

        //计算私海的流失数量
        List<MerchantVO> merchantVOS = merchantMapper.selectPrivateSea(null, null, null, null, null);
        if (!CollectionUtils.isEmpty(merchantVOS)) {
            for (MerchantVO merchantVO : merchantVOS) {
                //计算拜访天数和未下单天数
                List<ReleaseVO> releaseVOS = followUpRelationMapper.getMerchaWithBD(merchantVO.getmId());
                long notOrder = 0;
                long notFollow = 0;
                long dangerDay = 0;
                if (!CollectionUtils.isEmpty(releaseVOS)) {
                    LocalDate orderTime = Global.getStartTimeByDate(releaseVOS.get(0).getOrderTime()).toLocalDate().plusDays(1);
                    LocalDate followTime = Global.getStartTimeByDate(releaseVOS.get(0).getFollowTime()).toLocalDate().plusDays(1);
                    notOrder =  now.toEpochDay()-orderTime.toEpochDay();
                    notFollow = now.toEpochDay()-followTime.toEpochDay();
                }
                if (notFollow < 15 && notOrder < 30) {
                    dangerDay = Math.max(15 - notFollow, 30 - notOrder);
                } else if (notFollow < 15 && notOrder >= 30) {
                    dangerDay = Math.min(15 - notFollow, 60 - notOrder);
                } else if (notFollow >= 15 && notOrder < 30) {
                    dangerDay = 30 - notOrder;
                }
                FollowUpRelation update = new FollowUpRelation();
                update.setDangerDay((int)dangerDay);
                update.setId(merchantVO.getFollowId());
                try {
                    followUpRelationMapper.updateReassign(update);
                } catch (Exception e) {
                    logger.error("更新用户倒计时失败,失败用户跟进mId为:{},倒计时为:{},,栈信息:{}",merchantVO.getmId(),dangerDay,e.getMessage());
                    tag = true;
                }
            }
        }
        // 发送失败用户信息
        if (tag) {
            Map<String, String> md = new HashMap<>(2);
            String url = configMapper.selectOne("ScheduleTaskRobotUrl").getValue();
            md.put("title", "自动释放用户至公海任务部分失败");

            String sb = "#### 部分用户自动释放至公海失败\n" +
                    "> ###### 任务入口：followUpRelationServiceImpl.autoPrivateSea" + "\n" +
                    "> ###### 请查看日志并处理";
            md.put("text", sb);

            DingTalkRobotUtil.sendMsgAndAtAll(DingTalkRobotUtil.MARKDOWN, url, () -> md);
        }
    }

    @Override
    public void autoOpenSea() {
        LocalDateTime now = LocalDateTime.now();
        List<MerchantVO> merchantVOS = merchantMapper.selectOpenSea(null, null, null, null, null,null,null,null,null,null,null);
        if (!CollectionUtils.isEmpty(merchantVOS)) {
            for (MerchantVO merchantVO : merchantVOS) {
                int followType = 0;
                int timingFollowType = 0;
                int order = ordersMapper.count(merchantVO.getReassignTime(), now, merchantVO.getmId());
                int timingOrder = ordersMapper.countTimingNotDelivery(merchantVO.getmId());
                if (timingOrder > 0) {
                    timingFollowType = 1;
                }
                if (order > 0 ) {
                    followType = 1;
                }
                FollowUpRelation update = new FollowUpRelation();
                if(!Objects.equals(merchantVO.getFollowType(), 2)){
                    update.setFollowType(followType);
                }
                update.setTimingFollowType(timingFollowType);
                update.setId(merchantVO.getFollowId());
                //followUpRelationMapper.updateReassign(update);
            }
        }

    }

    @Override
    public AjaxResult queryRelationRecord(int pageIndex, int pageSize, Integer mId) {
        PageHelper.startPage(pageIndex, pageSize);
        List<CrmRelationRecord> relationList = crmRelationRecordMapper.selectByMid(mId);
        if (CollectionUtils.isEmpty(relationList)) {
            return AjaxResult.getErrorWithMsg(mId + "未查询到跟进记录");
        }
        // 获取商户最新跟进信息
        FollowUpRelation followUpRelation = followUpRelationMapper.selectByMid(mId);
        if(Objects.nonNull(followUpRelation) && followUpRelation.getReassign()!=null && !followUpRelation.getReassign()){
            CrmRelationRecord relationRecord = relationList.get(0);
            relationRecord.setAdminName("公海");
        }
        relationList.stream().filter(el -> Objects.equals("杨晗", el.getAdminName())).forEach(el -> el.setAdminName("公海"));
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(relationList));
    }

    @Override
    public AjaxResult queryPrivateSea(int pageIndex,int pageSize, PrivateSeaInput privateSeaInput) {
        //查询私海
        privateSeaInput.setAdminId(super.getAdminId());
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = DateUtils.getAtBeginningOfMonth();
        // 生命周期时间筛选(一天内)
        if(Objects.nonNull(privateSeaInput.getLifecycle())){
            privateSeaInput.setADayEarlier(now.minusDays(DateUtils.ONE_DATE));
        }
        // 本月有无下单,最近下单时间大于月初时间
        privateSeaInput.setAtBeginningOfMonth(startTime);
        // 获取数据,未满足核心客户及下单周期预警的筛选
        List<PrivateSeaVO> privateSeaVos = followUpRelationMapper.selectPrivateSea(privateSeaInput);
        if(CollectionUtils.isEmpty(privateSeaVos)){
            return AjaxResult.getOK();
        }
        // 下单周期预警 筛选
        privateSeaVos = selectOrderCycleWarn(pageIndex, pageSize, privateSeaInput, privateSeaVos);
        if(CollectionUtils.isEmpty(privateSeaVos)){
            return AjaxResult.getOK();
        }
        // 获取商户本月gmv,本月配送客单价,核心客户筛选,并处理未下单客户
        List<PrivateSeaVO> gmvList = selectGmvList(privateSeaInput, privateSeaVos);
        // 标签筛选
        if(CollectionUtils.isEmpty(gmvList)){
            return AjaxResult.getOK();
        }
        if(Objects.nonNull(privateSeaInput.getMerchantLabel())){
            gmvList = this.selectLabelList(privateSeaInput,gmvList);
        }
        if(CollectionUtils.isEmpty(gmvList)){
            return AjaxResult.getOK();
        }
        // 最后一页标识
        boolean isLastPage = false;
        List<PrivateSeaVO> collect = gmvList.stream().skip(pageIndex * pageSize).limit(pageSize).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)){
            isLastPage = true;
        }
        // 排序
        gmvList = selectSortType(pageIndex, pageSize, privateSeaInput, privateSeaVos, gmvList);
        // 分页
        gmvList = gmvList.stream().skip((pageIndex - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        // 填充上月gmv信息
        selectLastMonthGmv(now, startTime, privateSeaVos, gmvList);

        Map<String,Object> info = new HashMap<>(2);
        info.put("isLastPage",isLastPage);
        info.put("list",gmvList);
        return AjaxResult.getOK(info);
    }

    private List<PrivateSeaVO> selectLabelList(PrivateSeaInput privateSeaInput, List<PrivateSeaVO> gmvList) {
        // 查询符合条件的数据
        List<Integer> mIds = gmvList.stream().map(PrivateSeaVO::getMId).collect(Collectors.toList());
        privateSeaInput.setIds(mIds);
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_LABEL.getTableName());
        privateSeaInput.setDataTag(thisMonth.getDateFlag());
        List<Long> listByLabel = crmMerchantDayLabelMapper.selectMidListByLabel(privateSeaInput);
        if(CollectionUtils.isEmpty(listByLabel)){
            return null;
        }
        // 过滤数据
        List<PrivateSeaVO> middleList = new ArrayList<>(listByLabel.size());
        for (Long mId : listByLabel) {
            List<PrivateSeaVO> vos = gmvList.stream().filter(m -> Objects.equals(mId.intValue(), m.getMId())).collect(Collectors.toList());
            middleList.addAll(vos);
        }
        return middleList;
    }

    private List<PrivateSeaVO> selectSortType(int pageIndex, int pageSize, PrivateSeaInput privateSeaInput, List<PrivateSeaVO> privateSeaVos, List<PrivateSeaVO> gmvList) {
        // 排序
        if(Objects.nonNull(privateSeaInput.getSortType())){
            switch (privateSeaInput.getSortType()){
                case GMV_ASCENDING_ORDER:
                    gmvList.sort(Comparator.comparing(PrivateSeaVO::getThisMonthGmv,Comparator.nullsFirst(Comparator.naturalOrder())));
                    break;
                case DANGER_DAY_ASCENDING_ORDER:
                case DANGER_DAY_DESCENDING_ORDER:
                    // sql中已按倒计时排序,去除页面所需客户id
                    int i = 0;
                    List<PrivateSeaVO> dangerDayList = new ArrayList<>(pageSize * pageIndex);
                    for (PrivateSeaVO privateSeaVo : privateSeaVos) {
                        for (PrivateSeaVO vo : gmvList) {
                            if(privateSeaVo.getMId().equals(vo.getMId())){
                                dangerDayList.add(vo);
                                vo.setDangerDay(privateSeaVo.getDangerDay());
                                i++;
                                break;
                            }
                        }
                        // 寻找满筛选页数据
                        if(i == pageSize * pageIndex){
                            break;
                        }
                    }
                    gmvList = dangerDayList;
                    break;
                default:
                    break;
            }
        }
        return gmvList;
    }

    private void selectLastMonthGmv(LocalDateTime now, LocalDateTime startTime, List<PrivateSeaVO> privateSeaVos, List<PrivateSeaVO> gmvList) {
        // 上月数据更新标识
        DataSynchronizationInformation lastMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_MONTH_GMV.getTableName());
        // 上月开始时间
        LocalDateTime lastMonthStart = startTime.minusMonths(DateUtils.ONE_DATE);
        // 循环pageSize次
        for (PrivateSeaVO seaVO : gmvList) {
            // 获取上月gmv,上月配送客单价
            PrivateSeaVO lastPrivateSeaVO = crmMerchantMonthGmvMapper.selectByMid(seaVO.getMId(), lastMonth.getDateFlag());
            if(Objects.nonNull(lastPrivateSeaVO)){
                seaVO.setLastMonthGmv(lastPrivateSeaVO.getThisMonthGmv());
                seaVO.setLastMonthDeliveryUnitPrice(lastPrivateSeaVO.getThisMonthDeliveryUnitPrice());
            }
            // 本月产品数
            int skuNum = ordersMapper.selectSkuNumByMid(seaVO.getMId(), startTime, now);
            seaVO.setThisMonthSkuCount(skuNum);
            // 上月产品数
            int lastSkuNum = ordersMapper.selectSkuNumByMid(seaVO.getMId(),lastMonthStart, startTime);
            seaVO.setLastMonthSkuCount(lastSkuNum);
            // 释放倒计时
            if(Objects.isNull(seaVO.getDangerDay())){
                for (PrivateSeaVO privateSeaVo : privateSeaVos) {
                    if(seaVO.getMId().equals(privateSeaVo.getMId())){
                        seaVO.setDangerDay(privateSeaVo.getDangerDay());
                        break;
                    }
                }
            }
        }
    }

    private List<PrivateSeaVO> selectGmvList(PrivateSeaInput privateSeaInput, List<PrivateSeaVO> privateSeaVos) {
        List<Integer> ids = privateSeaVos.stream().map(PrivateSeaVO::getMId).collect(Collectors.toList());
        privateSeaInput.setIds(ids);
        // 本月数据更新时间
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_GMV.getTableName());
        // 本月gmv,本月配送客单价,核心客户筛选
        privateSeaInput.setDataTag(thisMonth.getDateFlag());
        // 处理本月未下单的商户数据
        List<PrivateSeaVO> gmvList;
        if(Objects.nonNull(privateSeaInput.getCoreMerchantTag()) &&
                Objects.equals(CoreMerchantENUM.CORE_MERCHANT.ordinal(), privateSeaInput.getCoreMerchantTag())){
            gmvList = crmMerchantDayGmvMapper.selectByAreaNo(privateSeaInput);
        }else {
            // 当月有下单的客户(包含核心)
            Integer coreMerchantTag = privateSeaInput.getCoreMerchantTag();
            privateSeaInput.setCoreMerchantTag(null);
            gmvList = crmMerchantDayGmvMapper.selectByAreaNo(privateSeaInput);
            List<Integer> gmvIds = gmvList.stream().map(PrivateSeaVO::getMId).collect(Collectors.toList());
            // 普通商户筛选
            if(Objects.nonNull(coreMerchantTag)){
                gmvList = gmvList.stream().filter(p -> Objects.equals(CoreMerchantENUM.ORDINARY_MERCHANT.ordinal(),p.getCoreMerchantTag())).collect(Collectors.toList());
            }
            // 当月未下单的客户
            ids.removeAll(gmvIds);
            for (Integer id : ids) {
                List<PrivateSeaVO> vos = privateSeaVos.stream().filter(p -> id.equals(p.getMId())).collect(Collectors.toList());
                gmvList.addAll(vos);
            }
        }
        return gmvList;
    }

    private List<PrivateSeaVO> selectOrderCycleWarn(int pageIndex, int pageSize, PrivateSeaInput privateSeaInput, List<PrivateSeaVO> privateSeaVos) {
        // 下单周期预警 筛选
        List<PrivateSeaVO> privateSea = new ArrayList<>(pageSize * pageIndex);
        if(Objects.nonNull(privateSeaInput.getOrderCycleWarn())){
            // 记录下单周期预警筛选字段出现次数
            int i = 0;
            for (PrivateSeaVO vo : privateSeaVos) {
                // 下单周期预警,默认不预警
                Boolean orderCycleWarn = false;
                // 未下单天数
                if (Objects.nonNull(vo.getLastOrderTime())) {
                    LocalDate orderTime = Global.getStartTimeByDate(vo.getLastOrderTime()).toLocalDate().plusDays(1);
                    Long notOrder = LocalDate.now().toEpochDay() - orderTime.toEpochDay();
                    int orderCycle = this.averageOrderCycle(Long.valueOf(vo.getMId()));
                    if (orderCycle == 0) {
                        orderCycle = notOrder.intValue();
                    }
                    orderCycleWarn = notOrder > orderCycle || notOrder > 30;
                }
                if(Objects.equals(privateSeaInput.getOrderCycleWarn(),orderCycleWarn)){
                    vo.setOrderCycleWarn(orderCycleWarn);
                    privateSea.add(vo);
                    i++;
                }
                // 寻找满筛选页数据
                if(i == pageSize * pageIndex){
                    break;
                }
            }
            privateSeaVos = privateSea;
        }
        return privateSeaVos;
    }

    @Override
    public AjaxResult merchantNearby(NearbyReq req) {
        //匹配地址
        Area area = areaService.matchArea(req.getProvince(), req.getCity(), req.getArea());
        if (area != null) {
            req.setAreaNo(area.getAreaNo());
        }

        List<NearbyVO> voList = new ArrayList<>();

        //下拉选择数据
        if (req.getPoi() == null) {
            voList.addAll(queryMerchantPool(req));
            voList.addAll(queryCluePool(req));
            voList.sort((o1, o2) -> o2.getScore().compareTo(o1.getScore()));
            if (voList.size() > 20) {
                return AjaxResult.getOK(voList.subList(0, 20));
            } else {
                return AjaxResult.getOK(voList);
            }
        }

        //结果页数据：全部客户
        if(req.getQueryType() == null){
          voList.addAll(queryMerchantPool(req));
          voList.addAll(queryCluePool(req));
        } //未注册客户
        else if (Objects.equals(3, req.getQueryType())) {
            voList.addAll(queryCluePool(req));
        } //已注册客户
        else {
            List<NearbyVO> list = queryMerchantPool(req);
            if (!CollectionUtils.isEmpty(list)) {
                //我的客户
                if (Objects.equals(0, req.getQueryType())) {
                    list = list.stream().filter(el -> el.getRegisterFlag() && el.getMyMerchant()).collect(Collectors.toList());
                } //别人的客户
                else if (Objects.equals(1, req.getQueryType())) {
                    list = list.stream().filter(el -> el.getRegisterFlag() && !el.getOpenSeaFlag() && !el.getMyMerchant()).collect(Collectors.toList());
                }//公海客户
                else if (Objects.equals(2, req.getQueryType())) {
                    list = list.stream().filter(el -> el.getRegisterFlag() && el.getOpenSeaFlag()).collect(Collectors.toList());
                }
            }
            voList.addAll(list);
        }

        voList.sort(Comparator.comparing(NearbyVO::getDistance));

        if (req.getLimit() != null && voList.size() > req.getLimit()) {
            return AjaxResult.getOK(voList.subList(0, req.getLimit()));
        }

        return AjaxResult.getOK(voList);
    }

    @Override
    public AjaxResult saveMonthPurmoney(Long mId, String monthPurmoney) {
        //插入到月度金额表
        merchantMonthPurmoneyMapper.deleteByMid(mId);
        MerchantMonthPurmoney merchantMonthPurmoney = new MerchantMonthPurmoney();
        merchantMonthPurmoney.setMonthPurmoney(monthPurmoney);
        merchantMonthPurmoney.setCreator(getAdminId());
        merchantMonthPurmoney.setCreateTime(LocalDateTime.now());
        merchantMonthPurmoney.setMId(mId);
        merchantMonthPurmoneyMapper.insertSelective(merchantMonthPurmoney);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAndInsertFollow(FollowUpRelation followInput){
        if(Objects.isNull(followInput) || Objects.isNull(followInput.getmId())){
            throw new DefaultServiceException("商户id不能为空");
        }
        // 每次变更均改变其最新跟进时间
        followInput.setReassignTime(LocalDateTime.now());
        followInput.setAddTime(LocalDateTime.now());
        FollowUpRelation oldFollow = followUpRelationMapper.selectByMid(followInput.getmId().intValue());
        // 无商户跟进记录,新增一条
        if(Objects.isNull(oldFollow)){
            followUpRelationMapper.insertSelective(followInput);
        }else {
            // 有跟进记录,更新商户跟进记录
            followInput.setId(oldFollow.getId());
            followUpRelationMapper.updateReassign(followInput);
        }
    }

    @Override
    public FollowUpRelation getInfoByMid(Long mId) {
        return followUpRelationMapper.selectByMid(mId.intValue());
    }

    private List<NearbyVO> queryCluePool(NearbyReq req) {
        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
        } catch (Exception e) {
            throw new DefaultServiceException("es连接异常");
        }

        //查询大众点评数据
        BoolQueryBuilder builder = new BoolQueryBuilder();
        builder.filter(new MatchPhraseQueryBuilder("city", req.getCity().replace("市", "")))
                .filter(new RangeQueryBuilder("manage").gt(0))
                .should(new MatchQueryBuilder("district", req.getArea()));
        if (StringUtils.isNotBlank(req.getInput())) {
            builder.should(new MultiMatchQueryBuilder(req.getInput(), "shop_name", "district", "shop_region", "shopping_mall", "address","cuisine_type"));
        }

        if (req.getPoi() != null) {
            GeoDistanceQueryBuilder geoBuilder = new GeoDistanceQueryBuilder("poi").point(req.getPoi().getLat(), req.getPoi().getLon());
            if (req.getDistance() != null) {
                geoBuilder.distance(req.getDistance() + "m");
            } else {
                geoBuilder.distance("10000m");
            }
            builder.filter(geoBuilder);
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(builder);

        SearchRequest searchRequest = new SearchRequest(net.summerfarm.common.util.es.EsIndexContext.INDEX_CRUL_POOL);
        searchRequest.source(searchSourceBuilder);
        if(req.getPoi() == null){
            searchSourceBuilder.from(0).size(20);
        } else {
            if (req.getLimit() != null && req.getQueryType() == null){
                searchSourceBuilder.from(0).size(req.getLimit());
            } else {
                searchSourceBuilder.from(0).size(1000);
            }

            GeoDistanceSortBuilder sortBuilder = new GeoDistanceSortBuilder("poi", req.getPoi().getLat(), req.getPoi().getLon());
            searchSourceBuilder.sort(sortBuilder);
        }

        List<NearbyVO> result = new ArrayList<>();
        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            for (SearchHit el : response.getHits()) {
                MerchantCluePool pool = merchantCluePoolMapper.selectEffectClue(el.getId());
                //过滤已注册用户
                if (pool != null && pool.getMId() != null) {
                    continue;
                }

                NearbyVO vo = new NearbyVO();
                vo.setEsId(el.getId());
                vo.setRegisterFlag(false);
                vo.setScore(el.getScore());
                //判断是否生成地推码
                vo.setLeadFlag(false);
                if (pool != null && pool.getMlId() != null) {
                    MerchantLeads leads = merchantLeadsMapper.selectById(pool.getMlId().intValue());
                    if (leads != null) {
                        vo.setLeadFlag(true);
                    }
                }

                JSONObject jsonObject = JSONObject.parseObject(el.getSourceAsString());
                vo.setMname(jsonObject.getString("shop_name"));
                vo.setProvince(jsonObject.getString("province"));
                vo.setCity(jsonObject.getString("city"));
                vo.setArea(jsonObject.getString("district"));
                vo.setAddress(jsonObject.getString("address"));
                String phone = jsonObject.getString("phone_number");
                if(StringUtils.isMobile(phone)){
                    vo.setPhone(phone);
                }
                vo.setIschain(jsonObject.getString("ischain"));
                vo.setCuisine_type(jsonObject.getString("cuisine_type"));
                JSONObject poi = jsonObject.getJSONObject("poi");
                if (poi != null) {
                    PoiVO poiVO = new PoiVO(poi.getFloat("lon"), poi.getFloat("lat"));
                    vo.setPoi(poiVO);
                    if (req.getPoi() != null) {
                        double distance = GaoDeUtil.getLineDistance(req.getPoi().getLon(), req.getPoi().getLat(), poiVO.getLon(), poiVO.getLat());
                        vo.setDistance((int) distance);
                    }
                }

                result.add(vo);
            }
        } catch (IOException e) {
            logger.error("商品信息搜索异常，", e);
        } finally {
            EsClientPoolUtil.returnClient(client);
        }

        return result;
    }

    /**
     * 小程序适用：计算拜访天数和未下单天数
     * @param psv 传递条件与储存结果的对象
     */
    private void notOrderAndFollow(PrivateSeaVO psv) {
        List<ReleaseVO> releaseVOS = followUpRelationMapper.getMerchaWithBD(Long.valueOf(psv.getMId()));
        if (!CollectionUtils.isEmpty(releaseVOS)) {
            LocalDate now = LocalDate.now();
            LocalDate orderTime = Global.getStartTimeByDate(releaseVOS.get(0).getOrderTime()).toLocalDate().plusDays(1);
            LocalDate followTime = Global.getStartTimeByDate(releaseVOS.get(0).getFollowTime()).toLocalDate().plusDays(1);
            Long notOrder = now.toEpochDay() - orderTime.toEpochDay();
            Long notFollow = now.toEpochDay() - followTime.toEpochDay();
            psv.setNotOrder(notOrder >= 0 ? notOrder.intValue() : 0);
            psv.setNotFollow(notFollow.intValue());
        }
    }

    private List<NearbyVO> queryMerchantPool(NearbyReq req) {
        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
        } catch (Exception e) {
            throw new DefaultServiceException("es连接异常");
        }

        SearchRequest searchRequest = new SearchRequest(EsIndexContext.INDEX_MERCHANT);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        //查询已注册客户
        BoolQueryBuilder builder = new BoolQueryBuilder();
        builder.filter(new TermQueryBuilder("islock", 0))
                .filter(new MatchPhraseQueryBuilder("city", req.getCity()))
                .should(new MatchQueryBuilder("area", req.getArea()));
        if (req.getPoi() != null) {
            GeoDistanceQueryBuilder geoBuilder = new GeoDistanceQueryBuilder("contacts.poi").point(req.getPoi().getLat(), req.getPoi().getLon());
            if (req.getDistance() != null) {
                geoBuilder.distance(req.getDistance() + "m");
            }
            builder.filter(geoBuilder);
        }

        if (StringUtils.isNotBlank(req.getInput())) {
            builder.should(new MultiMatchQueryBuilder(req.getInput(), "mname", "contacts.address", "address","cuisine_type"));
        }

        if(req.getPoi() == null){
            searchSourceBuilder.from(0).size(20);
        } else {
            searchSourceBuilder.from(0).size(1000);

            GeoDistanceSortBuilder sortBuilder = new GeoDistanceSortBuilder("contacts.poi", req.getPoi().getLat(), req.getPoi().getLon());
            searchSourceBuilder.sort(sortBuilder);
        }

        searchSourceBuilder.query(builder);
        searchRequest.source(searchSourceBuilder);

        List<NearbyVO> result = new ArrayList<>();
        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            for (SearchHit el : response.getHits()) {
                JSONObject jsonObject = JSONObject.parseObject(el.getSourceAsString());
                String conStr = jsonObject.getString("contacts");
                if (StringUtils.isNotBlank(conStr)) {
                    List<Contact> contactList = JSONObject.parseArray(jsonObject.getString("contacts"), Contact.class);
                    for (Contact contact : contactList) {
                        if (!Objects.equals(1, contact.getStatus())){
                            continue;
                        }
                        double lineDistance = -1D;
                        if(req.getPoi() != null){
                            if(Objects.isNull(contact.getPoi())){
                                logger.info("加载客户联系地址poi失败:{}",contact.toString());
                                continue;
                            }
                            lineDistance = GaoDeUtil.getLineDistance(req.getPoi().getLon(), req.getPoi().getLat(), contact.getPoi().getLon(), contact.getPoi().getLat());
                            if (req.getDistance() != null && (int)lineDistance > req.getDistance()){
                                continue;
                            }
                        }

                        NearbyVO vo = new NearbyVO();
                        vo.setScore(el.getScore());
                        vo.setRegisterFlag(true);
                        vo.setMId(el.getId());
                        vo.setContactId(contact.getContactId());
                        vo.setMname(jsonObject.getString("mname"));
                        vo.setContact(contact.getContact());
                        vo.setProvince(contact.getProvince());
                        vo.setCity(contact.getCity());
                        vo.setArea(contact.getArea());
                        vo.setAddress(contact.getAddress());
                        vo.setPhone(contact.getPhone());
                        vo.setCuisine_type(jsonObject.getString("cuisine_type"));
                        vo.setPoi(contact.getPoi());
                        if (lineDistance >= 0) {
                            vo.setDistance((int) lineDistance);
                        }

                        //判断是否是私海客户
                        Long mId = Long.valueOf(vo.getMId());
                        FollowUpRelation query = new FollowUpRelation();
                        query.setmId(mId);
                        query.setReassign(false);
                        FollowUpRelation relation = followUpRelationMapper.selectOne(query);

                        if (relation == null){
                            vo.setOpenSeaFlag(true);
                            vo.setMyMerchant(false);
                        } else {
                            vo.setOpenSeaFlag(false);
                            vo.setMyMerchant(Objects.equals(relation.getAdminId(), getAdminId()));
                        }

                        result.add(vo);
                    }
                }
            }
        } catch (IOException e) {
            logger.error("商品信息搜索异常，", e);
        } finally {
            EsClientPoolUtil.returnClient(client);
        }

        return result;
    }
}
