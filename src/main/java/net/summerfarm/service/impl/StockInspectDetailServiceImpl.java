package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.ReflectUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.StorageLocation;
import net.summerfarm.facade.StockTaskStorageFacade;
import net.summerfarm.facade.goods.GoodsReadFacade;
import net.summerfarm.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.goods.client.enums.GuaranteeUnitEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.Evaluation;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.StockInspectDetailVO;
import net.summerfarm.model.vo.StockTaskProcessDetailVO;
import net.summerfarm.service.StockInspectDetailService;
import net.summerfarm.service.defectConfig.enums.ReceiptMethodEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xiang
 * create at:  2021-11-01
 */
@Service
public class StockInspectDetailServiceImpl extends BaseService implements StockInspectDetailService {

    @Resource
    private StockInspectDetailMapper stockInspectDetailMapper;
    @Resource
    private PurchasesMapper purchasesMapper;
    @Resource
    private GoodsReadFacade goodsReadFacade;
    @Resource
    private SupplierMapper supplierMapper;
    @Resource
    private StockTaskProcessDetailMapper stockTaskProcessDetailMapper;
    @Resource
    private StockArrangeItemMapper stockArrangeItemMapper;
    @Resource
    private StockTaskStorageFacade stockTaskStorageFacade;

    private static final String HISTORY = "历史评价";

    @Override
    public AjaxResult selectInspect(int pageIndex, int pageSize, StockInspectDetail selectKey) {

        Map<String, GoodsInfoDTO> goodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByPdIds(selectKey.getWarehouseNo(), Lists.newArrayList(selectKey.getPdId()), null);
        selectKey.setQuerySkus(Lists.newArrayList(goodsInfoDTOMap.keySet()));
        PageHelper.startPage(pageIndex, pageSize);
        List<StockInspectDetailVO> details = stockInspectDetailMapper.select(selectKey);
        details.forEach(stockInspectDetail ->
                stockInspectDetail.setWarehouseName(Global.warehouseMap.getOrDefault(stockInspectDetail.getAreaNo(), "")));
        return AjaxResult.getOK(new PageInfo<>(details));
    }

    @Override
    public AjaxResult updateInspect(StockInspectDetailVO selectKey) {
        StockInspectDetail detail = stockInspectDetailMapper.selectByPrimaryKey(selectKey.getId());


        if (Objects.isNull(detail)) {
            return AjaxResult.getErrorWithMsg("错误的货检任务编号！");
        }
        if (Objects.isNull(selectKey.getStatus()) && !Objects.equals(NumberUtils.INTEGER_ZERO, detail.getStatus())) {
            return AjaxResult.getErrorWithMsg("该任务无法编辑！");
        }
        List<String> pictures = selectKey.getPictures();
        if (CollectionUtils.isEmpty(pictures) && Objects.isNull(selectKey.getStatus()) &&
                !selectKey.getReceiptMethod().equals(ReceiptMethodEnum.NORMAL.getCode())) {
            return AjaxResult.getErrorWithMsg("请上传货检图片！");
        }

        if (!Objects.isNull(selectKey.getCheckQuantity()) && selectKey.getCheckQuantity() > detail.getInQuantity()) {
            return AjaxResult.getErrorWithMsg("抽检数量不能大于实际入库数量！");
        }

        if (!Objects.isNull(selectKey.getQualifiedQuantity()) && selectKey.getCheckQuantity() < selectKey.getQualifiedQuantity()) {
            return AjaxResult.getErrorWithMsg("合格数量不能大于抽检数量！");
        }

        if (!Objects.isNull(selectKey.getDamageQuantity()) && selectKey.getCheckQuantity() < selectKey.getDamageQuantity()) {
            return AjaxResult.getErrorWithMsg("内质货损数量不能大于抽检数量！");
        }
        //拼接图片存储规则
        if (CollectionUtils.isNotEmpty(pictures)) {
            StringJoiner joiner = new StringJoiner(Global.SEPARATING_SYMBOL);
            for (String picture : pictures) {
                joiner.add(picture);
            }
            selectKey.setPhotos(joiner.toString());
        }
        if (Objects.isNull(selectKey.getStatus())) {
            selectKey.setStatus(NumberUtils.INTEGER_ONE);
        }
        selectKey.setUpdater(getAdminName());
        selectKey.setAuditProof(JSON.toJSONString(selectKey.getAuditProofs()));
        selectKey.setEvaluate(JSON.toJSONString(selectKey.getEvaluations()));
        stockInspectDetailMapper.update(selectKey);
        return AjaxResult.getOK();

    }

    @Override
    public AjaxResult insertInspect(StockInspectDetail selectKey) {


        if (Objects.isNull(selectKey.getStockTaskProcessId()) && StringUtils.isEmpty(selectKey.getSku())) {
            throw new DefaultServiceException("参数错误！");
        }
        //获取老任务id
        StockTask stockTask = stockTaskStorageFacade.getStockTaskId(selectKey.getStockTaskProcessId().longValue(),null);
        //老任务id
        Integer stockTaskId = stockTask.getId();
        List<StockInspectDetail> details = stockInspectDetailMapper.selectByTaskId(selectKey.getStockTaskProcessId(), selectKey.getSku());
        if (CollectionUtils.isNotEmpty(details)) {
            throw new DefaultServiceException("sku:" + selectKey.getSku() + "有待货检任务存在！");
        }

        //旧任务id
        List<StockTaskProcessDetailVO> detailVos = stockTaskProcessDetailMapper.selectByStockTaskId(stockTaskId, selectKey.getSku());


        if (CollectionUtils.isEmpty(detailVos)) {
            throw new DefaultServiceException("无可匹配入库单!");
        }
        List<StockTaskProcessDetailVO> purchasesList = detailVos.stream().filter(el -> selectKey.getSku().equals(el.getSku())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(purchasesList)) {
            throw new DefaultServiceException("无可匹配采购单!");
        }
        StockTaskProcessDetailVO detailVO = purchasesList.get(NumberUtils.INTEGER_ZERO);
        Purchases purchases = purchasesMapper.selectByNo(detailVO.getListNo());

        //旧任务id
        StockArrangeItem item = stockArrangeItemMapper.selectByTaskIdAndSku(stockTaskId, selectKey.getSku());
        if (Objects.isNull(item)) {
            throw new DefaultServiceException("无可匹配的预约单！");
        }

        Set<String> sku = new HashSet<>();
        sku.add(selectKey.getSku());

        List<GoodsInfoDTO> goodsInfoDTOS = goodsReadFacade.listGoodsInfoBySkus(null, Lists.newArrayList(sku), stockTask.getTenantId());
        Map<String, GoodsInfoDTO> goodsInfoDTOMap = goodsInfoDTOS.stream().collect(Collectors.toMap(GoodsInfoDTO::getSku, Function.identity(), (o1, o2) -> o1));
        if (goodsInfoDTOMap.size() == 0) {
            throw new DefaultServiceException("无匹配的sku!");
        }

        //统计入库数
        Integer totalQuantity = detailVos.stream().map(StockTaskProcessDetailVO::getQuantity).reduce((v1, v2) -> v1 + v2).orElse(0);
        GoodsInfoDTO inventory = goodsInfoDTOMap.getOrDefault(selectKey.getSku(),new GoodsInfoDTO());
        selectKey.setPdName(inventory.getTitle());
        selectKey.setWeight(inventory.getSpecification());
        selectKey.setType(inventory.getAgentType());
        selectKey.setStorageLocation(inventory.getStorageLocation());
        selectKey.setStorageMethod(StorageLocation.getTypeById(inventory.getStorageLocation()));
        selectKey.setQualityTime(inventory.getGuaranteePeriod());
        selectKey.setQualityTimeUnit(GuaranteeUnitEnum.goods2Xm(inventory.getGuaranteeUnit()));
        selectKey.setUnit(inventory.getSpecificationUnit());
        selectKey.setBatch(detailVO.getListNo());
        selectKey.setAreaNo(purchases.getAreaNo());
        selectKey.setCreator(getAdminName());
        selectKey.setCreateTime(LocalDateTime.now());
        selectKey.setStockTaskProcessId(selectKey.getStockTaskProcessId());
        Supplier supplier = supplierMapper.selectByPrimaryKey(selectKey.getSupplierId());
        selectKey.setSupplier(null != supplier ? supplier.getName() : "");
        selectKey.setQuantity(item.getArrivalQuantity());
        selectKey.setInQuantity(totalQuantity);
        selectKey.setStatus(NumberUtils.INTEGER_ZERO);

        stockInspectDetailMapper.insert(selectKey);

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectInspectDetail(int id) {
        StockInspectDetail stockInspectDetail = stockInspectDetailMapper.selectByPrimaryKey(id);
        if (!Objects.isNull(stockInspectDetail)) {
            StockInspectDetailVO stockInspectDetailVO = new StockInspectDetailVO();
            BeanUtils.copyProperties(stockInspectDetail, stockInspectDetailVO);
            stockInspectDetailVO.setAuditProofs(JSONObject.parseArray(stockInspectDetail.getAuditProof(), String.class));
            // 老数据处理
            List<Evaluation> evaluations;
            try {
                evaluations = JSONObject.parseArray(stockInspectDetail.getEvaluate(), Evaluation.class);
            } catch (Exception e) {
                Evaluation evaluation = Evaluation.builder().desc(stockInspectDetail.getEvaluate()).questionType(HISTORY).build();
                evaluations = Lists.newArrayList(evaluation);
            }
            stockInspectDetailVO.setEvaluations(evaluations);
            String warehouseName = Global.warehouseMap.getOrDefault(stockInspectDetailVO.getAreaNo(), "");
            stockInspectDetailVO.setWarehouseName(warehouseName);
            if (Objects.nonNull(stockInspectDetail.getCheckQuantity()) && Objects.nonNull(stockInspectDetail.getQualifiedQuantity()) &&
                    stockInspectDetail.getFailedNum() == 0 && stockInspectDetail.getCheckQuantity() - stockInspectDetail.getQualifiedQuantity() > 0) {
                stockInspectDetailVO.setFailedNum(stockInspectDetail.getCheckQuantity() - stockInspectDetail.getQualifiedQuantity());
            }
            if (stockInspectDetail.getReceiptMethod() < 0) {
                stockInspectDetailVO.setReceiptMethod(null);
            }
            return AjaxResult.getOK(stockInspectDetailVO);
        }
        return AjaxResult.getOK();
    }
}
