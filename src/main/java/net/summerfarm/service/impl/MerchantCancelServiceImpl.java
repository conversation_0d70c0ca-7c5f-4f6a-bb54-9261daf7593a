package net.summerfarm.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.enums.CommonStatus;
import net.summerfarm.enums.MerchantCancelEnum;
import net.summerfarm.facade.MerchantCancelFacade;
import net.summerfarm.mall.client.req.MerchantCancelInputReq;
import net.summerfarm.mapper.manage.AreaMapper;
import net.summerfarm.mapper.manage.MerchantCancelMapper;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.domain.Area;
import net.summerfarm.model.domain.FollowUpRelation;
import net.summerfarm.model.domain.MerchantCancel;
import net.summerfarm.model.domain.MerchantSubAccount;
import net.summerfarm.model.input.MerchantCancelInsertReq;
import net.summerfarm.model.input.MerchantCancelPageQuery;
import net.summerfarm.model.input.MerchantCancelReq;
import net.summerfarm.model.vo.AdminVO;
import net.summerfarm.model.vo.MerchantCancelVO;
import net.summerfarm.service.AdminService;
import net.summerfarm.service.FollowUpRelationService;
import net.summerfarm.service.MerchantCancelService;
import net.summerfarm.service.MerchantSubAccountService;
import net.xianmu.common.exception.BizException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 门店注销
 * @date 2023/4/20 16:30:55
 */
@Service
@Slf4j
public class MerchantCancelServiceImpl extends BaseService implements MerchantCancelService {

    @Resource
    private MerchantCancelMapper merchantCancelMapper;

    @Resource
    @Lazy
    private AdminService adminService;

    @Resource
    private MerchantSubAccountService merchantSubAccountService;

    @Resource
    @Lazy
    private FollowUpRelationService followUpRelationService;

    @Resource
    private MerchantCancelFacade merchantCancelFacade;

    @Resource
    private AreaMapper areaMapper;


    @Override
    public PageInfo<MerchantCancelVO> getPage(MerchantCancelPageQuery merchantCancelPageQuery) {
        Integer pageSize = merchantCancelPageQuery.getPageSize();
        Integer pageIndex = merchantCancelPageQuery.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<MerchantCancelVO> merchantCancelVOList = merchantCancelMapper.getPage(merchantCancelPageQuery);
        //获取地推人员信息和运营区域
        merchantCancelVOList.stream().forEach(e -> {
            FollowUpRelation upRelation = followUpRelationService.getInfoByMid(e.getMId());
            if (Objects.nonNull(upRelation)) {
                e.setAdminRealName(upRelation.getReassign() ? "默认邀请码" : upRelation.getAdminName());
            }
            Area area = areaMapper.selectByAreaNo(Integer.valueOf(e.getAreaNo()));
            e.setAreaName(area.getAreaName());
        });
        return PageInfoHelper.createPageInfo(merchantCancelVOList);
    }

    @Override
    public MerchantCancelVO insert(MerchantCancelInsertReq merchantCancelInsertReq) {
        //校验是否已经注销
        MerchantCancel merchantCancel = new MerchantCancel();
        merchantCancel.setMId(merchantCancelInsertReq.getMId());
        merchantCancel.setStatus(MerchantCancelEnum.CANCELLED.getCode());
        MerchantCancel selectByEntity = merchantCancelMapper.selectByEntity(merchantCancel);
        if (Objects.nonNull(selectByEntity)) {
            throw new BizException("当前门店已注销，无需重复申请！");
        }
        MerchantCancelInputReq merchantCancelInputReq = new MerchantCancelInputReq();
        merchantCancelInputReq.setCertificate(merchantCancelInsertReq.getCertificate());
        merchantCancelInputReq.setRemake(merchantCancelInsertReq.getRemake());
        merchantCancelInputReq.setMId(merchantCancelInsertReq.getMId());
        merchantCancelInputReq.setResource(CommonStatus.YES.getCode());
        merchantCancelInputReq.setCreator(getAdminId().longValue());
        merchantCancelInputReq.setMName(merchantCancelInsertReq.getMName());
        return merchantCancelFacade.insert(merchantCancelInputReq);
    }

    @Override
    public MerchantCancelVO getDetail(MerchantCancelReq merchantCancelReq) {
        if (Objects.isNull(merchantCancelReq) || Objects.isNull(merchantCancelReq.getId())) {
            throw new BizException("门店申请注销ID不能为空！");
        }
        MerchantCancel merchantCancel = merchantCancelMapper.selectByPrimaryKey(merchantCancelReq.getId());
        if (Objects.isNull(merchantCancel)) {
            return null;
        }
        MerchantCancelVO merchantCancelVO = new MerchantCancelVO();
        merchantCancelVO.setId(merchantCancel.getId());
        merchantCancelVO.setMId(merchantCancel.getMId());
        merchantCancelVO.setPhone(merchantCancel.getPhone());
        merchantCancelVO.setResource(merchantCancel.getResource());
        merchantCancelVO.setRemake(merchantCancel.getRemake());
        merchantCancelVO.setStatus(merchantCancel.getStatus());
        merchantCancelVO.setCertificate(merchantCancel.getCertificate());

        //获取申请人信息 0-商城  1-后台
        AdminVO creator = new AdminVO();
        if(Objects.equals(CommonStatus.YES.getCode(), merchantCancel.getResource())) {
            Admin select = adminService.select(merchantCancel.getCreator().intValue());
            creator.setRealname(select.getRealname());
            creator.setAdminId(select.getAdminId());
        } else {
            MerchantSubAccount merchantSubAccount = merchantSubAccountService.getByAccountIdIgnoreDel(merchantCancel.getCreator());
            creator.setRealname(merchantSubAccount.getContact());
            creator.setAdminId(merchantSubAccount.getAccountId().intValue());
        }
        merchantCancelVO.setCreator(creator);
        merchantCancelVO.setCreateTime(merchantCancel.getCreateTime());
        if (Objects.equals(merchantCancel.getStatus(), MerchantCancelEnum.CANCELLED.getCode())) {
            merchantCancelVO.setUpdateTime(merchantCancel.getUpdateTime());
        }
        return merchantCancelVO;
    }

    @Override
    public Boolean updateStatus(MerchantCancelReq merchantCancelReq) {
        if (Objects.isNull(merchantCancelReq) || Objects.isNull(merchantCancelReq.getId())) {
            throw new BizException("门店申请注销ID不能为空！");
        }
        MerchantCancelInputReq merchantCancelInputReq = new MerchantCancelInputReq();
        merchantCancelInputReq.setId(merchantCancelReq.getId());
        merchantCancelInputReq.setStatus(merchantCancelReq.getStatus());
        merchantCancelInputReq.setUpdater(getAdminId().longValue());
        return merchantCancelFacade.updateStatus(merchantCancelInputReq);
    }

    @Override
    public MerchantCancelVO check(MerchantCancelReq merchantCancelReq) {
        if (Objects.isNull(merchantCancelReq) || Objects.isNull(merchantCancelReq.getMId())) {
            throw new BizException("门店申请注销编号不能为空！");
        }
        MerchantCancelInputReq merchantCancelInputReq = new MerchantCancelInputReq();
        merchantCancelInputReq.setMId(merchantCancelReq.getMId());
        List<String> check = merchantCancelFacade.check(merchantCancelInputReq);
        MerchantCancelVO merchantCancelVO = new MerchantCancelVO();
        merchantCancelVO.setCause(check);
        return merchantCancelVO;
    }

    @Override
    public MerchantCancelVO getInfo(MerchantCancelReq merchantCancelReq) {
        if (Objects.isNull(merchantCancelReq)) {
            return null;
        }
        MerchantCancel merchantCancel = new MerchantCancel();
        merchantCancel.setPhone(merchantCancelReq.getPhone());
        merchantCancel.setStatus(merchantCancelReq.getStatus());
        merchantCancel = merchantCancelMapper.selectByEntity(merchantCancel);
        if (Objects.isNull(merchantCancel)) {
            return null;
        }
        MerchantCancelVO merchantCancelVO = new MerchantCancelVO();
        merchantCancelVO.setMId(merchantCancel.getMId());
        merchantCancelVO.setRemake(merchantCancel.getRemake());
        merchantCancelVO.setPhone(merchantCancel.getPhone());
        merchantCancelVO.setCertificate(merchantCancel.getCertificate());
        merchantCancelVO.setUpdateTime(merchantCancel.getUpdateTime());
        merchantCancelVO.setCreateTime(merchantCancel.getCreateTime());
        merchantCancelVO.setId(merchantCancel.getId());
        return merchantCancelVO;
    }

    @Override
    public MerchantCancelVO promptlyCancel(MerchantCancelReq merchantCancelReq) {
        //假如不是待注销状态则不处理
        MerchantCancel merchantCancel = merchantCancelMapper.selectByPrimaryKey(merchantCancelReq.getId());
        if (Objects.isNull(merchantCancel) || !Objects.equals(merchantCancel.getStatus(), MerchantCancelEnum.TO_BE_CANCELLED.getCode())) {
            throw new BizException("门店注销记录为空或者不是待注销状态！");
        }
        MerchantCancelVO merchantCancelVO = new MerchantCancelVO();

        //校验是否可以注销
        MerchantCancelInputReq merchantCancelInputReq = new MerchantCancelInputReq();
        merchantCancelInputReq.setMId(merchantCancel.getMId());
        List<String> check = merchantCancelFacade.check(merchantCancelInputReq);
        if (!CollectionUtils.isEmpty(check)) {
            merchantCancelVO.setCause(check);
            return merchantCancelVO;
        }

        //立即注销
        merchantCancelInputReq.setId(merchantCancel.getId());
        Boolean aBoolean = merchantCancelFacade.promptlyCancel(merchantCancelInputReq);
        if (!aBoolean) {
            throw new BizException("注销失败！");
        }
        return merchantCancelVO;
    }
}
