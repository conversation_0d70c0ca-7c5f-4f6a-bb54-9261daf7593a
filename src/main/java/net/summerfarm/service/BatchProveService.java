package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.input.pms.ProveQueryPmsInput;
import net.summerfarm.model.param.BatchProveParam;
import net.summerfarm.model.vo.pms.ProveBatchPmsVO;
import net.xianmu.common.result.CommonResult;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/10/20  17:31
 */
public interface BatchProveService {

    /**
     * 库存列表查询
     * @Author: ct
     * @param pageIndex 下标
     * @param pageSize  页码
     * @param param  提阿健
     * @return
     **/
    AjaxResult selectList(Integer pageSize, Integer pageIndex, BatchProveParam param);

    /**
     * 证明列表查询
     * @Author: ct
     * @param pageIndex 下标
     * @param pageSize  页码
     * @param batchProve  提阿健
     * @return
     **/
    AjaxResult selectBatchProveList(Integer pageSize, Integer pageIndex, BatchProveParam batchProve);

    /**
     * 查询证件状态0无须 1未上传
     */
    Integer getProveStatus(String sku,Integer warehouseNo);

    /**
     * 查询必填证件
     * @param warehouseNo 仓库编号
     * @param sku sku
     * @return 所需证件列表
     */
    List<Integer> selectBatchProveStandard(Integer warehouseNo, String sku);

    List<ProveBatchPmsVO> selectBatchProveStandardBatch(ProveQueryPmsInput input);
}
