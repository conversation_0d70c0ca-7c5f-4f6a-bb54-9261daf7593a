package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.FollowWhiteList;
import net.summerfarm.model.vo.FollowWhiteListVO;

/**
 * <AUTHOR> ct
 * create at:  2019/7/23  12:02 PM
 */
public interface FollowWhiteListService {

    /**
    * 新增白名单
    */
    AjaxResult save(FollowWhiteList followWhiteList);

    /**
    * 更新白名单
    */
    AjaxResult update(Long mId);


    /**
     * 白名单列表查询
     * @param pageIndex 页数
     * @param pageSize 数量
     * @param followWhiteListVO 查询条件
     * @return 白名单列表
     */
    AjaxResult queryWhiteList(int pageIndex, int pageSize, FollowWhiteListVO followWhiteListVO);

    /**
     * 移除白名单
     */
    AjaxResult deleteWhiteList(Long mId);

}
