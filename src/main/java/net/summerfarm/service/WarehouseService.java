package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.vo.WarehouseCenterVO;
import net.summerfarm.model.vo.WarehouseStorageCenterExtVO;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.model.vo.WarehouseStorageCenterVO;

import java.util.List;

public interface WarehouseService {
    /**
     * 分页查询仓库数据
     *
     * @param pageIndex 页码
     * @param pageSize  分页大小
     * @param name      仓库名称
     * @param type      类型
     * @return
     */
    AjaxResult listWarehouseStoragePage(int pageIndex, int pageSize, String name, Integer type);

    /**
     * 新增仓库
     *
     * @param storageCenter 仓库
     * @return
     */
    AjaxResult insertWarehouseStorage(WarehouseStorageCenterVO storageCenter);

    /**
     * 修改仓库
     *
     * @param storageCenter 仓库
     * @return
     */
    AjaxResult updateWarehouseStorage(WarehouseStorageCenterVO storageCenter);

    /**
     * 查询所有仓库
     *
     * @param type   类型
     * @param status 状态
     * @return 仓库
     */
    AjaxResult<List<WarehouseStorageCenterVO>> selectWarehouseStorage(Integer type, Integer status);

    /**
     * 查询所有仓库（扩展字段）
     * @param type
     * @param status
     * @return
     */
    AjaxResult<List<WarehouseStorageCenterExtVO>> selectWarehouseStorageExt(Integer type, Integer status);

    /**
     * 查询所有物流中心
     *
     * @param pageIndex 页码
     * @param pageSize  分页代销
     * @return 配送中心及城市
     */
    AjaxResult selectWarehouseLogisticsPage(int pageIndex, int pageSize, WarehouseLogisticsCenter logisticsCenter);

    /**
     * 查询所有物流中心
     *
     * @param status 状态
     * @return
     */
    AjaxResult<List<WarehouseCenterVO>> selectWarehouseLogistics(Integer status);

    /**
     * 添加配送中心
     *
     * @param instance 配送中心信息
     * @return
     */
    AjaxResult addWarehouseLogistics(WarehouseCenterVO instance);

    /**
     * 修改配送中心
     *
     * @param instance 配送中心信息
     * @return
     */
    AjaxResult updateWarehouseLogistics(WarehouseCenterVO instance);

    /**
     * 删除配送中心使用库存仓
     *
     * @param storeNo     物流中心编号
     * @param warehouseNo 仓库编号
     * @return
     */
    AjaxResult deleteLogisticsUseStorage(Integer storeNo, Integer warehouseNo);

    /**
     * 查询城市可用库存城市
     *
     * @param areaNo 城市编号
     * @param sku    sku
     * @return WarehouseStorage
     */
    List<WarehouseStorageCenter> selectUsableStorageList(Integer areaNo, String sku);

    /**
     * 取消修改截单
     */
    AjaxResult cancelUpdateCloseTime(Integer storeNo);

    


}
