package net.summerfarm.service.stockTransfer.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.biz.stockTransfer.StockTransferBizService;
import net.summerfarm.biz.stockTransfer.constants.TransferExportConstant;
import net.summerfarm.biz.stockTransfer.converter.StockTransferConverter;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.BizException;
import net.summerfarm.common.exceptions.ErrorCode;
import net.summerfarm.common.object.Page;
import net.summerfarm.common.util.CheckHelper;
import net.summerfarm.common.util.DateUtil;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.common.util.RequestHolder;
import net.summerfarm.contexts.Global;
import net.summerfarm.dao.stockTransfer.StockTransferDAO;
import net.summerfarm.dao.stockTransfer.StockTransferItemDAO;
import net.summerfarm.dao.stockTransfer.StockTransferItemOpDAO;
import net.summerfarm.dao.stockTransfer.StockTransferItemOpDetailDAO;
import net.summerfarm.dao.stockTransfer.dataobject.StockTransferDO;
import net.summerfarm.dao.stockTransfer.dataobject.StockTransferItemDO;
import net.summerfarm.dao.stockTransfer.dataobject.StockTransferItemOpDO;
import net.summerfarm.dao.stockTransfer.dataobject.StockTransferItemOpDetailDO;
import net.summerfarm.facade.goods.GoodsReadFacade;
import net.summerfarm.facade.goods.dto.GoodsInfoDTO;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.mapper.manage.SkuBatchCodeMapper;
import net.summerfarm.mapper.manage.StockTakingMapper;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.SkuBatchCode;
import net.summerfarm.model.domain.StockTakingListDetail;
import net.summerfarm.model.domain.StoreRecord;
import net.summerfarm.service.stockTransfer.StockTransferQueryService;
import net.summerfarm.service.stockTransfer.dto.TransferOutInfo;
import net.summerfarm.service.stockTransfer.dto.req.*;
import net.summerfarm.service.stockTransfer.dto.res.*;
import net.summerfarm.service.stockTransfer.enums.StorageLocationEnum;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StockTransferQueryServiceImpl extends BaseService implements StockTransferQueryService {

    @Resource
    private StockTransferDAO stockTransferDAO;

    @Resource
    private StockTransferItemDAO stockTransferItemDAO;

    @Resource
    private WarehouseStorageService warehouseStorageService;

    @Resource
    private StockTransferItemOpDAO stockTransferItemOpDAO;

    @Resource
    private StockTransferItemOpDetailDAO stockTransferItemOpDetailDAO;

    @Resource
    private GoodsReadFacade goodsReadFacade;

    @Resource
    private SkuBatchCodeMapper skuBatchCodeMapper;

    @Resource
    private StoreRecordMapper storeRecordMapper;

    @Resource
    private StockTransferBizService stockTransferBizService;

    @Resource
    private StockTakingMapper stockTakingMapper;

    @Resource
    private ConfigMapper configMapper;

    public static final String KN_CABINET = "KN01";

    @Override
    public Page<StockTransferListRes> pageStockTransfer(StockTransferListQuery query) {
        log.info("start query stockTransfer list:{}", JSON.toJSONString(query));
        List<Long> ids = getIds(query);
        StockTransferDO stockTransferDO = StockTransferDO.builder()
                .id(query.getStockTransferId())
                .state(query.getState())
                .transferDimension(query.getTransferDimension())
                .createdAt(query.getCreatedTime())
                .warehouseNo(query.getWarehouseNo())
                .remark(query.getRemark())
                .build();
        if ((Objects.nonNull(query.getSku()) || Objects.nonNull(query.getProductName())) && CollectionUtils.isEmpty(ids)) {
            return new Page<>(Lists.newArrayList(), query.getPageSize(), query.getPageNum(), 0L);
        }

        List<StockTransferDO> stockTransferDos = stockTransferDAO.pageByIdsAndCondition(ids, stockTransferDO, (query.getPageNum() - 1) * query.getPageSize(), query.getPageSize());
        if (CollectionUtils.isEmpty(stockTransferDos)) {
            return new Page<>(Lists.newArrayList(), query.getPageSize(), query.getPageNum(), 0L);
        }
        Map<Integer, String> adminIdMap = getAdminIdMap(stockTransferDos);
        List<StockTransferListRes> result = stockTransferDos.stream()
                .map(StockTransferConverter.INSTANCE::stockTransferDoToRes)
                .peek(item -> {
                    if (StringUtils.isEmpty(item.getFounder())) {
                        return;
                    }
                    item.setWarehouseName(Global.warehouseMap.getOrDefault(item.getWarehouseNo(), ""));
                    item.setFounder(adminIdMap.getOrDefault(Integer.valueOf(item.getFounder()), ""));
                })
                .collect(Collectors.toList());
        int total = stockTransferDAO.countByIdsAndCondition(ids, stockTransferDO);
        return new Page<>(result, query.getPageSize(), query.getPageNum(), total);
    }

    @Override
    public StockTransferDetailRes getStockTransferDetail(StockTransferDetailQuery query) {
        StockTransferDO stockTransferDO = stockTransferDAO.selectById(query.getStockTransferId());
        if (Objects.isNull(stockTransferDO)) {
            return StockTransferDetailRes.builder().build();
        }
        return StockTransferConverter.INSTANCE.stockTransferDoToDetail(stockTransferDO);
    }

    @Override
    public List<StockTransferItemDetailRes> listStockTransferItemDetail(StockTransferItemDetailQuery query) {
        StockTransferDO transferDO = stockTransferDAO.selectById(query.getStockTransferId());
        List<StockTransferItemDO> stockTransferItemDos = stockTransferItemDAO.listByStockTransferId(query.getStockTransferId());
        if (CollectionUtils.isEmpty(stockTransferItemDos)) {
            return Lists.newArrayList();
        }
        WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNo(transferDO.getWarehouseNo().intValue());

        // 查询转入sku信息
        List<String> inSkus = stockTransferItemDos.stream().map(StockTransferItemDO::getTransferInSku).collect(Collectors.toList());
        List<GoodsInfoDTO> goodsInfoDTOS = goodsReadFacade.listGoodsInfoBySkus(transferDO.getWarehouseNo(), inSkus, null);
        Map<String, GoodsInfoDTO> goodsInfoDTOMap = goodsInfoDTOS.stream().collect(Collectors.toMap(GoodsInfoDTO::getSku, Function.identity(),(o1,o2)->o1));

                List<Long> itemIds = stockTransferItemDos.stream().map(StockTransferItemDO::getId).collect(Collectors.toList());
        List<StockTransferItemOpDO> opDos = stockTransferItemOpDAO.listByItemId(itemIds);
        List<Long> opIds = opDos.stream().map(StockTransferItemOpDO::getId).collect(Collectors.toList());
        List<StockTransferItemOpDetailDO> opDetailDos = CollectionUtils.isEmpty(opIds) ? Lists.newArrayList() :
                stockTransferItemOpDetailDAO.listByOpIds(opIds);
        Map<Long/*opId*/, List<Long>> opIdOutNumMap = opDetailDos.stream().collect(Collectors.groupingBy(StockTransferItemOpDetailDO::getStockTransferItemOpId,
                Collectors.mapping(StockTransferItemOpDetailDO::getTransferOutNum, Collectors.toList())));
        Map<Long/*itemId*/, List<Long>> itemIdOutNumMap = opDos.stream().collect(Collectors.groupingBy(StockTransferItemOpDO::getStockTransferItemId,
                Collectors.mapping(opDO -> {
                    List<Long> list = opIdOutNumMap.getOrDefault(opDO.getId(), Lists.newArrayList());
                    long outTotal = list.stream().mapToLong(Long::longValue).sum();
                    return calculate(outTotal, opDO.getTransferRatio());
                }, Collectors.toList())));

        return stockTransferItemDos.stream()
                .map(item -> {
                    GoodsInfoDTO goodsInfoDTO = goodsInfoDTOMap.getOrDefault(item.getTransferInSku(), new GoodsInfoDTO());
                    return StockTransferItemDetailRes.builder()
                            .stockTransferItemId(item.getId())
                            .goodsCategory(Objects.equals(goodsInfoDTO.getCategoryType(), 4) ? TransferExportConstant.FRUIT :
                                    TransferExportConstant.NOT_FRUIT)
                            .actualTransferInNum(itemIdOutNumMap.getOrDefault(item.getId(),
                                    Lists.newArrayList()).stream().mapToLong(Long::longValue).sum())
                            .preTransferInNum(item.getPreTransferInNum())
                            .transferInGoodsId(goodsInfoDTO.getSkuMapping().getAgentSpuId())
                            .transferInGoodsSku(item.getTransferInSku())
                            .transferInGoodsSpec(goodsInfoDTO.getSpecification())
                            .transferInGoodsName(goodsInfoDTO.getTitle())
                            .storageArea(StorageLocationEnum.convert(goodsInfoDTO.getStorageLocation()))
                            .packaging(goodsInfoDTO.getSpecificationUnit())
                            .isDomestic(goodsInfoDTO.getPlaceType())
                            .skuType(goodsInfoDTO.getAgentType())
                            .build();
                }).collect(Collectors.toList());
    }

    @Override
    public List<TransferItemOpDetailRes> listTransferItemOpDetail(StockTransferItemOpQuery query) {
        List<Long> itemIds = query.getItemIds();
        if (CollectionUtils.isEmpty(itemIds)) {
            return Lists.newArrayList();
        }
        List<StockTransferItemDO> stockTransferItemDos = stockTransferItemDAO.listByIds(itemIds);
        List<StockTransferItemOpDO> opDos = stockTransferItemOpDAO.listByItemId(itemIds);
        if (CollectionUtils.isEmpty(opDos)) {
            return Lists.newArrayList();
        }

        // 获取库存仓号
        Long warehouseNo = getWarehouseNo(stockTransferItemDos);

        // 查询操作详情, 转换map
        List<Long> opIds = opDos.stream().map(StockTransferItemOpDO::getId).collect(Collectors.toList());
        List<StockTransferItemOpDetailDO> opDetailDos = stockTransferItemOpDetailDAO.listByOpIds(opIds);
        Map<Long, List<StockTransferItemOpDetailDO>> opDetailMap = opDetailDos.stream()
                .collect(Collectors.groupingBy(StockTransferItemOpDetailDO::getStockTransferItemOpId));

        // 查询打印次数
        List<String> inSkus = stockTransferItemDos.stream().map(StockTransferItemDO::getTransferInSku).collect(Collectors.toList());
        List<String> inBatchs = opDetailDos.stream().map(StockTransferItemOpDetailDO::getTransferInBatch).distinct().collect(Collectors.toList());
        List<SkuBatchCode> skuBatchCodes = CollectionUtils.isEmpty(inBatchs) ? Lists.newArrayList() :
                skuBatchCodeMapper.listBySkuAndBatchs(inSkus, inBatchs);
        Map<String, Integer> batchPrintMap = CollectionUtils.isEmpty(skuBatchCodes) ? Maps.newHashMap() :
                skuBatchCodes.stream().collect(Collectors.toMap(SkuBatchCode::getPurchaseNo, SkuBatchCode::getPrintNumber, (o1, o2) -> o2));

        // 查询操作人
        List<Integer> adminIds = opDos.stream()
                .map(StockTransferItemOpDO::getOperator)
                .filter(StringUtils::isNotEmpty)
                .filter(item -> !item.equals(TransferExportConstant.NULL))
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        Map<Integer, String> adminMap = getAdminMap(adminIds);

        WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNo(warehouseNo.intValue());
        // 查询转入sku信息
        List<String> outSkus = opDos.stream().map(StockTransferItemOpDO::getTransferOutSku).collect(Collectors.toList());
        List<GoodsInfoDTO> goodsInfoDTOS = goodsReadFacade.listGoodsInfoBySkus(warehouseStorageCenter.getWarehouseNo().longValue(), outSkus, null);
        Map<String, GoodsInfoDTO> goodsInfoDTOMap = goodsInfoDTOS.stream().collect(Collectors.toMap(GoodsInfoDTO::getSku, Function.identity(), (o1, o2) -> o1));
        // 组装返回转出批次的信息
        Map<Long, String> inBatchMap = opDetailDos.stream().collect(Collectors.toMap(StockTransferItemOpDetailDO::getStockTransferItemOpId,
                StockTransferItemOpDetailDO::getTransferInBatch, (o1, o2) -> o2));

        // 组装返回参数
        return opDos.stream()
                .map(opDO -> {
                    List<StockTransferItemOpDetailDO> opDetailDos1 = opDetailMap.getOrDefault(opDO.getId(), Lists.newArrayList());
                    StockTransferItemOpDetailDO opDetailDO = opDetailDos1.stream().findFirst().orElse(StockTransferItemOpDetailDO.builder().build());
                    List<TransferOutBatchInfo> batchInfos = buildBatchInfos(warehouseNo, opDO, opDetailDos1);
                    long outTotal = opDetailDos1.stream().mapToLong(StockTransferItemOpDetailDO::getTransferOutNum).sum();
                    Long inTotal = calculate(outTotal, opDO.getTransferRatio());
                    GoodsInfoDTO goodsInfoDTO = goodsInfoDTOMap.getOrDefault(opDO.getTransferOutSku(), new GoodsInfoDTO());

                    return TransferItemOpDetailRes.builder()
                            .stockTransferItemOpId(opDO.getId())
                            .goodsCategory(Objects.equals(goodsInfoDTO.getCategoryType(), 4) ? TransferExportConstant.FRUIT :
                                    TransferExportConstant.NOT_FRUIT)
                            .stockTransferItemId(opDO.getStockTransferItemId())
                            .transferRatio(opDO.getTransferRatio())
                            .transferOutSku(opDO.getTransferOutSku())
                            .transferInBatch(inBatchMap.getOrDefault(opDO.getId(), ""))
                            .transferInCabinet(Objects.isNull(opDetailDO.getTransferInCabinet()) ? KN_CABINET : opDetailDO.getTransferInCabinet())
                            .produceTime(opDO.getProduceDate())
                            .shelfLife(opDO.getShelfLife())
                            .outBatchInfos(batchInfos)
                            .printNum(batchPrintMap.getOrDefault(opDetailDO.getTransferInBatch(), 0))
                            .operator(adminMap.getOrDefault(CheckHelper.stringIfEmpty(opDO.getOperator()), TransferExportConstant.SYSTEM))
                            .operateTime(opDO.getCreatedAt())
                            .transferOutTotal(outTotal)
                            .transferInTotal(inTotal)
                            .specification(goodsInfoDTO.getSpecification())
                            .transferOutGoodsName(goodsInfoDTO.getTitle())
                            .build();
                }).collect(Collectors.toList());
    }

    private Long getWarehouseNo(List<StockTransferItemDO> stockTransferItemDos) {
        Long stockTransferId = stockTransferItemDos.get(NumberUtils.INTEGER_ZERO).getStockTransferId();
        StockTransferDO stockTransferDO = stockTransferDAO.selectById(stockTransferId);
        return stockTransferDO.getWarehouseNo();
    }

    private List<TransferOutBatchInfo> buildBatchInfos(Long warehouseNo, StockTransferItemOpDO opDO, List<StockTransferItemOpDetailDO> opDetailDos) {
        return opDetailDos.stream().map(detail -> TransferOutBatchInfo.builder()
                .transferOutNum(detail.getTransferOutNum())
                .shelfLife(detail.getShelfLife())
                .transferOutCabinetNo(detail.getTransferOutCabinet())
                .transferOutBatch(detail.getTransferOutBatch())
                .build()).collect(Collectors.toList());
    }

    private Map<Integer, String> getAdminIdMap(List<StockTransferDO> stockTransferDos) {
        List<Integer> adminIds = stockTransferDos.stream()
                .map(StockTransferDO::getOperator)
                .filter(StringUtils::isNotEmpty)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        return getAdminMap(adminIds);
    }

    @Nullable
    private List<Long> getIds(StockTransferListQuery query) {
        List<Long> ids = null;
        if (Objects.nonNull(query.getStockTransferId())) {
            ids = Lists.newArrayList(query.getStockTransferId());
        } else {
            Set<String> skus = Sets.newHashSet();
            if (StringUtils.isNotEmpty(query.getSku()) && Objects.nonNull(query.getPdId())) {
                Set<String> skuList = querySkusByPdName(query.getWarehouseNo(), query.getPdId());
                if (skuList.contains(query.getSku())) {
                    skus.add(query.getSku());
                }
            } else if (StringUtils.isNotEmpty(query.getSku())) {
                skus.add(query.getSku());
            } else if (Objects.nonNull(query.getPdId())) {
                Set<String> skuList = querySkusByPdName(query.getWarehouseNo(), query.getPdId());
//                if (CollectionUtils.isEmpty(skuList) && Objects.nonNull(query.getPdId())) {
//                    throw new net.xianmu.common.exception.BizException("该货品不存在");
//                }
                skus.addAll(skuList);
            }

            List<String> fillSkus = skus.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(fillSkus)) {
                ids = stockTransferItemDAO.listBySku(fillSkus);
            }
        }
        return ids;
    }

    @NotNull
    private Set<String> querySkusByPdName(Long warehouseNo, Long pdId) {
        Map<String, GoodsInfoDTO> stringGoodsInfoDTOMap = goodsReadFacade.mapGoodsInfoByPdIds(warehouseNo, Lists.newArrayList(pdId), null);
        return stringGoodsInfoDTOMap.keySet();
    }

    private Long calculate(Long transferOutNum, String transferRatio) {
        if (transferOutNum == 0L) {
            return 0L;
        }
        String[] split = transferRatio.split(":");
        Double num = transferOutNum * Double.parseDouble(split[1]) / Double.parseDouble(split[0]);
        return num.longValue();
    }

    private BigDecimal calculateRatio(String transferRatio) {
        if (StringUtils.isBlank(transferRatio)) {
            return BigDecimal.ZERO;
        }
        String[] split = transferRatio.split(":");
        BigDecimal ratio = BigDecimal.valueOf(Double.parseDouble(split[1])).divide(BigDecimal.valueOf(Double.parseDouble(split[0])), 2, BigDecimal.ROUND_HALF_EVEN);
        return ratio;
    }

    @Override
    public void exportStockTransfer(StockTransferExportQuery query) {
        log.info("start to export:{}", query);
        StockTransferDO stockTransferDO = stockTransferDAO.selectById(query.getStockTransferId());
        Workbook workbook = stockTransferBizService.excelTransfer(query.getStockTransferId());
        String warehouseName = Global.warehouseMap.get(stockTransferDO.getWarehouseNo().intValue());
        try {
            ExcelUtils.outputExcel(workbook, DateUtil.formatYmdWithOutSplitDate(stockTransferDO.getCreatedAt()) +
                    warehouseName + "转换任务.xls", RequestHolder.getResponse());
        } catch (IOException e) {
            log.error("导出异常", e);
            throw new BizException(ErrorCode.EXPORT_ERROR);
        }
    }

    @Override
    public void checkStocktakingState(StockTakingStateQuery query) {
        //判断是否在盘点中
        List<StockTakingListDetail> outStockTakings = stockTakingMapper.selectTakingSku(query.getWarehouseNo().intValue(),
                query.getOutSku());
        List<StockTakingListDetail> inStockTakings = stockTakingMapper.selectTakingSku(query.getWarehouseNo().intValue(),
                query.getInSku());
        if (CollectionUtils.isNotEmpty(outStockTakings) || CollectionUtils.isNotEmpty(inStockTakings)) {
            throw new BizException(ErrorCode.SKU_STOCKTAKING);
        }
    }

    @Override
    public Boolean validStockTransferCost(StockTransferCostQuery stockTransferCostQuery) {
        logger.info("库存转换操作成本校验入参：{}", JSON.toJSONString(stockTransferCostQuery));
        Config config = configMapper.selectOne("stockTransferCostOffset");
        StockTransferDO stockTransferDO = stockTransferDAO.selectById(stockTransferCostQuery.getStockTransferId());
        CheckHelper.isNull(stockTransferDO, ErrorCode.STOCK_TRANSFER_NOT_EXIST);
        List<TransferOutInfo> transferOutInfoList = stockTransferCostQuery.getTransferOutInfos();
        BigDecimal predictCost = BigDecimal.ZERO;
        BigDecimal cost = BigDecimal.ZERO;
        // 转入sku批次库存
        StoreRecord inSkuStoreRecord = storeRecordMapper.selectLastBatchOne(stockTransferCostQuery.getTransferInSku(), stockTransferDO.getWarehouseNo().intValue());
        // 未获取到默认校验通过
        if (null == inSkuStoreRecord || null == inSkuStoreRecord.getCost() || BigDecimal.ZERO.compareTo(inSkuStoreRecord.getCost()) >= 0) {
            return true;
        }
        log.info("转入sku：{}，成本：{}", stockTransferCostQuery.getTransferInSku(), inSkuStoreRecord.getCost());
        for (TransferOutInfo transferOutInfo : transferOutInfoList) {
            // 转出sku批次库存
            StoreRecord outSkuStoreRecord = storeRecordMapper.selectOne(StoreRecord.builder()
                    .batch(transferOutInfo.getTransferOutBatch())
                    .sku(stockTransferCostQuery.getTransferOutSku())
                    .qualityDate(DateUtil.toLocalDate(transferOutInfo.getShelfLife()))
                    .areaNo(stockTransferDO.getWarehouseNo().intValue())
                    .build());
            // 转入数量
            BigDecimal ratio = calculateRatio(stockTransferCostQuery.getTransferRatio());
            // 转入sku预计成本
            BigDecimal predictCostTemp = outSkuStoreRecord.getCost().multiply(ratio);
            log.info("计算预计转入sku成本：{}，仓：{}，转入sku：{}，转出sku：{}，批次：{}，效期：{}，cost：{}", predictCostTemp, stockTransferDO.getWarehouseNo().intValue(), stockTransferCostQuery.getTransferInSku(), stockTransferCostQuery.getTransferOutSku(), transferOutInfo.getTransferOutBatch(), DateUtil.toLocalDate(transferOutInfo.getShelfLife()), outSkuStoreRecord.getCost());
            predictCost = predictCost.add(predictCostTemp);
            // 转入sku成本
            cost = cost.add(inSkuStoreRecord.getCost());
        }
        // 成本偏差10%以上返回提示
        BigDecimal offset = predictCost.subtract(cost).divide(cost, 2, BigDecimal.ROUND_HALF_EVEN);
        log.info("计算预计转入sku：{}，预计转入sku总成本：{}，转入sku总成本：{}，成本偏差：{}", stockTransferCostQuery.getTransferInSku(), predictCost.doubleValue(), cost.doubleValue(), offset.doubleValue());
        double defaultOffset = 0.1;
        if (null != config && !StringUtils.isBlank(config.getValue())) {
            defaultOffset = Double.parseDouble(config.getValue());
        }
        return Math.abs(offset.doubleValue()) < defaultOffset;
    }

}
