package net.summerfarm.service.stockTransfer.dto.req;

import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 转换任务列表查询
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StockTransferDetailQuery implements Serializable {
    private static final long serialVersionUID = -8650464486886364918L;

    /**
     * 任务id
     */
    @NotNull(message = "任务id不可为空")
    Long stockTransferId;
}
