package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.MerchantSituationDTO;
import net.summerfarm.model.vo.MerchantSituationVO;
import net.summerfarm.mq.DtsModel;


public interface MerchantSituationService {


    /**
    * 审批 type 0 关闭 1 通过
    */
    AjaxResult approval(MerchantSituationVO merchantSituationVO, Integer type);

    /**
    * 新增客情申请
    */
    AjaxResult insertMerchantSituation(MerchantSituationVO merchantSituationVO);

    /**
    * 审核客情申请单
    */
    AjaxResult examineMerchantSituation(MerchantSituationVO merchantSituationVO, Integer type);

    /**
    * 查询客情申请单
    */
    AjaxResult queryMerchantSituationList(int pageIndex, int pageSize, MerchantSituationVO merchantSituationVO, String keyword);

    AjaxResult queryMerchantSituation(int id);

    /**
    * 查寻已申请客情金额和总计
    */
    AjaxResult merchantSituationQuota();

    /**
     * 校验客情券合法性
     * @param merchantSituationDTO 客情申请
     * @return 客情券
     */
    AjaxResult checkMonthLivingCouponLegitimacy(MerchantSituationDTO merchantSituationDTO);

}
