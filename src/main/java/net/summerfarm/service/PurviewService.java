package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.Purview;
import org.springframework.validation.BindingResult;

/**
 * @Package: net.summerfarm.service
 * @Description: 系统权限业务类
 * @author: <EMAIL>
 * @Date: 2016/7/24
 */
public interface PurviewService {
    /**
     * 分页查询
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @return
     */
    AjaxResult select(int pageIndex, int pageSize, Purview selectKeys);

    /**
     * 新增权限
     * @param record
     * @param bindingResult
     * @return
     */
    AjaxResult save(Purview record, BindingResult bindingResult);

    /**
     * 修改
     * @param id
     * @param record
     * @param bindingResult
     * @return
     */
    AjaxResult update(Integer id, Purview record, BindingResult bindingResult);

    AjaxResult selectAll();

    AjaxResult delete(Integer id);
}
