package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.SettlementPaymentRecord;
import net.summerfarm.model.input.SettlementQuery;
import net.summerfarm.model.vo.SettlementDetailVO;
import net.summerfarm.model.vo.SettlementVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

public interface SettlementService {
    /**
     * 列表页数据查询
     *
     * @param pageIndex 页码
     * @param pageSize  分页大小
     * @param query     查询条件
     * @return
     */
    AjaxResult select(int pageIndex, int pageSize, SettlementQuery query);


    /**
     * 根据采购单查询未结算采购项
     * @param purchaseNo 采购单号
     * @return
     */
    AjaxResult selectUnSettleByPurchaseNo(String purchaseNo);

    /**
     * 根据供应商id查询未结算采购项
     * @param supplierId
     * @param startTime
     * @param endTime
     * @return
     */
    AjaxResult selectUnSettleBySupplierId(Integer supplierId, LocalDate startTime, LocalDate endTime);

    /**
     * 发起结算单
     * @return
     */
    AjaxResult save(SettlementVO instance);

    /**
     * 查询结算单详情
     * @param settlementId
     * @return
     */
    AjaxResult detail(Integer settlementId);

    /**
     * 发起单次结算
     * @param record
     * @return
     */
    AjaxResult saveRecord(SettlementPaymentRecord record);

    /**
     * 修改打款记录备注
     * @param recordId 记录id
     * @param remark 备注
     * @return
     */
    AjaxResult updateRemark(Integer recordId, String remark);

    /**
     * 结算打款审核
     * @param recordId
     * @param auditFlag 0、拒绝 1、通过
     * @return
     */
    AjaxResult audit(Integer recordId, Integer auditFlag);

    /**
     * 结算单审批
     * @param recordId
     * @param approveFlag 0、拒绝 1、通过
     * @return
     */
    AjaxResult approve(Integer recordId, Integer approveFlag);


    /**
     * 结算打款
     * @param recordId
     * @param auditFlag 0、拒绝 1、通过
     * @param paymentVoucher 打款凭证
     * @return
     */
    AjaxResult pay(Integer recordId, Integer auditFlag, String paymentVoucher);

    /**
     * 手动关闭结算单
     * @param settlementId 结算单号
     * @return
     */
    AjaxResult close(Integer settlementId);

    /**
     * 上传打款凭证
     * @param recordId
     * @param voucher
     * @return
     */
    AjaxResult upVoucher(Integer recordId, String voucher);

    /**
     * 查看结算单统计数据
     * @return
     */
    AjaxResult data();

    /**
     * 导出数据
     * @param id
     * @return
     */
    void export(Integer id, Integer recordId);

    /**
     * @Description: 导出未发起结算单的采购单明细
     * @Return: void
     * @Date: 2020/10/13 16:21
     * @Author: <EMAIL>
     */
    void unSettlePurchaseExport();

    /**
     * 处理逾期订单    发送钉钉消息给发起人
     */
    void unHandleSettlement();

    /**
     * 将待处理的结算单 发送钉钉消息给对应处理人
     */
    void remindOperator();


    /**
     * 根据条件查询结算单详情
     * @param settlementDetailVO
     * @return
     */
    AjaxResult selectDetail(SettlementDetailVO settlementDetailVO);

    /**
     * 根据采购单号查询可发起金额  = 结算总金额-已结算金额-退结金额-扣款金额-结算中金额
     * @param purchasesNo
     * @return
     */
    Map<String,BigDecimal> selectAvailableAmount(String purchasesNo);

    /**
     * 结算单历史数据初始化
     *
     * @return
     */
    AjaxResult refundSettlementAmountInitialization();

    /**
     * 待结算状态修改正确
     * @return
     */
    AjaxResult settlementStatus();

    /**
     * 付款单撤回
     * @param recordId 付款单记录id
     * @return
     */
    AjaxResult withdraw(Integer recordId);

    /**
     * 根据结算单id查询剩余可发起金额
     * @param settlementId
     * @return
     */
    BigDecimal queryLeftAmount(Integer settlementId);
}
