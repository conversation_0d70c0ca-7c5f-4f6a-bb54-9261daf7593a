package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.SaveReceiptInput;
import net.summerfarm.model.input.ReceiptBillConfirmInfo;
import net.summerfarm.model.input.ReceiptBillConfirmInput;
import net.summerfarm.model.vo.FinanceReceiptVO;
import net.summerfarm.mq.DtsModel;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2021年12月25日 20:21:00
 */
public interface FinanceReceiptService {

    /**
     * 应收核销-收款单列表
     *
     * @param pageIndex
     * @param pageSize
     * @param financeReceiptVO
     * @return
     */
    AjaxResult listWriteOff(Integer pageIndex, Integer pageSize, FinanceReceiptVO financeReceiptVO);

    /**
     * 根据id查询收款单详情
     *
     * @param id
     * @return
     */
    AjaxResult detailWriteOff(Long id);

    /**
     * 应收核销-收款单-确认核销
     *
     * @param financeReceiptVO
     * @return
     */
    AjaxResult confirmWriteOff(FinanceReceiptVO financeReceiptVO);


    /**
     * 应收核销-收款单-撤销
     *
     * @param id
     * @return
     */
    AjaxResult cancel(Long id);

    /**
     * 应收核销-收款单-撤销
     *
     * @param inputList
     * @return
     */
    AjaxResult cancel(List<ReceiptBillConfirmInfo> inputList);


    /**
     * 应收核销-收款单-驳回
     *
     * @param id
     * @return
     */
    AjaxResult reject(Long id);

    /**
     * 保存收款单
     *
     * @param financeReceiptVOS
     * @return
     */
    AjaxResult saveReceipt(FinanceReceiptVO financeReceiptVOS);

    /**
     * 根据账单id查询账单收款详情
     *
     * @param id
     * @return
     */
    AjaxResult detailsBillReceipt(Long id);

    /**
     * 收款流水格式核销单详情
     *
     * @param id
     * @param financeBankFlowingWaterId
     * @return
     */
    AjaxResult billReceipt(Long id, Long financeBankFlowingWaterId);

    /**
     * 核销滞后钉钉通知任务
     */
    void writeOffTimeoutTask();

    /**
     * 发送确认核销钉钉消息
     *
     * @param dtsModel
     */
    void sendConfirmMessage(DtsModel dtsModel);

    /**
     * 认领账单
     *
     * @param saveReceiptInput
     */
    void claimBill(SaveReceiptInput saveReceiptInput);

    /**
     * 审核账单
     *
     * @param inputList
     * @return
     */
    void auditBill(ReceiptBillConfirmInput inputList);

    /**
     * 认领鲜沐卡
     *
     * @param saveReceiptInput 保存收据输入
     */
    void claimRecharge(SaveReceiptInput saveReceiptInput);

    /**
     * 审核鲜沐卡充值单
     *
     * @param inputList
     * @return
     */
    void auditRecharge(ReceiptBillConfirmInput inputList);
}
