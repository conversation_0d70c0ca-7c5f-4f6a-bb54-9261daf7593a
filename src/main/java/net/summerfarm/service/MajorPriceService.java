package net.summerfarm.service;

import java.util.Collection;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.MajorPrice;
import net.summerfarm.model.input.MajorMerchantQuery;
import net.summerfarm.model.input.MajorPriceInput;
import net.summerfarm.model.input.MajorPriceReq;
import net.summerfarm.model.vo.MajorAreaStoreVO;
import net.summerfarm.model.vo.MajorPriceAutoUpdateVO;
import net.summerfarm.model.vo.MajorPriceVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Created by wjd on 2018/7/17.
 */

public interface MajorPriceService {

    AjaxResult majorPrice(MajorPriceVO majorPriceVO);

    AjaxResult updateMajorPrice(MajorPriceReq majorPriceReq);

    /**
     * 查询大客户数据统计
     * @param startTime
     * @param endTime
     * @return
     */
    AjaxResult selectTotal(LocalDate startTime, LocalDate endTime);

    /**
     * 大客户账单查询
     * @param startTime
     * @param endTime
     * @return
     */
    AjaxResult selectBill(int pageIndex, int pageSize, LocalDate startTime, LocalDate endTime);

    AjaxResult selectMajorPrice(int pageIndex, int pageSize, MajorPriceInput majorPriceInput);

    /**
     * 查询大客户门店管理
     * @return
     */
    AjaxResult selectStoreManage();

    /**
     * 查询大客户关联的门店
     * @param majorMerchantQuery 查询条件
     * @return 门店清单
     */
    AjaxResult majorMerchantDetail(MajorMerchantQuery majorMerchantQuery);

    void majortemplate();

    AjaxResult getTemplateData(Integer adminId, Integer direct, MultipartFile file);

    AjaxResult selectTotalProductStock(Integer pageIndex, Integer pageSize, MajorAreaStoreVO majorAreaStoreVO);

    /**
     * 大客户库存数据导出
     * @param majorAreaStoreVO
     * @return
     */
    AjaxResult exportTotalProductStock(MajorAreaStoreVO majorAreaStoreVO, HttpServletResponse response);

    void majorPriceNotice();

    /**
     * 根据筛选条件下载报价单
     * @param majorPriceInput
     */
    void export(MajorPriceInput majorPriceInput);

    /**
     * 根据筛选条件下载报价单
     * @param majorPriceInput
     */
    void exportSaasMajorPrice(MajorPriceInput majorPriceInput);

    /**
     * 判断majorPrices内是否都是可以插入的
     * @param majorPrices
     * @return
     */
    AjaxResult failCheck(List<MajorPriceVO> majorPrices);

    /**
     * 低价监控
     * @param adminId 大客户id
     * @param areaNo 城市编号
     */
    void lowPriceRemainder(Integer adminId, Integer areaNo);

    /**
     * 商城价变动时判断是否需要触发低价：主要是避免大客户的报价比商城活动价高
     * @param areaNo 城市编号
     * @param sku sku
     */
    void lowPriceWhenPriceChange(Integer areaNo, String sku);

    /**
     * 大客户报价单批量改价
     * @param majorPriceInputList
     * @return
     */
    AjaxResult updateBatch(List<MajorPriceInput> majorPriceInputList);

    /**
     * 商城价变动时实时对报价单调价
     * @param areaNo
     * @param sku
     */
    void realTimeMajorPrice(Integer areaNo, String sku);

    /**
     * 定时对报价单调价
     * @param majorCycle
     */
    void timedMajorPrice(Integer majorCycle);

    /**
     * 报价单自动调价
     * @param majorPrice
     */
    void autoChangeMajorPrice(MajorPriceAutoUpdateVO majorPrice);

    Boolean executeCostInversion(Integer id);

    void sendDingDingMsg(BigDecimal costPrice, BigDecimal price, Integer areaNo, String sku, Integer adminId, Integer warehouseNo, String batch);

    /**
     * 门店账单汇总导出
     * @param startTime
     * @param endTime
     * @param response
     * @return
     */
    void storeBillExport(LocalDate startTime, LocalDate endTime, HttpServletResponse response);

    /**
     * 查询sku是否在有效报价单
     * @param adminId 大客户adminId
     * @param direct 合作方式
     * @param areaNo 城市编号
     * @param sku sku
     * @param categoryId 类目ID
     * @return 报价单信息
     */
    Boolean queryMajorPrice(Integer adminId, Integer direct, Integer areaNo, String sku, Integer categoryId);

    List<MajorPrice> getMajorPriceByAreaNoList(Integer adminId, Integer direct, Collection<Integer> areaNoList, Collection<String> skuList);

    /**
     * 查询用户是否有类目报价单
     * @param adminId 大客户adminId
     * @param direct 合作方式
     * @param areaNo 城市编号
     * @param categoryId categoryId
     * @return 报价单信息
     */
    Boolean queryMajorCategory(Integer adminId, Integer direct, Integer areaNo, Integer categoryId);

    void batchExpireMajorPrice(List<Long> idList);

}
