package net.summerfarm.service.item.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * @Date 2025/4/27 17:11
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PopProductPublishSourceCommand {

    /**
     * 创建来源 1、鲜沐品复制给pop
     */
    private Integer createSource;

    /**
     * 来源编码
     */
    private String sourceCode;
}
