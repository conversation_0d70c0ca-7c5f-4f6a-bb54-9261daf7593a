package net.summerfarm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.MarketCouponSendStatusEnum;
import net.summerfarm.model.domain.Coupon;
import net.summerfarm.model.domain.MarketCouponSend;
import net.summerfarm.model.domain.MarketCouponSendDetail;
import net.summerfarm.model.input.MarketCouponSendQuery;
import net.summerfarm.model.input.MarketCouponSendReq;
import net.summerfarm.model.vo.MarketCouponSendVO;
import net.summerfarm.mq.DtsModel;

public interface MarketCouponSendService {
    /**
     * 给发放商户优惠券并修改发放记录状态
     * @param sendId 发放记录id
     * @param couponSendStatusEnum 发放记录状态枚举类
     */
    void sendMerchantCouponAndUpdateStatus(Long sendId, String handleUserId, MarketCouponSendStatusEnum couponSendStatusEnum);

    /**
     * 查询发放记录列表
     * @param couponSendQuery 查询条件
     * @return 放记录列表
     */
    PageInfo<MarketCouponSendVO> select(int pageIndex, int pageSize, MarketCouponSendQuery couponSendQuery);

    /**
     * 查询发放记录详情
     * @param id 发放记录id
     * @return 详情列表
     */
    PageInfo<MarketCouponSendDetail> detail(Integer pageIndex, Integer pageSize, Long id);

    /**
     * 撤回优惠券
     * @param id
     * @return
     */
    AjaxResult recall(Long id);

    /**
     * 导出优惠券
     * @param id
     * @return
     */
    AjaxResult export(Long id);

    /**
     * 发送营销短信
     * @param id
     */
    void sendSmsToMerchant(Long id);

    /**
     * 审批通过后的营销短信提示
     * @param dtsModel
     */
    void sendAfterAuditSmsToMerchant(DtsModel dtsModel);

    /**
     * @description: 人工发放卡劵审批后续
     * @author: lzh
     * @date: 2023/9/1 17:42
     * @param: [bizId, handlerUserId, auditPass]
     * @return: void
     **/
    void sendMerchantCoupon(Long bizId, String handlerUserId, MarketCouponSendStatusEnum auditPass);

    /**
     * @description: 定时任务人工发放卡劵-针对定时发放卡劵
     * @author: lzh
     * @date: 2023/9/4 14:48
     * @param: [key]
     * @return: void
     **/
    void sendMerchantCouponJob(String key);

    /**
     * @description: 发放记录发放具体卡劵
     * @author: lzh
     * @date: 2023/9/4 15:34
     * @param: [coupon, couponSend]
     * @return: void
     **/
    void sendCoupon(MarketCouponSend couponSend);

    /**
     * @description: 取消发放
     * @author: lzh
     * @date: 2023/9/4 17:42
     * @param: [marketCouponSendReq]
     * @return: java.lang.Boolean
     **/
    Boolean cancel(MarketCouponSendReq marketCouponSendReq);
}
