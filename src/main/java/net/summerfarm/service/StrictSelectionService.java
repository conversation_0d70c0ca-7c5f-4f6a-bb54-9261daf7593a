package net.summerfarm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.StrictSelection;
import net.summerfarm.model.vo.StrictSelectionVO;
import org.springframework.web.multipart.MultipartFile;

public interface StrictSelectionService {
    /**
     * 分页查询严选数据
     *
     * @param pageIndex 页码
     * @param pageSize  分页大小
     * @param query     查询条件
     * @return 分页数据
     */
    PageInfo<? extends StrictSelection> selectPage(int pageIndex, int pageSize, StrictSelectionVO query);

    /**
     * 根据id查询详情
     *
     * @param id 主键
     * @return StrictSelectionVO
     */
    StrictSelectionVO selectById(Integer id);

    /**
     * 新增严选
     *
     * @param instance 严选
     */
    String addStrictSelection(StrictSelectionVO instance);

    /**
     * 修改严选
     *
     * @param instance 严选
     */
    String updateStrictSelection(StrictSelectionVO instance);

    /**
     * 导出模板
     */
    void getTemplate();

    /**
     * 上传数据
     * @param file file
     * @return
     */
    AjaxResult upload(Integer id, MultipartFile file);

    /**
     * 更新严选最新排序
     */
    void autoUpdateSort();

    String checkSelection(StrictSelectionVO instance);

}