package net.summerfarm.service;

import net.summerfarm.enums.AddressSnapshotTypeEnum;
import net.summerfarm.mapper.DeliveryPlanRemarkSnapshotMapper;
import net.summerfarm.model.DTO.merchant.ContactAddressRemark;
import net.summerfarm.model.converter.DeliverPlanRemarkConverter;
import net.summerfarm.model.domain.Contact;
import net.summerfarm.model.domain.DeliveryPlan;
import net.summerfarm.model.domain.crm.DeliveryPlanRemarkSnapshot;
import net.summerfarm.model.vo.DeliveryPlanVO;
import net.summerfarm.model.vo.SampleApplyReviewVO;
import net.summerfarm.model.vo.SampleApplyVO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DeliverPlanRemarkSnapshotService {

    @Resource
    DeliveryPlanRemarkSnapshotMapper deliveryPlanRemarkSnapshotMapper;


    public void mergeDeliveryPlanVOSSnapshot(List<DeliveryPlanVO> deliveryPlanVOS) {
        if (CollectionUtils.isEmpty(deliveryPlanVOS)){
            return;
        }
        List<String> businessIds = deliveryPlanVOS.stream().map(it->it.getId().toString()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(businessIds)){
            return;
        }
        List<DeliveryPlanRemarkSnapshot> deliveryPlanRemarkSnapshots = deliveryPlanRemarkSnapshotMapper.selectByTypeBusinessIds(AddressSnapshotTypeEnum.SnapshotType.DELIVERY_PLAN.ordinal(), businessIds);
       // List<DeliveryPlanRemarkSnapshot> afterSnapshots = deliveryPlanRemarkSnapshotMapper.selectByTypeBusinessIds(AddressSnapshotTypeEnum.SnapshotType.AFTER_SALE.ordinal(), businessIds);
       //  deliveryPlanRemarkSnapshots.addAll(afterSnapshots);
        if (CollectionUtils.isEmpty(deliveryPlanRemarkSnapshots)){
            return;
        }
        Map<String, DeliveryPlanRemarkSnapshot> maps = deliveryPlanRemarkSnapshots.stream().collect(Collectors.toMap(DeliveryPlanRemarkSnapshot::getBusinessId, Function.identity(), (key1, key2) -> key2));
        deliveryPlanVOS.forEach(
                it-> it.setDeliveryPlanRemarkSnapshot(maps.get(it.getId().toString()))
        );
    }


    public void addSampleApplySnapshot(Contact contact, SampleApplyVO samleApplyVO) {
        DeliveryPlanRemarkSnapshot deliveryPlanRemarkSnapshot = DeliverPlanRemarkConverter.toDeliveryPlanRemarkSnapshot(contact);
        deliveryPlanRemarkSnapshot.setBusinessId(samleApplyVO.getSampleId().toString());
        deliveryPlanRemarkSnapshot.setType(AddressSnapshotTypeEnum.SnapshotType.SAMPLE_APPLY.ordinal());
        deliveryPlanRemarkSnapshot.setCreateTime(new Date());
        deliveryPlanRemarkSnapshot.setUpdateTime(new Date());
        deliveryPlanRemarkSnapshotMapper.insertSelective(deliveryPlanRemarkSnapshot);
    }

    public void addDeliveryPlan(Contact contact, DeliveryPlan plan) {
        deliveryPlanRemarkSnapshotMapper.deleteByTypeBusinessId(AddressSnapshotTypeEnum.SnapshotType.DELIVERY_PLAN.ordinal(), plan.getId().toString());
        DeliveryPlanRemarkSnapshot deliveryPlanRemarkSnapshot = DeliverPlanRemarkConverter.toDeliveryPlanRemarkSnapshot(contact);
        deliveryPlanRemarkSnapshot.setBusinessId(plan.getId().toString());
        deliveryPlanRemarkSnapshot.setType(AddressSnapshotTypeEnum.SnapshotType.DELIVERY_PLAN.ordinal());
        deliveryPlanRemarkSnapshot.setCreateTime(new Date());
        deliveryPlanRemarkSnapshot.setUpdateTime(new Date());
        deliveryPlanRemarkSnapshotMapper.insertSelective(deliveryPlanRemarkSnapshot);
    }



    public void mergeSampleApplyReviewVOSnapshot(SampleApplyReviewVO sampleApplyReviewVO) {
        List<DeliveryPlanRemarkSnapshot> deliveryPlanRemarkSnapshots = deliveryPlanRemarkSnapshotMapper.selectByTypeBusinessIds(AddressSnapshotTypeEnum.SnapshotType.SAMPLE_APPLY.ordinal(), Arrays.asList(sampleApplyReviewVO.getSampleId().toString()));
        if (CollectionUtils.isEmpty(deliveryPlanRemarkSnapshots)){
            return;
        }
        String addressRemark = deliveryPlanRemarkSnapshots.get(0).getAddressRemark();
        ContactAddressRemark contactAddressRemark = ContactAddressRemark.initContactAddressRemark(addressRemark);
        sampleApplyReviewVO.setContactAddressRemark(contactAddressRemark);
    }
}
