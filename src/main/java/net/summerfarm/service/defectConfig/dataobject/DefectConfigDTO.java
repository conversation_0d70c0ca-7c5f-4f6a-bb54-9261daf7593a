package net.summerfarm.service.defectConfig.dataobject;

import lombok.*;
import lombok.experimental.FieldDefaults;
import net.summerfarm.service.defectConfig.enums.DefectConfigStateEnum;
import net.summerfarm.service.defectConfig.enums.DefectTypeEnum;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DefectConfigDTO implements Serializable {
    private static final long serialVersionUID = 6537170534238254364L;

    Long id;

    /**
     * 缺陷类型
     * @see DefectTypeEnum
     */
    Integer defectType;

    /**
     * 缺陷描述
     */
    String defectDesc;

    /**
     * 状态 0-删除，1-正常
     * @see DefectConfigStateEnum
     */
    Integer state;
}
