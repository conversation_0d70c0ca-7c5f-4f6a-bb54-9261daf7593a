package net.summerfarm.service.defectConfig.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ReceiptMethodEnum {
    NORMAL(0, "正常入库"),
    PICK(1, "挑选入库"),
    SPECIAL(2, "特批入库"),
    CONCESSION(3, "让步接受"),
    REJECT(4, "拒收"),
    ;

    public static String convert(Integer code) {
        return Arrays.stream(ReceiptMethodEnum.values())
                .filter(o -> o.getCode().equals(code))
                .findFirst().orElse(ReceiptMethodEnum.NORMAL)
                .getDesc();
    }

    Integer code;
    String desc;
}
