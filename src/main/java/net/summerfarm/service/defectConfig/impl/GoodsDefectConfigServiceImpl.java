package net.summerfarm.service.defectConfig.impl;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.BizException;
import net.summerfarm.common.exceptions.ErrorCode;
import net.summerfarm.dao.defectConfig.GoodsDefectConfigDAO;
import net.summerfarm.dao.defectConfig.dataobject.DefectConfigDO;
import net.summerfarm.service.defectConfig.GoodsDefectConfigService;
import net.summerfarm.service.defectConfig.convert.DefectConfigConvert;
import net.summerfarm.service.defectConfig.dataobject.DefectConfigDTO;
import net.summerfarm.service.defectConfig.enums.DefectConfigStateEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GoodsDefectConfigServiceImpl extends BaseService implements GoodsDefectConfigService {

    @Resource
    private GoodsDefectConfigDAO goodsDefectConfigDAO;

    @Override
    public void createDefectConfig(DefectConfigDTO defectConfig) {
        Integer adminId = getAdminId();
        DefectConfigDO defectConfigDo = DefectConfigDO.builder()
                .defectDesc(defectConfig.getDefectDesc())
                .defectType(defectConfig.getDefectType())
                .deletedAt(0L)
                .operator(Objects.nonNull(adminId) ? String.valueOf(adminId) : null)
                .state(defectConfig.getState())
                .build();
        try {
            goodsDefectConfigDAO.save(defectConfigDo);
        } catch (DuplicateKeyException e) {
            throw new BizException(ErrorCode.DEFECT_TYPE_EXIST);
        }
    }

    @Override
    public void updateDefectConfig(DefectConfigDTO defectConfig) {
        Integer adminId = getAdminId();
        DefectConfigDO defectConfigDo = DefectConfigDO.builder()
                .id(defectConfig.getId())
                .defectDesc(defectConfig.getDefectDesc())
                .defectType(defectConfig.getDefectType())
                .deletedAt(0L)
                .operator(Objects.nonNull(adminId) ? String.valueOf(adminId) : null)
                .state(defectConfig.getState())
                .build();
        goodsDefectConfigDAO.update(defectConfigDo);
    }

    @Override
    public void deleteDefectConfig(DefectConfigDTO defectConfig) {
        log.info("删除配置:{}", defectConfig);
        Integer adminId = getAdminId();
        DefectConfigDO defectConfigDo = DefectConfigDO.builder()
                .deletedAt(System.currentTimeMillis())
                .operator(Objects.nonNull(adminId) ? String.valueOf(adminId) : null)
                .state(DefectConfigStateEnum.DELETE.getCode())
                .id(defectConfig.getId())
                .build();
        goodsDefectConfigDAO.update(defectConfigDo);
    }

    @Override
    public List<DefectConfigDTO> selectConfigs() {
        List<DefectConfigDO> list = goodsDefectConfigDAO.list();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        return list.stream().map(DefectConfigConvert.INSTANCE::convertDefectConfigDTO).collect(Collectors.toList());
    }
}
