package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.NotifyDeliveryFeeDTO;
import net.summerfarm.model.DTO.UpdateDeliveryDateDto;
import net.summerfarm.model.DTO.UpdateDeliveryPlanDTO;
import net.summerfarm.model.DTO.ValidateLimitSpuDTO;
import net.summerfarm.model.bo.SetDeliveryPlanResult;
import net.summerfarm.model.bo.UpdateDeliveryPlanResultBO;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.domain.Contact;
import net.summerfarm.model.domain.DeliveryPlan;
import net.summerfarm.model.domain.MsgTemplate;
import net.summerfarm.model.input.DeliveryDateQueryInput;
import net.summerfarm.model.input.OuterAddressReq;
import net.summerfarm.model.vo.DeliveryPlanHeartVo;
import net.summerfarm.model.vo.DeliveryPlanVO;
import net.summerfarm.model.vo.TimingOrderVO;
import net.xianmu.common.result.CommonResult;
import org.apache.poi.ss.usermodel.Workbook;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public interface DeliveryPlanService {

    void tomorrowOrderLock(DeliveryPlanVO deliveryPlanVO, Integer areaNo, MsgTemplate template, List<Admin> toAdmins, List<DeliveryPlanVO> msg);

    void theDayAfterTomorrowLock(DeliveryPlanVO deliveryPlanVO, Integer areaNo, List<DeliveryPlanVO> msg, MsgTemplate template);

    void timingOrderLock(DeliveryPlanVO deliveryPlanVO, Integer areaNo);

    /**
     * 省心送配送计划解冻库存
     * @param deliveryPlanVO
     */
    void timingOrderUnLock(DeliveryPlanVO deliveryPlanVO);
    /**
     * 更新采购，调拨 省心送订单冻结重新
     */
    void timingOrderLockQuantity(String sku, Integer storeNo, LocalDate oldDate, LocalDate newDate, String type);

    /**
     * 获取未配送计划
     * @param contactId 地址id
     * @return
     */
    AjaxResult selectWait(Long contactId);

    /**
     * 批量更新配送计划
     */
    AjaxResult batchUpdateDeliveryDate(UpdateDeliveryDateDto updateDeliveryDateDto);

    /**
     * 取消当前日期和新日期之间的省心送订单
     * @param contactIdList
     * @param nowDeliveryDate
     * @param newDeliveryDate
     * @param cancelHeartList
     * @param dpIdList
     */
    void cancelHeartOrderBetweenDay(List<Integer> contactIdList, LocalDate nowDeliveryDate, LocalDate newDeliveryDate, ArrayList<DeliveryPlanHeartVo> cancelHeartList, ArrayList<Integer> dpIdList);

    /**
     * 批量设置配送计划（省心送）
     * @param updateDeliveryPlanDTO 批量设置配送计划请求实体类
     * @return 请求结果
     */
    AjaxResult batchSetDeliveryPlan(UpdateDeliveryPlanDTO updateDeliveryPlanDTO);

    /**
     * 处理结果集成Excel
     * @param updateDeliveryPlanResultBO
     * @return excel对象
     */
    Workbook handleResultToExcel(UpdateDeliveryPlanResultBO updateDeliveryPlanResultBO);

    /**
     * 校验限定SPU（省心送）
     * @param validateLimitSpuDTO 校验限定SPU请求实体类
     * @return 请求结果
     */
    AjaxResult validateLimitSpu(ValidateLimitSpuDTO validateLimitSpuDTO);

    /**
     * 扣减库存并设置配送计划
     * @param timingOrder 省心送订单
     * @param setDeliveryPlanResult 设置配送计划结果
     * @param contact 联系人地址
     * @param plan 配送计划
     * @return 设置结果
     */
    boolean reduceStoreAndSetDeliveryPlan(TimingOrderVO timingOrder, SetDeliveryPlanResult setDeliveryPlanResult, Contact contact, DeliveryPlan plan);

    /**
     * 发送配送费调整提醒
     * @param notifyDeliveryFeeDTO
     */
    boolean notifyModifyDeliveryFee(NotifyDeliveryFeeDTO notifyDeliveryFeeDTO);


    /**
     * 查询可配送日
     * @param deliveryDateQueryInput
     */
    CommonResult queryCouldDeliveryDate(DeliveryDateQueryInput deliveryDateQueryInput);
}
