package net.summerfarm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.CompanyAccount;
import net.summerfarm.model.vo.CompanyAccountVO;
import org.springframework.web.multipart.MultipartFile;

public interface CompanyAccountService {

    AjaxResult save(MultipartFile file, CompanyAccount companyAccount);

    AjaxResult<PageInfo<CompanyAccountVO>> selectList(int pageIndex, int pageSize, CompanyAccountVO companyAccountVO);

    AjaxResult selectDetail(Integer id);

    AjaxResult select();

    AjaxResult update(CompanyAccount companyAccount);
}
