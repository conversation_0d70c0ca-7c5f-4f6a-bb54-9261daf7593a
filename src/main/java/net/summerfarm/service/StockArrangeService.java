package net.summerfarm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.BatchProveDTO;
import net.summerfarm.model.domain.Activity;
import net.summerfarm.model.param.ArrangeParam;
import net.summerfarm.model.param.pms.PurchaseSkuQuery;
import net.summerfarm.model.vo.ArrangeResultVO;
import net.summerfarm.model.vo.StockArrangeVO;
import net.summerfarm.model.vo.WarehouseBatchProveRecordVO;
import net.summerfarm.model.vo.pms.PurchaseSkuCountVO;
import net.summerfarm.module.pms.model.input.BatchSkuInput;
import net.summerfarm.module.pms.model.vo.SkuWeightVO;
import net.summerfarm.wms.instore.dto.req.StockStorageMqData;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface StockArrangeService {

    /**
     * 新增预约单
     * @param stockArrangeVO
     * @param flag true:扣减可预约数量  false：不扣减
     * @return 入库预约单id(stock_arrange id)
     */
    Integer insert(StockArrangeVO stockArrangeVO, boolean flag);

    /**
     * 校验能否发起新增预约单
     * @param purchasesNo
     * @return
     */
    AjaxResult arrangeCheck(String purchasesNo);

    /**
     * 新增预约单
     * @param record
     * @return
     */
    AjaxResult insertStockArrange(StockArrangeVO record);

    /**
     * 可预约sku项列表
     * @param purchasesNo
     * @return
     */
    AjaxResult selectStockArrange(String purchasesNo);

    /**
     * 可入库sku项列表
     * @param id
     * @return
     */
    AjaxResult selectStockArrange(Integer id);

    /**
     * 关闭预约单
     * @param stockArrangeId
     * @return
     */
    AjaxResult closeStockArrange(Integer stockArrangeId);

    /**
     * 校验可否关闭预约单
     * @param id
     * @return true:显示按钮,false:隐藏按钮
     */
    AjaxResult arrangeTaskCheck(Integer id);

    /**
     * 查询预约单列表
     * @param purchasesNo
     * @return
     */
    AjaxResult selectStockArrangeList(String purchasesNo);

    /**
     * 查询预约单详情
     * @param stockArrangeId
     * @return
     */
    StockArrangeVO selectStockArrangeDetail(Integer stockArrangeId);


    /**
     * 初始化采购单，入库任务入库进度，采购单可预约状态
     * @return
     */
    AjaxResult checkProcessState(Activity activity);

    /**
     * 查询库存仓采购入库在途数量
     * @param warehouseNo
     * @param allocationDate
     * @param nextAllocationDate
     * @return
     */
    @Deprecated
    Map<String,Integer> selectRoadQuantity(Integer warehouseNo, LocalDate allocationDate, LocalDate nextAllocationDate);

    // 修改预约单状态
    void updateStockState(StockArrangeVO stockArrange);

    // 修改采购单
    void updatePurchase(Integer stockArrangeId, StockArrangeVO stockArrange);

    /**
     * 根据入库预约条目批次id(stock_arrange_item_detail)查询证件详情
     * @param stockArrangeItemDetailId 入库预约条目批次id
     * @return 证件信息
     */
    AjaxResult selectBatchProveDetail(Integer stockArrangeItemDetailId);

    /**
     * 查看预约条目详情
     * @param stockArrangeItemId 入库预约条目id
     * @return 效期列表
     */
    AjaxResult selectStockArrangeItemDetail(Integer stockArrangeItemId);

    /**
     * 查看发货详情
     * @param stockArrangeId 发货单号
     * @return 发货详情
     */
    AjaxResult selectSrmStockArrangeDetail(Integer stockArrangeId);

    /**
     * 根据采购单查看发货情况
     * @param purchasesNo 采购单号
     * @return 发货情况
     */
    AjaxResult selectSrmStockArrangeDetail(String purchasesNo);

    /**
     * 查询仓库sku所需证件
     * @param warehouseNo 仓库编号
     * @param sku sku
     * @return 所需证件数组
     */
    AjaxResult selectBatchProveStandard(Integer warehouseNo, String sku);

    AjaxResult update(StockArrangeVO param);

    /**
     * 取消预约单（委托单）
     * @param sourceId 原入库预约单id
     * @param type 类型
     * @return
     */
    AjaxResult cancelDistOrder(String sourceId,Integer type);

    PageInfo<ArrangeResultVO> selectPage(ArrangeParam selectKeys);
    /**
     *
     * @param stockStorageMqData
     */
    void updateDetail(StockStorageMqData stockStorageMqData);

    AjaxResult updateRemark(Integer id, String remark);

    AjaxResult updateCheckReport(BatchProveDTO checkReport);

    WarehouseBatchProveRecordVO selectBatchProveDetailInfo(Integer stockArrangeItemDetailId);

    String getSourceByTaskId(String taskId);

    List<PurchaseSkuCountVO> selectCountByPurchaseSkuList(List<PurchaseSkuQuery> skuQueryList);


    void initHistoryWarehouseData();

    List<SkuWeightVO> selectBatchWeight(BatchSkuInput input);
}
