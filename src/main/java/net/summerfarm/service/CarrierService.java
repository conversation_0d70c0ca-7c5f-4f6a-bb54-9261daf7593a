package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.CarrierAccount;
import net.summerfarm.model.input.CarrierInput;
import net.summerfarm.model.vo.CarrierVo;

import java.util.List;

/**
 * @Classname CarrierService
 * @Description 承运商相关服务.
 * @Date 2021/12/28 14:43
 * @Created by hx
 */
public interface CarrierService {
    /**
     * 分页查询承运商
     * @param carrierInput 承运商入参类
     * @return 承运商数据
     */
    AjaxResult selectCarrier(CarrierInput carrierInput, int pageIndex, int pageSize);

    /**
     * 查看承运商详情
     * @param id 承运商ID
     * @return 承运商详情
     */
    AjaxResult selectCarrierDetail(Long id);

    /**
     * 增加承运商
     * @param carrierInput 承运商入参类
     * @return 成功提示
     */
    AjaxResult addCarrier(CarrierInput carrierInput);

    /**
     * 编辑承运商
     * @param carrierInput 承运商入参类
     * @return 成功提示
     */
    AjaxResult updateCarrier(CarrierInput carrierInput);

    /**
     * 导出承运商
     * @param carrierInput 承运商入参类
     * @return 承运商详情EXECL
     */
    AjaxResult exportCarrier(CarrierInput carrierInput);

    /**
     * 取出所有承运商列表
     * @return
     */
    AjaxResult selectAll(String carrierName);

    /**
     * 根据名称查询承运商信息
     * @param carrierName
     * @return
     */
    CarrierVo queryByName(String carrierName);

    /**
     * 根据税号
     *
     * @param merchantName
     * @param taxNumber
     * @return
     */
    CarrierVo queryByTaxNumber(String merchantName, String taxNumber);

    /**
     * 初始化承运商数据到中间表
     */
    void initializationCarrier();

    CarrierAccount selectCarrierAccount(Integer id);

    List<CarrierVo> selectByCarrierName(String carrierName);
}
