package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.StockInspectDetail;
import net.summerfarm.model.vo.StockInspectDetailVO;

/**
 * <AUTHOR> xiang
 * create at:  2021-11-01
 */
public interface StockInspectDetailService {
    /**
     * 货检列表
     * @param pageIndex
     * @param pageSize
     * @param selectKey
     * @return
     */
    AjaxResult selectInspect(int pageIndex, int pageSize, StockInspectDetail selectKey);

    AjaxResult updateInspect(StockInspectDetailVO selectKey);

    AjaxResult insertInspect(StockInspectDetail selectKey);

    AjaxResult selectInspectDetail(int id);
}
