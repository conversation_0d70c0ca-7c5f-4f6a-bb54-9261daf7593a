package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.MajorCategory;
import net.summerfarm.model.input.MajorCategoryReq;
import net.summerfarm.model.vo.MajorCategoryVO;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/2/1  11:29
 */
public interface MajorCategoryService {

    /**
    * 插入类目报价单
    */
    AjaxResult insertMajorCategory(MajorCategory majorCategory);

    /**
    * 批量插入
    */
    AjaxResult insertMajorCategoryList(List<MajorCategory> majorCategoryList);

    /**
    * 修改类目报价单
    */
    AjaxResult updateMajorCategory(MajorCategory majorCategory);

    /**
    * 查询类目报价单
    */
    AjaxResult selectMajorCategory(Integer pageIndex, Integer pageSize, MajorCategoryVO majorCategory);


    /**
    * 发送钉钉消息提醒
    */
    void sendDingTalkMsg();

    AjaxResult updateBatchMajorCategory(MajorCategoryReq majorCategoryReq);
}
