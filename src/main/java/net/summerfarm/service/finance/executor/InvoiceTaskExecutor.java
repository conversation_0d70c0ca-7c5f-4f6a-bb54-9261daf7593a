package net.summerfarm.service.finance.executor;

import net.summerfarm.biz.finance.bo.BwInvoiceResponseBO;
import net.summerfarm.biz.finance.bo.BwResponseContentInvoiceBO;
import net.summerfarm.biz.finance.config.FinanceInvoiceConfig;
import net.summerfarm.biz.finance.constant.BwResponseCodeConstant;
import net.summerfarm.biz.finance.dto.FinancialInvoiceSellerInfoDTO;
import net.summerfarm.biz.finance.enums.FinancialInvoiceAsyncTaskEnum;
import net.summerfarm.common.exceptions.finance.BwLoginExpirationException;
import net.summerfarm.common.exceptions.finance.BwScanFaceExpirationException;
import net.summerfarm.enums.InvoiceResultEnum;
import net.summerfarm.mapper.FinancialInvoiceAsyncTaskMapper;
import net.summerfarm.mapper.manage.FinancialInvoiceMapper;
import net.summerfarm.model.bo.FinancialInvoiceAsyncTaskBO;
import net.summerfarm.model.domain.FinancialInvoice;
import net.summerfarm.model.domain.FinancialInvoiceAsyncTask;
import net.xianmu.common.exception.ProviderException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Set;

/**
 * @description: 发票任务执行器抽象类
 * @author: George
 * @date: 2024-05-16
 **/
public abstract class InvoiceTaskExecutor {

    private static final Logger log = LoggerFactory.getLogger(InvoiceTaskExecutor.class);
    @Resource
    private FinancialInvoiceAsyncTaskMapper financialInvoiceAsyncTaskMapper;
    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private FinanceInvoiceConfig financeInvoiceConfig;

    public void execute(FinancialInvoiceAsyncTaskBO taskBO) {
        // 1、处理上次的异步taskId
        boolean asyncSuccess = processLastTaskResult(taskBO);
        if (!asyncSuccess) {
            log.info("发票{}上次异步任务未完成或不存在，即将调用具体发票接口", taskBO.getInvoiceId());
            // 2、调用具体的发票接口
            BwInvoiceResponseBO bwInvoiceResponseBO = callInvoiceInterface(taskBO);

            // 3、处理各种结果的返回
            String respCode = bwInvoiceResponseBO.getCode();
            if (isSuccess(respCode)) {
                onSuccess(taskBO);
            } else if (isAsync(respCode)) {
                onAsync(taskBO);
            } else {
                onFail(taskBO);
            }
        }

        // 4、扩展点，子类实现
        afterExecute(taskBO);
    }

    /**
     * 处理上次异步任务（蓝字、红字、下载等）
     * @param task
     * @return
     */
    protected boolean processLastTaskResult(FinancialInvoiceAsyncTaskBO task) {
        String taskId = task.getTaskId();
        if (taskId == null) {
            return false;
        }
        // 调用百旺接口查询结果
        BwInvoiceResponseBO bwInvoiceResponseBO = queryAsyncResult(task);
        task.setBwInvoiceResponseBO(bwInvoiceResponseBO);

        // 查询结果处理
        String responseCode = bwInvoiceResponseBO.getCode();
        if (isSuccess(responseCode)) {
            onSuccess(task);
            return true;
        } else if (isAgain(responseCode)) {
            onAgain(task);
            return true;
        } else {
            onLastTaskFail(task);
            return true;
        }
    }

    private void onAgain(FinancialInvoiceAsyncTaskBO task) {
        Long id = task.getId();
        Long invoiceId = task.getInvoiceId();
        log.warn("任务id:{}, 发票id:{}异步任务查询结果需要再次请求", id, invoiceId);
        // 重置为初始化状态
        financialInvoiceAsyncTaskMapper.updateTaskResult(id, FinancialInvoiceAsyncTaskEnum.Status.INIT.getResult());
    }

    /**
     * 是需要再次请求
     * @param responseCode
     * @return
     */
    protected boolean isAgain(String responseCode) {
        return BwResponseCodeConstant.QUERY_RESULT_NEED_REQUEST_AGAIN.equals(responseCode);
    }

    private void onLastTaskFail(FinancialInvoiceAsyncTaskBO task) {
        // 这里可能是登录失效或者其他异常，需要将本次taskId清除通过重试解决
        Long id = task.getId();
        log.info("任务id:{},发票{}上次异步任务失败，即将清除taskId...", id, task.getInvoiceId());
        financialInvoiceAsyncTaskMapper.removeTaskId(id);

        //失败处理
        onFail(task);
    }

    /**
     * 调用百旺接口
     * @param taskBO
     * @return
     */
    protected abstract BwInvoiceResponseBO callInvoiceInterface(FinancialInvoiceAsyncTaskBO taskBO);

    /**
     * 是否成功
     * 这里只有蓝票可能第一次就成功，也可能是异步，红票、下载发票只有异步
     * @param respCode
     * @return
     */
    protected boolean isSuccess(String respCode) {
        return BwResponseCodeConstant.SUCCESS.equals(respCode);
    }

    /**
     * 是否异步
     * 这里蓝票、红票、下载发票都有可能是异步
     * 返回的异步标识不同，所以需要子类重写
     * @param respCode
     * @return
     */
    protected boolean isAsync(String respCode) {
        return BwResponseCodeConstant.ASYNC_CODE.equals(respCode);
    }

    /**
     * 成功处理
     * @param taskBO
     */
    protected abstract void onSuccess(FinancialInvoiceAsyncTaskBO taskBO);

    /**
     * 异步处理
     * 一般异步都是返回taskId需要缓存2h过期
     * @param taskBO
     */
    protected void onAsync(FinancialInvoiceAsyncTaskBO taskBO) {
        BwInvoiceResponseBO bwInvoiceResponseBO = taskBO.getBwInvoiceResponseBO();
        BwResponseContentInvoiceBO content = bwInvoiceResponseBO.getContent();
        String taskId = content.getTaskId();
        if (taskId == null) {
            log.error("百旺数电发票:{}接口返回异常", taskBO.getInvoiceId(), new ProviderException("百旺数电发票异步TaskID缺失"));
            return;
        }
        // 更新taskID 后续需要查询结果
        log.info("百旺数电发票接口异步, 返回taskId:{}", taskId);
        storageTaskId(taskBO.getId(), taskId);

        // 更新状态为初始化
        financialInvoiceAsyncTaskMapper.updateTaskResult(taskBO.getId(), FinancialInvoiceAsyncTaskEnum.Status.INIT.getResult());
    }

    /**
     * 失败处理
     * @param taskBO
     */
    protected void onFail(FinancialInvoiceAsyncTaskBO taskBO) {
        // 判断是否到达最大重试次数
        boolean reached = reachMaxInvokeCount(taskBO);
        if (!reached) {
            // 更新状态为初始化 后续重试
            financialInvoiceAsyncTaskMapper.updateTaskResult(taskBO.getId(), FinancialInvoiceAsyncTaskEnum.Status.INIT.getResult());
        }

        BwInvoiceResponseBO bwInvoiceResponseBO = taskBO.getBwInvoiceResponseBO();
        String responseCode = bwInvoiceResponseBO.getCode();
        if (responseCode == null) {
            log.error("百旺数电发票:{}, 接口未返回code", taskBO.getInvoiceId(), new ProviderException(bwInvoiceResponseBO.getMessages()));
            return;
        }
        // 登录失效逻辑处理
        FinancialInvoiceSellerInfoDTO sellerInfoDTO = taskBO.getSellerInfoDTO();
        onLoginFail(responseCode, sellerInfoDTO);

        // 这里进行告警，等待下次任务重试
        Integer currentInvokeCount = taskBO.getInvokeCount() + 1;
        String errorMessage = "百旺数电发票接口返回未知的code，message:" + bwInvoiceResponseBO.getMessages();
        if (currentInvokeCount % 10 == 0) {
            log.error("任务id:{}，百旺数电发票接口返回未知的 code:{}, message:{}，请关注", taskBO.getId(), responseCode, bwInvoiceResponseBO.getMessages(), new ProviderException(errorMessage));
        } else {
            log.warn("任务id:{}，百旺数电发票接口返回未知的 code:{}, message:{}，请关注", taskBO.getId(), responseCode, bwInvoiceResponseBO.getMessages(), new ProviderException(errorMessage));
        }
    }

    /**
     * 达到最大重试次数告警并更新任务状态
     * @param taskBO
     */
    private boolean reachMaxInvokeCount(FinancialInvoiceAsyncTaskBO taskBO) {
        Integer maxInvokeCount = financeInvoiceConfig.getMaxTaskInvokeCount();
        Integer currentInvokeCount = taskBO.getInvokeCount() + 1;
        FinancialInvoiceAsyncTask task = new FinancialInvoiceAsyncTask();
        task.setId(taskBO.getId());
        boolean reach = currentInvokeCount >= maxInvokeCount;

        Set<String> knownFailCodes = financeInvoiceConfig.getKnownFailCodes();
        BwInvoiceResponseBO bwInvoiceResponseBO = taskBO.getBwInvoiceResponseBO();
        String responseCode = bwInvoiceResponseBO.getCode();
        boolean isKnownFailCode = false;
        if (!CollectionUtils.isEmpty(knownFailCodes) && knownFailCodes.contains(responseCode)) {
            isKnownFailCode = true;
        }

        if (reach || isKnownFailCode) {
            log.error("发票{}任务执行次数达到最大次数或者明确失败，更新为失败，请关注", taskBO.getInvoiceId(), new ProviderException("任务执行次数达到最大次数或者明确失败，请关注"));
            task.setTaskResult(FinancialInvoiceAsyncTaskEnum.Status.FAIL.getResult());
        }
        task.setInvokeCount(currentInvokeCount);
        financialInvoiceAsyncTaskMapper.updateByPrimaryKeySelective(task);
        return reach || isKnownFailCode;
    }

    private void onLoginFail(String responseCode, FinancialInvoiceSellerInfoDTO sellerInfoDTO) {
        // 登录失效code
        Set<String> loginExpirationCodes = BwResponseCodeConstant.LOGIN_EXPIRATION;
        if (loginExpirationCodes.contains(responseCode)) {
            throw new BwLoginExpirationException("百旺登录失效", sellerInfoDTO.getLoginName(), sellerInfoDTO.getPassword(), sellerInfoDTO.getTaxNumber(), sellerInfoDTO.getAreaCode());
        }
        // 扫脸过期code
        Set<String> scanFaceExpirationCodes = BwResponseCodeConstant.SCAN_FACE_EXPIRATION;
        if (scanFaceExpirationCodes.contains(responseCode)) {
            throw new BwScanFaceExpirationException("百旺扫脸认证过期");
        }
    }

    protected abstract void afterExecute(FinancialInvoiceAsyncTaskBO taskBO);

    protected abstract BwInvoiceResponseBO queryAsyncResult(FinancialInvoiceAsyncTaskBO taskBO);

    private void storageTaskId(Long id, String taskId) {
        FinancialInvoiceAsyncTask task = new FinancialInvoiceAsyncTask();
        task.setId(id);
        task.setTaskId(taskId);
        financialInvoiceAsyncTaskMapper.updateByPrimaryKeySelective(task);
    }

    /**
     * 完结任务
     * @param task
     */
    protected void finishTask(FinancialInvoiceAsyncTaskBO task) {
        // 理论上不会失败，先行提交事务
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            task.setTaskResult(FinancialInvoiceAsyncTaskEnum.Status.FINISHED.getResult());
            task.setInvokeCount(task.getInvokeCount() + 1);
            financialInvoiceAsyncTaskMapper.updateByPrimaryKeySelective(task);
            log.info("发票任务{}状态更新为{}", task.getId(), task.getTaskResult());
            transactionManager.commit(status);
        } catch (Exception e) {
            log.error("发票任务{}状态更新失败，请关注", task.getId(), e);
            transactionManager.rollback(status);
        }
    }
}
