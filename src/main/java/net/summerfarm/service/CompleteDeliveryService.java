package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.CompleteDelivery;
import net.summerfarm.model.vo.CompleteDeliveryVO;
import org.springframework.stereotype.Repository;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2021/07/10
 * 完成配送提醒
 */
@Repository
@Deprecated
public interface CompleteDeliveryService {

    AjaxResult selectKeyAccount();

    AjaxResult addCompleteDelivery(CompleteDeliveryVO completeDeliveryVO);

    AjaxResult select(Integer pageIndex, Integer pageSize, CompleteDelivery completeDelivery);

    AjaxResult update(CompleteDeliveryVO completeDeliveryVO);

    AjaxResult delete(Integer id);

    AjaxResult updateStatus(Integer id, Integer status);

    /**
     * 完成配送提醒，定时发送提醒
     */
    void sendCompleteDelivery();

    /**
     * 未完成配送表格下载
     * @return
     */
    void downLoadCompleteDelivery(LocalDateTime localDateTime, HttpServletResponse response);

    /**
     * 根据城配仓查询行政区域
     * @param storeNo
     * @param address
     * @return
     */
    AjaxResult getCityByStoreNo(Integer id,Integer storeNo, String address,String city);

    /**
     * 查询省市区
     * @param name
     * @return
     */
    AjaxResult getCity(String name,Integer storeNo);

    /**
     * 数据初始化
     * @return
     */
    AjaxResult dataInit();
}
