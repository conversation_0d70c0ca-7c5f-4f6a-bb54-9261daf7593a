package net.summerfarm.service;

import cn.hutool.json.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.request.OapiRobotSendRequest;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.LeaderInfoDTO;
import net.summerfarm.model.domain.DingTalkLinkMsg;

import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.function.Supplier;

/**
 * <AUTHOR> ct
 * create at:  2020/2/26  18:10
 * 钉钉
 */

public interface DingTalkService {

    /**
     * 创建审批流
     */
    void createProcessInstance(String sku, Integer areaNo);

    /**
     * 审批拒绝,同意回掉
     */

    void getDingTalk();
    /**
     * 创建回调接口
     */
    Map<String, String> DingTalkSyncProcess(String signature, String timestamp, String nonce, JSONObject json);

    void addSyncEvent();

    /**
     * 钉钉部门信息更新
     */
    void getDeptMsg();

    void getDeptIdList(List<Long> plist);

    void getDeptDetail(Long id);

    void getStaffDetails(Long id);

    void getStatus();

    void getDeptById();


    void sendBigOrder(String orderNo,String sku,String dpId);

    void updateUrl();

    void dingTalkRobotLink(DingTalkLinkMsg dingTalkLinkMsg);

    void dingTalkRobotTxtAT(String url, String txt, OapiRobotSendRequest.At at);

    /**
     * 发起应收账款钉钉审批流程实例
     * @param title
     * @param rechargeNum
     * @param picturePaths
     * @param remark
     */
    void createAccountReceivableProcessInstance(String title, Double rechargeNum, JSONArray picturePaths, String remark, Integer rechargeId);

    void rechargeHandle(String processInstanceId);


    AjaxResult getByIdStaffDetails(Long id);

    /**
     * 根据用户id查询直系主管id和职位
     * @param userId
     * @return
     */
    String getManagerAndTitle(String userId);

    /**
     * 发送盘盈盘亏预警提醒
     * @param taskId
     * @param type
     * @param msg
     * @return
     */
    void sendCargoDamageMsg(Integer taskId, String type, StringJoiner msg,Integer auditId);

    /**
     * 根据用户id查询主管信息
     * @param userId
     * @return
     */
    LeaderInfoDTO getManagerInfo(String userId);

    /**
     * @description: 门店注销发送钉钉OA审批流
     * @author: lzh
     * @date: 2023/4/25 10:44
     * @param: [id] 门店注销记录ID
     * @return: void
     **/
    void createMerchantCancel(Long id);
}

