package net.summerfarm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.ProductConfig;
import net.summerfarm.model.domain.Products;
import net.summerfarm.model.input.ProductQuery;
import net.summerfarm.model.input.ProductsSaveReq;
import net.summerfarm.model.param.PdNameSearchParam;
import net.summerfarm.model.vo.ProductsCheckVO;
import net.summerfarm.model.vo.ProductsCreateQuery;
import net.summerfarm.model.vo.ProductsVO;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.item.command.PopProductCopyCommand;
import net.summerfarm.service.item.command.PopProductPublishCommand;
import net.summerfarm.service.item.resp.PopProductCopyResp;
import net.summerfarm.service.item.resp.PopProductPublishResp;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Package: net.summerfarm.service
 * @Description: 商品分类业务类
 * @author: <EMAIL>
 * @Date: 2016/7/26
 */
public interface ProductsService {
    /**
     * 分页查询
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @return
     */
    AjaxResult select(int pageIndex, int pageSize, ProductsVO selectKeys);


    /**
     * 商品管理页分页
     * @param pageIndex 当前页
     * @param pageSize 每页记录数
     * @param productQuery 查询对象
     * @return 结果集对象
     */
    AjaxResult selectPage(int pageIndex, int pageSize, ProductQuery productQuery);

    /**
     * 查询对应品牌下的所有商品
     * @param brandId
     * @return
     */
    AjaxResult select(Integer brandId);

    /**
     * 新建商品分类
     * @param productsReq
     * @return
     */
    AjaxResult save(ProductsSaveReq productsReq);


    /**
     * 自动新增采购负责人
     * @param pdId
     */
     void insertResponsible(Long pdId);

    /**
     * 修改商品分类
     * @param id
     * @param productsReq
     * @return
     */
    AjaxResult update(Long id, ProductsSaveReq productsReq);

    /**
     *
     * @param ids
     * @return
     */
    AjaxResult delete(Long[] ids);

    /**
     * 回收spu
     * @param id
     * @return
     */
    AjaxResult recovery(Long id);

    void esDataSyncOrInit();

    /**
     * 批量商品操作
     * @param type 类型：0、新增 1、修改
     * @param file
     * @return
     */
    AjaxResult createProductBatch(Integer type, MultipartFile file);

    /**
     * 插入spu、sku信息
     * @param checkVO
     */
    void insertSpuAndSku(ProductsCheckVO checkVO);

    /**
     * 修改spu、sku
     * @param restoreId 一键还原点
     * @param checkVO
     */
    void updateSpuAndSku(Integer restoreId, ProductsCheckVO checkVO);

    /**
     * 导出模板或报告
     * @param type          类型：0、新增 1、修改
     * @param reportFile  报告文件
     */
    void getTemplateOrReport(Integer type, String reportFile);

    /**
     * 导出SPU/SKU模板
     * @param type          类型：0、新增 1、修改
     *
     */
    AjaxResult getSpuSkuTemplateOrReport(Integer type);

    /**
     * 商品上新邮件通知,定时任务,每天早晨10点执行
     */
    void freshProductsNotice();

    /**
     * 采购上新
     * @param req 商品信息
     * @return 操作结果
     */
    AjaxResult purchaseAddProduct(ProductsSaveReq req);

    /**
     * 运营上新
     * @param req 商品信息
     * @return 操作结果
     */
    AjaxResult operateAddProduct(ProductsSaveReq req);

    /**
     * 商品上新提示
     * @param hours 统计区间
     */
    void createProductReport(Integer hours);

    /**
     * 上新审批数据查询
     *
     * @param pageIndex 页码
     * @param pageSize  分页大小
     * @param query     查询条件
     * @return 上新商品
     */
    AjaxResult selectCreate(int pageIndex, int pageSize, ProductsCreateQuery query);

    /**
     * 上新忽略
     * @param productsVO productsVO
     * @return 操作结果
     */
    AjaxResult auditFail(ProductsVO productsVO);

    /**
     * 商品名模糊查询pdId
     * @param pdName pdName
     * @return 商品名和pdId
     */
    AjaxResult pdNameSearch(String pdName);

    /**
     * 商品名模糊查询pdId
     * @param pdName pdName
     * @return 商品名和pdId
     */
    AjaxResult saasPdNameSearch(String pdName);

    List<Products> saasPdNameSearch(PdNameSearchParam param);

    /**
     * 上新钉钉消息提示
     * @param dtsModel
     */
    void sendNewMessage(DtsModel dtsModel);

    /**
     * 模糊查询商品名称+联动sku
     * @param name 商品名称
     * @param sku sku
     * @return 商品信息
     */
    AjaxResult queryProductName(String name, Integer sku);

    /**
     * 下载填写指南
     * @return
     */
    AjaxResult getCompletionGuide();

    /**
     * 下载批量更新城市售卖信息模板
     * @return
     */
    AjaxResult citySalesInformationTemplate();

    /**
     * 根据id查询
     * @param id
     * @return
     */
    Products selectByPrimaryKey(Long id);

    /**
     * 根据id获取spu
     * @param pdIdList
     * @return
     */
    List<Products> selectListByIdList(List<Long> pdIdList);

    /**
     * 根据二级类目id查询售后规则配置
     *
     * <AUTHOR>
     * @date 2024/8/6 16:16
     * @param secondCategoryId 二级类目id
     * @return List<net.summerfarm.model.ProductConfig>
     */
    List<ProductConfig> listAfterSaleRuleConfig(Long secondCategoryId);


}
