package net.summerfarm.service;

import net.summerfarm.enums.SeriesTypeEnum;
import net.summerfarm.model.domain.SeriesOfArea;
import net.summerfarm.model.domain.SeriesOfSku;
import net.summerfarm.model.vo.SeriesOfAreaVO;
import net.summerfarm.model.vo.SeriesOfSkuVO;

import java.util.List;

public interface SeriesService {

    /**
     * 处理sku聚合
     *
     * @param type     {@link SeriesTypeEnum}
     * @param seriesId 聚合id
     * @param skuList  skuList
     */
    void handleSkuSeries(SeriesTypeEnum type, Integer seriesId, List<? extends SeriesOfSku> skuList);

    /**
     * 查询sku聚合
     *
     * @param type     {@link SeriesTypeEnum}
     * @param seriesId 聚合id
     * @return List<SeriesOfSku>
     */
    List<SeriesOfSkuVO> querySkuSeries(SeriesTypeEnum type, Integer seriesId);

    /**
     * 处理城市聚合
     *
     * @param type     {@link SeriesTypeEnum}
     * @param seriesId 聚合id
     * @param areaList skuList
     */
    void handleAreaNoSeries(SeriesTypeEnum type, Integer seriesId, List<? extends SeriesOfArea> areaList);

    /**
     * 查询城市集合
     *
     * @param type     {@link SeriesTypeEnum}
     * @param seriesId 聚合id
     * @return List<SeriesOfArea>
     */
    List<SeriesOfAreaVO> queryAreaSeries(SeriesTypeEnum type, Integer seriesId);

    /**
     * 更新排序
     * @param id
     */
    void autoUpdateSort(Integer id);

    /**
     * 查询城市名称
     * @param type     {@link SeriesTypeEnum}
     * @param seriesId 聚合id
     * @return List<String>
     */
    List<String> queryAreaNameList(SeriesTypeEnum type, Integer seriesId);
}