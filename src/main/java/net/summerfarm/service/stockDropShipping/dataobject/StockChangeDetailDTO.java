package net.summerfarm.service.stockDropShipping.dataobject;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * 出入库任务明细
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StockChangeDetailDTO implements Serializable {
    private static final long serialVersionUID = -6199402477545762114L;
    /**
     * 任务id
     */
    Integer stockTaskId;

    /**
     * sku
     */
    String sku;

    /**
     * 预约数量
     */
    Integer estimateNum;

    /**
     * 真实数量
     */
    Integer actualNum;
    /**
     * 采购单号
     */
    String batch;

    /**
     * 供应商
     */
    String supplier;

    /**
     *
     */
    Integer supplierId;
}
