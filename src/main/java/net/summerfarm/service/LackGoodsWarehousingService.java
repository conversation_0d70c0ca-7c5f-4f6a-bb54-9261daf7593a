package net.summerfarm.service;

import net.summerfarm.model.domain.StockStorageItemDetail;
import net.summerfarm.model.domain.StockTask;
import net.summerfarm.model.domain.StockTaskItemDetail;
import net.summerfarm.model.input.StockTaskSaleOutReq;

import java.math.BigDecimal;
import java.util.HashMap;

/**
 * Description: <br/>
 * date: 2022/3/31 17:52<br/>
 *
 * <AUTHOR> />
 */
public interface LackGoodsWarehousingService {
    /**
     *  单条数据入库
     * @param req
     * @param task
     * @param amountMap
     * @param processId
     */
    void skuInOutStore(StockTaskSaleOutReq req  , StockTask task, HashMap<String,String> amountMap, Integer processId);


    /**
     * item处理
     * @param req
     * @param stockTaskItemDetail
     * @return
     */
    StockStorageItemDetail handItemDetail(StockTaskSaleOutReq req, StockTaskItemDetail stockTaskItemDetail);

    /**
     * 处理批次信息
     */
    void handleStoreRecord(StockTask stockTask, StockTaskItemDetail stockTaskItemDetail, BigDecimal totalAmount, String sku);
}
