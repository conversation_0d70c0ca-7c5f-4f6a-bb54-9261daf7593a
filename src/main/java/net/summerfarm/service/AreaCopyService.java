package net.summerfarm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.DTO.areacopy.*;
import net.summerfarm.model.domain.AreaCopyDetail;

public interface AreaCopyService {

    /**
     * 生成城市复制单
     * @param sourceAreaNo 源城市编号
     * @param targetAreaNo 应用城市编号
     */
    AjaxResult copy(Integer sourceAreaNo, Integer targetAreaNo);

    /**
     * 确认复制城市（提交复制）
     * @param copyId 城市复制单号
     */
    void confirm(Long copyId);

    /**
     * 编辑复制单详情数据
     * @param detail 复制单中复制的sku数据详情
     */
    void editCopyDetail(CopyDetailDTO detail);

    /**
     * binlog订阅处理城市复制
     * 新增复制单->生成复制单商品数据
     * 复制单状态变化->复制商品数据
     */
    void applyCopyAreaDetail(AreaCopyDetail areaCopyDetail);

    PageInfo<AreaCopyListDTO> list(AreaCopyListQueryDTO areaCopyListQueryDTO);

    PageInfo<CopyDetailListDTO> detail(Long copyId, Integer sortType, Integer pageIndex, Integer pageSize);

    void deleteById(Long copyId);

    AreaCopyDTO detail(Long copyId);
}
