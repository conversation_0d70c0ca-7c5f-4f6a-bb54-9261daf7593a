package net.summerfarm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.ProductLabelValue;
import net.summerfarm.model.domain.Supplier;
import net.summerfarm.model.domain.SupplierAccount;
import net.summerfarm.model.domain.SupplierConnect;
import net.summerfarm.model.input.*;
import net.summerfarm.model.input.purchase.SupplierQueryInput;
import net.summerfarm.model.vo.PurchasesPlanVO;
import net.summerfarm.model.vo.SupplierCoordinationVO;
import net.summerfarm.model.vo.SupplierTenantVO;
import net.summerfarm.model.vo.SupplierVO;
import net.xianmu.common.result.CommonResult;

import java.util.List;
import java.util.Map;

/**
 * Created by wjd on 2017/8/28.
 */
public interface SupplierService {

    /**
     * 新增供应商审核
     * @param supplier 供货商
     */
    AjaxResult insert(SupplierReq supplier);

    /**
     * 更新供应商
     * @param supplier 供货商
     */
    AjaxResult update(SupplierReq supplier);

    /**
     * 新增或修改供应商联系人
     * @param supplierConnect 供货商联系人
     */
    AjaxResult insertOrUpdateConnect(SupplierConnect supplierConnect);

    /**
     * 删除联系人
     * @param id 联系人id
     */
    void deleteConnect(Integer id);

    /**
     * 查询审核通过的供应商
     * @param supplier 供货商查询参数
     */
    List<SupplierReq> select(Supplier supplier);

    /**
     * 查询审核通过的供应商
     * @param supplierReq 供货商查询参数
     */
    AjaxResult selectByPage(SupplierReq supplierReq, int pageIndex, int pageSize);

    /**
     * 供应商详情
     * @param id 供货商id
     */
    AjaxResult selectSupplierRecord(Integer id, PurchasesPlanVO purchasesPlanVO);

    /**
     * 新增或修改供应商联系人
     * @param supplierAccount 供货商联系人
     */
    AjaxResult insertOrUpdateAccount(SupplierAccount supplierAccount);

    /**
     * 删除付款方式
     * @param accountId 付款方式id
     */
    AjaxResult deleteAccount(Integer accountId);

    /**
     * 名称校验（已停用）
     */
    boolean nameCheck(Integer id, String name);

    /**
     * 根据id查询供应商
     */
    Supplier selectByPrimaryKey(Integer id);

    /**
     * 供应商账户详情查询
     * @param id 付款方式id
     */
    List<SupplierAccount> selectAccount(Integer id);

    /**
     * 验证供应商税号，发票销售方名称
     * @param supplierVO
     * @return
     */
    AjaxResult check(SupplierVO supplierVO);

    /**
     * 供应商详情
     * @param id 供应商id
     */
    AjaxResult detail(Integer id);

    /**
     * 下拉框查询(所有)
     */
    AjaxResult selectSupplierName(int pageIndex, int pageSize, Integer valid);

    /**
     * 启用供应商
     */
    void enableSupplier(int id);

    /**
     * 停用供应商
     */
    void closeSupplierAudit(int id);

    /**
     * 查询审核列表
     */
    AjaxResult selectAuditByPage(SupplierReq selectKeys, int pageIndex, int pageSize);

    /**
     * 更新供应商相关材料
     */
    AjaxResult updateRelatedFile(SupplierFileReq supplier);

    /**
     * 更新供应商合作商品
     */
    AjaxResult updateCategory(SupplierCategoryReq supplier);

    /**
     * 更新供应商分级考核
     */
    AjaxResult updateGrade(SupplierGradeReq supplier);

    /**
     * 上传已盖章的合同
     */
    AjaxResult uploadSealedContract(SupplierContractReq supplierContractReq);

    /**
     * 重新发起供应商申请
     */
    AjaxResult restartAudit(SupplierReq supplierReq);

    /**
     * 上传未盖章的合同
     */
    AjaxResult uploadUnsealedContract(SupplierContractReq supplierContractReq);

    /**
     * 定时任务，停用供应商
     */
    AjaxResult disableSupplier();

    /**
     * 定时任务，更新
     */
    AjaxResult batchUpdateManager();

    /**
     * 新增供应商相关材料文件
     */
    AjaxResult insertRelatedFile(SupplierFileReq supplier);

    /**
     * 供应商基础信息分页查询
     */
    AjaxResult pageBaseInfoByInput(SupplierQueryInput queryInput, int pageIndex, int pageSize);

    /**
     * 修改商品标签
     * @param productLabelValue
     * @return
     */
    AjaxResult updateLabel(ProductLabelValue productLabelValue);

    /**
     * 查看商品+标签
     * @param pageIndex
     * @param pageSize
     * @param inventoryReq
     * @return
     */
    AjaxResult purchasingLabel(int pageIndex, int pageSize, InventoryReq inventoryReq);

    /**
     *  初始化供应商数据到中间表
     */
    void initializationSupplier();

    AjaxResult updateQrCodeSwitch(SupplierReq supplier);

    /**
     * 根据供应商id查询密码
     * @param supplier 供应商id
     * @return 密码
     */
    CommonResult<String> queryPasswordBySupplierId(SupplierReq supplier);

    AjaxResult selectWithSaasTenantId(int pageIndex, int pageSize, SaasSupplierReq req);

    AjaxResult saveTenantSupplier(SaasSupplierInput input);

    AjaxResult saasSupplierDetail(Integer supplierId);

    AjaxResult allSuppliers(Long tenantId);

    Map<String,List<SupplierVO>> selectSupplierNameWithSource();

    /**
     * 查询租户下供应商
     *
     * @param tenantId
     * @return
     */
    List<SupplierTenantVO> selectSupplierByTenant(Long tenantId);

    /**
     * 查询供应商协同配置
     *
     * @param supplierId 输入参数
     * @return 协同配置
     */
    SupplierCoordinationVO queryCoordination(Long supplierId);


    /**
     * 更新供应商协同配置
     *
     * @param supplierCoordinationInput 协同配置输入
     * @return 是否成功
     */
    Boolean updateSupplierCoordination(SupplierCoordinationInput supplierCoordinationInput);

    /**
     * 查询供应商分页列表
     *
     * @param supplierReq 请求入参
     * @return 当页数据
     */
    PageInfo<SupplierTenantVO> querySupplierPage(SaasSupplierReq supplierReq);

}
