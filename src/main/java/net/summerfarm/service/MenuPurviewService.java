package net.summerfarm.service;


import net.summerfarm.model.domain.MenuPurview;
import net.summerfarm.model.vo.MenuPurviewVO;

import java.util.List;

/**
* @Author:ct
* @Date:5:49 PM 2019/6/17
*
*/
public interface MenuPurviewService {

    /**
    * 查询所有的菜单权限
    */
    List<MenuPurviewVO> queryAll();

    /**
    * 插入菜单权限
    */
    Integer insertMenuPurview(MenuPurview menuPurview);

    /**
    * 修改菜单权限
    */
    Integer updateMenuPurview(MenuPurview menuPurview);
}
