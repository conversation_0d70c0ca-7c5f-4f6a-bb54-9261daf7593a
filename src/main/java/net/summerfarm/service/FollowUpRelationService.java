package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.FollowUpRelation;
import net.summerfarm.model.input.NearbyReq;
import net.summerfarm.model.input.PrivateSeaInput;
import net.summerfarm.model.vo.MerchantVO;
import net.summerfarm.model.vo.PrivateSeaVO;

import java.util.List;

/**
 * @Package: net.summerfarm.service
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/4/27
 */
public interface FollowUpRelationService {
    /**
     * 批量修改客戶归属bd
     * @param mIds mid集合
     * @param toAdminId 归属bd
     * @param reason 修改原因
     * @param type 是否可以跨白名单操作，1：可以
     * @return ok
     */
    AjaxResult reassign(List<Long> mIds, Integer toAdminId, String reason, Integer type);

    AjaxResult bdAssign(List<Long> mId);

    /**
     * 自动释放用户,更新释放倒计时
     */
    void autoRelease();

    void autoOpenSea();

    /**
     * 公海列表\公海到私海\私海到公海\倒计时
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param select 查询条件
     * @return 列表
     */
    AjaxResult selectOpenSea(int pageIndex, int pageSize, MerchantVO select);

    /**
     * 查询客户归属BD流转记录
     * @param mId
     * @return
     */
    AjaxResult queryRelationRecord(int pageIndex,int pageSize,Integer mId);

    /**
     * 查询私海数据
     * @param privateSeaInput 查询条件
     * @return 私海数据列表
     */
    AjaxResult queryPrivateSea(int pageIndex,int pageSize, PrivateSeaInput privateSeaInput);

    /**
     * 搜索附近的客户
     * @param req 查询条件
     * @return
     */
    AjaxResult merchantNearby(NearbyReq req);

    AjaxResult saveMonthPurmoney(Long mId, String monthPurmoney);

    /**
     * 计算近三个月内客户平均下单周期
     * @param mId 商户id
     * @return 近三个月内客户平均下单周期，单位：天
     */
    int averageOrderCycle(Long mId);

    /**
     * 计算拜访天数和未下单天数，并填充
     * @param merchantVO 商户信息
     */
    void notOrderOrFollow(MerchantVO merchantVO);

    /**
     * 销售与商户关系变动,判断是否已存在跟进关系
     * 在销售-商户关系表中更新(或插入),在流转记录表中插入
     * @param followUpRelation 销售-商户关系
     * @return
     */
    void updateAndInsertFollow(FollowUpRelation followUpRelation);

    /**
     * @description: 根据门店ID获取门店负责人信息
     * @author: lzh
     * @date: 2023/4/25 11:17
     * @param: [mId] 门店ID
     * @return: net.summerfarm.model.domain.FollowUpRelation
     **/
    FollowUpRelation getInfoByMid(Long mId);
}
