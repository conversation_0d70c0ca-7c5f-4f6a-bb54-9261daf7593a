package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.SalePredictionConfig;
import net.summerfarm.model.domain.StockBoard;
import net.summerfarm.model.input.AdminSkuMappingInput;
import net.summerfarm.model.input.StockBoardInput;
import net.summerfarm.model.vo.StockHistorySalesVO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 库存看板业务层
 * @author: qingjun.miao
 * @Date: 2021/09/14
 */
public interface StockService {

    /**
     * 配置预警库存
     * @param sku
     * @param warehouseNo
     * @param warningInventory
     * @return
     */
    AjaxResult updateWarningQuantity(String sku, Integer warehouseNo, Integer warningInventory);

    /**
     * 查询销量预测配置
     * @param type
     * @return
     */
    AjaxResult selectConfig(Integer type);

    /**
     * 配置销量预测配置
     * @param config
     * @return
     */
    AjaxResult predictionRule(SalePredictionConfig config);

    /**
     * 看板数据
     * @param stockBoardInput
     * @return
     */
    AjaxResult selectKanban(StockBoardInput stockBoardInput);

    /**
     * 查询近日销量
     * @param stockBoard
     * @param isFilter
     * @return
     */
    StockBoard getRecentSales(StockBoard stockBoard, Boolean isFilter, Map<String, StockHistorySalesVO> salesMap);

    /**
     * 查询批量缓存sku
     * @param adminId
     * @return
     */
    AjaxResult selectSkuList(Integer adminId);

    /**
     * 新老品判断
     * @param sku
     * @param warehouseNo
     * @return
     */
     Boolean isOld(String sku, Integer warehouseNo);

    /**
     * 获取增量
     * @param board
     * @return
     */
    Integer getIncrementSales(StockBoard board);

    /**
     * 删除近三天外的销量流水
     */
    void deleteSalesRecord();

    /**
     * 导出数据
     * @param list
     * @return
     */
    void export(List<StockBoard> list);

    /**
     * 更新管理员映射sku
     * @param input
     * @return
     */
    AjaxResult updateMapping(AdminSkuMappingInput input);
}
