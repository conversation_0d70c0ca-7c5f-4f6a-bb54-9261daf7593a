package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.Adjustment;
import net.summerfarm.model.input.AdjustmentInput;

/**
 * @description 调整单业务层
 * <AUTHOR>
 * @date 2021/12/4 13:46
 */
public interface AdjustmentService {

    /**
     * 根据条件分页查询调整单
     * @param pageIndex
     * @param pageSize
     * @param input
     * @return
     */
    AjaxResult select(int pageIndex, int pageSize, AdjustmentInput input);

    /**
     * 查询调整单总金额
     * @param input
     * @return
     */
    AjaxResult selectTotalAmount(AdjustmentInput input);

    /**
     * 调整单详情
     * @param adjustNo
     * @return
     */
    AjaxResult selectDetail(String adjustNo);

    /**
     * 调整单审批
     * @param input
     * @return
     */
    AjaxResult approve(AdjustmentInput input);

    /**
     * 新增调整单
     * @param input
     * @return
     */
    AjaxResult saveAdjustment(AdjustmentInput input);


    /**
     * 分摊调整单
     *
     * @param adjustment 调整单
     */
    void apportionAdjustment( Adjustment adjustment );
}
