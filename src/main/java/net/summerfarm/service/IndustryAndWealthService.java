package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.FileDownloadRecord;
import net.summerfarm.model.input.IndustryAndWealthInput;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title: IndustryAndWealthService
 * @date 2022/3/2917:14
 */
public interface IndustryAndWealthService {

    /**
     * 收入模块信息
     *
     * @param industryAndWealthInput
     * @return
     */
    AjaxResult selectIncome(IndustryAndWealthInput industryAndWealthInput);

    /**
     * 收入确认额趋势
     *
     * @param industryAndWealthInput
     * @return
     */
    AjaxResult selectIncomeTrend(IndustryAndWealthInput industryAndWealthInput);

    /**
     * 资金情况
     *
     * @param industryAndWealthInput
     * @return
     */
    AjaxResult selectCapital(IndustryAndWealthInput industryAndWealthInput);

    /**
     * 资金确认额趋势
     *
     * @param industryAndWealthInput
     * @return
     */
    AjaxResult selectCapitalTrend(IndustryAndWealthInput industryAndWealthInput);

    /**
     * 提交计算
     *
     * @param fileDownloadRecord
     * @return
     */
    AjaxResult submitDate(FileDownloadRecord fileDownloadRecord);

    /**
     * 自动上传业财：现结账期明细压缩包
     */
    void autoCreatCurrentIIncome();

    /**
     * 组装上传收入-现结订单单据及明细
     * @param id
     * @param fileName
     * @param nameDetail
     */
    void downloadCurrentIIncome(Long id, String fileName, String nameDetail);

    /**
     * 组装上传收入-账期订单单据
     * @param id
     * @param fileName
     */
    void downloadAccountingPeriod (Long id, String fileName);

    /**
     * 组装资金-现结订单收入单据
     * @param id
     * @param fileName
     */
    void downloadCapital(Long id, String fileName);

    /**
     * 组装上传库存变动记录明细
     * @param id 记录id
     * @param fileName 文件名
     */
    void downloadCurrentCost(Long id, String fileName);

    /**
     * 查询成本板块信息
     * @param industryAndWealthInput 查询条件
     * @return 成本板块信息
     */
    AjaxResult cost(IndustryAndWealthInput industryAndWealthInput);

    /**
     * 查询成本板块变化趋势
     * @param industryAndWealthInput 查询条件
     * @return 成本板块变化趋势
     */
    AjaxResult costTrend(IndustryAndWealthInput industryAndWealthInput);
}
