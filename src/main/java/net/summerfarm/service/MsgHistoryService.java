package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.MsgTemplate;

/**
 * @Package: net.summerfarm.service
 * @Description: 消息记录
 * @author: <EMAIL>
 * @Date: 2016/8/29
 */
public interface MsgHistoryService {
    /**
     * 发消息
     * @param msgTemplate
     * @param modeId
     * @param receiver
     * @return
     */
    AjaxResult send(MsgTemplate msgTemplate, int modeId, String receiver);
}
