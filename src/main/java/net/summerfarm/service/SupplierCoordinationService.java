package net.summerfarm.service;

import net.summerfarm.common.exceptions.ErrorCode;
import net.summerfarm.model.vo.PurchasesResultVO;
import net.summerfarm.model.vo.SupplierCoordinationVO;

/**
 * <AUTHOR>
 * @Description 供应商协同服务
 * @Date create in 2023/3/30 13:43
 */
public interface SupplierCoordinationService<T> {

    /**
     * 业务前置检查是否有必要协同
     * 如：
     * 1、采购订单协同需检查是否为普通采购，是否多供应商
     * 2、入库预约需检查是否供应商
     *
     * @param t 领域模型实体类
     * @return 是否需要协同
     */
    boolean precheck(T t);

    /**
     * 获取供应商ID
     *
     * @param t 领域模型实体类
     * @return 是否协同
     */
    Integer getSupplierId(T t);

    /**
     * 根据单据号获取供应商ID
     *
     * @param documentId 单据id
     * @return 采购单号
     */
    Integer getSupplierId(Long documentId);

    /**
     * 供应商配置是否协同
     *
     * @param supplierCoordinationVO 供应商协同配置
     * @return 是否协同
     */
    boolean isNeedCoordination(SupplierCoordinationVO supplierCoordinationVO);

    /**
     * 当前流程需协同,抛出异常信息
     *
     * @return 异常信息
     */
    ErrorCode throwErrorMsg();
}
