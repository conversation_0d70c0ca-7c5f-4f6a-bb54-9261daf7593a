package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.bo.GoodsCodeBO;
import net.summerfarm.model.domain.GoodsCodeOrder;
import net.summerfarm.model.domain.SkuBatchCode;
import net.summerfarm.model.vo.SkuBatchCodeVO;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2021/12/13  15:16
 */
public interface SkuBatchCodeService {

    /**
    * 查询批次信息
    */
    AjaxResult querySkuBatchCode(String BatchCode);


    /**
     * 查询批次信息
     */
    List<SkuBatchCode> queryBatchSkuBatchCode(List<String> BatchCodes);


    /**
     * 生成条码
     * @param warehouseNo
     * @param skuBatchCode
     */
    void createBatchCode(SkuBatchCode skuBatchCode, Integer warehouseNo);


    /**
     * 获取打印条形码数据信息
     * @param skuBatchCodeVO
     * @return
     */
    AjaxResult selectGoodsCodeBOMsg(SkuBatchCodeVO skuBatchCodeVO);

    /**
     * 获取条码信息
     * @param skuBatchCode
     * @return
     */
    SkuBatchCode selectSkuBatchCod(SkuBatchCode skuBatchCode);

    /**
     * 查询批次信息
     */
    AjaxResult queryDetailSkuBatchCode(String BatchCode);

}
