package net.summerfarm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.input.QuantityChangeRecordPageQuery;
import net.summerfarm.model.param.QuantityChangeRecordQueryVO;
import net.summerfarm.model.vo.FruitAreaSkuVO;
import net.summerfarm.model.vo.QuantityChangeRecordVO;

/**
 * Created by wjd on 2018/11/24.
 */
public interface QuantityRecordService {

    AjaxResult selectChangeList(int pageIndex, int pageSize, QuantityChangeRecordQueryVO selectKeys, Long tenantId);

    /**
     * 查询仓库所在水果sku的库存标准值数据
     * @param pageIndex
     * @param pageSize
     * @param input
     * @return
     */
    AjaxResult queryFruitsDataByAreaNo(int pageIndex, int pageSize, FruitAreaSkuVO input);

    /**
     * 分页查询sku库存变更记录（基于selectChangeList()）
     *
     * <AUTHOR>
     * @date 2023/2/24 16:10
     * @param pageQuery 分页查询条件
     * @return PageInfo<net.summerfarm.model.vo.QuantityChangeRecordVO> 分页结果
     */
    PageInfo<QuantityChangeRecordVO> pageQueryQuantityChangeRecord(QuantityChangeRecordPageQuery pageQuery);
}
