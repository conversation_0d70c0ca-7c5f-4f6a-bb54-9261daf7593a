package net.summerfarm.service.bms;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.bms.BmsPaymentDocument;
import net.summerfarm.model.input.bms.BmsDeliveryReconciliationQuery;

import java.io.IOException;

/**
 * <AUTHOR>
 * @create 2022/8/29
 */
public interface BmsDeliveryReconciliationService {
    /**
     * 分页查询对账单
     * @param param
     * @return
     */
    AjaxResult selectDeliveryReconciliation(BmsDeliveryReconciliationQuery param);

    /**
     * 发起结算打款
     * @param param
     * @return
     */
    AjaxResult upsertPaymentDocument(BmsDeliveryReconciliationQuery param);

    /**
     * 处理对账单审批失败or撤销
     * @param bizId
     * @param handlerUserId
     */
    void auditDeliveryReconciliationRefuse(Long bizId, String handlerUserId);

    /**
     * 处理对账单审批成功
     * @param bizId
     * @param handlerUserId
     */
    void auditDeliveryReconciliationSuccess(Long bizId, String handlerUserId);

    /**
     * 对账单列表导出
     * @param param
     * @return
     */
    void exportDeliveryReconciliationList(BmsDeliveryReconciliationQuery param) throws IOException;

    /**
     * 查询对账单详情
     * @param param
     * @return
     */
    AjaxResult selectDeliveryReconciliationDetail(BmsDeliveryReconciliationQuery param);


    void createAdjustmentProcess(BmsPaymentDocument bmsPaymentDocument, String adminName, String msg, String settleAccountUrl);
}
