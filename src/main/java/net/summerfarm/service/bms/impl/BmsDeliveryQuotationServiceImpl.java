package net.summerfarm.service.bms.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.RequestHolder;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.CommonNumbersEnum;
import net.summerfarm.enums.bms.BmsCalculateTypeEnum;
import net.summerfarm.mapper.bms.*;
import net.summerfarm.mapper.manage.AdminMapper;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.domain.CarrierQuotationArea;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.bms.*;
import net.summerfarm.model.input.bms.BmsDeliveryQuotationQuery;
import net.summerfarm.model.input.bms.BmsQuotationConflictQuery;
import net.summerfarm.model.param.bms.BmsDeliveryQuotationParam;
import net.summerfarm.model.param.bms.BmsQuotationDetailParam;
import net.summerfarm.model.vo.bms.BmsDeliveryQuotationDetailVO;
import net.summerfarm.model.vo.bms.BmsDeliveryQuotationDetailsVO;
import net.summerfarm.model.vo.bms.BmsDeliveryQuotationVO;
import net.summerfarm.model.vo.bms.BmsDeliveryQuoteCalculateCostVO;
import net.summerfarm.service.bms.BmsDeliveryQuotationService;
import net.xianmu.common.exception.ProviderException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/8/18
 */
@Service
public class BmsDeliveryQuotationServiceImpl extends BaseService implements BmsDeliveryQuotationService {

    @Resource
    private BmsDeliveryQuotationMapper bmsDeliveryQuotationMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private BmsQuotationDetailMapper bmsQuotationDetailMapper;
    @Resource
    private BmsDeliveryQuotationDetailMapper bmsDeliveryQuotationDetailMapper;
    @Resource
    private BmsDeliveryQuoteCalculateCostMapper bmsDeliveryQuoteCalculateCostMapper;
    @Resource
    private BmsDeliveryQuotationRegionMapper bmsDeliveryQuotationRegionMapper;
    @Resource
    private BmsDeliveryQuotationAreaMapper bmsDeliveryQuotationAreaMapper;
    @Resource
    private BmsDeliveryQuotaCalculateCostRangeMapper bmsDeliveryQuotaCalculateCostRangeMapper;
    @Resource
    private ConfigMapper configMapper;


    @Override
    public AjaxResult selectDeliveryQuotation(BmsDeliveryQuotationQuery param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        // 处理省市区字段
        List<CarrierQuotationArea> quotationAreas = param.getQuotationAreas();
        if(!CollectionUtils.isEmpty(quotationAreas)){
            List<String> cities = quotationAreas.stream().map(CarrierQuotationArea::getCity).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(cities)){
                param.setCities(cities);
            }
            List<String> districts = quotationAreas.stream().map(CarrierQuotationArea::getArea).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(districts) && !"".equals(districts.get(NumberUtils.INTEGER_ZERO))){
                param.setDistricts(districts);
            }
        }

        List<BmsDeliveryQuotationVO> bmsDeliveryQuotationVos = bmsDeliveryQuotationMapper.select(param);
        bmsDeliveryQuotationVos.forEach(quotation->{
            // 最后一次操作人
            Admin admin = adminMapper.selectByPrimaryKey(Objects.nonNull(quotation.getLastUpdateId()) ? quotation.getLastUpdateId() : quotation.getCreator());
            if (Objects.nonNull(admin)){
                quotation.setLastUpdateAdminName(admin.getRealname());
            }
            if (Objects.isNull(quotation.getUpdateTime())){
                quotation.setUpdateTime(quotation.getCreateTime());
            }
            if (Objects.isNull(quotation.getUpdateTime())){
                quotation.setUpdateTime(quotation.getCreateTime());
            }
            List<BmsDeliveryQuotationRegion> regions = bmsDeliveryQuotationRegionMapper.selectByQuotationId(quotation.getId());
            if (!CollectionUtils.isEmpty(regions)) {
                List<String> districts = regions.stream().map(BmsDeliveryQuotationRegion::getArea).collect(Collectors.toList());
                quotation.setDistricts(districts);
            }
        });
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(bmsDeliveryQuotationVos));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveDeliveryQuotation(BmsDeliveryQuotationParam param) {
        //参数校验
        AjaxResult checkResult = checkParam(param);
        if (!checkResult.isSuccess()){
            return checkResult;
        }
        List<CarrierQuotationArea> quotationAreas = param.getQuotationAreas();
        BmsDeliveryQuotationArea area = bmsDeliveryQuotationAreaMapper.selectExist(param.getServiceArea());
        CarrierQuotationArea quotationArea = quotationAreas.get(NumberUtils.INTEGER_ZERO);
        if (Objects.isNull(area)){
            // 新增服务区域
            bmsDeliveryQuotationAreaMapper.insert(param);
        }else{
            param.setServiceAreaId(area.getId());
        }

        // 基础信息
        param.setCreator(getAdminId());
        param.setProvince(quotationArea.getProvince());
        param.setCity(quotationArea.getCity());
        bmsDeliveryQuotationMapper.save(param);

        Iterator<CarrierQuotationArea> iterator = quotationAreas.iterator();
        while (iterator.hasNext()){
            CarrierQuotationArea next = iterator.next();
            if (StringUtils.isEmpty(next.getArea())){
                iterator.remove();
            }
            next.setId(param.getId().longValue());
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(quotationAreas)){
            bmsDeliveryQuotationRegionMapper.batchInsert(quotationAreas);
        }

        // 报价详情
        List<BmsDeliveryQuotationDetail> quotationDetails = param.getQuotationDetails();
        quotationDetails.forEach(detail-> detail.setBmsDeliveryQuotationId(param.getId()));
        bmsDeliveryQuotationDetailMapper.batchInsertDetails(quotationDetails);

        // 计费模型
        List<BmsDeliveryQuoteCalculateCostVO> calculateCostList = param.getCalculateCosts();
        calculateCostList.forEach(cost-> cost.setBmsDeliveryQuotationId(param.getId()));
        bmsDeliveryQuoteCalculateCostMapper.batchInsertCosts(calculateCostList);

        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateDeliveryQuotation(BmsDeliveryQuotationParam param) {
        //参数校验
        AjaxResult checkResult = checkParam(param);
        if (!checkResult.isSuccess()){
            return checkResult;
        }
        //移除关联表相关数据
        bmsDeliveryQuotationDetailMapper.deleteByQuotationId(param.getId());
        //更新时保留原来有的费用模型id
        List<BmsDeliveryQuoteCalculateCostVO> updateList = param.getCalculateCosts().stream().filter(f->Objects.nonNull(f.getId())).collect(Collectors.toList());
        List<Integer> updateIds = updateList.stream().map(BmsDeliveryQuoteCalculateCostVO::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(updateIds)) {
            bmsDeliveryQuoteCalculateCostMapper.deleteByQuotationId(param.getId(), updateIds);
        }else {
            bmsDeliveryQuoteCalculateCostMapper.deleteAllByQuotationId(param.getId());
        }
        bmsDeliveryQuotationRegionMapper.deleteByQuotationId(param.getId());
        //更新公式
        if (!CollectionUtils.isEmpty(updateList)) {
            updateList.forEach(f -> bmsDeliveryQuoteCalculateCostMapper.updateById(f));
        }
        //然后新增
        List<CarrierQuotationArea> quotationAreas = param.getQuotationAreas();
        CarrierQuotationArea quotationArea = quotationAreas.get(NumberUtils.INTEGER_ZERO);
        Iterator<CarrierQuotationArea> iterator = quotationAreas.iterator();
        while (iterator.hasNext()){
            CarrierQuotationArea next = iterator.next();
            if (StringUtils.isEmpty(next.getArea())){
                iterator.remove();
            }
            next.setId(param.getId().longValue());
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(quotationAreas)){
            bmsDeliveryQuotationRegionMapper.batchInsert(quotationAreas);
        }
        // 报价详情
        List<BmsDeliveryQuotationDetail> quotationDetails = param.getQuotationDetails();
        quotationDetails.forEach(detail-> detail.setBmsDeliveryQuotationId(param.getId()));
        bmsDeliveryQuotationDetailMapper.batchInsertDetails(quotationDetails);
        // 计费模型
        List<BmsDeliveryQuoteCalculateCostVO> calculateCostList = param.getCalculateCosts();
        calculateCostList.forEach(cost-> cost.setBmsDeliveryQuotationId(param.getId()));
        List<BmsDeliveryQuoteCalculateCostVO> insertList = calculateCostList.stream().filter(f -> Objects.isNull(f.getId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(insertList)){
        bmsDeliveryQuoteCalculateCostMapper.batchInsertCosts(insertList);
        }
        //更新报报价形式0择优报价，1组合报价
        param.setProvince(quotationArea.getProvince());
        param.setCity(quotationArea.getCity());
        param.setLastUpdaterId(getAdminId());
        bmsDeliveryQuotationMapper.update(param);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveQuotationDetail(BmsQuotationDetailParam param) {
        Integer count = bmsQuotationDetailMapper.checkDetailExist(param.getQuoteName().trim(), param.getSourceType());
        if (count > NumberUtils.INTEGER_ZERO) {
            return AjaxResult.getErrorWithMsg("报价字段命名重复");
        }
        bmsQuotationDetailMapper.save(param);
        return AjaxResult.getOK(param);
    }

    @Override
    public AjaxResult selectQuotationDetail(BmsQuotationDetailParam param) {
        List<BmsQuotationDetail> result = bmsQuotationDetailMapper.select(param);
        return AjaxResult.getOK(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult invalidDeliveryQuotation(BmsDeliveryQuotationParam param) {
        bmsDeliveryQuotationMapper.delete(param.getId(),getAdminId());
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectDeliveryQuotationDetail(Integer id) {
        BmsDeliveryQuotationVO quotationVO= bmsDeliveryQuotationMapper.selectById(id);
        BmsDeliveryQuotationDetailsVO result = new BmsDeliveryQuotationDetailsVO();
        List<BmsDeliveryQuotationRegion> regions = bmsDeliveryQuotationRegionMapper.selectByQuotationId(quotationVO.getId());
        List<String> districts = regions.stream().map(BmsDeliveryQuotationRegion::getArea).collect(Collectors.toList());
        result.setServiceArea(quotationVO.getServiceArea());
        result.setProvince(quotationVO.getProvince());
        result.setCity(quotationVO.getCity());
        result.setDistricts(districts);
        result.setQuotaType(quotationVO.getQuotaType());
        result.setCarrierName(quotationVO.getCarrierName());
        result.setStoreName(quotationVO.getStoreName());
        result.setQuotaForm(quotationVO.getQuotaForm());
        result.setStoreNo(quotationVO.getStoreNo());
        result.setStatus(quotationVO.getStatus());
        List<BmsDeliveryQuotationDetailVO> details = bmsDeliveryQuotationDetailMapper.selectByQuotationId(id);
        result.setQuotationDetails(details);
        List<BmsDeliveryQuoteCalculateCostVO> calculateCostVos = bmsDeliveryQuoteCalculateCostMapper.selectByQuotationId(id);
        result.setQuoteCalculateCosts(calculateCostVos);
        return AjaxResult.getOK(result);
    }

    @Override
    public AjaxResult selectQuotationArea() {
        List<BmsDeliveryQuotationArea> result = bmsDeliveryQuotationAreaMapper.selectServiceArea();
        return AjaxResult.getOK(result);
    }

    @Override
    public void exportDeliveryQuotationList(BmsDeliveryQuotationQuery param) throws IOException {

        List<CarrierQuotationArea> quotationAreas = param.getQuotationAreas();
        if(!CollectionUtils.isEmpty(quotationAreas)){
            List<String> cities = quotationAreas.stream().map(CarrierQuotationArea::getCity).collect(Collectors.toList());
            param.setCities(cities);
            List<String> districts = quotationAreas.stream().map(CarrierQuotationArea::getArea).collect(Collectors.toList());
            param.setDistricts(districts);
        }
        List<BmsDeliveryQuotationVO> bmsDeliveryQuotationVos = bmsDeliveryQuotationMapper.select(param);
        bmsDeliveryQuotationVos.forEach(quotation -> {
            // 最后一次操作人
            Admin admin = adminMapper.selectByPrimaryKey(Objects.nonNull(quotation.getLastUpdateId()) ? quotation.getLastUpdateId() : quotation.getCreator());
            quotation.setLastUpdateAdminName(admin.getRealname());
            if (Objects.isNull(quotation.getUpdateTime())) {
                quotation.setUpdateTime(quotation.getCreateTime());
            }
            List<BmsDeliveryQuotationRegion> regions = bmsDeliveryQuotationRegionMapper.selectByQuotationId(quotation.getId());
            StringJoiner joiner = new StringJoiner("/");
            regions.forEach(region -> {
                joiner.add(region.getArea());
            });
            quotation.setDistrict(joiner.toString());
        });

        List<Integer> quotationIds = bmsDeliveryQuotationVos.stream().map(BmsDeliveryQuotation::getId).collect(Collectors.toList());
        List<String> quotationNames = bmsDeliveryQuotationDetailMapper.selectAllDetail(quotationIds);
        List<String> calculateNames= bmsDeliveryQuoteCalculateCostMapper.selectAllCalculateName(quotationIds);
        Workbook workbook = new HSSFWorkbook();
        Sheet trafficSheet = workbook.createSheet("报价单");
        Row trafficTitle = trafficSheet.createRow(0);
        trafficTitle.createCell(0).setCellValue("承运商");
        trafficTitle.createCell(1).setCellValue("服务区域");
        trafficTitle.createCell(2).setCellValue("服务城配仓");
        trafficTitle.createCell(3).setCellValue("服务省/市");
        trafficTitle.createCell(4).setCellValue("服务行政区/县");
        trafficTitle.createCell(5).setCellValue("报价类型");
        trafficTitle.createCell(6).setCellValue("报价形式");
        trafficTitle.createCell(7).setCellValue("报价单状态");
        int titleIndex = 8;
        for (String s : quotationNames) {
            trafficTitle.createCell(titleIndex).setCellValue(s);
            titleIndex++;
        }
        int partitionIndex= titleIndex;
        for (String calculateName : calculateNames) {
            trafficTitle.createCell(titleIndex).setCellValue(calculateName);
            titleIndex++;
        }

        int rowIndex = 1;
        for (BmsDeliveryQuotationVO quotationVo : bmsDeliveryQuotationVos) {
            Row row = trafficSheet.createRow(rowIndex);
            row.createCell(0).setCellValue(quotationVo.getCarrierName());
            row.createCell(1).setCellValue(quotationVo.getServiceArea());
            row.createCell(2).setCellValue(quotationVo.getStoreName());
            row.createCell(3).setCellValue(quotationVo.getProvince() + "/" + quotationVo.getCity());
            row.createCell(4).setCellValue(quotationVo.getDistrict());
            row.createCell(5).setCellValue(Objects.equals(0, quotationVo.getQuotaType()) ? "内区" : "外区");
            row.createCell(6).setCellValue(Objects.equals(0, quotationVo.getQuotaForm()) ? "择优报价" : "组合报价");
            row.createCell(7).setCellValue(Objects.equals(0, quotationVo.getStatus()) ? "正常" : "作废");
            int rowCellIndex = 8;
            List<BmsDeliveryQuotationDetailVO> detailVos = bmsDeliveryQuotationDetailMapper.selectByQuotationId(quotationVo.getId());

            List<BmsDeliveryQuoteCalculateCostVO> costVos = bmsDeliveryQuoteCalculateCostMapper.selectByQuotationId(quotationVo.getId());

            Map<Integer, List<BmsDeliveryQuotaCalculateCostRange>> rangeInfoMap = bmsDeliveryQuotaCalculateCostRangeMapper.batchQueryByQuoteCalculateCostIdList(
                    costVos.stream().map(BmsDeliveryQuoteCalculateCostVO::getId).collect(Collectors.toList()))
                    .stream().collect(Collectors.groupingBy(BmsDeliveryQuotaCalculateCostRange::getQuoteCalculateCostId));

            if (!CollectionUtils.isEmpty(rangeInfoMap)) {
                for (BmsDeliveryQuoteCalculateCostVO costVo : costVos) {
                    if (rangeInfoMap.containsKey(costVo.getId())) {
                        costVo.setRangeInfo(rangeInfoMap.get(costVo.getId()));
                    }
                }
            }

            if (CollectionUtils.isEmpty(detailVos) && CollectionUtils.isEmpty(costVos)) {
                continue;
            }
            // 报价详情字段
            List<BmsDeliveryQuotationDetailVO> quotationDetailVos = detailVos
                    .stream().filter(detail -> StringUtils.isNotBlank(detail.getQuoteName())).collect(Collectors.toList());
            Map<String, BmsDeliveryQuotationDetailVO> map = quotationDetailVos.stream().collect(Collectors.toMap(BmsDeliveryQuotationDetailVO::getQuoteName, Function.identity(), (o1, o2) -> o1));
            // 计费模型字段
            List<BmsDeliveryQuoteCalculateCostVO> costDetailVos = costVos
                    .stream().filter(detail -> StringUtils.isNotBlank(detail.getCalculateName())).collect(Collectors.toList());
            Map<String, BmsDeliveryQuoteCalculateCostVO> costMap = costDetailVos.stream().collect(Collectors.toMap(BmsDeliveryQuoteCalculateCostVO::getCalculateName, Function.identity(), (o1, o2) -> o1));

            String stringCellValue = trafficSheet.getRow(0).getCell(rowCellIndex).getStringCellValue();
            while (Objects.nonNull(stringCellValue)) {
                BmsDeliveryQuotationDetailVO detailsVO = map.get(stringCellValue);
                if (Objects.nonNull(detailsVO)) {
                    row.createCell(rowCellIndex).setCellValue(String.valueOf(Objects.nonNull(detailsVO.getAmount()) ? detailsVO.getAmount() : ""));
                } else {
                    row.createCell(rowCellIndex).setCellValue("");
                }

                rowCellIndex++;
                stringCellValue = trafficSheet.getRow(0).getCell(rowCellIndex).getStringCellValue();

                if (rowCellIndex == partitionIndex) {
                    break;
                }
            }
            String calculateNameTitle = trafficSheet.getRow(0).getCell(rowCellIndex).getStringCellValue();
            while (Objects.nonNull(calculateNameTitle)) {
                BmsDeliveryQuoteCalculateCostVO detailsVO = costMap.get(calculateNameTitle);
                if (Objects.nonNull(detailsVO)) {
                    if (!CollectionUtils.isEmpty(detailsVO.getRangeInfo())) {
                        StringBuffer stringBuffer = new StringBuffer();
                        for (BmsDeliveryQuotaCalculateCostRange bmsDeliveryQuotaCalculateCostRange : detailsVO.getRangeInfo()) {
                            stringBuffer.append(bmsDeliveryQuotaCalculateCostRange.getLowerLimit()+ " <= " +bmsDeliveryQuotaCalculateCostRange.getQuoteCalculateCostName()+" < "+ bmsDeliveryQuotaCalculateCostRange.getUpperLimit()).append(" 公式：").append(bmsDeliveryQuotaCalculateCostRange.getFormula()).append("\n");
                        }
                        CellStyle cellStyle = workbook.createCellStyle();
                        cellStyle.setWrapText(true);
                        Cell cell = row.createCell(rowCellIndex);
                        cell.setCellValue(stringBuffer + " (" + BmsCalculateTypeEnum.getDescByCode(detailsVO.getCalculateType()) + ") ");
                        cell.setCellStyle(cellStyle);
                    }else {
                        row.createCell(rowCellIndex).setCellValue(Objects.nonNull(detailsVO.getFormula()) ? detailsVO.getFormula() + " (" + BmsCalculateTypeEnum.getDescByCode(detailsVO.getCalculateType()) + ") " : "");
                    }
                } else {
                    row.createCell(rowCellIndex).setCellValue("");
                }
                rowCellIndex++;
                try {
                    calculateNameTitle = trafficSheet.getRow(0).getCell(rowCellIndex).getStringCellValue();
                } catch (NullPointerException ignored) {
                    break;
                }
            }

            rowIndex++;
        }


        String fileName = "城配报价单" + LocalDate.now() + ".xls";
        ExcelUtils.outputExcel(workbook, fileName, RequestHolder.getResponse());
    }

    @Override
    public AjaxResult selectQuotationAreaExist(BmsDeliveryQuotationParam param) {
        List<String>  districts= bmsDeliveryQuotationRegionMapper.select(param);
        return AjaxResult.getOK(districts);
    }

    @Override
    public AjaxResult queryQuotationConflict(BmsQuotationConflictQuery param) {
//        checkConflictParam(param);
        //根据仓库编号，查出所有报价单，查询所有计费模型中费用名称相同的数据，校验计费模型列表查询是否存在冲突
        List<BmsDeliveryQuotationVO> bmsDeliveryQuotationVoList = bmsDeliveryQuotationMapper.selectByStoreNo(param);
        if (CollectionUtils.isEmpty(bmsDeliveryQuotationVoList)){
            return AjaxResult.getOK();
        }
        String storeName = Global.storeMap.get(param.getStoreNo());
        //计费逻辑冲突集合
        List<BmsDeliveryQuoteCalculateCostVO> conflictList = Lists.newArrayList();
        //根据报价单id查出所有计费模型中费用名称相同的数据
        List<Integer> quotationIds = bmsDeliveryQuotationVoList.stream().map(BmsDeliveryQuotationVO::getId).collect(Collectors.toList());
        //更新时过滤自己这个报价单
        if (Objects.nonNull(param.getId())){
            quotationIds.remove(param.getId());
        }

        List<String> calculateNames = param.getCalculateCosts().stream().map(BmsDeliveryQuoteCalculateCostVO::getCalculateName).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(quotationIds) && !CollectionUtils.isEmpty(calculateNames)){
            Map<String, List<BmsDeliveryQuoteCalculateCostVO>> bmsDeliveryQuoteCalculateCostMap = bmsDeliveryQuoteCalculateCostMapper.selectByQuotataionIds(quotationIds, calculateNames).stream().collect(Collectors.groupingBy(BmsDeliveryQuoteCalculateCostVO::getCalculateName));
            for (BmsDeliveryQuoteCalculateCostVO bmsDeliveryQuoteCalculateCostVO : param.getCalculateCosts()) {
                List<BmsDeliveryQuoteCalculateCostVO> dbCalculateCost = bmsDeliveryQuoteCalculateCostMap.get(bmsDeliveryQuoteCalculateCostVO.getCalculateName());
                ////且计算逻辑不同且参数中的计费模型
                if (!CollectionUtils.isEmpty(dbCalculateCost) && !dbCalculateCost.get(CommonNumbersEnum.ZERO.getNumber()).getCalculateType().equals(bmsDeliveryQuoteCalculateCostVO.getCalculateType())) {
                    bmsDeliveryQuoteCalculateCostVO.setStoreName(storeName);
                    conflictList.add(bmsDeliveryQuoteCalculateCostVO);
                }
            }
        }
        return AjaxResult.getOK(conflictList);
    }

    private void checkConflictParam(BmsQuotationConflictQuery param) {
        Map<String, List<BmsDeliveryQuoteCalculateCostVO>> paramMap = param.getCalculateCosts().stream().collect(Collectors.groupingBy(BmsDeliveryQuoteCalculateCostVO::getCalculateName));
        for (Map.Entry<String, List<BmsDeliveryQuoteCalculateCostVO>> map : paramMap.entrySet()) {
            if (map.getValue().size() > 1){
                throw new ProviderException("计费模型中存在相同费用名称：" + map.getKey());
            }
        }
    }

    private List<List<String>> detailExcelDynamicHead(List<String> fieldNames) {
        List<List<String>> list = Lists.newArrayList();
        for (String fieldName : fieldNames) {
            List<String> head = Lists.newArrayList();
            head.add(fieldName);
            list.add(head);
        }
        return list;
    }

    private AjaxResult checkParam(BmsDeliveryQuotationParam param){
        List<CarrierQuotationArea> quotationAreas = param.getQuotationAreas();
        if (CollectionUtils.isEmpty(quotationAreas)){
            return AjaxResult.getErrorWithMsg("服务区域为空");
        }
        if (CollectionUtils.isEmpty(param.getQuotationDetails())||CollectionUtils.isEmpty(param.getCalculateCosts())){
            return AjaxResult.getErrorWithMsg("报价详情和计费模型为必填");
        }

        List<BmsDeliveryQuoteCalculateCostVO> calculateCosts = param.getCalculateCosts();
        // 校验计费模型中报价字段都存在
        for (BmsDeliveryQuoteCalculateCostVO cost : calculateCosts) {
            List<Integer> bmsCalculationItemIds = cost.getBmsCalculationItemIds();
            Config config = configMapper.selectOne("bms_delivery_quotation_system_field");
            String[] split = config.getValue().split(",");
            List<Integer> checkItemIds = param.getQuotationDetails().stream().map(BmsDeliveryQuotationDetail::getBmsCalculationItemId).collect(Collectors.toList());
            for (String id : split) {
                checkItemIds.add(Integer.valueOf(id));
            }
            if (CollectionUtils.isEmpty(bmsCalculationItemIds) || CollectionUtils.isEmpty(checkItemIds)) {
                continue;
            }
            if (!checkItemIds.containsAll(bmsCalculationItemIds)) {
                return AjaxResult.getErrorWithMsg(cost.getCalculateName() + "计费模型字段缺失");
            }
        }

        Set<String> cities = quotationAreas.stream().map(CarrierQuotationArea::getCity).collect(Collectors.toSet());
        if (!Objects.equals(NumberUtils.INTEGER_ONE,cities.size())){
            return AjaxResult.getErrorWithMsg("只能选取一个行政服务市");
        }
        CarrierQuotationArea quotationArea = quotationAreas.get(NumberUtils.INTEGER_ZERO);
        // 校验报价单服务区县是否重复
        List<BmsDeliveryQuotation> quotations = bmsDeliveryQuotationMapper.selectByCity(quotationArea.getCity());
        //中山市,东莞市,潜江市,仙桃市,天门市特殊逻辑处理
        Config noAreaCity = configMapper.selectOne("NO_AREA_CITY");
        if (noAreaCity.getValue().contains(quotationArea.getCity())){
            for (BmsDeliveryQuotation quotation : quotations) {
                if (quotation.getCity().contains(quotationArea.getCity()) && (Objects.isNull(param.getId()) ||  (Objects.nonNull(param.getId()) &&  !param.getId().equals(quotation.getId())))) {
                    return AjaxResult.getErrorWithMsg("报价单已存在" + quotationArea.getCity() + "行政服务区/县");
                }
            }
        }
        List<String> areas = quotationAreas.stream().map(CarrierQuotationArea::getArea).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        for (BmsDeliveryQuotation quotation : quotations) {
            List<BmsDeliveryQuotationRegion> regions = bmsDeliveryQuotationRegionMapper.selectByQuotationId(quotation.getId());
            for (BmsDeliveryQuotationRegion region : regions) {
                if (areas.contains(region.getArea()) && (Objects.isNull(param.getId()) ||  (Objects.nonNull(param.getId()) &&  !param.getId().equals(quotation.getId())))) {
                    return AjaxResult.getErrorWithMsg("报价单已存在" + region.getArea() + "行政服务区/县");
                }
            }
        }


        return AjaxResult.getOK();
    }


}
