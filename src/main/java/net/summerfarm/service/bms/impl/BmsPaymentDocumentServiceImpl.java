package net.summerfarm.service.bms.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.constant.dingding.ProcessInstanceBizTypeEnum;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.dingding.bo.DingdingFormBO;
import net.summerfarm.dingding.bo.ProcessInstanceCreateBO;
import net.summerfarm.dingding.exception.DingdingProcessException;
import net.summerfarm.dingding.service.DingdingProcessInstanceService;
import net.summerfarm.enums.CommonNumbersEnum;
import net.summerfarm.enums.FinanceExpenseTypeEnum;
import net.summerfarm.enums.PurchaseInvoiceEnum;
import net.summerfarm.enums.bms.BmsInvoiceStatusEnum;
import net.summerfarm.enums.bms.BmsPaymentStatusEnum;
import net.summerfarm.mapper.FinancePaymentOrderMapper;
import net.summerfarm.mapper.bms.*;
import net.summerfarm.mapper.manage.CarrierAccountMapper;
import net.summerfarm.mapper.manage.CarrierMapper;
import net.summerfarm.mapper.manage.PurchaseInvoiceMapper;
import net.summerfarm.mapper.tms.CarrierInvoiceMapper;
import net.summerfarm.model.domain.Carrier;
import net.summerfarm.model.domain.CarrierAccount;
import net.summerfarm.model.domain.FinancePaymentOrder;
import net.summerfarm.model.domain.PurchaseInvoice;
import net.summerfarm.model.domain.bms.BmsDeliveryReconciliation;
import net.summerfarm.model.domain.bms.BmsPaymentInvoiceRel;
import net.summerfarm.model.domain.bms.BmsQuotationProcess;
import net.summerfarm.model.domain.easyexcel.bms.BmsPaymentDocumentListExcel;
import net.summerfarm.model.domain.tms.CarrierInvoice;
import net.summerfarm.model.input.bms.BmsDeliveryReconciliationQuery;
import net.summerfarm.model.input.bms.BmsPaymentDocumentQuery;
import net.summerfarm.model.input.bms.BmsSettleAccountQuery;
import net.summerfarm.model.param.bms.BmsPaymentDocumentParam;
import net.summerfarm.model.vo.CarrierVo;
import net.summerfarm.model.vo.PurchaseInvoiceVO;
import net.summerfarm.model.vo.bms.BmsDeliveryReconciliationVO;
import net.summerfarm.model.vo.bms.BmsPaymentDocumentVO;
import net.summerfarm.model.vo.bms.BmsSettleAccountVO;
import net.summerfarm.module.bms.common.model.OperatorBO;
import net.summerfarm.module.bms.facade.BmsServiceClientFacade;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.summerfarm.service.PurchaseInvoiceService;
import net.summerfarm.service.bms.BmsPaymentDocumentService;
import net.xianmu.bms.client.req.recon.ProcessCallBackRequest;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static net.summerfarm.contexts.Global.storeMap;


/**
 * <AUTHOR>
 * @create 2022/8/29
 */
@Service
public class BmsPaymentDocumentServiceImpl extends BaseService implements BmsPaymentDocumentService {
    @Resource
    private BmsPaymentDocumentMapper bmsPaymentDocumentMapper;
    @Resource
    @Lazy
    private PurchaseInvoiceService purchaseInvoiceService;
    @Resource
    private BmsSettleAccountMapper bmsSettleAccountMapper;
    @Resource
    private CarrierMapper carrierMapper;
    @Resource
    private DingdingProcessInstanceService dingdingProcessInstanceService;
    @Resource
    private BmsQuotationProcessMapper bmsQuotationProcessMapper;
    @Resource
    private CarrierAccountMapper carrierAccountMapper;
    @Resource
    private CarrierInvoiceMapper carrierInvoiceMapper;
    @Resource
    private PurchaseInvoiceMapper purchaseInvoiceMapper;
    @Resource
    private BmsPaymentInvoiceRelMapper bmsPaymentInvoiceRelMapper;
    @Resource
    private FinancePaymentOrderMapper financePaymentOrderMapper;
    @Resource
    private BmsDeliveryReconciliationMapper bmsDeliveryReconciliationMapper;

    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    private BmsServiceClientFacade bmsServiceClientFacade;

    @Autowired
    MqProducer mqProducer;



    @Override
    public AjaxResult selectPaymentDocument(BmsPaymentDocumentQuery param) {
        PageHelper.startPage(param.getPageIndex(),param.getPageSize());
        List<BmsPaymentDocumentVO> result = bmsPaymentDocumentMapper.select(param);
        result.forEach(detail-> detail.setStoreName(storeMap.get(detail.getStoreNo())));

        return AjaxResult.getOK(PageInfoHelper.createPageInfo(result));
    }

    @Override
    public AjaxResult selectPaymentDocumentDetail(BmsPaymentDocumentQuery param) {
        BmsPaymentDocumentVO document = bmsPaymentDocumentMapper.selectByPrimaryKey(param.getId());
        //根据结算单查询出对应的发票
        List<Integer> invoiceIdList = bmsPaymentInvoiceRelMapper.selectByPaymentId(document.getId());
        if (!CollectionUtils.isEmpty(invoiceIdList)) {
            List<PurchaseInvoiceVO> purchaseInvoiceVOList = purchaseInvoiceMapper.selectByIdList(invoiceIdList);
            purchaseInvoiceVOList.forEach(r->{
                List<String> fileAddress = purchaseInvoiceMapper.fileAddress(r.getId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
                List<String> photoList = new ArrayList<>();
                for (String file : fileAddress) {
                    String[] split = file.split("\\.");
                    //如果后缀为pdf，则证明是pdf文件
                    if (!Objects.equals(split[CommonNumbersEnum.ONE.getNumber()], "pdf")) {
                        photoList.add(file);
                    }
                }
                r.setPhotoList(photoList);
                r.setPurchaseInvoiceId(r.getId());
            document.setPurchaseInvoiceVOList(purchaseInvoiceVOList);
            });
        }
        if (Objects.nonNull(document.getCarrierId())) {
            Carrier carrier = carrierMapper.selectByPrimaryKey(document.getCarrierId().longValue());
            if (Objects.nonNull(carrier)){
                document.setCarrierName(carrier.getCarrierName());
                document.setCarrierId(carrier.getId().intValue());
                CarrierAccount carrierAccount = carrierAccountMapper.selectByPrimaryKey(document.getCarrierAccountId());
                document.setPayType(!Objects.isNull(carrierAccount) ? carrierAccount.getPayType() : null);
            }
        }
        //存在财务付款凭证，调用财务service查询付款凭证paymentVoucher
        if (Objects.nonNull(document.getFinancePaymentOrderId())){
            FinancePaymentOrder financePaymentOrder = financePaymentOrderMapper.queryById(document.getFinancePaymentOrderId());
            if (Objects.nonNull(financePaymentOrder) && Objects.nonNull(financePaymentOrder.getPaymentVoucher())) {
                document.setPaymentVoucher(financePaymentOrder.getPaymentVoucher());
            }
        }
        return AjaxResult.getOK(document);
    }

    @Override
    public Integer selectAccountId(Integer id) {
        BmsPaymentDocumentVO document = bmsPaymentDocumentMapper.selectByPrimaryKey(id);
        return document.getCarrierAccountId();
    }

    @Override
    public BmsPaymentDocumentVO selectDetailId(Integer id) {
        BmsPaymentDocumentVO document = bmsPaymentDocumentMapper.selectByPrimaryKey(id);
        if(document == null){
            throw new BizException("bms打款单不存在");
        }
        return document;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult commitPaymentDocument(BmsPaymentDocumentParam param) {

        List<PurchaseInvoiceVO> purchaseInvoiceVOList = purchaseInvoiceMapper.selectByIdList(param.getInvoiceIdList());
        if (CollectionUtils.isEmpty(purchaseInvoiceVOList)) {
          return  AjaxResult.getErrorWithMsg("发票不存在");
        }
        BmsPaymentDocumentVO document = bmsPaymentDocumentMapper.selectDetailByPrimaryKey(param.getPaymentDocumentId());
        BigDecimal invoiceTotal = purchaseInvoiceVOList.stream().map(PurchaseInvoiceVO::getIncludedTax).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (invoiceTotal.compareTo(document.getPaymentAmount()) != 0) {
           return AjaxResult.getErrorWithMsg("发票金额与打款单金额不符");
        }
        //判断发票与结算打款单的关联关系是否唯一，一张发票不可绑定多个结算打款单
        param.getInvoiceIdList().forEach(i->{
            List<Integer> documentIds = bmsPaymentInvoiceRelMapper.selectByInvoiceId(i);
            if (!CollectionUtils.isEmpty(documentIds) && !documentIds.contains(param.getPaymentDocumentId())){
                PurchaseInvoice purchaseInvoice = purchaseInvoiceMapper.selectById(i);
                if (Objects.nonNull(purchaseInvoice)) {
                    throw new DefaultServiceException("发票" + purchaseInvoice.getInvoiceCode() + purchaseInvoice.getInvoiceNumber() + "已绑定其他结算打款单");
                }
            }
        });


        document.setInvoiceStatus(1);
        //先删除该打款单关联的所有发票，然后新增
        bmsPaymentInvoiceRelMapper.unBindPurchaseInvoiceByDocumentId(param.getPaymentDocumentId());
        //批量插入结算打款发票关联表
        param.getInvoiceIdList().forEach(i-> bmsPaymentInvoiceRelMapper.insert(BmsPaymentInvoiceRel.builder()
                .bmsPaymentDocumentId(param.getPaymentDocumentId().longValue())
                .purchaseInvoiceId(i.longValue()).build()));
        bmsPaymentDocumentMapper.update(document);
        createAdjustmentProcess(document.getId().longValue(),getAdminName(),(Objects.isNull(document.getCarrierName()) ? "" : document.getCarrierName()) +" "+document.getPaymentAmount());

        // 记录操作记录
        BmsQuotationProcess process = new BmsQuotationProcess();
        process.setSourceId(param.getPaymentDocumentId());
        process.setCreator(getAdminName());
        process.setType(2);
        process.setOperationContent("发起打款审批");
        bmsQuotationProcessMapper.insert(process);
        return AjaxResult.getOK();
    }



    @Override
    public AjaxResult selectCarrierInvoice(BmsPaymentDocumentParam param) {
        Carrier carrier = carrierMapper.selectByPrimaryKey(param.getCarrierId().longValue());
        if (Objects.isNull(carrier)){
            return AjaxResult.getErrorWithMsg("承运商不存在");
        }
        CarrierInvoice invoice = carrierInvoiceMapper.selectByCarrierId(carrier.getId());
        if (Objects.isNull(invoice)){
            return AjaxResult.getErrorWithMsg("承运商无税号");
        }

        List<PurchaseInvoiceVO> purchaseInvoiceVos = purchaseInvoiceService.invoiceBms(param.getInvoiceSearchKey(), invoice.getTaxNo());
        return AjaxResult.getOK(purchaseInvoiceVos);
    }

    @Override
    public BmsPaymentDocumentVO queryBmsPaymentDocument(Integer invoiceId, Integer expenseType) {
        BmsPaymentDocumentVO result = null;
        if(Objects.equals(expenseType, FinanceExpenseTypeEnum.PROXY_WAREHOUSE.getId())){
            result = queryBmsProxyWarehousePaymentDocument(invoiceId);
        }else{
            result = bmsPaymentDocumentMapper.selectByInvoiceId(invoiceId);
            if (Objects.isNull(result) || Objects.isNull(result.getReconciliationNo())){
                throw new DefaultServiceException("根据发票id找不到对应结算打款单，发票id:"+ invoiceId);
            }
        }

        this.wrapBmsPaymentDoc(result);
        return result;
    }

    private void wrapBmsPaymentDoc(BmsPaymentDocumentVO result) {
        BmsSettleAccountQuery query = new BmsSettleAccountQuery();
        query.setReconciliationNo(result.getReconciliationNo());
        List<BmsSettleAccountVO> accounts = bmsSettleAccountMapper.selectSettleAccount(query);

        if (!CollectionUtils.isEmpty(accounts)) {
            BmsSettleAccountVO accountVO = accounts.get(NumberUtils.INTEGER_ZERO);
            int monthValue = accountVO.getDeliveryStartDate().getMonthValue();
            result.setQuotationArea(accountVO.getServiceAreaName());
            result.setMonth(monthValue);
        }
        List<BmsDeliveryReconciliation> bmsDeliveryReconciliations = bmsDeliveryReconciliationMapper.selectListByPaymentId(result.getId());

        if(!CollectionUtils.isEmpty(bmsDeliveryReconciliations)){
            result.setReconIdList(bmsDeliveryReconciliations.stream().map(BmsDeliveryReconciliation::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public BmsPaymentDocumentVO queryBmsProxyWarehousePaymentDocument(Integer invoiceId) {
        BmsPaymentDocumentVO result = bmsPaymentDocumentMapper.selectProxyWarehouseByInvoiceId(invoiceId);
        if (Objects.isNull(result) || Objects.isNull(result.getReconciliationNo())){
            throw new DefaultServiceException("根据发票id找不到对应结算打款单，发票id:"+ invoiceId);
        }
        return result;
    }

    @Override
    public BmsPaymentDocumentVO queryBmsPaymentDocumentById(Integer paymentDocumentId) {
        BmsPaymentDocumentVO result = bmsPaymentDocumentMapper.selectDetailByPrimaryKey(paymentDocumentId);
        BmsSettleAccountQuery query = new BmsSettleAccountQuery();
        query.setReconciliationNo(result.getReconciliationNo());
        List<BmsSettleAccountVO> accounts = bmsSettleAccountMapper.selectSettleAccount(query);
        if (!CollectionUtils.isEmpty(accounts)) {
            BmsSettleAccountVO accountVO = accounts.get(NumberUtils.INTEGER_ZERO);
            int monthValue = accountVO.getDeliveryStartDate().getMonthValue();
            result.setQuotationArea(accountVO.getServiceAreaName());
            result.setMonth(monthValue);
        }
        return result;
    }

    @Override
    public List<CarrierVo> queryCarriers() {
        return carrierMapper.selectAllInvoice();
    }

    @Override
    public CarrierVo queryCarrier(Long id) {
        return carrierMapper.selectCarrierInvoice(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleFailPaymentDocument(Integer id, String reason) {
        if (Objects.isNull(id)){
            return;
        }
        if (dynamicConfig.bmsCallBackSwitch("FMS_PAY_REFUSE")) {
            ProcessCallBackRequest request = new ProcessCallBackRequest();
            request.setBizId(Long.valueOf(id));
            request.setRemark(reason);
            bmsServiceClientFacade.refuseByPay(request);
            return;
        }

        BmsPaymentDocumentVO document = bmsPaymentDocumentMapper.selectDetailByPrimaryKey(id);

        BmsDeliveryReconciliationQuery query = new BmsDeliveryReconciliationQuery();
        query.setPaymentDocumentsId(id);
        List<BmsDeliveryReconciliationVO> reconciliationVos = bmsDeliveryReconciliationMapper.select(query);
        BmsQuotationProcess process = new BmsQuotationProcess();
        reconciliationVos.forEach(reconciliation->{
            bmsDeliveryReconciliationMapper.unbindById(reconciliation.getId(),3);
            // 记录打款失败信息
            process.setSourceId(reconciliation.getId());
            process.setType(1);
            process.setOperationContent("财务-打款失败");
            bmsQuotationProcessMapper.insert(process);
        });

        // 记录打款失败信息
        process.setSourceId(document.getId());
        process.setType(2);
        process.setOperationContent("财务-打款失败");
        bmsQuotationProcessMapper.insert(process);

        document.setPaymentStatus(2);
        bmsPaymentDocumentMapper.deleteInvoiceId(document);
        logger.info("打款单:{}打款失败",document.getId());
        //解绑对应的发票
        bmsPaymentInvoiceRelMapper.unBindPurchaseInvoiceByDocumentId(id);
    }

    @Override
    public void handleFailAnotherPaymentDocument(Long id, String reason) {
        if (Objects.isNull(id)){
            return;
        }
        BmsPaymentDocumentVO document = bmsPaymentDocumentMapper.selectInHandById(id.intValue());
        if (Objects.isNull(document)){
            logger.info("发票id:{}未找到匹配打款单",id);
            return;
        }
        //todo 记录打款失败信息
        document.setPaymentStatus(2);
        bmsPaymentDocumentMapper.deleteInvoiceId(document);
        logger.info("打款单:{}打款失败",document.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public void handleSuccessPaymentDocument(Long id) {
        if (Objects.isNull(id)){
            return;
        }
        BmsPaymentDocumentVO document = bmsPaymentDocumentMapper.selectInHandById(id.intValue());
        if (Objects.isNull(document)){
            logger.info("打款单id:{}未找到匹配打款单",id);
            return;
        }
//        // 记录打款成功信息
//        BmsQuotationProcess process = new BmsQuotationProcess();
//        process.setSourceId(id.intValue());
//        process.setType(2);
//        process.setOperationContent("打款成功");
//        bmsQuotationProcessMapper.insert(process);
//
//        document.setPaymentStatus(3);
//        bmsPaymentDocumentMapper.update(document);
//        logger.info("打款单:{}打款成功",document.getId());
    }

    @Override
    public void auditPaymentDocumentSuccess(Long bizId, String handlerUserId, String remark, OperatorBO adminVO) {
        BmsQuotationProcess process = new BmsQuotationProcess();
        process.setSourceId(bizId.intValue());
        process.setCreator(Objects.nonNull(adminVO)?adminVO.getName():null);
        process.setType(2);
        process.setOperationContent("发起打款审批成功");
        process.setRemark(remark);
        bmsQuotationProcessMapper.insert(process);

        BmsPaymentDocumentVO document = bmsPaymentDocumentMapper.selectDetailByPrimaryKey(bizId.intValue());
        document.setPaymentStatus(1);
        document.setInvoiceStatus(3);
        bmsPaymentDocumentMapper.update(document);
        BmsPaymentDocumentParam param = new BmsPaymentDocumentParam();
        param.setPaymentDocumentId(document.getId());
        //根据结算单查询发票单id列表
        List<Integer> invoiceIdList = bmsPaymentInvoiceRelMapper.selectByPaymentId(document.getId());
        param.setInvoiceIdList(invoiceIdList);
        param.setBmsPaymentDocumentVO(queryBmsPaymentDocumentById(document.getId()));
        //消息推送
        MQData mqData = new MQData(MType.BMS_INVOICE_MESSAGE.name());
        mqData.setData(param);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null,JSON.toJSONString(mqData));
    }

    @Override
    public void auditPaymentDocumentRefuse(Long bizId, String handlerUserId) {
        BmsPaymentDocumentVO document = bmsPaymentDocumentMapper.selectDetailByPrimaryKey(bizId.intValue());
        document.setInvoiceStatus(2);
        bmsPaymentDocumentMapper.update(document);
    }

    @Override
    public void exportPaymentDocumentList(BmsPaymentDocumentQuery param) throws IOException {
        List<BmsPaymentDocumentVO> result = bmsPaymentDocumentMapper.select(param);

        List<BmsPaymentDocumentListExcel> excelDatas = Lists.newArrayList();
        for (BmsPaymentDocumentVO paymentDocumentVO : result) {
            BmsPaymentDocumentListExcel excel = new BmsPaymentDocumentListExcel();
            excel.setPaymentNo(paymentDocumentVO.getPaymentNo());
            excel.setCarrierName(paymentDocumentVO.getCarrierName());
            excel.setPaymentAmount(paymentDocumentVO.getPaymentAmount());
            excel.setInvoiceStatus(BmsInvoiceStatusEnum.getDescriptionByResult(paymentDocumentVO.getInvoiceStatus()));
            excel.setPaymentStatus(BmsPaymentStatusEnum.getDescriptionByResult(paymentDocumentVO.getPaymentStatus()));
            excel.setCreateTime(BaseDateUtils.localDateTimeToString(paymentDocumentVO.getCreateTime()));
            excel.setSettleMonth(paymentDocumentVO.getSettleMonth());
            excel.setStoreName(storeMap.get(paymentDocumentVO.getStoreNo()));
            excelDatas.add(excel);
        }
        HttpServletResponse response = RequestHolder.getResponse();
        String fileName = "结算打款单.xls";
        ExcelUtils.setResponseHeader(response, fileName);
        String[] fieldNames = {"打款单编号", "承运商", "打款金额", "开票状态", "打款状态", "任务生成时间", "结算月份", "城配仓"};
        List<String> list = new ArrayList<>(Arrays.asList(fieldNames));
        EasyExcel.write(response.getOutputStream())
                .head(detailExcelDynamicHead(list)).sheet("结算打款单").doWrite(excelDatas);
    }

    private List<List<String>> detailExcelDynamicHead(List<String> fieldNames) {
        List<List<String>> list = Lists.newArrayList();
        for (String fieldName : fieldNames) {
            List<String> head = Lists.newArrayList();
            head.add(fieldName);
            list.add(head);
        }
        return list;
    }

    @Override
    public void createAdjustmentProcess(Long documentId, String adminName, String msg){

        // 审批业务实例
        ProcessInstanceCreateBO processInstanceCreateBO = new ProcessInstanceCreateBO();
        processInstanceCreateBO.setBizTypeEnum(ProcessInstanceBizTypeEnum.BMS_RECONCILIATION_PAYMENT_AUDIT);
        // 发起人adminId
        processInstanceCreateBO.setAdminId(getAdminId());
        // 业务数据id 用于标识
        processInstanceCreateBO.setBizId(documentId);
        BmsPaymentDocumentVO bmsPaymentDocumentVO = bmsPaymentDocumentMapper.selectByPrimaryKey(documentId.intValue());
        bmsPaymentDocumentVO.setStoreName(storeMap.get(bmsPaymentDocumentVO.getStoreNo()));
        // 审批表单参数
        List<DingdingFormBO> dingForms = new ArrayList<>(3);
        DingdingFormBO df0 = new DingdingFormBO();
        df0.setFormName("发起时间");
        df0.setFormValue(DateUtils.localDateTimeToString(LocalDateTime.now()));
        dingForms.add(df0);
        DingdingFormBO df1 = new DingdingFormBO();
        df1.setFormName("发起人");
        df1.setFormValue(getAdminName());
        dingForms.add(df1);
        DingdingFormBO df2 = new DingdingFormBO();
        df2.setFormName("应付费用");
        df2.setFormValue(msg);
        dingForms.add(df2);
        if (Objects.nonNull(bmsPaymentDocumentVO.getSettleMonth())) {
            DingdingFormBO settleMonth = new DingdingFormBO();
            settleMonth.setFormName("结算月份");
            settleMonth.setFormValue(bmsPaymentDocumentVO.getSettleMonth());
            dingForms.add(settleMonth);
        }
        if (Objects.nonNull(bmsPaymentDocumentVO.getStoreName())) {
            DingdingFormBO storeName = new DingdingFormBO();
            storeName.setFormName("城配仓");
            storeName.setFormValue(bmsPaymentDocumentVO.getStoreName());
            dingForms.add(storeName);
        }
        processInstanceCreateBO.setDingdingForms(dingForms);
        try {
            dingdingProcessInstanceService.createProcessInstance(processInstanceCreateBO);
        } catch (DingdingProcessException e) {
            logger.info("钉钉调用失败:{}", e);
            throw new DefaultServiceException(e);
        }
    }
}
