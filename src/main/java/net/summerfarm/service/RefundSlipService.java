package net.summerfarm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.RefundSlip;
import net.summerfarm.model.vo.RefundSlipVO;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @title: RefundSlipService 退款单
 * @date 2021/10/25 16:37
 */
@Repository
public interface RefundSlipService {

    /**
     * 查询退款单列表
     * @param pageIndex
     * @param pageSize
     * @param query
     * @return
     */
    AjaxResult selectRefundSlip(int pageIndex, int pageSize, RefundSlipVO query);

    /**
     * 待退款退款单确认退款上传信息
     * @param query
     * @return
     */
    AjaxResult adoptRefundSlip(RefundSlipVO query);


    /**
     * 当退货单审核不通过时，结算单数据回退，重新判断结算单状态
     * @param settlementId
     * @param refundSettlementAmount
     * @param refundSlipNo
     */
    void examineRefundSlip(Integer settlementId, BigDecimal refundSettlementAmount, String refundSlipNo);


    /**
     * 产生退货单时生成退款单
     * 结算单号, 退货单号, 退款金额,退结金额
     * @param refundSlip
     */
    void createRefundSlip(RefundSlip refundSlip, BigDecimal refundSettlementAmount);


    /**
     * 当退货审核通过，退款单由审核中-》待退款
     * @param settlementId
     * @param refundSlipNo
     */
    void waitRefundSlip(Integer settlementId, String refundSlipNo);

    /**
     * 更新结算单的状态
     * @param settlementId
     */
    void updateStatus(Integer settlementId);
}
