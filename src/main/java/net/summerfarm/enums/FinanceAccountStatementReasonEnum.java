package net.summerfarm.enums;

/**
 * <AUTHOR>
 * 采购对账单作废状态枚举
 */

public enum FinanceAccountStatementReasonEnum {

    //作废原因：0：审核失败 1：供应商驳回 2：账单审核失败 3：撤回申请 4：付款审核失败
    /**
     *  0:审核失败
     */
    APPROVE_FAIL,

    /**
     * 1:供应商驳回
     */
    SUPPLIER_REJECTION,

    /**
     * 2:账单审核失败
     */
    BILL_APPROVAL_FAILED,

    /**
     * 3：撤回申请(2022-09-21关闭账单)
     */
    WITHDRAWAL_OF_APPLICATION,

    /**
     * 4：付款审核失败
     */
    PAYMENT_APPROVAL_FAILED;
}
