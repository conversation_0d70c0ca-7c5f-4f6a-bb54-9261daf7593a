package net.summerfarm.enums;

public enum StockWarehouseChangeTaskStateEnum {

    WAIT(0,"待执行"),
    HANDING(1,"执行中"),
    END(2,"执行完毕"),
    STOP(3,"手动终止");

    private Integer state;

    private String description;

    StockWarehouseChangeTaskStateEnum(Integer state, String description){
        this.state = state;
        this.description = description;
    }


    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
