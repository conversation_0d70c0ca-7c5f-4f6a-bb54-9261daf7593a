package net.summerfarm.enums;

/**
 *
 * <AUTHOR>
 */
public enum RechargeAutoSendCouponEnum {
    /**
     * AUTOSEND:表示自动发送标识
     * NOAUTOSEND: 表示不自动发送标识
     */
    AUTOSEND("自动发送充值优惠券", 1),
    NOAUTOSEND("不自动发送充值优惠券",0);
    private String state ;
    private Integer sendStatus;

    RechargeAutoSendCouponEnum(String state, Integer sendStatus) {
        this.state = state;
        this.sendStatus = sendStatus;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Integer getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
    }
}
