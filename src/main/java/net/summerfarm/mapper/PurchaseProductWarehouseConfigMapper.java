package net.summerfarm.mapper;

import net.summerfarm.model.DTO.purchase.ProductAdminsDTO;
import net.summerfarm.model.DTO.purchase.ProductBaseInfoDTO;
import net.summerfarm.model.DTO.purchase.ProductWarehouseConfigDTO;
import net.summerfarm.model.domain.purchase.PurchaseProductWarehouseConfig;
import net.summerfarm.model.input.purchase.ProductWarehouseConfigInput;
import net.summerfarm.model.input.purchase.ProductWarehouseConfigUpdate;
import net.summerfarm.model.input.purchase.ProductWarehouseConfigUpsert;
import net.summerfarm.model.input.purchase.ProductWarehouseQueryInput;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PurchaseProductWarehouseConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PurchaseProductWarehouseConfig record);

    int insertSelective(PurchaseProductWarehouseConfig record);

    int insertBatch(@Param("entities") List<PurchaseProductWarehouseConfig> entities);

    PurchaseProductWarehouseConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PurchaseProductWarehouseConfig record);

    int updateByPdIdsAndWarehouseNos(ProductWarehouseConfigUpdate productWarehouseConfigUpdate, @Param("pdIds")List<Long> pdIds, @Param("warehouseNos")List<Integer> warehouseNos);

    int updateByPrimaryKey(PurchaseProductWarehouseConfig record);

    List<ProductBaseInfoDTO> queryProductBaseConfigs(ProductWarehouseConfigInput input);

    List<ProductAdminsDTO> queryAdminNames(@Param("pdIds") List<Long> pdIds);

    List<ProductWarehouseConfigDTO> queryProductWarehouseConfig(Long pdId);

    int deleteByPdIdAndWarehouseNo(Long pdId, List<Integer> warehouseNos);

    List<Long> selectPdIdByAdminAndWarehouse(Integer adminId, Integer warehouseNo);

    String selectAdminNameByPdIdAndWarehouse(Long pdId, Integer outStore);

    List<Integer> selectExistWareHouse(List<Integer> warehouseNos, Long pdId);

    Long countNum();

    List<Integer> groupWarehouseNo();

    Integer deleteByWarehouseNos(List<Integer> warehouseNos);

    List<PurchaseProductWarehouseConfig> selectConfigByWarehouseLimit(Integer warehouseNo, Integer startNum, Integer pageSize);

    List<Long> groupPdIdLimit(Integer startNum, Integer pageSize);

    List<PurchaseProductWarehouseConfig> selectByQueryInput(ProductWarehouseQueryInput queryInput);
}