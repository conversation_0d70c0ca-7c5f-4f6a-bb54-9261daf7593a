package net.summerfarm.mapper.wms;

import net.summerfarm.model.domain.wms.CostBatchCostMappingDO;
import net.summerfarm.model.domain.wms.CostProduceBatchMappingDO;
import net.summerfarm.model.domain.wms.WarehouseCostBatchDO;
import net.summerfarm.model.domain.wms.WarehouseCostBatchDetailDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description
 * @Date 2023/2/20 13:53
 * @<AUTHOR>
 */
@Repository
public interface WarehouseCostBatchDAO {

    /**
     *
     *
     * <AUTHOR>
     * @date 2023/2/20 13:59
     * @param produceBatchIdList 生产批次ID列表
     * @return java.util.List<WarehouseCostBatchDO>
     */
    List<WarehouseCostBatchDO> selectListByProduceBatchIdList(@Param("produceBatchIdList") List<Long> produceBatchIdList);

    /**
     * 根据sku和库存仓查询批次数据
     *
     * <AUTHOR>
     * @date 2023/2/20 15:37
     * @param warehouseNo 库存仓
     * @param sku sku编码
     * @param shelfLifeOrderBy 排序
     * @return java.util.List<net.summerfarm.model.domain.wms.CostProduceBatchMappingDO>
     */
    List<CostProduceBatchMappingDO> selectListByWarehouseNoAndSku(@Param("warehouseNo") Integer warehouseNo, @Param("sku") String sku, @Param("shelfLifeOrderBy") Integer shelfLifeOrderBy);

    /**
     * 根据sku列表和仓列表查询
     *
     * <AUTHOR>
     * @date 2023/3/28 18:37
     * @param warehouseNoList 仓列表
     * @param skuList sku列表
     * @return java.util.List<net.summerfarm.model.domain.wms.CostProduceBatchMappingDO>
     */
    List<CostProduceBatchMappingDO> selectListByWareNoAndSkuList(@Param("warehouseNoList") List<Integer> warehouseNoList, @Param("skuList") List<String> skuList);

    /**
     *
     *
     * <AUTHOR>
     * @date 2023/3/28 18:45
     * @param warehouseNoList 仓列表
     * @param skuList sku列表
     * @return java.util.List<net.summerfarm.model.domain.wms.WarehouseCostBatchDetailDO>
     */
    List<CostBatchCostMappingDO> selectListByWareNoListAndSkuList(@Param("warehouseNoList") List<Integer> warehouseNoList, @Param("skuList") List<String> skuList);
}
