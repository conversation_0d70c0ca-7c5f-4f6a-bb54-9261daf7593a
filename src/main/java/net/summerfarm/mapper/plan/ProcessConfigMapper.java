package net.summerfarm.mapper.plan;

import net.summerfarm.model.domain.plan.ProcessConfig;

public interface ProcessConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_config
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_config
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    int insert(ProcessConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_config
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    int insertSelective(ProcessConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_config
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    ProcessConfig selectByPrimaryKey(Long id);

    /**
     * 查飞书
     * @param code
     * @return
     */
    ProcessConfig selectOneByProcessCode(String code);


    /**
     * 查飞书
     * @param code
     * @return
     */
    ProcessConfig selectOneByDingCode(String code);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_config
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    int updateByPrimaryKeySelective(ProcessConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_config
     *
     * @mbg.generated Fri Jul 21 15:11:06 CST 2023
     */
    int updateByPrimaryKey(ProcessConfig record);
}