package net.summerfarm.mapper.plan;

import net.summerfarm.model.domain.plan.ReplenishmentPlan;
import net.summerfarm.model.vo.plan.ReplenishmentPlanVO;
import net.summerfarm.module.scp.model.input.replenishment.plan.ReplenishmentPlanQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ReplenishmentPlanMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ReplenishmentPlan record);

    int insertSelective(ReplenishmentPlan record);

    ReplenishmentPlan selectByPrimaryKey(Long id);

    ReplenishmentPlan selectByReplenishmentNo(String no);

    List<ReplenishmentPlan> selectByReplenishmentNos(@Param("no") List<String> no);

    int updateByPrimaryKeySelective(ReplenishmentPlan record);

    int updateByPrimaryKey(ReplenishmentPlan record);


    int updateStatusByReplenishmentNo(@Param("replenishmentPlanNos") List<String> replenishmentPlanNos,Integer status);

    List<ReplenishmentPlanVO> queryByIdAndNoAndStatus(List<Long> list, String replenishmentNo, Integer status);

    ReplenishmentPlan selectLastReplenishment();


    List<ReplenishmentPlan> queryReplenishmentPlans(ReplenishmentPlanQuery replenishmentPlanQuery);
}