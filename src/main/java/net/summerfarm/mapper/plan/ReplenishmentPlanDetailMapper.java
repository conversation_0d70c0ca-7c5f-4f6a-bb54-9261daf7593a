package net.summerfarm.mapper.plan;

import net.summerfarm.model.domain.plan.ReplenishmentPlanDetail;
import net.summerfarm.module.scp.model.input.replenishment.plan.ReplenishmentPlanDetailQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ReplenishmentPlanDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ReplenishmentPlanDetail record);

    int insertSelective(ReplenishmentPlanDetail record);

    ReplenishmentPlanDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ReplenishmentPlanDetail record);

    int updateByPrimaryKey(ReplenishmentPlanDetail record);

    List<ReplenishmentPlanDetail> queryByPdNameAndSku(String pdName, String sku);

    List<ReplenishmentPlanDetail> queryByReplenishmentPlanId(Long replenishmentPlanId);

    List<ReplenishmentPlanDetail> queryByReplenishmentPlanIdAndViewDate(String  replenishmentPlanSubNo, LocalDate viewDate);

    List<ReplenishmentPlanDetail> queryBySubOrderNos(@Param("subOrderNos") List<String> subOrderNos);

    Integer batchInsert(@Param("replenishmentPlanDetails") List<ReplenishmentPlanDetail> replenishmentPlanDetails);

    List<ReplenishmentPlanDetail> queryReplenishmentDetails(ReplenishmentPlanDetailQuery replenishmentPlanDetailQuery);
}