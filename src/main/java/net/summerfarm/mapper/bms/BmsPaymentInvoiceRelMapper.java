package net.summerfarm.mapper.bms;

import net.summerfarm.model.domain.bms.BmsPaymentInvoiceRel;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BmsPaymentInvoiceRelMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BmsPaymentInvoiceRel record);

    int insertSelective(BmsPaymentInvoiceRel record);

    BmsPaymentInvoiceRel selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BmsPaymentInvoiceRel record);

    int updateByPrimaryKey(BmsPaymentInvoiceRel record);

    List<Integer> selectByPaymentId(Integer paymentDocumentId);

    List<Integer> selectByInvoiceId(Integer invoiceId);

    /**
     * 删除结算打款单相关发票的关联关系
     * @param documentId 结算打款单id
     */
    void unBindPurchaseInvoiceByDocumentId(Integer documentId);
}