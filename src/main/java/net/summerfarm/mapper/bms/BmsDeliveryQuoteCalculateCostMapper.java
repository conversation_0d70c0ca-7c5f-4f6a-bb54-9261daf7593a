package net.summerfarm.mapper.bms;

import net.summerfarm.model.domain.bms.BmsDeliveryQuoteCalculateCost;
import net.summerfarm.model.vo.bms.BmsDeliveryQuoteCalculateCostVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/8/19
 */
public interface BmsDeliveryQuoteCalculateCostMapper {
    void batchInsertCosts(@Param("items") List<BmsDeliveryQuoteCalculateCostVO> calculateCosts);

    List<BmsDeliveryQuoteCalculateCostVO> selectByQuotationId(Integer quotationId);

    void insert(BmsDeliveryQuoteCalculateCost cost);

    List<String> selectAllCalculateName(@Param("quotationIds") List<Integer> quotationIds);

    /**
     * 根据报价单id删除
     * @param quotationId 报价单id
     * @param dbIds 数据库老计费模型的id
     */
    int deleteByQuotationId(@Param("quotationId") Integer quotationId, @Param("dbIds") List<Integer> dbIds);

    /**
     * 根据报价单id删除
     * @param quotationId 报价单id
     */
    int deleteAllByQuotationId(@Param("quotationId") Integer quotationId);

    /**
     * 更新计费模型
     */
    int updateById(BmsDeliveryQuoteCalculateCostVO bmsDeliveryQuoteCalculateCostVO);

    List<BmsDeliveryQuoteCalculateCostVO> selectByQuotataionIds(@Param("quotationIds") List<Integer> quotationIds, @Param("calculateNames") List<String> calculateNames);
}
