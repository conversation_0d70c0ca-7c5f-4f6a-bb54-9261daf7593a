package net.summerfarm.mapper.bms;

import net.summerfarm.model.domain.bms.DeliverySettleAccountsDetail;
import net.summerfarm.model.input.bms.BmsSettleAccountQuery;
import net.summerfarm.model.vo.bms.BmsDeliverySettleAccountsDetailVO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/8/29
 */
public interface BmsDeliverySettleAccountsDetailMapper {

    List<BmsDeliverySettleAccountsDetailVO> selectByAccountId(Integer settleAccountId);

    void insert(BmsDeliverySettleAccountsDetailVO accountsDetailVO);

    List<BmsDeliverySettleAccountsDetailVO> selectByCondition(BmsSettleAccountQuery param);

    BmsDeliverySettleAccountsDetailVO selectById(Integer id);

    DeliverySettleAccountsDetail selectByPath(BmsSettleAccountQuery param);
}
