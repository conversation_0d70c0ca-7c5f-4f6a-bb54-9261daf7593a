package net.summerfarm.mapper.srm;

import net.summerfarm.model.domain.srm.SrmSupplierOfferDetailAuditRecord;

public interface SrmSupplierOfferDetailAuditRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table srm_supplier_offer_detail_audit_record
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table srm_supplier_offer_detail_audit_record
     *
     * @mbg.generated
     */
    int insert(SrmSupplierOfferDetailAuditRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table srm_supplier_offer_detail_audit_record
     *
     * @mbg.generated
     */
    int insertSelective(SrmSupplierOfferDetailAuditRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table srm_supplier_offer_detail_audit_record
     *
     * @mbg.generated
     */
    SrmSupplierOfferDetailAuditRecord selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table srm_supplier_offer_detail_audit_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(SrmSupplierOfferDetailAuditRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table srm_supplier_offer_detail_audit_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(SrmSupplierOfferDetailAuditRecord record);

    /**
     * 查询是否存在审核中的报价详情
     * @param offerDetailId 报价详情id
     * @return 结果
     */
    boolean existByOfferDetailId(Long offerDetailId);

    /**
     * 根据报价详情id查询报价详情审核记录
     * @param offerDetailId
     * @return
     */
    SrmSupplierOfferDetailAuditRecord selectByOfferDetailId(Long offerDetailId);
}