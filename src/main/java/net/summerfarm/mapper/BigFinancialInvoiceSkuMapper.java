package net.summerfarm.mapper;

import net.summerfarm.model.domain.BigFinancialInvoiceSku;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BigFinancialInvoiceSkuMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BigFinancialInvoiceSku record);

    int updateStatueByFinancialBatchNo(@Param("invoiceId") Long financialInvoiceId,
                                       @Param("batchNo") String batchNo, @Param("status") int status);

    int batchInsert(@Param("items") List<BigFinancialInvoiceSku> stockTransferDos);

    List<BigFinancialInvoiceSku> selectByFinancialId(@Param("invoiceId") Long invoiceId);

    int countByOrderNo(@Param("orderNo") String orderNo);

    List<Long> selectFinancialIdByOrderSku(@Param("orderNo") String orderNo, @Param("sku") String sku);


    List<BigFinancialInvoiceSku> selectByOrderSku(@Param("orderNo") String orderNo, @Param("sku") String sku);

    int updateRedCountById(@Param("id") Long id);

    List<BigFinancialInvoiceSku> selectByOrderNo(@Param("orderNo") String orderNo, @Param("status") Integer status);

    BigFinancialInvoiceSku selectByBatchNo(@Param("batchNo") String batchNo, @Param("status") Integer status);

    int updateStatusByInvoiceId(@Param("invoiceId") Long financialInvoiceId, @Param("status") int status);

    int countByInvoiceId(@Param("invoiceId") Long financialInvoiceId);
}
