package net.summerfarm.mapper;

import net.summerfarm.model.domain.crm.DeliveryPlanRemarkSnapshot;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DeliveryPlanRemarkSnapshotMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DeliveryPlanRemarkSnapshot record);

    int insertSelective(DeliveryPlanRemarkSnapshot record);

    DeliveryPlanRemarkSnapshot selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeliveryPlanRemarkSnapshot record);

    int updateByPrimaryKey(DeliveryPlanRemarkSnapshot record);

    /**
     * 根据类型和业务id查询快照集合
     * @param type 类型
     * @param businessIds 业务id结合
     * @return
     */
    List<DeliveryPlanRemarkSnapshot> selectByTypeBusinessIds(@Param("type") Integer type, @Param("businessIds")List<String> businessIds);

    /**
     *  根据类型和业务id删除快照记录
     * @param type
     * @param businessId
     * @return
     */
    int deleteByTypeBusinessId(@Param("type") Integer type, @Param("businessId")String businessId);
}