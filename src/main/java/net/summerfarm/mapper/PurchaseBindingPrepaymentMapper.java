package net.summerfarm.mapper;

import net.summerfarm.model.PurchaseBindingPrepayment;
import net.summerfarm.model.vo.PurchaseAmountVO;
import net.summerfarm.model.vo.PurchaseBindingPrepaymentVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


/**
 * 采购绑定预付款mapper
 *
 * <AUTHOR>
 * @since 2022-01-16 21:21:52
 */
public interface PurchaseBindingPrepaymentMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PurchaseBindingPrepayment queryById(Object id);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param purchaseBindingPrepayment 实例对象
     * @return 对象列表
     */
    List<PurchaseBindingPrepayment> queryAll(PurchaseBindingPrepayment purchaseBindingPrepayment);

    /**
     * 新增数据
     *
     * @param purchaseBindingPrepayment 实例对象
     * @return 影响行数
     */
    int insert(PurchaseBindingPrepayment purchaseBindingPrepayment);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<PurchaseBindingPrepayment> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PurchaseBindingPrepayment> entities);

    /**
     * 修改数据
     *
     * @param purchaseBindingPrepayment 实例对象
     * @return 影响行数
     */
    int update(PurchaseBindingPrepayment purchaseBindingPrepayment);

    /**
     * 根据预付单id查询
     * @param id
     * @return
     */
    List<PurchaseBindingPrepaymentVO> queryByPurchaseAdvancedOrderId(Long id);

    /**
     * 查询绑定的金额
     * @param purchaseNo
     * @param supplierId
     * @param status 1、未绑定 2、绑定 3、解绑
     * @return
     */
    BigDecimal queryBindAmount(String purchaseNo, Integer supplierId, Integer status);

    /**
     * 根据预付单查询其所有出入库单
     * @param id
     * @return
     */
    List<Integer> selectByProcessDetail(Long id);

    /**
     * 查询流程中的预付单
     * @param purchaseNoList
     * @param supplierId
     * @return
     */
    List<String> selectProcessAdvance(@Param("purchaseNoList") List<String> purchaseNoList, @Param("supplierId") Integer supplierId);

    BigDecimal queryAdvanceAmount(@Param("purchaseNo") String purchaseNo, @Param("supplierId") Integer supplierId);

    List<PurchaseAmountVO> queryAdvanceAmountList(@Param("list")List<String> list, @Param("supplierId") Integer supplierId, @Param("advanceEndTime") String advanceEndTime);

    BigDecimal queryCanUserAdvanceAmountByPurchaseNo(String purchaseNo);


    List<PurchaseBindingPrepaymentVO> getFinishedDataList(Long accountId);
}

