package net.summerfarm.mapper;

import net.summerfarm.model.domain.FinancePaymentOrder;
import net.summerfarm.model.vo.FinancePaymentOrderVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 付款单表(FinancePaymentOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-01-19 17:42:37
 */
public interface FinancePaymentOrderMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    FinancePaymentOrder queryById(Long id);

    /**
     * 查询单条数据
     *
     * @param financePaymentOrder 实例对象
     * @return 实例对象
     */
    FinancePaymentOrder selectOne(FinancePaymentOrder financePaymentOrder);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param financePaymentOrder 实例对象
     * @return 对象列表
     */
    List<FinancePaymentOrderVO> queryAll(FinancePaymentOrderVO financePaymentOrder);

    /**
     * 付款单发票销售方数据初始化
     * @param type
     * @return
     */
    List<FinancePaymentOrderVO> queryData(@Param("type") Integer type);

    /**
     * 付款单发票销售方数据初始化
     * @param type
     * @return
     */
    List<FinancePaymentOrderVO> queryDataOne(@Param("type") Integer type);

    /**
     * 新增数据
     *
     * @param financePaymentOrder 实例对象
     * @return 影响行数
     */
    int insert(FinancePaymentOrder financePaymentOrder);

    /**
     * 新增
     * @param financePaymentOrder
     * @return
     */
    int insertData(FinancePaymentOrder financePaymentOrder);


    /**
     * 修改数据
     *
     * @param financePaymentOrder 实例对象
     * @return 影响行数
     */
    int update(FinancePaymentOrder financePaymentOrder);

    /**
    * 获取已完成的对账单的付款单ID
    * @date 2023/2/21 18:38
    * @param * @Param accountId:
    * @return * @return: java.lang.Long
    */
    List<FinancePaymentOrderVO> getIdByFinishedAccountIdList(@Param("list") List<Long> accountIdList);

}

