package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.market.ActivityInfoExt;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivityInfoExtMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(ActivityInfoExt record);

    
    int insertSelective(ActivityInfoExt record);

    
    ActivityInfoExt selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(ActivityInfoExt record);

    
    int updateByPrimaryKey(ActivityInfoExt record);
}