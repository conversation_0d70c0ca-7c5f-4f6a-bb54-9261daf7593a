package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DiscountCardToMerchant;
import net.summerfarm.model.vo.DiscountCardToMerchantVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
/**
* @Author:ct
* @Date:15:30 2020/5/20
* @param:  * @Param null
* @return:
*/

@Repository
public interface DiscountCardToMerchantMapper {

    /**
    * 根据卡id 查询发放记录
     * @param
     * @return
    */
    List<DiscountCardToMerchantVO> selectByCardId(DiscountCardToMerchantVO discountCardToMerchantVO);

    List<DiscountCardToMerchant>   selectByParam(DiscountCardToMerchant discountCardToMerchant);

    int updateByPrimaryKeySelective(DiscountCardToMerchant record);

    int updateByPrimaryKey(DiscountCardToMerchant record);

    int invalidDiscountCard(Long mId);

    DiscountCardToMerchant selectByPrimaryKey(Integer id);

    Integer insertSelective(DiscountCardToMerchant discountCardToMerchant);

    DiscountCardToMerchant selectUsableDiscountCard(@Param("mId") Long mId, @Param("discountCardId") Integer discountCardId, @Param("now") LocalDate now);

    int invalidDiscountCardById(@Param("id") Integer id);

    int returnCardById(@Param("id") Integer id);
}