package net.summerfarm.mapper.manage;


import net.summerfarm.model.domain.LuckyDrawPrizeRecord;

public interface LuckyDrawPrizeRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(LuckyDrawPrizeRecord record);

    int insertSelective(LuckyDrawPrizeRecord record);

    LuckyDrawPrizeRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LuckyDrawPrizeRecord record);

    int updateByPrimaryKey(LuckyDrawPrizeRecord record);
}