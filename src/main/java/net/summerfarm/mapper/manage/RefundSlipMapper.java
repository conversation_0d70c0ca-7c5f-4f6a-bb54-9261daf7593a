package net.summerfarm.mapper.manage;

import net.summerfarm.model.RefundSlip;
import net.summerfarm.model.vo.RefundSlipVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> 2021-10-22
 */
@Repository
public interface RefundSlipMapper {

    /**
     * 将退款单作废
     * @param updater
     * @param refundSlipNo
     * @param settlementId
     * @return
     */
    int deleteByPrimaryKey(String updater, String refundSlipNo, Integer settlementId);

    /**
     * 将退款单审核中-》待退款
     * @param updater
     * @param refundSlipNo
     * @param settlementId
     * @return
     */
    int updateWaitStatus(String updater, String refundSlipNo, Integer settlementId);

    /**
     * 生成退款单
     * @param record
     * @return
     */
    int insert(RefundSlip record);

    int insertSelective(RefundSlip record);

    /**
     * 根据结算单查询该结算单退款金额
     * @param settlementId
     * @return
     */
    RefundSlip selectByPrimaryKey(Integer settlementId);

    /**
     * 查询退款单
     * @param settlementId
     * @param refundSlipNo
     * @return
     */
    RefundSlip selectByRefundSlipNo(Integer settlementId, String refundSlipNo);

    /**
     * 查询结算单退款单列表
     * @param refundSlipVO
     * @return
     */
    List<RefundSlipVO> selectList(RefundSlipVO refundSlipVO);

    /**
     * 根据结算单id查询该结算单的退款单
     * @param refundSlip
     * @return
     */
    List<RefundSlipVO> selectById(RefundSlip refundSlip);

    /**
     * 修改退款单
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(RefundSlip record);

    /**
     * 更改退款单状态（待退款-》已退款）
     * @param record
     * @return
     */
    int updateByPrimaryKey(RefundSlip record);

    /**
     * 根据结算单查询退款单数量
     * @param settlementId
     * @return
     */
    int selectRefundSlipNum(Integer settlementId);

    /**
     * 根据退货单查询对应退款单
     * @param purchasesBackNo
     * @return
     */
    RefundSlip selectByPurchasesBackNo(String purchasesBackNo);

    /**
     * 根据结算单号查询退款单
     * @param id
     * @return
     */
    List<RefundSlip> selectBySettlementId(Integer id);
}
