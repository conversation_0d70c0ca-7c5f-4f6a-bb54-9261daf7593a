package net.summerfarm.mapper.manage;

import com.cosfo.manage.client.product.req.SummerFarmSynchronizedSkuReq;
import com.cosfo.summerfarm.model.dto.SummerfarmSynchronizedSkuDTO;
import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.goods.client.req.XmSyncSkuReq;
import net.summerfarm.manage.client.inventory.dto.req.UpdateSkuOutdatedReqDTO;
import net.summerfarm.model.DTO.AreaStorageDTO;
import net.summerfarm.model.DTO.inventory.InventoryInfoDTO;
import net.summerfarm.model.DTO.purchase.SkuBaseInfoDTO;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.InventoryWMSInfo;
import net.summerfarm.model.domain.StockBoard;
import net.summerfarm.model.input.AdminSkuMappingInput;
import net.summerfarm.model.input.InventoryQueryInput;
import net.summerfarm.model.input.InventoryReq;
import net.summerfarm.model.vo.*;
import net.summerfarm.model.vo.inventory.SkuSyncToItemVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@Repository
public interface InventoryMapper {
    InventoryVO selectBySkusc(String sku);
    int deleteAll(String[] skus);

    int updateByPdId(@Param("array") Long[] pdIds, @Param("outdated") Boolean outdated);

    /**
     * 插入
     * @param record
     * @return
     */
    int insertSelective(Inventory record);

    Inventory selectOne(Inventory selectKeys);

    /**
     * 根据条件查询数量
     *
     * @param map
     * @return
     */
    int count(Map<String, Object> map);


    /**
     * 根据条件查询
     *
     * @param selectKeys
     * @return
     */
    @RequiresDataPermission(originalField = "ak.area_no")
    List<InventoryVO> select(InventoryReq selectKeys);

    /**
     * 根据条件查询 SupplierVisible
     * @param selectKeys
     * @return
     */
    List<InventoryVO> selectSupplierVisible(InventoryReq selectKeys);

    @Deprecated
    List<InventoryVO> selectSkuList(InventoryReq selectKeys);


    /**
     * 根据sku更新
     *
     * @param record
     * @return
     */
    int update(Inventory record);

    /**
     * 查询仓库库存
     *
     * @param selectKeys
     * @return
     */
    @RequiresDataPermission(originalField = "a.area_no")
    Integer countStoreStock(@Param("selectKeys") StockVO selectKeys, @Param("hasInventory") Integer hasInventory,
                            @Param("skusByPdId") List<String> skusByPdId);

    /**
     * 查询仓库库存
     *
     * @param selectKeys
     * @return
     */
    @RequiresDataPermission(originalField = "a.area_no")
    List<StockVO> selectStoreStock(StockVO selectKeys);

    /**
     * 查询仓库库存
     *
     * @param selectKeys
     * @return
     */
    @RequiresDataPermission(originalField = "a.area_no")
    List<StockVO> selectStoreStockV1(@Param("selectKeys") StockVO selectKeys, @Param("hasInventory") Integer hasInventory);

    /**
     * 查询仓库库存
     *
     * @param selectKeys
     * @return
     */
    @RequiresDataPermission(originalField = "a.area_no")
    List<StockVO> selectStoreStockEx(@Param("selectKeys") StockVO selectKeys, @Param("hasInventory") Integer hasInventory,
                                     @Param("skusByPdId") List<String> skusByPdId);

    /**
     * 查询指定仓sku及名称
     *
     * @return
     */
    List<SKUVO> selectSkus(@Param("areaNo") Integer areaNo, @Param("characters") Integer characters, @Param("categoryId") Integer categoryId);

    List<Inventory> selectList(Inventory selectKeys);

    /**
     * 根据商品id列表查询
     *
     * @param pdIds
     * @return
     */
    List<Inventory> selectByPdIds(@Param("pdIds") Set<Long> pdIds);

    InventoryVO selectBySkuAndAreaNo(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    /**
     * 查询对应spu下的sku
     */
    @RequiresDataPermission(originalField = "ak.area_no")
    List<InventoryVO> selectBySpu(InventoryReq selectKeys);

    List<InventoryVO> selectVOBySpu(InventoryReq selectKey);

    InventoryVO selectSkuType(@Param("sku") String sku);

    InventoryVO selectSkuAfterSale(@Param("sku") String sku);

    List<Inventory> selectByPdId(@Param("pdId") Integer pdId);

    /**
     * 查询代仓sku大客户备注
     *
     * @param sku sku
     * @return 备注
     */
    String selectNameRemakes(@Param("sku") String sku);

    List<SKUVO> selectSkusByType(@Param("type") Integer type, @Param("adminId") Integer adminId);

    Inventory queryBySku(@Param("sku") String sku);

    List<InventoryVO> queryBySkus(List<String> list);



    List<InventoryVO> queryWeightNumBySkus(List<String> list);
    /**
     * 查询所有未删除sku
     *
     * @param type 类型：1 全部,2乳制品,3非乳制品,4水果
     * @return sku list
     */
    List<String> selectSkuByCategoryType(@Param("type") Integer type);

    /**
     * sku是不是水果
     *
     * @param sku sku
     * @return t、水果 f、其他
     */
    boolean isFruit(String sku);

    InventoryVO selectInventoryVOBySku(@Param("sku") String sku);

    List<InventoryVO> selectInventoryVOBySkus(@Param("skus") List<String> skus);

    List<String> selectFruitSkuBySkus(@Param("skus") List<String> skus);

    /**
     * 查询样品池
     */
    List<InventoryVO> selectSampleSku();

    /**
     * 查询在某一个仓库下使用的样品
     *
     * @param areaNo
     * @return
     */

    List<InventoryVO> selectSampleSkuByArea(@Param("areaNo") Integer areaNo, @Param("queryStr") String queryStr);

    List<InventoryVO> selectNullAfterSaleUnit();

    int updateAfterSaleUnit(InventoryVO param);

    /**
     * 根据sku列表查询商品名称
     *
     * @param skus
     * @return
     */
    List<InventoryVO> queryPdNameBySkus(@Param("skus") Set<String> skus);

    /**
     * 查询sku wms信息
     *
     * @param sku
     * @return
     */
    InventoryWMSInfo selectSkuWMSInfo(String sku);

    /**
     * 批量查询Sku wms信息
     *
     * @param list
     * @return
     */
    List<InventoryWMSInfo> batchSelectSkuWMSInfo(List<String> list);

    List<SkuBaseInfoDTO> selectSkuBaseInfosBySku(@Param("skus") List<String> skus);

    /**
     * BD gmv统计时要排除的sku
     *
     * @return
     */
    List<String> selectBDGmvExcludeSku();

    List<InventoryVO> selectSkuByAddTime(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 新 查询库存信息
     */
    @RequiresDataPermission(originalField = "ar.area_no")
    List<StockVO> selectStockNew(StockVO selectKeys);

    List<InventoryVO> selectSkuInfo();

    /**
     * 获取已删除的sku的信息
     *
     * @return
     */
    List<InventoryVO> selectOutdatedSku();

    List<InventoryVO> skuQuery(InventoryVO query);

    /**
     * 查询各个仓的水果
     *
     * @return
     */
    List<StockBoard> selectFruit();

    List<InventoryVO> selectBySku(InventoryReq inventoryReq);

    List<Inventory> selectBySkuList(@Param("skuList") List<String> skuList);

    /**
     * 根据sku或者商品名称匹配
     *
     * @param queryStr
     * @param samplePool
     * @return
     */
    List<InventoryVO> matchSkuOrName(@Param("queryStr") String queryStr, @Param("skuList") List<String> skus, @Param("samplePool") Integer samplePool);

    /**
     * 根据sku或者商品名称匹配
     *
     * @param queryStr
     * @return
     */
    List<InventoryVO> saasMatchSkuOrName(@Param("queryStr") String queryStr, @Param("skuList") List<String> skus, @Param("adminId") Integer adminId, @Param("createTypeList") List<Integer> createTypeList);

    /**
     * 根据批量sku查询
     *
     * @param input
     * @return
     */
    List<InventoryVO> selectBySkuStr(AdminSkuMappingInput input);

    /**
     * 根据sku查询
     *
     * @param sku
     * @return
     */
    Inventory selectOneBySku(String sku);

    /**
     * 根据sku查询售卖城市和库存仓
     *
     * @param sku sku编号
     * @return sku售卖城市编号和库存仓
     */
    List<AreaStorageDTO> selectAreaNoAndStorage(String sku);

    /**
     * 取城市sku所在的库存仓
     *
     * @param sku    sku
     * @param areaNo 城市编号
     * @return sku所在库存仓编号
     */
    Integer selectWarehouseNo(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    /**
     * 查询sku周期成本价
     *
     * @param sku         sku
     * @param warehouseNo 库存总仓
     * @return
     */
    BigDecimal selectCycleCost(@Param("sku") String sku, @Param("warehouseNo") Integer warehouseNo);

    /**
     * 查询某一个sku中，指定extType的sku
     *
     * @param skus     sku列表
     * @param extTypes extType列表
     * @return sku
     */
    Set<String> selectBySkusAndExtTypes(@Param("skus") Collection<String> skus, @Param("extTypes") List<Integer> extTypes);

    /**
     * 根据sku查询sky、商品、类目表的信息
     *
     * @param sku
     * @return
     */
    InventoryVO selectInventoryBySku(@Param("sku") String sku);

    /**
     * 根据sku和上新状态查询商品信息
     *
     * <AUTHOR>
     * @Date 2022/11/24 16:50
     * @param sku sku编码
     * @param outdatedList 上新状态
     * @return net.summerfarm.model.vo.InventoryVO
     **/
    InventoryVO selectBySkuAndOutdated(@Param("sku") String sku, @Param("outdatedList") List<Integer> outdatedList);

    /**
     * 查询商品属性
     *
     * @param sku
     * @return
     */
    InventoryVO selectProperty(String sku);

    /**
     * 调拨单模块筛选采购负责人
     *
     * @param queryStr
     * @param skus
     * @return
     */
    @Deprecated
    List<InventoryVO> match2SkuOrName(@Param("queryStr") String queryStr, @Param("skus") List<String> skus);

    Boolean checkIsFruit(String sku);

    List<Inventory> selectByQueryInput(InventoryQueryInput queryInput);

    /**
     * 根据pdId以及sku性质查询某个spu下的sku
     *
     * @param pdId    pdId
     * @param extType sku性质
     * @return sku列表
     */
    List<Inventory> selectByPdIdAndExtType(@Param("pdId") Integer pdId, @Param("extType") Integer extType);

    List<Inventory> selectByExtTypes(@Param("extTypes") Collection<Integer> extTypes);


    // 根据sku主键id批量查询
    List<Inventory> selectByIds(@Param("invIds") List<Long> invIds);

    Integer selectSkuVisible(String sku);

    /**
     * 根据sku获取图片，（优先获取sku头图，否则去spu图片）
     */
    String getPicBySku(String sku);

    /**
     * 根据sku最近一次采购员id
     */
    Integer getRecentPurchaserIdBySku(String sku);

    /**
     * 查询每日需要同步的sku信息
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<SummerfarmSynchronizedSkuDTO> queryNeedSynchronizedSkuInfo(@Param("startTime") String startTime, @Param("endTime") String endTime);
    /**
     * 根据skuId查询需要同步的sku信息
     *
     * @param skuId
     * @return
     */
    SummerfarmSynchronizedSkuDTO queryNeedSynchronizedSkuInfoBySkuId(@Param("skuId") Long skuId);

    InventoryInfoDTO selectEffectiveInventoryBySku(String sku);

    /**
     * 查询商品信息
     * @param sku
     * @return
     */
    InventoryVO selectBySkuMsg(@Param("sku") String sku);

    List<Inventory> listByPdIdsNoCareOutdated(@Param("pdIds") Set<Long> pdIds);

    /**
     *  根据主键查询
     * @param id
     * @return
     */
    Inventory selectByPrimaryKey(Long id);
    /**
     * 根据特定重量条件查询商品信息
     *
     * <AUTHOR>
     * @Date 2022/11/8 10:54
     **/
    List<Inventory> selectByMinWeightNum(@Param("weightNum") BigDecimal weightNum);

    /**
     * 根据特定体积条件查询商品信息 优化？
     *
     * <AUTHOR>
     * @Date 2022/11/10 17:32
     * @param data 长宽高值
     * @return java.util.List<net.summerfarm.model.domain.Inventory>
     **/
    List<Inventory> selectByMinVolume(@Param("data") BigDecimal data);

    /**
     * 查询sku库存总量
     *
     * <AUTHOR>
     * @Date 2022/11/8 11:54
     * @param sku sku编码
     * @return java.lang.Integer 库存总数
     **/
    Integer selectInventorySumBySku(@Param("sku") String sku);

    /**
     * 根据sku上新状态查询商品信息
     *
     * <AUTHOR>
     * @Date 2022/11/14 18:17
     * @param outdated 上新状态
     * @return java.util.List<net.summerfarm.model.domain.Inventory>
     **/
    List<Inventory> selectBySkuStatus(@Param("outdated") Integer outdated);

    /**
     * 查询新品数据
     *
     * <AUTHOR>
     * @Date 2022/11/24 15:37
     * @param outdated 上新状态
     * @param auditStatus 审核状态
     * @return java.util.List<net.summerfarm.model.domain.Inventory>
     **/
    List<Inventory> selectNewSku(@Param("outdated") Integer outdated, @Param("auditStatus") Integer auditStatus);

    /**
     * 根据pdId和adminId绑定sku
     *
     * @param pdId
     * @param adminId
     * @return
     */
    List<Inventory> selectByPdIdAndAdminId(@Param("pdId") Integer pdId, @Param("adminId") Integer adminId);

    List<InventorySkuVO> selectSkuInfoBySkuList(@Param("skuList") List<String> skuList);


    List<Integer> selectSaasAdminIdList();

    int updateTenantIdByAdminId(@Param("adminId") Integer adminId, @Param("tenantId") Long tenantId);

    /**
     * 批量修改sku 生命周期
     * @param list
     * @return
     */
    int batchUpdateOutdated(@Param("list") List<UpdateSkuOutdatedReqDTO> list);

    List<SkuSyncToItemVO> selectNeedSyncToItemSku(@Param("skus")Set<Long> id);

    List<SkuSyncToItemVO> selectNeedSyncToItemByPdId(@Param("pdIds")Set<Long> id);

    @Deprecated
    List<Long> selectNeedSyncToItemProduct(@Param("pdIds")Set<Long> id);

    List<SummerFarmSynchronizedSkuReq> queryNeedSyncSkuList(@Param("pdIds")List<Long> pdIds, @Param("skuIds")List<Long> skuIds);

    List<XmSyncSkuReq> queryNeedSyncSkuListForGoods(@Param("pdIds")List<Long> pdIds, @Param("skuIds")List<Long> skuIds);

    List<Inventory> queryNeedSyncSkuInfo(@Param("startTime") String startTime, @Param("endTime") String endTime,@Param("skuId")Long skuId,@Param("maxSkuId")Long maxSkuId);

    List<Inventory> selectBySkus(@Param("skus")List<String> skus);

    /**
     * 根据sku批量查询
     * @param skuList skuList
     * @return
     */
    List<Inventory> selectSkuListBySku(@Param("skus") List<String> skuList);



    List<Long> querySkuIdsByAdminId(@Param("adminId")Integer adminId);

    /**
     * 批量获取sku的预警天数
     * @param skus
     * @return
     */
    List<InventoryVO> batchSelectSkuInfo(@Param("skuIds") Set<String> skus);

    List<SKUVO> queryAllSku(@Param("sku") String sku);

    List<InventoryVO> listAllPriceAdjustmentJobSku();

    /**
     * 校验是否满足自动报价
     * @param skuList
     * @return
     */
    List<InventoryVO> checkAutoGenerateMajorPrice(@Param("skuList") List<String> skuList);

    void updateG2g(@Param("skus") List<String> skus);
}
