package net.summerfarm.mapper.manage;

import cn.hutool.core.date.DateTime;
import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.Activity;
import net.summerfarm.model.domain.PurchasesPlan;
import net.summerfarm.model.domain.StockTask;
import net.summerfarm.model.domain.StockTaskItem;
import net.summerfarm.model.domain.wms.StockTaskWaveConfig;
import net.summerfarm.model.input.LackGoodsListReq;
import net.summerfarm.model.input.ReturnRejectListReq;
import net.summerfarm.model.input.StockTaskQuery;
import net.summerfarm.model.input.StockTaskReq;
import net.summerfarm.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Repository
public interface StockTaskMapper {

    int insert(StockTask stockTask);

    int update(StockTask stockTask);

    int updateByTaskNo(StockTask stockTask);

    /**
     * 根据出库任务id更新邮件推送状态
     * @param stockTask 出库任务
     * @return 更新结果
     */
    int updateOptionFlagById(StockTask stockTask);

    StockTask selectByPrimaryKey(Integer id);

    /**
     * 根据id list查询出库任务列表
     * @param list id list
     * @return 出库任务列表
     */
    List<StockTask> selectListByPrimaryKey(@Param("list") List<Integer> list);

    StockTask selectOne(@Param("taskNo") String taskNo, @Param("type") Integer type);

    /**
     * 根据出库任务id查询任务扩展状态位
     * @param id 出库任务id
     * @return 查询结果
     */
    StockTask selectOptionFlagById(@Param("id") Integer id);
    /**
     * 根据出库任务编号查询出库任务的推送状态位
     * @param idList 出库任务id列表
     * @return 出库任务列表
     */
    List<StockTask> selectOptionFlagByIdList(@Param("idList") List<Integer> idList);

    /**
     * 查询采购单关联入库任务状态 便于判断采购单状态
     *
     * @param taskNo
     * @param type
     * @return
     */
    List<Integer> selectAllStatus(@Param("taskNo") String taskNo, @Param("type") Integer type);

    /**
     * 新查询采购入库任务
     */
    List<StockTask> selectPurchasesTasks(@Param("taskNo") String taskNo, @Param("type") Integer type);

    /**
     * 列表查询
     *
     * @param id
     * @param pdId
     * @param sku
     * @param types
     * @param type
     * @param areaNo
     * @param state
     * @param taskNo
     * @param storeNo
     * @param supplierId
     * @return
     */
    @RequiresDataPermission(originalField = "t.area_no")
    List<StockTaskVO> selectWithItem(@Param("id") Integer id, @Param("pdId") Integer pdId, @Param("sku") String sku
            , @Param("list") List<Integer> types, @Param("type") Integer type, @Param("typeList") List<Integer> typeList, @Param("areaNo") Integer areaNo, @Param("state") Integer state
            , @Param("stateList") List<Integer> states, @Param("taskNo") String taskNo, @Param("storeNo") Integer storeNo, @Param("supplierId") Integer supplierId
            , @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("processState") Integer processState, @Param("mailPushState") Integer mailPushState, @Param("startAddTime") LocalDateTime startAddTime, @Param("endAddTime") LocalDateTime endAddTime, @Param("tenantId") Long tenantId, @Param("warehouseNoList") List<Integer> warehouseNoList);

    @RequiresDataPermission(originalField = "st.area_no")
    List<StockTaskVO> selectUnfinishTask(StockTaskReq stockTaskReq);

    @RequiresDataPermission(originalField = "st.area_no")
    List<StockTaskVO> unfinishTaskExcludeTransfer(StockTaskReq stockTaskReq);

    int updateByPrimaryKey(StockTask stockTask);

    //查询出入库中的sku
    List<StockTaskItem> selectStoreingSku(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    //查询采购入库中的sku
    List<PurchasesPlan> selectPurchasingSku(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    @RequiresDataPermission(originalField = "st.area_no")
    List<StockTaskVO> selectVO(StockTaskReq stockTaskReq);

    //查询未完成的销售/出样/补发出库任务
    List<StockTask> selectUnFinishTask(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    //查询库存本金
    BigDecimal selectTaskMoney(@Param("storeNo") Integer storeNo, @Param("type") Integer type, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);


    // 根据创建时间查询
    List<StockTask> stockTaskList(@Param("areaNo") Integer areaNo, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    // 根据修改时间查询
    List<StockTask> finishTaskList(@Param("areaNo") Integer areaNo, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    // 待出库调拨任务
    List<StockSubscribeVO> waitOutAllocationTask(@Param("areaNo") Integer areaNo, @Param("expectTime") LocalDate expectTime);

    List<StockTaskItemVO> centerToStoreData(StockTaskItemVO stockTaskItemVO);

    /**
     * 查询任务状态
     *
     * @param type       {@link net.summerfarm.enums.StockTaskType}
     * @param purchaseNo
     * @param sku
     * @return
     */
    Integer selectTaskState(@Param("type") Integer type, @Param("purchaseNo") String purchaseNo, @Param("sku") String sku);

    List<StoreRecordVO> selectUnStoreList(StoreRecordVO storeRecord);

    List<StoreRecordVO> selectQuantitySum(StockTaskReq stockTaskReq);
    List<StoreRecordVO> selectQuantitySumEx(StockTaskReq stockTaskReq);

    List<StockTask> selectUnFinishAllocationTask(Integer areaNo);

    // 查询出入库数量、重量
    List<StoreRecordVO> selectSaleTaskQuantitySum(StockTaskReq stockTaskReq);
    List<StoreRecordVO> selectSaleTaskQuantitySumEx(StockTaskReq stockTaskReq);

    /**
     * 查询配送仓是否已经生成销售出库任务
     *
     * @param storeNo      配送仓编号
     * @param deliveryDate 日期
     */
    List<StockTask> selectSaleTaskMsg(@Param("storeNo") Integer storeNo, @Param("deliveryDate") LocalDate deliveryDate);

    /**
     * 根据批次查询是否生成入库任务
     *
     * @param purchaseNo
     * @return
     */
    int selectByPurchaseNo(@Param("purchaseNo") String purchaseNo);

    StockTask selectBySku(@Param("areaNo") Integer areaNo, @Param("sku") String sku, @Param("list") List<Integer> types);

    StockTask selectbyTerm(Integer id);

    /**
     * 根据任务id 批量查询任务信息
     *
     * @param list
     * @return
     */
    List<StockTask> selectBatchStockTask(List<Integer> list);

    /**
     * 获取临保转换信息
     *
     * @param startDate
     * @return
     */
    List<StockTask> selectByTemporaryTranf(LocalDate startDate);

    /**
     * 根据盘点单id查询任务
     *
     * @param id
     * @return
     */
    StockTask selectByStockTakingListId(Integer id);

    /**
     * 获取 （未入库，部分入库）销售出库，出样出库，补货出库，销售自提 未操作出库数量
     *
     * @param warehouseNo
     * @param sku
     * @return
     */
    Integer selectUnOutStore(@Param("warehouseNo") Integer warehouseNo, @Param("sku") String sku);

    List<StockTask> selectUnFinish(Activity activity);

    List<StockTask> selectByTaskId(Activity activity);

    List<StockTask> selectExpectTask(Date expectStartTime, Date expectEndTime, Integer areaNo);


    List<StockTask> selectWarehouseAndExpectTime(Date expectStartTime, Date expectEndTime, @Param("areaNo") List<Integer> areaNo);


    /**
     * 查询缺货sku的采购补货情况,线上销售库存小于等于0为缺货,只查询未和部分入库的信息,只查询调拨入库,出库,采购入库时的信息
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 补货仓\sku\数量
     */
    List<StockTaskItemDetailVO> selectStockTaskNotice(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询缺货sku的调拨补货情况,线上销售库存小于等于0为缺货,只查询待出库,配送中(发出)及已入库(到达)调拨单
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 补货仓\sku\数量\次日达仓库
     */
    List<StockTaskItemDetailVO> selectAllocationNextDayArrive(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    // 查询调拨出库列表
    @RequiresDataPermission(originalField = "st.area_no")
    List<StockTaskVO> selectAllotIn(StockTaskReq stockTaskReq);

    // 查询调拨入库列表

    @RequiresDataPermission(originalField = "st.area_no")
    List<StockTaskVO> selectAllotOut(StockTaskReq stockTaskReq);

    /**
     * 退货入库任务列表
     *
     * @param returnRejectListReq
     * @return
     */
    @RequiresDataPermission(originalField = "t.areaNo")
    List<ReturnRejectListVo> getReturnRejectDataList(ReturnRejectListReq returnRejectListReq);

    /**
     * 查询订单和配送信息
     *
     * @param result
     * @return
     */
    List<OrderAndSendVo> getOrderAndSendAboutInfo(StockTaskResult result);

    /**
     * 拒收查询订单和配送信息
     *
     * @param result
     * @return
     */
    List<OrderAndSendVo> getRejectOrderAndSendAboutInfo(StockTaskResult result);

    /**
     * 缺货入库列表查询
     *
     * @param lackGoodsListReq
     * @return
     *
     */
    @RequiresDataPermission(originalField = "t.area_no")
    List<LackGoodsApprovedListVo> lackGoodsList(LackGoodsListReq lackGoodsListReq);

    /**
     * 订单是否已经存在
     *
     * @param orderNo
     * @return
     */
    int selectByOrderNoAndTime(@Param("orderNo") String orderNo);

    /**
     * 刷数据的查询接口
     * 仅输数据使用
     */
    List<StockTask> allData(@Param("ids") List<Long> ids, @Param("type") Integer type, @Param("beginId") Long beginId);

    @Deprecated
    List<StockTask> selectByTime(@Param("createdAt") Date createdAt, @Param("updatedAt") Date updatedAt);

    /**
     * 获取城配仓出库任务未出数量
     * @param storeNo
     * @param sku
     * @return
     */
    Integer selectUnOutStoreByStoreNo(@Param("storeNo") Integer storeNo, @Param("sku") String sku);

    int selectUnFinishRoadQuantity(@Param("inStore")Integer areaNo,@Param("sku") String sku);

    /**
     *
     * @param createDate
     * @param storeNo
     * @return
     */
    List<StockTask> selectByStoreNoAndTime(@Param("createDate") LocalDate createDate, @Param("storeNo")Integer storeNo);

    /**
     * 查询出库任务列表
     *
     * <AUTHOR>
     * @date 2023/2/16 14:23
     * @param stockTaskQuery 出库任务查询
     * @return java.util.List<net.summerfarm.model.domain.StockTask>
     */
    List<StockTask> selectListByCondition(StockTaskQuery stockTaskQuery);


    List<StockTaskWaveConfig> queryWaveConfig();

}
