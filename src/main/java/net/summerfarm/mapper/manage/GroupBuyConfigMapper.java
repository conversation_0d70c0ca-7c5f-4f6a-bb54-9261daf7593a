package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.market.GroupBuyConfigDetailDTO;
import net.summerfarm.model.DTO.market.GroupBuyConfigListQueryDTO;
import net.summerfarm.model.GroupBuyConfigListDTO;
import net.summerfarm.model.domain.GroupBuyConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface GroupBuyConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(GroupBuyConfig record);

    int insertSelective(GroupBuyConfig record);

    GroupBuyConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GroupBuyConfig record);

    int updateByPrimaryKey(GroupBuyConfig record);

    /**
     * 查询有效的活动（待生效和生效中的）
     */
    GroupBuyConfig selectByAreaNoAndEffectiveStatus(@Param("areaNo") Integer areaNo);

    /**
     * 根据指定条件查询团购活动列表
     */
    List<GroupBuyConfigListDTO> selectList(GroupBuyConfigListQueryDTO groupBuyConfigListQueryDTO);

    /**
     * 根据活动id查询活动详情数据
     */
    GroupBuyConfigDetailDTO selectById(Long id);

    List<Integer> selectEffectiveAreaNo();

    int updateActualEndTime(Long id);

    List<GroupBuyConfig> selectByActualEndTime(LocalDateTime finishTime);

    int updateStatusById(Long id);
}