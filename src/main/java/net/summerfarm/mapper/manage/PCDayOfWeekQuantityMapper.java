package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PCDayOfWeekQuantity;
import net.summerfarm.model.vo.PCDayOfWeekQuantityVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Deprecated
public interface PCDayOfWeekQuantityMapper {
    int insert(PCDayOfWeekQuantity record);

    int insertSelective(PCDayOfWeekQuantity record);

    int insertOrUpdate(PCDayOfWeekQuantity record);
}