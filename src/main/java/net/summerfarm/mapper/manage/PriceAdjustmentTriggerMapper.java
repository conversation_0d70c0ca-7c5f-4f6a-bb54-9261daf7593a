package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PriceAdjustmentTrigger;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PriceAdjustmentTriggerMapper {
    int insert(PriceAdjustmentTrigger record);

    int insertSelective(PriceAdjustmentTrigger record);

    List<PriceAdjustmentTrigger> selectByBusinessId(Long businessId);

    List<PriceAdjustmentTrigger> sumByTypeAndPrice(Long businessId);

    List<PriceAdjustmentTrigger> selectLastTrigger(@Param("storeNo") Integer storeNo, @Param("sku") String sku);

    /**
     * 查询生效批次
     * @param businessId businessId
     * @return
     */
    String selectValidBatch(Long businessId);
}