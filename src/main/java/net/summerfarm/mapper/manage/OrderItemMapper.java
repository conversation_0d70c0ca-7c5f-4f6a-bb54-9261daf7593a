package net.summerfarm.mapper.manage;

import java.util.Collection;
import net.summerfarm.model.domain.OrderItem;
import net.summerfarm.model.input.OrderItemInput;
import net.summerfarm.model.vo.OrderItemVO;
import net.summerfarm.model.vo.SalesVolumeVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
public interface OrderItemMapper {

    int insertBatch(List<OrderItem> orderItems);

    /**
     * 查询订单下对应商品购买记录
     * @param orderNo
     * @param sku
     * @return
     */
    List<OrderItem> selectList(@Param("orderNo") String orderNo, @Param("sku") String sku, @Param("suitId") Integer suitId);

    /**
     * 获取订单内sku信息
     * @param orderNo 订单编号
     * @return sku信息列表
     */
    List<OrderItemVO> selectOrderItemByOrderNo(@Param("orderNo") String orderNo);


    List<OrderItemVO> selectOrderItemAndExtraByOrderNo(@Param("orderNo") String orderNo);

    List<OrderItemVO> select(OrderItemInput selectKeys);

    /**
     * 查询订单项sku数量
     * @param orderNo
     * @return
     */
    int selectSkuAmount(@Param("orderNo") String orderNo);

    void updateStatusByOrderNo(@Param("orderNo") String orderNo, @Param("status") Integer status);

    List<SalesVolumeVO> selectSaleVolumes(@Param("sku") String sku, @Param("pdName") String pdName, @Param("areaNo") Integer areaNo,
                                          @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,
                                          @Param("dataPermission") Set<Integer> dataPermission, @Param("categoryType") Integer categoryType);

    List<OrderItemVO> selectOrderItemByMId(OrderItemVO orderItemVO);

    List<OrderItem> selectOrderItemByStoreNo(@Param("storeNo") Integer storeNo, @Param("sku") String sku, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 指定区间内最早的下单时间
     * @param storeNo
     * @param sku
     * @param startTime
     * @param endTime
     * @return
     */
    LocalDateTime selectEarlyOrderItem(@Param("storeNo") Integer storeNo, @Param("sku") String sku, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询sku日期内销售数量
     *
     * @param storeNo
     * @param sku
     * @param startDate
     * @param endDate
     * @return
     */
    //int querySaleQuantity(@Param("storeNo") Integer storeNo, @Param("sku") String sku, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
    * 查询订单项信息 ,不包含省心送sku
    */
    List<OrderItemVO> selectByOrderNo(String orderNo);

    /**
     * 批量查询订单项信息 ,不包含省心送sku. 主要用来数据导出
     */
    List<OrderItemVO> selectByOrderNoSet(@Param("orderNoSet") Collection<String> orderNoSet);

    /**
     * 查询订单项信息 ,不包含精准送sku
     */
    List<OrderItemVO> selectByOrderNoNew(String orderNo);

    int queryTotalOrderAmount(@Param("mId") Long mId);

    /**
     * 将订单信息，不为null的信息插入到orderItem表内
     * @param item
     * @return
     */
    int insertSelective(OrderItem item);

    /**
     * 根据订单号查询出直发采购类型的订单对应的订单项信息
     * @param orderNo
     * @return
     */
    List<OrderItem> queryItemList(@Param("orderNo") String orderNo);

    /**
     * 根据订单号查询订单项信息
     * @param orderNo
     * @return
     */
    List<OrderItem> querySkuDetailItemList(@Param("orderNo") String orderNo);

    /**
     * 查询BD 排除统计商品的销售额
     * @param startTime
     * @param endTime
     * @param adminId BD-id
     * @param areaNo
     * @return
     */
    Double queryBDExcludeSpuSales(@Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime, @Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo);

    /**
     * 根据订单号号和下单时的区域所属总仓查询出订单详情内的sku详情
     * @param orderNo
     * @return
     */
    List<OrderItemVO> selectOrderItemByOrderNoAndAreaNo(@Param("orderNo") String orderNo);

    /**
    *
    */
    List<OrderItem> selectSkuByOrderList(@Param("dpIdList") List<Integer> dpIdList, @Param("deliveryTime") LocalDate deliveryTime);

    /**
     * 查询订单所属门店id
     * @param orderItemId
     * @return
     */
    Long selectMidByItemId(Long orderItemId);

    /**
     * 订单编号查询几条明细
     * @param orderNo
     * @return
     */
    Integer countItem(String orderNo);

    /**
     * 根据id查询订单明细
     * @param id
     * @return
     */
    OrderItem selectById(Long id);


    OrderItem selectByPrimaryKey(Long id);

    /**
     * 根据ID获取订单项信息
     * @param orderItemId
     * @return
    */
    OrderItem selectByOrderItemId(Long orderItemId);

    List<OrderItem> queryItemListByOrderNoAndDPId(@Param("orderNo")String orderNo,@Param("deliveryPlanId") Integer deliveryPlanId);

    /**
     * 查询订单拦截的商品信息
     * @param orderNo
     * @return
     */
    List<OrderItemVO> selectInterceptByOrderNo(String orderNo);

    /**
     * 撤销订单项
     * @param id
     * @param status
     */
    void updateStatusById(Long id, Integer status);

    List<OrderItemVO> selectInvoiceOrderItemVO();

    /**
     * 根据地址配送时间获取订单信息
     * @param contactId
     * @param deliveryTime
     * @return
     */
    List<OrderItemVO> selectByContactId(@Param("contactId") Long contactId, @Param("deliveryTime") LocalDate deliveryTime);

    /**
     * 查询订单项信息
     * @param orderItemVO
     * @return
     */
    OrderItemVO selectOrderItem(OrderItemVO orderItemVO);


    /**
     * 根据sku查询出插入到订单项中的sku信息
     *
     * @param sku
     * @return
     */
    OrderItem selectSkuInfosBySku(@Param("sku") String sku);

    /**
     * 批量修改
     * @param orderItems
     * @return
     */
    int batchUpdate(@Param("list") List<OrderItem> orderItems);
    /**
     * 获取门店时间范围内/指定订单的精准送金额
     *
     * @param mId           门店id
     * @param startDatetime 开始时间
     * @param endDatetime   结束日期时间
     * @param orderNos      订单编号
     * @return {@link BigDecimal}
     */
    BigDecimal getTimeFrameFeeByMId(@Param("mId")Integer mId,@Param("startDatetime")LocalDateTime startDatetime,@Param("endDatetime")LocalDateTime endDatetime,@Param("orderNos")List<String> orderNos);

    /**
     * 根据itemId批量查询
     * @param itemIds itemIds
     * @return
     */
    List<OrderItem> selectListByItemId(@Param("list") List<Long> itemIds);

    /**
     * 根据订单编号和sku查询订单明细
     *
     * @param orderNo
     * @param sku
     * @return {@link OrderItem}
     */
    OrderItem selectByOrderNoAndSku(@Param("orderNo")String orderNo,@Param("sku")String sku);
}
