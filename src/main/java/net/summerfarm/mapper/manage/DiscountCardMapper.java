package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DiscountCard;
import net.summerfarm.model.vo.DiscountCardUseRecordVO;
import net.summerfarm.model.vo.DiscountCardVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/5/20  14:08
 */
@Repository
public interface DiscountCardMapper {

    /**
    * 插入新卡
    */
    int insertSelective(DiscountCard discountCard);

    DiscountCard selectByPrimaryKey(Integer id);

    /**
    * 查询所有卡
    * @param:  * @Param null
    * @return:
    */
    List<DiscountCard> selectAllCard(@Param("name") String name);

    List<DiscountCardUseRecordVO> selectAllOrder(DiscountCardUseRecordVO discountCardUseRecordVO);

    DiscountCardVO selectByCardId(Integer id);

    /**
     * 将要更新的内容更新到对应的discount_card表中,更新内容可选
     * @param discountCard 查询的入参
     * @return 受影响的行数
     */
    Integer updateOne(DiscountCard discountCard);

    /**
     * 查询优惠卡订单使用次数
     * @param discountCardMerchantId discount_card_to_merchant.id
     * @param orderNo orderNo
     * @return 优惠卡订单使用次数
     */
    Long selectUseTimes(@Param("discountCardMerchantId") Integer discountCardMerchantId,@Param("orderNo") String orderNo);

}
