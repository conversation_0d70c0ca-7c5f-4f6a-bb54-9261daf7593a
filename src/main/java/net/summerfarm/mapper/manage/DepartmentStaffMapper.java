package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DepartmentStaff;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> by 2021/04/28
 */
@Repository
public interface DepartmentStaffMapper {

    int insert(DepartmentStaff departmentStaff);

    List<DepartmentStaff> selectByStaff(Long deptId, String unionId);

    int updateByStaff(DepartmentStaff departmentStaff);

    int updateByStaffStatus();

    /**
     * 查询钉钉职工部门信息
     * @param userId 钉钉用户id
     * @return 钉钉职工信息
     */
    DepartmentStaff findOneByUserId(@Param("userId") String userId);
}
