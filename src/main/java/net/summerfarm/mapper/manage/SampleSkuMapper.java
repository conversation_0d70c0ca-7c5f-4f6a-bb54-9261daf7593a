package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SampleSku;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/4/24  17:37
 */
@Repository
public interface SampleSkuMapper {

    int insertSampleSku(List<SampleSku> list);

    /**
     * 根据样品申请id查询
     * @param sampleId
     * @return
     */
    List<SampleSku> selectBySampleId(Integer sampleId);

    /**
     * 根据id更新拦截状态
     * @param sampleIds
     * @param interceptFlag
     * @param interceptTime
     */
    void updateInterceptFlagByIdList(@Param("sampleIds") List<Integer> sampleIds,
                                     @Param("interceptFlag") Integer interceptFlag,
                                     @Param("showFlag") Integer showFlag,
                                     @Param("interceptTime")LocalDateTime interceptTime);

    void updateById(SampleSku sampleSku);
}
