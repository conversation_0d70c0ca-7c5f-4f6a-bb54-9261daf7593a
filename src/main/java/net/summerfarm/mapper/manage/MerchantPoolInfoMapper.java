package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.DTO.market.circle.MerchantPoolQueryDTO;
import net.summerfarm.model.domain.MerchantPoolInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantPoolInfoMapper {

    int deleteById(Long id);

    int insert(MerchantPoolInfo merchantPoolInfo);

    MerchantPoolInfo selectById(@Param("id") Long id);

    int update(MerchantPoolInfo merchantPoolInfo);

    List<MerchantPoolInfo> selectByQuery(MerchantPoolQueryDTO queryDTO);

    List<String> listCreators(String creator);

    List<MerchantPoolInfo> listAllOnlineUpdate();

    /**
     * 根据名称进行模糊查询
     * @param merchantName
     * @return
     */
    List<MerchantPoolInfo> selectByName(String merchantName);

    List<MerchantPoolInfo> getNameByIds(@Param("ids") List<Long> ids);
}
