package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.StockTakingList;
import net.summerfarm.model.vo.StockTakingListVO;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface StockTakingListMapper {

    int insert(StockTakingList stockTakingList);

    int delete(Integer id);

    @RequiresDataPermission(originalField = "st.area_no")
    List<StockTakingListVO> select(StockTakingListVO stockTakingListVO);

    StockTakingListVO selectByPrimaryKey(Integer id);

    int updateByPrimaryKey(StockTakingList stockTakingList);

    int updateByTakingId(StockTakingList stockTakingList);

    StockTakingList selectBySku(Integer areaNo, String sku);
}
