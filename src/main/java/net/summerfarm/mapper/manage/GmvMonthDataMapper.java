package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.GmvMonthData;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 销售日gmv
  * <AUTHOR> 2021.12.06
 */
@Repository
public interface GmvMonthDataMapper {
    int deleteByPrimaryKey(Long id);

    int insert(GmvMonthData record);

    /**
     * 插入数据
     * @param record
     * @return
     */
    int insertSelective(GmvMonthData record);

    /**
     * 销售当月日gmv
     *
     * @param startTime 截单开始时间，当月开始时间
     * @param endTime   当前时间的截单开始时间
     * @param adminId  adminId
     * @return 销售月日gmv
     */
    List<GmvMonthData> selectByPrimaryKey(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("adminId") Integer adminId);

    /**
     * 销售当月gmv(不包含当日）
     *
     * @param startTime 截单开始时间，当月开始时间
     * @param endTime   当前时间的截单开始时间
     * @param adminId  adminId
     * @return 销售月日gmv
     */
    GmvMonthData selectByBD(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("adminId") Integer adminId);

    /**
     * 销售下根据城市插叙数据（数据初始化用）
     * @param areaNo 区域
     * @param adminId 销售id
     * @return
     */
    GmvMonthData selectOne(@Param("areaNo") Integer areaNo, @Param("adminId") Integer adminId);

    /**
     * 销售当月gmv（不包含当日，城市维度）
     *
     * @param startTime 截单开始时间，当月开始时间
     * @param endTime   当前时间的截单开始时间
     * @param adminId  adminId
     * @param areaNo 运营服务区域
     * @return 销售月日gmv（城市维度）
     */
    GmvMonthData selectAreaNo(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo);

    int updateByPrimaryKeySelective(GmvMonthData record);

    /**
     * 数据初始化加入区域下单数
     * @param record
     * @return
     */
    int updateByPrimaryKey(GmvMonthData record);

    /**
     * 销售
     * @param startTime
     * @return
     */
    List<Integer> selectByAdminId(@Param("startTime") LocalDateTime startTime);
}