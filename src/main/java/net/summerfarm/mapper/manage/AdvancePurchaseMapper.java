package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.AdvancePurchase;
import net.summerfarm.model.vo.AdvancePurchaseVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;


@Repository
public interface AdvancePurchaseMapper {

    /**
    * 新增预购单
    */
    int addAdvancePurchase(AdvancePurchase advancePurchase);

    /**
    * 修改预购单信息
    */
    int updateAdvancePurchase(AdvancePurchase advancePurchase);

    /**
    * 查询预购单信息
    */
    AdvancePurchase selectAdvancePurchaseById(Integer id);

    /**
    * 查询预购单列表
    */
    List<AdvancePurchaseVO> selectAllAdvancePurchase(AdvancePurchaseVO advancePurchaseVO);

    /**
     * 根据ID查询预购单详情
     */
    AdvancePurchaseVO selectDetailVOById(Integer id);

    /**
    * 查询可用金额大于0的已打款预购单
    */
    List<AdvancePurchase> selectUseAdvancePurchase(Integer supplierId);

    /**
    * 扣减可用金额
    */
    Integer deductUseAmount(@Param("id") Integer id, @Param("useAmount") BigDecimal useAmount);

    /**
    * 添加可用金额
    */
    Integer addUseAmount(@Param("id") Integer id, @Param("useAmount") BigDecimal useAmount);

    /**
    * 查询可用金额已打款预购单
    */
    List<AdvancePurchase> selectAdvancePurchase(@Param("supplierId") Integer supplierId, @Param("purchaseNo") String purchaseNo);



}
