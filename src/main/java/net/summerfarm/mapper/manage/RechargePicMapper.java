package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.RechargePic;

import java.util.List;

/**
 * 充值/退款申请图片
 * <AUTHOR> 2021/09/03
 */
public interface RechargePicMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(RechargePic record);

    /**
     * 插入退款申请的图片
     * @param record
     * @return
     */
    int insertSelective(RechargePic record);

    RechargePic selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(RechargePic record);

    int updateByPrimaryKey(RechargePic record);

    /**
     * 根据充值/退款申请表id查询该id下图片
     * @param id
     * @return
     */
    List<String> selectById(Integer id);
}