package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.GoodsLocationDetail;
import net.summerfarm.model.domain.StoreRecord;
import net.summerfarm.model.input.GoodsLocationDetailOutReq;
import net.summerfarm.model.input.GoodsTransferQuery;
import net.summerfarm.model.vo.GoodsLocationDetailVO;
import net.summerfarm.model.vo.StoreRecordVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/3/23  16:56
 */
@Repository
public interface GoodsLocationDetailMapper {

    /**
     *批量插入
     * @param list
     * @return
    */
    int insertBathDetail(List<GoodsLocationDetail> list);

    /**
     *插入
     * @param goodsLocationDetail
     * @return
     */
    int insertDetail(GoodsLocationDetail goodsLocationDetail);

    /**
     * 修改
     * @param goodsLocationDetail
     * @return
    */
    int updateDetail(GoodsLocationDetail goodsLocationDetail);

    /**
     * 查询
     * @param goodsLocationDetailVo
     * @return
    */
    List<GoodsTransferQuery> selectDetailBySku(GoodsLocationDetailVO goodsLocationDetailVo);

    List<GoodsLocationDetail> selectDetailByAll(String glNo, String sku);

    List<GoodsLocationDetail> selectDetail(String glNo);
    /**
     * 查询
     * @param goodsLocationDetail
     * @return
     */
    GoodsLocationDetail selectByDetail(GoodsLocationDetail goodsLocationDetail);

    /**
     * 变更数量
     * @param quantity
     * @param status
     * @param id
     * @return
     */
    int updateQuantity(@Param("quantity") Integer quantity, @Param("status") Integer status, @Param("id") Integer id);


    /**
     * 查询
     * @param sku
     * @return
     */
    List<GoodsLocationDetailVO> selectBySku(String sku);

    /**
     * 查询
     * @param purchasesNo
     * @return
     */
    List<GoodsLocationDetailVO> selectByPurchaseNo(String purchasesNo);

    /**
     * 查询详细信息
     * @param storeNo
     * @return
    */
    List<GoodsLocationDetailOutReq> selectOutReq(Integer storeNo);

    /**
     * 更改冻结数量
     * @param goodsLocationDetail
     * @return
    */
    int updateLockQuantity(GoodsLocationDetail goodsLocationDetail);

    /**
     * 变更数量，以及销售冻结数量
     * @param quantity
     * @param status
     * @param saleQuantity
     * @param id
     * @return
    */
    int updateQuantityByType(@Param("quantity") Integer quantity, @Param("saleQuantity") Integer saleQuantity, @Param("status") Integer status, @Param("id") Integer id);


    List<GoodsLocationDetailVO> selectGoodsByGlNo(String glNo);

    List<GoodsLocationDetail> selectDetailList(GoodsLocationDetail goodsLocationDetail);

    List<GoodsLocationDetail> selectDetailListWithStoreNo(GoodsLocationDetailVO goodsLocationDetail);

    /**
     * 查询
     * @param sku
     * @return
     */
    List<GoodsLocationDetailVO> selectBySkuSaleLock(String sku);


    List<GoodsLocationDetail> selectDetailByGlNo(GoodsLocationDetail goodsLocationDetail);

    /**
     * 获取货位上的可用批次信息
     * @param sku
     * @return
     */
    List<GoodsLocationDetailVO> selectBySkuAndMin(@Param("sku") String sku);

    /**
     * 查询货位详情
     * @param gld
     * @return
     */
    GoodsLocationDetail selectByDetailVO(GoodsLocationDetailVO gld);

    /**
     * 货位上无可用批次,获取最近的批次信息
     * @param sku
     */
    List<GoodsLocationDetailVO> selectBySkuAndNear(@Param("sku") String sku);

    /**
    * 获取货位上数据大于0
    */
    List<StoreRecordVO> selectQuantityBySku(@Param("sku") String sku);

    // 查询所有可用货位
    List<GoodsLocationDetailVO> selectAllDetail(String sku);
}
