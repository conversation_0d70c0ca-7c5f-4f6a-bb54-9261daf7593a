package net.summerfarm.mapper.manage;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import net.summerfarm.model.domain.RedPacketCirculation;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

public interface RedPacketCirculationMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(RedPacketCirculation record);

    int insertSelective(RedPacketCirculation record);

    RedPacketCirculation selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(RedPacketCirculation record);

    int updateByPrimaryKey(RedPacketCirculation record);

    List<RedPacketCirculation> selectTotalMoney();

}