package net.summerfarm.mapper.manage;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import net.summerfarm.model.domain.MerchantUpdateRecord;
import net.summerfarm.model.vo.MerchantUpdateRecordVO;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

public interface MerchantUpdateRecordMapper {

    int insert(MerchantUpdateRecord record);

    int insertSelective(MerchantUpdateRecord record);

    MerchantUpdateRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantUpdateRecord record);

    int updateByPrimaryKey(MerchantUpdateRecord record);

    /**
     * 根据入参查询出具体个数
     * @param queryMerchantUpdateRecordVO
     * @return
     */
    int countMerchantByAdmin(MerchantUpdateRecordVO queryMerchantUpdateRecordVO);
}