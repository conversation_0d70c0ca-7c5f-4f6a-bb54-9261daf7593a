package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.MenuPurview;
import net.summerfarm.model.vo.MenuPurviewVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MenuPurviewMapper {

    MenuPurview queryById(Integer id);

    List<MenuPurview> queryMenuPurviewList(List<Integer> list);

    List<MenuPurviewVO> queryAll();

    Integer insert(MenuPurview menuPurview);

    Integer updateById(MenuPurview menuPurview);

    List<MenuPurview> queryAllMenuPurview();


}
