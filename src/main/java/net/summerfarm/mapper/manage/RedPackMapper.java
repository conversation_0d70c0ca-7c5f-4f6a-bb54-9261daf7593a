package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.RedPack;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RedPackMapper {
    int deleteByPrimaryKey(Integer rpId);

    int insert(RedPack record);

    int insertSelective(RedPack record);

    RedPack selectByPrimaryKey(Integer rpId);

    int updateByPrimaryKeySelective(RedPack record);

    int updateByPrimaryKey(RedPack record);

    /**
     * 根据提现查询红包记录
     * @param cashId 提现ID
     * @return 红包记录
     */
    List<RedPack> selectByCashId(Integer cashId);
    /**
     * 查询未体现的红包记录
     * @param mId 商户ID
     * @param accountId 子账号ID
     * @return 红包记录
     */
    List<RedPack> selectByMIdAndAccountId(@Param("mId") Long mId, @Param("accountId") Long accountId);

    /**
     * 查询红包
     * @param orderNo 订单
     * @return RedPack
     */
    RedPack selectByOrderNo(String orderNo);
}