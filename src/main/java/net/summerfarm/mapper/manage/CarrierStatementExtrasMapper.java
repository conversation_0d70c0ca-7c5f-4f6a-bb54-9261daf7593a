package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CarrierStatement;
import net.summerfarm.model.domain.CarrierStatementExtras;

import java.math.BigDecimal;
import java.util.List;

/**
 * 承运商结算单杂费
 */
public interface CarrierStatementExtrasMapper {
    /**
     * 根据ID删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     * @param record
     * @return
     */
    int insert(CarrierStatementExtras record);
    /**
     * 新增
     * @param record
     * @return
     */
    int insertSelective(CarrierStatementExtras record);

    /**
     * 根据id查看
     * @param id
     * @return
     */
    CarrierStatementExtras selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(CarrierStatementExtras record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(CarrierStatementExtras record);

    /**
     * 查找杂费
     * @param carrierStatement
     * @return
     */
    List<CarrierStatementExtras> selectCarrierStatementExtras(CarrierStatement carrierStatement);

    /**
     * 查看杂费总和
     * @param carrierStatement
     * @return
     */
    BigDecimal selectExtrasPrice(CarrierStatement carrierStatement);
}