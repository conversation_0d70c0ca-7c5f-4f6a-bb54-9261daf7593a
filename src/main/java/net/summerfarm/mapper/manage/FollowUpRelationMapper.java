package net.summerfarm.mapper.manage;

import akka.io.Inet;
import net.summerfarm.model.domain.FollowUpRelation;
import net.summerfarm.model.input.PrivateSeaInput;
import net.summerfarm.model.input.SalesDataInput;
import net.summerfarm.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface FollowUpRelationMapper {
    /**
     * 插入操作
     * @param record 插入信息
     * @return 0|1
     */
    int insertSelective(FollowUpRelation record);

    /**
     * 查询商户跟进信息
     * @param selectKeys 查询条件,mid,reassign,adminName
     * @return 商户跟进信息
     */
    FollowUpRelation selectOne(FollowUpRelation selectKeys);

    int countFollowUp(@Param("startTime") LocalDateTime startTime, @Param("adminId") Integer adminId, @Param("reassign") Boolean reassign, @Param("areaNo") Integer areaNo);

    /**
     * 查询跟进用户信息
     * @param selectKeys2 查询条件
     * @return 用户信息
     */
    List<FollowUpRelation>  select(FollowUpRelation selectKeys2);

    /**
     * 根据id更新
     * @param followUpRelation 更新的信息
     * @return 1|0
     */
    int updateReassign(FollowUpRelation followUpRelation);

    /**
     * 查询未下单与未拜访信息
     * @param mId 商户id
     * @return 未下单与未拜访信息
     */
    List<ReleaseVO> getMerchaWithBD(@Param("mId") Long mId);

    /**
     * 查询公海未下单与未拜访信息
     * @param mId 商户id
     * @return 公海未下单信息
     */
    List<ReleaseVO> getMerchaWithOpenSea(@Param("mId") Long mId);

    List<FollowUpRelation> selectByAreaNo(FollowUpRelationVO followUpRelationVO);

    int deleteByMid(Long mId);

    FollowUpRelation selectByMid(Integer mId);

    /**
     * 获取私海列表
     * @param privateSeaInput 查询条件
     * @return 私海列表
     */
    List<PrivateSeaVO> selectPrivateSea(PrivateSeaInput privateSeaInput);

    /**
     * 历史流转次数
     * @param mId 商户id
     * @return 流转次数
     */
    int countByMId(Long mId);

    /**
     * 更新bd私海客户信息,adminId必传
     * @param followUpRelation 更进信息
     */
    void updateReassignByAdminId(FollowUpRelation followUpRelation);

    /**
     * 更新私海商户信息
     * @param adminId bd的id
     * @param infoArea 商户所在的运营区域
     * @param followUpRelation 跟进信息
     */
    void updateReassignByAdminIdArea(@Param("adminId") Integer adminId, @Param("infoArea") List<Integer> infoArea, @Param("followUpRelation") FollowUpRelation followUpRelation);

    /**
     * 查询bd下的私海客户,分城市查询
     * @param adminId 销售id
     * @param infoArea 城市ids
     * @return 私海客户信息
     */
    List<FollowUpRelation> selectByAreaNos(@Param("adminId") Integer adminId, @Param("infoArea") List<Integer> infoArea);
    /**
    * 获取大客户门店无跟进人数据
    */
    List<Long> selectNotInAllMId(Integer adminId);
    /**
     * 查询商户的最新跟进bd信息
     * @param mId 页数
     * @return 商户最新跟进bd名字及状态
     */
    MerchantVO queryFollow(@Param("mId") Long mId);

    /**
     * 私海数
     * @param adminId 销售id
     * @param areaNos 地区编号
     * @return 销售私海数量
     */
    int selectPrivateNum(@Param("adminId") Integer adminId,@Param("areaNos") List<Integer> areaNos);

    /**
     * 获取所有大客户销售团队id
     * @return adminIds
     */
    List<Integer> selectBdType();

    /**
     * 获取公,私海客户数
     * @param salesDataInput 查询条件
     * @return 公私海客户数
     */
    SalesDataVo selectMerchantNum(SalesDataInput salesDataInput);

    /**
     * 获取公海倒闭客户数
     * @param salesDataInput 搜索条件
     * @return 公海倒闭客户数
     */
    int selectOperateMerchantInOpenSea(SalesDataInput salesDataInput);

    List<FollowUpRelation> listByMIds(@Param("mIds") List<Long> mIds);

    FollowUpRelation selectLastFollowOne(@Param("adminId") Integer adminId, @Param("mId") Integer mId);

    /**
     * 商户未重新分配跟进人
     *
     * @param mId m id
     * @return {@link FollowUpRelation}
     */
    FollowUpRelation selectNotReassignByMid(@Param("mId")Integer mId);

    List<FollowUpRelation> batchByMids(@Param("mIds") List<Long> merchantIds);
}