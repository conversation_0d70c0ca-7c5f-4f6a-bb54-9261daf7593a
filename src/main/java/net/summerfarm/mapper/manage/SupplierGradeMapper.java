package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SupplierGrade;

public interface SupplierGradeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SupplierGrade record);

    int insertSelective(SupplierGrade record);

    SupplierGrade selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SupplierGrade record);

    int updateByPrimaryKeyWithBLOBs(SupplierGrade record);

    int updateByPrimaryKey(SupplierGrade record);

    void removeBySupplierId(Integer supplierId);
}