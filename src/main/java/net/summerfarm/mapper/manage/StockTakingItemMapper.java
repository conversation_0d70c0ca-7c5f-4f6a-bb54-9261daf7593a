package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.StockTakingItem;
import net.summerfarm.model.vo.StockTakingItemVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StockTakingItemMapper {

    //查询未盘点的sku
    List<StockTakingItemVO> selectUnTakingItem(@Param("takingId") Integer takingId, @Param("pdName") String pdName, @Param("sku") String sku);

    //查询已盘点的sku
    List<StockTakingItemVO> selectTakingItem(@Param("takingId") Integer takingId, @Param("pdName") String pdName, @Param("sku") String sku, @Param("diff") Boolean diff);

    int insertBatch(List<StockTakingItem> stockTakingItems);

    StockTakingItem selectByPrimaryKey(Integer id);

    int updateByPrimary(StockTakingItem stockTakingItem);
    //根据盘点单id修改
    int updateByTakingId(StockTakingItem stockTakingItem);

    int updateState(@Param("takingId") Integer takingId, @Param("sku") String sku);

    //查询某个盘点单中的sku
    List<StockTakingItem> selectByStockTakingListId(Integer stockTakingListId);

    List<StockTakingItem> selectUnFinishSku(StockTakingItem stockTakingItem);

    /**
     * 查询盘点单明细
     * @param id
     * @return
     */
    List<StockTakingItem> selectByTakingId(Integer id);
}
