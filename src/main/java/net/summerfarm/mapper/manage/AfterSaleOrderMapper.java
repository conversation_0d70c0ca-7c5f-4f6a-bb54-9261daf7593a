package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.AfterSaleOrder;
import net.summerfarm.model.input.AfterSaleOrderQuery;
import net.summerfarm.model.input.AfterSaleProofExportInput;
import net.summerfarm.model.vo.AfterSaleOrderVO;
import net.summerfarm.model.vo.OrderItemVO;
import net.summerfarm.model.vo.OrderNoAreaNoVo;
import net.summerfarm.model.vo.OrderVO;
import net.summerfarm.model.vo.finance.AdminAfterSaleExportVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AfterSaleOrderMapper {

    @RequiresDataPermission(originalField = "m.area_no")
    List<AfterSaleOrderVO> select(AfterSaleOrder selectKeys);

    @RequiresDataPermission(originalField = "m.area_no")
    List<AfterSaleOrderVO> selectByOneProof(AfterSaleOrder selectKeys);

    List<AfterSaleOrderVO> selectByOrderNo(@Param("orderNo") String orderNo);

    AfterSaleOrderVO selectByAfterSaleOrderNo(String afterSaleOrderNo);

    int updateByAfterSaleOrderNo(AfterSaleOrder updateKeys);

    int insertSelective(AfterSaleOrder record);

    int countByMId(Long mId);

    int countNotClose(Long mId);

    /**
     * 修改售后归属店铺
     *
     * @param oldMid    原mid
     * @param newMid    新mid
     * @param accountId 账号id
     * @return int
     */
    int changeOrderMerchant(@Param("oldMid") Long oldMid, @Param("newMid") Long newMid, @Param("accountId") Long accountId);


    List<AfterSaleOrderVO> queryAfterOrderByMId(OrderVO selectKeys);

    Integer queryAfterOrderQuantity(@Param("addTime") LocalDateTime addTime, @Param("sku") String sku, @Param("deliveryDate") LocalDate deliveryDate);

    Integer queryByOrderNoQuantity(@Param("orderNo") String orderNo, @Param("afterTime") LocalDateTime afterTime, @Param("sku") String sku);

    /**
     * 根据入参来获取对应的list
     * @param afterSaleOrderQuery 查询条件
     * @return 售后信息
     */
    List<AfterSaleOrderVO> selectBySelectKeys(AfterSaleOrderQuery afterSaleOrderQuery);

    /**
     * 根据订单号获取对应的售后订单
     * 仅获取发送退款的,包括直接退款与账单退款
     * @param orderNo 订单号
     * @return 售后订单
     */
    List<AfterSaleOrderVO> queryByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据sku, orderNo,
     *
     * @param orderNo
     * @param sku
     * @param status
     * @return
     */
    List<AfterSaleOrderVO> queryBySelectKey(@Param("orderNo") String orderNo, @Param("sku") String sku, @Param("status") Integer status);

    /**
     * 根据配送仓获取省心送未到货售后数量
     */

    List<AfterSaleOrderVO> selectQuantityByStoreNo(@Param("storeNo") Integer storeNo, @Param("sku") String sku, @Param("areaNo") Integer areaNo);


    /**
     * 根据订单号查询售后单信息
     */
    List<AfterSaleOrderVO> selectByOrderNoNew(AfterSaleOrder afterSaleOrder);

    /**
     * 查询补货待审核信息
     */
    List<AfterSaleOrderVO> selectReplenishment();

    List<AfterSaleOrderVO> selectReplenishmentByOrderNo(AfterSaleOrder afterSaleOrder);


    List<AfterSaleOrder> selectByOrderNoSku(@Param("sku") String sku, @Param("orderNo") String orderNo,@Param("productType") Integer productType);

    /**
     * 查询订单详情售后情况
     *
     * @param orderNo
     * @param sku
     * @param suitId
     * @return
     */
    AfterSaleOrderVO selectByOrderNoSkuPrice(@Param("orderNo") String orderNo, @Param("sku") String sku, @Param("suitId") Integer suitId);

    /**
     * 查询指定日期内售后成功的订单相亲的售后情况
     * @param orderNo
     * @param sku
     * @param suitId
     * @param startTime
     * @param endTime
     * @return
     */
    AfterSaleOrderVO selectAppointTime(@Param("orderNo") String orderNo, @Param("sku") String sku, @Param("suitId") Integer suitId, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime);

    List<AfterSaleOrderVO> selectExport(AfterSaleOrderVO selectKeys);

    /**
     * 查询非订单非本月的售后订单
     *
     * @param mId
     * @param startTime
     * @param endTime
     * @param orderNos
     * @return
     */
    List<AfterSaleOrder> selectMonthOrderNo(@Param("mId") Long mId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("orderNos") List<String> orderNos);

    /**
     * 根据订单号查询售后单信息
     * @param orderNo
     * @param startTime
     * @param endTime
     * @return
     */
    List<AfterSaleOrderVO> selectByAfterOrderNo(@Param("orderNo") String orderNo, @Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime);

    /**
     * 查询本账期内未到货退款售后订单
     *
     * @param startTime
     * @param endTime
     * @param mId
     * @return
     */
    List<AfterSaleOrderVO> selectUndelivered(@Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime, @Param("mId") Long mId);

    /**
     * 查询本账期内订单未到货退款售后订单
     * @param ordersList
     * @param mId
     * @return
     */
    List<AfterSaleOrderVO> undeliveredByOrdersList(@Param("ordersList")List<String> ordersList, @Param("mId") Long mId);

    Long selectCount(AfterSaleOrderVO selectKeys);

    OrderNoAreaNoVo getONoAndANByAfterSaleNo(@Param("afterSaleNo") String afterSaleNo);

    /**
     * 获取订单的售后类型
     * @param taskNo
     * @param sku
     * @param afterType
     * @return
     */
    List<Integer> getAfterSaleRemarkTypeList(@Param("taskNo") String taskNo, @Param("sku") String sku,@Param("afterType") Integer afterType);

    /**
     * 统计配送计划是否有售后
     * @param dpId 配送计划ID
     * @return
     */
    int countBydeliveryPlanId(@Param("dpId") Integer dpId);

    /**
     * 查询订单对应的售后单是否有退运费的售后
     * @param orderNo
     * @return
     */
    Integer selectByDeliveryFeeCount(String orderNo);

    /**
     * 根据MID查询售后金额
     * @param MId
     * @param now
     * @param beforeTime
     * @param deliveryed
     * @retur
     */
    BigDecimal selectMoneyByMid(Long MId, LocalDateTime now, LocalDateTime beforeTime,Integer deliveryed);

    /**
     * 查询售后信息 包含待审核，待审批，完成
     */
    List<AfterSaleOrder> selectAfterOrder(OrderItemVO vo);

    /**
     * 查询是否重复
     * @param afterSaleOrderNo 售后单号
     * @return 数量
     */
    int countByAfterSaleOrderNo(String afterSaleOrderNo);

    /**
     * 获取用户发起售后的数量
     *
     */
    int afterSaleCountByMId(@Param("mId") Long mId, @Param("accountId") Long accountId);

    /**
     * 批量插入信息
     * @param afterSaleOrders 插入信息
     * @return 数量
     */
    int saveBatchAfterSaleOrder(List<AfterSaleOrder> afterSaleOrders);

    /**
     * 根据订单号，售后类型
     * @param orderNo
     * @param status
     * @return
     */
    List<AfterSaleOrder> selectAfterOrderByOrderNo(@Param("orderNo") String orderNo, @Param("list") List<Integer> status);


    /**
     * 查询订单售后金额
     *
     * @param orderNos 订单号
     * @return {@link BigDecimal}
     */
    BigDecimal selectAfterSaleAmountByOrderNos(@Param("orderNos") List<String> orderNos);

    /**
     * 查询订单售后
     *
     * @param ordersNos 订单号
     * @return {@link List}<{@link AdminAfterSaleExportVO}>
     */
    List<AdminAfterSaleExportVO> selectAfterSaleOrderByOrderNos(@Param("orderNos") List<String> ordersNos, @Param("mIdList") List<Integer> mIdList, @Param("afterSaleStartTime") LocalDateTime afterSaleStartTime, @Param("afterSaleEndTime") LocalDateTime afterSaleEndTime);

    /**
     * 查询订单售后
     *
     * @param input input
     * @return {@link List}<{@link AfterSaleOrderVO}>
     */
    void selectAfterSaleProofForExport(@Param("input") AfterSaleProofExportInput input, ResultHandler<?> resultHandler);


    /**
     * 查询售后订单sku信息
     *
     * @param afterSaleOrderNo afterSaleOrderNo
     * @return {@link List}<{@link AfterSaleOrderVO}>
     */
    AfterSaleOrderVO queryByAfterSaleOrderNo(@Param("afterSaleOrderNo") String afterSaleOrderNo);

    /**
     * 查询未到货退款成功的售后单
     * @param orderNo
     * @return
     */
    List<AfterSaleOrderVO> selectSuccessByRefundAfterSaleOrder(@Param("orderNo") String orderNo);
}