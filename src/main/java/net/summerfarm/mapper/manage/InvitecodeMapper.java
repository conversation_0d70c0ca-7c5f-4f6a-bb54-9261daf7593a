package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Invitecode;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

@Repository
public interface InvitecodeMapper {

    int insertSelective(Invitecode record);


    int updateByPrimaryKeySelective(Invitecode record);


    Invitecode selectUnique(Map<String, Object> selectKeys);


    int updateByAdminIdSelective(Invitecode invitecode);

    HashMap selectInvitecode(HashMap map);
}