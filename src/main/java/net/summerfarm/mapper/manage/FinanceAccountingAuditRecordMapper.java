package net.summerfarm.mapper.manage;


import net.summerfarm.model.domain.FinanceAccountingAuditRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【finance_accounting_audit_record(账单确认审核记录)】的数据库操作Mapper
 * @createDate 2023-07-05 16:19:57
 * @Entity generator.domain.FinanceAccountingAuditRecord
 */
public interface FinanceAccountingAuditRecordMapper {
    /**
     * 插入
     *
     * @param auditRecord 审核记录
     */
    void insert(FinanceAccountingAuditRecord auditRecord);

    /**
     * 根据选择的账单编号更新
     *
     * @param auditRecord 审计记录
     */
    void updateByBillNoSelective(FinanceAccountingAuditRecord auditRecord);

    /**
     * 按账单编号列出
     *
     * @param billNo 账单编号
     * @return {@link List}<{@link FinanceAccountingAuditRecord}>
     */
    List<FinanceAccountingAuditRecord> listByBillNo(@Param("billNo")String billNo);

}




