package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SupplierUpdateLog;
import net.summerfarm.model.vo.PurchasesVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> 2021/10/14
 */
@Repository
public interface SupplierUpdateLogMapper {

    int deleteByPrimaryKey(Integer id);

    /**
     * 插入供应上修改记录
     * @param record
     * @return
     */
    int insert(SupplierUpdateLog record);

    int insertSelective(SupplierUpdateLog record);

    /**
     * 查询修改过供应商的采购单信息
     * @param selectKeys
     * @return
     */
    List<SupplierUpdateLog> selectAll(PurchasesVO selectKeys);

    int updateByPrimaryKeySelective(SupplierUpdateLog record);

    int updateByPrimaryKey(SupplierUpdateLog record);
}