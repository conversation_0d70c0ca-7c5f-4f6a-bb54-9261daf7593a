package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SkuMapping;
import net.summerfarm.model.vo.SkuMappingVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SkuMappingMapper {

    List<SkuMappingVO> selectList(SkuMappingVO selectKey);

    SkuMapping selectOne(SkuMapping skuMapping);

    int insert(SkuMapping skuMapping);

    int delete(@Param("id") Integer id);

    int update(SkuMapping skuMapping);

    int insertBatch(List<SkuMapping> skuMappings);

    /**
     * 根据sku、adminId查询需要推送商品基础信息的sku
     * @param skuMappingVO
     * @return
     */
    List<SkuMappingVO> selectOuterPlatformSkuByCondition(SkuMappingVO skuMappingVO);

    /**
     * 查询所有商品映射需要推送商品基础信息的sku
     * @return
     */
    List<SkuMappingVO> selectOuterPlatformSkuList();

    /**
     * 根据adminId、外部平台id、外部sku查询商品映射
     * @param adminId
     * @param outerPlatformId
     * @param mappingSku
     * @return
     */
    SkuMappingVO selectMappingSku(Integer adminId, Integer outerPlatformId, String mappingSku);

    /**
     * 根据id查询商品映射
     * @param id
     * @return
     */
    SkuMappingVO selectById(Integer id);


    /**
     * 查询订单映射sku
     *
     * @param orderNos 订单号
     * @return {@link SkuMapping}
     */
    List<SkuMapping> selectByOrderNos(@Param("orderNos") List<String> orderNos);

    /**
     * 统计映射订单个数
     *
     * @param orderNos 订单号
     * @return int
     */
    int countMappingByOrderNos(@Param("orderNos") List<String> orderNos);
}
