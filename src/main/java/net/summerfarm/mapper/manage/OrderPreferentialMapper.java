package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.OrderPreferential;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
/**
* @Author:ct
* @Date:17:48 2020/1/6
*/
@Repository
public interface OrderPreferentialMapper {
    /**
    * 查询优惠信息
    * @param orderNo 订单号
    * @return List
    */
    List<OrderPreferential> selectPreferential(String orderNo);

    /**
    * 查询运费优惠信息
    * @param orderNo 订单号
    * @return BigDecimal
    */
    BigDecimal selectDeliveryFeeAmountByOrderNo(String orderNo);
}
