package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Adjustment;
import net.summerfarm.model.domain.AdjustmentDetail;
import net.summerfarm.model.vo.AdjustmentVO;
import net.summerfarm.model.input.AdjustmentInput;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description 调整单mapper
 * <AUTHOR>
 * @date 2021/12/4 13:48
 */
@Repository
public interface AdjustmentMapper {

    /**
     * 根据条件查询调整单
     * @param input
     * @return
     */
    List<Adjustment> select(AdjustmentInput input);

    /**
     * 调整单列表
     * @param input
     * @return
     */
    List<AdjustmentVO> selectAll(AdjustmentInput input);

    /**
     * 根据账单编号查询该账单的调整总金额
     * @param billNumber 账单编号
     * @return
     */
    BigDecimal selectAdjustment(@Param("billNumber") String billNumber);

    /**
     * 根据账单编号查询门店的调整总金额
     * @param billNumber
     * @param mId
     * @return
     */
    BigDecimal selectByStore(@Param("billNumber") String billNumber, @Param("mId") Long mId);

    /**
     * 账期账单确认后，门店的调整总金额
     * @param billNumber
     * @param mId
     * @return
     */
    BigDecimal selectStore(@Param("billNumber") String billNumber, @Param("mId") Long mId);

    /**
     * 根据条件统计调整单总金额
     * @param input
     * @return
     */
    BigDecimal selectTotalAmount(AdjustmentInput input);

    /**
     * 根据调整单号查询
     * @param adjustNo
     * @return
     */
    AdjustmentVO selectByNo(String adjustNo);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    Adjustment selectByPrimaryKey(Integer id);

    /**
     * 查询账单最后调整分摊时间
     * @param billNumber
     * @return
     */
    Adjustment selectTime(@Param("billNumber") String billNumber);

    /**
     * 更新
     * @param adjustment
     * @return
     */
    int updateSelective(Adjustment adjustment);

    /**
     * 查询账单待审核状态调整单
     * @param billNumber 账单编号
     * @return
     */
    List<Adjustment> selectByAdjustment(@Param("billNumber") String billNumber);

    /**
     * 查看今天发起的调整单个数
     * @param countKeys
     * @return
     */
    Integer count(Map countKeys);

    /**
     * 新增调整单
     * @param insert
     */
    void insertSelective(Adjustment insert);

    /**
     * 更新分摊状态
     * @param adjustment
     */
    void updateAverageFlag(Adjustment adjustment);

    /**
     * 查询调整单
     * @param billNumber
     * @return
     */
    List<Adjustment> selectAdjustmentByBillNumber(@Param("billNumber") String billNumber);

    /**
     * 查询店铺调整单
     * @param billNumber
     * @param mId
     * @return
     */
    List<Adjustment> selectAdjustmentByBillNumberByStore(@Param("billNumber") String billNumber, @Param("mId") Long mId);


    /**
     * 查询调整单
     * @param detail
     * @param status
     * @return
     */
    List<Adjustment> selectByDetail(@Param("detail") AdjustmentDetail detail, @Param("status") Integer status);

    int selectCountByBillNumberStatus(@Param("billNumber") String billNumber);

    /**
     * 根据账单编号已调整查询实付或者配送金额
     *
     * @param billNo    账单编号
     * @param isPaidFee 是否是实付金额 true 是
     * @return {@link BigDecimal}
     */
    BigDecimal selectPaidOrDeliveryFeeByBillNo(@Param("billNo") String billNo, @Param("adjustNo") String adjustNo, @Param("isPaidFee") boolean isPaidFee);

    /**
     * 批量获取调整单数据
     * @param billNumberList billNumberList
     * @param status status
     * @return
     */
    List<AdjustmentVO> selectAdjustList(@Param("list") List<String> billNumberList, @Param("status") Integer status);

    /**
     * 根据状态查询品牌调整总价
     *
     * @param nameRemakes 品牌名称
     * @param customerConfirmStatus  确认状态
     * @param financialAudit 审核状态
     * @return
     */
    BigDecimal selectTotalByNameRemakesAndStatus(@Param("nameRemakes") String nameRemakes, @Param("customerConfirmStatus") Byte customerConfirmStatus,
                                                 @Param("financialAudit") Integer financialAudit);
}
