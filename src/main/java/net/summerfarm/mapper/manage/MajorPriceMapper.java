package net.summerfarm.mapper.manage;

import com.cosfo.summerfarm.model.dto.supplyprice.SummerfarmSkuPriceInfoDTO;
import io.swagger.models.auth.In;
import java.util.Collection;
import net.summerfarm.model.domain.MajorPrice;
import net.summerfarm.model.domain.MajorPriceDO;
import net.summerfarm.model.domain.Merchant;
import net.summerfarm.model.input.MajorPriceInput;
import net.summerfarm.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * Created by wjd on 2018/7/17.
 */
@Repository
public interface MajorPriceMapper {

    void delete(@Param("adminId") Integer adminId, @Param("direct") Integer direct);

    void deleteBatch(List<Integer> ids);

    MajorPrice selectOne(MajorPrice selectKey);

    void insertBatch(List<MajorPrice> list);

    MajorDataCountVO selectDataCount(HashMap map);

    List<HashMap> selectDataTrend(HashMap map);

    List<MajorSkuDataVO> selectMajorSkuDataVO(HashMap map);

    List<MajorSkuDataVO> selectBill(HashMap map);

    List<String> selectBillOrderNo(HashMap map);

    List<MajorPriceInput> selectMajorPrice(MajorPriceInput majorPriceInput);

    List<MajorPriceVO> selectList(MajorPrice selectKey);

    List<Merchant> selectMerchantsByMajorPrice(MajorPrice selectKey);

    /**
     * 单条更新报价单
     * @param record
     * @return
     */
    int updateByPrimaryKey(MajorPrice record);

    /**
     * 查询大客户指定城市是否有低价数据
     * @param adminId 大客户id
     * @param areaNo 城市编号
     * @return
     */
    @Deprecated
    List<MajorPriceLowRemainder> selectLowPriceRemainder(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo);

    /**
     * 查询大客户指定城市是否有低价数据
     * @param adminId
     * @param areaNo
     * @param sku
     * @return
     */
    @Deprecated
    List<MajorPriceLowRemainder> selectLowPriceRemainderSku(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo, @Param("sku") String sku);

    /**
     * 修改报价单价格
     * @param id id
     * @param price 价格
     */
    int updatePriceById(@Param("id") Integer id, @Param("price") BigDecimal price);

//    /**
//     * 查询客户是否有低价sku
//     * @param areaNo 城市编号
//     * @param sku sku
//     * @return
//     */
//    List<MajorPriceLowRemainder> selectLowPriceSku(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    /**
     * 批量更新大客户改价
     * @param majorPriceInputList
     * @return
     */
    void updateBatch(List<MajorPriceInput> majorPriceInputList);

    /**
     * 更新大客户报价单
     * @param majorPriceInput
     */
    void update(MajorPriceInput majorPriceInput);

    /**
     * 根据id查询areaNo和sku
     * @param id
     * @return
     */
    MajorPrice selectAreaNoAndSkuById(Integer id);

    /**
     * 查询需要自动更新报价单的数量
     * @param majorPriceAutoUpdateVO
     * @return
     */
    int countAutoUpdateMajorPrice(MajorPriceAutoUpdateVO majorPriceAutoUpdateVO);

    /**
     * 查询需要自动更新的报价单
     * @param majorPriceAutoUpdateVO
     * @return
     */
    List<MajorPriceAutoUpdateVO> selectAutoUpdateMajorPrice(MajorPriceAutoUpdateVO majorPriceAutoUpdateVO);

    /**
     * 修改报价单价格和成本价
     * @param id
     * @param price
     * @param cost
     * @return
     */
    int updatePriceAndCostById(@Param("id") Integer id, @Param("price") BigDecimal price, @Param("cost") BigDecimal cost);


    MajorPrice selectById(Integer id);

    List<Long> selectValidMajorPriceByAreaNo(Integer areaNo);

    int batchExpireMajorPrice(@Param("idList") List<Long> idList);

    List<CostChangeVo> lowerCostWhenCostChange(@Param("sku") String sku, @Param("warehouseNo") Integer warehouseNo);

    MajorPrice selectPriceById(Integer id);

    /**
     * 根据报价单状态查询单条报价单信息
     * @param adminId
     * @param areaNo
     * @param sku
     * @param direct
     * @param validStatus
     * @return
     */
    MajorPrice selectByInvalidStatus(Integer adminId, Integer areaNo, String sku, Integer direct, Integer validStatus);

    /**
     * 根据sku、地区、adminId查询账期生效的报价单
     * @param sku sku
     * @param areaNo 运营服务区
     * @param adminId adminId
     * @param direct 合作模式
     * @return
     */
    @Deprecated
    MajorPrice selectValidMajorPrice(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo, @Param("sku") String sku, @Param("direct") Integer direct);


    MajorPriceInput selectSalePrice(@Param("sku") String sku, @Param("areaNo") Integer areaNo);


    /**
     * 查询报价单价格区间
     * @param adminId adminId
     * @param sku sku
     * @return 价格区间
     */
    @Deprecated
    List<MajorPriceDO> queryListMajorPrice(@Param("adminId") Integer adminId, @Param("sku") String sku, @Param("areaNos") List<Integer> areaNos);

    /**
     * 查询生效的报价单
     * @param adminId 大客户adminId
     * @param direct 合作方式
     * @param areaNo 城市编号
     * @param sku sku
     * @return 报价单信息
     */
    MajorPrice getMajorPrice(Integer adminId, Integer direct, Integer areaNo, String sku);

    /**
     * 批量查询报价单数据,是 getMajorPrice 的批量接口
     * @param adminId 大客户adminId
     * @param direct 合作方式(账期/现结)
     * @param areaNoList 城市编号列表（area.area_no）
     * @param skuList  sku列表
     * @return 报价单信息
     */
    List<MajorPrice> getMajorPriceByAreaNoList(Integer adminId, Integer direct, Collection<Integer> areaNoList, Collection<String> skuList);

    /**
     * 查询所有毛利率的报价单
     * todo price type 有写死
     * @return
     */
    List<MajorPriceAutoUpdateVO> listAllGrossProfitMajorPrice(@Param("majorCycle") Integer majorCycle, @Param("offset") Integer offset, @Param("limit") Integer limit);
}
