package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.AreaSuit;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AreaSuitMapper {

    int insertSelective(AreaSuit record);

    int updateByPrimaryKeySelective(AreaSuit record);

    int insertBatch(List<AreaSuit> areaSuits);

    int update(AreaSuit areaSuit);

    @RequiresDataPermission(originalField = "au.area_no")
    List<AreaSuit> selectBySuitId(int suitId);

    boolean skuInSuit(@Param("areaNo") Integer areaNo, @Param("sku") String sku);
}