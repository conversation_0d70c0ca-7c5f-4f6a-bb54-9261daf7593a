package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PurchasesAccountPlan;
import net.summerfarm.model.vo.PurchasesAccountPlanVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface PurchasesAccountPlanMapper {

    /**
     * 查询物流核算信息
     * @param purchasesNo
     * @return
    */
    PurchasesAccountPlan selectAccount(String purchasesNo);
    /**
    * 查询是否有核算
     * @param id 订单项id
     * @return
    */
    PurchasesAccountPlan selectPurchases(Integer id);

    int insertBatch(List<PurchasesAccountPlan> list);

    /**
     *根据核算单ID查询详情
     * @param accountId
     * @return
    */
    List<PurchasesAccountPlan> selectByAccountId(Integer accountId);

    /**
     * 根绝核算单id查询信息
     * @param id
     * @return
    */
    List<PurchasesAccountPlanVO> selectAccountPlanVO(Integer id);


    List<PurchasesAccountPlanVO> selectLogistics(Integer id);


    PurchasesAccountPlan selectPurchaseById(@Param("sku") String sku, @Param("purchaseNo") String purchaseNo);

}
