package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Refund;
import net.summerfarm.model.vo.RefundVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface RefundMapper {

    int insertSelective(Refund record);

    List<Refund> selectByOrderNo(String orderNo);

    int updateSelectiveByAfterSaleOrderNo(Refund updateKeys);

    BigDecimal selectRefundAmount(@Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime,
                                  @Param("payTypes") List<String> payTypes, @Param("accountId") Integer accountId);

    /**
     * 根据售后单号查询退款信息
     * @param afterSaleOrderNo
     * @return
     */
    RefundVO selectByAfterSaleOrderNo(String afterSaleOrderNo);
}