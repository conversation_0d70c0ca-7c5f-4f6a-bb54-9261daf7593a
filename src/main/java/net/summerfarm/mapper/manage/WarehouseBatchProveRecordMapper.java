package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.BatchProve;
import net.summerfarm.model.domain.WarehouseBatchProveRecord;
import net.summerfarm.model.param.BatchProveParam;
import net.summerfarm.model.vo.WarehouseBatchProveRecordVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/07/12 13:48
 */
@Repository
public interface WarehouseBatchProveRecordMapper {

    /**
     * 批量插入
     * @param batchProveVOS
     */
    void batchInsert(@Param("list") List<WarehouseBatchProveRecord> batchProveVOS);

    void insert(@Param("item") WarehouseBatchProveRecord batchProveVOS);

    /**
     * 根据批次sku查询证件信息
     * @param param
     * @return
     */
    List<WarehouseBatchProveRecordVO> selectByBatchAndSku(BatchProveParam param);

    WarehouseBatchProveRecordVO selectByArrangeItemDetailId(Integer stockArrangeItemDetailId);

    void updateByDetailId(@Param("item") WarehouseBatchProveRecord warehouseBatchProveRecord);

    WarehouseBatchProveRecordVO selectDetail(Integer stockArrangeItemDetailId);

    int countByDetailId(Integer stockArrangeItemDetailId);

    // 查询所有批次
    List<WarehouseBatchProveRecordVO> selectInBatchProve(BatchProveParam batchProve);

}
