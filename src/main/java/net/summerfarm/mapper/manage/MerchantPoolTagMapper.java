package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.domain.MerchantPoolTag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantPoolTagMapper {

    int deleteById(Long id);

    int insert(MerchantPoolTag merchantPoolTag);

    MerchantPoolTag selectById(Long id);

    int update(MerchantPoolTag merchantPoolTag);

    List<MerchantPoolTag> listByType(@Param("type") Integer type);

    List<MerchantPoolTag> listAll();


}