package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PanicBuySku;
import net.summerfarm.model.vo.PanicBuySkuVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PanicBuySkuMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(PanicBuySku record);

    int insertSelective(PanicBuySku record);

    PanicBuySku selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PanicBuySku record);

    int updateByPrimaryKey(PanicBuySku record);

    List<PanicBuySkuVO> selectByPanicId(Integer panicId);

    void deleteByPanicId(Integer panicId);

    void sellOut(Integer id);

    List<String> selectByDate();

    List<PanicBuySku> selectSkuList(@Param("areaNo") Integer areaNo, @Param("sku") String sku);

    List<PanicBuySku> selectSkuListById(@Param("id") Integer id);

    /**
     * 查询在有效的活动中的秒杀商品数据
     * @param areaNo 城市编号
     * @param sku    商品编号
     * @return       秒杀商品数据
     */
    List<PanicBuySku> selectBySKuInEccectivePanicBuy(@Param("areaNo") Integer areaNo, @Param("sku") String sku);
}