package net.summerfarm.mapper.manage;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.PageVo;
import net.summerfarm.enums.ProductsPropertTypeEnum;
import net.summerfarm.model.DTO.inventory.SkuPropertyInfoDTO;
import net.summerfarm.model.DTO.inventory.SkuPropertyValueDTO;
import net.summerfarm.model.domain.ProductsPropertyValue;
import net.summerfarm.model.domain.ProductsPropertyValueInfo;
import net.summerfarm.model.vo.ProductsPropertyValueVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface ProductsPropertyValueMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(ProductsPropertyValue record);

    int insertSelective(ProductsPropertyValue record);

    ProductsPropertyValue selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ProductsPropertyValue record);

    int updateByPrimaryKey(ProductsPropertyValue record);

    List<ProductsPropertyValueVO> selectValue(@Param("pdId") Integer pdId, @Param("sku") String sku);


    @Select("select distinct sku from products_property_value where sku is not null")
    List<String> selectAllSku();

    List<String> selectAllLevelPropertySku(@Param("productsPropertyId") Integer productsPropertyId);

    ProductsPropertyValue selectBySkuAndSpu(@Param("pdId") Integer pdId, @Param("sku") String sku, @Param("productsPropertyId") Integer productsPropertyId);

    List<ProductsPropertyValueVO> selectKeyPropertyValue(@Param("categoryId") Integer categoryId, @Param("productsPropertyId") Integer productsPropertyId);

    void deleteByPdId(@Param("pdId") Long pdId, @Param("noDel") Set<Integer> noDel);

    void deleteCategoryKeyPropertyValue(@Param("categoryId") Integer categoryId, @Param("propertyId") Integer propertyId);

    void deleteSpuPropertyValue(@Param("pdId") Long pdId);

    /**
     * 根据关键属性查找pdNo
     * @param categoryId 类目id
     * @param createType 上新类型
     * @param productsPropertyId 关键属性id
     * @param productsPropertyValue 关键属性值
     * @return pdNo
     */
    List<String> selectPdNoByKeyValue(@Param("categoryId") Integer categoryId, @Param("createType") Integer createType, @Param("productsPropertyId") Integer productsPropertyId, @Param("productsPropertyValue") String productsPropertyValue);

    /**
     * 根据销售属性查询sku
     * @param pdId pdId
     * @param extType 商品性质
     * @param productsPropertyId 销售属性id
     * @param productsPropertyValue 销售属性值
     * @param adminId 代仓商品所属
     * @return sku
     */
    List<String> selectSkuBySaleValue(@Param("pdId") Long pdId, @Param("extType") Integer extType, @Param("type") Integer type, @Param("productsPropertyId") Integer productsPropertyId, @Param("productsPropertyValue") String productsPropertyValue, Integer adminId);

    /**
     * 获取所有自营品牌id
     * @return 自营品牌id
     */
    List<Long> selectBrandId();

    /**
     * 获取某个pdId下某一批sku的执行类型的属性值列表
     * @param pdId pdId
     * @param skus sku列表
     * @param type 属性类型{@link ProductsPropertTypeEnum#CORE_PROPERTY,ProductsPropertTypeEnum#SALE_PROPERTY}
     * @return 属性值列表
     */
    List<SkuPropertyInfoDTO> selectByPdIdAndSkusAndPropertyType(@Param("pdId")Integer pdId,
                                                                @Param("skus")List<String> skus,
                                                                @Param("type")int type);

    List<SkuPropertyValueDTO> selectByPdIdAndSkuAndPropertyType(@Param("pdId")Integer pdId,
                                                                @Param("sku") String sku,
                                                                @Param("type") int type);

    /**
     * 查询某个pdId或者某个sku的指定属性id的属性信息
     * @param productsPropertyIds 属性id集合
     * @param pdId pdId
     * @param sku sku
     * @return 属性值
     */
    List<ProductsPropertyValue> selectByProductsPropertyIdInAndPdIdOrSku(@Param("productsPropertyIds")Collection<Integer> productsPropertyIds,
                                                                         @Param("pdId")Long pdId,
                                                                         @Param("sku")String sku);

    /**
     * 根据pdId和属性id查询
     * @param pdId
     * @param productsPropertyIds
     * @return
     */
    List<ProductsPropertyValueInfo> selectByPdIdAndProductsPropertyIds(@Param("pdId")Long pdId, @Param("productsPropertyIds")List<Long> productsPropertyIds);


    /**
     * 查询某一个sku的所有属性（包括其对应的spu的关键属性以及其自身的销售属性），如果有指定属性id，还会根据属性id过滤
     * @param pdId pdId
     * @param sku  sku
     * @param productsPropertyIds 属性id集合 非必须
     * @return 某个sku的所有属性或指定属性值信息
     */
    List<ProductsPropertyValueInfo> selectAllBySku(@Param("pdId")Long pdId,
                                               @Param("sku")String sku,
                                               @Param("productsPropertyIds")Collection<Integer> productsPropertyIds);


    /**
     * 查询指定属性id的所有属性值
     * @param propertyId 属性id
     * @param pageVo     分页信息
     * @return 属性值列表
     */
    List<String> selectAllproductsPropertyValuepropertyId(@Param("propertyId") Integer propertyId,
                                                          @Param("pageVo") PageVo pageVo);

    /**
     * 根据pdId和属性id查询
     *
     * @param pdId
     * @param propertyId
     * @return
     */
    ProductsPropertyValue selectByPdIdAndPropertyId(@Param("pdId") Long pdId, @Param("propertyId") Long propertyId);

    /**
     * 根据pdId集合查询列表
     *
     * @param pdIds
     * @return
     */
    List<ProductsPropertyValue> selectByPdIds(@Param("pdIds") List<Long> pdIds);

    List<ProductsPropertyValueVO> listByPdIds(@Param("pdIds") List<Long> pdIds);

    List<ProductsPropertyValueInfo> selectByPdIdAndSkuAndProductsPropertyIds(@Param("pdId") Long pdId, @Param("sku") String sku, @Param("productsPropertyIds") List<Long> propertyIds);

    List<String> listValueByPropertyId(Long propertyId);

    List<String> queryContainsGSku();

    void updateG2g(@Param("skus")List<String> skus);

}
