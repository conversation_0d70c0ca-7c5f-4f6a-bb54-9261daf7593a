package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DeliveryEvaluation;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DeliveryEvaluationMapper继承基类
 */
@Repository
public interface DeliveryEvaluationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DeliveryEvaluation record);

    int insertSelective(DeliveryEvaluation record);

    DeliveryEvaluation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeliveryEvaluation record);

    int updateByPrimaryKey(DeliveryEvaluation record);

    List<DeliveryEvaluation> selectList(DeliveryEvaluation record);
}