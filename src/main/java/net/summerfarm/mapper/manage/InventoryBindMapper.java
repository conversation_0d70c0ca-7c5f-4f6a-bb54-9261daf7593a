package net.summerfarm.mapper.manage;

import java.util.List;
import net.summerfarm.model.domain.InventoryBind;
import org.apache.ibatis.annotations.Param;

public interface InventoryBindMapper {
    int insert(InventoryBind record);

    int insertSelective(InventoryBind record);

    /**
     * 更新绑定关系
     * @param id 主键
     * @param bindSku 绑定的关系sku
     */
    int updateBindSkuById(@Param("id")Long id, @Param("bindSku") String bindSku);

    int batchInsert(@Param("list") List<InventoryBind> list);

    /**
     * 根据pdId查询已绑定的sku数据
     *
     * @param pdId    pdId
     * @param extType
     * @return 已绑定的sku信息
     */
    List<InventoryBind> selectByPdIdAndExtType(@Param("pdId")Long pdId, @Param("extType")Integer extType);

    /**
     * 根据绑定的商品sku查询绑定关系
     *
     * @param pdId
     * @param bindSku 绑定的商品sku
     * @return 绑定信息
     */
    List<InventoryBind> selectByBindSku(@Param("pdId") Long pdId, @Param("bindSku")String bindSku);

    InventoryBind selectByBindSkuAndExtType(@Param("pdId") Long pdId,
                                            @Param("bindSku")String bindSku,
                                            @Param("extType") Integer extType);

    /**
     * 根据pdId以及sku查询绑定关系
     * @param pdId pdId
     * @param sku sku
     * @return 绑定信息
     */
    InventoryBind selectOneByPdIdAndSku(@Param("pdId")Long pdId, @Param("sku")String sku);

    InventoryBind selectByPdIdAndSkuAndBindSku(@Param("pdId")Long pdId,@Param("sku")String sku,@Param("bindSku")String bindSku);

    /**
     * 非删除的sku 绑定关系 临时处理
     * @param pdId
     * @param bindSku
     * @param extType
     * @return
     */
    InventoryBind selectByBindSkuAndExtTypeV2(@Param("pdId") Long pdId, @Param("bindSku")String bindSku, @Param("extType") Integer extType);



}