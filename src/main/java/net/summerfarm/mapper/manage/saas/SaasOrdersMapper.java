package net.summerfarm.mapper.manage.saas;

import net.summerfarm.model.domain.saas.SaasOrders;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SaasOrdersMapper {
    int deleteByPrimaryKey(String orderNo);

    int insert(SaasOrders record);

    int insertSelective(SaasOrders record);

    SaasOrders selectByPrimaryKey(String orderNo);

    int updateByPrimaryKeySelective(SaasOrders record);

    int updateByPrimaryKey(SaasOrders record);

    /**
     * 批量插入
     *
     * @param list
     */
    void batchInsert(@Param("list") List<SaasOrders> list);
}