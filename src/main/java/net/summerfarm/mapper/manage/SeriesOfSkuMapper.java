package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SeriesOfSku;
import net.summerfarm.model.vo.SeriesOfSkuVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SeriesOfSkuMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SeriesOfSku record);

    int insertSelective(SeriesOfSku record);

    SeriesOfSku selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SeriesOfSku record);

    int updateByPrimaryKey(SeriesOfSku record);

    List<SeriesOfSkuVO> selectByModel(SeriesOfSku query);

    void deleteBySeries(@Param("seriesType") Integer type, @Param("seriesId") Integer seriesId);

    void updateSelectionSort(Integer id);
}