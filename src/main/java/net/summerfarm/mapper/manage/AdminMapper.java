package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.AdminDingDTO;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.input.StockBoardInput;
import net.summerfarm.model.vo.AdminVO;
import net.summerfarm.model.vo.AdminVos;
import net.summerfarm.model.vo.ValuesVo;
import net.summerfarm.model.vo.finance.AdminOverviewExportVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;

@Repository
public interface AdminMapper {

    int insertSelective(Admin record);

    Admin selectByPrimaryKey(Integer adminId);

    List<Admin> listNameByAdminId(@Param("items") List<Integer> adminIds);

    /**
     * 查看品牌充送开关
     * @param adminId
     * @return
     */
    Admin selectAdminSwitch(Integer adminId);

    int updateByPrimaryKeySelective(Admin record);

    Admin select(String username);


    List<Admin> selectByKey(Admin selecyKey);

    List<AdminVO> selectPageInfo(Admin selectKeys);

    List<AdminVO> selectMajorPageInfo(AdminVO selectKeys);

    AdminVO selectWithRoles(Integer adminId);

    Admin selectByInvitecode(String invitecode);

    List<AdminVO> selectAll(@Param("baseUserIds") List<Long> baseUserIds);

    /**
     * 查询所有销售的adminId
     * @return adminIds
     */
    List<Integer> selectBD(@Param("baseUserIds") List<Long> baseUserIds);

    List<AdminVO> selectByRoleTypes(@Param("baseUserIds") List<Long> baseUserIds);

    List<Admin> selectInId(String[] ccAdminIds);

    List<Admin>  selectByAdminIds(@Param("list") List<Integer> list);

    LocalDate selectCreateTime(HashMap map);

    /**
     * 获取大客户信息
     * @param adminId 大客户adminId
     * @return 大客户信息
     */
    AdminVO selectByAdminId(Integer adminId);

    Admin selectByAid(Integer adminId);

    /**
     * 查询所有BD
     * @param kaAdminId 大客户id
     * @return
     */
    List<Admin> selectBdListByKaAdminId(Integer kaAdminId);

    /**
     * 根据申请人真实名字查询到具体的amdin信息
     * @param applicant
     * @return
     */
    Admin selectByRealName(@Param("realname") String applicant);


    /**
     * 根据申请人真实名字查询到具体的amdin信息
     * @param applicant
     * @return
     */
    Admin selectByRealNameNew(@Param("realname") String applicant);

    List<Admin> selectByPhone(@Param("phone") String phone);

    List<Admin> selectUpdateCloseTime();

    int updateCloseTime(@Param("adminId") Integer adminId, @Param("updateCloseTime") String updateCloseTime);


    List<ValuesVo> selectByRoleTypesNames(@Param("baseUserIds") List<Long> baseUserIds, @Param("adminName") String adminName);

    /**
     * 查询行政同学的邮箱
     * @param departmentId
     * @return
     */
    List<Admin> selectAdministration(@Param("departmentId") Integer departmentId);

    /**
     * 查询特殊客户（大客户备注） 目前仅茶百道
     * @return
     */
    List<Admin> selectKeyAccount(@Param("keyAccountNames") List<String> list, @Param("baseUserIds") List<Long> baseUserIds);


    /**
     * 根据用户名模糊查询
     * @param username
     * @return
     */
    List<Admin> selectLikeUsername(String username);

    /**
     * 通过大客户adminIdStr去获取报价单中的sku
     * @param
     * @return
     */
    List<String> selectSkuByAdminIdStr(StockBoardInput input);

    /**
     * 查询大客户信息
     * @return
     */
    List<AdminVos> selectMajor(@Param("baseUserIds") List<Long> baseUserIds);

    /**
     * 查询所以品牌的销售
     * @return
     */
    List<Admin> selectSalerName();

    /**
     * 根据品牌名称模糊搜索品牌
     * @param nameRemakes
     * @return
     */
    List<Admin> selectByNameRemakes(@Param("nameRemakes")String nameRemakes,@Param("baseUserIds") List<Long> baseUserIds);

    /**
     * 查询非大客户的管理员信息
     * @return
     */
    List<Admin> selectNotMajorAdmin(@Param("baseUserIds") List<Long> baseUserIds);

    /**
     * 查询茶百道和书亦大客户账号
     * @return
     */
    List<Integer> selectCBDAndSY();

    List<Admin> selectAllInfo();

    List<Admin> selectByIds(@Param("list") List<Long> list);

    /**
     * 根据名称备注查询admin
     * @param nameRemakes 名称备注
     * @return admin
     */
    List<Admin> queryNameRemakes(@Param("nameRemakes") String nameRemakes);

    /**
     * 查询相同密码数量
     * @param password 密码
     * @return 数量
     */
    Integer selectSamePwdCount(String password);

    AdminDingDTO selectDingAdmin(Long mId);

    AdminDingDTO selectBdName(Long mId);

    List<Admin> listByRealName(@Param("realName") String realName);

    List<Admin> listByRealNameLike(@Param("realName") String realName);

    int updateBaseUserId(@Param("adminId")Long adminId, @Param("userBaseId")Long userBaseId);

    int updateDriverCarBaseUserId(@Param("id")Long id, @Param("userBaseId")Long userBaseId);

    int updateSupplierBaseUserId(@Param("id")Long id, @Param("userBaseId")Long userBaseId);

    List<Admin> selectAllAdmin();


    Long selectUserBaseIdById(@Param("adminId")Integer adminId);

    Integer selectAdminIdByUserBaseId(@Param("userBaseId")Long userBaseId);

    List<Admin> selectByUserBaseIds(@Param("baseUserIds")List <Long> userBaseIds);

    /**
     * 大客户现结/账期门店统计
     *
     * @param adminId 管理员id
     * @return {@link AdminOverviewExportVo}
     */
    AdminOverviewExportVo selectAdminMerchantCount(@Param("adminId")Integer adminId);

    /**
     * 根据品牌名称查询用户
     *
     * @param nameRemakes 品牌名称
     * @return {@link List}<{@link Admin}>
     */
    List<Admin> selectByNameRemakesList(@Param("nameRemakes")List<String> nameRemakes);

}
