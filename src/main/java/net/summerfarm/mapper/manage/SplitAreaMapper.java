package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SplitArea;
import net.summerfarm.model.vo.SplitAreaVO;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface SplitAreaMapper {

    /**
    * 插入
    */
    int saveSplitArea(SplitArea splitArea);

    /**
    * 修改状态
    */
    int updateSplitArea(SplitArea splitArea);


    SplitArea selectSplitAreaByAreaNo(SplitArea splitArea);

    /**
    * 根据时间查询待生效的数据
    */
    List<SplitArea> selectByTime(LocalDate date);

    /**
    * 查询信息
    */
    SplitAreaVO selectSplitAreaNo(Integer splitAreaNo);

    /**
    * 根据id查询详情
    */
    SplitArea selectSplitAreaById(Integer id);


    SplitArea selectSplitAreaAll(Integer areaNo);



}
