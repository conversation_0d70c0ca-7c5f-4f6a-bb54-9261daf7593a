package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.StockStorageItem;
import net.summerfarm.model.domain.StockTaskItem;
import net.summerfarm.model.input.purchase.StockDashboardQueryInput;
import net.summerfarm.model.vo.StockTaskItemVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface StockTaskItemMapper {
    int insert(StockTaskItem stockTaskItem);

    int insertBatch(List<StockTaskItem> stockTaskItems);

    List<StockTaskItem> select(StockTaskItem stockTaskItem);

    int update(StockTaskItem stockTaskItem);

    List<StockTaskItemVO> selectByStockTaskId(Integer stockTaskId);

    /**
     * 根据出库任务id查询出库任务详情
     * @param stockTaskIds 出库任务id
     * @return 出库任务详情
     */
    List<StockTaskItemVO> selectByStockTaskIdList(@Param("stockTaskIds") List<Integer> stockTaskIds);

    StockTaskItem selectByPrimaryKey(Integer id);

    /**
     * 查询sku
     * @param sku
     * @param startTime
     * @param startTime
     * @return
     */
    List<StockTaskItemVO> selectAutoTransfer(@Param("storeNo") Integer storeNo, @Param("sku") String sku, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
    * 查询sku某天的销售出库
    */
    List<StockTaskItem> queryNumberBySku(@Param("sku") String sku, @Param("date") LocalDate date);

    /**
    * 查询自提订单的销售任务
    */
    StockTaskItem queryNumberByOrderNo(@Param("sku") String sku, @Param("orderNo") String orderNo);

    /**
    * @param stockTaskId
     * @param sku
     * @return
    */
    StockTaskItem queryStockTaskItem(@Param("sku") String sku, @Param("stockTaskId") Integer stockTaskId);

    /**
     * 查询入库任务
     * @param type
     * @param purchaseNo
     * @param sku
     * @return
     */
    List<StockTaskItem> selectOne(@Param("type") Integer type, @Param("taskNo") String purchaseNo, @Param("sku") String sku);

    /**
     * 查询入库任务
     * @param type
     * @param purchaseNo
     * @return
     */
    List<StockTaskItem> selectByPurchaseNo(@Param("type") Integer type, @Param("taskNo") String purchaseNo);


    BigDecimal countCapacity(@Param("stockTaskIds")List<Integer> stockTaskIds);
}
