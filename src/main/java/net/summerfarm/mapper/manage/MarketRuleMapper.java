package net.summerfarm.mapper.manage;

import java.util.Collection;
import net.summerfarm.model.domain.MarketRule;
import net.summerfarm.model.input.market.MarketCouponReturnPageInput;
import net.summerfarm.model.vo.MarketCouponReturnVO;
import net.summerfarm.model.vo.MarketRuleVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MarketRuleMapper {
    int insert(MarketRule record);

    int updateByPrimaryKeySelective(MarketRule record);

    List<MarketRule> select(MarketRule selectKeys);

    List<MarketRule> selectWithout(MarketRule selectKeys);

    MarketRule selectOne(Integer id);

    List<MarketRuleVO> selectOpen(@Param("skuList") Collection<String> skuList);

    List<MarketRule> selectWithoutBySku(MarketRule selectKeys);

    /**
     * 查询销售可见的所有未过期满减活动
     * @param areaNo 地区编号
     * @param name 活动名称
     * @param adminId 销售id
     * @return 满减活动信息
     */
    List<MarketRule> selectMarketRuleByBD(@Param("areaNo") Long areaNo, @Param("name") String name, @Param("adminId") Integer adminId);

    /**
     * 查询当前类目中的活动
     * @param categoryId
     * @return
     */
    List<MarketRule> selectMarketList(Integer categoryId);

    List<MarketCouponReturnVO> page(MarketCouponReturnPageInput input);
}