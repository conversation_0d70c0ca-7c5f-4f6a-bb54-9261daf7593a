package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.ExpenseDetail;
import net.summerfarm.model.param.bms.ExpenseParam;
import net.summerfarm.model.vo.ExpenseDetailVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> xiang
 * create at:  2021-11-11
 */
@Repository
public interface ExpenseDetailMapper {

    List<ExpenseDetail> selectByExpenseId(Integer id);

    List<ExpenseDetailVO> selectByParam(ExpenseParam param);
}
