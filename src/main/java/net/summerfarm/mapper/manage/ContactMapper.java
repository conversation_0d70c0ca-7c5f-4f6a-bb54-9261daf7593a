package net.summerfarm.mapper.manage;

import net.summerfarm.common.util.es.dto.EsContactDTO;
import net.summerfarm.model.DTO.ContactNameDTO;
import net.summerfarm.model.domain.Contact;
import net.summerfarm.model.input.FenceInput;
import net.summerfarm.model.vo.ContactVO;
import net.summerfarm.model.vo.FenceInfoVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@Repository
public interface ContactMapper {

    int insertSelective(Contact record);

    List<Contact> select(Contact contact);

    int updateByPrimaryKeySelective(Contact record);

    int updatePath(@Param("contactId") Long contactId);

    List<ContactVO> selectVOList(@Param("storeNo") Integer storeNo, @Param("deliveryStartDate") LocalDate deliveryStartDate, @Param("deliveryEndDate") LocalDate deliveryEndDate,
                                 @Param("times") Integer times);

    Contact selectByPrimaryKey(Long contactId);

    ContactVO selectById(Long contactId);

    /**
     * 更换账号地址绑定
     *
     * @param oldMid
     * @param newMid
     * @return
     */
    int changeContactMerchant(@Param("oldMid") Long oldMid, @Param("newMid") Long newMid);

    List<Contact> selectAllByMId(@Param("mId") Long mId);

    List<Contact> selectByMid(@Param("mId") Long mId, @Param("status") Integer status);

    /**
     * 更新地址信息
     *
     * @param fenceInput
     * @return
    */
    int updateContact(FenceInput fenceInput);

    /**
     * 根据地址查看相似的
     * @param contact 地址信息
     * @return
     */
    List<Contact> selectSimilar(Contact contact);

    /**
     * 查看城市和区域在配送周期内的联系地址
     * @param city 城市
     * @param area 区域
     * @param notExist 配送周期
     * @return 联系地址
     */
    List<Contact> selectByAreaDelivery(@Param("city") String city, @Param("area") String area, @Param("notExist") List<String> notExist);

    /**
     * 根据商户ID集合批量查询联系人信息
     * @param midSet 商户ID集合
     * @return 联系人信息
     */
    List<Contact> selectContacts(@Param(value = "midSet") Set<Long> midSet);

    /**
     * 获取联系人信息:es更新使用,勿动
     * @param mId 商户id
     * @return 联系人信息
     */
    List<EsContactDTO> selectEsContactByMId(@Param("mId") Long mId);

    /**
     * @param contactId contactId
     * @return 联系人信息
     */
    ContactNameDTO selectContactName(@Param("contactId")Long contactId);

    /**
     * 根据省市区查询联系人配送周期不为空的数据
     * @param provinceList 省
     * @param cityList 市
     * @param areaList 区
     * @return
     */
    List<ContactVO> selectDeliveryFrequent(@Param("provinceList")List<String> provinceList, @Param("cityList") List<String> cityList,
                                         @Param("areaList")List<String> areaList);

    /**
     * 更新联系人城配仓编号
     * @param city 城市
     * @param area 区域
     * @param newStoreNo 新城配仓编号
     */
    void updateContactStoreNo(@Param("city") String city, @Param("area") String area, @Param("newStoreNo") Integer newStoreNo, @Param("businessLine") Integer businessLine);

    /**
     * 取默认地址
     * @param mId 用户ID
     * @return 默认地址
     */
    Contact selectIsDefaultByMid(Long mId);

    /**
     * 获取有效的地址配送规则信息-仅配送规则初始化使用
     * @param
     * @return 联系人信息
     */
    List<Contact> selectAllDeLiveryFee();
}