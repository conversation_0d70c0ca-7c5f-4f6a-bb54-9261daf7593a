package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.SupplierCoordinationConfig;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【supplier_coordination_config(供应商协同配置)】的数据库操作Mapper
 * @createDate 2023-03-27 11:55:49
 * @Entity generator.domain.SupplierCoordinationConfig
 */
@Repository
public interface SupplierCoordinationConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SupplierCoordinationConfig record);

    int insertSelective(SupplierCoordinationConfig record);

    SupplierCoordinationConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SupplierCoordinationConfig record);

    int updateByPrimaryKey(SupplierCoordinationConfig record);

    /**
     * 根据供应商ID更新
     *
     * @param record
     * @return
     */
    int updateBySupplierIdSelective(SupplierCoordinationConfig record);

    /**
     * 根据供应商ID查询协同信息
     *
     * @param supplierId 供应商ID
     * @return 供应商协同配置
     */
    SupplierCoordinationConfig selectBySupplierId(Long supplierId);

}
