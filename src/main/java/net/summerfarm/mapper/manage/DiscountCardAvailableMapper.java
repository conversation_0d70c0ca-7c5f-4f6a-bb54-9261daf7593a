package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DiscountCardAvailable;
import net.summerfarm.model.vo.DiscountCardAvailableVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/5/20  14:41
 */
@Repository
public interface DiscountCardAvailableMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(DiscountCardAvailable record);

    int insertBatch(List<DiscountCardAvailable> list);

    int insertSelective(DiscountCardAvailable record);

    DiscountCardAvailable selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DiscountCardAvailable record);

    int updateByPrimaryKey(DiscountCardAvailable record);

    List<DiscountCardAvailable> selectByCardId(Integer id);

    List<DiscountCardAvailable> selectBySku(String sku);

    List<DiscountCardAvailableVO> selectDetailByCardId(Integer id);

}
