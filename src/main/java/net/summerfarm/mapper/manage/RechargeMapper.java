package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Recharge;
import net.summerfarm.model.vo.RechargeVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface RechargeMapper {

    int insert(Recharge recharge);

    /**
     * 退款申请
     * @param recharge
     * @return
     */
    int insertRefund(Recharge recharge);

    Recharge selectByPrimaryKey(Integer id);

    /**
     * 审核操作记录
     * @param recharge
     * @return
     */
    int update(Recharge recharge);

    List<RechargeVO> select(RechargeVO selectKey);

    /**
     * 查询收款流水认领的鲜沐卡金额
     * @param id
     * @return
     */
    BigDecimal selectWater(Long id);

    /**
     * 查询收款流水认领的鲜沐卡详情
     * @param id
     * @return
     */
    List<RechargeVO> selectWaterList(Long id);

    Recharge selectOne(Recharge selectKey);

    RechargeVO selectVO(@Param("rechargeNo") String rechargeNo);

    /**
     * 批量查询记录
     * @param recordNos
     * @return
     */
    List<RechargeVO> selectVOList(@Param("recordNos") List<String> recordNos);
}
