package net.summerfarm.mapper.manage;

import java.time.LocalDateTime;
import java.util.List;
import net.summerfarm.model.DTO.market.ActivityBasicQueryDTO;
import net.summerfarm.model.DTO.market.ActivityItemScopeDTO;
import net.summerfarm.model.DTO.market.ActivityPageRespDTO;
import net.summerfarm.model.DTO.market.ActivityScopeQueryDTO;
import net.summerfarm.model.domain.Activity;
import net.summerfarm.model.domain.market.ActivityBasicInfo;
import net.summerfarm.model.domain.market.ActivityItemConfig;
import net.summerfarm.model.domain.market.ActivityScopeConfig;
import net.summerfarm.model.input.MallActivityQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ActivityBasicInfoMapper {
    
    int deleteByPrimaryKey(Long id);

    
    int insert(ActivityBasicInfo record);

    
    int insertSelective(ActivityBasicInfo record);

    
    ActivityBasicInfo selectByPrimaryKey(Long id);

    
    int updateByPrimaryKeySelective(ActivityBasicInfo record);

    
    int updateByPrimaryKey(ActivityBasicInfo record);

    List<ActivityScopeConfig> selectByScope(ActivityBasicQueryDTO queryDTO);

    List<ActivityPageRespDTO> listByQuery(ActivityBasicQueryDTO queryDTO);

    List<ActivityItemScopeDTO> listByScope(@Param("list") List<ActivityScopeQueryDTO> list, @Param("type") Integer type, @Param("activityStatus") Integer activityStatus);

    ActivityBasicInfo getEffectingById(@Param("id") Long id);

    /**
     * 查询未失效的活动
     * @param list
     * @param type
     * @return
     */
    List<ActivityItemScopeDTO> listValidByScope(@Param("list") List<ActivityScopeQueryDTO> list, @Param("type") Integer type);


    List<Long> listValidByNowTime();

    /**
     * 查询所有处于生效时间内的数据:
     * @return
     */
    List<Long> listValidInitDataByNowTime();

    /**
     * 查询所有处理生效时间内的数据:
     * @return
     */
    List<Long> listInValidInitDataByStartDate(@Param("startDate")LocalDateTime startDate, @Param("endDate")LocalDateTime endDate);


    List<String> listAreaNoByBasicId(@Param("basicIdList") List<Long> list);

    List<String> listLargeAreaNoByBasicId(@Param("basicIdList") List<Long> list);


    List<Activity> listValidByQuery(MallActivityQuery mallActivityQuery);


    List<ActivityBasicInfo> selectUnderwayByEntity(ActivityBasicInfo basicInfo);
}