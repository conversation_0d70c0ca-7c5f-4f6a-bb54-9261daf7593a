package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.AreaDTO;
import net.summerfarm.model.DTO.market.MarketRuleDTO;
import net.summerfarm.model.DTO.market.MarketRuleScopeDTO;
import net.summerfarm.model.domain.AreaMarketRule;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface AreaMarketRuleMapper {

    /**
     * 批量保存城市数据
     * @param areaMarketRuleList 城市数据对象
     * @return
     */
    int insertBatch(@Param("list") List<AreaMarketRule> areaMarketRuleList);

    /**
     * 删除城市数据
     * @param ruleId 规则id
     */
    void deleteByRuleId(Integer ruleId);

    /**
     * 查询城市组数据
     * @param ruleId 规则id
     * @return 城市组数据集合
     */
    List<AreaDTO> select(Integer ruleId);

    /**
     * 批量查询城市组数据
     * @param marketRuleIds 规则id
     * @return 城市组数据集合
     */
    List<AreaDTO> batchSelectList(@Param("list") List<Integer> marketRuleIds);

    List<MarketRuleDTO> selectByScope(@Param("scopes") List<MarketRuleScopeDTO> scopes, @Param("orderTime") Date orderTime);
}