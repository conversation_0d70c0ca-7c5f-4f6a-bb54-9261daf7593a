package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.QuantityRecord;
import net.summerfarm.model.vo.QuantityRecordVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by wjd on 2018/10/26.
 */

@Repository
public interface QuantityRecordMapper {
    List<QuantityRecord> selectLastOnlineQuantityRecord(@Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("startTime") LocalDateTime startTime);

    //最早的售罄情况
    List<QuantityRecord> selectNearlySoldOutRecord(@Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("quantityType") String quantityType, @Param("newQuantity") Integer newQuantity, @Param("startTime") LocalDateTime startTime);

    //区间内的库存变化
    List<QuantityRecord> selectQuantityRecordList(@Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("quantityType") String quantityType, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    QuantityRecord selectOneByEndTime(@Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("quantityType") String quantityType, @Param("endTime") LocalDateTime endTime);

    QuantityRecord selectOneByStartTime(@Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("quantityType") String quantityType, @Param("startTime") LocalDateTime startTime);

    List<String> selectSoldOutRecord(@Param("storeNo") Integer storeNo, @Param("quantityType") String quantityType, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    List<QuantityRecord> selectQuantityRecordListArea(@Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("quantityType") String quantityType, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    QuantityRecord selectOneByStartTimeAreaNo(@Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("quantityType") String quantityType, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    List<String> selectListByEndTimeByAreaNo(@Param("storeNo") Integer storeNo, @Param("quantityType") String quantityType, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("areaNo") Integer areaNo);

    QuantityRecord selectOneByEndTimeBySku(@Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("quantityType") String quantityType, @Param("endTime") LocalDateTime endTime, @Param("startTime") LocalDateTime startTime);

}
