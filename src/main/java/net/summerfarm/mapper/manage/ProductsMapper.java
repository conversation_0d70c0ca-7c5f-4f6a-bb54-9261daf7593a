package net.summerfarm.mapper.manage;

import net.summerfarm.common.util.PageVo;
import net.summerfarm.manage.client.inventory.dto.req.InventoryUpdateReqDTO;
import net.summerfarm.manage.client.inventory.dto.req.UpdateSkuOutdatedReqDTO;
import net.summerfarm.manage.client.products.dto.req.UpdateSpuOutdatedReqDTO;
import net.summerfarm.model.domain.Products;
import net.summerfarm.model.input.ProductQuery;
import net.summerfarm.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
public interface ProductsMapper {

    int insertSelective(Products record);

    int updateByPrimaryKeySelective(Products record);

    int update(Products products);

    List<ProductsVO> select(ProductsVO selectKeys);

    /**
     * 根据条件分页查询
     * @param productQuery 查询对象
     * @return 商品及sku集合
     */
    List<ProductListVO> selectPageByCondition(ProductQuery productQuery);

    /**
     * 通过pdId查询下面的sku数据
     * @param pdId pdId
     * @return sku数据集合
     */
    List<InventoryDetailVO> selectInventoryByPdId(Long pdId);

    int deleteAll(Long[] ids);

    ProductsVO selectByPdId(Long pdId);

    List<Products> selectList(Products products);

    List<Products> selectPageForCreatePurchaseRule(Products products);

    ProductsSkuVO selectVO(Products selectKey);

    List<Products> queryEsProductsInfo(@Param("pageIndex") Integer pageIndex, @Param("pageSize") Integer pageSize);

    /**
     * 根据类目id查询
     * @param categoryIds
     * @return 类目下所有有效状态下的sku
     */
    List<Products> selectByCategoryIds(@Param("categoryIds") Set<Integer> categoryIds);

    /**
     * 根据类目id查询
     * @param categoryIds
     * @return 类目下所有sku
     */
    List<Products> selectByCategoryIdList(@Param("categoryIds") Set<Integer> categoryIds);

    /**
     * 根据名称和类目精确查找商品
     * @param categoryId 类目id
     * @param pdName     商品名称
     * @return
     */
    List<Products> selectByCIdAndPdName(@Param("categoryId") Integer categoryId, @Param("pdName") String pdName
            ,@Param("list") List<String> pdNameList);

    /**
     * 校验pdNo是否存在
     * @param pdNo
     * @return
     */
    Long selectPdIdByPdNo(String pdNo);

    Products selectByPrimaryKey(Long pdId);

    /**
     * 不在回收站中的sku
     * @return
     */
    List<SKUGroupVO> selectGroupBySpu();

    /**
     * 查询时段内spu商品
     */
    List<ProductsVO> selectByStart(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计待审核上新
     * @param createType 上新类型
     * @param start 开始时间
     * @param end 结束时间
     * @return
     */
    int countUnAudit(@Param("createType") int createType, @Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

    /**
     * 查询上新商品
     * @param query 查询条件
     * @return spu信息
     */
    List<ProductsVO> selectCreate(ProductsCreateQuery query);

    /**
     * 查询pdName和pdId
     * @return pdName和pdId
     */
    List<Products> selectSearchData();

    /**
     * 根据sku查询
     * @param sku
     * @return
     */
    ProductVO queryBySku(String sku);

    /**
     * 查询是否有重复的SPU
     * @param pdNo
     * @return
     */
    int count(Integer pdNo);

    /**
     * 查询所有商品名称
     * @param pageVo 分页信息
     * @return 商品名称数据
     */
    List<String> selectAllPdName(PageVo pageVo);

    /**
     * 查询重复的商品名称
     * @param pdName
     * @return
     */
    int selectByPdNameCount(String pdName);

    /**
     * 根据pdNo查询商品列表
     *
     * <AUTHOR>
     * @date 2024/8/13 14:31
     * @param pdNoList 商品编码列表
     * @return List<Products>
     */
    List<Products> selectListByPdNo(@Param("pdNoList")List<String> pdNoList);

    /**
     * 根据PdNo查询Cid
     * @param spu
     * @return
     */
    Integer selectCidByPdNo(String spu);

    /**
     * 根据pdNo查询
     * @param pdNo
     * @return
     */
    Products selectPdNo(String pdNo);

    List<Products> selectSaasData(Integer adminId);

    List<Products> selectSaasDataByCreateTypeList(Integer adminId, @Param("createTypeList") List<Integer> createTypeList);

    List<Products> selectListByIdList(@Param("pdIdList") List<Long> pdIdList);


    /**
     * 查询商品
     *
     * @param sku
     * @return
     */
    ProductVO queryProductBySku(String sku);


    ProductVO queryProductBySkuV2(String sku);

    List<ProductVO>  queryProductBySkuList(List<String> list);

    int batchUpdate(@Param("list") List<InventoryUpdateReqDTO> list);

    List<ProductVO> queryListBySku(@Param("skus") List<String> skus);

    /**
     * 批量修改spu 生命周期
     * @param list
     * @return
     */
    int batchUpdateOutdated(@Param("list") List<UpdateSpuOutdatedReqDTO> list);
}
