package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.TimingOrderRefundTime;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TimingOrderRefundTimeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TimingOrderRefundTime record);

    int insertSelective(TimingOrderRefundTime record);

    TimingOrderRefundTime selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TimingOrderRefundTime record);

    int updateByPrimaryKey(TimingOrderRefundTime record);

    TimingOrderRefundTime selectByOrderNo(@Param("orderNo")String orderNo);

    List<TimingOrderRefundTime> selectByOrderNos(@Param("orderNos")List<String> orderNos);

    int batchUpdateByPrimaryKey(@Param("list")List<TimingOrderRefundTime> records);
}