package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.RechargeRecord;
import net.summerfarm.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface RechargeRecordMapper {
    int insert(RechargeRecord rechargeRecord);

    @RequiresDataPermission(originalField = "m.area_no")
    List<RechargeRecordVO> selectRechargeRecord(RechargeRecordVO selectKey);

    RechargeRecord selectOne(RechargeRecord selectKey);

    /**
     * 修改充值记录账号归属
      * @param oldMid 原账号id
     * @param newMid 新账号id
     * @param accountId 子账号id
     * @return
     */
    int changeRecordMerchant(@Param("oldMid") Long oldMid, @Param("newMid") Long newMid, @Param("accountId") Long accountId);


    /**
     * 被合并账户流水历史
     *
     * @param rechargeRecordNo 变动记录编号
     * @param mId              被合并门店id
     * @param accountId        被合并门店账户id
     * @param type             类型 0 消费 2 充值
     */
    void insertMergedRechargeRecord(@Param("rechargeRecordNo")String rechargeRecordNo,@Param("mId")Long mId,@Param("accountId")Long accountId,@Param("type")Integer type);

    /**
     * 合并账户流水历史
     *
     * @param rechargeRecordNo 变动记录编号
     * @param mId              合并门店id
     * @param subMId           被合并门店id
     * @param accountId        被合并门店账户id
     * @param type             类型 0 消费 2 充值
     */
    void insertCombineRechargeRecord(@Param("rechargeRecordNo")String rechargeRecordNo,@Param("mId")Long mId,@Param("subMId")Long subMId,@Param("accountId")Long accountId,@Param("type")Integer type);

    /**
     * 鲜沐卡总余额
     * 有余额的门店数量
     * @param localDateTime
     * @return
     */
    CardSurveyVO selectDate(LocalDateTime localDateTime);

    /**
     * 充值金额模块数据源查询
     * @param startTime
     * @param endTime
     * @return
     */
    RechargeAmountVO selectRecharge(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 消费金额模块数据查询
     * @param startTime
     * @param endTime
     * @return
     */
    ConsumptionAmountVO selectConsumption(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 订单退款模块数据查询
     * @param startTime
     * @param endTime
     * @return
     */
    OrderRefundAmountVO selectRefund(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 余额退还模块数据查询
     * @param startTime
     * @param endTime
     * @return
     */
    BalanceRefundAmountVO selectBalanceRefund(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 鲜沐卡总余额
     * 有余额的门店数量
     * @param localDateTime
     * @return
     */
    List<RechargeRecord> selectDateForXianMu(LocalDateTime localDateTime);


}
