package net.summerfarm.mapper.manage;

import net.summerfarm.model.DTO.purchase.StockAllocationCategoryConfigDTO;
import net.summerfarm.model.domain.StockAllocationCategoryConfig;
import net.summerfarm.model.domain.StockAllocationConfigRecord;
import net.summerfarm.model.input.StockAllocationConfigInput;
import net.summerfarm.model.vo.StockAllocationConfigVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: xrt
 * @Date: 2022/02/16
 */

@Deprecated
@Repository
public interface StockAllocationCategoryConfigMapper {

    /**
     * 查询采购负责人
     * @param selectKeys
     * @return
     */
    @Deprecated
    List<StockAllocationConfigVO> select(StockAllocationConfigInput selectKeys);

    /**
     * 修改
     * @param selectKeys
     */
    @Deprecated
    void update(StockAllocationConfigInput selectKeys);

    /**
     * 删除
     * @param id
     */
    @Deprecated
    void deleteByPrimaryKey(Integer id);

    /**
     * 新增
     * @param selectKeys
     */
    @Deprecated
    void insert(StockAllocationConfigInput selectKeys);


    /**
     * 根据商品id查询
     * @param pdId
     * @return
     */
    @Deprecated
    StockAllocationCategoryConfig selectByPdId(String pdId);

    /**
     * 根据adminId查询负责的pdNo
     * @return
     */
    @Deprecated
    List<String> selectByAdminId(Integer adminId);

    @Deprecated
    List<StockAllocationCategoryConfigDTO> selectList(Integer startNum ,Integer pageSize);
}
