package net.summerfarm.mapper.manage;

import io.lettuce.core.dynamic.annotation.Param;
import net.summerfarm.model.domain.WillExpirePriceLog;

/**
 * @author: <EMAIL>
 * @create: 2022/6/13
 */
public interface WillExpirePriceLogMapper {

    int deleteByPrimaryKey(Long id);

    int insert(WillExpirePriceLog record);

    int insertSelective(WillExpirePriceLog record);

    WillExpirePriceLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WillExpirePriceLog record);

    WillExpirePriceLog getLastBySkuAndArea(@Param("areaNo") Integer areaNo, @Param("sku") String sku);
}
