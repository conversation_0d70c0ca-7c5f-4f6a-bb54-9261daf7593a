package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.ShoppingCart;
import net.summerfarm.model.vo.OrderItemVO;
import org.apache.ibatis.annotations.Param;


import java.time.LocalDateTime;
import java.util.List;

public interface ShoppingCartMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ShoppingCart record);

    int insertSelective(ShoppingCart record);

    ShoppingCart selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ShoppingCart record);

    int updateByPrimaryKey(ShoppingCart record);

    int deleteByMids(@Param("mIdList") List<Long> list);

    int deleteByMid(Long mId);

    /**
     * 根据biz_id 和 sku 进行删除，限量200
     * @param bizId
     * @param sku
     * @return
     */
    int deleteByBizIdAndSku(@Param("bizId") Long bizId,@Param("sku") String sku);

    /**
     * 根据bizId进行删除，限量200
     * @param bizId
     * @return
     */
    int deleteByBizId(Long bizId);

    /**
     * 查询具有客户大单的订单
     * @param mId 订单编号
     * @param time 加购时间
     * @return 客户大单的订单详情
     */
    List<OrderItemVO> selectOrderReminder(@Param("mId") Long mId, @Param("time") LocalDateTime time);
}