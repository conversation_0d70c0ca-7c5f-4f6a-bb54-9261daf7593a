package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CrmManageArea;
import net.summerfarm.model.domain.MajorRebate;
import net.summerfarm.model.input.QueryManageAreaInput;
import net.summerfarm.model.vo.ManageAreaVo;
import net.summerfarm.model.vo.ManagerAreaVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmManageAreaMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CrmManageArea record);

    int insertSelective(CrmManageArea record);

    CrmManageArea selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CrmManageArea record);

    int updateByPrimaryKey(CrmManageArea record);

    /**
     * 查询区域配置页面信息
     * @param queryManageAreaInput 查询条件：城市，区域，区域负责人，部门负责人
     * @return 区域配置页面信息
     */
    List<ManageAreaVo> selectManageArea(QueryManageAreaInput queryManageAreaInput);

    void insertArea(@Param("mbId") Integer mbId, @Param("city") List<Integer> subCity, @Param("adminId") Integer adminId);

    void deleteArea(Integer mbId);

    List<MajorRebate> selectArea(@Param("id") Integer id, @Param("saveManageAreaInputId") Integer saveManageAreaInputId);

    /**
     * 获取城市配仓信息
     * @param areaNo
     * @return
     */
    CrmManageArea selectByAreaNO(Integer areaNo);

    /**
     * 查询所有城市负责人和负责的所有城市
     * @return 所有城市负责人和负责的所有城市汇总
     */
    List<ManagerAreaVO> selectManagerAreaList();
}