package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.ChangeFence;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface ChangeFenceMapper {

    /**
    * 根据城市名称查询
     * @param areaNo
     * @return
    */
    List<ChangeFence> selectByAreaNo(Integer areaNo);


    /**
     * 新增切仓
     * @param changeFence
    */
    int insertChangeFence(ChangeFence changeFence);


    /**
     * 更改状态
     *
     *
    */
    int updateChangeFence(@Param("id") Integer id, @Param("status") Integer status);

    /**
     * 更新
     * @param changeFence
     * @return
     */
    int updateSelective(ChangeFence changeFence);


    /**
     * 根据id
     * @param fenceId
     * @return
     */
    ChangeFence selectByFenceId(Integer fenceId);


    List<ChangeFence> selectByChangeToFenceId(Integer changToFenceId);

    /**
    * 根据城配仓查询
    */
    List<ChangeFence> selectByChangeToStoreNo(Integer storeNo);

    /**
     * 根据运营服务区域取最老的切仓计划
     * @param areaNo
     * @return
     */
    ChangeFence selectEffectByAreaNo(Integer areaNo);

    /**
     * 根据主键id查询
     * @param id
     * @return
     */
    ChangeFence selectByPrimaryKey(Integer id);

    /**
     * 查询指定日期成功的切仓任务
     * @param date 日期
     * @return 成功的切仓任务集合
     */
    List<ChangeFence> selectSuccessTasks(LocalDate date);

    /**
     * 查询是否存在处理中的切仓任务
     * @param storeNo 城配仓编号
     * @return 任务数
     */
    Integer selectHandlingTasksByStoreNo(Integer storeNo);

    /**
     * 查询是否存在处理中的切仓任务
     * @param fenceId 围栏ID
     * @return 任务数
     */
    Integer selectHandlingTasksByFenceId(Integer fenceId);

    /**
     * 查询是否存在处理中的切仓任务
     * @return 任务数
     */
    String selectHandlingAreas();
}
