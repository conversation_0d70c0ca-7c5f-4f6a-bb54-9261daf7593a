package net.summerfarm.mapper.manage;

import java.util.Collection;
import java.util.Set;
import net.summerfarm.model.domain.FileDownloadRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/12 11:49
 */
@Repository
public interface FileDownloadRecordMapper {

    /**
     * 根据导出人id查询导出记录
     * @param adminId
     * @return
     */
    List<FileDownloadRecord> selectByAdminId(@Param("adminId") Integer adminId, @Param("source") String source,@Param("tenantId")Long tenantId);

    /**
     * 查询非已过期状态并且有过期时间的数据
     * @return
     */
    List<FileDownloadRecord> select();

    /**
     * 资金、收入单据/明细列表展示
     * @param type
     * @param startTime
     * @return
     */
    List<FileDownloadRecord> selectByType(@Param("type") Integer type, @Param("startTime") LocalDate startTime, @Param("source") String source,@Param("tenantId")Long tenantId);

    /**
     * 查询待执行的任务(status = 0), 默认根据ID排序
     * @param type
     * @return
     */
    List<FileDownloadRecord> selectPendingTasks(@Param("type") Integer type);

    /**
     * 新增导出记录
     * @param fileDownloadRecord
     * @return
     */
    int insert(FileDownloadRecord fileDownloadRecord);

    /**
     * 更新导出记录
     * @param record
     */
    void updateById(FileDownloadRecord record);

    /**
     * 更新导出记录的文件名
     * @param uId
     * @param fileName
     * @return
     */
    int updateFileNameByUid(@Param("uId") String uId, @Param("fileName") String fileName);

    /**
     * 更新导出记录
     * @param record
     */
    void update(FileDownloadRecord record);

    /**
     * 修改状态
     * @param record
     */
    void updateFileName(FileDownloadRecord record);

    /**
     * 过期下载中心记录
     * @param id
     */
    void expireDownloadRecords(Long id);

    /**
     * 修改状态
     * @param record
     */
    void updateNameDetail(FileDownloadRecord record);

    /**
     * 修改过期数据状态
     */
    void updateExpiredData();

    /**
     * 获取上传文件信息,根据UID
     * @param uId 唯一标识
     * @return 文件信息
     */
    FileDownloadRecord selectByUid(String uId);

    /**
     * 查询信息
     * @param id
     * @return
     */
    FileDownloadRecord selectById(Long id);

    /**
     * 根据ID列表查询
     * @param ids
     * @return
     */
    List<FileDownloadRecord> selectByIdCollection(@Param("ids") Collection<Long> ids, @Param("type") Integer type);

    //FileDownloadRecord selectByTime(Long id);

    /**
     * 删除指定导出记录
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 删除用户所有的文件导出记录
     * @param adminId
     * @return
     */
    int deleteAll(Integer adminId);

}
