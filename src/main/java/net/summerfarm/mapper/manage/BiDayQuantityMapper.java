package net.summerfarm.mapper.manage;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface BiDayQuantityMapper {
    Integer quantitySum(@Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("areaNos") List<Integer> areaNos, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    Integer storeQuantity(@Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("adddate") LocalDate adddate);

    /**
     * 查询每天剩余库存总和
     * @param storeNo
     * @param sku
     * @param startDate
     * @param endDate
     * @return
     */
    int queryLeftQuantity(@Param("storeNo") Integer storeNo, @Param("sku") String sku, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
}
