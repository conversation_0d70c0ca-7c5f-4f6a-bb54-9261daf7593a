package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.CirclePeopleRuleAdmin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigInteger;
import java.util.List;

@Mapper
public interface CirclePeopleRuleAdminMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CirclePeopleRuleAdmin record);

    int insertSelective(CirclePeopleRuleAdmin record);

    CirclePeopleRuleAdmin selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CirclePeopleRuleAdmin record);

    int updateByPrimaryKey(CirclePeopleRuleAdmin record);

    void insertMids(@Param("mIds") List<Long> mIds, @Param("ruleId") Integer rule_id, @Param("adminId") Integer adminId);

    void deleteByRuleId(Integer id);
}