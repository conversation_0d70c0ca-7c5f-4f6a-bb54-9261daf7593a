package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.OrdersCoupon;
import net.summerfarm.model.vo.MerchantCouponVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface OrdersCouponMapper {

    int insertSelective(OrdersCoupon ordersCoupon);

    /**
     * 查询订单使用的优惠券信息
     * @param orderNo （"orderNo", 订单号）
     * @return
     */
    List<MerchantCouponVO> select(@Param("orderNo") String orderNo);
}