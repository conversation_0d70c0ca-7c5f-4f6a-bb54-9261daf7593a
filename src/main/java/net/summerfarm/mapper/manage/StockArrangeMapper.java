package net.summerfarm.mapper.manage;


import net.summerfarm.model.domain.StockArrange;
import net.summerfarm.model.input.srm.SrmSupplierStockArrangeQuery;
import net.summerfarm.model.param.ArrangeParam;
import net.summerfarm.model.param.pms.PurchaseSkuQuery;
import net.summerfarm.model.vo.ArrangeResultVO;
import net.summerfarm.model.vo.ArrangeSkuNumVO;
import net.summerfarm.model.vo.StockArrangeVO;
import net.summerfarm.model.vo.pms.PurchaseSkuCountVO;
import net.summerfarm.model.vo.srm.SrmStockArrangeVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> xiang
 * create at:  2021-12-29
 */
@Repository
public interface StockArrangeMapper {

    void insert(StockArrange arrange);

    /**
     * 根据id查询预约单
     * @param id
     * @return
     */
    StockArrangeVO selectByPrimaryKey(Integer id);

    /**
     * 修改预约单
     * @param update
     */
    void update(StockArrange update);

    /**
     * 根据任务id修改预约单
     * @param update
     */
    void updateByTaskId(StockArrange update);

    /**
     * 根据任务id修改预约单操作标识
     * @param update
     */
    void updateStateByTaskId(StockArrange update);

    /**
     * 根据采购单编号查询未完成的预约单
     * @param purchaseNo
     * @return
     */
    List<StockArrangeVO> selectUnFinishByPurchaseNo(String purchaseNo);

    /**
     * 根据采购单编号查询预约单
     * @param purchasesNo
     * @return
     */
    List<StockArrangeVO> selectByPurchaseNo(String purchasesNo);

    /**
     * 根据任务id查询预约单
     * @param id
     * @return
     */
    StockArrangeVO selectByTaskId(Integer id);

    /**
     * 入库更新采购单操作状态
     * @param updateIsClose
     */
    void updateStateByTaskIdNew(StockArrange updateIsClose);


    /**
     * 条件查询预约单
     * @param srmSupplierStockArrangeQuery 查询条件
     */
    List<StockArrangeVO> selectBySupplierQuery(@Param("srmSupplierStockArrangeQuery")SrmSupplierStockArrangeQuery srmSupplierStockArrangeQuery);

    SrmStockArrangeVO selectSrmDetailById(Integer stockArrangeId);

    SrmStockArrangeVO selectSrmDetailByPurchasesNo(String purchasesNo);

    Integer selectUnFinishRoadQuantity(@Param("warehouseNo")Integer areaNo,@Param("sku") String sku);

    void updateArrangeTimeByPrimayKey(StockArrangeVO param);

    List<ArrangeResultVO> selectPageWithSass(ArrangeParam selectKeys);

    List<ArrangeSkuNumVO> getSkuCountDataByStockArrangeIds(@Param("ids") List<Integer> stockArrangeIds);

    List<ArrangeSkuNumVO> getArrangeCountByStockArrangeIds(@Param("ids") List<Integer> stockArrangeIds);

    Integer updateRemark(@Param("id") Integer id,@Param("remark") String remark);

    String getSourceByTaskId(String taskId);

    void updateWarehouseNoById(@Param("warehouseNo") Integer warehouseNo, @Param("id") Integer id);

    List<StockArrange> selectNotWarehouseNoDataList();

}
