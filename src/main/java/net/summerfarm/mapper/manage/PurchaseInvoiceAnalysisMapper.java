package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PurchaseInvoiceAnalysis;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 2021/08/06
 */
@Repository
public interface PurchaseInvoiceAnalysisMapper {

    /**
     * 查看该管理员是否有未处理的数据
     * @param adminId
     * @return
     */
    int selectFile(Integer adminId);

    /**
     * 将未处理的数据清除
     * @param adminId
     * @param updater
     * @return
     */
    int updateStatus(@Param("adminId") Integer adminId, @Param("updater") String updater);

    /**
     * 将解析成功数据清除
     * @param adminId
     * @param updater
     * @return
     */
    int updateSuccessStatus(@Param("adminId") Integer adminId, @Param("updater") String updater);

    int deleteByPrimaryKey(Integer id);

    /**
     * 将excel导入的数据插入数据库
     * @param purchaseInvoiceAnalysis
     * @return
     */
    int insert(PurchaseInvoiceAnalysis purchaseInvoiceAnalysis);

    /**
     * 查询导入excel的解析结果
     * @param adminId
     * @param analysisType
     * @return
     */
    List<PurchaseInvoiceAnalysis> selectList(Integer adminId, Integer analysisType);

    /**
     * 含税金额总计
     * @param adminId
     * @param analysisType
     * @return
     */
    BigDecimal selectIncludedTaxSum(Integer adminId, Integer analysisType);

    /**
     * 查询同此导入的数据是否重复
     * @param invoiceCode
     * @param invoiceNumber
     * @return
     */
    int selectSameTime(String invoiceCode, String invoiceNumber);

    int selectHaveMoney(Integer adminId, Integer analysisType);

    int insertSelective(PurchaseInvoiceAnalysis record);

    PurchaseInvoiceAnalysis selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PurchaseInvoiceAnalysis record);

    int updateByPrimaryKey(PurchaseInvoiceAnalysis record);
}