package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.BiDayCost;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface BiDayCostMapper {
    BigDecimal getCost(@Param("sku") String sku, @Param("storeNo") Integer storeNo, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    List<String> maxCostSku(@Param("storeNos") List<Integer> storeNos, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("limit") Integer limit);

    List<Long> maxCostSpu(@Param("storeNos") List<Integer> storeNos, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("limit") Integer limit);

    List<BiDayCost> costSkuAll(@Param("sku") String sku, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("limit") Integer limit, @Param("storeNo") Integer storeNo);

    /**
    * 查询仓库的总成本
    */
    BigDecimal costAll(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("storeNo") Integer storeNo);

}
