package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.DirectPurchaseRecharge;
import net.summerfarm.model.input.DirectPurchaseRechargeQuery;
import net.summerfarm.model.vo.DirectPurchaseOrderVO;
import net.summerfarm.model.vo.DirectPurchaseRechargeVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Date: 2020/11/17 10:29
 * @Author: <EMAIL>
 */
@Repository
public interface DirectPurchaseRechargeMapper {

    int insert(DirectPurchaseRecharge record);

    int insertSelective(DirectPurchaseRecharge record);

    DirectPurchaseRecharge selectByPrimaryKey(@Param("id") Long id);

    int updateByPrimaryKeySelective(DirectPurchaseRecharge record);

    int updateByPrimaryKey(DirectPurchaseRecharge record);

    /**
     * 根据入参条件来获取返回的列表集合,包括了所有的详情内容
     * @param selectKeys
     * @return
     */
    List<DirectPurchaseRechargeVO> selectByKeys(DirectPurchaseRechargeQuery selectKeys);

    /**
     * 根据订单号查询已经支付过的流水信息
     * @param orderNo
     */
    List<DirectPurchaseOrderVO> selectByorderNo(@Param("orderNo") String orderNo);

    /**
     * 根据mId得到充值成功的流水号对应的资金使用情况
     * @param mId
     * @return
     */
    List<DirectPurchaseOrderVO> selectBymId(@Param("mId") Long mId);
}