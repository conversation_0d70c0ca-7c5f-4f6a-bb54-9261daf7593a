package net.summerfarm.mapper.manage;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import net.summerfarm.model.domain.PartnershipBuy;

public interface PartnershipBuyMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(PartnershipBuy record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(PartnershipBuy record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    PartnershipBuy selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(PartnershipBuy record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(PartnershipBuy record);
}