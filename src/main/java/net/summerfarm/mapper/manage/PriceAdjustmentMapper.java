package net.summerfarm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.model.domain.PriceAdjustment;
import net.summerfarm.model.vo.CostAndMarketPriceVO;
import net.summerfarm.model.vo.PriceAdjustVO;
import net.summerfarm.enums.PriceAdjustStatus;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PriceAdjustmentMapper {
    int insert(PriceAdjustment record);

    PriceAdjustment selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PriceAdjustment record);

    int updateByPrimaryKey(PriceAdjustment record);

    List<PriceAdjustVO> selectPriceAdjustVO(PriceAdjustVO selectKeys);

    List<CostAndMarketPriceVO> selectCostAndMarketPrice(@Param("sku") String sku, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    List<CostAndMarketPriceVO> selectLastCostAndMarketPrice(@Param("sku") String sku, @Param("startTime") LocalDateTime startTime);

    List<CostAndMarketPriceVO> selectNearlyCostAndMarketPrice(@Param("sku") String sku, @Param("endTime") LocalDateTime endTime);

    int selectInFlow(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    List<PriceAdjustVO> selectPriceAdjustVORecord(PriceAdjustVO selectKeys);

    int update2outOfTime(PriceAdjustVO outOfTimeSelectKeys);

    /**
     * 查找最近的已生效的调价记录
     * @param sku
     * @param startTime
     * @return
     */
    PriceAdjustment selectNearlyPriceAdjust(@Param("sku") String sku, @Param("startTime") LocalDateTime startTime, @Param("storeNo") Integer storeNo);

    /**
     * 取消待执行的调价记录
     *
     * @param storeNo storeNo
     * @param sku     sku
     * @return
     */
    int update2cancel(@Param("storeNo") Integer storeNo, @Param("sku") String sku);

    /**
     * 根据状态查询
     * @param status 状态 {@link PriceAdjustStatus}
     * @return
     */
    List<PriceAdjustment> selectByStatus(Integer status);


    @RequiresDataPermission(originalField = "a.area_no ")
    List<PriceAdjustVO> selectPriceAdjustVO2(PriceAdjustVO selectKeys);

    @RequiresDataPermission(originalField = "a.area_no ")
    List<PriceAdjustVO> selectPriceAdjustVO3(PriceAdjustVO selectKeys);

}