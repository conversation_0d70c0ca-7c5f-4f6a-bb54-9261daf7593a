package net.summerfarm.mapper.manage;


import net.summerfarm.model.domain.GoodsCodeOrder;
import net.summerfarm.model.vo.GoodsCodeOrderVO;

import java.util.List;

/**
 * 条码记录信息
 * <AUTHOR> ct
 * create at:  2021/12/13  11:35
 */
public interface GoodsCodeOrderMapper {


    /**
     * 新增
     * @param goodsCodeOrder
     * @return
     */
    int saveGoodsCodeOrder(GoodsCodeOrder goodsCodeOrder);

    /**
     *  批量新增
     * @param list
     * @return
     */
    int saveBatchGoodsCodeOrder(List<GoodsCodeOrder> list);

    /**
     * 查询信息
     * @param goodsCodeOrder
     * @return
     */
    List<GoodsCodeOrder> queryCodeMsg(GoodsCodeOrder goodsCodeOrder);

    /**
    * 查询订单信息
    */
    List<GoodsCodeOrderVO> queryGoodsCodeOrderVO(GoodsCodeOrder goodsCodeOrder);


    List<GoodsCodeOrderVO> querySaasGoodsCodeOrderVO(GoodsCodeOrder goodsCodeOrder);
}
