package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.Fence;
import net.summerfarm.model.input.FenceInput;
import net.summerfarm.model.vo.AllEfficientFenceVo;
import net.summerfarm.model.vo.CityStoreNoVO;
import net.summerfarm.model.vo.FenceVO;
import net.summerfarm.model.vo.ProvinceCityAreaVO;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface FenceMapper {

    /**
    * 插入
     * @param
     * @return
    */
    int insertFence(Fence fence);

    /**
    * 修改
     * @param fence
     * @return
    */
    int updateFence(Fence fence);

    /**
    * 作废
     * @param id 围栏id
     * @return
    */
    int deleteFence(Integer id);

    /**
    * 列表查询
    */
    List<FenceVO> selectByFence(FenceVO fence);


    /**
    * 详细信息查询
     * @param id 围栏id
     * @return
    */
    FenceVO selectFenceById(Integer id);

    /**
    * 根据高德Id获取围栏信息
    */
    Fence selectFenceByGdId(String gdId);

    /**
    * 查询信息
    */
    List<Fence> selectFence(Fence fence);

    /**
     * 详细信息查询
     * @param fenceName 围栏id
     * @return
     */
    Fence selectFenceByName(String fenceName);


    /**
    * 获取城市围栏
    */
    List<Fence> selectFenceArea(Fence fence);

    /**
    * 获取打包id
    */
    Integer selectMaxPackId();

    List<Integer> selectStoreNoByPickId(Integer pickId);

    /**
     * 根据等级获取地址
     */
    List<FenceInput> selectAll(String level);

    /**
    * 根据城市编号获取需要调整的信息
     * @param areaNo 区域编号
     * @return
    */
    List<Fence> selectChangeByAreaNo(Integer areaNo);

    /**
     * 修改 城配仓
     * @param fence
     * @return
     */
    int updateStoreNoFence(Fence fence);

    /**
    *
    */
    Fence selectFenceByFenceId(Integer id);

    /**
     * @param area 城市
     * @param city 区
     * @return 围栏信息
     */
    FenceVO selectFenceVoByAreaCity(@Param("area") String area, @Param("city") String city);

    /**
     * 查找所有有效的围栏
     * @return 有效的围栏
     */
    List<Fence> selectFenceAll();

    /**
     * 根据城配仓获取围栏数据
     * @param storeNos 城配仓编号数据
     * @return 截单围栏
     */
    List<Fence> selectByStroeNos(@Param("storeNos") Collection<Integer> storeNos);

    /**
     * 查询使用中或暂停的城配仓编号
     * @param areaNo areaNo
     * @return storeNo
     */
    Integer selectStoreNoByAreaNo(@Param("areaNo") Integer areaNo);

    /**
     * 查询所有有效的围栏信息
     * @return
     */
    List<AllEfficientFenceVo> getAllEfficientFenceInfo();

    /**
     * 根据城配仓查询行政区域
     * @param storeNo
     * @param address
     * @return
     */
    List<CityStoreNoVO> getCityByStoreNo(@Param("storeNo") Integer storeNo,@Param("address") String address,@Param("city")String city);

    List<ProvinceCityAreaVO> selectAddressByAreaNo(@Param("areaNo")Integer areaNo);

    /**
     * 根据城市查询围栏信息
     *
     * @param cityName
     * @return
     */
    List<FenceVO> selectByCityName(@Param("cityName") String cityName);

    /**
     * 根据城市查询围栏信息
     *
     * @param cityNames
     * @return
     */
    List<FenceVO> selectByCityNames(@Param("cityNames") List<String> cityNames);

    /**
     * 根据城市区域查询实际行政城市
     *
     * @param areaNo
     * @return
     */
    List<String> selectCityNameByAreaNo(Integer areaNo);

    List<Fence> listByAreaNos(@Param("areaNos") Collection<Integer> areaNos);

    /**
     * 批量更新围栏组信息
     * @param fenceIds 围栏Id集合
     * @param packId 组Id
     */
    void updateFencePack(@Param("fenceIds")List<Integer> fenceIds,
                         @Param("packId") Integer packId);

    /**
     * 查询SKU库存仓映射关系
     * @param storeNos 城配仓编号集合
     * @param sku 品
     * @return SKU库存仓映射关系对应库存仓集合
     */
    List<WarehouseStorageCenter> selectWarehouseListByMappingWithStoreNosAndSku(@Param(value = "list") List<Integer> storeNos, @Param(value = "sku") String sku);
}
