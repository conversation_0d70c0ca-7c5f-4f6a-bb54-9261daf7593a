package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.OrderItemPreferential;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 订单优惠明细表(OrderItemPreferential)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-17 19:23:10
 */
public interface OrderItemPreferentialMapper {

    /**
     * 通过订单号查询数据
     *
     * @param orderNo 订单编号
     * @return 实例对象
     */
    List<OrderItemPreferential> queryByOrderNo(@Param("orderNo") String orderNo);

    int insertSelective(OrderItemPreferential record);
}

