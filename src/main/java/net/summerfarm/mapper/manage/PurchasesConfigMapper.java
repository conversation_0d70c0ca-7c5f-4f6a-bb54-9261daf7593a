package net.summerfarm.mapper.manage;

import net.summerfarm.model.domain.PurchasesConfig;
import net.summerfarm.model.vo.PurchasesConfigVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PurchasesConfigMapper {

    int insert(PurchasesConfig purchasesConfig);

    int insertBatch(List<PurchasesConfig> purchasesConfigs);

    PurchasesConfig selectByPrimaryKey(Integer id);

    int delete(Integer id);

    int deleteBatch(List<Integer> ids);

    List<PurchasesConfigVO> selectVO(PurchasesConfigVO purchasesConfigVO);

    int update(PurchasesConfig purchasesConfig);

    List<PurchasesConfigVO> selectVOList(PurchasesConfigVO purchasesConfigVO);

    PurchasesConfig selectOne(PurchasesConfig purchasesConfig);

    List<PurchasesConfig> select(PurchasesConfig purchasesConfig);

    List<PurchasesConfig> selectBySomeKey(@Param("type") Integer type, @Param("areaManageId") Integer areaManageId, @Param("areaNo") Integer areaNo);

}
