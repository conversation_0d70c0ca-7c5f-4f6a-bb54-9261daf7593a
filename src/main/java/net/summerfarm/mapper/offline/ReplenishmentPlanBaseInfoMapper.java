package net.summerfarm.mapper.offline;

import net.summerfarm.model.domain.offline.ReplenishmentPlanBaseInfo;

import java.util.List;

public interface ReplenishmentPlanBaseInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ReplenishmentPlanBaseInfo record);

    int insertSelective(ReplenishmentPlanBaseInfo record);

    ReplenishmentPlanBaseInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ReplenishmentPlanBaseInfo record);

    int updateByPrimaryKey(ReplenishmentPlanBaseInfo record);

    List<ReplenishmentPlanBaseInfo> selectList();
}