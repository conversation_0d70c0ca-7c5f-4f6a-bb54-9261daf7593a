package net.summerfarm.mapper.offline;

import java.util.List;
import net.summerfarm.model.domain.MerchantTagsPool;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface MerchantTagsPoolMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(MerchantTagsPool record);

    MerchantTagsPool selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantTagsPool record);

    List<MerchantTagsPool> getByMId(@Param("mId") Long mId);

}