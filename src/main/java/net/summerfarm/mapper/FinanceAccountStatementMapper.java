package net.summerfarm.mapper;

import net.summerfarm.model.domain.FinanceAccountStatement;
import net.summerfarm.model.input.FinanceAccountStatementDBQuery;
import net.summerfarm.model.input.FinanceAccountStatementQuery;
import net.summerfarm.model.vo.FinanceAccountStatementVO;
import net.summerfarm.module.pms.model.input.AccountQueryInput;
import net.summerfarm.module.pms.model.vo.AccountListVO;
import net.summerfarm.module.pms.model.vo.FinanceAccountDataVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 财务对账单
 * <AUTHOR>
 */
@Repository
public interface FinanceAccountStatementMapper {
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     * @param record
     * @return
     */
    int insert(FinanceAccountStatement record);

    int insertSelective(FinanceAccountStatement record);

    /**
     * 查询待匹配发票阶段的对账单是否是未被匹配
     * @param id
     * @return
     */
    FinanceAccountStatement selectByPrimaryKey(Long id);

    /**
     * 查询单个信息
     * @param id
     * @return
     */
    FinanceAccountStatement selectId(Long id);

    /**
     * 查询对账单信息
     * @param id
     * @return
     */
    FinanceAccountStatementVO select(Long id);

    /**
     * 查询对账单信息
     * @param id
     * @return
     */
    FinanceAccountDataVO selectNewData(Long id);

    /**
     * 查询对账单信息
     * @param walletsId
     * @return
     */
    List<FinanceAccountStatementVO> selectByWallets(Long walletsId);

    /**
     * 待复核票夹归档，对账单状态变为待付款
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(FinanceAccountStatement record);

    /**
     * 查询对账单列表
     * @param financeAccountStatementInput
     * @return
     */
    List<AccountListVO> selectList(AccountQueryInput financeAccountStatementInput);

    /**
     * 历史数据查询 用来做数据初始化
     * @param status
     * @return
     */
    List<FinanceAccountStatementVO> selectOldList(Integer status);

    /**
     * 票夹驳回时对账单重新返回可以匹配
     * @param walletsId
     * @param currentProcessor
     * @return
     */
    int updateByPrimaryKey(@Param("walletsId") Long walletsId,@Param("currentProcessor") String currentProcessor);

    /**
     * 对账单可匹配
     * @param id
     * @return
     */
    int updateById(@Param("id") Long id);

    /**
     * 根据税号找到相应的对账单
     * @param financeAccountStatementQuery
     * @return
     */
    List<FinanceAccountStatementVO> selectByTaxNumber(FinanceAccountStatementDBQuery financeAccountStatementQuery);

    /**
     * 根据票夹id查询对账单信息
     * @param id
     * @return
     */
    List<FinanceAccountStatementVO> selectByWalletsId(Long id);

    /**
     * 查询待复核发票阶段的对账单信息
     * @param id
     * @return
     */
    List<FinanceAccountStatement> selectById(Long id);

    /**
     * 查询待复核阶段的对账单是否全部预付
     * @param id
     * @return
     */
    BigDecimal selectAdd(Long id);

    /**
     * 修改还没被发票匹配的待匹配发票阶段的对账单
     * @param financeAccountStatement
     * @return
     */
    int update(FinanceAccountStatement financeAccountStatement);

    /**
     * 对账单状态修改
     * @param financeAccountStatement
     * @return
     */
    int updateBack(FinanceAccountStatement financeAccountStatement);

    /**
     * 查询供应商采购单的金额
     * @param purchaseNo
     * @param supplierId
     * @param type
     * @return
     */
    BigDecimal selectByPurchaseNo(@Param("purchaseNo") String purchaseNo, @Param("supplierId") Integer supplierId, @Param("type") Integer type);

    /**
     * @param purchaseNo 采购单号
     * @param supplierId 供应商id
     * @return 符合条件的对账单列表
     */
    List<FinanceAccountStatementVO> selectByPurchaseNoAndSupplierId(@Param("purchaseNo") String purchaseNo, @Param("supplierId") Integer supplierId);

    List<String> queryInProcessPurchaseList(@Param("list") List<String> purchaseNoList, @Param("supplierId") Integer supplierId);

    BigDecimal queryAdjustAmountToalWithFinished(@Param("purchaseNo")String purchaseNo, @Param("supplierId") Integer supplierId);

    /**
    *
    * @date 2023/2/21 16:21
    * @param * @Param endTime:
    * @return * @return: java.util.List<java.lang.Long>
    */
    List<Long> getFinishedAccountIdByTime(String endTime);
}