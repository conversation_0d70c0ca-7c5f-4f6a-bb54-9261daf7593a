package net.summerfarm.mapper;

import net.summerfarm.model.domain.ProductLabel;
import net.summerfarm.model.domain.ProductLabelValue;
import net.summerfarm.model.vo.ProductLabelValueVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public interface ProductLabelValueMapper {

    /**
     * 批量插入
     * @param productLabelValueVos
     * @return
     */
    int insertBatch(List<ProductLabelValueVo>  productLabelValueVos);

    /**
     * 查询标签表ID
     * @return
     */
    List<Long> selectProductLabelId();

    /**
     * 根据ID修改
     * @param productLabelValue
     */
    void updateById(ProductLabelValue productLabelValue);

    /**
     * 查询所有标签
     * @return
     */
    List<ProductLabel> selectAll();

    /**
     * 根据sku和labelId查询
     * @param sku
     * @param labelId
     * @return
     */
    ProductLabelValueVo selectLabelBySkuAndLabelId(String sku, Long labelId);

    /**
     * 根据sku查询
     *
     * <AUTHOR>
     * @date 2025/1/13 15:27
     * @param sku
     * @return java.util.List<net.summerfarm.model.vo.ProductLabelValueVo>
     */
    List<ProductLabelValueVo> selectLabelBySku(@Param(value = "sku")String sku);

    /**
     * 查询打标商品
     * @param skuList
     * @return
     */
    List<ProductLabelValue> selectByLabelIdAndLabelValueAndSku(@Param(value = "skuList") List<String> skuList,Long labelId,Integer labelValue);
}
