package net.summerfarm.facade;

import com.cosfo.manage.client.page.PageResp;
import com.cosfo.manage.client.product.ProductProvider;
import com.cosfo.manage.client.product.req.ProductCategoryIdQueryReq;
import com.cosfo.manage.client.product.resp.ProductSkuCodeResp;
import lombok.extern.log4j.Log4j;
import net.summerfarm.common.util.SaasThreadLocalUtil;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Log4j
@Component
public class SaasCategoryFacade {
    @DubboReference
    private ProductProvider productProvider;

    /** 根据SAAS类目获取鲜沐sku的集合 **/
    public List<String> querySkuBySaasCategoryId(Integer categoryId) {
        List<String> skuList = new ArrayList<>();
        int pageIndex = 1;
        int pageSize = 1000;
        int total = 0;
        do {
            ProductCategoryIdQueryReq req = new ProductCategoryIdQueryReq();
            req.setCategoryId(Long.valueOf(categoryId));
            req.setTenantId(SaasThreadLocalUtil.getTenantId());
            req.setPageIndex(pageIndex);
            req.setPageSize(pageSize);
            DubboResponse<PageResp<ProductSkuCodeResp>> dtoDubboResponse = productProvider.queryProductBySaasCategoryId(req);
            if (dtoDubboResponse == null || dtoDubboResponse.getData() == null) {
                break;
            }
            PageResp<ProductSkuCodeResp> result = dtoDubboResponse.getData();
            List<String> collect = result.getList().stream()
                    .map(ProductSkuCodeResp::getAgentSkuCode)
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(collect)) {
                skuList.addAll(collect);
            }
            total = result.getTotal();
            pageIndex++;
        } while (skuList.size() < total);
        if (CollectionUtils.isEmpty(skuList)) {
            skuList.add("null");
        }
        return skuList;
    }

}
