package net.summerfarm.facade.marketing;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.facade.marketing.converter.ActivityConverter;
import net.summerfarm.facade.marketing.input.ActivityUpdateInput;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.client.activity.provide.ActivityProvide;
import net.xianmu.marketing.center.client.activity.req.ActivityUpdateAuditStatusReq;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/30 15:43
 * @PackageName:net.summerfarm.facade.marketing
 * @ClassName: ActivityFacade
 * @Description: TODO
 * @Version 1.0
 */
@Slf4j
@Component
public class ActivityFacade {

    @DubboReference
    private ActivityProvide activityProvide;

    public Boolean updateAuditStatus(ActivityUpdateInput input) {
        ActivityUpdateAuditStatusReq req = ActivityConverter.toActivityUpdateAuditStatusReq(input);
        DubboResponse<Boolean> response = activityProvide.updateActivityAuditStatus(req);
        if(Objects.isNull(response)|| !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())){
            log.error("ActivityFacade[]updateAuditStatus[]error,input:{}", JSON.toJSONString(input));
            return false;
        }
        return response.getData();
    }
}
