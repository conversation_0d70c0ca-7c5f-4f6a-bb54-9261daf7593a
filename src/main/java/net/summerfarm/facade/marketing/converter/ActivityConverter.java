package net.summerfarm.facade.marketing.converter;

import net.summerfarm.facade.marketing.input.ActivityUpdateInput;
import net.xianmu.marketing.center.client.activity.req.ActivityUpdateAuditStatusReq;

/**
 * <AUTHOR>
 * @Date 2025/6/30 15:57
 * @PackageName:net.summerfarm.facade.marketing.converter
 * @ClassName: ActivityConverter
 * @Description: TODO
 * @Version 1.0
 */
public class ActivityConverter {

    public static ActivityUpdateAuditStatusReq toActivityUpdateAuditStatusReq(ActivityUpdateInput input) {
        ActivityUpdateAuditStatusReq req = new ActivityUpdateAuditStatusReq();
        req.setBasicInfoId(input.getBasicInfoId());
        req.setAuditStatus(input.getAuditStatus());
        req.setAuditUserId(input.getAuditUserId());
        req.setAuditRemark(input.getAuditRemark());
        req.setAuditTime(input.getAuditTime());
        return req;
    }
}
