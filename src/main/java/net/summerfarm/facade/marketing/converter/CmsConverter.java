package net.summerfarm.facade.marketing.converter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import net.summerfarm.facade.marketing.dto.CmsPageInfoDTO;
import net.xianmu.marketing.center.client.cms.resp.CmsPageInfoResp;

/**
 * @author: <EMAIL>
 * @create: 2023/8/1
 */
public class CmsConverter {


    private CmsConverter() {
        // 无需实现
    }

    public static List<CmsPageInfoDTO> toCmsPageInfoDTOList(
            List<CmsPageInfoResp> cmsPageInfoRespList) {
        if (cmsPageInfoRespList == null) {
            return Collections.emptyList();
        }
        List<CmsPageInfoDTO> cmsPageInfoDTOList = new ArrayList<>();
        for (CmsPageInfoResp cmsPageInfoResp : cmsPageInfoRespList) {
            cmsPageInfoDTOList.add(toCmsPageInfoDTO(cmsPageInfoResp));
        }
        return cmsPageInfoDTOList;
    }

    public static CmsPageInfoDTO toCmsPageInfoDTO(CmsPageInfoResp cmsPageInfoResp) {
        if (cmsPageInfoResp == null) {
            return null;
        }
        CmsPageInfoDTO cmsPageInfoDTO = new CmsPageInfoDTO();
        cmsPageInfoDTO.setId(cmsPageInfoResp.getId());
        cmsPageInfoDTO.setName(cmsPageInfoResp.getName());
        cmsPageInfoDTO.setStartTime(cmsPageInfoResp.getStartTime());
        cmsPageInfoDTO.setEndTime(cmsPageInfoResp.getEndTime());
        cmsPageInfoDTO.setBackgroundColor(cmsPageInfoResp.getBackgroundColor());
        cmsPageInfoDTO.setButtonType(cmsPageInfoResp.getButtonType());
        cmsPageInfoDTO.setPageShare(cmsPageInfoResp.getPageShare());
        cmsPageInfoDTO.setSharePicture(cmsPageInfoResp.getSharePicture());
        cmsPageInfoDTO.setShareTitle(cmsPageInfoResp.getShareTitle());
        cmsPageInfoDTO.setShareDesc(cmsPageInfoResp.getShareDesc());
        return cmsPageInfoDTO;
    }
}
