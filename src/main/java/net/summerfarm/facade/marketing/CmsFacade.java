package net.summerfarm.facade.marketing;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.facade.marketing.converter.CmsConverter;
import net.summerfarm.facade.marketing.dto.CmsPageInfoDTO;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.client.cms.provider.CmsQueryProvider;
import net.xianmu.marketing.center.client.cms.resp.CmsPageInfoResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @author: <EMAIL>
 * @create: 2023/8/1
 */
@Slf4j
@Component
public class CmsFacade {

    @DubboReference
    private CmsQueryProvider cmsQueryProvider;

    /**
     * 获取cms专题页基础信息
     * @param id
     * @return
     */
    public CmsPageInfoDTO getBasicInfoById(Long id) {
        DubboResponse<CmsPageInfoResp> response = cmsQueryProvider.getBasicInfoById(id);
        if (response == null || !response.isSuccess()) {
            return null;
        }
        CmsPageInfoResp resp = response.getData();
        CmsPageInfoDTO cmsPageInfoDTO = CmsConverter.toCmsPageInfoDTO(resp);
        return cmsPageInfoDTO;
    }

}
