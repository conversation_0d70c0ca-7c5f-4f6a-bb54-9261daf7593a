package net.summerfarm.facade.auth.convert;

import com.google.common.base.Joiner;
import net.summerfarm.model.DTO.auth.MenuPurviewDTO;
import net.summerfarm.model.vo.auth.MenuPurviewVO;
import net.xianmu.authentication.client.dto.AuthMenuPurview;
import net.xianmu.authentication.client.input.PurviewWeighVO;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;


public class AuthMenuVOConvert {


    public static MenuPurviewVO convertMenuPurviewVoByAuthMenuPurview(AuthMenuPurview authMenuPurview) {
        MenuPurviewVO menuPurviewVO = new MenuPurviewVO();
        if (!StringUtils.isEmpty(authMenuPurview.getSonPurview())) {
            List<String> strings = Arrays.asList(authMenuPurview.getSonPurview().split(";"));
            menuPurviewVO.setSonPurviewList(strings);
        }
        menuPurviewVO.setSonPurview(authMenuPurview.getSonPurview());
        menuPurviewVO.setCreateTime(authMenuPurview.getCreateTime());
        menuPurviewVO.setDefaultType(authMenuPurview.getDefaultType());
        menuPurviewVO.setDescription(authMenuPurview.getDescription());
        menuPurviewVO.setId(authMenuPurview.getId());
        menuPurviewVO.setPurviewName(authMenuPurview.getPurviewName());
        menuPurviewVO.setWeight(authMenuPurview.getWeight());
        menuPurviewVO.setUrl(authMenuPurview.getUrl());
        menuPurviewVO.setUpdateTime(authMenuPurview.getUpdateTime());
        menuPurviewVO.setMenuName(authMenuPurview.getMenuName());
        menuPurviewVO.setType(authMenuPurview.getType());
        menuPurviewVO.setLastUpdater(authMenuPurview.getLastUpdater());
        menuPurviewVO.setSystemOrigin(authMenuPurview.getSystemOrigin());
        menuPurviewVO.setParentId(authMenuPurview.getParentId());
        return menuPurviewVO;
    }

    public static AuthMenuPurview dtoToAuthMenuPurview(MenuPurviewDTO authMenuPurview) {
        AuthMenuPurview menuPurviewVO = new AuthMenuPurview();
        menuPurviewVO.setDescription(authMenuPurview.getDescription());
        menuPurviewVO.setId(authMenuPurview.getId());
        menuPurviewVO.setParentId(authMenuPurview.getParentId());
        menuPurviewVO.setPurviewName(authMenuPurview.getType() == 1 ? authMenuPurview.getMenuName() : null);
        menuPurviewVO.setWeight(authMenuPurview.getWeight());
        menuPurviewVO.setUrl(authMenuPurview.getUrl());
        if (!CollectionUtils.isEmpty(authMenuPurview.getSonPurviewList())){
            menuPurviewVO.setSonPurview(Joiner.on(",").join(authMenuPurview.getSonPurviewList()));
        }
        menuPurviewVO.setMenuName(authMenuPurview.getType() == 1 ? null : authMenuPurview.getMenuName());
        if (authMenuPurview.getType() != null) {
            menuPurviewVO.setType(authMenuPurview.getType().byteValue());
        }
        return menuPurviewVO;
    }

    public static PurviewWeighVO dtoToPurviewWeighVOList(MenuPurviewDTO menuPurviewDTO) {
        PurviewWeighVO purviewWeighVO = new PurviewWeighVO();
        purviewWeighVO.setWeight(menuPurviewDTO.getWeight());
        purviewWeighVO.setId(menuPurviewDTO.getId());
        return purviewWeighVO;
    }
}
