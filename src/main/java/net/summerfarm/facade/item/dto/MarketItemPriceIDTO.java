package net.summerfarm.facade.item.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-07-18
 * @description
 */
@Data
public class MarketItemPriceIDTO {

    /**
     * item id
     */
    private Long itemId;
    /**
     * 0、按成本价百分比上浮 1、按成本价定额上浮 2、固定价 3、按售价百分比下调 4、按售价定额下调
     */
    private Integer strategyType;
    /**
     * 策略值
     */
    private BigDecimal strategyValue;

    /**
     * 价格目标 0-品牌方,1-门店,2-运营区域
     */
    private Integer targetType;
    /**
     * 报价目标Id
     */
    private Long targetId;
}
