package net.summerfarm.facade.pms;


import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.pms.client.provider.SrmMessageConfigQueryProvider;
import net.summerfarm.pms.client.req.SrmMessageConfigQueryReq;
import net.summerfarm.pms.client.resp.SrmMessageConfigListResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class SrmQueryFacade {
    @DubboReference
    private SrmMessageConfigQueryProvider srmMessageConfigQueryProvider;


    public List<SrmMessageConfigListResp> querySrmMessageConfigList(SrmMessageConfigQueryReq srmMessageConfigQueryReq) {
        log.info("请求获取srm消息配置:{}", JSON.toJSONString(srmMessageConfigQueryReq));
        DubboResponse<List<SrmMessageConfigListResp>> response = srmMessageConfigQueryProvider.querySrmMessageConfigList(srmMessageConfigQueryReq);
        if(!response.isSuccess()){
            throw new ProviderException("请求获取srm消息配置失败"+response.getMsg());
        }
        return response.getData();
    }


}
