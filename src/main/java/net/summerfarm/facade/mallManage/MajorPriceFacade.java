package net.summerfarm.facade.mallManage;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.product.mall.MajorPriceCommandProvider;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/4/10 16:10
 */
@Slf4j
@Component
public class MajorPriceFacade {

    @DubboReference(timeout = 10000)
    private MajorPriceCommandProvider majorPriceCommandProvider;

    public void newLowPriceRemainder(Integer adminId, Integer areaNo, String sku){
        DubboResponse<Void> response = majorPriceCommandProvider.newLowPriceRemainder(adminId, areaNo, sku);
        if (response.isSuccess()) {
            return;
        }
        throw new ProviderException(response.getMsg());
    }

}
