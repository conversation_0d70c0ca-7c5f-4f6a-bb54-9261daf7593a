package net.summerfarm.facade.process.convert;

import com.google.common.collect.Lists;
import net.summerfarm.common.client.enums.ProcessEnum;
import net.summerfarm.common.client.req.process.ProcessCreateInstanceRequest;
import net.summerfarm.common.client.req.process.ProcessFormRequest;
import net.summerfarm.common.client.req.process.ProcessNodeRequest;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.dingding.bo.ProcessInstanceCreateBO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/27 15:27
 */
public class ProcessConvert {

    public static ProcessCreateInstanceRequest convert(ProcessInstanceCreateBO createBO) {
        ProcessCreateInstanceRequest request = new ProcessCreateInstanceRequest();
        request.setProcessType(createBO.getBizTypeEnum().getBizType());
        request.setBizId(createBO.getBizId());
        request.setAdminId(createBO.getAdminId().toString());

        if (CollectionUtils.isNotEmpty(createBO.getDingdingForms())) {
            request.setFormList(createBO.getDingdingForms().stream().map(data -> {
                ProcessFormRequest formRequest = new ProcessFormRequest();
                formRequest.setFormName(data.getFormName());
                formRequest.setFormValue(data.getFormValue());

                return formRequest;
            }).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(createBO.getCcList())) {
            String ccList = createBO.getCcList();
            List<String> asList = Arrays.asList(ccList.split(","));
            ProcessNodeRequest nodeRequest = new ProcessNodeRequest();
            nodeRequest.setAdminIdList(asList);
            request.setCcAdminList(Lists.newArrayList(nodeRequest));
        }
        if (StringUtils.isNotBlank(createBO.getApprovers())) {
            List<String> strings = Arrays.asList(createBO.getApprovers().split(","));
            ProcessNodeRequest nodeRequest = new ProcessNodeRequest();
            nodeRequest.setAdminIdList(strings);
            request.setApproversAdminList(Lists.newArrayList(nodeRequest));
        }
        request.setProcessAccountType(ProcessEnum.ProcessAccountEnum.XIAN_MU.name());

        return request;
    }
}
