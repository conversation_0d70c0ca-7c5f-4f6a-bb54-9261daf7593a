package net.summerfarm.facade;

import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.manage.client.tenant.resp.TenantInfoResp;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.CommonException;
import net.summerfarm.model.DTO.SaasTokenInfoDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static net.summerfarm.contexts.ResultConstant.LOGIN_FIRST;

/**
 * <AUTHOR>
 * @date 2023/3/8  18:41
 */
@Component
@Slf4j
public class SaasTenantFacade {
    @DubboReference
    private TenantProvider tenantProvider;

    public SaasTokenInfoDTO getXmTmpToken(String saasToken){
        DubboResponse<TenantInfoResp> response = tenantProvider.getTenantInfo(saasToken);
        TenantInfoResp data = response.getData();
        if (data == null) {
            throw new CommonException(LOGIN_FIRST, "token is error");
        }
        return SaasTokenInfoDTO.builder()
                .adminId(Optional.ofNullable(data.getAdminId()).orElse(null))
                .phone(data.getPhone())
                .tenantName(data.getName())
                .tenantId(data.getTenantId())
                .tenantName(data.getName())
                .tenantAccountName(data.getTenantAccountName())
                .tenantAccountId(data.getTenantAccountId()).build();
    }

    /**
     * 根据门店ID集合查询配送面单是否打印价格 true-打印 false-关闭 默认true
     * @param storeIdList 门店集合ID
     * @return 结果
     */
    public Map<Long, Boolean> queryDeliveryNotePrintPriceSwitch(List<Long> storeIdList) {
        if(CollectionUtils.isEmpty(storeIdList)){
            return Collections.emptyMap();
        }
        DubboResponse<Map<Long, Boolean>> response = tenantProvider.queryDeliveryNotePrintPriceSwitch(storeIdList);
        if (!response.isSuccess()) {
            log.error("invoke tenantProvider.queryDeliveryNotePrintPriceSwitch fail request:{}, msg:{}", JSONUtil.toJsonStr(storeIdList), response.getMsg(),new BizException("根据门店ID集合查询配送面单是否打印价格 ，" + response.getMsg()));
            return Collections.emptyMap();
        }
        if(response.getData() == null){
            return Collections.emptyMap();
        }
        return response.getData();
    }

}
