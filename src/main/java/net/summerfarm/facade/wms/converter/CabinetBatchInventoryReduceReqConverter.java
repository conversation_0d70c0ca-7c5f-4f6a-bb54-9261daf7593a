package net.summerfarm.facade.wms.converter;

import net.summerfarm.model.DTO.req.cabinetInventory.CabinetBatchInventoryReduceDetailReq;
import net.summerfarm.model.DTO.req.cabinetInventory.CabinetBatchInventoryReduceReq;
import net.summerfarm.model.DTO.res.cabinetInventory.CabinetBatchInventoryReduceResp;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetBatchInventoryReduceDetailReqDTO;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetBatchInventoryReduceReqDTO;
import net.summerfarm.wms.inventory.resp.cabinetInventory.CabinetBatchInventoryReduceRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CabinetBatchInventoryReduceReqConverter {

    CabinetBatchInventoryReduceReqConverter INSTANCE = Mappers.getMapper(CabinetBatchInventoryReduceReqConverter.class);

    CabinetBatchInventoryReduceReq convert(CabinetBatchInventoryReduceReqDTO req);

    CabinetBatchInventoryReduceDetailReq convertDetail(CabinetBatchInventoryReduceDetailReqDTO req);

    List<CabinetBatchInventoryReduceDetailReq> convertDetailList(List<CabinetBatchInventoryReduceDetailReqDTO> req);

    CabinetBatchInventoryReduceRespDTO convertResp(CabinetBatchInventoryReduceResp req);

}
