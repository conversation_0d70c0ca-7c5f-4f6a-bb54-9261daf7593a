package net.summerfarm.facade.wms.converter;

import net.summerfarm.facade.wms.dto.CabinetInventoryRecommendDTO;
import net.summerfarm.facade.wms.dto.CabinetInventoryRecommendOutFacadeRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/6
 */
@Mapper
public interface CabinetInventoryRecommendConverter {

    CabinetInventoryRecommendConverter INSTANCE = Mappers.getMapper(CabinetInventoryRecommendConverter.class);

    CabinetInventoryRecommendDTO convert(CabinetInventoryRecommendOutFacadeRespDTO cabinetInventoryRecommendOutFacadeRespDTO);

}
