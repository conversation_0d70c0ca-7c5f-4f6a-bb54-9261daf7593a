package net.summerfarm.facade.wms.converter;

import net.summerfarm.common.util.DateUtil;
import net.summerfarm.facade.wms.dto.*;
import net.summerfarm.model.domain.cabinetInventory.CabinetBatchInventory;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetBatchQueryReqDTO;
import net.summerfarm.wms.inventory.resp.cabinetInventory.CabinetBatchInventoryAddReqDTO;
import net.summerfarm.wms.inventory.resp.cabinetInventory.CabinetBatchQueryResDTO;
import net.summerfarm.wms.inventory.resp.cabinetInventory.CabinetInventoryRecommendOutRespDTO;
import net.summerfarm.wms.inventory.resp.cabinetInventory.CabinetInventorySingleRecommendOutReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CabinetBatchInventoryConverter {
    CabinetBatchInventoryConverter INSTANCE = Mappers.getMapper(CabinetBatchInventoryConverter.class);

    CabinetBatchQueryReqDTO convert(CabinetBatchQueryReq req);

    @Mappings({
            @Mapping(source = "cabinetCode" , target = "cabinetNo"),
            @Mapping(target = "produceDate", expression = "java(net.summerfarm.common.util.DateUtil.toLocalDate(req.getProduceDate()))"),
            @Mapping(target = "qualityDate", expression = "java(net.summerfarm.common.util.DateUtil.toLocalDate(req.getQualityDate()))")
    })
    CabinetBatchQueryRes convert(CabinetBatchInventory req);

    CabinetBatchInventoryAddReqDTO convert(CabinetBatchInventoryAddFacadeReq req);

    CabinetInventorySingleRecommendOutReqDTO convert(CabinetInventorySingleRecommendOutReqFacadeDTO dto);

    CabinetInventoryRecommendOutFacadeRespDTO convert(CabinetInventoryRecommendOutRespDTO dto);
}
