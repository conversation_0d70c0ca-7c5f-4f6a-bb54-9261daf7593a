package net.summerfarm.facade.wms.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SafeInventoryWarnDTO {

    /**
     * 仓库编码
     */
    private Integer warehouseNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 锁定数量
     */
    private Integer lockQuantity;

    /**
     * 锁定时间
     */
    private Date lockTime;

    /**
     * 锁定原因
     */
    private String lockReason;

    /**
     * 释放数量
     */
    private Integer releaseQuantity;

    /**
     * 释放原因
     */
    private String releaseReason;

    /**
     * 创建人
     */
    private String createOperator;

    /**
     * 创建人id
     */
    private Long createOperatorId;

    /**
     * 更新人
     */
    private String updateOperator;

    /**
     * 更新人id
     */
    private Long updateOperatorId;

}
