package net.summerfarm.facade.mall;

import net.summerfarm.mall.client.provider.MerchantProvider;
import net.summerfarm.mall.client.req.merchant.SubAccountQueryReq;
import net.summerfarm.mall.client.req.merchant.TagManageReq;
import net.summerfarm.mall.client.resp.MerchantSubAccountResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class MerchantSubFacade {

    @DubboReference
    private MerchantProvider merchantProvider;


    /**
     * 公众号打标
     *
     * @return {@link Integer}
     */
    public Integer merchantSignTags(TagManageReq tagManageReq){
        DubboResponse<Integer> dubboResponse = merchantProvider.merchantSignTags(tagManageReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

    /**
     * 查询账号详细信息
     *
     * @return {@link MerchantSubAccountResp}
     */
    public MerchantSubAccountResp subAccountInfo(SubAccountQueryReq subAccountQueryReq){
        DubboResponse<MerchantSubAccountResp> dubboResponse = merchantProvider.subAccountInfo(subAccountQueryReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }




}
