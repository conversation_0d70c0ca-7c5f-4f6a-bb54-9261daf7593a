package net.summerfarm.facade.mall;

import net.summerfarm.facade.mall.converter.AfterSaleOrderConverter;
import net.summerfarm.mall.client.provider.AfterSaleOrderProvider;
import net.summerfarm.mall.client.req.afterSale.AfterSaleOrderReq;
import net.summerfarm.mall.client.req.afterSale.CalcAfterSaleCouponReq;
import net.summerfarm.mall.client.req.afterSale.DeliveryStatusQueryReq;
import net.summerfarm.mall.client.req.afterSale.HandleTypeQueryReq;
import net.summerfarm.mall.client.resp.ExecutableAfterSaleResp;
import net.summerfarm.model.vo.AfterSaleOrderVO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class AfterSaleFacade {

    @DubboReference
    private AfterSaleOrderProvider afterSaleOrderProvider;

    /**
     * 售后审核
     *
     * @return {@link Boolean}
     */
    public Boolean afterSaleHandle(AfterSaleOrderVO afterSaleOrderVO){
        AfterSaleOrderReq afterSaleOrderReq = AfterSaleOrderConverter.afterSaleOrderVO2Req(afterSaleOrderVO);
        DubboResponse<Boolean> dubboResponse = afterSaleOrderProvider.afterSaleHandle(afterSaleOrderReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new BizException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

    /**
     * 获取已到货/未到货售后
     *
     * @return {@link ExecutableAfterSaleResp}
     */
    public ExecutableAfterSaleResp afterSaleDeliveryStatus(DeliveryStatusQueryReq deliveryStatusQueryReq){
        DubboResponse<ExecutableAfterSaleResp> dubboResponse = afterSaleOrderProvider.afterSaleDeliveryStatus(deliveryStatusQueryReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new BizException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

    /**
     * 获取售后服务类型
     *
     * @return {@link ExecutableAfterSaleResp}
     */
    public ExecutableAfterSaleResp afterSaleGetHandleType(HandleTypeQueryReq handleTypeQueryReq){
        DubboResponse<ExecutableAfterSaleResp> dubboResponse = afterSaleOrderProvider.afterSaleGetHandleType(handleTypeQueryReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new BizException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

    /**
     * 获取最大售后数量
     *
     * @return {@link Integer}
     */
    public Integer afterSaleManageCalculateQuantity(AfterSaleOrderVO afterSaleOrderVO){
        AfterSaleOrderReq afterSaleOrderReq = AfterSaleOrderConverter.afterSaleOrderVO2Req(afterSaleOrderVO);
        DubboResponse<Integer> dubboResponse = afterSaleOrderProvider.afterSaleManageCalculateQuantity(afterSaleOrderReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new BizException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

    /**
     * 获取最大售后金额
     *
     * @return {@link BigDecimal}
     */
    public BigDecimal afterSaleManageAfterSaleMoney(AfterSaleOrderVO afterSaleOrderVO){
        AfterSaleOrderReq afterSaleOrderReq = AfterSaleOrderConverter.afterSaleOrderVO2Req(afterSaleOrderVO);
        DubboResponse<BigDecimal> dubboResponse = afterSaleOrderProvider.afterSaleManageAfterSaleMoney(afterSaleOrderReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new BizException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

    /**
     * 批量生成售后单
     *
     * @return {@link Boolean}
     */
    public Boolean afterSaleBatchSave(List<AfterSaleOrderVO> afterSaleOrderVOList){
        List<AfterSaleOrderReq> afterSaleOrderReqList = AfterSaleOrderConverter.afterSaleOrderVO2List(afterSaleOrderVOList);
        DubboResponse<Boolean> dubboResponse = afterSaleOrderProvider.afterSaleBatchSave(afterSaleOrderReqList);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new BizException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

    /**
     * 商城审批
     *
     * @return {@link Boolean}
     */
    public Boolean afterSaleAudit(String afterSaleOrderNo,int status, String auditRemark,String adminName){
        AfterSaleOrderReq afterSaleOrderReq = new AfterSaleOrderReq();
        afterSaleOrderReq.setAfterSaleOrderNo(afterSaleOrderNo);
        afterSaleOrderReq.setStatus(status);
        afterSaleOrderReq.setAuditeRemark(auditRemark);
        afterSaleOrderReq.setAuditer(adminName);
        DubboResponse<Boolean> dubboResponse = afterSaleOrderProvider.afterSaleAudit(afterSaleOrderReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new BizException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

    /**
     * 后台提交售后
     *
     * @return {@link Boolean}
     */
    public Boolean afterSaleManageSave(AfterSaleOrderVO afterSaleOrderVO){
        AfterSaleOrderReq afterSaleOrderReq = AfterSaleOrderConverter.afterSaleOrderVO2Req(afterSaleOrderVO);
        DubboResponse<Boolean> dubboResponse = afterSaleOrderProvider.afterSaleManageSave(afterSaleOrderReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new BizException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

    /**
     * 计算售后优惠券
     *
     * @return {@link Map}
     */
    public Map<String, String> afterSaleCoupon(CalcAfterSaleCouponReq calcAfterSaleCouponReq){
        DubboResponse<Map<String, String>> dubboResponse = afterSaleOrderProvider.afterSaleCoupon(calcAfterSaleCouponReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new BizException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }


}
