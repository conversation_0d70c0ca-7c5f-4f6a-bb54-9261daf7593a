package net.summerfarm.facade.wnc;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseLogisticsQueryProvider;
import net.summerfarm.wnc.client.resp.WarehousLogisticsCenterResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 城配仓查询<br/>
 * date: 2024/6/18 10:30<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class WarehouseLogisticsQueryFacede {

    @DubboReference
    private WarehouseLogisticsQueryProvider warehouseLogisticsQueryProvider;


    /**
     * 查询所有的POP城配仓
     * @return 城配仓编号集合
     */
    public List<Integer> queryPopWarehouseLogisticsList() {
        DubboResponse<List<WarehousLogisticsCenterResp>> response = warehouseLogisticsQueryProvider.queryPopWarehouseLogisticsList();
        if (Objects.isNull(response) || !DubboResponse.COMMON_SUCCESS_CODE.equals(response.getCode())){
            log.error("WarehouseLogisticsQueryFacede[]queryPopWarehouseLogisticsList[]error cause:{}", JSON.toJSONString(response));
            throw new BizException(response.getMsg());
        }
        List<WarehousLogisticsCenterResp> data = response.getData();
        if(CollectionUtils.isEmpty(data)){
            return Collections.emptyList();
        }
        return data.stream().map(WarehousLogisticsCenterResp::getStoreNo).collect(Collectors.toList());
    }
}
