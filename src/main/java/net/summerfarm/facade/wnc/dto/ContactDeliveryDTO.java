package net.summerfarm.facade.wnc.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description
 * @date 2023/11/15 15:05:02
 */
@Data
public class ContactDeliveryDTO implements Serializable {

    /**
     * 地址ID
     */
    private Long contactId;

    /**
     * 周期方案 1周计算 2间隔计算
     */
    private Integer frequentMethod;

    /**
     * 周的配送周期 0每天 1周一 依次,多个逗号分隔
     */
    private String weekDeliveryFrequent;

    /**
     * 配送间隔周期
     */
    private Integer deliveryFrequentInterval;

    /**
     * 开始计算日期
     */
    private LocalDate beginCalculateDate;
}
