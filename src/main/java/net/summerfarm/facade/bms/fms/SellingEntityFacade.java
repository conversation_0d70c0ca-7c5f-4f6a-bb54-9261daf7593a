package net.summerfarm.facade.bms.fms;

import net.summerfarm.biz.finance.convert.FinancialInvoiceSellerInfoConverter;
import net.summerfarm.biz.finance.dto.FinancialInvoiceSellerInfoDTO;
import net.xianmu.bms.client.provider.sellingEntity.SellingEntityQueryProvider;
import net.xianmu.bms.client.req.sellingEntity.SellingEntityQueryReq;
import net.xianmu.bms.client.resp.sellingEntity.SellingEntityDetailInfoResp;
import net.xianmu.bms.client.resp.sellingEntity.SellingEntityInfoResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: George
 * @date: 2025-03-18
 **/
@Service
public class SellingEntityFacade {

    @DubboReference
    private SellingEntityQueryProvider sellingEntityQueryProvider;

    public FinancialInvoiceSellerInfoDTO querySellingEntity(String sellingEntityName) {
        List<FinancialInvoiceSellerInfoDTO> list = querySellingEntityList(Collections.singleton(sellingEntityName));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 查询销售主体
     * @param sellingEntityNames
     * @return
     */
    public List<FinancialInvoiceSellerInfoDTO> querySellingEntityList(Set<String> sellingEntityNames) {
        SellingEntityQueryReq req = new SellingEntityQueryReq();
        req.setSellingEntityNameList(sellingEntityNames);
        DubboResponse<List<SellingEntityDetailInfoResp>> response = sellingEntityQueryProvider.querySellingEntity(req);
        if(!response.isSuccess()){
            throw new ProviderException("查询销售主体失败，错误信息：" + response.getMsg());
        }
        List<SellingEntityDetailInfoResp> list = response.getData();
        return list.stream().map(FinancialInvoiceSellerInfoConverter::convertDetail).collect(Collectors.toList());
    }

    public FinancialInvoiceSellerInfoDTO queryDefaultSellingEntity() {
        DubboResponse<SellingEntityInfoResp> response = sellingEntityQueryProvider.queryDefaultSellingEntity();
        if(!response.isSuccess()){
            throw new ProviderException("查询默认销售主体失败，错误信息：" + response.getMsg());
        }
        return FinancialInvoiceSellerInfoConverter.convert(response.getData());
    }
}
