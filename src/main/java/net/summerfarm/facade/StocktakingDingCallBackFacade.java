package net.summerfarm.facade;

import net.summerfarm.wms.stocktaking.StocktakingDingCallBackProvider;
import net.summerfarm.wms.stocktaking.dto.StocktakingCallBackDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Component
public class StocktakingDingCallBackFacade {

    @DubboReference
    private StocktakingDingCallBackProvider stocktakingDingCallBackProvider;

    public void agree(StocktakingCallBackDTO stocktakingCallBackDTO) {
        stocktakingDingCallBackProvider.agree(stocktakingCallBackDTO);
    }

    public void terminate(StocktakingCallBackDTO stocktakingCallBackDTO) {
        stocktakingDingCallBackProvider.terminate(stocktakingCallBackDTO);
    }

    public void refuse(StocktakingCallBackDTO stocktakingCallBackDTO) {
        stocktakingDingCallBackProvider.refuse(stocktakingCallBackDTO);
    }
}
