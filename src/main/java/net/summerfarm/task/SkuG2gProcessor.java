package net.summerfarm.task;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.service.ProductsPropertyValueService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class SkuG2gProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private ProductsPropertyValueService service;

    @Resource
    private ProductsPropertyValueMapper mapper;

    @Resource
    private InventoryMapper inventoryMapper;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        boolean hasNext = true;
        PageInfo<String> page;
        while (hasNext) {
            page = service.queryContainsGSku(1,100);
            List<String> skus = page.getList ();
            if(CollectionUtil.isNotEmpty (skus)){
                try {
                    mapper.updateG2g (skus);
                    inventoryMapper.updateG2g (skus);
                }catch (Exception e){
                    log.warn ("【系统G2g 】执行失败,sku:{}",JSON.toJSONString (skus), e);
                }
            }
            hasNext = page.isHasNextPage ();
            try {
                Thread.sleep (1000 * 2 * 60);//2分钟 执行100个 ,防止dts监听 爆炸
            } catch (InterruptedException e) {
                throw new RuntimeException (e);
            }
            log.info ("SkuG2gProcessor - list.size={}", page.getTotal ());
        }

        return new ProcessResult(true);
    }
}
