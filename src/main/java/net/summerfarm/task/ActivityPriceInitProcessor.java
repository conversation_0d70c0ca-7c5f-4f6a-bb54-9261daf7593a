package net.summerfarm.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.DateUtil;
import net.summerfarm.common.util.SpringContextUtil;
import net.summerfarm.mapper.manage.ActivityBasicInfoMapper;
import net.summerfarm.mapper.manage.ActivitySkuDetailMapper;
import net.summerfarm.mapper.manage.ActivitySkuPriceMapper;
import net.summerfarm.model.DTO.market.ActivityLadderConfigDTO;
import net.summerfarm.model.DTO.market.ActivityLadderPriceDTO;
import net.summerfarm.model.domain.market.ActivitySkuDetail;
import net.summerfarm.model.domain.market.ActivitySkuPrice;
import net.summerfarm.service.ActivityNewService;
import net.summerfarm.service.MajorPriceService;
import net.xianmu.redis.support.util.XmRedisUtils;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 活动初始化
 *
 * @author: <EMAIL>
 * @create: 2023/2/1
 */
@Slf4j
@Component
public class ActivityPriceInitProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private MajorPriceService majorPriceService;
    @Resource
    private ActivityNewService activityNewService;
    @Resource
    private ActivityBasicInfoMapper activityBasicInfoMapper;
    @Resource
    private ActivitySkuDetailMapper activitySkuDetailMapper;
    @Resource
    private ActivitySkuPriceMapper activitySkuPriceMapper;

    private static final String DATA_TYPE_APPOINT = "appoint";
    private static final String DATA_TYPE_VALID = "valid";
    private static final String DATA_TYPE_INVALID = "invalid";


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        String instanceParameters = context.getInstanceParameters();
        if (StringUtils.isBlank(instanceParameters)) {
            log.error("任务参数为空!");
            return new ProcessResult(true);
        }
        JSONObject map = JSON.parseObject(instanceParameters);
        String dataType = map.getString("dataType");
        Integer batchSize = map.getInteger("batchSize") == null ? 1 : map.getInteger("batchSize");
        try {
            log.info("[初始化活动阶梯]任务开始执行, context: 【{}】", JSON.toJSONString(context));

            List<Long> basicIdList = this.getBasicIdList(dataType, map);

            log.info("待处理的basicIdList:{}", JSON.toJSONString(basicIdList));

            if (CollUtil.isEmpty(basicIdList)) {
                log.info("[初始化活动阶梯]暂无需要处理的活动!!");
                return new ProcessResult(true);
            }

            this.handle(basicIdList, batchSize);
            log.info("[初始化活动阶梯]]执行完成!!");
        } catch (Exception e) {
            log.error("[初始化活动阶梯]]执行失败!", e);
        }
        return new ProcessResult(true);
    }

    private List<Long> getBasicIdList(String dataType, JSONObject map) {
        List<Long> basicIdList = new ArrayList<>();
        if (DATA_TYPE_VALID.equals(dataType)) {
            log.info("开始初始化生效的活动");
            basicIdList = activityBasicInfoMapper.listValidInitDataByNowTime();
        } else if (DATA_TYPE_INVALID.equals(dataType)) {
            log.info("开始初始化失效的活动");
            String startDate = map.getString("startDate");
            String endDate = map.getString("endDate");
            basicIdList = activityBasicInfoMapper.listInValidInitDataByStartDate(DateUtil.parseDtsDate(startDate), DateUtil.parseDtsDate(endDate));
        } else if (DATA_TYPE_APPOINT.equals(dataType)) {
            log.info("开始初始化指定的活动");
            basicIdList = JSON.parseArray(map.getString("idList"), Long.class);
        } else {
            log.warn("未知类型！");
        }
        return basicIdList;
    }

    private void handle(List<Long> basicIdList, Integer batchSize) {
        if (CollUtil.isEmpty(basicIdList)) {
            return;
        }

        ActivityPriceInitProcessor bean = SpringContextUtil.getBean("activityPriceInitProcessor", ActivityPriceInitProcessor.class);
        // 单个活动id下的 sku数最大为400.映射到价格维度，最大数量为20000左右
        List<List<Long>> split = ListUtil.split(basicIdList, batchSize);
        split.forEach(list -> {
            try {
                bean.handleInner(list);
            } catch (Exception e) {
                log.error("[初始化活动阶梯]执行失败!, 当前批次:basicIdList:{}", JSON.toJSONString(list), e);
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleInner(List<Long> basicIdList) {
        if (CollUtil.isEmpty(basicIdList)) {
            return;
        }
        // 处理sku_detail 配置
        List<ActivitySkuDetail> activitySkuDetails = activitySkuDetailMapper.listAllByBasicInfoIds(basicIdList);
        log.info("当前批次待处理的活动数据, size:{}", activitySkuDetails.size());
        if (CollUtil.isNotEmpty(activitySkuDetails)) {
            List<ActivitySkuDetail> updateList = new ArrayList<>();
            List<List<ActivitySkuDetail>> split = ListUtil.split(activitySkuDetails, 100);
            for (List<ActivitySkuDetail> skuDetails : split) {
                for (ActivitySkuDetail skuDetail : skuDetails) {
                    if (skuDetail.getLadderConfig() != null) {
                        log.info("当前配置已处理,无需再次处理, skuDetail:{}", JSON.toJSONString(skuDetail));
                    } else {
                        skuDetail.setLadderConfig(ActivityLadderConfigDTO.initDefaultLadderConfig(skuDetail.getAmount(), skuDetail.getRoundingMode(), skuDetail.getAdjustType()));
                        updateList.add(skuDetail);
                    }
                }
                if(CollUtil.isNotEmpty(updateList)) {
                    activitySkuDetailMapper.updateBatch(updateList);
                }
                updateList.clear();
            }
        }
        log.info("当前批次活动配置处理完毕");

        // 处理价格
        List<ActivitySkuPrice> activitySkuPrices = activitySkuPriceMapper.selectByBasicIds(basicIdList);
        log.info("当前批次待处理的价格数据, size:{}", activitySkuPrices.size());

        if (CollUtil.isNotEmpty(activitySkuPrices)) {
            List<ActivitySkuPrice> updateList = new ArrayList<>();
            List<List<ActivitySkuPrice>> split = ListUtil.split(activitySkuPrices, 100);
            for (List<ActivitySkuPrice> prices : split) {
                for (ActivitySkuPrice price : prices) {
                    if (price.getLadderPrice() != null) {
                        log.info("当前配置已处理,无需再次处理, skuDetail:{}", JSON.toJSONString(price));
                    } else {
                        price.setLadderPrice(ActivityLadderPriceDTO.initDefaultLadderPrice(price.getActivityPrice()));
                        updateList.add(price);
                    }
                }
                if(CollUtil.isNotEmpty(updateList)) {
                    activitySkuPriceMapper.updateLadderPriceBatch(updateList);
                }
                updateList.clear();
            }
        }
    }

}
