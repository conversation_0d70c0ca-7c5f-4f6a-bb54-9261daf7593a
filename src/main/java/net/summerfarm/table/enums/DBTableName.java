package net.summerfarm.table.enums;

/**
 * <AUTHOR>
 * @Description: binlog监听表名，测试环境需要配置canal，生成环境需要修改dts监听表
 */
public class DBTableName {
    /**
     * 商品表
     */
    public static final String PRODUCTS = "products";
    /**
     * 购物车表
     */
    public static final String TROLLEY = "trolley";
    /**
     * 订单明细表
     */
    public static final String ORDER_ITEM = "order_item";

    /**
     * 商品表
     */
    public static final String STOCK_TASK_PICK = "stock_task_pick";

    /**
     * 城市实际库存表
     */
    public static final String AREA_STORE = "area_store";

    /**
     * 直发采购业务用户充值审核表
     */
    public static final String DIRECT_PURCHASE_RECHARGE = "direct_purchase_recharge";

    /**
     * 财务审核开票记录表
     */
    public static final String  FINANCIAL_INVOICE = "financial_invoice";

    /**
     * 样品申请表
     */
    public static final String  SAMPLE_APPLY = "sample_apply";

    /**
     * 拜访记录表
     */
    public static final String  FOLLOW_UP_RECORD = "follow_up_record";

    /**
     * 拜访计划表
     */
    public static final String  VISIT_PLAN = "visit_plan";

    /**
     * 商品SKU表
     */
    public static final String  INVENTORY = "inventory";

    /**
     * 任务执行日志表
     */
    public static final String  SCHEDULE_TASK_LOG = "schedule_task_log";

    /**
     * 收款单表
     */
    public static final String  FINANCE_RECEIPT = "finance_receipt";

    /**
     * 账期订单表
     */
    public static final String  FINANCE_ACCOUNTING_PERIOD_ORDER = "finance_accounting_period_order";

    /**
     * 门店表
     */
    public static final String  MERCHANT = "merchant";

    /**
     * 切仓记录表
     */
    public static final String  CHANGE_FENCE = "change_fence";

    /**
     * 库存变动表
     */
    public static final String STORE_RECORD = "store_record";

    /**
     * 订单表
     */
    public static final String ORDERS = "orders";

    /**
     * 出入库任务明细表
     */
    public static final String  STOCK_TASK_ITEM = "stock_task_item";

    /**
     * 采购退货明细表
     */
    public static final String  PURCHASES_BACK_DETAIL = "purchases_back_detail";

    /**
     * 预付配置表
     */
    public static final String  PREPAY_INVENTORY = "prepay_inventory";

    /**
     * 优惠券发放记录表
     */
    public static final String  MARKET_COUPON_SEND_DETAIL = "market_coupon_send_detail";

    /**
     * 运营区域商品表
     */
    public static final String AREA_SKU = "area_sku";

    /**
     * 异常订单处理表
     */
    public static final String ORDER_ABNORMAL = "order_abnormal";

    /**
     * 联系地址表
     */
    public static final String CONTACT = "contact";

    /**
     * 销售与商户关系
     */
    public static final String FOLLOW_UP_RELATION = "follow_up_relation";

    /**
     * 白名单
     */
    public static final String MERCHANT_FOLLOW_WHITE_LIST = "merchant_follow_white_list";

    /**
     * 运营区域复制详情表
     */
    public static final String AREA_COPY_DETAIL = "area_copy_detail";

    /**
     * 品牌表
     */
    public static final String BRAND = "brand";

    /**
     * 毛利率配置表
     */
    public static final String INTEREST_RATE_CONFIG = "interest_rate_config";

    /**
     * 商品属性值表
     */
    public static final String PRODUCTS_PROPERTY_VALUE = "products_property_value";

    /**
     * sku库存仓映射表
     */
    public static final String WAREHOUSE_INVENTORY_MAPPING = "warehouse_inventory_mapping";

    /**
     * 库存数量变更记录表
     */
    public static final String QUANTITY_CHANGE_RECORD = "quantity_change_record";

    /**
     * 售后明细表
     */
    public static final String AFTER_SALE_ORDER = "after_sale_order";

    /**
     * 退款表
     */
    public static final String REFUND = "refund";


    /**
     * 退款表
     */
    public static final String ADMIN = "admin";



    /**
     * 用户基础
     */
    public static final String USER_BASE = "user_base";


    /**
     * 用户扩展
     */
    public static final String USER_PROPERTIES_EXT = "user_properties_ext";


    /**
     * admin 用户扩展
     */
    public static final String ADMIN_AUTH_EXTEND = "admin_auth_extend";

    /**
     * admin 用户扩展
     */
    public static final String USER_AUTH = "user_auth";


    /**
     * 活动价格表
     */
    public static final String ACTIVITY_SKU_PRICE = "activity_sku_price";


    /**
     *
     */
    public static final String MAJOR_PRICE = "major_price";

    /**
     * 配置表
     */
    public static final String CONFIG = "config";

    /**
     * 配送计划
     */
    public static final String DELIVERY_PLAN = "delivery_plan";
}
