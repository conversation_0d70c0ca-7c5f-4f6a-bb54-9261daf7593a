package net.summerfarm.table.item.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.ItemProductInfoUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.mapper.manage.ProductsPropertyMapper;
import net.summerfarm.mapper.manage.ProductsPropertyValueMapper;
import net.summerfarm.model.domain.ProductsProperty;
import net.summerfarm.service.item.ProductSyncToItemService;
import net.summerfarm.table.enums.DBTableName;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-04-28
 * @description 属性变化同步到货品
 */
@Slf4j
@Service
public class ProductPropertyValueIDbDmlImpl extends AbstractDbDml {
    @Resource
    private RedisTemplate redisTemplate;
    private static final String BACKGROUND_CATEGORY = "background_category";

    @Resource
    private ProductSyncToItemService productSyncToItemService;

    @Resource
    private ProductsPropertyMapper productsPropertyMapper;

    @Resource
    private ProductsPropertyValueMapper productsPropertyVauleMapper;
    @Override
    public String getTableDmlName() {
        return DBTableName.PRODUCTS_PROPERTY_VALUE;
    }

    @Override
    protected void syncInsertItemHandle(Map<String, String> data) {
        Long propertyId = Long.valueOf(data.get("products_property_id"));
        if (Global.BRAND.equals (propertyId)) {
            updateBrandList(data.get ("products_property_value"));
        }
    }
    @Override
    protected void syncUpdateItemHandle(Map<String, String> oldData, Map<String, String> data) {
        Long propertyId = Long.valueOf(data.get("products_property_id"));
        if (Global.BRAND.equals (propertyId)) {
            updateBrandList(data.get ("products_property_value"));
        }
        if (!ItemProductInfoUtil.PROPERTIES_ID_LIST.contains(propertyId)) {
           return;
        }
        String pdId = data.get("pd_id");
        if (StringUtils.isNotBlank(pdId)) {
            productSyncToItemService.propertyUpdateSyncToSpu(Long.valueOf(pdId));
        }
    }

    @Override
    protected void syncInsertItemRetryHandle(Map<String, String> data) {

    }
    @Override
    protected void syncUpdateItemRetryHandle(Map<String, String> oldData, Map<String, String> data) {
        this.syncUpdateItemHandle(oldData,data);
    }

    /**
     * 更新品牌列表
     * <p>当品牌属性发生变化时，将新品牌添加到品牌列表中，并清除相关缓存</p>
     *
     * @param brandName 品牌名称，不能为空
     */
    private void updateBrandList(String brandName) {
        // 参数校验
        if (StringUtils.isBlank(brandName)) {
            log.warn("Brand name is blank, skip updating brand list");
            return;
        }

        try {
            // 获取品牌属性配置
            ProductsProperty productsProperty = productsPropertyMapper.selectByPrimaryKey(Global.BRAND.intValue());
            if (productsProperty == null) {
                log.warn("Brand property configuration not found, skip updating brand list");
                return;
            }

            String formatStr = productsProperty.getFormatStr();

            // 处理已有格式化字符串的情况
            if (StringUtils.isNotBlank(formatStr)) {
                handleExistingFormatStr(productsProperty, formatStr, brandName);
            } else {
                // 处理格式化字符串为空的情况
                handleEmptyFormatStr(productsProperty, brandName);
            }

        } catch (Exception e) {
            log.error("Failed to update brand list for brand: {}", brandName, e);
        }
    }

    /**
     * 处理已有格式化字符串的情况
     */
    private void handleExistingFormatStr(ProductsProperty productsProperty, String formatStr, String brandName) {
        try {
            List<String> existingBrands = JSON.parseArray(formatStr, String.class);
            if (existingBrands == null) {
                log.warn("Failed to parse existing brand list from format string: {}", formatStr);
                return;
            }

            // 检查品牌是否已存在
            if (existingBrands.contains(brandName)) {
                log.debug("Brand '{}' already exists in format string, skip adding", brandName);
                return;
            }

            // 从数据库获取完整品牌列表并添加新品牌
            updateBrandPropertyWithNewBrand(productsProperty, brandName);

        } catch (Exception e) {
            log.error("Error handling existing format string for brand: {}", brandName, e);
            throw e;
        }
    }

    /**
     * 处理格式化字符串为空的情况
     */
    private void handleEmptyFormatStr(ProductsProperty productsProperty, String brandName) {
        try {
            List<String> brandList = productsPropertyVauleMapper.listValueByPropertyId(Global.BRAND);
            if (brandList == null) {
                log.warn("Failed to get brand list from database");
                return;
            }

            // 检查品牌是否已存在
            if (brandList.contains(brandName)) {
                log.debug("Brand '{}' already exists in database, skip adding", brandName);
                return;
            }

            // 添加新品牌并更新
            brandList.add(brandName);
            updateBrandPropertyAndClearCache(productsProperty, brandList, brandName);

        } catch (Exception e) {
            log.error("Error handling empty format string for brand: {}", brandName, e);
            throw e;
        }
    }

    /**
     * 更新品牌属性（从数据库获取完整列表）
     */
    private void updateBrandPropertyWithNewBrand(ProductsProperty productsProperty, String brandName) {
        List<String> brandList = productsPropertyVauleMapper.listValueByPropertyId(Global.BRAND);
        if (brandList == null) {
            log.warn("Failed to get brand list from database");
            return;
        }

        brandList.add(brandName);
        updateBrandPropertyAndClearCache(productsProperty, brandList, brandName);
    }

    /**
     * 更新品牌属性并清除缓存
     */
    private void updateBrandPropertyAndClearCache(ProductsProperty productsProperty, List<String> brandList, String brandName) {
        try {
            // 更新品牌属性
            productsProperty.setFormatStr(JSON.toJSONString(brandList));
            productsPropertyMapper.updateByPrimaryKeySelective(productsProperty);

            // 清除背景分类缓存
            clearBackgroundCategoryCache();

            log.info("Successfully added new brand '{}' to list and cleared cache", brandName);

        } catch (Exception e) {
            log.error("Failed to update brand property or clear cache for brand: {}", brandName, e);
            throw e;
        }
    }

    /**
     * 清除背景分类缓存
     */
    private void clearBackgroundCategoryCache() {
        try {
            if (redisTemplate.hasKey(BACKGROUND_CATEGORY)) {
                redisTemplate.delete(BACKGROUND_CATEGORY);
                log.debug("Background category cache cleared successfully");
            }
        } catch (Exception e) {
            log.warn("Failed to clear background category cache", e);
            // 缓存清除失败不应该影响主流程，所以只记录警告
        }
    }
}
