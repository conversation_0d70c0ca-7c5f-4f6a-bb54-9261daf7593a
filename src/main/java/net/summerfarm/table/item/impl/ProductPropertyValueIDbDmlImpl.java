package net.summerfarm.table.item.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.ItemProductInfoUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.mapper.manage.ProductsPropertyMapper;
import net.summerfarm.mapper.manage.ProductsPropertyValueMapper;
import net.summerfarm.model.domain.ProductsProperty;
import net.summerfarm.service.item.ProductSyncToItemService;
import net.summerfarm.table.enums.DBTableName;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-04-28
 * @description 属性变化同步到货品
 */
@Slf4j
@Service
public class ProductPropertyValueIDbDmlImpl extends AbstractDbDml {
    @Resource
    private RedisTemplate redisTemplate;
    private static final String BACKGROUND_CATEGORY = "background_category";

    @Resource
    private ProductSyncToItemService productSyncToItemService;

    @Resource
    private ProductsPropertyMapper productsPropertyMapper;

    @Resource
    private ProductsPropertyValueMapper productsPropertyVauleMapper;
    @Override
    public String getTableDmlName() {
        return DBTableName.PRODUCTS_PROPERTY_VALUE;
    }

    @Override
    protected void syncInsertItemHandle(Map<String, String> data) {
        Long propertyId = Long.valueOf(data.get("products_property_id"));
        if (Global.BRAND.equals (propertyId)) {
            updateAndRemoveCatch (data.get ("products_property_value"));
        }
    }
    @Override
    protected void syncUpdateItemHandle(Map<String, String> oldData, Map<String, String> data) {
        Long propertyId = Long.valueOf(data.get("products_property_id"));
        if (Global.BRAND.equals (propertyId)) {
            updateAndRemoveCatch (data.get ("products_property_value"));
        }
        if (!ItemProductInfoUtil.PROPERTIES_ID_LIST.contains(propertyId)) {
           return;
        }
        String pdId = data.get("pd_id");
        if (StringUtils.isNotBlank(pdId)) {
            productSyncToItemService.propertyUpdateSyncToSpu(Long.valueOf(pdId));
        }
    }

    @Override
    protected void syncInsertItemRetryHandle(Map<String, String> data) {

    }
    @Override
    protected void syncUpdateItemRetryHandle(Map<String, String> oldData, Map<String, String> data) {
        this.syncUpdateItemHandle(oldData,data);
    }

    private void updateAndRemoveCatch(String brandName) {
        if (StringUtils.isBlank(brandName)) {
            log.warn("Brand name is blank, skip updating brand list");
            return;
        }

        try {
            ProductsProperty productsProperty = productsPropertyMapper.selectByPrimaryKey(Global.BRAND.intValue());
            if (productsProperty == null) {
                log.warn("Brand property not found, skip updating brand list");
                return;
            }

            String formatStr = productsProperty.getFormatStr();
            if (!StringUtils.isBlank(formatStr)) {
                List<String> dbList = JSON.parseArray(formatStr, String.class);
                if (!dbList.contains(brandName)) {
                    updateAndRemoveCach (productsProperty, brandName);
                }
            } else {
                updateAndRemoveCach (productsProperty, brandName);
            }

        } catch (Exception e) {
            log.error("Failed to update brand list for brand: {}", brandName, e);
        }
    }

    private void updateAndRemoveCach(ProductsProperty productsProperty, String brandName) {
        List<String> brandList = productsPropertyVauleMapper.listValueByPropertyId(Global.BRAND);
        brandList.add(brandName);
        productsProperty.setFormatStr(JSON.toJSONString(brandList));
        productsPropertyMapper.updateByPrimaryKeySelective(productsProperty);
        // 清除背景分类缓存
        if (redisTemplate.hasKey(BACKGROUND_CATEGORY)) {
            redisTemplate.delete(BACKGROUND_CATEGORY);
        }
    }

}
