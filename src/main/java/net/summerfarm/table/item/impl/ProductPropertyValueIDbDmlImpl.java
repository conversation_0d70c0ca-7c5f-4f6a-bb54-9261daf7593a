package net.summerfarm.table.item.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.ItemProductInfoUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.mapper.manage.ProductsPropertyMapper;
import net.summerfarm.model.domain.ProductsProperty;
import net.summerfarm.service.item.ProductSyncToItemService;
import net.summerfarm.table.enums.DBTableName;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 商品属性值数据库变更处理器
 * 负责处理商品属性值的增删改操作，并同步到相关系统
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023-04-28
 */
@Slf4j
@Service
public class ProductPropertyValueIDbDmlImpl extends AbstractDbDml {

    private static final String BACKGROUND_CATEGORY_CACHE_KEY = "background_category";
    private static final String PD_ID_FIELD = "pd_id";
    private static final String PROPERTY_ID_FIELD = "products_property_id";
    private static final String PROPERTY_VALUE_FIELD = "products_property_value";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ProductSyncToItemService productSyncToItemService;

    @Resource
    private ProductsPropertyMapper productsPropertyMapper;
    @Override
    public String getTableDmlName() {
        return DBTableName.PRODUCTS_PROPERTY_VALUE;
    }

    @Override
    protected void syncInsertItemHandle(Map<String, String> data) {
        Long propertyId = Long.valueOf(data.get("products_property_id"));
        if (Global.BRAND.equals (propertyId)) {
            updateBrandList(data.get ("products_property_value"));
        }
    }
    @Override
    protected void syncUpdateItemHandle(Map<String, String> oldData, Map<String, String> data) {
        Long propertyId = Long.valueOf(data.get("products_property_id"));
        if (Global.BRAND.equals (propertyId)) {
            updateBrandList(data.get ("products_property_value"));
        }
        if (!ItemProductInfoUtil.PROPERTIES_ID_LIST.contains(propertyId)) {
           return;
        }
        String pdId = data.get("pd_id");
        if (StringUtils.isNotBlank(pdId)) {
            productSyncToItemService.propertyUpdateSyncToSpu(Long.valueOf(pdId));
        }
    }

    @Override
    protected void syncInsertItemRetryHandle(Map<String, String> data) {

    }
    @Override
    protected void syncUpdateItemRetryHandle(Map<String, String> oldData, Map<String, String> data) {
        this.syncUpdateItemHandle(oldData,data);
    }

   private void updateBrandList(String brandName){
       ProductsProperty productsProperty = productsPropertyMapper.selectByPrimaryKey (Global.BRAND.intValue ());
        if (productsProperty != null) {
            String formatStr = productsProperty.getFormatStr ();
            if (StringUtils.isNotBlank (formatStr)) {
                List<String> list = JSON.parseArray (formatStr, String.class);
                if (!list.contains (brandName)) {
                    list.add (brandName);
                    productsProperty.setFormatStr (JSON.toJSONString (list));
                    productsPropertyMapper.updateByPrimaryKeySelective (productsProperty);
                    
                    if (redisTemplate.hasKey(BACKGROUND_CATEGORY)) {
                        redisTemplate.delete(BACKGROUND_CATEGORY);
                    }
                }
            }
        }
    }
}
