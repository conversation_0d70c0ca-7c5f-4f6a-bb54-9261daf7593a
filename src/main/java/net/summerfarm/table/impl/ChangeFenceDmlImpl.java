package net.summerfarm.table.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.DtsModelTypeEnum;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.AreaService;
import net.summerfarm.table.DbTableDml;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/4/22 15:08
 */
@Slf4j
@Service
public class ChangeFenceDmlImpl implements DbTableDml {

    @Resource
    private AreaService areaService;

    @Override
    public void tableDml(DtsModel dtsModel) {
        if (Objects.equals(dtsModel.getType(), DtsModelTypeEnum.INSERT.name())) {
            dtsModel.consumerData(map -> {
//                Integer id = Integer.parseInt(map.get("id"));
//                areaService.sendChangeMessage(id);
            });
        } else if (Objects.equals(dtsModel.getType(), DtsModelTypeEnum.UPDATE.name())) {
//            areaService.sendCancelMessage(dtsModel);
        }
    }
}
