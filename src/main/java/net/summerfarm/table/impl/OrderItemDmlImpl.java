package net.summerfarm.table.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.enums.DtsModelTypeEnum;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.OrderService;
import net.summerfarm.table.DbTableDml;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/4/22 14:20
 */
@Slf4j
@Service
public class OrderItemDmlImpl implements DbTableDml {

    @Resource
    private OrderService orderService;

    @Override
    public void tableDml(DtsModel dtsModel) {
        if (Objects.equals(DtsModelTypeEnum.DELETE.name(),dtsModel.getType())) {
            return;
        }
        if (Objects.equals(DtsModelTypeEnum.INSERT.name(),dtsModel.getType())) {
            orderService.saveValiditySnapshot(dtsModel);
        }
        dtsModel.consumerData(map -> {
            String amount = map.get("amount");
            int parseInt = Integer.parseInt(amount);
            if(parseInt >= NumberUtils.INTEGER_FIFTY){
                String orderNo = map.get("order_no");
                log.info("下单达到大单标准的订单号:{},数量:{}",orderNo,amount);
                orderService.sendOrderReminder(orderNo);
            }
        });
    }
}
