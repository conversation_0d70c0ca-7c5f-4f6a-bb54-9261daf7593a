package net.summerfarm.table.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.EsProductInfoUtil;
import net.summerfarm.enums.DtsModelTypeEnum;
import net.summerfarm.enums.es.XianmuAreaSkuInfoFieldEnum;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.table.DbTableDml;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class InterestRateConfigDmlImpl implements DbTableDml {


    @Override
    public void tableDml(DtsModel dtsModel) {
    }
}
