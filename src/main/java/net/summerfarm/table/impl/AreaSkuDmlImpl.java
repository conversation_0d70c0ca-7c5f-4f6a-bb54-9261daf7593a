package net.summerfarm.table.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.config.AutoMajorPriceConfig;
import net.summerfarm.enums.DtsModelTypeEnum;
import net.summerfarm.enums.InventoryExtTypeEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.AreaSkuRecord;
import net.summerfarm.model.domain.Inventory;
import net.summerfarm.model.domain.SkuPriceTaskRecord;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.MajorPriceService;
import net.summerfarm.service.PriceAdjustService;
import net.summerfarm.table.DbTableDml;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class AreaSkuDmlImpl implements DbTableDml {
    @Resource
    PriceAdjustService priceAdjustService;
    @Resource
    InventoryMapper inventoryMapper;
    @Resource
    AreaSkuRecordMapper areaSkuRecordMapper;
    @Resource
    private SkuPriceTaskRecordMapper skuPriceTaskRecordMapper;
    @Resource
    AreaMapper areaMapper;

    @NacosValue(value = "${not.update.bind.price.start.time:18:29:00}", autoRefreshed = true)
    private String startTime;

    @NacosValue(value = "${not.update.bind.price.end.time:04:30:00}", autoRefreshed = true)
    private String endTime;
    @Resource
    private MajorPriceService majorPriceService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private AutoMajorPriceConfig autoMajorPriceConfig;

    /**
     * 上架状态
     */
    public static final String ON_SALE = "1";

    @Override
    public void tableDml(DtsModel dtsModel) {
        String type = dtsModel.getType();
        if (Objects.equals(type, DtsModelTypeEnum.DELETE.name())) {
            return;
        }

        if (Objects.equals(type, DtsModelTypeEnum.INSERT.name())) {
            dtsModel.consumerData(data -> {
                handleBindSkuAutoUpdatePrice(data);
                for (Map<String, String> newData : dtsModel.getData()) {
                    saveAreaSkuRecord(newData);
                }
            });
        } else if (Objects.equals(type, DtsModelTypeEnum.UPDATE.name())) {
            List<Map<String, String>> data = dtsModel.getData();
            List<Map<String, String>> old = dtsModel.getOld();
            if (CollectionUtils.isEmpty(old)) {
                log.error("订阅area_sku表数据，变更前数据为null,dtsModel:{}", JSON.toJSONString(dtsModel));
                return;
            }

            //上下架记录
            for (int i = 0; i < old.size(); i++) {
                Map<String, String> oldData = old.get(i);
                Map<String, String> newData = data.get(i);
                if (oldData.containsKey("on_sale")) {
                    saveAreaSkuRecord(newData);
                }
            }

            for (int i = 0; i < old.size(); i++) {
                handleBindBiz(old.get(i), data.get(i));
            }
        }

    }



    private void handleBindBiz(Map<String, String> oldData, Map<String, String> data) {
        String price = oldData.get("price");
        // 数据变更未涉及到价格变动不做联动自动改价
        if (StringUtils.isEmpty(price)) {
            return;
        }
        if (org.springframework.util.CollectionUtils.isEmpty(data)) {
            return;
        }
        handleBindSkuAutoUpdatePrice(data);
    }

    private void handleBindSkuAutoUpdatePrice(Map<String, String> data) {
        String sku = data.get("sku");
        String areaNo = data.get("area_no");
        String price = data.get("price");
        if (StringUtils.isEmpty(sku)) {
            log.info("当前sku信息不全，无法联动改价, sku:{}", sku);
            return;
        }
        log.info("处理sku联动调价, sku:{}, areaNo:{}", sku, areaNo);
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            if (LocalTime.now().isAfter(LocalTime.parse(startTime)) || LocalTime.now().isBefore(LocalTime.parse(endTime))) {
                //判断是否夜间自动调价的记录内的sku，如果是则不做联动改价（凌晨4:30之前的取前一天时间）
                LocalDate localDate = LocalDate.now();
                if (LocalTime.now().isBefore(LocalTime.parse(endTime))) {
                    localDate = localDate.minusDays(1);
                }
                SkuPriceTaskRecord taskRecord = skuPriceTaskRecordMapper.selectBySkuAndAreaNo(localDate, sku, Integer.parseInt(areaNo));
                if (taskRecord != null) {
                    log.info("当前sku数据为当天夜间自动调价的记录内的sku，不做联动改价, sku:{}, areaNo:{}, startTime:{}, endTime:{}", sku, areaNo, startTime, endTime);
                    return;
                }
            }
        }
        log.info("处理sku联动调价-开始, sku:{}, areaNo:{}", sku, areaNo);
        Inventory query = new Inventory();
        query.setSku(sku);
        Inventory inventory = inventoryMapper.selectOne(query);

        if (inventory == null) {
            log.info("sku数据不存在:{}", sku);
            return;
        }
        Long pdId = inventory.getPdId();
        Integer extType = inventory.getExtType();
        if (pdId == null || extType == null) {
            log.info("当前sku信息不全，无法联动改价, pdId:{}, extType:{}", pdId, extType);
            return;
        }

        if (!Objects.equals(extType, InventoryExtTypeEnum.NORMAL.type())) {
            log.info("当前sku类型非常规sku，不做联动改价,{}", extType);
            return;
        }
        try {
            priceAdjustService.bindSkuAutoUpdatePrice(pdId, sku, Integer.parseInt(areaNo), new BigDecimal(price));
        } catch (Exception e) {
            log.warn("联调调价失败，sku:{}, areaNo:{}, cause:{}", sku, areaNo, Throwables.getStackTraceAsString(e));
        }
    }

    private void saveAreaSkuRecord(Map<String, String> data) {
        String sku = data.get("sku");
        String updater = data.get("updater");
        String areaNo = data.get("area_no");
        String onSaleStr = data.get("on_sale");
        if (StringUtils.isEmpty(onSaleStr)) {
            log.error("上下架字段信息为null, data:{}", JSON.toJSONString(data));
            return;
        }
        if (StringUtils.isEmpty(updater)) {
            updater = "系统";
        }
        boolean onSale = onSaleStr.equals("1");//NOSONAR
        // 插入一条上下架记录
        AreaSkuRecord areaSkuRecord = null;
        try {
            areaSkuRecord = new AreaSkuRecord(updater, sku, Integer.parseInt(areaNo), "上下架", onSale, LocalDateTime.now());
            areaSkuRecordMapper.insertSelective(areaSkuRecord);
        } catch (NumberFormatException e) {
            log.error("binlog监听area_sku表插入上下架记录异常，data:{}", JSON.toJSONString(data));
        }
    }
}
