package net.summerfarm.table.impl;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.facade.auth.AuthUserFacade;
import net.summerfarm.mapper.srm.SrmSupplierUserMapper;
import net.summerfarm.mapper.tms.TmsDriverMapper;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.table.DbTableDml;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * @describe
 * @date 2022/9/17 21:50
 */
@Slf4j
@Service
public class UserPropertiesExtDmlImpl implements DbTableDml {

    @Override
    public void tableDml(DtsModel dtsModel) {

        log.info("UserPropertiesExtDmlImpl binlog监听 {}", JSONUtil.toJsonStr(dtsModel));

         log.info("UserPropertiesExtDmlImpl binlog监听 {} end    ", JSONUtil.toJsonStr(dtsModel.getData()));

    }
}
