package net.summerfarm.table.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.DtsModelTypeEnum;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.impl.ProductStockServiceImpl;
import net.summerfarm.table.DbTableDml;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/4/22 14:22
 */
@Slf4j
@Service
public class WarehouseInventoryMappingDmlImpl implements DbTableDml {
    @Resource
    private ProductStockServiceImpl productStockService;

    @Override
    public void tableDml(DtsModel dtsModel) {
        if (Objects.equals(DtsModelTypeEnum.UPDATE.name(),dtsModel.getType())) {
            // 执行数据同步Elasticsearch
            List<Map<String, String>> oldDatas = dtsModel.getOld();
            List<Map<String, String>> datas = dtsModel.getData();
            if(CollectionUtils.isEmpty(oldDatas)){
                return;
            }
            for (int i = 0; i < oldDatas.size(); i++) {
                Map<String, String> oldData = oldDatas.get (i);
                Map<String, String> data = datas.get (i);

                //监听warehouse_no是否变更 假如变更了则需要自动上下架逻辑
                String oldWareHouseNo = oldData.get ("warehouse_no");
                String warehouseNo = data.get ("warehouse_no");
                String sku = data.get ("sku");
                try {
                    if (!StringUtils.isEmpty (oldWareHouseNo) && !Objects.equals (oldWareHouseNo, warehouseNo)) {
                        log.info ("WarehouseInventoryMappingDmlImpl[]autoOnSale[]start[]warehouseNo:{}, sku:{}", warehouseNo, sku);
                        productStockService.autoOnSale (Integer.valueOf (warehouseNo), sku);
                    }
                } catch (Exception e) {
                    log.error ("WarehouseInventoryMappingDmlImpl[]autoOnSale[]error[]cause:{}", e);
                }
            }
        }
    }
}
