package net.summerfarm.module.pms.infrastructure.mapper;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.module.pms.infrastructure.model.AllocationOrderItemEntity;
import net.summerfarm.module.pms.infrastructure.model.AllocationOrderEntity;
import net.summerfarm.module.pms.model.input.StockAllocationInput;
import net.summerfarm.module.pms.model.vo.AllocationOrderEntityVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OldAllocationOrderMapper {

    int updateByPrimaryKeySelective(AllocationOrderEntity record);

    //不带数据权限查询
    List<AllocationOrderEntityVO> selectWithOutDataPermission(StockAllocationInput selectKeys);

    /**
     * 根据出库任务编号查询调拨计划调入仓
     * @param list 出库任务编号list
     * @return 包含调入仓的VO
     */
    List<AllocationOrderEntityVO> selectInStoreByListNoList(@Param("list") List<String> list);

    AllocationOrderEntityVO selectOne(String listNo);

}