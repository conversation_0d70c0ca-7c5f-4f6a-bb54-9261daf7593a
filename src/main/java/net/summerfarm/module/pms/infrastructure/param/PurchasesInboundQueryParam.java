package net.summerfarm.module.pms.infrastructure.param;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/15 17:19
 */
@Data
public class PurchasesInboundQueryParam {

    /**
     * 入库离线表id
     * sql  : id >= xx
     */
    private Long inboundIdGte;

    /**
     * 入库离线表id
     * sql  : id <= xx
     */
    private Long inboundIdLte;

    /**
     * 供应商
     */
    private List<Long> supplierList;

    /**
     * 查询限制
     */
    private Integer limitNum;



    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

}
