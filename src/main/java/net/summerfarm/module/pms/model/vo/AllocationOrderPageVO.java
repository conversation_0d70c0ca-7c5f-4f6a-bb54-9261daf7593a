package net.summerfarm.module.pms.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Date 2023/2/22 18:01
 */
@Data
public class AllocationOrderPageVO  {


    /**
     * 创建时间
     */
    private LocalDateTime addtime;

    /**
     * 创建人姓名
     */
    private String createAdminName;

    /**
     * 调入仓编号
     */
    private Integer inStore;

    /**
     * 调入仓名称
     */
    private String inStoreName;

    /**
     * 调拨单号
     */
    private String listNo;

    /**
     * 来源
     */
    private Integer originType;

    /**
     * 出库仓
     */
    private Integer outStore;

    /**
     * 出库仓名称
     */
    private String outStoreName;

    /**
     * 计划单号id
     */
    private Long planListId;

    /**
     * 计划单号
     */
    private  String planListNo;

    /**
     * @see net.summerfarm.module.pms.common.enums.AllocationOrderEnums.StockAllocationListStatus
     * 状态
     */
    private  Integer status;

    /**
     * 温区
     * @see net.summerfarm.module.pms.common.enums.AllocationOrderEnums.StorageLocation
     */
    private  Integer storageLocation;

    /**
     * 调入时间
     */
    private LocalDateTime inTime;

    /**
     * 调出时间
     */
    private LocalDateTime outTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatetime;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 预计入库时间
     */
    private LocalDateTime expectTime;

    /**
     * 仓库类型
     */
    private Integer outWarehouseType;

    /**
     * 仓库类型名称
     */
    private String outWarehouseTypeName;

    /**
     * 服务商
     */
    private String outWarehouseServiceProviderName;

    /**
     * 仓库类型
     */
    private Integer inWarehouseType;

    /**
     * 仓库类型名称
     */
    private String inWarehouseTypeName;

    /**
     * 服务商
     */
    private String inWarehouseServiceProviderName;

}
