package net.summerfarm.module.pms.model.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Date 2023/2/22 18:12
 */
@Data
public class AllocationOrderPageQueryInput {


    /**
     * 调拨单号
     */
    private String listNo;

    /**
     * 调拨计划单号
     */
    private String planListNo;


    /**
     * 调出仓编号
     */
    private Integer outStore;

    /**
     * 调出仓列表
     */
    private List<Integer> outStores;

    /**
     * "调入仓编号
     */
    private Integer inStore;

    /**
     * 调入仓列表
     */
    private List<Integer> inStores;

    /**
     * 调拨单状态
     */
    private Integer status;


    /**
     * 商品名称
     */
    private String pdName;

    /**
     * sku编号
     */
    private String sku;

    /**
     * 发起人
     */
    private Integer createAdmin;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 页数
     */
    private Integer pageIndex;

    /**
     * 页码
     */
    private Integer pageSize;
}
