package net.summerfarm.module.pms.model.dto;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class FinanceAccountAdjustDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 对账单id
     */
    private Long financeAccountStatementId;

    /**
     * 对账单明细id
     */
    private Long financeAccountStatementDetailId;

    /**
     * 调整金额
     */
    private BigDecimal adjustAmount;

    /**
     * 调整类型
     */
    private String adjustType;

    /**
     * 备注
     */
    private String remark;


}
