package net.summerfarm.module.pms.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class FinanceAccountSkuVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 对账单ID **/
    private Long accountId;
    /** sku **/
    private String skuCode;
    /** 名称 **/
    private String pdName;
    /** 商品图 **/
    private String skuPic;
    /** 单位 **/
    private String skuUnit;
    /** 规格 **/
    private String skuSpec;
    /** 收货数量 **/
    private Integer receivedQuantity;
    /** 退货数量 **/
    private Integer returnQuantity;
    /** 总金额 **/
    private BigDecimal totalAmount;
    /** 调整金额 **/
    private BigDecimal adjustAmount;
    /** 明细集合 **/
    private List<FinanceAccountSkuDetailVO> skuDetailVOList;

}
