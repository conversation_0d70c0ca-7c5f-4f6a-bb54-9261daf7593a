package net.summerfarm.module.pms.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class FinanceAccountSkuDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 对账明细ID **/
    private Long financeAccountDetailId;

    /** 对账单ID **/
    private Long financeAccountId;

    /** 仓库名称 **/
    private String warehouseName;
    /** 出入库类型 56、采购退货任务 11、采购入库任务 **/
    private Integer type;
    /** 单价 **/
    private BigDecimal price;
    /** 总价 **/
    private BigDecimal amount;
    /** 时间 **/
    private LocalDateTime createTime;
    /** 调账总额 **/
    private BigDecimal adjustAmount;
    /** 出入库数量 **/
    private Integer quantity;



}
