package net.summerfarm.module.pms.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class AccountDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long detailId;
    private Long financeAccountId;
    @ApiModelProperty(value="出入库时间")
    private LocalDateTime addTime;
    /**
     * 出入库任务单表id
     */
    @ApiModelProperty(value="出入库任务单表id")
    private Integer stockTaskProcessId;
    @ApiModelProperty(value="商品名称")
    private String pdName;

    @ApiModelProperty(value="sku")
    private String sku;

    @ApiModelProperty(value = "税率分类编码")
    private String taxRateCode;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRateValue;

    @ApiModelProperty(value = "出入库总额")
    private BigDecimal excludingTax;

    @ApiModelProperty(value = "不含税金额")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "出入库数量")
    private Integer quantity;

    @ApiModelProperty(value = "采购单单价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "规格")
    private String weight;

    @ApiModelProperty(value = "出入库任务单详情表表id")
    private Integer stockTaskProcessDetailId;

    @ApiModelProperty(value="采购单号")
    private String purchaseNo;

    @ApiModelProperty(value="sku图片")
    private String detailPicture;

    @ApiModelProperty(value="仓库编号")
    private Integer warehouseNo;

    @ApiModelProperty(value="仓库名称")
    private String warehouseName;

    @ApiModelProperty(value="出入库类型 56、采购退货任务 11、采购入库任务 21越仓入库 60 越仓出库")
    private Integer type;

    private BigDecimal amount;
    private BigDecimal adjustAmount;

}
