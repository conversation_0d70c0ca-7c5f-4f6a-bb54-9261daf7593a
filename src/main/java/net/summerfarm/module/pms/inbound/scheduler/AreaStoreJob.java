package net.summerfarm.module.pms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.AreaStoreService;
import net.summerfarm.service.PurchaseCalculateService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/** 水果近期销量同步 **/
@Component
@Slf4j
public class AreaStoreJob extends XianMuJavaProcessor {

    @Resource
    private AreaStoreService areaStoreService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("水果近期销量同步开始:{}", LocalDate.now());
        areaStoreService.updateRecentSales();
        log.info("水果近期销量同步结束:{}", LocalDate.now());
        return new ProcessResult(true);
    }
}
