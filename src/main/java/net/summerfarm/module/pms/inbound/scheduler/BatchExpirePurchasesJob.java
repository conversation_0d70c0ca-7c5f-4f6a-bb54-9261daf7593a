package net.summerfarm.module.pms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.PurchasesService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

@Component
@Slf4j
public class BatchExpirePurchasesJob extends XianMuJavaProcessor {

    @Resource
    private PurchasesService purchasesService;
    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("定时扫描即将到期采购单 start: {}", LocalDate.now());
        purchasesService.batchSendExpireSkuDingTalkMsg();
        log.info("定时扫描即将到期采购单 end: {}", LocalDate.now());
        return new ProcessResult(true);
    }
}
