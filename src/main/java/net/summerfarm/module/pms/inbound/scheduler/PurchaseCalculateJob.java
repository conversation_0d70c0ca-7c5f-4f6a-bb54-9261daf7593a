package net.summerfarm.module.pms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.PurchaseCalculateService;
import net.summerfarm.service.PurchasePredictionService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
/** 安全水位计算 **/
@Component
@Slf4j
public class PurchaseCalculateJob extends XianMuJavaProcessor {

    @Resource
    private PurchaseCalculateService purchaseCalculateService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("安全水位计算开始同步:{}", LocalDate.now());
        purchaseCalculateService.runPurchaseTask();
        log.info("安全水位计算同步结束:{}", LocalDate.now());
        return new ProcessResult(true);
    }
}
