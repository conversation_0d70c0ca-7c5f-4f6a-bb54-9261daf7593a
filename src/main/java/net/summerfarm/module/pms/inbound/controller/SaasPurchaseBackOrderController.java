package net.summerfarm.module.pms.inbound.controller;

import com.github.pagehelper.PageInfo;
import net.summerfarm.annotation.CheckSaasToken;
import net.summerfarm.model.vo.PurchasesBackVO;
import net.summerfarm.service.PurchasesBackService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2023/5/16 15:43
 */

@CheckSaasToken
@RestController
@RequestMapping(value = "/pms-service/saas-purchase-back")
public class SaasPurchaseBackOrderController {

    @Resource
    PurchasesBackService purchasesBackService;


    /**
     * 查询saas采购退货单分页列表
     *
     * @param purchasesBackVO 查询入参
     * @return 当页数据
     */
    @PostMapping(value = "/query/page")
    public CommonResult<PageInfo<PurchasesBackVO>> select(@RequestBody PurchasesBackVO purchasesBackVO) {
        return CommonResult.ok(purchasesBackService.select(purchasesBackVO.getPageIndex(), purchasesBackVO.getPageSize(), purchasesBackVO));
    }

    /**
     * 导出采购退货单（最多不超过5000条）
     *
     * @param purchasesBackVO 查询入参
     * @return 是否成功
     */
    @PostMapping("/export")
    public CommonResult<Boolean> export(@RequestBody PurchasesBackVO purchasesBackVO) {
        purchasesBackService.export4Saas(purchasesBackVO);
        return CommonResult.ok(true);
    }
}
