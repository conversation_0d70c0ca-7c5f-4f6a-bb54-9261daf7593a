package net.summerfarm.module.pms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.OrderService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

@Component
@Slf4j
public class ConfirmOrderServiceJob extends XianMuJavaProcessor {

    @Resource
    private OrderService orderService;
    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("一键发货任务 start:{}", LocalDate.now());
        orderService.autoConfirm();
        log.info("一键发货任务 end:{}", LocalDate.now());
        return new ProcessResult(true);
    }
}
