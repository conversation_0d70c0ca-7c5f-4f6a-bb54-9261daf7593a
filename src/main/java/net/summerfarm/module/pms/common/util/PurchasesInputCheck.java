package net.summerfarm.module.pms.common.util;

import com.google.common.base.Preconditions;
import net.summerfarm.module.pms.model.input.PurchasesPaymentExportInput;
import org.apache.commons.collections4.CollectionUtils;

import java.time.Duration;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/13 15:23
 */
public class PurchasesInputCheck {

    private final static Integer DAY_LIMIT = 95;

    /**
     * 参数校验
     *
     * @param input
     */
    public static void saasPurchasesPaymentCheck(PurchasesPaymentExportInput input) {
        Preconditions.checkArgument(Objects.nonNull(input), "请输入请求参数");
        Preconditions.checkArgument(Objects.nonNull(input.getStartTime()), "请输入导出开始时间");
        Preconditions.checkArgument(Objects.nonNull(input.getEndTime()), "请输入导出结束时间");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(input.getSupplierList()), "请选择供应商");

        Duration duration = Duration.between(input.getStartTime(), input.getEndTime());
        long days = duration.getSeconds() / (60 * 60 * 24);
        if (days >= DAY_LIMIT) {
            throw new IllegalArgumentException("输入的导出时间时间间隔过大");
        }
    }

}
