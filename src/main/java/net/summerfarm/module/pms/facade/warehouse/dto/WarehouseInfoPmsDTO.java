package net.summerfarm.module.pms.facade.warehouse.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> chenjie
 * @date : 2023-03-06 12:03
 * @describe :
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseInfoPmsDTO {
    /** 仓库编号 **/
    private Integer warehouseNo;
    /** 仓库名称 **/
    private String warehouseName;
    /** 仓库类型 **/
    private Integer warehouseType;
    /** 仓库类型名称 **/
    private String warehouseTypeName;
    /** 服务商 **/
    private String serviceProviderName;
    /** 仓库负责人 **/
    private Integer manageAdminId;
    /** 仓库负责人 **/
    private String personContact;
    /** 联系方式 **/
    private String phone;
    /**
     * 租户id
     */
    private Long tenantId;


}
