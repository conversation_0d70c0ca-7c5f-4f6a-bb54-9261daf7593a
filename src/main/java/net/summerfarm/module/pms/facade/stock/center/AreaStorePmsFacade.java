package net.summerfarm.module.pms.facade.stock.center;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.log4j.Log4j;
import net.summerfarm.enums.AbnormalRecordTypeEnum;
import net.summerfarm.enums.NextDayArriveEnum;
import net.summerfarm.enums.OtherStockChangeTypeEnum;
import net.summerfarm.enums.StoreRecordType;
import net.summerfarm.mapper.manage.AreaStoreMapper;
import net.summerfarm.mapper.manage.StoreRecordMapper;
import net.summerfarm.model.domain.AreaStore;
import net.summerfarm.model.domain.PurchasesBackDetail;
import net.summerfarm.model.domain.QuantityChangeRecord;
import net.summerfarm.model.domain.StoreRecord;
import net.summerfarm.model.vo.AreaStoreVO;
import net.summerfarm.model.vo.PurchasesBackDetailVO;
import net.summerfarm.module.pms.facade.stock.center.dto.AreaStoreResPmsDTO;
import net.summerfarm.module.pms.facade.stock.center.dto.NextDayReachPmsDTO;
import net.summerfarm.module.pms.facade.stock.center.dto.StoreRecordResPmsDTO;
import net.summerfarm.module.pms.facade.stock.center.input.*;
import net.summerfarm.service.AreaStoreService;
import net.summerfarm.service.PurchasesConfigService;
import net.summerfarm.service.QuantityChangeRecordService;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Log4j
@Component
public class AreaStorePmsFacade {
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private StoreRecordMapper storeRecordMapper;
    @Lazy
    @Resource
    private PurchasesConfigService purchasesConfigService;
    @Lazy
    @Resource
    private AreaStoreService areaStoreService;
    @Lazy
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;


    /** 查询库存仓批次库存列表接口 **/
    public List<StoreRecordResPmsDTO> listByNearlyBatchInventory(ListByNearlyBatchInventoryReqPmsInput input) {
        List<StoreRecordResPmsDTO> result = Lists.newArrayList();
        for (ListByNearlyBatchInventoryReqPmsInput.ListByNearlyBatchInventoryDetailReqPmsInput detail : input.getBatchInventoryDetailReqDTOList()) {
            StoreRecord storeRecord = storeRecordMapper.selectNearly(detail.getSku(), detail.getBatch(), detail.getAreaNo(), detail.getQualityDate());
            StoreRecordResPmsDTO vo = new StoreRecordResPmsDTO();
            BeanUtils.copyProperties(storeRecord, vo);
            result.add(vo);
        }
        return result;
    }




    /**
     * 入库预约增加在途、更新状态、库存预警
     * **/
    public void doAddRoadByStockArrange(PurchaseAddRoadReqPmsInput addRoadReqPmsDTO) {
        Integer warehouseNo = addRoadReqPmsDTO.getWarehouseNo();
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        for (PurchaseAddRoadReqPmsInput.PurchaseAddRoadDetailReqPmsDTO detail : addRoadReqPmsDTO.getPurchaseAddRoadDetailReqDTOList()) {
            String skuCode = detail.getSkuCode();
            AreaStore areaStore = areaStoreService.selectAreaStore(warehouseNo, skuCode);
            // 添加库存sku信息
            if (Objects.isNull(areaStore)) {
                throw new BizException("sku不存在该仓库中:" + skuCode);
            }
            areaStoreService.updateRoadStockByStoreNo(detail.getAddRoadQuantity(), skuCode, warehouseNo, addRoadReqPmsDTO.getStockChangeType(), addRoadReqPmsDTO.getOperatorNo(), recordMap);
            areaStoreService.updateAreaStoreStatus(warehouseNo, skuCode);
        }
        quantityChangeRecordService.insertRecord(recordMap);
    }

    /**
     * 入库预约减少在途
     * **/
    public void doReduceRoadReq(PurchaseReduceRoadReqPmsInput reduceRoadReqPmsDTO) {
        Integer warehouseNo = reduceRoadReqPmsDTO.getWarehouseNo();
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        for (PurchaseReduceRoadReqPmsInput.PurchaseReduceRoadDetailReqPmsDTO detailReqPmsDTO : reduceRoadReqPmsDTO.getPurchaseReduceRoadDetailReqDTOList()) {
            // 减在途库存
            areaStoreService.updateRoadStockByStoreNo(-detailReqPmsDTO.getReduceRoadQuantity(), detailReqPmsDTO.getSkuCode(), warehouseNo, reduceRoadReqPmsDTO.getStockChangeType(), reduceRoadReqPmsDTO.getOperatorNo(), recordMap);
        }
        quantityChangeRecordService.insertRecord(recordMap);
    }

    /** 采购退货冻结库存 **/
    public void doOccupyInventoryByPurchasesBack(PurchaseOccupyReqPmsInput input) {
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        for (PurchaseOccupyReqPmsInput.PurchaseOccupyDetailReqPmsDTO detail : input.getPurchaseOccupyDetailReqDTOList()) {
            AreaStore select = areaStoreMapper.selectOne(new AreaStore(input.getWarehouseNo(), detail.getSkuCode()));
            if (select.getSync() == 1) {
                areaStoreService.updateOnlineStockByStoreNo(true, -detail.getOccupyQuantity(), detail.getSkuCode(), input.getWarehouseNo(), OtherStockChangeTypeEnum.PURCHASES_BACK_SAVE, input.getOperatorNo(), recordMap, NumberUtils.INTEGER_ONE);
            }
            areaStoreService.updateLockStockByWarehouseNo(detail.getOccupyQuantity(), detail.getSkuCode(), null, input.getWarehouseNo(), OtherStockChangeTypeEnum.PURCHASES_BACK_SAVE, input.getOperatorNo(), recordMap);
        }
        //插入库存变化记录
        quantityChangeRecordService.insertRecord(recordMap);
    }

    /**冻结库存 新增采购退货明细 **/
    public void doOccupyInventoryBySaveDetailPurchasesBack(PurchaseOccupyReqPmsInput input) {
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        for (PurchaseOccupyReqPmsInput.PurchaseOccupyDetailReqPmsDTO detail : input.getPurchaseOccupyDetailReqDTOList()) {
            AreaStore select = areaStoreMapper.selectOne(new AreaStore(input.getWarehouseNo(), detail.getSkuCode()));
            if (select.getSync() == 1) {
                areaStoreService.updateOnlineStockByStoreNo(true, -detail.getOccupyQuantity(), detail.getSkuCode(), input.getWarehouseNo(), OtherStockChangeTypeEnum.PURCHASES_BACK_UPDATE, input.getOperatorNo(), recordMap, NumberUtils.INTEGER_ONE);
            }

            areaStoreService.updateLockStockByWarehouseNo(detail.getOccupyQuantity(), detail.getSkuCode(), null, input.getWarehouseNo() , OtherStockChangeTypeEnum.PURCHASES_BACK_UPDATE, input.getOperatorNo(), recordMap);
        }
        quantityChangeRecordService.insertRecord(recordMap);
    }


    /** 释放库存，增加虚拟库存 **/
    public void doReleaseInventoryByPurchasesBack(PurchaseReleaseReqPmsInput input) {
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        for (PurchaseReleaseReqPmsInput.PurchaseReleaseDetailReqPmsDTO detail : input.getPurchaseReleaseDetailReqDTOList()) {
            AreaStore select = areaStoreMapper.selectOne(new AreaStore(input.getWarehouseNo(), detail.getSkuCode()));
            if (select.getSync() == 1) {
                areaStoreService.updateOnlineStockByStoreNo(true, detail.getReleaseQuantity(), detail.getSkuCode(), input.getWarehouseNo(), input.getStockChangeType(), input.getOperatorNo(), recordMap, NumberUtils.INTEGER_ONE);
            }

            areaStoreService.updateLockStockByWarehouseNo(-detail.getReleaseQuantity(), detail.getSkuCode(), null, input.getWarehouseNo(), input.getStockChangeType(), input.getOperatorNo(), recordMap);
        }
        quantityChangeRecordService.insertRecord(recordMap);
    }

    /** 释放库存，减仓库库存 **/
    public void doReleaseInventoryByHandleBackDetail(PurchaseReleaseReqPmsInput input, PurchasesBackDetail record, PurchasesBackDetailVO detail, String operatorName) {
        StoreRecord select = new StoreRecord();
        select.setBatch(record.getBatch());
        select.setSku(record.getSku());
        select.setAreaNo(record.getAreaNo());
        select.setQualityDate(record.getQualityDate());
        StoreRecord lasted = storeRecordMapper.selectOne(select);
        if (lasted == null) {
            throw new BizException("sku:" + record.getSku() + "批次：" + record.getBatch() + "保质期:" + record.getQualityDate() + "批次不存在");
        } else {
            if (detail.getOutQuantity() > lasted.getStoreQuantity()) {
                throw new BizException("sku:" + record.getSku() + "批次：" + record.getBatch() + "保质期:" + record.getQualityDate() + "批次剩余库存不足");
            }
        }

        StoreRecord insert = new StoreRecord(lasted.getBatch(), lasted.getSku(), StoreRecordType.PURCHASES_BACK.getId(), detail.getOutQuantity(), lasted.getUnit(), operatorName, null,
                new Date(), lasted.getAreaNo(), lasted.getQualityDate(), lasted.getProductionDate(), lasted.getStoreQuantity() - detail.getOutQuantity(), lasted.getCost()
                , lasted.getTenantId());
        storeRecordMapper.insert(insert);

        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        for (PurchaseReleaseReqPmsInput.PurchaseReleaseDetailReqPmsDTO dtoDetail : input.getPurchaseReleaseDetailReqDTOList()) {
            // 释放冻结库存 减仓库库存
            areaStoreService.updateLockStockByWarehouseNo(-dtoDetail.getReleaseQuantity(), dtoDetail.getSkuCode(), null,input.getWarehouseNo(), input.getStockChangeType(), null, recordMap);
            areaStoreService.updateStoreStockByWarehouseNo(-dtoDetail.getReleaseQuantity(), dtoDetail.getSkuCode(), input.getWarehouseNo(), input.getStockChangeType(), StoreRecordType.PURCHASES_BACK.getId(), null, recordMap);
        }
        quantityChangeRecordService.insertRecord(recordMap);
    }


    /** 释放库存，增加虚拟库存(关闭任务) **/
    public void doReleaseInventoryByCloseTaskPurchasesBack(PurchaseReleaseReqPmsInput input) {
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        for (PurchaseReleaseReqPmsInput.PurchaseReleaseDetailReqPmsDTO detail : input.getPurchaseReleaseDetailReqDTOList()) {
            areaStoreService.updateAreaStoreStatus(input.getWarehouseNo(), detail.getSkuCode());
            if (Objects.equals(detail.getReleaseQuantity(), 0)) {
                continue;
            }
            AreaStore select = areaStoreMapper.selectOne(new AreaStore(input.getWarehouseNo(), detail.getSkuCode()));
            if (select.getSync() == 1) {
                areaStoreService.updateOnlineStockByStoreNo(true,  detail.getReleaseQuantity(), detail.getSkuCode(), input.getWarehouseNo(), OtherStockChangeTypeEnum.PURCHASES_BACK_FINISH,null, recordMap,NumberUtils.INTEGER_ONE);
            }
            areaStoreService.updateLockStockByWarehouseNo(-detail.getReleaseQuantity(), detail.getSkuCode(),null ,input.getWarehouseNo(), OtherStockChangeTypeEnum.PURCHASES_BACK_FINISH, null, recordMap);
        }
        quantityChangeRecordService.insertRecord(recordMap);
        }


    /**
     * 调拨占用库存
     * @param input
     */
    public void doAllocationOccupyInventory(PurchaseOccupyReqPmsInput input){
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        for (PurchaseOccupyReqPmsInput.PurchaseOccupyDetailReqPmsDTO detail : input.getPurchaseOccupyDetailReqDTOList()) {
            AreaStore select = areaStoreMapper.selectOne(new AreaStore(input.getWarehouseNo(), detail.getSkuCode()));
            if (select.getSync() == 1) {
                areaStoreService.updateOnlineStockByStoreNo(true, -detail.getOccupyQuantity(), detail.getSkuCode(), input.getWarehouseNo(), input.getStockChangeType(), input.getOperatorNo(), recordMap, NumberUtils.INTEGER_ONE);
            }

            areaStoreService.updateLockStockByWarehouseNo(detail.getOccupyQuantity(), detail.getSkuCode(), null, input.getWarehouseNo() , input.getStockChangeType(), input.getOperatorNo(), recordMap);
        }
        quantityChangeRecordService.insertRecord(recordMap);
     }

    /**
     * 调拨释放库存
     * @param input
     */
    public void doAllocationReleaseInventory(PurchaseReleaseReqPmsInput input){
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        for (PurchaseReleaseReqPmsInput.PurchaseReleaseDetailReqPmsDTO detail : input.getPurchaseReleaseDetailReqDTOList()) {
            AreaStore select = areaStoreMapper.selectOne(new AreaStore(input.getWarehouseNo(), detail.getSkuCode()));
            if (select.getSync() == 1) {
                areaStoreService.updateOnlineStockByStoreNo(true, detail.getReleaseQuantity(), detail.getSkuCode(), input.getWarehouseNo(), input.getStockChangeType(), input.getOperatorNo(), recordMap, NumberUtils.INTEGER_ONE);
            }

            areaStoreService.updateLockStockByWarehouseNo(-detail.getReleaseQuantity(), detail.getSkuCode(), null, input.getWarehouseNo(), input.getStockChangeType(), input.getOperatorNo(), recordMap);
        }
        quantityChangeRecordService.insertRecord(recordMap);
    }


    /**
     * 查询仓库库存
     * @param tenantId
     * @param warehouseNo
     * @param skuCodeList
     * @return
     */
    public List<AreaStoreResPmsDTO> listByTidAndWnoAndSku(Long tenantId, Integer warehouseNo, List<String> skuCodeList) {
        List<AreaStoreResPmsDTO> areaStoreResPmsDTOS = skuCodeList.stream().map(sku -> {
            AreaStoreVO storeVO = areaStoreMapper.select(new AreaStore(warehouseNo, sku));
            AreaStoreResPmsDTO areaStoreResPmsDTO = new AreaStoreResPmsDTO();
            areaStoreResPmsDTO.setOnlineQuantity(storeVO.getOnlineQuantity());
            areaStoreResPmsDTO.setSkuCode(sku);
            areaStoreResPmsDTO.setQuantity(storeVO.getQuantity());
            areaStoreResPmsDTO.setId(storeVO.getId());
            areaStoreResPmsDTO.setWarehouseNo(warehouseNo);
            if (Objects.equals(NumberUtils.INTEGER_ONE, storeVO.getSync())) {
                areaStoreResPmsDTO.setAvailableQuantity(storeVO.getOnlineQuantity());
            } else {
                // 可用库存
                int availableQuantity = storeVO.getQuantity() - storeVO.getLockQuantity() - storeVO.getSafeQuantity();
                areaStoreResPmsDTO.setAvailableQuantity(availableQuantity);
            }
            return areaStoreResPmsDTO;
        }).collect(Collectors.toList());
        return areaStoreResPmsDTOS;
    }



    /**
     * 调拨异常操作
     * @param abnormalStoreSkuInput
     */
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    public void doAbnormalRecordReq(AbnormalStoreSkuInput abnormalStoreSkuInput) {
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
            AreaStoreVO areaStore = areaStoreMapper.select(new AreaStore(abnormalStoreSkuInput.getWarehouseNo(), abnormalStoreSkuInput.getSku()));
            OtherStockChangeTypeEnum reason = Objects.equals(AbnormalRecordTypeEnum.BACK.ordinal(), abnormalStoreSkuInput.getReasonType()) ? OtherStockChangeTypeEnum.ALLOCATION_END_IN : OtherStockChangeTypeEnum.ALLOCATION_DAMAGE_OUT_TASK;
            if (!Objects.isNull(abnormalStoreSkuInput.getNextDayArrive())) {
                Integer nextDayArrive = abnormalStoreSkuInput.getNextDayArrive();
                if (Objects.equals(NextDayArriveEnum.NEXT_DAY_ARRIVE.ordinal(), nextDayArrive)) {
                    if (areaStore.getSync() == 1) {
                        // 次日达扣减虚拟库存
                        areaStoreService.updateOnlineStockByStoreNo(true, -abnormalStoreSkuInput.getQuantity(), abnormalStoreSkuInput.getSku(), abnormalStoreSkuInput.getWarehouseNo(), reason, abnormalStoreSkuInput.getListNo(), recordMap, NumberUtils.INTEGER_ONE);
                        if (areaStore.getOnlineQuantity() < abnormalStoreSkuInput.getQuantity()) {
                            // 超卖提醒
                            //sendOverSaleMsg(param, listVO);
                        }
                    }
                } else if (Objects.equals(NextDayArriveEnum.NOT_NEXT_DAY_ARRIVE.ordinal(), nextDayArrive)) {
                    // 非次日达扣减在途库存
                    areaStoreService.updateRoadStockByStoreNo(-abnormalStoreSkuInput.getQuantity(), abnormalStoreSkuInput.getSku(), abnormalStoreSkuInput.getWarehouseNo(), reason, abnormalStoreSkuInput.getListNo(), recordMap);
                }
            } else {
                // 未配置路线默认非次日达
                // 非次日达扣减在途库存
                areaStoreService.updateRoadStockByStoreNo(-abnormalStoreSkuInput.getQuantity(), abnormalStoreSkuInput.getSku(), abnormalStoreSkuInput.getWarehouseNo(), reason, abnormalStoreSkuInput.getListNo(), recordMap);
            }
        quantityChangeRecordService.insertRecord(recordMap);
    }




    /**
     * 完成调拨任务库存修改
     */
    public List<NextDayReachPmsDTO> doAllocationOutStock(Integer inWarehouseNo, Integer outWarehouseNo, List<AllocationOutStockInput> allocationOutStockInputs, Integer nextDayReach, String listNo){
        List<NextDayReachPmsDTO> nextDayArriveSkuList = Lists.newArrayList();
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        allocationOutStockInputs.forEach(allocationOutStockInput -> {
            AreaStoreVO inAreaStore = areaStoreMapper.selectNoPermission(new AreaStore(inWarehouseNo, allocationOutStockInput.getSku()));
            AreaStoreVO outAreaStore = areaStoreMapper.selectNoPermission(new AreaStore(outWarehouseNo, allocationOutStockInput.getSku()));
            // 库存同步且是次日达添加虚拟库存
            if (inAreaStore.getSync() == 1 && nextDayReach == 0) {
                areaStoreService.updateOnlineStockByStoreNo(true, allocationOutStockInput.getActualOutQuantityTotal(), allocationOutStockInput.getSku(), inWarehouseNo, OtherStockChangeTypeEnum.STOCK_ALLOCATION_DELIVERING, listNo, recordMap, NumberUtils.INTEGER_ONE);
                NextDayReachPmsDTO nextDayReachPmsDTO = new NextDayReachPmsDTO();
                nextDayReachPmsDTO.setSku(allocationOutStockInput.getSku());
                nextDayReachPmsDTO.setActualOutQuantityTotal(allocationOutStockInput.getActualOutQuantityTotal());
//            record.setActualOutQuantity(actualOutQuantityTotal);
//            record.setListNo(listNo);
//            record.setStatus(NumberUtils.INTEGER_ZERO);
//            record.setSku(sku);
//            record.setOutStore(listRecord.getOutStore());
//            record.setInStore(listRecord.getInStore());
//            quickRecordMapper.insert(record);
            }

            // 2.应发实发补差
            int outDiff = allocationOutStockInput.getQuantity() - allocationOutStockInput.getActualOutQuantityTotal();
            if (outDiff > 0) {
                if (outAreaStore.getSync() == 1) {
                    areaStoreService.updateOnlineStockByStoreNo(true, outDiff, allocationOutStockInput.getSku(), outWarehouseNo, OtherStockChangeTypeEnum.STOCK_ALLOCATION_BALANCE, listNo, recordMap, NumberUtils.INTEGER_ONE);
                }
                //完成出库根据扣减虚拟库存扣减冻结库存
                areaStoreService.updateLockStockByWarehouseNo(-outDiff, allocationOutStockInput.getSku(), null,outWarehouseNo,OtherStockChangeTypeEnum.STOCK_ALLOCATION_BALANCE, listNo, recordMap);
            }

            //非次日达 添加在途库存
            if(nextDayReach == 1){
                areaStoreService.updateRoadStockByStoreNo( allocationOutStockInput.getActualOutQuantityTotal(), allocationOutStockInput.getSku(), inWarehouseNo, OtherStockChangeTypeEnum.STOCK_ALLOCATION_DELIVERING, listNo, recordMap);
            }
            //修改sku状态
            areaStoreService.updateAreaStoreStatus(inWarehouseNo, allocationOutStockInput.getSku());
            //调拨任务完成城市库存sku状态更新
            areaStoreService.updateAreaStoreStatus(outWarehouseNo,allocationOutStockInput.getSku());
        });
        quantityChangeRecordService.insertRecord(recordMap);

        return nextDayArriveSkuList;
    }

    public void checkWnoAndSkuExists(Long tenantId, Integer warehouseNo, List<String> skuCodeList) {
        Set<String> paramSkuList = Sets.newHashSet(skuCodeList);
        List<AreaStoreResPmsDTO> resPmsDTOList = listByTidAndWnoAndSku(tenantId, warehouseNo, skuCodeList);
        Set<String> resSkuList = resPmsDTOList.stream().map(AreaStoreResPmsDTO::getSkuCode).collect(Collectors.toSet());
        if (paramSkuList.size() != resSkuList.size()) {
            paramSkuList.removeAll(resSkuList);
            throw new BizException("该sku不存在仓库中,skuCodeList:" + paramSkuList);
        }
    }




}
