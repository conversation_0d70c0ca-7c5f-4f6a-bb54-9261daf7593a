package net.summerfarm.module.wms.model.converter;

import net.summerfarm.model.param.StoreRecordQueryVO;
import net.summerfarm.model.vo.StoreRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface StoreRecordQueryVOConverter {

    StoreRecordQueryVOConverter INSTANCE = Mappers.getMapper(StoreRecordQueryVOConverter.class);

    StoreRecordQueryVO convert(StoreRecordVO storeRecordVO);
}
