package net.summerfarm.module.wms.biz.service;

import net.summerfarm.model.domain.OrderItem;
import net.summerfarm.model.domain.SampleSku;

import java.time.LocalDate;
import java.util.List;

public interface StockTaskOrderCancelService {

    /**
     * 释放订单冻结库存
     *
     * @param stockTaskId
     * @param adminName
     */
    void handleCabinetReleaseByStockTaskIdFinish(Integer stockTaskId, String adminName);


    /**
     * 处理取消订单，多出返库
     *
     * @param warehouseNo
     * @param storeNo
     * @param deliveryDate
     * @param orderItems
     */
    void handleSaleCancelOrderReturnInbound(Integer warehouseNo, Integer storeNo, LocalDate deliveryDate,
                                            List<OrderItem> orderItems, Integer taskType);

    /**
     * 处理取消出样订单，多出返库
     * @param warehouseNo
     * @param storeNo
     * @param deliveryDate
     * @param sampleSkus
     */
    void handleCancelSampleOrderReturnInbound(Integer warehouseNo, Integer storeNo, LocalDate deliveryDate,
                                              List<SampleSku> sampleSkus, Integer taskType);

    /**
     * 获取未创建的任务明细
     * @param warehouseNo
     * @param storeNo
     * @param deliveryDate
     * @param orderItems
     * @param taskType
     * @return
     */
    List<OrderItem> getNotCreateSaleOrderTaskItem(Integer warehouseNo, Integer storeNo, LocalDate deliveryDate,
                                                  List<OrderItem> orderItems, Integer taskType);

    /**
     * 处理出库波次初始化冻结
     * @param stockTaskId
     */
    void handleStockTaskWaveOccupyInit(Integer stockTaskId);

    /**
     * 处理出库波次扣减冻结
     * @param stockTaskId
     * @param sku
     * @param changeQuantity
     */
    void handleStockTaskWaveOccupyReduce(Integer stockTaskId, String sku, Integer changeQuantity);

    /**
     * 获取未创建的任务明细（越库出库）
     * @param warehouseNo
     * @param storeNo
     * @param deliveryDate
     * @param orderItems
     * @param taskType
     * @return
     */
    List<OrderItem> getNotCreateSaleOrderTaskItemForCross(Integer warehouseNo, Integer storeNo,
                                                          LocalDate deliveryDate,
                                                          List<OrderItem> orderItems, Integer taskType);
}
