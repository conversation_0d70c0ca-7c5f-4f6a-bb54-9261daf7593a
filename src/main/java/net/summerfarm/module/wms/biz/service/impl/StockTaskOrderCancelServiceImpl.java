package net.summerfarm.module.wms.biz.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.DateUtil;
import net.summerfarm.common.util.OptionFlagUtil;
import net.summerfarm.contexts.BaseConstant;
import net.summerfarm.dao.StockTaskStorageDAO;
import net.summerfarm.dao.dataobject.StockTaskStorageDO;
import net.summerfarm.enums.*;
import net.summerfarm.facade.wms.CabinetInventoryFacade;
import net.summerfarm.mapper.manage.StockTaskMapper;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.wms.StockTaskItemCabinetOccupyDO;
import net.summerfarm.model.domain.wms.StockTaskWaveSkuOccupyDO;
import net.summerfarm.module.wms.biz.factory.StockTaskItemDetailUpdateFactory;
import net.summerfarm.module.wms.biz.factory.StockTaskWaveSkuOccupyFactory;
import net.summerfarm.module.wms.biz.model.OrderReturnInboundDO;
import net.summerfarm.module.wms.biz.service.StockTaskOrderCancelService;
import net.summerfarm.module.wms.infrastructure.repository.StockTaskItemCabinetOccupyRepository;
import net.summerfarm.module.wms.infrastructure.repository.StockTaskOrderSkuRepository;
import net.summerfarm.module.wms.infrastructure.repository.StockTaskWaveRepository;
import net.summerfarm.module.wms.infrastructure.repository.StockTaskWaveSkuOccupyRepository;
import net.summerfarm.service.*;
import net.summerfarm.warehouse.model.domain.WarehouseLogisticsCenter;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.summerfarm.wms.instore.dto.req.StockStorageCreateReqDTO;
import net.summerfarm.wms.instore.dto.req.StockStorageItemCreateDTO;
import net.summerfarm.wms.inventory.req.cabinetInventory.CabinetInventoryReleaseReqDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StockTaskOrderCancelServiceImpl implements StockTaskOrderCancelService {

    @Resource
    private MqProducer mqProducer;
    @Autowired
    private StockTaskOrderSkuRepository stockTaskOrderSkuRepository;
    @Resource
    private WarehouseStorageService warehouseStorageService;
    @Resource
    private WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private ProductsService productsService;
    @Resource
    private CategoryService categoryService;
    @Resource
    private StockTaskItemCabinetOccupyRepository stockTaskItemCabinetOccupyRepository;
    @Resource
    private CabinetInventoryFacade cabinetInventoryFacade;
    @Resource
    private StockTaskMapper stockTaskMapper;
    @Resource
    private StockTaskStorageDAO stockTaskStorageDAO;
    @Resource
    private AreaStoreService areaStoreService;
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    private StockTaskWaveRepository stockTaskWaveRepository;
    @Resource
    private StockTaskWaveSkuOccupyRepository stockTaskWaveSkuOccupyRepository;

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void handleCabinetReleaseByStockTaskIdFinish(Integer stockTaskId, String adminName) {
        StockTask stockTask = stockTaskMapper.selectByPrimaryKey(stockTaskId);
        if (stockTask == null) {
            return;
        }
        List<StockTaskItemCabinetOccupyDO> cabinetOccupyDOList = stockTaskItemCabinetOccupyRepository
                .selectListByStockTaskIdOccupyed(stockTaskId, null);
        if (CollectionUtils.isEmpty(cabinetOccupyDOList)) {
            return;
        }

        cabinetOccupyDOList = cabinetOccupyDOList.stream()
                .filter(s -> s.getOccupyQuantity() > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cabinetOccupyDOList)) {
            return;
        }

        CabinetInventoryReleaseReqDTO releaseReqDTO = StockTaskItemDetailUpdateFactory
                .newInstance(stockTask, cabinetOccupyDOList);
        cabinetInventoryFacade.releaseCabinetInventory(releaseReqDTO);

        for (StockTaskItemCabinetOccupyDO stockTaskItemCabinetOccupyDO : cabinetOccupyDOList) {
            stockTaskItemCabinetOccupyRepository.updateReleaseChange(
                    stockTaskItemCabinetOccupyDO.getId(),
                    stockTaskItemCabinetOccupyDO.getOccupyQuantity()
            );
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void handleSaleCancelOrderReturnInbound(Integer warehouseNo, Integer storeNo,
                                                   LocalDate deliveryDate, List<OrderItem> orderItems,
                                                   Integer taskType) {
        Map<String, List<OrderItem>> orderItemMap = CollectionUtils.isEmpty(orderItems) ? new HashMap<>() :
                orderItems.stream().collect(Collectors.groupingBy(OrderItem::getOrderNo));
        // 获取已生成的所有任务
        List<String> orderNoList = stockTaskOrderSkuRepository.selectOutOrderNoByNotCancel(
                warehouseNo, storeNo, net.summerfarm.common.util.DateUtil.formatYmdDate(deliveryDate), taskType);

        Map<Long, List<OrderReturnInboundDO>> stockTaskReturnMap = new HashMap<>();
        for (String orderNo : orderNoList) {

            // 查询已生成的任务明细
            List<StockTaskOrderSkuDO> stockTaskOrderSkuDOList = stockTaskOrderSkuRepository.selectListNoByOutOrderNoList(
                    warehouseNo, storeNo, net.summerfarm.common.util.DateUtil.formatYmdDate(deliveryDate),
                    Arrays.asList(orderNo), taskType
            );
            if (CollectionUtils.isEmpty(stockTaskOrderSkuDOList)) {
                continue;
            }

            // 检查幂等
            List<Long> stockTaskIdList = stockTaskOrderSkuDOList.stream()
                    .map(StockTaskOrderSkuDO::getStockTaskId)
                    .distinct()
                    .collect(Collectors.toList());
            Long maxStockTaskId = stockTaskIdList.stream()
                    .sorted(Comparator.reverseOrder())
                    .findFirst()
                    .orElse(null);
            if (!CollectionUtils.isEmpty(stockTaskIdList)) {
                List<StockTaskStorageDO> stockTaskStorageDOList = stockTaskStorageDAO.queryMoreInBySourceId("" + maxStockTaskId);
                if (!CollectionUtils.isEmpty(stockTaskStorageDOList)) {
                    log.warn("多出反库任务幂等过滤订单 {} {} {}", orderNo, stockTaskIdList, maxStockTaskId);
                    continue;
                }
            }

            Map<String, Integer> beforSkuQuantityMap = stockTaskOrderSkuDOList.stream()
                    .collect(Collectors.toMap(
                            StockTaskOrderSkuDO::getSku, StockTaskOrderSkuDO::getQuantity, Integer::sum));

            // 比较最新的任务明细
            List<OrderItem> orderItemList = orderItemMap.get(orderNo);
            Map<String, Integer> nowSkuQuantityMap = orderItemList == null ?
                    new HashMap<>() :
                    orderItemList.stream()
                            .collect(Collectors.toMap(
                                    OrderItem::getSku, OrderItem::getAmount, Integer::sum));

            for (Map.Entry<String, Integer> beforSkuQuantityEntry : beforSkuQuantityMap.entrySet()) {
                String sku = beforSkuQuantityEntry.getKey();
                Integer beforeQuantity = beforSkuQuantityEntry.getValue();

                Integer nowQuantity = nowSkuQuantityMap.get(sku);
                nowQuantity = nowQuantity == null ? 0 : nowQuantity;

                Integer diffQuantity = 0;
                // 全部取消
                if (beforeQuantity.compareTo(nowQuantity) >= 0) {
                    diffQuantity = beforeQuantity - nowQuantity;
                }

                if (diffQuantity != 0) {
                    List<OrderReturnInboundDO> list = stockTaskReturnMap.get(maxStockTaskId);
                    if (CollectionUtils.isEmpty(list)) {
                        list = new ArrayList<>();
                    }
                    list.add(
                            OrderReturnInboundDO.builder()
                                    .warehouseNo(warehouseNo)
                                    .storeNo(storeNo)
                                    .deliveryDate(deliveryDate)
                                    .orderNo(orderNo)
                                    .sku(sku)
                                    .quantity(diffQuantity)
                                    .build()
                    );

                    stockTaskReturnMap.put(maxStockTaskId, list);
                }
            }
        }

        // 发送多出返库入库
        if (!CollectionUtils.isEmpty(stockTaskReturnMap)) {
            sendCreateReturnInbound(warehouseNo, storeNo, deliveryDate,
                    stockTaskReturnMap);
        }
    }

    @Override
    public void handleStockTaskWaveOccupyInit(Integer stockTaskId) {
        if (stockTaskId == null) {
            return;
        }

        StockTask stockTask = stockTaskMapper.selectByPrimaryKey(stockTaskId);
        if (!OptionFlagUtil.hasValue(stockTask.getOptionFlag(), OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode())) {
            return;
        }
        List<StockTaskWaveSkuOccupyDO> stockTaskWaveSkuOccupyDOList = stockTaskWaveSkuOccupyRepository
                .selectListByStockTaskId(stockTaskId, null);
        if (!CollectionUtils.isEmpty(stockTaskWaveSkuOccupyDOList)) {
            return;
        }

        List<StockTaskOrderSkuDO> stockTaskOrderSkuDOList = stockTaskOrderSkuRepository.selectListByTaskId(stockTaskId);
        if (CollectionUtils.isEmpty(stockTaskOrderSkuDOList)) {
            return;
        }


        Integer warehouseNo = stockTask.getAreaNo();
        Integer storeNo = stockTask.getOutStoreNo();
        LocalDate deliveryDate = stockTask.getExpectTime().toLocalDate();


        List<StockTaskWaveSkuOccupyDO> createWaveSkuOccupyList = new ArrayList<>();

        Integer type = stockTask.getType();
        if (type.equals(StoreRecordType.SALE_OUT.getId())) {
            List<OrderItem> orderItemList = stockTaskWaveRepository.getSaleOrderItem(
                    warehouseNo, storeNo, deliveryDate
            );

            createWaveSkuOccupyList = StockTaskWaveSkuOccupyFactory.createSale(
                    stockTask, stockTaskOrderSkuDOList, orderItemList);
        } else if (type.equals(StoreRecordType.DEMO_OUT.getId())) {
            List<SampleSku> sampleSkuList = stockTaskWaveRepository.getSampleApplyItem(
                    warehouseNo, deliveryDate, storeNo
            );

            createWaveSkuOccupyList = StockTaskWaveSkuOccupyFactory.createSampleSku(
                    stockTask, stockTaskOrderSkuDOList, sampleSkuList);
        } else if (type.equals(StoreRecordType.SUPPLY_AGAIN_TASK.getId())) {
            List<OrderItem> orderItemList = stockTaskWaveRepository.getAfterSaleItem(
                    warehouseNo, storeNo, deliveryDate
            );
            createWaveSkuOccupyList = StockTaskWaveSkuOccupyFactory.createSale(
                    stockTask, stockTaskOrderSkuDOList, orderItemList);
        }

        stockTaskWaveSkuOccupyRepository.insertList(createWaveSkuOccupyList);
    }

    @Override
    public void handleStockTaskWaveOccupyReduce(Integer stockTaskId, String sku, Integer changeQuantity) {
        if (stockTaskId == null) {
            return;
        }

        // 波次出库任务在截单处理冻结
        StockTask task = stockTaskMapper.selectByPrimaryKey(stockTaskId);
        if (task == null ||
                !OptionFlagUtil.hasValue(task.getOptionFlag(), OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode())) {
            return;
        }

        // 出库波次SKU占用
        List<StockTaskWaveSkuOccupyDO> waveSkuOccupyDOList = stockTaskWaveSkuOccupyRepository.selectListByStockTaskId(
                stockTaskId, Arrays.asList(sku));
        if (CollectionUtils.isEmpty(waveSkuOccupyDOList)) {
            return;
        }
        StockTaskWaveSkuOccupyDO stockTaskWaveSkuOccupyDO = waveSkuOccupyDOList.get(0);
        if (changeQuantity > stockTaskWaveSkuOccupyDO.getRemainOccupyQuantity() +
                stockTaskWaveSkuOccupyDO.getRemainNotOccupyQuantity()) {
            log.error("变更数量超过剩余订单锁定数量 {} {} {} {}",
                    stockTaskId,
                    changeQuantity,
                    stockTaskWaveSkuOccupyDO.getRemainOccupyQuantity(),
                    stockTaskWaveSkuOccupyDO.getRemainNotOccupyQuantity());
            throw new BizException("变更数量超过剩余订单锁定数量");
        }

        Integer remainQuantity = changeQuantity;

        Integer remainOccupyQuantityReduce = 0;
        if (stockTaskWaveSkuOccupyDO.getRemainOccupyQuantity() == 0){
            remainOccupyQuantityReduce = 0;
        } else if (remainQuantity <= stockTaskWaveSkuOccupyDO.getRemainOccupyQuantity()) {
            remainOccupyQuantityReduce = remainQuantity;
            remainQuantity = remainQuantity - remainOccupyQuantityReduce;
        } else {
            remainOccupyQuantityReduce = stockTaskWaveSkuOccupyDO.getRemainOccupyQuantity();
            remainQuantity = remainQuantity - remainOccupyQuantityReduce;
        }

        Integer remainNotOccupyQuantityReduce = 0;
        if (remainQuantity > 0) {
            if (remainQuantity <= stockTaskWaveSkuOccupyDO.getRemainNotOccupyQuantity()) {
                remainNotOccupyQuantityReduce = remainQuantity;
                remainQuantity = remainQuantity - remainNotOccupyQuantityReduce;
            } else {
                log.error("变更数量超过订单锁定剩余数量 {} {} {} {}",
                        stockTaskId,
                        changeQuantity,
                        stockTaskWaveSkuOccupyDO.getRemainOccupyQuantity(),
                        stockTaskWaveSkuOccupyDO.getRemainNotOccupyQuantity());
                throw new BizException("变更数量超过订单锁定剩余数量");
            }
        }

        stockTaskWaveSkuOccupyRepository.updatePickChange(
                stockTaskWaveSkuOccupyDO.getId(),
                remainOccupyQuantityReduce,
                remainNotOccupyQuantityReduce
        );

        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();

        Integer type = task.getType();
        Integer warehouseNo = task.getAreaNo();
        Integer storeNo = task.getOutStoreNo();

        if (remainOccupyQuantityReduce != 0){
            if (type.equals(StoreRecordType.SALE_OUT.getId()) || type.equals(StoreRecordType.OWN_SALE_OUT.getId())) {
                // 释放冻结库存
                areaStoreService.updateLockStockByWarehouseNo(
                        -remainOccupyQuantityReduce, sku,
                        storeNo, warehouseNo,
                        SaleStockChangeTypeEnum.SALE_OUT,
                        task.getId() + "", recordMap);
            } else if (type.equals(StoreRecordType.DEMO_OUT.getId())) {
                //减锁定库存
                areaStoreService.updateLockStockByWarehouseNo(
                        -remainOccupyQuantityReduce, sku,
                        storeNo, warehouseNo,
                        SaleStockChangeTypeEnum.DEMO_OUT,
                        task.getId() + "", recordMap);
            } else if (type.equals(StoreRecordType.SUPPLY_AGAIN_TASK.getId())) {
                //减锁定库存
                areaStoreService.updateLockStockByWarehouseNo(
                        -remainOccupyQuantityReduce, sku,
                        storeNo, warehouseNo,
                        SaleStockChangeTypeEnum.RECOVER_OUT,
                        task.getId() + "", recordMap);
            }
        }

        quantityChangeRecordService.insertRecord(recordMap);
    }


    private void sendCreateReturnInbound(Integer warehouseNo, Integer storeNo, LocalDate deliveryDate,
                                         Map<Long, List<OrderReturnInboundDO>> stockTaskReturnMap) {
        if (CollectionUtils.isEmpty(stockTaskReturnMap)) {
            return;
        }

        for (Map.Entry<Long, List<OrderReturnInboundDO>> longListEntry : stockTaskReturnMap.entrySet()) {
            Long maxStockTaskId = longListEntry.getKey();
            List<OrderReturnInboundDO> orderReturnInboundDOList = longListEntry.getValue();
            if (CollectionUtils.isEmpty(orderReturnInboundDOList)) {
                continue;
            }

            // 库存仓
            WarehouseStorageCenter warehouseStorageCenter = warehouseStorageService.selectByWarehouseNo(warehouseNo);
            String warehouseName = warehouseStorageCenter == null ? null : warehouseStorageCenter.getWarehouseName();

            // 城配仓
            WarehouseLogisticsCenter warehouseLogisticsCenter = warehouseLogisticsService.selectByStoreNo(storeNo);
            String storeName = warehouseLogisticsCenter == null ? null : warehouseLogisticsCenter.getStoreName();

            // 备注
            String remarkJson = JSONObject.toJSONString(orderReturnInboundDOList);
            InputStream inputStream = new ByteArrayInputStream(remarkJson.getBytes(StandardCharsets.UTF_8));
            String fileName = "多出返库任务信息" + warehouseName + "_" + storeName + "_" +
                    net.summerfarm.common.util.DateUtil.formatYmdDate(deliveryDate) + "_"
                    + DateUtil.formatDateBasic(LocalDateTime.now()) + ".txt";
            OssUploadResult ossUploadResult = OssUploadUtil.upload(fileName, inputStream, OSSExpiredLabelEnum.NO_EXPIRATION);
            String remarkUrl = ossUploadResult.getUrl();

            List<String> skuCodeList = orderReturnInboundDOList.stream()
                    .map(OrderReturnInboundDO::getSku)
                    .distinct()
                    .collect(Collectors.toList());
            List<Inventory> inventoryList = inventoryService.selectBySkuList(skuCodeList);
            Map<String, Inventory> inventoryMap = inventoryList.stream().collect(Collectors.toMap(Inventory::getSku, Function.identity(), (x1, x2) -> x1));

            List<Long> pdIdList = inventoryList.stream()
                    .map(Inventory::getPdId)
                    .distinct()
                    .collect(Collectors.toList());
            List<Products> productsList = productsService.selectListByIdList(pdIdList);
            Map<Long, Products> productsMap = productsList.stream().collect(Collectors.toMap(Products::getPdId, Function.identity(), (x1, x2) -> x1));

            List<Integer> categoryIdList = productsList.stream()
                    .map(Products::getCategoryId)
                    .distinct()
                    .collect(Collectors.toList());
            List<Category> categoryList = categoryService.selectTypeByIds(categoryIdList);
            Map<Integer, Category> categoryMap = categoryList.stream().collect(Collectors.toMap(Category::getId, Function.identity(), (x1, x2) -> x1));


            // 创建差异入库单
            StockStorageCreateReqDTO build = StockStorageCreateReqDTO.builder()
                    .adminId(0L)
                    .operatorName("系统默认")
                    .type(StockTaskType.OUT_MORE_IN.getId())
                    .inWarehouseNo(warehouseNo)
                    .inWarehouseName(warehouseName)
                    .outWarehouseNo(storeNo)
                    .outWarehouseName(storeName)
                    .expectTime(DateUtil.toLocalDateTime(DateUtil.toDate(deliveryDate)))
                    .sourceNo("" + maxStockTaskId)
                    .tenantId(BaseConstant.XIANMU_TENANT_ID)
                    .remark(remarkUrl)
                    .build();

            List<StockStorageItemCreateDTO> storageItemCreateDTOList = new ArrayList<>();
            for (OrderReturnInboundDO orderReturnInboundDO : orderReturnInboundDOList) {
                Inventory sku = inventoryMap.get(orderReturnInboundDO.getSku());
                if (sku == null) {
                    log.error("返库查询SKU不存在: {} \n换行告警", orderReturnInboundDO.getSku());
                    throw new BizException("返库查询SKU不存在:" + orderReturnInboundDO.getSku());
                }
                Products spu = productsMap.get(sku.getPdId());
                if (spu == null) {
                    log.error("返库查询spu不存在: {} \n换行告警", orderReturnInboundDO.getSku());
                    throw new BizException("返库查询SKU不存在:" + orderReturnInboundDO.getSku());
                }

                Category category = categoryMap.get(spu.getCategoryId());
                if (category == null) {
                    log.error("返库查询类目不存在: {} \n换行告警", orderReturnInboundDO.getSku());
                    throw new BizException("返库查询SKU不存在:" + orderReturnInboundDO.getSku());
                }

                storageItemCreateDTOList.add(
                        StockStorageItemCreateDTO.builder()
                                .category(category.getCategory())
                                .tenantId(BaseConstant.XIANMU_TENANT_ID)
                                .sku(orderReturnInboundDO.getSku())
                                // 1 全部,2乳制品,3非乳制品,4水果
                                .categoryType(category.getType())
                                .packaging(sku.getUnit())
                                .pdName(spu.getPdName())
                                // sku 归属类型   0 自营 1 代仓
                                .skuType(sku.getType())
                                // 应入数
                                .quantity(orderReturnInboundDO.getQuantity())
                                .specification(sku.getWeight())
                                .temperature(spu.getStorageLocation())

                                .build()
                );
            }

            Map<String, StockStorageItemCreateDTO> map = storageItemCreateDTOList.stream().collect(Collectors.toMap(
                    s -> s.getSku(), Function.identity(), (a, b) -> {
                        a.setQuantity(a.getQuantity() + b.getQuantity());
                        return a;
                    }));

            build.setStorageItemCreateDTOList(new ArrayList<>(map.values()));
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        mqProducer.send("wms-list", "tag_wms_stock_storage_task_create", build);
                    } catch (Throwable throwable){
                        log.error("mqProducer send tag_wms_stock_storage_task_create throwable", throwable);
                    }
                }
            });
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void handleCancelSampleOrderReturnInbound(Integer warehouseNo, Integer storeNo, LocalDate deliveryDate,
                                                     List<SampleSku> sampleSkus, Integer taskType) {
        Map<String, List<SampleSku>> orderItemMap = CollectionUtils.isEmpty(sampleSkus) ? new HashMap<>() :
                sampleSkus.stream().collect(Collectors.groupingBy(s -> "" + s.getSampleId()));
        // 获取已生成的所有任务
        List<String> orderNoList = stockTaskOrderSkuRepository.selectOutOrderNoByNotCancel(
                warehouseNo, storeNo, net.summerfarm.common.util.DateUtil.formatYmdDate(deliveryDate), taskType);

        Map<Long, List<OrderReturnInboundDO>> stockTaskReturnMap = new HashMap<>();
        for (String orderNo : orderNoList) {

            // 查询已生成的任务明细
            List<StockTaskOrderSkuDO> stockTaskOrderSkuDOList = stockTaskOrderSkuRepository.selectListNoByOutOrderNoList(
                    warehouseNo, storeNo, net.summerfarm.common.util.DateUtil.formatYmdDate(deliveryDate),
                    Arrays.asList(orderNo), taskType
            );
            if (CollectionUtils.isEmpty(stockTaskOrderSkuDOList)) {
                continue;
            }

            // 检查幂等
            List<Long> stockTaskIdList = stockTaskOrderSkuDOList.stream()
                    .map(StockTaskOrderSkuDO::getStockTaskId)
                    .distinct()
                    .collect(Collectors.toList());
            Long maxStockTaskId = stockTaskIdList.stream()
                    .sorted(Comparator.reverseOrder())
                    .findFirst()
                    .orElse(null);
            if (!CollectionUtils.isEmpty(stockTaskIdList)) {
                List<StockTaskStorageDO> stockTaskStorageDOList = stockTaskStorageDAO.queryMoreInBySourceId("" + maxStockTaskId);
                if (!CollectionUtils.isEmpty(stockTaskStorageDOList)) {
                    log.warn("多出反库任务幂等过滤订单 {} {} {}", orderNo, stockTaskIdList, maxStockTaskId);
                    continue;
                }
            }


            Map<String, Integer> beforSkuQuantityMap = stockTaskOrderSkuDOList.stream()
                    .collect(Collectors.toMap(
                            StockTaskOrderSkuDO::getSku, StockTaskOrderSkuDO::getQuantity, Integer::sum));

            // 比较最新的任务明细
            List<SampleSku> orderItemList = orderItemMap.get(orderNo);
            Map<String, Integer> nowSkuQuantityMap = orderItemList == null ?
                    new HashMap<>() :
                    orderItemList.stream()
                            .collect(Collectors.toMap(
                                    SampleSku::getSku, SampleSku::getAmount, Integer::sum));

            for (Map.Entry<String, Integer> beforSkuQuantityEntry : beforSkuQuantityMap.entrySet()) {
                String sku = beforSkuQuantityEntry.getKey();
                Integer beforeQuantity = beforSkuQuantityEntry.getValue();

                Integer nowQuantity = nowSkuQuantityMap.get(sku);
                nowQuantity = nowQuantity == null ? 0 : nowQuantity;

                Integer diffQuantity = 0;
                // 全部取消
                if (nowQuantity == null) {
                    diffQuantity = beforeQuantity;
                }
                // 部分取消
                else if (beforeQuantity.compareTo(nowQuantity) > 0) {
                    diffQuantity = beforeQuantity - nowQuantity;
                } else if (beforeQuantity.compareTo(nowQuantity) < 0) {
                    log.error("退货数量异常，订单应出数量超过上一次的应出数量, {} {} {} {} {} \n换行告警",
                            orderNo, sku, deliveryDate, beforeQuantity, nowQuantity);
                    continue;
                }

                if (diffQuantity != 0) {
                    List<OrderReturnInboundDO> list = stockTaskReturnMap.get(maxStockTaskId);
                    if (CollectionUtils.isEmpty(list)) {
                        list = new ArrayList<>();
                    }
                    list.add(
                            OrderReturnInboundDO.builder()
                                    .warehouseNo(warehouseNo)
                                    .storeNo(storeNo)
                                    .deliveryDate(deliveryDate)
                                    .orderNo(orderNo)
                                    .sku(sku)
                                    .quantity(diffQuantity)
                                    .build()
                    );

                    stockTaskReturnMap.put(maxStockTaskId, list);
                }
            }
        }

        // 发送多出返库入库
        if (!CollectionUtils.isEmpty(stockTaskReturnMap)) {
            sendCreateReturnInbound(warehouseNo, storeNo, deliveryDate,
                    stockTaskReturnMap);
        }
    }

    @Override
    public List<OrderItem> getNotCreateSaleOrderTaskItem(Integer warehouseNo, Integer storeNo,
                                                         LocalDate deliveryDate,
                                                         List<OrderItem> orderItems, Integer taskType) {
        if (CollectionUtils.isEmpty(orderItems)) {
            return new ArrayList<>();
        }

        List<String> orderNoList = orderItems.stream().map(OrderItem::getOrderNo).distinct().collect(Collectors.toList());

        List<String> existOrderNoList = stockTaskOrderSkuRepository.selectOutOrderNoByOutOrderNoList(
                warehouseNo, storeNo, net.summerfarm.common.util.DateUtil.formatYmdDate(deliveryDate),
                orderNoList, StoreRecordType.SALE_OUT.getId());

        List<OrderItem> notCreateTaskOrderItems = orderItems.stream()
                .filter(orderItem -> !existOrderNoList.contains(orderItem.getOrderNo()))
                .collect(Collectors.toList());

        // 已生成过的比对差值
        Map<String, List<OrderItem>> createdTaskOrderItemMap = orderItems.stream()
                .filter(orderItem -> existOrderNoList.contains(orderItem.getOrderNo()))
                .collect(Collectors.groupingBy(OrderItem::getOrderNo));
        for (Map.Entry<String, List<OrderItem>> createdTaskOrderItemEntry : createdTaskOrderItemMap.entrySet()) {

            String orderNo = createdTaskOrderItemEntry.getKey();

            // 查询已生成的任务明细
            List<StockTaskOrderSkuDO> stockTaskOrderSkuDOList = stockTaskOrderSkuRepository.selectListNoByOutOrderNoList(
                    warehouseNo, storeNo, net.summerfarm.common.util.DateUtil.formatYmdDate(deliveryDate),
                    Arrays.asList(orderNo), taskType
            );
            if (CollectionUtils.isEmpty(stockTaskOrderSkuDOList)) {
                continue;
            }

            Map<String, Integer> beforSkuQuantityMap = stockTaskOrderSkuDOList.stream()
                    .collect(Collectors.toMap(
                            StockTaskOrderSkuDO::getSku, StockTaskOrderSkuDO::getQuantity, Integer::sum));

            // 比较最新的任务明细
            List<OrderItem> orderItemList = createdTaskOrderItemEntry.getValue();
            if (CollectionUtils.isEmpty(orderItemList)) {
                continue;
            }

            Map<String, Integer> nowSkuQuantityMap = orderItemList == null ?
                    new HashMap<>() :
                    orderItemList.stream()
                            .collect(Collectors.toMap(
                                    OrderItem::getSku, OrderItem::getAmount, Integer::sum));

            for (Map.Entry<String, Integer> beforSkuQuantityEntry : beforSkuQuantityMap.entrySet()) {
                String sku = beforSkuQuantityEntry.getKey();
                Integer beforeQuantity = beforSkuQuantityEntry.getValue();

                Integer nowQuantity = nowSkuQuantityMap.get(sku);
                nowQuantity = nowQuantity == null ? 0 : nowQuantity;

                // 部分取消，多出入库任务兜底
                if (beforeQuantity.compareTo(nowQuantity) > 0) {
                    continue;
                } else if (beforeQuantity.compareTo(nowQuantity) < 0) {
                    OrderItem beforeOrderItem = null;
                    for (OrderItem orderItem : orderItemList) {
                        if (orderItem.getSku().equals(sku)) {
                            beforeOrderItem = orderItem;
                            break;
                        }
                    }
                    if (beforeOrderItem == null) {
                        log.error("多生成的出库任务无法匹配到SKU订单明细行 {} {}\n换行告警", sku, orderItemList);
                        throw new BizException("多生成的出库任务无法匹配到SKU订单明细行");
                    }

                    OrderItem orderItemTmp = new OrderItem();
                    BeanUtils.copyProperties(beforeOrderItem, orderItemTmp);
                    orderItemTmp.setAmount(nowQuantity - beforeQuantity);

                    notCreateTaskOrderItems.add(orderItemTmp);
                }
            }
        }
        return notCreateTaskOrderItems;
    }

    @Override
    public List<OrderItem> getNotCreateSaleOrderTaskItemForCross(Integer warehouseNo, Integer storeNo,
                                                         LocalDate deliveryDate,
                                                         List<OrderItem> orderItems, Integer taskType) {
        if (CollectionUtils.isEmpty(orderItems)) {
            return new ArrayList<>();
        }

        List<String> orderNoList = orderItems.stream().map(OrderItem::getOrderNo).distinct().collect(Collectors.toList());

        List<String> existOrderNoList = stockTaskOrderSkuRepository.selectOutOrderNoByOutOrderNoList(
                warehouseNo, storeNo, net.summerfarm.common.util.DateUtil.formatYmdDate(deliveryDate),
                orderNoList, taskType);

        List<OrderItem> notCreateTaskOrderItems = orderItems.stream()
                .filter(orderItem -> !existOrderNoList.contains(orderItem.getOrderNo()))
                .collect(Collectors.toList());

        return notCreateTaskOrderItems;
    }
}
