package net.summerfarm.module.wms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.AreaStoreService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 清除预留库存Job
 */
@Component
@Slf4j
public class AutoClearReserveQuantityJob extends XianMuJavaProcessor {

    @Autowired
    private AreaStoreService areaStoreService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        areaStoreService.autoClearReserveQuantity();
        return new ProcessResult(true);
    }
}
