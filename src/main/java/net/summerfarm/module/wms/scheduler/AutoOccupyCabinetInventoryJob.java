//package net.summerfarm.module.wms.scheduler;
//
//import com.alibaba.schedulerx.worker.domain.JobContext;
//import com.alibaba.schedulerx.worker.processor.ProcessResult;
//import com.google.common.collect.Lists;
//import net.summerfarm.model.domain.StockTask;
//import net.summerfarm.model.domain.StockTaskItem;
//import net.summerfarm.service.CabinetStockTaskService;
//import net.summerfarm.service.StockTaskService;
//import net.xianmu.task.process.XianMuJavaProcessor;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * @Description 自动锁定库位库存
// * @Date 2023/5/17 17:19
// * @<AUTHOR>
// */
//@Component
//public class AutoOccupyCabinetInventoryJob extends XianMuJavaProcessor {
//
//    @Resource
//    private CabinetStockTaskService cabinetStockTaskService;
//    @Resource
//    private StockTaskService stockTaskService;
//
//    @Override
//    public ProcessResult processResult(JobContext context) throws Exception {
//        // todo 查询所有已开启精细化仓列表
//        List<Integer> warehouseNoList = Lists.newArrayList();
//        warehouseNoList.forEach(warehouseNo -> {
//            // 根据仓 + 冻结状态查询所有未冻结的出库任务列表
//            List<StockTask> stockTaskList = cabinetStockTaskService.listStockTaskByWarehouseNoAndLockStatus(warehouseNo, 0); // todo 未冻结的任务
//            stockTaskList.forEach(stockTask -> {
//                Integer stockTaskId = stockTask.getId();
//                // 查询出库任务明细
//                List<StockTaskItem> stockTaskItemList = Lists.newArrayList();
//                // 调用库存中心锁定库位库存
//
//                // 回写出库任务明细库位表
//
//                //
//
//
//            });
//
//        });
//
//        return null;
//    }
//}
