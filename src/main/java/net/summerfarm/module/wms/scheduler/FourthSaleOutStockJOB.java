package net.summerfarm.module.wms.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.summerfarm.service.StockTaskService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description
 * @Date 2023/3/24 18:40
 * @<AUTHOR>
 */
@Component
public class FourthSaleOutStockJOB extends XianMuJavaProcessor {

    @Resource
    private StockTaskService stockTaskService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        stockTaskService.saleOutStockTask(context.getJobParameters());
        return new ProcessResult(true);
    }
}
