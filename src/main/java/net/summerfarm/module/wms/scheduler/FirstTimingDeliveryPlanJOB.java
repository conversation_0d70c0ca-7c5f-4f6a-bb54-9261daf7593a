package net.summerfarm.module.wms.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.summerfarm.service.MerchantLifecycleService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description
 * @Date 2023/3/22 10:44
 * @<AUTHOR>
 */
@Component
public class FirstTimingDeliveryPlanJOB extends XianMuJavaProcessor {

    @Resource
    private MerchantLifecycleService merchantLifecycleService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        merchantLifecycleService.timingDelivryPlan(context.getJobParameters());
        return new ProcessResult(true);
    }
}
