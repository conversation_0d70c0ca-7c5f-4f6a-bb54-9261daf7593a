package net.summerfarm.module.wms.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.summerfarm.service.TransferService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description 自动拆包定时任务
 * @Date 2023/3/13 10:56
 * @<AUTHOR>
 */
@Component
public class InSkuAutoTransferJOB extends XianMuJavaProcessor {

    @Resource
    private TransferService transferService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        transferService.inSkuAutoTransferTask();
        return new ProcessResult(true);
    }
}
