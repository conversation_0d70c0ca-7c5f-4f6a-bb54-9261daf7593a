package net.summerfarm.module.wms.infrastructure.repository;

import net.summerfarm.model.domain.wms.StockTaskItemCabinetOccupyDO;
import net.summerfarm.model.domain.wms.StockTaskItemCabinetOccupyUpdate;
import net.summerfarm.wms.inventory.resp.cabinetInventory.CabinetInventoryOccupyReduceBatchRespDTO;

import java.util.List;

public interface StockTaskItemCabinetOccupyRepository {

    List<StockTaskItemCabinetOccupyDO> selectListByStockTaskIdOccupyed(Integer stockTaskId, List<String> skuCodeList);

    void updatePickChange(StockTaskItemCabinetOccupyUpdate build);

    /**
     * 释放变更
     * @param id
     * @param releaseChangeQuantity
     */
    void updateReleaseChange(Long id, Integer releaseChangeQuantity);


    void handlerCabinetOccupyItem(List<StockTaskItemCabinetOccupyDO> skuOccupyList, List<CabinetInventoryOccupyReduceBatchRespDTO> occupyReduceBatchRespDTOList);
}
