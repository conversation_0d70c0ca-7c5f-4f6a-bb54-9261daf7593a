package net.summerfarm.module.products.inbound.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.InventoryAdjustPriceTypeEnum;
import net.summerfarm.mapper.manage.AreaSkuMapper;
import net.summerfarm.mapper.manage.SkuPriceTaskRecordMapper;
import net.summerfarm.model.DTO.AreaSkuUpdatePriceDTO;
import net.summerfarm.model.DTO.inventory.PriceTaskRecordIntervalDTO;
import net.summerfarm.model.DTO.inventory.RiseSkuPriceJobConfigDTO;
import net.summerfarm.model.domain.PriceStrategy;
import net.summerfarm.model.domain.SkuPriceTaskRecord;
import net.summerfarm.model.vo.AreaSkuVO;
import net.summerfarm.model.vo.LadderPriceVO;
import net.summerfarm.model.vo.PriceStrategyAuditRecordVO;
import net.summerfarm.module.products.common.enums.PriceTaskRecordStatusEnum;
import net.summerfarm.service.PriceStrategyService;
import net.summerfarm.service.helper.AdjustSkuPriceHelper;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 凌晨执行，回滚sku价格
 *
 * @author: <EMAIL>
 * @create: 2023/12/4
 */
@Slf4j
@Component
public class RollBackSkuPriceJob extends XianMuJavaProcessorV2 {

    @Resource
    private SkuPriceTaskRecordMapper skuPriceTaskRecordMapper;

    @Resource
    private AreaSkuMapper areaSkuMapper;

    @Resource
    private AdjustSkuPriceHelper adjustSkuPriceHelper;

    @Resource
    private PriceStrategyService priceStrategyService;

    @Resource
    private ApplicationContext context;

    private RollBackSkuPriceJob selfService;
    @PostConstruct
    private void setSelf() {
        selfService = context.getBean(RollBackSkuPriceJob.class);
    }

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("【系统夜间自动调价】执行回滚sku价格开始");
        // 查询出所有需要回滚的记录
        LocalDate exeDate = LocalDate.now().minusDays(1L);
        String parameters = context.getInstanceParameters();
        Long startId = null;
        Integer fixedAreaNo = null;
        Integer pageSize = 100;
        Long sleepTime = 0L;
        if (StringUtils.isNotBlank(context.getJobParameters())) {
            RiseSkuPriceJobConfigDTO jobConfigDTO = JSON.parseObject(context.getJobParameters(),
                    RiseSkuPriceJobConfigDTO.class);
            pageSize = jobConfigDTO.getPageSize() == null ? pageSize : jobConfigDTO.getPageSize();
            sleepTime = jobConfigDTO.getSleepTime() == null ? sleepTime : jobConfigDTO.getSleepTime();
        }
        if (StringUtils.isNotBlank(parameters)) {
            RiseSkuPriceJobConfigDTO jobConfigDTO = JSON.parseObject(parameters,
                    RiseSkuPriceJobConfigDTO.class);
            fixedAreaNo = jobConfigDTO.getFixedAreaNo();
            startId = jobConfigDTO.getStartId();
            exeDate = jobConfigDTO.getRollbackDate() == null ? exeDate
                    : jobConfigDTO.getRollbackDate();
            pageSize = jobConfigDTO.getPageSize() == null ? pageSize : jobConfigDTO.getPageSize();
        }
        //fixedAreaNo的优先级高于startId
        if (fixedAreaNo != null) {
            int offset = 0;
            log.info("【系统夜间自动调价】执行回滚sku价格开始,areaNo:{}", fixedAreaNo);
            while (true) {
                if (sleepTime > 0) {
                    Thread.sleep(sleepTime);
                }
                List<SkuPriceTaskRecord> taskRecords = skuPriceTaskRecordMapper.pageByAreaNo(exeDate, fixedAreaNo, offset, pageSize);
                if (CollectionUtil.isEmpty(taskRecords)) {
                    log.info("【系统夜间自动调价】执行回滚sku价格结束,areaNo:{}", fixedAreaNo);
                    break;
                }
                // 过滤掉已经回滚的记录
                taskRecords = taskRecords.stream().filter(x -> !Objects.equals(x.getStatus(), PriceTaskRecordStatusEnum.CALLBACK_SUCCESS.getCode())).collect(
                        Collectors.toList());
                selfService.rollBackPrice(taskRecords);
                offset += pageSize;
            }
        } else {
            PriceTaskRecordIntervalDTO fromAndToId = skuPriceTaskRecordMapper.getFromAndToId(exeDate);
            if (fromAndToId == null) {
                log.warn("【系统夜间自动调价】执行回滚sku价格结束,未获取到数据,exeDate:{}", exeDate);
                return new ProcessResult(true);
            }
            if (startId == null) {
                startId = fromAndToId.getFromId();
            }
            while (startId <= fromAndToId.getToId()) {
                if (sleepTime > 0) {
                    Thread.sleep(sleepTime);
                }
                List<SkuPriceTaskRecord> taskRecords = skuPriceTaskRecordMapper.pageById(exeDate, startId, pageSize);
                startId += pageSize;
                if (CollectionUtil.isEmpty(taskRecords)) {
                    break;
                }
                // 过滤掉已经回滚的记录
                taskRecords = taskRecords.stream().filter(x -> !Objects.equals(x.getStatus(),
                        PriceTaskRecordStatusEnum.CALLBACK_SUCCESS.getCode())).collect(
                        Collectors.toList());
                selfService.rollBackPrice(taskRecords);
                log.info("【系统夜间自动调价】执行回滚sku价格,startId:{}已完成", startId - pageSize);
            }
        }
        log.info("【系统夜间自动调价】执行回滚sku价格结束");
        return new ProcessResult(true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void rollBackPrice(List<SkuPriceTaskRecord> taskRecords) {
        if (CollectionUtil.isEmpty(taskRecords)) {
            return;
        }
        List<AreaSkuUpdatePriceDTO> priceList = Lists.newArrayList();
        List<SkuPriceTaskRecord> rollbackRecords = Lists.newArrayList();
        for (SkuPriceTaskRecord taskRecord : taskRecords) {
            String sku = taskRecord.getSku();
            Integer areaNo = taskRecord.getAreaNo();
            try {
                //只有上调执行成功或者回滚失败的才能执行回滚
                if (!Objects.equals(taskRecord.getStatus(), PriceTaskRecordStatusEnum.RISE_SUCCESS.getCode()) && !Objects.equals(
                        taskRecord.getStatus(), PriceTaskRecordStatusEnum.CALLBACK_FAIL.getCode())) {
                    taskRecord.setStatus(PriceTaskRecordStatusEnum.CALLBACK_FAIL.getCode());
                    taskRecord.setReason("非上调成功、回滚失败状态");
                    skuPriceTaskRecordMapper.updateByPrimaryKeySelective(taskRecord);
                    continue;
                }
                taskRecord.setStatus(PriceTaskRecordStatusEnum.CALLBACK.getCode());
                skuPriceTaskRecordMapper.updateByPrimaryKeySelective(taskRecord);
                AreaSkuVO areaSkuVO = areaSkuMapper.selectBySkuAndAreaNo(sku, areaNo);
                areaSkuVO.setPrice(taskRecord.getOldPrice());
                AreaSkuUpdatePriceDTO priceDTO = new AreaSkuUpdatePriceDTO();
                priceDTO.setId(areaSkuVO.getId());
                priceDTO.setOriginalPrice(taskRecord.getNewPrice());
                priceDTO.setNewPrice(taskRecord.getOldPrice());
                priceDTO.setUpdater("夜间自动调价-回滚");
                // 不再计算阶梯价
                /*String ladderPrice = areaSkuVO.getLadderPrice();
                if (StringUtils.isNotBlank(ladderPrice) && !"[]".equals(ladderPrice)) {
                    List<LadderPriceVO> ladderPriceVOS = JSON.parseArray(ladderPrice, LadderPriceVO.class);
                    for (LadderPriceVO ladderPriceVO : ladderPriceVOS) {
                        if (ladderPriceVO.getAdjustType() == null
                                || ladderPriceVO.getAmount() == null) {
                            continue;
                        }
                        //只处理定额减的阶梯价
                        if (!InventoryAdjustPriceTypeEnum.QUOTA_REDUCTION.getType().equals(ladderPriceVO.getAdjustType())) {
                            continue;
                        }
                        PriceStrategy strategy = new PriceStrategy();
                        strategy.setAdjustType(ladderPriceVO.getAdjustType());
                        strategy.setAmount(ladderPriceVO.getAmount());
                        strategy.setRoundingMode(ladderPriceVO.getRoundingMode());
                        PriceStrategyAuditRecordVO vo = priceStrategyService.calcStrategyPrice(strategy, null, areaSkuVO.getPrice());
                        BigDecimal resultPrice = vo.getNewPrice();
                        ladderPriceVO.setPrice(resultPrice);
                    }
                    priceDTO.setLadderPrice(JSON.toJSONString(ladderPriceVOS));
                }*/
                priceList.add(priceDTO);
                rollbackRecords.add(taskRecord);
            } catch (Exception e) {
                log.warn("【系统夜间自动调价】回滚sku价格异常,taskRecord:{},cause:{}", taskRecord, Throwables.getStackTraceAsString(e));
                taskRecord.setStatus(PriceTaskRecordStatusEnum.CALLBACK_FAIL.getCode());
                skuPriceTaskRecordMapper.updateByPrimaryKeySelective(taskRecord);
            }

        }
        batchUpdateStatus(priceList, rollbackRecords);
    }

    public void batchUpdateStatus(List<AreaSkuUpdatePriceDTO> priceList, List<SkuPriceTaskRecord> rollbackRecords) {
        List<Boolean> resList = adjustSkuPriceHelper.dealPriceAdjustment(priceList);
        List<SkuPriceTaskRecord> successList = new ArrayList<>();
        List<SkuPriceTaskRecord> failList = new ArrayList<>();
        for (int i = 0; i < resList.size(); i++) {
            if (resList.get(i)) {
                successList.add(rollbackRecords.get(i));
            } else {
                failList.add(rollbackRecords.get(i));
            }
        }
        if (CollectionUtil.isNotEmpty(successList)) {
            skuPriceTaskRecordMapper.batchUpdateStatusById(successList, PriceTaskRecordStatusEnum.CALLBACK_SUCCESS.getCode());
        }
        if (CollectionUtil.isNotEmpty(failList)) {
            skuPriceTaskRecordMapper.batchUpdateStatusById(failList, PriceTaskRecordStatusEnum.CALLBACK_FAIL.getCode());
        }
    }
}
