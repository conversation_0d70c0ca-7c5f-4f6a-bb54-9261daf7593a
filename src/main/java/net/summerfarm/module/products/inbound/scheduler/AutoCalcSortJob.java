package net.summerfarm.module.products.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.AreaSkuService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * @Author: zhuyantao
 * @date: 2023/3/10 2:53 下午
 * @description 自动更新排序值
 * cron 00 00 01 * * ?
 */
@Component
@Slf4j
public class AutoCalcSortJob extends XianMuJavaProcessor {

    @Resource
    private AreaSkuService areaSkuService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("自动更新排序值areaSkuService.autoCalcSort start :{}", LocalDate.now());
        areaSkuService.autoCalcSort();
        log.info("自动更新排序值 end :{}", LocalDate.now());
        return new ProcessResult(true);
    }
}
