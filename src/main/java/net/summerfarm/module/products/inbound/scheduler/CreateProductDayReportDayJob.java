package net.summerfarm.module.products.inbound.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.ProductsService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Objects;

/**
 * @Author: zhuyantao
 * @date: 2023/3/10 3:04 下午
 * @description 上新审核日提醒
 * 0 0 9 * * ?
 */
@Component
@Slf4j
public class CreateProductDayReportDayJob extends XianMuJavaProcessor {

    @Resource
    private ProductsService productsService;

    private static final Integer HOUR = 12;

    private static final String PARAM_NAME = "hour";

    /**
     * @param context hour = 12
     * @return
     * @throws Exception
     */
    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        Integer paramHour = null;
        log.info("上新审核日提醒Job param:{}",context.getJobParameters());
        try {
            JSONObject jsonObject = JSON.parseObject(context.getJobParameters());
            paramHour = jsonObject.getInteger(PARAM_NAME);
        } catch (NumberFormatException e) {
            log.error("Exception CreateProductDayReportDayJob param:{}", context.getJobParameters(), e);
        }
        if (Objects.isNull(paramHour)) {
            paramHour = HOUR;
        }
        log.info("上新审核日提醒 end :{},param:{}", LocalDate.now(),paramHour);
        productsService.createProductReport(paramHour);
        log.info("上新审核日提醒 end :{}", LocalDate.now());
        return new ProcessResult(true);
    }
}
