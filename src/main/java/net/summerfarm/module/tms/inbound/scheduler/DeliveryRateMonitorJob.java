package net.summerfarm.module.tms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.CompleteDeliveryService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description:配送及时率预警播报
 * date: 2023/3/22 11:07
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeliveryRateMonitorJob extends XianMuJavaProcessor {

    @Resource
    private CompleteDeliveryService completeDeliveryService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("配送及时率预警播报任务开始");
        completeDeliveryService.sendCompleteDelivery();
        return new ProcessResult(true);
    }
}
