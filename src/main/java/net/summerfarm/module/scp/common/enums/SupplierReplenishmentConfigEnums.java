package net.summerfarm.module.scp.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public interface SupplierReplenishmentConfigEnums {

    /**
     * 不定期补货模式
     */
    List<ReplenishmentMode> UNSCHEDULED_REPLENISHMENT = Lists.newArrayList(ReplenishmentMode.IRREGULAR_INDEFINITE, ReplenishmentMode.IRREGULAR_QUANTITATIVE);

    @Getter
    @AllArgsConstructor
    enum ReplenishmentMode implements Enum2Args {
        /**
         * 补货模式 1:不定期不定量,2:定期不定量,3:不定期定量,4:定期定量
         */
        IRREGULAR_INDEFINITE(1, "不定期不定量"),
        REGULAR_INDEFINITE(2, "定期不定量"),
        IRREGULAR_QUANTITATIVE(3, "不定期定量"),
        REGULAR_QUANTITATIVE(4, "定期定量"),
        ;

        private Integer value;
        private String content;

        public static String getNameByCode(Integer code) {
            ReplenishmentMode[] values = values();
            for (ReplenishmentMode value : values) {
                if (value.getValue().equals(code)) {
                    return value.getContent();
                }
            }
            return null;
        }

        /**
         * 补货模式包装
         *
         * @param code 编码
         * @return 模式
         */
        public static ReplenishmentMode wrap(Integer code) {
            return code == null ? null : Arrays.stream(ReplenishmentMode.values())
                    .filter(rm -> Objects.equals(code, rm.getValue()))
                    .findFirst()
                    .orElse(null);
        }

        /**
         * 判断当前补货模式是否为不定期
         *
         * @param replenishmentMode 补货模式
         * @return 是/否
         */
        public static boolean isUnscheduled(ReplenishmentMode replenishmentMode) {
            return replenishmentMode != null && UNSCHEDULED_REPLENISHMENT.contains(replenishmentMode);
        }
    }
}
