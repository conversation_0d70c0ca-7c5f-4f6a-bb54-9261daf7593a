package net.summerfarm.module.scp.model.input;


import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AllocationPlanAddDTO {

    /**
     * 是否草稿
     */
    private Boolean draft;

    /**
     * 预计出库时间
     */
    private LocalDateTime expectOutTime;

    /**
     * 主键
     */
    private Long id;

    /**
     * 入库仓库
     */
    private Integer inStore;

    /**
     * 出库仓库
     */
    private Integer outStore;

    /**
     * 详情
     */
    private List<AllocationPlanDetailAddDTO> planDetailAddDTOList;

    /**
     * 是否采购参与
     */
    private Integer purchasePartake;

    /**
     * 是否销售参与
     */
    private Integer salePartake;

    /**
     * 温区拆分规则
     * @see net.summerfarm.module.pms.common.enums.AllocationOrderEnums
     */
    private List<Integer> temperatureSplitRule;

    /**
     * 是否干线调度(0:否 1:是)
     * @see net.summerfarm.module.scp.common.enums.AllocationPlanEnums.TrunkFlag
     */
    private Integer trunkFlag;
}
