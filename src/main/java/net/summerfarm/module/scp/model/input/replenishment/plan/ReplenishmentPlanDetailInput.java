package net.summerfarm.module.scp.model.input.replenishment.plan;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class ReplenishmentPlanDetailInput {

    /**
     * 补货计划ids
     */
    private List<String> replenishmentPlanIds;


    /**
     * 补货计划id
     */
    private String replenishmentPlanId;


    /**
     * sku
     */
    private String sku;


    /**
     * 多sku
     */
    private List<String> skus;


    /**
     * 补货计划子单号
     */
    private String replenishmentPlanSubNo;


    /**
     * 多补货子单号
     */
    private List<String> replenishmentPlanSubNos;


    /**
     * 补货计划单号
     */
    private String replenishmentPlanNo;


    /**
     * 多计划单号
     */
    private List<String> replenishmentPlanNos;


    /**
     * 日期
     */
    private LocalDate viewDate;
}
