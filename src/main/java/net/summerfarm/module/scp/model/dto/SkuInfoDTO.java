package net.summerfarm.module.scp.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SkuInfoDTO implements Serializable {

    private static final long serialVersionUID = 2159429295836357434L;


    /**
     * sku编码
     */
    private String sku;
    /**
     * 商品id
     */
    private Long pdId;

    /**
     * 商品编码
     */
    private String pdNo;
    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 图片
     */
    private String picturePath;

    /**
     * 规格
     */
    private String weight;


    /**
     * 体积
     */
    private String volume;

    /**
     * 重量
     */
    private BigDecimal weightNum;

    /**
     * 温区
     */
    private Integer storageArea;


    /**
     * 品牌
     */
    private String brand;
}
