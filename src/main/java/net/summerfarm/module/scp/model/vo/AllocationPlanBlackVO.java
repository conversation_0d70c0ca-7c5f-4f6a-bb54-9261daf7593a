package net.summerfarm.module.scp.model.vo;

import lombok.Data;
import net.summerfarm.model.DTO.WarehouseDTO;
import net.summerfarm.model.DTO.purchase.ProductWarehouseConfigDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Date 2022/10/24 14:11
 */

@Data
public class AllocationPlanBlackVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 规格
     */
    private String weight;

    /**
     * sku
     */
    private String sku;

    /**
     * 仓库信息
     */
    private List<WarehouseDTO> warehouseDTOList;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 图片
     */
    private String picturePath;

}
