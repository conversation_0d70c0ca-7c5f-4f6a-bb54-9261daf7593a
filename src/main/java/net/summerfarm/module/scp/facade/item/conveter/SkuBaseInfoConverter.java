package net.summerfarm.module.scp.facade.item.conveter;


import net.summerfarm.common.util.StringUtils;
import net.summerfarm.goods.client.enums.StorageLocationEnum;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.module.scp.model.dto.SkuInfoDTO;


import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class SkuBaseInfoConverter {


    private SkuBaseInfoConverter() {
        // 无需实现
    }

    public static List<SkuInfoDTO> toSkuBaseInfoDTOList(List<ProductSkuDetailResp> productSkuDetailRespList, Boolean mainPic) {
        if (productSkuDetailRespList == null) {
            return Collections.emptyList();
        }
        List<SkuInfoDTO> skuInfoDTOList = new ArrayList<>();
        for (ProductSkuDetailResp productSkuDetailResp : productSkuDetailRespList) {
            skuInfoDTOList.add(toSkuBaseInfoDTO(productSkuDetailResp, mainPic));
        }
        return skuInfoDTOList;
    }

    public static SkuInfoDTO toSkuBaseInfoDTO(ProductSkuDetailResp productSkuDetailResp, Boolean mainPic) {
        if (productSkuDetailResp == null) {
            return null;
        }
        SkuInfoDTO skuInfoDTO = new SkuInfoDTO();
        skuInfoDTO.setSku(productSkuDetailResp.getSku());
        skuInfoDTO.setVolume(productSkuDetailResp.getVolume());
        skuInfoDTO.setBrand(productSkuDetailResp.getBrandName());
        skuInfoDTO.setPdId(productSkuDetailResp.getSkuMapping().getAgentSpuId());
        skuInfoDTO.setPdNo(productSkuDetailResp.getSkuMapping().getSpu());
        skuInfoDTO.setPdName(productSkuDetailResp.getTitle());
        skuInfoDTO.setWeight(productSkuDetailResp.getSpecification());
        if (mainPic != null && mainPic) {
            skuInfoDTO.setPicturePath(productSkuDetailResp.getMainPicture());
        } else {
            skuInfoDTO.setPicturePath(StringUtils.isBlank(productSkuDetailResp.getSkuPicture()) ? productSkuDetailResp.getMainPicture() : productSkuDetailResp.getSkuPicture());
        }
        skuInfoDTO.setStorageArea(StorageLocationEnum.saas2Xm(productSkuDetailResp.getStorageLocation()));
        skuInfoDTO.setWeightNum(productSkuDetailResp.getWeight());
        return skuInfoDTO;
    }


}
