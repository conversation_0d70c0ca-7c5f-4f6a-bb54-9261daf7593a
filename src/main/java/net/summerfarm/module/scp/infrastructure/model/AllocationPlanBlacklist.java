package net.summerfarm.module.scp.infrastructure.model;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AllocationPlanBlacklist {
    private Long id;

    private Long pdId;

    private String pdName;

    private String sku;

    private Integer creatorId;

    private String creatorName;

    private Integer updaterId;

    private String updaterName;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String weight;

    private Boolean isDelete;

}