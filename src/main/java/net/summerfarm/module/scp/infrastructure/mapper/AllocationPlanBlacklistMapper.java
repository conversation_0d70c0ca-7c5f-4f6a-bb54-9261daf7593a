package net.summerfarm.module.scp.infrastructure.mapper;

import net.summerfarm.module.scp.infrastructure.model.AllocationPlanBlacklist;
import net.summerfarm.module.scp.model.vo.AllocationPlanBlackVO;

import java.util.List;

public interface AllocationPlanBlacklistMapper {

    int insertSelective(AllocationPlanBlacklist record);

    AllocationPlanBlacklist selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AllocationPlanBlacklist record);

    int updateByPrimaryKey(AllocationPlanBlacklist record);

    List<AllocationPlanBlackVO> queryAllByPdNameAndSkuAndWarehouseNo(String pdName, String sku, List<Integer> list);


}