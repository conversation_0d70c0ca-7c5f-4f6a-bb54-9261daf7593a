package net.summerfarm.module.crm.inbound.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mapper.manage.MajorPriceMapper;
import net.summerfarm.model.DTO.inventory.MajorPriceAutoFixJobParameterDTO;
import net.summerfarm.model.vo.MajorPriceAutoUpdateVO;
import net.summerfarm.service.MajorPriceService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 成本倒挂的报价单补偿任务
 * @author: <EMAIL>
 * @create: 2024/3/7
 */
@Component
@Slf4j
public class MajorPriceAutoFixJob extends XianMuJavaProcessor {

    @Resource
    private MajorPriceService majorPriceService;

    @Resource
    private MajorPriceMapper majorPriceMapper;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        MajorPriceAutoFixJobParameterDTO dto = new MajorPriceAutoFixJobParameterDTO();
        Integer offset = 0;
        Integer limit = 500;
        if (StringUtils.isNotBlank(context.getJobParameters())) {
            dto = JSON.parseObject(context.getJobParameters(), MajorPriceAutoFixJobParameterDTO.class);
        }
        if (dto == null) {
            log.warn("【成本倒挂的报价单补偿任务】参数为空");
            return new ProcessResult(true);
        }
        offset = dto.getOffset() == null ? offset : dto.getOffset();
        limit = dto.getLimit() == null ? limit : dto.getLimit();
        while (true) {
            //获取所有毛利率形式且生效中的报价单
            List<MajorPriceAutoUpdateVO> list = majorPriceMapper.listAllGrossProfitMajorPrice(dto.getMajorCycle(), offset, limit);
            if (CollectionUtil.isEmpty(list)) {
                log.info("【成本倒挂的报价单补偿任务】已经没有需要处理的报价单");
                break;
            }
            log.info("【成本倒挂的报价单补偿任务】开始处理报价单补偿任务,majorCycle:{},共{}条", dto.getMajorCycle(), list.size());
            for (MajorPriceAutoUpdateVO majorPriceAutoUpdateVO : list) {
                majorPriceService.autoChangeMajorPrice(majorPriceAutoUpdateVO);
            }
            offset = offset + limit;
        }
        return new ProcessResult(true);
    }
}
