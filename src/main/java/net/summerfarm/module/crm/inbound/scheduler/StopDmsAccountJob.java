package net.summerfarm.module.crm.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.impl.MerchantServiceImpl;
import net.summerfarm.service.impl.RamTaskMessageImpl;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
/**
 * crm-dms账号关闭
 */
public class StopDmsAccountJob extends XianMuJavaProcessor {
    @Resource
    RamTaskMessageImpl ramTaskMessageImpl;
    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("StopDmsAccountJob 任务调度成功！");
        ramTaskMessageImpl.scanTask();
        return new ProcessResult(true);
    }
}
