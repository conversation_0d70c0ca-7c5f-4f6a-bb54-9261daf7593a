package net.summerfarm.module.crm.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.ChangeRecordService;
import net.summerfarm.service.DingTalkService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

@Component
@Slf4j
public class CommissionChangeJob extends XianMuJavaProcessor {

    @Resource
    private ChangeRecordService changeRecordService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("CommissionChangeJob 任务调度成功！");
        changeRecordService.queryRecord();
        return new ProcessResult(true);
    }
}
