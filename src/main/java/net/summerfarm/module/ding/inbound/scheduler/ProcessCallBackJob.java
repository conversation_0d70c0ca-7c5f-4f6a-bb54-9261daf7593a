package net.summerfarm.module.ding.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.task.ProcessCallBackService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

@Component
@Slf4j
public class ProcessCallBackJob extends XianMuJavaProcessor {

    @Resource
    private ProcessCallBackService processCallBackService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("拉取钉钉回调失败消息任务执行:{}", LocalDate.now());
        processCallBackService.processCallBackPullTask();
        return new ProcessResult(true);
    }
}
