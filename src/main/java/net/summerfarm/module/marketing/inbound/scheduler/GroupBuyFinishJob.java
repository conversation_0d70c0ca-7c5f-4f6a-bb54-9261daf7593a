package net.summerfarm.module.marketing.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.GroupBuyService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * @Author: zhu<PERSON>tao
 * @date: 2023/3/10 4:15 下午
 * @description 团购成团任务
 * net.summerfarm.module.marketing.inbound.scheduler.GroupBuyFinishJob
 */
@Component
@Slf4j
public class GroupBuyFinishJob extends XianMuJavaProcessor {
    @Resource
    private GroupBuyService groupBuyService;

    //0 0 */1 * * ?
    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("团购成团任务groupBuyService.groupBuyFinish end :{}", LocalDate.now());
        groupBuyService.groupBuyFinish();
        log.info("团购成团任务groupBuyService.groupBuyFinish end :{}", LocalDate.now());
        return new ProcessResult(true);

    }
}
