package net.summerfarm.module.marketing.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.CirclePeopleNewService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * @Author: zhuyantao
 * @date: 2023/3/10 4:07 下午
 * @description 圈人包自动修改net.summerfarm.module.marketing.inbound.scheduler.CirclePeopleUpdateAllPakDataJob
 * 0 0 5-17 * * ?
 */
@Component
@Slf4j
public class CirclePeopleUpdateAllPakDataJob extends XianMuJavaProcessor {
    @Resource
    private CirclePeopleNewService circlePeopleNewService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("圈人包自动修改circlePeopleNewService.updateAllPakData end :{}", LocalDate.now());
        circlePeopleNewService.updateAllPakData();
        log.info("圈人包自动修改 end :{}", LocalDate.now());
        return new ProcessResult(true);
    }
}
