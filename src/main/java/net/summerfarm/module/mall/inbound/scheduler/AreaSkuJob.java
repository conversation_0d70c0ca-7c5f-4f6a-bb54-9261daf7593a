package net.summerfarm.module.mall.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.model.domain.OnSaleAdjustment;
import net.summerfarm.service.OnSaleAdjustmentService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/5  17:38
 */
@Component
@Slf4j
public class AreaSkuJob extends XianMuJavaProcessor {
    @Resource
    private OnSaleAdjustmentService onSaleAdjustmentService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        //处理上架
        List<OnSaleAdjustment> openSaleList = onSaleAdjustmentService.queryListByStatusAndUpTime(1, 0, LocalDateTime.now());
        if (!CollectionUtils.isEmpty(openSaleList)) {
            for (OnSaleAdjustment onSaleAdjustment : openSaleList) {
                onSaleAdjustmentService.execute(onSaleAdjustment.getId());
            }
        }

        //处理下架
        List<OnSaleAdjustment> cloesSaleList = onSaleAdjustmentService.queryListByStatusAndUpTime(0, 0, LocalDateTime.now());
        if (!CollectionUtils.isEmpty(cloesSaleList)) {
            for (OnSaleAdjustment onSaleAdjustment : cloesSaleList) {
                onSaleAdjustmentService.execute(onSaleAdjustment.getId());
            }
        }

        return new ProcessResult(true);
    }
}
