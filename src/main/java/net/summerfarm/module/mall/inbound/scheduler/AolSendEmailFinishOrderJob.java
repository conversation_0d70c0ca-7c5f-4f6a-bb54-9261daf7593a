package net.summerfarm.module.mall.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.OrderOuterInfoService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * AOL订单-配送完成邮件
 * <AUTHOR>
 */
@Component
@Slf4j
public class AolSendEmailFinishOrderJob extends XianMuJavaProcessor {

    @Autowired
    private OrderOuterInfoService orderOuterInfoService;


    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("AOL订单-配送完成邮件 start :{}", LocalDate.now());
        orderOuterInfoService.sendEmailAOLFinish();
        log.info("AOL订单-配送完成邮件 end :{}", LocalDate.now());
        return new ProcessResult(true);
    }
}
