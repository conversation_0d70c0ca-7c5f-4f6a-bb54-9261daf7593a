package net.summerfarm.module.bms.facade;

import net.summerfarm.module.pms.common.util.RpcInvokeTemplate;
import net.xianmu.bms.client.provider.adjust.CostAdjustProcessProvider;
import net.xianmu.bms.client.provider.adjust.PayeeAdjustProcessProvider;
import net.xianmu.bms.client.provider.adjust.PayeeAdjustQueryProvider;
import net.xianmu.bms.client.provider.fms.payment.FinancePaymentCommandProvider;
import net.xianmu.bms.client.provider.payment.PaymentDocQueryProvider;
import net.xianmu.bms.client.provider.payment.PaymentProcessProvider;
import net.xianmu.bms.client.req.adjust.CostAdjustProcessRequest;
import net.xianmu.bms.client.req.adjust.PayeeAdjustProcessRequest;
import net.xianmu.bms.client.req.adjust.PayeeAdjustQueryRequest;
import net.xianmu.bms.client.req.fms.payment.FinancePaymentUpsertRequest;
import net.xianmu.bms.client.req.payment.PaymentDocQueryRequest;
import net.xianmu.bms.client.req.recon.ProcessCallBackRequest;
import net.xianmu.bms.client.resp.adjust.PayeeAdjustResponse;
import net.xianmu.bms.client.resp.fms.payment.FinancePaymentUpsertResponse;
import net.xianmu.bms.client.resp.payment.PaymentDocDetailResponse;
import net.xianmu.bms.client.resp.recon.ProcessCallBackResponse;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/6 16:02
 */
@Component
public class BmsServiceClientFacade {

    @DubboReference
    private PayeeAdjustQueryProvider payeeAdjustQueryProvider;

    @DubboReference
    private CostAdjustProcessProvider costAdjustProcessProvider;

    @DubboReference
    private PayeeAdjustProcessProvider payeeAdjustProcessProvider;

    @DubboReference
    private PaymentProcessProvider paymentProcessProvider;

    @DubboReference
    private PaymentDocQueryProvider paymentDocQueryProvider;

    @DubboReference
    private FinancePaymentCommandProvider financePaymentCommandProvider;


    /**
     * 创建付款单
     *
     * @param fmsFinancePaymentDTO
     * @return
     */
    public Long createFmsPaymentOrder(FmsFinancePaymentDTO fmsFinancePaymentDTO) {

        FinancePaymentUpsertRequest request = new FinancePaymentUpsertRequest();
        request.setAdditionalId(fmsFinancePaymentDTO.getAdditionalId());
        request.setType(fmsFinancePaymentDTO.getType());
        request.setAmount(fmsFinancePaymentDTO.getAmount());
        request.setStatus(fmsFinancePaymentDTO.getStatus());
        request.setOperateName(fmsFinancePaymentDTO.getOperateName());
        request.setOperateId(fmsFinancePaymentDTO.getOperateId());
        request.setPayeeId(fmsFinancePaymentDTO.getPayeeId());
        request.setPayeeName(fmsFinancePaymentDTO.getPayeeName());
        request.setUpdater(fmsFinancePaymentDTO.getUpdater());
        request.setApproveFormFeatureMap(fmsFinancePaymentDTO.getApproveFeatureMap());
        if (fmsFinancePaymentDTO.getFinancePaymentAccountFeatureRequest() != null) {
            request.setFinancePaymentAccountFeatureRequest(fmsFinancePaymentDTO.getFinancePaymentAccountFeatureRequest());
        }
        if(fmsFinancePaymentDTO.getFinancePaymentOrderFeatureRequest()!=null){
            request.setFinancePaymentOrderFeatureRequest(fmsFinancePaymentDTO.getFinancePaymentOrderFeatureRequest());
        }
        DubboResponse<FinancePaymentUpsertResponse> paymentOrder = financePaymentCommandProvider.createAdvancePaymentOrder(request);
        if (Objects.nonNull(paymentOrder) && paymentOrder.isSuccess() && Objects.nonNull(paymentOrder.getData())) {
            return paymentOrder.getData().getId();
        }
        throw new ProviderException("创建付款单失败");
    }


    public void refuseByPay(ProcessCallBackRequest request) {
        DubboResponse<ProcessCallBackResponse> processCallBackResponseDubboResponse = paymentProcessProvider.refuseByFmsPay(request);
        if (!processCallBackResponseDubboResponse.isSuccess()) {
            throw new ProviderException("打款单付款失败状态变更失败");
        }
    }

    /**
     * 查询打款单详情
     *
     * @param paymentDocQueryRequest
     * @return
     */
    public PaymentDocDetailResponse queryPaymentDocDetail(PaymentDocQueryRequest paymentDocQueryRequest) {
        DubboResponse<PaymentDocDetailResponse> paymentDocDetailResponseDubboResponse = paymentDocQueryProvider.queryPaymentDetail(paymentDocQueryRequest);
        if (!paymentDocDetailResponseDubboResponse.isSuccess()) {
            throw new ProviderException("查询失败");
        }

        return paymentDocDetailResponseDubboResponse.getData();
    }


    public void terminateByCommitProcess(ProcessCallBackRequest request) {
        DubboResponse<ProcessCallBackResponse> processCallBackResponseDubboResponse = paymentProcessProvider.terminateByCommitProcess(request);
        if (!processCallBackResponseDubboResponse.isSuccess()) {
            throw new ProviderException("提交打款撤销操作失败");
        }
    }

    public void refuseByCommitProcess(ProcessCallBackRequest request) {
        DubboResponse<ProcessCallBackResponse> processCallBackResponseDubboResponse = paymentProcessProvider.refuseByCommitProcess(request);
        if (!processCallBackResponseDubboResponse.isSuccess()) {
            throw new ProviderException("提交打款拒绝操作失败");
        }
    }

    public void agreeByCommitProcess(ProcessCallBackRequest request) {
        DubboResponse<ProcessCallBackResponse> processCallBackResponseDubboResponse = paymentProcessProvider.agreeByCommitProcess(request);
        if (!processCallBackResponseDubboResponse.isSuccess()) {
            throw new ProviderException("提交打款同意操作失败");
        }
    }

    public void terminateByCreateProcess(ProcessCallBackRequest request) {
        DubboResponse<ProcessCallBackResponse> processCallBackResponseDubboResponse = paymentProcessProvider.terminateByCreateProcess(request);
        if (!processCallBackResponseDubboResponse.isSuccess()) {
            throw new ProviderException("发起打款撤销操作失败");
        }
    }

    public void refuseByCreateProcess(ProcessCallBackRequest request) {
        DubboResponse<ProcessCallBackResponse> processCallBackResponseDubboResponse = paymentProcessProvider.refuseByCreateProcess(request);
        if (!processCallBackResponseDubboResponse.isSuccess()) {
            throw new ProviderException("发起打款拒绝操作失败");
        }
    }

    public void agreeByCreateProcess(ProcessCallBackRequest request) {
        DubboResponse<ProcessCallBackResponse> processCallBackResponseDubboResponse = paymentProcessProvider.agreeByCreateProcess(request);
        if (!processCallBackResponseDubboResponse.isSuccess()) {
            throw new ProviderException("发起打款同意操作失败");
        }
    }


    public void agree4CostAdjust(CostAdjustProcessRequest request) {
        RpcInvokeTemplate.invoke(() -> costAdjustProcessProvider.agree(request), "net.xianmu.bms.client.provider.adjust.CostAdjustProcessProvider.agree", request);
    }

    public void refuse4CostAdjust(CostAdjustProcessRequest request) {
        RpcInvokeTemplate.invoke(() -> costAdjustProcessProvider.refuse(request), "net.xianmu.bms.client.provider.adjust.CostAdjustProcessProvider.refuse", request);
    }

    public void terminate4CostAdjust(CostAdjustProcessRequest request) {
        RpcInvokeTemplate.invoke(() -> costAdjustProcessProvider.terminate(request), "net.xianmu.bms.client.provider.adjust.CostAdjustProcessProvider.terminate", request);
    }


    public void agreeByPayeeAdjust(PayeeAdjustProcessRequest request) {
        RpcInvokeTemplate.invoke(() -> payeeAdjustProcessProvider.agree(request), "net.xianmu.bms.client.provider.adjust.PayeeAdjustProcessProvider.agree", request);
    }

    public void refuseByPayeeAdjust(PayeeAdjustProcessRequest request) {
        RpcInvokeTemplate.invoke(() -> payeeAdjustProcessProvider.refuse(request), "net.xianmu.bms.client.provider.adjust.PayeeAdjustProcessProvider.refuse", request);
    }

    public void terminateByPayeeAdjust(PayeeAdjustProcessRequest request) {
        RpcInvokeTemplate.invoke(() -> payeeAdjustProcessProvider.terminate(request), "net.xianmu.bms.client.provider.adjust.PayeeAdjustProcessProvider.terminate", request);
    }


    public List<PayeeAdjustResponse> queryPayeeAdjust(PayeeAdjustQueryRequest request) {
        DubboResponse<List<PayeeAdjustResponse>> invoke = RpcInvokeTemplate.invoke(() -> payeeAdjustQueryProvider.queryPayeeAdjustList(request), "net.xianmu.bms.client.provider.adjust.PayeeAdjustQueryProvider.queryPayeeAdjustList", request);

        return invoke.getData();
    }

}
