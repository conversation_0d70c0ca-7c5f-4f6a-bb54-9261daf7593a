package net.summerfarm.module.bms.facade.convert;

import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.model.domain.Admin;
import net.xianmu.bms.client.req.adjust.CostAdjustProcessRequest;
import net.xianmu.bms.client.req.adjust.PayeeAdjustProcessRequest;
import net.xianmu.bms.client.req.adjust.PayeeAdjustQueryRequest;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/6 16:07
 */
public class BmsFacadeConvert {

    public static PayeeAdjustQueryRequest convertByApprovingPayeeQuery(List<Long> idList, String sourceType, String businessType) {
        PayeeAdjustQueryRequest request = new PayeeAdjustQueryRequest();
        request.setSourceIdList(idList);
        request.setSourceType(sourceType);
        request.setStatus("APPROVING");
        request.setBusinessType(businessType);

        return request;
    }


    public static CostAdjustProcessRequest convertByCostAdjust(DingdingResultBO result, Integer adminId, Admin adminVO) {
        CostAdjustProcessRequest request = new CostAdjustProcessRequest();
        request.setBizId(result.getBizId());
        request.setBizType(result.getBizType());

        request.setRemark(result.getRemark());
        request.setOperatorId(adminId);
        if (Objects.nonNull(adminVO)) {
            request.setOperatorName(adminVO.getRealname());
        }
        return request;
    }


    public static PayeeAdjustProcessRequest convertByPayeeAdjust(DingdingResultBO result, Integer adminId, Admin adminVO) {
        PayeeAdjustProcessRequest request = new PayeeAdjustProcessRequest();
        request.setBizId(result.getBizId());
        request.setRemark(result.getRemark());
        request.setOperatorId(adminId);
        if (Objects.nonNull(adminVO)) {
            request.setOperatorName(adminVO.getRealname());
        }

        return request;
    }

}
