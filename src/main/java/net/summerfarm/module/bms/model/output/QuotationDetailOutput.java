package net.summerfarm.module.bms.model.output;

import lombok.Data;
import net.summerfarm.module.bms.common.constant.QuotationEnum;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/17 14:48
 */
@Data
public class QuotationDetailOutput implements Serializable {

    /**
     * 承运商
     */
    @Deprecated
    private String carrierName;

    /**
     * 服务区域
     */
    private String serviceArea;

    private Long serviceAreaId;

    /**
     * 城配仓
     */
    @Deprecated
    private String storeName;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private List<String> districts;

    /**
     * 内区 外区
     */
    private Integer quotaType;

    /**
     * 择优报价
     */
    private Integer quotaForm;

    /**
     * 报价单主键
     */
    private Long id;

    /**
     * 报价方
     */
    private String bidderName;

    /**
     * 报价对象
     */
    private String quotaTargetName;

    /**
     * 计费项
     */
    private List<QuotationCalcItemOutput> quotationDetails;

    /**
     * 报价详情
     */
    private List<QuotationCalcFormulaOutput> quoteCalculateCosts;

    /**
     * 业务类型
     * @see QuotationEnum.BusinessType
     */
    private String businessType;

    private Integer status;


}
