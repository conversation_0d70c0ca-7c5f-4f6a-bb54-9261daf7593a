package net.summerfarm.module.bms.model.output.wallet;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/7/7 18:35
 */
@Data
public class WalletsEstimatePayOutput implements Serializable {

    /**
     * 预计付款时间
     */
    private LocalDate payDate;

    /**
     * 预计付款金额
     */
    private BigDecimal amount;


}
