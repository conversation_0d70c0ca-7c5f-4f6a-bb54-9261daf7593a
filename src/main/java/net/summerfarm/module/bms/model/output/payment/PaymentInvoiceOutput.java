package net.summerfarm.module.bms.model.output.payment;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/22 17:25
 */
@Data
public class PaymentInvoiceOutput implements Serializable {

    /**
     * 发票主键
     */
    private Integer id;

    /**
     * 开票日期
     */
    private LocalDate billingDate;

    /**
     * 发票号码
     */
    private String invoiceNumber;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 不含税金额
     */
    private BigDecimal excludingTax;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 税率
     */
    private Integer taxRate;

    /**
     * 含税金额
     */
    private BigDecimal includedTax;

    /**
     * 蓝色 /红色
     */
    private Integer invoiceTypeFace;

    /**
     * 普票 专票
     */
    private Integer invoiceType;

    /**
     * 电子 / 纸质
     */
    private Integer invoiceForm;

    private String creator;

    private List<String> photoList;

    private Integer purchaseInvoiceId;

    private String supplierName;

    private Integer type;
}
