package net.summerfarm.module.bms.model.check;

import com.google.common.base.Preconditions;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.model.input.payment.PaymentDocCancelInput;
import net.summerfarm.module.bms.model.input.payment.PaymentDocCommitInput;
import net.summerfarm.module.bms.model.input.payment.PaymentDocInsertInput;
import net.summerfarm.module.bms.model.input.payment.PaymentDocQueryInput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/22 14:53
 */
public class PaymentDocInputCheck {


    /**
     * 打款单列表 参数校验
     *
     * @param input
     */
    public static void listCheck(PaymentDocQueryInput input) {
        Preconditions.checkArgument(Objects.nonNull(input), "请输入查询条件");
        Preconditions.checkArgument(Objects.nonNull(input.getBusinessType()), "请选择结算单类型");
        Preconditions.checkArgument(QuotationEnum.BusinessType.parseByName(input.getBusinessType()).isPresent(), "该结算单类型不存在");

        if (Objects.isNull(input.getPageIndex())) {
            input.setPageIndex(1);
        }
        if (Objects.isNull(input.getPageSize())) {
            input.setPageSize(10);
        }
    }


    /**
     * 打款单详情 参数校验
     *
     * @param input
     */
    public static void detailCheck(PaymentDocQueryInput input) {
        Preconditions.checkArgument(Objects.nonNull(input), "请输入查询条件");
        Preconditions.checkArgument(Objects.nonNull(input.getId()), "请选择打款单");
    }


    /**
     * 作废打款单 参数校验
     *
     * @param input
     */
    public static void cancelCheck(PaymentDocCancelInput input) {
        Preconditions.checkArgument(Objects.nonNull(input), "请输入查询条件");
        Preconditions.checkArgument(Objects.nonNull(input.getPaymentDocumentId()), "请选择打款单");
    }


    /**
     * 提交打款单 参数校验
     *
     * @param input
     */
    public static void commitCheck(PaymentDocCommitInput input) {
        Preconditions.checkArgument(Objects.nonNull(input), "请输入查询条件");
        Preconditions.checkArgument(Objects.nonNull(input.getPaymentDocumentId()), "请选择打款单");
        if (Objects.isNull(input.getPayeeId())
                && Objects.nonNull(input.getCarrierId())) {
            input.setPayeeId(input.getCarrierId().longValue());
        }
        Preconditions.checkArgument(Objects.nonNull(input.getPayeeId()), "请选择收款方");
        Preconditions.checkArgument(Objects.nonNull(input.getPayeeAccountId()), "请选择收款方账户");
        Preconditions.checkArgument(Objects.nonNull(input.getBusinessType()), "请选择业务类型");
    }


    /**
     * 对账单 创建打款单
     *
     * @param input
     */
    public static void insertCheck(PaymentDocInsertInput input) {
        Preconditions.checkArgument(Objects.nonNull(input), "请输入查询条件");
        Preconditions.checkArgument(Objects.nonNull(input.getPayeeId()), "请选择收款方");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(input.getIds()), "请选择对账单");
        Preconditions.checkArgument(StringUtils.isNotBlank(input.getSettleAccountUrl()), "请上传对账凭证");
        Preconditions.checkArgument(StringUtils.isNotBlank(input.getBusinessType()), "请选择业务类型");

    }
}
