package net.summerfarm.module.bms.model.output;

import lombok.Data;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.common.model.QuotationRegionBO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/17 14:42
 */
@Data
public class QuotationBaseOutput implements Serializable {

    /**
     * 报价单主键
     */
    private Long id;

    /**
     * @see QuotationBaseOutput#bidderName
     * 承运商
     */
    @Deprecated
    private String carrierName;

    /**
     * 服务区域
     */
    private String serviceArea;

    /**
     * @see QuotationBaseOutput#quotaTargetName
     * 城配仓
     */
    @Deprecated
    private String storeName;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private List<String> districts;

    /**
     * 城配 内外区
     */
    private Integer quotaType;

    /**
     * 报价方状态
     */
    private Integer status;

    /**
     * 更新人
     */
    private String lastUpdateAdminName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /************************************新对象**********************************/

    /**
     * 报价方
     */
    private String bidderName;

    /**
     * 报价对象
     */
    private String quotaTargetName;

    /**
     * 业务类型
     * @see QuotationEnum.BusinessType
     */
    private String businessType;

    /**
     * 服务省市区
     */
    private List<QuotationRegionBO> quotationAreaList;

}
