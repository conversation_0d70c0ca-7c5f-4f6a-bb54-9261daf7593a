package net.summerfarm.module.bms.model.input.reconciliation;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/27 10:48
 */
@Data
public class ReconPaymentUpsertInput implements Serializable {

    /**
     * 对账单主键
     */
    private Long id;

    /**
     * 打款方式
     */
    private List<ReconPaymentUpsertDetailInput> reconPaymentConfigList;


    @Data
    public static class ReconPaymentUpsertDetailInput implements Serializable {

        /**
         * 打款单打款方式
         */
        private String reconPaymentType;


        /**
         * 金额
         */
        private BigDecimal payableAmount;
    }


}
