package net.summerfarm.module.bms.model.check;

import com.google.common.base.Preconditions;
import net.summerfarm.module.bms.common.constant.ReconciliationEnum;
import net.summerfarm.module.bms.model.input.reconciliation.ReconPaymentUpsertInput;
import net.summerfarm.module.bms.model.input.reconciliation.ReconciliationInsertInput;
import net.summerfarm.module.bms.model.input.reconciliation.ReconciliationQueryInput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/24 15:46
 */
public class ReconciliationInputCheck {

    /**
     * 对账单列表 参数校验
     *
     * @param input
     */
    public static void initAndCheck(ReconciliationQueryInput input) {
        Preconditions.checkArgument(Objects.nonNull(input), "请输入查询条件");
        Preconditions.checkArgument(StringUtils.isNotBlank(input.getBusinessType()), "请选择业务类型");

        if (Objects.isNull(input.getPageSize())) {
            input.setPageSize(10);
        }
        if (Objects.isNull(input.getPageIndex())) {
            input.setPageIndex(1);
        }
    }


    /**
     * 对账单详情 参数校验
     *
     * @param input
     */
    public static void detailCheck(ReconciliationQueryInput input) {
        Preconditions.checkArgument(Objects.nonNull(input), "请输入查询条件");
        Preconditions.checkArgument(Objects.nonNull(input.getId()), "请选择对账单");
    }

    /**
     * 编辑对账单打款方式 参数校验
     *
     * @param input
     */
    public static void reconPaymentCheck(ReconPaymentUpsertInput input) {
        Preconditions.checkArgument(Objects.nonNull(input), "请输入查询条件");
        Preconditions.checkArgument(Objects.nonNull(input.getId()), "请选择对账单");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(input.getReconPaymentConfigList()), "最少填写一种打款方式");

        for (ReconPaymentUpsertInput.ReconPaymentUpsertDetailInput reconPaymentUpsertDetailInput : input.getReconPaymentConfigList()) {
            if (Objects.isNull(reconPaymentUpsertDetailInput.getPayableAmount())) {
                throw new IllegalArgumentException("请输入金额");
            }
            ReconciliationEnum.ReconPaymentType.parseByName(reconPaymentUpsertDetailInput.getReconPaymentType()).orElseThrow(() -> new IllegalArgumentException("不存在这种打款方式"));
        }
    }


    /**
     * 对账 参数校验
     *
     * @param input
     */
    public static void reconInsertCheck(ReconciliationInsertInput input) {
        Preconditions.checkArgument(Objects.nonNull(input), "请选择明细单");
        Preconditions.checkArgument(Objects.nonNull(input.getBidderId()), "请选择承运商");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(input.getSettleAccountIds()), "请选择明细单");
        Preconditions.checkArgument(StringUtils.isNotBlank(input.getReconciliationProofUrl()), "请上传对账凭证");
        Preconditions.checkArgument(StringUtils.isNotBlank(input.getBusinessType()), "请选择业务类型");

    }

    public static void exportCheck(ReconciliationQueryInput input) {

    }
}
