package net.summerfarm.module.bms.model.input;

import lombok.Data;
import net.summerfarm.enums.bms.BmsQuotaFormEnum;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.common.model.QuotationRegionBO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/21 13:44
 */
@Data
public class QuotationUpsertInput implements Serializable {

    /**
     * 报价单主键 更新用
     */
    private Long id;

    /**
     * 承运商
     */
    @Deprecated
    private Long carrierId;

    @Deprecated
    private String carrierName;

    /**
     * 服务区域
     */
    private String serviceArea;

    /**
     * 城配仓
     */
    @Deprecated
    private Long storeNo;

    private String storeName;

    /**
     * 报价方
     */
    private Long bidderId;

    /**
     * 报价方
     */
    private String bidderName;

    /**
     * 报价对象
     */
    private Long quotaTargetId;

    /**
     * 报价对象
     */
    private String quotaTargetName;

    /**
     * 内外区
     *
     * @see QuotationEnum.DeliveryQuotaType
     */
    private Integer quotaType;

    /**
     * 报价形式
     *
     * @see BmsQuotaFormEnum
     */
    private Integer quotaForm;

    /**
     * 业务类型
     *
     * @see QuotationEnum.BusinessType
     */
    private String businessType;

    /**
     * 计费项
     */
    private List<QuotationCalcItemUpsertInput> quotationDetails;

    /**
     * 计费公式
     */
    private List<QuotationCalcFormulaUpsertInput> calculateCosts;

    /**
     * 服务 省市区
     */
    private List<QuotationRegionBO> quotationAreas;

}
