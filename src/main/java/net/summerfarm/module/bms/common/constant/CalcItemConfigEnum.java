package net.summerfarm.module.bms.common.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/4/3 18:00
 */
public interface CalcItemConfigEnum {



    @Getter
    enum QuotaCalcItemTypeEnum{
        /**
         * 系统报价
         */
        SYSTEM(0, "系统报价"),

        CUSTOM(1, "自定义报价"),


        ;

        private final Integer code;

        private final String desc;

        QuotaCalcItemTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }


    @Getter
    enum QuotaCalcItemSourceTypeEnum{
        /**
         * 报价详情
         */
        QUOTE_ITEM(0, "报价详情"),

        QUOTE_FORMULA(1, "计算模型"),


        ;

        private final Integer code;

        private final String desc;

        QuotaCalcItemSourceTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }


}
