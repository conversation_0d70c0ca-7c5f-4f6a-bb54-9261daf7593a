package net.summerfarm.module.bms.common.convert;

import net.summerfarm.module.bms.common.model.QuotationRegionBO;
import net.summerfarm.module.bms.domain.model.QuotationCalcFormulaDO;
import net.summerfarm.module.bms.domain.model.QuotationCalcItemDO;
import net.summerfarm.module.bms.domain.model.QuotationDO;
import net.summerfarm.module.bms.domain.repository.param.QuotationQueryParam;
import net.summerfarm.module.bms.model.input.QuotationQueryInput;
import net.summerfarm.module.bms.model.input.QuotationUpsertInput;
import net.summerfarm.module.bms.model.output.QuotationBaseOutput;
import net.summerfarm.module.bms.model.output.QuotationCalcFormulaOutput;
import net.summerfarm.module.bms.model.output.QuotationCalcItemOutput;
import net.summerfarm.module.bms.model.output.QuotationDetailOutput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/17 17:24
 */
public class QuotationBoundConvert {

    public static QuotationDO convertByUpdate(QuotationUpsertInput input) {
        QuotationDO quotationDO = new QuotationDO();
        quotationDO.setId(input.getId());
        quotationDO.setQuotaForm(input.getQuotaForm());

        return convertQuotationDO(input, quotationDO);
    }


    /**
     * 报价单详情
     *
     * @param input
     * @return
     */
    public static QuotationDO convertByInsert(QuotationUpsertInput input) {
        QuotationDO quotationDO = new QuotationDO();

        quotationDO.setQuotaType(input.getQuotaType());
        quotationDO.setQuotaForm(input.getQuotaForm());
        if (Objects.nonNull(input.getBidderId())) {
            quotationDO.setBidderId(input.getBidderId());
        } else {
            quotationDO.setBidderId(input.getCarrierId());
        }

        quotationDO.setBidderName(input.getBidderName());
        if (Objects.nonNull(input.getQuotaTargetId())) {
            quotationDO.setQuotaTargetId(input.getQuotaTargetId());
        } else {
            quotationDO.setQuotaTargetId(input.getStoreNo());
        }
        quotationDO.setQuotaTargetName(input.getQuotaTargetName());
        quotationDO.setBusinessType(input.getBusinessType());

        return convertQuotationDO(input, quotationDO);

    }


    /**
     * 查询参数 转换
     *
     * @param input
     * @return
     */
    public static QuotationQueryParam convertInput(QuotationQueryInput input) {
        QuotationQueryParam param = new QuotationQueryParam();
        if (Objects.isNull(input.getBidderId())) {
            param.setBidderId(input.getCarrierId());
        } else {
            param.setBidderId(input.getBidderId());
        }
        param.setServiceAreaId(input.getServiceAreaId());
        if (Objects.isNull(input.getQuotaTargetId())) {
            param.setQuotaTargetId(input.getStoreNo());
        } else {
            param.setQuotaTargetId(input.getQuotaTargetId());
        }
        param.setStatus(input.getStatus());
        param.setBusinessType(input.getBusinessType());
        param.setQuotaType(input.getQuotaType());
        if (CollectionUtils.isNotEmpty(input.getQuotationAreas())) {
            param.setAreaList(input.getQuotationAreas().stream().filter(data -> StringUtils.isNotEmpty(data.getArea())).map(QuotationRegionBO::getArea).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(input.getQuotationAreas())) {
            param.setCityList(input.getQuotationAreas().stream().filter(data -> StringUtils.isNotEmpty(data.getCity())).map(QuotationRegionBO::getCity).collect(Collectors.toList()));
        }

        return param;
    }

    /**
     * 报价单返回值转换
     *
     * @param entity
     * @return
     */
    public static QuotationBaseOutput convertBaseOutput(QuotationDO entity) {
        QuotationBaseOutput output = new QuotationBaseOutput();
        output.setId(entity.getId());
        output.setCarrierName(entity.getBidderName());
        if (Objects.nonNull(entity.getServiceAreaDO())) {
            output.setServiceArea(entity.getServiceAreaDO().getArea());
        }
        output.setStoreName(entity.getQuotaTargetName());
        output.setProvince(entity.getProvince());
        output.setCity(entity.getCity());
        if (CollectionUtils.isNotEmpty(entity.getRegionBOList())) {
            output.setDistricts(entity.getRegionBOList().stream().map(QuotationRegionBO::getArea).collect(Collectors.toList()));
        }
        output.setQuotaType(entity.getQuotaType());
        output.setStatus(entity.getStatus());
        output.setLastUpdateAdminName(entity.getUpdateUserName());
        output.setUpdateTime(entity.getUpdateTime());
        output.setBidderName(entity.getBidderName());
        output.setQuotaTargetName(entity.getQuotaTargetName());
        output.setBusinessType(entity.getBusinessType());
        output.setQuotationAreaList(entity.getRegionBOList());

        return output;
    }

    /**
     * 报价单详情 convert
     *
     * @param entity
     * @return
     */
    public static QuotationDetailOutput convertDetailOutput(QuotationDO entity) {
        QuotationDetailOutput output = new QuotationDetailOutput();
        output.setCarrierName(entity.getBidderName());
        if (Objects.nonNull(entity.getServiceAreaDO())) {
            output.setServiceArea(entity.getServiceAreaDO().getArea());
            output.setServiceAreaId(entity.getServiceAreaDO().getId());
        }
        output.setStoreName(entity.getQuotaTargetName());
        output.setProvince(entity.getProvince());
        output.setCity(entity.getCity());
//        if (CollectionUtils.isNotEmpty(entity.getRegionBOList())) {
//        }
        output.setDistricts(entity.getRegionBOList().stream().map(QuotationRegionBO::getArea).collect(Collectors.toList()));

        output.setQuotaType(entity.getQuotaType());
        output.setQuotaForm(entity.getQuotaForm());
        output.setId(entity.getId());
        output.setBidderName(entity.getBidderName());
        output.setQuotaTargetName(entity.getQuotaTargetName());
        if (CollectionUtils.isNotEmpty(entity.getCalcItemDOList())) {
            output.setQuotationDetails(
                    entity.getCalcItemDOList().stream().map(QuotationBoundConvert::calcItemConvert).collect(Collectors.toList())
            );
        }
        if (CollectionUtils.isNotEmpty(entity.getCalcFormulaDOList())) {
            output.setQuoteCalculateCosts(
                    entity.getCalcFormulaDOList().stream().map(QuotationBoundConvert::calcFormulaConvert).collect(Collectors.toList())
            );
        }
        output.setBusinessType(entity.getBusinessType());
        output.setStatus(entity.getStatus());

        return output;
    }

    /**
     * 报价单 计费单项
     *
     * @param itemDO
     * @return
     */
    public static QuotationCalcItemOutput calcItemConvert(QuotationCalcItemDO itemDO) {
        QuotationCalcItemOutput output = new QuotationCalcItemOutput();
        output.setId(itemDO.getId());
        output.setBmsCalculationItemId(itemDO.getItemId());
        output.setQuoteName(itemDO.getItemName());
        output.setAmount(itemDO.getAmount());
        output.setUnit(itemDO.getUnit());

        return output;
    }

    /**
     * 报价单 计费公式
     *
     * @param formulaDO
     * @return
     */
    public static QuotationCalcFormulaOutput calcFormulaConvert(QuotationCalcFormulaDO formulaDO) {
        QuotationCalcFormulaOutput output = new QuotationCalcFormulaOutput();
        output.setId(formulaDO.getId());
        output.setFormula(formulaDO.getFormula());
        output.setCalculateType(formulaDO.getCalculateType());
        output.setCalculateName(formulaDO.getFormulaName());
        output.setId(formulaDO.getId());


        return output;
    }


    private static QuotationDO convertQuotationDO(QuotationUpsertInput input, QuotationDO quotationDO) {
        if (CollectionUtils.isNotEmpty(input.getQuotationAreas())) {
            QuotationRegionBO quotationRegionBO = input.getQuotationAreas().get(0);
            quotationDO.setProvince(quotationRegionBO.getProvince());
            quotationDO.setCity(quotationRegionBO.getCity());

            quotationDO.setRegionBOList(
                    input.getQuotationAreas().stream().map(area -> {
                        QuotationRegionBO regionBO = new QuotationRegionBO();
                        regionBO.setProvince(area.getProvince());
                        regionBO.setCity(area.getCity());
                        regionBO.setArea(area.getArea());

                        return regionBO;
                    }).collect(Collectors.toList())
            );
        }

        if (CollectionUtils.isNotEmpty(input.getQuotationDetails())) {
            quotationDO.setCalcItemDOList(
                    input.getQuotationDetails().stream().map(details -> {
                        QuotationCalcItemDO itemDO = new QuotationCalcItemDO();
                        itemDO.setId(details.getId());
                        itemDO.setItemId(details.getBmsCalculationItemId());
                        itemDO.setItemName(details.getBmsCalculationItemName());
                        itemDO.setAmount(details.getAmount());
                        itemDO.setUnit(details.getUnit());

                        return itemDO;
                    }).collect(Collectors.toList())
            );
        }

        if (CollectionUtils.isNotEmpty(input.getCalculateCosts())) {
            quotationDO.setCalcFormulaDOList(
                    input.getCalculateCosts().stream().map(cost -> {
                        QuotationCalcFormulaDO formulaDO = new QuotationCalcFormulaDO();
                        formulaDO.setId(cost.getId());
                        formulaDO.setFormulaName(cost.getCalculateName());
                        formulaDO.setFormula(cost.getFormula());
                        formulaDO.setCalculateType(cost.getCalculateType());

                        return formulaDO;
                    }).collect(Collectors.toList())
            );

        }

        return quotationDO;
    }

}
