package net.summerfarm.module.bms.common.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/3/31 16:40
 */
public interface QuotationProcessEnum {


    /**
     * 计费公式 status
     */
    @Getter
    enum QuotationProcessType {
        /**
         * 审批中
         */
        SETTLE_ACCOUNT(0, "结算明细单"),

        RECONCILIATION(1, "结算对账单"),

        PAYMENT_DOC(2, "结算打款单"),


        ;

        private final Integer code;

        private final String desc;

        QuotationProcessType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
}
