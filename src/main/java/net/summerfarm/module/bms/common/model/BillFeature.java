package net.summerfarm.module.bms.common.model;

import lombok.Data;
import net.summerfarm.common.object.WalletsEstimatePayInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/4 17:01
 */
@Data
public class BillFeature {

    /**
     * 预估付款信息列表
     */
    private List<WalletsEstimatePayInfo> walletsEstimatePayInfoList;

    /**
     * 账单信息
     */
    private List<BillInfoFeature> billInfoFeatureList;

    @Data
    public static class BillInfoFeature {

        /**
         * 账单id
         */
        private Long id;

        /**
         * 账单创建人名称
         */
        private String createUserName;
    }

}
