package net.summerfarm.module.bms.common.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/3/30 16:59
 */
public interface PaymentDocEnum {

    String[] DELIVERY_EXPORT_HEAD = {"打款单编号", "承运商", "应付金额", "开票状态", "打款状态", "任务生成时间", "结算月份", "城配仓"};

    String[] TRUNK_EXPORT_HEAD = {"打款单编号", "承运商", "应付金额", "开票状态", "打款状态", "任务生成时间", "结算月份"};

    /**
     * 计费公式 status
     */
    @Getter
    enum PaymentDocStatus {

        /**
         *
         */
        CREATED(-1, "已创建"),

        /**
         * 审批中
         */
        APPROVING(0, "审批中"),

        APPROVAL_FAIL(1, "审批失败"),

        APPROVAL_SUCCESS(2, "审批成功"),
        ;

        private final Integer code;

        private final String desc;

        PaymentDocStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
}
