package net.summerfarm.module.bms.common.constant;

import lombok.Getter;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/3/31 15:41
 */
public interface ReconciliationEnum {


    @Getter
    enum ReconStatusEnum {
        /**
         * 对账中
         */
        CREATED(0, "已创建/对账中"),

        APPROVAL_SUCCESS(1, "审批成功/已对账"),

        /**
         * 审批中
         */
        APPROVING(2, "审批中"),

        APPROVAL_FAIL(3, "审批失败"),

        ;

        private final Integer code;

        private final String desc;

        ReconStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }


    /**
     * 对账单
     * 支付方式枚举
     */
    @Getter
    enum ReconPaymentType {


        /**
         * 财务打款 desc不能修改，和钉钉审批绑死了
         */
        FINANCE_PAYMENT("财务打款"),

        OFFLINE_PAYMENT("已线下结算"),

        ;

        private final String desc;

        ReconPaymentType(String desc) {
            this.desc = desc;
        }

        public static Optional<ReconPaymentType> parseByName(String name) {
            for (ReconPaymentType reconPaymentType : ReconPaymentType.values()) {
                if (Objects.equals(name, reconPaymentType.name())) {
                    return Optional.of(reconPaymentType);
                }
            }
            return Optional.empty();
        }
    }


}
