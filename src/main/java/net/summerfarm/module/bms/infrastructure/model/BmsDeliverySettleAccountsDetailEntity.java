package net.summerfarm.module.bms.infrastructure.model;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class BmsDeliverySettleAccountsDetailEntity {
    private Long id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String path;

    private Byte haveSpannedArea;

    private Byte haveQuotationConflict;

    private Integer totalPosition;

    private Integer taxiPosition;

    private Integer noDeliveryTimePosition;

    private BigDecimal systemKilometers;

    private BigDecimal actualKilometers;

    private BigDecimal loadFactor;

    private BigDecimal startingKilometers;

    private Integer skuQuantity;

    private Integer quotationId;

    private String driver;

    private Long startingPositions;

    private Integer carType;

    private String quotationFeature;

    private String fulfillFeature;

    private Long fulfillId;

    private BigDecimal weight;

    private BigDecimal volume;

    private Integer productQuantity;

    private String area;

    private String shiftType;

    private String remark;

}