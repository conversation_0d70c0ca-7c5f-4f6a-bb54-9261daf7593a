package net.summerfarm.module.bms.infrastructure.mapper;

import net.summerfarm.module.bms.domain.repository.param.CalculationItemQueryParam;
import net.summerfarm.module.bms.infrastructure.model.BmsCalculationItemEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BmsCalculationItemEntityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BmsCalculationItemEntity record);

    int insertSelective(BmsCalculationItemEntity record);

    BmsCalculationItemEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BmsCalculationItemEntity record);

    int updateByPrimaryKey(BmsCalculationItemEntity record);

    /**
     * 条件查询
     *
     * @param queryParam
     * @return
     */
    List<BmsCalculationItemEntity> selectByParam(CalculationItemQueryParam queryParam);

    int initBusinessType();
}