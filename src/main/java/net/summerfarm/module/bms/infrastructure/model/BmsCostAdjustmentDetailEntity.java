package net.summerfarm.module.bms.infrastructure.model;

import lombok.Data;

import java.math.BigDecimal;
@Data
public class BmsCostAdjustmentDetailEntity {
    /**
     * 结算项id
     */
    private Integer calculationDetailsId;

    /**
     * 结算项id
     */
    private Integer id;

    private BigDecimal oldAmount;

    private BigDecimal newAmount;

    private String remake;

    private String calculateName;

    private Integer type;

    /**
     * 发起人id
     */
    private Integer applicantAdminId;

    /**
     * 发起人
     */
    private Integer applicantAdminName;
}
