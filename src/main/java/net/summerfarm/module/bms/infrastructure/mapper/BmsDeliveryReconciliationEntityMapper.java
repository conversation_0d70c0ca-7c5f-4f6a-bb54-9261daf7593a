package net.summerfarm.module.bms.infrastructure.mapper;

import net.summerfarm.model.vo.bms.BmsAdjustmentDetailVO;
import net.summerfarm.model.vo.bms.BmsAdjustmentVO;
import net.summerfarm.module.bms.domain.repository.param.ReconciliationQueryParam;
import net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BmsDeliveryReconciliationEntityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BmsDeliveryReconciliationEntity record);

    int insertSelective(BmsDeliveryReconciliationEntity record);

    BmsDeliveryReconciliationEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BmsDeliveryReconciliationEntity record);

    int updateByPrimaryKey(BmsDeliveryReconciliationEntity record);

    /**
     * 条件查询
     *
     * @param queryParam
     * @return
     */
    List<BmsDeliveryReconciliationEntity> selectByParam( ReconciliationQueryParam queryParam);

    /**
     * 刷数据用
     *
     * @param idList
     * @return
     */
    List<BmsDeliveryReconciliationEntity> selectAll4Update(@Param("idList") List<Long> idList);


    /**
     * 初始化 报价单 更新人字段
     *
     * @return
     */
    int initOperator(@Param("idList") List<Long> idList);

    /**
     * 初始化创建者
     *
     * @return
     */
    int initCreatName(@Param("idList") List<Long> idList);

    /**
     * 初始化更新人
     *
     * @return
     */
    int initUpdateName(@Param("idList") List<Long> idList);

    /**
     * 打款数据
     *
     * @param idList
     * @return
     */
    int initPayment(@Param("idList") List<Long> idList);

    void updateByUnbind(@Param("paymentDocumentsId") Integer paymentDocumentsId);

    List<BmsAdjustmentVO> selectFinishAdjust(@Param("id")Integer id, @Param("type")Integer type, @Param("status") Integer status);

    List<BmsAdjustmentDetailVO> selectCostAdjustDetail(@Param("id")Integer id, @Param("type")Integer type);

    List<Integer> selectAllInReview(Integer type);

    BmsDeliveryReconciliationEntity selectRecLastVO(Integer id);

    BmsDeliveryReconciliationEntity selectBySourceId(Long sourceId);
}