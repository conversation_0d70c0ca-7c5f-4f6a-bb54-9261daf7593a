package net.summerfarm.module.bms.infrastructure.mapper;

import net.summerfarm.model.vo.bms.BmsCalculationDetailsVO;
import net.summerfarm.module.bms.domain.repository.param.settle.SettleFulfillAmountQueryParam;
import net.summerfarm.module.bms.infrastructure.model.BmsCalculationDetailsEntity;

import java.util.List;

public interface BmsCalculationDetailsEntityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BmsCalculationDetailsEntity record);

    int insertSelective(BmsCalculationDetailsEntity record);

    BmsCalculationDetailsEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BmsCalculationDetailsEntity record);

    int updateByPrimaryKey(BmsCalculationDetailsEntity record);


    /**
     * 根据明细单查询 履约费用列表
     *
     * @param queryParam
     * @return
     */
    List<BmsCalculationDetailsEntity> selectBySettleParam(SettleFulfillAmountQueryParam queryParam);

    /**
     * 根据明细履约信息查询 履约费用列表
     *
     * @param queryParam
     * @return
     */
    List<BmsCalculationDetailsEntity> selectBySettleFulfillParam(SettleFulfillAmountQueryParam queryParam);


    int initCostName();

    List<BmsCalculationDetailsVO> selectByAccountsDetailId(Integer id);
}