package net.summerfarm.module.bms.infrastructure.facade.config;

import com.google.common.collect.Lists;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.bms.BmsDeliveryQuotationDetail;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/13 14:42
 */
@Component
public class ConfigFacade {

    @Resource
    private ConfigMapper configMapper;

    /**
     * 返回系统配置项
     *
     * @return
     */
    public List<Integer> systemItemIdList() {
        List<String> temp = Lists.newArrayList();
        Config config = configMapper.selectOne("bms_delivery_quotation_system_field");
        String[] split = config.getValue().split(",");

        Collections.addAll(temp, split);

        return temp.stream().map(Integer::valueOf).collect(Collectors.toList());
    }


    /**
     * 查询特殊城市
     *
     * @return
     */
    public String querySpecialCity() {

        Config noAreaCity = configMapper.selectOne("NO_AREA_CITY");
        return noAreaCity.getValue();
    }


    /**
     * 前缀
     *
     * @return
     */
    public String bmsUploadPrefix() {
        Config bmsPrefixConfig = configMapper.selectOne("BMS_UPLOAD_PREFIX");
        if(Objects.isNull(bmsPrefixConfig) || StringUtils.isEmpty(bmsPrefixConfig.getValue())){
            return "https://azure.summerfarm.net/";
        }
        return bmsPrefixConfig.getValue();
    }

}
