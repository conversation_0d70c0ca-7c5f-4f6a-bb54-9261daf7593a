package net.summerfarm.module.bms.infrastructure.model;

import java.time.LocalDateTime;

public class BmsSettleAccountItemEntity {
    private Long id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer settleAccountsDetailsId;

    private Integer settleAccountId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSettleAccountsDetailsId() {
        return settleAccountsDetailsId;
    }

    public void setSettleAccountsDetailsId(Integer settleAccountsDetailsId) {
        this.settleAccountsDetailsId = settleAccountsDetailsId;
    }

    public Integer getSettleAccountId() {
        return settleAccountId;
    }

    public void setSettleAccountId(Integer settleAccountId) {
        this.settleAccountId = settleAccountId;
    }
}