package net.summerfarm.module.bms.infrastructure.mapper;

import net.summerfarm.module.bms.domain.repository.param.PaymentInvoiceQueryParam;
import net.summerfarm.module.bms.infrastructure.model.BmsPaymentInvoiceRelEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BmsPaymentInvoiceRelEntityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BmsPaymentInvoiceRelEntity record);

    int insertSelective(BmsPaymentInvoiceRelEntity record);

    BmsPaymentInvoiceRelEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BmsPaymentInvoiceRelEntity record);

    int updateByPrimaryKey(BmsPaymentInvoiceRelEntity record);

    /**
     * 根据参数查询
     *
     * @param param
     * @return
     */
    List<BmsPaymentInvoiceRelEntity> selectByParam(PaymentInvoiceQueryParam param);


    /**
     * 批量写入
     *
     * @param invoiceRelEntities
     * @return
     */
    int batchInsert(@Param("items") List<BmsPaymentInvoiceRelEntity> invoiceRelEntities);

    /**
     * 删除结算打款单相关发票的关联关系
     * @param documentId 结算打款单id
     */
    void unBindPurchaseInvoiceByDocumentId(Integer documentId);
}