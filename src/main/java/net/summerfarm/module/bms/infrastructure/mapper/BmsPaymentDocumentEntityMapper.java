package net.summerfarm.module.bms.infrastructure.mapper;

import net.summerfarm.model.vo.bms.BmsPaymentDocumentVO;
import net.summerfarm.module.bms.domain.repository.param.PaymentDocQueryParam;
import net.summerfarm.module.bms.infrastructure.model.BmsPaymentDocumentEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BmsPaymentDocumentEntityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BmsPaymentDocumentEntity record);

    int insertSelective(BmsPaymentDocumentEntity record);

    BmsPaymentDocumentEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BmsPaymentDocumentEntity record);

    int updateByPrimaryKey(BmsPaymentDocumentEntity record);

    /**
     * 批量查询
     *
     * @param param
     * @return
     */
    List<BmsPaymentDocumentEntity> selectByParam(PaymentDocQueryParam param);


    /**
     * 查询全部待刷数据，请勿使用
     * @param idList
     * @return
     */
    List<BmsPaymentDocumentEntity> selectAll4Update(@Param("idList") List<Long> idList);

    /**
     * 初始化 业务类型
     *
     * @return
     */
    int initBusinessType();

    int initPayee(@Param("idList") List<Long> idList);

    BmsPaymentDocumentVO selectInHandById(Integer bmsPaymentDocumentId);
}