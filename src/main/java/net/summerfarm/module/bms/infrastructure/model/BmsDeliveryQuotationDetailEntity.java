package net.summerfarm.module.bms.infrastructure.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class BmsDeliveryQuotationDetailEntity {
    private Long id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer bmsCalculationItemId;

    private Integer bmsDeliveryQuotationId;

    private BigDecimal amount;

    private String bmsCalculationItemName;

    private String unit;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getBmsCalculationItemId() {
        return bmsCalculationItemId;
    }

    public void setBmsCalculationItemId(Integer bmsCalculationItemId) {
        this.bmsCalculationItemId = bmsCalculationItemId;
    }

    public Integer getBmsDeliveryQuotationId() {
        return bmsDeliveryQuotationId;
    }

    public void setBmsDeliveryQuotationId(Integer bmsDeliveryQuotationId) {
        this.bmsDeliveryQuotationId = bmsDeliveryQuotationId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getBmsCalculationItemName() {
        return bmsCalculationItemName;
    }

    public void setBmsCalculationItemName(String bmsCalculationItemName) {
        this.bmsCalculationItemName = bmsCalculationItemName == null ? null : bmsCalculationItemName.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }
}