package net.summerfarm.module.bms.domain.model;

import lombok.Data;
import net.summerfarm.enums.bms.BmsInvoiceStatusEnum;
import net.summerfarm.enums.bms.BmsPaymentStatusEnum;
import net.summerfarm.module.bms.domain.model.feature.PaymentAccountFeature;
import net.summerfarm.module.bms.domain.model.feature.PaymentSettleFeature;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/22 14:01
 */
@Data
public class PaymentDocDO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 编号
     */
    private String paymentNo;

    /**
     * 收款人
     */
    private Long payeeId;

    /**
     * 收款方 账户
     */
    private Integer payeeAccountId;

    /**
     * 收款人
     */
    private String payeeName;

    /**
     * 金额
     */
    private BigDecimal paymentAmount;

    /**
     * 财务打款金额
     */
    private BigDecimal totalAmount;

    /**
     * 结算月
     */
    private String settleMonth;

    /**
     * 任务状态
     */
    private Integer invoiceStatus;

    /**
     * 打款状态
     */
    private Integer paymentStatus;

    /**
     * 打款单状态（是否审核）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 城配仓
     */
    private Integer storeNo;

    /**
     * 业务类型
     */
    private String businessType;


    /**
     * 付款方式
     */
    private Integer payType;

    /**
     * 备注
     */
    private String remake;

    /**
     * 对账凭证
     */
    private String settleAccountUrl;

    /**
     * fms 打款单主键
     */
    private Integer financePaymentOrderId;

    /**
     * 结算方式 值对象
     */
    private List<PaymentSettleFeature> paymentSettleFeatureList;

    /**
     * 支付账户快照
     */
    private PaymentAccountFeature paymentAccountFeature;

    /**
     * 打款单关联发票集合
     */
    private List<PaymentInvoiceRelDO> paymentInvoiceRelList;


    /**
     * 提交打款 状态校验
     * false：不可以提交
     * @return
     */
    public boolean commitStatusCheck() {
        List<Integer> statusList = Arrays.asList(BmsInvoiceStatusEnum.REFUSE.getResult(), BmsInvoiceStatusEnum.START.getResult());
        if (statusList.contains(invoiceStatus) && Objects.equals(paymentStatus, BmsPaymentStatusEnum.START.getResult())) {
            return true;
        }
        return false;
    }

    /**
     * 打款单是否可以作废
     * 任务状态为审核失败或待开票，打款状态为待打款
     * false:不可以作废
     * @return
     */
    public boolean cancelStatusCheck() {
        List<Integer> statusList = Arrays.asList(BmsInvoiceStatusEnum.REFUSE.getResult(), BmsInvoiceStatusEnum.START.getResult(), BmsInvoiceStatusEnum.CANCEL.getResult());
        if (statusList.contains(invoiceStatus) && Objects.equals(paymentStatus, BmsPaymentStatusEnum.START.getResult())) {
            return true;
        }
        return false;
    }

    /**
     * 当应付=0 时直接提交，不需要生成fms 打款
     *
     * @return
     */
    public boolean directCommit() {
        return directCommit(paymentAmount);
    }

    public static boolean directCommit(BigDecimal paymentAmount) {
        return BigDecimal.ZERO.compareTo(paymentAmount) == 0;
    }

}
