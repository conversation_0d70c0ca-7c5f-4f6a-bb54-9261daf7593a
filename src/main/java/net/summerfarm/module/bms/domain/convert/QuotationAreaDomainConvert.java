package net.summerfarm.module.bms.domain.convert;

import net.summerfarm.module.bms.domain.model.QuotationServiceAreaDO;
import net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationAreaEntity;

/**
 * <AUTHOR>
 * @date 2023/3/21 15:23
 */
public class QuotationAreaDomainConvert {

    /**
     * 对象 convert
     *
     * @param entity
     * @return
     */
    public static QuotationServiceAreaDO convert(BmsDeliveryQuotationAreaEntity entity) {
        QuotationServiceAreaDO areaDO = new QuotationServiceAreaDO();
        areaDO.setId(entity.getId());
        areaDO.setArea(entity.getArea());

        return areaDO;
    }

}
