package net.summerfarm.module.bms.domain.repository.quota;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.mapper.bms.BmsDeliveryQuotationRegionMapper;
import net.summerfarm.model.domain.bms.BmsDeliveryQuotationRegion;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.common.convert.PageConvert;
import net.summerfarm.module.bms.common.model.OperatorBO;
import net.summerfarm.module.bms.domain.model.QuotationCalcFormulaDO;
import net.summerfarm.module.bms.domain.model.QuotationCalcItemDO;
import net.summerfarm.module.bms.domain.model.QuotationDO;
import net.summerfarm.module.bms.domain.convert.QuotationDomainConvert;
import net.summerfarm.module.bms.domain.repository.param.PageParam;
import net.summerfarm.module.bms.infrastructure.facade.pms.PmsCarrierQueryFacade;
import net.summerfarm.module.bms.infrastructure.facade.pms.model.PmsCarrierDTO;
import net.summerfarm.module.bms.infrastructure.facade.wms.WmsStoreFacade;
import net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryQuotaCalculateCostEntityMapper;
import net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryQuotationDetailEntityMapper;
import net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryQuotationEntityMapper;
import net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotaCalculateCostEntity;
import net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationDetailEntity;
import net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationEntity;
import net.summerfarm.module.bms.domain.repository.param.QuotationQueryParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/17 15:03
 */
@Component
public class QuotationRepository {

    @Resource
    private BmsDeliveryQuotationEntityMapper bmsDeliveryQuotationEntityMapper;

    @Resource
    private BmsDeliveryQuotationRegionMapper bmsDeliveryQuotationRegionMapper;

    @Resource
    private BmsDeliveryQuotationDetailEntityMapper bmsDeliveryQuotationDetailEntityMapper;

    @Resource
    private BmsDeliveryQuotaCalculateCostEntityMapper bmsDeliveryQuotaCalculateCostEntityMapper;

    @Resource
    private PmsCarrierQueryFacade pmsCarrierQueryFacade;

    @Resource
    private WmsStoreFacade wmsStoreFacade;


    /**
     * 分页查询
     * 查询报价单列表 基础信息
     *
     * @param queryParam
     * @return
     */
    public PageInfo<QuotationDO> queryQuotationPage(QuotationQueryParam queryParam, PageParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<QuotationDO> queryQuotationList = queryQuotationList(queryParam);

        return PageInfoHelper.createPageInfo(queryQuotationList);
    }


    /**
     * 列表查询
     *
     * @param queryParam
     * @return
     */
    public List<QuotationDO> queryQuotationList(QuotationQueryParam queryParam) {
        //查询报价单
        List<BmsDeliveryQuotationEntity> quotaList = bmsDeliveryQuotationEntityMapper.selectByParam(queryParam);
        if (CollectionUtils.isEmpty(quotaList)) {
            return Collections.emptyList();
        }
        List<QuotationDO> quotationDOList = PageConvert.pageConvert(quotaList, QuotationDomainConvert::quotationConvert);
        //查询服务省市区
        quotationDOList.forEach(data -> {
            List<BmsDeliveryQuotationRegion> regionList = bmsDeliveryQuotationRegionMapper.selectByQuotationId(data.getId().intValue());
            data.setRegionBOList(regionList.stream().map(region -> QuotationDomainConvert.areaConvert(data, region)).collect(Collectors.toList())
            );
        });

        if (BooleanUtils.isTrue(queryParam.getHasQueryBidder())) {
            List<Long> collect = quotationDOList.stream().map(QuotationDO::getBidderId).collect(Collectors.toList());
            Map<Long, PmsCarrierDTO> carrierMap = pmsCarrierQueryFacade.queryCarrierMapByIdList(collect);
            for (QuotationDO quotationDO : quotationDOList) {
                if (carrierMap.containsKey(quotationDO.getBidderId())) {
                    quotationDO.setBidderName(carrierMap.get(quotationDO.getBidderId()).getCarrierName());
                }
            }
        }
        if (BooleanUtils.isTrue(queryParam.getHasQueryQuotaTarget())) {
            for (QuotationDO quotationDO : quotationDOList) {
                if (Objects.equals(quotationDO.getBusinessType(), QuotationEnum.BusinessType.DELIVERY_BUSINESS.name())
                        && Objects.nonNull(quotationDO.getQuotaTargetId())) {
                    quotationDO.setQuotaTargetName(wmsStoreFacade.getStoreName(quotationDO.getQuotaTargetId().intValue()));
                }

            }
        }
        return quotationDOList;
    }

    /**
     * 查询报价单详情
     *
     * @param param
     * @return
     */
    public Optional<QuotationDO> queryQuotationDetail(QuotationQueryParam param) {
        if (Objects.isNull(param) || Objects.isNull(param.getId())) {
            return Optional.empty();
        }
        Long quotationId = param.getId();
        BmsDeliveryQuotationEntity bmsDeliveryQuotationEntity = bmsDeliveryQuotationEntityMapper.selectByPrimaryKey(quotationId);
        QuotationDO quotationDO = QuotationDomainConvert.quotationConvert(bmsDeliveryQuotationEntity);

        List<BmsDeliveryQuotationRegion> regionList = bmsDeliveryQuotationRegionMapper.selectByQuotationId(quotationId.intValue());
        quotationDO.setRegionBOList(regionList.stream().map(region -> QuotationDomainConvert.areaConvert(quotationDO, region)).collect(Collectors.toList()));

        if (BooleanUtils.isTrue(param.getHasQueryItem())) {
            List<BmsDeliveryQuotationDetailEntity> bmsDeliveryQuotationDetailEntities = bmsDeliveryQuotationDetailEntityMapper.selectByQuotationId(quotationId.intValue());
            List<QuotationCalcItemDO> itemDOList = bmsDeliveryQuotationDetailEntities.stream().map(QuotationDomainConvert::itemConvert).collect(Collectors.toList());
            quotationDO.setCalcItemDOList(itemDOList);
        }

        if (BooleanUtils.isTrue(param.getHasQueryFormula())) {
            List<BmsDeliveryQuotaCalculateCostEntity> bmsDeliveryQuotaCalculateCostEntities = bmsDeliveryQuotaCalculateCostEntityMapper.selectNormalByQuotationId(quotationId.intValue());
            List<QuotationCalcFormulaDO> formulaDOList = bmsDeliveryQuotaCalculateCostEntities.stream().map(QuotationDomainConvert::formulaConvert).collect(Collectors.toList());
            quotationDO.setCalcFormulaDOList(formulaDOList);
        }
        if (BooleanUtils.isTrue(param.getHasQueryBidder())) {
            if (Objects.nonNull(quotationDO.getBidderId())) {
                pmsCarrierQueryFacade.queryCarrierById(quotationDO.getBidderId()).ifPresent(data -> {
                    quotationDO.setBidderName(data.getCarrierName());
                });
            }
        }
        if (BooleanUtils.isTrue(param.getHasQueryQuotaTarget())) {
            if (Objects.equals(quotationDO.getBusinessType(), QuotationEnum.BusinessType.DELIVERY_BUSINESS.name())) {
                if (Objects.nonNull(quotationDO.getQuotaTargetId())) {
                    quotationDO.setQuotaTargetName(wmsStoreFacade.getStoreName(quotationDO.getQuotaTargetId().intValue()));
                }
            }
        }
        return Optional.of(quotationDO);
    }


    public Long insertQuotation(QuotationDO quotationDO, OperatorBO operatorBO) {
        BmsDeliveryQuotationEntity entity = new BmsDeliveryQuotationEntity();
        LocalDateTime now = LocalDateTime.now();

        entity.setCreateTime(now);
        entity.setUpdateTime(now);

        entity.setStatus(QuotationEnum.QuotationStatus.NORMAL.getCode());
        if (Objects.equals(quotationDO.getBusinessType(), QuotationEnum.BusinessType.DELIVERY_BUSINESS.name())) {
            entity.setStoreNo(quotationDO.getQuotaTargetId().intValue());
        }
        entity.setProvince(quotationDO.getProvince());
        entity.setCity(quotationDO.getCity());
        entity.setQuotaType(quotationDO.getQuotaType());
        if (Objects.nonNull(quotationDO.getServiceAreaDO())) {
            entity.setServiceAreaId(quotationDO.getServiceAreaDO().getId().intValue());
            entity.setServiceAreaName(quotationDO.getServiceAreaDO().getArea());
        }

        entity.setCarrierId(quotationDO.getBidderId().intValue());
        if (Objects.nonNull(operatorBO)) {
            entity.setLastUpdateId(operatorBO.getId());
            entity.setCreator(operatorBO.getId());
            entity.setCreateUserName(operatorBO.getName());
            entity.setUpdateUserName(operatorBO.getName());
        }

        entity.setQuotaForm(quotationDO.getQuotaForm());
        entity.setBidderId(quotationDO.getBidderId());
        entity.setBidderName(quotationDO.getBidderName());
        entity.setQuotaTargetId(quotationDO.getQuotaTargetId());
        entity.setQuotaTargetName(quotationDO.getQuotaTargetName());
        entity.setBusinessType(quotationDO.getBusinessType());

        bmsDeliveryQuotationEntityMapper.insertSelective(entity);

        return entity.getId();
    }

    public int delete(Long id, OperatorBO operatorBO) {
        if (Objects.isNull(id)) {
            return 0;
        }
        BmsDeliveryQuotationEntity entity = new BmsDeliveryQuotationEntity();

        entity.setId(id);
        entity.setStatus(QuotationEnum.QuotationStatus.DELETED.getCode());
        if (Objects.nonNull(operatorBO)) {
            entity.setUpdateTime(LocalDateTime.now());
            entity.setLastUpdateId(operatorBO.getId());
            entity.setUpdateUserName(operatorBO.getName());
        }

        return bmsDeliveryQuotationEntityMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 更新报价单
     *
     * @param quotationDO
     * @param operatorBO
     * @return
     */
    public int updateQuotationById(QuotationDO quotationDO, OperatorBO operatorBO) {
        if (Objects.isNull(quotationDO)) {
            return 0;
        }
        LocalDateTime now = LocalDateTime.now();
        BmsDeliveryQuotationEntity entity = new BmsDeliveryQuotationEntity();
        entity.setId(quotationDO.getId());
        entity.setUpdateTime(now);
        if (Objects.nonNull(operatorBO)) {
            entity.setLastUpdateId(operatorBO.getId());
            entity.setUpdateUserName(operatorBO.getName());
        }
        entity.setProvince(quotationDO.getProvince());
        entity.setCity(quotationDO.getCity());
        entity.setQuotaForm(quotationDO.getQuotaForm());

        return bmsDeliveryQuotationEntityMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 根据报价单查询前置仓配置项
     *
     * @param quoteId
     * @return
     */
    public BigDecimal selectAdvanceByQuoteId(Integer quoteId) {
        if (Objects.isNull(quoteId)) {
            return BigDecimal.ZERO;
        }
        return bmsDeliveryQuotationEntityMapper.selectAdvanceByQuotationId(quoteId);
    }
}
