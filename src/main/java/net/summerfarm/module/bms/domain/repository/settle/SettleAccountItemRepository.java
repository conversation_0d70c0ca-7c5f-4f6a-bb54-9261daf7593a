package net.summerfarm.module.bms.domain.repository.settle;

import net.summerfarm.module.bms.infrastructure.mapper.BmsSettleAccountItemEntityMapper;
import net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountItemEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class SettleAccountItemRepository {
    @Resource
    BmsSettleAccountItemEntityMapper bmsSettleAccountItemEntityMapper;

    public Integer save (BmsSettleAccountItemEntity settleAccountItemEntity) {
        return bmsSettleAccountItemEntityMapper.insertSelective(settleAccountItemEntity);
    }
}
