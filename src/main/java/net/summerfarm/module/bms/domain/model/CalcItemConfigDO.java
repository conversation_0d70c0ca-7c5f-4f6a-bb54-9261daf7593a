package net.summerfarm.module.bms.domain.model;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/4/3 17:34
 */
@Data
public class CalcItemConfigDO {

    /**
     * id
     */
    private Long id;

    /**
     * 计算项 名称
     */
    private String itemName;

    /**
     * 单位
     */
    private String unit;

    private Integer sourceType;

    private Integer type;

    private String businessType;
}
