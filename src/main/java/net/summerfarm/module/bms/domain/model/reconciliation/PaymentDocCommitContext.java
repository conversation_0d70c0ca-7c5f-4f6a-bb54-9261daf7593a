package net.summerfarm.module.bms.domain.model.reconciliation;

import lombok.Data;
import net.summerfarm.module.bms.domain.model.PaymentDocDO;
import net.summerfarm.module.bms.infrastructure.facade.pms.model.PmsCarrierAccountDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/11 16:28
 */
@Data
public class PaymentDocCommitContext {

    /**
     * 打款单主键
     */
    private Long paymentDocId;

    /**
     * 发票列表
     */
    private List<Integer> invoiceIdList;

    /**
     * 收款方
     */
    private Long payeeId;

    /**
     * 收款方账户id
     */
    private Integer payeeAccountId;

    /**
     * 收款方 支付账户
     */
    private PmsCarrierAccountDTO carrierAccountDTO;

}
