package net.summerfarm.module.bms.domain.repository.settle;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import net.summerfarm.module.bms.domain.convert.SettleFulfillConvert;
import net.summerfarm.module.bms.domain.model.feature.SettleTrunkFulfillFeature;
import net.summerfarm.module.bms.domain.model.settle.SettleFulfillAmountDO;
import net.summerfarm.module.bms.domain.model.settle.SettleFulfillDO;
import net.summerfarm.module.bms.domain.repository.adjust.AdjustRepository;
import net.summerfarm.module.bms.domain.repository.param.CostAdjustQueryParam;
import net.summerfarm.module.bms.domain.repository.param.settle.SettleFulfillAmountQueryParam;
import net.summerfarm.module.bms.infrastructure.mapper.BmsDeliverySettleAccountsDetailEntityMapper;
import net.summerfarm.module.bms.infrastructure.model.BmsDeliverySettleAccountsDetailEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/28 17:32
 */
@Component
public class SettleFulfillRepository {

    @Resource
    private BmsDeliverySettleAccountsDetailEntityMapper bmsDeliverySettleAccountsDetailEntityMapper;

    @Resource
    private SettleFulfillAmountRepository settleFulfillAmountRepository;

    @Resource
    private AdjustRepository adjustRepository;

    /**
     * 根据明细单查询 履约信息
     *
     * @param settleAccountId
     * @return
     */
    public List<SettleFulfillDO> queryBySettleAccountId(Long settleAccountId) {
        if (Objects.isNull(settleAccountId)) {
            return Lists.newArrayList();
        }
        return bmsDeliverySettleAccountsDetailEntityMapper.queryBySettleAccountId(settleAccountId.intValue())
                .stream().map(entity -> {
                    SettleFulfillDO settleFulfillDO = new SettleFulfillDO();
                    settleFulfillDO.setId(entity.getId());
                    settleFulfillDO.setCarType(entity.getCarType());
                    settleFulfillDO.setQuoteId(entity.getQuotationId());
                    settleFulfillDO.setWeight(entity.getWeight());
                    settleFulfillDO.setVolume(entity.getVolume());
                    settleFulfillDO.setProductQuantity(entity.getProductQuantity());
                    settleFulfillDO.setArea(entity.getArea());
                    settleFulfillDO.setShiftType(entity.getShiftType());
                    settleFulfillDO.setRemark(entity.getRemark());
                    settleFulfillDO.setSystemKilometers(entity.getSystemKilometers());
                    if (StringUtils.isNotEmpty(entity.getFulfillFeature())) {
                        settleFulfillDO.setSettleTrunkFulfillFeature(
                                JSON.parseObject(entity.getFulfillFeature(), SettleTrunkFulfillFeature.class)
                        );
                    }
                    return settleFulfillDO;
                }).collect(Collectors.toList());
    }

    /**
     * 查询履约单
     *
     * @param id
     * @return
     */
    public Optional<SettleFulfillDO> queryById(Long id) {
        if (Objects.isNull(id)) {
            return Optional.empty();
        }
        BmsDeliverySettleAccountsDetailEntity entity = bmsDeliverySettleAccountsDetailEntityMapper.selectByPrimaryKey(id);
        if (Objects.isNull(entity)) {
            return Optional.empty();
        }
        SettleFulfillDO settleFulfillDO = new SettleFulfillDO();
        settleFulfillDO.setId(entity.getId());
        settleFulfillDO.setCarType(entity.getCarType());
        settleFulfillDO.setWeight(entity.getWeight());
        settleFulfillDO.setVolume(entity.getVolume());
        settleFulfillDO.setProductQuantity(entity.getProductQuantity());
        settleFulfillDO.setArea(entity.getArea());
        settleFulfillDO.setShiftType(entity.getShiftType());
        settleFulfillDO.setRemark(entity.getRemark());
        settleFulfillDO.setSystemKilometers(entity.getSystemKilometers());
        if (StringUtils.isNotEmpty(entity.getFulfillFeature())) {
            settleFulfillDO.setSettleTrunkFulfillFeature(
                    JSON.parseObject(entity.getFulfillFeature(), SettleTrunkFulfillFeature.class)
            );
        }

        SettleFulfillAmountQueryParam amountQueryParam = new SettleFulfillAmountQueryParam();
        amountQueryParam.setSettleFulfillId(id.intValue());
        List<SettleFulfillAmountDO> settleFulfillAmountDOS = settleFulfillAmountRepository.queryBySettleFulfill(amountQueryParam);
        settleFulfillDO.setSettleFulfillAmountDOList(settleFulfillAmountDOS);

        CostAdjustQueryParam costAdjustQueryParam = new CostAdjustQueryParam();
        costAdjustQueryParam.setSettleFulfillId(Collections.singletonList(id));

        settleFulfillDO.setCostAdjustDOList(
                adjustRepository.queryByParam(costAdjustQueryParam));

        return Optional.of(settleFulfillDO);
    }

    /**
     * 保存
     */
    public Integer save(SettleFulfillDO settleFulfillDO) {
        BmsDeliverySettleAccountsDetailEntity bmsDeliverySettleAccountsDetailEntity = SettleFulfillConvert.convertByDO(settleFulfillDO);
        bmsDeliverySettleAccountsDetailEntityMapper.insertSelective(bmsDeliverySettleAccountsDetailEntity);
        return bmsDeliverySettleAccountsDetailEntity.getId().intValue();
    }

    public Integer countByFulfillId(Long pathId) {
        return bmsDeliverySettleAccountsDetailEntityMapper.countByFulfillId(pathId);
    }
}
