package net.summerfarm.module.bms.domain.repository.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/17 16:36
 */
@Data
public class QuotationQueryParam {

    /**
     * 报价单主键
     */
    private Long id;

    /**
     * 报价方主键
     */
    private Long bidderId;

    /**
     * 服务区域主键
     */
    private Long serviceAreaId;

    /**
     * 报价对象主键
     */
    private Long quotaTargetId;

    /**
     * 报价单状态
     */
    private Integer status;

    /**
     * 业务类型
     */
    private String businessType;


    /**
     * 内外区
     */
    private Integer quotaType;

    /**
     * 区
     */
    private List<String> areaList;

    /**
     * 市
     */
    private List<String> cityList;

    /**
     * 城市
     */
    private String city;

    /**
     * 是否查询配置项
     */
    private Boolean hasQueryItem;

    /**
     * 是否查询计费公式
     */
    private Boolean hasQueryFormula;

    /**
     * 查询报价方
     */
    private Boolean hasQueryBidder;

    /**
     * 查询结算对象
     */
    private Boolean hasQueryQuotaTarget;


}
