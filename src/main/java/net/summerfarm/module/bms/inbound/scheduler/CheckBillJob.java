package net.summerfarm.module.bms.inbound.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.service.CheckBillService;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 对账信息
 * @date 2023/3/20 11:46:20
 */
@Component
@Slf4j
public class CheckBillJob extends XianMuJavaProcessor {

    @Autowired
    private CheckBillService checkBillService;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("CheckBillJob[]processResult[]start[]times:{}", LocalDateTime.now());
        String result = checkBillService.checkBill();
        log.info("CheckBillJob[]processResult[]end[]times:{},result:{}", LocalDateTime.now(), result);
        return new ProcessResult(true);
    }
}
