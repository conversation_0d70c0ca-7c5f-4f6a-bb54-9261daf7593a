package net.summerfarm.module.bms.inbound.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryQuotationDetailEntityMapper;
import net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryQuotationEntityMapper;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/3/20 11:33
 */
@Component
@Slf4j
public class QuotationSyncJob extends XianMuJavaProcessor {

    @Resource
    private BmsDeliveryQuotationEntityMapper deliveryQuotationEntityMapper;

    @Resource
    private BmsDeliveryQuotationDetailEntityMapper bmsDeliveryQuotationDetailEntityMapper;

    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        String text = context.getInstanceParameters();
        List<Long> quotaIdList = Lists.newArrayList();
        log.info("同步报价单数据 任务开始:{} 参数:{}", LocalDateTime.now(), text);
        if (StringUtils.isNotEmpty(text)) {
            quotaIdList = JSON.parseArray(text, Long.class);
        }

        int btResult = deliveryQuotationEntityMapper.initBusinessType(quotaIdList);
        log.info("初始化 btResult : {}", btResult);

        int opResult = deliveryQuotationEntityMapper.initOperator(quotaIdList);
        log.info("初始化 opResult : {}", opResult);

        int bi = deliveryQuotationEntityMapper.initBidder(quotaIdList);
        log.info("初始化 bi : {}", bi);

        int san = deliveryQuotationEntityMapper.initServiceAreaName(quotaIdList);
        log.info("初始化 san : {}", san);

        int bn = deliveryQuotationEntityMapper.initBidderName(quotaIdList);
        log.info("初始化 bn : {}", bn);

        int tn = deliveryQuotationEntityMapper.initTargetName(quotaIdList);
        log.info("初始化 tn : {}", tn);

        int cn = deliveryQuotationEntityMapper.initCreatName(quotaIdList);
        log.info("初始化 cn : {}", cn);

        int un = deliveryQuotationEntityMapper.initUpdateName(quotaIdList);
        log.info("初始化 un : {}", un);

        int in = bmsDeliveryQuotationDetailEntityMapper.initItemName();
        log.info("初始化 in : {}", in);

        log.info("同步报价单数据 任务结算:{}", LocalDateTime.now());

        return new ProcessResult(true);
    }
}
