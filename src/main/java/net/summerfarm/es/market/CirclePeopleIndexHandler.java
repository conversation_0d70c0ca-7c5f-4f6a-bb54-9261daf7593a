package net.summerfarm.es.market;

import java.util.List;
import net.summerfarm.mq.DtsModel;

/**
 * 圈人平台 ES标签索引数据处理
 * @author: <EMAIL>
 * @create: 2022/8/11
 */
public interface CirclePeopleIndexHandler {

    /**
     * 更新商户索引数据
     * @param dtsModel 商户信息
     */
    void updateEsIndex(DtsModel dtsModel);

    /**
     * 定时任务,批量更新商标签信息
     */
    void batchUpdateEsIndex(List<Long> mIds);

    /**
     * 初始化新增商户索引
     */
    void initInsertEsIndex();

    /**
     * 指定从startMId开始同步新的数据，每次同步interval+1的数据(循环遍历直至同步万最后一个数据)
     *
     * @param startMId 起始mId
     * @param interval 每次同步的跨度
     */
    void insertEsByCondition(Long startMId, Long interval);

    /**
     * 批量插入es索引商家基础标签信息
     * @param mIds
     */
    void batchInsertEsIndex(List<Long> mIds);

    /**
     * 删除mId的doc文件
     * @param mId
     */
    void delDoc(String mId);

}
