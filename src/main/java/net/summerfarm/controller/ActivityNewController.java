package net.summerfarm.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.valid.group.market.ActivityCreate;
import net.summerfarm.common.valid.group.market.BasicInfoUpdate;
import net.summerfarm.common.valid.group.market.ScopeConfig;
import net.summerfarm.common.valid.group.market.SkuConfig;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.DTO.market.ActivityBasicInfoDTO;
import net.summerfarm.model.DTO.market.ActivityBlackAndWhiteListDTO;
import net.summerfarm.model.DTO.market.ActivityItemConfigDTO;
import net.summerfarm.model.DTO.market.ActivityNewDTO;
import net.summerfarm.model.DTO.market.ActivityPageQueryDTO;
import net.summerfarm.model.DTO.market.ActivityPageRespDTO;
import net.summerfarm.model.DTO.market.ActivityScopeConfigDTO;
import net.summerfarm.model.DTO.market.ActivitySkuBatchDTO;
import net.summerfarm.model.DTO.market.LargeAreaSkuPriceDTO;
import net.summerfarm.model.input.ActivityBlackAndWhiteListPageQuery;
import net.summerfarm.model.input.ActivityBlackAndWhiteListReq;
import net.summerfarm.model.input.ActivityBlackAndWhiteListUpdateReq;
import net.summerfarm.model.input.ActivityBlackAndWhiteListUploadReq;
import net.summerfarm.model.input.ActivityItemConfigReq;
import net.summerfarm.model.input.ActivityBlackAndWhiteListInsertReq;
import net.summerfarm.model.input.ActivityPricingParamsInsertReq;
import net.summerfarm.service.ActivityNewService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 新营销活动模块
 * @author: <EMAIL>
 * @create: 2022/12/1
 */
@RestController
@RequestMapping(value = "/activity")
public class ActivityNewController {

    @Resource
    private ActivityNewService activityNewService;

    /**
     * 创建活动
     * @param activityNewDTO
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/add/basic-info", method = RequestMethod.POST)
    public CommonResult addBasicInfo(@Validated(ActivityCreate.class) @RequestBody ActivityNewDTO activityNewDTO) {
        return activityNewService.addBasicInfo(activityNewDTO);
    }

    /**
     * 修改活动基础信息
     * @param basicInfoDTO
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/update/basic-info", method = RequestMethod.POST)
    public CommonResult updateBasicInfo(@Validated(BasicInfoUpdate.class) @RequestBody ActivityBasicInfoDTO basicInfoDTO) {
        return activityNewService.updateBasicInfo(basicInfoDTO);
    }

    /**
     * 删除活动
     * @param basicInfoId
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delete", method = RequestMethod.POST)
    public CommonResult delete(Long basicInfoId) {
        return activityNewService.delete(basicInfoId);
    }

    /**
     * 新增活动范围
     * @param scopeConfigDTO
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/add/scope-config", method = RequestMethod.POST)
    public CommonResult addScopeConfig(@Validated(ScopeConfig.class) @RequestBody ActivityScopeConfigDTO scopeConfigDTO) {
        return activityNewService.addScopeConfig(scopeConfigDTO);
    }

    /**
     * 删除活动范围
     * @param scopeConfigDTO
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delete/scope-config", method = RequestMethod.POST)
    public CommonResult deleteScopeConfig(@RequestBody ActivityScopeConfigDTO scopeConfigDTO) {
        return activityNewService.deleteScopeConfig(scopeConfigDTO);
    }

    /**
     * 新增、修改活动商品
     * @param itemConfigDTO
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/update/item", method = RequestMethod.POST)
    public CommonResult updateItem(@Validated(SkuConfig.class) @RequestBody ActivityItemConfigDTO itemConfigDTO) {
        return activityNewService.updateItem(itemConfigDTO);
    }

    /**
     * 删除活动商品
     * @param basicInfoId
     * @param sku
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delete/item", method = RequestMethod.POST)
    public CommonResult deleteItem(Long basicInfoId, String sku) {
        return activityNewService.deleteItem(basicInfoId, sku);
    }

    /**
     * 获取活动详情
     * @param basicInfoId
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/detail", method = RequestMethod.POST)
    public CommonResult<ActivityNewDTO> getDetail(Long basicInfoId) {
        return activityNewService.getDetail(basicInfoId);
    }

    /**
     * 活动开启、关闭
     * @param basicInfoId
     * @param status
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/open-down", method = RequestMethod.POST)
    public CommonResult openDown(Long basicInfoId, Integer status) {

        return activityNewService.openDown(basicInfoId, status);
    }

    /**
     * 活动列表页查询
     * @param pageQueryDTO
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/page", method = RequestMethod.POST)
    public CommonResult<PageInfo<ActivityPageRespDTO>> page(@RequestBody ActivityPageQueryDTO pageQueryDTO) {

        return activityNewService.page(pageQueryDTO);
    }

    /**
     * 活动商品价格明细列表
     * @param basicInfoId
     * @param sku
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/sku/price", method = RequestMethod.POST)
    public CommonResult<List<LargeAreaSkuPriceDTO>> listSkuPrice(Long basicInfoId, String sku) {

        return activityNewService.listSkuPrice(basicInfoId, sku);
    }

    /**
     * 批量添加活动sku
     * @param multipartFile
     * @return
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/add/batch-sku", method = RequestMethod.POST)
    public CommonResult<ActivitySkuBatchDTO> batchAddSku(@RequestParam("file") MultipartFile multipartFile) {
        return activityNewService.batchAddSku(multipartFile);
    }

    /**
     * 开启/关闭自动定价
     * @param activityItemConfigReq
     * @return Boolean
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/onOff/autoPrice", method = RequestMethod.POST)
    public CommonResult<Boolean> automaticPrice(@RequestBody @Validated ActivityItemConfigReq activityItemConfigReq) {
        return CommonResult.ok(activityNewService.automaticPrice(activityItemConfigReq));
    }

    /**
     * 黑名单模版下载
     * @param response
     * @return Boolean
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/black-list/template", method = RequestMethod.POST)
    public void downloadBlackList(HttpServletResponse response) {
        activityNewService.downloadBlackList(response);
    }

    /**
     * 白名单模版下载
     * @param response
     * @return Boolean
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/white-list/template", method = RequestMethod.POST)
    public void downloadWhiteList(HttpServletResponse response) {
        activityNewService.downloadWhiteList(response);
    }

    /**
     * 黑白名单列表
     * @param activityBlackListPageQuery
     * @return PageInfo<ActivityBlackListDTO>
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/black-white-list", method = RequestMethod.POST)
    public CommonResult<PageInfo<ActivityBlackAndWhiteListDTO>> blackAndWhiteList(@RequestBody @Validated ActivityBlackAndWhiteListPageQuery activityBlackListPageQuery) {
        return CommonResult.ok(activityNewService.blackAndWhiteList(activityBlackListPageQuery));
    }

    /**
     * 单个或批量删除黑名单
     * @param activityBlackListReq
     * @return Boolean
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/delete/black-white", method = RequestMethod.POST)
    public CommonResult<Boolean> batchDeleteWhiteAndBlackList(@RequestBody ActivityBlackAndWhiteListReq activityBlackListReq) {
        return CommonResult.ok(activityNewService.batchDeleteWhiteAndBlackList(activityBlackListReq));
    }

    /**
     * 上传黑、白名单
     * @param
     * @return Boolean
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/upload/black-white-list", method = RequestMethod.POST)
    public CommonResult<Map<String, Object>> uploadWhiteAndBlackList(@RequestBody @Validated ActivityBlackAndWhiteListUploadReq uploadReq) {
        return CommonResult.ok(activityNewService.uploadWhiteAndBlackList(uploadReq));
    }

    /**
     * 新增黑白名单信息
     * @param activityWhiteListReq
     * @return ActivityWhiteListDTO
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/insert/black-white-list", method = RequestMethod.POST)
    public CommonResult<ActivityBlackAndWhiteListDTO> insertWhiteAndBlackList(@RequestBody @Validated ActivityBlackAndWhiteListInsertReq activityWhiteListReq) {
        return CommonResult.ok(activityNewService.insertWhiteAndBlackList(activityWhiteListReq));
    }

    /**
     * 修改黑白名单信息
     * @param andWhiteListUpdateReq
     * @return ActivityWhiteListDTO
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/update/black-white-list", method = RequestMethod.POST)
    public CommonResult<Boolean> updateWhiteAndBlackList(@RequestBody @Validated ActivityBlackAndWhiteListUpdateReq andWhiteListUpdateReq) {
        return CommonResult.ok(activityNewService.updateWhiteAndBlackList(andWhiteListUpdateReq));
    }

    /**
     * 查询黑白名单详情
     * @param andWhiteListReq
     * @return ActivityWhiteListDTO
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/detail/black-white-list", method = RequestMethod.POST)
    public CommonResult<ActivityBlackAndWhiteListDTO> getDetailWhiteAndBlackList(@RequestBody ActivityBlackAndWhiteListReq andWhiteListReq) {
        return CommonResult.ok(activityNewService.getDetailWhiteAndBlackList(andWhiteListReq));
    }

    /**
     * 临保风险品定价参数设置
     * @param paramsInsertReq
     * @return Boolean
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/insert/pricing-params", method = RequestMethod.POST)
    public CommonResult<Boolean> insertPricingParams(@RequestBody @Validated ActivityPricingParamsInsertReq paramsInsertReq) {
        return CommonResult.ok(activityNewService.insertPricingParams(paramsInsertReq));
    }

    /**
     * 临保风险品定价参数查询
     * @param
     * @return Boolean
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/pricing-params", method = RequestMethod.POST)
    public CommonResult<Map<String, Map<String, String>>> getPricingParams() {
        return CommonResult.ok(activityNewService.getPricingParams());
    }

    /**
     * 临保风险品进特价活动线上化定时任务
     * @param key
     * @return Boolean
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/risk-job", method = RequestMethod.POST)
    public CommonResult<Boolean> temporaryInsuranceRiskJob(@RequestParam("key") String key) {
        activityNewService.temporaryInsuranceRiskJob(key);
        return CommonResult.ok();
    }

    /**
     * 临保风险品库存定时任务
     * @param key
     * @return Boolean
     */
    @RequiresPermissions(value = {"activity:insert-update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/risk-stock-job", method = RequestMethod.POST)
    public CommonResult<Boolean> temporaryInsuranceRiskStockJob(@RequestParam("key") String key) {
        activityNewService.temporaryInsuranceRiskStockJob(key);
        return CommonResult.ok();
    }

}
