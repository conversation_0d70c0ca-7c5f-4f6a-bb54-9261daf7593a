package net.summerfarm.controller.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstant;
import net.summerfarm.common.exceptions.BizException;
import net.summerfarm.common.util.DingCallbackCrypto;
import net.summerfarm.contexts.Global;
import net.summerfarm.dingding.bo.DingdingCallBackBO;
import net.summerfarm.dingding.service.DingdingProcessInstanceService;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

import static net.summerfarm.common.constant.dingding.DingdingConstant.JSON_KEY_ENCRYPT;

@Slf4j
@RestController
@RequestMapping("/dingding")
public class DingdingCallBackController {

    /**
     * 相应钉钉回调时成功返回的值
     */
    private static final String CALLBACK_RESPONSE_SUCCESS = "success";


    @Autowired
    MqProducer mqProducer;

    @Resource
    DingdingProcessInstanceService dingdingProcessInstanceService;

    /**
     * 钉钉回调接口
     *
     * @param msgSignature 消息签名
     * @param timestamp    时间戳
     * @param nonce        随机字符串
     * @param callbackData success加密字符串
     * @return
     */
    @PostMapping("/event/flow-callback")
    public Map<String, String> flowCallback(@RequestParam(value = "msg_signature", required = false) String msgSignature,
                                            @RequestParam(value = "timestamp", required = false) String timestamp,
                                            @RequestParam(value = "nonce", required = false) String nonce,
                                            @RequestBody(required = false) JSONObject callbackData) {
        try {
            DingCallbackCrypto callbackCrypto = new DingCallbackCrypto(DingdingConstant.AES_TOKEN,
                    DingdingConstant.AES_KEY, DingdingConstant.APP_KEY);
            String decryptMsg = decodeMessage(msgSignature, timestamp, nonce, callbackData, callbackCrypto);
            verifyMsgAndSendMQMsg(decryptMsg);
            return callbackCrypto.getEncryptedMap(CALLBACK_RESPONSE_SUCCESS, System.currentTimeMillis(),
                    DingCallbackCrypto.Utils.getRandomStr(8));
        } catch (DingCallbackCrypto.DingTalkEncryptException e) {
            log.error("钉钉回调使用appKey解签失败，尝试使用corpId解签,msg_signature:{},timestamp:{},nonce:{},callbackData:{}", msgSignature,
                    timestamp, nonce, callbackData, e);
            try {
                DingCallbackCrypto callbackCrypto = new DingCallbackCrypto(DingdingConstant.AES_TOKEN,
                        DingdingConstant.AES_KEY, Global.CORPID);
                String decryptMsg = decodeMessage(msgSignature, timestamp, nonce, callbackData, callbackCrypto);
                verifyMsgAndSendMQMsg(decryptMsg);
                return callbackCrypto.getEncryptedMap(CALLBACK_RESPONSE_SUCCESS, System.currentTimeMillis(),
                        DingCallbackCrypto.Utils.getRandomStr(8));
            } catch (DingCallbackCrypto.DingTalkEncryptException ex) {
                log.error("钉钉回调使用corpId解签失败,msg_signature:{},timestamp:{},nonce:{},callbackData:{}", msgSignature,
                        timestamp, nonce, callbackData, e);
            }
        } catch (Exception e) {
            log.error("钉钉回调处理异常", e);
        }
        return null;
    }

    /**
     * 钉钉回调消息解密并发送mq消息
     *
     * @param msgSignature   消息签名
     * @param timestamp      时间戳
     * @param nonce          随机数
     * @param callbackData   回调数据
     * @param callbackCrypto 加解密对象
     * @throws DingCallbackCrypto.DingTalkEncryptException 加解密异常
     */
    private String decodeMessage(String msgSignature,
                                 String timestamp,
                                 String nonce,
                                 JSONObject callbackData,
                                 DingCallbackCrypto callbackCrypto) throws DingCallbackCrypto.DingTalkEncryptException {
        String encryptMsg = callbackData.getString(JSON_KEY_ENCRYPT);
        return callbackCrypto.getDecryptMsg(msgSignature, timestamp, nonce, encryptMsg);
    }

    /**
     * 验证消息并使用mq发送消息
     * 验证通过的情况下发送消息到消息队列
     *
     * @param decryptMsg 钉钉回调数据明文
     */
    private void verifyMsgAndSendMQMsg(String decryptMsg) {
        // 验证消息合法性
        boolean verifyResult = dingdingProcessInstanceService.verifyFlowValid(JSON.parseObject(decryptMsg));
        if (!verifyResult) {
            log.error("消息验证不通过,{}", decryptMsg);
        }
        DingdingCallBackBO dingdingCallBackBO = new DingdingCallBackBO();
        dingdingCallBackBO.setDecryptData(JSON.parseObject(decryptMsg));

        MQData mqData = new MQData();
        mqData.setType(MType.DINGDING_PROCESS_CALL_BACK.name());
        mqData.setData(dingdingCallBackBO);
        log.info("发送钉钉回调消息数据:{}", JSON.toJSONString(mqData));
        mqProducer.send("mall-list",null,JSON.toJSONString(mqData));
    }

}
