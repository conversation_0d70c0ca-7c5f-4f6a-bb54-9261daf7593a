package net.summerfarm.controller;

import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.input.FinanceBankFlowingWaterInput;
import net.summerfarm.service.FinanceBankFlowingWaterService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @title: FinanceBankFlowingWaterController
 * 招银收款单
 * @date 2022/6/2811:04
 */
@RestController
@RequestMapping("/financeBankFlowingWater")
public class FinanceBankFlowingWaterController {

    @Resource
    private FinanceBankFlowingWaterService financeBankFlowingWaterService;

    @ApiOperation(value = "收款流水列表", httpMethod = "GET", tags = "招银收款单")
    @RequiresPermissions(value = {"financeBankFlowingWater:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/list/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult list(@PathVariable int pageIndex, @PathVariable int pageSize, FinanceBankFlowingWaterInput financeBankFlowingWaterInput) {
        return financeBankFlowingWaterService.list(pageIndex, pageSize, financeBankFlowingWaterInput);
    }

    @ApiOperation(value = "撤销收款单流水", httpMethod = "GET", tags = "招银收款单")
    @RequiresPermissions(value = {"financeBankFlowingWater:cancel", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/cancel", method = RequestMethod.GET)
    public AjaxResult cancel(Long id) {
        return financeBankFlowingWaterService.cancel(id);
    }

    @ApiOperation(value = "收款单流水详情", httpMethod = "GET", tags = "招银收款单")
    @RequiresPermissions(value = {"financeBankFlowingWater:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public AjaxResult detail(Long id, Integer payType) {
        return financeBankFlowingWaterService.detail(id, payType);
    }

    @ApiOperation(value = "收款单流水下载", httpMethod = "GET", tags = "招银收款单")
    @RequiresPermissions(value = {"financeBankFlowingWater:download", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/download", method = RequestMethod.GET)
    public AjaxResult download(FinanceBankFlowingWaterInput financeBankFlowingWaterInput) {
        return financeBankFlowingWaterService.download(financeBankFlowingWaterInput);
    }

}
