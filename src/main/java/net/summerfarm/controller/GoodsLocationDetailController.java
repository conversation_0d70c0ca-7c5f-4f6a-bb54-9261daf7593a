package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.vo.GoodsLocationDetailVO;
import net.summerfarm.model.vo.GoodsTransferVO;
import net.summerfarm.service.GoodsLocationDetailService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> ct
 * create at:  2020/3/30  11:33
 */
@RestController
@Api(tags = "货位信息管理")
@RequestMapping(value = "/goods")
public class GoodsLocationDetailController {

    @Resource
    private GoodsLocationDetailService goodsLocationDetailService;
    
    @ApiOperation(value = "查询货位详情",httpMethod = "GET",tags = "货位信息")
    @ApiImplicitParam(name = "storeNo", value = "仓库编号", paramType = "query", required = true)
    @RequestMapping(value = "/select/locationDetail", method = RequestMethod.GET)
    public AjaxResult queryLocationDetail(Integer storeNo){
        return goodsLocationDetailService.selectLocationDetail(storeNo);
    }

    @ApiOperation(value = "查询货位信息",httpMethod = "GET",tags = "货位信息")
    @ApiImplicitParam(name = "storeNo", value = "仓库编号", paramType = "query", required = true)
    @RequestMapping(value = "/select/location", method = RequestMethod.GET)
    public AjaxResult queryLocation(Integer storeNo){
        return goodsLocationDetailService.selectGoodsLocation(storeNo);
    }


    @ApiOperation(value = "货位转移列表",httpMethod = "GET",tags = "货位信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"goods:transfer", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/record/list/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectRecordList(@PathVariable int pageIndex, @PathVariable int pageSize,GoodsLocationDetailVO goodsLocationDetailVO){
        return goodsLocationDetailService.selectGoodsDetailRecord(pageIndex, pageSize,goodsLocationDetailVO);
    }

    @RequestMapping(value = "/record/detail", method = RequestMethod.GET)
    public AjaxResult selectDetail(Integer id){
        return goodsLocationDetailService.selectGoodsRecordDetail(id);
    }

    @ApiOperation(value = "货位详情",httpMethod = "GET",tags = "货位信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequestMapping(value = "/locationDetail/glNo/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectGoodsLocationByGlNO(@PathVariable int pageIndex, @PathVariable int pageSize,GoodsLocationDetailVO goodsLocationDetailVO){
        return goodsLocationDetailService.selectGoodsLocationByGlNO(pageIndex, pageSize,goodsLocationDetailVO);
    }

    @ApiOperation(value = "新增货位转移",httpMethod = "GET",tags = "货位信息")
    @RequestMapping(value = "/insert/goodsTransfer", method = RequestMethod.POST)
    public AjaxResult insertGoodsTransfer( @RequestBody GoodsTransferVO goodsTransferVO){
        return goodsLocationDetailService.insertGoodsTransfer(goodsTransferVO);
    }

    @RequestMapping(value = "/select/glNo", method = RequestMethod.GET)
    public AjaxResult selectGoodsByGlNo(String glNo){
        return goodsLocationDetailService.selectGoodsByGlNo(glNo);
    }

    @ApiOperation(value = "查询货位转移详情",httpMethod = "GET",tags = "货位信息")
    @ApiImplicitParam(name = "id",value = "货位转移id",paramType = "query",required = true)
    @RequestMapping(value = "/select/goodsTransfer", method = RequestMethod.GET)
    public AjaxResult queryGoodsTransferDetail(Integer id){
        return goodsLocationDetailService.queryGoodsTransferDetail(id);
    }
}
