package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.PurchasesConfig;
import net.summerfarm.model.vo.PurchasesConfigVO;
import net.summerfarm.service.BiStockUpService;
import net.summerfarm.service.PurchasesConfigService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "采购信息配置")
@RestController
@RequestMapping("/purchases-config")
public class PurchasesConfigController {

    @Resource
    private PurchasesConfigService purchasesConfigService;
    @Resource
    private BiStockUpService biStockUpService;

    @ApiOperation(value = "采购信息新增",httpMethod = "POST",tags = "采购信息配置")
    @RequiresPermissions(value = {"purchases-config:save",Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/save",method = RequestMethod.POST)
    public AjaxResult save(@Validated(value = {Add.class}) PurchasesConfig purchasesConfig, BindingResult result){
        if (result.hasFieldErrors()){
            return AjaxResult.getError(result.getFieldError().getDefaultMessage());
        }
        return purchasesConfigService.save(purchasesConfig);
    }

    @ApiOperation(value = "采购信息删除",httpMethod = "DELETE",tags = "采购信息配置")
    @ApiImplicitParam(name = "id",value = "id",paramType = "path",required = true)
    @RequiresPermissions(value = {"purchases-config:delete", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/delete/{id}",method = RequestMethod.DELETE)
    public AjaxResult delete(@PathVariable Integer id){
        return purchasesConfigService.delete(id);
    }

    @ApiOperation(value = "采购信息修改",httpMethod = "PUT",tags = "采购信息配置")
    @RequiresPermissions(value = {"purchases-config:update",Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/update",method = RequestMethod.PUT)
    public AjaxResult update(@RequestBody @Validated(value = {Update.class}) PurchasesConfig purchasesConfig, BindingResult bindingResult){
        if (bindingResult.hasFieldErrors()){
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }
        return purchasesConfigService.update(purchasesConfig);
    }


    @ApiOperation(value = "采购信息查询",httpMethod = "GET",tags = "采购信息配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true),
    })
    @RequiresPermissions(value = {"purchases-config:select",Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/{pageIndex}/{pageSize}",method = RequestMethod.GET)
    public AjaxResult selectList(@PathVariable int pageIndex, @PathVariable int pageSize, PurchasesConfigVO purchasesConfigVO){
        return purchasesConfigService.selectList(pageIndex,pageSize,purchasesConfigVO);
    }

    @ApiOperation(value = "采购商品管理",httpMethod = "GET",tags = "采购信息配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true),
    })
    @RequiresPermissions(value = {"purchases-config:select",Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/list/{pageIndex}/{pageSize}",method = RequestMethod.GET)
    public AjaxResult selectDataList(@PathVariable int pageIndex,@PathVariable int pageSize,PurchasesConfigVO purchasesConfigVO){
        return purchasesConfigService.selectDataList(pageIndex,pageSize,purchasesConfigVO);
    }

    /*@ApiOperation(value = "备货量查询",httpMethod = "GET",tags = "采购信息配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页数", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true),
    })
    @RequiresPermissions(value = {"stock-up:select",Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/stock-up/{pageIndex}/{pageSize}",method = RequestMethod.GET)
    public AjaxResult stockUpQuery(@PathVariable int pageIndex, @PathVariable int pageSize, BiStockUpVO stockUpVO){
        return purchasesConfigService.stockUpList(pageIndex,pageSize,stockUpVO);
    }

    @ApiOperation(value = "备货量配置查询",httpMethod = "GET",tags = "采购信息配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页数", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true),
    })
    @RequiresPermissions(value = {"stock-up:select",Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/stock-up-config/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult stockUpConfigList(@PathVariable int pageIndex, @PathVariable int pageSize, BiStockUpConfigVO biStockUpConfigVO) {
        return purchasesConfigService.stockUpConfigList(pageIndex, pageSize, biStockUpConfigVO);
    }

    @ApiOperation(value = "备货量配置新增",httpMethod = "POST",tags = "采购信息配置")
    @RequiresPermissions(value = {"stock-up:update",Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/stock-up-config/save", method = RequestMethod.POST)
    public AjaxResult stockUpConfigSave(@RequestBody @Validated(value = Add.class) BiStockUpConfig biStockUpConfig, BindingResult result) {
        if (result.hasFieldErrors()) {
            return AjaxResult.getError(result.getFieldError().getDefaultMessage());
        }
        return purchasesConfigService.stockUpConfigSave(biStockUpConfig);
    }

    @ApiOperation(value = "备货量配置删除", httpMethod = "DELETE", tags = "采购信息配置")
    @ApiImplicitParam(name = "id", value = "备货量配置id", paramType = "path", required = true)
    @RequiresPermissions(value = {"stock-up:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/stock-up-config/delete/{id}", method = RequestMethod.DELETE)
    public AjaxResult stockUpConfigDelete(@PathVariable Integer id) {
        return purchasesConfigService.stockUpConfigDelete(id);
    }


    @ApiOperation(value = "备货量数据下载",httpMethod = "GET",tags = "采购信息配置")
    @RequiresPermissions(value = {"stock-up:download",Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/stock-up/download",method = RequestMethod.GET)
    public AjaxResult stockUpDownload(BiStockUpVO stockUpVO){
        return purchasesConfigService.stockUpDownload(stockUpVO);
    }*/

    @ApiOperation(value = "采购信息配置详情查询",httpMethod = "GET",tags = "采购信息配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sku", value = "采购信息配置sku", paramType = "query", required = true),
            @ApiImplicitParam(name = "storeNo", value = "仓库编号", paramType = "query", required = true)
    })
    @RequestMapping(value = "/detail",method = RequestMethod.GET)
    public AjaxResult selectPurchasesConfig(String sku ,Integer storeNo){
        return AjaxResult.getOK(purchasesConfigService.selectPurchasesConfig(sku,storeNo));
    }

    @ApiOperation(value = "采购商品管理导出",httpMethod = "GET",tags = "采购信息配置")
    @RequiresPermissions(value = {"purchases-config:select",Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/download",method = RequestMethod.GET)
    public void download(PurchasesConfigVO purchasesConfigVO){
        purchasesConfigService.download(purchasesConfigVO);
    }
}
