package net.summerfarm.controller.saasPurchase;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.annotation.CheckSaasToken;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.SaasThreadLocalUtil;
import net.summerfarm.model.input.SaasSupplierInput;
import net.summerfarm.model.input.SaasSupplierReq;
import net.summerfarm.service.CategoryService;
import net.summerfarm.service.EnterpriseInformationService;
import net.summerfarm.service.InvoiceConfigService;
import net.summerfarm.service.SupplierService;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> chenjie
 * @date : 2022-12-14 13:15
 * @describe :
 */
@Api(tags = "SAAS供应商管理")
@RestController
@CheckSaasToken
@RequestMapping(value = "/summerfarm-manage/saas-supplier")
public class SaasSupplierController {
    @Resource
    InvoiceConfigService invoiceConfigService;
    @Resource
    private EnterpriseInformationService enterpriseInformationService;
    @Resource
    private SupplierService supplierService;

    @Resource
    private CategoryService categoryService;

    @ApiOperation(value = "天眼查查询信息", httpMethod = "GET")
    @RequestMapping(value = "/selectTianYanCha", method = RequestMethod.GET)
    public AjaxResult selectTianYanCha(String name) {
        return invoiceConfigService.selectTianYanCha(name);
    }
    @ApiOperation(value = "查询已保存的工商名称", httpMethod = "GET")
    @RequestMapping(value = "/businessAll", method = RequestMethod.GET)
    public AjaxResult selectAll(String name) {
        return enterpriseInformationService.selectAll(name);
    }

    @ApiOperation(value = "根据税号查询工商信息", httpMethod = "GET")
    @RequestMapping(value = "/byTaxNum", method = RequestMethod.GET)
    public AjaxResult selectByTaxNum(@RequestParam String taxNum) {
        return enterpriseInformationService.selectByTaxNum(taxNum);
    }

    @RequestMapping(value = "/list/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, SaasSupplierReq req) {
        req.setTenantId(SaasThreadLocalUtil.getTenantId());
        return supplierService.selectWithSaasTenantId(pageIndex, pageSize, req);
    }

    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    public AjaxResult saveOrUpdate(@RequestBody @Validated SaasSupplierInput input) {
        input.setTenantId(SaasThreadLocalUtil.getTenantId());
        input.setCreator(SaasThreadLocalUtil.getTenantAccountName());
        int maxAccountNum = 10;
        if (!CollectionUtils.isEmpty(input.getAccountList()) && input.getAccountList().size() > maxAccountNum) {
            throw new DefaultServiceException("最多可添加10个付款帐号");
        }
        return supplierService.saveTenantSupplier(input);
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public AjaxResult detail(@RequestParam Integer supplierId) {
        return supplierService.saasSupplierDetail(supplierId);
    }

    @RequestMapping(value = "/allSuppliers", method = RequestMethod.GET)
    public AjaxResult allSuppliers() {
        return supplierService.allSuppliers(SaasThreadLocalUtil.getTenantId());
    }


    @GetMapping("/category")
    public AjaxResult selectAll(){
        return categoryService.select();
    }

}
