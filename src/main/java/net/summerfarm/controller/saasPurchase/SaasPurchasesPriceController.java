package net.summerfarm.controller.saasPurchase;

import com.github.pagehelper.PageInfo;
import net.summerfarm.annotation.CheckSaasToken;
import net.summerfarm.common.util.SaasThreadLocalUtil;
import net.summerfarm.model.input.purchase.*;
import net.summerfarm.model.vo.purchase.PurchasesPriceAmountVO;
import net.summerfarm.model.vo.purchase.PurchasesPriceSkuTimeVO;
import net.summerfarm.model.vo.purchase.PurchasesPriceVO;
import net.summerfarm.service.PurchasesPriceService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;


/** 采购价格管理 **/
@RestController
@CheckSaasToken
@RequestMapping(value = "/summerfarm-manage/purchases-price")
public class SaasPurchasesPriceController {
    @Resource
    private PurchasesPriceService purchasesPriceService;


    /** 根据saasSkuId获取税率 **/
    @PostMapping("/getTaxRate")
    public CommonResult<List<PurchasesPriceAmountVO>> getTaxRate(@RequestBody @Validated PurchasesPriceAmountInput input){
        return CommonResult.ok(purchasesPriceService.getTaxRate(input));
    }

    /** 采购价格列表 **/
    @PostMapping("/list")
    public CommonResult<PageInfo<PurchasesPriceVO>> priceList(@RequestBody @Validated PurchasesPriceQueryInput input){
        input.setTenantId(SaasThreadLocalUtil.getTenantId());
        input.setSource(SaasThreadLocalUtil.getSource());
        return CommonResult.ok(purchasesPriceService.priceList(input));
    }


    /** 采购价格新增 **/
    @PostMapping("/save")
    public CommonResult<Long> priceSave(@RequestBody @Validated PurchasesPriceSaveInput input){
        input.setTenantId(SaasThreadLocalUtil.getTenantId());
        input.setSource(SaasThreadLocalUtil.getSource());
        purchasesPriceService.save(input);
        return CommonResult.ok();
    }

    /** 采购价格修改 **/
    @PostMapping("/update")
    public CommonResult<Integer> priceUpdate(@RequestBody @Validated PurchasesPriceUpdateInput input){
        return CommonResult.ok(purchasesPriceService.update(input));
    }

    /** 获取sku的时间段 **/
    @PostMapping("/getTimeList")
    public CommonResult<List<PurchasesPriceSkuTimeVO> > getSkuTimeList(@RequestBody @Validated PurchasesPriceTimeInput input){
        return CommonResult.ok(purchasesPriceService.getSkuTimeList(input));
    }

    /** 采购价格导出 **/
    @PostMapping("/export")
    public CommonResult<Void> priceExport(@RequestBody @Validated PurchasesPriceQueryInput input) throws IOException {
        input.setTenantId(SaasThreadLocalUtil.getTenantId());
        input.setSource(SaasThreadLocalUtil.getSource());
        purchasesPriceService.exportData(input);
        return CommonResult.ok();
    }

}
