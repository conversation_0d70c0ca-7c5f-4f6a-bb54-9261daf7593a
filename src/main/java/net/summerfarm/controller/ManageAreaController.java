package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.input.QueryManageAreaInput;
import net.summerfarm.model.input.SaveManageAreaInput;
import net.summerfarm.service.ManageAreaService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "区域配置管理")
@RestController
@RequestMapping(value = "/managearea")
public class ManageAreaController {

    @Resource
    ManageAreaService manageAreaService;

    @ApiOperation(value = "区域配置查询",httpMethod = "GET",tags = "区域配置管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"manage-area:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/selectManageArea/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectManageArea(@PathVariable int pageIndex, @PathVariable int pageSize, QueryManageAreaInput queryManageAreaInput){
        return manageAreaService.selectManageArea(pageIndex,pageSize,queryManageAreaInput);
    }


    @ApiOperation(value = "区域配置编辑",httpMethod = "POST",tags = "区域配置管理")
    @RequiresPermissions(value = {"manage-area:save", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/saveManageArea", method = RequestMethod.POST)
    public AjaxResult saveManageArea(@RequestBody @Validated SaveManageAreaInput saveManageAreaInput){
        return manageAreaService.saveManageArea(saveManageAreaInput);
    }

    @ApiOperation(value = "区域配置删除",httpMethod = "DELETE",tags = "区域配置管理")
    @ApiImplicitParam(name = "id",value = "区域配置id",paramType = "path",required = true)
    @RequiresPermissions(value = {"manage-area:delete", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/deleteManageArea/{id}", method = RequestMethod.DELETE)
    public AjaxResult deleteManageArea(@PathVariable int id){
        return manageAreaService.deleteManageArea(id);
    }

    @ApiOperation(value = "模糊查询区域名称",httpMethod = "GET",tags = "区域配置管理")
    @ApiImplicitParam(name = "zoneName",value = "区域名称",paramType = "query",required = true)
    @RequestMapping(value = "/queryZoneName", method = RequestMethod.GET)
    @RequiresPermissions(value = {"manage-area:select", Global.SA},logical = Logical.OR)
    public AjaxResult queryZoneName(String zoneName){
        return manageAreaService.queryZoneName(zoneName);
    }


    @ApiOperation(value = "模糊查询负责人",httpMethod = "GET",tags = "区域配置管理")
    @ApiImplicitParam(name = "adminName",value = "adminname",paramType = "query",required = true)
    @RequestMapping(value = "/queryAdmin", method = RequestMethod.GET)
    @RequiresPermissions(value = {"manage-area:select", Global.SA},logical = Logical.OR)
    public AjaxResult queryAdmin(String adminName){
        return manageAreaService.queryAdmin(adminName);
    }


    @ApiOperation(value = "查询已存在城市",httpMethod = "GET",tags = "区域配置管理")
    @RequestMapping(value = "/queryExistArea", method = RequestMethod.GET)
    @RequiresPermissions(value = {"manage-area:select", Global.SA},logical = Logical.OR)
    public AjaxResult queryExistArea(){
        return manageAreaService.queryExistArea();
    }

    @RequestMapping(value = "/queryExistCity", method = RequestMethod.GET)
    public AjaxResult queryExistCity(){
        return manageAreaService.queryExistCity();
    }

}
