package net.summerfarm.controller;

import net.summerfarm.module.products.inbound.scheduler.RiseSkuPriceJob;
import net.summerfarm.module.products.inbound.scheduler.RollBackSkuPriceJob;
import net.summerfarm.service.TraceService;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/20 11:47
 */
@RestController
@RequestMapping("/trace")
public class TraceController {

    @Resource
    private TraceService traceService;

    @Resource
    private RiseSkuPriceJob riseSkuPriceJob;

    @Resource
    private RollBackSkuPriceJob rollBackSkuPriceJob;

    /**
     * 测试trace log
     */
    @PostMapping
    public void testTraceLog() {
        traceService.testTraceLog();
        traceService.asyTestTraceLog();
    }

    @PostMapping("/test")
    public void test(@RequestBody XmJobInput context) throws Exception {
        if (context.getJobId() == 1) {
            riseSkuPriceJob.processResult(context);
        } else {
            rollBackSkuPriceJob.processResult(context);
        }
    }
}
