package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.SettlementConfig;
import net.summerfarm.model.input.SettlementConfigReq;
import net.summerfarm.service.SettlementConfigService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-15
 */
@Api(tags = "结算单配置管理")
@RestController
@RequestMapping("settlementConfig")
public class SettlementConfigController {

    @Resource
    private SettlementConfigService settlementConfigService;

    @ApiOperation(value = "查询结算单配置",httpMethod = "GET",tags = "结算单管理")
    @RequiresPermissions(value = {Global.SA, "settlementConfig:select"}, logical = Logical.OR)
    @RequestMapping(value = "/selectReviewConfig", method = RequestMethod.GET)
    public AjaxResult selectAll() {
        return settlementConfigService.selectConfigList();
    }

    @ApiOperation(value = "创建或修改结算单配置",httpMethod = "POST",tags = "结算单管理")
    @RequiresPermissions(value = {Global.SA, "settlementConfig:update"}, logical = Logical.OR)
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    public AjaxResult saveOrUpdate(@RequestBody SettlementConfig config) {
        return settlementConfigService.saveOrUpdateBatch(config);
    }


}
