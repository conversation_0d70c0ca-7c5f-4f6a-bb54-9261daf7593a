package net.summerfarm.controller;

import net.summerfarm.model.domain.FinancialInvoiceAsyncTask;
import net.summerfarm.service.FinancialInvoiceAsyncTaskService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2024-07-19
 **/
@RestController
@RequestMapping(value = "/financial-invoice-async-task")
public class FinancialInvoiceAsyncTaskController {

    @Resource
    private FinancialInvoiceAsyncTaskService financialInvoiceAsyncTaskService;

    /**
     * 后门接口
     * 没有实际场景在用
     */
    @PostMapping(value = "/update")
    public CommonResult update(@RequestBody FinancialInvoiceAsyncTask financialInvoiceAsyncTask) {
        financialInvoiceAsyncTaskService.updateByPrimaryKeySelective(financialInvoiceAsyncTask);
        return CommonResult.ok();
    }
}
