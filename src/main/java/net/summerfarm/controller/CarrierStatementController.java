package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.CarrierStatement;
import net.summerfarm.model.domain.CarrierStatementExtras;
import net.summerfarm.model.vo.CarrierStatementVo;
import net.summerfarm.service.CarrierStatementService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Classname CarrierStatementController
 * @Description 配送结算单相关接口.
 * @Date 2021/12/31 15:23
 * @Created by hx
 */
@RestController
@RequestMapping("/carrier/statement")
public class CarrierStatementController {

    @Resource
    private CarrierStatementService carrierStatementService;

    /**
     * 分页查看承运商结算单.
     * @param pageIndex
     * @param pageSize
     * @param carrierStatement
     * @return
     */
    @RequestMapping(value = "/select/{pageIndex}/{pageSize}")
    @RequiresPermissions(value = {"carrier-statement:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectCarrierStatement(@PathVariable int pageIndex, @PathVariable int pageSize, CarrierStatementVo carrierStatement){
        return carrierStatementService.selectCarrierStatement(carrierStatement,pageIndex,pageSize);
    }

    /**
     * 查看杂费.
     */
    @RequestMapping(value = "/select/extras")
    @RequiresPermissions(value = {"carrier-statement:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectCarrierStatementExtras(CarrierStatement carrierStatement){
        return carrierStatementService.selectCarrierStatementExtras(carrierStatement);
    }

    /**
     * 新增杂费.
     */
    @RequestMapping(value = "/extras/add",method = RequestMethod.POST)
    @RequiresPermissions(value = {"carrier-statement:save", Global.SA}, logical = Logical.OR)
    public AjaxResult addExtras(@RequestBody CarrierStatementExtras carrierStatementExtras){
        return carrierStatementService.addExtras(carrierStatementExtras);
    }

    /**
     * 编辑杂费.
     */
    @RequestMapping(value = "/extras/update",method = RequestMethod.POST)
    @RequiresPermissions(value = {"carrier-statement:save", Global.SA}, logical = Logical.OR)
    public AjaxResult updateExtras(@RequestBody CarrierStatementExtras carrierStatementExtras){
        return carrierStatementService.updateExtras(carrierStatementExtras);
    }

    /**
     * 导出配送结算单.
     */
    @RequestMapping(value = "/export",method = RequestMethod.GET)
    @RequiresPermissions(value = {"carrier-statement:export", Global.SA}, logical = Logical.OR)
    public AjaxResult exportCarrierStatement(CarrierStatementVo carrierStatement){
        return carrierStatementService.exportCarrierStatement(carrierStatement);
    }

    /**
     * 定时任务生成配送结算单
     */
    @RequestMapping(value = "/task",method = RequestMethod.GET)
    public AjaxResult carrierStatementTask(){
        return carrierStatementService.carrierStatementTask();
    }

    /**
     * 定时任务生成前置仓费
     */
    @RequestMapping(value = "/feeTask",method = RequestMethod.GET)
    public AjaxResult deliveryCarFeeTask(){
        return carrierStatementService.deliveryCarFeeTask();
    }



}
