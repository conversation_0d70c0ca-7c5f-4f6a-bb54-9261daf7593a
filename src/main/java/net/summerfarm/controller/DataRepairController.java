package net.summerfarm.controller;

import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.biz.finance.util.ExceptionUtil;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.MapUtil;
import net.summerfarm.enums.*;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.DeliveryPlanVO;
import net.summerfarm.model.vo.OrderItemVO;
import net.summerfarm.model.vo.OrderVO;
import net.summerfarm.service.StockTaskMsgService;
import net.summerfarm.service.StockTaskOrderSkuService;
import net.summerfarm.service.StockTaskService;
import net.summerfarm.service.WarehouseConfigService;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @desc 数据修复后门
 * <AUTHOR>
 * @Date 2023/1/5 12:10
 **/
@Slf4j
@RestController
@RequestMapping("/data-repair")
public class DataRepairController {

    @Resource
    private StockTaskService stockTaskService;
    @Resource
    private StockTaskMsgService stockTaskMsgService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private StockTaskMapper stockTaskMapper;
    @Resource
    private StockTaskItemMapper stockTaskItemMapper;
    @Resource
    private StockTaskOrderSkuService stockTaskOrderSkuService;
    @Resource
    private WarehouseConfigService warehouseConfigService;

    /**
     * 补发售后出库任务
     *
     * <AUTHOR>
     * @Date 2023/1/5 12:12
     **/
    @RequestMapping(value = "/afterSaleOutStore")
    public CommonResult<String> afterSaleOutStore(
            @RequestParam("deliveryDate") @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) LocalDate deliveryDate,
            @RequestParam("warehouseNo") Integer warehouseNo,
            @RequestParam("storeNo") Integer storeNo) {
        ExceptionUtil.checkAndThrow(Objects.nonNull(warehouseNo) && warehouseNo > 0, "请填写库存仓");
        ExceptionUtil.checkAndThrow(Objects.nonNull(storeNo) && storeNo > 0, "请填写城配仓");
        ExceptionUtil.checkAndThrow(Objects.nonNull(deliveryDate), "请填写配送日期");
        stockTaskService.createAfterSaleTask(warehouseNo, storeNo, deliveryDate);
        return CommonResult.ok("OK");
    }

    @RequestMapping("/createStockTaskByPlanId")
    public CommonResult<Integer> createStockTaskByPlanId(@RequestParam("deliveryPlanId") Integer deliveryPlanId) {
        ExceptionUtil.checkAndThrow(Objects.nonNull(deliveryPlanId) && deliveryPlanId > 0, "请填写配送计划ID");
        Integer stockTaskId = stockTaskService.createStockTaskByPlanId(deliveryPlanId);
        return CommonResult.ok(stockTaskId);
    }

    @RequestMapping("/sendStockTaskProcessMsg")
    public CommonResult<Boolean> sendStockTaskProcessMsg(@RequestParam("stockTaskProcessId") Integer stockTaskProcessId) {
        ExceptionUtil.checkAndThrow(Objects.nonNull(stockTaskProcessId) && stockTaskProcessId > 0, "请填写配送通知单ID");
        stockTaskMsgService.handleStockTaskSkuOutMsg(stockTaskProcessId);
        return CommonResult.ok(true);
    }

    @RequestMapping("/deleteCache")
    public CommonResult<String> sendStockTaskProcessMsg(@RequestParam("cacheKey") String cacheKeyPrefix) {
        Set<String> keys = redisTemplate.keys(cacheKeyPrefix  + "*");
        redisTemplate.delete(keys);
        return CommonResult.ok(JSONUtil.toJsonStr(keys));
    }

    @RequestMapping("/createStockTaskByOrderNoList")
    public CommonResult<List<Integer>> createStockTaskByOrderNoList(
            @RequestParam("exeDate") @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) LocalDate exeDate
            , @RequestBody List<String> orderNoList) {
        ExceptionUtil.checkAndThrow(!CollectionUtils.isEmpty(orderNoList), "请填写订单编号");
        List<DeliveryPlanRelationDO> deliveryPlanRelationDOList = deliveryPlanMapper.selectByOrderNoList(orderNoList);
        ExceptionUtil.checkAndThrow(!CollectionUtils.isEmpty(deliveryPlanRelationDOList), "请输入有效的订单编码");
        Map<String, List<DeliveryPlanRelationDO>> deliveryPlanMap = deliveryPlanRelationDOList.stream().collect(
                Collectors.groupingBy(item -> MapUtil.generateKey(item.getStoreNo(), item.getWarehouseNo())));

        List<Integer> result = Lists.newArrayList();
        deliveryPlanMap.values().forEach(deliveryPlanList -> {
            try {
                Integer storeNo = deliveryPlanList.get(0).getStoreNo();
                Integer warehouseNo = deliveryPlanList.get(0).getWarehouseNo();

                StockTask stockTask = new StockTask();
                stockTask.setAreaNo(warehouseNo);
                stockTask.setAddtime(LocalDateTime.now());
                stockTask.setType(StoreRecordType.SALE_OUT.getId());
                stockTask.setExpectTime(exeDate.plusDays(1).atTime(0, 0));
                stockTask.setState(StockTaskState.WAIT_IN_OUT.getId());
                stockTask.setOutStoreNo(storeNo);
                stockTask.setOutType(SaleOutTypeEnum.NORMAL.ordinal());
                stockTaskMapper.insert(stockTask);
                //根据sku分组
                Map<String, List<DeliveryPlanRelationDO>> skuOrderMap = deliveryPlanList.stream().collect(Collectors.groupingBy(DeliveryPlanRelationDO::getSku));

                //分组排序
                Set<String> skuSet = skuOrderMap.keySet();
                List<String> skuList = new ArrayList<>(skuSet);
                List<String> sortSkuList = skuList.stream().sorted().collect(Collectors.toList());
                //生成item
                sortSkuList.forEach(sku -> {
                    List<DeliveryPlanRelationDO> items = skuOrderMap.get(sku);
                    StockTaskItem item = new StockTaskItem();
                    item.setSku(sku);
                    item.setQuantity(items.stream().mapToInt(DeliveryPlanRelationDO::getAmount).sum());
                    item.setStockTaskId(stockTask.getId());
                    stockTaskItemMapper.insert(item);

                    try {
                        Map<String, List<DeliveryPlanRelationDO>> orderMap = items.stream().collect(Collectors.groupingBy(DeliveryPlanRelationDO::getOrderNo));
                        orderMap.forEach((orderNo, orderItemList) -> {

                            StockTaskOrderSku orderSku = StockTaskOrderSku.builder()
                                    .stockTaskId(stockTask.getId().longValue())
                                    .outOrderNo(orderNo)
                                    .sku(sku)
                                    .quantity(orderItemList.stream().mapToInt(DeliveryPlanRelationDO::getAmount).sum())
                                    .actualQuantity(NumberUtils.INTEGER_ZERO)
                                    .creator("销售出库")
                                    .operator("销售出库").build();
                            stockTaskOrderSkuService.insertStockTaskOrderSku(orderSku);

                        });
                    } catch (Exception e) {
                        log.error("销售/补发出库生成任务订单sku关系失败, orderItemList:{}", JSONUtil.toJsonStr(items),e);
                        // 精细化仓管理
                        if (warehouseConfigService.openCabinetManagement(stockTask.getAreaNo())) {
                            throw new BizException("销售/补发出库生成任务订单sku关系失败, \n换行告警");
                        }
                    }
                });
                result.add(stockTask.getId());
            } catch (Exception e) {
                log.error("出库任务创建失败 deliveryPlanList:{}",JSONUtil.toJsonStr(deliveryPlanList), e);
            }

        });

        return CommonResult.ok(result);

    }



}
