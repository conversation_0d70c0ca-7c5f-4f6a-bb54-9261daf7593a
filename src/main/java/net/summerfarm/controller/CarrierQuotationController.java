package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.CarrierQuotationArea;
import net.summerfarm.model.input.CarrierQuotationInput;
import net.summerfarm.service.CarrierQuotationService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * @Classname CarrierQuotationController
 * @Description 承运商报价单.
 * @Date 2021/12/30 14:41
 * @Created by hx
 */
@RestController
@RequestMapping("/carrier/quotation")
public class CarrierQuotationController {

    @Resource
    private CarrierQuotationService carrierQuotationService;

    /**
     * 分页查看承运商报价单
     * @param pageIndex
     * @param pageSize
     * @param carrierQuotationInput
     * @return
     */
    @RequestMapping(value = "/select/{pageIndex}/{pageSize}")
    @RequiresPermissions(value = {"carrier-quotation:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectCarrier(@PathVariable int pageIndex, @PathVariable int pageSize, CarrierQuotationInput carrierQuotationInput){
        return carrierQuotationService.selectCarrierQuotation(carrierQuotationInput,pageIndex,pageSize);
    }

    /**
     * 查看详情承运商报价单
     * @param id
     * @return
     */
    @RequestMapping(value = "/select/detail/{id}")
    @RequiresPermissions(value = {"carrier-quotation:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectCarrierQuotationDetail(@PathVariable Long id){
        return carrierQuotationService.selectCarrierQuotationDetail(id);
    }


    /**
     * 下载承运商报价单模板
     * @return
     */
    @RequestMapping(value = "/template")
    @RequiresPermissions(value = {"carrier-quotation:select", Global.SA}, logical = Logical.OR)
    public AjaxResult template(){
        return carrierQuotationService.template();
    }

    /**
     * 作废运商报价单
     * @return
     */
    @RequestMapping(value = "/delete/{id}",method = RequestMethod.DELETE)
    @RequiresPermissions(value = {"carrier-quotation:delete", Global.SA}, logical = Logical.OR)
    public AjaxResult delete(@PathVariable Long id){
        return carrierQuotationService.delete(id);
    }

    /**
     * 新增承运商报价单
     * @return
     */
    @RequestMapping(value = "/add",method = RequestMethod.POST)
    @RequiresPermissions(value = {"carrier-quotation:save", Global.SA}, logical = Logical.OR)
    public AjaxResult add(@RequestBody CarrierQuotationInput carrierQuotationInput){
        return carrierQuotationService.add(carrierQuotationInput);
    }


    /**
     * 批量新增承运商报价单
     * @return
     */
    @RequestMapping(value = "/batchAdd",method = RequestMethod.POST)
    @RequiresPermissions(value = {"carrier-quotation:save", Global.SA}, logical = Logical.OR)
    public AjaxResult batchAdd(@RequestParam("file") MultipartFile file){
        return carrierQuotationService.batchAdd(file);
    }

    /**
     * 更新承运商报价单
     * @return
     */
    @RequestMapping(value = "/update",method = RequestMethod.POST)
    @RequiresPermissions(value = {"carrier-quotation:save", Global.SA}, logical = Logical.OR)
    public AjaxResult update(@RequestBody CarrierQuotationInput carrierQuotationInput){
        return carrierQuotationService.update(carrierQuotationInput);
    }

    /**
     * 导出承运商报价单
     * @return
     */
    @RequestMapping(value = "/export",method = RequestMethod.GET)
    @RequiresPermissions(value = {"carrier-quotation:export", Global.SA}, logical = Logical.OR)
    public AjaxResult export( CarrierQuotationInput carrierQuotationInput){
        return carrierQuotationService.export(carrierQuotationInput);
    }

    /**
     * 查询所有服务区域
     */
    @RequestMapping(value = "/selectArea",method = RequestMethod.GET)
    @RequiresPermissions(value = {"carrier-quotation:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectArea(String serviceArea){
        return carrierQuotationService.selectArea(serviceArea);
    }

    /**
     * 根据省市查询已选市区
     */
    @RequestMapping(value = "/selectExistArea",method = RequestMethod.GET)
    public AjaxResult selectExistArea(CarrierQuotationArea carrierQuotationArea){
        return carrierQuotationService.selectExistArea(carrierQuotationArea);
    }


}
