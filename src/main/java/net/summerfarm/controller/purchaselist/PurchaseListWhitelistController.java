package net.summerfarm.controller.purchaselist;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.input.PurchaseListWhitelistInput;
import net.summerfarm.model.vo.purchaselist.PurchaseListWhitelistVO;
import net.summerfarm.service.purchaselist.PurchaseListWhitelistService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.RequiresAuthentication;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/purchase-list")
public class PurchaseListWhitelistController {

    @Resource
    private PurchaseListWhitelistService purchaseListWhitelistService;
    /**
     * 配置白名单
     * @param purchaseListWhitelistInput
     * @return
     */
    @RequiresAuthentication
    @PostMapping("/upsert/config")
    public CommonResult<Boolean> configWhitelist(@RequestBody PurchaseListWhitelistInput purchaseListWhitelistInput) {
        purchaseListWhitelistService.editPurchaseListWhitelist(purchaseListWhitelistInput);
        return CommonResult.ok();
    }


    /**
     * 获取白名单配置
     * @return
     */
    @RequiresAuthentication
    @PostMapping("/query/detail")
    public CommonResult<PurchaseListWhitelistVO> getWhitelistConfig() {
        return CommonResult.ok(purchaseListWhitelistService.getWhitelistGroup());
    }

    /**
     * 获取白名单配置
     * @return
     */
    @RequiresAuthentication
    @PostMapping("/upsert/del")
    public CommonResult<Boolean> delWhitelistConfig(@RequestBody PurchaseListWhitelistInput purchaseListWhitelistInput) {
        return CommonResult.ok(purchaseListWhitelistService.delWhitelistGroup(purchaseListWhitelistInput.getGroupId()));
    }
}
