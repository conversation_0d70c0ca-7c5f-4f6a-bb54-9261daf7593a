package net.summerfarm.controller.finance;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.SpringContextUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.FinanceAccountingAuditRecord;
import net.summerfarm.model.input.BillQueryInput;
import net.summerfarm.model.input.finance.AccountingPeriodOrderInput;
import net.summerfarm.model.input.finance.ReissueBillInput;
import net.summerfarm.model.vo.BillInfoVo;
import net.summerfarm.model.vo.finance.FinanceAccountingPeriodOrderResult;
import net.summerfarm.model.vo.finance.FinanceAccountingPeriodOrderSummary;
import net.summerfarm.model.vo.finance.FinancePeriodDetailVO;
import net.summerfarm.model.vo.finance.FinanceStoreDetailVo;
import net.summerfarm.service.finance.FinanceAccountingPeriodOrderService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.ibatis.annotations.Param;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;

/**
 * 账期订单-新
 *
 * <AUTHOR>
 * @Date 2023/3/30 18:26
 */
@Slf4j
@RestController
@RequestMapping(value = "/finance/accountingPeriodOrder")
public class FinanceAccountingPeriodOrderController {
    @Resource
    FinanceAccountingPeriodOrderService orderService;

    /**
     * 账期列表/信息统计
     *
     * @param query 订单查询条件
     * @return {@link CommonResult}<{@link PageInfo}<{@link FinanceAccountingPeriodOrderResult}>>
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:list", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/allList")
    public CommonResult<PageInfo<FinanceAccountingPeriodOrderSummary>> allList(@RequestBody AccountingPeriodOrderInput query) {
        return orderService.allList(query);
    }

    /**
     * 账期详情列表
     *
     * @param input 订单查询条件
     * @return {@link CommonResult}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:list", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/list")
    public CommonResult<PageInfo<FinanceAccountingPeriodOrderResult>> selectList(@RequestBody AccountingPeriodOrderInput input) {
        return orderService.listPeriod(input);
    }

    /**
     * 账单批量导出
     *
     * @param input 订单查询条件
     * @return {@link CommonResult}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:list", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/download")
    public CommonResult<Void> download(@RequestBody AccountingPeriodOrderInput input) {
        return orderService.downloadByCondition(input);
    }

    /**
     * 账单导出
     *
     * @param input 订单查询条件
     * @return {@link CommonResult}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:list", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/downloadByBillNo")
    public CommonResult<Long> downloadById(@RequestBody AccountingPeriodOrderInput input) {
        return orderService.storeDetailsDownLoad(input.getBillNumber());
    }

    /**
     * 账单更新
     *
     * @param input 订单查询条件
     * @return {@link CommonResult}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:update", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/update")
    public CommonResult<Void> updateById(@RequestBody AccountingPeriodOrderInput input) {
        return CommonResult.ok();
    }

    /**
     * 批量更新账单
     *
     * @param input 订单查询条件
     * @return {@link CommonResult}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:list", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/batch/update")
    public CommonResult<Void> batchUpdate(@RequestBody AccountingPeriodOrderInput input) {
        return orderService.billUpdate(input);
    }

    /**
     * 审核账单
     *
     * @param input 订单查询条件
     * @return {@link CommonResult}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:audit", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/audit")
    public CommonResult<Void> audit(@RequestBody AccountingPeriodOrderInput input) {
        return orderService.financialAudit(input);
    }

    /**
     * 确认账单
     *
     * @param input 订单查询条件
     * @return {@link CommonResult}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:confirm", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/confirm")
    public CommonResult<Void> confirm(@RequestBody AccountingPeriodOrderInput input) {
        return orderService.confirmBill(input.getId());
    }

    /**
     * 应收概况
     *
     * @param input 订单查询条件
     * @return {@link CommonResult}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:list", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/receivable-overview")
    public CommonResult<FinanceAccountingPeriodOrderSummary> receivableOverview(@RequestBody AccountingPeriodOrderInput input) {
        return orderService.receivableOverview(input);
    }

    /**
     * 逾期详情
     *
     * @param input 订单查询条件
     * @return {@link CommonResult}<{@link FinanceAccountingPeriodOrderSummary}>
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:list", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/overdue-details")
    public CommonResult<PageInfo<FinanceAccountingPeriodOrderSummary>> overdueDetails(@RequestBody AccountingPeriodOrderInput input) {
        return orderService.overdueDetails(input);
    }

    /**
     * 大客户账单补发
     *
     * @param input 补发条件
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:update", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/reissueBill/brand")
    public CommonResult<Void> brandReissueBill(@RequestBody ReissueBillInput input) {
        orderService.reissueBrandBill(input);
        return CommonResult.ok();
    }

    /**
     * 单店账单补发
     *
     * @param input 补发条件
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:update", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/reissueBill/singleStore")
    public CommonResult<Void> singleStoreReissueBill(@RequestBody ReissueBillInput input) {
        orderService.reissueSingleStoreBill(input);
        return CommonResult.ok();
    }

    /**
     * 账期生成
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:list", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/generateAccountingPeriodBill")
    public CommonResult<Void> generateAccountingPeriodBill() {
        orderService.generateAccountingPeriodBill();
        return CommonResult.ok();
    }

    /**
     * 批量查询账单信息
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:list", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/query/bills")
    public CommonResult<List<BillInfoVo>> selectBills(@RequestBody BillQueryInput input) {
        return CommonResult.ok(orderService.selectByBillNos(input.getBillNos()));
    }


    /**
     * 确认详情
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:list", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/query/audit-record")
    public CommonResult<List<FinanceAccountingAuditRecord>> auditRecord(@RequestBody AccountingPeriodOrderInput input) {
        return CommonResult.ok(orderService.listAuditRecord(input.getBillNumber()));
    }

    /**
     * 账单详情概览
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:list", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/query/bill-detail")
    public CommonResult<FinanceAccountingPeriodOrderSummary> billDetail(@RequestBody AccountingPeriodOrderInput input) {
        return CommonResult.ok(orderService.selectDetailByNameRemakes(input));
    }

    /**
     * 门店详情
     */
    @RequiresPermissions(value = {"accountingPeriodOrder:list", Global.SA}, logical = Logical.OR)
    @PostMapping(value = "/query/store-detail")
    public CommonResult<List<FinanceStoreDetailVo>> storeDetail(@RequestBody AccountingPeriodOrderInput input) {
        return CommonResult.ok(orderService.selectStoreDetailByBillNo(input));
    }
}
