package net.summerfarm.controller.bms;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.input.bms.BmsPaymentDocumentQuery;
import net.summerfarm.model.param.bms.BmsPaymentDocumentParam;
import net.summerfarm.module.bms.inbound.controller.PaymentDocCommandController;
import net.summerfarm.module.bms.inbound.controller.PaymentDocQueryController;
import net.summerfarm.module.bms.model.input.payment.PaymentDocCommitInput;
import net.summerfarm.module.bms.model.input.payment.PaymentDocQueryInput;
import net.summerfarm.module.bms.model.input.reconciliation.ReconciliationQueryInput;
import net.summerfarm.service.bms.BmsPaymentDocumentService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2022/8/29
 */
@RestController
@RequestMapping("/bms")
public class BmsPaymentDocumentController {

    @Resource
    private PaymentDocCommandController paymentDocCommandController;

    @Resource
    private PaymentDocQueryController paymentDocQueryController;


    @Resource
    private BmsPaymentDocumentService bmsPaymentDocumentService;

    @ApiOperation(value = "分页查询结算打款单", httpMethod = "POST", tags = "结算管理")
    @RequiresPermissions(value = {"payment-document:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/payment-document/list", method = RequestMethod.POST)
    public AjaxResult selectPaymentDocument(@RequestBody BmsPaymentDocumentQuery param) {
        if(param.getBusinessType()!=null){
            return AjaxResult.getOK(paymentDocQueryController.queryPaymentDocPage( JSON.parseObject(JSON.toJSONString(param), PaymentDocQueryInput.class)).getData());
        }
        return bmsPaymentDocumentService.selectPaymentDocument(param);
    }

    @ApiOperation(value = "查询结算打款单详情", httpMethod = "POST", tags = "结算管理")
    @RequiresPermissions(value = {"payment-document:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/payment-document/detail", method = RequestMethod.POST)
    public AjaxResult selectPaymentDocumentDetail(@RequestBody @Validated BmsPaymentDocumentQuery param) {
        if(param.getBusinessType()!=null){
            return AjaxResult.getOK(paymentDocQueryController.queryPaymentDetail( JSON.parseObject(JSON.toJSONString(param), PaymentDocQueryInput.class)).getData());
        }
        return bmsPaymentDocumentService.selectPaymentDocumentDetail(param);
    }

    @ApiOperation(value = "查询发票信息", httpMethod = "POST", tags = "结算管理")
    @RequiresPermissions(value = {"carrier-invoice:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/invoice/list", method = RequestMethod.POST)
    public AjaxResult selectCarrierInvoice(@RequestBody @Validated BmsPaymentDocumentParam param) {
        return bmsPaymentDocumentService.selectCarrierInvoice(param);
    }

    @ApiOperation(value = "提交打款单", httpMethod = "POST", tags = "结算管理")
    @RequiresPermissions(value = {"payment-document:insert", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upsert/payment-document/commit", method = RequestMethod.POST)
    public AjaxResult upsertPaymentDocument(@RequestBody BmsPaymentDocumentParam param) {
        if(param.getBusinessType()!=null){
            return AjaxResult.getOK(paymentDocCommandController.commitPaymentDoc( JSON.parseObject(JSON.toJSONString(param), PaymentDocCommitInput.class)).getData());
        }
        return bmsPaymentDocumentService.commitPaymentDocument(param);
    }

}
