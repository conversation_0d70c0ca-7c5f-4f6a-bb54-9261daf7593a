package net.summerfarm.controller.bms;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.input.bms.BmsDeliveryProcessQuery;
import net.summerfarm.model.input.bms.BmsSettleAccountQuery;
import net.summerfarm.service.bms.BmsQuotationProcessService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2022/9/9
 */
@RestController
@RequestMapping("/bms")
@Deprecated
@Slf4j
public class BmsQuotationProcessController {

    @Resource
    private BmsQuotationProcessService bmsQuotationProcessService;

    @ApiOperation(value = "操作记录", httpMethod = "POST", tags = "结算管理")
    @RequiresPermissions(value = {"quotation-process:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/query/quotation-process/details", method = RequestMethod.POST)
    public AjaxResult selectAccountFeeDetails(@RequestBody BmsDeliveryProcessQuery param) {
        log.info("[upgrade bidder]selectAccountFeeDetails");
        return bmsQuotationProcessService.selectQuotationProcess(param);
    }
}
