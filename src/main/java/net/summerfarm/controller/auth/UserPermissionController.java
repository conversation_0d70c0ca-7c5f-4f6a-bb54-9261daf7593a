package net.summerfarm.controller.auth;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.contexts.Global;
import net.summerfarm.controller.auth.req.UserPermissionReq;
import net.summerfarm.mapper.manage.AdminDataPermissionMapper;
import net.summerfarm.mapper.manage.AdminMapper;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.domain.AdminDataPermission;
import net.xianmu.common.input.BasePageInput;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/user_warehouse_permission")
@Slf4j
public class UserPermissionController {

    @Resource
    private AdminDataPermissionMapper adminDataPermissionMapper;

    @Resource
    private AdminMapper adminMapper;

    @PostMapping("/query/page")
    public CommonResult<PageInfo<Admin>> userWarehousePermission(@RequestBody UserPermissionReq input) {
        List<Integer> queryAdminIds = Lists.newArrayList();
        if (StringUtils.isNotEmpty(input.getUserName())) {
            List<Admin> queryAdmins = adminMapper.listByRealNameLike(input.getUserName());
            queryAdminIds = queryAdmins.stream().map(Admin::getAdminId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(queryAdminIds)) {
                log.info("根据名称未查询到数据!userName:{}", input.getUserName());
                return CommonResult.ok(new PageInfo<>(Lists.newArrayList()));
            }
        }
        List<Integer> finalQueryAdminIds = queryAdminIds;
        PageInfo<AdminDataPermission> pageInfo = PageHelper.startPage(input.getPageNum(), input.getPageSize()).doSelectPageInfo(() ->
                adminDataPermissionMapper.selectByWarehouseNo(input.getWarehouseNo(), finalQueryAdminIds));
        List<AdminDataPermission> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return CommonResult.ok(new PageInfo<>(Lists.newArrayList()));
        }
        List<Integer> adminIds = list.stream().map(AdminDataPermission::getAdminId).collect(Collectors.toList());
        List<Admin> admins = adminMapper.listNameByAdminId(adminIds);
        PageInfo<Admin> adminPageInfo = new PageInfo<>(admins);
        adminPageInfo.setTotal(pageInfo.getTotal());
        adminPageInfo.setHasNextPage(pageInfo.isHasNextPage());
        return CommonResult.ok(adminPageInfo);
    }
}
