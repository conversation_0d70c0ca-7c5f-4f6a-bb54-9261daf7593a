package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2022年05月06日
 */
@RestController
public class HeartbeatController {

    /**
     * 检测服务是否正常
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/ok", method = RequestMethod.GET)
    public AjaxResult OK() {
        return AjaxResult.getOK();
    }

}
