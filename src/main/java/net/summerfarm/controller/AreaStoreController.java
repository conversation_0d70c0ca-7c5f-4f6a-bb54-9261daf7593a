package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.OtherStockChangeTypeEnum;
import net.summerfarm.model.domain.AreaStore;
import net.summerfarm.model.domain.QuantityChangeRecord;
import net.summerfarm.service.AreaStoreService;
import net.summerfarm.service.QuantityChangeRecordService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/4/26
 */
@Api(tags = "城市实际库存控制类")
@RestController
@RequestMapping(value = "/area-store")
@Slf4j
public class AreaStoreController {

    @Resource
    private AreaStoreService areaStoreService;
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;


    public static final String KEY_ID = "lock:LockId:safeQuantity";


    /**
     * 修改采购提前期
     *
     * @param updateKeys
     * @return
     */
    @ApiOperation(value = "修改采购提前期",httpMethod = "POST",tags = "城市实际库存控制类")
    @RequiresPermissions(value = {"area-store:lead-time", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/lead-time", method = RequestMethod.POST)
    public AjaxResult updateLeadTime(AreaStore updateKeys) {
        return areaStoreService.updateLeadTime(updateKeys);
    }

    /**
     * 修改采购人
     *
     * @param updateKeys
     * @return
     */
    @ApiOperation(value = "修改采购人",httpMethod = "POST",tags = "城市实际库存控制类")
    @RequiresPermissions(value = {"area-store:purchaser", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchaser", method = RequestMethod.POST)
    public AjaxResult updatePurchaser(AreaStore updateKeys) {
        return areaStoreService.updatePurchaser(updateKeys);
    }

    /**
     * 修改虚拟库存
     *
     * @return
     */
    @ApiOperation(value = "修改虚拟库存", httpMethod = "POST", tags = "城市实际库存控制类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeNo", value = "物流中心编号", paramType = "query", required = true),
            @ApiImplicitParam(name = "sku", value = "sku", paramType = "query", required = true),
            @ApiImplicitParam(name = "onlineQuantity", value = "线上库存", paramType = "query", required = true)
    })
    @RequiresPermissions(value = {"area-store:purchaser", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/update-online", method = RequestMethod.POST)
    public AjaxResult updateOnlineQuantity(Integer storeNo, String sku, Integer onlineQuantity) {
        if (storeNo == null || StringUtils.isBlank(sku) || onlineQuantity == null) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        areaStoreService.updateOnlineStockByStoreNo(false, onlineQuantity, sku, storeNo, OtherStockChangeTypeEnum.ONLINE_STOCK_CHANGE, null, recordMap,1);
        quantityChangeRecordService.insertRecord(recordMap);
        return AjaxResult.getOK();
    }

    /**
     * 查看sku可以使用的仓库
     *
     * @return
     */
    @ApiOperation(value = "查看所有可以使用的仓库", httpMethod = "POST", tags = "城市实际库存控制类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeNo", value = "城市编号", paramType = "query", required = true),
            @ApiImplicitParam(name = "sku", value = "sku编号", paramType = "query", required = true)
    })
    @RequiresPermissions(value = {"area:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/optional-store", method = RequestMethod.GET)
    public AjaxResult effectiveStore(Integer storeNo, String sku) {
        return areaStoreService.optionalStore(storeNo, sku);
    }

    /**
     * 修改sku状态
     */
    @ApiOperation(value = "修改sku状态", httpMethod = "GET", tags = "城市实际库存控制类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "warehouseNo", value = "城市编号", paramType = "query", required = true),
            @ApiImplicitParam(name = "sku", value = "sku编号", paramType = "query", required = true)
    })
    @RequiresPermissions(value = {"area:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/update-status", method = RequestMethod.GET)
    public AjaxResult updateStockExt(Integer warehouseNo, String sku, Integer status) {
        return areaStoreService.updateStockExt(warehouseNo, sku, status);
    }
}
