package net.summerfarm.controller.srm;

import lombok.RequiredArgsConstructor;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.input.srm.SrmSupplierStockArrangeQuery;
import net.summerfarm.model.vo.StockArrangeVO;
import net.summerfarm.model.vo.srm.SrmStockArrangeVO;
import net.summerfarm.service.StockArrangeService;
import net.summerfarm.service.srm.SrmSupplierStockArrangeService;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * Description:供应商发货管理Controller层
 * <AUTHOR>
 */
@RestController
@RequestMapping("/srm/stock/arrange")
@RequiredArgsConstructor
public class SrmSupplierStockArrangeController {
    @Resource
    StockArrangeService stockArrangeService;
    @Resource
    SrmSupplierStockArrangeService srmSupplierStockArrangeService;


    /**
     * 发货管理列表
     */
    @RequestMapping(value = "/select/{pageIndex}/{pageSize}",method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, @Validated SrmSupplierStockArrangeQuery srmSupplierStockArrangeQuery, BindingResult result) {
        if (result.hasFieldErrors()) {
            return AjaxResult.getErrorWithMsg(Objects.requireNonNull(result.getFieldError()).getDefaultMessage());
        }
        return srmSupplierStockArrangeService.select(pageIndex, pageSize, srmSupplierStockArrangeQuery);
    }

    /**
     * 根据采购单查看发货情况
     */
    @Deprecated
    @RequestMapping(value = "/list/{purchasesNo}", method = RequestMethod.GET)
    public AjaxResult<SrmStockArrangeVO> selectStockArrangeList(@PathVariable String purchasesNo) {
        return stockArrangeService.selectSrmStockArrangeDetail(purchasesNo);
    }

    /**
     * 查看发货详情
     */
    @Deprecated
    @RequestMapping(value = "/detail/{stockArrangeId}", method = RequestMethod.GET)
    public AjaxResult<SrmStockArrangeVO> selectStockArrangeDetail(@PathVariable Integer stockArrangeId) {
        return stockArrangeService.selectSrmStockArrangeDetail(stockArrangeId);
    }

    /**
     * 查看预约条目详情
     */
    @RequestMapping(value = "item/detail/{stockArrangeItemId}", method = RequestMethod.GET)
    public AjaxResult selectStockArrangeItemDetail(@PathVariable Integer stockArrangeItemId) {
        return stockArrangeService.selectStockArrangeItemDetail(stockArrangeItemId);
    }

    /**
     * 查看预约证件详情
     */
    @RequestMapping(value = "/batchProve/detail/{stockArrangeItemDetailId}", method = RequestMethod.GET)
    public AjaxResult selectBatchProveDetail(@PathVariable Integer stockArrangeItemDetailId) {
        return stockArrangeService.selectBatchProveDetail(stockArrangeItemDetailId);
    }

    /**
     * 发起入库预约
     */
    @RequestMapping(method = RequestMethod.POST)
    public AjaxResult insertStockArrange(@RequestBody StockArrangeVO record) {
        return stockArrangeService.insertStockArrange(record);
    }

    /**
     * 取消入库预约
     */
    @RequestMapping(value = "/cancel/{stockArrangeId}", method = RequestMethod.GET)
    public AjaxResult cancelStockArrange(@PathVariable Integer stockArrangeId) {
        return stockArrangeService.closeStockArrange(stockArrangeId);
    }

    /**
     * 查询仓库sku所需证件
     * @param warehouseNo 仓库编号
     * @param sku sku
     * @return 所需证件数组
     */
    @GetMapping("/standard")
    public AjaxResult selectBatchProveStandard(Integer warehouseNo,String sku){
        return stockArrangeService.selectBatchProveStandard(warehouseNo,sku);
    }

}
