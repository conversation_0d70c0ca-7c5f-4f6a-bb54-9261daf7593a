package net.summerfarm.controller;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.SaveReceiptInput;
import net.summerfarm.model.input.ReceiptBillConfirmInput;
import net.summerfarm.model.vo.FinanceReceiptVO;
import net.summerfarm.service.FinanceReceiptService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 收款单
 *
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2021年12月22日 16:16:00
 */
@RestController
@RequestMapping("/receivable")
public class FinanceReceiptController {

    @Resource
    private FinanceReceiptService financeReceiptService;

    /**
     * 应收核销-收款单-列表
     * @param pageIndex
     * @param pageSize
     * @param financeReceiptVO
     * @return
     */
    @RequiresPermissions(value = {"finance-receivable:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/listWriteOff/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult listWriteOff(@PathVariable int pageIndex, @PathVariable int pageSize, FinanceReceiptVO financeReceiptVO) {
        return financeReceiptService.listWriteOff(pageIndex, pageSize, financeReceiptVO);
    }

    /**
     * 应收核销-收款单-详情
     * @param id
     * @return
     */
    @RequestMapping(value = "/detailWriteOff", method = RequestMethod.GET)
    public AjaxResult detailWriteOff(Long id) {
        return financeReceiptService.detailWriteOff(id);
    }

    /**
     * 应收核销-收款单-确认核销
     * @param financeReceiptVO
     * @return
     */
    @RequiresPermissions(value = {"finance-receivable:confirmWriteOff", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/confirmWriteOff", method = RequestMethod.POST)
    public AjaxResult confirmWriteOff(@RequestBody FinanceReceiptVO financeReceiptVO) {
        return financeReceiptService.confirmWriteOff(financeReceiptVO);
    }

    /**
     * 应收核销-收款单-撤销
     * @param financeReceiptVO
     * @return
     */
    @RequiresPermissions(value = {"finance-receivable:cancel", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public AjaxResult cancel(@RequestBody FinanceReceiptVO financeReceiptVO) {
        return financeReceiptService.cancel(financeReceiptVO.getId());
    }

    /**
     * 应收核销-收款单-驳回
     * @param financeReceiptVO
     * @return
     */
    @RequiresPermissions(value = {"finance-receivable:cancel", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/reject", method = RequestMethod.POST)
    public AjaxResult reject(@RequestBody FinanceReceiptVO financeReceiptVO) {
        return financeReceiptService.reject(financeReceiptVO.getId());
    }

    /**
     * 账单管理-收款流水认领（核销单）
     * @param financeReceiptVOS
     * @return
     */
    @RequiresPermissions(value = {"finance-receivable:saveReceipt", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/saveReceipt", method = RequestMethod.POST)
    public AjaxResult saveReceipt(@RequestBody FinanceReceiptVO financeReceiptVOS) {
        return financeReceiptService.saveReceipt(financeReceiptVOS);
    }

    /**
     * 应收-账期订单-确认-收款详情
     * @param id
     * @return
     */
    @RequestMapping(value = "/detailsBillReceipt", method = RequestMethod.GET)
    public AjaxResult detailsBillReceipt(Long id) {
        return financeReceiptService.detailsBillReceipt(id);
    }

    /**
     * 应收-账期订单-确认-收款详情(后台)
     * @param id
     * @return
     */
    @RequestMapping(value = "/details/billReceipt", method = RequestMethod.GET)
    public AjaxResult billReceipt(Long id,Long financeBankFlowingWaterId) {
        return financeReceiptService.billReceipt(id,financeBankFlowingWaterId);
    }

    /**
     * 账单核销
     * @param saveReceiptInput
     * @return
     */
    @PostMapping(value = "/details/claim/bill")
    @RequiresPermissions(value = {"finance-receivable:saveReceipt", Global.SA}, logical = Logical.OR)
    public CommonResult<Void> claimBill(@Valid @RequestBody SaveReceiptInput saveReceiptInput) {
        saveReceiptInput.setAuditFlag(Boolean.FALSE);
        financeReceiptService.claimBill(saveReceiptInput);
        return CommonResult.ok();
    }

    /**
     * 鲜沐卡充值
     * @param saveReceiptInput
     * @return
     */
    @PostMapping(value = "/details/claim/recharge")
    @RequiresPermissions(value = {"finance-receivable:saveReceipt", Global.SA}, logical = Logical.OR)
    public CommonResult<Void> claimRecharge(@Valid @RequestBody SaveReceiptInput saveReceiptInput) {
        saveReceiptInput.setAuditFlag(Boolean.FALSE);
        financeReceiptService.claimRecharge(saveReceiptInput);
        return CommonResult.ok();
    }

    /**
     * 代下单鲜沐卡充值
     * @param saveReceiptInput
     * @return
     */
    @PostMapping(value = "/upsert/help-order/claim/recharge")
    @RequiresPermissions(value = {"finance-receivable:saveReceipt", Global.SA}, logical = Logical.OR)
    public CommonResult<Void> claimHelpOrderRecharge(@Valid @RequestBody SaveReceiptInput saveReceiptInput) {
        saveReceiptInput.setAuditFlag(Boolean.TRUE);
        financeReceiptService.claimRecharge(saveReceiptInput);
        return CommonResult.ok();
    }
}
