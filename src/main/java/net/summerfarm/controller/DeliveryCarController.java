package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.DeliveryCar;
import net.summerfarm.service.DeliveryCarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * Created by wjd on 2018/4/24.
 */
@Api(tags = "配送车辆管理")
@RestController
@RequestMapping(value = "/deliverycar")
public class DeliveryCarController {

    @Resource
    private DeliveryCarService deliveryCarService;


    @ApiOperation(value = "配送车辆分页查询", httpMethod = "GET", tags = "配送车辆管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true),
            @ApiImplicitParam(name = "no", value = "仓库编号", paramType = "query", required = false),
            @ApiImplicitParam(name = "driver", value = "司机", paramType = "query", required = false),
            @ApiImplicitParam(name = "status", value = "状态：0、冻结 1、正常（默认）", paramType = "query", required = false)
    })
    @RequiresPermissions(value = {"deliverycar:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult activitySkuList(@PathVariable int pageIndex, @PathVariable int pageSize, DeliveryCar query) {
        return deliveryCarService.select(pageIndex, pageSize, query);
    }

    @ApiOperation(value = "新增配送车辆",httpMethod = "POST",tags = "配送车辆管理")
    @RequiresPermissions(value = {"deliverycar:insert", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public AjaxResult insert(@Validated(value = {Add.class}) DeliveryCar deliveryCar, BindingResult result) {
        if (result.hasFieldErrors()){
            return AjaxResult.getError(Objects.requireNonNull(result.getFieldError()).getDefaultMessage());
        }
        return deliveryCarService.insert(deliveryCar);
    }

    @ApiOperation(value = "修改配送车辆",httpMethod = "POST",tags = "配送车辆管理")
    @RequiresPermissions(value = {"deliverycar:update", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public AjaxResult update(@Validated(value = {Update.class}) DeliveryCar deliveryCar,BindingResult result) {
        if (result.hasFieldErrors()){
            return AjaxResult.getError(Objects.requireNonNull(result.getFieldError()).getDefaultMessage());
        }
        return deliveryCarService.update(deliveryCar);
    }

    @ApiOperation(value = "配送车辆列表",httpMethod = "GET",tags = "配送车辆管理")
    @RequiresPermissions(value = {"deliverycar:select",Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/list",method = RequestMethod.GET)
    public AjaxResult deliveryCarList(DeliveryCar deliveryCar){
        return deliveryCarService.deliveryCarList(deliveryCar);
    }


}
