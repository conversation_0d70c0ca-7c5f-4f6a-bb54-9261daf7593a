package net.summerfarm.controller;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.DTO.AdminMerchantDTO;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.domain.AdminDataPermission;
import net.summerfarm.model.input.DmsAccountApplyInput;
import net.summerfarm.model.input.MajorPriceInput;
import net.summerfarm.model.input.MajorPriceReq;
import net.summerfarm.model.vo.AdminVO;
import net.summerfarm.model.vo.MajorPriceVO;
import net.summerfarm.module.wms.infrastructure.facade.TenantQueryFacade;
import net.summerfarm.service.AdminService;
import net.summerfarm.service.DingTalkService;
import net.summerfarm.service.MajorPriceService;
import net.summerfarm.service.impl.AdminServiceImpl;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresAuthentication;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * @Package: net.summerfarm.controller
 * @Description: 管理员管理
 * @author: <EMAIL>
 * @Date: 2016/8/4
 */
@Api(tags = "管理员管理")
@RestController
@RequestMapping(value = "/admin")
public class AdminController {

    @Resource
    private AdminServiceImpl adminService;

    @Resource
    private MajorPriceService majorPriceService;
    @Resource
    private DingTalkService dingTalkService;
    @Resource
    private TenantQueryFacade tenantQueryFacade;

    private static final Lock lock = new ReentrantLock();

    /**
     * 分页查询
     *
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @return
     */
    @ApiOperation(value = "分页查询",httpMethod = "GET",tags = "管理员管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"admin:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, AdminVO selectKeys) {
        AjaxResult<PageInfo<AdminVO>> result = adminService.select(pageIndex, pageSize, selectKeys);
        // temp 自营仓项目过滤已入住saas 大客户
        if (Objects.nonNull(selectKeys.getType()) && selectKeys.getType() == 2) {
            // 查询已经入驻saas的客户
            List<TenantResp> tenantRespList = tenantQueryFacade.listSaasTenant();
            List<Long> saasAdminIdList = tenantRespList.stream().map(TenantResp::getAdminId).collect(Collectors.toList());
            result.getData().getList().forEach( data -> {
                if (saasAdminIdList.contains(data.getAdminId().longValue())) {
                    data.setSaasAdmin(Boolean.TRUE);
                }
            });
        }
        return result;
    }

    /**
     * 查询含有roleType角色的所有帐号
     *
     * @param roleType
     * @return
     */
    @ApiOperation(value = "查询含有roleType角色的所有帐号",httpMethod = "GET",tags = "管理员管理")
    @ApiImplicitParam(name = "roleType",value = "角色id列表 以，隔开",paramType = "query")
    @RequestMapping(value = "/all", method = RequestMethod.GET)
    public AjaxResult selectAll(Integer roleType) {
        return adminService.selectAll(roleType);
    }

    /**
     * 查询含有roleType角色的所有帐号
     *
     * @return
     */
    @ApiOperation(value = "查询含有roleType角色的所有帐号",httpMethod = "GET",tags = "管理员管理")
    @RequiresPermissions(value = {"admin:purchase", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/purchase", method = RequestMethod.GET)
    public AjaxResult selectPurchase() {
        return adminService.selectPurchase();
    }

    @ApiOperation(value = "查询含有roleType角色的所有帐号",httpMethod = "GET",tags = "管理员管理")
    @ApiImplicitParam(name = "roleType",value = "角色id列表 以，隔开",paramType = "query")
    @RequiresPermissions(value = {"admin:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/roleTypes", method = RequestMethod.GET)
    public AjaxResult selectByRoleTypes(String roleTypes) {
        return adminService.selectByRoleTypes(roleTypes);
    }


    @ApiOperation(value = "查询大客户信息",httpMethod = "GET",tags = "管理员管理")
    @RequiresPermissions(value = {"admin:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/selectMajor/info", method = RequestMethod.GET)
    public AjaxResult selectMajor() {
        return adminService.selectMajor();
    }

    /**
     * 管理员详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "管理员详情",httpMethod = "GET",tags = "管理员管理")
    @ApiImplicitParam(name = "id",value = "管理员id",paramType = "path",required = true)
    @RequiresPermissions(value = {"admin:selectWithRole", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable Integer id) {
        return adminService.selectWithRole(id);
    }

    /**
     * 新增用户
     *
     * @param record
     * @param bindingResult
     * @return
     */
    @ApiOperation(value = "新增用户",httpMethod = "POST",tags = "管理员管理")
    @RequiresPermissions(value = {"admin:save", Global.SA}, logical = Logical.OR)
    @RequestMapping(method = RequestMethod.POST)
    public AjaxResult save(@Validated({Add.class}) @RequestBody AdminVO record, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());//NOSONAR
        }
        return adminService.saveAdmin(record);
    }

    /**
     * 解锁/锁定用户
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "解锁/锁定用户",httpMethod = "PUT",tags = "管理员管理")
    @ApiImplicitParam(name = "id",value = "管理员id",paramType = "path",required = true)
    @RequiresPermissions(value = {"admin:lock", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{id}/lock", method = RequestMethod.PUT)
    public AjaxResult lockAdmin(@PathVariable int id, @RequestBody Admin admin) {
        return adminService.lock(id, admin);
    }

    /**
     * 分配角色及数据权限
     *
     * @param id
     * @param valueJson
     * @return
     */
    @ApiOperation(value = "分配角色",httpMethod = "PUT",tags = "管理员管理")
    @ApiImplicitParam(name = "id",value = "角色id",paramType = "path",required = true)
    @RequiresPermissions(value = {"admin:role", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{id}/role", method = RequestMethod.PUT)
    public AjaxResult specifiedRole(@RequestBody JSONObject valueJson, @PathVariable int id) {
        String roles = valueJson.getString("roleIds");
        List<Integer> roleIds = new ArrayList<>();
        if (StringUtils.isNotBlank(roles)){
            String[] split = roles.split(",");
            Arrays.stream(split).forEach(el -> roleIds.add(Integer.valueOf(el)));
        }
        String jsonString = valueJson.getString("dataPermissions");
        List<AdminDataPermission> dataPermissions = JSONObject.parseArray(jsonString, AdminDataPermission.class);
        return adminService.specifiedRoles(id, roleIds, dataPermissions);
    }

    /**
     * 修改密码
     *
     * @param username
     * @param valueJson
     * @return
     */
    @ApiOperation(value = "修改密码",httpMethod = "PUT",tags = "管理员管理")
    @ApiImplicitParam(name = "username",value = "用户名",paramType = "path",required = true)
    @RequiresAuthentication
    @RequestMapping(value = "/{username}/password", method = RequestMethod.PUT)
    public AjaxResult resetPassword(@PathVariable String username, @RequestBody JSONObject valueJson) {
        String old = valueJson.get("old") + "";
        String target = valueJson.get("target") + "";
        return adminService.resetPassword(username, old, target);
    }

    /**
     * 编辑个人信息
     */
    @ApiOperation(value = "编辑个人信息",httpMethod = "POST",tags = "管理员管理")
    @ApiImplicitParam(name = "id",value = "管理员id",paramType = "path",required = true)
    @RequiresAuthentication
    @RequestMapping(value = "/{id}", method = RequestMethod.POST)
    public AjaxResult update(@PathVariable int id, @RequestBody AdminVO adminVO) {
        adminVO.setAdminId(id);
        return adminService.update(adminVO);
    }


    @ApiOperation(value = "大客户价格模板下载",httpMethod = "GET",tags = "管理员管理")
    @RequiresPermissions(value = {"major:majortemplate", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/majortemplate", method = RequestMethod.GET)
    public void majortemplate() throws IOException {
        majorPriceService.majortemplate();
    }


    @ApiOperation(value = "大客户价格数据上传",httpMethod = "POST",tags = "管理员管理")
    @RequiresPermissions(value = {"major:majorprice", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/upload/majorprice", method = RequestMethod.POST)
    public AjaxResult majorPrice(@RequestBody MajorPriceVO majorPriceVO) {
            return majorPriceService.majorPrice(majorPriceVO);
    }

    @ApiOperation(value = "大客户报价单修改",httpMethod = "POST",tags = "管理员管理")
    @RequiresPermissions(value = {"major:majorprice", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/update/majorprice",method = RequestMethod.POST)
    public AjaxResult updateMajorPrice(@RequestBody MajorPriceReq majorPriceReq){
            return majorPriceService.updateMajorPrice(majorPriceReq);
    }

    @ApiOperation(value = "大客户报价单修改",httpMethod = "POST",tags = "管理员管理")
    @RequiresPermissions(value = {"major:majorprice", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/init/majorprice",method = RequestMethod.POST)
    public AjaxResult initMajorPrice(@RequestBody MajorPriceReq majorPriceReq){
        try {
            return majorPriceService.updateMajorPrice(majorPriceReq);
        } catch (Exception e) {
            return AjaxResult.getErrorWithMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "大客户报价单查询",httpMethod = "GET",tags = "管理员管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"rebate:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/majorprice/list/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult majorPriceList(@PathVariable int pageIndex, @PathVariable int pageSize,MajorPriceInput majorPriceInput) {
        if (majorPriceInput.getAdminId() == null || majorPriceInput.getDirect() == null){
            return AjaxResult.getErrorWithMsg("传入参数错误");
        }
        return majorPriceService.selectMajorPrice(pageIndex,pageSize,majorPriceInput);
    }

    @ApiOperation(value = "大客户报价单数据获取(从上传的excel获取)",httpMethod = "POST",tags = "管理员管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "adminId",value = "客户id",paramType = "query",required = true),
            @ApiImplicitParam(name = "direct",value = "直营加盟",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"major:majortemplate", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/get/template/data", method = RequestMethod.POST)
    public AjaxResult getTemplateData(Integer adminId, Integer direct, @RequestParam("file") MultipartFile file) {
        return majorPriceService.getTemplateData(adminId, direct, file);
    }

    @ApiOperation(value = "大客户报价单批量改价",httpMethod = "POST",tags = "管理员管理")
    @RequiresPermissions(value = {"major:majorprice", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/batchUpdate/majorprice", method = RequestMethod.POST)
    public AjaxResult batchUpdate(@RequestBody List<MajorPriceInput> majorPriceInputList){
        return majorPriceService.updateBatch(majorPriceInputList);
    }

    @ApiOperation(value = "查询大客户信息",httpMethod = "GET",tags = "管理员管理")
    @RequestMapping(value = "/queryByMid", method = RequestMethod.GET)
    public AjaxResult selectAdminbyMerchantId(Long mid){
       return adminService.selectAdminByMerchantId(mid);
    }

    @RequestMapping(value = "/dingTalkSync", method = RequestMethod.POST)
    @ResponseBody
    public Map<String,String> dingTalkSync(@RequestParam(value = "signature", required = false) String signature,
                                           @RequestParam(value = "timestamp", required = false) String timestamp,
                                           @RequestParam(value = "nonce", required = false) String nonce,
                                           @RequestBody(required = false) JSONObject json){
        return dingTalkService.DingTalkSyncProcess(signature,timestamp,nonce,json);
    }

    @ApiOperation(value = "大客户报价单数据导出", httpMethod = "GET", tags = "管理员管理")
    @RequiresPermissions(value = {"major:majortemplate", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/major-price/export", method = RequestMethod.GET)
    public void export(MajorPriceInput majorPriceInput) {
        majorPriceService.export(majorPriceInput);
    }

    @ApiOperation(value = "Saas大客户报价单数据导出", httpMethod = "GET", tags = "管理员管理")
    @RequiresPermissions(value = {"major:majortemplate", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/saas-major-price/export", method = RequestMethod.GET)
    public void exportSaasMajorPrice(MajorPriceInput majorPriceInput) {
        majorPriceService.exportSaasMajorPrice(majorPriceInput);
    }

    @ApiOperation(value = "发送验证码", httpMethod = "GET", tags = "管理员管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "认证类型：0 手机号 ,1 邮箱"),
            @ApiImplicitParam(name = "phoneOrEmail", value = "手机号 邮箱"),
    })
    @GetMapping("/send/code")
    public AjaxResult sendVerificationCode(String phoneOrEmail,String type){
        return adminService.sendVerificationCode( phoneOrEmail, type);
    }

    @ApiOperation(value = "更改密码", httpMethod = "GET", tags = "管理员管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "password", value = "密码",paramType = "query",required = true),
            @ApiImplicitParam(name = "code", value = "验证码",paramType = "query",required = true)
    })
    @PostMapping("/update/password")
    public AjaxResult updatePassword(String phoneOrEmail, String password, String code) {
        return adminService.updatePassword(phoneOrEmail, code, password);
    }

    @ApiOperation(value = "获取员工详情", httpMethod = "GET", tags = "管理员管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "部门id",paramType = "query",required = true)
    })
    @GetMapping("/dingTalk/idDetails")
    public AjaxResult getByIdStaffDetails(Long id){
        return dingTalkService.getByIdStaffDetails(id);
    }

    @GetMapping("/dingTalk/details")
    public void getStaffDetails(Long id){
        dingTalkService.getStaffDetails(id);
        return;
    }


    @ApiOperation(value = "根据用户名去获取管理员", httpMethod = "GET", tags = "管理员管理")
    @RequiresPermissions(value = {"admin:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/selectByUsername", method = RequestMethod.GET)
    public AjaxResult selectByUsername(@RequestParam("username") String username) {
        return adminService.selectByUsername(username);
    }




    @ApiOperation(value = "根据用户名去获取管理员", httpMethod = "GET", tags = "管理员管理")
    @RequestMapping(value = "/personalInfo", method = RequestMethod.GET)
    public CommonResult<AdminVO> personInfo() {
        return adminService.personalInfo();
    }
    /**
     * 根据品牌名称模糊搜索品牌
     * @param nameRemakes
     * @return
     */
    @RequestMapping(value = "/selectByNameRemakes", method = RequestMethod.GET)
    public AjaxResult selectByNameRemakes(@RequestParam("nameRemakes") String nameRemakes) {
        return adminService.selectByNameRemakes(nameRemakes);
    }

    /**
     * 查询非大客户的管理员账号
     * @return
     */
    @RequestMapping(value = "/selectNotMajorAdmin", method = RequestMethod.GET)
    public AjaxResult selectNotMajorAdmin() {
        return adminService.selectNotMajorAdmin();
    }

    /**
     * 创建dms账号
     * @param dmsAccountApplyInput 入参
     * @return
     */
    @RequestMapping(value = "/createDmsAccount", method = RequestMethod.POST)
    public AjaxResult createDmsAccount(@RequestBody DmsAccountApplyInput dmsAccountApplyInput) {
        adminService.createDmsAccount(dmsAccountApplyInput.getReason(),dmsAccountApplyInput.getDepartment());
        return AjaxResult.getOK();
    }

    @RequestMapping(value = "/bigMerchant-data/monthly")
    public AjaxResult queryBigMerchantDataMonthly(Admin selectKeys){
        return adminService.queryBigMerchantDataMonthly(selectKeys);
    }

    @PostMapping(value = "/query/contact-by-name_remakes")
    public CommonResult<List<AdminMerchantDTO>> queryContactByNameRemake(@RequestParam String nameRemakes){
        return adminService.queryContactByNameRemakes(nameRemakes);
    }

    /**
     * 默认密码统一初始化
     */
    @PostMapping("/upsert/default-pwd-init")
    public CommonResult<Void> defaultPwdInit(@RequestBody List<Integer> adminIds) {
        return adminService.defaultPwdInit(adminIds);
    }

    /**
     * 密码统一初始化
     */
    @PostMapping("/upsert/srm-default-pwd-init")
    public CommonResult<Void> srmDefaultPwdInit() {
        return adminService.srmDefaultPwdInit();
    }

    /**
     * 模糊查询管理员名称
     * @return
     */
    @PostMapping("/query/list-name")
    public CommonResult<List<Admin>> listByName(String name) {
        return adminService.listByName(name);
    }


    /**
     * 查询POP大客户信息
     * @return
     */
    @PostMapping("/query/list-pop-white")
    public CommonResult<List<AdminVO>> listPopWhite() {
        return CommonResult.ok(adminService.listPopWhite());
    }
}
