package net.summerfarm.controller;

import io.swagger.annotations.ApiImplicitParams;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.redis.TransferLock;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.input.StockTaskProcessReq;
import net.summerfarm.model.input.StockTaskReq;
import net.summerfarm.model.vo.TransformVO;
import net.summerfarm.service.TransferService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Api(tags = "库存转换")
@RestController
@RequestMapping(value = "/transfer")
public class TransferController {

    @Resource
    private TransferService transferService;

    @Resource
    private TransferLock transferLock;

    @ApiOperation(value = "转换任务列表", httpMethod = "GET", tags = "库存转换")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"store-stock:task-select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/list/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult taskList(@PathVariable int pageIndex, @PathVariable int pageSize, StockTaskReq stockTaskReq) {
        return transferService.stockList(pageIndex, pageSize, stockTaskReq);
    }


    @ApiOperation(value = "库存转换", httpMethod = "POST", tags = "库存转换")
    @RequiresPermissions(value = {"stock:transfer", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/transform", method = RequestMethod.POST)
    public AjaxResult transfer(@RequestBody TransformVO transformVO) {
        Long lockId = transferLock.nextId();
        if (!transferLock.tryLock("transfer" + transformVO.getStockTaskId(), lockId, 30L, TimeUnit.SECONDS)) {
            return AjaxResult.getError("REPEAT_COMMIT");
        }
        try {
            return transferService.stockTransfer(transformVO);
        } finally {
            transferLock.unLock("transfer" + transformVO.getStockTaskId(), lockId);
        }
    }


    @ApiOperation(value = "库存转换单列表", httpMethod = "GET", tags = "库存转换")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"store-stock:selectProcess", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/process/list/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult processList(@PathVariable int pageIndex, @PathVariable int pageSize, StockTaskProcessReq req) {
        return transferService.processList(pageIndex, pageSize, req);
    }

    @ApiOperation(value = "转换出入库单详情",httpMethod = "GET",tags = "库存转换")
    @ApiImplicitParam(name = "id",value = "出入库单编号",paramType = "path",required = true)
    @RequiresPermissions(value = {"store-stock:selectProcess", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/process/detail/{id}",method = RequestMethod.GET)
    public AjaxResult selectTransferProcessDetail(@PathVariable Integer id){
        return transferService.selectTransferProcessDetail(id);
    }

    @ApiOperation(value = "库存转换记录", httpMethod = "GET", tags = "库存转换")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"store-stock:selectProcess", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/process/record/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult processRecord(@PathVariable int pageIndex, @PathVariable int pageSize, StockTaskProcessReq req) {
        return transferService.processList(pageIndex, pageSize, req);
    }


}
