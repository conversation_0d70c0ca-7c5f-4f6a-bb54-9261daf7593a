package net.summerfarm.controller;

 import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.redis.KeyConstant;
 import net.summerfarm.common.util.MD5Util;
 import net.summerfarm.contexts.Global;
 import net.summerfarm.model.vo.PanicBuyVO;
import net.summerfarm.service.MerchantService;
import net.summerfarm.service.PanicBuyService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-05-12
 * @description
 */
@Api(tags = "秒杀")
@Slf4j
@RestController
@RequestMapping("/panic")
public class PanicBuyController {
    @Resource
    private PanicBuyService panicBuyService;
    @Resource
    private MerchantService merchantService;
    @Autowired
    private RedissonClient redissonClient;
    @Resource
    private BaseService baseService;

    @ApiOperation(value = "分页查询秒杀数据", httpMethod = "GET", tags = "秒杀")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true),
            @ApiImplicitParam(name = "status", value = "秒杀状态：0、待开抢 1、疯抢中 2、已结束", paramType = "query"),
            @ApiImplicitParam(name = "queryStartTime", value = "查询开始时间", paramType = "query"),
            @ApiImplicitParam(name = "queryEndTime", value = "查询结束时间", paramType = "query"),
            @ApiImplicitParam(name = "queryStoreNoList", value = "查询城配仓", paramType = "query", example = "1,2,10"),
            @ApiImplicitParam(name = "queryAreaNoList", value = "查询城市", paramType = "query", example = "1001,1002,2750"),
            @ApiImplicitParam(name = "progress", value = "售罄状态：0、完全售罄 1、全未售罄 2、部分售罄", paramType = "query"),
            @ApiImplicitParam(name = "sku", value = "sku搜索", paramType = "query")
    })
    @RequiresPermissions(value = {"panic:select", Global.SA}, logical = Logical.OR)
    @GetMapping("/{pageIndex}/{pageSize}")
    public AjaxResult selectPanicBuy(@PathVariable int pageIndex, @PathVariable int pageSize, PanicBuyVO instance) {
        return panicBuyService.selectPanicBuy(pageIndex, pageSize, instance);
    }

    @ApiOperation(value = "查询秒杀详情数据", httpMethod = "GET", tags = "秒杀")
    @ApiImplicitParam(name = "id", value = "秒杀id", paramType = "path", required = true)
    @ApiResponse(code = 200, message = "请求成功", response = PanicBuyVO.class)
    @GetMapping("/{id}")
    @RequiresPermissions(value = {"panic:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectDetail(@PathVariable Integer id) {
        return panicBuyService.selectDetail(id);
    }

    @ApiOperation(value = "新增秒杀", httpMethod = "PUT", tags = "秒杀")
    @ApiImplicitParam(dataTypeClass = PanicBuyVO.class)
    @PutMapping()
    @RequiresPermissions(value = {"panic:add-update", Global.SA}, logical = Logical.OR)
    public AjaxResult addPanicBuy(@RequestBody PanicBuyVO instance) {
        String redisKey = MD5Util.string2MD5(baseService.getAdminId().toString()+instance.getSkuList().toString()+instance.getAreaList().toString());
        RLock redissonLock = redissonClient.getLock(KeyConstant.PANIC_BUY_LOCK_KEY + redisKey);
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                return AjaxResult.getErrorWithMsg("正在处理，请稍后");
            }
            return panicBuyService.addPanicBuy(instance);
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            throw new DefaultServiceException("正在处理，请稍后");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    @ApiOperation(value = "秒杀检查", httpMethod = "PUT", tags = "秒杀")
    @ApiImplicitParam(dataTypeClass = PanicBuyVO.class)
    @PutMapping("/checkPanicBuy")
    @RequiresPermissions(value = {"panic:add-update", Global.SA}, logical = Logical.OR)
    public AjaxResult checkPanicBuy(@RequestBody PanicBuyVO instance) {
        return panicBuyService.checkPanicBuy(instance);
    }

    @ApiOperation(value = "修改秒杀", httpMethod = "POST", tags = "秒杀")
    @ApiImplicitParam(dataTypeClass = PanicBuyVO.class)
    @PostMapping()
    @RequiresPermissions(value = {"panic:add-update", Global.SA}, logical = Logical.OR)
    public AjaxResult updatePanicBuy(@RequestBody PanicBuyVO instance) {
        return panicBuyService.updatePanicBuy(instance);
    }

    @ApiOperation(value = "秒杀售罄", httpMethod = "POST", tags = "秒杀")
    @ApiImplicitParam(dataTypeClass = PanicBuyVO.class)
    @PostMapping("/sellout")
    @RequiresPermissions(value = {"panic:add-update", Global.SA}, logical = Logical.OR)
    public AjaxResult sellOut(@RequestBody PanicBuyVO instance) {
        return panicBuyService.sellOut(instance);
    }

    @ApiOperation(value = "删除秒杀", httpMethod = "DELETE", tags = "秒杀")
    @ApiImplicitParam(name = "id", value = "秒杀id", paramType = "path", required = true)
    @DeleteMapping("/{id}")
    @RequiresPermissions(value = {"panic:delete", Global.SA}, logical = Logical.OR)
    public AjaxResult delete(@PathVariable Integer id) {
        return panicBuyService.delete(id);
    }

    @ApiOperation(value = "批量修改秒杀", httpMethod = "POST", tags = "秒杀")
    @PostMapping("/batch")
    @RequiresPermissions(value = {"panic:add-update", Global.SA}, logical = Logical.OR)
    public AjaxResult batchAdd(@RequestBody PanicBuyVO instance) {
        return panicBuyService.batchAdd(instance);
    }

    @GetMapping("/get")
    public void aa(){
        panicBuyService.timedTaskOrderSku();
    }

    @RequiresPermissions(value = {"card-voucher:select", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/panicBuy",method = RequestMethod.GET)
    public AjaxResult selectListPanicBuy(Long mId){
        return merchantService.selectListPanicBuy(mId);
    }
}
