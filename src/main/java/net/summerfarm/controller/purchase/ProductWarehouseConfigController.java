package net.summerfarm.controller.purchase;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseController;
import net.summerfarm.common.exceptions.BizAssert;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.exceptions.ErrorCode;
import net.summerfarm.common.util.MathUtil;
import net.summerfarm.model.DTO.purchase.SupplierBaseInfoDTO;
import net.summerfarm.module.scp.common.enums.ProductWarehouseConfigEnums;
import net.summerfarm.enums.RedissonLockKey;
import net.summerfarm.module.scp.common.enums.SupplierReplenishmentConfigEnums;
import net.summerfarm.model.input.purchase.*;
import net.summerfarm.model.vo.purchase.ProductWarehouseConfigVO;
import net.summerfarm.model.vo.purchase.ProductWarehouseDetailConfigVO;
import net.summerfarm.service.purchase.ProductWarehouseConfigService;
import net.xianmu.common.result.CommonResult;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 采购-品仓配置接口
 * todo zjx 确认接口下线情况
 */
@Slf4j
@RestController
@RequestMapping(value = "/productWarehouseConfig")
public class ProductWarehouseConfigController extends BaseController {


    @Autowired
    private ProductWarehouseConfigService productWarehouseConfigService;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 一级品仓配置页面分页查询接口
     *
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @GetMapping(value = "/{pageIndex}/{pageSize}")
    public AjaxResult<PageInfo<List<ProductWarehouseConfigVO>>> pageQueryProductConfig(@PathVariable int pageIndex, @PathVariable int pageSize, ProductWarehouseConfigInput input) {
        return productWarehouseConfigService.pageQueryProductConfig(pageIndex, pageSize, input);
    }

    @GetMapping(value = "/enabled/supplier/{pdId}/{warehouseNo}")
    public AjaxResult<List<SupplierBaseInfoDTO>> queryEnableSupplier(@PathVariable Long pdId, @PathVariable Integer warehouseNo) {
        return productWarehouseConfigService.queryEnableSupplier(pdId, warehouseNo);
    }


    /**
     * 批量设置采购员/计划员
     *
     * @param productWarehouseConfigUpsert
     * @return
     */
    @PostMapping("/user/upsert")
    public CommonResult<Void> productWarehouseConfigUpdate(@RequestBody @Validated ProductWarehouseConfigUpsert productWarehouseConfigUpsert) {
        productWarehouseConfigUpsert.setOperatorId(getAdminId());
        productWarehouseConfigService.productWarehouseConfigUpdate(productWarehouseConfigUpsert);
        return CommonResult.ok();
    }

    /**
     * 二级品仓配置详细查询接口
     *
     * @param pdId
     * @return
     */
    @GetMapping(value = "/detail/{pdId}")
    public AjaxResult<List<ProductWarehouseDetailConfigVO>> listSpuConfigDetail(@PathVariable Long pdId) {
        return productWarehouseConfigService.listSpuConfigDetail(pdId);
    }

    /**
     * 品仓配置批量保存接口
     *
     * @param saveInput
     * @return
     */
    @PostMapping(value = "")
    public AjaxResult<Void> batchSaveConfig(@Validated @RequestBody ProductWarehouseSaveInput saveInput) {
        for (ProductWarehouseSaveConfigInput productWarehouseSaveConfigInput : saveInput.getConfigList()) {
            if (Objects.equals(productWarehouseSaveConfigInput.getPurchaseType(), ProductWarehouseConfigEnums.PurchaseType.INDIRECT_PURCHASE.getValue())) {
                continue;
            }
            long defaultSupplierCount = productWarehouseSaveConfigInput.getSupplierInfos().stream()
                    .peek(supplierInput -> {
                        // 检查补货配置(提前期、备货期和下单日期)
                        supplierInput.getReplenishmentConfigs().forEach(rc -> {
                            checkOrderDateNotNull(rc);
                            checkReplenishmentDayNum(rc);
                        });
                    })
                    .map(ProductWarehouseSupplierSaveInput::getPrimaryFlag)
                    .filter(pf -> Objects.equals(pf, 1))
                    .count();
            // 品仓默认供应商必须配置且只能存在一个
            BizAssert.isTrue(defaultSupplierCount == 1, ErrorCode.DEFAULT_SUPPLIER_MUST_HAVE_ONE);
        }
        Long pdId = saveInput.getConfigList().get(0).getPdId();
        RLock redissonLock = redissonClient.getLock(RedissonLockKey.PRODUCT_WAREHOUSE_CONFIG_SAVE + pdId);
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                return AjaxResult.getErrorWithMsg("当前有其他人正在操作该品类配置");
            }
            return productWarehouseConfigService.batchSaveConfig(saveInput);
        } catch (
                InterruptedException e) {
            log.error("锁获取异常", e);
            throw new DefaultServiceException("当前有其他人正在操作该品类配置");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                redissonLock.unlock();
            }
        }

    }

    /**
     * 不定期补货需校验提前期和备货期
     *
     * @param replenishmentInput 补货配置
     * @version 自动生成补货计划
     */
    private void checkReplenishmentDayNum(SupplierReplenishmentSaveInput replenishmentInput) {
        Integer replenishmentModeCode = replenishmentInput.getReplenishmentMode();
        SupplierReplenishmentConfigEnums.ReplenishmentMode replenishmentMode = SupplierReplenishmentConfigEnums.ReplenishmentMode.wrap(replenishmentModeCode);
        // 定期的跳过
        if (!SupplierReplenishmentConfigEnums.ReplenishmentMode.isUnscheduled(replenishmentMode)) {
            return;
        }
        // 0<=提前期<=30
        BizAssert.isTrue(MathUtil.between(replenishmentInput.getPreDay(), 0, 30), ErrorCode.PRE_DAY_TIME_INTERVAL_ERROR);
        // （0<=备货期<=45）&&（备货期>=提前期）
        BizAssert.isTrue(MathUtil.between(replenishmentInput.getBacklogDay(), replenishmentInput.getPreDay(), 45), ErrorCode.BACKLOG_DAY_TIME_INTERVAL_ERROR);
    }

    /**
     * 如果定期不定量补货模式,订货时间不能为空
     *
     * @param replenishmentInput 补货配置
     */
    private void checkOrderDateNotNull(SupplierReplenishmentSaveInput replenishmentInput) {
        if (Objects.equals(replenishmentInput.getReplenishmentMode(), SupplierReplenishmentConfigEnums.ReplenishmentMode.REGULAR_INDEFINITE.getValue())) {
            BizAssert.notNull(replenishmentInput.getOrderDate(), "定期不定量补货模式时,orderDate不能为空");
        }
    }


    /**
     * 手动新增品仓配置,用于初始配置未生成情况
     * 策略:pdId必传
     * 不传warehouseNo程序将取全量开放仓与当前配置已存在仓做差集,再插入数据
     * 传入warehouseNo 程序将检测配置表中是否含有该仓配置,没有则插入数据
     *
     * @param pdId
     * @param warehouseNo
     * @return
     */
    @RequestMapping(value = {"/manual/{pdId}/{warehouseNo}", "/manual/{pdId}"}, method = RequestMethod.POST)
    public AjaxResult<Integer> manualAddProductConfig(@PathVariable Long pdId, @PathVariable(required = false) Integer warehouseNo) {
        return AjaxResult.getOK(productWarehouseConfigService.manualAddProductConfig(pdId, warehouseNo));
    }

    /**
     * 处理当前时间前一天的库存仓新增，开放，关闭，导致的品仓配置需要变化的数据
     *
     * @return
     */
    @PostMapping(value = "dealWarehouseChangeTask")
    public AjaxResult<Boolean> dealWarehouseChangeTask() {
        return AjaxResult.getOK(productWarehouseConfigService.dealWarehouseChangeTask());
    }


    /**
     * 初始化安全水位
     * @return
     */
    @PostMapping(value = "/dealWaterLevelTask")
    public CommonResult<Void> dealWaterLevelTask() {
        productWarehouseConfigService.initWaterLevelAndBacklogDay();
        return CommonResult.ok();
    }


}
