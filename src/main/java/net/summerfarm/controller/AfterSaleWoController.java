package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.AfterSaleWo;
import net.summerfarm.service.AfterSaleWoService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-07-29
 * @description
 */
@RestController
@Api(tags = "售后工单")
@RequestMapping("/after-sale-wo")
public class AfterSaleWoController {
    @Resource
    private AfterSaleWoService afterSaleWoService;

    @ApiOperation(value = "售后工单分页", httpMethod = "GET", tags = "售后工单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true)
    })
    @GetMapping("/{pageIndex}/{pageSize}")
    @RequiresPermissions(value = {"after-sale-order:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectPage(@PathVariable int pageIndex, @PathVariable int pageSize, AfterSaleWo instance) {
        return afterSaleWoService.listPage(pageIndex, pageSize, instance);
    }

    @ApiOperation(value = "新增售后工单", httpMethod = "PUT", tags = "售后工单")
    @PutMapping("/add")
    @RequiresPermissions(value = {"after-sale-order:select", Global.SA}, logical = Logical.OR)
    public AjaxResult add(@RequestBody AfterSaleWo instance) {
        return afterSaleWoService.addAfterSaleWo(instance);
    }

    @ApiOperation(value = "新增工单跟进", httpMethod = "PUT", tags = "售后工单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "woId", value = "工单id", paramType = "query", required = true),
            @ApiImplicitParam(name = "context", value = "跟进内容", paramType = "query", required = true),
            @ApiImplicitParam(name = "finishFlag", value = "立即结束标识：true、立即结束 false、否", paramType = "query", required = true)
    })
    @PutMapping("/add-process")
    @RequiresPermissions(value = {"after-sale-order:select", Global.SA}, logical = Logical.OR)
    public AjaxResult addProcess(Integer woId, String context, Boolean finishFlag) {
        return afterSaleWoService.addAfterSaleWoProcess(woId, context, finishFlag);
    }
}
