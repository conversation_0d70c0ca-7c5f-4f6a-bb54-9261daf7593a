package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.service.SuitService;
import net.summerfarm.model.input.SuitAddReq;
import net.summerfarm.model.input.SuitQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Package: net.summerfarm.controller
 * @Description: 组合包管理
 * @author: <EMAIL>
 * @Date: 2017/7/21
 */
@Api(tags = "组合包管理")
@RestController
@RequestMapping("/sku/suit")
public class SuitController {


    @Resource
    private SuitService suitService;

    /**
     * 分页查询
     * @param pageIndex
     * @param pageSize
     * @param selectKeys
     * @return
     */
    @ApiOperation(value = "分页查询",httpMethod = "GET",tags = "组合包管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"sku-suit:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, SuitQuery selectKeys){
        return suitService.select(pageIndex, pageSize, selectKeys);
    }

    /**
     * 组合包详情
     * @param id
     * @return
     */
    @ApiOperation(value = "组合包详情",httpMethod = "GET",tags = "组合包管理")
    @ApiImplicitParam(name = "id",value = "组合包id",paramType = "path",required = true)
    @RequiresPermissions(value = {"sku-suit:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public AjaxResult selectDetail(@PathVariable int id) {
        return suitService.selectDetail(id);
    }

    /**
     * 新增/修改组合包
     * @return
     */
    @ApiOperation(value = "新增/修改组合包",httpMethod = "POST",tags = "组合包管理")
    @RequiresPermissions(value = {"sku-suit:save", Global.SA},logical = Logical.OR)
    @RequestMapping(method = RequestMethod.POST)
    public AjaxResult save(@Validated @RequestBody SuitAddReq suitAddReq, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }
        return suitService.save(suitAddReq);
    }

    /**
     * 删除组合包sku
     * @return
     */
    @ApiOperation(value = "删除组合包sku",httpMethod = "POST",tags = "组合包管理")
    @ApiImplicitParam(name = "suitItemId",value = "组合包详情id",paramType = "path",required = true)
    @RequiresPermissions(value = {"sku-suit:delete", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "/item/{suitItemId}", method = RequestMethod.POST)
    public AjaxResult deleteSuitItem(@PathVariable Integer suitItemId) {
        return suitService.deleteSuitItem(suitItemId);
    }

    /**
     * 查询所有的组合包
     */
    @ApiOperation(value = "所有组合包查询",httpMethod = "GET",tags = "组合包管理")
    @RequestMapping(value = "/select-all", method = RequestMethod.GET)
    public AjaxResult selectAll() {
        return suitService.selectAll();
    }

}
