package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.Brand;
import net.summerfarm.service.BrandService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Package: net.summerfarm.controller
 * @Description: 品牌管理
 * @author: <EMAIL>
 * @Date: 2016/8/4
 */
@Api(tags = "品牌管理")
@RestController
@RequestMapping(value = "/brand")
public class BrandController {

    @Resource
    private BrandService brandService;

    @ApiOperation(value = "分页查询",httpMethod = "GET",tags = "品牌管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true)
    })
    @RequiresPermissions(value = {"brand:select", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "{pageIndex}/{pageSize}",method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, Brand selectKeys){
        return brandService.select(pageIndex, pageSize, selectKeys);
    }

    @ApiOperation(value = "品牌查询",httpMethod = "GET",tags = "品牌管理")
    @RequiresPermissions(value = {"brand:select", Global.SA},logical = Logical.OR)
    @RequestMapping(method = RequestMethod.GET)
    public AjaxResult select(){
        return brandService.select();
    }

    @ApiOperation(value = "新增品牌",httpMethod = "POST",tags = "品牌管理")
    @RequiresPermissions(value = {"brand:insert", Global.SA},logical = Logical.OR)
    @RequestMapping(method = RequestMethod.POST)
    public AjaxResult save(@Validated({Add.class}) Brand record, BindingResult bindingResult){
        return brandService.save(record, bindingResult);
    }

    @ApiOperation(value = "修改品牌",httpMethod = "PUT",tags = "品牌管理")
    @ApiImplicitParam(name = "brandId",value = "品牌id",paramType = "path",required = true)
    @RequiresPermissions(value = {"brand:update", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "{brandId}", method = RequestMethod.PUT)
    public AjaxResult update(@PathVariable Integer brandId, @Validated({Update.class}) @RequestBody Brand record, BindingResult bindingResult){
        return brandService.update(brandId, record, bindingResult);
    }

    @ApiOperation(value = "删除品牌",httpMethod = "DELETE",tags = "品牌管理")
    @ApiImplicitParam(name = "brandId",value = "品牌id",paramType = "path",required = true)
    @RequiresPermissions(value = {"brand:update", Global.SA},logical = Logical.OR)
    @RequestMapping(value = "{brandId}", method = RequestMethod.DELETE)
    public AjaxResult update(@PathVariable Integer brandId){
        return brandService.delete(brandId);
    }



}
