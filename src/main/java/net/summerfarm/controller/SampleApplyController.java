package net.summerfarm.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.domain.SampleApply;
import net.summerfarm.model.vo.SampleApplyVO;
import net.summerfarm.service.SampleApplyService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> ct
 * create at:  2020/4/26  17:35
 */
@RestController
@Api(tags = "样品管理")
@RequestMapping("/sampleApply")
public class SampleApplyController {

    @Resource
    SampleApplyService sampleApplyService;

    /**
     * 创建样品申请
     */
    @ApiOperation(value = "新增申请",httpMethod = "POST",tags = "样品管理")
    @RequestMapping(value = "/insert" ,method = RequestMethod.POST )
    @RequiresPermissions(value = {"sampleApply:insert", Global.SA}, logical = Logical.OR)
    public AjaxResult insertSampleApply(@RequestBody SampleApplyVO sampleApplyVO){
       return sampleApplyService.insertSampleApply(sampleApplyVO);
    }

    /**
     * 更新用户反馈
     */
    @ApiOperation(value = "更新用户反馈",httpMethod = "POST",tags = "样品管理")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @RequiresPermissions(value = {"sampleApply:update", Global.SA}, logical = Logical.OR)
    public AjaxResult updateSampleApply(@RequestBody SampleApply sampleApply){
        return sampleApplyService.updateSampleApply(sampleApply);
    }

    /**
     * 查询列表信息
     */
    @ApiOperation(value = "查询列表信息",httpMethod = "GET",tags = "样品管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex",value = "页码",paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize",value = "数量",paramType = "path",required = true),
            @ApiImplicitParam(name = "keyword",value = "搜索关键词",paramType = "query",required = false)
    })
    @RequestMapping(value = "/selectSample/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    @RequiresPermissions(value = {"sampleApply:select", Global.SA}, logical = Logical.OR)
    public AjaxResult selectSampleApplyVO(@PathVariable int pageIndex, @PathVariable int pageSize, SampleApply sampleApply, String keyword){
       return sampleApplyService.selectSampleApplyVO(pageIndex,pageSize,sampleApply, keyword);
    }

    /**
     * 取消样品申请
     * @param sampleId
     * @return
     */
    @ApiOperation(value = "取消样品申请",httpMethod = "GET",tags = "样品管理")
    @ApiImplicitParam(name = "sampleId",value = "样品id",paramType = "path",required = true)
    @GetMapping("/cancel/{sampleId}")
    @RequiresPermissions(value = {"sampleApply:update", Global.SA}, logical = Logical.OR)
    public AjaxResult cancelSampleApply(@PathVariable int sampleId){
        return sampleApplyService.cancelSampleApply(sampleId);
    }

    @PostMapping("/checkSampleApplyReviewLegitimacy")
    public AjaxResult checkSampleApplyReviewLegitimacy(@RequestBody SampleApplyVO sampleApplyVO){
        return sampleApplyService.checkSampleApplyReviewLegitimacy(sampleApplyVO);
    }

}
