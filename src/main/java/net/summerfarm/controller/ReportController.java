package net.summerfarm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.model.vo.DeliveryOrdersVO;
import net.summerfarm.service.MerchantService;
import net.summerfarm.service.OrderService;
import net.summerfarm.service.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * @Package: net.summerfarm.controller
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/2/28
 */
@Api(tags = "数据管理")
@RestController
@RequestMapping(value = "/report")
public class ReportController {

    @Resource
    private ReportService reportService;

    @Resource
    private MerchantService merchantService;

    @Autowired
    private OrderService orderService;



    @RequiresPermissions(value = {"report:bd-gmv", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/bd/gmv")
    public AjaxResult BDGmv(Integer areaNo) {
        return reportService.BDGmv(areaNo);
    }

    @RequiresPermissions(value = {"report:gmv", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/bd/merchant")
    public AjaxResult BDMerchant(Integer areaNo,Integer adminId) {
        return reportService.BDMerchant(areaNo,adminId);
    }

    @RequiresPermissions(value = {"report:gmv", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/bd/merchant-lifecycle")
    public AjaxResult BDMerchantLifecycle(Integer areaNo) {
        return reportService.BDMerchantLifecycle(areaNo);
    }

    @RequiresPermissions(value = {"report:gmv", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/bd/merchant-member")
    public AjaxResult BDMerchantMember(Integer areaNo) {
        return reportService.BDMerchantMember(areaNo);
    }


    @ApiOperation(value = "待完成gmv目标查询", httpMethod = "GET", tags = "数据管理")
    @RequiresPermissions(value = {"report:daily-mission", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/daily-business-mission")
    public AjaxResult dailyBusinessMission() {
        return reportService.dailyBusinessMission();
    }

    @ApiOperation(value = "客户月度gmv查询", httpMethod = "GET", tags = "数据管理")
    @RequiresPermissions(value = {"report:finished-data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/finished-gmv-data")
    public AjaxResult finishedGmvData() {
        return reportService.finishedGmvData();
    }

    @ApiOperation(value = "客户月度下单数查询", httpMethod = "GET", tags = "数据管理")
    @RequiresPermissions(value = {"report:finished-data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/finished-merchant-data")
    public AjaxResult finishedMerchantData() {
        return reportService.finishedMerchantData();
    }

    @ApiOperation(value = "查询个人每日gmv", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParam(name = "areaNo", value = "城市编号", paramType = "query")
    @RequiresPermissions(value = {"report:gmv", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/gmv")
    public AjaxResult dayGmv(Integer areaNo,@RequestParam(required = false) String startDate,@RequestParam(required = false) String endDate) {
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        if(!StringUtils.isEmpty(startDate) && !StringUtils.isEmpty(endDate)){
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            startTime = LocalDateTime.parse(startDate, df);
            endTime = LocalDateTime.parse(endDate,df);
        }
        return reportService.selectGmv(startTime,endTime, areaNo);
    }


    @ApiOperation(value = "数据看板总体查询", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParam(name = "areaNo", value = "城市编号", paramType = "query")
    @RequiresPermissions(value = {"report:gmv-total", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/gmv-total", method = RequestMethod.GET)
    public AjaxResult totalGmv(String areaNo) {
        return reportService.selectTotalGmv(areaNo);
    }

    @ApiOperation(value = "数据看板当日|当月查询", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型:month|day", paramType = "query", required = true),
            @ApiImplicitParam(name = "areaNo", value = "城市编号", paramType = "query")
    })
    @RequiresPermissions(value = {"report:gmv-type", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/gmv-type", method = RequestMethod.GET)
    public AjaxResult gmvByType(String type, String areaNo) {
        return reportService.selectGmvByType(type, areaNo);
    }

    @ApiOperation(value = "数据看板当日|当月查询", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型:month|day", paramType = "query", required = true),
            @ApiImplicitParam(name = "areaNo", value = "城市编号", paramType = "query")
    })
    @RequiresPermissions(value = {"report:gmv-type", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/gmv/statistical", method = RequestMethod.GET)
    public AjaxResult queryGmv(String type, String areaNo) {
        return reportService.queryGmv(type, areaNo);
    }

    @ApiOperation(value = "数据看板趋势图数据查询", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaNos", value = "城市编号列表(用,隔开)", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "类型:month|day", paramType = "query", required = true),
            @ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query")
    })
    @RequiresPermissions(value = {"report:gmv-trend", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/gmv-trend", method = RequestMethod.GET)
    public AjaxResult getGmvTrend(String areaNos, String type, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date startDate,
                                  @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date endDate) {
        return reportService.selectGmvTrend(areaNos, type, startDate, endDate);
    }

    @ApiOperation(value = "数据看板趋势图下载", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaNos", value = "城市编号列表(用,隔开)", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "类型:month|day", paramType = "query", required = true),
            @ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query")
    })
    @RequiresPermissions(value = {"report:gmv", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/gmv-download", method = RequestMethod.GET)
    public AjaxResult downloadGmvTrend(HttpServletResponse response, Integer areaNo, String type, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date startDate,
                                       @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date endDate) {
        return reportService.downloadGmvTrend(response, areaNo, type, startDate, endDate);
    }

    @ApiOperation(value = "BD月度gmv详情", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParam(name = "adminId", value = "管理员id", paramType = "query")
    @RequiresPermissions(value = {"report:monthdetail", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/monthdetail")
    public AjaxResult monthDetail(Integer adminId) {
        return reportService.selectMonthDetail(adminId);
    }


    @ApiOperation(value = "服务器当前时间", httpMethod = "GET", tags = "数据管理")
    @RequestMapping(value = "/now", method = RequestMethod.GET)
    public AjaxResult now() {
        return AjaxResult.getOK(System.currentTimeMillis());
    }

    @ApiOperation(value = "商户数据", httpMethod = "GET", tags = "数据管理")
    @RequiresPermissions(value = {"data-manage:merchat-data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/merchant-data", method = RequestMethod.GET)
    public AjaxResult merchantData() {
        return merchantService.merchantData();
    }

    @ApiOperation(value = "商户环比数据", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型:week|day", paramType = "query"),
            @ApiImplicitParam(name = "value", value = "lifecycle或size或type", paramType = "query")
    })
    @RequiresPermissions(value = {"report:merchat-ring-data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/merchant-ring-data", method = RequestMethod.GET)
    public AjaxResult merchantRingData(String type, String value) {
        return merchantService.merchantRingData(type, value);
    }

    @ApiOperation(value = "订单数据", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", paramType = "query"),
            @ApiImplicitParam(name = "start", value = "开始时间", paramType = "query"),
            @ApiImplicitParam(name = "end", value = "结束时间", paramType = "query")
    })
    @RequiresPermissions(value = {"report:order-data", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/order-data", method = RequestMethod.GET)
    public AjaxResult orderData(String phone, @DateTimeFormat(pattern = "yyyy-MM-dd") Date start, @DateTimeFormat(pattern = "yyyy-MM-dd") Date end) {
        return orderService.orderData(phone, DateUtils.date2LocalDate(start), DateUtils.date2LocalDate(end));
    }

    @ApiOperation(value = "待配送订单数据", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path", required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "path", required = true)
    })
    @RequiresPermissions(value = {"report:notdelivery-orders", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/notdelivery-orders/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult notDeliveryOrders(@PathVariable int pageIndex, @PathVariable int pageSize, DeliveryOrdersVO deliveryOrdersVO) {
        return orderService.notDeliveryOrders(pageIndex, pageSize, deliveryOrdersVO);
    }

    @ApiOperation(value = "待配送订单详情数据", httpMethod = "GET", tags = "数据管理")
    @RequiresPermissions(value = {"report:notdelivery-ordersdetail", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/notdelivery-ordersdetail", method = RequestMethod.GET)
    public AjaxResult notDeliveryOrdersDetail(DeliveryOrdersVO deliveryOrdersVO) {
        return orderService.notDeliveryOrdersDetail(deliveryOrdersVO);
    }

    @ApiOperation(value = "sku销量信息查询", httpMethod = "GET", tags = "数据管理")
    @RequiresPermissions(value = {"report:sku-sales", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/sku-sales", method = RequestMethod.GET)
    public AjaxResult skuSales(DeliveryOrdersVO deliveryOrdersVO) {
        return orderService.skuSales(deliveryOrdersVO);
    }

    @ApiOperation(value = "价格记录查询", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaNo", value = "城市编号", paramType = "query"),
            @ApiImplicitParam(name = "sku", value = "sku编号", paramType = "query"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", paramType = "query")
    })
    @RequiresPermissions(value = {"report:price-record", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/price-record", method = RequestMethod.GET)
    public AjaxResult priceRecord(Integer areaNo, String sku, @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT) Date startTime, @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT) Date endTime) {
        return reportService.selectPriceRecord(areaNo, sku, DateUtils.date2LocalDateTime(startTime), DateUtils.date2LocalDateTime(endTime));
    }

    @ApiOperation(value = "销量排行分页实时", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型:month|day", paramType = "query"),
            @ApiImplicitParam(name = "areaNo", value = "城市编号", paramType = "query"),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "query",required = true)
    })
    @RequiresPermissions(value = {"report:price-record", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/sales-volume/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult salesVolumeData(String type, Integer areaNo, @PathVariable int pageIndex, @PathVariable int pageSize) {
        return reportService.selectSalesVolumeData(type, areaNo, pageIndex, pageSize);
    }


    @ApiOperation(value = "销量排行分页查询", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sku", value = "sku编号", paramType = "query"),
            @ApiImplicitParam(name = "pdName", value = "商品名称", paramType = "query"),
            @ApiImplicitParam(name = "areaNo", value = "城市编号", paramType = "query",required = true),
            @ApiImplicitParam(name = "categoryType", value = "1 全部,2乳制品,3非乳制品,4水果", paramType = "query",required = true),
            @ApiImplicitParam(name = "pageIndex", value = "页码", paramType = "path",required = true),
            @ApiImplicitParam(name = "pageSize", value = "数量", paramType = "query",required = true)
    })
    @RequiresPermissions(value = {"report:price-record", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/sales-volume/search/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult salesVolumeData(@DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date startTime,
                                      @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date endTime,
                                      @PathVariable int pageIndex, @PathVariable int pageSize,
                                      String sku, String pdName, Integer areaNo, Integer categoryType) {
        LocalDateTime start=LocalDate.of(2016,1,1).atTime(22,0);
        LocalDateTime end=LocalDate.now().atTime(22,0);
        if(startTime!=null){
            start=DateUtils.date2LocalDate(startTime).minusDays(1).atTime(22,0);
        }
        if(endTime!=null){
            end=DateUtils.date2LocalDate(endTime).minusDays(1).atTime(22,0);
        }
        return reportService.selectSalesVolumeData(start,end, sku, pdName, areaNo, pageIndex, pageSize, categoryType);
    }

    @ApiOperation(value = "GMV和成本查询", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型:month|day", paramType = "query"),
            @ApiImplicitParam(name = "storeNo", value = "城市编号", paramType = "query"),
            @ApiImplicitParam(name = "sku", value = "sku编号", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query")
    })
    @RequiresPermissions(value = {"report:price-record", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/gmv-cost", method = RequestMethod.GET)
    public AjaxResult selectGmvAndCost(String type, Integer storeNo, String sku, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date startDate, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date endDate) {
        return reportService.selectGmvAndCostBySku(type, storeNo, sku, DateUtils.date2LocalDate(startDate), DateUtils.date2LocalDate(endDate));
    }

    @ApiOperation(value = "销量和售价数据查询", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型:month|day", paramType = "query"),
            @ApiImplicitParam(name = "areaNo", value = "城市编号", paramType = "query"),
            @ApiImplicitParam(name = "sku", value = "sku编号", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query")
    })
    @RequiresPermissions(value = {"report:sales-volume", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/sales-volume", method = RequestMethod.GET)
    public AjaxResult selectVolumeDataBySku(String type, Integer areaNo, String sku, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date startDate, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date endDate) {
        return reportService.selectSalesVolumeDataBySku(type, areaNo, sku, DateUtils.date2LocalDate(startDate), DateUtils.date2LocalDate(endDate));
    }

    @ApiOperation(value = "库存周转率查询", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeNos", value = "选择仓库：0代表全部仓库,多选用,隔开"),
            @ApiImplicitParam(name = "skus", value = "sku名称多选用,隔开"),
            @ApiImplicitParam(name = "startDate", value = "开始时间"),
            @ApiImplicitParam(name = "endDate", value = "结束时间"),
            @ApiImplicitParam(name = "cycle", value = "周期"),
            @ApiImplicitParam(name = "num", value = "周期数")
    })
    @RequiresPermissions(value = {"report:turn-over", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/turn-over", method = RequestMethod.GET)
    public AjaxResult storeQuantityTurnOver(String storeNos, String skus, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date startDate,
                                            @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date endDate, Integer cycle, Integer num) {
        return reportService.storeQuantityTurnOver(storeNos, skus, startDate == null ? null : DateUtils.date2LocalDate(startDate),
                endDate == null ? null : DateUtils.date2LocalDate(endDate), cycle, num);
    }

    @ApiOperation(value = "库存周转率下载", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeNos", value = "选择仓库：0代表全部仓库,多选用,隔开"),
            @ApiImplicitParam(name = "skus", value = "sku名称多选用,隔开"),
            @ApiImplicitParam(name = "startDate", value = "开始时间"),
            @ApiImplicitParam(name = "endDate", value = "结束时间")
    })
    @RequiresPermissions(value = {"report:gmv", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/turn-over/download", method = RequestMethod.GET)
    public AjaxResult storeQuantityTurnOverDownLoad(String storeNos, String skus, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date startDate,
                                                    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date endDate, HttpServletResponse response) {
        return reportService.storeQuantityTurnOverDownload(storeNos, skus, startDate == null ? null : DateUtils.date2LocalDate(startDate),
                endDate == null ? null : DateUtils.date2LocalDate(endDate), response);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeNos", value = "选择仓库：0代表全部仓库,多选用,隔开"),
            @ApiImplicitParam(name = "type", value = "类型：sku|spu"),
            @ApiImplicitParam(name = "startDate", value = "开始时间"),
            @ApiImplicitParam(name = "endDate", value = "结束时间"),
            @ApiImplicitParam(name = "names", value = "商品id集合，用，隔开"),
            @ApiImplicitParam(name = "num", value = "周期数"),
            @ApiImplicitParam(name = "cycle", value = "周期")

    })
    @ApiOperation(value = "库存金额占比查询", httpMethod = "GET", tags = "数据管理")
    @RequiresPermissions(value = {"report:gmv", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/cost-rate", method = RequestMethod.GET)
    public AjaxResult skuCostRate(String storeNos, String type, String names, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date startDate,
                                  @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date endDate, Integer cycle, Integer num) {
        return reportService.skuCostRate(storeNos, type, names, startDate == null ? null : DateUtils.date2LocalDate(startDate),
                endDate == null ? null : DateUtils.date2LocalDate(endDate), cycle, num);
    }

    @ApiOperation(value = "库存金额占比表格下载", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeNos", value = "选择仓库：0代表全部仓库,多选用,隔开"),
            @ApiImplicitParam(name = "type", value = "类型：sku|spu"),
            @ApiImplicitParam(name = "startDate", value = "开始时间"),
            @ApiImplicitParam(name = "endDate", value = "结束时间"),
            @ApiImplicitParam(name = "names", value = "商品id集合，用，隔开")

    })
    @RequiresPermissions(value = {"report:gmv", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/cost-rate/download", method = RequestMethod.GET)
    public AjaxResult skuCostRateDownload(String storeNos, String type, String names, @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date startDate,
                                          @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT) Date endDate, HttpServletResponse response) {
        return reportService.skuCostRateDownload(storeNos, type, names, startDate == null ? null : DateUtils.date2LocalDate(startDate),
                endDate == null ? null : DateUtils.date2LocalDate(endDate), response);
    }

    @ApiOperation(value = "售罄数据查询", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeNos", value = "仓库编号列表用,隔开"),
            @ApiImplicitParam(name = "skus", value = "sku列表"),
            @ApiImplicitParam(name = "startTime", value = "开始时间"),
            @ApiImplicitParam(name = "endTime", value = "结束时间"),
            @ApiImplicitParam(name = "cycle", value = "周期"),
            @ApiImplicitParam(name = "num", value = "周期数量")
    })
    @RequiresPermissions(value = {"report:gmv", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/sale-out", method = RequestMethod.GET)
    public AjaxResult saleOutData(String storeNos, String skus, @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT) Date startTime,
                                  @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT) Date endTime, Integer cycle, Integer num) {
        return reportService.saleOutData(storeNos, skus, startTime == null ? null : DateUtils.date2LocalDateTime(startTime),
                endTime == null ? null : DateUtils.date2LocalDateTime(endTime), cycle, num);
    }

    @ApiOperation(value = "售罄数据下载", httpMethod = "GET", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeNos", value = "仓库编号列表用,隔开"),
            @ApiImplicitParam(name = "skus", value = "sku列表"),
            @ApiImplicitParam(name = "startTime", value = "开始时间"),
            @ApiImplicitParam(name = "endTime", value = "结束时间")
    })
    @RequiresPermissions(value = {"report:gmv", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/sale-out/download")
    public AjaxResult saleOutDataDownload(String storeNos, String skus, @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT) Date startTime,
                                          @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT) Date endTime, HttpServletResponse response) {
        return reportService.saleOutDataDownload(storeNos, skus, startTime == null ? null : DateUtils.date2LocalDateTime(startTime),
                endTime == null ? null : DateUtils.date2LocalDateTime(endTime), response);
    }

    @ApiOperation(value = "利润/库存/销售额占比", tags = "数据管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeNos", value = "仓库编号列表用,隔开"),
            @ApiImplicitParam(name = "type", value = "类型：sku|spu"),
            @ApiImplicitParam(name = "names", value = "商品id列表用，隔开"),
            @ApiImplicitParam(name = "endTime", value = "结束时间"),
            @ApiImplicitParam(name = "cycle", value = "周期"),
            @ApiImplicitParam(name = "num", value = "周期数量")
    })
    @RequiresPermissions(value = {"report:gmv", Global.SA}, logical = Logical.OR)
    @RequestMapping(value = "/profit-cost", method = RequestMethod.GET)
    public AjaxResult profitCostRate(String storeNos, String type, String names, Integer cycle, Integer num,
                                     @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT) Date endTime) {
        return reportService.profitCostRate(storeNos, type, names, DateUtils.date2LocalDateTime(endTime), cycle, num);
    }
}
