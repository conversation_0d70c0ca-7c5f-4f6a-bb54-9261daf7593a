package net.summerfarm.controller.common;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.input.common.ConfigSymbolQueryInput;
import net.summerfarm.model.vo.common.ConfigVO;
import net.summerfarm.service.common.CommonConfigQueryService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/common/config/query")
public class CommonConfigQueryController {

    @Autowired
    private CommonConfigQueryService commonConfigQueryService;

    /**
     * 根据key查询配置
     * @param key
     * @return
     */
    @PostMapping("/value")
    public AjaxResult<ConfigVO> getConfigValue(@RequestParam String key) {
        if (StringUtils.isBlank(key)) {
            return AjaxResult.getErrorWithMsg("key不能为空");
        }
        ConfigVO result= commonConfigQueryService.getConfigValue(key);
        return AjaxResult.getOK(result);
    }

    /**
     * 根据分隔符查询并返回分隔后的value数组
     * @param input
     * @param bindingResult
     * @return
     */
    @PostMapping("/separate-value")
    public AjaxResult<List<String>> getSeparateConfigValue(@Validated ConfigSymbolQueryInput input, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getErrorWithMsg(bindingResult.getFieldError().getDefaultMessage());
        }
        List<String> result = commonConfigQueryService.getSeparateConfigValue(input);
        return AjaxResult.getOK(result);
    }

    /**
     * 获取以传入键值开头的所有配置
     * @param key
     * @return
     */
    @PostMapping("/start-with")
    public AjaxResult<List<ConfigVO>> getConfigStartWith(@RequestParam String key) {
        if (StringUtils.isBlank(key)) {
            return AjaxResult.getErrorWithMsg("key不能为空");
        }
        List<ConfigVO> result = commonConfigQueryService.getConfigStartWith(key);
        return AjaxResult.getOK(result);
    }



}
