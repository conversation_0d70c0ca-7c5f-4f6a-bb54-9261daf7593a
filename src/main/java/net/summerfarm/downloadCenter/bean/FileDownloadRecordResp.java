package net.summerfarm.downloadCenter.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description:文件下载中心数据响应对象
 * date: 2023/1/10 15:08
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FileDownloadRecordResp {

    /**
     * 唯一标识
     */
    private String uid;

    /**
     * oss访问地址
     */
    private String ossAccessAddress;

    public FileDownloadRecordResp(String uid){
        this.uid = uid;
    }
}
