package net.summerfarm.downloadCenter.handler;

import net.summerfarm.enums.FileDownloadRecordEnum;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * Description:下载中心业务处理接口
 * date: 2023/1/9 14:46
 *
 * <AUTHOR>
 */
public interface DownloadHandler {

    /**
     * 业务处理逻辑
     * @param uploadObj 上传对象
     * @return 字节数组
     * @throws IOException
     */
    byte[] handle(Object uploadObj) throws IOException;

    /**
     * 获取业务枚举
     * @return 业务枚举
     */
    FileDownloadRecordEnum getBizEnum();

    /**
     * 生成字节数组
     * @param workbook excel对象
     * @return 字节数组
     * @throws IOException
     */
    default byte[] generateByteArray(Workbook workbook) throws IOException {
        if (workbook == null){
            return null;
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        workbook.write(byteArrayOutputStream);
        return byteArrayOutputStream.toByteArray();
    }
}
