package net.summerfarm.dingding.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponse;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.req.process.ProcessCreateInstanceRequest;
import net.summerfarm.common.constant.dingding.DingdingEventTypeConstant;
import net.summerfarm.common.constant.dingding.ProcessInstanceBizTypeEnum;
import net.summerfarm.common.util.DingTalkUtils;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.config.model.ProcessMigrateBO;
import net.summerfarm.dingding.bo.*;
import net.summerfarm.dingding.enums.DingdingProcessFlowStatusEnum;
import net.summerfarm.dingding.exception.DingdingProcessException;
import net.summerfarm.dingding.handler.DingdingConfig;
import net.summerfarm.facade.process.ProcessFacade;
import net.summerfarm.facade.process.convert.ProcessConvert;
import net.summerfarm.mapper.manage.DepartmentStaffMapper;
import net.summerfarm.mapper.manage.DingdingProcessFlowMapper;
import net.summerfarm.model.domain.AdminAuthExtend;
import net.summerfarm.model.domain.DepartmentStaff;
import net.summerfarm.model.domain.dingding.DingdingProcessFlow;
import net.summerfarm.service.AdminAuthExtendService;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static net.summerfarm.common.constant.dingding.DingdingConstant.JSON_KEY_EVENT_TYPE;
import static net.summerfarm.common.constant.dingding.DingdingConstant.JSON_KEY_PROCESS_INSTANCE_ID;
import static net.summerfarm.common.constant.dingding.DingdingEventTypeConstant.BPMS_INSTANCE_CHANGE;
import static net.summerfarm.common.constant.dingding.DingdingEventTypeConstant.BPMS_TASK_CHANGE;
import static net.summerfarm.dingding.enums.DingdingProcessFlowStatusEnum.IN_APPROVAL;
import static net.summerfarm.dingding.enums.DingdingProcessFlowStatusEnum.TERMINATION;

/**
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021-12-21
 */
@Slf4j
@Service
public class DingdingProcessInstanceService {

    @Resource
    private DingdingConfig dingdingConfig;

    @Resource
    AdminAuthExtendService adminAuthExtendService;

    @Resource
    DepartmentStaffMapper departmentStaffMapper;

    @Resource
    DingdingProcessFlowMapper dingdingProcessFlowMapper;

    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    ProcessFacade processFacade;

    /**
     * 创建审批流
     *
     * @param processInstanceCreateBO 审批流参数
     * @return 审批流id
     */
    public ProcessCreateResultBO createProcessInstance(ProcessInstanceCreateBO processInstanceCreateBO) throws DingdingProcessException {
        log.info("DingdingProcessInstanceService[]createProcessInstance[]start[]processInstanceCreateBO:{}", JSON.toJSONString(processInstanceCreateBO));
        ProcessCreateResultBO result = checkParam(processInstanceCreateBO);
        if (!result.isSuccess()) {
            return result;
        }
        if(processInstanceCreateBO.getBizTypeEnum()!=null
            && ProcessMigrateBO.containMigrate(dynamicConfig.getProcessMigrate(),processInstanceCreateBO.getBizTypeEnum().getBizType())){
            ProcessCreateInstanceRequest convert = ProcessConvert.convert(processInstanceCreateBO);

            String instance = processFacade.createInstance(convert);
            return ProcessCreateResultBO.success(instance);
        }

        String processCode = dingdingConfig.getProcessCode(processInstanceCreateBO.getBizTypeEnum().getProcessCode());
        if (StringUtils.isEmpty(processCode)) {
            throw new DingdingProcessException("审批模版为空");
        }


        //审批发起人
        Long departmentId = null;
        String userId;
        if (Objects.nonNull(processInstanceCreateBO.getAdminId())) {
            // 查询审批人的钉钉用户id以及所在部门
            AdminAuthExtend adminAuthExtend = adminAuthExtendService.findDingdingUserId(processInstanceCreateBO.getAdminId());

            userId = adminAuthExtend.getUserId();
            // 获取员工部门信息-从系统组织架构表里获取用户部门信息
            DepartmentStaff departmentStaff = departmentStaffMapper.findOneByUserId(userId);

            if (departmentStaff != null) {
                departmentId = departmentStaff.getDeptId();
            }
        } else {
            userId = dingdingConfig.getProcessCode("originator");
        }

        //审批人
        if (StringUtils.isNotBlank(processInstanceCreateBO.getApprovers())) {
            List<AdminAuthExtend> adminAuthExtends = getAdminAuthExtends(processInstanceCreateBO.getApprovers());
            if (CollectionUtils.isNotEmpty(adminAuthExtends)) {
                String approvers = adminAuthExtends.stream().map(AdminAuthExtend::getUserId).collect(Collectors.joining(","));
                processInstanceCreateBO.setApprovers(approvers);
            }
        }

        //抄送人
        if (StringUtils.isNotBlank(processInstanceCreateBO.getCcList())) {
            List<AdminAuthExtend> adminAuthExtends = getAdminAuthExtends(processInstanceCreateBO.getCcList());
            if (CollectionUtils.isNotEmpty(adminAuthExtends)) {
                String ccList = adminAuthExtends.stream().map(AdminAuthExtend::getUserId).collect(Collectors.joining(","));
                processInstanceCreateBO.setCcList(ccList);
            }
        }

        // 如果从组织架构中未成功获取到组织架构信息，则调用钉钉职工查询接口进行查询
        if (departmentId == null) {
            OapiV2UserGetResponse.UserGetResponse dingdingUserInfo = DingTalkUtils.getDingdingUserInfo(userId);
            if (dingdingUserInfo == null) {
                log.warn("未查询到钉钉用户信息:{}", userId);
                throw new DingdingProcessException("未查询到钉钉用户信息:" + userId);
            }
            List<Long> deptIdList = dingdingUserInfo.getDeptIdList();
            if (CollectionUtils.isNotEmpty(deptIdList)) {
                departmentId = deptIdList.get(0);
            }
        }
        //查询对应的ProcessCode
//        String processCode = dingdingConfig.getProcessCode(processInstanceCreateBO.getBizTypeEnum().getProcessCode());
        processInstanceCreateBO.setOriginatorUserId(userId);
        processInstanceCreateBO.setDeptId(departmentId);
        String processInstanceId = DingTalkUtils.createProcessInstance(processInstanceCreateBO, processCode);
        if (StringUtils.isEmpty(processInstanceId)) {
            log.warn("请求钉钉发起审批失败,参数:{}", processInstanceCreateBO);
            throw new DingdingProcessException("请求钉钉发起审批失败");
        }

        // 本地方法调用事务不生效，故从spring容器中获取代理对象调动方法，使事务生效

        LocalDateTime now = LocalDateTime.now();
        DingdingProcessFlow flow = DingdingProcessFlow.buildFrom(processInstanceCreateBO, processInstanceId, processCode);
        flow.setCreateTime(now);
        flow.setUpdateTime(now);
        // 保存审批数据
        dingdingProcessFlowMapper.insertSelective(flow);
        return ProcessCreateResultBO.success(processInstanceId);
    }

    /**
     * 撤销审批工作流-发起撤销后，此时已向钉钉发起撤销，
     * 但是在钉钉审批终止回调前，数据处于审批撤销中的状态
     *
     * @param terminateInstanceProcessBO 撤销工作流参数
     * @return true 撤销成功 false撤销失败
     */
    public TerminateInstanceResultBO terminateProcess(TerminateInstanceProcessBO terminateInstanceProcessBO) throws DingdingProcessException {
        TerminateInstanceResultBO result = checkParam(terminateInstanceProcessBO);
        if (!result.isSuccess()) {
            return result;
        }
        int bizType = terminateInstanceProcessBO.getBizTypeEnum().getBizType();
        Long bizId = terminateInstanceProcessBO.getBizId();
        DingdingProcessFlow flow = dingdingProcessFlowMapper.selectOneByBizIdAndBizType(bizId, bizType);
        if (flow == null) {
            return TerminateInstanceResultBO.error("流程不存在");
        }
        Integer processStatus = flow.getProcessStatus();
        if (TERMINATION.getStatus() == flow.getProcessStatus()) {
            return TerminateInstanceResultBO.error("流程撤销处理中，请不要重复发起");
        }
        if (IN_APPROVAL.getStatus() != processStatus) {
            return TerminateInstanceResultBO.error("流程已完结");
        }


        AdminAuthExtend adminAuthExtend = adminAuthExtendService.findDingdingUserId(terminateInstanceProcessBO.getAdminId());

        terminateInstanceProcessBO.setProcessInstanceId(flow.getProcessInstanceId());
        terminateInstanceProcessBO.setOriginatorUserId(adminAuthExtend.getUserId());
        // 通知钉钉撤销审批，当审批成功后，由钉钉回调处理后续流程
        boolean dingdingResult = DingTalkUtils.terminateProcess(terminateInstanceProcessBO);
        if (!dingdingResult) {
            return TerminateInstanceResultBO.error("撤销发起失败，请稍后重试");
        }
        String remark = terminateInstanceProcessBO.getRemark();
        if (StringUtils.isEmpty(remark)) {
            flow.setRemark(remark);
        }
        flow.setProcessStatus(DingdingProcessFlowStatusEnum.TERMINATION.getStatus());
        dingdingProcessFlowMapper.updateById(flow);
        return TerminateInstanceResultBO.success(bizId);
    }

    private TerminateInstanceResultBO checkParam(TerminateInstanceProcessBO terminateInstanceProcessBO) {
        ProcessInstanceBizTypeEnum processInstanceBizTypeEnum = terminateInstanceProcessBO.getBizTypeEnum();
        if (processInstanceBizTypeEnum == null) {
            return TerminateInstanceResultBO.error("流程业务为空");
        }
        Integer adminId = terminateInstanceProcessBO.getAdminId();
        if (adminId == null || adminId < 0) {
            return TerminateInstanceResultBO.error("撤销人id错误");
        }
        Long bizId = terminateInstanceProcessBO.getBizId();
        if (bizId == null) {
            return TerminateInstanceResultBO.error("业务数据id为空");
        }
        return TerminateInstanceResultBO.success(terminateInstanceProcessBO.getBizId());
    }

    private ProcessCreateResultBO checkParam(ProcessInstanceCreateBO processInstanceCreateBO) throws DingdingProcessException {
        ProcessInstanceBizTypeEnum bizTypeEnum = processInstanceCreateBO.getBizTypeEnum();
        if (bizTypeEnum == null) {
            throw new DingdingProcessException("发起审批的业务类型为空");
        }
//        String processCode = dingdingConfig.getProcessCode(processInstanceCreateBO.getBizTypeEnum().getProcessCode());
//        if (StringUtils.isEmpty(processCode)) {
//            throw new DingdingProcessException("审批模版为空");
//        }
        /*Integer adminId = processInstanceCreateBO.getAdminId();
        if (adminId == null) {
            throw new DingdingProcessException("发起审批的系统用户id为空");
        }*/

        List<DingdingFormBO> dingdingForms = processInstanceCreateBO.getDingdingForms();
        if (CollectionUtils.isEmpty(dingdingForms)) {
            throw new DingdingProcessException("审批表单数据为空");
        }
        for (DingdingFormBO dingdingForm : dingdingForms) {
            if (dingdingForm == null) {
                throw new DingdingProcessException("空表单数据");
            }
            String formName = dingdingForm.getFormName();
            String formValue = dingdingForm.getFormValue();
            if (StringUtils.isEmpty(formName)) {
                throw new DingdingProcessException("表单名称为空");
            }
            if (StringUtils.isEmpty(formValue)) {
                throw new DingdingProcessException(String.format("表单:%s 的值为空", formName));
            }
        }
        return ProcessCreateResultBO.success(null);
    }

    /**
     * 钉钉回调消息验证
     * 对于非审批消息 消息已经是最终状态的审批数据不投递消息队列处理
     *
     * @param eventJson 钉钉回调明文数据
     */
    public boolean verifyFlowValid(JSONObject eventJson) {
        String eventType = eventJson.getString(JSON_KEY_EVENT_TYPE);
        // 判断如果是钉钉URL回调检测不继续往下执行，直接返回
        if (DingdingEventTypeConstant.CHECK_URL.equals(eventType)) {
            return false;
        }

        String processInstanceId = eventJson.getString(JSON_KEY_PROCESS_INSTANCE_ID);
        String title = eventJson.getString("title");
        if (StringUtils.isEmpty(processInstanceId)) {
            log.warn("未从钉钉回调数据中获取到审批实例id,{}", eventJson);
            return false;
        }
        // 查询审批数据
        DingdingProcessFlow flow = dingdingProcessFlowMapper.selectByProcessInstanceId(processInstanceId);
        if (flow == null) {
            log.warn("审批不存在，审批实例id:{},审批标题:{}", processInstanceId, title);
            return false;
        }
        // 判断状态
        Integer processStatus = flow.getProcessStatus();
        if (processStatus == null) {
            log.warn("当前审批实例数据异常，审批实例id:{}", processInstanceId);
            return false;
        }

        // 如果当前数据已经为终态了，那么不允许继续往下操作
        if (IN_APPROVAL.getStatus() != processStatus && TERMINATION.getStatus() != processStatus) {
            log.warn("当前审批单状态已为最终状态，禁止继续操作，当前审批单状态:{}", processStatus);
            return false;
        }
        return true;
    }

    /**
     * @description: 批量获取钉钉用户信息
     * @author: lzh
     * @date: 2023/4/26 17:16
     * @param: [processInstanceCreateBO]
     * @return: java.util.List<net.summerfarm.model.domain.AdminAuthExtend>
     **/
    private List<AdminAuthExtend> getAdminAuthExtends(String admins) {
        List<String> asList = Arrays.asList(admins.split(","));
        List<Integer> approverAdmin = asList.stream().map(Integer::valueOf).collect(Collectors.toList());
        List<AdminAuthExtend> adminAuthExtends = adminAuthExtendService.getListByAdminId(approverAdmin);
        return adminAuthExtends;
    }

    /**
     * @description: 同意或拒绝审批任务 -- 注：需要添加审批评论附件需将文件上传至审批钉盘空间需要额外添加
     * @author: lzh
     * @date: 2023/4/27 14:45
     * @param: [processInstanceId]
     * @return: java.lang.Boolean
     **/
    public Boolean processAudit(ProcessAuditBO processAuditBO) {
        if (Objects.isNull(processAuditBO)) {
            return Boolean.FALSE;
        }
        log.info("DingdingProcessInstanceService[]processAudit[]start[]processAuditBO:{}", JSON.toJSONString(processAuditBO));
        DingdingProcessFlow dingdingProcessFlow = dingdingProcessFlowMapper.selectByProcessInstanceId(processAuditBO.getProcessInstanceId());
        if (Objects.isNull(dingdingProcessFlow)) {
            log.warn("DingdingProcessInstanceService[]processAudit[]error dingding process flow not exist processAuditBO:{}", JSON.toJSONString(processAuditBO));
            throw new BizException("审批实例ID不存在!");
        }
        ProcessAuditResultBO processAuditResultBO = DingTalkUtils.processAudit(processAuditBO);
        log.info("DingdingProcessInstanceService[]processAudit[]end[]processAuditResultBO:{}", JSON.toJSONString(processAuditResultBO));
        return processAuditResultBO.getResult();
    }

    /**
     * @description: 获取审批实例详情
     * @author: lzh
     * @date: 2023/4/27 16:01
     * @param: [processInstanceId] 审批实例ID
     * @return: com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult
     **/
    public GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult getProcessInfo(String processInstanceId) {
        log.info("DingdingProcessInstanceService[]processAudit[]start[]processInstanceId:{}", processInstanceId);
        DingdingProcessFlow dingdingProcessFlow = dingdingProcessFlowMapper.selectByProcessInstanceId(processInstanceId);
        if (Objects.isNull(dingdingProcessFlow)) {
            log.warn("DingdingProcessInstanceService[]processAudit[]error dingding process flow not exist processInstanceId:{}", processInstanceId);
            throw new BizException("审批实例ID不存在!");
        }
        GetProcessInstanceResponse processDetail = DingTalkUtils.getProcessDetail(processInstanceId);
        GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult result = processDetail.getBody().getResult();
        log.info("DingdingProcessInstanceService[]processAudit[]end[]result:{}", JSON.toJSONString(result));
        return result;
    }
}
