package net.summerfarm.dingding.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/24  16:28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DingTalkMsgReceiverIdBO extends DingTalkMsgBO {
    /**
     * 消息接收用户Id：adminId、tms 司机Id、srm 供应商Id
     */
    private List<Long> receiverIdList;



    public DingTalkMsgReceiverIdBO(DingTalkMsgBO dingTalkMsgBO){
        this.setMsgType(dingTalkMsgBO.getMsgType());
        this.setTitle(dingTalkMsgBO.getTitle());
        this.setText(dingTalkMsgBO.getText());
        this.setMessageUrl(dingTalkMsgBO.getMessageUrl());
        this.setPicUrl(dingTalkMsgBO.getPicUrl());
        this.setContent(dingTalkMsgBO.getContent());

    }
}
