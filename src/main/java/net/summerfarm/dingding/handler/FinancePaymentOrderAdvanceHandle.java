package net.summerfarm.dingding.handler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstantKey;
import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.service.FinancePaymentOrderService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 采购预付付款单审批回调
 */
@Slf4j
//@Component
public class FinancePaymentOrderAdvanceHandle extends DingdingConfig implements DingdingEventHandler {

    @Resource
    private FinancePaymentOrderService financePaymentOrderService;

    @Override
    public String getProcessCode() {
        return getProcessCode(DingdingConstantKey.FINANCE_PAYMENT_ORDER_ADVANCE_APPROVAL_CODE);
    }

    @Override
    public void start(DingdingResultBO result) {
        log.info("审批开始!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());

    }

    @Override
    public void terminate(DingdingResultBO result) {
        log.info("审批撤销或终止了!bizId是 {}", result.getBizId());
    }

    @Override
    public void agree(DingdingResultBO result) {
        log.info("审批通过!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
        financePaymentOrderService.approvedFinancePaymentOrderAdvance(result.getBizId(), result.getHandlerUserId());
    }

    @Override
    public void refuse(DingdingResultBO result) {
        log.info("审批不通过!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
        financePaymentOrderService.refuseFinancePaymentOrderAdvance(result.getBizId(), result.getHandlerUserId());
    }

}
