package net.summerfarm.dingding.handler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstantKey;
import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.service.FinancePaymentOrderService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @title: FinancePaymentOrderBmsHandler
 * @date 2022/8/29 18:21
 */
@Slf4j
@Component
public class FinancePaymentOrderBmsHandler extends DingdingConfig implements DingdingEventHandler {

    @Resource
    private FinancePaymentOrderService financePaymentOrderService;

    @Override
    public String getProcessCode() {
        return getProcessCode(DingdingConstantKey.BMS_PAYMENT_ORDER_APPROVAL_CODE);
    }

    @Override
    public void start(DingdingResultBO result) {
        log.info("审批开始!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
    }

    @Override
    public void terminate(DingdingResultBO result) {
        log.info("审批撤回!BMS付款单bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
        financePaymentOrderService.refuseFinancePaymentOrderBms(result.getBizId(), result.getHandlerUserId());
    }

    @Override
    public void agree(DingdingResultBO result) {
        log.info("审批通过!BMS付款单bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
        financePaymentOrderService.approvedFinancePaymentOrderBms(result.getBizId(), result.getHandlerUserId());
    }

    @Override
    public void refuse(DingdingResultBO result) {
        log.info("审批不通过!BMS付款单bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
        financePaymentOrderService.refuseFinancePaymentOrderBms(result.getBizId(), result.getHandlerUserId());
    }

}
