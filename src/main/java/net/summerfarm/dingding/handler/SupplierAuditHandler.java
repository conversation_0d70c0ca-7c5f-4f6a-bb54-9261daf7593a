package net.summerfarm.dingding.handler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstantKey;
import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.service.SupplierService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description 供应商审核结果回调处理
 */
@Slf4j
//@Component
public class SupplierAuditHandler extends DingdingConfig implements DingdingEventHandler{
    @Resource
    private SupplierService supplierService;

    @Override
    public String getProcessCode() {
        return getProcessCode(DingdingConstantKey.SUPPLIER_AUDIT_CODE);
    }

    @Override
    public void start(DingdingResultBO result) {
        log.info("审批开始!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
    }

    @Override
    public void terminate(DingdingResultBO result) {

    }

    @Override
    public void agree(DingdingResultBO result) {
        log.info("审批通过!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
        supplierService.enableSupplier(result.getBizId().intValue());
    }

    @Override
    public void refuse(DingdingResultBO result) {
        log.info("审批不通过!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
        supplierService.closeSupplierAudit(result.getBizId().intValue());

    }
}
