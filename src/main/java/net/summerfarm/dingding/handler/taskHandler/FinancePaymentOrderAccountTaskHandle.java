package net.summerfarm.dingding.handler.taskHandler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstantKey;
import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.dingding.handler.DingdingConfig;
import net.summerfarm.service.impl.FinancePaymentOrderServiceImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @title: FinancePaymentOrderAccountTaskHandle
 * @date 2022/8/1010:52
 * 采购对账单付款单付款审批任务回调
 */
@Slf4j
//@Component
public class FinancePaymentOrderAccountTaskHandle extends DingdingConfig implements DingdingEventTaskHandler {

    @Resource
    private FinancePaymentOrderServiceImpl financePaymentOrderService;

    @Override
    public String getProcessCode() {
        return getProcessCode(DingdingConstantKey.FINANCE_PAYMENT_ORDER_ACCOUNT_APPROVAL_CODE);
    }

    @Override
    public void start(DingdingResultBO result) {
        log.info("审批任务开始!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
    }

    @Override
    public void cancel(DingdingResultBO result) {

    }

    @Override
    public void agree(DingdingResultBO result) {
        log.info("审批任务通过!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
        financePaymentOrderService.approvedFinancePaymentOrderAccountTask(result.getBizId(), result.getHandlerUserId());
    }

    @Override
    public void refuse(DingdingResultBO result) {
        log.info("审批任务拒绝!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());

    }

    @Override
    public void redirect(DingdingResultBO result) {

    }

}
