package net.summerfarm.dingding.handler.taskHandler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstantKey;
import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.dingding.handler.DingdingConfig;
import net.summerfarm.service.FinanceAccountStatementService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 2022-08-09
 * 采购对账单审批任务回调
 */
@Slf4j
//@Component
public class FinanceAccountStatementAuditTaskHandler extends DingdingConfig implements DingdingEventTaskHandler {

    @Resource
    private FinanceAccountStatementService financeAccountStatementService;

    @Override
    public String getProcessCode() {
        return getProcessCode(DingdingConstantKey.PURCHASE_STATEMENT_APPROVAL_CODE);
    }

    @Override
    public void start(DingdingResultBO result) {
        log.info("审批任务开始!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
    }

    @Override
    public void cancel(DingdingResultBO result) {

    }

    @Override
    public void agree(DingdingResultBO result) {
        log.info("审批任务通过!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
        financeAccountStatementService.approvedTaskAccountStatement(result.getBizId(), result.getHandlerUserId());
    }

    @Override
    public void refuse(DingdingResultBO result) {
        log.info("审批任务拒绝!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());

    }

    @Override
    public void redirect(DingdingResultBO result) {

    }
}
