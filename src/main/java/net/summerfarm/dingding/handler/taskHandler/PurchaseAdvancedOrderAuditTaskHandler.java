package net.summerfarm.dingding.handler.taskHandler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstantKey;
import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.dingding.handler.DingdingConfig;
import net.summerfarm.service.PurchaseAdvancedOrderService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @title: PurchaseAdvancedOrderAuditTaskHandler
 * @date 2022/8/1011:09
 * 采购预付单审批任务回调
 */
@Slf4j
//@Component
public class PurchaseAdvancedOrderAuditTaskHandler extends DingdingConfig implements DingdingEventTaskHandler{

    @Resource
    private PurchaseAdvancedOrderService purchaseAdvancedOrderService;

    @Override
    public String getProcessCode() {
        return getProcessCode(DingdingConstantKey.PURCHASE_ADVANCED_ORDER_APPROVAL_CODE);
    }

    @Override
    public void start(DingdingResultBO result) {
        log.info("审批任务开始!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
    }

    @Override
    public void cancel(DingdingResultBO result) {

    }

    @Override
    public void agree(DingdingResultBO result) {
        log.info("审批任务通过!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());
        purchaseAdvancedOrderService.approvedPurchaseAdvancedOrderTask(result.getBizId(), result.getHandlerUserId());
    }

    @Override
    public void refuse(DingdingResultBO result) {
        log.info("审批任务拒绝!bizId是 {},审批人id:{}", result.getBizId(), result.getHandlerUserId());

    }

    @Override
    public void redirect(DingdingResultBO result) {

    }
}
