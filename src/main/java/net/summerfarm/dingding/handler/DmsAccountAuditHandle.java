package net.summerfarm.dingding.handler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstantKey;
import net.summerfarm.common.constant.dingding.ProcessInstanceBizTypeEnum;
import net.summerfarm.common.redis.KeyConstant;
import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.service.AdminService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class DmsAccountAuditHandle extends DingdingConfig implements DingdingEventHandler{

    @Resource
    private AdminService adminService;
    @Resource
    RedisTemplate<String, String> redisTemplate;

    @Override
    public String getProcessCode() {
        return getProcessCode(DingdingConstantKey.DMS_ACCOUNT_AUDIT_CODE);
    }

    @Override
    public void start(DingdingResultBO result) {
        log.info("审批开始!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
    }

    @Override
    public void terminate(DingdingResultBO result) {

    }

    @Override
    public void agree(DingdingResultBO result) {
        log.info("审批通过!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
        adminService.createDmsCallBack(result.getBizId().intValue());
    }

    @Override
    public void refuse(DingdingResultBO result) {
        log.info("审批拒绝!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
        if (Boolean.TRUE.equals(redisTemplate.hasKey(KeyConstant.DMS_REDIS_PRE + result.getBizId()))) {
            redisTemplate.delete(KeyConstant.DMS_REDIS_PRE + result.getBizId());
        }
    }

}
