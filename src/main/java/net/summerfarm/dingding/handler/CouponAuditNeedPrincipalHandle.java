package net.summerfarm.dingding.handler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstantKey;
import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.enums.MarketCouponSendStatusEnum;
import net.summerfarm.service.MarketCouponSendService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class CouponAuditNeedPrincipalHandle extends DingdingConfig implements DingdingEventHandler {

    @Resource
    private MarketCouponSendService marketCouponSendService;

    @Override
    public String getProcessCode() {
        return getProcessCode(DingdingConstantKey.COUPON_AUDIT_NEED_PRINCIPAL);
    }

    @Override
    public void start(DingdingResultBO result) {
        log.info("审批开始!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
    }

    @Override
    public void terminate(DingdingResultBO result) {
        log.info("CouponAuditNeedPrincipalHandle[]审批撤回!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
        marketCouponSendService.sendMerchantCoupon(result.getBizId(),result.getHandlerUserId(), MarketCouponSendStatusEnum.AUDIT_REFUSE);
    }

    @Override
    public void agree(DingdingResultBO result) {
        log.info("CouponAuditNeedPrincipalHandle[]审批通过!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
        marketCouponSendService.sendMerchantCoupon(result.getBizId(),result.getHandlerUserId(), MarketCouponSendStatusEnum.AUDIT_PASS);
    }

    @Override
    public void refuse(DingdingResultBO result) {
        log.info("CouponAuditNeedPrincipalHandle[]审批拒绝!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
        marketCouponSendService.sendMerchantCoupon(result.getBizId(),result.getHandlerUserId(), MarketCouponSendStatusEnum.AUDIT_REFUSE);
    }

}
