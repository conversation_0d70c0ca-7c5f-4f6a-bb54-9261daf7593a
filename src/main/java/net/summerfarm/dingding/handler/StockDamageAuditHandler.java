package net.summerfarm.dingding.handler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.constant.dingding.DingdingConstantKey;
import net.summerfarm.contexts.Global;
import net.summerfarm.dingding.bo.DingdingResultBO;
import net.summerfarm.wms.mq.enums.AuditTypeEnums;
import net.summerfarm.wms.mq.stockdamage.StockDamageAuditDTO;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Classname StocktakingAudutHandler
 * @Description 货损审批.
 * @Date 2022/2/10 16:02
 * @Created by hx
 */
@Slf4j
@Component
public class StockDamageAuditHandler extends DingdingConfig implements DingdingEventHandler{

    @Resource
    private MqProducer mqProducer;

    @Override
    public String getProcessCode() {
        return getProcessCode(DingdingConstantKey.STOCKDAMAGE_AUDUT_CODE);
    }

    @Override
    public void start(DingdingResultBO result) {
        log.info("审批开始!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
    }

    @Override
    public void terminate(DingdingResultBO result) {
        log.info("审批撤销!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());

        // 消息转发到wms,让wms进行消费
        StockDamageAuditDTO stockDamageAuditDTO = StockDamageAuditDTO.builder()
                .stockDamageTaskId(result.getBizId())
                .adminId(result.getHandlerUserId())
                .auditOperate(AuditTypeEnums.TERMINATE.getCode())
                .build();
        mqProducer.send(Global.STOCK_TASK, "tag_wms_stock_task_damage_audit", stockDamageAuditDTO);
        // stockDamageTaskService.auditStockDamageRefuse(result.getBizId(),result.getHandlerUserId());
    }

    @Override
    public void agree(DingdingResultBO result) {
        log.info("审批通过!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());

        // 消息转发到wms,让wms进行消费
        StockDamageAuditDTO stockDamageAuditDTO = StockDamageAuditDTO.builder()
                .stockDamageTaskId(result.getBizId())
                .adminId(result.getHandlerUserId())
                .auditOperate(AuditTypeEnums.AGREE.getCode())
                .build();
        mqProducer.send(Global.STOCK_TASK, "tag_wms_stock_task_damage_audit", stockDamageAuditDTO);

//        try {
//            stockDamageTaskService.auditStockDamageSuccess(result.getBizId(),result.getHandlerUserId());
//        } catch (DefaultServiceException e){
//            log.error(e.getMessage(), e);
//        }
    }

    @Override
    public void refuse(DingdingResultBO result) {
        log.info("审批不通过!bizId是 {},审批人id:{}",result.getBizId(),result.getHandlerUserId());
        // 消息转发到wms,让wms进行消费
        StockDamageAuditDTO stockDamageAuditDTO = StockDamageAuditDTO.builder()
                .stockDamageTaskId(result.getBizId())
                .adminId(result.getHandlerUserId())
                .auditOperate(AuditTypeEnums.REFUSE.getCode())
                .build();
        mqProducer.send(Global.STOCK_TASK, "tag_wms_stock_task_damage_audit", stockDamageAuditDTO);
        // stockDamageTaskService.auditStockDamageRefuse(result.getBizId(),result.getHandlerUserId());
    }
}
