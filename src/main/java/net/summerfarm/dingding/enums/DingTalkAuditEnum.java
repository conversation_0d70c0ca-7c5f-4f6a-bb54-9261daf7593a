package net.summerfarm.dingding.enums;

/**
 * @Description:  共枚举
 * @Author: lzh
 * @Time: 2023/4/11 0010 09:59
 * @ModifyBy:
 */

public enum DingTalkAuditEnum {

    /**
     * 审批操作，取值。
     *
     * agree：同意
     *
     * refuse：拒绝
     */
    AGREE(0,"AGREE"),
    REFUSE(1,"REFUSE")
    ;
    private Integer code;

    private String value;

    DingTalkAuditEnum(Integer code, String value){
        this.code = code;
        this.value = value;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByKey(Integer key){
        for (DingTalkAuditEnum c : DingTalkAuditEnum.values()) {
            if (c.getCode() .equals(key)) {
                return c.value;
            }
        }
        return null;
    }

}
