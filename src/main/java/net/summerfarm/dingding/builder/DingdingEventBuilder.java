package net.summerfarm.dingding.builder;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.SpringContextUtil;
import net.summerfarm.common.util.TempThreadLocalUtil;
import net.summerfarm.dingding.handler.DingdingEventHandler;
import net.summerfarm.dingding.handler.taskHandler.DingdingEventTaskHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 钉钉事件处理器生成
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021-12-21
 */
@Component
@Slf4j
public class DingdingEventBuilder implements ApplicationListener<ContextRefreshedEvent> {

    private static Collection<DingdingEventHandler> dingdingEventHandlers;

    private static Collection<DingdingEventTaskHandler> dingdingEventTaskHandlers;

    /**
     * 钉钉事件处理器创建 审批实例
     * @param processCode 审批模版code
     * @return 钉钉事件处理器
     */
    public static DingdingEventHandler builder(String processCode){
        if (CollectionUtils.isEmpty(dingdingEventHandlers)) {
            dingdingEventHandlers = SpringContextUtil.getApplicationContext()
                    .getBeansOfType(DingdingEventHandler.class)
                    .values();
        }
        return dingdingEventHandlers.stream()
                .filter(bean -> processCode.equals(bean.getProcessCode()))
                .findFirst()
                .orElse(null);
    }

    public static DingdingEventHandler builderByFeiShu(String processCode){
        TempThreadLocalUtil.feiShuProcessStart();
        try {
            if (CollectionUtils.isEmpty(dingdingEventHandlers)) {
                dingdingEventHandlers = SpringContextUtil.getApplicationContext()
                        .getBeansOfType(DingdingEventHandler.class)
                        .values();
            }
            return dingdingEventHandlers.stream()
                    .filter(bean -> processCode.equals(bean.getProcessCode()))
                    .findFirst()
                    .orElse(null);
        } finally {
            TempThreadLocalUtil.processClear();
        }
    }

    /**
     * 钉钉事件处理器创建 审批事件
     * @param processCode 审批模版code
     * @return 钉钉事件处理器
     */
    public static DingdingEventTaskHandler builderTask(String processCode){
        DingdingEventTaskHandler dingdingEventTaskHandler = null;
        if (CollectionUtils.isEmpty(dingdingEventTaskHandlers)) {
            dingdingEventTaskHandlers = SpringContextUtil.getApplicationContext()
                    .getBeansOfType(DingdingEventTaskHandler.class)
                    .values();
        }
        return dingdingEventTaskHandlers.stream()
                .filter(bean -> processCode.equals(bean.getProcessCode()))
                .findFirst().orElse(dingdingEventTaskHandler);
    }

    /**
     * 钉钉事件处理器创建 审批事件
     * @param processCode 审批模版code
     * @return 钉钉事件处理器
     */
    public static DingdingEventTaskHandler builderFeiShuTask(String processCode){
        TempThreadLocalUtil.feiShuProcessStart();
        try {
            DingdingEventTaskHandler dingdingEventTaskHandler = null;
            if (CollectionUtils.isEmpty(dingdingEventTaskHandlers)) {
                dingdingEventTaskHandlers = SpringContextUtil.getApplicationContext()
                        .getBeansOfType(DingdingEventTaskHandler.class)
                        .values();
            }
            return dingdingEventTaskHandlers.stream()
                    .filter(bean -> processCode.equals(bean.getProcessCode()))
                    .findFirst().orElse(dingdingEventTaskHandler);
        } finally {
            TempThreadLocalUtil.processClear();
        }
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        //防止重复执行。
        if(event.getApplicationContext().getParent() == null){
            dingdingEventHandlers = SpringContextUtil.getApplicationContext()
                    .getBeansOfType(DingdingEventHandler.class)
                    .values();
            log.info("钉钉审批回调事件处理器初始化完成,共初始化{}个事件处理器", dingdingEventHandlers.size());
        }
    }
}