
# mybatis sql 打印
#logging:
#   level:
#     net.summerfarm.mapper.manage.TaxRateConfigMapper: debug
#mybatis:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
server:
  port: 80
  servlet:
    session:
      timeout: 3600
spring:
  application:
    id: manage
    name: manage
    servlet:
      multipart:
        max-file-size: 5MB
  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制
  http:
    encoding:
      charset: UTF-8
  datasource:
    tomcat:
      initial-size: 0
      max-active: 20
      max-idle: 20
      min-idle: 1
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 30000
  freemarker:
    template-loader-path: classpath:static/template/
    suffix: .ftl
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: 0fba89cd-351e-4e9f-86dc-4b4fbc06170e
    groupId: manage
    appKey: 4RjgQTxjHT9m5Cquz61BFQ==

logging:
  level:
    root:  info
    org.springframework:  INFO
    org.mybatis:  INFO
    com.summerfarm: INFO
  pattern:
    console: "%d - %msg%n"

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 关闭es健康检查
management:
  health:
    elasticsearch:
      enabled: false
log-path: ${APP_LOG_DIR:../log}

mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 1
  maxActive: 20
  maxWait: 6000
  minIdle: 1
  offline:
    password: xianmu619
    url: **************************************************************************************************
    username: test
  password: xianmu619
  testWhileIdle: true
  url: ******************************************************************************************
  username: test
rocketmq:
  consumer:
    access-key: Rocketmq
    secret-key: Rocketmq
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: Rocketmq
    group: GID_manage
    secret-key: Rocketmq
    sendMsgTimeout: 10000
stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.feishu.cn
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory
xianmu:
  mall:
    domain: https://devh5.summerfarm.net
dms:
  accessKeyId: LTAI5tPZ4eRj2vMx8tfZY49C
  accessKeySecret: ******************************
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619

wechat:
  mp:
    app-id: wxe974267a2af8c927
    secret: 5b1d731e26f5d942de354d8d0f719ce2
    aes-key: wtvj3m8TT8ewbwk3pJVSO3zqIXLP1wvbNe3enDurrOA
    token: summerfarm
    #mybatis:
    #configuration:
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
saas:
  domain: http://cosfo-mall-svc
  host: http://cosfo-mall-svc
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: cfccb911-1306-4ea7-8a9f-18436f9dd245
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: offline
    timeout: 5000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    timeout: 10000

xm:
  oss:
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devossperm.summerfarm.net
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devosstemp.summerfarm.net
  log:
    enable: true
    resp:

dingTalk:
  workNotice:
    stockArrange:
      appKey: dingkgj359gpyufmhewq
      appSecret: 3-FVw314POXaPNTwHBjMHUcAtcrEHcmJnve8JBBLwul90NVIH4RdowHL8aocLKAj
      agentId: 2601674134