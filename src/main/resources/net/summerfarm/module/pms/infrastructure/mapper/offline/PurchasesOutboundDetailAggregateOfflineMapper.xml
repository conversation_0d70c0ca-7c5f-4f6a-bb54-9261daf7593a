<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.pms.infrastructure.mapper.offline.PurchasesOutboundDetailAggregateOfflineMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.pms.infrastructure.model.offline.PurchasesOutboundDetailAggregateOfflineEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="refund_batch_no" jdbcType="VARCHAR" property="refundBatchNo" />
    <result column="outbound_stock" jdbcType="INTEGER" property="outboundStock" />
    <result column="outbound_price" jdbcType="DECIMAL" property="outboundPrice" />
    <result column="purchases_stock" jdbcType="INTEGER" property="purchasesStock" />
    <result column="purchases_price" jdbcType="DECIMAL" property="purchasesPrice" />
    <result column="sku_no" jdbcType="VARCHAR" property="skuNo" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="packaging" jdbcType="VARCHAR" property="packaging" />
    <result column="saas_sku_no" jdbcType="VARCHAR" property="saasSkuNo" />
    <result column="saas_sku_name" jdbcType="VARCHAR" property="saasSkuName" />
    <result column="saas_specification" jdbcType="VARCHAR" property="saasSpecification" />
    <result column="saas_packaging" jdbcType="VARCHAR" property="saasPackaging" />
    <result column="outbound_create_user_id" jdbcType="BIGINT" property="outboundCreateUserId" />
    <result column="outbound_create_user_name" jdbcType="VARCHAR" property="outboundCreateUserName" />
    <result column="outbound_create_user_phone" jdbcType="VARCHAR" property="outboundCreateUserPhone" />
    <result column="inbound_create_user_id" jdbcType="BIGINT" property="inboundCreateUserId" />
    <result column="inbound_create_user_name" jdbcType="VARCHAR" property="inboundCreateUserName" />
    <result column="inbound_create_user_phone" jdbcType="VARCHAR" property="inboundCreateUserPhone" />
    <result column="supplier_type" jdbcType="INTEGER" property="supplierType" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="tenant_name" jdbcType="VARCHAR" property="tenantName" />
    <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="outbound_time" jdbcType="TIMESTAMP" property="outboundTime" />
    <result column="supplier_id" jdbcType="BIGINT" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, batch_no, refund_batch_no, outbound_stock, outbound_price, 
    purchases_stock, purchases_price, sku_no, sku_name, specification, packaging, saas_sku_no, 
    saas_sku_name, saas_specification, saas_packaging, outbound_create_user_id, outbound_create_user_name, 
    outbound_create_user_phone, inbound_create_user_id, inbound_create_user_name, inbound_create_user_phone, 
    supplier_type, tenant_id, tenant_name, warehouse_id, warehouse_name, outbound_time, 
    supplier_id, supplier_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchases_outbound_detail_aggregate_offline
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchases_outbound_detail_aggregate_offline
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.pms.infrastructure.model.offline.PurchasesOutboundDetailAggregateOfflineEntity">
    insert into purchases_outbound_detail_aggregate_offline (id, create_time, update_time, 
      batch_no, refund_batch_no, outbound_stock, 
      outbound_price, purchases_stock, purchases_price, 
      sku_no, sku_name, specification, 
      packaging, saas_sku_no, saas_sku_name, 
      saas_specification, saas_packaging, outbound_create_user_id, 
      outbound_create_user_name, outbound_create_user_phone, 
      inbound_create_user_id, inbound_create_user_name, 
      inbound_create_user_phone, supplier_type, tenant_id, 
      tenant_name, warehouse_id, warehouse_name, 
      outbound_time, supplier_id, supplier_name
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{batchNo,jdbcType=VARCHAR}, #{refundBatchNo,jdbcType=VARCHAR}, #{outboundStock,jdbcType=INTEGER}, 
      #{outboundPrice,jdbcType=DECIMAL}, #{purchasesStock,jdbcType=INTEGER}, #{purchasesPrice,jdbcType=DECIMAL}, 
      #{skuNo,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, #{specification,jdbcType=VARCHAR}, 
      #{packaging,jdbcType=VARCHAR}, #{saasSkuNo,jdbcType=VARCHAR}, #{saasSkuName,jdbcType=VARCHAR}, 
      #{saasSpecification,jdbcType=VARCHAR}, #{saasPackaging,jdbcType=VARCHAR}, #{outboundCreateUserId,jdbcType=BIGINT}, 
      #{outboundCreateUserName,jdbcType=VARCHAR}, #{outboundCreateUserPhone,jdbcType=VARCHAR}, 
      #{inboundCreateUserId,jdbcType=BIGINT}, #{inboundCreateUserName,jdbcType=VARCHAR}, 
      #{inboundCreateUserPhone,jdbcType=VARCHAR}, #{supplierType,jdbcType=INTEGER}, #{tenantId,jdbcType=BIGINT}, 
      #{tenantName,jdbcType=VARCHAR}, #{warehouseId,jdbcType=BIGINT}, #{warehouseName,jdbcType=VARCHAR}, 
      #{outboundTime,jdbcType=TIMESTAMP}, #{supplierId,jdbcType=BIGINT}, #{supplierName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.pms.infrastructure.model.offline.PurchasesOutboundDetailAggregateOfflineEntity">
    insert into purchases_outbound_detail_aggregate_offline
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="refundBatchNo != null">
        refund_batch_no,
      </if>
      <if test="outboundStock != null">
        outbound_stock,
      </if>
      <if test="outboundPrice != null">
        outbound_price,
      </if>
      <if test="purchasesStock != null">
        purchases_stock,
      </if>
      <if test="purchasesPrice != null">
        purchases_price,
      </if>
      <if test="skuNo != null">
        sku_no,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="packaging != null">
        packaging,
      </if>
      <if test="saasSkuNo != null">
        saas_sku_no,
      </if>
      <if test="saasSkuName != null">
        saas_sku_name,
      </if>
      <if test="saasSpecification != null">
        saas_specification,
      </if>
      <if test="saasPackaging != null">
        saas_packaging,
      </if>
      <if test="outboundCreateUserId != null">
        outbound_create_user_id,
      </if>
      <if test="outboundCreateUserName != null">
        outbound_create_user_name,
      </if>
      <if test="outboundCreateUserPhone != null">
        outbound_create_user_phone,
      </if>
      <if test="inboundCreateUserId != null">
        inbound_create_user_id,
      </if>
      <if test="inboundCreateUserName != null">
        inbound_create_user_name,
      </if>
      <if test="inboundCreateUserPhone != null">
        inbound_create_user_phone,
      </if>
      <if test="supplierType != null">
        supplier_type,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="tenantName != null">
        tenant_name,
      </if>
      <if test="warehouseId != null">
        warehouse_id,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="outboundTime != null">
        outbound_time,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="refundBatchNo != null">
        #{refundBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="outboundStock != null">
        #{outboundStock,jdbcType=INTEGER},
      </if>
      <if test="outboundPrice != null">
        #{outboundPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchasesStock != null">
        #{purchasesStock,jdbcType=INTEGER},
      </if>
      <if test="purchasesPrice != null">
        #{purchasesPrice,jdbcType=DECIMAL},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="packaging != null">
        #{packaging,jdbcType=VARCHAR},
      </if>
      <if test="saasSkuNo != null">
        #{saasSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="saasSkuName != null">
        #{saasSkuName,jdbcType=VARCHAR},
      </if>
      <if test="saasSpecification != null">
        #{saasSpecification,jdbcType=VARCHAR},
      </if>
      <if test="saasPackaging != null">
        #{saasPackaging,jdbcType=VARCHAR},
      </if>
      <if test="outboundCreateUserId != null">
        #{outboundCreateUserId,jdbcType=BIGINT},
      </if>
      <if test="outboundCreateUserName != null">
        #{outboundCreateUserName,jdbcType=VARCHAR},
      </if>
      <if test="outboundCreateUserPhone != null">
        #{outboundCreateUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="inboundCreateUserId != null">
        #{inboundCreateUserId,jdbcType=BIGINT},
      </if>
      <if test="inboundCreateUserName != null">
        #{inboundCreateUserName,jdbcType=VARCHAR},
      </if>
      <if test="inboundCreateUserPhone != null">
        #{inboundCreateUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="supplierType != null">
        #{supplierType,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="tenantName != null">
        #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=BIGINT},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="outboundTime != null">
        #{outboundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=BIGINT},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.pms.infrastructure.model.offline.PurchasesOutboundDetailAggregateOfflineEntity">
    update purchases_outbound_detail_aggregate_offline
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="refundBatchNo != null">
        refund_batch_no = #{refundBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="outboundStock != null">
        outbound_stock = #{outboundStock,jdbcType=INTEGER},
      </if>
      <if test="outboundPrice != null">
        outbound_price = #{outboundPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchasesStock != null">
        purchases_stock = #{purchasesStock,jdbcType=INTEGER},
      </if>
      <if test="purchasesPrice != null">
        purchases_price = #{purchasesPrice,jdbcType=DECIMAL},
      </if>
      <if test="skuNo != null">
        sku_no = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        specification = #{specification,jdbcType=VARCHAR},
      </if>
      <if test="packaging != null">
        packaging = #{packaging,jdbcType=VARCHAR},
      </if>
      <if test="saasSkuNo != null">
        saas_sku_no = #{saasSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="saasSkuName != null">
        saas_sku_name = #{saasSkuName,jdbcType=VARCHAR},
      </if>
      <if test="saasSpecification != null">
        saas_specification = #{saasSpecification,jdbcType=VARCHAR},
      </if>
      <if test="saasPackaging != null">
        saas_packaging = #{saasPackaging,jdbcType=VARCHAR},
      </if>
      <if test="outboundCreateUserId != null">
        outbound_create_user_id = #{outboundCreateUserId,jdbcType=BIGINT},
      </if>
      <if test="outboundCreateUserName != null">
        outbound_create_user_name = #{outboundCreateUserName,jdbcType=VARCHAR},
      </if>
      <if test="outboundCreateUserPhone != null">
        outbound_create_user_phone = #{outboundCreateUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="inboundCreateUserId != null">
        inbound_create_user_id = #{inboundCreateUserId,jdbcType=BIGINT},
      </if>
      <if test="inboundCreateUserName != null">
        inbound_create_user_name = #{inboundCreateUserName,jdbcType=VARCHAR},
      </if>
      <if test="inboundCreateUserPhone != null">
        inbound_create_user_phone = #{inboundCreateUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="supplierType != null">
        supplier_type = #{supplierType,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="tenantName != null">
        tenant_name = #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseId != null">
        warehouse_id = #{warehouseId,jdbcType=BIGINT},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="outboundTime != null">
        outbound_time = #{outboundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=BIGINT},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.pms.infrastructure.model.offline.PurchasesOutboundDetailAggregateOfflineEntity">
    update purchases_outbound_detail_aggregate_offline
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      refund_batch_no = #{refundBatchNo,jdbcType=VARCHAR},
      outbound_stock = #{outboundStock,jdbcType=INTEGER},
      outbound_price = #{outboundPrice,jdbcType=DECIMAL},
      purchases_stock = #{purchasesStock,jdbcType=INTEGER},
      purchases_price = #{purchasesPrice,jdbcType=DECIMAL},
      sku_no = #{skuNo,jdbcType=VARCHAR},
      sku_name = #{skuName,jdbcType=VARCHAR},
      specification = #{specification,jdbcType=VARCHAR},
      packaging = #{packaging,jdbcType=VARCHAR},
      saas_sku_no = #{saasSkuNo,jdbcType=VARCHAR},
      saas_sku_name = #{saasSkuName,jdbcType=VARCHAR},
      saas_specification = #{saasSpecification,jdbcType=VARCHAR},
      saas_packaging = #{saasPackaging,jdbcType=VARCHAR},
      outbound_create_user_id = #{outboundCreateUserId,jdbcType=BIGINT},
      outbound_create_user_name = #{outboundCreateUserName,jdbcType=VARCHAR},
      outbound_create_user_phone = #{outboundCreateUserPhone,jdbcType=VARCHAR},
      inbound_create_user_id = #{inboundCreateUserId,jdbcType=BIGINT},
      inbound_create_user_name = #{inboundCreateUserName,jdbcType=VARCHAR},
      inbound_create_user_phone = #{inboundCreateUserPhone,jdbcType=VARCHAR},
      supplier_type = #{supplierType,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      tenant_name = #{tenantName,jdbcType=VARCHAR},
      warehouse_id = #{warehouseId,jdbcType=BIGINT},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      outbound_time = #{outboundTime,jdbcType=TIMESTAMP},
      supplier_id = #{supplierId,jdbcType=BIGINT},
      supplier_name = #{supplierName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryInterval" parameterType="net.summerfarm.module.pms.infrastructure.param.PurchasesOutboundQueryParam"
  resultType="net.summerfarm.module.pms.infrastructure.model.offline.PurchasesPaymentIntervalEntity">
    select min(id) minId , max(id) maxId from purchases_outbound_detail_aggregate_offline
    <where>
      <if test="startTime != null">
        and outbound_time &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        and outbound_time &lt;= #{endTime}
      </if>
      <if test="supplierList != null and supplierList.size() > 0">
        and supplier_id in
        <foreach collection="supplierList" item="supplierId" open="(" close=")" separator=",">
          #{supplierId}
        </foreach>
      </if>
    </where>
  </select>

  <select id="selectByParam" parameterType="net.summerfarm.module.pms.infrastructure.param.PurchasesOutboundQueryParam"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from purchases_outbound_detail_aggregate_offline
    <where>
      <if test="startTime != null">
        and outbound_time &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        and outbound_time &lt;= #{endTime}
      </if>
      <if test="supplierList != null and supplierList.size() > 0">
        and supplier_id in
        <foreach collection="supplierList" item="supplierId" open="(" close=")" separator=",">
          #{supplierId}
        </foreach>
      </if>
      <if test="outboundIdGte != null">
        and id &gt;= #{outboundIdGte}
      </if>
      <if test="outboundIdLte != null">
        and id &lt;= #{outboundIdLte}
      </if>
    </where>
    <if test="limitNum != null">
      limit #{limitNum}
    </if>
  </select>
</mapper>