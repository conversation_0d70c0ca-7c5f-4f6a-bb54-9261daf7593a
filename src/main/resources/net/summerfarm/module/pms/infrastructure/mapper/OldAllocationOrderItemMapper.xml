<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.module.pms.infrastructure.mapper.OldAllocationOrderItemMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.module.pms.infrastructure.model.AllocationOrderItemEntity" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="list_no" property="listNo" jdbcType="VARCHAR" />
    <result column="out_quantity" property="outQuantity" jdbcType="INTEGER" />
    <result column="allocation_lock_quantity" property="allocationLockQuantity" jdbcType="INTEGER" />
    <result column="reason" property="reason" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_role" property="createRole" jdbcType="VARCHAR" />
    <result column="type" property="type"/>
  </resultMap>

  <resultMap id="WithDetail" type="net.summerfarm.module.pms.model.vo.AllocationOrderItemEntityVO">
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="list_no" property="listNo" jdbcType="VARCHAR" />
    <result column="out_quantity" property="outQuantity" jdbcType="INTEGER" />
    <result column="reason" property="reason" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="addtime" property="addtime"/>
    <result column="updatetime" property="updatetime" />
    <result column="pd_name" property="pdName" />
    <result column="weight" property="weight" />
    <result column="maturity" property="maturity" />
    <result column="unit" property="unit" />
    <result column="storage_method" property="storageMethod" />
    <result column="storage_location" property="storageLocation"/>
    <result column="type" property="type"/>
    <result column="price" property="price" />
    <result column="weightNum" property="weightNum" jdbcType="DECIMAL"/>
    <result column="volumn" property="volume" jdbcType="VARCHAR" />
    <result column="nameRemakes" property="nameRemakes"/>
    <result column="skuType" property="skuType"/>
    <result column="out_time" property="outTime"/>
    <result column="type" property="type"/>
    <result column="allocation_lock_quantity" property="allocationLockQuantity"/>
    <result column="is_direct" property="isDirect"/>
    <result column="system_adjust" property="systemAdjust"/>
    <result column="purchaser_adjust" property="purchaserAdjust"/>
    <result column="purchase_remark" property="purchaseRemark"/>
    <result column="sale_adjust" property="saleAdjust"/>
    <result column="sale_remark" property="saleRemark"/>
    <result column="operate_adjust" property="operateAdjust"/>
    <result column="operate_remark" property="operateRemark"/>
    <result column="create_role" property="createRole"/>
    <result column="creator" property="creator"/>
    <result column="pd_no" property="pdNo"/>
    <collection property="stockAllocationItemDetails" column="id" javaType="ArrayList"
                select="net.summerfarm.module.pms.infrastructure.mapper.OldAllocationOrderItemMapper.selectByItemId"/>
  </resultMap>


  <select id="selectWithDetail" parameterType="string" resultMap="WithDetail">
    SELECT t.id, t.sku, t.list_no, t.out_quantity, t.reason, t.remark, t.addtime, t.updatetime,
     t.pd_name, t.weight, t.maturity, t.type,t.price, ar1.status outStatus, ar2.status inStatus,s.in_store inStore,s.out_store outStore, i.unit, p.storage_method,
     i.weight_num weightNum,i.volume,ad.name_remakes nameRemakes,p.storage_location,i.type skuType,i.ext_type extType, t.type, t.allocation_lock_quantity, t.is_direct, t.system_adjust,
     t.purchaser_adjust, t.purchase_remark, t.sale_adjust, t.sale_remark, t.operate_adjust , t.operate_remark , t.create_role, t.creator, p.pd_no pdNo ,p.pd_id pdId
    FROM stock_allocation_item t
    LEFT  JOIN stock_allocation_list s on s.list_no=t.list_no
    left join inventory i on i.sku = t.sku
    left join products p on p.pd_id = i.pd_id
    LEFT  JOIN warehouse_stock_ext ar1 on ar1.sku=t.sku and ar1.warehouse_no = s.out_store
    LEFT  JOIN warehouse_stock_ext ar2 on ar2.sku=t.sku and ar2.warehouse_no = s.in_store
    left join admin ad on ad.admin_id = i.admin_id
    WHERE t.list_no = #{listNo}
  </select>

  <select id="getRoad" resultType="net.summerfarm.model.domain.StockAllocationItemDetail">
    select sal.expect_time receiveTime,sai.out_quantity actualOutQuantity from stock_allocation_list sal
left join stock_allocation_item sai on sal.list_no=sai.list_no
left join stock_allocation_item_detail said on said.stock_allocation_item_id=sai.id
where sal.status=4 and sai.sku=#{sku} and sal.in_store=#{areaNo}
  </select>

  <select id="selectByListNoAndSku" resultType="net.summerfarm.module.pms.infrastructure.model.AllocationOrderItemEntity">
    SELECT id,sku,list_no listNo,out_quantity outQuantity,reason,remark,pd_name pdName,weight,maturity,status,type,price
    FROM stock_allocation_item
    WHERE list_no = #{listNo,jdbcType=VARCHAR}
    <if test="list != null and list.size>0">
      AND sku IN
      <foreach collection="list" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="selectBySku" resultType="net.summerfarm.module.pms.infrastructure.model.AllocationOrderItemEntity">
SELECT id,sku,list_no listNo,out_quantity outQuantity,reason,remark,pd_name pdName,weight,maturity,status,type,price,create_role createRole
    FROM stock_allocation_item
    WHERE list_no = #{listNo,jdbcType=VARCHAR} and sku = #{sku}
  </select>

</mapper>