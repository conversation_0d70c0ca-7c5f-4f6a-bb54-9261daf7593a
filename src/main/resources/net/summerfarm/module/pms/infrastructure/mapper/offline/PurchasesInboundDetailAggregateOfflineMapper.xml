<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.pms.infrastructure.mapper.offline.PurchasesInboundDetailAggregateOfflineMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.pms.infrastructure.model.offline.PurchasesInboundDetailAggregateOfflineEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="inbound_stock" jdbcType="INTEGER" property="inboundStock" />
    <result column="inbound_price" jdbcType="DECIMAL" property="inboundPrice" />
    <result column="purchases_stock" jdbcType="INTEGER" property="purchasesStock" />
    <result column="purchases_price" jdbcType="DECIMAL" property="purchasesPrice" />
    <result column="sku_no" jdbcType="VARCHAR" property="skuNo" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="packaging" jdbcType="VARCHAR" property="packaging" />
    <result column="saas_sku_no" jdbcType="VARCHAR" property="saasSkuNo" />
    <result column="saas_sku_name" jdbcType="VARCHAR" property="saasSkuName" />
    <result column="saas_specification" jdbcType="VARCHAR" property="saasSpecification" />
    <result column="saas_packaging" jdbcType="VARCHAR" property="saasPackaging" />
    <result column="inbound_create_user_id" jdbcType="BIGINT" property="inboundCreateUserId" />
    <result column="inbound_create_user_name" jdbcType="VARCHAR" property="inboundCreateUserName" />
    <result column="inbound_create_user_phone" jdbcType="VARCHAR" property="inboundCreateUserPhone" />
    <result column="warehouse_id" jdbcType="BIGINT" property="warehouseId" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="tenant_name" jdbcType="VARCHAR" property="tenantName" />
    <result column="inbound_time" jdbcType="TIMESTAMP" property="inboundTime" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="supplier_id" jdbcType="BIGINT" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_type" jdbcType="INTEGER" property="supplierType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, batch_no, inbound_stock, inbound_price, purchases_stock, 
    purchases_price, sku_no, sku_name, specification, packaging, saas_sku_no, saas_sku_name, 
    saas_specification, saas_packaging, inbound_create_user_id, inbound_create_user_name, 
    inbound_create_user_phone, warehouse_id, tenant_id, tenant_name, inbound_time, warehouse_name, 
    supplier_id, supplier_name, supplier_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchases_inbound_detail_aggregate_offline
    where id = #{id,jdbcType=BIGINT}
  </select>



  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchases_inbound_detail_aggregate_offline
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.pms.infrastructure.model.offline.PurchasesInboundDetailAggregateOfflineEntity">
    insert into purchases_inbound_detail_aggregate_offline (id, create_time, update_time, 
      batch_no, inbound_stock, inbound_price, 
      purchases_stock, purchases_price, sku_no, 
      sku_name, specification, packaging, 
      saas_sku_no, saas_sku_name, saas_specification, 
      saas_packaging, inbound_create_user_id, inbound_create_user_name, 
      inbound_create_user_phone, warehouse_id, tenant_id, 
      tenant_name, inbound_time, warehouse_name, 
      supplier_id, supplier_name, supplier_type
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{batchNo,jdbcType=VARCHAR}, #{inboundStock,jdbcType=INTEGER}, #{inboundPrice,jdbcType=DECIMAL}, 
      #{purchasesStock,jdbcType=INTEGER}, #{purchasesPrice,jdbcType=DECIMAL}, #{skuNo,jdbcType=VARCHAR}, 
      #{skuName,jdbcType=VARCHAR}, #{specification,jdbcType=VARCHAR}, #{packaging,jdbcType=VARCHAR}, 
      #{saasSkuNo,jdbcType=VARCHAR}, #{saasSkuName,jdbcType=VARCHAR}, #{saasSpecification,jdbcType=VARCHAR}, 
      #{saasPackaging,jdbcType=VARCHAR}, #{inboundCreateUserId,jdbcType=BIGINT}, #{inboundCreateUserName,jdbcType=VARCHAR}, 
      #{inboundCreateUserPhone,jdbcType=VARCHAR}, #{warehouseId,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, 
      #{tenantName,jdbcType=VARCHAR}, #{inboundTime,jdbcType=TIMESTAMP}, #{warehouseName,jdbcType=VARCHAR}, 
      #{supplierId,jdbcType=BIGINT}, #{supplierName,jdbcType=VARCHAR}, #{supplierType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.pms.infrastructure.model.offline.PurchasesInboundDetailAggregateOfflineEntity">
    insert into purchases_inbound_detail_aggregate_offline
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="inboundStock != null">
        inbound_stock,
      </if>
      <if test="inboundPrice != null">
        inbound_price,
      </if>
      <if test="purchasesStock != null">
        purchases_stock,
      </if>
      <if test="purchasesPrice != null">
        purchases_price,
      </if>
      <if test="skuNo != null">
        sku_no,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="packaging != null">
        packaging,
      </if>
      <if test="saasSkuNo != null">
        saas_sku_no,
      </if>
      <if test="saasSkuName != null">
        saas_sku_name,
      </if>
      <if test="saasSpecification != null">
        saas_specification,
      </if>
      <if test="saasPackaging != null">
        saas_packaging,
      </if>
      <if test="inboundCreateUserId != null">
        inbound_create_user_id,
      </if>
      <if test="inboundCreateUserName != null">
        inbound_create_user_name,
      </if>
      <if test="inboundCreateUserPhone != null">
        inbound_create_user_phone,
      </if>
      <if test="warehouseId != null">
        warehouse_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="tenantName != null">
        tenant_name,
      </if>
      <if test="inboundTime != null">
        inbound_time,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="supplierType != null">
        supplier_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="inboundStock != null">
        #{inboundStock,jdbcType=INTEGER},
      </if>
      <if test="inboundPrice != null">
        #{inboundPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchasesStock != null">
        #{purchasesStock,jdbcType=INTEGER},
      </if>
      <if test="purchasesPrice != null">
        #{purchasesPrice,jdbcType=DECIMAL},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="packaging != null">
        #{packaging,jdbcType=VARCHAR},
      </if>
      <if test="saasSkuNo != null">
        #{saasSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="saasSkuName != null">
        #{saasSkuName,jdbcType=VARCHAR},
      </if>
      <if test="saasSpecification != null">
        #{saasSpecification,jdbcType=VARCHAR},
      </if>
      <if test="saasPackaging != null">
        #{saasPackaging,jdbcType=VARCHAR},
      </if>
      <if test="inboundCreateUserId != null">
        #{inboundCreateUserId,jdbcType=BIGINT},
      </if>
      <if test="inboundCreateUserName != null">
        #{inboundCreateUserName,jdbcType=VARCHAR},
      </if>
      <if test="inboundCreateUserPhone != null">
        #{inboundCreateUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="warehouseId != null">
        #{warehouseId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="tenantName != null">
        #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="inboundTime != null">
        #{inboundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=BIGINT},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierType != null">
        #{supplierType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.pms.infrastructure.model.offline.PurchasesInboundDetailAggregateOfflineEntity">
    update purchases_inbound_detail_aggregate_offline
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="inboundStock != null">
        inbound_stock = #{inboundStock,jdbcType=INTEGER},
      </if>
      <if test="inboundPrice != null">
        inbound_price = #{inboundPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchasesStock != null">
        purchases_stock = #{purchasesStock,jdbcType=INTEGER},
      </if>
      <if test="purchasesPrice != null">
        purchases_price = #{purchasesPrice,jdbcType=DECIMAL},
      </if>
      <if test="skuNo != null">
        sku_no = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        specification = #{specification,jdbcType=VARCHAR},
      </if>
      <if test="packaging != null">
        packaging = #{packaging,jdbcType=VARCHAR},
      </if>
      <if test="saasSkuNo != null">
        saas_sku_no = #{saasSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="saasSkuName != null">
        saas_sku_name = #{saasSkuName,jdbcType=VARCHAR},
      </if>
      <if test="saasSpecification != null">
        saas_specification = #{saasSpecification,jdbcType=VARCHAR},
      </if>
      <if test="saasPackaging != null">
        saas_packaging = #{saasPackaging,jdbcType=VARCHAR},
      </if>
      <if test="inboundCreateUserId != null">
        inbound_create_user_id = #{inboundCreateUserId,jdbcType=BIGINT},
      </if>
      <if test="inboundCreateUserName != null">
        inbound_create_user_name = #{inboundCreateUserName,jdbcType=VARCHAR},
      </if>
      <if test="inboundCreateUserPhone != null">
        inbound_create_user_phone = #{inboundCreateUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="warehouseId != null">
        warehouse_id = #{warehouseId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="tenantName != null">
        tenant_name = #{tenantName,jdbcType=VARCHAR},
      </if>
      <if test="inboundTime != null">
        inbound_time = #{inboundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=BIGINT},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierType != null">
        supplier_type = #{supplierType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.pms.infrastructure.model.offline.PurchasesInboundDetailAggregateOfflineEntity">
    update purchases_inbound_detail_aggregate_offline
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      inbound_stock = #{inboundStock,jdbcType=INTEGER},
      inbound_price = #{inboundPrice,jdbcType=DECIMAL},
      purchases_stock = #{purchasesStock,jdbcType=INTEGER},
      purchases_price = #{purchasesPrice,jdbcType=DECIMAL},
      sku_no = #{skuNo,jdbcType=VARCHAR},
      sku_name = #{skuName,jdbcType=VARCHAR},
      specification = #{specification,jdbcType=VARCHAR},
      packaging = #{packaging,jdbcType=VARCHAR},
      saas_sku_no = #{saasSkuNo,jdbcType=VARCHAR},
      saas_sku_name = #{saasSkuName,jdbcType=VARCHAR},
      saas_specification = #{saasSpecification,jdbcType=VARCHAR},
      saas_packaging = #{saasPackaging,jdbcType=VARCHAR},
      inbound_create_user_id = #{inboundCreateUserId,jdbcType=BIGINT},
      inbound_create_user_name = #{inboundCreateUserName,jdbcType=VARCHAR},
      inbound_create_user_phone = #{inboundCreateUserPhone,jdbcType=VARCHAR},
      warehouse_id = #{warehouseId,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      tenant_name = #{tenantName,jdbcType=VARCHAR},
      inbound_time = #{inboundTime,jdbcType=TIMESTAMP},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      supplier_id = #{supplierId,jdbcType=BIGINT},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      supplier_type = #{supplierType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryInterval" parameterType="net.summerfarm.module.pms.infrastructure.param.PurchasesInboundQueryParam"
  resultType="net.summerfarm.module.pms.infrastructure.model.offline.PurchasesPaymentIntervalEntity">
    select min(id) minId , max(id) maxId from purchases_inbound_detail_aggregate_offline
    <where>
      <if test="startTime != null">
        and inbound_time &gt;= #{startTime}
      </if>
      <if test="endTime != null">
      and inbound_time &lt;= #{endTime}
    </if>
      <if test="supplierList != null and supplierList.size() > 0">
        and supplier_id in
        <foreach collection="supplierList" item="supplierId" open="(" close=")" separator=",">
          #{supplierId}
        </foreach>
      </if>
    </where>
  </select>

  <select id="selectByParam" parameterType="net.summerfarm.module.pms.infrastructure.param.PurchasesInboundQueryParam"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from purchases_inbound_detail_aggregate_offline
    <where>
      <if test="startTime != null">
        and inbound_time &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        and inbound_time &lt;= #{endTime}
      </if>
      <if test="supplierList != null and supplierList.size() > 0">
        and supplier_id in
        <foreach collection="supplierList" item="supplierId" open="(" close=")" separator=",">
          #{supplierId}
        </foreach>
      </if>
      <if test="inboundIdGte != null">
        and id &gt;= #{inboundIdGte}
      </if>
      <if test="inboundIdLte != null">
         and id &lt;= #{inboundIdLte}
      </if>
    </where>
    <if test="limitNum != null">
       limit #{limitNum}
    </if>
  </select>
</mapper>