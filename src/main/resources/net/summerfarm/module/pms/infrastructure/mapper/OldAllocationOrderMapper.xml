<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.module.pms.infrastructure.mapper.OldAllocationOrderMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.module.pms.infrastructure.model.AllocationOrderEntity" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="list_no" property="listNo" jdbcType="VARCHAR" />
    <result column="create_admin" property="createAdmin" jdbcType="INTEGER" />
    <result column="create_admin_name" property="createAdminName" jdbcType="VARCHAR" />
    <result column="audit_admin" property="auditAdmin" jdbcType="INTEGER" />
    <result column="audit_admin_name" property="auditAdminName" jdbcType="VARCHAR" />
    <result column="out_store" property="outStore" jdbcType="INTEGER" />
    <result column="out_store_name" property="outStoreName" jdbcType="VARCHAR" />
    <result column="in_store" property="inStore" jdbcType="INTEGER" />
    <result column="in_store_name" property="inStoreName" jdbcType="VARCHAR" />
    <result column="out_time" property="outTime" />
    <result column="expect_time" property="expectTime"  />
    <result column="expect_out_time" property="expectOutTime"  />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="in_time" property="inTime"  />
    <result column="in_status" property="inStatus"  />
    <result column="out_status" property="outStatus"  />
    <result column="transport" property="transport" jdbcType="INTEGER" />
    <result column="tracking_no" property="trackingNo" jdbcType="VARCHAR" />
    <result column="addtime" property="addtime" />
    <result column="updatetime" property="updatetime" />
    <result column="plan_list_no" property="planListNo"/>
    <result column="next_day_arrive" property="nextDayArrive"/>
    <result column="plan_list_id" property="planListId"/>
    <result column="trunk_flag" property="trunkFlag"/>
    <result column="tenant_id" property="tenantId"/>
  </resultMap>

  <select id="selectOne" parameterType="string" resultType="net.summerfarm.module.pms.model.vo.AllocationOrderEntityVO">
    select t.id , t.list_no listNo, t.create_admin_name createAdminName, t.create_admin createAdmin, t.out_store_name outStoreName, t.in_store inStore, t.out_store outStore, t.in_store_name inStoreName, t.status, t.addtime, t.updatetime,t.order_type orderType,sr.sale_quantity_start saleQuantityStart,sr.sale_quantity_end saleQuantityEnd,
     t.in_store_admin inStoreAdmin, t.in_store_admin_name inStoreAdminName, t.out_store_admin outStoreAdmin, t.out_store_admin_name outStoreAdminName,sr.config_id configId,
     t.in_status inStatus, t.out_status outStatus,sr.cycle_type cycleType ,sr.sale_partake salePartake,sr.logistics_time logisticsTime,
     t.audit_admin auditAdmin, t.audit_admin_name auditAdminName, t.out_time outTime, t.in_time inTime, t.expect_time expectTime, t.transport, t.tracking_no trackingNo,sr.interval_days intervalDays,sr.purchase_partake purchasePartake,t.expect_out_time expectOutTime,t.plan_list_no planListNo,t.next_day_arrive nextDayArrive,t.plan_list_id planListId,t.trunk_flag trunkFlag,t.tenant_id tenantId,
     t.out_warehouse_tenant_id outWarehouseTenantId,t.in_warehouse_tenant_id inWarehouseTenantId,t.phone,t.tenant_name tenantName
    FROM stock_allocation_list t
     left join stock_allocation_config_record sr on sr.list_no = t.list_no
     WHERE t.list_no = #{listNo}
  </select>

  <select id="selectWithOutDataPermission" resultType="net.summerfarm.module.pms.model.vo.AllocationOrderEntityVO" parameterType="net.summerfarm.module.pms.model.input.StockAllocationInput">
    select DISTINCT t.list_no listNo, t.create_admin_name createAdminName, t.out_store_name outStoreName, t.in_store_name inStoreName, t.status, t.addtime, t.updatetime,
    t.out_store outStore, t.in_store inStore
    from stock_allocation_list t
    <if test="sku != null or pdName != null">
      LEFT JOIN stock_allocation_item sai on t.list_no = sai.list_no
    </if>
    <where>
      <if test="outStore != null">
        AND t.out_store = #{outStore}
      </if>
      <if test="inStore != null">
        AND t.in_store = #{inStore}
      </if>
      <if test="status != null">
        AND t.status = #{status}
      </if>
      <if test="status == null">
        AND t.status != 0
        AND t.status != 9
      </if>
      <if test="createAdmin != null">
        AND t.create_admin = #{createAdmin}
      </if>
      <if test="listNo != null">
        AND t.list_no like concat('%',#{listNo},'%')
      </if>
      <if test="sku !=null">
        AND sai.sku = #{sku}
      </if>
      <if test="pdName != null">
        AND sai.pd_name like concat('%',#{pdName},'%')
      </if>
    </where>
    ORDER BY t.addtime DESC
  </select>

  <select id="selectInStoreByListNoList" resultType="net.summerfarm.module.pms.model.vo.AllocationOrderEntityVO">
    select t.list_no listNo, t.in_store inStore, t.expect_out_time expectOutTime
      FROM stock_allocation_list t
     WHERE t.list_no IN
    <foreach collection="list" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.pms.infrastructure.model.AllocationOrderEntity" >
    update stock_allocation_list
    <set >
      <if test="auditAdmin != null" >
        audit_admin = #{auditAdmin,jdbcType=INTEGER},
      </if>
        <if test="auditAdminName != null">
            audit_admin_name =#{auditAdminName},
        </if>
      <if test="outStore != null" >
        out_store = #{outStore,jdbcType=INTEGER},
      </if>
        <if test="outStoreName != null" >
        out_store_name = #{outStoreName},
      </if>
      <if test="inStore != null" >
        in_store = #{inStore},
      </if>
      <if test="inStoreName != null" >
        in_store_name = #{inStoreName},
      </if>
      <if test="outTime != null" >
        out_time = #{outTime},
      </if>
      <if test="expectTime != null" >
        expect_time = #{expectTime},
      </if>
      <if test="expectOutTime != null" >
        expect_out_time = #{expectOutTime},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="inTime != null" >
        in_time = #{inTime},
      </if>
      <if test="transport != null" >
        transport = #{transport,jdbcType=INTEGER},
      </if>
      <if test="trackingNo != null" >
        tracking_no = #{trackingNo,jdbcType=VARCHAR},
      </if>
        <if test="inStoreAdmin != null">
           in_store_admin = #{inStoreAdmin},
        </if>
        <if test=" inStoreAdminName!= null">
            in_store_admin_name= #{inStoreAdminName},
        </if>
        <if test=" outStoreAdmin!= null">
            out_store_admin= #{outStoreAdmin},
        </if>
        <if test=" outStoreAdminName!= null">
            out_store_admin_name = #{outStoreAdminName},
        </if>
        <if test=" outStatus!= null">
            out_status= #{outStatus},
        </if>
        <if test=" inStatus!= null">
            in_status= #{inStatus},
        </if>
        <if test="updatetime != null">
           updatetime = #{updatetime},
        </if>
      <if test="nextDayArrive != null">
        next_day_arrive = #{nextDayArrive},
      </if>
      <if test="trunkFlag != null">
        trunk_flag = #{trunkFlag},
      </if>
    </set>
    where list_no = #{listNo}
  </update>

</mapper>