<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryQuotaCalculateCostEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotaCalculateCostEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bms_delivery_quotation_id" jdbcType="INTEGER" property="bmsDeliveryQuotationId" />
    <result column="calculate_name" jdbcType="VARCHAR" property="calculateName" />
    <result column="formula" jdbcType="VARCHAR" property="formula" />
    <result column="calculate_type" jdbcType="TINYINT" property="calculateType" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, bms_delivery_quotation_id, calculate_name, formula, 
    calculate_type, status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_delivery_quota_calculate_cost
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_delivery_quota_calculate_cost
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotaCalculateCostEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_quota_calculate_cost (id, create_time, update_time, 
      bms_delivery_quotation_id, calculate_name, 
      formula, calculate_type, status
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{bmsDeliveryQuotationId,jdbcType=INTEGER}, #{calculateName,jdbcType=VARCHAR}, 
      #{formula,jdbcType=VARCHAR}, #{calculateType,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotaCalculateCostEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_quota_calculate_cost
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="bmsDeliveryQuotationId != null">
        bms_delivery_quotation_id,
      </if>
      <if test="calculateName != null">
        calculate_name,
      </if>
      <if test="formula != null">
        formula,
      </if>
      <if test="calculateType != null">
        calculate_type,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bmsDeliveryQuotationId != null">
        #{bmsDeliveryQuotationId,jdbcType=INTEGER},
      </if>
      <if test="calculateName != null">
        #{calculateName,jdbcType=VARCHAR},
      </if>
      <if test="formula != null">
        #{formula,jdbcType=VARCHAR},
      </if>
      <if test="calculateType != null">
        #{calculateType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotaCalculateCostEntity">
    update bms_delivery_quota_calculate_cost
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bmsDeliveryQuotationId != null">
        bms_delivery_quotation_id = #{bmsDeliveryQuotationId,jdbcType=INTEGER},
      </if>
      <if test="calculateName != null">
        calculate_name = #{calculateName,jdbcType=VARCHAR},
      </if>
      <if test="formula != null">
        formula = #{formula,jdbcType=VARCHAR},
      </if>
      <if test="calculateType != null">
        calculate_type = #{calculateType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotaCalculateCostEntity">
    update bms_delivery_quota_calculate_cost
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      bms_delivery_quotation_id = #{bmsDeliveryQuotationId,jdbcType=INTEGER},
      calculate_name = #{calculateName,jdbcType=VARCHAR},
      formula = #{formula,jdbcType=VARCHAR},
      calculate_type = #{calculateType,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectNormalByQuotationId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bms_delivery_quota_calculate_cost
    where bms_delivery_quotation_id = #{quotationId,jdbcType=INTEGER} and status = 0
  </select>

  <insert id="batchInsertFormula" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_quota_calculate_cost(
    calculate_name, bms_delivery_quotation_id, formula, calculate_type,create_time,update_time
    )values
    <foreach collection="items" item="item" index="index" separator=",">
      (#{item.calculateName}, #{item.bmsDeliveryQuotationId}, #{item.formula}, #{item.calculateType},#{item.createTime},#{item.updateTime})
    </foreach>
  </insert>

  <delete id="deleteByQuotationId">
    update bms_delivery_quota_calculate_cost set status = -1 where bms_delivery_quotation_id = #{quotationId} and
    id not in <foreach collection="dbIds" item="item" open="(" separator="," close=")">
    #{item}
  </foreach>
  </delete>
  <delete id="deleteAllByQuotationId">
    update bms_delivery_quota_calculate_cost set status = -1 where bms_delivery_quotation_id = #{quotationId}
  </delete>
</mapper>