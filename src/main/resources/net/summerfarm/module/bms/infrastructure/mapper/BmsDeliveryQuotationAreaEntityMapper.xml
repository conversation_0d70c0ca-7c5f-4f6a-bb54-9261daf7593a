<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryQuotationAreaEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationAreaEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="area" jdbcType="VARCHAR" property="area" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, area
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_delivery_quotation_area
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_delivery_quotation_area
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationAreaEntity"  keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_quotation_area (id, create_time, update_time, 
      area)
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{area,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationAreaEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_quotation_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="area != null">
        area,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationAreaEntity">
    update bms_delivery_quotation_area
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationAreaEntity">
    update bms_delivery_quotation_area
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      area = #{area,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByParam" parameterType="net.summerfarm.module.bms.domain.repository.param.QuotationAreaParam"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bms_delivery_quotation_area
    <where>
      <if test="area != null">
        area = #{area}
      </if>
    </where>
  </select>
</mapper>