<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryQuotationRegionEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationRegionEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delivery_quotation_id" jdbcType="BIGINT" property="deliveryQuotationId" />
    <result column="area" jdbcType="VARCHAR" property="area" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, delivery_quotation_id, area
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_delivery_quotation_region
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_delivery_quotation_region
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationRegionEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_quotation_region (id, create_time, update_time, 
      delivery_quotation_id, area)
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deliveryQuotationId,jdbcType=BIGINT}, #{area,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationRegionEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_quotation_region
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deliveryQuotationId != null">
        delivery_quotation_id,
      </if>
      <if test="area != null">
        area,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryQuotationId != null">
        #{deliveryQuotationId,jdbcType=BIGINT},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationRegionEntity">
    update bms_delivery_quotation_region
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryQuotationId != null">
        delivery_quotation_id = #{deliveryQuotationId,jdbcType=BIGINT},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationRegionEntity">
    update bms_delivery_quotation_region
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      delivery_quotation_id = #{deliveryQuotationId,jdbcType=BIGINT},
      area = #{area,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByQuotationId">
    delete from bms_delivery_quotation_region where delivery_quotation_id = #{quotationId}
  </delete>

  <insert id="batchInsertRegion">
    insert into bms_delivery_quotation_region(
    create_time, update_time, delivery_quotation_id, area
    )values
    <foreach collection="items" item="item" index="index" separator=",">
      (#{item.createTime}, #{item.updateTime}, #{item.deliveryQuotationId}, #{item.area})
    </foreach>
  </insert>
</mapper>