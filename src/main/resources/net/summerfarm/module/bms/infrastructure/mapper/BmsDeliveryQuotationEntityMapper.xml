<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryQuotationEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="last_update_id" jdbcType="INTEGER" property="lastUpdateId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="quota_type" jdbcType="INTEGER" property="quotaType" />
    <result column="service_area_id" jdbcType="INTEGER" property="serviceAreaId" />
    <result column="carrier_id" jdbcType="INTEGER" property="carrierId" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="quota_form" jdbcType="TINYINT" property="quotaForm" />
    <result column="service_area_name" jdbcType="VARCHAR" property="serviceAreaName" />
    <result column="bidder_id" jdbcType="BIGINT" property="bidderId" />
    <result column="bidder_name" jdbcType="VARCHAR" property="bidderName" />
    <result column="quota_target_id" jdbcType="BIGINT" property="quotaTargetId" />
    <result column="quota_target_name" jdbcType="VARCHAR" property="quotaTargetName" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, last_update_id, status, store_no, province, city, quota_type, 
    service_area_id, carrier_id, creator, quota_form, service_area_name, bidder_id, bidder_name, 
    quota_target_id, quota_target_name, business_type, create_user_name, update_user_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_delivery_quotation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_delivery_quotation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationEntity"
          keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_quotation (id, create_time, update_time,
      last_update_id, status, store_no, 
      province, city, quota_type, 
      service_area_id, carrier_id, creator, 
      quota_form, service_area_name, bidder_id, 
      bidder_name, quota_target_id, quota_target_name, 
      business_type, create_user_name, update_user_name
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{lastUpdateId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{storeNo,jdbcType=INTEGER}, 
      #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{quotaType,jdbcType=INTEGER}, 
      #{serviceAreaId,jdbcType=INTEGER}, #{carrierId,jdbcType=INTEGER}, #{creator,jdbcType=INTEGER}, 
      #{quotaForm,jdbcType=TINYINT}, #{serviceAreaName,jdbcType=VARCHAR}, #{bidderId,jdbcType=BIGINT}, 
      #{bidderName,jdbcType=VARCHAR}, #{quotaTargetId,jdbcType=BIGINT}, #{quotaTargetName,jdbcType=VARCHAR}, 
      #{businessType,jdbcType=VARCHAR}, #{createUserName,jdbcType=VARCHAR}, #{updateUserName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_quotation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="lastUpdateId != null">
        last_update_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="quotaType != null">
        quota_type,
      </if>
      <if test="serviceAreaId != null">
        service_area_id,
      </if>
      <if test="carrierId != null">
        carrier_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="quotaForm != null">
        quota_form,
      </if>
      <if test="serviceAreaName != null">
        service_area_name,
      </if>
      <if test="bidderId != null">
        bidder_id,
      </if>
      <if test="bidderName != null">
        bidder_name,
      </if>
      <if test="quotaTargetId != null">
        quota_target_id,
      </if>
      <if test="quotaTargetName != null">
        quota_target_name,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="createUserName != null">
        create_user_name,
      </if>
      <if test="updateUserName != null">
        update_user_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateId != null">
        #{lastUpdateId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="quotaType != null">
        #{quotaType,jdbcType=INTEGER},
      </if>
      <if test="serviceAreaId != null">
        #{serviceAreaId,jdbcType=INTEGER},
      </if>
      <if test="carrierId != null">
        #{carrierId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="quotaForm != null">
        #{quotaForm,jdbcType=TINYINT},
      </if>
      <if test="serviceAreaName != null">
        #{serviceAreaName,jdbcType=VARCHAR},
      </if>
      <if test="bidderId != null">
        #{bidderId,jdbcType=BIGINT},
      </if>
      <if test="bidderName != null">
        #{bidderName,jdbcType=VARCHAR},
      </if>
      <if test="quotaTargetId != null">
        #{quotaTargetId,jdbcType=BIGINT},
      </if>
      <if test="quotaTargetName != null">
        #{quotaTargetName,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="createUserName != null">
        #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="updateUserName != null">
        #{updateUserName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationEntity">
    update bms_delivery_quotation
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateId != null">
        last_update_id = #{lastUpdateId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="quotaType != null">
        quota_type = #{quotaType,jdbcType=INTEGER},
      </if>
      <if test="serviceAreaId != null">
        service_area_id = #{serviceAreaId,jdbcType=INTEGER},
      </if>
      <if test="carrierId != null">
        carrier_id = #{carrierId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="quotaForm != null">
        quota_form = #{quotaForm,jdbcType=TINYINT},
      </if>
      <if test="serviceAreaName != null">
        service_area_name = #{serviceAreaName,jdbcType=VARCHAR},
      </if>
      <if test="bidderId != null">
        bidder_id = #{bidderId,jdbcType=BIGINT},
      </if>
      <if test="bidderName != null">
        bidder_name = #{bidderName,jdbcType=VARCHAR},
      </if>
      <if test="quotaTargetId != null">
        quota_target_id = #{quotaTargetId,jdbcType=BIGINT},
      </if>
      <if test="quotaTargetName != null">
        quota_target_name = #{quotaTargetName,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="createUserName != null">
        create_user_name = #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="updateUserName != null">
        update_user_name = #{updateUserName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationEntity">
    update bms_delivery_quotation
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      last_update_id = #{lastUpdateId,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      store_no = #{storeNo,jdbcType=INTEGER},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      quota_type = #{quotaType,jdbcType=INTEGER},
      service_area_id = #{serviceAreaId,jdbcType=INTEGER},
      carrier_id = #{carrierId,jdbcType=INTEGER},
      creator = #{creator,jdbcType=INTEGER},
      quota_form = #{quotaForm,jdbcType=TINYINT},
      service_area_name = #{serviceAreaName,jdbcType=VARCHAR},
      bidder_id = #{bidderId,jdbcType=BIGINT},
      bidder_name = #{bidderName,jdbcType=VARCHAR},
      quota_target_id = #{quotaTargetId,jdbcType=BIGINT},
      quota_target_name = #{quotaTargetName,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      create_user_name = #{createUserName,jdbcType=VARCHAR},
      update_user_name = #{updateUserName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByParam" parameterType="net.summerfarm.module.bms.domain.repository.param.QuotationQueryParam"
          resultMap="BaseResultMap">
    select t1.*
    from bms_delivery_quotation t1
    left join bms_delivery_quotation_region t2 on t1.id = t2.delivery_quotation_id
    <where>
      <if test="id != null">
       and  t1.id = #{id}
      </if>
      <if test="bidderId != null">
        and t1.bidder_id = #{bidderId}
      </if>
      <if test="serviceAreaId != null">
        and t1.service_area_id = #{serviceAreaId}
      </if>
      <if test="quotaTargetId != null">
        and t1.quota_target_id = #{quotaTargetId}
      </if>
      <if test="serviceAreaId != null">
        and t1.service_area_id = #{serviceAreaId}
      </if>
      <if test="status != null">
        and t1.status = #{status}
      </if>
      <if test="businessType != null">
        and  t1.business_type = #{businessType}
      </if>
      <if test="quotaType != null">
        and  t1.quota_type = #{quotaType}
      </if>
      <if test="city != null">
        and  t1.city = #{city}
      </if>
      <if test="areaList != null and areaList.size() > 0">
        and t2.area in
        <foreach collection="areaList" item="area" open="(" close=")" separator=",">
          #{area}
        </foreach>
      </if>
      <if test="cityList != null and cityList.size() > 0">
        and t1.city in
        <foreach collection="cityList" item="city" open="(" close=")" separator=",">
          #{city}
        </foreach>
      </if>
    </where>
    group by t1.id
    order by t1.id desc
  </select>

  <update id="initBusinessType">
    update bms_delivery_quotation set business_type = 'DELIVERY_BUSINESS' where business_type is null
    <if test="idList != null and idList.size() > 0">
      and id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
</update>

  <update id="initOperator">
    update bms_delivery_quotation
    set last_update_id = creator,
        update_time    = create_time
    where last_update_id is null
    <if test="idList != null and idList.size() > 0">
      and id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initBidder">
    update bms_delivery_quotation
    set bidder_id = carrier_id,quota_target_id = store_no
    where (business_type = 'DELIVERY_BUSINESS' or business_type is null)  and bidder_id is null
    <if test="idList != null and idList.size() > 0">
      and id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initServiceAreaName">
    update bms_delivery_quotation t1 left join bms_delivery_quotation_area t2 on t1.service_area_id = t2.id
    set t1.service_area_name = t2.area where t1.service_area_name is null
    <if test="idList != null and idList.size() > 0">
      and t1.id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initBidderName">
    update bms_delivery_quotation t1 left join carrier t2 on t1.carrier_id = t2.id
    set t1.bidder_name = t2.carrier_name where t1.bidder_name is null
    <if test="idList != null and idList.size() > 0">
      and t1.id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initTargetName">
    update bms_delivery_quotation t1 left join warehouse_logistics_center t2 on t1.store_no = t2.store_no
    set t1.quota_target_name = t2.store_name where t1.quota_target_name is null
    <if test="idList != null and idList.size() > 0">
      and t1.id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initCreatName">
    update bms_delivery_quotation t1 left join admin t2 on t1.creator = t2.admin_id
      set t1.create_user_name = t2.realname where t1.create_user_name is null
    <if test="idList != null and idList.size() > 0">
      and t1.id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initUpdateName">
    update bms_delivery_quotation t1 left join admin t2 on t1.last_update_id = t2.admin_id
      set t1.update_user_name = t2.realname where t1.update_user_name is null
    <if test="idList != null and idList.size() > 0">
      and t1.id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <select id="selectAdvanceByQuotationId" resultType="java.math.BigDecimal">
    select IFNULL(SUM(bdqd.amount),0)
    from bms_delivery_quotation_detail bdqd
           left join bms_calculation_item bci on bdqd.bms_calculation_item_id = bci.id
    where bdqd.bms_delivery_quotation_id =#{id}
      and bci.quote_name = '前置仓应付'
  </select>
</mapper>