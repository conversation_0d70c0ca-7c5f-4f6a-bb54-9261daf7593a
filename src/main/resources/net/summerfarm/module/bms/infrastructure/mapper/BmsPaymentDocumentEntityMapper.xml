<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsPaymentDocumentEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsPaymentDocumentEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="payment_no" jdbcType="VARCHAR" property="paymentNo" />
    <result column="payment_type" jdbcType="VARCHAR" property="paymentType" />
    <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount" />
    <result column="payment_status" jdbcType="TINYINT" property="paymentStatus" />
    <result column="invoice_status" jdbcType="TINYINT" property="invoiceStatus" />
    <result column="settle_account_url" jdbcType="VARCHAR" property="settleAccountUrl" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="remake" jdbcType="VARCHAR" property="remake" />
    <result column="invoice_id" jdbcType="INTEGER" property="invoiceId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="carrier_id" jdbcType="INTEGER" property="carrierId" />
    <result column="carrier_account_id" jdbcType="INTEGER" property="carrierAccountId" />
    <result column="settle_month" jdbcType="VARCHAR" property="settleMonth" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="finance_payment_order_id" jdbcType="INTEGER" property="financePaymentOrderId" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="payment_settle_feature" jdbcType="VARCHAR" property="paymentSettleFeature" />
    <result column="payee_id" jdbcType="BIGINT" property="payeeId" />
    <result column="payee_name" jdbcType="VARCHAR" property="payeeName" />
    <result column="payment_account_feature" jdbcType="VARCHAR" property="paymentAccountFeature" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />

  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, payment_no, payment_type, payment_amount, payment_status, 
    invoice_status, settle_account_url, creator, remake, invoice_id, status, carrier_id, 
    carrier_account_id, settle_month, store_no, finance_payment_order_id, business_type, 
    payment_account_feature, payee_id, payee_name,payment_settle_feature,total_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_payment_document
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_payment_document
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsPaymentDocumentEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_payment_document (id, create_time, update_time, 
      payment_no, payment_type, payment_amount, 
      payment_status, invoice_status, settle_account_url, 
      creator, remake, invoice_id, 
      status, carrier_id, carrier_account_id, 
      settle_month, store_no, finance_payment_order_id, 
      business_type, payment_account_feature, payee_id, 
      payee_name,payment_settle_feature,total_amount)
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{paymentNo,jdbcType=VARCHAR}, #{paymentType,jdbcType=VARCHAR}, #{paymentAmount,jdbcType=DECIMAL}, 
      #{paymentStatus,jdbcType=TINYINT}, #{invoiceStatus,jdbcType=TINYINT}, #{settleAccountUrl,jdbcType=VARCHAR}, 
      #{creator,jdbcType=INTEGER}, #{remake,jdbcType=VARCHAR}, #{invoiceId,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{carrierId,jdbcType=INTEGER}, #{carrierAccountId,jdbcType=INTEGER}, 
      #{settleMonth,jdbcType=VARCHAR}, #{storeNo,jdbcType=INTEGER}, #{financePaymentOrderId,jdbcType=INTEGER}, 
      #{businessType,jdbcType=VARCHAR}, #{paymentAccountFeature,jdbcType=VARCHAR}, #{payeeId,jdbcType=BIGINT}, 
      #{payeeName,jdbcType=VARCHAR}, #{paymentSettleFeature,jdbcType=VARCHAR}, #{totalAmount,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsPaymentDocumentEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_payment_document
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="paymentNo != null">
        payment_no,
      </if>
      <if test="paymentType != null">
        payment_type,
      </if>
      <if test="paymentAmount != null">
        payment_amount,
      </if>
      <if test="paymentStatus != null">
        payment_status,
      </if>
      <if test="invoiceStatus != null">
        invoice_status,
      </if>
      <if test="settleAccountUrl != null">
        settle_account_url,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="remake != null">
        remake,
      </if>
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="carrierId != null">
        carrier_id,
      </if>
      <if test="carrierAccountId != null">
        carrier_account_id,
      </if>
      <if test="settleMonth != null">
        settle_month,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="financePaymentOrderId != null">
        finance_payment_order_id,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="paymentAccountFeature != null">
        payment_account_feature,
      </if>
      <if test="payeeId != null">
        payee_id,
      </if>
      <if test="payeeName != null">
        payee_name,
      </if>
      <if test="paymentSettleFeature != null">
        payment_settle_feature,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentNo != null">
        #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=VARCHAR},
      </if>
      <if test="paymentAmount != null">
        #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentStatus != null">
        #{paymentStatus,jdbcType=TINYINT},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="settleAccountUrl != null">
        #{settleAccountUrl,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="remake != null">
        #{remake,jdbcType=VARCHAR},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="carrierId != null">
        #{carrierId,jdbcType=INTEGER},
      </if>
      <if test="carrierAccountId != null">
        #{carrierAccountId,jdbcType=INTEGER},
      </if>
      <if test="settleMonth != null">
        #{settleMonth,jdbcType=VARCHAR},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="financePaymentOrderId != null">
        #{financePaymentOrderId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="paymentAccountFeature != null">
        #{paymentAccountFeature,jdbcType=VARCHAR},
      </if>
      <if test="payeeId != null">
        #{payeeId,jdbcType=BIGINT},
      </if>
      <if test="payeeName != null">
        #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="paymentSettleFeature != null">
        #{paymentSettleFeature,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsPaymentDocumentEntity">
    update bms_payment_document
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentNo != null">
        payment_no = #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="paymentType != null">
        payment_type = #{paymentType,jdbcType=VARCHAR},
      </if>
      <if test="paymentAmount != null">
        payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentStatus != null">
        payment_status = #{paymentStatus,jdbcType=TINYINT},
      </if>
      <if test="invoiceStatus != null">
        invoice_status = #{invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="settleAccountUrl != null">
        settle_account_url = #{settleAccountUrl,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="remake != null">
        remake = #{remake,jdbcType=VARCHAR},
      </if>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="carrierId != null">
        carrier_id = #{carrierId,jdbcType=INTEGER},
      </if>
      <if test="carrierAccountId != null">
        carrier_account_id = #{carrierAccountId,jdbcType=INTEGER},
      </if>
      <if test="settleMonth != null">
        settle_month = #{settleMonth,jdbcType=VARCHAR},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="financePaymentOrderId != null">
        finance_payment_order_id = #{financePaymentOrderId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="paymentAccountFeature != null">
        payment_account_feature = #{paymentAccountFeature,jdbcType=VARCHAR},
      </if>
      <if test="payeeId != null">
        payee_id = #{payeeId,jdbcType=BIGINT},
      </if>
      <if test="payeeName != null">
        payee_name = #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="paymentSettleFeature != null">
        payment_settle_feature = #{paymentSettleFeature,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsPaymentDocumentEntity">
    update bms_payment_document
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      payment_no = #{paymentNo,jdbcType=VARCHAR},
      payment_type = #{paymentType,jdbcType=VARCHAR},
      payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      payment_status = #{paymentStatus,jdbcType=TINYINT},
      invoice_status = #{invoiceStatus,jdbcType=TINYINT},
      settle_account_url = #{settleAccountUrl,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      remake = #{remake,jdbcType=VARCHAR},
      invoice_id = #{invoiceId,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      carrier_id = #{carrierId,jdbcType=INTEGER},
      carrier_account_id = #{carrierAccountId,jdbcType=INTEGER},
      settle_month = #{settleMonth,jdbcType=VARCHAR},
      store_no = #{storeNo,jdbcType=INTEGER},
      finance_payment_order_id = #{financePaymentOrderId,jdbcType=INTEGER},
      business_type = #{businessType,jdbcType=VARCHAR},
      payment_account_feature = #{paymentAccountFeature,jdbcType=VARCHAR},
      payee_id = #{payeeId,jdbcType=BIGINT},
      payee_name = #{payeeName,jdbcType=VARCHAR},
      payment_settle_feature = #{paymentSettleFeature,jdbcType=VARCHAR},
      total_amount = #{totalAmount,jdbcType=DECIMAL}

        where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByParam" resultMap="BaseResultMap" parameterType="net.summerfarm.module.bms.domain.repository.param.PaymentDocQueryParam">
    select
    <include refid="Base_Column_List" />
    from bms_payment_document
    <where>
      <if test="paymentNo != null">
        and payment_no =  #{paymentNo,jdbcType=VARCHAR}
      </if>
      <if test="storeNo != null">
        and store_no =  #{storeNo,jdbcType=INTEGER}
      </if>
      <if test="payeeId != null">
        and payee_id =  #{payeeId,jdbcType=BIGINT}
      </if>
      <if test="paymentStatus != null">
        and payment_status =  #{paymentStatus,jdbcType=TINYINT}
      </if>
      <if test="invoiceStatus != null">
        and invoice_status =  #{invoiceStatus,jdbcType=TINYINT}
      </if>
      <if test="businessType != null">
        and  business_type =  #{businessType,jdbcType=VARCHAR}
      </if>
      <if test="settleMonth != null">
        and  settle_month =  #{settleMonth,jdbcType=VARCHAR}
      </if>
      <if test="statusList != null and statusList.size() > 0">
        and status in
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
          #{status}
        </foreach>
      </if>
    </where>
    order by id desc
  </select>

  <select id="selectAll4Update" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bms_payment_document where business_type is null
    <if test="idList != null and idList.size() > 0">
      and id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </select>
    <select id="selectInHandById" resultType="net.summerfarm.model.vo.bms.BmsPaymentDocumentVO">
      select
        bpd.id,
        bpd.payment_no paymentNo,
        bpd.payment_amount paymentAmount,
        bpd.invoice_status invoiceStatus,
        bpd.payment_status paymentStatus,
        bpd.create_time  createTime,
        bpd.creator,
        bpd.remake
      from  bms_payment_document bpd
      where bpd.id =#{id} and bpd.payment_status =1 and bpd.status > 0
    </select>

    <update id="initBusinessType">
    update bms_payment_document set business_type = 'DELIVERY_BUSINESS' where business_type is null;
  </update>

  <update id="initPayee">
      update bms_payment_document t1 left join carrier t2 on t1.carrier_id = t2.id
      set t1.payee_name = t2.carrier_name,t1.payee_id = t1.carrier_id where t1.payee_name is null
      <if test="idList != null and idList.size() > 0">
          and t1.id in
          <foreach collection="idList" item="id" open="(" close=")" separator=",">
              #{id}
          </foreach>
      </if>
  </update>
</mapper>