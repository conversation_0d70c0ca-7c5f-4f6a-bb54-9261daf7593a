<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsSettleAccountItemEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountItemEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="settle_accounts_details_id" jdbcType="INTEGER" property="settleAccountsDetailsId" />
    <result column="settle_account_id" jdbcType="INTEGER" property="settleAccountId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, settle_accounts_details_id, settle_account_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_settle_account_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_settle_account_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountItemEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_settle_account_item (id, create_time, update_time, 
      settle_accounts_details_id, settle_account_id
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{settleAccountsDetailsId,jdbcType=INTEGER}, #{settleAccountId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountItemEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_settle_account_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="settleAccountsDetailsId != null">
        settle_accounts_details_id,
      </if>
      <if test="settleAccountId != null">
        settle_account_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settleAccountsDetailsId != null">
        #{settleAccountsDetailsId,jdbcType=INTEGER},
      </if>
      <if test="settleAccountId != null">
        #{settleAccountId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountItemEntity">
    update bms_settle_account_item
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settleAccountsDetailsId != null">
        settle_accounts_details_id = #{settleAccountsDetailsId,jdbcType=INTEGER},
      </if>
      <if test="settleAccountId != null">
        settle_account_id = #{settleAccountId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountItemEntity">
    update bms_settle_account_item
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      settle_accounts_details_id = #{settleAccountsDetailsId,jdbcType=INTEGER},
      settle_account_id = #{settleAccountId,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>