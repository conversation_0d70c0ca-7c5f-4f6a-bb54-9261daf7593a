<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsCostAdjustmentDetailEntityMapper">
    <insert id="insert">
        insert into bms_cost_adjustment_detail (cost_adjustment_id, source_id,old_amount,new_amount,remake)
        values ( #{settleAdjustmentId}, #{id}, #{oldAmount}, #{newAmount}, #{remake})
    </insert>


    <select id="selectCostAdjustDetail" resultType="net.summerfarm.model.vo.bms.BmsAdjustmentDetailVO">
        select old_amount oldAmount,
               new_amount newAmount,
               remake,
               bcd.calculate_cost_name calculateName,
               bcd.id calculationDetailsId
        from bms_cost_adjustment_detail bcad
        left join bms_calculation_details bcd on bcd.id=bcad.source_id
        where cost_adjustment_id =#{costAdjustmentId}
    </select>
</mapper>