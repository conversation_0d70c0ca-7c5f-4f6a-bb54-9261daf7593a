<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryReconciliationAdjustmentEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationAdjustmentEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="applicant_admin_id" jdbcType="INTEGER" property="applicantAdminId" />
    <result column="approved_admin_id" jdbcType="INTEGER" property="approvedAdminId" />
    <result column="approves_time" jdbcType="TIMESTAMP" property="approvesTime" />
    <result column="source_id" jdbcType="INTEGER" property="sourceId" />
    <result column="source_type" jdbcType="TINYINT" property="sourceType" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, applicant_admin_id, approved_admin_id, approves_time, 
    source_id, source_type, status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_delivery_reconciliation_adjustment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_delivery_reconciliation_adjustment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationAdjustmentEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_reconciliation_adjustment (id, create_time, update_time, 
      applicant_admin_id, approved_admin_id, approves_time, 
      source_id, source_type, status
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{applicantAdminId,jdbcType=INTEGER}, #{approvedAdminId,jdbcType=INTEGER}, #{approvesTime,jdbcType=TIMESTAMP}, 
      #{sourceId,jdbcType=INTEGER}, #{sourceType,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationAdjustmentEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_reconciliation_adjustment (applicant_admin_id, source_id,source_type)
    values ( #{creator}, #{id},
             #{type})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationAdjustmentEntity">
    update bms_delivery_reconciliation_adjustment
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applicantAdminId != null">
        applicant_admin_id = #{applicantAdminId,jdbcType=INTEGER},
      </if>
      <if test="approvedAdminId != null">
        approved_admin_id = #{approvedAdminId,jdbcType=INTEGER},
      </if>
      <if test="approvesTime != null">
        approves_time = #{approvesTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationAdjustmentEntity">
    update bms_delivery_reconciliation_adjustment
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      applicant_admin_id = #{applicantAdminId,jdbcType=INTEGER},
      approved_admin_id = #{approvedAdminId,jdbcType=INTEGER},
      approves_time = #{approvesTime,jdbcType=TIMESTAMP},
      source_id = #{sourceId,jdbcType=INTEGER},
      source_type = #{sourceType,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="revokeAdjustment">
      update bms_cost_adjustment set status=1 where id=#{id}
  </update>

  <select id="selectByParam" parameterType="net.summerfarm.module.bms.domain.repository.param.ReconciliationAdjustQueryParam"
  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bms_delivery_reconciliation_adjustment
    <where>
      <if test="sourceId != null">
        and source_id =  #{sourceId}
      </if>
      <if test="status != null">
        and status =  #{status}
      </if>
      <if test="sourceType != null">
        and source_type =  #{sourceType}
      </if>
    </where>
    order by id desc
  </select>

  <select id="selectSettlementInApproval"
          resultType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationAdjustmentEntity">
    select bdra.id
    from bms_settle_account bsa
    left join bms_delivery_reconciliation_adjustment bdra on bsa.id = bdra.source_id
    <where>
      bdra.status= 0
      <if test="id != null">
        AND bdra.source_id = #{id}
      </if>
      <if test="type != null">
        AND bdra.source_type = #{type}
      </if>
    </where>
    limit 1
  </select>
</mapper>