<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryReconciliationPaymentEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationPaymentEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="reconciliation_id" jdbcType="BIGINT" property="reconciliationId" />
    <result column="payment_type" jdbcType="VARCHAR" property="paymentType" />
    <result column="payable_amount" jdbcType="DECIMAL" property="payableAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, reconciliation_id, payment_type, payable_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_delivery_reconciliation_payment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_delivery_reconciliation_payment
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationPaymentEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_reconciliation_payment (id, create_time, update_time, 
      reconciliation_id, payment_type, payable_amount
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{reconciliationId,jdbcType=BIGINT}, #{paymentType,jdbcType=VARCHAR}, #{payableAmount,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationPaymentEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_reconciliation_payment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="reconciliationId != null">
        reconciliation_id,
      </if>
      <if test="paymentType != null">
        payment_type,
      </if>
      <if test="payableAmount != null">
        payable_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reconciliationId != null">
        #{reconciliationId,jdbcType=BIGINT},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=VARCHAR},
      </if>
      <if test="payableAmount != null">
        #{payableAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationPaymentEntity">
    update bms_delivery_reconciliation_payment
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reconciliationId != null">
        reconciliation_id = #{reconciliationId,jdbcType=BIGINT},
      </if>
      <if test="paymentType != null">
        payment_type = #{paymentType,jdbcType=VARCHAR},
      </if>
      <if test="payableAmount != null">
        payable_amount = #{payableAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationPaymentEntity">
    update bms_delivery_reconciliation_payment
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      reconciliation_id = #{reconciliationId,jdbcType=BIGINT},
      payment_type = #{paymentType,jdbcType=VARCHAR},
      payable_amount = #{payableAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByReconciliationId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bms_delivery_reconciliation_payment
    where reconciliation_id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByReconciliationId">
    delete from bms_delivery_reconciliation_payment
    where reconciliation_id = #{reconId,jdbcType=BIGINT}
  </delete>
</mapper>