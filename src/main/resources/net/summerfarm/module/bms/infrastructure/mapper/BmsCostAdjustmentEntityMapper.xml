<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsCostAdjustmentEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsCostAdjustmentEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="applicant_admin_id" jdbcType="INTEGER" property="applicantAdminId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="approved_admin_id" jdbcType="INTEGER" property="approvedAdminId" />
    <result column="approved_time" jdbcType="TIMESTAMP" property="approvedTime" />
    <result column="settle_accounts_detail_id" jdbcType="INTEGER" property="settleAccountsDetailId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, applicant_admin_id, status, approved_admin_id, approved_time, 
    settle_accounts_detail_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_cost_adjustment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_cost_adjustment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsCostAdjustmentEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_cost_adjustment (id, create_time, update_time, 
      applicant_admin_id, status, approved_admin_id, 
      approved_time, settle_accounts_detail_id)
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{applicantAdminId,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{approvedAdminId,jdbcType=INTEGER}, 
      #{approvedTime,jdbcType=TIMESTAMP}, #{settleAccountsDetailId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsCostAdjustmentEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_cost_adjustment (applicant_admin_id, settle_accounts_detail_id)
    values ( #{creator}, #{id})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsCostAdjustmentEntity">
    update bms_cost_adjustment
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applicantAdminId != null">
        applicant_admin_id = #{applicantAdminId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="approvedAdminId != null">
        approved_admin_id = #{approvedAdminId,jdbcType=INTEGER},
      </if>
      <if test="approvedTime != null">
        approved_time = #{approvedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settleAccountsDetailId != null">
        settle_accounts_detail_id = #{settleAccountsDetailId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsCostAdjustmentEntity">
    update bms_cost_adjustment
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      applicant_admin_id = #{applicantAdminId,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      approved_admin_id = #{approvedAdminId,jdbcType=INTEGER},
      approved_time = #{approvedTime,jdbcType=TIMESTAMP},
      settle_accounts_detail_id = #{settleAccountsDetailId,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="revokeAdjustment">
    update bms_cost_adjustment set status=1 where id=#{id}
  </update>

  <select id="selectAllInReview" resultType="java.lang.Integer">
    select ifnull(bsa.id,0)
    from bms_cost_adjustment bca
           left join bms_settle_account_item bsai on bsai.settle_accounts_details_id = bca.settle_accounts_detail_id
           left join bms_settle_account bsa on bsa.id = bsai.settle_account_id
    where bca.status = 0
  </select>

  <select id="selectInReviewByAccountId" resultType="java.lang.Integer">
    select count(1)
    from bms_settle_account bsa
           left join bms_settle_account_item bsai on bsa.id=bsai.settle_account_id
           left join bms_delivery_settle_accounts_detail bdsad on bsai.settle_accounts_details_id = bdsad.id
           left join bms_cost_adjustment bca on bca.settle_accounts_detail_id = bdsad.id
    where bca.status = 0
      and bsa.id = #{accountId}
  </select>

  <select id="queryByParam"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bms_cost_adjustment
    <where>
      <if test="settleFulfillId != null and settleFulfillId.size() > 0">
        and settle_accounts_detail_id in
        <foreach collection="settleFulfillId" item="id" open="(" close=")" separator=",">
          #{id}
        </foreach>
      </if>
      <if test="status != null">
        and  status =  #{status}
      </if>
    </where>
    order by id desc
  </select>
  <select id="selectInReviewByDetailIds" resultType="java.lang.Integer">
    select count(1)
    from  bms_cost_adjustment
    where status = 0
      and settle_accounts_detail_id =#{id}
  </select>
  <select id="selectFinishAdjust" resultType="net.summerfarm.model.vo.bms.BmsAdjustmentVO">
    select
      bca.id ,
      a1.realname applicantAdminName,
      a2.realname approvedAdminName,
      bca.id costAdjustmentId,
      bca.approved_time approvesTime,
      bca.create_time createTime
    from bms_cost_adjustment bca
           left join admin a1 on a1.admin_id = bca.applicant_admin_id
           left join admin a2 on a2.admin_id = bca.approved_admin_id
    where bca.settle_accounts_detail_id =#{id}
      and bca.status =3
    order by bca.id desc
  </select>
    <select id="selectCostAdjustDetail" resultType="net.summerfarm.model.vo.bms.BmsAdjustmentDetailVO">
      select old_amount oldAmount,
             new_amount newAmount,
             remake,
             bcd.calculate_cost_name calculateName,
             bcd.id calculationDetailsId
      from bms_cost_adjustment_detail bcad
             left join bms_calculation_details bcd on bcd.id=bcad.source_id
      where cost_adjustment_id =#{costAdjustmentId}
    </select>
  <select id="selectByDeliverySettleAccountsDetailId"
          resultType="net.summerfarm.module.bms.infrastructure.model.BmsCostAdjustmentEntity">
    select bca.status,
           bca.applicant_admin_id applicantAdminId,
           bca.id
    from bms_cost_adjustment bca
    where bca.settle_accounts_detail_id =#{id}
      and bca.status =0
  </select>
  <select id="selectLast" resultType="net.summerfarm.module.bms.infrastructure.model.BmsCostAdjustmentEntity">
    select bca.status,
           a.realname applicantAdminName,
           bca.id costAdjustmentId,
           bca.id
    from bms_cost_adjustment bca
           left join admin a on a.admin_id = bca.applicant_admin_id
    where bca.settle_accounts_detail_id =#{id}
    order by bca.id desc
    limit 1
  </select>

</mapper>