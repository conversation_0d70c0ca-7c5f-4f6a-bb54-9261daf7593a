<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryReconciliationEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="reconciliation_no" jdbcType="VARCHAR" property="reconciliationNo" />
    <result column="service_area_id" jdbcType="INTEGER" property="serviceAreaId" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="carrier_id" jdbcType="INTEGER" property="carrierId" />
    <result column="payable_amount" jdbcType="DECIMAL" property="payableAmount" />
    <result column="actual_amount" jdbcType="DECIMAL" property="actualAmount" />
    <result column="deduction_amount" jdbcType="DECIMAL" property="deductionAmount" />
    <result column="payment_documents_id" jdbcType="INTEGER" property="paymentDocumentsId" />
    <result column="remake" jdbcType="VARCHAR" property="remake" />
    <result column="reconciliation_proof_url" jdbcType="VARCHAR" property="reconciliationProofUrl" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="advance_warehouse_amount" jdbcType="DECIMAL" property="advanceWarehouseAmount" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="overhead" jdbcType="DECIMAL" property="overhead" />
    <result column="tax" jdbcType="DECIMAL" property="tax" />
    <result column="settle_month" jdbcType="VARCHAR" property="settleMonth" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="reconciliation_dimension_key" jdbcType="VARCHAR" property="reconciliationDimensionKey" />
    <result column="service_area_name" jdbcType="VARCHAR" property="serviceAreaName" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="payment_doc_no" jdbcType="VARCHAR" property="paymentDocNo" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, reconciliation_no, service_area_id, store_no, carrier_id, 
    payable_amount, actual_amount, deduction_amount, payment_documents_id, remake, reconciliation_proof_url, 
    status, creator, advance_warehouse_amount, updater, overhead, tax, settle_month, 
    business_type, reconciliation_dimension_key, service_area_name, create_user_name, 
    update_user_name, payment_doc_no, merchant_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_delivery_reconciliation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_delivery_reconciliation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_reconciliation (id, create_time, update_time, 
      reconciliation_no, service_area_id, store_no, 
      carrier_id, payable_amount, actual_amount, 
      deduction_amount, payment_documents_id, remake, 
      reconciliation_proof_url, status, creator, 
      advance_warehouse_amount, updater, overhead, 
      tax, settle_month, business_type, 
      reconciliation_dimension_key, service_area_name, 
      create_user_name, update_user_name, payment_doc_no, 
      merchant_id)
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{reconciliationNo,jdbcType=VARCHAR}, #{serviceAreaId,jdbcType=INTEGER}, #{storeNo,jdbcType=INTEGER}, 
      #{carrierId,jdbcType=INTEGER}, #{payableAmount,jdbcType=DECIMAL}, #{actualAmount,jdbcType=DECIMAL}, 
      #{deductionAmount,jdbcType=DECIMAL}, #{paymentDocumentsId,jdbcType=INTEGER}, #{remake,jdbcType=VARCHAR}, 
      #{reconciliationProofUrl,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{creator,jdbcType=INTEGER}, 
      #{advanceWarehouseAmount,jdbcType=DECIMAL}, #{updater,jdbcType=INTEGER}, #{overhead,jdbcType=DECIMAL}, 
      #{tax,jdbcType=DECIMAL}, #{settleMonth,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, 
      #{reconciliationDimensionKey,jdbcType=VARCHAR}, #{serviceAreaName,jdbcType=VARCHAR}, 
      #{createUserName,jdbcType=VARCHAR}, #{updateUserName,jdbcType=VARCHAR}, #{paymentDocNo,jdbcType=VARCHAR}, 
      #{merchantId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_reconciliation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="reconciliationNo != null">
        reconciliation_no,
      </if>
      <if test="serviceAreaId != null">
        service_area_id,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="carrierId != null">
        carrier_id,
      </if>
      <if test="payableAmount != null">
        payable_amount,
      </if>
      <if test="actualAmount != null">
        actual_amount,
      </if>
      <if test="deductionAmount != null">
        deduction_amount,
      </if>
      <if test="paymentDocumentsId != null">
        payment_documents_id,
      </if>
      <if test="remake != null">
        remake,
      </if>
      <if test="reconciliationProofUrl != null">
        reconciliation_proof_url,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="advanceWarehouseAmount != null">
        advance_warehouse_amount,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="overhead != null">
        overhead,
      </if>
      <if test="tax != null">
        tax,
      </if>
      <if test="settleMonth != null">
        settle_month,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="reconciliationDimensionKey != null">
        reconciliation_dimension_key,
      </if>
      <if test="serviceAreaName != null">
        service_area_name,
      </if>
      <if test="createUserName != null">
        create_user_name,
      </if>
      <if test="updateUserName != null">
        update_user_name,
      </if>
      <if test="paymentDocNo != null">
        payment_doc_no,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reconciliationNo != null">
        #{reconciliationNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceAreaId != null">
        #{serviceAreaId,jdbcType=INTEGER},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="carrierId != null">
        #{carrierId,jdbcType=INTEGER},
      </if>
      <if test="payableAmount != null">
        #{payableAmount,jdbcType=DECIMAL},
      </if>
      <if test="actualAmount != null">
        #{actualAmount,jdbcType=DECIMAL},
      </if>
      <if test="deductionAmount != null">
        #{deductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentDocumentsId != null">
        #{paymentDocumentsId,jdbcType=INTEGER},
      </if>
      <if test="remake != null">
        #{remake,jdbcType=VARCHAR},
      </if>
      <if test="reconciliationProofUrl != null">
        #{reconciliationProofUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="advanceWarehouseAmount != null">
        #{advanceWarehouseAmount,jdbcType=DECIMAL},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="overhead != null">
        #{overhead,jdbcType=DECIMAL},
      </if>
      <if test="tax != null">
        #{tax,jdbcType=DECIMAL},
      </if>
      <if test="settleMonth != null">
        #{settleMonth,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="reconciliationDimensionKey != null">
        #{reconciliationDimensionKey,jdbcType=VARCHAR},
      </if>
      <if test="serviceAreaName != null">
        #{serviceAreaName,jdbcType=VARCHAR},
      </if>
      <if test="createUserName != null">
        #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="updateUserName != null">
        #{updateUserName,jdbcType=VARCHAR},
      </if>
      <if test="paymentDocNo != null">
        #{paymentDocNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationEntity">
    update bms_delivery_reconciliation
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reconciliationNo != null">
        reconciliation_no = #{reconciliationNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceAreaId != null">
        service_area_id = #{serviceAreaId,jdbcType=INTEGER},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="carrierId != null">
        carrier_id = #{carrierId,jdbcType=INTEGER},
      </if>
      <if test="payableAmount != null">
        payable_amount = #{payableAmount,jdbcType=DECIMAL},
      </if>
      <if test="actualAmount != null">
        actual_amount = #{actualAmount,jdbcType=DECIMAL},
      </if>
      <if test="deductionAmount != null">
        deduction_amount = #{deductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentDocumentsId != null">
        payment_documents_id = #{paymentDocumentsId,jdbcType=INTEGER},
      </if>
      <if test="remake != null">
        remake = #{remake,jdbcType=VARCHAR},
      </if>
      <if test="reconciliationProofUrl != null">
        reconciliation_proof_url = #{reconciliationProofUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="advanceWarehouseAmount != null">
        advance_warehouse_amount = #{advanceWarehouseAmount,jdbcType=DECIMAL},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="overhead != null">
        overhead = #{overhead,jdbcType=DECIMAL},
      </if>
      <if test="tax != null">
        tax = #{tax,jdbcType=DECIMAL},
      </if>
      <if test="settleMonth != null">
        settle_month = #{settleMonth,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="reconciliationDimensionKey != null">
        reconciliation_dimension_key = #{reconciliationDimensionKey,jdbcType=VARCHAR},
      </if>
      <if test="serviceAreaName != null">
        service_area_name = #{serviceAreaName,jdbcType=VARCHAR},
      </if>
      <if test="createUserName != null">
        create_user_name = #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="updateUserName != null">
        update_user_name = #{updateUserName,jdbcType=VARCHAR},
      </if>
      <if test="paymentDocNo != null">
        payment_doc_no = #{paymentDocNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationEntity">
    update bms_delivery_reconciliation
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      reconciliation_no = #{reconciliationNo,jdbcType=VARCHAR},
      service_area_id = #{serviceAreaId,jdbcType=INTEGER},
      store_no = #{storeNo,jdbcType=INTEGER},
      carrier_id = #{carrierId,jdbcType=INTEGER},
      payable_amount = #{payableAmount,jdbcType=DECIMAL},
      actual_amount = #{actualAmount,jdbcType=DECIMAL},
      deduction_amount = #{deductionAmount,jdbcType=DECIMAL},
      payment_documents_id = #{paymentDocumentsId,jdbcType=INTEGER},
      remake = #{remake,jdbcType=VARCHAR},
      reconciliation_proof_url = #{reconciliationProofUrl,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      creator = #{creator,jdbcType=INTEGER},
      advance_warehouse_amount = #{advanceWarehouseAmount,jdbcType=DECIMAL},
      updater = #{updater,jdbcType=INTEGER},
      overhead = #{overhead,jdbcType=DECIMAL},
      tax = #{tax,jdbcType=DECIMAL},
      settle_month = #{settleMonth,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      reconciliation_dimension_key = #{reconciliationDimensionKey,jdbcType=VARCHAR},
      service_area_name = #{serviceAreaName,jdbcType=VARCHAR},
      create_user_name = #{createUserName,jdbcType=VARCHAR},
      update_user_name = #{updateUserName,jdbcType=VARCHAR},
      payment_doc_no = #{paymentDocNo,jdbcType=VARCHAR},
      merchant_id = #{merchantId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByParam" resultMap="BaseResultMap" parameterType="net.summerfarm.module.bms.domain.repository.param.ReconciliationQueryParam">
    select
    <include refid="Base_Column_List" />
    from bms_delivery_reconciliation
    <where>
      <if test="idList != null and idList.size() > 0">
        and id in
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
          #{id}
        </foreach>
      </if>
      <if test="reconciliationNo != null">
        and reconciliation_no =  #{reconciliationNo,jdbcType=VARCHAR}
      </if>
      <if test="serviceAreaId != null">
        and service_area_id =  #{serviceAreaId}
      </if>
      <if test="merchantId != null">
        and merchant_id =  #{merchantId}
      </if>
      <if test="status != null">
        and status =  #{status}
      </if>
      <if test="dimensionKey != null">
        and reconciliation_dimension_key =  #{dimensionKey}
      </if>
      <if test="paymentDocNo != null">
        and payment_doc_no =  #{paymentDocNo}
      </if>
      <if test="settleMonth != null">
        and settle_month =  #{settleMonth}
      </if>
      <if test="businessType != null">
        and business_type =  #{businessType}
      </if>
    </where>
    order by id desc
  </select>


  <select id="selectAll4Update"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bms_delivery_reconciliation where business_type is null
    <if test="idList != null and idList.size() > 0">
      and id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </select>
    <select id="selectFinishAdjust" resultType="net.summerfarm.model.vo.bms.BmsAdjustmentVO">
      select bdra.id    ,
             a1.realname        applicantAdminName,
             a2.realname        approvedAdminName,
             bdra.approves_time approvesTime,
             bdra.create_time   createTime
      from bms_delivery_reconciliation_adjustment bdra
             left join admin a1 on a1.admin_id = bdra.applicant_admin_id
             left join admin a2 on a2.admin_id = bdra.approved_admin_id
      where
        source_id = #{id}
        and bdra.source_type =#{type}
        and bdra.status = #{status}
      group by bdra.id desc
    </select>
  <select id="selectCostAdjustDetail" resultType="net.summerfarm.model.vo.bms.BmsAdjustmentDetailVO">
    select
    bdra.id,
    bdrad.old_amount oldAmount,
    bdrad.new_amount newAmount,
    bdrad.remake,
    bdrad.type
    from bms_delivery_reconciliation_adjustment bdra
    left join bms_delivery_reconciliation_adjustment_detail bdrad on bdra.id=bdrad.reconciliation_adjustment_id
    <where>
      <if test="id != null">
        AND bdra.id = #{id}
      </if>
      <if test="type != null">
        AND bdra.source_type = #{type}
      </if>
    </where>
  </select>
    <select id="selectAllInReview" resultType="java.lang.Integer">
      select source_id
      from bms_delivery_reconciliation_adjustment
      where source_type = #{type}
        and status = 0
    </select>
    <select id="selectRecLastVO"
            resultType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationEntity">
      select
      bdra.id,
      bdra.status,
      bdra.create_time createTime,
      a1.realname        applicantAdminName,
      a2.realname        approvedAdminName
      from bms_delivery_reconciliation bdr
      left join bms_delivery_reconciliation_adjustment bdra on bdr.id = bdra.source_id
      left join admin a1 on a1.admin_id = bdra.applicant_admin_id
      left join admin a2 on a2.admin_id = bdra.approved_admin_id
      where
          bdra.source_id = #{id}
          AND bdra.source_type = 0

      order by bdra.id desc
      limit 1
    </select>
  <select id="selectBySourceId"
          resultType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryReconciliationEntity">
    select
    bdra.id ,
    a.realname applicantAdminName,
    bdra.status
    from bms_delivery_reconciliation_adjustment bdra
    left join admin a on a.admin_id=bdra.applicant_admin_id
    where
       bdra.source_id = #{sourceId}
      AND bdra.source_type = 0
      AND bdra.status = 0

  </select>

  <update id="initCreatName">
    update bms_delivery_reconciliation t1 left join admin t2 on t1.creator = t2.admin_id
    set t1.create_user_name = t2.realname where t1.create_user_name is null
    <if test="idList != null and idList.size() > 0">
      and t1.id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initUpdateName">
    update bms_delivery_reconciliation t1 left join admin t2 on t1.updater = t2.admin_id
    set t1.update_user_name = t2.realname where t1.update_user_name is null
    <if test="idList != null and idList.size() > 0">
      and t1.id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initOperator">
    update bms_delivery_reconciliation
    set updater = creator,
    update_time    = create_time
    where updater is null
    <if test="idList != null and idList.size() > 0">
      and id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initPayment">
    update bms_delivery_reconciliation t1 left join bms_payment_document t2 on t1.payment_documents_id = t2.id
    set t1.payment_doc_no = t2.payment_no where t1.payment_doc_no is null
    <if test="idList != null and idList.size() > 0">
      and id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="updateByUnbind">
    update bms_delivery_reconciliation
    set payment_documents_id =null,
        payment_doc_no =null,
        status=3
    where payment_documents_id = #{paymentDocumentsId}
  </update>

</mapper>