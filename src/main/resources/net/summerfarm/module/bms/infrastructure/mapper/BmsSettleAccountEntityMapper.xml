<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsSettleAccountEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="delivery_start_date" jdbcType="DATE" property="deliveryStartDate" />
    <result column="quotation_area_id" jdbcType="INTEGER" property="quotationAreaId" />
    <result column="carrier_id" jdbcType="INTEGER" property="carrierId" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="delivery_end_date" jdbcType="DATE" property="deliveryEndDate" />
    <result column="reconciliation_id" jdbcType="INTEGER" property="reconciliationId" />
    <result column="discharge_amount" jdbcType="DECIMAL" property="dischargeAmount" />
    <result column="reconciliation_no" jdbcType="VARCHAR" property="reconciliationNo" />
    <result column="bidder_id" jdbcType="INTEGER" property="bidderId" />
    <result column="bidder_name" jdbcType="VARCHAR" property="bidderName" />
    <result column="settle_target_id" jdbcType="BIGINT" property="settleTargetId" />
    <result column="settle_target_name" jdbcType="VARCHAR" property="settleTargetName" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="settle_account_no" jdbcType="VARCHAR" property="settleAccountNo" />
    <result column="quotation_area_name" jdbcType="VARCHAR" property="quotationAreaName" />

  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, status, delivery_start_date, quotation_area_id, carrier_id, 
    store_no, province, city, delivery_end_date, reconciliation_id, discharge_amount, 
    reconciliation_no, bidder_id, bidder_name, settle_target_id, settle_target_name, 
    business_type, settle_account_no,quotation_area_name,quotation_area_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_settle_account
    where id = #{id,jdbcType=BIGINT}
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_settle_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_settle_account (id, create_time, update_time, 
      status, delivery_start_date, quotation_area_id, 
      carrier_id, store_no, province, 
      city, delivery_end_date, reconciliation_id, 
      discharge_amount, reconciliation_no, bidder_id, 
      bidder_name, settle_target_id, settle_target_name, 
      business_type, settle_account_no,quotation_area_name)
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{deliveryStartDate,jdbcType=DATE}, #{quotationAreaId,jdbcType=INTEGER}, 
      #{carrierId,jdbcType=INTEGER}, #{storeNo,jdbcType=INTEGER}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{deliveryEndDate,jdbcType=DATE}, #{reconciliationId,jdbcType=INTEGER}, 
      #{dischargeAmount,jdbcType=DECIMAL}, #{reconciliationNo,jdbcType=VARCHAR}, #{bidderId,jdbcType=INTEGER}, 
      #{bidderName,jdbcType=VARCHAR}, #{settleTargetId,jdbcType=BIGINT}, #{settleTargetName,jdbcType=VARCHAR}, 
      #{businessType,jdbcType=VARCHAR}, #{settleAccountNo,jdbcType=VARCHAR},#{quotationAreaName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_settle_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="deliveryStartDate != null">
        delivery_start_date,
      </if>
      <if test="quotationAreaId != null">
        quotation_area_id,
      </if>
      <if test="carrierId != null">
        carrier_id,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="deliveryEndDate != null">
        delivery_end_date,
      </if>
      <if test="reconciliationId != null">
        reconciliation_id,
      </if>
      <if test="dischargeAmount != null">
        discharge_amount,
      </if>
      <if test="reconciliationNo != null">
        reconciliation_no,
      </if>
      <if test="bidderId != null">
        bidder_id,
      </if>
      <if test="bidderName != null">
        bidder_name,
      </if>
      <if test="settleTargetId != null">
        settle_target_id,
      </if>
      <if test="settleTargetName != null">
        settle_target_name,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="settleAccountNo != null">
        settle_account_no,
      </if>
      <if test="quotationAreaName != null">
        quotation_area_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="deliveryStartDate != null">
        #{deliveryStartDate,jdbcType=DATE},
      </if>
      <if test="quotationAreaId != null">
        #{quotationAreaId,jdbcType=INTEGER},
      </if>
      <if test="carrierId != null">
        #{carrierId,jdbcType=INTEGER},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="deliveryEndDate != null">
        #{deliveryEndDate,jdbcType=DATE},
      </if>
      <if test="reconciliationId != null">
        #{reconciliationId,jdbcType=INTEGER},
      </if>
      <if test="dischargeAmount != null">
        #{dischargeAmount,jdbcType=DECIMAL},
      </if>
      <if test="reconciliationNo != null">
        #{reconciliationNo,jdbcType=VARCHAR},
      </if>
      <if test="bidderId != null">
        #{bidderId,jdbcType=INTEGER},
      </if>
      <if test="bidderName != null">
        #{bidderName,jdbcType=VARCHAR},
      </if>
      <if test="settleTargetId != null">
        #{settleTargetId,jdbcType=BIGINT},
      </if>
      <if test="settleTargetName != null">
        #{settleTargetName,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="settleAccountNo != null">
        #{settleAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="quotationAreaName != null">
        #{quotationAreaName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountEntity">
    update bms_settle_account
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="deliveryStartDate != null">
        delivery_start_date = #{deliveryStartDate,jdbcType=DATE},
      </if>
      <if test="quotationAreaId != null">
        quotation_area_id = #{quotationAreaId,jdbcType=INTEGER},
      </if>
      <if test="carrierId != null">
        carrier_id = #{carrierId,jdbcType=INTEGER},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="deliveryEndDate != null">
        delivery_end_date = #{deliveryEndDate,jdbcType=DATE},
      </if>
      <if test="reconciliationId != null">
        reconciliation_id = #{reconciliationId,jdbcType=INTEGER},
      </if>
      <if test="dischargeAmount != null">
        discharge_amount = #{dischargeAmount,jdbcType=DECIMAL},
      </if>
      <if test="reconciliationNo != null">
        reconciliation_no = #{reconciliationNo,jdbcType=VARCHAR},
      </if>
      <if test="bidderId != null">
        bidder_id = #{bidderId,jdbcType=INTEGER},
      </if>
      <if test="bidderName != null">
        bidder_name = #{bidderName,jdbcType=VARCHAR},
      </if>
      <if test="settleTargetId != null">
        settle_target_id = #{settleTargetId,jdbcType=BIGINT},
      </if>
      <if test="settleTargetName != null">
        settle_target_name = #{settleTargetName,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="settleAccountNo != null">
        settle_account_no = #{settleAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="quotationAreaName != null">
        quotation_area_name = #{quotationAreaName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsSettleAccountEntity">
    update bms_settle_account
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      delivery_start_date = #{deliveryStartDate,jdbcType=DATE},
      quotation_area_id = #{quotationAreaId,jdbcType=INTEGER},
      carrier_id = #{carrierId,jdbcType=INTEGER},
      store_no = #{storeNo,jdbcType=INTEGER},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      delivery_end_date = #{deliveryEndDate,jdbcType=DATE},
      reconciliation_id = #{reconciliationId,jdbcType=INTEGER},
      discharge_amount = #{dischargeAmount,jdbcType=DECIMAL},
      reconciliation_no = #{reconciliationNo,jdbcType=VARCHAR},
      bidder_id = #{bidderId,jdbcType=INTEGER},
      bidder_name = #{bidderName,jdbcType=VARCHAR},
      settle_target_id = #{settleTargetId,jdbcType=BIGINT},
      settle_target_name = #{settleTargetName,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      settle_account_no = #{settleAccountNo,jdbcType=VARCHAR},
        quotation_area_name = #{quotationAreaName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByParam"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bms_settle_account
    <where>
      <if test="settleAccountIdList != null">
        and id in
        <foreach item = "id" collection="settleAccountIdList" separator="," open="(" close=")">
          #{id}
        </foreach>
      </if>
      <if test="filterSettleAccountIdList != null">
        and id not in
        <foreach item = "id" collection="filterSettleAccountIdList" separator="," open="(" close=")">
          #{id}
        </foreach>
      </if>
      <if test="deliveryStartDate != null">
        AND delivery_start_date <![CDATA[>=]]> #{deliveryStartDate}
      </if>
      <if test="deliveryEndDate != null">
        AND delivery_end_date <![CDATA[<=]]> #{deliveryEndDate}
      </if>

      <if test="bidderId != null">
        AND bidder_id =#{bidderId}
      </if>
      <if test="settleTargetId != null">
        AND settle_target_id =#{settleTargetId}
      </if>
      <if test="serviceAreaId != null">
        AND quotation_area_id =#{serviceAreaId}
      </if>
      <if test="province != null">
        AND province =#{province}
      </if>
      <if test="cityList != null and cityList.size() > 0">
        and city in
        <foreach collection="cityList" item="city" open="(" close=")" separator=",">
          #{city}
        </foreach>
      </if>
      <if test="haveReconciliation != null and haveReconciliation == 0">
        AND reconciliation_id is null
      </if>
      <if test="haveReconciliation != null and haveReconciliation == 1">
        AND reconciliation_id is not null
      </if>
      <if test="haveSettleAccountNo != null and haveSettleAccountNo == 0">
        AND settle_account_no is null
      </if>
        <if test="reconciliationNo != null">
        AND reconciliation_no =#{reconciliationNo}
      </if>
      <if test="settleAccountNo != null">
        AND settle_account_no =#{settleAccountNo}
      </if>
      <if test="status != null">
        AND status =#{status}
      </if>
      <if test="businessType != null">
        AND business_type =#{businessType}
      </if>
      <if test="carType != null">
        AND settle_target_id = #{carType}
      </if>
    </where>
    order by delivery_start_date desc , id desc
  </select>

  <update id="initBusinessType">
    update bms_settle_account set business_type = 'DELIVERY_BUSINESS' where business_type is null
    <if test="idList != null and idList.size() > 0">
      and id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initBidder">
    update bms_settle_account
    set bidder_id = carrier_id,settle_target_id = store_no
    where (business_type = 'DELIVERY_BUSINESS' or business_type is null)  and bidder_id is null
    <if test="idList != null and idList.size() > 0">
      and id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initBidderName">
    update bms_settle_account t1 left join carrier t2 on t1.carrier_id = t2.id
    set t1.bidder_name = t2.carrier_name where t1.bidder_name is null
    <if test="idList != null and idList.size() > 0">
      and t1.id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>


  <update id="initTargetName">
    update bms_settle_account t1 left join warehouse_logistics_center t2 on t1.store_no = t2.store_no
    set t1.settle_target_name = t2.store_name where t1.settle_target_name is null
    <if test="idList != null and idList.size() > 0">
      and t1.id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initReconNo">
    update bms_settle_account t1 left join bms_delivery_reconciliation t2 on t1.reconciliation_id = t2.id
    set t1.reconciliation_no = t2.reconciliation_no where t1.reconciliation_id is not null
    <if test="idList != null and idList.size() > 0">
      and t1.id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>

  <update id="initServiceAreaName">
    update bms_settle_account t1 left join bms_delivery_quotation_area t2 on t1.quotation_area_id = t2.id
    set t1.quotation_area_name = t2.area where t1.quotation_area_name is null
    <if test="idList != null and idList.size() > 0">
      and t1.id in
      <foreach collection="idList" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </update>
    <update id="checkSettleAccount">
      update bms_settle_account
      set status = 1
      where
      id in
      <foreach item = "item" collection="ids" separator="," open="(" close=")">
        #{item}
      </foreach>
    </update>
</mapper>