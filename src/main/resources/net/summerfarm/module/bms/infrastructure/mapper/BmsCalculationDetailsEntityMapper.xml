<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsCalculationDetailsEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsCalculationDetailsEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="source_id" jdbcType="INTEGER" property="sourceId" />
    <result column="calculate_cost_id" jdbcType="INTEGER" property="calculateCostId" />
    <result column="source_type" jdbcType="TINYINT" property="sourceType" />
    <result column="calculate_cost_name" jdbcType="VARCHAR" property="calculateCostName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, type, amount, source_id, calculate_cost_id, source_type, 
    calculate_cost_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_calculation_details
    where id = #{id,jdbcType=BIGINT}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_calculation_details
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsCalculationDetailsEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_calculation_details (id, create_time, update_time, 
      type, amount, source_id, 
      calculate_cost_id, source_type, calculate_cost_name
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, #{sourceId,jdbcType=INTEGER}, 
      #{calculateCostId,jdbcType=INTEGER}, #{sourceType,jdbcType=TINYINT}, #{calculateCostName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsCalculationDetailsEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_calculation_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="calculateCostId != null">
        calculate_cost_id,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="calculateCostName != null">
        calculate_cost_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="calculateCostId != null">
        #{calculateCostId,jdbcType=INTEGER},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=TINYINT},
      </if>
      <if test="calculateCostName != null">
        #{calculateCostName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsCalculationDetailsEntity">
    update bms_calculation_details
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="calculateCostId != null">
        calculate_cost_id = #{calculateCostId,jdbcType=INTEGER},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=TINYINT},
      </if>
      <if test="calculateCostName != null">
        calculate_cost_name = #{calculateCostName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsCalculationDetailsEntity">
    update bms_calculation_details
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=INTEGER},
      amount = #{amount,jdbcType=DECIMAL},
      source_id = #{sourceId,jdbcType=INTEGER},
      calculate_cost_id = #{calculateCostId,jdbcType=INTEGER},
      source_type = #{sourceType,jdbcType=TINYINT},
      calculate_cost_name = #{calculateCostName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectBySettleParam"
          resultMap="BaseResultMap">
    select bcd.*
    from bms_settle_account bsa
    left join bms_settle_account_item bsai on  bsa.id=bsai.settle_account_id
    left join bms_delivery_settle_accounts_detail bdsad on bsai.settle_accounts_details_id = bdsad.id
    left join bms_calculation_details bcd on bcd.source_id = bdsad.id
    <where>
      <if test="settleAccountId != null">
        and bsa.id =  #{settleAccountId}
      </if>
      <if test="sourceType != null">
        and bsa.source_type =  #{sourceType}
      </if>
    </where>
    order by bcd.type
  </select>

  <select id="selectBySettleFulfillParam"
          resultMap="BaseResultMap">
    select bcd.*
    from bms_delivery_settle_accounts_detail bdsad
           left join bms_calculation_details bcd on bcd.source_id = bdsad.id
    where bcd.source_type = 0 and bcd.source_id=#{settleFulfillId}
    order by bcd.type
  </select>
    <select id="selectByAccountsDetailId" resultType="net.summerfarm.model.vo.bms.BmsCalculationDetailsVO">
      select
        bcd.id,
        bcd.amount,
        bcd.calculate_cost_name calculateName
      from bms_delivery_settle_accounts_detail bdsad
             left join bms_calculation_details bcd on bcd.source_id = bdsad.id
      where bcd.source_type = 0 and bcd.source_id=#{id}
      order by bcd.type
    </select>

    <update id="initCostName">
    update bms_calculation_details t1 left join bms_delivery_quota_calculate_cost t2 on t1.calculate_cost_id = t2.id
    set t1.calculate_cost_name = t2.calculate_name where t1.calculate_cost_id is not null
  </update>
</mapper>