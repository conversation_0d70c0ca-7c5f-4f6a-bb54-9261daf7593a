<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryReconciliationAdjustmentDetailEntityMapper">
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into bms_delivery_reconciliation_adjustment_detail (reconciliation_adjustment_id, type,old_amount,new_amount,remake)
        values ( #{reconciliationAdjustmentId}, #{adjustType}, #{oldAmount}, #{newAmount}, #{remake})
    </insert>

    <select id="selectByadjustmentId"
            resultType="net.summerfarm.model.vo.bms.BmsAdjustmentDetailVO">
        select
        bdra.id,
        bdrad.old_amount oldAmount,
        bdrad.new_amount newAmount,
        bdrad.remake,
        bdrad.type
        from bms_delivery_reconciliation_adjustment bdra
        left join bms_delivery_reconciliation_adjustment_detail bdrad on bdra.id=bdrad.reconciliation_adjustment_id
        where bdra.id = #{id}
        AND bdra.source_type = 0

    </select>
    <select id="selectCopeAndDeduction"
            resultType="net.summerfarm.model.domain.bms.BmsDeliveryReconciliationAdjustmentDetail">
        select old_amount oldAmount,
               new_amount newAmount,
               `type`
               from bms_delivery_reconciliation_adjustment_detail
        where reconciliation_adjustment_id =#{reconciliationAdjustmentId} and `type` in (0,1,3,4,5)
    </select>
</mapper>