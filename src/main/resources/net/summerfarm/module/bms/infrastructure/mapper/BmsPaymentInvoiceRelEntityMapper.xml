<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsPaymentInvoiceRelEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsPaymentInvoiceRelEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bms_payment_document_id" jdbcType="BIGINT" property="bmsPaymentDocumentId" />
    <result column="purchase_invoice_id" jdbcType="BIGINT" property="purchaseInvoiceId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, bms_payment_document_id, purchase_invoice_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_payment_invoice_rel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_payment_invoice_rel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsPaymentInvoiceRelEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_payment_invoice_rel (id, bms_payment_document_id, purchase_invoice_id
      )
    values (#{id,jdbcType=BIGINT}, #{bmsPaymentDocumentId,jdbcType=BIGINT}, #{purchaseInvoiceId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsPaymentInvoiceRelEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_payment_invoice_rel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bmsPaymentDocumentId != null">
        bms_payment_document_id,
      </if>
      <if test="purchaseInvoiceId != null">
        purchase_invoice_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bmsPaymentDocumentId != null">
        #{bmsPaymentDocumentId,jdbcType=BIGINT},
      </if>
      <if test="purchaseInvoiceId != null">
        #{purchaseInvoiceId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsPaymentInvoiceRelEntity">
    update bms_payment_invoice_rel
    <set>
      <if test="bmsPaymentDocumentId != null">
        bms_payment_document_id = #{bmsPaymentDocumentId,jdbcType=BIGINT},
      </if>
      <if test="purchaseInvoiceId != null">
        purchase_invoice_id = #{purchaseInvoiceId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsPaymentInvoiceRelEntity">
    update bms_payment_invoice_rel
    set bms_payment_document_id = #{bmsPaymentDocumentId,jdbcType=BIGINT},
      purchase_invoice_id = #{purchaseInvoiceId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectByParam"
  resultMap="BaseResultMap" parameterType="net.summerfarm.module.bms.domain.repository.param.PaymentInvoiceQueryParam">
    select
    <include refid="Base_Column_List"/>
    from bms_payment_invoice_rel
    <where>
      <if test="paymentDocId != null">
        bms_payment_document_id = #{paymentDocId,jdbcType=BIGINT}
      </if>
      <if test="invoiceId != null">
        purchase_invoice_id= #{purchaseInvoiceId,jdbcType=BIGINT}
      </if>
      <if test="invoiceIdList != null and invoiceIdList.size() > 0">
        and purchase_invoice_id in
        <foreach collection="invoiceIdList" item="invoiceId" open="(" close=")" separator=",">
          #{invoiceId}
        </foreach>
      </if>
    </where>
  </select>

  <insert id="batchInsert">
    insert into bms_payment_invoice_rel(
    bms_payment_document_id, purchase_invoice_id
    )values
    <foreach collection="items" item="item" index="index" separator=",">
      ( #{item.bmsPaymentDocumentId}, #{item.purchaseInvoiceId})
    </foreach>
  </insert>

  <delete id="unBindPurchaseInvoiceByDocumentId">
    delete from bms_payment_invoice_rel where bms_payment_document_id = #{documentId}
  </delete>
</mapper>