<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsDeliveryQuotationDetailEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationDetailEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bms_calculation_item_id" jdbcType="INTEGER" property="bmsCalculationItemId" />
    <result column="bms_delivery_quotation_id" jdbcType="INTEGER" property="bmsDeliveryQuotationId" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="bms_calculation_item_name" jdbcType="VARCHAR" property="bmsCalculationItemName" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, bms_calculation_item_id, bms_delivery_quotation_id, 
    amount, bms_calculation_item_name, unit
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_delivery_quotation_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_delivery_quotation_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationDetailEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_quotation_detail (id, create_time, update_time, 
      bms_calculation_item_id, bms_delivery_quotation_id, 
      amount, bms_calculation_item_name, unit
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{bmsCalculationItemId,jdbcType=INTEGER}, #{bmsDeliveryQuotationId,jdbcType=INTEGER}, 
      #{amount,jdbcType=DECIMAL}, #{bmsCalculationItemName,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationDetailEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_quotation_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="bmsCalculationItemId != null">
        bms_calculation_item_id,
      </if>
      <if test="bmsDeliveryQuotationId != null">
        bms_delivery_quotation_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="bmsCalculationItemName != null">
        bms_calculation_item_name,
      </if>
      <if test="unit != null">
        unit,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bmsCalculationItemId != null">
        #{bmsCalculationItemId,jdbcType=INTEGER},
      </if>
      <if test="bmsDeliveryQuotationId != null">
        #{bmsDeliveryQuotationId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="bmsCalculationItemName != null">
        #{bmsCalculationItemName,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationDetailEntity">
    update bms_delivery_quotation_detail
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bmsCalculationItemId != null">
        bms_calculation_item_id = #{bmsCalculationItemId,jdbcType=INTEGER},
      </if>
      <if test="bmsDeliveryQuotationId != null">
        bms_delivery_quotation_id = #{bmsDeliveryQuotationId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="bmsCalculationItemName != null">
        bms_calculation_item_name = #{bmsCalculationItemName,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliveryQuotationDetailEntity">
    update bms_delivery_quotation_detail
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      bms_calculation_item_id = #{bmsCalculationItemId,jdbcType=INTEGER},
      bms_delivery_quotation_id = #{bmsDeliveryQuotationId,jdbcType=INTEGER},
      amount = #{amount,jdbcType=DECIMAL},
      bms_calculation_item_name = #{bmsCalculationItemName,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="initItemName">
    update bms_delivery_quotation_detail t1 left join bms_calculation_item bci on t1.bms_calculation_item_id = bci.id
      set t1.bms_calculation_item_name = bci.quote_name ,t1.unit = bci.unit
    where t1.bms_calculation_item_name is null
  </update>

  <select id="selectByQuotationId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bms_delivery_quotation_detail
    where bms_delivery_quotation_id = #{quotationId,jdbcType=INTEGER}
  </select>

  <insert id="batchInsertItem" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_quotation_detail(
    bms_calculation_item_id, bms_delivery_quotation_id, amount,unit,bms_calculation_item_name,create_time,update_time
    )values
    <foreach collection="items" item="item" index="index" separator=",">
      (#{item.bmsCalculationItemId}, #{item.bmsDeliveryQuotationId}, #{item.amount},#{item.unit},#{item.bmsCalculationItemName},#{item.createTime},#{item.updateTime})
    </foreach>
  </insert>

  <delete id="deleteByQuotationId">
    delete from bms_delivery_quotation_detail where bms_delivery_quotation_id = #{quotationId}
  </delete>

</mapper>