<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsCalculationItemEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsCalculationItemEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="quote_name" jdbcType="VARCHAR" property="quoteName" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="source_type" jdbcType="TINYINT" property="sourceType" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />

  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, quote_name, type, unit, source_type,business_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_calculation_item
    where id = #{id,jdbcType=BIGINT}
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_calculation_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsCalculationItemEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_calculation_item (id, create_time, update_time, 
      quote_name, type, unit, 
      source_type,business_type)
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{quoteName,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT}, #{unit,jdbcType=VARCHAR}, 
      #{sourceType,jdbcType=TINYINT},#{businessType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsCalculationItemEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_calculation_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="quoteName != null">
        quote_name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="quoteName != null">
        #{quoteName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=TINYINT},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsCalculationItemEntity">
    update bms_calculation_item
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="quoteName != null">
        quote_name = #{quoteName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=TINYINT},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsCalculationItemEntity">
    update bms_calculation_item
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      quote_name = #{quoteName,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      unit = #{unit,jdbcType=VARCHAR},
      source_type = #{sourceType,jdbcType=TINYINT},
        business_type = #{businessType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByParam"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bms_calculation_item
    <where>
      <if test="quoteName !=null ">
        AND   quote_name=#{quoteName}
      </if>
      <if test="quoteNameFuzzy != null">
        and quote_name  like CONCAT (#{quoteNameFuzzy} ,'%')
      </if>
      <if test="type != null">
        and type = #{type,jdbcType=TINYINT}
      </if>
      <if test="sourceType != null">
        and source_type = #{sourceType,jdbcType=TINYINT}
      </if>
      <if test="businessType != null">
        and business_type = #{businessType}
      </if>
    </where>
    order by id desc
  </select>


  <update id="initBusinessType">
    update bms_calculation_item
    set business_type = 'DELIVERY_BUSINESS'
    where business_type is null
  </update>
</mapper>