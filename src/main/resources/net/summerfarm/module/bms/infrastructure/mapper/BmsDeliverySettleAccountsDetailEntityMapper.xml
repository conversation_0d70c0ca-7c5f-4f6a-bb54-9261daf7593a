<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.bms.infrastructure.mapper.BmsDeliverySettleAccountsDetailEntityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.bms.infrastructure.model.BmsDeliverySettleAccountsDetailEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="have_spanned_area" jdbcType="TINYINT" property="haveSpannedArea" />
    <result column="have_quotation_conflict" jdbcType="TINYINT" property="haveQuotationConflict" />
    <result column="total_position" jdbcType="INTEGER" property="totalPosition" />
    <result column="taxi_position" jdbcType="INTEGER" property="taxiPosition" />
    <result column="no_delivery_time_position" jdbcType="INTEGER" property="noDeliveryTimePosition" />
    <result column="system_kilometers" jdbcType="DECIMAL" property="systemKilometers" />
    <result column="actual_kilometers" jdbcType="DECIMAL" property="actualKilometers" />
    <result column="load_factor" jdbcType="DECIMAL" property="loadFactor" />
    <result column="starting_kilometers" jdbcType="DECIMAL" property="startingKilometers" />
    <result column="sku_quantity" jdbcType="INTEGER" property="skuQuantity" />
    <result column="quotation_id" jdbcType="INTEGER" property="quotationId" />
    <result column="driver" jdbcType="VARCHAR" property="driver" />
    <result column="starting_positions" jdbcType="DECIMAL" property="startingPositions" />
    <result column="car_type" jdbcType="INTEGER" property="carType" />
    <result column="quotation_feature" jdbcType="VARCHAR" property="quotationFeature" />
    <result column="fulfill_feature" jdbcType="VARCHAR" property="fulfillFeature" />
    <result column="fulfill_id" jdbcType="BIGINT" property="fulfillId" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
    <result column="volume" jdbcType="DECIMAL" property="volume" />
    <result column="product_quantity" jdbcType="INTEGER" property="productQuantity" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="shift_type" jdbcType="VARCHAR" property="shiftType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />

  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, path, have_spanned_area, have_quotation_conflict, total_position, 
    taxi_position, no_delivery_time_position, system_kilometers, actual_kilometers, load_factor, 
    starting_kilometers, sku_quantity, quotation_id, driver, starting_positions, car_type, 
    quotation_feature, fulfill_feature,fulfill_id, weight, volume, product_quantity, area, shift_type, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bms_delivery_settle_accounts_detail
    where id = #{id,jdbcType=BIGINT}
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bms_delivery_settle_accounts_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliverySettleAccountsDetailEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_settle_accounts_detail (id, create_time, update_time, 
      path, have_spanned_area, have_quotation_conflict, 
      total_position, taxi_position, no_delivery_time_position, 
      system_kilometers, actual_kilometers, load_factor, 
      starting_kilometers, sku_quantity, quotation_id, 
      driver, starting_positions, car_type, 
      quotation_feature, fulfill_feature,fulfill_id)
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{path,jdbcType=VARCHAR}, #{haveSpannedArea,jdbcType=TINYINT}, #{haveQuotationConflict,jdbcType=TINYINT}, 
      #{totalPosition,jdbcType=INTEGER}, #{taxiPosition,jdbcType=INTEGER}, #{noDeliveryTimePosition,jdbcType=INTEGER}, 
      #{systemKilometers,jdbcType=DECIMAL}, #{actualKilometers,jdbcType=DECIMAL}, #{loadFactor,jdbcType=DECIMAL}, 
      #{startingKilometers,jdbcType=DECIMAL}, #{skuQuantity,jdbcType=INTEGER}, #{quotationId,jdbcType=INTEGER}, 
      #{driver,jdbcType=VARCHAR}, #{startingPositions,jdbcType=DECIMAL}, #{carType,jdbcType=INTEGER}, 
      #{quotationFeature,jdbcType=VARCHAR}, #{fulfillFeature,jdbcType=VARCHAR},#{fulfillId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliverySettleAccountsDetailEntity" keyProperty="id" useGeneratedKeys="true">
    insert into bms_delivery_settle_accounts_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="haveSpannedArea != null">
        have_spanned_area,
      </if>
      <if test="haveQuotationConflict != null">
        have_quotation_conflict,
      </if>
      <if test="totalPosition != null">
        total_position,
      </if>
      <if test="taxiPosition != null">
        taxi_position,
      </if>
      <if test="noDeliveryTimePosition != null">
        no_delivery_time_position,
      </if>
      <if test="systemKilometers != null">
        system_kilometers,
      </if>
      <if test="actualKilometers != null">
        actual_kilometers,
      </if>
      <if test="loadFactor != null">
        load_factor,
      </if>
      <if test="startingKilometers != null">
        starting_kilometers,
      </if>
      <if test="skuQuantity != null">
        sku_quantity,
      </if>
      <if test="quotationId != null">
        quotation_id,
      </if>
      <if test="driver != null">
        driver,
      </if>
      <if test="startingPositions != null">
        starting_positions,
      </if>
      <if test="carType != null">
        car_type,
      </if>
      <if test="quotationFeature != null">
        quotation_feature,
      </if>
      <if test="fulfillFeature != null">
        fulfill_feature,
      </if>
      <if test="fulfillId != null">
        fulfill_id,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="volume != null">
        volume,
      </if>
      <if test="productQuantity != null">
        product_quantity,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="shiftType != null">
        shift_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="haveSpannedArea != null">
        #{haveSpannedArea,jdbcType=TINYINT},
      </if>
      <if test="haveQuotationConflict != null">
        #{haveQuotationConflict,jdbcType=TINYINT},
      </if>
      <if test="totalPosition != null">
        #{totalPosition,jdbcType=INTEGER},
      </if>
      <if test="taxiPosition != null">
        #{taxiPosition,jdbcType=INTEGER},
      </if>
      <if test="noDeliveryTimePosition != null">
        #{noDeliveryTimePosition,jdbcType=INTEGER},
      </if>
      <if test="systemKilometers != null">
        #{systemKilometers,jdbcType=DECIMAL},
      </if>
      <if test="actualKilometers != null">
        #{actualKilometers,jdbcType=DECIMAL},
      </if>
      <if test="loadFactor != null">
        #{loadFactor,jdbcType=DECIMAL},
      </if>
      <if test="startingKilometers != null">
        #{startingKilometers,jdbcType=DECIMAL},
      </if>
      <if test="skuQuantity != null">
        #{skuQuantity,jdbcType=INTEGER},
      </if>
      <if test="quotationId != null">
        #{quotationId,jdbcType=INTEGER},
      </if>
      <if test="driver != null">
        #{driver,jdbcType=VARCHAR},
      </if>
      <if test="startingPositions != null">
        #{startingPositions,jdbcType=DECIMAL},
      </if>
      <if test="carType != null">
        #{carType,jdbcType=INTEGER},
      </if>
      <if test="quotationFeature != null">
        #{quotationFeature,jdbcType=VARCHAR},
      </if>
      <if test="fulfillFeature != null">
        #{fulfillFeature,jdbcType=VARCHAR},
      </if>
      <if test="fulfillId != null">
        #{fulfillId,jdbcType=BIGINT},
      </if>
      <if test="weight != null">
        #{weight},
      </if>
      <if test="volume != null">
        #{volume},
      </if>
      <if test="productQuantity != null">
        #{productQuantity},
      </if>
      <if test="area != null">
        #{area},
      </if>
      <if test="shiftType != null">
        #{shiftType},
      </if>
      <if test="remark != null">
        #{remark}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliverySettleAccountsDetailEntity">
    update bms_delivery_settle_accounts_detail
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="path != null">
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="haveSpannedArea != null">
        have_spanned_area = #{haveSpannedArea,jdbcType=TINYINT},
      </if>
      <if test="haveQuotationConflict != null">
        have_quotation_conflict = #{haveQuotationConflict,jdbcType=TINYINT},
      </if>
      <if test="totalPosition != null">
        total_position = #{totalPosition,jdbcType=INTEGER},
      </if>
      <if test="taxiPosition != null">
        taxi_position = #{taxiPosition,jdbcType=INTEGER},
      </if>
      <if test="noDeliveryTimePosition != null">
        no_delivery_time_position = #{noDeliveryTimePosition,jdbcType=INTEGER},
      </if>
      <if test="systemKilometers != null">
        system_kilometers = #{systemKilometers,jdbcType=DECIMAL},
      </if>
      <if test="actualKilometers != null">
        actual_kilometers = #{actualKilometers,jdbcType=DECIMAL},
      </if>
      <if test="loadFactor != null">
        load_factor = #{loadFactor,jdbcType=DECIMAL},
      </if>
      <if test="startingKilometers != null">
        starting_kilometers = #{startingKilometers,jdbcType=DECIMAL},
      </if>
      <if test="skuQuantity != null">
        sku_quantity = #{skuQuantity,jdbcType=INTEGER},
      </if>
      <if test="quotationId != null">
        quotation_id = #{quotationId,jdbcType=INTEGER},
      </if>
      <if test="driver != null">
        driver = #{driver,jdbcType=VARCHAR},
      </if>
      <if test="startingPositions != null">
        starting_positions = #{startingPositions,jdbcType=DECIMAL},
      </if>
      <if test="carType != null">
        car_type = #{carType,jdbcType=INTEGER},
      </if>
      <if test="quotationFeature != null">
        quotation_feature = #{quotationFeature,jdbcType=VARCHAR},
      </if>
      <if test="fulfillFeature != null">
        fulfill_feature = #{fulfillFeature,jdbcType=VARCHAR},
      </if>
      <if test="fulfillId != null">
        fulfill_id = #{fulfillId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.bms.infrastructure.model.BmsDeliverySettleAccountsDetailEntity">
    update bms_delivery_settle_accounts_detail
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      path = #{path,jdbcType=VARCHAR},
      have_spanned_area = #{haveSpannedArea,jdbcType=TINYINT},
      have_quotation_conflict = #{haveQuotationConflict,jdbcType=TINYINT},
      total_position = #{totalPosition,jdbcType=INTEGER},
      taxi_position = #{taxiPosition,jdbcType=INTEGER},
      no_delivery_time_position = #{noDeliveryTimePosition,jdbcType=INTEGER},
      system_kilometers = #{systemKilometers,jdbcType=DECIMAL},
      actual_kilometers = #{actualKilometers,jdbcType=DECIMAL},
      load_factor = #{loadFactor,jdbcType=DECIMAL},
      starting_kilometers = #{startingKilometers,jdbcType=DECIMAL},
      sku_quantity = #{skuQuantity,jdbcType=INTEGER},
      quotation_id = #{quotationId,jdbcType=INTEGER},
      driver = #{driver,jdbcType=VARCHAR},
      starting_positions = #{startingPositions,jdbcType=DECIMAL},
      car_type = #{carType,jdbcType=INTEGER},
      quotation_feature = #{quotationFeature,jdbcType=VARCHAR},
      fulfill_feature = #{fulfillFeature,jdbcType=VARCHAR},
      fulfill_id = #{fulfillId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryBySettleAccountId"
          resultMap="BaseResultMap">
    select
      bdsad.*
    from bms_settle_account bsa
           left join bms_settle_account_item bsai on bsai.settle_account_id= bsa.id
           left join bms_delivery_settle_accounts_detail bdsad on bsai.settle_accounts_details_id = bdsad.id
where bsa.id = #{settleAccountId}
  </select>
  <select id="countByFulfillId" resultType="java.lang.Integer">
    select count(1) from bms_delivery_settle_accounts_detail where fulfill_id = #{pathId}
  </select>
</mapper>