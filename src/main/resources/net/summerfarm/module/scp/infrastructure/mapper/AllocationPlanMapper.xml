<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.scp.infrastructure.mapper.AllocationPlanMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.scp.infrastructure.model.AllocationPlan">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="list_no" jdbcType="VARCHAR" property="listNo" />
    <result column="plan_list_no" jdbcType="VARCHAR" property="planListNo" />
    <result column="out_warehouse_no" jdbcType="INTEGER" property="outWarehouseNo" />
    <result column="in_warehouse_no" jdbcType="INTEGER" property="inWarehouseNo" />
    <result column="out_warehouse_name" jdbcType="VARCHAR" property="outWarehouseName" />
    <result column="in_warehouse_name" jdbcType="VARCHAR" property="inWarehouseName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="expect_out_time" jdbcType="TIMESTAMP" property="expectOutTime" />
    <result column="expect_in_time" jdbcType="TIMESTAMP" property="expectInTime" />
    <result column="in_store_admin" jdbcType="INTEGER" property="inStoreAdmin" />
    <result column="in_store_admin_name" jdbcType="VARCHAR" property="inStoreAdminName" />
    <result column="out_store_admin" jdbcType="INTEGER" property="outStoreAdmin" />
    <result column="out_store_admin_name" jdbcType="VARCHAR" property="outStoreAdminName" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="updater_name" jdbcType="VARCHAR" property="updaterName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="out_time" jdbcType="TIMESTAMP" property="outTime" />
    <result column="trunk_flag" jdbcType="INTEGER" property="trunkFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, list_no, plan_list_no, out_warehouse_no, in_warehouse_no, out_warehouse_name,
    in_warehouse_name, status, expect_out_time, expect_in_time, in_store_admin, in_store_admin_name, out_store_admin, out_store_admin_name, order_type,
    creator_id, creator_name, updater_id, updater_name, create_time, update_time, out_time,trunk_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from allocation_plan
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectNoDraft"  resultType="net.summerfarm.module.scp.model.vo.AllocationPlanVO">
    select id,creator_name creatorName,in_warehouse_name inWarehouseName,out_warehouse_name outWarehouseName,plan_list_no planListNo,status,create_time createTime
    from allocation_plan
    <where>
    <if test="planListNo!=null">
     and plan_list_no like CONCAT('%',#{planListNo},'%')
    </if>
    <if test="inWarehouseNo!=null">
    and in_warehouse_no=#{inWarehouseNo}
    </if>
      <if test="outWarehouseNo!=null">
        and out_warehouse_no=#{outWarehouseNo}
      </if>
    <if test="status!=null">
      and status in (10,15,20,25,30)
    </if>
    <if test="list !=null  and list.size > 0">
        and id in
      <foreach collection="list" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
    </where>
    order by id desc
  </select>

  <insert id="insertSelective" parameterType="net.summerfarm.module.scp.infrastructure.model.AllocationPlan" keyColumn="id" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into allocation_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="listNo != null">
        list_no,
      </if>
      <if test="planListNo != null">
        plan_list_no,
      </if>
      <if test="outWarehouseNo != null">
        out_warehouse_no,
      </if>
      <if test="inWarehouseNo != null">
        in_warehouse_no,
      </if>
      <if test="outWarehouseName != null">
        out_warehouse_name,
      </if>
      <if test="inWarehouseName != null">
        in_warehouse_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="expectOutTime != null">
        expect_out_time,
      </if>
      <if test="expectInTime != null">
        expect_in_time,
      </if>
      <if test="inStoreAdmin != null">
        in_store_admin,
      </if>
      <if test="inStoreAdminName != null">
        in_store_admin_name,
      </if>
      <if test="outStoreAdmin != null">
        out_store_admin,
      </if>
      <if test="outStoreAdminName != null">
        out_store_admin_name,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updaterName != null">
        updater_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="outTime != null">
        out_time,
      </if>
      <if test="trunkFlag != null">
        trunk_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="listNo != null">
        #{listNo,jdbcType=VARCHAR},
      </if>
      <if test="planListNo != null">
        #{planListNo,jdbcType=VARCHAR},
      </if>
      <if test="outWarehouseNo != null">
        #{outWarehouseNo,jdbcType=INTEGER},
      </if>
      <if test="inWarehouseNo != null">
        #{inWarehouseNo,jdbcType=INTEGER},
      </if>
      <if test="outWarehouseName != null">
        #{outWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="inWarehouseName != null">
        #{inWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="expectOutTime != null">
        #{expectOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectInTime != null">
        #{expectInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inStoreAdmin != null">
        #{inStoreAdmin,jdbcType=INTEGER},
      </if>
      <if test="inStoreAdminName != null">
        #{inStoreAdminName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreAdmin != null">
        #{outStoreAdmin,jdbcType=INTEGER},
      </if>
      <if test="outStoreAdminName != null">
        #{outStoreAdminName,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outTime != null">
        #{outTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trunkFlag != null">
        #{trunkFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.scp.infrastructure.model.AllocationPlan">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update allocation_plan
    <set>
      <if test="listNo != null">
        list_no = #{listNo,jdbcType=VARCHAR},
      </if>
      <if test="planListNo != null">
        plan_list_no = #{planListNo,jdbcType=VARCHAR},
      </if>
      <if test="outWarehouseNo != null">
        out_warehouse_no = #{outWarehouseNo,jdbcType=INTEGER},
      </if>
      <if test="inWarehouseNo != null">
        in_warehouse_no = #{inWarehouseNo,jdbcType=INTEGER},
      </if>
      <if test="outWarehouseName != null">
        out_warehouse_name = #{outWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="inWarehouseName != null">
        in_warehouse_name = #{inWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="expectOutTime != null">
        expect_out_time = #{expectOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectInTime != null">
        expect_in_time = #{expectInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inStoreAdmin != null">
        in_store_admin = #{inStoreAdmin,jdbcType=INTEGER},
      </if>
      <if test="inStoreAdminName != null">
        in_store_admin_name = #{inStoreAdminName,jdbcType=VARCHAR},
      </if>
      <if test="outStoreAdmin != null">
        out_store_admin = #{outStoreAdmin,jdbcType=INTEGER},
      </if>
      <if test="outStoreAdminName != null">
        out_store_admin_name = #{outStoreAdminName,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        updater_name = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outTime != null">
        out_time = #{outTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trunkFlag != null">
       trunk_flag = #{trunkFlag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>