<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.scp.infrastructure.mapper.AllocationPlanDetailTransferMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.scp.infrastructure.model.AllocationPlanDetailTransfer">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="allocation_plan_detail_id" jdbcType="BIGINT" property="allocationPlanDetailId" />
    <result column="pd_id" jdbcType="INTEGER" property="pdId" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="system_adjust" jdbcType="DECIMAL" property="systemAdjust" />
    <result column="sale_quantity" jdbcType="DECIMAL" property="saleQuantity" />
    <result column="road_quantity" jdbcType="INTEGER" property="roadQuantity" />
    <result column="avl_quantity" jdbcType="DECIMAL" property="avlQuantity" />
    <result column="rates" jdbcType="VARCHAR" property="rates" />
    <result column="free_delivery_quantity" jdbcType="INTEGER" property="freeDeliveryQuantity" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="updater_name" jdbcType="VARCHAR" property="updaterName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    id, allocation_plan_detail_id, pd_id, pd_name, sku, system_adjust, sale_quantity, 
    road_quantity, avl_quantity, rates, free_delivery_quantity, creator_id, creator_name, 
    updater_id, updater_name, create_time, update_time, weight, is_delete
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from allocation_plan_detail_transfer
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="queryAllByAllocationPlanDetailId"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from allocation_plan_detail_transfer where allocation_plan_detail_id=#{allocationPlanDetailId} and is_delete=0
  </select>
  <insert id="insert" parameterType="net.summerfarm.module.scp.infrastructure.model.AllocationPlanDetailTransfer">
    insert into allocation_plan_detail_transfer (id, allocation_plan_detail_id, pd_id, 
      pd_name, sku, system_adjust, 
      sale_quantity, road_quantity, avl_quantity, 
      rates, free_delivery_quantity, creator_id, 
      creator_name, updater_id, updater_name, 
      create_time, update_time, weight, 
      is_delete)
    values (#{id,jdbcType=BIGINT}, #{allocationPlanDetailId,jdbcType=BIGINT}, #{pdId,jdbcType=INTEGER}, 
      #{pdName,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{systemAdjust,jdbcType=DECIMAL},
      #{saleQuantity,jdbcType=DECIMAL}, #{roadQuantity,jdbcType=INTEGER}, #{avlQuantity,jdbcType=DECIMAL}, 
      #{rates,jdbcType=VARCHAR}, #{freeDeliveryQuantity,jdbcType=INTEGER}, #{creatorId,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{updaterId,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{weight,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.module.scp.infrastructure.model.AllocationPlanDetailTransfer">
    insert into allocation_plan_detail_transfer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="allocationPlanDetailId != null">
        allocation_plan_detail_id,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="pdName != null">
        pd_name,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="systemAdjust != null">
        system_adjust,
      </if>
      <if test="saleQuantity != null">
        sale_quantity,
      </if>
      <if test="roadQuantity != null">
        road_quantity,
      </if>
      <if test="avlQuantity != null">
        avl_quantity,
      </if>
      <if test="rates != null">
        rates,
      </if>
      <if test="freeDeliveryQuantity != null">
        free_delivery_quantity,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updaterName != null">
        updater_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="allocationPlanDetailId != null">
        #{allocationPlanDetailId,jdbcType=BIGINT},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=INTEGER},
      </if>
      <if test="pdName != null">
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="systemAdjust != null">
        #{systemAdjust,jdbcType=DECIMAL},
      </if>
      <if test="saleQuantity != null">
        #{saleQuantity,jdbcType=DECIMAL},
      </if>
      <if test="roadQuantity != null">
        #{roadQuantity,jdbcType=INTEGER},
      </if>
      <if test="avlQuantity != null">
        #{avlQuantity,jdbcType=DECIMAL},
      </if>
      <if test="rates != null">
        #{rates,jdbcType=VARCHAR},
      </if>
      <if test="freeDeliveryQuantity != null">
        #{freeDeliveryQuantity,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.scp.infrastructure.model.AllocationPlanDetailTransfer">
    update allocation_plan_detail_transfer
    <set>
      <if test="allocationPlanDetailId != null">
        allocation_plan_detail_id = #{allocationPlanDetailId,jdbcType=BIGINT},
      </if>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=INTEGER},
      </if>
      <if test="pdName != null">
        pd_name = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="systemAdjust != null">
        system_adjust = #{systemAdjust,jdbcType=DECIMAL},
      </if>
      <if test="saleQuantity != null">
        sale_quantity = #{saleQuantity,jdbcType=DECIMAL},
      </if>
      <if test="roadQuantity != null">
        road_quantity = #{roadQuantity,jdbcType=INTEGER},
      </if>
      <if test="avlQuantity != null">
        avl_quantity = #{avlQuantity,jdbcType=DECIMAL},
      </if>
      <if test="rates != null">
        rates = #{rates,jdbcType=VARCHAR},
      </if>
      <if test="freeDeliveryQuantity != null">
        free_delivery_quantity = #{freeDeliveryQuantity,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        updater_name = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.scp.infrastructure.model.AllocationPlanDetailTransfer">
    update allocation_plan_detail_transfer
    set allocation_plan_detail_id = #{allocationPlanDetailId,jdbcType=BIGINT},
      pd_id = #{pdId,jdbcType=INTEGER},
      pd_name = #{pdName,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      system_adjust = #{systemAdjust,jdbcType=DECIMAL},
      sale_quantity = #{saleQuantity,jdbcType=DECIMAL},
      road_quantity = #{roadQuantity,jdbcType=INTEGER},
      avl_quantity = #{avlQuantity,jdbcType=DECIMAL},
      rates = #{rates,jdbcType=VARCHAR},
      free_delivery_quantity = #{freeDeliveryQuantity,jdbcType=INTEGER},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      updater_id = #{updaterId,jdbcType=INTEGER},
      updater_name = #{updaterName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      weight = #{weight,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>