<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.scp.infrastructure.mapper.AllocationPlanDetailMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.module.scp.infrastructure.model.AllocationPlanDetail">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="allocation_plan_id" jdbcType="BIGINT" property="allocationPlanId"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="pd_id" jdbcType="BIGINT" property="pdId"/>
        <result column="pd_name" jdbcType="VARCHAR" property="pdName"/>
        <result column="system_adjust" jdbcType="INTEGER" property="systemAdjust"/>
        <result column="out_quantity" jdbcType="INTEGER" property="outQuantity"/>
        <result column="purchaser_adjust" jdbcType="INTEGER" property="purchaserAdjust"/>
        <result column="purchase_remark" jdbcType="VARCHAR" property="purchaseRemark"/>
        <result column="sale_adjust" jdbcType="INTEGER" property="saleAdjust"/>
        <result column="sale_remark" jdbcType="VARCHAR" property="saleRemark"/>
        <result column="operate_adjust" jdbcType="INTEGER" property="operateAdjust"/>
        <result column="operate_remark" jdbcType="VARCHAR" property="operateRemark"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_role" jdbcType="TINYINT" property="createRole"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="allocation_lock_quantity" jdbcType="INTEGER" property="allocationLockQuantity"/>
        <result column="source" jdbcType="TINYINT" property="source"/>
        <result column="replenishment_plan_allocation_task_id" jdbcType="BIGINT" property="replenishmentPlanAllocationTaskId"/>
        <result column="is_direct" jdbcType="INTEGER" property="isDirect"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, allocation_plan_id, sku, pd_id, pd_name, system_adjust, out_quantity, purchaser_adjust,
        purchase_remark, sale_adjust, sale_remark, operate_adjust, operate_remark, create_time,
        update_time, create_role, creator, allocation_lock_quantity, source,replenishment_plan_allocation_task_id,is_direct
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from allocation_plan_detail
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectAllBySkuAndPdName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from allocation_plan_detail
        <where>
            <if test="sku !=null">
                AND sku = #{sku}
            </if>
            <if test="pdName !=null">
                AND pd_name like concat('%',#{pdName},'%')
            </if>
            <if test="source !=null">
                AND source=#{source}
            </if>
        </where>

    </select>
    <select id="selectAllByAllocationPlanIdAndSku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from allocation_plan_detail where allocation_plan_id=#{allocationPlanId} and sku=#{sku}
    </select>
    <select id="selectAllByAllocationPlanId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from allocation_plan_detail where allocation_plan_id=#{allocationPlanId} order by id desc
    </select>

    <select id="selectAllByAllocationPlanAndSkuAndPdNameAndBrand"  resultType="net.summerfarm.module.scp.infrastructure.model.AllocationPlanDetail">
        select
        apd.id,apd.allocation_plan_id allocationPlanId,apd.sku,apd.pd_id pdId,apd.pd_name pdName,apd.system_adjust
        systemAdjust
        ,apd.out_quantity outQuantity,
        purchaser_adjust purchaserAdjust,
        purchase_remark purchaseRemark,
        sale_adjust saleAdjust,
        sale_remark saleRemark,
        operate_adjust operateAdjust,
        operate_remark operateRemark,
        apd.create_time createTime,
        apd.update_time updateTime,
        apd.create_role createRole,
        apd.creator,
        apd.source,
        allocation_lock_quantity allocationLockQuantity,
        create_role createRole,
        is_direct isDirect
        from allocation_plan_detail apd left join products_property_value ppv on ppv.pd_id= apd.pd_id and ppv.products_property_id = 2
        where allocation_plan_id=#{allocationPlanId}
        <if test="sku !=null">
            AND apd.sku = #{sku}
        </if>
        <if test="pdName !=null">
            AND apd.pd_name like concat('%',#{pdName},'%')
        </if>
        <if test="brand !=null and brand !=''">
            AND ppv.products_property_value like concat('%',#{brand},'%')
        </if>
        group by sku
        order by id desc
    </select>
    <select id="selectAllByStatus" resultType="net.summerfarm.module.scp.infrastructure.model.AllocationPlanDetail">
        select apd.id,
               allocation_plan_id       allocationPlanId,
               sku,
               pd_id                    pdId,
               pd_name                  pdName,
               system_adjust            systemAdjust,
               out_quantity             outQuantity,
               purchaser_adjust         purchaserAdjust,
               purchase_remark          purchaseRemark,
               sale_adjust              saleAdjust,
               sale_remark              saleRemark,
               operate_adjust           operateAdjust,
               operate_remark           operateRemark,
               apd.create_time          createTime,
               apd.update_time          updateTime,
               apd.create_role          createRole,
               apd.creator,
               allocation_lock_quantity allocationLockQuantity,
               create_role              createRole
        from allocation_plan_detail apd
                 left join allocation_plan ap on apd.allocation_plan_id = ap.id
        where ap.status = #{status}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from allocation_plan_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="net.summerfarm.module.scp.infrastructure.model.AllocationPlanDetail" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into allocation_plan_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="allocationPlanId != null">
                allocation_plan_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="pdId != null">
                pd_id,
            </if>
            <if test="pdName != null">
                pd_name,
            </if>
            <if test="systemAdjust != null">
                system_adjust,
            </if>
            <if test="outQuantity != null">
                out_quantity,
            </if>
            <if test="purchaserAdjust != null">
                purchaser_adjust,
            </if>
            <if test="purchaseRemark != null">
                purchase_remark,
            </if>
            <if test="saleAdjust != null">
                sale_adjust,
            </if>
            <if test="saleRemark != null">
                sale_remark,
            </if>
            <if test="operateAdjust != null">
                operate_adjust,
            </if>
            <if test="operateRemark != null">
                operate_remark,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createRole != null">
                create_role,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="allocationLockQuantity != null">
                allocation_lock_quantity,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="replenishmentPlanAllocationTaskId != null">
                replenishment_plan_allocation_task_id,
            </if>
            <if test="isDirect != null">
                is_direct,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="allocationPlanId != null">
                #{allocationPlanId,jdbcType=BIGINT},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="pdId != null">
                #{pdId,jdbcType=INTEGER},
            </if>
            <if test="pdName != null">
                #{pdName,jdbcType=VARCHAR},
            </if>
            <if test="systemAdjust != null">
                #{systemAdjust,jdbcType=INTEGER},
            </if>
            <if test="outQuantity != null">
                #{outQuantity,jdbcType=INTEGER},
            </if>
            <if test="purchaserAdjust != null">
                #{purchaserAdjust,jdbcType=INTEGER},
            </if>
            <if test="purchaseRemark != null">
                #{purchaseRemark,jdbcType=VARCHAR},
            </if>
            <if test="saleAdjust != null">
                #{saleAdjust,jdbcType=INTEGER},
            </if>
            <if test="saleRemark != null">
                #{saleRemark,jdbcType=VARCHAR},
            </if>
            <if test="operateAdjust != null">
                #{operateAdjust,jdbcType=INTEGER},
            </if>
            <if test="operateRemark != null">
                #{operateRemark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createRole != null">
                #{createRole,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="allocationLockQuantity != null">
                #{allocationLockQuantity,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                #{source},
            </if>
            <if test="replenishmentPlanAllocationTaskId != null">
                #{replenishmentPlanAllocationTaskId},
            </if>
            <if test="isDirect != null">
                #{isDirect,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch" parameterType="net.summerfarm.model.domain.MajorPrice" useGeneratedKeys="true"
            keyProperty="id">
        insert into allocation_plan_detail (allocation_plan_id, sku,
        pd_id, pd_name, system_adjust,
        out_quantity, purchaser_adjust, purchase_remark,
        sale_adjust, sale_remark, operate_adjust,
        operate_remark, create_time, update_time,
        create_role, creator, allocation_lock_quantity
        )
        <foreach collection="list" item="item" index="index" separator=",">
            values (#{allocationPlanId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR},
            #{pdId,jdbcType=INTEGER}, #{pdName,jdbcType=VARCHAR}, #{systemAdjust,jdbcType=INTEGER},
            #{outQuantity,jdbcType=INTEGER}, #{purchaserAdjust,jdbcType=INTEGER}, #{purchaseRemark,jdbcType=VARCHAR},
            #{saleAdjust,jdbcType=INTEGER}, #{saleRemark,jdbcType=VARCHAR}, #{operateAdjust,jdbcType=INTEGER},
            #{operateRemark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
            #{createRole,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, #{allocationLockQuantity,jdbcType=INTEGER}
            )
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.scp.infrastructure.model.AllocationPlanDetail">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update allocation_plan_detail
        <set>
            <if test="allocationPlanId != null">
                allocation_plan_id = #{allocationPlanId,jdbcType=BIGINT},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="pdId != null">
                pd_id = #{pdId,jdbcType=INTEGER},
            </if>
            <if test="pdName != null">
                pd_name = #{pdName,jdbcType=VARCHAR},
            </if>
            <if test="systemAdjust != null">
                system_adjust = #{systemAdjust,jdbcType=INTEGER},
            </if>
            <if test="outQuantity != null">
                out_quantity = #{outQuantity,jdbcType=INTEGER},
            </if>
            <if test="purchaserAdjust != null">
                purchaser_adjust = #{purchaserAdjust,jdbcType=INTEGER},
            </if>
            <if test="purchaseRemark != null">
                purchase_remark = #{purchaseRemark,jdbcType=VARCHAR},
            </if>
            <if test="saleAdjust != null">
                sale_adjust = #{saleAdjust,jdbcType=INTEGER},
            </if>
            <if test="saleRemark != null">
                sale_remark = #{saleRemark,jdbcType=VARCHAR},
            </if>
            <if test="operateAdjust != null">
                operate_adjust = #{operateAdjust,jdbcType=INTEGER},
            </if>
            <if test="operateRemark != null">
                operate_remark = #{operateRemark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createRole != null">
                create_role = #{createRole,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="allocationLockQuantity != null">
                allocation_lock_quantity = #{allocationLockQuantity,jdbcType=INTEGER},
            </if>
            <if test="isDirect != null">
                is_direct = #{isDirect,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.scp.infrastructure.model.AllocationPlanDetail">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update allocation_plan_detail
        set allocation_plan_id = #{allocationPlanId,jdbcType=BIGINT},
        sku = #{sku,jdbcType=VARCHAR},
        pd_id = #{pdId,jdbcType=INTEGER},
        pd_name = #{pdName,jdbcType=VARCHAR},
        system_adjust = #{systemAdjust,jdbcType=INTEGER},
        out_quantity = #{outQuantity,jdbcType=INTEGER},
        purchaser_adjust = #{purchaserAdjust,jdbcType=INTEGER},
        purchase_remark = #{purchaseRemark,jdbcType=VARCHAR},
        sale_adjust = #{saleAdjust,jdbcType=INTEGER},
        sale_remark = #{saleRemark,jdbcType=VARCHAR},
        operate_adjust = #{operateAdjust,jdbcType=INTEGER},
        operate_remark = #{operateRemark,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_role = #{createRole,jdbcType=TINYINT},
        creator = #{creator,jdbcType=VARCHAR},
        allocation_lock_quantity = #{allocationLockQuantity,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>