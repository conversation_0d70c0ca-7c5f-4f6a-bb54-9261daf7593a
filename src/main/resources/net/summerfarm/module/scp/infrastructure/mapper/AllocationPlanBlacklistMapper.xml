<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.module.scp.infrastructure.mapper.AllocationPlanBlacklistMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.module.scp.infrastructure.model.AllocationPlanBlacklist">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pd_id" jdbcType="BIGINT" property="pdId" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="updater_name" jdbcType="VARCHAR" property="updaterName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pd_id, pd_name, sku, creator_id, creator_name, updater_id, updater_name, create_time, 
    update_time, weight, is_delete
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from allocation_plan_blacklist
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" parameterType="net.summerfarm.module.scp.infrastructure.model.AllocationPlanBlacklist" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into allocation_plan_blacklist
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="pdName != null">
        pd_name,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updaterName != null">
        updater_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=BIGINT},
      </if>
      <if test="pdName != null">
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.module.scp.infrastructure.model.AllocationPlanBlacklist">
    update allocation_plan_blacklist
    <set>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=BIGINT},
      </if>
      <if test="pdName != null">
        pd_name = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        updater_name = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.module.scp.infrastructure.model.AllocationPlanBlacklist">
    update allocation_plan_blacklist
    set pd_id = #{pdId,jdbcType=BIGINT},
      pd_name = #{pdName,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      updater_id = #{updaterId,jdbcType=INTEGER},
      updater_name = #{updaterName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      weight = #{weight,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryAllByPdNameAndSkuAndWarehouseNo" resultType="net.summerfarm.module.scp.model.vo.AllocationPlanBlackVO">
    select  apb.id, apb.pd_id pdId,apb.pd_name pdName,apb.weight,apb.sku,apb.creator_id creatorId,apb.creator_name creatorName,apb.updater_id updaterId,apb.updater_name updaterName,apb.create_time createTime,apb.update_time updateTime
    from allocation_plan_blacklist apb left join allocation_plan_blacklist_warehouse apbw on apb.id=apbw.allocation_plan_blacklist_id
    <where>
      <if test="sku !=null">
        AND sku = #{sku}
      </if>
      <if test="pdName !=null">
        AND pd_name like concat('%',#{pdName},'%')
      </if>
      <if test="list !=null  and list.size > 0">
        and  warehouse_no in
        <foreach collection="list" item="warehouseNo" open="(" close=")" separator=",">
          #{warehouseNo}
        </foreach>
      </if>
     and apb.is_delete=0 and apbw.is_delete=0
    </where>
    group by sku order by id desc
  </select>
</mapper>