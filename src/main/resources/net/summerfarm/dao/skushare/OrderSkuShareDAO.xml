<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.dao.skushare.OrderSkuShareDAO">
  <resultMap id="BaseResultMap" type="net.summerfarm.dao.skushare.dataobject.OrderSkuShareDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="transfer_out_sku" jdbcType="VARCHAR" property="transferOutSku" />
    <result column="transfer_in_sku" jdbcType="VARCHAR" property="transferInSku" />
    <result column="share_total_quantity" jdbcType="INTEGER" property="shareTotalQuantity" />
    <result column="share_rate" jdbcType="VARCHAR" property="shareRate" />
    <result column="transfer_rate" jdbcType="VARCHAR" property="transferRate" />
    <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
    <result column="out_order_type" jdbcType="INTEGER" property="outOrderType" />
    <result column="should_transfer_out_quantity" jdbcType="INTEGER" property="shouldTransferOutQuantity" />
    <result column="actual_transfer_out_quantity" jdbcType="INTEGER" property="actualTransferOutQuantity" />
    <result column="remaining_transfer_out_quantity" jdbcType="INTEGER" property="remainingTransferOutQuantity" />
    <result column="should_transfer_in_quantity" jdbcType="INTEGER" property="shouldTransferInQuantity" />
    <result column="actual_transfer_in_quantity" jdbcType="INTEGER" property="actualTransferInQuantity" />
    <result column="remaining_transfer_in_quantity" jdbcType="INTEGER" property="remainingTransferInQuantity" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, warehouse_no, transfer_out_sku, transfer_in_sku, share_total_quantity, 
    share_rate, transfer_rate, out_order_no, out_order_type, should_transfer_out_quantity, 
    actual_transfer_out_quantity, remaining_transfer_out_quantity, should_transfer_in_quantity, 
    actual_transfer_in_quantity, remaining_transfer_in_quantity, status, creator, operator, 
    remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from inventory_order_sku_share_transfer
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from inventory_order_sku_share_transfer
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.dao.skushare.dataobject.OrderSkuShareDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into inventory_order_sku_share_transfer (create_time, update_time, warehouse_no, 
      transfer_out_sku, transfer_in_sku, share_total_quantity, 
      share_rate, transfer_rate, out_order_no, 
      out_order_type, should_transfer_out_quantity, 
      actual_transfer_out_quantity, remaining_transfer_out_quantity, 
      should_transfer_in_quantity, actual_transfer_in_quantity, 
      remaining_transfer_in_quantity, status, creator, 
      operator, remark)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{warehouseNo,jdbcType=INTEGER}, 
      #{transferOutSku,jdbcType=VARCHAR}, #{transferInSku,jdbcType=VARCHAR}, #{shareTotalQuantity,jdbcType=INTEGER}, 
      #{shareRate,jdbcType=VARCHAR}, #{transferRate,jdbcType=VARCHAR}, #{outOrderNo,jdbcType=VARCHAR}, 
      #{outOrderType,jdbcType=INTEGER}, #{shouldTransferOutQuantity,jdbcType=INTEGER}, 
      #{actualTransferOutQuantity,jdbcType=INTEGER}, #{remainingTransferOutQuantity,jdbcType=INTEGER}, 
      #{shouldTransferInQuantity,jdbcType=INTEGER}, #{actualTransferInQuantity,jdbcType=INTEGER}, 
      #{remainingTransferInQuantity,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.dao.skushare.dataobject.OrderSkuShareDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into inventory_order_sku_share_transfer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="transferOutSku != null">
        transfer_out_sku,
      </if>
      <if test="transferInSku != null">
        transfer_in_sku,
      </if>
      <if test="shareTotalQuantity != null">
        share_total_quantity,
      </if>
      <if test="shareRate != null">
        share_rate,
      </if>
      <if test="transferRate != null">
        transfer_rate,
      </if>
      <if test="outOrderNo != null">
        out_order_no,
      </if>
      <if test="outOrderType != null">
        out_order_type,
      </if>
      <if test="shouldTransferOutQuantity != null">
        should_transfer_out_quantity,
      </if>
      <if test="actualTransferOutQuantity != null">
        actual_transfer_out_quantity,
      </if>
      <if test="remainingTransferOutQuantity != null">
        remaining_transfer_out_quantity,
      </if>
      <if test="shouldTransferInQuantity != null">
        should_transfer_in_quantity,
      </if>
      <if test="actualTransferInQuantity != null">
        actual_transfer_in_quantity,
      </if>
      <if test="remainingTransferInQuantity != null">
        remaining_transfer_in_quantity,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="transferOutSku != null">
        #{transferOutSku,jdbcType=VARCHAR},
      </if>
      <if test="transferInSku != null">
        #{transferInSku,jdbcType=VARCHAR},
      </if>
      <if test="shareTotalQuantity != null">
        #{shareTotalQuantity,jdbcType=INTEGER},
      </if>
      <if test="shareRate != null">
        #{shareRate,jdbcType=VARCHAR},
      </if>
      <if test="transferRate != null">
        #{transferRate,jdbcType=VARCHAR},
      </if>
      <if test="outOrderNo != null">
        #{outOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="outOrderType != null">
        #{outOrderType,jdbcType=INTEGER},
      </if>
      <if test="shouldTransferOutQuantity != null">
        #{shouldTransferOutQuantity,jdbcType=INTEGER},
      </if>
      <if test="actualTransferOutQuantity != null">
        #{actualTransferOutQuantity,jdbcType=INTEGER},
      </if>
      <if test="remainingTransferOutQuantity != null">
        #{remainingTransferOutQuantity,jdbcType=INTEGER},
      </if>
      <if test="shouldTransferInQuantity != null">
        #{shouldTransferInQuantity,jdbcType=INTEGER},
      </if>
      <if test="actualTransferInQuantity != null">
        #{actualTransferInQuantity,jdbcType=INTEGER},
      </if>
      <if test="remainingTransferInQuantity != null">
        #{remainingTransferInQuantity,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.dao.skushare.dataobject.OrderSkuShareDO">
    update inventory_order_sku_share_transfer
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="transferOutSku != null">
        transfer_out_sku = #{transferOutSku,jdbcType=VARCHAR},
      </if>
      <if test="transferInSku != null">
        transfer_in_sku = #{transferInSku,jdbcType=VARCHAR},
      </if>
      <if test="shareTotalQuantity != null">
        share_total_quantity = #{shareTotalQuantity,jdbcType=INTEGER},
      </if>
      <if test="shareRate != null">
        share_rate = #{shareRate,jdbcType=VARCHAR},
      </if>
      <if test="transferRate != null">
        transfer_rate = #{transferRate,jdbcType=VARCHAR},
      </if>
      <if test="outOrderNo != null">
        out_order_no = #{outOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="outOrderType != null">
        out_order_type = #{outOrderType,jdbcType=INTEGER},
      </if>
      <if test="shouldTransferOutQuantity != null">
        should_transfer_out_quantity = #{shouldTransferOutQuantity,jdbcType=INTEGER},
      </if>
      <if test="actualTransferOutQuantity != null">
        actual_transfer_out_quantity = #{actualTransferOutQuantity,jdbcType=INTEGER},
      </if>
      <if test="remainingTransferOutQuantity != null">
        remaining_transfer_out_quantity = #{remainingTransferOutQuantity,jdbcType=INTEGER},
      </if>
      <if test="shouldTransferInQuantity != null">
        should_transfer_in_quantity = #{shouldTransferInQuantity,jdbcType=INTEGER},
      </if>
      <if test="actualTransferInQuantity != null">
        actual_transfer_in_quantity = #{actualTransferInQuantity,jdbcType=INTEGER},
      </if>
      <if test="remainingTransferInQuantity != null">
        remaining_transfer_in_quantity = #{remainingTransferInQuantity,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.dao.skushare.dataobject.OrderSkuShareDO">
    update inventory_order_sku_share_transfer
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      transfer_out_sku = #{transferOutSku,jdbcType=VARCHAR},
      transfer_in_sku = #{transferInSku,jdbcType=VARCHAR},
      share_total_quantity = #{shareTotalQuantity,jdbcType=INTEGER},
      share_rate = #{shareRate,jdbcType=VARCHAR},
      transfer_rate = #{transferRate,jdbcType=VARCHAR},
      out_order_no = #{outOrderNo,jdbcType=VARCHAR},
      out_order_type = #{outOrderType,jdbcType=INTEGER},
      should_transfer_out_quantity = #{shouldTransferOutQuantity,jdbcType=INTEGER},
      actual_transfer_out_quantity = #{actualTransferOutQuantity,jdbcType=INTEGER},
      remaining_transfer_out_quantity = #{remainingTransferOutQuantity,jdbcType=INTEGER},
      should_transfer_in_quantity = #{shouldTransferInQuantity,jdbcType=INTEGER},
      actual_transfer_in_quantity = #{actualTransferInQuantity,jdbcType=INTEGER},
      remaining_transfer_in_quantity = #{remainingTransferInQuantity,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectNeedFilledOrderSkuShare" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from inventory_order_sku_share_transfer
    <where>
      status = 1 AND remaining_transfer_out_quantity > 0
      <if test="warehouseNo != null">
        AND warehouse_no = #{warehouseNo}
      </if>
      <if test="transferOutSku != null">
        AND transfer_out_sku = #{transferOutSku}
      </if>
      <if test="transferInSku != null">
        AND transfer_in_sku = #{transferInSku}
      </if>
    </where>
  </select>

  <update id="updateOrderSkuShareById" parameterType="net.summerfarm.biz.skushare.UpdateOrderSkuShare">
    update inventory_order_sku_share_transfer
    set update_time = now()
    <if test="actualTransferOutQuantity != null">
      , actual_transfer_out_quantity = #{actualTransferOutQuantity}
    </if>
    <if test="remainingTransferOutQuantity != null">
      , remaining_transfer_out_quantity = #{remainingTransferOutQuantity}
    </if>
    <if test="actualTransferInQuantity != null">
      , actual_transfer_in_quantity = #{actualTransferInQuantity}
    </if>
    <if test="remainingTransferInQuantity != null">
      , remaining_transfer_in_quantity = #{remainingTransferInQuantity}
    </if>
    <if test="operator != null">
      ,operator = #{operator}
    </if>
    where id = #{id}
    <if test="oldRemainingTransferOutQuantity != null">
      AND remaining_transfer_out_quantity = #{oldRemainingTransferOutQuantity}
    </if>
    <if test="oldRemainingTransferInQuantity != null">
      AND remaining_transfer_in_quantity = #{oldRemainingTransferInQuantity}
    </if>
  </update>
</mapper>