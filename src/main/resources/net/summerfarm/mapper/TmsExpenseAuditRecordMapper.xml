<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.TmsExpenseAuditRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.TmsExpenseAuditRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="mname" jdbcType="VARCHAR" property="mname" />
    <result column="audit_name" jdbcType="VARCHAR" property="auditName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="delivery_path_id" jdbcType="INTEGER" property="deliveryPathId" />
    <result column="expense_id" jdbcType="INTEGER" property="expenseId" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, mname, audit_name, `status`, driver_id, delivery_path_id, 
    expense_id, submit_time
  </sql>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.TmsExpenseAuditRecord" useGeneratedKeys="true">
    insert into tms_expense_audit_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="mname != null">
        mname,
      </if>
      <if test="auditName != null">
        audit_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="deliveryPathId != null">
        delivery_path_id,
      </if>
      <if test="expenseId != null">
        expense_id,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mname != null">
        #{mname,jdbcType=VARCHAR},
      </if>
      <if test="auditName != null">
        #{auditName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="deliveryPathId != null">
        #{deliveryPathId,jdbcType=INTEGER},
      </if>
      <if test="expenseId != null">
        #{expenseId,jdbcType=INTEGER},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

</mapper>