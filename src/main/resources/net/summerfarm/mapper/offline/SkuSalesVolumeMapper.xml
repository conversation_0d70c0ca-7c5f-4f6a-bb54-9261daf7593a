<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.SkuSalesVolumeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.offline.SkuSalesVolume">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="sales_volume" jdbcType="INTEGER" property="salesVolume" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="date_flag" jdbcType="INTEGER" property="dateFlag" />
  </resultMap>

  <sql id="Base_Column_List">
    id, warehouse_no, sku, sales_volume, create_time, update_time, date_flag
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sku_sales_volume
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByEntity" resultMap="BaseResultMap" parameterType="net.summerfarm.model.domain.offline.SkuSalesVolume">
    select
    <include refid="Base_Column_List" />
    from sku_sales_volume
    <where>
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="warehouseNo != null">
        and warehouse_no = #{warehouseNo}
      </if>
      <if test="dateFlag != null">
        and date_flag = #{dateFlag}
      </if>
    </where>
    limit 1
  </select>
</mapper>