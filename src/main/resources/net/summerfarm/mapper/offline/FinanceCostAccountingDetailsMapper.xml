<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.FinanceCostAccountingDetailsMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceCostAccountingDetails">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="warehouseNo" column="warehouse_no" jdbcType="INTEGER"/>
            <result property="warehouseName" column="warehouse_name" jdbcType="VARCHAR"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="outboundQuantity" column="outbound_quantity" jdbcType="INTEGER"/>
            <result property="amountExcludingTax" column="amount_excluding_tax" jdbcType="DECIMAL"/>
            <result property="costType" column="cost_type" jdbcType="TINYINT"/>
            <result property="confirmationDate" column="confirmation_date" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        warehouse_no,warehouse_name,sku,
        outbound_quantity,amount_excluding_tax,cost_type,
        confirmation_date
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from finance_cost_accounting_details
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <select id="selectAllByTime" resultType="net.summerfarm.model.DTO.FinanceCostAccountDTO">
        SELECT
            cost_type costType,
            IFNULL(SUM(amount_excluding_tax),0.00) amountExcludingTax
        FROM finance_cost_accounting_details
        WHERE confirmation_date <![CDATA[ >= ]]> #{startTime} AND confirmation_date <![CDATA[ <= ]]> #{endTime}
        and cost_type IN (3,4,5,6,7,8)
        GROUP BY cost_type
    </select>
    <select id="queryCostAccountingDetails" resultType="net.summerfarm.model.domain.FinanceCostAccountingDetails">
        SELECT id,warehouse_no warehouseNo,warehouse_name warehouseName,sku,outbound_quantity outboundQuantity,
            amount_excluding_tax amountExcludingTax,confirmation_date confirmationDate
        FROM finance_cost_accounting_details
        WHERE id > #{lastBatchMaxId} AND cost_type = #{type}
        AND confirmation_date <![CDATA[ >= ]]> #{startTime} AND confirmation_date <![CDATA[ <= ]]> #{endTime}
        ORDER BY id LIMIT #{size}
    </select>


</mapper>
