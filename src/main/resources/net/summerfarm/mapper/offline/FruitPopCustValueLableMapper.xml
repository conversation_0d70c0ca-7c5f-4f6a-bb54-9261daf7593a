<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.FruitPopCustValueLableMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FruitPopCustValueLable">
        <!--@mbg.generated-->
        <!--@Table fruit_pop_cust_value_lable-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="cust_id" jdbcType="BIGINT" property="custId"/>
        <result column="cust_value_lable" jdbcType="VARCHAR" property="custValueLable"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_time, update_time, cust_id, cust_value_lable
    </sql>

    <!--auto generated by MybatisCodeHelper on 2025-03-10-->
    <select id="selectOneByCustId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fruit_pop_cust_value_lable
        where cust_id=#{custId,jdbcType=BIGINT}
    </select>
</mapper>