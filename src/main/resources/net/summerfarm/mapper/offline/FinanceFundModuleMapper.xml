<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.FinanceFundModuleMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceFundModule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cash_direct_consumption" jdbcType="DECIMAL" property="cashDirectConsumption" />
    <result column="cash_timing_order" jdbcType="DECIMAL" property="cashTimingOrder" />
    <result column="cash_recharge_record" jdbcType="DECIMAL" property="cashRechargeRecord" />
    <result column="cash_refunded_upon_arrival" jdbcType="DECIMAL" property="cashRefundedUponArrival" />
    <result column="cash_refund_for_non_arrival" jdbcType="DECIMAL" property="cashRefundForNonArrival" />
    <result column="refund_timing_order" jdbcType="DECIMAL" property="refundTimingOrder" />
    <result column="refund_recharge_record" jdbcType="DECIMAL" property="refundRechargeRecord" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="revenue_recognition" jdbcType="DATE" property="revenueRecognition" />
  </resultMap>
  <sql id="Base_Column_List">
    id, cash_direct_consumption, cash_timing_order, cash_recharge_record, cash_refunded_upon_arrival, 
    cash_refund_for_non_arrival, refund_timing_order, refund_recharge_record, create_time, 
    revenue_recognition
  </sql>
  <select id="selectAll" resultType="net.summerfarm.model.vo.FinanceFundModuleVO">
    select ifnull(sum(cash_direct_consumption),0) cashDirectConsumption,ifnull(sum(cash_timing_order),0) cashTimingOrder,ifnull(sum(cash_recharge_record),0) cashRechargeRecord,
           ifnull(sum(cash_refunded_upon_arrival),0) cashRefundedUponArrival,ifnull(sum(cash_refund_for_non_arrival),0) cashRefundForNonArrival,
           ifnull(sum(refund_timing_order),0) refundTimingOrder,ifnull(sum(refund_recharge_record),0) refundRechargeRecord
    from finance_fund_module
    where revenue_recognition <![CDATA[>=]]> #{startTime} and revenue_recognition <![CDATA[<=]]> #{endTime}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_fund_module
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceFundModule" useGeneratedKeys="true">
    insert into finance_fund_module (cash_direct_consumption, cash_timing_order, 
      cash_recharge_record, cash_refunded_upon_arrival, 
      cash_refund_for_non_arrival, refund_timing_order, 
      refund_recharge_record, create_time, revenue_recognition
      )
    values (#{cashDirectConsumption,jdbcType=DECIMAL}, #{cashTimingOrder,jdbcType=DECIMAL}, 
      #{cashRechargeRecord,jdbcType=DECIMAL}, #{cashRefundedUponArrival,jdbcType=DECIMAL}, 
      #{cashRefundForNonArrival,jdbcType=DECIMAL}, #{refundTimingOrder,jdbcType=DECIMAL}, 
      #{refundRechargeRecord,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{revenueRecognition,jdbcType=DATE}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceFundModule" useGeneratedKeys="true">
    insert into finance_fund_module
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cashDirectConsumption != null">
        cash_direct_consumption,
      </if>
      <if test="cashTimingOrder != null">
        cash_timing_order,
      </if>
      <if test="cashRechargeRecord != null">
        cash_recharge_record,
      </if>
      <if test="cashRefundedUponArrival != null">
        cash_refunded_upon_arrival,
      </if>
      <if test="cashRefundForNonArrival != null">
        cash_refund_for_non_arrival,
      </if>
      <if test="refundTimingOrder != null">
        refund_timing_order,
      </if>
      <if test="refundRechargeRecord != null">
        refund_recharge_record,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="revenueRecognition != null">
        revenue_recognition,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cashDirectConsumption != null">
        #{cashDirectConsumption,jdbcType=DECIMAL},
      </if>
      <if test="cashTimingOrder != null">
        #{cashTimingOrder,jdbcType=DECIMAL},
      </if>
      <if test="cashRechargeRecord != null">
        #{cashRechargeRecord,jdbcType=DECIMAL},
      </if>
      <if test="cashRefundedUponArrival != null">
        #{cashRefundedUponArrival,jdbcType=DECIMAL},
      </if>
      <if test="cashRefundForNonArrival != null">
        #{cashRefundForNonArrival,jdbcType=DECIMAL},
      </if>
      <if test="refundTimingOrder != null">
        #{refundTimingOrder,jdbcType=DECIMAL},
      </if>
      <if test="refundRechargeRecord != null">
        #{refundRechargeRecord,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="revenueRecognition != null">
        #{revenueRecognition,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceFundModule">
    update finance_fund_module
    <set>
      <if test="cashDirectConsumption != null">
        cash_direct_consumption = #{cashDirectConsumption,jdbcType=DECIMAL},
      </if>
      <if test="cashTimingOrder != null">
        cash_timing_order = #{cashTimingOrder,jdbcType=DECIMAL},
      </if>
      <if test="cashRechargeRecord != null">
        cash_recharge_record = #{cashRechargeRecord,jdbcType=DECIMAL},
      </if>
      <if test="cashRefundedUponArrival != null">
        cash_refunded_upon_arrival = #{cashRefundedUponArrival,jdbcType=DECIMAL},
      </if>
      <if test="cashRefundForNonArrival != null">
        cash_refund_for_non_arrival = #{cashRefundForNonArrival,jdbcType=DECIMAL},
      </if>
      <if test="refundTimingOrder != null">
        refund_timing_order = #{refundTimingOrder,jdbcType=DECIMAL},
      </if>
      <if test="refundRechargeRecord != null">
        refund_recharge_record = #{refundRechargeRecord,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="revenueRecognition != null">
        revenue_recognition = #{revenueRecognition,jdbcType=DATE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceFundModule">
    update finance_fund_module
    set cash_direct_consumption = #{cashDirectConsumption,jdbcType=DECIMAL},
      cash_timing_order = #{cashTimingOrder,jdbcType=DECIMAL},
      cash_recharge_record = #{cashRechargeRecord,jdbcType=DECIMAL},
      cash_refunded_upon_arrival = #{cashRefundedUponArrival,jdbcType=DECIMAL},
      cash_refund_for_non_arrival = #{cashRefundForNonArrival,jdbcType=DECIMAL},
      refund_timing_order = #{refundTimingOrder,jdbcType=DECIMAL},
      refund_recharge_record = #{refundRechargeRecord,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      revenue_recognition = #{revenueRecognition,jdbcType=DATE}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectList" resultType="net.summerfarm.model.vo.FinanceFundModuleVO">
    select
      ifnull(cash_direct_consumption,0) cashDirectConsumption,ifnull(cash_timing_order,0) cashTimingOrder,ifnull(cash_recharge_record,0) cashRechargeRecord,
      ifnull(cash_refunded_upon_arrival,0) cashRefundedUponArrival,ifnull(cash_refund_for_non_arrival,0) cashRefundForNonArrival,
      ifnull(refund_timing_order,0) refundTimingOrder,ifnull(refund_recharge_record,0) refundRechargeRecord ,revenue_recognition revenueRecognition
    from finance_fund_module
    where revenue_recognition <![CDATA[>=]]> #{startTime} and revenue_recognition <![CDATA[<=]]> #{endTime}
    order by revenue_recognition
  </select>
</mapper>