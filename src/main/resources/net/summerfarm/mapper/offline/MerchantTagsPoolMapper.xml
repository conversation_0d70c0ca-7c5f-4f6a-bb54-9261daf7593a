<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.MerchantTagsPoolMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantTagsPool">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="m_id" jdbcType="BIGINT" property="mId"/>
    <result column="tag_id" jdbcType="BIGINT" property="tagId"/>
    <result column="tag_value" jdbcType="VARCHAR" property="tagValue"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `m_id`, `tag_id`, `tag_value`, `create_time`, `update_time`
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_tags_pool
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from merchant_tags_pool
    where `id` = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.MerchantTagsPool">
    insert into merchant_tags_pool
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="mId != null">
        `m_id`,
      </if>
      <if test="tagId != null">
        `tag_id`,
      </if>
      <if test="tagValue != null">
        `tag_value`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=BIGINT},
      </if>
      <if test="tagValue != null">
        #{tagValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.model.domain.MerchantTagsPool">
    update merchant_tags_pool
    <set>
      <if test="mId != null">
        `m_id` = #{mId,jdbcType=BIGINT},
      </if>
      <if test="tagId != null">
        `tag_id` = #{tagId,jdbcType=BIGINT},
      </if>
      <if test="tagValue != null">
        `tag_value` = #{tagValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="getByMId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_tags_pool
    where `m_id` = #{mId,jdbcType=BIGINT}
  </select>
</mapper>