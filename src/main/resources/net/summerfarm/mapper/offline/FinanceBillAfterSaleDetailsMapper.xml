<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.FinanceBillAfterSaleDetailsMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.offline.FinanceBillAfterSaleDetails">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="afterSaleOrderId" column="after_sale_order_id" jdbcType="VARCHAR"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="orderItemId" column="order_item_id" jdbcType="BIGINT"/>
            <result property="deliveryPathId" column="delivery_path_id" jdbcType="BIGINT"/>
            <result property="serviceArea" column="service_area" jdbcType="VARCHAR"/>
            <result property="province" column="province" jdbcType="VARCHAR"/>
            <result property="city" column="city" jdbcType="VARCHAR"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="pdName" column="pd_name" jdbcType="VARCHAR"/>
            <result property="category1" column="category1" jdbcType="VARCHAR"/>
            <result property="taxRate" column="tax_rate" jdbcType="DECIMAL"/>
            <result property="finishTime" column="finish_time" jdbcType="DATE"/>
            <result property="afterSaleAmt" column="after_sale_amt" jdbcType="DECIMAL"/>
            <result property="afterSaleAmtNotax" column="after_sale_amt_notax" jdbcType="DECIMAL"/>
            <result property="afterSaleInSkuCnt" column="after_sale_in_sku_cnt" jdbcType="BIGINT"/>
            <result property="afterSaleInCost" column="after_sale_in_cost" jdbcType="DECIMAL"/>
            <result property="afterSaleInCostNotax" column="after_sale_in_cost_notax" jdbcType="DECIMAL"/>
            <result property="afterSaleAddSkuCnt" column="after_sale_add_sku_cnt" jdbcType="BIGINT"/>
            <result property="afterSaleAddCost" column="after_sale_add_cost" jdbcType="DECIMAL"/>
            <result property="afterSaleAddCostNotax" column="after_sale_add_cost_notax" jdbcType="DECIMAL"/>
            <result property="custTeam" column="cust_team" jdbcType="VARCHAR"/>
            <result property="handleType" column="handle_type" jdbcType="VARCHAR"/>
            <result property="afterSaleAddLackSkuCnt" column="after_sale_add_lack_sku_cnt" jdbcType="BIGINT"/>
            <result property="afterSaleAddLackRevenueAmt" column="after_sale_add_lack_revenue_amt" jdbcType="DECIMAL"/>
            <result property="afterSaleAddLackRevenueAmtNotax" column="after_sale_add_lack_revenue_amt_notax" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        after_sale_order_id,order_no,order_item_id,
        delivery_path_id,service_area,province,
        city,sku,pd_name,
        category1,tax_rate,finish_time,
        after_sale_amt,after_sale_amt_notax,after_sale_in_sku_cnt,
        after_sale_in_cost,after_sale_in_cost_notax,after_sale_add_sku_cnt,
        after_sale_add_cost,after_sale_add_cost_notax,cust_team,
        handle_type,after_sale_add_lack_sku_cnt,after_sale_add_lack_revenue_amt,
        after_sale_add_lack_revenue_amt_notax
    </sql>
    <select id="selectAfterSaleAmtByMid" resultType="java.math.BigDecimal">
        select ifnull(sum(after_sale_amt),0)
        from finance_bill_after_sale_details sd
        where `m_id` = #{mId}
        <if test="startDatetime!=null">
            and finish_time >=#{startDatetime}
        </if>
        <if test="endDatetime!=null">
            and finish_time <![CDATA[<=]]> #{endDatetime}
        </if>
    </select>
    <select id="selectAfterSaleAmtByItemId" resultType="java.math.BigDecimal">
        select ifnull(sum(after_sale_amt),0)
        from finance_bill_after_sale_details sd
        where `order_item_id` = #{orderItemId}
        <if test="dateStart!=null">
            and finish_time >=#{dateStart}
        </if>
        <if test="dateEnd!=null">
            and finish_time <![CDATA[<=]]> #{dateEnd}
        </if>
    </select>
    <select id="selectAfterSaleAmtByOrderNo" resultType="java.math.BigDecimal">
        select ifnull(sum(after_sale_amt),0)
        from finance_bill_after_sale_details sd
        where `order_no` = #{orderNo}
        <if test="dateStart!=null">
            and finish_time >=#{dateStart}
        </if>
        <if test="dateEnd!=null">
            and finish_time <![CDATA[<=]]> #{dateEnd}
        </if>
    </select>
    <select id="selectAfterSaleByMids" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from finance_bill_after_sale_details sd
        where m_id in
        <foreach collection="mIds" open="(" close=")" separator="," item="mId">
            #{mId}
        </foreach>
        <if test="startDatetime!=null">
            and finish_time >=#{startDatetime}
        </if>
        <if test="endDatetime!=null">
            and finish_time <![CDATA[<=]]> #{endDatetime}
        </if>
    </select>
</mapper>
