<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.FinanceRevenueDocumentMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceRevenueDocument">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="manual_code" jdbcType="INTEGER" property="manualCode" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="price_amount" jdbcType="DECIMAL" property="priceAmount" />
    <result column="tax_rate_value" jdbcType="DECIMAL" property="taxRateValue" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="revenue_recognition" jdbcType="DATE" property="revenueRecognition" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="bill_number" jdbcType="VARCHAR" property="billNumber" />
    <result column="out_times_fee" jdbcType="DECIMAL" property="outTimesFee" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, manual_code, sku, quantity, warehouse_no, warehouse_name, price_amount, tax_rate_value, 
    province, city, `type`, revenue_recognition, create_time,bill_number,delivery_fee,out_times_fee,unit,pd_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_revenue_document
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_revenue_document
    where manual_code = #{manualCode} and `type` = #{type} and revenue_recognition = #{startTime}
    order by revenue_recognition asc
  </select>

  <select id="selectByTypeDetail" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_revenue_document
    where manual_code = #{manualCode} and `type` = #{type} and revenue_recognition = #{startTime}
    order by revenue_recognition asc
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_revenue_document
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceRevenueDocument" useGeneratedKeys="true">
    insert into finance_revenue_document (manual_code, sku, quantity, 
      warehouse_no, warehouse_name, price_amount, 
      tax_rate_value, province, city, 
      `type`, revenue_recognition, create_time
      )
    values (#{manualCode,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{quantity,jdbcType=INTEGER}, 
      #{warehouseNo,jdbcType=INTEGER}, #{warehouseName,jdbcType=VARCHAR}, #{priceAmount,jdbcType=DECIMAL}, 
      #{taxRateValue,jdbcType=DECIMAL}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{revenueRecognition,jdbcType=DATE}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceRevenueDocument" useGeneratedKeys="true">
    insert into finance_revenue_document
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="manualCode != null">
        manual_code,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="priceAmount != null">
        price_amount,
      </if>
      <if test="taxRateValue != null">
        tax_rate_value,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="revenueRecognition != null">
        revenue_recognition,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="manualCode != null">
        #{manualCode,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="priceAmount != null">
        #{priceAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRateValue != null">
        #{taxRateValue,jdbcType=DECIMAL},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="revenueRecognition != null">
        #{revenueRecognition,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceRevenueDocument">
    update finance_revenue_document
    <set>
      <if test="manualCode != null">
        manual_code = #{manualCode,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="priceAmount != null">
        price_amount = #{priceAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRateValue != null">
        tax_rate_value = #{taxRateValue,jdbcType=DECIMAL},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="revenueRecognition != null">
        revenue_recognition = #{revenueRecognition,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByAll" parameterType="net.summerfarm.model.input.FinanceRevenueDocumentInput" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_revenue_document
    <where>
      <if test="manualCode != null">
        and manual_code = #{manualCode,jdbcType=INTEGER}
      </if>
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="quantity != null">
        and quantity = #{quantity,jdbcType=INTEGER}
      </if>
      <if test="warehouseNo != null">
        and warehouse_no = #{warehouseNo,jdbcType=INTEGER}
      </if>
      <if test="warehouseName != null">
        and warehouse_name = #{warehouseName,jdbcType=VARCHAR}
      </if>
      <if test="priceAmount != null">
        and price_amount = #{priceAmount,jdbcType=DECIMAL}
      </if>
      <if test="taxRateValue != null">
        and tax_rate_value = #{taxRateValue,jdbcType=DECIMAL}
      </if>
      <if test="province != null">
        and province = #{province,jdbcType=VARCHAR}
      </if>
      <if test="city != null">
        and city = #{city,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
      <if test="revenueRecognition != null">
        and revenue_recognition = #{revenueRecognition,jdbcType=DATE}
      </if>
      <if test="startTime != null and endTime != null">
        and revenue_recognition <![CDATA[>=]]> #{startTime}
        and revenue_recognition <![CDATA[<=]]> #{endTime}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="billNumber != null">
        and bill_number = #{billNumber}
      </if>
    </where>
    <if test=" pageSize != null and pageNum != null">
      limit #{pageNum},#{pageSize}
    </if>
  </select>

  <select id="selectByAllCount" parameterType="net.summerfarm.model.input.FinanceRevenueDocumentInput" resultType="integer">
    select count(*)
    from finance_revenue_document
    <where>
      <if test="manualCode != null">
        and manual_code = #{manualCode,jdbcType=INTEGER}
      </if>
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="quantity != null">
        and quantity = #{quantity,jdbcType=INTEGER}
      </if>
      <if test="warehouseNo != null">
        and warehouse_no = #{warehouseNo,jdbcType=INTEGER}
      </if>
      <if test="warehouseName != null">
        and warehouse_name = #{warehouseName,jdbcType=VARCHAR}
      </if>
      <if test="priceAmount != null">
        and price_amount = #{priceAmount,jdbcType=DECIMAL}
      </if>
      <if test="taxRateValue != null">
        and tax_rate_value = #{taxRateValue,jdbcType=DECIMAL}
      </if>
      <if test="province != null">
        and province = #{province,jdbcType=VARCHAR}
      </if>
      <if test="city != null">
        and city = #{city,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
      <if test="revenueRecognition != null">
        and revenue_recognition = #{revenueRecognition,jdbcType=DATE}
      </if>
      <if test="startTime != null and endTime != null">
        and revenue_recognition <![CDATA[>=]]> #{startTime}
        and revenue_recognition <![CDATA[<=]]> #{endTime}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="billNumber != null">
        and bill_number = #{billNumber}
      </if>
    </where>
  </select>

  <select id="selectByFee" parameterType="net.summerfarm.model.input.FinanceRevenueDocumentInput" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_revenue_document
    <where>
      delivery_fee <![CDATA[>]]> 0
      <if test="manualCode != null">
        and manual_code = #{manualCode,jdbcType=INTEGER}
      </if>
      <if test="quantity != null">
        and quantity = #{quantity,jdbcType=INTEGER}
      </if>
      <if test="warehouseNo != null">
        and warehouse_no = #{warehouseNo,jdbcType=INTEGER}
      </if>
      <if test="warehouseName != null">
        and warehouse_name = #{warehouseName,jdbcType=VARCHAR}
      </if>
      <if test="priceAmount != null">
        and price_amount = #{priceAmount,jdbcType=DECIMAL}
      </if>
      <if test="taxRateValue != null">
        and tax_rate_value = #{taxRateValue,jdbcType=DECIMAL}
      </if>
      <if test="province != null">
        and province = #{province,jdbcType=VARCHAR}
      </if>
      <if test="city != null">
        and city = #{city,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
      <if test="revenueRecognition != null">
        and revenue_recognition = #{revenueRecognition,jdbcType=DATE}
      </if>
      <if test="startTime != null and endTime != null">
        and revenue_recognition <![CDATA[>=]]> #{startTime}
        and revenue_recognition <![CDATA[<=]]> #{endTime}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="billNumber != null">
        and bill_number = #{billNumber}
      </if>
    </where>
  </select>

  <select id="selectByOutTimesFee" parameterType="net.summerfarm.model.input.FinanceRevenueDocumentInput" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_revenue_document
    <where>
      out_times_fee <![CDATA[>]]> 0
      <if test="manualCode != null">
        and manual_code = #{manualCode,jdbcType=INTEGER}
      </if>
      <if test="quantity != null">
        and quantity = #{quantity,jdbcType=INTEGER}
      </if>
      <if test="warehouseNo != null">
        and warehouse_no = #{warehouseNo,jdbcType=INTEGER}
      </if>
      <if test="warehouseName != null">
        and warehouse_name = #{warehouseName,jdbcType=VARCHAR}
      </if>
      <if test="priceAmount != null">
        and price_amount = #{priceAmount,jdbcType=DECIMAL}
      </if>
      <if test="taxRateValue != null">
        and tax_rate_value = #{taxRateValue,jdbcType=DECIMAL}
      </if>
      <if test="province != null">
        and province = #{province,jdbcType=VARCHAR}
      </if>
      <if test="city != null">
        and city = #{city,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
      <if test="revenueRecognition != null">
        and revenue_recognition = #{revenueRecognition,jdbcType=DATE}
      </if>
      <if test="startTime != null and endTime != null">
        and revenue_recognition <![CDATA[>=]]> #{startTime}
        and revenue_recognition <![CDATA[<=]]> #{endTime}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="billNumber != null">
        and bill_number = #{billNumber}
      </if>
    </where>
  </select>

  <select id="selectByBill" parameterType="net.summerfarm.model.input.FinanceRevenueDocumentInput" resultMap="BaseResultMap">
    select distinct bill_number,revenue_recognition
    from finance_revenue_document
    <where>
      <if test="manualCode != null">
        and manual_code = #{manualCode,jdbcType=INTEGER}
      </if>
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="quantity != null">
        and quantity = #{quantity,jdbcType=INTEGER}
      </if>
      <if test="warehouseNo != null">
        and warehouse_no = #{warehouseNo,jdbcType=INTEGER}
      </if>
      <if test="warehouseName != null">
        and warehouse_name = #{warehouseName,jdbcType=VARCHAR}
      </if>
      <if test="priceAmount != null">
        and price_amount = #{priceAmount,jdbcType=DECIMAL}
      </if>
      <if test="taxRateValue != null">
        and tax_rate_value = #{taxRateValue,jdbcType=DECIMAL}
      </if>
      <if test="province != null">
        and province = #{province,jdbcType=VARCHAR}
      </if>
      <if test="city != null">
        and city = #{city,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
      <if test="revenueRecognition != null">
        and revenue_recognition = #{revenueRecognition,jdbcType=DATE}
      </if>
      <if test="startTime != null and endTime != null">
        and revenue_recognition <![CDATA[>=]]> #{startTime}
        and revenue_recognition <![CDATA[<=]]> #{endTime}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="billNumber != null">
        and bill_number = #{billNumber}
      </if>
    </where>
  </select>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceRevenueDocument">
    update finance_revenue_document
    set manual_code = #{manualCode,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=INTEGER},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      price_amount = #{priceAmount,jdbcType=DECIMAL},
      tax_rate_value = #{taxRateValue,jdbcType=DECIMAL},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      revenue_recognition = #{revenueRecognition,jdbcType=DATE},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>