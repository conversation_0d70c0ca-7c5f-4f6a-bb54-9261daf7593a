<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.CrmMerchantDayLabelMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CrmMerchantDayLabel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="merchant_label"  property="merchantLabel" />
    <result column="day_tag" jdbcType="INTEGER" property="dayTag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, m_id, merchant_label, day_tag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" resultType="string">
    select merchant_label
    from crm_merchant_day_label
    where m_id = #{mId} AND day_tag = #{dayTag}
  </select>
  <select id="selectMidListByInput" resultType="net.summerfarm.model.vo.MerchantVO">
    select DISTINCT m_id mId
    from crm_merchant_day_label
    where day_tag = #{dayTag}
    <if test="selectKeys.size != null">
      and size = #{selectKeys.size}
    </if>
    <if test="selectKeys.mId != null">
      and m_id = #{selectKeys.mId}
    </if>
    <if test="selectKeys.areaNo != null">
      and area_no = #{selectKeys.areaNo}
    </if>
    and merchant_label in
    <foreach collection="selectKeys.merchantLabelList" open="(" close=") " separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectMidListByLabel" resultType="long">
    select m_id
    from crm_merchant_day_label
    where day_tag = #{dataTag}
    and merchant_label = #{merchantLabel}
    and m_id IN
    <foreach collection="ids" open="(" close=") " separator="," item="item">
      #{item}
    </foreach>
  </select>
</mapper>