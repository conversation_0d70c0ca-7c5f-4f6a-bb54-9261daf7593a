<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.FinanceCashEttlementDocumentDetailsMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceCashEttlementDocumentDetails">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="whereabouts_of_funds" jdbcType="INTEGER" property="whereaboutsOfFunds" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="pay_type" jdbcType="VARCHAR" property="payType" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="mname" jdbcType="VARCHAR" property="mname" />
    <result column="realname" jdbcType="VARCHAR" property="realname" />
    <result column="name_remakes" jdbcType="VARCHAR" property="nameRemakes" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="bank_pay_end_time" jdbcType="TIMESTAMP" property="bankPayEndTime" />
    <result column="delivery_time" jdbcType="DATE" property="deliveryTime" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="out_times_fee" jdbcType="DECIMAL" property="outTimesFee" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="order_sale_type" jdbcType="INTEGER" property="orderSaleType" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="tax_rate_value" jdbcType="DECIMAL" property="taxRateValue" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="price_amount" jdbcType="DECIMAL" property="priceAmount" />
    <result column="original_price_amount" jdbcType="DECIMAL" property="originalPriceAmount" />
    <result column="detailed_type" jdbcType="INTEGER" property="detailedType" />
    <result column="create_time" jdbcType="DATE" property="createTime" />
    <result column="revenue_recognition" jdbcType="DATE" property="revenueRecognition" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="cost" jdbcType="DECIMAL" property="cost" />
  </resultMap>
  <sql id="Base_Column_List">
    id, whereabouts_of_funds, order_no, province, city, pay_type, m_id, admin_id, mname, 
    realname, name_remakes, end_time, bank_pay_end_time, delivery_time, finish_time, 
    out_times_fee, order_type, order_sale_type, delivery_fee, confirm_time, sku, `type`, 
    amount, tax_rate_value, tax_amount, price_amount, original_price_amount, detailed_type, 
    create_time, revenue_recognition,order_time,unit,pd_name,cost
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_cash_ettlement_document_details
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_cash_ettlement_document_details
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceCashEttlementDocumentDetails" useGeneratedKeys="true">
    insert into finance_cash_ettlement_document_details (whereabouts_of_funds, order_no, province, 
      city, pay_type, m_id, 
      admin_id, mname, realname, 
      name_remakes, end_time, bank_pay_end_time, 
      delivery_time, finish_time, out_times_fee, 
      order_type, order_sale_type, delivery_fee, 
      confirm_time, sku, `type`, 
      amount, tax_rate_value, tax_amount, 
      price_amount, original_price_amount, detailed_type, 
      create_time, revenue_recognition)
    values (#{whereaboutsOfFunds,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{payType,jdbcType=VARCHAR}, #{mId,jdbcType=BIGINT}, 
      #{adminId,jdbcType=INTEGER}, #{mname,jdbcType=VARCHAR}, #{realname,jdbcType=VARCHAR}, 
      #{nameRemakes,jdbcType=VARCHAR}, #{endTime,jdbcType=TIMESTAMP}, #{bankPayEndTime,jdbcType=TIMESTAMP}, 
      #{deliveryTime,jdbcType=DATE}, #{finishTime,jdbcType=TIMESTAMP}, #{outTimesFee,jdbcType=DECIMAL}, 
      #{orderType,jdbcType=INTEGER}, #{orderSaleType,jdbcType=INTEGER}, #{deliveryFee,jdbcType=DECIMAL}, 
      #{confirmTime,jdbcType=TIMESTAMP}, #{sku,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{amount,jdbcType=INTEGER}, #{taxRateValue,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, 
      #{priceAmount,jdbcType=DECIMAL}, #{originalPriceAmount,jdbcType=DECIMAL}, #{detailedType,jdbcType=INTEGER}, 
      #{createTime,jdbcType=DATE}, #{revenueRecognition,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceCashEttlementDocumentDetails" useGeneratedKeys="true">
    insert into finance_cash_ettlement_document_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="whereaboutsOfFunds != null">
        whereabouts_of_funds,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="mname != null">
        mname,
      </if>
      <if test="realname != null">
        realname,
      </if>
      <if test="nameRemakes != null">
        name_remakes,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="bankPayEndTime != null">
        bank_pay_end_time,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="finishTime != null">
        finish_time,
      </if>
      <if test="outTimesFee != null">
        out_times_fee,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderSaleType != null">
        order_sale_type,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="confirmTime != null">
        confirm_time,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="taxRateValue != null">
        tax_rate_value,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="priceAmount != null">
        price_amount,
      </if>
      <if test="originalPriceAmount != null">
        original_price_amount,
      </if>
      <if test="detailedType != null">
        detailed_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="revenueRecognition != null">
        revenue_recognition,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="whereaboutsOfFunds != null">
        #{whereaboutsOfFunds,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="mname != null">
        #{mname,jdbcType=VARCHAR},
      </if>
      <if test="realname != null">
        #{realname,jdbcType=VARCHAR},
      </if>
      <if test="nameRemakes != null">
        #{nameRemakes,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bankPayEndTime != null">
        #{bankPayEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=DATE},
      </if>
      <if test="finishTime != null">
        #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outTimesFee != null">
        #{outTimesFee,jdbcType=DECIMAL},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderSaleType != null">
        #{orderSaleType,jdbcType=INTEGER},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="taxRateValue != null">
        #{taxRateValue,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceAmount != null">
        #{priceAmount,jdbcType=DECIMAL},
      </if>
      <if test="originalPriceAmount != null">
        #{originalPriceAmount,jdbcType=DECIMAL},
      </if>
      <if test="detailedType != null">
        #{detailedType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=DATE},
      </if>
      <if test="revenueRecognition != null">
        #{revenueRecognition,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceCashEttlementDocumentDetails">
    update finance_cash_ettlement_document_details
    <set>
      <if test="whereaboutsOfFunds != null">
        whereabouts_of_funds = #{whereaboutsOfFunds,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="mname != null">
        mname = #{mname,jdbcType=VARCHAR},
      </if>
      <if test="realname != null">
        realname = #{realname,jdbcType=VARCHAR},
      </if>
      <if test="nameRemakes != null">
        name_remakes = #{nameRemakes,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bankPayEndTime != null">
        bank_pay_end_time = #{bankPayEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=DATE},
      </if>
      <if test="finishTime != null">
        finish_time = #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outTimesFee != null">
        out_times_fee = #{outTimesFee,jdbcType=DECIMAL},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderSaleType != null">
        order_sale_type = #{orderSaleType,jdbcType=INTEGER},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="confirmTime != null">
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="taxRateValue != null">
        tax_rate_value = #{taxRateValue,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceAmount != null">
        price_amount = #{priceAmount,jdbcType=DECIMAL},
      </if>
      <if test="originalPriceAmount != null">
        original_price_amount = #{originalPriceAmount,jdbcType=DECIMAL},
      </if>
      <if test="detailedType != null">
        detailed_type = #{detailedType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=DATE},
      </if>
      <if test="revenueRecognition != null">
        revenue_recognition = #{revenueRecognition,jdbcType=DATE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectAmount" parameterType="net.summerfarm.model.input.FinanceCashEttlementDocumentDetailsInput" resultType="net.summerfarm.model.domain.FinanceCashEttlementDocumentDetails">
    select
      ifnull(sum(tax_amount),0) taxAmount,
      ifnull(sum(price_amount),0) priceAmount,
      ifnull(sum(original_price_amount),0) originalPriceAmount,
      ifnull(sum(cost * amount),0) cost
    from finance_cash_ettlement_document_details
    where
      detailed_type = #{detailedType}
      and revenue_recognition = #{startTime}
  </select>

  <select id="selectByAll" parameterType="net.summerfarm.model.input.FinanceCashEttlementDocumentDetailsInput" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_cash_ettlement_document_details
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="whereaboutsOfFunds != null">
        and whereabouts_of_funds = #{whereaboutsOfFunds,jdbcType=INTEGER}
      </if>
      <if test="orderNo != null">
        and order_no = #{orderNo,jdbcType=VARCHAR}
      </if>
      <if test="province != null">
        and province = #{province,jdbcType=VARCHAR}
      </if>
      <if test="city != null">
        and city = #{city,jdbcType=VARCHAR}
      </if>
      <if test="payType != null">
        and pay_type = #{payType,jdbcType=VARCHAR}
      </if>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="adminId != null">
        and admin_id = #{adminId,jdbcType=INTEGER}
      </if>
      <if test="mname != null">
        and mname = #{mname,jdbcType=VARCHAR}
      </if>
      <if test="realname != null">
        and realname = #{realname,jdbcType=VARCHAR}
      </if>
      <if test="nameRemakes != null">
        and name_remakes = #{nameRemakes,jdbcType=VARCHAR}
      </if>
      <if test="endTime != null">
        and end_time = #{endTime,jdbcType=TIMESTAMP}
      </if>
      <if test="bankPayEndTime != null">
        and bank_pay_end_time = #{bankPayEndTime,jdbcType=TIMESTAMP}
      </if>
      <if test="deliveryTime != null">
        and delivery_time = #{deliveryTime,jdbcType=DATE}
      </if>
      <if test="finishTime != null">
        and finish_time = #{finishTime,jdbcType=TIMESTAMP}
      </if>
      <if test="outTimesFee != null">
        and out_times_fee = #{outTimesFee,jdbcType=DECIMAL}
      </if>
      <if test="orderType != null">
        and order_type = #{orderType,jdbcType=INTEGER}
      </if>
      <if test="orderSaleType != null">
        and order_sale_type = #{orderSaleType,jdbcType=INTEGER}
      </if>
      <if test="deliveryFee != null">
        and delivery_fee = #{deliveryFee,jdbcType=DECIMAL}
      </if>
      <if test="confirmTime != null">
        and confirm_time = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
      <if test="amount != null">
        and amount = #{amount,jdbcType=INTEGER}
      </if>
      <if test="taxRateValue != null">
        and tax_rate_value = #{taxRateValue,jdbcType=DECIMAL}
      </if>
      <if test="taxAmount != null">
        and tax_amount = #{taxAmount,jdbcType=DECIMAL}
      </if>
      <if test="priceAmount != null">
        and price_amount = #{priceAmount,jdbcType=DECIMAL}
      </if>
      <if test="originalPriceAmount != null">
        and original_price_amount = #{originalPriceAmount,jdbcType=DECIMAL}
      </if>
      <if test="detailedType != null">
        and detailed_type = #{detailedType,jdbcType=INTEGER}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=DATE}
      </if>
      <if test="revenueRecognition != null">
        and revenue_recognition = #{revenueRecognition,jdbcType=DATE}
      </if>
      <if test="startTime != null and endTimes != null">
        and revenue_recognition <![CDATA[>=]]> #{startTime}
        and revenue_recognition <![CDATA[<=]]> #{endTimes}
      </if>
    </where>
    <if test=" pageSize != null and pageNum != null">
      limit #{pageNum},#{pageSize}
    </if>
  </select>

  <select id="selectByAllCount" parameterType="net.summerfarm.model.input.FinanceCashEttlementDocumentDetailsInput" resultType="integer">
    select count(*)
    from finance_cash_ettlement_document_details
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="whereaboutsOfFunds != null">
        and whereabouts_of_funds = #{whereaboutsOfFunds,jdbcType=INTEGER}
      </if>
      <if test="orderNo != null">
        and order_no = #{orderNo,jdbcType=VARCHAR}
      </if>
      <if test="province != null">
        and province = #{province,jdbcType=VARCHAR}
      </if>
      <if test="city != null">
        and city = #{city,jdbcType=VARCHAR}
      </if>
      <if test="payType != null">
        and pay_type = #{payType,jdbcType=VARCHAR}
      </if>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="adminId != null">
        and admin_id = #{adminId,jdbcType=INTEGER}
      </if>
      <if test="mname != null">
        and mname = #{mname,jdbcType=VARCHAR}
      </if>
      <if test="realname != null">
        and realname = #{realname,jdbcType=VARCHAR}
      </if>
      <if test="nameRemakes != null">
        and name_remakes = #{nameRemakes,jdbcType=VARCHAR}
      </if>
      <if test="endTime != null">
        and end_time = #{endTime,jdbcType=TIMESTAMP}
      </if>
      <if test="bankPayEndTime != null">
        and bank_pay_end_time = #{bankPayEndTime,jdbcType=TIMESTAMP}
      </if>
      <if test="deliveryTime != null">
        and delivery_time = #{deliveryTime,jdbcType=DATE}
      </if>
      <if test="finishTime != null">
        and finish_time = #{finishTime,jdbcType=TIMESTAMP}
      </if>
      <if test="outTimesFee != null">
        and out_times_fee = #{outTimesFee,jdbcType=DECIMAL}
      </if>
      <if test="orderType != null">
        and order_type = #{orderType,jdbcType=INTEGER}
      </if>
      <if test="orderSaleType != null">
        and order_sale_type = #{orderSaleType,jdbcType=INTEGER}
      </if>
      <if test="deliveryFee != null">
        and delivery_fee = #{deliveryFee,jdbcType=DECIMAL}
      </if>
      <if test="confirmTime != null">
        and confirm_time = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
      <if test="amount != null">
        and amount = #{amount,jdbcType=INTEGER}
      </if>
      <if test="taxRateValue != null">
        and tax_rate_value = #{taxRateValue,jdbcType=DECIMAL}
      </if>
      <if test="taxAmount != null">
        and tax_amount = #{taxAmount,jdbcType=DECIMAL}
      </if>
      <if test="priceAmount != null">
        and price_amount = #{priceAmount,jdbcType=DECIMAL}
      </if>
      <if test="originalPriceAmount != null">
        and original_price_amount = #{originalPriceAmount,jdbcType=DECIMAL}
      </if>
      <if test="detailedType != null">
        and detailed_type = #{detailedType,jdbcType=INTEGER}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=DATE}
      </if>
      <if test="revenueRecognition != null">
        and revenue_recognition = #{revenueRecognition,jdbcType=DATE}
      </if>
      <if test="startTime != null and endTimes != null">
        and revenue_recognition <![CDATA[>=]]> #{startTime}
        and revenue_recognition <![CDATA[<=]]> #{endTimes}
      </if>
    </where>
  </select>
  <select id="selectByDeliveryFee" parameterType="net.summerfarm.model.input.FinanceCashEttlementDocumentDetailsInput" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_cash_ettlement_document_details
    <where>
      delivery_fee <![CDATA[>]]> 0
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="whereaboutsOfFunds != null">
        and whereabouts_of_funds = #{whereaboutsOfFunds,jdbcType=INTEGER}
      </if>
      <if test="orderNo != null">
        and order_no = #{orderNo,jdbcType=VARCHAR}
      </if>
      <if test="province != null">
        and province = #{province,jdbcType=VARCHAR}
      </if>
      <if test="city != null">
        and city = #{city,jdbcType=VARCHAR}
      </if>
      <if test="payType != null">
        and pay_type = #{payType,jdbcType=VARCHAR}
      </if>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="adminId != null">
        and admin_id = #{adminId,jdbcType=INTEGER}
      </if>
      <if test="mname != null">
        and mname = #{mname,jdbcType=VARCHAR}
      </if>
      <if test="realname != null">
        and realname = #{realname,jdbcType=VARCHAR}
      </if>
      <if test="nameRemakes != null">
        and name_remakes = #{nameRemakes,jdbcType=VARCHAR}
      </if>
      <if test="endTime != null">
        and end_time = #{endTime,jdbcType=TIMESTAMP}
      </if>
      <if test="bankPayEndTime != null">
        and bank_pay_end_time = #{bankPayEndTime,jdbcType=TIMESTAMP}
      </if>
      <if test="deliveryTime != null">
        and delivery_time = #{deliveryTime,jdbcType=DATE}
      </if>
      <if test="finishTime != null">
        and finish_time = #{finishTime,jdbcType=TIMESTAMP}
      </if>
      <if test="outTimesFee != null">
        and out_times_fee = #{outTimesFee,jdbcType=DECIMAL}
      </if>
      <if test="orderType != null">
        and order_type = #{orderType,jdbcType=INTEGER}
      </if>
      <if test="orderSaleType != null">
        and order_sale_type = #{orderSaleType,jdbcType=INTEGER}
      </if>
      <if test="confirmTime != null">
        and confirm_time = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
      <if test="amount != null">
        and amount = #{amount,jdbcType=INTEGER}
      </if>
      <if test="taxRateValue != null">
        and tax_rate_value = #{taxRateValue,jdbcType=DECIMAL}
      </if>
      <if test="taxAmount != null">
        and tax_amount = #{taxAmount,jdbcType=DECIMAL}
      </if>
      <if test="priceAmount != null">
        and price_amount = #{priceAmount,jdbcType=DECIMAL}
      </if>
      <if test="originalPriceAmount != null">
        and original_price_amount = #{originalPriceAmount,jdbcType=DECIMAL}
      </if>
      <if test="detailedType != null">
        and detailed_type = #{detailedType,jdbcType=INTEGER}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=DATE}
      </if>
      <if test="revenueRecognition != null">
        and revenue_recognition = #{revenueRecognition,jdbcType=DATE}
      </if>
      <if test="startTime != null and endTime != null">
        and revenue_recognition <![CDATA[>=]]> #{startTime}
        and revenue_recognition <![CDATA[<=]]> #{endTimes}
      </if>
    </where>
  </select>
  <select id="selectByOutTimesFee" parameterType="net.summerfarm.model.input.FinanceCashEttlementDocumentDetailsInput" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_cash_ettlement_document_details
    <where>
      out_times_fee <![CDATA[>]]> 0
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="whereaboutsOfFunds != null">
        and whereabouts_of_funds = #{whereaboutsOfFunds,jdbcType=INTEGER}
      </if>
      <if test="orderNo != null">
        and order_no = #{orderNo,jdbcType=VARCHAR}
      </if>
      <if test="province != null">
        and province = #{province,jdbcType=VARCHAR}
      </if>
      <if test="city != null">
        and city = #{city,jdbcType=VARCHAR}
      </if>
      <if test="payType != null">
        and pay_type = #{payType,jdbcType=VARCHAR}
      </if>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="adminId != null">
        and admin_id = #{adminId,jdbcType=INTEGER}
      </if>
      <if test="mname != null">
        and mname = #{mname,jdbcType=VARCHAR}
      </if>
      <if test="realname != null">
        and realname = #{realname,jdbcType=VARCHAR}
      </if>
      <if test="nameRemakes != null">
        and name_remakes = #{nameRemakes,jdbcType=VARCHAR}
      </if>
      <if test="endTime != null">
        and end_time = #{endTime,jdbcType=TIMESTAMP}
      </if>
      <if test="bankPayEndTime != null">
        and bank_pay_end_time = #{bankPayEndTime,jdbcType=TIMESTAMP}
      </if>
      <if test="deliveryTime != null">
        and delivery_time = #{deliveryTime,jdbcType=DATE}
      </if>
      <if test="finishTime != null">
        and finish_time = #{finishTime,jdbcType=TIMESTAMP}
      </if>
      <if test="orderType != null">
        and order_type = #{orderType,jdbcType=INTEGER}
      </if>
      <if test="orderSaleType != null">
        and order_sale_type = #{orderSaleType,jdbcType=INTEGER}
      </if>
      <if test="confirmTime != null">
        and confirm_time = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
      <if test="amount != null">
        and amount = #{amount,jdbcType=INTEGER}
      </if>
      <if test="taxRateValue != null">
        and tax_rate_value = #{taxRateValue,jdbcType=DECIMAL}
      </if>
      <if test="taxAmount != null">
        and tax_amount = #{taxAmount,jdbcType=DECIMAL}
      </if>
      <if test="priceAmount != null">
        and price_amount = #{priceAmount,jdbcType=DECIMAL}
      </if>
      <if test="originalPriceAmount != null">
        and original_price_amount = #{originalPriceAmount,jdbcType=DECIMAL}
      </if>
      <if test="detailedType != null">
        and detailed_type = #{detailedType,jdbcType=INTEGER}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=DATE}
      </if>
      <if test="revenueRecognition != null">
        and revenue_recognition = #{revenueRecognition,jdbcType=DATE}
      </if>
      <if test="startTime != null and endTime != null">
        and revenue_recognition <![CDATA[>=]]> #{startTime}
        and revenue_recognition <![CDATA[<=]]> #{endTimes}
      </if>
    </where>
  </select>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceCashEttlementDocumentDetails">
    update finance_cash_ettlement_document_details
    set whereabouts_of_funds = #{whereaboutsOfFunds,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      pay_type = #{payType,jdbcType=VARCHAR},
      m_id = #{mId,jdbcType=BIGINT},
      admin_id = #{adminId,jdbcType=INTEGER},
      mname = #{mname,jdbcType=VARCHAR},
      realname = #{realname,jdbcType=VARCHAR},
      name_remakes = #{nameRemakes,jdbcType=VARCHAR},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      bank_pay_end_time = #{bankPayEndTime,jdbcType=TIMESTAMP},
      delivery_time = #{deliveryTime,jdbcType=DATE},
      finish_time = #{finishTime,jdbcType=TIMESTAMP},
      out_times_fee = #{outTimesFee,jdbcType=DECIMAL},
      order_type = #{orderType,jdbcType=INTEGER},
      order_sale_type = #{orderSaleType,jdbcType=INTEGER},
      delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      sku = #{sku,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      amount = #{amount,jdbcType=INTEGER},
      tax_rate_value = #{taxRateValue,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      price_amount = #{priceAmount,jdbcType=DECIMAL},
      original_price_amount = #{originalPriceAmount,jdbcType=DECIMAL},
      detailed_type = #{detailedType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=DATE},
      revenue_recognition = #{revenueRecognition,jdbcType=DATE}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>