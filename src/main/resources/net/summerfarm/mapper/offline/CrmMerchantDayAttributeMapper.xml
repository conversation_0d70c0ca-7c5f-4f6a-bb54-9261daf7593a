<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.CrmMerchantDayAttributeMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CrmMerchantDayAttribute">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="mId" column="m_id" jdbcType="BIGINT"/>
        <result property="notVisited" column="not_visited" jdbcType="INTEGER"/>
        <result property="daysWithoutOrder" column="days_without_order" jdbcType="INTEGER"/>
        <result property="merchantLifecycle" column="merchant_lifecycle" jdbcType="TINYINT"/>
        <result property="orderFrequency" column="order_frequency" jdbcType="INTEGER"/>
        <result property="timingFollowType" column="timing_follow_type" jdbcType="TINYINT"/>
        <result property="dayTag" column="day_tag" jdbcType="INTEGER"/>
        <result property="totalGmv" column="merchant_total_gmv"/>
        <result property="coreMerchantTag" column="core_merchant_tag"/>
        <result property="lifecycle" column="lifecycle" jdbcType="VARCHAR"/>
        <result property="rValue" column="r_value" jdbcType="VARCHAR"/>
        <result property="fValue" column="f_value" jdbcType="VARCHAR"/>
        <result property="mValue" column="m_value" jdbcType="VARCHAR"/>
        <result property="daysNotLoggedIn" column="days_not_logged_in" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        m_id,not_visited,days_without_order,
        merchant_lifecycle,order_frequency,timing_follow_type,
        day_tag
    </sql>

    <select id="selectByPrimaryKey"  resultMap="BaseResultMap">
        select cmd.m_id,cmd.not_visited,cmd.days_without_order,cmd.merchant_lifecycle,cmd.order_frequency,cmd.timing_follow_type
        ,mdg.core_merchant_tag,mdg.merchant_total_gmv,cmd.lifecycle lifecycle,cmd.r_value,cmd.f_value,cmd.m_value,days_not_logged_in
        from crm_merchant_day_attribute cmd
        LEFT JOIN crm_merchant_day_gmv mdg ON cmd.m_id = mdg.m_id AND cmd.day_tag = mdg.day_tag
        where  cmd.m_id = #{mId,jdbcType=BIGINT} and cmd.day_tag = #{dayTag}
    </select>
</mapper>
