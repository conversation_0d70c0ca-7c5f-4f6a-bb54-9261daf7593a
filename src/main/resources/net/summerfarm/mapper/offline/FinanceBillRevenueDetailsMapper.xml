<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.FinanceBillRevenueDetailsMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.offline.FinanceBillRevenueDetails">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="orderItemId" column="order_item_id" jdbcType="BIGINT"/>
            <result property="deliveryPathId" column="delivery_path_id" jdbcType="BIGINT"/>
            <result property="payType" column="pay_type" jdbcType="VARCHAR"/>
            <result property="serviceArea" column="service_area" jdbcType="VARCHAR"/>
            <result property="province" column="province" jdbcType="VARCHAR"/>
            <result property="city" column="city" jdbcType="VARCHAR"/>
            <result property="mId" column="m_id" jdbcType="BIGINT"/>
            <result property="mname" column="mname" jdbcType="VARCHAR"/>
            <result property="realname" column="realname" jdbcType="VARCHAR"/>
            <result property="nameRemakes" column="name_remakes" jdbcType="VARCHAR"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="pdName" column="pd_name" jdbcType="VARCHAR"/>
            <result property="category1" column="category1" jdbcType="VARCHAR"/>
            <result property="taxRate" column="tax_rate" jdbcType="DECIMAL"/>
            <result property="orderSkuCnt" column="order_sku_cnt" jdbcType="BIGINT"/>
            <result property="realSkuCnt" column="real_sku_cnt" jdbcType="BIGINT"/>
            <result property="realTotalAmt" column="real_total_amt" jdbcType="DECIMAL"/>
            <result property="originTotalAmt" column="origin_total_amt" jdbcType="DECIMAL"/>
            <result property="totalDiscountAmt" column="total_discount_amt" jdbcType="DECIMAL"/>
            <result property="deliveryAmt" column="delivery_amt" jdbcType="DECIMAL"/>
            <result property="outTimesAmt" column="out_times_amt" jdbcType="DECIMAL"/>
            <result property="payTime" column="pay_time" jdbcType="DATE"/>
            <result property="finishTime" column="finish_time" jdbcType="DATE"/>
            <result property="revenueAmt" column="revenue_amt" jdbcType="DECIMAL"/>
            <result property="revenueAmtNotax" column="revenue_amt_notax" jdbcType="DECIMAL"/>
            <result property="taxAmt" column="tax_amt" jdbcType="DECIMAL"/>
            <result property="unitCost" column="unit_cost" jdbcType="DECIMAL"/>
            <result property="unitCostNotax" column="unit_cost_notax" jdbcType="DECIMAL"/>
            <result property="cost" column="cost" jdbcType="DECIMAL"/>
            <result property="costNotax" column="cost_notax" jdbcType="DECIMAL"/>
            <result property="deliveryCouponAmt" column="delivery_coupon_amt" jdbcType="DECIMAL"/>
            <result property="custTeam" column="cust_team" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        order_no,order_item_id,delivery_path_id,
        pay_type,service_area,province,
        city,m_id,mname,
        realname,name_remakes,sku,
        pd_name,category1,tax_rate,
        order_sku_cnt,real_sku_cnt,real_total_amt,
        origin_total_amt,total_discount_amt,delivery_amt,
        out_times_amt,pay_time,finish_time,
        revenue_amt,revenue_amt_notax,tax_amt,
        unit_cost,unit_cost_notax,cost,
        cost_notax,delivery_coupon_amt,cust_team,
        remark
    </sql>
    <select id="storeBillInfo" resultType="net.summerfarm.model.domain.FinanceAccountingStore">
        select SUM(ifnull(delivery_amt, 0))                             deliveryFee,
               SUM(ifnull(real_total_amt, 0) + ifnull(delivery_amt, 0)) totalPrice,
               SUM(ifnull(out_times_amt, 0))                            outTimesFee,
               rev.m_id                                                 mId
        from finance_bill_revenue_details rev
        where rev.`m_id` = #{mId}
        <if test="startDatetime!=null">
            and rev.finish_time >=#{startDatetime}
        </if>
        and rev.finish_time  <![CDATA[<=]]>#{endDatetime}
    </select>
    <select id="storeBillDetail" resultType="net.summerfarm.model.DTO.finance.FinanceAccountingStoreDetailDTO">
        select SUM(ifnull(delivery_amt, 0))                             deliveryFee,
               SUM(ifnull(real_total_amt, 0) + ifnull(delivery_amt, 0)) totalPrice,
               SUM(ifnull(real_total_amt, 0))                           totalAmountReceivable,
               SUM(ifnull(out_times_amt, 0))                            outTimesFee,
               rev.m_id                                                 mId,
               rev.order_no                                             orderNo,
               rev.finish_time                                          finishTime
        from finance_bill_revenue_details rev
        where rev.`m_id` = #{mId}
        <if test="startDatetime!=null">
            and rev.finish_time >=#{startDatetime}
        </if>
        and rev.finish_time  <![CDATA[<=]]> #{endDatetime}
        group by rev.order_no
        order by rev.finish_time asc
    </select>
    <select id="selectTimeRangeMId" resultType="java.lang.Integer">
        select DISTINCT (`m_id`)
        from finance_bill_revenue_details rev
        where rev.finish_time BETWEEN #{startDatetime} and #{endDatetime} and `name_remakes` = '无'
    </select>
    <select id="selectByOrderNo" resultType="net.summerfarm.model.DTO.finance.FinanceOrderDTO">
        select sum(real_total_amt+ifnull(delivery_amt,0)+ifnull(out_times_amt,0)) billAmount,fbd.order_item_id orderItemId,fbd.order_no orderNo
        from finance_bill_revenue_details fbd
        where fbd.order_no in
        <foreach collection="orderNo" separator="," open="(" close=")" item="orderNo">
            #{orderNo}
        </foreach>
        group by fbd.order_item_id
    </select>
    <select id="selectItemByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from finance_bill_revenue_details where order_no=#{orderNo}
    </select>
</mapper>
