<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.CustAfterDlvProfitLabelMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CustAfterDlvProfitLabel">
    <!--@mbg.generated-->
    <!--@Table cust_after_dlv_profit_label-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="date_tag" jdbcType="INTEGER" property="dateTag" />
    <result column="cust_id" jdbcType="BIGINT" property="custId" />
    <result column="dlv_profit_label" jdbcType="VARCHAR" property="dlvProfitLabel" />
    <result column="dlv_profit_rate_label" jdbcType="VARCHAR" property="dlvProfitRateLabel" />
    <result column="life_cycle" jdbcType="VARCHAR" property="lifeCycle" />
    <result column="life_cycle_detail" jdbcType="VARCHAR" property="lifeCycleDetail" />
    <result column="dlv_profit_group" jdbcType="VARCHAR" property="dlvProfitGroup" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, date_tag, cust_id, dlv_profit_label, dlv_profit_rate_label, 
    life_cycle, life_cycle_detail, dlv_profit_group
  </sql>

  <select id="selectOneByCustIdAndNoSaas" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cust_after_dlv_profit_label
    where cust_id=#{custId,jdbcType=BIGINT}
    and life_cycle_detail != 'SAAS'
  </select>
</mapper>