<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.summerfarm.mapper.offline.XianmuCardStatisticsMapper">

  <select id="queryByStatDate" resultType="net.summerfarm.model.domain.XianmuCardStatistics">
    SELECT
    stat_date AS statDate,
    total_balance AS totalBalance,
    total_balance_weekly_ratio AS totalBalanceWeeklyRatio,
    store_count_with_balance AS storeCountWithBalance,
    store_count_weekly_ratio AS storeCountWeeklyRatio,
    avg_store_balance AS avgStoreBalance,
    avg_store_balance_weekly_ratio AS avgStoreBalanceWeeklyRatio
    FROM xianmu_card_statistics
    WHERE stat_date = #{statDate}
  </select>

  <select id="queryByStatDates" resultType="net.summerfarm.model.domain.XianmuCardStatistics">
    SELECT
    stat_date AS statDate,
    total_balance AS totalBalance,
    total_balance_weekly_ratio AS totalBalanceWeeklyRatio,
    store_count_with_balance AS storeCountWithBalance,
    store_count_weekly_ratio AS storeCountWeeklyRatio,
    avg_store_balance AS avgStoreBalance,
    avg_store_balance_weekly_ratio AS avgStoreBalanceWeeklyRatio
    FROM xianmu_card_statistics
    WHERE stat_date BETWEEN #{startTime} AND #{endTime}
    order by stat_date
  </select>
</mapper>