<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.StockDashboardFutureMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.offline.StockDashboardFuture">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pd_id" jdbcType="INTEGER" property="pdId" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="view_date" jdbcType="TIMESTAMP" property="viewDate" />
    <result column="on_way_quantity" jdbcType="INTEGER" property="onWayQuantity" />
    <result column="transfer_in_quantity" jdbcType="INTEGER" property="transferInQuantity" />
    <result column="on_way_order_quantity" jdbcType="INTEGER" property="onWayOrderQuantity" />
    <result column="po_on_way_quantity" jdbcType="DECIMAL" property="poOnWayQuantity" />
    <result column="transfer_order_in_quantity" jdbcType="DECIMAL" property="transferOrderInQuantity" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pd_id, sku_id, warehouse_no, view_date,
    on_way_quantity, transfer_in_quantity,on_way_order_quantity, po_on_way_quantity, transfer_order_in_quantity
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_dashboard_future
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from stock_dashboard_future
    <where>
        <if test="skuId != null">
            and sku_id = #{skuId}
        </if>
        <if test="warehouseNo != null">
          and warehouse_no = #{warehouseNo}
        </if>
        <if test="startDate != null">
          and view_date >= #{startDate}
        </if>
        <if test="endDate != null">
          and view_date <![CDATA[<]]> #{endDate}
        </if>
    </where>
    order by view_date asc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_dashboard_future
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.offline.StockDashboardFuture">
    insert into stock_dashboard_future (id, pd_id, sku_id, 
      warehouse_no, view_date, on_way_quantity,
      transfer_in_quantity, po_on_way_quantity, transfer_order_in_quantity)
    values (#{id,jdbcType=BIGINT}, #{pdId,jdbcType=INTEGER}, #{skuId,jdbcType=VARCHAR}, 
      #{warehouseNo,jdbcType=INTEGER}, #{viewDate,jdbcType=TIMESTAMP}, #{onWayQuantity,jdbcType=INTEGER},
      #{transferInQuantity,jdbcType=INTEGER}, #{poOnWayQuantity,jdbcType=DECIMAL}, #{transferOrderInQuantity,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.offline.StockDashboardFuture">
    insert into stock_dashboard_future
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="viewDate != null">
        view_date,
      </if>
      <if test="onWayQuantity != null">
        on_way_quantity,
      </if>
      <if test="transferInQuantity != null">
        transfer_in_quantity,
      </if>
      <if test="poOnWayQuantity != null">
        po_on_way_quantity,
      </if>
      <if test="transferOrderInQuantity != null">
        transfer_order_in_quantity,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="viewDate != null">
        #{viewDate,jdbcType=TIMESTAMP},
      </if>
      <if test="onWayQuantity != null">
        #{onWayQuantity,jdbcType=INTEGER},
      </if>
      <if test="transferInQuantity != null">
        #{transferInQuantity,jdbcType=INTEGER},
      </if>
      <if test="poOnWayQuantity != null">
        #{poOnWayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="transferOrderInQuantity != null">
        #{transferOrderInQuantity,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.offline.StockDashboardFuture">
    update stock_dashboard_future
    <set>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="viewDate != null">
        view_date = #{viewDate,jdbcType=TIMESTAMP},
      </if>
      <if test="onWayQuantity != null">
        on_way_quantity = #{onWayQuantity,jdbcType=INTEGER},
      </if>
      <if test="transferInQuantity != null">
        transfer_in_quantity = #{transferInQuantity,jdbcType=INTEGER},
      </if>
      <if test="poOnWayQuantity != null">
        po_on_way_quantity = #{poOnWayQuantity,jdbcType=DECIMAL},
      </if>
      <if test="transferOrderInQuantity != null">
        transfer_order_in_quantity = #{transferOrderInQuantity,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.offline.StockDashboardFuture">
    update stock_dashboard_future
    set pd_id = #{pdId,jdbcType=INTEGER},
      sku_id = #{skuId,jdbcType=VARCHAR},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      view_date = #{viewDate,jdbcType=TIMESTAMP},
      on_way_quantity = #{onWayQuantity,jdbcType=INTEGER},
      transfer_in_quantity = #{transferInQuantity,jdbcType=INTEGER},
      po_on_way_quantity = #{poOnWayQuantity,jdbcType=DECIMAL},
      transfer_order_in_quantity = #{transferOrderInQuantity,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectSumList" resultMap="BaseResultMap">
    select sku_id ,SUM(IFNULL(on_way_quantity,0)) on_way_quantity,SUM(IFNULL(on_way_order_quantity,0)) on_way_order_quantity,SUM(IFNULL(transfer_in_quantity,0)) transfer_in_quantity
    from  stock_dashboard_future
    <where>
      warehouse_no= #{warehouseNo}  and view_date <![CDATA[<]]> #{endDate} and view_date >= #{startDate}
      <if test="skuIds != null and skuIds.size != 0">
        and sku_id in
        <foreach collection="skuIds" open="(" close=")" separator="," item="it">
          #{it}
        </foreach>
      </if>
    </where>
    group by sku_id
  </select>
</mapper>