<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.FinanceCashSettlementMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceCashSettlement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="manual_code" jdbcType="INTEGER" property="manualCode" />
    <result column="whereabouts_of_funds" jdbcType="INTEGER" property="whereaboutsOfFunds" />
    <result column="including_tax" jdbcType="DECIMAL" property="includingTax" />
    <result column="tax_rate_value" jdbcType="DECIMAL" property="taxRateValue" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_time" jdbcType="DATE" property="createTime" />
    <result column="revenue_recognition" jdbcType="DATE" property="revenueRecognition" />
  </resultMap>
  <sql id="Base_Column_List">
    id, manual_code, whereabouts_of_funds, including_tax, tax_rate_value, `type`, create_time, 
    revenue_recognition
  </sql>
  <select id="selectByDate" resultMap="BaseResultMap">
    select manual_code
    from finance_cash_settlement
    where revenue_recognition = #{startTime}
    group by manual_code
    order by manual_code asc
  </select>

  <select id="selectByAll" resultType="net.summerfarm.model.domain.FinanceCashSettlement">
    select  id, manual_code manualCode, whereabouts_of_funds whereaboutsOfFunds, including_tax includingTax, tax_rate_value taxRateValue, `type`, create_time createTime,
    revenue_recognition revenueRecognition
    from finance_cash_settlement
    where revenue_recognition = #{startTime}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_cash_settlement
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceCashSettlement" useGeneratedKeys="true">
    insert into finance_cash_settlement (manual_code, whereabouts_of_funds, including_tax, 
      tax_rate_value, `type`, create_time, 
      revenue_recognition)
    values (#{manualCode,jdbcType=INTEGER}, #{whereaboutsOfFunds,jdbcType=INTEGER}, #{includingTax,jdbcType=DECIMAL}, 
      #{taxRateValue,jdbcType=DECIMAL}, #{type,jdbcType=INTEGER}, #{createTime,jdbcType=DATE}, 
      #{revenueRecognition,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceCashSettlement" useGeneratedKeys="true">
    insert into finance_cash_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="manualCode != null">
        manual_code,
      </if>
      <if test="whereaboutsOfFunds != null">
        whereabouts_of_funds,
      </if>
      <if test="includingTax != null">
        including_tax,
      </if>
      <if test="taxRateValue != null">
        tax_rate_value,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="revenueRecognition != null">
        revenue_recognition,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="manualCode != null">
        #{manualCode,jdbcType=INTEGER},
      </if>
      <if test="whereaboutsOfFunds != null">
        #{whereaboutsOfFunds,jdbcType=INTEGER},
      </if>
      <if test="includingTax != null">
        #{includingTax,jdbcType=DECIMAL},
      </if>
      <if test="taxRateValue != null">
        #{taxRateValue,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=DATE},
      </if>
      <if test="revenueRecognition != null">
        #{revenueRecognition,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceCashSettlement">
    update finance_cash_settlement
    <set>
      <if test="manualCode != null">
        manual_code = #{manualCode,jdbcType=INTEGER},
      </if>
      <if test="whereaboutsOfFunds != null">
        whereabouts_of_funds = #{whereaboutsOfFunds,jdbcType=INTEGER},
      </if>
      <if test="includingTax != null">
        including_tax = #{includingTax,jdbcType=DECIMAL},
      </if>
      <if test="taxRateValue != null">
        tax_rate_value = #{taxRateValue,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=DATE},
      </if>
      <if test="revenueRecognition != null">
        revenue_recognition = #{revenueRecognition,jdbcType=DATE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceCashSettlement">
    update finance_cash_settlement
    set manual_code = #{manualCode,jdbcType=INTEGER},
      whereabouts_of_funds = #{whereaboutsOfFunds,jdbcType=INTEGER},
      including_tax = #{includingTax,jdbcType=DECIMAL},
      tax_rate_value = #{taxRateValue,jdbcType=DECIMAL},
      `type` = #{type,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=DATE},
      revenue_recognition = #{revenueRecognition,jdbcType=DATE}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>