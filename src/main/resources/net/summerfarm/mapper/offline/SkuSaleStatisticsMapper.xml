<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.SkuSaleStatisticsMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.offline.SkuSaleStatisticsPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="rdc_k" jdbcType="INTEGER" property="rdcK"/>
        <result column="in_sku_k" jdbcType="VARCHAR" property="inSkuK"/>
        <result column="in_sale_cnt_b15d" jdbcType="DECIMAL" property="inSaleCntB15d"/>
        <result column="in_sale_cnt_b7d" jdbcType="DECIMAL" property="inSaleCntB7d"/>
        <result column="in_min_sale_cnt" jdbcType="DECIMAL" property="inMinSaleCnt"/>
        <result column="in_max_sale_seven" jdbcType="DECIMAL" property="inMaxSaleSeven"/>
        <result column="out_sku_k" jdbcType="VARCHAR" property="outSkuK"/>
        <result column="out_sale_cnt_b15d" jdbcType="DECIMAL" property="outSaleCntB15d"/>
        <result column="out_sale_cnt_b7d" jdbcType="DECIMAL" property="outSaleCntB7d"/>
        <result column="out_min_sale_cnt" jdbcType="DECIMAL" property="outMinSaleCnt"/>
        <result column="out_max_sale_seven" jdbcType="DECIMAL" property="outMaxSaleSeven"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="date_flag" jdbcType="VARCHAR" property="dateFlag"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,`rdc_k`,`in_sku_k`,`in_sale_cnt_b15d`,`in_sale_cnt_b7d`,
        `in_min_sale_cnt`,`in_max_sale_seven`,`out_sku_k`,`out_sale_cnt_b15d`,`out_sale_cnt_b7d`,
        `out_min_sale_cnt`,`out_max_sale_seven`,`create_time`,`date_flag`
    </sql>

    <select id="selectListByDateFlag" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sku_sale_statistics
        where date_flag = #{dateFlag}
    </select>

</mapper>