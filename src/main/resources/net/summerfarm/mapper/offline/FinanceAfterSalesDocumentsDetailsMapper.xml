<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.FinanceAfterSalesDocumentsDetailsMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceAfterSalesDocumentsDetails">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="whereabouts_of_funds" jdbcType="INTEGER" property="whereaboutsOfFunds" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="after_sale_order_no" jdbcType="VARCHAR" property="afterSaleOrderNo" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="after_sale_unit" jdbcType="VARCHAR" property="afterSaleUnit" />
    <result column="handle_num" jdbcType="DECIMAL" property="handleNum" />
    <result column="handletime" jdbcType="TIMESTAMP" property="handletime" />
    <result column="deliveryed" jdbcType="INTEGER" property="deliveryed" />
    <result column="bank_refund_end_time" jdbcType="TIMESTAMP" property="bankRefundEndTime" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="pay_type" jdbcType="VARCHAR" property="payType" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="mname" jdbcType="VARCHAR" property="mname" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="realname" jdbcType="VARCHAR" property="realname" />
    <result column="name_remakes" jdbcType="VARCHAR" property="nameRemakes" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="tax_rate_value" jdbcType="DECIMAL" property="taxRateValue" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="create_time" jdbcType="DATE" property="createTime" />
    <result column="refund_find" jdbcType="DATE" property="refundFind" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="after_sales_service_type"  property="afterSalesServiceType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, whereabouts_of_funds, order_no, after_sale_order_no, quantity, after_sale_unit, 
    handle_num, handletime, deliveryed, bank_refund_end_time, province, city, pay_type, 
    m_id, mname, admin_id, realname, name_remakes, sku, `type`, tax_rate_value, tax_amount, 
    create_time, refund_find,unit,pd_name,after_sales_service_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_after_sales_documents_details
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_after_sales_documents_details
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAfterSalesDocumentsDetails" useGeneratedKeys="true">
    insert into finance_after_sales_documents_details (whereabouts_of_funds, order_no, after_sale_order_no, 
      quantity, after_sale_unit, handle_num, 
      handletime, deliveryed, bank_refund_end_time, 
      province, city, pay_type, 
      m_id, mname, admin_id, 
      realname, name_remakes, sku, 
      `type`, tax_rate_value, tax_amount, 
      create_time, refund_find)
    values (#{whereaboutsOfFunds,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, #{afterSaleOrderNo,jdbcType=VARCHAR}, 
      #{quantity,jdbcType=INTEGER}, #{afterSaleUnit,jdbcType=VARCHAR}, #{handleNum,jdbcType=DECIMAL}, 
      #{handletime,jdbcType=TIMESTAMP}, #{deliveryed,jdbcType=INTEGER}, #{bankRefundEndTime,jdbcType=TIMESTAMP}, 
      #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{payType,jdbcType=VARCHAR}, 
      #{mId,jdbcType=BIGINT}, #{mname,jdbcType=VARCHAR}, #{adminId,jdbcType=INTEGER}, 
      #{realname,jdbcType=VARCHAR}, #{nameRemakes,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{taxRateValue,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=DATE}, #{refundFind,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAfterSalesDocumentsDetails" useGeneratedKeys="true">
    insert into finance_after_sales_documents_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="whereaboutsOfFunds != null">
        whereabouts_of_funds,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="afterSaleOrderNo != null">
        after_sale_order_no,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="afterSaleUnit != null">
        after_sale_unit,
      </if>
      <if test="handleNum != null">
        handle_num,
      </if>
      <if test="handletime != null">
        handletime,
      </if>
      <if test="deliveryed != null">
        deliveryed,
      </if>
      <if test="bankRefundEndTime != null">
        bank_refund_end_time,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="mname != null">
        mname,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="realname != null">
        realname,
      </if>
      <if test="nameRemakes != null">
        name_remakes,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="taxRateValue != null">
        tax_rate_value,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="refundFind != null">
        refund_find,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="whereaboutsOfFunds != null">
        #{whereaboutsOfFunds,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleOrderNo != null">
        #{afterSaleOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="afterSaleUnit != null">
        #{afterSaleUnit,jdbcType=VARCHAR},
      </if>
      <if test="handleNum != null">
        #{handleNum,jdbcType=DECIMAL},
      </if>
      <if test="handletime != null">
        #{handletime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryed != null">
        #{deliveryed,jdbcType=INTEGER},
      </if>
      <if test="bankRefundEndTime != null">
        #{bankRefundEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="mname != null">
        #{mname,jdbcType=VARCHAR},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="realname != null">
        #{realname,jdbcType=VARCHAR},
      </if>
      <if test="nameRemakes != null">
        #{nameRemakes,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="taxRateValue != null">
        #{taxRateValue,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=DATE},
      </if>
      <if test="refundFind != null">
        #{refundFind,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceAfterSalesDocumentsDetails">
    update finance_after_sales_documents_details
    <set>
      <if test="whereaboutsOfFunds != null">
        whereabouts_of_funds = #{whereaboutsOfFunds,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleOrderNo != null">
        after_sale_order_no = #{afterSaleOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="afterSaleUnit != null">
        after_sale_unit = #{afterSaleUnit,jdbcType=VARCHAR},
      </if>
      <if test="handleNum != null">
        handle_num = #{handleNum,jdbcType=DECIMAL},
      </if>
      <if test="handletime != null">
        handletime = #{handletime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryed != null">
        deliveryed = #{deliveryed,jdbcType=INTEGER},
      </if>
      <if test="bankRefundEndTime != null">
        bank_refund_end_time = #{bankRefundEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="mname != null">
        mname = #{mname,jdbcType=VARCHAR},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="realname != null">
        realname = #{realname,jdbcType=VARCHAR},
      </if>
      <if test="nameRemakes != null">
        name_remakes = #{nameRemakes,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="taxRateValue != null">
        tax_rate_value = #{taxRateValue,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=DATE},
      </if>
      <if test="refundFind != null">
        refund_find = #{refundFind,jdbcType=DATE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectAfter" parameterType="net.summerfarm.model.input.FinanceAfterSalesDocumentsDetailsInput" resultType="net.summerfarm.model.domain.FinanceAfterSalesDocumentsDetails">
    select ifnull(sum(tax_amount),0) taxAmount,ifnull(sum(handle_num),0) handleNum
    from finance_after_sales_documents_details
    where
      deliveryed = #{deliveryed}
      and refund_find = #{startTime}
      and `type` = 0
  </select>
  <select id="selectByAll" parameterType="net.summerfarm.model.input.FinanceAfterSalesDocumentsDetailsInput" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_after_sales_documents_details
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="whereaboutsOfFunds != null">
        and whereabouts_of_funds = #{whereaboutsOfFunds,jdbcType=INTEGER}
      </if>
      <if test="orderNo != null">
        and order_no = #{orderNo,jdbcType=VARCHAR}
      </if>
      <if test="afterSaleOrderNo != null">
        and after_sale_order_no = #{afterSaleOrderNo,jdbcType=VARCHAR}
      </if>
      <if test="quantity != null">
        and quantity = #{quantity,jdbcType=INTEGER}
      </if>
      <if test="afterSaleUnit != null">
        and after_sale_unit = #{afterSaleUnit,jdbcType=VARCHAR}
      </if>
      <if test="handleNum != null">
        and handle_num = #{handleNum,jdbcType=DECIMAL}
      </if>
      <if test="handletime != null">
        and handletime = #{handletime,jdbcType=TIMESTAMP}
      </if>
      <if test="deliveryed != null">
        and deliveryed = #{deliveryed,jdbcType=INTEGER}
      </if>
      <if test="bankRefundEndTime != null">
        and bank_refund_end_time = #{bankRefundEndTime,jdbcType=TIMESTAMP}
      </if>
      <if test="province != null">
        and province = #{province,jdbcType=VARCHAR}
      </if>
      <if test="city != null">
        and city = #{city,jdbcType=VARCHAR}
      </if>
      <if test="payType != null">
        and pay_type = #{payType,jdbcType=VARCHAR}
      </if>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="mname != null">
        and mname = #{mname,jdbcType=VARCHAR}
      </if>
      <if test="adminId != null">
        and admin_id = #{adminId,jdbcType=INTEGER}
      </if>
      <if test="realname != null">
        and realname = #{realname,jdbcType=VARCHAR}
      </if>
      <if test="nameRemakes != null">
        and name_remakes = #{nameRemakes,jdbcType=VARCHAR}
      </if>
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
      <if test="taxRateValue != null">
        and tax_rate_value = #{taxRateValue,jdbcType=DECIMAL}
      </if>
      <if test="taxAmount != null">
        and tax_amount = #{taxAmount,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=DATE}
      </if>
      <if test="refundFind != null">
        and refund_find = #{refundFind,jdbcType=DATE}
      </if>
      <if test="startTime != null and endTime != null">
        and refund_find <![CDATA[>=]]> #{startTime}
        and refund_find <![CDATA[<=]]> #{endTime}
      </if>
      <if test=" pageSize != null and pageNum != null">
        limit #{pageNum},#{pageSize}
      </if>
    </where>
  </select>

  <select id="selectByAllCount" parameterType="net.summerfarm.model.input.FinanceAfterSalesDocumentsDetailsInput" resultType="integer">
    select count(*)
    from finance_after_sales_documents_details
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="whereaboutsOfFunds != null">
        and whereabouts_of_funds = #{whereaboutsOfFunds,jdbcType=INTEGER}
      </if>
      <if test="orderNo != null">
        and order_no = #{orderNo,jdbcType=VARCHAR}
      </if>
      <if test="afterSaleOrderNo != null">
        and after_sale_order_no = #{afterSaleOrderNo,jdbcType=VARCHAR}
      </if>
      <if test="quantity != null">
        and quantity = #{quantity,jdbcType=INTEGER}
      </if>
      <if test="afterSaleUnit != null">
        and after_sale_unit = #{afterSaleUnit,jdbcType=VARCHAR}
      </if>
      <if test="handleNum != null">
        and handle_num = #{handleNum,jdbcType=DECIMAL}
      </if>
      <if test="handletime != null">
        and handletime = #{handletime,jdbcType=TIMESTAMP}
      </if>
      <if test="deliveryed != null">
        and deliveryed = #{deliveryed,jdbcType=INTEGER}
      </if>
      <if test="bankRefundEndTime != null">
        and bank_refund_end_time = #{bankRefundEndTime,jdbcType=TIMESTAMP}
      </if>
      <if test="province != null">
        and province = #{province,jdbcType=VARCHAR}
      </if>
      <if test="city != null">
        and city = #{city,jdbcType=VARCHAR}
      </if>
      <if test="payType != null">
        and pay_type = #{payType,jdbcType=VARCHAR}
      </if>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="mname != null">
        and mname = #{mname,jdbcType=VARCHAR}
      </if>
      <if test="adminId != null">
        and admin_id = #{adminId,jdbcType=INTEGER}
      </if>
      <if test="realname != null">
        and realname = #{realname,jdbcType=VARCHAR}
      </if>
      <if test="nameRemakes != null">
        and name_remakes = #{nameRemakes,jdbcType=VARCHAR}
      </if>
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
      <if test="taxRateValue != null">
        and tax_rate_value = #{taxRateValue,jdbcType=DECIMAL}
      </if>
      <if test="taxAmount != null">
        and tax_amount = #{taxAmount,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=DATE}
      </if>
      <if test="refundFind != null">
        and refund_find = #{refundFind,jdbcType=DATE}
      </if>
      <if test="startTime != null and endTime != null">
        and refund_find <![CDATA[>=]]> #{startTime}
        and refund_find <![CDATA[<=]]> #{endTime}
      </if>
      <if test=" pageSize != null and pageNum != null">
        limit #{pageNum},#{pageSize}
      </if>
    </where>
  </select>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceAfterSalesDocumentsDetails">
    update finance_after_sales_documents_details
    set whereabouts_of_funds = #{whereaboutsOfFunds,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      after_sale_order_no = #{afterSaleOrderNo,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=INTEGER},
      after_sale_unit = #{afterSaleUnit,jdbcType=VARCHAR},
      handle_num = #{handleNum,jdbcType=DECIMAL},
      handletime = #{handletime,jdbcType=TIMESTAMP},
      deliveryed = #{deliveryed,jdbcType=INTEGER},
      bank_refund_end_time = #{bankRefundEndTime,jdbcType=TIMESTAMP},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      pay_type = #{payType,jdbcType=VARCHAR},
      m_id = #{mId,jdbcType=BIGINT},
      mname = #{mname,jdbcType=VARCHAR},
      admin_id = #{adminId,jdbcType=INTEGER},
      realname = #{realname,jdbcType=VARCHAR},
      name_remakes = #{nameRemakes,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      tax_rate_value = #{taxRateValue,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=DATE},
      refund_find = #{refundFind,jdbcType=DATE}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>