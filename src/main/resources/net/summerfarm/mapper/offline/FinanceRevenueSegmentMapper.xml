<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.FinanceRevenueSegmentMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceRevenueSegment">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cash_settlement_income" jdbcType="DECIMAL" property="cashSettlementIncome" />
    <result column="income_refund" jdbcType="DECIMAL" property="incomeRefund" />
    <result column="account_period_income" jdbcType="DECIMAL" property="accountPeriodIncome" />
    <result column="account_estimated_income" jdbcType="DECIMAL" property="accountEstimatedIncome" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="revenue_recognition" jdbcType="DATE" property="revenueRecognition" />
  </resultMap>
  <sql id="Base_Column_List">
    id, cash_settlement_income, income_refund, account_period_income, account_estimated_income, 
    create_time, revenue_recognition
  </sql>
  <select id="selectByEndTime"  resultType="net.summerfarm.model.domain.FinanceRevenueSegment">
    select
      id, ifnull(cash_settlement_income,0) cashSettlementIncome, ifnull(income_refund,0) incomeRefund, ifnull(account_period_income,0) accountPeriodIncome, ifnull(account_estimated_income,0) accountEstimatedIncome,
      create_time createTime, revenue_recognition revenueRecognition
    from finance_revenue_segment
    where revenue_recognition = #{revenueRecognition}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_revenue_segment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceRevenueSegment" useGeneratedKeys="true">
    insert into finance_revenue_segment (cash_settlement_income, income_refund, 
      account_period_income, account_estimated_income, 
      create_time, revenue_recognition)
    values (#{cashSettlementIncome,jdbcType=DECIMAL}, #{incomeRefund,jdbcType=DECIMAL}, 
      #{accountPeriodIncome,jdbcType=DECIMAL}, #{accountEstimatedIncome,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{revenueRecognition,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceRevenueSegment" useGeneratedKeys="true">
    insert into finance_revenue_segment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cashSettlementIncome != null">
        cash_settlement_income,
      </if>
      <if test="incomeRefund != null">
        income_refund,
      </if>
      <if test="accountPeriodIncome != null">
        account_period_income,
      </if>
      <if test="accountEstimatedIncome != null">
        account_estimated_income,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="revenueRecognition != null">
        revenue_recognition,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cashSettlementIncome != null">
        #{cashSettlementIncome,jdbcType=DECIMAL},
      </if>
      <if test="incomeRefund != null">
        #{incomeRefund,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodIncome != null">
        #{accountPeriodIncome,jdbcType=DECIMAL},
      </if>
      <if test="accountEstimatedIncome != null">
        #{accountEstimatedIncome,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="revenueRecognition != null">
        #{revenueRecognition,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceRevenueSegment">
    update finance_revenue_segment
    <set>
      <if test="cashSettlementIncome != null">
        cash_settlement_income = #{cashSettlementIncome,jdbcType=DECIMAL},
      </if>
      <if test="incomeRefund != null">
        income_refund = #{incomeRefund,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodIncome != null">
        account_period_income = #{accountPeriodIncome,jdbcType=DECIMAL},
      </if>
      <if test="accountEstimatedIncome != null">
        account_estimated_income = #{accountEstimatedIncome,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="revenueRecognition != null">
        revenue_recognition = #{revenueRecognition,jdbcType=DATE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceRevenueSegment">
    update finance_revenue_segment
    set cash_settlement_income = #{cashSettlementIncome,jdbcType=DECIMAL},
      income_refund = #{incomeRefund,jdbcType=DECIMAL},
      account_period_income = #{accountPeriodIncome,jdbcType=DECIMAL},
      account_estimated_income = #{accountEstimatedIncome,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      revenue_recognition = #{revenueRecognition,jdbcType=DATE}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectAll" resultType="net.summerfarm.model.domain.FinanceRevenueSegment">
    select
      ifnull(sum(cash_settlement_income),0) cashSettlementIncome,
      ifnull(sum(income_refund),0) incomeRefund,
      ifnull(sum(account_period_income),0) accountPeriodIncome,
      ifnull(sum(cash_settlement_cost),0) cashSettlementCost,
      ifnull(sum(account_period_cost),0) accountPeriodCost
    from finance_revenue_segment
    where revenue_recognition <![CDATA[>=]]> #{startTime} and revenue_recognition <![CDATA[<=]]> #{endTime}
  </select>
  <select id="select" resultType="net.summerfarm.model.vo.FinanceRevenueSegmentVO">
    select ifnull(cash_settlement_income,0) cashSettlementIncome, ifnull(income_refund,0) incomeRefund, ifnull(account_period_income,0) accountPeriodIncome,revenue_recognition revenueRecognition
    from finance_revenue_segment
    where revenue_recognition <![CDATA[>=]]> #{startTime} and revenue_recognition <![CDATA[<=]]> #{endTime}
    order by revenue_recognition
  </select>

</mapper>