<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.offline.StockSalesVolumeMapper">

    <select id="select"  resultType="net.summerfarm.model.domain.StockSkuStatistics" >
        select sku,SUM(IFNULL(quantity,0)) quantity
        from  stock_sku_statistics
        where warehouse_no= #{warehouseNo}  and  `date`<![CDATA[<=]]> #{endTime} and `date` <![CDATA[>=]]> #{startTime} group by sku
    </select>

    <select id="selectBySku"  resultType="net.summerfarm.model.domain.StockSkuStatistics" >
        select sku,SUM(IFNULL(quantity,0)) quantity
        from  stock_sku_statistics
        where warehouse_no= #{warehouseNo}  and  `date` <![CDATA[<=]]> #{endTime} and `date` <![CDATA[>=]]> #{startTime} and sku= #{sku}
    </select>

    <select id="selectByDateAndSku" resultType="net.summerfarm.model.domain.StockSkuStatistics">
        SELECT sss.sku,duration_rate_14d durationRate14d,turnover_rate_7d turnoverRate7d from stock_sku_statistics sss join (select sku, MAX(date) as date from stock_sku_statistics t where
        sku in <foreach collection="skus" separator="," close=")" open="(" item="sku">
        #{sku,jdbcType=VARCHAR}
    </foreach>
        GROUP BY sku) t on sss.sku = t.sku and sss.date = t.date;

    </select>

    <select id="selectBySkuAndWerhouseNo" resultType="net.summerfarm.model.domain.StockSkuStatistics">
        select sku, warehouse_no warehouseNo, SUM(IFNULL(quantity,0)) quantity
        from  stock_sku_statistics
        where warehouse_no= #{warehouseNo} and sku= #{sku}
    </select>
</mapper>