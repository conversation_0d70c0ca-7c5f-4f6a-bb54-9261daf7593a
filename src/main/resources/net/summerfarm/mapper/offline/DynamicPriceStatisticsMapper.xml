<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.DynamicPriceStatisticsMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.offline.DynamicPriceStatistics">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="uv" jdbcType="INTEGER" property="uv" />
    <result column="click_conversion" jdbcType="DECIMAL" property="clickConversion" />
    <result column="sales_volume" jdbcType="INTEGER" property="salesVolume" />
    <result column="sell_out" jdbcType="BIT" property="sellOut" />
    <result column="sales_speed_hour" jdbcType="VARCHAR" property="salesSpeedHour" />
    <result column="sell_out_hour" jdbcType="VARCHAR" property="sellOutHour" />
    <result column="date_flag" jdbcType="INTEGER" property="dateFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `sku`, `warehouse_no`, `uv`, `click_conversion`, `sales_volume`, `sell_out`, 
    `sales_speed_hour`, `sell_out_hour`, `date_flag`, `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from dynamic_price_statistics
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from dynamic_price_statistics
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.offline.DynamicPriceStatistics">
    insert into dynamic_price_statistics (`id`, `sku`, `warehouse_no`, 
      `uv`, `click_conversion`, `sales_volume`, 
      `sell_out`, `sales_speed_hour`, `sell_out_hour`, 
      `date_flag`, `create_time`, `update_time`
      )
    values (#{id,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{warehouseNo,jdbcType=INTEGER}, 
      #{uv,jdbcType=INTEGER}, #{clickConversion,jdbcType=DECIMAL}, #{salesVolume,jdbcType=INTEGER}, 
      #{sellOut,jdbcType=BIT}, #{salesSpeedHour,jdbcType=VARCHAR}, #{sellOutHour,jdbcType=VARCHAR}, 
      #{dateFlag,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.offline.DynamicPriceStatistics">
    insert into dynamic_price_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="sku != null">
        `sku`,
      </if>
      <if test="warehouseNo != null">
        `warehouse_no`,
      </if>
      <if test="uv != null">
        `uv`,
      </if>
      <if test="clickConversion != null">
        `click_conversion`,
      </if>
      <if test="salesVolume != null">
        `sales_volume`,
      </if>
      <if test="sellOut != null">
        `sell_out`,
      </if>
      <if test="salesSpeedHour != null">
        `sales_speed_hour`,
      </if>
      <if test="sellOutHour != null">
        `sell_out_hour`,
      </if>
      <if test="dateFlag != null">
        `date_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="uv != null">
        #{uv,jdbcType=INTEGER},
      </if>
      <if test="clickConversion != null">
        #{clickConversion,jdbcType=DECIMAL},
      </if>
      <if test="salesVolume != null">
        #{salesVolume,jdbcType=INTEGER},
      </if>
      <if test="sellOut != null">
        #{sellOut,jdbcType=BIT},
      </if>
      <if test="salesSpeedHour != null">
        #{salesSpeedHour,jdbcType=VARCHAR},
      </if>
      <if test="sellOutHour != null">
        #{sellOutHour,jdbcType=VARCHAR},
      </if>
      <if test="dateFlag != null">
        #{dateFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.offline.DynamicPriceStatistics">
    update dynamic_price_statistics
    <set>
      <if test="sku != null">
        `sku` = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        `warehouse_no` = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="uv != null">
        `uv` = #{uv,jdbcType=INTEGER},
      </if>
      <if test="clickConversion != null">
        `click_conversion` = #{clickConversion,jdbcType=DECIMAL},
      </if>
      <if test="salesVolume != null">
        `sales_volume` = #{salesVolume,jdbcType=INTEGER},
      </if>
      <if test="sellOut != null">
        `sell_out` = #{sellOut,jdbcType=BIT},
      </if>
      <if test="salesSpeedHour != null">
        `sales_speed_hour` = #{salesSpeedHour,jdbcType=VARCHAR},
      </if>
      <if test="sellOutHour != null">
        `sell_out_hour` = #{sellOutHour,jdbcType=VARCHAR},
      </if>
      <if test="dateFlag != null">
        `date_flag` = #{dateFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.offline.DynamicPriceStatistics">
    update dynamic_price_statistics
    set `sku` = #{sku,jdbcType=VARCHAR},
      `warehouse_no` = #{warehouseNo,jdbcType=INTEGER},
      `uv` = #{uv,jdbcType=INTEGER},
      `click_conversion` = #{clickConversion,jdbcType=DECIMAL},
      `sales_volume` = #{salesVolume,jdbcType=INTEGER},
      `sell_out` = #{sellOut,jdbcType=BIT},
      `sales_speed_hour` = #{salesSpeedHour,jdbcType=VARCHAR},
      `sell_out_hour` = #{sellOutHour,jdbcType=VARCHAR},
      `date_flag` = #{dateFlag,jdbcType=INTEGER},
      `create_time` = #{createTime,jdbcType=TIMESTAMP},
      `update_time` = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByQuery" parameterType="net.summerfarm.model.DTO.DynamicPriceStatisticsQueryDTO"
    resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
        from dynamic_price_statistics
    where `sku` = #{sku}
    and `warehouse_no` = #{warehouseNo}
    and `date_flag` &lt;= #{dateFlag}
  </select>
</mapper>