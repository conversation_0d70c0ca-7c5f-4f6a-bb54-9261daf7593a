<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.ReplenishmentPlanBaseInfoMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.offline.ReplenishmentPlanBaseInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="pd_id" jdbcType="BIGINT" property="pdId" />
    <result column="spu_no" jdbcType="VARCHAR" property="spuNo" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="small_sku" jdbcType="VARCHAR" property="smallSku" />
    <result column="rates" jdbcType="VARCHAR" property="rates" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sku, pd_id, spu_no, pd_name, brand, warehouse_no, small_sku, rates
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from replenishment_plan_base_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectList" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from replenishment_plan_base_info
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from replenishment_plan_base_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.offline.ReplenishmentPlanBaseInfo">
    insert into replenishment_plan_base_info (id, sku, pd_id, 
      spu_no, pd_name, brand, warehouse_no,
      small_sku, rates
      )
    values (#{id,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{pdId,jdbcType=BIGINT}, 
      #{spuNo,jdbcType=VARCHAR}, #{pdName,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{warehouseNo,jdbcType=INTEGER},
      #{smallSku,jdbcType=VARCHAR}, #{rates,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.offline.ReplenishmentPlanBaseInfo">
    insert into replenishment_plan_base_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="spuNo != null">
        spu_no,
      </if>
      <if test="pdName != null">
        pd_name,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="smallSku != null">
        small_sku,
      </if>
      <if test="rates != null">
        rates,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=BIGINT},
      </if>
      <if test="spuNo != null">
        #{spuNo,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="smallSku != null">
        #{smallSku,jdbcType=VARCHAR},
      </if>
      <if test="rates != null">
        #{rates,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.offline.ReplenishmentPlanBaseInfo">
    update replenishment_plan_base_info
    <set>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=BIGINT},
      </if>
      <if test="spuNo != null">
        spu_no = #{spuNo,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        pd_name = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="smallSku != null">
        small_sku = #{smallSku,jdbcType=VARCHAR},
      </if>
      <if test="rates != null">
        rates = #{rates,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.offline.ReplenishmentPlanBaseInfo">
    update replenishment_plan_base_info
    set sku = #{sku,jdbcType=VARCHAR},
      pd_id = #{pdId,jdbcType=BIGINT},
      spu_no = #{spuNo,jdbcType=VARCHAR},
      pd_name = #{pdName,jdbcType=VARCHAR},
        brand = #{brand,jdbcType=VARCHAR},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      small_sku = #{smallSku,jdbcType=VARCHAR},
      rates = #{rates,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>