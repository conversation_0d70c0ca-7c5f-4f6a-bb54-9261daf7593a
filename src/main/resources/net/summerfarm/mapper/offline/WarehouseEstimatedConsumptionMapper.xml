<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.WarehouseEstimatedConsumptionMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.offline.WarehouseEstimatedConsumption">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pd_id" jdbcType="INTEGER" property="pdId" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="view_date" jdbcType="TIMESTAMP" property="viewDate" />
    <result column="forecast_sales" jdbcType="DECIMAL" property="forecastSales" />
    <result column="forecast_transfer_out" jdbcType="DECIMAL" property="forecastTransferOut" />
    <result column="forecast_consumption" jdbcType="DECIMAL" property="forecastConsumption" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pd_id, sku_id, warehouse_no, view_date, forecast_sales,forecast_transfer_out,forecast_consumption
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from warehouse_estimated_consumption
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectList" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from warehouse_estimated_consumption
      <where>
        <if test="skuId != null">
          and sku_id = #{skuId}
        </if>
        <if test="warehouseNo != null">
          and warehouse_no = #{warehouseNo}
        </if>
        <if test="startDate != null">
          and view_date >= #{startDate}
        </if>
        <if test="endDate != null">
          and view_date <![CDATA[<]]> #{endDate}
        </if>
      </where>
        order by view_date asc
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from warehouse_estimated_consumption
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.offline.WarehouseEstimatedConsumption">
    insert into warehouse_estimated_consumption (id, pd_id, sku_id, 
      warehouse_no, view_date, forecast_sales,
      forecast_transfer_out, forecast_consumption)
    values (#{id,jdbcType=BIGINT}, #{pdId,jdbcType=INTEGER}, #{skuId,jdbcType=VARCHAR}, 
      #{warehouseNo,jdbcType=INTEGER}, #{viewDate,jdbcType=TIMESTAMP}, #{forecastSales,jdbcType=INTEGER},
      #{forecastTransferOut,jdbcType=INTEGER}, #{forecastConsumption,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.offline.WarehouseEstimatedConsumption">
    insert into warehouse_estimated_consumption
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="viewDate != null">
        view_date,
      </if>
      <if test="forecastSales != null">
        forecast_sales,
      </if>
      <if test="forecastTransferOut != null">
        forecast_transfer_out,
      </if>
      <if test="forecastConsumption != null">
        forecast_consumption,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="viewDate != null">
        #{viewDate,jdbcType=TIMESTAMP},
      </if>
      <if test="forecastSales != null">
        #{forecastSales,jdbcType=INTEGER},
      </if>
      <if test="forecastTransferOut != null">
        #{forecastTransferOut,jdbcType=INTEGER},
      </if>
      <if test="forecastConsumption != null">
        #{forecastConsumption,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.offline.WarehouseEstimatedConsumption">
    update warehouse_estimated_consumption
    <set>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="viewDate != null">
        view_date = #{viewDate,jdbcType=TIMESTAMP},
      </if>
      <if test="forecastSales != null">
        forecast_sales = #{forecastSales,jdbcType=INTEGER},
      </if>
      <if test="forecastTransferOut != null">
        forecast_transfer_out = #{forecastTransferOut,jdbcType=INTEGER},
      </if>
      <if test="forecastConsumption != null">
        forecast_consumption = #{forecastConsumption,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.offline.WarehouseEstimatedConsumption">
    update warehouse_estimated_consumption
    set pd_id = #{pdId,jdbcType=INTEGER},
      sku_id = #{skuId,jdbcType=VARCHAR},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      view_date = #{viewDate,jdbcType=TIMESTAMP},
      forecast_sales = #{forecastSales,jdbcType=INTEGER},
      forecast_transfer_out = #{forecastTransferOut,jdbcType=INTEGER},
      forecast_consumption = #{forecastConsumption,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectSumSaleQuantity"  resultType="net.summerfarm.model.domain.offline.WarehouseEstimatedConsumption" >
        select sku_id skuId,SUM(IFNULL(forecast_sales,0)) forecastSales
        from  warehouse_estimated_consumption
        <where>
          warehouse_no= #{warehouseNo}  and view_date <![CDATA[<]]> #{endDate} and view_date >= #{startDate}
          <if test="skuIds != null and skuIds.size != 0">
            and sku_id in
            <foreach collection="skuIds" open="(" close=")" separator="," item="it">
              #{it}
            </foreach>
          </if>
        </where>
         group by sku_id
    </select>
</mapper>