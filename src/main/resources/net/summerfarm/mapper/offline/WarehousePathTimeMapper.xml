<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.WarehousePathTimeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.offline.WarehousePathTime">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="in_warehouse_no" jdbcType="INTEGER" property="inWarehouseNo" />
    <result column="out_warehouse_no" jdbcType="INTEGER" property="outWarehouseNo" />
    <result column="cost_time" jdbcType="INTEGER" property="costTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, in_warehouse_no, out_warehouse_no, cost_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from warehouse_path_time
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectBySelective" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from warehouse_path_time
      <where>
        <if test="id != null">
          and id = #{id}
        </if>
        <if test="inWarehouseNo != null">
          and in_warehouse_no = #{inWarehouseNo}
        </if>
        <if test="outWarehouseNo != null">
          and out_warehouse_no = #{outWarehouseNo}
        </if>
        <if test="inWarehouseNos != null and inWarehouseNos.size != 0">
          and in_warehouse_no in
          <foreach collection="inWarehouseNos" open="(" close=")" separator="," item="inWarehouseNo">
            #{inWarehouseNo}
          </foreach>
        </if>
        <if test="outWarehouseNos != null and outWarehouseNos.size != 0">
          and out_warehouse_no in
          <foreach collection="outWarehouseNos" open="(" close=")" separator="," item="outWarehouseNo">
            #{outWarehouseNo}
          </foreach>
        </if>
        <if test="costTime != null">
          and cost_time = #{costTime}
        </if>
      </where>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from warehouse_path_time
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.offline.WarehousePathTime">
    insert into warehouse_path_time (id, in_warehouse_no, out_warehouse_no, 
      cost_time)
    values (#{id,jdbcType=BIGINT}, #{inWarehouseNo,jdbcType=INTEGER}, #{outWarehouseNo,jdbcType=INTEGER}, 
      #{costTime,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.offline.WarehousePathTime">
    insert into warehouse_path_time
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="inWarehouseNo != null">
        in_warehouse_no,
      </if>
      <if test="outWarehouseNo != null">
        out_warehouse_no,
      </if>
      <if test="costTime != null">
        cost_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="inWarehouseNo != null">
        #{inWarehouseNo,jdbcType=INTEGER},
      </if>
      <if test="outWarehouseNo != null">
        #{outWarehouseNo,jdbcType=INTEGER},
      </if>
      <if test="costTime != null">
        #{costTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.offline.WarehousePathTime">
    update warehouse_path_time
    <set>
      <if test="inWarehouseNo != null">
        in_warehouse_no = #{inWarehouseNo,jdbcType=INTEGER},
      </if>
      <if test="outWarehouseNo != null">
        out_warehouse_no = #{outWarehouseNo,jdbcType=INTEGER},
      </if>
      <if test="costTime != null">
        cost_time = #{costTime,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.offline.WarehousePathTime">
    update warehouse_path_time
    set in_warehouse_no = #{inWarehouseNo,jdbcType=INTEGER},
      out_warehouse_no = #{outWarehouseNo,jdbcType=INTEGER},
      cost_time = #{costTime,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>