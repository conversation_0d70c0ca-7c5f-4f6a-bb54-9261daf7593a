<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.TemporaryInsuranceRiskMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.offline.TemporaryInsuranceRisk">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="large_area_no" jdbcType="INTEGER" property="largeAreaNo" />
    <result column="date_flag" jdbcType="INTEGER" property="dateFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, sku, warehouse_no, area_no, large_area_no, date_flag, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from temporary_insurance_risk
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByEntity" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from temporary_insurance_risk
    <where>
      <if test="dateFlag != null">
        and date_flag = #{dateFlag,jdbcType=INTEGER}
      </if>
    </where>
  </select>
</mapper>