<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.StockDashboardHistoryMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.offline.StockDashboardHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pd_id" jdbcType="INTEGER" property="pdId" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="view_date" jdbcType="TIMESTAMP" property="viewDate" />
    <result column="consumption" jdbcType="INTEGER" property="consumption" />
    <result column="sales_quantity" jdbcType="INTEGER" property="salesQuantity" />
    <result column="transfer_out_quantity" jdbcType="INTEGER" property="transferOutQuantity" />
    <result column="init_quantity" jdbcType="INTEGER" property="initQuantity" />
    <result column="enabled_quantity" jdbcType="INTEGER" property="enabledQuantity" />
    <result column="on_way_quantity" jdbcType="INTEGER" property="onWayQuantity" />
    <result column="transfer_in_quantity" jdbcType="INTEGER" property="transferInQuantity" />
    <result column="terminal_enabled_quantity" jdbcType="INTEGER" property="terminalEnabledQuantity" />
    <result column="on_way_order_quantity"  jdbcType="INTEGER" property="onWayOrderQuantity"/>
    <result column="order_sales_quantity"  jdbcType="DECIMAL" property="orderSalesQuantity"/>
    <result column="timing_delivery_out_quantity"  jdbcType="DECIMAL" property="timingDeliveryOutQuantity"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, pd_id, sku_id, warehouse_no, view_date, consumption, sales_quantity, transfer_out_quantity, 
    init_quantity, enabled_quantity, on_way_quantity, transfer_in_quantity,terminal_enabled_quantity,on_way_order_quantity,order_sales_quantity,timing_delivery_out_quantity
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_dashboard_history
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectList" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from stock_dashboard_history
      <where>
        <if test="skuId != null">
          and sku_id = #{skuId}
        </if>
        <if test="warehouseNo != null">
          and warehouse_no = #{warehouseNo}
        </if>
        <if test="startDate != null">
          and view_date >= #{startDate}
        </if>
        <if test="endDate != null">
          and view_date <![CDATA[<]]> #{endDate}
        </if>
        <if test="skuIds != null and skuIds.size() != 0">
          and sku_id in
          <foreach collection="skuIds" item="skuId" open=" (" close=")" separator=",">
            #{skuId}
          </foreach>
        </if>
      </where>
      order by view_date asc
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_dashboard_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.offline.StockDashboardHistory">
    insert into stock_dashboard_history (id, pd_id, sku_id, 
      warehouse_no, view_date, consumption, 
      sales_quantity, transfer_out_quantity, init_quantity, 
      enabled_quantity, on_way_quantity, transfer_in_quantity, timing_delivery_out_quantity
      )
    values (#{id,jdbcType=BIGINT}, #{pdId,jdbcType=INTEGER}, #{skuId,jdbcType=VARCHAR}, 
      #{warehouseNo,jdbcType=INTEGER}, #{viewDate,jdbcType=TIMESTAMP}, #{consumption,jdbcType=INTEGER}, 
      #{salesQuantity,jdbcType=INTEGER}, #{transferOutQuantity,jdbcType=INTEGER}, #{initQuantity,jdbcType=INTEGER}, 
      #{enabledQuantity,jdbcType=INTEGER}, #{onWayQuantity,jdbcType=INTEGER}, #{transferInQuantity,jdbcType=INTEGER},#{timingDeliveryOutQuantity,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.offline.StockDashboardHistory">
    insert into stock_dashboard_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="viewDate != null">
        view_date,
      </if>
      <if test="consumption != null">
        consumption,
      </if>
      <if test="salesQuantity != null">
        sales_quantity,
      </if>
      <if test="transferOutQuantity != null">
        transfer_out_quantity,
      </if>
      <if test="initQuantity != null">
        init_quantity,
      </if>
      <if test="enabledQuantity != null">
        enabled_quantity,
      </if>
      <if test="onWayQuantity != null">
        on_way_quantity,
      </if>
      <if test="transferInQuantity != null">
        transfer_in_quantity,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="viewDate != null">
        #{viewDate,jdbcType=TIMESTAMP},
      </if>
      <if test="consumption != null">
        #{consumption,jdbcType=INTEGER},
      </if>
      <if test="salesQuantity != null">
        #{salesQuantity,jdbcType=INTEGER},
      </if>
      <if test="transferOutQuantity != null">
        #{transferOutQuantity,jdbcType=INTEGER},
      </if>
      <if test="initQuantity != null">
        #{initQuantity,jdbcType=INTEGER},
      </if>
      <if test="enabledQuantity != null">
        #{enabledQuantity,jdbcType=INTEGER},
      </if>
      <if test="onWayQuantity != null">
        #{onWayQuantity,jdbcType=INTEGER},
      </if>
      <if test="transferInQuantity != null">
        #{transferInQuantity,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.offline.StockDashboardHistory">
    update stock_dashboard_history
    <set>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="viewDate != null">
        view_date = #{viewDate,jdbcType=TIMESTAMP},
      </if>
      <if test="consumption != null">
        consumption = #{consumption,jdbcType=INTEGER},
      </if>
      <if test="salesQuantity != null">
        sales_quantity = #{salesQuantity,jdbcType=INTEGER},
      </if>
      <if test="transferOutQuantity != null">
        transfer_out_quantity = #{transferOutQuantity,jdbcType=INTEGER},
      </if>
      <if test="initQuantity != null">
        init_quantity = #{initQuantity,jdbcType=INTEGER},
      </if>
      <if test="enabledQuantity != null">
        enabled_quantity = #{enabledQuantity,jdbcType=INTEGER},
      </if>
      <if test="onWayQuantity != null">
        on_way_quantity = #{onWayQuantity,jdbcType=INTEGER},
      </if>
      <if test="transferInQuantity != null">
        transfer_in_quantity = #{transferInQuantity,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.offline.StockDashboardHistory">
    update stock_dashboard_history
    set pd_id = #{pdId,jdbcType=INTEGER},
      sku_id = #{skuId,jdbcType=VARCHAR},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      view_date = #{viewDate,jdbcType=TIMESTAMP},
      consumption = #{consumption,jdbcType=INTEGER},
      sales_quantity = #{salesQuantity,jdbcType=INTEGER},
      transfer_out_quantity = #{transferOutQuantity,jdbcType=INTEGER},
      init_quantity = #{initQuantity,jdbcType=INTEGER},
      enabled_quantity = #{enabledQuantity,jdbcType=INTEGER},
      on_way_quantity = #{onWayQuantity,jdbcType=INTEGER},
      transfer_in_quantity = #{transferInQuantity,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectHistorySaleQuantity"  resultType="net.summerfarm.model.domain.offline.StockDashboardHistory" >
    select sku_id skuId,SUM(IFNULL(sales_quantity,0)) salesQuantity
    from  stock_dashboard_history
    <where>
      warehouse_no= #{warehouseNo}  and view_date <![CDATA[<=]]> #{endDate} and view_date >= #{startDate}
      <if test="skuIds != null and skuIds.size != 0">
        and sku_id in
        <foreach collection="skuIds" open="(" close=")" separator="," item="it">
          #{it}
        </foreach>
      </if>
    </where>
    group by sku_id
  </select>
</mapper>