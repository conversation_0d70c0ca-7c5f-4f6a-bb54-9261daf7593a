<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.offline.SkuWarehouseSellLabelMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.offline.SkuWarehouseSellLabel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="pd_id" jdbcType="BIGINT" property="pdId" />
    <result column="spu_no" jdbcType="VARCHAR" property="spuNo" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="level_label" jdbcType="VARCHAR" property="levelLabel" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sku, pd_id, spu_no, pd_name, warehouse_no, level_label
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sku_warehouse_sell_label
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectBySkuWarehouseNo" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from sku_warehouse_sell_label
      where sku = #{sku} and warehouse_no = #{warehouseNo}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sku_warehouse_sell_label
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.offline.SkuWarehouseSellLabel">
    insert into sku_warehouse_sell_label (id, sku, pd_id, 
      spu_no, pd_name, warehouse_no, 
      level_label
      )
    values (#{id,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{pdId,jdbcType=BIGINT}, 
      #{spuNo,jdbcType=VARCHAR}, #{pdName,jdbcType=VARCHAR}, #{warehouseNo,jdbcType=INTEGER}, 
      #{levelLabel,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.offline.SkuWarehouseSellLabel">
    insert into sku_warehouse_sell_label
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="spuNo != null">
        spu_no,
      </if>
      <if test="pdName != null">
        pd_name,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="levelLabel != null">
        level_label,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=BIGINT},
      </if>
      <if test="spuNo != null">
        #{spuNo,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="levelLabel != null">
        #{levelLabel,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.offline.SkuWarehouseSellLabel">
    update sku_warehouse_sell_label
    <set>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=BIGINT},
      </if>
      <if test="spuNo != null">
        spu_no = #{spuNo,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        pd_name = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="levelLabel != null">
        level_label = #{levelLabel,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.offline.SkuWarehouseSellLabel">
    update sku_warehouse_sell_label
    set sku = #{sku,jdbcType=VARCHAR},
      pd_id = #{pdId,jdbcType=BIGINT},
      spu_no = #{spuNo,jdbcType=VARCHAR},
      pd_name = #{pdName,jdbcType=VARCHAR},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      level_label = #{levelLabel,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>