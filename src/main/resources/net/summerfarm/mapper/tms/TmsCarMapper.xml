<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.tms.TmsCarMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.tms.TmsCar">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="car_number" jdbcType="VARCHAR" property="carNumber" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="volume" jdbcType="DECIMAL" property="volume" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
    <result column="car_photos" jdbcType="VARCHAR" property="carPhotos" />
    <result column="driver_photos" jdbcType="VARCHAR" property="driverPhotos" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="admin_id" jdbcType="VARCHAR" property="adminId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, car_number, type, volume, weight, car_photos, driver_photos, status, admin_id, 
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tms_car
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tms_car
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.tms.TmsCar">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into tms_car (id, car_number, type, 
      volume, weight, car_photos, 
      driver_photos, status, admin_id, 
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{carNumber,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{volume,jdbcType=DECIMAL}, #{weight,jdbcType=DECIMAL}, #{carPhotos,jdbcType=VARCHAR}, 
      #{driverPhotos,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{adminId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.tms.TmsCar">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into tms_car
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="carNumber != null">
        car_number,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="volume != null">
        volume,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="carPhotos != null">
        car_photos,
      </if>
      <if test="driverPhotos != null">
        driver_photos,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carNumber != null">
        #{carNumber,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="volume != null">
        #{volume,jdbcType=DECIMAL},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=DECIMAL},
      </if>
      <if test="carPhotos != null">
        #{carPhotos,jdbcType=VARCHAR},
      </if>
      <if test="driverPhotos != null">
        #{driverPhotos,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.tms.TmsCar">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tms_car
    <set>
      <if test="carNumber != null">
        car_number = #{carNumber,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="volume != null">
        volume = #{volume,jdbcType=DECIMAL},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=DECIMAL},
      </if>
      <if test="carPhotos != null">
        car_photos = #{carPhotos,jdbcType=VARCHAR},
      </if>
      <if test="driverPhotos != null">
        driver_photos = #{driverPhotos,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.tms.TmsCar">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tms_car
    set car_number = #{carNumber,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      volume = #{volume,jdbcType=DECIMAL},
      weight = #{weight,jdbcType=DECIMAL},
      car_photos = #{carPhotos,jdbcType=VARCHAR},
      driver_photos = #{driverPhotos,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      admin_id = #{adminId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>