<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.tms.CarrierInvoiceMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.tms.CarrierInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="carrier_id" jdbcType="BIGINT" property="carrierId" />
    <result column="invoice_head" jdbcType="VARCHAR" property="invoiceHead" />
    <result column="tax_no" jdbcType="VARCHAR" property="taxNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, carrier_id, invoice_head, tax_no, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from carrier_invoice
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from carrier_invoice
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.tms.CarrierInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into carrier_invoice (id, carrier_id, invoice_head, 
      tax_no, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{carrierId,jdbcType=BIGINT}, #{invoiceHead,jdbcType=VARCHAR}, 
      #{taxNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.tms.CarrierInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into carrier_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="carrierId != null">
        carrier_id,
      </if>
      <if test="invoiceHead != null">
        invoice_head,
      </if>
      <if test="taxNo != null">
        tax_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="carrierId != null">
        #{carrierId,jdbcType=BIGINT},
      </if>
      <if test="invoiceHead != null">
        #{invoiceHead,jdbcType=VARCHAR},
      </if>
      <if test="taxNo != null">
        #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.tms.CarrierInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update carrier_invoice
    <set>
      <if test="carrierId != null">
        carrier_id = #{carrierId,jdbcType=BIGINT},
      </if>
      <if test="invoiceHead != null">
        invoice_head = #{invoiceHead,jdbcType=VARCHAR},
      </if>
      <if test="taxNo != null">
        tax_no = #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.tms.CarrierInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update carrier_invoice
    set carrier_id = #{carrierId,jdbcType=BIGINT},
      invoice_head = #{invoiceHead,jdbcType=VARCHAR},
      tax_no = #{taxNo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByCarrierId">
    delete from carrier_invoice where carrier_id = #{id}
  </delete>

  <select id="selectByCarrierId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM carrier_invoice WHERE carrier_id = #{id}
  </select>
</mapper>