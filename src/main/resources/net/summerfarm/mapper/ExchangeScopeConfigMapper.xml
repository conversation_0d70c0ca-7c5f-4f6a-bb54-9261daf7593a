<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.ExchangeScopeConfigMapper">

    <update id="updateExchangeScopeConfig" parameterType="net.summerfarm.model.domain.ExchangeScopeConfig">
        update exchange_scope_config
        <set>
            <if test="scopeId != null">
                scope_id = #{scopeId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <delete id="deleteById">
        delete from exchange_scope_config where id = #{id}
    </delete>
    <select id="selectById" resultType="net.summerfarm.model.domain.ExchangeScopeConfig">
        select id,base_info_id baseInfoId,scope_id scopeId,status,type,creator,create_time createTime
        from exchange_scope_config where id = #{id}
    </select>
    <select id="selectByBaseInfoIdAndScopeId" resultType="net.summerfarm.model.vo.ExchangeScopeConfigVO">
        select id,base_info_id baseInfoId,scope_id scopeId,status,type,creator,create_time createTime
        from exchange_scope_config where base_info_id=#{baseInfoId} and scope_id= #{scopeId}
    </select>
    <select id="selectByBaseInfoId" resultType="net.summerfarm.model.vo.ExchangeScopeConfigVO">
        select id,base_info_id baseInfoId,scope_id scopeId,status,type,creator,create_time createTime
        from exchange_scope_config where base_info_id=#{baseInfoId}
    </select>
    <insert id="insertExchangeScopeConfig" parameterType="net.summerfarm.model.domain.ExchangeScopeConfig" useGeneratedKeys="true" keyProperty="id">
        insert into exchange_scope_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="baseInfoId != null">
                base_info_id,
            </if>
            <if test="scopeId != null">
                `scope_id`,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="baseInfoId != null">
                #{baseInfoId},
            </if>
            <if test="scopeId != null">
                #{scopeId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>
</mapper>