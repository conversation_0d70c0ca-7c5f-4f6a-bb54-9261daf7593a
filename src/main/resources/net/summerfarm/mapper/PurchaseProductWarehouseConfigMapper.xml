<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.PurchaseProductWarehouseConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.purchase.PurchaseProductWarehouseConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pd_id" jdbcType="BIGINT" property="pdId" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="purchase_type" jdbcType="INTEGER" property="purchaseType" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="admin_name" jdbcType="VARCHAR" property="adminName" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="planner_id" jdbcType="INTEGER" property="plannerId" />
    <result column="planner_name" jdbcType="VARCHAR" property="plannerName" />
    <result column="safe_water_level" jdbcType="INTEGER" property="safeWaterLevel"/>
    <result column="backlog_day" jdbcType="INTEGER" property="backlogDay"/>

  </resultMap>
  <sql id="Base_Column_List">
    id, pd_id, warehouse_no, purchase_type, admin_id, create_time, updater, update_time, 
    admin_name, creator,planner_id,planner_name,safe_water_level,backlog_day
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_product_warehouse_config
    where id = #{id,jdbcType=BIGINT}
  </select>


  <select id="queryProductBaseConfigs" resultType="net.summerfarm.model.DTO.purchase.ProductBaseInfoDTO">
    select pwc.id id,pwc.pd_id pdId,pd.pd_no pdNo,pd.pd_name pdName,pd.picture_path picturePath
    from purchase_product_warehouse_config pwc
           left join products pd on pwc.pd_id = pd.pd_id
           left join purchase_product_warehouse_supplier_config sc
                     on pwc.pd_id = sc.pd_id and pwc.warehouse_no = sc.warehouse_no
    <where>
      <!-- 过滤saas自营 -->
      pd.create_type != 3
      <if test="pdId != null">
        AND pd.pd_id = #{pdId}
      </if>
      <if test="pdNo != null">
        AND pd.pd_no = #{pdNo}
      </if>
      <if test="spuStatus != null">
        AND pd.outdated = #{spuStatus}
      </if>
      <if test="pdName != null">
        AND pd.pd_name like CONCAT(#{pdName},'%')
      </if>
      <if test="warehouseNo != null">
        and sc.warehouse_no = #{warehouseNo}
      </if>
      <if test="supplierId != null">
        and sc.supplier_id =  #{supplierId}
      </if>
      <if test="warehouseNo != null">
        and sc.warehouse_no = #{warehouseNo}
      </if>
      <if test="adminId != null">
        and pwc.admin_id = #{adminId}
      </if>
      <if test="plannerId != null">
        and pwc.planner_id = #{plannerId}
      </if>
      <if test="pdIds !=null and pdIds.size>0">
        and pwc.pd_id in
        <foreach collection="pdIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
    group by pwc.pd_id,pd.pd_name,pd.picture_path
  </select>
  <select id="queryAdminNames" resultType="net.summerfarm.model.DTO.purchase.ProductAdminsDTO">
    select pd_id pdId,GROUP_CONCAT(distinct admin_name) adminNames,GROUP_CONCAT(distinct planner_name) plannerNames
      from purchase_product_warehouse_config
      where pd_id in
        <foreach collection="pdIds" open="(" close=")" separator="," item="pdId">
            #{pdId}
        </foreach>
    group by pd_id
  </select>


  <select id="queryProductWarehouseConfig"
          resultType="net.summerfarm.model.DTO.purchase.ProductWarehouseConfigDTO">
select pwc.id,pwc.pd_id pdId,pd.pd_name pdName,pwc.warehouse_no warehouseNo,wsc.warehouse_name warehouseName
    ,pwc.purchase_type purchaseType,pwc.admin_id adminId,pwc.admin_name adminName,pwc.planner_id plannerId,
       pwc.planner_name plannerName
    from purchase_product_warehouse_config pwc
        left join products pd on pd.pd_id = pwc.pd_id
        left join warehouse_storage_center wsc on pwc.warehouse_no = wsc.warehouse_no
    where pd.pd_id = #{pdId}
   order by pwc.pd_id,pwc.warehouse_no
  </select>
    <select id="selectPdIdByAdminAndWarehouse" resultType="java.lang.Long">
      select distinct pd_id from purchase_product_warehouse_config where admin_id = #{adminId} and warehouse_no = #{warehouseNo}
    </select>
  <select id="selectAdminNameByPdIdAndWarehouse" resultType="java.lang.String">
    select admin_name from purchase_product_warehouse_config where pd_id = #{pdId} and warehouse_no = #{outStore} limit 1
  </select>
    <select id="selectExistWareHouse" resultType="java.lang.Integer">
      select warehouse_no from purchase_product_warehouse_config where pd_id = #{pdId} and warehouse_no in
        <foreach collection="warehouseNos" open="(" close=")" separator="," item="warehouseNo">
          #{warehouseNo}
        </foreach>
    </select>
  <select id="countNum" resultType="java.lang.Long">
    select count(*) from purchase_product_warehouse_config;
  </select>
    <select id="groupWarehouseNo" resultType="java.lang.Integer">
      select warehouse_no from purchase_product_warehouse_config group by warehouse_no
    </select>
    <select id="selectConfigByWarehouseLimit"
            resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from purchase_product_warehouse_config
      where warehouse_no = #{warehouseNo} limit #{startNum},#{pageSize}

    </select>
  <select id="groupPdIdLimit" resultType="java.lang.Long">
    select
    pd_id
    from purchase_product_warehouse_config group by pd_id
        limit #{startNum},#{pageSize}
  </select>
    <select id="selectByQueryInput"
            resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from purchase_product_warehouse_config
      <where>
        <if test = "id != null">
          and id = #{id}
        </if>
        <if test = "ids != null and ids.size > 0">
          and id in
          <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        <if test = "pdId != null">
          and pd_id = #{pdId}
        </if>
        <if test = "pdIds != null and pdIds.size > 0">
          and pd_id in
          <foreach collection="pdIds" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        <if test = "warehouseNo != null">
          and warehouse_no = #{warehouseNo}
        </if>
        <if test = "warehouseNos != null and warehouseNos.size > 0">
          and warehouse_no in
          <foreach collection="warehouseNos" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        <if test = "purchaseType != null">
          and purchase_type = #{purchaseType}
        </if>
        <if test = "purchaseTypes != null and purchaseTypes.size > 0">
          and purchase_type in
          <foreach collection="purchaseTypes" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        <if test = "adminId != null">
          and admin_id = #{adminId}
        </if>
        <if test = "adminIds != null and adminIds.size > 0">
          and admin_id in
          <foreach collection="adminIds" open="(" close=")" separator="," item="item">
            #{item}
          </foreach>
        </if>
        <if test = "adminName != null">
          and admin_name = #{adminName}
        </if>
        <if test = "adminNameStartWith != null">
          and admin_name like CONCAT(#{adminNameStartWith},'%')
        </if>
        <if test = "adminNameLike != null">
          and admin_name = CONCAT('%',#{adminNameLike},'%')
        </if>
      </where>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_product_warehouse_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <delete id="deleteByPdIdAndWarehouseNo">
      delete from purchase_product_warehouse_config where pd_id = #{pdId} and warehouse_no in
      <foreach collection="warehouseNos" open="(" close=")" separator="," item="warehouseNo">
        #{warehouseNo}
      </foreach>
    </delete>
  <delete id="deleteByWarehouseNos">
    delete from purchase_product_warehouse_config where warehouse_no in
        <foreach collection="warehouseNos" open="(" close=")" separator="," item="warehouseNo">
          #{warehouseNo}
        </foreach>
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.purchase.PurchaseProductWarehouseConfig">
    insert into purchase_product_warehouse_config (id, pd_id, warehouse_no, 
      purchase_type, admin_id, create_time, 
      updater, update_time, admin_name, 
      creator)
    values (#{id,jdbcType=BIGINT}, #{pdId,jdbcType=BIGINT}, #{warehouseNo,jdbcType=INTEGER}, 
      #{purchaseType,jdbcType=INTEGER}, #{adminId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{adminName,jdbcType=VARCHAR}, 
      #{creator,jdbcType=INTEGER})
  </insert>
  <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
    insert into purchase_product_warehouse_config(pd_id, warehouse_no,
    purchase_type, admin_id, create_time,
    updater, update_time, admin_name,
    creator,planner_id,planner_name,safe_water_level,backlog_day)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.pdId}, #{entity.warehouseNo}, #{entity.purchaseType},
      #{entity.adminId}, #{entity.createTime}, #{entity.updater},
      #{entity.updateTime}, #{entity.adminName}, #{entity.creator},#{entity.plannerId},#{entity.plannerName},#{entity.safeWaterLevel},#{entity.backlogDay})
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.purchase.PurchaseProductWarehouseConfig">
    insert into purchase_product_warehouse_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="purchaseType != null">
        purchase_type,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="adminName != null">
        admin_name,
      </if>
      <if test="creator != null">
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=BIGINT},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="purchaseType != null">
        #{purchaseType,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adminName != null">
        #{adminName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.purchase.PurchaseProductWarehouseConfig">
    update purchase_product_warehouse_config
    <set>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=BIGINT},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="purchaseType != null">
        purchase_type = #{purchaseType,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adminName != null">
        admin_name = #{adminName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="safeWaterLevel != null">
        safe_water_level = #{safeWaterLevel,jdbcType=INTEGER},
      </if>
      <if test="backlogDay != null">
        backlog_day = #{backlogDay},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPdIdsAndWarehouseNos" parameterType="net.summerfarm.model.input.purchase.ProductWarehouseConfigUpdate">
    update purchase_product_warehouse_config
    <set>
      <if test="productWarehouseConfigUpdate.adminId != null">
        admin_id = #{productWarehouseConfigUpdate.adminId},
      </if>
      <if test="productWarehouseConfigUpdate.adminName != null">
        admin_name = #{productWarehouseConfigUpdate.adminName},
      </if>
      <if test="productWarehouseConfigUpdate.plannerId != null">
        planner_id = #{productWarehouseConfigUpdate.plannerId},
      </if>
      <if test="productWarehouseConfigUpdate.plannerName != null">
        planner_name = #{productWarehouseConfigUpdate.plannerName},
      </if>
      <if test="productWarehouseConfigUpdate.updater != null">
        updater = #{productWarehouseConfigUpdate.updater,jdbcType=INTEGER},
      </if>
    </set>
    WHERE pd_id IN
    <foreach collection="pdIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    <if test="warehouseNos != null and warehouseNos.size>0">
      AND warehouse_no IN
      <foreach collection="warehouseNos" item="warehouseNo" open="(" close=")" separator=",">
        #{warehouseNo}
      </foreach>
    </if>

  </update>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.purchase.PurchaseProductWarehouseConfig">
    update purchase_product_warehouse_config
    set pd_id = #{pdId,jdbcType=BIGINT},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      purchase_type = #{purchaseType,jdbcType=INTEGER},
      admin_id = #{adminId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      admin_name = #{adminName,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>