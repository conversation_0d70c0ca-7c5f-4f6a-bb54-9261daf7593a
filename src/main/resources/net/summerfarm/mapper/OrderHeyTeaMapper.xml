<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.OrderHeyTeaMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.OrderHeyTea">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="ht_order_code" jdbcType="VARCHAR" property="htOrderCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    order_no, ht_order_code, create_time, update_time
  </sql>
  <insert id="insert" parameterType="net.summerfarm.model.domain.OrderHeyTea">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_hey_tea (order_no, ht_order_code, create_time, 
      update_time)
    values (#{orderNo,jdbcType=VARCHAR}, #{htOrderCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.OrderHeyTea">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_hey_tea
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="htOrderCode != null">
        ht_order_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="htOrderCode != null">
        #{htOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <delete id="deleteByOrderNo">
    delete from order_hey_tea
    where order_no = #{orderNo}
  </delete>

  <select id="selectByHtOrderCode" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"></include>
    from order_hey_tea
    where ht_order_code = #{htOrderCode}
  </select>

  <select id="selectByOrderNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from order_hey_tea
    where order_no in
    <foreach collection="orderNos" open="(" close=")" separator="," item="orderNo">
      #{orderNo}
    </foreach>
  </select>
  <select id="countByOrderNos" resultType="java.lang.Integer">
    select count(*) from order_hey_tea
    where order_no in
    <foreach collection="orderNos" open="(" close=")" separator="," item="orderNo">
      #{orderNo}
    </foreach>
  </select>
</mapper>