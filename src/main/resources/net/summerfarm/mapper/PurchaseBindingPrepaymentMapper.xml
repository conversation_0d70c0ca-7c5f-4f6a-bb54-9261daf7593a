<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.PurchaseBindingPrepaymentMapper">

    <resultMap type="net.summerfarm.model.PurchaseBindingPrepayment" id="PurchaseBindingPrepaymentMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="purchaseAdvancedOrderId" column="purchase_advanced_order_id" jdbcType="INTEGER"/>
        <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
        <result property="advanceAmount" column="advance_amount" jdbcType="DECIMAL"/>
        <result property="purchaseNo" column="purchase_no" jdbcType="VARCHAR"/>
        <result property="bindingStatus" column="binding_status" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="PurchaseBindingPrepaymentMap">
        select id,
               purchase_advanced_order_id,
               advance_amount,
               purchase_no,
               binding_status,
               creator,
               create_time,
               update_time
        from xianmudb.purchase_binding_prepayment
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="PurchaseBindingPrepaymentMap">
        select
        id, purchase_advanced_order_id, advance_amount, purchase_no, binding_status, creator, create_time, update_time,supplier_id
        from xianmudb.purchase_binding_prepayment
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="purchaseAdvancedOrderId != null">
                and purchase_advanced_order_id = #{purchaseAdvancedOrderId}
            </if>
            <if test="advanceAmount != null">
                and advance_amount = #{advanceAmount}
            </if>
            <if test="purchaseNo != null and purchaseNo != ''">
                and purchase_no = #{purchaseNo}
            </if>
            <if test="bindingStatus != null">
                and binding_status = #{bindingStatus}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into xianmudb.purchase_binding_prepayment(purchase_advanced_order_id, advance_amount, purchase_no,
                                                         binding_status, creator, supplier_id)
        values (#{purchaseAdvancedOrderId}, #{advanceAmount}, #{purchaseNo}, #{bindingStatus}, #{creator}, #{supplierId})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into xianmudb.purchase_binding_prepayment(purchase_advanced_order_id, advance_amount, purchase_no,
        binding_status, creator, supplier_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.purchaseAdvancedOrderId}, #{entity.advanceAmount}, #{entity.purchaseNo}, #{entity.bindingStatus},
            #{entity.creator}, #{entity.supplier_id})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update xianmudb.purchase_binding_prepayment
        <set>
            <if test="purchaseAdvancedOrderId != null">
                purchase_advanced_order_id = #{purchaseAdvancedOrderId},
            </if>
            <if test="advanceAmount != null">
                advance_amount = #{advanceAmount},
            </if>
            <if test="purchaseNo != null and purchaseNo != ''">
                purchase_no = #{purchaseNo},
            </if>
            <if test="bindingStatus != null">
                binding_status = #{bindingStatus},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="queryByPurchaseAdvancedOrderId" resultType="net.summerfarm.model.vo.PurchaseBindingPrepaymentVO">
        select pbp.id,
               pbp.purchase_advanced_order_id purchaseAdvancedOrderId,
               pbp.advance_amount             advanceAmount,
               pbp.purchase_no                purchaseNo,
               pbp.binding_status             bindingStatus,
               pbp.creator,
               pbp.create_time                createTime,
               pbp.update_time                updateTime,
               p.add_time                    addTime
        from  purchase_binding_prepayment pbp
        left join purchases p on p.purchase_no = pbp.purchase_no
        where purchase_advanced_order_id = #{id}
    </select>

    <select id="queryBindAmount" resultType="java.math.BigDecimal">
        select ifnull(sum(advance_amount), 0)
        from purchase_binding_prepayment
        where purchase_no = #{purchaseNo} and supplier_id = #{supplierId} and binding_status = #{status}
    </select>

    <select id="selectByProcessDetail" parameterType="long" resultType="int">
        select s.id
        from purchase_binding_prepayment pbp
                 left join purchases_plan pp on pbp.purchase_no = pp.purchase_no and pbp.supplier_id = pp.supplier_id
                 left join stock_task_process_detail s on s.list_no = pp.purchase_no and s.sku = pp.sku
                 LEFT JOIN stock_task_process stp on stp.id = s.stock_task_process_id
                 LEFT JOIN stock_task st on st.id = stp.stock_task_id
        where st.type in (11,56) and pbp.purchase_advanced_order_id = #{id} and s.id is not null
        group by s.id
    </select>

    <select id="selectProcessAdvance" resultType="string">
        select pbp.purchase_no
        from purchase_binding_prepayment pbp
        left join purchase_advanced_order pao on pbp.purchase_advanced_order_id = pao.id
        where
              pbp.supplier_id = #{supplierId}
        and pbp.binding_status <![CDATA[ <> ]]> 3 and pao.status in (1,4,5)
        <if test = "purchaseNoList != null and purchaseNoList.size > 0">
            and pbp.purchase_no  in
            <foreach collection="purchaseNoList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryAdvanceAmount" resultType="java.math.BigDecimal">
        select  ifnull(SUM(CASE WHEN binding_status = 2 THEN advance_amount ELSE -advance_amount END),0) as advanceAmount
        from purchase_binding_prepayment
        where purchase_no = #{purchaseNo} and supplier_id = #{supplierId} and binding_status in (2,3)
    </select>

    <select id="queryAdvanceAmountList" resultType="net.summerfarm.model.vo.PurchaseAmountVO">
        select  purchase_no as purchaseNo,
                SUM(CASE WHEN binding_status = 2 THEN advance_amount ELSE -advance_amount END) as amount
        from purchase_binding_prepayment
        where binding_status in (2,3) and supplier_id = #{supplierId}
        <if test="advanceEndTime!=null">
            and create_time <![CDATA[<=]]> #{advanceEndTime}
        </if>
        and purchase_no   in
            <foreach collection="list" open="(" close=")" separator="," item="purchaseNo">
                #{purchaseNo}
            </foreach>
        GROUP BY purchase_no
    </select>

    <select id="queryCanUserAdvanceAmountByPurchaseNo" resultType="java.math.BigDecimal">
        select ifnull(SUM(CASE WHEN binding_status = 2 THEN advance_amount ELSE -advance_amount END), 0)
        from purchase_binding_prepayment
        where purchase_no = #{purchaseNo} and  binding_status in (2,3)
    </select>

    <select id="getFinishedDataList" resultType="net.summerfarm.model.vo.PurchaseBindingPrepaymentVO">
        select  purchase_no as purchaseNo,
                supplier_id as supplierId,
                CASE WHEN binding_status = 2 THEN advance_amount ELSE -advance_amount END as advanceAmount,
                create_time as createTime
        from purchase_binding_prepayment
        where binding_status in (2,3) and purchase_no in (select purchase_no from finance_account_statement_detail where finance_account_statement_id =#{accountId})
    </select>
</mapper>

