<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.WmsBatchFrozenMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.WmsBatchFrozen">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="batch" jdbcType="VARCHAR" property="batch" />
    <result column="lock_quantity" jdbcType="INTEGER" property="lockQuantity" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="type_id" jdbcType="INTEGER" property="typeId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, warehouse_no, sku, batch, lock_quantity, `type`, type_id, 
    `status`, reason
  </sql>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.WmsBatchFrozen" useGeneratedKeys="true">
    insert into wms_batch_frozen
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="batch != null">
        batch,
      </if>
      <if test="lockQuantity != null">
        lock_quantity,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="reason != null">
        reason,
      </if>
        <if test="qualityDate != null">
            quality_date,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=VARCHAR},
      </if>
      <if test="lockQuantity != null">
        #{lockQuantity,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
        <if test="qualityDate != null">
            #{qualityDate,jdbcType=VARCHAR},
        </if>
    </trim>
  </insert>
    <update id="updateStatus">
      update  wms_batch_frozen set status = 1 where type = #{type} and type_id = #{typeId}
    </update>
    <select id="selectByCondition" resultType="java.lang.Integer">
      select ifnull(sum(lock_quantity),0)
      from wms_batch_frozen
      where warehouse_no = #{areaNo}
        and sku = #{sku}
        and batch = #{batch}
        and type = 53
        and status = 0;
    </select>
  <select id="selectByDetail" resultType="java.lang.Integer">
    select ifnull(sum(lock_quantity),0)
      from wms_batch_frozen
      where warehouse_no = #{areaNo}
        and sku = #{sku}
        and batch = #{batch}
        and quality_date = #{qualityDate}
        and status = 0;
  </select>
</mapper>