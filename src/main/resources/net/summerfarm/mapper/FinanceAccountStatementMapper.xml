<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.FinanceAccountStatementMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceAccountStatement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="estimate_amount" jdbcType="DECIMAL" property="estimateAmount" />
    <result column="total_bill_amount" jdbcType="DECIMAL" property="totalBillAmount" />
    <result column="wallets_id" jdbcType="BIGINT" property="walletsId" />
    <result column="write_off_amount" jdbcType="DECIMAL" property="writeOffAmount" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="tax_number" jdbcType="VARCHAR" property="taxNumber" />
    <result column="supplier_account_id" jdbcType="INTEGER" property="supplierAccountId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_admin_id" jdbcType="INTEGER" property="creatorAdminId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="pd_type" jdbcType="INTEGER" property="pdType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="delete_reason" jdbcType="INTEGER" property="deleteReason" />
    <result column="supplier_confirm_status" jdbcType="INTEGER" property="supplierConfirmStatus" />
    <result column="confirm_user" jdbcType="INTEGER" property="confirmUser" />
    <result column="account_name" jdbcType="INTEGER" property="accountName" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="account_bank" jdbcType="INTEGER" property="accountBank" />
    <result column="account" jdbcType="INTEGER" property="account" />
    <result column="account_ascription" jdbcType="INTEGER" property="accountAscription" />
  </resultMap>
  <sql id="Base_Column_List">
    id, estimate_amount, total_bill_amount, wallets_id, write_off_amount, supplier_id, 
    supplier_name, tax_number, supplier_account_id, creator, create_time, pd_type, remark, 
    `status`,creator_admin_id,supplier_confirm_status,confirm_user,account_name,account_bank,account,account_ascription,pay_type,creator_admin_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_account_statement
    where id = #{id,jdbcType=BIGINT}  and status = 6
  </select>

  <select id="selectId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_account_statement
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="select" parameterType="java.lang.Long" resultType="net.summerfarm.model.vo.FinanceAccountStatementVO">
    select
         f.id, f.estimate_amount estimateAmount, f.total_bill_amount totalBillAmount, f.wallets_id walletsId, f.write_off_amount writeOffAmount, f.supplier_id supplierId,
         f.supplier_name supplierName, f.tax_number taxNumber, f.supplier_account_id supplierAccountId,  f.remark,f.`status`,
           s.pay_type payType,s.account_name accountName,s.account_bank accountBank,s.account_ascription accountAscription,s.account,f.current_processor currentProcessor,f.delete_reason deleteReason,
           f.supplier_confirm_status supplierConfirmStatus,f.creator ,f.creator_admin_id creatorAdminId,f.confirm_user confirmUser
    from finance_account_statement f
    left join supplier_account s on f.supplier_account_id = s.id
    where f.id = #{id}
  </select>

  <select id="selectNewData" parameterType="java.lang.Long"
          resultType="net.summerfarm.module.pms.model.vo.FinanceAccountDataVO">
    select f.id,
           f.estimate_amount         estimateAmount,
           f.total_bill_amount       totalBillAmount,
           f.wallets_id              walletsId,
           f.write_off_amount        writeOffAmount,
           f.supplier_id             supplierId,
           f.supplier_name           supplierName,
           f.tax_number              taxNumber,
           f.supplier_account_id     supplierAccountId,
           f.remark,
           f.creator,
           f.`status`,
           f.pay_type                payType,
           f.account_name            accountName,
           f.account_bank            accountBank,
           f.account_ascription      accountAscription,
           f.account,
           f.current_processor       currentProcessor,
           f.delete_reason           deleteReason,
           f.supplier_confirm_status supplierConfirmStatus,
           f.create_time             createTime,
           f.confirm_user            confirmUser
    from finance_account_statement f
    where f.id = #{id}
  </select>

  <select id="selectByWallets" parameterType="java.lang.Long" resultType="net.summerfarm.model.vo.FinanceAccountStatementVO">
    select
      f.id, f.estimate_amount estimateAmount, f.total_bill_amount totalBillAmount, f.wallets_id walletsId, f.write_off_amount writeOffAmount, f.supplier_id supplierId,
      f.supplier_name supplierName, f.tax_number taxNumber, f.supplier_account_id supplierAccountId,  f.remark,f.`status`,
      s.pay_type payType,s.account_name accountName,s.account_bank accountBank,s.account_ascription accountAscription,s.account,f.current_processor currentProcessor,f.delete_reason deleteReason,
      f.confirm_user confirmUser
    from finance_account_statement f
           left join supplier_account s on f.supplier_account_id = s.id
    where f.wallets_id = #{walletsId}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_account_statement
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAccountStatement" useGeneratedKeys="true">
    insert into finance_account_statement (estimate_amount, total_bill_amount, 
      wallets_id, write_off_amount, supplier_id, 
      supplier_name, tax_number, supplier_account_id, 
      creator, create_time, pd_type, 
      remark, `status`,creator_admin_id,account_name,account_bank,account,account_ascription,pay_type)
    values (#{estimateAmount,jdbcType=DECIMAL}, #{totalBillAmount,jdbcType=DECIMAL}, 
      #{walletsId,jdbcType=BIGINT}, #{writeOffAmount,jdbcType=DECIMAL}, #{supplierId,jdbcType=INTEGER}, 
      #{supplierName,jdbcType=VARCHAR}, #{taxNumber,jdbcType=VARCHAR}, #{supplierAccountId,jdbcType=INTEGER}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{pdType,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},#{creatorAdminId},#{accountName},#{accountBank},#{account},#{accountAscription},#{payType})
  </insert>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAccountStatement" useGeneratedKeys="true">
    insert into finance_account_statement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="estimateAmount != null">
        estimate_amount,
      </if>
      <if test="totalBillAmount != null">
        total_bill_amount,
      </if>
      <if test="walletsId != null">
        wallets_id,
      </if>
      <if test="writeOffAmount != null">
        write_off_amount,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="taxNumber != null">
        tax_number,
      </if>
      <if test="supplierAccountId != null">
        supplier_account_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="pdType != null">
        pd_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="estimateAmount != null">
        #{estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalBillAmount != null">
        #{totalBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="walletsId != null">
        #{walletsId,jdbcType=BIGINT},
      </if>
      <if test="writeOffAmount != null">
        #{writeOffAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="taxNumber != null">
        #{taxNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierAccountId != null">
        #{supplierAccountId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pdType != null">
        #{pdType,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceAccountStatement">
    update finance_account_statement
    <set>
      <if test="estimateAmount != null">
        estimate_amount = #{estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalBillAmount != null">
        total_bill_amount = #{totalBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="walletsId != null">
        wallets_id = #{walletsId,jdbcType=BIGINT},
      </if>
      <if test="writeOffAmount != null">
        write_off_amount = #{writeOffAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="taxNumber != null">
        tax_number = #{taxNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierAccountId != null">
        supplier_account_id = #{supplierAccountId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pdType != null">
        pd_type = #{pdType,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="currentProcessor != null">
        current_processor = #{currentProcessor},
      </if>
    </set>
    where wallets_id = #{walletsId} and `status` = 7
  </update>

  <update id="updateByPrimaryKey">
    update finance_account_statement
    set
      wallets_id = null,
      `status` = 6,
      current_processor = #{currentProcessor}
    where wallets_id = #{walletsId} and status = 7
  </update>

  <update id="updateById">
    update finance_account_statement
    set
      wallets_id = null
    where id = #{id}
  </update>

  <select id="selectByTaxNumber" parameterType="net.summerfarm.model.input.FinanceAccountStatementDBQuery" resultType="net.summerfarm.model.vo.FinanceAccountStatementVO">
    select
    id, estimate_amount estimateAmount, total_bill_amount totalBillAmount, wallets_id walletsId, write_off_amount writeOffAmount, supplier_id supplierId,
    supplier_name supplierName, tax_number taxNumber, supplier_account_id supplierAccountId, creator, create_time createTime, pd_type pdType, remark,`status`
    from finance_account_statement
    <where>
      tax_number = #{taxNumber} and status = 6 and wallets_id is null
      <if test=" creator != null" >
        and creator = #{creator}
      </if>
      <if test=" startTime != null and endTime != null" >
        and create_time <![CDATA[>=]]> #{startTime}
        and create_time <![CDATA[<=]]> #{endTime}
      </if>
      <if test=" id != null" >
        and id = #{id}
      </if>
    </where>
    ORDER by create_time DESC
  </select>

  <select id="selectByWalletsId" parameterType="long" resultType="net.summerfarm.model.vo.FinanceAccountStatementVO">
    select
    id, estimate_amount estimateAmount, total_bill_amount totalBillAmount, wallets_id walletsId, write_off_amount writeOffAmount, supplier_id supplierId,
    supplier_name supplierName, tax_number taxNumber, supplier_account_id supplierAccountId, creator, create_time createTime, pd_type pdType, remark,`status`
    from finance_account_statement
    where
      wallets_id = #{id} and status in (0,5,7,8)
  </select>

  <select id="selectById" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_account_statement
    where
      wallets_id = #{id} and status = 7
  </select>

  <select id="selectAdd" parameterType="long" resultType="decimal">
    select ifnull((sum(total_bill_amount) - sum(write_off_amount)),0)
    from finance_account_statement
    where
      wallets_id = #{id} and status = 7
  </select>

  <select id="selectByPurchaseNo" resultType="decimal">
    select ifnull(sum(fa.amount), 0)
      from finance_account_statement f
      left join finance_account_statement_detail fa on f.id = fa.finance_account_statement_id
       where f.supplier_id = #{supplierId} and fa.purchase_no = #{purchaseNo} and fa.type = #{type}
             and f.status in (0, 1, 5, 6, 7)
  </select>

  <update id="update" parameterType="net.summerfarm.model.domain.FinanceAccountStatement">
    update finance_account_statement
    set
      wallets_id = #{walletsId},
      `status` = 7,
      current_processor = #{currentProcessor}
    where id = #{id,jdbcType=INTEGER} and status = 6 and wallets_id is null
  </update>

  <select id="selectList" resultType="net.summerfarm.module.pms.model.vo.AccountListVO">
    select f.id,
           f.estimate_amount         estimateAmount,
           f.total_bill_amount       totalBillAmount,
           f.wallets_id              walletsId,
           f.write_off_amount        writeOffAmount,
           f.supplier_id             supplierId,
           f.supplier_name           supplierName,
           f.tax_number              taxNumber,
           f.supplier_account_id     supplierAccountId,
           f.creator,
           f.create_time             createTime,
           f.pd_type                 pdType,
           f.remark,
           f.`status`,
           f.current_processor       currentProcessor,
           f.supplier_confirm_status supplierConfirmStatus
    from finance_account_statement f
    left join finance_account_statement_detail fasd on f.id = fasd.finance_account_statement_id
    <where>
      <if test="id != null">
        and f.id = #{id}
      </if>
      <if test="supplierId != null">
        and f.supplier_id = #{supplierId}
      </if>
      <if test="creator != null">
        and f.creator = #{creator}
      </if>
      <if test="status != null">
        and f.`status` = #{status}
      </if>
      <if test="currentProcessor != null">
        and f.current_processor = #{currentProcessor}
      </if>
      <if test=" purchaseNo != null">
        and fasd.purchase_no like CONCAT (#{purchaseNo} ,'%')
      </if>
      <if test=" supplierConfirmStatus != null">
        and f.supplier_confirm_status = #{supplierConfirmStatus}
      </if>
    </where>
    group by f.id
    order by f.create_time Desc
  </select>
  <select id="selectOldList" parameterType="integer" resultType="net.summerfarm.model.vo.FinanceAccountStatementVO">
    select
    f.id, f.estimate_amount estimateAmount, f.total_bill_amount totalBillAmount, f.wallets_id walletsId, f.write_off_amount writeOffAmount, f.supplier_id supplierId,
    f.supplier_name supplierName, f.tax_number taxNumber, f.supplier_account_id supplierAccountId, fa.creator, fa.create_time createTime, f.pd_type pdType, f.remark,f.`status`,
    fa.auditor,fa.approver,fa.payer,fa.creator_admin_id creatorAdminId,fa.auditor_admin_id auditorAdminId,fa.approver_admin_id approverAdminId,fa.payer_admin_id payerAdminId,
    fa.audit_time auditTime,fa.pay_time payTime,fa.approve_time approveTime,fa.cancel_time cancelTime
    from finance_account_statement f
    left join finance_audit_record fa on f.id = fa.additional_id and fa.`type` = 2
    where f.status = #{status}
    order by f.create_time Desc
  </select>
    <select id="selectByPurchaseNoAndSupplierId" resultType="net.summerfarm.model.vo.FinanceAccountStatementVO">
      select f.id, f.estimate_amount estimateAmount, f.total_bill_amount totalBillAmount, f.wallets_id walletsId, f.write_off_amount writeOffAmount, f.supplier_id supplierId,
             f.supplier_name supplierName, f.tax_number taxNumber, f.supplier_account_id supplierAccountId,  f.remark,f.`status`,
             f.current_processor currentProcessor,f.delete_reason deleteReason,
             f.supplier_confirm_status supplierConfirmStatus,f.creator ,f.creator_admin_id creatorAdminId,f.confirm_user confirmUser
      from finance_account_statement f
             left join finance_account_statement_detail fa on f.id = fa.finance_account_statement_id
      where f.supplier_id = #{supplierId}
        and fa.purchase_no = #{purchaseNo}
    </select>

    <update id="updateBack">
    update finance_account_statement
    <set>
      <if test="estimateAmount != null">
        estimate_amount = #{estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalBillAmount != null">
        total_bill_amount = #{totalBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="walletsId != null">
        wallets_id = #{walletsId,jdbcType=BIGINT},
      </if>
      <if test="writeOffAmount != null">
        write_off_amount = #{writeOffAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="taxNumber != null">
        tax_number = #{taxNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierAccountId != null">
        supplier_account_id = #{supplierAccountId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pdType != null">
        pd_type = #{pdType,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="advancedOrderId != null">
        `advanced_order_id` = #{advancedOrderId},
      </if>
      <if test="deleteReason != null">
        `delete_reason` = #{deleteReason},
      </if>
      <if test="currentProcessor != null">
        `current_processor` = #{currentProcessor},
      </if>
      <if test="supplierConfirmStatus != null">
        `supplier_confirm_status` = #{supplierConfirmStatus},
      </if>
      <if test="confirmUser != null">
        `confirm_user` = #{confirmUser},
      </if>
      <if test="accountName != null">
        `account_name` = #{accountName},
      </if>
      <if test="accountBank != null">
        `account_bank` = #{accountBank},
      </if>
      <if test="account != null">
        `account` = #{account},
      </if>
      <if test="accountAscription != null">
        `account_ascription` = #{accountAscription},
      </if>
      <if test="payType != null">
        `pay_type` = #{payType},
      </if>
    </set>
    where id = #{id}
  </update>

  <select id="queryInProcessPurchaseList" resultType="java.lang.String">
    select distinct d.purchase_no
    from finance_account_statement f
    left join finance_account_statement_detail d on d.finance_account_statement_id=f.id
    where f.supplier_id=#{supplierId} and f.status not in (8,9) and d.type in (11,21)
    and d.purchase_no in
    <foreach collection="list" close=")" item="purchasesNo" open="(" separator=",">
      #{purchasesNo}
    </foreach>
  </select>

  <select id="queryAdjustAmountToalWithFinished" resultType="java.math.BigDecimal">
    select ifnull(sum(d.adjust_amount),0)
    from finance_account_statement f
    left join finance_account_statement_detail d on d.finance_account_statement_id=f.id
    where f.supplier_id=#{supplierId} and f.status =8  and d.purchase_no =#{purchaseNo}
  </select>

  <select id="getFinishedAccountIdByTime" resultType="java.lang.Long">
    select id
    from finance_account_statement
    where status=8 and create_time <![CDATA[ <= ]]> #{endTime} and id not in (select finance_account_statement_id from finance_account_verification_temp )
    order by create_time asc
  </select>
</mapper>