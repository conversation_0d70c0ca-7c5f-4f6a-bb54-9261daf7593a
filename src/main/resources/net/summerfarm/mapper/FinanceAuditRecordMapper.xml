<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.FinanceAuditRecordMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceAuditRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="additional_id" jdbcType="BIGINT" property="additionalId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="creator_admin_id" jdbcType="INTEGER" property="creatorAdminId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="auditor" jdbcType="VARCHAR" property="auditor"/>
        <result column="auditor_admin_id" jdbcType="INTEGER" property="auditorAdminId"/>
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="approver" jdbcType="VARCHAR" property="approver"/>
        <result column="approver_admin_id" jdbcType="INTEGER" property="approverAdminId"/>
        <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime"/>
        <result column="payer" jdbcType="VARCHAR" property="payer"/>
        <result column="payer_admin_id" jdbcType="INTEGER" property="payerAdminId"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime"/>
    </resultMap>

    <sql id="Base_column_field">
        id, additional_id, type, creator, creator_admin_id, create_time, auditor, auditor_admin_id, audit_time, approver, approver_admin_id, approve_time, payer, payer_admin_id, pay_time, update_time, cancel_time
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAuditRecord"
            useGeneratedKeys="true">
        insert into finance_audit_record (additional_id, `type`, creator,
                                          creator_admin_id, auditor,
                                          auditor_admin_id, audit_time, approver,
                                          approver_admin_id, approve_time, payer,
                                          payer_admin_id, pay_time, cancel_time)
        values (#{additionalId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR},
                #{creatorAdminId,jdbcType=INTEGER}, #{auditor,jdbcType=VARCHAR},
                #{auditorAdminId,jdbcType=INTEGER}, #{auditTime,jdbcType=TIMESTAMP}, #{approver,jdbcType=VARCHAR},
                #{approverAdminId,jdbcType=INTEGER}, #{approveTime,jdbcType=TIMESTAMP}, #{payer,jdbcType=VARCHAR},
                #{payerAdminId,jdbcType=INTEGER}, #{payTime,jdbcType=TIMESTAMP}, #{cancelTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAuditRecord"
            useGeneratedKeys="true">
        insert into finance_audit_record <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="additionalId != null">
            additional_id,
        </if>
        <if test="type != null">
            `type`,
        </if>
        <if test="creator != null">
            creator,
        </if>
        <if test="creatorAdminId != null">
            creator_admin_id,
        </if>
        <if test="auditor != null">
            auditor,
        </if>
        <if test="auditorAdminId != null">
            auditor_admin_id,
        </if>
        <if test="auditTime != null">
            audit_time,
        </if>
        <if test="approver != null">
            approver,
        </if>
        <if test="approverAdminId != null">
            approver_admin_id,
        </if>
        <if test="approveTime != null">
            approve_time,
        </if>
        <if test="payer != null">
            payer,
        </if>
        <if test="payerAdminId != null">
            payer_admin_id,
        </if>
        <if test="payTime != null">
            pay_time,
        </if>
        <if test="cancelTime != null">
            cancel_time,
        </if>
        <if test="paymentReviewId != null">
            payment_review_id,
        </if>
        <if test="paymentReview != null">
            payment_review,
        </if>
        <if test="paymentReviewTime != null">
            payment_review_time,
        </if>
        <if test="invoiceReview != null">
            invoice_review,
        </if>
        <if test="invoiceReviewId != null">
            invoice_review_id,
        </if>
        <if test="invoiceReviewTime != null">
            invoice_review_time,
        </if>
        <if test="drawer != null">
            drawer,
        </if>
        <if test="drawerId != null">
            drawer_id,
        </if>
        <if test="drawerTime != null">
            drawer_time,
        </if>
    </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="additionalId != null">
                #{additionalId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="creatorAdminId != null">
                #{creatorAdminId},
            </if>
            <if test="auditor != null">
                #{auditor},
            </if>
            <if test="auditorAdminId != null">
                #{auditorAdminId},
            </if>
            <if test="auditTime != null">
                #{auditTime},
            </if>
            <if test="approver != null">
                #{approver},
            </if>
            <if test="approverAdminId != null">
                #{approverAdminId},
            </if>
            <if test="approveTime != null">
                #{approveTime},
            </if>
            <if test="payer != null">
                #{payer},
            </if>
            <if test="payerAdminId != null">
                #{payerAdminId},
            </if>
            <if test="payTime != null">
                #{payTime},
            </if>
            <if test="cancelTime != null">
                #{cancelTime},
            </if>
            <if test="paymentReviewId != null">
                #{paymentReviewId},
            </if>
            <if test="paymentReview != null">
                #{paymentReview},
            </if>
            <if test="paymentReviewTime != null">
                #{paymentReviewTime},
            </if>
            <if test="invoiceReview != null">
                #{invoiceReview},
            </if>
            <if test="invoiceReviewId != null">
                #{invoiceReviewId},
            </if>
            <if test="invoiceReviewTime != null">
                #{invoiceReviewTime},
            </if>
            <if test="drawer != null">
                #{drawer},
            </if>
            <if test="drawerId != null">
                #{drawerId},
            </if>
            <if test="drawerTime != null">
                #{drawerTime},
            </if>
        </trim>
    </insert>


    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceAuditRecord">
        update finance_audit_record
        <set>
            <if test="additional_id != null">
                additional_id = #{additionalId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creator_admin_id != null">
                creator_admin_id = #{creatorAdminId,jdbcType=INTEGER},
            </if>
            <if test="create_time != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditor != null">
                auditor = #{auditor,jdbcType=VARCHAR},
            </if>
            <if test="auditor_admin_id != null">
                auditor_admin_id = #{auditorAdminId,jdbcType=INTEGER},
            </if>
            <if test="audit_time != null">
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="approver != null">
                approver = #{approver,jdbcType=VARCHAR},
            </if>
            <if test="approver_admin_id != null">
                approver_admin_id = #{approverAdminId,jdbcType=INTEGER},
            </if>
            <if test="approve_time != null">
                approve_time = #{approveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payer != null">
                payer = #{payer,jdbcType=VARCHAR},
            </if>
            <if test="payer_admin_id != null">
                payer_admin_id = #{payerAdminId,jdbcType=INTEGER},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancelTime != null">
                cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="update" parameterType="net.summerfarm.model.domain.FinanceAuditRecord">
        update finance_audit_record
        set additional_id     = #{additionalId,jdbcType=BIGINT},
            `type`            = #{type,jdbcType=INTEGER},
            creator           = #{creator,jdbcType=VARCHAR},
            creator_admin_id  = #{creatorAdminId,jdbcType=INTEGER},
            create_time       = #{createTime,jdbcType=TIMESTAMP},
            auditor           = #{auditor,jdbcType=VARCHAR},
            auditor_admin_id  = #{auditorAdminId,jdbcType=INTEGER},
            audit_time        = #{auditTime,jdbcType=TIMESTAMP},
            approver          = #{approver,jdbcType=VARCHAR},
            approver_admin_id = #{approverAdminId,jdbcType=INTEGER},
            approve_time      = #{approveTime,jdbcType=TIMESTAMP},
            payer             = #{payer,jdbcType=VARCHAR},
            payer_admin_id    = #{payerAdminId,jdbcType=INTEGER},
            pay_time          = #{payTime,jdbcType=TIMESTAMP},
            cancel_time          = #{cancelTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_column_field"/>
        from finance_audit_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="additionalId != null">
                and additional_id = #{additionalId}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
        </where>
    </select>

    <select id="selectOne" resultMap="BaseResultMap">
        select <include refid="Base_column_field"/>
        from finance_audit_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="additionalId != null">
                and additional_id = #{additionalId}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
        </where>
    </select>

</mapper>
