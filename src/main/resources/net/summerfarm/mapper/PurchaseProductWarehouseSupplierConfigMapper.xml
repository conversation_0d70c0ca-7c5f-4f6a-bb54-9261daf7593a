<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.PurchaseProductWarehouseSupplierConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.purchase.PurchaseProductWarehouseSupplierConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pd_id" jdbcType="BIGINT" property="pdId" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="complete_flag" jdbcType="INTEGER" property="completeFlag" />
    <result column="primary_flag" jdbcType="INTEGER" property="primaryFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pd_id, warehouse_no, supplier_id, complete_flag, primary_flag, create_time, updater, 
    update_time, creator
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_product_warehouse_supplier_config
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectMissWarehouse" resultType="net.summerfarm.model.DTO.purchase.ProductMissWareHouseDTO">
      SELECT
      pdid pdId,
      count(*) waitConfigWarehouse
      FROM
      (
      SELECT
      ppwc.pd_id pdId,
      ppwc.warehouse_no,
      ppwsc.id,
      GROUP_CONCAT( primary_flag ) primary_flag
      FROM
      purchase_product_warehouse_config ppwc
      LEFT JOIN purchase_product_warehouse_supplier_config ppwsc ON ppwc.pd_id = ppwsc.pd_id
      AND ppwc.warehouse_no = ppwsc.warehouse_no
      LEFT JOIN warehouse_storage_center wc ON wc.warehouse_no = ppwc.warehouse_no
      where 1=1
      <if test="pdIds !=null and pdIds.size>0">
        and ppwc.pd_id in
        <foreach collection="pdIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      AND purchase_type = 1
      AND wc.warehouse_name NOT LIKE '%测试%'
      GROUP BY
      ppwc.pd_id,
      ppwc.warehouse_no
      ) s
      WHERE
      ! FIND_IN_SET( 1, primary_flag )
      OR primary_flag IS NULL
      GROUP BY
      pdid
    </select>

  <select id="selectNoMissWarehouse" resultType="net.summerfarm.model.DTO.purchase.ProductMissWareHouseDTO">
    SELECT pdId,waitConfigWarehouse from (
    SELECT
    pdid pdId,
    count(*) waitConfigWarehouse
    FROM
    (
    SELECT
    ppwc.pd_id pdId,
    ppwc.warehouse_no,
    ppwsc.id,
    GROUP_CONCAT( primary_flag ) primary_flag
    FROM
    purchase_product_warehouse_config ppwc
    LEFT JOIN purchase_product_warehouse_supplier_config ppwsc ON ppwc.pd_id = ppwsc.pd_id
    AND ppwc.warehouse_no = ppwsc.warehouse_no
    LEFT JOIN warehouse_storage_center wc ON wc.warehouse_no = ppwc.warehouse_no
    where 1=1
    <if test="pdIds !=null and pdIds.size>0">
      and ppwc.pd_id in
      <foreach collection="pdIds" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
    AND purchase_type = 1
    AND wc.warehouse_name NOT LIKE '%测试%'
    GROUP BY
    ppwc.pd_id,
    ppwc.warehouse_no
    ) s
    WHERE
    ! FIND_IN_SET( 1, primary_flag )
    OR primary_flag IS NULL
    GROUP BY
    pdid) p where waitConfigWarehouse=0
  </select>
    <select id="queryProductWarehouseSupplierConfig"
            resultType="net.summerfarm.model.DTO.purchase.ProductWarehouseSupplierDTO">
    select pwsc.id,pwsc.pd_id pdId,pd.pd_name pdName,pwsc.warehouse_no warehouseNo,wsc.warehouse_name warehouseName
         ,pwsc.supplier_id supplierId,s.name supplierName,pwsc.complete_flag completeFlag,pwsc.primary_flag primaryFlag
      from purchase_product_warehouse_supplier_config pwsc
        left join products pd on pd.pd_id = pwsc.pd_id
        left join warehouse_storage_center wsc on pwsc.warehouse_no = wsc.warehouse_no
        left join supplier s on pwsc.supplier_id = s.id
    where pwsc.pd_id = #{pdId}
    order by pwsc.pd_id,pwsc.warehouse_no
    </select>
    <select id="selectConfigByWarehouseLimit"
            resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from purchase_product_warehouse_supplier_config
      where warehouse_no = #{warehouseNo} limit #{startNum},#{pageSize}
    </select>
  <select id="selectByQueryInput"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from purchase_product_warehouse_supplier_config
    <where>
      <if test = "id != null">
        and id = #{id}
      </if>
      <if test = "ids != null and ids.size > 0">
        and id in
        <foreach collection="ids" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test = "pdId != null">
        and pd_id = #{pdId}
      </if>
      <if test = "pdIds != null and pdIds.size > 0">
        and pd_id  in
        <foreach collection="pdIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test = "warehouseNo != null">
        and warehouse_no = #{warehouseNo}
      </if>
      <if test = "warehouseNos != null and warehouseNos.size > 0">
        and warehouse_no in
        <foreach collection="warehouseNos" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test = "supplierId != null">
        and supplier_id = #{supplierId}
      </if>
      <if test = "supplierIds != null and supplierIds.size > 0">
        and supplier_id in
        <foreach collection="supplierIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test = "completeFlag != null">
        and complete_flag = #{completeFlag}
      </if>
      <if test = "primaryFlag != null">
        and primary_flag = #{primaryFlag}
      </if>
    </where>

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_product_warehouse_supplier_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByPdIdAndWarehouseNo">
    delete from purchase_product_warehouse_supplier_config
    where pd_id = #{pdId} and warehouse_no in
    <foreach collection="warehouseNos" open="(" close=")" separator="," item="warehouseNo">
      #{warehouseNo}
    </foreach>
  </delete>
  <delete id="deleteByWarehouseNos">
    delete from purchase_product_warehouse_supplier_config where warehouse_no in
    <foreach collection="warehouseNos" open="(" close=")" separator="," item="warehouseNo">
      #{warehouseNo}
    </foreach>
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.purchase.PurchaseProductWarehouseSupplierConfig">
    insert into purchase_product_warehouse_supplier_config (id, pd_id, warehouse_no, 
      supplier_id, complete_flag, primary_flag, 
      create_time, updater, update_time, 
      creator)
    values (#{id,jdbcType=BIGINT}, #{pdId,jdbcType=BIGINT}, #{warehouseNo,jdbcType=INTEGER}, 
      #{supplierId,jdbcType=INTEGER}, #{completeFlag,jdbcType=INTEGER}, #{primaryFlag,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER})
  </insert>
  <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
    insert into purchase_product_warehouse_supplier_config(pd_id, warehouse_no,
    supplier_id, complete_flag, primary_flag,
    create_time, updater, update_time,
    creator)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.pdId}, #{entity.warehouseNo}, #{entity.supplierId},
      #{entity.completeFlag}, #{entity.primaryFlag}, #{entity.createTime},
      #{entity.updater}, #{entity.updateTime}, #{entity.creator})
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.purchase.PurchaseProductWarehouseSupplierConfig">
    insert into purchase_product_warehouse_supplier_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="completeFlag != null">
        complete_flag,
      </if>
      <if test="primaryFlag != null">
        primary_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=BIGINT},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="completeFlag != null">
        #{completeFlag,jdbcType=INTEGER},
      </if>
      <if test="primaryFlag != null">
        #{primaryFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.purchase.PurchaseProductWarehouseSupplierConfig">
    update purchase_product_warehouse_supplier_config
    <set>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=BIGINT},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="completeFlag != null">
        complete_flag = #{completeFlag,jdbcType=INTEGER},
      </if>
      <if test="primaryFlag != null">
        primary_flag = #{primaryFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.purchase.PurchaseProductWarehouseSupplierConfig">
    update purchase_product_warehouse_supplier_config
    set pd_id = #{pdId,jdbcType=BIGINT},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      supplier_id = #{supplierId,jdbcType=INTEGER},
      complete_flag = #{completeFlag,jdbcType=INTEGER},
      primary_flag = #{primaryFlag,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>