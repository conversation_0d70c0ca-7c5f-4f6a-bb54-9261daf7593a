<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.PurchasePrepaymentPoolRecordMapper">

    <resultMap type="net.summerfarm.model.domain.PurchasePrepaymentPoolRecord" id="PurchasePrepaymentPoolRecordMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="billId" column="bill_id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="PurchasePrepaymentPoolRecordMap">
        select id,
               supplier_id,
               supplier_name,
               bill_id,
               type,
               amount,
               create_time,
               update_time
        from xianmudb.purchase_prepayment_pool_record
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="PurchasePrepaymentPoolRecordMap">
        select
        id, supplier_id, supplier_name, bill_id, type, amount, create_time, update_time
        from xianmudb.purchase_prepayment_pool_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="supplierId != null">
                and supplier_id = #{supplierId}
            </if>
            <if test="supplierName != null and supplierName != ''">
                and supplier_name = #{supplierName}
            </if>
            <if test="billId != null">
                and bill_id = #{billId}
            </if>
            <if test="purchaseAdvancedOrderId != null">
                and bill_id = #{purchaseAdvancedOrderId}
            </if>
            <if test="financeAdvancedRefundId != null">
                and bill_id = #{financeAdvancedRefundId}
            </if>
            <if test="statementsId != null">
                and bill_id = #{statementsId}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="startTime != null and endTime != null">
                and create_time <![CDATA[>=]]> #{startTime}
                and create_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
        order by id desc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into xianmudb.purchase_prepayment_pool_record(supplier_id, supplier_name, bill_id, type, amount)
        values (#{supplierId}, #{supplierName}, #{billId}, #{type}, #{amount})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into xianmudb.purchase_prepayment_pool_record(supplier_id, supplier_name, bill_id, type, amount)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.supplierId}, #{entity.supplierName}, #{entity.billId}, #{entity.type}, #{entity.amount})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update xianmudb.purchase_prepayment_pool_record
        <set>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="supplierName != null and supplierName != ''">
                supplier_name = #{supplierName},
            </if>
            <if test="billId != null">
                bill_id = #{billId},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>

