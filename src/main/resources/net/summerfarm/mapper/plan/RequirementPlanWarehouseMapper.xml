<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.plan.RequirementPlanWarehouseMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.plan.RequirementPlanWarehouse">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="requirement_plan_id" jdbcType="BIGINT" property="requirementPlanId" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, requirement_plan_id, warehouse_no, warehouse_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from requirement_plan_warehouse
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="queryByWarehouseNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from requirement_plan_warehouse where warehouse_no in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
  <select id="queryByRequirementPlanId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from requirement_plan_warehouse where requirement_plan_id = #{requirementPlanId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from requirement_plan_warehouse
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.plan.RequirementPlanWarehouse">
    insert into requirement_plan_warehouse (id, create_time, update_time, 
      requirement_plan_id, warehouse_no, warehouse_name
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{requirementPlanId,jdbcType=BIGINT}, #{warehouseNo,jdbcType=INTEGER}, #{warehouseName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.plan.RequirementPlanWarehouse">
    insert into requirement_plan_warehouse
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="requirementPlanId != null">
        requirement_plan_id,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requirementPlanId != null">
        #{requirementPlanId,jdbcType=BIGINT},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.plan.RequirementPlanWarehouse">
    update requirement_plan_warehouse
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requirementPlanId != null">
        requirement_plan_id = #{requirementPlanId,jdbcType=BIGINT},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.plan.RequirementPlanWarehouse">
    update requirement_plan_warehouse
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      requirement_plan_id = #{requirementPlanId,jdbcType=BIGINT},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into requirement_plan_warehouse (create_time, update_time, requirement_plan_id, warehouse_no, warehouse_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.createTime}, #{item.updateTime}, #{item.requirementPlanId}, #{item.warehouseNo}, #{item.warehouseName})
    </foreach>
  </insert>
</mapper>