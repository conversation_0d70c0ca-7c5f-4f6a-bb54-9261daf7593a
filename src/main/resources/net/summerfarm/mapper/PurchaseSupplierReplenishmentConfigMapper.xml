<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.PurchaseSupplierReplenishmentConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.purchase.PurchaseSupplierReplenishmentConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pd_id" jdbcType="BIGINT" property="pdId" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="replenishment_mode" jdbcType="INTEGER" property="replenishmentMode" />
    <result column="pre_day" jdbcType="INTEGER" property="preDay" />
    <result column="backlog_day" jdbcType="INTEGER" property="backlogDay" />
    <result column="order_date" jdbcType="INTEGER" property="orderDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="safe_water_level" jdbcType="INTEGER" property="safeWaterLevel" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pd_id, warehouse_no, supplier_id, replenishment_mode, pre_day,
    backlog_day, order_date, create_time, safe_water_level, updater, update_time, creator
  </sql>
  <sql id="rc_Base_Column_List">
    rc.id, rc.pd_id, rc.warehouse_no, rc.supplier_id, rc.replenishment_mode, rc.pre_day,
    rc.backlog_day, rc.order_date, rc.create_time, rc.safe_water_level, rc.updater, rc.update_time, rc.creator
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_supplier_replenishment_config
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="querySupplierReplenishmentConfigs"
            resultType="net.summerfarm.model.DTO.purchase.SupplierReplenishmentDTO">
      select id, pd_id pdId, warehouse_no warehouseNo, supplier_id supplierId, replenishment_mode replenishmentMode, pre_day preDay,
    backlog_day backlogDay, order_date orderDate, safe_water_level safeWaterLevel from purchase_supplier_replenishment_config
    <where>
      <if test = "id != null">
        and id = #{id}
      </if>
      <if test = "pdId != null">
        and pd_id = #{pdId}
      </if>
      <if test = "pdIds != null and pdIds.size > 0">
        and pd_id in
        <foreach collection="pdIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test = "warehouseNo != null">
        and warehouse_no = #{warehouseNo}
      </if>
      <if test = "warehouseNos != null">
        and warehouse_no in
        <foreach collection="warehouseNos" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test = "supplierId != null">
        and supplier_id = #{supplierId}
      </if>
      <if test = "supplierIds != null">
        and supplier_id in
        <foreach collection="supplierIds" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test = "replenishmentMode != null">
        and replenishment_mode = #{replenishmentMode}
      </if>
      <if test = "replenishmentModes != null">
        and replenishment_mode in
        <foreach collection="replenishmentModes" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
        order by id asc
    </select>
  <select id="getCompletedPrimaryConfig"
          resultMap="BaseResultMap">
    select
    <include refid="rc_Base_Column_List" />
    from
        purchase_supplier_replenishment_config rc left join purchase_product_warehouse_supplier_config sc
            on rc.pd_id =sc.pd_id and rc.warehouse_no=sc.warehouse_no and rc.supplier_id = sc.supplier_id
    where
          sc.complete_flag = 1 and sc.primary_flag = 1
      and rc.pd_id = #{pdId} and rc.warehouse_no = #{warehouseNo}
  </select>

  <select id="getMaxPreDay"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
    purchase_supplier_replenishment_config  where pd_id = #{pdId} and warehouse_no = #{warehouseNo} and supplier_id=#{supplierId} order by pre_day desc limit 1
  </select>
    <select id="selectConfigByWarehouseLimit"
            resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from purchase_supplier_replenishment_config
      where warehouse_no = #{warehouseNo} limit #{startNum},#{pageSize}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_supplier_replenishment_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <delete id="deleteByPdIdAndWarehouseNo">
      delete from purchase_supplier_replenishment_config
    where pd_id = #{pdId} and warehouse_no in
      <foreach collection="warehouseNos" open="(" close=")" separator="," item="warehouseNo">
        #{warehouseNo}
      </foreach>
    </delete>
    <delete id="deleteByWarehouseNos">
      delete from purchase_supplier_replenishment_config where warehouse_no in
      <foreach collection="warehouseNos" open="(" close=")" separator="," item="warehouseNo">
        #{warehouseNo}
      </foreach>
    </delete>
    <insert id="insert" parameterType="net.summerfarm.model.domain.purchase.PurchaseSupplierReplenishmentConfig">
    insert into purchase_supplier_replenishment_config (id, pd_id, warehouse_no, 
      supplier_id, replenishment_mode,
      pre_day, backlog_day, order_date, 
      create_time, safe_water_level, updater, 
      update_time, creator
      )
    values (#{id,jdbcType=BIGINT}, #{pdId,jdbcType=BIGINT}, #{warehouseNo,jdbcType=INTEGER}, 
      #{supplierId,jdbcType=INTEGER}, #{replenishmentMode,jdbcType=INTEGER},
      #{preDay,jdbcType=INTEGER}, #{backlogDay,jdbcType=INTEGER}, #{orderDate,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{safeWaterLevel,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.purchase.PurchaseSupplierReplenishmentConfig">
    insert into purchase_supplier_replenishment_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="replenishmentMode != null">
        replenishment_mode,
      </if>
      <if test="preDay != null">
        pre_day,
      </if>
      <if test="backlogDay != null">
        backlog_day,
      </if>
      <if test="orderDate != null">
        order_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="safeWaterLevel != null">
        safe_water_level,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=BIGINT},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="replenishmentMode != null">
        #{replenishmentMode,jdbcType=INTEGER},
      </if>
      <if test="preDay != null">
        #{preDay,jdbcType=INTEGER},
      </if>
      <if test="backlogDay != null">
        #{backlogDay,jdbcType=INTEGER},
      </if>
      <if test="orderDate != null">
        #{orderDate,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="safeWaterLevel != null">
        #{safeWaterLevel,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
    insert into purchase_supplier_replenishment_config(pd_id, warehouse_no,
    supplier_id, replenishment_mode,
    pre_day, backlog_day, order_date,
    create_time, safe_water_level, updater,
    update_time, creator)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.pdId}, #{entity.warehouseNo}, #{entity.supplierId},
      #{entity.replenishmentMode}, #{entity.preDay}, #{entity.backlogDay},
       #{entity.orderDate}, #{entity.createTime}, #{entity.safeWaterLevel},
       #{entity.updater}, #{entity.updateTime}, #{entity.creator})
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.purchase.PurchaseSupplierReplenishmentConfig">
    update purchase_supplier_replenishment_config
    <set>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=BIGINT},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="replenishmentMode != null">
        replenishment_mode = #{replenishmentMode,jdbcType=INTEGER},
      </if>
      <if test="preDay != null">
        pre_day = #{preDay,jdbcType=INTEGER},
      </if>
      <if test="backlogDay != null">
        backlog_day = #{backlogDay,jdbcType=INTEGER},
      </if>
      <if test="orderDate != null">
        order_date = #{orderDate,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="safeWaterLevel != null">
        safe_water_level = #{safeWaterLevel,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.purchase.PurchaseSupplierReplenishmentConfig">
    update purchase_supplier_replenishment_config
    set pd_id = #{pdId,jdbcType=BIGINT},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      supplier_id = #{supplierId,jdbcType=INTEGER},
      replenishment_mode = #{replenishmentMode,jdbcType=INTEGER},
      pre_day = #{preDay,jdbcType=INTEGER},
      backlog_day = #{backlogDay,jdbcType=INTEGER},
      order_date = #{orderDate,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      safe_water_level = #{safeWaterLevel,jdbcType=INTEGER},
      updater = #{updater,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>