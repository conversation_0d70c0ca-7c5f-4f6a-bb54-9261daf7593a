<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.OrderRelationMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.OrderRelation">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="masterOrderNo" column="master_order_no" jdbcType="VARCHAR"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="precisionDeliveryFee" column="precision_delivery_fee" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,master_order_no,order_no,
        create_time,update_time,precision_delivery_fee
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from order_relation
        where  id = #{id,jdbcType=BIGINT} 
    </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from order_relation
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.OrderRelation" useGeneratedKeys="true">
        insert into order_relation
        ( id,master_order_no,order_no
        ,create_time,update_time,precision_delivery_fee
        )
        values (#{id,jdbcType=BIGINT},#{masterOrderNo,jdbcType=VARCHAR},#{orderNo,jdbcType=VARCHAR}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP},#{precisionDeliveryFee,jdbcType=DECIMAL}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.OrderRelation" useGeneratedKeys="true">
        insert into order_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="masterOrderNo != null">master_order_no,</if>
                <if test="orderNo != null">order_no,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="precisionDeliveryFee != null">precision_delivery_fee,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="masterOrderNo != null">#{masterOrderNo,jdbcType=VARCHAR},</if>
                <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="precisionDeliveryFee != null">#{precisionDeliveryFee,jdbcType=DECIMAL},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.OrderRelation">
        update order_relation
        <set>
                <if test="masterOrderNo != null">
                    master_order_no = #{masterOrderNo,jdbcType=VARCHAR},
                </if>
                <if test="orderNo != null">
                    order_no = #{orderNo,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="precisionDeliveryFee != null">
                    precision_delivery_fee = #{precisionDeliveryFee,jdbcType=DECIMAL},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.OrderRelation">
        update order_relation
        set 
            master_order_no =  #{masterOrderNo,jdbcType=VARCHAR},
            order_no =  #{orderNo,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            precision_delivery_fee =  #{precisionDeliveryFee,jdbcType=DECIMAL}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <select id="selectByOrderNo" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from order_relation
      where  order_no = #{orderNo,jdbcType=BIGINT}
    </select>

    <select id="listActualPaidPrecision" resultType="net.summerfarm.model.domain.OrderRelation">
        SELECT ifnull(r.precision_delivery_fee, 0) - ifnull(p.amount, 0) precisionDeliveryFee, r.order_no orderNo
        from order_relation r
                 left join order_preferential p on r.order_no = p.order_no AND p.type = 18
        where r.order_no in
        <foreach collection="orderNos" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
