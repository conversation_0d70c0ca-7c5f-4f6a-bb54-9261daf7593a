<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.BigFinancialInvoiceSkuMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.BigFinancialInvoiceSku">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="order_sku_amount" jdbcType="INTEGER" property="orderSkuAmount"/>
        <result column="red_sku_amount" jdbcType="INTEGER" property="redSkuAmount"/>
        <result column="bill_number" jdbcType="VARCHAR" property="billNumber"/>
        <result column="pd_name" jdbcType="VARCHAR" property="pdName"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>

        <result column="invoice_id" jdbcType="BIGINT" property="invoiceId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, order_no, batch_no, sku, price, order_sku_amount,red_sku_amount, bill_number, invoice_id, `status`, pd_name,weight,
    update_time, create_time
  </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from big_financial_invoice_sku
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.BigFinancialInvoiceSku"
            useGeneratedKeys="true">
    insert into big_financial_invoice_sku (order_no, batch_no, sku, 
      price, order_sku_amount, red_sku_amount, bill_number, pd_name,
      invoice_id, `status`,weight, update_time,
      create_time)
    values (#{orderNo,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, 
      #{price,jdbcType=DECIMAL}, #{orderSkuAmount,jdbcType=INTEGER},#{redSkuAmount,jdbcType=INTEGER}, #{billNumber,jdbcType=VARCHAR}, #{pdName,jdbcType=VARCHAR},
      #{invoiceId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT},#{weight,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
      #{createTime,jdbcType=TIMESTAMP})
  </insert>

    <update id="updateStatueByFinancialBatchNo">
    update big_financial_invoice_sku
    set `status` = #{status}
    where  invoice_id =#{invoiceId} and batch_no=#{batchNo}
  </update>

    <insert id="batchInsert" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into big_financial_invoice_sku
        (order_no, batch_no, sku,
        price, order_sku_amount,red_sku_amount, bill_number, pd_name,weight,
        invoice_id, `status`)
        values
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.orderNo},
            #{item.batchNo},
            #{item.sku},
            #{item.price},
            #{item.orderSkuAmount},
            #{item.redSkuAmount},
            #{item.billNumber},
            #{item.pdName},
            #{item.weight},
            #{item.invoiceId},
            #{item.status})
        </foreach>
    </insert>
    <select id="selectByFinancialId" resultMap="BaseResultMap">
            select * from big_financial_invoice_sku where invoice_id = #{invoiceId} and status = 1
             order  by id desc
    </select>

    <select id="countByOrderNo" resultType="integer">
            select count(1) from big_financial_invoice_sku where order_no = #{orderNo}
    </select>

    <select id="selectFinancialIdByOrderSku" resultType="long">
            select invoice_id  from big_financial_invoice_sku where order_no = #{orderNo} and status = 1 and sku=#{sku}
             order  by id desc
    </select>


    <select id="selectByOrderSku" resultMap="BaseResultMap">
            select * from big_financial_invoice_sku where order_no = #{orderNo} and status = 1 and  sku=#{sku}
            order  by id desc
    </select>

    <select id="selectByOrderNo" resultMap="BaseResultMap">
            select * from big_financial_invoice_sku where order_no = #{orderNo} and status = #{status}
            order  by id desc
    </select>


    <select id="selectByBatchNo" resultMap="BaseResultMap">
            select * from big_financial_invoice_sku where batch_no = #{batchNo} and status = #{status}
            order  by id desc limit 1
    </select>

    <update id="updateRedCountById">
    update big_financial_invoice_sku
    set red_sku_amount = red_sku_amount +1
    where  id =#{id}
  </update>

    <update id="updateStatusByInvoiceId">
    update big_financial_invoice_sku
    set `status` = #{status}
    where  invoice_id =#{invoiceId}
  </update>

    <select id="countByInvoiceId" resultType="integer">
            select count(1) from big_financial_invoice_sku where invoice_id = #{invoiceId}
    </select>
</mapper>