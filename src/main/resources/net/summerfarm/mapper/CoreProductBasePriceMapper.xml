<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.CoreProductBasePriceMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CoreProductBasePrice">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="largeAreaNo" column="large_area_no" jdbcType="INTEGER"/>
        <result property="largeAreaName" column="large_area_name" jdbcType="VARCHAR"/>
        <result property="sku" column="sku" jdbcType="VARCHAR"/>
        <result property="pdId" column="pd_id" jdbcType="INTEGER"/>
        <result property="pdName" column="pd_name" jdbcType="VARCHAR"/>
        <result property="weight" column="weight" jdbcType="VARCHAR"/>
        <result property="basePrice" column="base_price" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,large_area_no,large_area_name,sku,pd_id,pd_name,weight,base_price,create_time,update_time
    </sql>
    <select id="selectBySkuAreaNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from core_product_base_price
        where large_area_no=#{areaNo} and sku=#{sku}
        <if test="id!=null">
            and id!=#{id}
        </if>
    </select>

</mapper>