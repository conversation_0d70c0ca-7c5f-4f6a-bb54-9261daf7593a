<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.ProductLabelValueMapper">


    <insert id="insertBatch" parameterType="net.summerfarm.model.vo.ProductLabelValueVo">
        insert into product_label_value (sku,label_id,label_value) VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.sku}, #{item.labelId}, #{item.labelValue})
        </foreach>
    </insert>

    <update id="updateById" parameterType="net.summerfarm.model.domain.ProductLabelValue">
        update product_label_value
        set label_value = #{labelValue} ,update_time = #{updateTime}
        where id = #{id}
    </update>

    <select id="selectProductLabelId" resultType="java.lang.Long">
        select id from product_label
    </select>
    <select id="selectAll" resultType="net.summerfarm.model.domain.ProductLabel">
        select id labelId,label_field labelField,label_name labelName from product_label
    </select>
    <select id="selectLabelBySkuAndLabelId" resultType="net.summerfarm.model.vo.ProductLabelValueVo">
        select id,label_id labelId,label_value labelValue,sku from product_label_value
        where sku = #{sku} and label_id = #{labelId}
        limit 1
    </select>
    <select id="selectByLabelIdAndLabelValueAndSku" resultType="net.summerfarm.model.domain.ProductLabelValue">
        select id,label_id labelId,label_value labelValue,sku from product_label_value
        where sku in
        <foreach collection="skuList" open="(" close=")" separator="," item="sku">
            #{sku}
        </foreach>
        and label_id = #{labelId} and label_value =#{labelValue}
    </select>

    <select id="selectLabelBySku" resultType="net.summerfarm.model.vo.ProductLabelValueVo">
        select
            id,
            label_id labelId,
            label_value labelValue,
            sku
        from
            product_label_value
        where
            sku = #{sku}
    </select>
</mapper>