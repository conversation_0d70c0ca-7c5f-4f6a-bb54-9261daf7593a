<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.WmsDamageStockTaskMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.WmsDamageStockTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="stock_task_id" jdbcType="INTEGER" property="stockTaskId" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="creater" jdbcType="VARCHAR" property="creater" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="tenant_id" jdbcType="INTEGER" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, stock_task_id, warehouse_no, `status`, creater, updater, 
    `type`, tenant_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wms_damage_stock_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectList" resultType="net.summerfarm.model.vo.WmsDamageStockTaskVo">
    select  distinct wdst.id,wdst.warehouse_no warehouseNo,wsc.warehouse_name warehouseName,wdst.stock_task_id stockTaskId,
            wdst.create_time createTime,wdst.task_end_time taskEndTime,wdst.status,wdst.creater,wdst.update_time updateTime,wdst.type
    from wms_damage_stock_task wdst
           inner join warehouse_storage_center wsc on wdst.warehouse_no = wsc.warehouse_no
           left join wms_damage_stock_item wdsi on wdst.id = wdsi.damage_stock_task_id
    <if test="pdId != null">
      left join inventory i on wdsi.sku = i.sku
    </if>
    <where>
      <if test="id !=null">
       and wdst.id = #{id}
      </if>
      <if test="warehouseNo !=null">
        and wdst.warehouse_no = #{warehouseNo}
      </if>
      <if test="status !=null">
        and wdst.status = #{status}
      </if>
      <if test="sku !=null">
        and wdsi.sku = #{sku}
      </if>
      <if test="pdId!=null">
        and i.pd_id = #{pdId}
      </if>
      <if test="type!=null">
        and wdst.type = #{type}
      </if>
      <if test="typeList != null and typeList.size != 0">
        and wdst.type in
        <foreach collection="typeList" open="(" close=")" separator="," item="type1">
          #{type1}
        </foreach>
      </if>
      <if test="tenantId != null">
        and wdst.tenant_id = #{tenantId,jdbcType=BIGINT}
      </if>
      <if test="creater != null and creater != '' ">
        and wdst.creater = #{creater,jdbcType=VARCHAR}
      </if>
      <if test="startTime != null">
        and wdst.create_time >= #{startTime,jdbcType=DATE}
      </if>
      <if test="endTime != null">
        and wdst.create_time &lt;= #{endTime,jdbcType=DATE}
      </if>

    </where>
        order by wdst.id desc

  </select>
    <select id="selectById" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from wms_damage_stock_task
      where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="hasUnFinishDamageTask" resultType="java.lang.Boolean">
      select count(1) > 0 from wms_damage_stock_item wdsi left join wms_damage_stock_task wdst on wdsi.damage_stock_task_id = wdst.id
      where   wdst.status in (0,1)
        and wdst.warehouse_no = #{warehouseNo} and wdsi.sku = #{sku}
        <if test="listNo != null">
          and wdsi.list_no =#{listNo}
        </if>
        and wdsi.quality_date=#{qualityDate}

    </select>
    <select id="selectByCondition" resultType="java.lang.Integer">
      select count(1)
      from wms_damage_stock_task wdst
             left join wms_damage_stock_item wdsi on wdst.id = wdsi.damage_stock_task_id
      where wdst.warehouse_no = #{areaNo}
        and wdsi.sku = #{sku} and wdst.status in(0,1)
    </select>
  <select id="selectByStockTaskId" resultType="net.summerfarm.model.domain.WmsDamageStockTask">
    select
    <include refid="Base_Column_List" />
    from wms_damage_stock_task
    where stock_task_id = #{stockTaskId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wms_damage_stock_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.WmsDamageStockTask" useGeneratedKeys="true">
    insert into wms_damage_stock_task (create_time, update_time, stock_task_id, 
      warehouse_no, `status`, creater, 
      updater, `type`)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{stockTaskId,jdbcType=INTEGER}, 
      #{warehouseNo,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{creater,jdbcType=VARCHAR}, 
      #{updater,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.WmsDamageStockTask" useGeneratedKeys="true">
    insert into wms_damage_stock_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="stockTaskId != null">
        stock_task_id,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="creater != null">
        creater,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="tenantId != null">
        `tenant_id`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stockTaskId != null">
        #{stockTaskId,jdbcType=INTEGER},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.WmsDamageStockTask">
    update wms_damage_stock_task
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stockTaskId != null">
        stock_task_id = #{stockTaskId,jdbcType=INTEGER},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="creater != null">
        creater = #{creater,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.WmsDamageStockTask">
    update wms_damage_stock_task
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      stock_task_id = #{stockTaskId,jdbcType=INTEGER},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      creater = #{creater,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <update id="updateStatusById">
      update wms_damage_stock_task
      set status = #{status},updater = #{realName}
      where id = #{id}
    </update>
    <update id="updateTaskId">
      update wms_damage_stock_task set stock_task_id = #{taskId} where id =#{id}
    </update>
</mapper>