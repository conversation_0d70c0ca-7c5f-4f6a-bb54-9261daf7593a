<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CrmCommissionCategoryMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CrmCommissionCategory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="proportion" property="proportion" />
    <result column="category_type" property="categoryType" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, proportion, category_type, delete_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_commission_category
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_commission_category
    where delete_flag = 0
    ORDER BY id
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CrmCommissionCategory" useGeneratedKeys="true">
    insert into crm_commission_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="proportion != null">
        proportion,
      </if>
      <if test="categoryType != null">
        category_type,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="proportion != null">
        #{proportion},
      </if>
      <if test="categoryType != null">
        #{categoryType},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        #{createName},
      </if>
      <if test="updateName != null">
        #{updateName},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CrmCommissionCategory">
    update crm_commission_category
    <set>
      <if test="proportion != null">
        proportion = #{proportion},
      </if>
      <if test="categoryType != null">
        category_type = #{categoryType},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertOrUpdateById" parameterType="net.summerfarm.model.domain.CrmCommissionCategory">
    insert into crm_commission_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="proportion != null">
        proportion,
      </if>
      <if test="categoryType != null">
        category_type,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="proportion != null">
        #{proportion},
      </if>
      <if test="categoryType != null">
        #{categoryType},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        #{createName},
      </if>
      <if test="updateName != null">
        #{updateName},
      </if>
    </trim>
    ON DUPLICATE KEY
    UPDATE
    <trim suffixOverrides=",">
      <if test="proportion != null">
        proportion = #{proportion},
      </if>
      <if test="categoryType != null">
        category_type = #{categoryType},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName},
      </if>
    </trim>
  </insert>
</mapper>