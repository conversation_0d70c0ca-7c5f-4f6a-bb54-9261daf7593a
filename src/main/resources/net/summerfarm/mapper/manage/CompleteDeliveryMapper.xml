<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.CompleteDeliveryMapper">

    <resultMap id="CompleteDeliveryVOMap" type="net.summerfarm.model.vo.CompleteDeliveryVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="store_no" property="storeNo" jdbcType="INTEGER"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="complete_delivery_time" property="completeDeliveryTime" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="area_name" property="areaName" jdbcType="VARCHAR"/>
        <result column="mname" property="mname" jdbcType="VARCHAR"/>
        <result column="size" property="size" jdbcType="VARCHAR"/>
        <collection property="completeDeliveryCustomers" ofType="net.summerfarm.model.domain.CompleteDeliveryCustomer">
            <result column="name_remakes" property="nameRemakes" jdbcType="VARCHAR"/>
            <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        </collection>

    </resultMap>
    <insert id="addCompleteDelivery" parameterType="net.summerfarm.model.domain.CompleteDelivery" useGeneratedKeys="true" keyProperty="id">
        insert into complete_delivery (region,area_no,complete_delivery_time,status,create_time,creator,updater,store_no,update_time,city)
        values
            (#{region},#{areaNo},#{completeDeliveryTime},0,now(),#{creator},#{updater},#{storeNo},now(),#{city})
    </insert>

    <select id="select" parameterType="net.summerfarm.model.vo.CompleteDeliveryVO" resultType="net.summerfarm.model.vo.CompleteDeliveryVO">
        select cd.region region,wlc.store_no storeNo,wlc.store_name storeName,cd.complete_delivery_time completeDeliveryTime,cd.status status,cd.updater updater,cd.id,cd.city
        from complete_delivery cd
        left join warehouse_logistics_center wlc on cd.store_no = wlc.store_no
        <where>
            <if test="region != null">
                and cd.region = #{region}
            </if>
            <if test="storeNo != null">
                and cd.store_no = #{storeNo}
            </if>
            <if test="areaNo != null">
                and cd.area_no = #{areaNo}
            </if>
            <if test="city != null">
                and cd.city like concat('%',#{city},'%')
            </if>
            <if test="completeDeliveryTime != null">
                and cd.complete_delivery_time = #{completeDeliveryTime}
            </if>
        </where>
    </select>

    <update id="update">
        update complete_delivery
        <set>
            <if test="completeDeliveryTime != null">
                complete_delivery_time = #{completeDeliveryTime},
            </if>
            <if test="realName != null">
                updater = #{realName},
            </if>
            update_time = now(),
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="Integer">
        delete from complete_delivery
        where id = #{id}
    </delete>

    <update id="updateStatus">
        update complete_delivery
        set status = #{status}
        where id = #{id}
    </update>

    <select id="selectAll" resultType="net.summerfarm.model.vo.CompleteDeliveryVO">
        select cd.region,cd.area_no areaNo,cd.store_no storeNo,cdc.admin_id adminId,cd.complete_delivery_time completeDeliveryTime,m.mname,m.size,wlc.store_name storeName,ar.area_name areaName
        from complete_delivery cd
        left join complete_delivery_customer cdc on cd.id = cdc.complete_delivery_id
        left join merchant m on cdc.admin_id = m.admin_id
        left join area ar on cd.area_no = ar.area_no
        left join warehouse_logistics_center wlc on cd.store_no = wlc.store_no
        where cd.status = 0
    </select>

    <select id="selectAreaNo" resultType="net.summerfarm.model.vo.CompleteDeliveryVO">
        select cd.region,cd.area_no areaNo,cd.store_no storeNo
        from complete_delivery cd
        where cd.status = 0
        group by cd.store_no
    </select>

    <select id="selectAccountSum" resultType="integer">
        select count(1)
        from complete_delivery cd
        inner  join complete_delivery_customer cdc on cd.id = cdc.complete_delivery_id
        where store_no = #{storeNo}
    </select>

    <select id="areaValid" resultType="net.summerfarm.model.vo.CompleteDeliveryVO">
        select cd.area_no areaNo,ar.area_name areaName
        from complete_delivery cd
        left join area ar on cd.area_no = ar.area_no
        where cd.area_no in
        <foreach collection="list" item="areaNo" open="(" separator="," close=")">
            #{areaNo}
        </foreach>
    </select>

    <select id="selectById" resultType="net.summerfarm.model.domain.CompleteDelivery">
        SELECT	t.city  FROM complete_delivery t where t.id = #{id}
    </select>

    <select id="selectAllData" resultType="net.summerfarm.model.domain.CompleteDelivery">
        select id,
               region,
               area_no areaNo,
               complete_delivery_time completeDeliveryTime ,
               status,
               create_time createTime,
               creator,
               updater,
               store_no storeNo,
               update_time updateTime,
               city
        from complete_delivery where city is null
    </select>
    <select id="selectCompleteDeliveryTime" resultType="java.lang.String">
        select cd.complete_delivery_time
        from complete_delivery cd
                 inner join complete_delivery_ad_code_mapping m on cd.id = m.complete_delivery_id
                 inner join ad_code_msg ad on m.ad_code = ad.ad_code and ad.status = 0
        where ad.city = #{city} and ad.area = #{area} and cd.status = 0;
    </select>
</mapper>