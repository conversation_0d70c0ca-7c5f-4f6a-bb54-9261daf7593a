<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.AttributeMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.Attribute" >
    <id column="attr_id" property="attrId" jdbcType="INTEGER" />
    <result column="attribute" property="attribute" jdbcType="VARCHAR" />
    <result column="field" property="field" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    attr_id, attribute, field
  </sql>





</mapper>