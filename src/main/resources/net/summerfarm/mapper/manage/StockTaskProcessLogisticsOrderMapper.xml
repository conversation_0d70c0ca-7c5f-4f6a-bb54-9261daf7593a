<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.StockTaskProcessLogisticsOrderMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.wms.StockTaskProcessLogisticsOrderDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="stock_task_process_id" jdbcType="BIGINT" property="stockTaskProcessId" />
        <result column="company" jdbcType="VARCHAR" property="company" />
        <result column="delivery_order_no" jdbcType="VARCHAR" property="deliveryOrderNo" />
        <result column="delivery_type" jdbcType="INTEGER" property="deliveryType" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="operator" jdbcType="VARCHAR" property="operator" />
        <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
        <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    </resultMap>
    <sql id="Base_Column_List">
        id, stock_task_process_id, company, delivery_order_no, delivery_type, remark, creator,
    operator, gmt_created, gmt_modified, is_deleted, last_ver
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wms_stock_task_process_logistics_order
        where id = #{id,jdbcType=BIGINT}
    </select>
    <insert id="insert" parameterType="net.summerfarm.model.domain.wms.StockTaskProcessLogisticsOrderDO">
        insert into wms_stock_task_process_logistics_order (id, stock_task_process_id, company,
                                                            delivery_order_no, delivery_type, remark,
                                                            creator, operator, gmt_created,
                                                            gmt_modified, is_deleted, last_ver
        )
        values (#{id,jdbcType=BIGINT}, #{stockTaskProcessId,jdbcType=BIGINT}, #{company,jdbcType=VARCHAR},
                #{deliveryOrderNo,jdbcType=VARCHAR}, #{deliveryType,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{gmtCreated,jdbcType=TIMESTAMP},
                #{gmtModified,jdbcType=TIMESTAMP}, 0, 1
               )
    </insert>
    <insert id="insertSelective" parameterType="net.summerfarm.model.domain.wms.StockTaskProcessLogisticsOrderDO">
        insert into wms_stock_task_process_logistics_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="stockTaskProcessId != null">
                stock_task_process_id,
            </if>
            <if test="company != null">
                company,
            </if>
            <if test="deliveryOrderNo != null">
                delivery_order_no,
            </if>
            <if test="deliveryType != null">
                delivery_type,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="gmtCreated != null">
                gmt_created,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="lastVer != null">
                last_ver,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="stockTaskProcessId != null">
                #{stockTaskProcessId,jdbcType=BIGINT},
            </if>
            <if test="company != null">
                #{company,jdbcType=VARCHAR},
            </if>
            <if test="deliveryOrderNo != null">
                #{deliveryOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="deliveryType != null">
                #{deliveryType,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreated != null">
                #{gmtCreated,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="lastVer != null">
                #{lastVer,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="findByStockTaskProcessId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wms_stock_task_process_logistics_order
        where stock_task_process_id = #{stockTaskProcessId,jdbcType=BIGINT}
    </select>

</mapper>