<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceAccountingAuditRecordMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceAccountingAuditRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="billNo" column="bill_no" jdbcType="CHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="auditor" column="auditor" jdbcType="VARCHAR"/>
            <result property="auditorId" column="auditor_id" jdbcType="BIGINT"/>
            <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,bill_no,creator,
        creator_id,create_time,auditor,
        auditor_id,audit_time,status,
        remark,update_time
    </sql>
    <insert id="insert">
        insert into finance_accounting_audit_record (bill_no,creator,creator_id)
        values (#{billNo},#{creator},#{creatorId})
    </insert>
    <update id="updateByBillNoSelective">
        update finance_accounting_audit_record
        <set>
            <if test="auditor != null">
                auditor = #{auditor},
            </if>
            <if test="auditorId != null">
                auditor_id = #{auditorId},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where bill_no = #{billNo} and status=0
    </update>
    <select id="listByBillNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from finance_accounting_audit_record where bill_no=#{billNo}
    </select>
</mapper>
