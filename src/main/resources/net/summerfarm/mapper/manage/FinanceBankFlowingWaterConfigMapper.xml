<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceBankFlowingWaterConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceBankFlowingWaterConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="trading_day" jdbcType="VARCHAR" property="tradingDay" />
    <result column="random_number" jdbcType="VARCHAR" property="randomNumber" />
    <result column="transfer_mark" jdbcType="VARCHAR" property="transferMark" />
    <result column="last_serial_number" jdbcType="INTEGER" property="lastSerialNumber" />
    <result column="number_of_debits" jdbcType="VARCHAR" property="numberOfDebits" />
    <result column="debit_amount" jdbcType="VARCHAR" property="debitAmount" />
    <result column="number_of_credits" jdbcType="VARCHAR" property="numberOfCredits" />
    <result column="credit_amount" jdbcType="VARCHAR" property="creditAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="bank_no" jdbcType="VARCHAR" property="bankNo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, trading_day, random_number, transfer_mark, last_serial_number, number_of_debits, 
    debit_amount, number_of_credits, credit_amount, create_time,bank_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_bank_flowing_water_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByLastSerialNumber" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_bank_flowing_water_config
    where trading_day = #{tradingDay} and bank_no=#{bankNo}
    order by last_serial_number desc
    limit 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_bank_flowing_water_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceBankFlowingWaterConfig" useGeneratedKeys="true">
    insert into finance_bank_flowing_water_config (trading_day, random_number, transfer_mark, 
      last_serial_number, number_of_debits, debit_amount, 
      number_of_credits, credit_amount, create_time
      )
    values (#{tradingDay,jdbcType=VARCHAR}, #{randomNumber,jdbcType=VARCHAR}, #{transferMark,jdbcType=VARCHAR}, 
      #{lastSerialNumber,jdbcType=INTEGER}, #{numberOfDebits,jdbcType=VARCHAR}, #{debitAmount,jdbcType=VARCHAR},
      #{numberOfCredits,jdbcType=VARCHAR}, #{creditAmount,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceBankFlowingWaterConfig" useGeneratedKeys="true">
    insert into finance_bank_flowing_water_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tradingDay != null">
        trading_day,
      </if>
      <if test="randomNumber != null">
        random_number,
      </if>
      <if test="transferMark != null">
        transfer_mark,
      </if>
      <if test="lastSerialNumber != null">
        last_serial_number,
      </if>
      <if test="numberOfDebits != null">
        number_of_debits,
      </if>
      <if test="debitAmount != null">
        debit_amount,
      </if>
      <if test="numberOfCredits != null">
        number_of_credits,
      </if>
      <if test="creditAmount != null">
        credit_amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="bankNo != null">
        bank_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tradingDay != null">
        #{tradingDay,jdbcType=VARCHAR},
      </if>
      <if test="randomNumber != null">
        #{randomNumber,jdbcType=VARCHAR},
      </if>
      <if test="transferMark != null">
        #{transferMark,jdbcType=VARCHAR},
      </if>
      <if test="lastSerialNumber != null">
        #{lastSerialNumber,jdbcType=INTEGER},
      </if>
      <if test="numberOfDebits != null">
        #{numberOfDebits,jdbcType=VARCHAR},
      </if>
      <if test="debitAmount != null">
        #{debitAmount,jdbcType=VARCHAR},
      </if>
      <if test="numberOfCredits != null">
        #{numberOfCredits,jdbcType=VARCHAR},
      </if>
      <if test="creditAmount != null">
        #{creditAmount,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bankNo != null">
        #{bankNo},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceBankFlowingWaterConfig">
    update finance_bank_flowing_water_config
    <set>
      <if test="tradingDay != null">
        trading_day = #{tradingDay,jdbcType=VARCHAR},
      </if>
      <if test="randomNumber != null">
        random_number = #{randomNumber,jdbcType=VARCHAR},
      </if>
      <if test="transferMark != null">
        transfer_mark = #{transferMark,jdbcType=VARCHAR},
      </if>
      <if test="lastSerialNumber != null">
        last_serial_number = #{lastSerialNumber,jdbcType=INTEGER},
      </if>
      <if test="numberOfDebits != null">
        number_of_debits = #{numberOfDebits,jdbcType=VARCHAR},
      </if>
      <if test="debitAmount != null">
        debit_amount = #{debitAmount,jdbcType=VARCHAR},
      </if>
      <if test="numberOfCredits != null">
        number_of_credits = #{numberOfCredits,jdbcType=VARCHAR},
      </if>
      <if test="creditAmount != null">
        credit_amount = #{creditAmount,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceBankFlowingWaterConfig">
    update finance_bank_flowing_water_config
    set trading_day = #{tradingDay,jdbcType=VARCHAR},
      random_number = #{randomNumber,jdbcType=VARCHAR},
      transfer_mark = #{transferMark,jdbcType=VARCHAR},
      last_serial_number = #{lastSerialNumber,jdbcType=INTEGER},
      number_of_debits = #{numberOfDebits,jdbcType=VARCHAR},
      debit_amount = #{debitAmount,jdbcType=VARCHAR},
      number_of_credits = #{numberOfCredits,jdbcType=VARCHAR},
      credit_amount = #{creditAmount,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>