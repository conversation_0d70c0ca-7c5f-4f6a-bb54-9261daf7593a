<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ActivitySceneConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.market.ActivitySceneConfig">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="basic_info_id" jdbcType="BIGINT" property="basicInfoId"/>
    <result column="platform" jdbcType="TINYINT" property="platform"/>
    <result column="place" jdbcType="TINYINT" property="place"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `basic_info_id`, `platform`, `place`, `del_flag`, `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_scene_config
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from activity_scene_config
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.market.ActivitySceneConfig">
    insert into activity_scene_config (`id`, `basic_info_id`, `platform`,
                                       `place`, `del_flag`, `create_time`,
                                       `update_time`)
    values (#{id,jdbcType=BIGINT}, #{basicInfoId,jdbcType=BIGINT}, #{platform,jdbcType=TINYINT},
            #{place,jdbcType=TINYINT}, #{delFlag,jdbcType=TINYINT},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.market.ActivitySceneConfig">
    insert into activity_scene_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="basicInfoId != null">
        `basic_info_id`,
      </if>
      <if test="platform != null">
        `platform`,
      </if>
      <if test="place != null">
        `place`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="basicInfoId != null">
        #{basicInfoId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=TINYINT},
      </if>
      <if test="place != null">
        #{place,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.model.domain.market.ActivitySceneConfig">
    update activity_scene_config
    <set>
      <if test="basicInfoId != null">
        `basic_info_id` = #{basicInfoId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        `platform` = #{platform,jdbcType=TINYINT},
      </if>
      <if test="place != null">
        `place` = #{place,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.market.ActivitySceneConfig">
    update activity_scene_config
    set `basic_info_id` = #{basicInfoId,jdbcType=BIGINT},
        `platform`      = #{platform,jdbcType=TINYINT},
        `place`         = #{place,jdbcType=TINYINT},
        `del_flag`      = #{delFlag,jdbcType=TINYINT},
        `create_time`   = #{createTime,jdbcType=TIMESTAMP},
        `update_time`   = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByInfoId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_scene_config
    where basic_info_id = #{basicInfoId} and del_flag = 0
  </select>
</mapper>