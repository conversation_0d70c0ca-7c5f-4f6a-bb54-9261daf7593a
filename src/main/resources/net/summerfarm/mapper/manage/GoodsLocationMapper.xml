<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.GoodsLocationMapper">


    <insert id="insertGoodsLocation" parameterType="net.summerfarm.model.domain.GoodsLocation" useGeneratedKeys="true" keyProperty="id">
        insert into goods_location (`store_no`, `gl_no` , `add_time`,`update_time`,`temperature` ,`passageway`,`type`)
        values (#{storeNo},#{glNo},#{addTime},#{updateTime},#{temperature},#{passageway},#{type} )
    </insert>

    <select id="selectGoodsLocations" resultType="net.summerfarm.model.domain.GoodsLocation">

        select id,
        store_no storeNo,
        gl_no glNo ,
        add_time addTime,
        update_time updateTime,
        temperature ,
        passageway,
        `type`
        from goods_location where store_no = #{storeNo}

    </select>

    <select id="selectByGlNo" resultType="net.summerfarm.model.domain.GoodsLocation">

        select id,
        store_no storeNo,
        gl_no glNo ,
        add_time addTime,
        update_time updateTime,
        temperature ,
        passageway,
        `type`
        from goods_location where store_no = #{storeNo} and gl_no = #{glNo}
    </select>

    <select id="selectByTemperature" resultType="net.summerfarm.model.domain.GoodsLocation">
        select id,
        store_no storeNo,
        gl_no glNo ,
        add_time addTime,
        update_time updateTime,
        temperature ,
        passageway,
        `type`
        from goods_location where store_no = #{storeNo} and temperature = #{temperature} and type = 1
    </select>
</mapper>