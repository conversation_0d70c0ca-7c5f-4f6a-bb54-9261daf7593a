<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CarrierStatementExtrasMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CarrierStatementExtras">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="delivery_car_path_id" jdbcType="BIGINT" property="deliveryCarPathId" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, creator, update_time, updater, delivery_car_path_id, delivery_time, 
    store_no, `path`, money, remarks
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from carrier_statement_extras
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectCarrierStatementExtras" resultMap="BaseResultMap">
        SELECT
      <include refid="Base_Column_List" />
      from carrier_statement_extras
      <where>
        <if test="deliveryTime!=null">
          and delivery_time = #{deliveryTime}
        </if>
        <if test="storeNo !=null">
          and store_no = #{storeNo}
        </if>
        <if test="path!=null">
          and path = #{path}
        </if>
      </where>
      order by id desc
    </select>
    <select id="selectExtrasPrice" resultType="java.math.BigDecimal">
      SELECT
      sum(money)
      from carrier_statement_extras
      <where>
        <if test="deliveryTime!=null">
          and delivery_time = #{deliveryTime}
        </if>
        <if test="storeNo !=null">
          and store_no = #{storeNo}
        </if>
        <if test="path!=null">
          and path = #{path}
        </if>
      </where>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from carrier_statement_extras
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CarrierStatementExtras" useGeneratedKeys="true">
    insert into carrier_statement_extras (create_time, creator, update_time, 
      updater, delivery_car_path_id, delivery_time, 
      store_no, `path`, money, 
      remarks)
    values (#{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=VARCHAR}, #{deliveryCarPathId,jdbcType=BIGINT}, #{deliveryTime,jdbcType=TIMESTAMP}, 
      #{storeNo,jdbcType=INTEGER}, #{path,jdbcType=VARCHAR}, #{money,jdbcType=DECIMAL}, 
      #{remarks,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CarrierStatementExtras" useGeneratedKeys="true">
    insert into carrier_statement_extras
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="deliveryCarPathId != null">
        delivery_car_path_id,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCarPathId != null">
        #{deliveryCarPathId,jdbcType=BIGINT},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CarrierStatementExtras">
    update carrier_statement_extras
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCarPathId != null">
        delivery_car_path_id = #{deliveryCarPathId,jdbcType=BIGINT},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="path != null">
        `path` = #{path,jdbcType=VARCHAR},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CarrierStatementExtras">
    update carrier_statement_extras
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      delivery_car_path_id = #{deliveryCarPathId,jdbcType=BIGINT},
      delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      store_no = #{storeNo,jdbcType=INTEGER},
      `path` = #{path,jdbcType=VARCHAR},
      money = #{money,jdbcType=DECIMAL},
      remarks = #{remarks,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>