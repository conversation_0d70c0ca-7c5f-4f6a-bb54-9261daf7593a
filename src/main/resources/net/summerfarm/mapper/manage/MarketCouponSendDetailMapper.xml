<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.MarketCouponSendDetailMapper">

    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id"
            parameterType="net.summerfarm.model.domain.MarketCouponSendDetail">
        insert into market_coupon_send_detail(`send_id`,`m_id`,`merchant_name`,`phone`,`status`)
        values
        <foreach collection="list" separator="," item="item">
        (#{item.sendId},#{item.mId},#{item.merchantName},#{item.phone},#{item.status})
        </foreach>
    </insert>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into market_coupon_send_detail(`send_id`,`m_id`,`merchant_name`,`phone`,`status`)
        values (#{sendId},#{mId},#{merchantName},#{phone},#{status})
    </insert>

    <update id="updateStatus">
        update market_coupon_send_detail set status = #{status} where send_id = #{sendId} and status = 0
    </update>

    <update id="updateBatch">
        update market_coupon_send_detail set status = #{status} where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="updateStatusByMids">
        update market_coupon_send_detail set status = #{status} where send_id = #{sendId} and m_id in
        <foreach collection="mIds" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </update>


    <select id="selectList" resultType="net.summerfarm.model.domain.MarketCouponSendDetail">
        select id,`send_id` sendId,`m_id` mId,`merchant_name` merchantName,`phone`,`status` from market_coupon_send_detail where send_id =#{sendId} order by status desc
    </select>

    <select id="selectByPrimaryKey" resultType="net.summerfarm.model.domain.MarketCouponSendDetail">
        /*FORCE_MASTER*/ select id, send_id sendId, m_id mId, merchant_name merchantName, phone, status
        from market_coupon_send_detail
        where id = #{id}
    </select>

    <delete id="deleteBySendId">
        delete from market_coupon_send_detail where send_id = #{sendId}
    </delete>

    <update id="updateStatusByIds">
        update market_coupon_send_detail set status = #{status} where id in
        <foreach collection="sendDetailIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="updateStatusByMidsAndOrigin">
        update market_coupon_send_detail set status = #{status} where send_id = #{sendId} And status = #{originStatus} and m_id in
        <foreach collection="mIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
</mapper>
