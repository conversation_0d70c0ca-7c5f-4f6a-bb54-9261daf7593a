<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.AreaSkuMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.AreaSku">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="ladder_price" jdbcType="VARCHAR" property="ladderPrice"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="sales_mode" jdbcType="INTEGER" property="salesMode"/>
        <result column="limited_quantity" jdbcType="INTEGER" property="limitedQuantity"/>
        <result column="original_price" jdbcType="DECIMAL" property="originalPrice"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="on_sale" jdbcType="BIT" property="onSale"/>
        <result column="share" jdbcType="BIT" property="share"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="pd_priority" jdbcType="INTEGER" property="pdPriority"/>
        <result column="info" jdbcType="VARCHAR" property="info"/>
        <result column="m_type" property="mType"/>
        <result column="corner_status" jdbcType="INTEGER" property="cornerStatus"/>
        <result column="open_sale" property="openSale"/>
        <result column="open_sale_time" property="openSaleTime"/>
        <result column="close_sale" property="closeSale"/>
        <result column="close_sale_time" property="closeSaleTime"/>
        <result column="fix_flag" property="fixFlag"/>
        <result column="fix_num" property="fixNum"/>
        <result column="show" property="show"/>
        <result column="show_advance" property="showAdvance"/>
    </resultMap>

    <resultMap id="ResultMap" type="net.summerfarm.model.vo.AreaSkuVO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
        <result column="large_area_no" jdbcType="INTEGER" property="largeAreaNo"/>
        <result column="large_area_name" jdbcType="VARCHAR" property="largeAreaName"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="ladder_price" jdbcType="VARCHAR" property="ladderPrice"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="sales_mode" jdbcType="INTEGER" property="salesMode"/>
        <result column="limited_quantity" jdbcType="INTEGER" property="limitedQuantity"/>
        <result column="original_price" jdbcType="DECIMAL" property="originalPrice"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="on_sale" jdbcType="BIT" property="onSale"/>
        <result column="share" jdbcType="BIT" property="share"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="pd_priority" jdbcType="INTEGER" property="pdPriority"/>
        <result column="info" jdbcType="VARCHAR" property="info"/>
        <result column="m_type" property="mType"/>
        <result column="corner_status" jdbcType="INTEGER" property="cornerStatus"/>
        <result column="open_sale" property="openSale"/>
        <result column="open_sale_time" property="openSaleTime"/>
        <result column="close_sale" property="closeSale"/>
        <result column="close_sale_time" property="closeSaleTime"/>
        <result column="fix_flag" property="fixFlag"/>
        <result column="fix_num" property="fixNum"/>
        <result column="show" property="show"/>
        <result column="show_advance" property="showAdvance"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, sku, area_no, quantity,ladder_price,sales_mode, limited_quantity, original_price, price, on_sale, priority, update_time , share,
    add_time,pd_priority,info,m_type,corner_status, open_sale, open_sale_time, close_sale, close_sale_time,fix_flag,fix_num,`show`
  </sql>

    <select id="selectByStoreAndSku" resultMap="BaseResultMap">
    select
    t.sku, t.area_no, t.share, t.on_sale, t.pd_priority, f.store_no storeNo,t.id
    from area_sku t
    left join fence f on f.area_no = t.area_no
    where  f.store_no = #{storeNo} and f.status = 0 AND t.sku = #{sku}
  </select>

    <update id="updateByAreaSku">
      UPDATE area_sku
      set ladder_price = #{ladderPrice},
      price = #{price},
      update_time = now()
      <if test="updater != null">
        ,updater = #{updater}
      </if>
      where sku =#{sku} AND area_no = #{areaNo}
    </update>


    <update id="updatePriceByAreaSku">
        UPDATE area_sku
        set
        price = #{price},
        update_time = now()
        <if test="updater != null">
            ,updater = #{updater}
        </if>
        where sku =#{sku} AND area_no = #{areaNo}
    </update>


    <select id="selectVO" parameterType="net.summerfarm.model.vo.AreaSkuVO"
            resultType="net.summerfarm.model.vo.AreaSkuVO">
        select t.id, t.sku, t.area_no areaNo, t.quantity, t.original_price originalPrice, t.price, t.on_sale onSale,
        t.priority, t.update_time updateTime, t.share,
        t.add_time addTime, a.area_name areaName, t.ladder_price ladderPrice, t.sales_mode salesMode, t.limited_quantity
        limitedQuantity,t.info,t.m_type mType,p.pd_name pdName
        from area_sku t
        LEFT JOIN area a on a.area_no = t.area_no
        INNER JOIN inventory i ON t.sku = i.sku
        INNER JOIN products p ON i.pd_id = p.pd_id
        <where>
            <if test="sku !=null">
                AND t.sku = #{sku}
            </if>
            <if test="areaNo != null">
                AND t.area_no = #{areaNo}
            </if>
            <if test="parentNo != null">
                AND a.large_area_no = #{parentNo}
            </if>
            <if test="onSale != null">
                AND t.on_sale = #{onSale}
            </if>
        </where>
    </select>

    <select id="selectOne" parameterType="net.summerfarm.model.domain.AreaSku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from area_sku
        <where>
            <if test="sku!=null">
                AND sku =#{sku}
            </if>
            <if test="areaNo!=null">
                and area_no = #{areaNo}
            </if>
            <if test="id !=null">
                AND id =#{id}
            </if>
            <if test="onSale != null">
                and on_sale = #{onSale}
            </if>
        </where>
    </select>


    <insert id="insertSelective" parameterType="net.summerfarm.model.domain.AreaSku">
        insert into area_sku
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sku != null">
                sku,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="originalPrice != null">
                original_price,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="onSale != null">
                on_sale,
            </if>
            <if test="share != null">
                share,
            </if>
            <if test="show != null">
                `show`,
            </if>
            <if test="priority != null">
                priority,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="ladderPrice != null">
                ladder_price,
            </if>
            <if test="limitedQuantity != null">
                limited_quantity,
            </if>
            <if test="salesMode != null">
                sales_mode,
            </if>
            <if test="info != null">
                info,
            </if>
            <if test="mType != null">
                m_type,
            </if>
            <if test="cornerStatus != null">
                corner_status,
            </if>
            <if test="openSale != null">
                open_sale,
            </if>
            <if test="openSaleTime != null">
                open_sale_time,
            </if>
            <if test="closeSale != null">
                close_sale,
            </if>
            <if test="closeSaleTime != null">
                close_sale_time,
            </if>
            <if test="fixFlag != null">
                fix_flag,
            </if>
            <if test="fixNum != null">
                fix_num,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="areaNo != null">
                #{areaNo,jdbcType=INTEGER},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="originalPrice != null">
                #{originalPrice,jdbcType=DECIMAL},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="onSale != null">
                #{onSale,jdbcType=BIT},
            </if>
            <if test="share != null">
                #{share,jdbcType=BIT},
            </if>
            <if test="show != null">
                #{show},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ladderPrice != null">
                #{ladderPrice},
            </if>
            <if test="limitedQuantity != null">
                #{limitedQuantity},
            </if>
            <if test="salesMode != null">
                #{salesMode},
            </if>
            <if test="info != null">
                #{info},
            </if>
            <if test="mType != null">
                #{mType},
            </if>
            <if test="cornerStatus != null">
                #{cornerStatus},
            </if>
            <if test="openSale != null">
                #{openSale} ,
            </if>
            <if test="openSaleTime != null">
                #{openSaleTime} ,
            </if>
            <if test="closeSale != null">
                #{closeSale} ,
            </if>
            <if test="closeSaleTime != null">
                #{closeSaleTime} ,
            </if>
            <if test="fixFlag != null">
                #{fixFlag},
            </if>
            <if test="fixNum != null">
                #{fixNum},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.AreaSku">
        update area_sku
        SET
        <if test="sku != null">
            sku = #{sku,jdbcType=VARCHAR},
        </if>
        <if test="areaNo != null">
            area_no = #{areaNo,jdbcType=INTEGER},
        </if>
        <if test="quantity != null">
            quantity = #{quantity,jdbcType=INTEGER},
        </if>
        <!--<if test="originalPrice != null">-->
        <!--original_price = #{originalPrice,jdbcType=DECIMAL},-->
        <!--</if>-->
        <!--<if test="price != null">-->
        <!--price = #{price,jdbcType=DECIMAL},-->
        <!--</if>-->
        <!--<if test="ladderPrice != null">-->
        <!--ladder_price = #{ladderPrice},-->
        <!--</if>-->
        <if test="limitedQuantity != null">
            limited_quantity = #{limitedQuantity},
        </if>
        <if test="salesMode != null">
            sales_mode = #{salesMode},
        </if>
        <if test="onSale != null">
            on_sale = #{onSale,jdbcType=BIT},
        </if>
        <if test="share != null">
            share = #{onSale,jdbcType=BIT},
        </if>
        <if test="show != null">
            `show` = #{show},
        </if>
        <if test="priority != null">
            priority = #{priority,jdbcType=INTEGER},
        </if>
        <if test="updateTime != null">
            update_time = #{updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="addTime != null">
            add_time = #{addTime,jdbcType=TIMESTAMP},
        </if>
        <if test="info != null">
            info = #{info},
        </if>
        <if test="mType != null">
            m_type = #{mType},
        </if>
        <if test="cornerStatus != null">
            corner_status = #{cornerStatus},
        </if>
        <if test="updater != null and updater != ''">
            updater = #{updater},
        </if>
        open_sale = #{openSale},
        open_sale_time = #{openSaleTime} ,
        close_sale = #{closeSale} ,
        close_sale_time = #{closeSaleTime}

        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateOnSale">
        update area_sku
        <set>
            <if test="onSale != null">
                on_sale = #{onSale,jdbcType=BIT},
            </if>
            <if test="priority != null">
                priority = #{priority,jdbcType=INTEGER},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
        </set>
        where
        sku = #{sku}
        <if test="areaNo != null">
            AND area_no = #{areaNo,jdbcType=INTEGER}
        </if>

    </update>

    <update id="updateAdvance">
        update area_sku
        SET show_advance = #{showAdvance},
        advance = #{advance}
        WHERE area_no = #{areaNo}
        AND sku = #{sku}
    </update>

    <update id="emptyAdvance">
        update area_sku
        SET show_advance=0,advance=NULL
        WHERE area_no = #{areaNo}
        AND sku IN
        <foreach collection="skus" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </update>


    <select id="selectBySku" resultMap="BaseResultMap">
    select ak.* ,a.area_name, a.large_area_no areaNo
    FROM
    area a
    Left Join (
        select area_no,store_no from fence
        where status = 0
        group by area_no,store_no
    ) f on f.area_no = a.area_no
    LEFT  JOIN  area_sku ak ON  a.area_no=ak.area_no
    WHERE ak.sku= #{sku} AND f.store_no =#{areaNo}
  </select>

    <select id="selectByAreaNoAndSku" resultMap="BaseResultMap">
        select ak.*
        from area_sku ak
        where ak.sku=#{sku} and ak.area_no=#{areaNo}
    </select>

    <update id="updateOnlineStockShare">
        update area_sku set quantity=if(#{quantity}+quantity  &lt; 0,quantity,#{quantity}+quantity)
        WHERE area_no=#{areaNo} and sku=#{sku}
    </update>


    <select id="selectPrice" resultType="net.summerfarm.model.domain.AreaSku">
        SELECT DISTINCT ak.sku,IFNULL(ak.price,i.sale_price) price
        FROM area_sku ak
        LEFT JOIN area a ON ak.area_no=a.area_no
        LEFT JOIN inventory i ON ak.sku=i.sku
        LEFT JOIN (
            select * from fence
            where status = 0
            group by area_no
        ) f on f.area_no = a.area_no
        LEFT join warehouse_inventory_mapping wim on ak.sku = wim.sku and f.store_no = wim.store_no
        <where>
            <if test="sku != null">
                AND ak.sku = #{sku}
            </if>
            <if test="storeNo != null">
                AND wim.warehouse_no = #{storeNo}
            </if>
            <if test="onSale != null">
                AND ak.on_sale = #{onSale}
            </if>
        </where>
    </select>

    <select id="selectByKeys" resultType="net.summerfarm.model.vo.AreaSkuVO">
        SELECT DISTINCT wim.warehouse_no parentNo,ak.sku
        FROM area_sku ak
        INNER JOIN area a ON ak.area_no=a.area_no
        INNER JOIN inventory i ON ak.sku=i.sku
        INNER JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN (
            select * from fence
            where status = 0
            group by area_no
        ) f on f.area_no = a.area_no
        LEFT join warehouse_inventory_mapping wim on ak.sku = wim.sku and f.store_no = wim.store_no
        <where>
            <if test="sku != null">
                AND ak.sku = #{sku}
            </if>
            <if test="pdName != null">
                AND p.pd_name LIKE concat('%',#{pdName} ,'%')
            </if>
            <if test="warehouseNo != null">
                AND wim.warehouse_no = #{warehouseNo}
            </if>
            <if test="onSale != null">
                AND ak.on_sale = #{onSale}
            </if>
            <if test="pdId != null">
                AND p.pd_id = #{pdId}
            </if>
            <if test="skuList != null and skuList.size() > 0">
                AND ak.sku IN
                <foreach collection="skuList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByKeysGroupByAreaNoAndStoreNo" resultType="net.summerfarm.model.vo.AreaSkuVO">
        SELECT DISTINCT wim.warehouse_no parentNo,ak.sku
        FROM area_sku ak
        INNER JOIN area a ON ak.area_no=a.area_no
        LEFT JOIN (
        select * from fence
        where status = 0
        group by area_no, store_no
        ) f on f.area_no = a.area_no
        LEFT join warehouse_inventory_mapping wim on ak.sku = wim.sku and f.store_no = wim.store_no
        <where>
            <if test="sku != null">
                AND ak.sku = #{sku}
            </if>
            <if test="warehouseNo != null">
                AND wim.warehouse_no = #{warehouseNo}
            </if>
            <if test="onSale != null">
                AND ak.on_sale = #{onSale}
            </if>
            <if test="skuList != null and skuList.size() > 0">
                AND ak.sku IN
                <foreach collection="skuList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectVOList" resultType="net.summerfarm.model.vo.AreaSkuVO">
        SELECT
        ak.sku aksku,ak.id, ak.`original_price` originalPrice, ak.`price`, ak.`ladder_price` ladderPrice,
        ak.`limited_quantity` limitedQuantity, ak.`sales_mode` salesMode,
        ak.on_sale onSale,ak.corner_status cornerStatus,
        ak.area_no areaNo,ak.show, a.area_name areaName,ak.info,ak.m_type mType, ak.area_no areaNoFilter,
        ak.open_sale openSale, ak.open_sale_time openSaleTime, ak.close_sale closeSale, ak.close_sale_time closeSaleTime,
        wim.warehouse_no warehouseNo,a.large_area_no parentNo
        FROM area_sku ak
        RIGHT JOIN area a on a.area_no = ak.area_no and a.status = 1
        LEFT JOIN (
            select area_no ,store_no from fence
            where status = 0
        ) f on f.area_no = a.area_no
        inner join warehouse_inventory_mapping wim on ak.sku = wim.sku and f.store_no = wim.store_no
        <where>
            <if test="sku != null">
                AND ak.sku = #{sku}
            </if>
            <if test="areaNo != null">
                AND ak.area_no = #{areaNo}
            </if>
        </where>
        group by a.area_no
        order by a.type, a.large_area_no, ak.add_time
    </select>
    <update id="updateOffSaleBatch">
        update area_sku
        set on_sale = 0,`show` = 0,updater = #{updater}
        <where>
            sku in
            <foreach collection="sku" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            <if test="areaNo != null">
                and area_no = #{areaNo}
            </if>
        </where>
    </update>
    <update id="updateQualityDate">
        update area_sku ak
        left join area a on ak.area_no = a.area_no
        set ak.update_time = now()
        <choose>
            <when test="qualityDate != null">
                ,ak.info = #{qualityDate}
            </when>
            <otherwise>
                ,ak.info = null
            </otherwise>
        </choose>
        where sku = #{sku}
        <if test="storeNo != null">
            and a.parent_no = #{storeNo}
        </if>
        <if test="areaNo != null">
            and ak.area_no = #{areaNo}
        </if>
        <choose>
            <when test="qualityDate != null">
                and (ak.info is null or ak.info != #{qualityDate})
            </when>
            <otherwise>
                and ak.info is not null
            </otherwise>
        </choose>
    </update>

    <select id="selectVOs" parameterType="net.summerfarm.model.vo.AreaSkuVO"
            resultType="net.summerfarm.model.vo.AreaSkuVO">
        SELECT ak.sku,a.large_area_no parentNo
        FROM area_sku ak
        INNER JOIN area a ON ak.area_no=a.area_no
        LEFT JOIN inventory i ON ak.sku=i.sku
        LEFT JOIN products p ON i.pd_id=p.pd_id

        <where>
            <if test="sku != null">
                AND ak.sku = #{sku}
            </if>
            <if test="onSale != null">
                AND ak.on_sale = #{onSale}
            </if>
            <if test="pdId != null">
                AND p.pd_id = #{pdId}
            </if>
        </where>
    </select>


    <select id="selectSkuPriceBySkus" resultType="net.summerfarm.model.domain.AreaSku">
        SELECT t.sku,t.price
        FROM
        (
        SELECT ak.sku,ak.price
        FROM area_sku ak
        INNER JOIN area a ON ak.area_no = a.area_no
        <where>
            ak.area_no = #{areaNo}
            <if test="skus != null and skus.size!=0">
                AND ak.sku IN
                <foreach collection="skus" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY ak.on_sale DESC ) t
        GROUP BY t.sku
    </select>

    <select id="selectSkuPrice" resultType="net.summerfarm.model.domain.AreaSku">
        SELECT t.sku,t.price
        FROM
        (
        SELECT ak.sku,ak.price
        FROM area_sku ak
        INNER JOIN area a ON ak.area_no = a.area_no
        LEFT JOIN (
            select *
            from fence
            where status = 0
            group by area_no
        ) f on f.area_no = a.area_no
        LEFT join warehouse_inventory_mapping wim on ak.sku = wim.sku and f.store_no = wim.store_no
        INNER JOIN area_store ar ON wim.warehouse_no = ar.area_no AND ak.sku=ar.sku
        <where>
            ar.area_no = #{areaNo}
            <if test="skus != null and skus.size!=0">
                AND ak.sku IN
                <foreach collection="skus" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY ak.on_sale DESC ) t
        GROUP BY t.sku
    </select>

    <select id="selectSortList" parameterType="net.summerfarm.model.input.InventoryReq" resultType="net.summerfarm.model.vo.AreaSkuVO">
        select * from (select if(fixFlag = 1, fn, @num := @num + 1) fixNum,
        picturePath,
        sku,
        pdName,
        weight,
        onSale,
        `show`,
        areaNo,
        mType,
        pdPriority,
        fixFlag,
        id,
        sort
        from (
        select p.picture_path picturePath,
        ak.sku,
        p.pd_name pdName,
        i.weight,
        ak.on_sale onSale,
        ak.`show`,
        ak.area_no areaNo,
        ak.m_type mType,
        pd_priority pdPriority,
        ak.fix_flag fixFlag,
        fix_num fn,
        ak.id id,
        if(ak.fix_flag = 1, ak.fix_num * -1, pd_priority) sort
        from area_sku ak
        left join inventory i on ak.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        left join interest_rate_config irc on ak.area_no = irc.area_no and ak.sku = irc.sku
        <where>
            and i.outdated = 0
            and ak.show = 1
            and on_sale = 1
            <if test="areaNo != null">
                and ak.area_no = #{areaNo}
            </if>
        </where>
        order by fixFlag desc, sort desc, irc.interest_rate desc
        ) t,(select @num := 4) as num) t2
        <where>
            <if test="sku != null">
                and t2.sku like concat('%',#{sku},'%')
            </if>
            <if test="pdName != null">
                and t2.pdName like concat('%',#{pdName},'%')
            </if>
            <if test="mType != null">
                and t2.mType = #{mType}
            </if>
            <if test="fixFlag != null">
                and t2.fixFlag = #{fixFlag}
            </if>
            <if test="outFlag != null">
                <choose>
                    <when test="outFlag == 0">
                        and t2.pdPriority &gt; 0
                    </when>
                    <otherwise>
                        and t2.pdPriority &lt; 0
                    </otherwise>
                </choose>
            </if>
        </where>
        order by fixNum
    </select>

    <update id="updatePdPriority">
    update area_sku set pd_priority = #{pdPriority} where sku = #{sku} and area_no = #{areaNo}
  </update>
    <update id="closeShowAndSale">
        update area_sku
        set `show` = 0, on_sale = 0, updater = #{updater}
        where sku in
        <foreach collection="array" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectAutoSale" resultType="net.summerfarm.model.domain.AreaSku">
        SELECT ak.id, ak.on_sale onSale, ak.sku, ak.area_no areaNo
        FROM area a
        INNER JOIN area_sku ak ON a.area_no = ak.area_no
        LEFT JOIN (
            select *
            from fence
            where status = 0
            group by area_no
        ) f on f.area_no = a.area_no
        left join warehouse_inventory_mapping wim on wim.store_no = f.store_no and wim.sku = ak.sku
        WHERE wim.warehouse_no = #{storeNo}
        AND ak.sku = #{sku}
        <if test="onSale != null">
            AND ak.on_sale = #{onSale}
        </if>
        <if test="openSale != null">
            AND ak.open_sale = #{openSale}
        </if>
        <if test="closeSale != null">
            AND ak.close_sale = #{closeSale}
        </if>
    </select>

    <update id="clearOpenSale">
        update area_sku
        <set>
            open_sale = null ,
            open_sale_time = null ,
        </set>
        where
        sku = #{sku}
        <if test="areaNo != null">
            AND area_no = #{areaNo,jdbcType=INTEGER}
        </if>
    </update>

    <update id="clearCloseSale">
        update area_sku
        <set>
            close_sale = null ,
            close_sale_time = null ,
        </set>
        where
        sku = #{sku}
        <if test="areaNo != null">
            AND area_no = #{areaNo,jdbcType=INTEGER}
        </if>
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM area_sku
        WHERE id = #{id}
    </select>

    <select id="selectList" parameterType="net.summerfarm.model.domain.AreaSku" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM area_sku
        <where>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
            <if test="areaNo != null">
                AND area_no = #{areaNo}
            </if>
        </where>
    </select>

    <update id="resetFixFlag" parameterType="integer">
        update area_sku set fix_flag = 0 where area_no = #{areaNo}
    </update>

    <update id="updateFixSort">
        update area_sku set fix_flag = 1,fix_num = #{fixNum} where area_no = #{areaNo} and sku = #{sku}
    </update>

    <select id="selectSkuList" resultType="net.summerfarm.model.vo.AreaSkuVO">
        select p.picture_path picturePath, p.pd_name pdName, i.weight, ak.sku,ak.area_no areaNo
        from area_sku ak
        left join inventory i on ak.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        <where>
            ak.area_no = #{areaNo}
            <if test="skus != null and skus.size!=0">
                and ak.sku in
                <foreach collection="skus" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updatePdPriorityCurrent" parameterType="net.summerfarm.model.domain.AreaSku">
        update area_sku ak
        set ak.pd_priority = #{pdPriority}
        where ak.id = #{id} and abs(ak.pd_priority - #{pdPriority}) &gt; 2
    </update>


    <select id="selectAllSku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from area_sku
        where area_no = #{areaNo}

    </select>
    <select id="selectAreaSkuVOList" resultType="net.summerfarm.model.vo.AreaSkuVO">
        select ak.id,
        sku,
        ak.area_no areaNo,
        original_price originalPrice,
        price
        from area_sku ak
        left join area a on ak.area_no = a.area_no
        where ak.area_no = #{areaNo}
        and ak.sku in
        <foreach collection="skuList" item="sku" separator="," open="(" close=")">
            #{sku}
        </foreach>
    </select>

    <update id="updateOnSaleAndPrice" parameterType="net.summerfarm.model.domain.AreaSku">
        update area_sku
        <set>
            <if test="onSale != null">
                on_sale = #{onSale,jdbcType=BIT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
        </set>
        where sku = #{sku} AND area_no = #{areaNo,jdbcType=INTEGER} and id =#{id}
    </update>

    <select id="selectSkuInStore" resultType="string">
        SELECT ars.sku sku, ars.on_sale onSale, iv.inv_id id
        FROM area_sku ars
        INNER JOIN area a ON ars.area_no = a.area_no
        INNER JOIN inventory iv ON ars.sku = iv.sku
        LEFT JOIN (
            select *
            from fence
            where status = 0
            group by area_no ,store_no
        ) f on f.area_no = a.area_no
        LEFT join warehouse_inventory_mapping wim on ars.sku = wim.sku and f.store_no = wim.store_no
        <where>
            <if test="storeNo != null">
                and wim.warehouse_no = #{storeNo}
            </if>
            <if test="onSale != null">
                and ars.on_sale = #{onSale}
            </if>
        </where>
        GROUP BY iv.inv_id,ars.on_sale
    </select>

    <select id="selectCloseSale" resultType="net.summerfarm.model.domain.AreaSku">
        select ak.id,
            ak.sku,
            ak.area_no areaNo
        from area_sku ak
        left join area a on ak.area_no = a.area_no
        LEFT JOIN (
            select *
            from fence
            where status = 0
            group by area_no ,store_no
        ) f on f.area_no = a.area_no
        left join warehouse_inventory_mapping wim on f.store_no = wim.store_no and wim.sku = ak.sku
        left join area_store a1 on wim.warehouse_no = a1.area_no and ak.sku = a1.sku
        where ak.close_sale = 1
            and ak.on_sale = 1
            and a1.online_quantity &lt;= 0
    </select>

    <select id="selectAreaSkuInfo" parameterType="net.summerfarm.model.vo.AreaSkuVO"
            resultType="net.summerfarm.model.vo.AreaSkuVO">
        select ak.area_no areaNo,a.area_name areaName,ak.`show`,ak.on_sale onSale
        from area_sku ak
        left join area a on ak.area_no = a.area_no
        inner join (
            select  area_no,store_no
            from  fence
            where status = 0  and store_no = #{parentNo}
            group by area_no,store_no
        ) f on  f.area_no = a.area_no
        <where>
             ak.sku = #{sku}
            <if test="onSale !=null">
                AND ak.on_sale = #{onSale}
            </if>
            <if test="show != null">
                and ak.`show` = #{show}
            </if>
        </where>

    </select>

    <select id="selectSkuInStoreByStore" resultType="string">
        SELECT ars.sku sku, ars.on_sale onSale, iv.inv_id id
        FROM area_sku ars
        INNER JOIN area a ON ars.area_no = a.area_no
        INNER JOIN inventory iv ON ars.sku = iv.sku
        left join (
            select  * from  fence
            where status = 0
            group by area_no,store_no
        ) f on  f.area_no = a.area_no
        <where>
            <if test="storeNoList != null">
                and f.store_no in
                <foreach collection="storeNoList" item="storeNo" separator="," open="(" close=")">
                    #{storeNo}
                </foreach>
            </if>
            <if test="onSale != null">
                and ars.on_sale = #{onSale}
            </if>
        </where>
        GROUP BY iv.inv_id,ars.on_sale
    </select>

    <select id="selectSku" resultType="string">
        SELECT ars.sku sku
        FROM area_sku ars
        INNER JOIN area a ON ars.area_no = a.area_no
        INNER JOIN inventory iv ON ars.sku = iv.sku
        INNER JOIN (
        select  * from  fence
        where status = 0
        group by area_no,store_no
        ) f on  f.area_no = a.area_no
        INNER JOIN warehouse_inventory_mapping wim on ars.sku = wim.sku and f.store_no = wim.store_no
        <where>
            <if test="storeNoList != null">
                and f.store_no in
                <foreach collection="storeNoList" item="storeNo" separator="," open="(" close=")">
                    #{storeNo}
                </foreach>
            </if>
            <if test="onSale != null">
                and ars.on_sale = #{onSale}
            </if>
            <if test="warehouseNo != null">
                and wim.warehouse_no = #{warehouseNo}
            </if>
        </where>
        GROUP BY iv.inv_id,ars.on_sale
    </select>

    <select id="lowerCostWhenCostChange" resultType="net.summerfarm.model.vo.CostChangeVo">
        select asr.cost_price as costPrice,asr.area_no as areaNo,asr.sku,ask.price from area_store asr
            left join area_sku ask on asr.sku = ask.sku and asr.area_no = ask.area_no
        where ask.on_sale = 1 and ask.sku =#{sku} and ask.area_no= #{areaNo}
    </select>
    <select id="selectNearExpiredSku" resultType="net.summerfarm.model.vo.AreaSkuVO">
        select i.sku,ifnull(ask.original_price,0) originalPrice,ask.price,ask.area_no areaNo,
               p.warn_time warnTime,p.pd_name pdName,i.unit,i.weight,
               p.picture_path picturePath, ifnull(`ar`.quantity, 0) quantity, ifnull(ar.lock_quantity, 0) lockQuantity,
        ifnull(ar.safe_quantity, 0) safeQuantity,
        i.pd_id pdId, wim.warehouse_no warehouseNo
            from
                inventory i
        inner join area_store `ar` on  i.sku = `ar`.sku
        inner join warehouse_inventory_mapping wim on wim.sku = i.sku and `ar`.area_no = wim.warehouse_no
        inner join (
        select area_no,store_no from fence where status = 0
        group by area_no,store_no
        ) f on f.store_no = wim.store_no
        inner join area_sku ask on i.sku = ask.sku and ask.area_no =f.area_no
        left join products p on i.pd_id = p.pd_id
        where i.ext_type = #{extType} and ask.on_sale = 1
            <if test="areaNo == null">
                and ask.area_no in (
                select distinct area_no from area a
                left join large_area la on a.large_area_no = la.large_area_no
                where a.status = 1 and la.status =1)
            </if>
             <if test="areaNo != null">
                and ask.area_no = #{areaNo}
             </if>
             <if test="sku != null">
                and i.sku = #{sku}
             </if>
        order by wim.warehouse_no desc
    </select>

    <select id="listNearExpiredSku" resultType="net.summerfarm.model.vo.AreaSkuVO">
        select i.sku,ifnull(ask.original_price,0) originalPrice,ask.price,ask.area_no areaNo,
        i.unit,i.weight, i.pd_id pdId, a.large_area_no largeAreaNo
        from inventory i
        inner join area_sku ask on i.sku = ask.sku
        inner join area a on ask.area_no = a.area_no
        where i.ext_type = 2 and ask.on_sale = 1 and i.outdated = 0
    </select>

    <select id="listWareHouseSkuMapping" resultType="net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping">
        select warehouse_no wareHouseNo, store_no storeNo, sku
        from warehouse_inventory_mapping
        where
        store_no in
        <foreach collection="storeNos" item="storeNo" open="(" separator="," close=")">
            #{storeNo}
        </foreach>
        and sku in
        <foreach collection="skus" item="sku" open="(" separator="," close=")">
            #{sku}
        </foreach>
    </select>

    <select id="selectSkuMaxPriceInAllArea" resultType="java.math.BigDecimal">
        select max(price)
        from area_sku
        where sku = #{sku}
    </select>

    <select id="selectSkuAreaPrice" resultMap="BaseResultMap">
        select sku, area_no, original_price , price
        from area_sku
        where sku = #{sku} and area_no in <foreach collection="areaNos" item="areaNo" open="(" close=")" separator=",">
                                            #{areaNo}
                                          </foreach> and on_sale = 1
    </select>

    <select id="selectAllBySku" resultType="net.summerfarm.model.domain.AreaSku">
        select price,original_price as originalPrice,sku,ask.area_no as areaNo,ladder_price ladderPrice
        from area_sku ask left join area a on ask.area_no = a.area_no
        where sku =#{sku} and a.status = 1
    </select>

    <select id="selectByWarehouseNo" resultType="java.lang.String" parameterType="net.summerfarm.model.input.InventoryReq">
        select distinct  i.sku  FROM inventory i
  		LEFT JOIN products p on i.pd_id = p.pd_id
        left join  category c on c.id=p.category_id
        LEFT JOIN area_sku ak on ak.sku = i.sku
        LEFT join fence f on f.area_no =ak.area_no
        LEFT join warehouse_logistics_center wlc on wlc.store_no = f.store_no
        left join warehouse_inventory_mapping wim on ak.sku = wim.sku and f.store_no = wim.store_no
        <where>
        <if test="categoryTypeList != null and categoryTypeList.size != 0">
          and c.type in
            <foreach collection="categoryTypeList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="onSale != null">
            and ak.on_sale =#{onSale}
        </if>
        <if test="type != null">
            and i.`type` =#{type}
        </if>
        <if test="extType != null">
            and i.ext_type = #{extType}
        </if>
        <if test="mType != null">
            and ak.m_type =#{mType}
        </if>
        <if test="warehouseNo != null">
            and wim.warehouse_no=#{warehouseNo}
        </if>
        </where>
    </select>

    <select id="selectOnSaleByWarehouseNo" resultType="java.lang.String" parameterType="net.summerfarm.model.input.InventoryReq">
        select distinct ass.sku
        from area_sku ass
        left join fence f on f.area_no =ass.area_no
        left join warehouse_logistics_center wlc on wlc.store_no = f.store_no
        left join warehouse_inventory_mapping wim on ass.sku = wim.sku and f.store_no = wim.store_no
        where wim.warehouse_no= #{warehouseNo} and  ass.on_sale=#{onSale}
    </select>

    <select id="selectSelfStoreAutoSale" resultType="net.summerfarm.model.domain.AreaSku">
        SELECT ak.id, ak.on_sale onSale, ak.sku, ak.area_no areaNo
        FROM area a
        Inner join (
        select area_no,store_no from fence where status = 0 and store_no = #{storeNo}
        group by area_no
        ) f on a.area_no = f.area_no
        INNER JOIN area_sku ak ON a.area_no = ak.area_no
        WHERE ak.sku = #{sku}
        AND ak.on_sale = #{onSale}
        <if test="openSale != null">
            AND ak.open_sale = #{openSale}
        </if>
        <if test="closeSale != null">
            AND ak.close_sale = #{closeSale}
        </if>
    </select>

    <select id="selectAutoSaleNew" resultType="net.summerfarm.model.domain.AreaSku">

        SELECT ak.id, ak.on_sale onSale, ak.sku, ak.area_no areaNo
        FROM area_store ar
        INNER join warehouse_inventory_mapping wim on ar.area_no = wim.warehouse_no and wim.sku = ar.sku
        inner join (
        select area_no,store_no from fence where status = 0
        group by area_no,store_no
        ) f on f.store_no = wim.store_no
        INNER JOIN area_sku ak ON f.area_no = ak.area_no AND ak.sku = ar.sku
        <where>
            ar.area_no = #{storeNo} and  ar.sku= #{sku}  AND ak.on_sale = #{onSale}
            <if test="openSale != null">
                AND ak.open_sale = #{openSale}
            </if>
            <if test="closeSale != null">
                AND ak.close_sale = #{closeSale}
            </if>
        </where>
        group by ak.area_no

    </select>

    <select id="selectBySkuAndAreaNos" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from area_sku
        where sku=#{sku} and area_no in
                                <foreach item="areaNo" index="index" collection="areaNos" open="(" separator="," close=")">
                                    #{areaNo,jdbcType=INTEGER}
                                </foreach>
    </select>
    <select id="queryAreaNoBySkuAndWarehouseNo" resultType="java.lang.Integer">
        select distinct f.area_no from warehouse_inventory_mapping wim
                                           left join fence f on wim.store_no = f.store_no
                                           left join area_sku asku on f.area_no = asku.area_no and wim.sku = asku .sku
        where wim.warehouse_no = #{warehouseNo} and wim.sku = #{sku} and f.status = 0  and asku.on_sale = 1
    </select>
    <select id="querySkuPriceInfo" resultType="com.cosfo.summerfarm.model.dto.SummerfarmSkuDTO">
        select
        i.inv_id skuId,i.pd_id spuId,a.sku,max(price) maxPrice,min(price) minPrice
        from
        area_sku a
        left join inventory i on a.sku = i.sku
        where
        i.inv_id in
        <foreach item="item" index="index" collection="skuIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by i.inv_id,i.pd_id,a.sku
    </select>

    <resultMap id="AreaCopyDetailResultMap" type="net.summerfarm.model.domain.AreaCopyDetail">
        <result column="sku" property="sku"/>
        <result column="original_price" property="originalPrice"/>
        <result column="new_price" property="newPrice"/>
        <result column="origin_interest_rate" property="originInterestRate"/>
        <result column="new_interest_rate" property="newInterestRate"/>
        <result column="auto_flag" property="autoFlag"/>
        <result column="on_sale" property="onSale"/>
        <result column="show" property="homeShow"/>
        <result column="close_sale" property="closeSale"/>
        <result column="close_sale_time" property="closeSaleTime"/>
        <result column="open_sale" property="openSale"/>
        <result column="open_sale_time" property="openSaleTime"/>
        <result column="sales_mode" property="salesMode"/>
        <result column="corner_status" property="cornerStatus"/>
        <result column="corner_open_time" property="cornerOpenTime"/>
        <result column="m_type" property="mType"/>
        <result column="limited_quantity" property="limitedQuantity"/>
    </resultMap>
    <select id="selectBySourceAreaNoSkuAndTargetAreaNo" resultMap="AreaCopyDetailResultMap">
        select as1.sku,
                as2.price as original_price,
                as1.price as new_price,
                as2.interest_rate as origin_interest_rate,
                as1.interest_rate as new_interest_rate,
                as1.auto_flag, as1.on_sale,
                as1.`show`,
                as1.close_sale,
                as1.close_sale_time,
                as1.open_sale,
                as1.open_sale_time,
                as1.sales_mode,
                as1.corner_status,
                as1.corner_open_time,
                as1.m_type,
                as1.limited_quantity
        from (select a1.sku,a1.price,interest_rate,auto_flag,on_sale,`show`,close_sale,close_sale_time,open_sale,open_sale_time,
                a1.sales_mode sales_mode,corner_status,corner_open_time,m_type,a1.limited_quantity
              from area_sku a1
                    left join interest_rate_config irc1 on irc1.area_no = a1.area_no and a1.sku = irc1.sku
                    left join inventory i on a1.sku = i.sku
              where a1.area_no = #{sourceAreaNo} and i.outdated = 0) as as1
        join (select a2.sku,a2.price,interest_rate
              from area_sku a2
                    left join interest_rate_config irc2 on a2.area_no = irc2.area_no and a2.sku = irc2.sku
                    left join inventory i on a2.sku = i.sku
              where a2.area_no = #{targetAreaNo} and i.outdated = 0) as2 on as1.sku = as2.sku
        limit #{pageVo.index}, #{pageVo.pageSize}
    </select>

    <update id="updateByAreaCopyDetail">
        update area_sku
        set on_sale = #{areaCopyDetail.onSale},
            `show` = #{areaCopyDetail.homeShow},
            close_sale = #{areaCopyDetail.closeSale},
            close_sale_time = #{areaCopyDetail.closeSaleTime},
            open_sale = #{areaCopyDetail.openSale},
            open_sale_time = #{areaCopyDetail.openSaleTime},
            sales_mode = #{areaCopyDetail.salesMode},
            corner_status = #{areaCopyDetail.cornerStatus},
            corner_open_time = #{areaCopyDetail.cornerOpenTime},
            m_type = #{areaCopyDetail.mType},
            limited_quantity = #{areaCopyDetail.limitedQuantity}
        where area_no = #{areaNo} and sku = #{areaCopyDetail.sku};
    </update>
    <select id="querySkuPrice"
            resultType="com.cosfo.summerfarm.model.dto.supplyprice.SummerfarmSkuPriceInfoDTO">
        select min(price) minPrice, max(price) maxPrice
        from area_sku
        where on_sale = 1
          and sku = #{sku}
          and area_no in
        <foreach collection="areaNos" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectNotDeleteData" resultMap="BaseResultMap">
        select ak.id,ak.sku,ak.area_no,ak.quantity,ak.`share`,ak.original_price,ak.price,ak.update_time,ak.on_sale,ak.add_time,
               ak.priority,ak.pd_priority,ak.ladder_price,ak.limited_quantity,ak.sales_mode,ak.`show`,ak.info,ak.m_type,ak.show_advance,
               ak.advance,ak.corner_status,ak.corner_open_time,ak.open_sale,ak.open_sale_time,ak.close_sale,ak.close_sale_time,
               ak.fix_flag,ak.fix_num
        from area_sku ak
            left join inventory i on ak.sku = i.sku
            left join products p on i.pd_id = p.pd_id
        where i.outdated <![CDATA[ <= ]]> 0
            and p.outdated <![CDATA[ <= ]]> 0
            and ak.area_no = #{areaNo}
        order by ak.id
        limit #{pageStart}, #{pageEnd}
    </select>

    <select id="selectBySkuAndAreaNo" resultMap="ResultMap">
        select ak.*, a.area_name, a.large_area_no
        from area a
        left join area_sku ak on  a.area_no=ak.area_no
        where ak.sku= #{sku} and ak.area_no =#{areaNo}
    </select>
    <select id="selectListBySkuAndAreaNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from area_sku a
        <where>
            <if test="sku != null">
                and a.sku= #{sku}
            </if>
            <if test="areaNo != null">
                and a.area_no =#{areaNo}
            </if>
        </where>
    </select>
    <select id="selectNeedSyncByUpdateTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from area_sku a
        <where>
            <if test="startTime != null and endTime != null">
                AND a.update_time between #{startTime} and #{endTime}
            </if>
            <if test="id != null">
                AND a.id  <![CDATA[> ]]>  #{id}
            </if>
            <if test="maxId != null">
                AND a.id <![CDATA[<= ]]> #{maxId}
            </if>
        </where>
        order by a.id asc
        limit 500
    </select>
    <select id="selectNeedSyncByUpdateTimeDesc" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from area_sku a
        <where>
            <if test="startTime != null and endTime != null">
                AND a.add_time between #{startTime} and #{endTime}
            </if>
<!--            <if test="id != null">-->
<!--                AND a.id  <![CDATA[> ]]>  #{id}-->
<!--            </if>-->
            <if test="maxId != null">
                AND a.id <![CDATA[< ]]> #{maxId}
            </if>
        </where>
        order by a.id desc
        limit 15
    </select>
    <select id="queryListSkuPrice" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from area_sku
        where on_sale = 1
        and sku = #{sku}
        and area_no in
        <foreach collection="areaNos" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="checkPrice" resultType="java.lang.Integer">
        SELECT COUNT(*)  FROM `area_sku` ask LEFT JOIN `area` a on ask.`area_no` = a.`area_no`
        WHERE ask.`sku` = #{sku,jdbcType=VARCHAR} and ask.`on_sale` = 1  and a.`status` = 1
        and a.`area_name` not LIKE CONCAT('%', '测试','%') and #{price,jdbcType=DECIMAL} >= ask.price
        and a.large_area_no = #{largeAreaNo,jdbcType=INTEGER}
    </select>

    <select id="selectAllBySkuAndOnSale" resultType="net.summerfarm.model.vo.AreaSkuVO">
        select price,original_price as originalPrice,sku,ask.area_no as areaNo,ladder_price ladderPrice, a.large_area_no largeAreaNo
        from area_sku ask left join area a on ask.area_no = a.area_no
        where ask.sku =#{sku} and a.status = 1 and ask.on_sale = #{onSale} and a.`area_name` not LIKE CONCAT('%', '测试','%')
    </select>

    <select id="selectSkuAreaByLareaAreaNo" resultMap="BaseResultMap">
        SELECT ask.sku, ask.area_no, ask.price FROM `area_sku` ask LEFT JOIN `area` a on ask.`area_no` = a.`area_no`
        WHERE ask.`sku` = #{sku,jdbcType=VARCHAR} and ask.`on_sale` = 1  and a.`status` = 1
          and a.`area_name` not LIKE CONCAT('%', '测试','%') and a.large_area_no = #{largeAreaNo,jdbcType=INTEGER}
    </select>

    <select id="getMaxId" resultType="java.lang.Integer">
        select max(id)
        from area_sku ask
    </select>

    <select id="pageById" resultMap="ResultMap">
        select *
        from area_sku
        where id &gt;= #{id}
        order by id asc
        limit #{pageSize}
    </select>

    <select id="listByAreaNo" resultMap="ResultMap">
        SELECT ak.id, ak.sku, ak.area_no, ak.price, ak.ladder_price
        FROM area_sku ak
            left join inventory i on ak.sku = i.sku
        where `area_no` = #{areaNo} and on_sale = 1 and i.ext_type = 0 and i.sub_type = 3
          and i.create_type in (0,1) and i.outdated = 0 order by ak.id limit #{offset}, #{pageSize}
    </select>

    <update id="updatePriceCMS" parameterType="net.summerfarm.model.DTO.AreaSkuUpdatePriceDTO">
        update area_sku
        set price = #{newPrice}
        <if test="ladderPrice != null">
            ,ladder_price = #{ladderPrice}
        </if>
        where id = #{id} and price = #{originalPrice}
    </update>
    <update id="udpateOnsale4CloseSaleBatchByIds">
        update area_sku
        set on_sale = 0, close_sale = #{closeSale},open_sale = null
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="udpateOnsale4OpenSaleBatchByIds">
        update area_sku
        set on_sale = 1,open_sale = #{openSale},close_sale = null
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <select id="getAllLadderArea" resultMap="ResultMap">
        select la.large_area_no,la.large_area_name,t.sku
        from   large_area la
                   inner join area a on a.large_area_no  = la.large_area_no
                   inner join area_sku t on t.`area_no` = a.`area_no`
        where t.`ladder_price` is not null
          and t.`ladder_price` != '[]'
        and t.`on_sale` = 1
        and a.`status`  = 1
        and la.status = 1
        <if test="largeAreaNo != null">
            and la.large_area_no = #{largeAreaNo}
        </if>
        group by la.`large_area_no`,la.large_area_name, t.sku;
    </select>



    <select id="getPendingLadderPrice" resultMap="ResultMap">
        SELECT t.sku, t.area_no, t.price, t.ladder_price
        from   large_area la
                   inner join area a on a.large_area_no  = la.large_area_no
                   inner join area_sku t on t.`area_no` = a.`area_no`
        inner join fence f on f.area_no = a.area_no
        where t.`ladder_price` is not null
          and t.`ladder_price` != '[]'
        and t.`on_sale` = 1
        and a.`status`  = 1
        and la.status = 1
        and f.status = 0
        and la.`large_area_no` = #{largeAreaNo}
        and t.sku = #{sku} order by t.area_no limit 1
    </select>
    <select id="selectBySkus" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from area_sku where sku in
        <foreach collection="skus" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
