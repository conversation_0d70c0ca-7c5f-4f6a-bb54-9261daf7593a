<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.CompanyAccountMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CompanyAccount">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="wx_account_info" property="wxAccountInfo" jdbcType="VARCHAR"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="addtime" property="addtime"/>
        <result column="channel" property="channel"/>
    </resultMap>

    <resultMap id="VOMap" type="net.summerfarm.model.vo.CompanyAccountVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="wx_account_info" property="wxAccountInfo" jdbcType="VARCHAR"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="addtime" property="addtime"/>
        <collection property="areaVOS" ofType="net.summerfarm.model.vo.AreaVO">
            <result column="area_name" property="areaName" jdbcType="VARCHAR"/>
            <result column="realname" property="realname" jdbcType="VARCHAR"/>
            <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
            <result column="status" property="status" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <sql id="BaseColumn">
        id,company_name,wx_account_info,admin_id,addtime,channel
    </sql>

    <insert id="insert" parameterType="net.summerfarm.model.domain.CompanyAccount" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO company_account(company_name,wx_account_info,addtime,admin_id)
        VALUES (#{companyName},#{wxAccountInfo} ,#{addtime} ,#{adminId} )
    </insert>

    <select id="selectList" parameterType="net.summerfarm.model.domain.CompanyAccount" resultType="net.summerfarm.model.vo.CompanyAccountVO">
        SELECT ca.id,ca.company_name companyName,ca.wx_account_info wxAccountInfo,ca.admin_id adminId,ca.addtime,ad.realname adminName,ca.channel
        FROM company_account ca
        LEFT JOIN admin ad ON ad.admin_id=ca.admin_id
        <if test="areaNo != null">
            INNER JOIN area a ON ca.id = a.company_account_id
        </if>
        <where>
            <if test="id != null">
                AND ca.id = #{id}
            </if>
            <if test="companyName != null">
                AND ca.company_name LIKE CONCAT('%',#{companyName} ,'%')
            </if>
            <if test="areaNo != null">
                AND a.area_no = #{areaNo}
            </if>
        </where>
        order by ca.id desc
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="VOMap">
        SELECT ca.id,ca.company_name,ca.wx_account_info,ca.admin_id,ca.addtime,a.area_name,a.area_no,a.status,a2.realname
        FROM company_account ca
        LEFT JOIN area a ON  ca.id = a.company_account_id
        LEFT JOIN admin a2 ON  a.admin_id = a2.admin_id
        WHERE ca.id = #{id,jdbcType=INTEGER}
    </select>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CompanyAccount">
        UPDATE company_account
        <set>
            <if test="wxAccountInfo != null">
                wx_account_info = #{wxAccountInfo} ,
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectOne" parameterType="net.summerfarm.model.domain.CompanyAccount" resultType="net.summerfarm.model.domain.CompanyAccount">
        SELECT ca.id,ca.company_name companyName,ca.wx_account_info wxAccountInfo,ca.admin_id adminId,ca.addtime
        FROM company_account ca
        <where>
            <if test="companyName != null">
                AND ca.company_name = #{companyName}
            </if>
        </where>
    </select>
    <select id="selectFixAccount" resultMap="BaseResultMap">
        select
        <include refid="BaseColumn"/>
        from company_account where channel in (0, 2)
        order by channel
    </select>
</mapper>