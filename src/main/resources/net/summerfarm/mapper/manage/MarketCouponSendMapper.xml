<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.MarketCouponSendMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="net.summerfarm.model.domain.MarketCouponSend">
        insert into market_coupon_send(`coupon_id`,`status`,`sender_name`,`audit_name`,`reason`,`send_msg_flag`,
                                       `send_type`,`release_time`,`actual_release_time`,`send_client`,`cash_customer`,`account_customer`,
                                       `send_status`)
        values (#{couponId},#{status},#{senderName},#{auditName},#{reason},#{sendMsgFlag},
                #{sendType},#{releaseTime},#{actualReleaseTime},#{sendClient},#{cashCustomer},#{accountCustomer},
                #{sendStatus})
    </insert>

    <update id="updateStatus">
        update market_coupon_send set status = #{status}, audit_name =#{auditName} where id = #{id} and status = 0
    </update>

    <select id="selectByPrimaryKey" resultType="net.summerfarm.model.domain.MarketCouponSend">
        select `id`,`coupon_id` as couponId,`status`,`sender_name` as senderName,`audit_name` as auditName,`reason`,`send_msg_flag` as sendMsgFlag,
            `send_type` as sendType,`release_time` as releaseTime,`actual_release_time` as actualReleaseTime,`send_client` as sendClient,
               `cash_customer` as cashCustomer,`account_customer` as accountCustomer,`send_status` as sendStatus
        from market_coupon_send where id =#{id}
    </select>

    <select id="select" resultType="net.summerfarm.model.vo.MarketCouponSendVO" parameterType="net.summerfarm.model.input.MarketCouponSendQuery">
        select mcs.id,mcs.coupon_id couponId,mcs.status,mcs.sender_name senderName,mcs.audit_name auditName,mcs.create_time createTime, mcs.`send_status` sendStatus,
               (case when c.agio_type = 1 then '商品优惠券'
                     when c.agio_type = 2 then '普通运费优惠券'
                     when c.agio_type = 3 then '精准送运费优惠券'
                     when c.agio_type = 4 then '红包'
                     when c.agio_type = 5 then '商品兑换券' end ) couponType,mcs.`send_type` sendType,mcs.`release_time` releaseTime, `actual_release_time` actualReleaseTime,
               c.name couponName,c.money,c.threshold from market_coupon_send mcs left join coupon c on mcs.coupon_id = c.id
        <where>
            <if test="couponId != null">
                and mcs.coupon_id =#{couponId}
            </if>
            <if test="couponName != null">
                and c.name like concat('%',#{couponName},'%')
            </if>
            <if test="money != null">
                and c.money = #{money}
            </if>
            <if test="status != null">
                and mcs.status = #{status}
            </if>
            <if test="sendStatus != null">
                and mcs.send_status = #{sendStatus}
            </if>
            <if test="startTime != null">
                AND mcs.create_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND mcs.create_time <![CDATA[<]]> #{endTime}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectByPrimaryKeyForceMaster" resultType="net.summerfarm.model.domain.MarketCouponSend">
        /*FORCE_MASTER*/ select `id`,`coupon_id` as couponId,`status`,`sender_name` as senderName,`audit_name` as auditName,`reason`,`send_msg_flag` as sendMsgFlag from market_coupon_send where id =#{id}
    </select>

    <update id="updateSendStatus">
        update market_coupon_send set send_status = #{sendStatus} where id = #{id} and send_status = #{originSendStatus}
    </update>

    <select id="selectByEntity" resultType="net.summerfarm.model.domain.MarketCouponSend">
        select `id`,`coupon_id` as couponId,`status`,`sender_name` as senderName,`audit_name` as auditName,`reason`,`send_msg_flag` as sendMsgFlag,
            `send_type` as sendType,`release_time` as releaseTime,`actual_release_time` as actualReleaseTime,`send_client` as sendClient,
               `cash_customer` as cashCustomer,`account_customer` as accountCustomer,`send_status` as sendStatus
        from market_coupon_send
        <where>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="sendType != null">
                and send_type = #{sendType}
            </if>
            <if test="sendStatus != null">
                and send_status = #{sendStatus}
            </if>
            <if test="releaseTime != null">
                and #{releaseTime} >= release_time
            </if>
        </where>
    </select>

    <update id="updateByPrimaryKeySelective">
        update market_coupon_send
        <set>
            <if test="actualReleaseTime != null">
                actual_release_time = #{actualReleaseTime},
            </if>
            <if test="sendStatus != null">
                send_status = #{sendStatus},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateSendStatusNotOrigin">
        update market_coupon_send set send_status = #{sendStatus} where id = #{id}
    </update>
</mapper>