<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTaskPickItemMapper">

    <insert id="insertBatch"  parameterType="net.summerfarm.model.domain.StockTaskPickItem"  useGeneratedKeys="true" keyProperty="id">
        insert into stock_task_pick_item (`add_time`, `sku`,`amount` ,`admin_id`,`admin_name`,`type`,out_store_no,store_no,delivery_time,close_order_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.addTime},#{item.sku},#{item.amount},#{item.adminId},#{item.adminName},#{item.type},#{item.outStoreNo},#{item.storeNo},
            #{item.deliveryTime},#{item.closeOrderTime})
        </foreach>
    </insert>

    <select id="selectList" resultType= "net.summerfarm.model.vo.StockTaskPickVO">
        select
        stpi.`sku` ,stpi.`amount` ,
        stpi.`admin_id` adminId,
        stpi.`admin_name` adminName,
        stpi.`type`,stpi.out_store_no outStoreNo
         ,stpi.store_no storeNo,stpi.delivery_time deliveryTime,i.unit,c.category categoryName,p.pd_name pdName,
         i.weight weight,i.type skuType,ad.name_remakes nameRemakes, i.ext_type extType
         from stock_task_pick_item stpi
        left join inventory i on stpi.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        left join category c on p.category_id = c.id
        left join admin ad on ad.admin_id = i.admin_id
        where  stpi.delivery_time =  #{deliveryTime} and stpi.store_no = #{storeNo}
        <if test="type != null">
          and  stpi.`type` = #{type}
        </if>
        <if test="closeOrderTime != null">
            and  stpi.`close_order_time` = #{closeOrderTime}
        </if>
    </select>

</mapper>