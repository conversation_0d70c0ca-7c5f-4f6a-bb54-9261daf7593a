<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ActivitySkuPriceMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.market.ActivitySkuPrice">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="basic_info_id" jdbcType="BIGINT" property="basicInfoId"/>
    <result column="sku_detail_id" jdbcType="BIGINT" property="skuDetailId"/>
    <result column="sku" jdbcType="VARCHAR" property="sku"/>
    <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
    <result column="sale_price" jdbcType="DECIMAL" property="salePrice"/>
    <result column="ladder_price" jdbcType="VARCHAR" property="ladderPrice"/>
    <result column="activity_price" jdbcType="DECIMAL" property="activityPrice"/>
    <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `basic_info_id`, `sku_detail_id`, `sku`, `area_no`, `sale_price`, `ladder_price`, `activity_price`,
    `updater_id`, `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sku_price
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from activity_sku_price
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.market.ActivitySkuPrice">
    insert into activity_sku_price (`id`, `basic_info_id`, `sku_detail_id`, `sku`,
                                    `area_no`, `sale_price`, `ladder_price`,
                                    `activity_price`, `updater_id`, `create_time`,
                                    `update_time`)
    values (#{id,jdbcType=BIGINT}, #{skuDetailId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR},
            #{areaNo,jdbcType=INTEGER}, #{salePrice,jdbcType=DECIMAL},
            #{ladderPrice,jdbcType=VARCHAR},
            #{activityPrice,jdbcType=DECIMAL}, #{updaterId,jdbcType=INTEGER},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.market.ActivitySkuPrice">
    insert into activity_sku_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="basicInfoId != null">
        `basic_info_id`,
      </if>
      <if test="skuDetailId != null">
        `sku_detail_id`,
      </if>
      <if test="sku != null">
        `sku`,
      </if>
      <if test="areaNo != null">
        `area_no`,
      </if>
      <if test="salePrice != null">
        `sale_price`,
      </if>
      <if test="ladderPrice != null">
        `ladder_price`,
      </if>
      <if test="activityPrice != null">
        `activity_price`,
      </if>
      <if test="updaterId != null">
        `updater_id`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="basicInfoId != null">
        #{basicInfoId,jdbcType=BIGINT},
      </if>
      <if test="skuDetailId != null">
        #{skuDetailId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="salePrice != null">
        #{salePrice,jdbcType=DECIMAL},
      </if>
      <if test="ladderPrice != null">
        #{ladderPrice,jdbcType=VARCHAR},
      </if>
      <if test="activityPrice != null">
        #{activityPrice,jdbcType=DECIMAL},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.model.domain.market.ActivitySkuPrice">
    update activity_sku_price
    <set>
      <if test="basicInfoId != null">
        `basic_info_id` = #{basicInfoId,jdbcType=BIGINT},
      </if>
      <if test="skuDetailId != null">
        `sku_detail_id` = #{skuDetailId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        `sku` = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        `area_no` = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="salePrice != null">
        `sale_price` = #{salePrice,jdbcType=DECIMAL},
      </if>
      <if test="ladderPrice != null">
        `ladder_price` = #{ladderPrice,jdbcType=VARCHAR},
      </if>
      <if test="activityPrice != null">
        `activity_price` = #{activityPrice,jdbcType=DECIMAL},
      </if>
      <if test="updaterId != null">
        `updater_id` = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey"
    parameterType="net.summerfarm.model.domain.market.ActivitySkuPrice">
    update activity_sku_price
    set `basic_info_id`  = #{basicInfoId,jdbcType=BIGINT},
        `sku_detail_id`  = #{skuDetailId,jdbcType=BIGINT},
        `sku`            = #{sku,jdbcType=VARCHAR},
        `area_no`        = #{areaNo,jdbcType=INTEGER},
        `sale_price`     = #{salePrice,jdbcType=DECIMAL},
        `ladder_price`   = #{ladderPrice,jdbcType=VARCHAR},
        `activity_price` = #{activityPrice,jdbcType=DECIMAL},
        `updater_id`     = #{updaterId,jdbcType=INTEGER},
        `create_time`    = #{createTime,jdbcType=TIMESTAMP},
        `update_time`    = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <update id="updatePrice" parameterType="net.summerfarm.model.domain.market.ActivitySkuPrice">
    update activity_sku_price
    set sale_price =#{salePrice},
    activity_price=#{activityPrice}
    <where>
      <if test="skuDetailId != null">
        and `sku_detail_id` = #{skuDetailId,jdbcType=BIGINT}
      </if>
      <if test="sku != null">
        and `sku` = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="areaNo != null">
        and `area_no` = #{areaNo,jdbcType=INTEGER}
      </if>
    </where>
  </update>


  <update id="updatePriceSelective" parameterType="net.summerfarm.model.domain.market.ActivitySkuPrice">
    update activity_sku_price
    <set>
    <if test="salePrice != null">
      `sale_price` = #{salePrice,jdbcType=DECIMAL},
    </if>
    <if test="ladderPrice != null">
      `ladder_price` = #{ladderPrice,jdbcType=VARCHAR},
    </if>
    <if test="activityPrice != null">
      `activity_price` = #{activityPrice,jdbcType=DECIMAL},
    </if>
    </set>
    <where>
        `sku_detail_id` = #{skuDetailId,jdbcType=BIGINT}
        and `sku` = #{sku,jdbcType=VARCHAR}
        and `area_no` = #{areaNo,jdbcType=INTEGER}
    </where>
  </update>

  <insert id="insertBatch" parameterType="net.summerfarm.model.domain.market.ActivitySkuPrice"
    useGeneratedKeys="true" keyProperty="id">
    insert into activity_sku_price (`basic_info_id`, `sku_detail_id`, `sku`,
    `area_no`, `sale_price`, `ladder_price`,
    `activity_price`, `updater_id`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.basicInfoId,jdbcType=BIGINT},#{item.skuDetailId,jdbcType=BIGINT},
      #{item.sku,jdbcType=VARCHAR},
      #{item.areaNo,jdbcType=INTEGER}, #{item.salePrice,jdbcType=DECIMAL},
      #{item.ladderPrice,jdbcType=VARCHAR},
      #{item.activityPrice,jdbcType=DECIMAL}, #{item.updaterId,jdbcType=INTEGER})
    </foreach>
  </insert>

  <delete id="deleteByBasicInfoId">
    delete
    from activity_sku_price
    where basic_info_id = #{basicInfoId}
  </delete>

  <delete id="deleteSkuByInfoId">
    delete from activity_sku_price
    where basic_info_id = #{basicInfoId}
    <if test="sku != null">
      and sku = #{sku}
    </if>
    <if test="areaNos != null and areaNos.size > 0">
      and area_no in
      <foreach collection="areaNos" item="areaNo" open="(" separator="," close=")" >
         #{areaNo}
      </foreach>
    </if>
  </delete>

  <delete id="deleteByAreaNo">

  </delete>

  <select id="selectByDetailId" resultMap="BaseResultMap">
    /*FORCE_MASTER*/
    select
    <include refid="Base_Column_List"/>
    from activity_sku_price
    where `sku_detail_id` = #{skuDetailId} and sku =#{sku} and area_no =#{areaNo}
  </select>

  <select id="selectBySkuAndBasicInfoId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_sku_price
    where basic_info_id = #{basicInfoId} and sku =#{sku}
  </select>


  <select id="selectByBasicIds" resultMap="BaseResultMap">
    /*FORCE_MASTER*/
    select
    <include refid="Base_Column_List"/>
    from activity_sku_price
    where basic_info_id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>



  <update id="updateLadderPriceBatch" parameterType="net.summerfarm.model.domain.market.ActivitySkuPrice">
    <foreach collection="list" item="item" index="index" separator=";">
      update activity_sku_price
      <set>
        ladder_price = #{item.ladderPrice,jdbcType=VARCHAR}
      </set>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper>
