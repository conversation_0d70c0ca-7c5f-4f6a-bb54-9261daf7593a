<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.BiPurchasesConfigMapper">

    <delete id="deleteAll">
        DELETE
        FROM bi_purchases_config
    </delete>

    <insert id="insert" parameterType="net.summerfarm.model.domain.BiPurchasesConfig">
        INSERT INTO bi_purchases_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="purchasesConfigId != null">
                purchases_config_id,
            </if>
            <if test="thirtyAvgQuantity != null">
                thirty_avg_quantity,
            </if>
            <if test="sevenQuantity != null">
                seven_quantity,
            </if>
            <if test="sevenAvgQuantity != null">
                seven_avg_quantity,
            </if>
            <if test="lastSevenQuantity != null">
                last_seven_quantity,
            </if>
            <if test="turnOverDay != null">
                turn_over_day,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="purchasesConfigId != null">
                #{purchasesConfigId} ,
            </if>
            <if test="thirtyAvgQuantity != null">
                #{thirtyAvgQuantity},
            </if>
            <if test="sevenQuantity != null">
                #{sevenQuantity} ,
            </if>
            <if test="sevenAvgQuantity != null">
                #{sevenAvgQuantity} ,
            </if>
            <if test="lastSevenQuantity != null">
                #{lastSevenQuantity} ,
            </if>
            <if test="turnOverDay != null">
                #{turnOverDay} ,
            </if>
        </trim>
    </insert>
    <select id="queryQuantity" resultType="net.summerfarm.model.domain.BiPurchasesConfig">
        select ifnull(sum(bpc.thirty_avg_quantity), 0) thirtyAvgQuantity,
           ifnull(sum(bpc.seven_quantity), 0)      sevenQuantity,
           ifnull(sum(bpc.seven_avg_quantity), 0)  sevenAvgQuantity,
           ifnull(sum(bpc.last_seven_quantity), 0) lastSevenQuantity
        from bi_purchases_config bpc left join purchases_config pc on bpc.purchases_config_id = pc.id
        where pc.sku = #{sku} and pc.area_no = #{storeNo}
    </select>
</mapper>