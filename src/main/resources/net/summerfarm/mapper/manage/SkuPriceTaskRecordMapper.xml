<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.SkuPriceTaskRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.SkuPriceTaskRecord">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="sku" jdbcType="VARCHAR" property="sku"/>
    <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
    <result column="old_price" jdbcType="DECIMAL" property="oldPrice"/>
    <result column="new_price" jdbcType="DECIMAL" property="newPrice"/>
    <result column="status" jdbcType="BIT" property="status"/>
    <result column="reason" jdbcType="VARCHAR" property="reason"/>
    <result column="exe_date" jdbcType="DATE" property="exeDate"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `sku`, `area_no`, `old_price`, `new_price`, `status`, `reason`, `exe_date`,
    `update_time`, `create_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from sku_price_task_record
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from sku_price_task_record
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.SkuPriceTaskRecord">
    insert into sku_price_task_record (`id`, `sku`, `area_no`,
                                       `old_price`, `new_price`, `status`,
                                       `reason`, `exe_date`, `update_time`,
                                       `create_time`)
    values (#{id,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{areaNo,jdbcType=INTEGER},
            #{oldPrice,jdbcType=DECIMAL}, #{newPrice,jdbcType=DECIMAL}, #{status,jdbcType=BIT},
            #{reason,jdbcType=VARCHAR}, #{exeDate,jdbcType=DATE}, #{updateTime,jdbcType=TIMESTAMP},
            #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.SkuPriceTaskRecord"
    useGeneratedKeys="true" keyProperty="id">
    insert into sku_price_task_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="sku != null">
        `sku`,
      </if>
      <if test="areaNo != null">
        `area_no`,
      </if>
      <if test="oldPrice != null">
        `old_price`,
      </if>
      <if test="newPrice != null">
        `new_price`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="reason != null">
        `reason`,
      </if>
      <if test="exeDate != null">
        `exe_date`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="oldPrice != null">
        #{oldPrice,jdbcType=DECIMAL},
      </if>
      <if test="newPrice != null">
        #{newPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="exeDate != null">
        #{exeDate,jdbcType=DATE},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.model.domain.SkuPriceTaskRecord">
    update sku_price_task_record
    <set>
      <if test="sku != null">
        `sku` = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        `area_no` = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="oldPrice != null">
        `old_price` = #{oldPrice,jdbcType=DECIMAL},
      </if>
      <if test="newPrice != null">
        `new_price` = #{newPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=BIT},
      </if>
      <if test="reason != null">
        `reason` = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="exeDate != null">
        `exe_date` = #{exeDate,jdbcType=DATE},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.SkuPriceTaskRecord">
    update sku_price_task_record
    set `sku`         = #{sku,jdbcType=VARCHAR},
        `area_no`     = #{areaNo,jdbcType=INTEGER},
        `old_price`   = #{oldPrice,jdbcType=DECIMAL},
        `new_price`   = #{newPrice,jdbcType=DECIMAL},
        `status`      = #{status,jdbcType=BIT},
        `reason`      = #{reason,jdbcType=VARCHAR},
        `exe_date`    = #{exeDate,jdbcType=DATE},
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
        `create_time` = #{createTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="getFromAndToId"
    resultType="net.summerfarm.model.DTO.inventory.PriceTaskRecordIntervalDTO">
    select min(id) as fromId, max(id) as toId
    from sku_price_task_record
    where exe_date = #{exeDate}
  </select>

  <select id="pageById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from sku_price_task_record
    where id &gt;= #{startId} and exe_date = #{exeDate}
    order by id asc
    limit #{pageSize}
  </select>

  <select id="selectBySkuAndAreaNo" resultMap="BaseResultMap">
    /*FORCE_MASTER*/
    select
    <include refid="Base_Column_List"/>
    from sku_price_task_record
    where sku = #{sku} and area_no = #{areaNo} and exe_date = #{exeDate}
  </select>

  <select id="listBySkusAndAreaNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from sku_price_task_record
    where area_no = #{areaNo} and exe_date = #{exeDate}
    and sku in
    <foreach collection="skus" item="sku" index="index" separator="," open="("  close=")">
      #{sku}
    </foreach>
  </select>

  <select id="pageByAreaNo" resultMap="BaseResultMap">
    select
      tr.*
    from sku_price_task_record tr
    inner join(SELECT id FROM sku_price_task_record
    where `area_no` = #{areaNo} and exe_date = #{exeDate} order by id limit #{offset}, #{pageSize}) a
    on tr.`id` = a.id
  </select>
  <insert id="batchInsertOrUpdate" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO sku_price_task_record (sku, area_no, old_price, new_price, status, reason, exe_date)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.sku}, #{item.areaNo}, #{item.oldPrice}, #{item.newPrice}, #{item.status}, #{item.reason}, #{item.exeDate})
    </foreach>
--     ON DUPLICATE KEY UPDATE
--       old_price = VALUES(old_price),
--       new_price = VALUES(new_price),
--       status = VALUES(status),
--       reason = VALUES(reason)
  </insert>
  <update id="batchUpdateStatusById">
    UPDATE sku_price_task_record
    SET status = #{status}
    WHERE id in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item.id}
    </foreach>
  </update>
</mapper>