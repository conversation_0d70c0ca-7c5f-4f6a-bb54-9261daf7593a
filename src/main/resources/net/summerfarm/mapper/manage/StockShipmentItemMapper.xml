<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockShipmentItemMapper">

    <resultMap id="WithDetail" type="net.summerfarm.module.pms.model.vo.AllocationOrderItemEntityVO">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="sku" property="sku" jdbcType="VARCHAR" />
        <result column="quantity" property="outQuantity" jdbcType="INTEGER" />
        <result column="task_no" property="listNo" jdbcType="INTEGER" />
        <collection property="stockAllocationItemDetails" column="id" javaType="ArrayList"
                    select="net.summerfarm.mapper.manage.StockShipmentItemDetailMapper.select"/>
    </resultMap>

    <resultMap id="withDetail" type="net.summerfarm.model.vo.StockTaskProcessVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <collection property="processDetailVOS" column="id" javaType="ArrayList"
                    select="net.summerfarm.mapper.manage.StockShipmentItemDetailMapper.selectProcess"/>
    </resultMap>

    <resultMap id="BaseResultMap" type="net.summerfarm.model.vo.StockShipmentItemVO" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="sku" property="sku" jdbcType="VARCHAR" />
        <result column="stock_task_id" property="stockTaskId" jdbcType="VARCHAR" />
        <result column="quantity" property="quantity" jdbcType="INTEGER" />
        <result column="actual_quantity" property="actualQuantity" jdbcType="VARCHAR" />
        <result column="add_time" property="addTime" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    </resultMap>

    <insert id="insertBatch">
        insert into stock_shipment_item (stock_task_id, sku, quantity, add_time,remark, tenant_id)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{stockTaskId}, #{item.sku}, #{item.outQuantity}, now(),#{item.operateRemark}, #{tenantId})
        </foreach>
    </insert>

    <update id="updateAdd">
    update stock_shipment_item
    <set>
        actual_quantity = actual_quantity + #{actualQuantityAdd}
    </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="select" parameterType="string" resultMap="WithDetail">
    SELECT t.id,t.quantity, t.sku,st.task_no,ar2.status inStatus,ar1.status outStatus,s.in_store inStore,s.out_store outStore,t.remark
    FROM  stock_shipment_item t
    LEFT  JOIN stock_task st on st.id= t.stock_task_id
    LEFT  JOIN stock_allocation_list s on s.list_no=st.task_no
    LEFT  JOIN warehouse_stock_ext ar1 on ar1.sku=t.sku and ar1.warehouse_no = s.out_store
    LEFT  JOIN warehouse_stock_ext ar2 on ar2.sku=t.sku and ar2.warehouse_no = s.in_store
    WHERE st.task_no = #{taskNo} and st.type= 50
  </select>

    <select id="selectByTaskNoList" parameterType="string" resultMap="WithDetail">
    SELECT t.id,t.quantity, t.sku,st.task_no,ar2.status inStatus,ar1.status outStatus,s.in_store inStore,s.out_store outStore,t.remark
    FROM  stock_shipment_item t
    LEFT  JOIN stock_task st on st.id= t.stock_task_id
    LEFT  JOIN stock_allocation_list s on s.list_no=st.task_no
    LEFT  JOIN warehouse_stock_ext ar1 on ar1.sku=t.sku and ar1.warehouse_no = s.out_store
    LEFT  JOIN warehouse_stock_ext ar2 on ar2.sku=t.sku and ar2.warehouse_no = s.in_store
    WHERE st.task_no IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    and st.type= 50
  </select>

    <select id="selectBySku" parameterType="string" resultType="net.summerfarm.model.domain.StockShipmentItem">
    SELECT t.id,t.quantity, t.sku,st.task_no,t.remark,t.actual_quantity actualQuantity
    FROM  stock_shipment_item t
    LEFT  JOIN stock_task st on st.id= t.stock_task_id
    WHERE st.task_no = #{taskNo} and st.type= 50 and t.sku=#{sku}
    </select>

    <select id="selectBySkuDetail" parameterType="net.summerfarm.model.domain.AbnormalRecord" resultType="net.summerfarm.model.domain.StockShipmentItemDetail">
    SELECT td.actual_out_quantity actualOutQuantity,td.gl_no glNo
    FROM  stock_task st
    LEFT  JOIN stock_shipment_item t on st.id= t.stock_task_id
    LEFT  JOIN stock_shipment_item_detail td on td.stock_shipment_item_id= t.id
    WHERE st.task_no = #{taskNo} and st.type= 50 and t.sku=#{sku} and td.quality_date=#{qualityDate} and td.purchase_no =#{batch}
    </select>

    <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    SELECT * FROM  stock_shipment_item WHERE id =  #{id}
  </select>
    <select id="selectByTaskNo" resultType="net.summerfarm.model.domain.StockShipmentItem">
    /*FORCE_MASTER*/
    SELECT t.id, t.sku, t.quantity,st.id  stockTaskId, t.actual_quantity actualQuantity
    FROM  stock_shipment_item t
    LEFT  JOIN stock_task st on st.id= t.stock_task_id
    WHERE st.task_no = #{listNo}
    </select>

    <select id="selectByTaskId" resultMap="withDetail">
    SELECT  st.id stockTaskId,st.type,st.expect_time expectTime,a.realname recorder,st.task_no taskNo,st.area_no areaNo,st.addtime
    FROM  stock_task st
    LEFT JOIN  admin a on st.admin_id =a.admin_id
    where st.id= #{stockTaskId}
    </select>

    <select id="countCapacity" resultType="java.math.BigDecimal">
        SELECT ROUND(sum(capacity)/1000,2) total_capacity from (
        SELECT i.weight_num* sti.quantity capacity FROM `stock_shipment_item` sti LEFT JOIN inventory i on sti.sku=i.sku WHERE stock_task_id in
        <foreach collection="stockTaskIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        ) s
    </select>

</mapper>