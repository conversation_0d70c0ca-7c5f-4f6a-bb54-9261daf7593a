<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CrmRelationRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CrmRelationRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="admin_name" jdbcType="VARCHAR" property="adminName" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="reassign" jdbcType="BOOLEAN" property="reassign" />
    <result column="last_follow_up_time" jdbcType="TIMESTAMP" property="lastFollowUpTime" />
    <result column="reassign_time" jdbcType="TIMESTAMP" property="reassignTime" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="follow_type" jdbcType="INTEGER" property="followType" />
    <result column="danger_day" jdbcType="INTEGER" property="dangerDay" />
    <result column="timing_follow_type" jdbcType="INTEGER" property="timingFollowType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, admin_id, admin_name, add_time, reassign, last_follow_up_time, reassign_time, 
    reason, follow_type, danger_day, timing_follow_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_relation_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crm_relation_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into crm_relation_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="adminName != null">
        admin_name,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="reassign != null">
        reassign,
      </if>
      <if test="lastFollowUpTime != null">
        last_follow_up_time,
      </if>
      <if test="reassignTime != null">
        reassign_time,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="followType != null">
        follow_type,
      </if>
      <if test="dangerDay != null">
        danger_day,
      </if>
      <if test="timingFollowType != null">
        timing_follow_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="adminName != null">
        #{adminName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reassign != null">
        #{reassign,jdbcType=BOOLEAN},
      </if>
      <if test="lastFollowUpTime != null">
        #{lastFollowUpTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reassignTime != null">
        #{reassignTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="followType != null">
        #{followType,jdbcType=INTEGER},
      </if>
      <if test="dangerDay != null">
        #{dangerDay,jdbcType=INTEGER},
      </if>
      <if test="timingFollowType != null">
        #{timingFollowType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <insert id="bulkInsert" parameterType="net.summerfarm.model.domain.FollowUpRelation">
    insert into crm_relation_record
      (m_id, admin_id, admin_name, add_time, reassign, last_follow_up_time, reassign_time, reason, follow_type,
      danger_day, timing_follow_type)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.mId}, #{item.adminId}, #{item.adminName}, #{item.addTime}, #{item.reassign}, #{item.lastFollowUpTime},
      #{item.reassignTime}, #{item.reason}, #{item.followType}, #{item.dangerDay}, #{item.timingFollowType})
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CrmRelationRecord">
    update crm_relation_record
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="adminName != null">
        admin_name = #{adminName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reassign != null">
        reassign = #{reassign,jdbcType=BOOLEAN},
      </if>
      <if test="lastFollowUpTime != null">
        last_follow_up_time = #{lastFollowUpTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reassignTime != null">
        reassign_time = #{reassignTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="followType != null">
        follow_type = #{followType,jdbcType=INTEGER},
      </if>
      <if test="dangerDay != null">
        danger_day = #{dangerDay,jdbcType=INTEGER},
      </if>
      <if test="timingFollowType != null">
        timing_follow_type = #{timingFollowType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByMid" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_relation_record
    WHERE m_id = #{mId,jdbcType=INTEGER}
    ORDER BY id DESC
  </select>
</mapper>