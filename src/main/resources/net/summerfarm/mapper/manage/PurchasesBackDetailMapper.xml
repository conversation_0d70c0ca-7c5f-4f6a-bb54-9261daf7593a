<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.PurchasesBackDetailMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PurchasesBackDetail">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="purchases_back_no" property="purchasesBackNo" jdbcType="VARCHAR"/>
        <result column="batch" property="batch" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="quality_date" property="qualityDate"/>
        <result column="production_date" property="productionDate"/>
        <result column="cost" property="cost" jdbcType="DECIMAL"/>
        <result column="out_quantity" property="outQuantity" jdbcType="INTEGER"/>
        <result column="total_cost" property="totalCost" jdbcType="DECIMAL"/>
        <result column="actual_out_quantity" property="actualOutQuantity" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="VOMap" type="net.summerfarm.model.vo.PurchasesBackDetailVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="purchases_back_no" property="purchasesBackNo" jdbcType="VARCHAR"/>
        <result column="batch" property="batch" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="quality_date" property="qualityDate"/>
        <result column="production_date" property="productionDate"/>
        <result column="cost" property="cost" jdbcType="DECIMAL"/>
        <result column="out_quantity" property="outQuantity" jdbcType="INTEGER"/>
        <result column="total_cost" property="totalCost" jdbcType="DECIMAL"/>
        <result column="actual_out_quantity" property="actualOutQuantity" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="status" property="status" javaType="INTEGER"/>
    </resultMap>

    <sql id="BaseColumn">
        id, purchases_back_no, batch, sku, area_no, quality_date, production_date, cost, out_quantity, total_cost, actual_out_quantity,type
    </sql>

    <insert id="insertBatch" parameterType="net.summerfarm.model.vo.PurchasesBackDetailVO">
        INSERT INTO purchases_back_detail(purchases_back_no, batch, sku, area_no, quality_date, production_date, cost, out_quantity, total_cost, actual_out_quantity,gl_no,type,supplier_id)
        VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.purchasesBackNo}, #{item.batch}, #{item.sku}, #{item.areaNo}, #{item.qualityDate}, #{item.productionDate}, #{item.cost}, #{item.outQuantity}, #{item.totalCost}, #{item.actualOutQuantity},#{item.glNo},#{item.type},#{item.supplierId})
        </foreach>
    </insert>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM purchases_back_detail
        WHERE id = #{id}
    </select>

    <update id="update" parameterType="net.summerfarm.model.domain.PurchasesBackDetail">
        UPDATE purchases_back_detail
        <set>
            <if test="outQuantity != null">
                out_quantity = #{outQuantity} ,
            </if>
            <if test="actualOutQuantity != null">
                actual_out_quantity = #{actualOutQuantity} ,
            </if>
            <if test="totalCost != null">
                total_cost = #{totalCost},
            </if>
        </set>
        WHERE id = #{id}
    </update>


    <delete id="delete" parameterType="java.lang.Integer">
        DELETE
        FROM purchases_back_detail
        WHERE id = #{id}
    </delete>

    <select id="selectOne" parameterType="net.summerfarm.model.domain.PurchasesBackDetail"
            resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM purchases_back_detail
        <where>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
            <if test="batch != null">
                AND batch = #{batch}
            </if>
            <if test="purchasesBackNo != null">
                AND purchases_back_no = #{purchasesBackNo}
            </if>
            <if test="areaNo != null">
                AND area_no = #{areaNo}
            </if>
            <choose>
                <when test="qualityDate != null">
                    AND quality_date = #{qualityDate}
                </when>
                <otherwise>
                    AND quality_date IS NULL
                </otherwise>
            </choose>
        </where>
    </select>

    <insert id="insert" parameterType="net.summerfarm.model.domain.PurchasesBackDetail">
        INSERT INTO purchases_back_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="purchasesBackNo != null">
                purchases_back_no,
            </if>
            <if test="batch != null">
                batch,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="qualityDate != null">
                quality_date,
            </if>
            <if test="productionDate != null">
                production_date,
            </if>
            <if test="cost != null">
                cost,
            </if>
            <if test="outQuantity != null">
                out_quantity,
            </if>
            <if test="totalCost != null">
                total_cost,
            </if>
            <if test="actualOutQuantity != null">
                actual_out_quantity,
            </if>
            <if test="glNo != null">
                gl_no,
            </if>
            <if test="type != null">
                type,
            </if>
        </trim>
        VALUE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="purchasesBackNo != null">
                #{purchasesBackNo} ,
            </if>
            <if test="batch != null">
                #{batch} ,
            </if>
            <if test="sku != null">
                #{sku} ,
            </if>
            <if test="areaNo != null">
                #{areaNo} ,
            </if>
            <if test="qualityDate != null">
                #{qualityDate} ,
            </if>
            <if test="productionDate != null">
                #{productionDate} ,
            </if>
            <if test="cost != null">
                #{cost} ,
            </if>
            <if test="outQuantity != null">
                #{outQuantity} ,
            </if>
            <if test="totalCost != null">
                #{totalCost} ,
            </if>
            <if test="actualOutQuantity != null">
                #{actualOutQuantity} ,
            </if>
            <if test="glNo != null">
                #{glNo},
            </if>
            <if test="type != null">
                #{type},
            </if>
        </trim>
    </insert>

    <select id="selectByNo" parameterType="java.lang.String" resultType="net.summerfarm.model.vo.PurchasesBackDetailVO">
        SELECT pbd.id,
               pbd.purchases_back_no             purchasesBackNo,
               pbd.batch,
               pbd.sku,
               pbd.area_no                       areaNo,
               pbd.quality_date                  qualityDate,
               pbd.production_date               productionDate,
               pbd.cost,
               pbd.total_cost                    totalCost,
               pbd.out_quantity                  outQuantity,
               pbd.total_cost                    totalCost,
               pbd.actual_out_quantity           actualOutQuantity,
               p.pd_name                         pdName,
               i.weight,
               ar.status,
               pbd.gl_no                         glNo,
               i.type                            skuType,
               ad.name_remakes                   nameRemakes,
               i.ext_type                        extType,
               ifnull(i.sku_pic, p.picture_path) pic,
               i.unit                            unit,
               i.volume                          volume,
               i.weight_num                      weightNum

        FROM purchases_back_detail pbd
        LEFT JOIN warehouse_stock_ext ar ON pbd.sku = ar.sku AND pbd.area_no = ar.warehouse_no
        LEFT JOIN inventory i ON pbd.sku = i.sku
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT join admin ad on ad.admin_id = i.admin_id
        WHERE pbd.purchases_back_no = #{purchasesBackNo}
    </select>

    <select id="selectByNoList" parameterType="java.lang.String" resultType="net.summerfarm.model.vo.PurchasesBackDetailVO">
        SELECT pbd.id ,pbd.purchases_back_no purchasesBackNo, pbd.batch, pbd.sku, pbd.area_no areaNo, pbd.quality_date qualityDate, pbd.production_date productionDate, pbd.cost,
         pbd.total_cost totalCost, pbd.out_quantity outQuantity, pbd.total_cost totalCost, pbd.actual_out_quantity actualOutQuantity, p.pd_name pdName, i.weight, ar.status,pbd.gl_no glNo,
         i.type skuType,ad.name_remakes nameRemakes,i.ext_type extType
        FROM purchases_back_detail pbd
        LEFT JOIN warehouse_stock_ext ar ON pbd.sku = ar.sku AND pbd.area_no = ar.warehouse_no
        LEFT JOIN inventory i ON pbd.sku = i.sku
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT join admin ad on ad.admin_id = i.admin_id
        WHERE pbd.purchases_back_no IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectVOs" resultType="net.summerfarm.model.vo.PurchasesBackDetailVO">
        SELECT pbd.id, pbd.purchases_back_no purchasesBackNo, pbd.batch, pbd.sku, pbd.area_no areaNo, pbd.quality_date qualityDate, pbd.production_date productionDate,
          pbd.cost, pbd.out_quantity outQuantity, pbd.total_cost totalCost, p.pd_name pdName, i.weight, i.ext_type extType, t2.store_quantity storeQuantity,s.name supplier,pbd.gl_no glNo,i.unit unit,
          pp.quantity,pp.price,convert(pp.price/pp.quantity,decimal (10,2)) singleCost,s.id supplierId, ifnull(i.sku_pic, p.picture_path) pic
        FROM purchases_back_detail pbd
        LEFT JOIN inventory i ON pbd.sku = i.sku
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT join purchases_plan pp on pp.purchase_no = pbd.batch and pp.sku = pbd.sku and pp.plan_status=1 and pp.origin_id is null
        LEFT join supplier s on s.id = pp.supplier_id
        LEFT JOIN (
          SELECT sr.batch, sr.sku, sr.quality_date, sr.area_no, sr.store_quantity
            FROM store_record sr
            INNER JOIN
            (
            SELECT MAX(id) id
            FROM store_record
            WHERE area_no = #{areaNo} and `batch` = #{purchasesNo}
            GROUP BY batch,sku,quality_date,area_no ) t ON sr.id = t.id
        ) t2 ON pbd.sku = t2.sku AND pbd.batch = t2.batch AND pbd.area_no = t2.area_no
        AND (pbd.quality_date = t2.quality_date OR (pbd.quality_date IS NULL AND t2.quality_date IS NULL ) )
        WHERE pbd.purchases_back_no = #{purchasesBackNo}
    </select>


    <select id="selectByNoAndSku" resultType="net.summerfarm.model.domain.PurchasesBackDetail">
        select ifnull(pbd.total_cost, 0) totalCost,pbd.out_quantity outQuantity,pbd.actual_out_quantity actualOutQuantity,pb.status,pb.type
        from purchases_back pb
        inner join  purchases_back_detail pbd on pbd.purchases_back_no = pb.purchases_back_no and pbd.sku = #{sku} and pbd.batch = #{bath}
        where pb.status != 1
    </select>
    <select id="selectByPurchaseNoAndSku" resultType="net.summerfarm.model.vo.PurchasesBackDetailVO">
        select pbd.purchases_back_no purchasesBackNo, pbd.batch, pbd.sku, ifnull(pbd.total_cost, 0) totalCost,pbd.out_quantity outQuantity,pbd.actual_out_quantity actualOutQuantity, pb.type type from purchases_back pb
        inner join  purchases_back_detail pbd on pbd.purchases_back_no = pb.purchases_back_no and pbd.sku = #{sku} and pbd.batch = #{bath}
        where pb.status = 2
    </select>

    <select id="selectAudite" resultType="java.lang.String">
        select pb.purchases_back_no purchasesBackNo from purchases_back pb
        inner join  purchases_back_detail pbd on pbd.purchases_back_no = pb.purchases_back_no and pbd.sku = #{sku} and pbd.batch = #{bath}
        where pb.status = 0
    </select>

    <select id="selectBackTotalCost" resultType = "java.math.BigDecimal">
        select sum(pbd.total_cost) from purchases p
        inner join purchases_plan pp on pp.purchase_no = p.purchase_no and pp.plan_status=1 and pp.origin_id is null
        inner join supplier s on pp.supplier_id = s.id and settle_form = 1
        inner join purchases_back_detail pbd on pbd.sku = pp.sku and pbd.batch = pp.purchase_no
        inner join purchases_back pb on pb.purchases_back_no = pbd.purchases_back_no and pb.status = 2
    where p.state in (1,2) and p.purchase_time >= #{startDate};
    </select>

    <select id="selectStatusByNoAndSku" resultType="net.summerfarm.model.domain.PurchasesBackDetail">
        select pbd.total_cost totalCost,pbd.out_quantity outQuantity,pbd.actual_out_quantity actualOutQuantity from purchases_back pb
        inner join  purchases_back_detail pbd on pbd.purchases_back_no = pb.purchases_back_no and pbd.sku = #{sku} and pbd.batch = #{bath}
        where pb.status = 0
    </select>

    <select id="queryUnsettlePurchaseBackDetail" resultMap="BaseResultMap">
     SELECT
	t1.purchase_no batch,
	t1.sku,
	t2.out_quantity out_quantity,
	t2.out_cost total_cost
FROM
	(
	SELECT
		pp.purchase_no purchase_no,
		pp.sku sku
	FROM
		purchases_plan pp
		LEFT JOIN purchases p ON pp.purchase_no = p.purchase_no
		LEFT JOIN inventory i ON pp.sku = i.sku
		LEFT JOIN supplier s ON pp.supplier_id = s.id
	WHERE
		s.settle_form != 1
		AND pp.plan_status = 1
		AND p.state != - 1
		AND i.type = 0
		AND pp.quantity != 0
		AND pp.price > 0
		AND pp.settle_flag = 0
	) t1
	INNER JOIN ( SELECT pbd.batch purchase_no, pbd.sku sku, pbd.out_quantity AS out_quantity, pbd.total_cost AS out_cost, pb.add_time FROM purchases_back pb LEFT JOIN purchases_back_detail pbd ON pb.purchases_back_no = pbd.purchases_back_no WHERE pb.STATUS = 2 AND pbd.out_quantity >= 0 AND pbd.total_cost >= 0 ) t2
	ON t1.purchase_no = t2.purchase_no
	AND t1.sku = t2.sku
    </select>

    <select id="selectDetailByNo" parameterType="java.lang.String" resultType="net.summerfarm.model.vo.PurchasesBackDetailVO">
        SELECT pbd.id ,pbd.purchases_back_no purchasesBackNo, pbd.batch, pbd.sku, pbd.area_no areaNo, pbd.quality_date qualityDate, pbd.production_date productionDate, pbd.cost,
         pbd.total_cost totalCost, pbd.out_quantity outQuantity, pbd.actual_out_quantity actualOutQuantity,pp.supplier_id supplierId
        FROM purchases_back_detail pbd
        LEFT join purchases_plan pp on pp.purchase_no = pbd.batch and pp.sku = pbd.sku and pp.plan_status=1 and pp.origin_id is null
        WHERE pbd.purchases_back_no = #{purchasesBackNo}
    </select>

    <select id="selectByPurchasesNoAndSku" resultType="net.summerfarm.model.vo.PurchasesBackDetailVO">
        select pb.type,pb.status,pbd.batch,pbd.out_quantity outQuantity,pbd.actual_out_quantity actualOutQuantity,total_cost totalCost,pbd.quality_date quantityDate
        from purchases_back_detail pbd left join purchases_back pb on pbd.purchases_back_no = pb.purchases_back_no
        where pbd.batch = #{purchasesNo} and pbd.sku=#{sku}
    </select>

    <select id="select" resultType="net.summerfarm.model.vo.PurchasesBackDetailVO">
        select pb.type,pb.status,pbd.batch,pbd.out_quantity outQuantity,pbd.actual_out_quantity actualOutQuantity,pbd.total_cost totalCost,pbd.quality_date qualityDate
        from purchases_back_detail pbd left join purchases_back pb on pbd.purchases_back_no = pb.purchases_back_no
        <where>
            <if test="batch != null">
                pbd.batch = #{batch}
            </if>
            <if test="sku != null">
                and pbd.sku = #{sku}
            </if>
            <if test="qualityDate != null">
                and pbd.quality_date = #{qualityDate}
            </if>
            <if test="type != null">
                and pbd.type = #{type}
            </if>
            <if test="status != null">
                and pb.status = #{status}
            </if>
        </where>
    </select>

    <select id="selectPurchasesBackDetail" resultType="net.summerfarm.model.vo.PurchasesBackDetailVO">
        select pbd.id,pbd.purchases_back_no purchasesBackNo,pbd.batch,pbd.sku,pbd.total_cost totalCost,pp.id purchasePlanId
        from purchases_back_detail pbd
        left join purchases_back pb on pbd.purchases_back_no = pb.purchases_back_no
        left join purchases_plan pp on pbd.batch = pp.purchase_no and pp.quantity != 0 and pp.price > 0
        where pb.status <![CDATA[<>]]> 1 and pbd.total_cost is not null
    </select>

    <select id="selectTotalCost" resultType="decimal" >
        SELECT ifnull(sum(pbd.total_cost),0) refundSettlementAmount
        FROM purchases_back_detail pbd
        LEFT JOIN purchases_back pb on pbd.purchases_back_no = pb.purchases_back_no
        LEFT JOIN purchases_plan pp on pbd.batch = pp.purchase_no and pbd.sku = pp.sku
        where pbd.batch = #{purchaseNo} and pp.supplier_id = #{supplierId} and pb.status <![CDATA[<>]]> 1 and pp.origin_id is null
    </select>

    <select id="addRefundSettlementAmount" resultType="decimal">
        SELECT ifnull(sum(pbd.total_cost),0) refundSettlementAmount
        FROM purchases_back_detail pbd
        LEFT JOIN purchases_back pb on pbd.purchases_back_no = pb.purchases_back_no
        where pbd.batch = #{purchaseNo} and pb.status <![CDATA[<>]]> 1 and pbd.sku = #{sku}
    </select>

    <select id="selectTotalQuantity" resultType="java.lang.Integer">
        select ifnull(sum(pbd.out_quantity),0)
        from purchases_back_detail pbd
        inner join purchases_back pb on pbd.purchases_back_no = pb.purchases_back_no and pb.status = 2
        <where>
             EXISTS ( SELECT 1  FROM `stock_task` WHERE `task_no` =pb.purchases_back_no  and state in (0,1,5) )
            <if test="batch != null">
               and pbd.batch = #{batch}
            </if>
            <if test="sku != null">
                and pbd.sku = #{sku}
            </if>
            <if test="areaNo != null">
                and pbd.area_no = #{areaNo}
            </if>
            <if test="qualityDate != null">
                and pbd.quality_date = #{qualityDate}
            </if>
            <if test="type != null">
                and pbd.type = #{type}
            </if>
            <if test="status != null">
                and pb.status = #{status}
            </if>
            <if test="actualOutQuantity != null">
                and pbd.actual_out_quantity = #{actualOutQuantity}
            </if>
        </where>
    </select>

    <select id="selectByPurchasesNo" resultType="net.summerfarm.model.vo.PurchasesBackDetailVO">
        select
            pb.type,pb.status,pbd.batch,
            pbd.out_quantity outQuantity,
            pbd.actual_out_quantity actualOutQuantity,
            total_cost totalCost,
            pbd.quality_date quantityDate,
            pbd.batch ,pbd.sku
        from purchases_back pb
        left join purchases_back_detail pbd on pbd.purchases_back_no = pb.purchases_back_no
        where pbd.batch = #{purchasesNo} and pb.status = 2
        <if test="sku != null">
          and  pbd.sku = #{sku}
        </if>
    </select>

    <select id="queryBackAmount" resultType="java.math.BigDecimal">
        select sum(round((pp.price / pp.quantity), 2) * pbd.out_quantity)
        from purchases_back_detail pbd
        inner join purchases_back pb on pbd.purchases_back_no = pb.purchases_back_no
        LEFT join purchases_plan pp on pp.purchase_no = pbd.batch and pp.sku = pbd.sku
        and pp.plan_status=1 and pp.origin_id is null
        where
            pb.status = 2 and pp.sku=#{sku}
        <if test="type != null">
            and pb.type = #{type}
        </if>
        and pbd.batch = #{purchaseNo}
    </select>

    <select id="queryBackQuantity" resultType="java.lang.Integer">
        select ifnull(sum(pbd.out_quantity), 0)
        from purchases_back_detail pbd
        inner join purchases_back pb on pbd.purchases_back_no = pb.purchases_back_no
        <where>
            and pb.status = 2
            <if test="type != null">
                and pb.type = #{type}
            </if>
            and pbd.batch = #{purchaseNo}
            and pbd.sku = #{sku}
        </where>
    </select>

    <select id="selectByBatch" resultType="java.lang.Integer">
        select ifnull(sum(pbd.out_quantity),0)
        from purchases_back_detail pbd
        left join purchases_back pb on pbd.purchases_back_no = pb.purchases_back_no
        where pbd.batch = #{purchasesNo} and pb.status = 2
    </select>
    <select id="selectLockBatch" resultType="java.lang.Integer">
        select ifnull(sum(pbd.out_quantity -pbd.actual_out_quantity), 0)
        from purchases_back pb
        left join purchases_back_detail pbd on pbd.purchases_back_no = pb.purchases_back_no
        left join stock_task st on st.task_no=pb.purchases_back_no
        <where>
                st.state in (0,1)
            <if test="batch != null">
                and pbd.batch = #{batch}
            </if>
            <if test="sku != null">
                and pbd.sku = #{sku}
            </if>
            <if test="qualityDate != null">
                and pbd.quality_date = #{qualityDate}
            </if>
            <if test="type != null">
                and pbd.type = #{type}
            </if>
            <if test="status != null">
                and pb.status = #{status}
            </if>
            <if test="areaNo != null">
                and pb.store_no = #{areaNo}
            </if>
        </where>
    </select>

    <select id="queryBackAmountByPurchaseNo" resultType="java.math.BigDecimal">
        select sum(round((pp.price / pp.quantity), 2) * pbd.out_quantity)
        from purchases_back_detail pbd
        inner join purchases_back pb on pbd.purchases_back_no = pb.purchases_back_no
        LEFT join purchases_plan pp on pp.purchase_no = pbd.batch and pp.sku = pbd.sku and pp.plan_status=1 and pp.origin_id is null
        <where>
             pb.status = 2
            <if test="type != null">
                and pb.type = #{type}
            </if>
            and pbd.batch = #{purchaseNo}
            and pp.supplier_id=#{supplierId}
        </where>
    </select>

    <select id="selectBackCountBySkuList" resultType="net.summerfarm.model.vo.pms.PurchaseSkuCountVO">
        SELECT
        SUM(CASE WHEN d.type = 0 THEN d.out_quantity ELSE 0 END) AS notInputBackCount,
        SUM(CASE WHEN d.type = 1 THEN d.out_quantity ELSE 0 END) AS inputBackCount,
        d.batch as purchaseNo,
        d.sku as sku
        FROM purchases_back_detail d
        LEFT JOIN purchases_back b  on b.purchases_back_no=d.purchases_back_no
        WHERE
            (d.batch, d.sku) IN (<foreach collection="skuList" item="item" separator=",">
                    (#{item.purchaseNo}, #{item.sku})
                </foreach>) and b.status= 2
        GROUP BY d.batch, d.sku
    </select>

    <select id="selectNotSupplierIdIds" resultType="java.lang.Long">
        select id
        from purchases_back_detail
        where supplier_id is null
    </select>

    <update id="updateSupplierIdById">
        update purchases_back_detail set supplier_id=#{supplierId} where id =#{id}
    </update>
</mapper>
