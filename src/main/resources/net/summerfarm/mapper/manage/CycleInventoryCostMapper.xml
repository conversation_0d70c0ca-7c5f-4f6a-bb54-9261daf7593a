<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CycleInventoryCostMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CycleInventoryCost">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="first_cycle_cost" jdbcType="DECIMAL" property="firstCycleCost" />
    <result column="first_cycle_cost_time" jdbcType="TIMESTAMP" property="firstCycleCostTime" />
    <result column="end_cycle_cost" jdbcType="DECIMAL" property="endCycleCost" />
    <result column="end_cycle_cost_time" jdbcType="TIMESTAMP" property="endCycleCostTime" />
    <result column="creater" jdbcType="INTEGER" property="creater" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, warehouse_no, sku, first_cycle_cost,first_cycle_cost_time, end_cycle_cost,end_cycle_cost_time, creater, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cycle_inventory_cost
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectBySku" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from cycle_inventory_cost
    <where>
      <if test="warehouseNo!=null">
        and warehouse_no= #{warehouseNo}
      </if>
      <if test="sku!=null">
        and sku = #{sku}
      </if>
    </where>
    limit 1
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from cycle_inventory_cost
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CycleInventoryCost" useGeneratedKeys="true">
    insert into cycle_inventory_cost (warehouse_no, sku, first_cycle_cost, 
      end_cycle_cost, creater, create_time
      )
    values (#{warehouseNo,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{firstCycleCost,jdbcType=DECIMAL}, 
      #{endCycleCost,jdbcType=DECIMAL}, #{creater,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CycleInventoryCost" useGeneratedKeys="true">
    insert into cycle_inventory_cost
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="firstCycleCost != null">
        first_cycle_cost,
      </if>
      <if test="firstCycleCostTime !=null">
        first_cycle_cost_time,
      </if>
      <if test="endCycleCost != null">
        end_cycle_cost,
      </if>
      <if test="creater != null">
        creater,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="firstCycleCost != null">
        #{firstCycleCost,jdbcType=DECIMAL},
      </if>
      <if test="firstCycleCostTime != null">
        #{firstCycleCostTime},
      </if>
      <if test="endCycleCost != null">
        #{endCycleCost,jdbcType=DECIMAL},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="insertInitialization">
    insert into cycle_inventory_cost (warehouse_no,sku,first_cycle_cost,first_cycle_cost_time,end_cycle_cost,end_cycle_cost_time,creater,create_time)
    select area_no,sku,0,now(),0,now(),1,now() from area_store

  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CycleInventoryCost">
    update cycle_inventory_cost
    <set>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="firstCycleCost != null">
        first_cycle_cost = #{firstCycleCost,jdbcType=DECIMAL},
      </if>
      <if test="endCycleCost != null">
        end_cycle_cost = #{endCycleCost,jdbcType=DECIMAL},
      </if>
      <if test="creater != null">
        creater = #{creater,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CycleInventoryCost">
    update cycle_inventory_cost
    set warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      first_cycle_cost = #{firstCycleCost,jdbcType=DECIMAL},
      end_cycle_cost = #{endCycleCost,jdbcType=DECIMAL},
      creater = #{creater,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByStoreRecord">

      update cycle_inventory_cost
    <trim prefix="set" suffixOverrides=",">
      <trim prefix=" first_cycle_cost =case" suffix="end,">
        <foreach collection="storeRecordList" item="i" index="index">
          <if test="i.cost!=null">
            when warehouse_no = #{i.areaNo} and sku = #{i.sku} then #{i.cost}
          </if>
        </foreach>
      </trim>
      <trim prefix=" first_cycle_cost_time =case" suffix="end,">
        <foreach collection="storeRecordList" item="i" index="index">
          <if test="i.cost!=null">
            when warehouse_no = #{i.areaNo} and sku = #{i.sku} then now()
          </if>
        </foreach>
      </trim>
      <trim prefix="end_cycle_cost =case" suffix="end,">
        <foreach collection="storeRecordList" item="i" index="index">
          <if test="i.cost!=null">
            when warehouse_no = #{i.areaNo} and sku = #{i.sku} then #{i.cost}
          </if>
        </foreach>
      </trim>
      <trim prefix=" end_cycle_cost_time =case" suffix="end,">
        <foreach collection="storeRecordList" item="i" index="index">
          <if test="i.cost!=null">
            when warehouse_no = #{i.areaNo} and sku = #{i.sku} then now()
          </if>
        </foreach>
      </trim>
    </trim>
    where
    <foreach collection="storeRecordList" separator="or" item="i" index="index" >
      warehouse_no = #{i.areaNo} and sku = #{i.sku}
    </foreach>


  </update>
  <update id="updateByStoreSku">
    update cycle_inventory_cost
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="end_cycle_cost =case" suffix="end,">
        <foreach collection="storeRecordVOS" item="i" index="index">
          <if test="i.circleCost!=null">
            when first_cycle_cost != 0 then first_cycle_cost
            when first_cycle_cost = 0 then end_cycle_cost
          </if>
        </foreach>
      </trim>
      <trim prefix="end_cycle_cost_time =case" suffix="end,">
        <foreach collection="storeRecordVOS" item="i" index="index">
          <if test="i.circleCost!=null">
            when first_cycle_cost != 0 then first_cycle_cost_time
            when first_cycle_cost = 0 then end_cycle_cost_time
          </if>
        </foreach>
      </trim>
      <trim prefix=" first_cycle_cost =case" suffix="end,">
        <foreach collection="storeRecordVOS" item="i" index="index">
          <if test="i.circleCost!=null">
            when warehouse_no = #{i.areaNo} and sku = #{i.sku} then #{i.circleCost}
          </if>
        </foreach>
      </trim>
      <trim prefix=" first_cycle_cost_time =case" suffix="end,">
        <foreach collection="storeRecordVOS" item="i" index="index">
          <if test="i.circleCost!=null">
            when warehouse_no = #{i.areaNo} and sku = #{i.sku} then now()
          </if>
        </foreach>
      </trim>
    </trim>
    where
    <foreach collection="storeRecordVOS" separator="or" item="i" index="index" >
      warehouse_no = #{i.areaNo} and sku = #{i.sku}
    </foreach>



  </update>
  <update id="update">
        update  cycle_inventory_cost set
          end_cycle_cost = case  when first_cycle_cost != 0 then first_cycle_cost
                                 when first_cycle_cost = 0 then end_cycle_cost end,
          end_cycle_cost_time =case when first_cycle_cost != 0 then first_cycle_cost_time
                                    when first_cycle_cost = 0 then end_cycle_cost_time end,
         first_cycle_cost_time = now(),
         first_cycle_cost = #{circleCost}
        where warehouse_no = #{areaNo} and sku = #{sku}
  </update>
</mapper>