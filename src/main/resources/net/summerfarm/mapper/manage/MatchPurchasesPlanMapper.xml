<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MatchPurchasesPlanMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MatchPurchasesPlan">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchases_plan_id" jdbcType="INTEGER" property="purchasesPlanId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="matching_schedule" jdbcType="VARCHAR" property="matchingSchedule" />
    <result column="wait_match" jdbcType="DECIMAL" property="waitMatch" />
  </resultMap>
  <sql id="Base_Column_List">
    id, purchases_plan_id, create_time, creator, update_time, updater, price, matching_schedule, 
    wait_match
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from match_purchases_plan
    where purchases_plan_id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    update match_purchases_plan
    set status = 1
    where purchases_plan_id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MatchPurchasesPlan" useGeneratedKeys="true">
    insert into match_purchases_plan (purchases_plan_id, create_time, creator, 
      update_time, updater, price, 
      matching_schedule, wait_match)
    values (#{purchasesPlanId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, 
      #{matchingSchedule,jdbcType=VARCHAR}, #{waitMatch,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MatchPurchasesPlan" useGeneratedKeys="true">
    insert into match_purchases_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      status,
      create_time,
      <if test="purchasesPlanId != null">
        purchases_plan_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="matchingSchedule != null">
        matching_schedule,
      </if>
      <if test="waitMatch != null">
        wait_match,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      0,
      now(),
      <if test="purchasesPlanId != null">
        #{purchasesPlanId,jdbcType=INTEGER},
      </if>

      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        now(),
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="matchingSchedule != null">
        '0',
      </if>
      <if test="waitMatch != null">
        #{price},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.MatchPurchasesPlan">
    update match_purchases_plan
    <set>
      <if test="purchasesPlanId != null">
        purchases_plan_id = #{purchasesPlanId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="matchingSchedule != null">
        matching_schedule = #{matchingSchedule,jdbcType=VARCHAR},
      </if>
      <if test="waitMatch != null">
        wait_match = #{waitMatch,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.MatchPurchasesPlan">
    update match_purchases_plan
    set update_time = now(),
      updater = #{updater,jdbcType=VARCHAR},
      matching_schedule = #{matchingSchedule,jdbcType=VARCHAR},
      wait_match = #{waitMatch,jdbcType=DECIMAL}
    where purchases_plan_id = #{purchasesPlanId,jdbcType=INTEGER}
  </update>

  <update id="update" parameterType="net.summerfarm.model.domain.PurchasesPlan">
    update match_purchases_plan
    set update_time = now(),
      updater = null,
      matching_schedule = '0',
      price = #{price},
      wait_match = #{price,jdbcType=DECIMAL}
    where purchases_plan_id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectSku" resultType="net.summerfarm.model.vo.MatchPurchaseOrderVO">
    select  mpo.purchase_time purchaseTime,mpo.purchase_no purchaseNo, mpo.sku sku,p.pd_name pdName, i.weight weight ,mpp.price price ,mpo.match_amount matchAmount,pi.invoice_code invoiceCode
           ,pi.invoice_number invoiceNumber,mpo.updater updater,mpo.update_time updateTime,mpo.id id
    from match_purchases_plan mpp
    left join purchases_plan pp on mpp.purchases_plan_id = pp.id and pp.plan_status = 1
    left join match_purchase_order mpo on pp.purchase_no = mpo.purchase_no and pp.sku = mpo.sku and mpo.status = 0
    left join purchase_invoice pi on mpo.purchase_invoice_id = pi.id and pi.delete_status = 0
    left join inventory i on mpo.sku  = i.sku
    left join products p on i.pd_id = p.pd_id
    where mpp.purchases_plan_id = #{id}
  </select>

</mapper>