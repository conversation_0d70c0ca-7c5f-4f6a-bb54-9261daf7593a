<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.MerchantLifecycleMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantLifecycle">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="m_id" property="mId" jdbcType="INTEGER"/>
        <result column="lifecycle" property="lifecycle" jdbcType="TINYINT"/>
        <result column="tag" property="tag" jdbcType="VARCHAR"/>
        <result column="purchasing_cycle_left" property="purchasingCycleLeft" jdbcType="INTEGER"/>
        <result column="coupon_expire_date" property="couponExpireDate" jdbcType="INTEGER"/>
        <result column="last_order_time" property="lastOrderTime" jdbcType="TIMESTAMP"/>
        <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, m_id, lifecycle, tag, purchasing_cycle_left, coupon_expire_date, last_order_time,add_time
  </sql>



    <insert id="insertLifecycle">
    INSERT INTO merchant_lifecycle (m_id, lifecycle, tag, coupon_expire_date, purchasing_cycle_left, add_time, last_order_time)

    SELECT  t.m_id,2 as '生命周期',
    IF(t.orderNum=2,CONCAT('上升',t.`上次采购间隔`),CONCAT('下降',t.`上次采购间隔`-7)) '标签1',
    TIMESTAMPDIFF(DAY,CURDATE()+1,DATE(t2.`优惠券到期时间`)) '优惠券到期时间',
    FORMAT(TIMESTAMPDIFF(DAY,DATE(t.`首次采购时间`),DATE(t.`上次采购时间`))/(t.orderNum-1),0)- TIMESTAMPDIFF(DAY,DATE(t.`上次采购时间`),CURDATE())  '预估采购周期剩余(单位:天)',NOW(), t.`上次采购时间`
    from (
    select m.m_id, m.register_time '注册时间',o.order_no ,
    MAX(o.order_time) '上次采购时间',
    MIN(o.order_time) '首次采购时间',
    TIMESTAMPDIFF(DAY,DATE(MAX(o.order_time)),CURDATE()) '上次采购间隔',
    COUNT(1) orderNum,
    SUM(o.total_price) '总支付金额',
    IF(COUNT(1)=2,TIMESTAMPDIFF(DAY,DATE(MIN(o.order_time)),DATE(MAX(o.order_time))),0) '初次复购时间间隔'
    FROM merchant m
    INNER JOIN orders o ON m.m_id = o.m_id AND o.`status` in (3,6)
    GROUP BY m.m_id
    ) t
    LEFT JOIN (SELECT mc.id, mc.m_id, MIN(mc.vaild_date) '优惠券到期时间'  FROM merchant_coupon mc WHERE mc.used = 0 AND mc.vaild_date > NOW() GROUP BY mc.m_id) t2 on t2.m_id =t.m_id
    WHERE t.orderNum =2
    OR (t.orderNum >2 AND t.`上次采购间隔` >=7)
    GROUP BY t.m_id
    UNION ALL

    SELECT t.m_id,1 as '生命周期',
    NULL as '标签1',
    TIMESTAMPDIFF(DAY,CURDATE()+1,DATE(t2.`优惠券到期时间`)) '优惠券到期时间',
    NULL as '预估采购周期剩余(单位:天)',NOW(), MAX(t.order_time)'上次采购时间'
    from
    (
    select m.m_id,m.mname '商户名称', m.register_time '注册时间', m.mcontact '联系人', m.phone '联系电话',CONCAT(m.city,m.area,m.address) '地址' ,o.order_no,
    COUNT(1) orderNum, o.order_time
    FROM merchant m
    INNER JOIN orders o ON m.m_id = o.m_id AND o.status in (3,6)
    GROUP BY m.m_id
    HAVING orderNum =1
    ) t
    LEFT JOIN (SELECT mc.id, mc.m_id, MIN(mc.vaild_date) '优惠券到期时间'  FROM merchant_coupon mc WHERE mc.used = 0 AND mc.vaild_date > NOW() GROUP BY mc.m_id) t2 on t2.m_id =t.m_id
    GROUP BY t.m_id

    UNION ALL

    SELECT t.m_id,3 as '生命周期',
    NULL as '标签1',
    TIMESTAMPDIFF(DAY,CURDATE()+1,DATE(t2.`优惠券到期时间`)) '优惠券到期时间',
    FORMAT(TIMESTAMPDIFF(DAY,DATE(t.`首次采购时间`),DATE(t.`上次采购时间`))/(t.orderNum-1),0)- TIMESTAMPDIFF(DAY,DATE(t.`上次采购时间`),CURDATE())  '预估采购周期剩余(单位:天)',
    NOW(), t.`上次采购时间`
    from (
    select m.m_id,m.mname '商户名称', m.register_time '注册时间', m.mcontact '联系人', m.phone '联系电话',CONCAT(m.city,m.area,m.address) '地址' ,o.order_no,
    MAX(o.order_time) '上次采购时间',
    MIN(o.order_time) '首次采购时间',
    COUNT(1) orderNum,
    SUM(o.total_price) '总支付金额'
    FROM merchant m
    INNER JOIN orders o ON m.m_id = o.m_id AND o.status in (3,6)
    GROUP BY m.m_id
    ) t
    LEFT JOIN (SELECT mc.id, mc.m_id, MIN(mc.vaild_date) '优惠券到期时间'  FROM merchant_coupon mc WHERE mc.used = 0 AND mc.vaild_date > NOW() GROUP BY mc.m_id) t2 on t2.m_id =t.m_id
    WHERE t.orderNum >2 AND TIMESTAMPDIFF(DAY,DATE(t.`上次采购时间`),CURDATE()) <![CDATA[<]]> 7
    GROUP BY t.m_id

    UNION ALL

    SELECT t.m_id,0 as '生命周期',
    NULL as '标签1',
    TIMESTAMPDIFF(DAY,CURDATE()+1,DATE(t2.`优惠券到期时间`)) '优惠券到期时间',
    NULL as '预估采购周期剩余(单位:天)', NOW(), NULL as '上次采购时间'
    from (
    select m.m_id,m.mname '商户名称', m.register_time '注册时间', m.mcontact '联系人', m.phone '联系电话',CONCAT(m.city,m.area,m.address) '地址'
    FROM merchant m
    WHERE m.m_id NOT in (SELECT o.m_id from orders o where o.status in (3,6))
    AND m.islock = 0
    ) t
    LEFT JOIN (SELECT mc.id, mc.m_id, MIN(mc.vaild_date) '优惠券到期时间'  FROM merchant_coupon mc WHERE mc.used = 0 AND mc.vaild_date > NOW() GROUP BY mc.m_id) t2 on t2.m_id =t.m_id ;
  </insert>
    <delete id="deleteLifecycleByTime">
        delete from merchant_lifecycle where add_time &lt; #{lastNow}
    </delete>

    <select id="selectLast" resultType="integer">
        select lifecycle FROM merchant_lifecycle
        where m_id= #{mId}
        order by id desc
        limit 1
    </select>


    <select id="lifecycleData" resultType="net.summerfarm.model.vo.DataVO">
        select (case
        WHEN ml.lifecycle='0' then '新注册'
        WHEN ml.lifecycle='1' then '首单'
        WHEN ml.lifecycle='2' then '非稳'
        else '稳定' END ) as name ,count(*) as number
        from merchant_lifecycle ml

        where ml.add_time <![CDATA[<]]> #{end}
        <if test="start !=null">
            AND ml.add_time <![CDATA[>=]]> #{start}
        </if>

        <if test="areaNo !=null and adminId !=null" >
            AND ml.m_id in (select m.m_id from  merchant m
            left join follow_up_relation f on m.m_id = f.m_id
            where  m.area_no=#{areaNo} and f.admin_id = #{adminId} and f.reassign = 0)
        </if>

        group by ml.lifecycle
    </select>


</mapper>