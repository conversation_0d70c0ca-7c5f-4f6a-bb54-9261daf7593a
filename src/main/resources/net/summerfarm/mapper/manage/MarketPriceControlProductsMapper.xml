<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MarketPriceControlProductsMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MarketPriceControlProducts">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="pd_id" jdbcType="BIGINT" property="pdId" />
    <result column="pd_no" jdbcType="VARCHAR" property="pdNo" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="price_hide" jdbcType="INTEGER" property="priceHide" />
    <result column="face_price_hide" jdbcType="INTEGER" property="facePriceHide" />
    <result column="price_control_line" jdbcType="DECIMAL" property="priceControlLine" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, creator, updater, pd_id, pd_no, pd_name, price_hide, face_price_hide,
    price_control_line, sku
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from market_price_control_products
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByPdId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from market_price_control_products
    where pd_id = #{pdId}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from market_price_control_products
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MarketPriceControlProducts" useGeneratedKeys="true">
    insert into market_price_control_products (create_time, update_time, creator, 
      updater, pd_id, pd_no, 
      pd_name)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{updater,jdbcType=VARCHAR}, #{pdId,jdbcType=BIGINT}, #{pdNo,jdbcType=VARCHAR}, 
      #{pdName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MarketPriceControlProducts" useGeneratedKeys="true">
    insert into market_price_control_products
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="pdNo != null">
        pd_no,
      </if>
      <if test="pdName != null">
        pd_name,
      </if>
      <if test="priceHide != null">
        price_hide,
      </if>
      <if test="facePriceHide != null">
        face_price_hide,
      </if>
      <if test="priceControlLine != null">
        price_control_line,
      </if>
      <if test="sku != null">
        sku,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=BIGINT},
      </if>
      <if test="pdNo != null">
        #{pdNo,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="priceHide != null">
         #{priceHide,jdbcType=INTEGER},
      </if>
      <if test="facePriceHide != null">
        #{facePriceHide,jdbcType=INTEGER},
      </if>
      <if test="priceControlLine != null">
        #{priceControlLine,jdbcType=DECIMAL},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.MarketPriceControlProducts">
    update market_price_control_products
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=BIGINT},
      </if>
      <if test="pdNo != null">
        pd_no = #{pdNo,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        pd_name = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="priceHide != null">
        price_hide = #{priceHide,jdbcType=INTEGER},
      </if>
      <if test="facePriceHide != null">
        face_price_hide = #{facePriceHide,jdbcType=INTEGER},
      </if>
      <if test="priceControlLine != null">
        price_control_line = #{priceControlLine,jdbcType=DECIMAL},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByAll" parameterType="net.summerfarm.model.input.MarketPriceControlProductsInput" resultType="net.summerfarm.model.vo.MarketPriceControlProductsVO">
    select  m.id, m.create_time createTime, m.update_time updateTime, m.creator, m.updater, p.pd_id pdId, p.pd_no pdNo,
           p.pd_name pdName,p.detail_picture picturePath, i.weight, m.sku, m.price_hide priceHide,
           m.face_price_hide facePriceHide, m.price_control_line priceControlLine
    from market_price_control_products m
    left join products p on m.pd_id = p.pd_id
    left join inventory i on m.sku = i.sku
    <where>
      <if test="id != null">
        and m.id = #{id}
      </if>
      <if test="createTime != null">
        and m.create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        and m.update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creator != null">
        and m.creator = #{creator,jdbcType=VARCHAR}
      </if>
      <if test="updater != null">
        and m.updater = #{updater,jdbcType=VARCHAR}
      </if>
      <if test="pdId != null">
        and m.pd_id = #{pdId,jdbcType=BIGINT}
      </if>
      <if test="pdNo != null">
        and p.pd_no = #{pdNo,jdbcType=VARCHAR}
      </if>
      <if test="pdName != null">
        and p.pd_name like CONCAT (#{pdName},'%')
      </if>
      <if test="sku != null">
        and m.sku = #{sku,jdbcType=VARCHAR}
      </if>
    </where>
    order by m.id desc
  </select>
  <select id="selectBySku" resultType="net.summerfarm.model.domain.MarketPriceControlProducts">
    select m.id, m.pd_id pdId, m.pd_no pdNo, m.pd_name pdName, m.sku
    from market_price_control_products m
    left join inventory i ON m.pd_id = i.pd_id
    WHERE i.sku = #{sku}
  </select>

  <select id="selectBySkus" resultType="net.summerfarm.model.domain.MarketPriceControlProducts">
    select m.id, m.pd_id pdId, m.pd_no pdNo, m.pd_name pdName, m.sku
    from market_price_control_products m
    WHERE m.sku in
    <foreach collection="skus" item="sku" open="(" separator="," close=")">
      #{sku}
    </foreach>
  </select>
  <select id="batchInset">
    insert into market_price_control_products (creator, updater, pd_id, pd_no, pd_name, sku, price_hide, face_price_hide)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.creator,jdbcType=VARCHAR}, #{item.updater,jdbcType=VARCHAR}, #{item.pdId,jdbcType=BIGINT}, #{item.pdNo,jdbcType=VARCHAR},
      #{item.pdName,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR}, #{item.priceHide,jdbcType=INTEGER}, #{item.facePriceHide,jdbcType=INTEGER})
    </foreach>
  </select>

  <select id="selectByPdIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from market_price_control_products
    WHERE sku is null and pd_id in
    <foreach collection="pdIds" item="pdId" open="(" separator="," close=")">
      #{pdId}
    </foreach>
  </select>

  <select id="getAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from market_price_control_products
    WHERE sku is null
  </select>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.MarketPriceControlProducts">
    update market_price_control_products
    set updater = #{updater,jdbcType=VARCHAR},
      price_control_line = #{priceControlLine,jdbcType=DECIMAL},
      price_hide = #{priceHide,jdbcType=INTEGER},
      face_price_hide = #{facePriceHide,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>