<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceAccountingStoreMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceAccountingStore">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="finance_order_id" jdbcType="BIGINT" property="financeOrderId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="saler_name" jdbcType="VARCHAR" property="salerName" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="mname" jdbcType="VARCHAR" property="mname" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="after_sale_money" jdbcType="DECIMAL" property="afterSaleMoney" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="out_times_fee" jdbcType="DECIMAL" property="outTimesFee" />
  </resultMap>
  <sql id="Base_Column_List">
    id, finance_order_id, m_id, saler_name, area_no, mname, phone, after_sale_money, 
    delivery_fee, total_price, create_time, creator
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="net.summerfarm.model.domain.FinanceAccountingStore">
    select IFNULL(sum(fd.after_sale_amount),0) afterSaleMoney,IFNULL(sum(fd.delivery_fee),0) deliveryFee,IFNULL(sum(fd.total_price),0) totalPrice,IFNULL(sum(fd.out_times_fee),0) outTimesFee,fd.finance_accounting_store_id id
    from finance_accounting_store_detail fd
    where fd.finance_accounting_store_id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectAreaNo" parameterType="java.lang.Long" resultType="net.summerfarm.model.vo.FinanceAccountingStoreVO">
    select distinct f.area_no areaNo,a.area_name areaName
    from finance_accounting_store f
    left join area a on f.area_no = a.area_no
    where f.m_id = #{mId}
  </select>

  <select id="selectAll" parameterType="long" resultType="net.summerfarm.model.vo.FinanceAccountingStoreVO">
    select f.id id,f.area_no areaNo,a.area_name areaName,f.m_id mId,f.mname mname,f.saler_name salerName,f.saler_id salerId,f.phone phone,f.total_price totalPrice,
           f.delivery_fee deliveryFee,f.after_sale_money afterSaleMoney,fa.bill_number billNumber,f.out_times_fee outTimesFee,fa.type,f.bill_start_time billStartTime,bill_generation_time billGenerationTime
    from finance_accounting_store f
    left join area a on f.area_no = a.area_no
    left join finance_accounting_period_order fa on f.finance_order_id = fa.id
    where f.finance_order_id = #{financeOrderId}
  </select>

  <select id="selectByAll" resultType="net.summerfarm.model.vo.FinanceAccountingStoreVO">
    select f.id id,f.area_no areaNo,a.area_name areaName,f.m_id mId,f.mname mname,f.saler_name salerName,f.phone phone,f.total_price totalPrice,
           f.delivery_fee deliveryFee,f.after_sale_money afterSaleMoney,fa.bill_number billNumber,f.out_times_fee outTimesFee,fa.type
    from finance_accounting_store f
    left join area a on f.area_no = a.area_no
    left join finance_accounting_period_order fa on f.finance_order_id = fa.id
    where f.finance_order_id = #{financeOrderId} and f.id = #{id}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_accounting_store
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAccountingStore" useGeneratedKeys="true">
    insert into finance_accounting_store (finance_order_id, m_id, saler_name, 
      area_no, mname, phone, 
      after_sale_money, delivery_fee, total_price, 
      create_time, creator,bill_start_time)
    values (#{financeOrderId,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, #{salerName,jdbcType=VARCHAR}, 
      #{areaNo,jdbcType=INTEGER}, #{mname,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{afterSaleMoney,jdbcType=DECIMAL}, #{deliveryFee,jdbcType=DECIMAL}, #{totalPrice,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR},#{billStartTime})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAccountingStore" useGeneratedKeys="true">
    insert into finance_accounting_store
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="financeOrderId != null">
        finance_order_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="salerName != null">
        saler_name,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="mname != null">
        mname,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="afterSaleMoney != null">
        after_sale_money,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="salerId != null">
        saler_id,
      </if>
      <if test="billStartTime != null">
        bill_start_time,
      </if>
      <if test="outTimesFee != null">
        out_times_fee,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="financeOrderId != null">
        #{financeOrderId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="salerName != null">
        #{salerName,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="mname != null">
        #{mname,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleMoney != null">
        #{afterSaleMoney,jdbcType=DECIMAL},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="salerId != null">
        #{salerId},
      </if>
      <if test="billStartTime != null">
        #{billStartTime},
      </if>
      <if test="outTimesFee != null">
        #{outTimesFee},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceAccountingStore">
    update finance_accounting_store
    <set>
      <if test="financeOrderId != null">
        finance_order_id = #{financeOrderId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="salerName != null">
        saler_name = #{salerName,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="mname != null">
        mname = #{mname,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleMoney != null">
        after_sale_money = #{afterSaleMoney,jdbcType=DECIMAL},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateBySelective">
    update finance_accounting_store
    set
        finance_order_id = #{financeOrderId}
    where finance_order_id = #{id}
  </update>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceAccountingStore">
    update finance_accounting_store
    set
      after_sale_money = #{afterSaleMoney,jdbcType=DECIMAL},
      delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      out_times_fee = #{outTimesFee}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByStore" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from finance_accounting_store
    where id = #{id}
  </select>

  <select id="selectOne" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from finance_accounting_store
    where id = #{id}
  </select>
  <select id="selectByOrderNo" resultType="net.summerfarm.model.domain.FinanceAccountingStore">
    SELECT <include refid="Base_Column_List"/>
    FROM finance_accounting_store fas
           LEFT JOIN finance_accounting_store_detail fasd ON fas.id = fasd.finance_accounting_store_id
    where fasd.order_no = #{orderNo}
    GROUP BY fas.id
  </select>

</mapper>