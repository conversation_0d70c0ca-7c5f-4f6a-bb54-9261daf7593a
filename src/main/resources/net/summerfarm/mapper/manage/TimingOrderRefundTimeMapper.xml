<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.TimingOrderRefundTimeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.TimingOrderRefundTime">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="refund_time" jdbcType="DATE" property="refundTime" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, refund_time, m_id, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from timing_order_refund_time
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByOrderNo" resultType="net.summerfarm.model.domain.TimingOrderRefundTime">
    select
    id, order_no orderNo, refund_time refundTime, m_id mId, create_time createTime, update_time updateTime
    from timing_order_refund_time
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </select>
  <select id="selectByOrderNos" resultType="net.summerfarm.model.domain.TimingOrderRefundTime">
    select
    id, order_no orderNo, refund_time refundTime, m_id mId, create_time createTime, update_time updateTime
    from timing_order_refund_time
    where order_no in
    <foreach collection="orderNos" close=")" open="(" separator="," item="orderNo">
      #{orderNo}
    </foreach>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from timing_order_refund_time
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.TimingOrderRefundTime" useGeneratedKeys="true">
    insert into timing_order_refund_time (order_no, refund_time, m_id, 
      create_time, update_time)
    values (#{orderNo,jdbcType=VARCHAR}, #{refundTime,jdbcType=DATE}, #{mId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.TimingOrderRefundTime" useGeneratedKeys="true">
    insert into timing_order_refund_time
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=DATE},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.TimingOrderRefundTime">
    update timing_order_refund_time
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=DATE},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.TimingOrderRefundTime">
    update timing_order_refund_time
    set order_no = #{orderNo,jdbcType=VARCHAR},
      refund_time = #{refundTime,jdbcType=DATE},
      m_id = #{mId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="batchUpdateByPrimaryKey">
    update timing_order_refund_time
    <trim prefix="set" suffixOverrides=",">

      <trim prefix="refund_time = case " suffix=" end, ">
        <foreach collection="list" item="item">
          <if test="item.refundTime != null">
            when `id` = #{item.id} then #{item.refundTime}
          </if>
        </foreach>
      </trim>

    </trim>
    where
    `id` in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item.id}
    </foreach>
  </update>
</mapper>