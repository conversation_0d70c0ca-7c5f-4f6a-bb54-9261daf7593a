<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PaymentMapper">
    <select id="selectPayAmount" resultType="java.math.BigDecimal">
        select sum(money) from payment where
        status = 1
        <if test="accountId != null">
            and company_account_id=#{accountId}
        </if>
        <if test="payTypes !=null and !payTypes.isEmpty()">
            and pay_type in
            <foreach collection="payTypes" item="payType" open="(" close=")" separator=",">
                #{payType}
            </foreach>
        </if>
        and end_time >= #{startTime} and end_time <![CDATA[<=]]> #{endTime};
    </select>
    <select id="selectPayTypeByOrderNo" resultType="java.lang.String">
        select pay_type from payment where order_no like concat(#{orderNo},'%') and status = 1 and order_no != concat(#{orderNo},'_A')
        limit 1
    </select>
    <select id="selectPayTimeByOrderNo" resultType="java.time.LocalDateTime">
        select
        end_time
        from payment where
        order_no = #{orderNo} and status = 1 limit 1
    </select>
</mapper>
