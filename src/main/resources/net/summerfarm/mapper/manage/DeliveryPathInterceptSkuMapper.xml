<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DeliveryPathInterceptSkuMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DeliveryPathInterceptSku">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="delivery_time" jdbcType="DATE" property="deliveryTime" />
    <result column="contact_id" jdbcType="INTEGER" property="contactId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, delivery_time, contact_id, sku, quantity, remark, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from delivery_path_intercept_sku
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from delivery_path_intercept_sku
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.DeliveryPathInterceptSku">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into delivery_path_intercept_sku (id, delivery_time, contact_id, 
      sku, quantity, remark, 
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{deliveryTime,jdbcType=DATE}, #{contactId,jdbcType=INTEGER}, 
      #{sku,jdbcType=VARCHAR}, #{quantity,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.DeliveryPathInterceptSku">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into delivery_path_intercept_sku
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="contactId != null">
        contact_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=DATE},
      </if>
      <if test="contactId != null">
        #{contactId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.DeliveryPathInterceptSku">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update delivery_path_intercept_sku
    <set>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=DATE},
      </if>
      <if test="contactId != null">
        contact_id = #{contactId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DeliveryPathInterceptSku">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update delivery_path_intercept_sku
    set delivery_time = #{deliveryTime,jdbcType=DATE},
      contact_id = #{contactId,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByDTAndCIdAndSku" resultMap="BaseResultMap">
    SELECT
      *
    FROM
      delivery_path_intercept_sku t
    WHERE
      t.delivery_time = #{deliveryTime}
      AND t.contact_id = #{contactId}
      AND t.sku = #{sku}
  </select>

  <select id="selectByDeliveryPathIdAndSku" resultMap="BaseResultMap">
    SELECT
      sum(dpis.quantity) as quantity,
      dpis.sku
    FROM
      delivery_path_intercept_sku dpis
        LEFT JOIN delivery_path dp ON dp.delivery_time = dpis.delivery_time
        AND dp.contact_id = dpis.contact_id
    WHERE
      dp.id = #{deliveryPathId}
      AND dpis.sku = #{sku}
    group by dpis.sku
  </select>
    <select id="selectByCIdDTITSku" resultType="net.summerfarm.model.domain.DeliveryPathInterceptSku">
      SELECT
      t.sku,
      sum(t.quantity) quantity
      FROM
      delivery_path_intercept_sku t
      WHERE
      1=1
      <if test="deliveryTime != null">
        AND t.delivery_time = #{deliveryTime}
      </if>
      <if test="contactId != null">
        AND t.contact_id = #{contactId}
      </if>
      <if test="sku != null">
        AND t.sku = #{sku}
      </if>
      <if test="interceptTime != null">
        AND t.create_time = #{interceptTime}
      </if>
      group by t.sku
    </select>
</mapper>