<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CouponSenderRuleMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CouponSenderRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="coupon_sender_id" jdbcType="INTEGER" property="couponSenderId" />
    <result column="scope_id" jdbcType="BIGINT" property="scopeId" />
    <result column="scope_type" jdbcType="TINYINT" property="scopeType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, coupon_sender_id, scope_id, scope_type, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from coupon_sender_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from coupon_sender_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.CouponSenderRule">
    insert into coupon_sender_rule (id, coupon_sender_id, scope_id, 
      scope_type, create_time,
      updater_id, update_time)
    values (#{id,jdbcType=BIGINT}, #{couponSenderId,jdbcType=INTEGER}, #{scopeId,jdbcType=BIGINT}, 
      #{scopeType,jdbcType=TINYINT},  #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.CouponSenderRule">
    insert into coupon_sender_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="couponSenderId != null">
        coupon_sender_id,
      </if>
      <if test="scopeId != null">
        scope_id,
      </if>
      <if test="scopeType != null">
        scope_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="couponSenderId != null">
        #{couponSenderId,jdbcType=INTEGER},
      </if>
      <if test="scopeId != null">
        #{scopeId,jdbcType=BIGINT},
      </if>
      <if test="scopeType != null">
        #{scopeType,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CouponSenderRule">
    update coupon_sender_rule
    <set>
      <if test="couponSenderId != null">
        coupon_sender_id = #{couponSenderId,jdbcType=INTEGER},
      </if>
      <if test="scopeId != null">
        scope_id = #{scopeId,jdbcType=BIGINT},
      </if>
      <if test="scopeType != null">
        scope_type = #{scopeType,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CouponSenderRule">
    update coupon_sender_rule
    set coupon_sender_id = #{couponSenderId,jdbcType=INTEGER},
      scope_id = #{scopeId,jdbcType=BIGINT},
      scope_type = #{scopeType,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByCouponSenderId">
    delete from coupon_sender_rule
    where coupon_sender_id = #{senderSetupId,jdbcType=INTEGER}
  </delete>

  <insert id="insertBatch">
    insert into coupon_sender_rule (id, coupon_sender_id, scope_id,
                                    scope_type)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.couponSenderId,jdbcType=INTEGER}, #{item.scopeId,jdbcType=BIGINT},
      #{item.scopeType,jdbcType=TINYINT})
    </foreach>
  </insert>

  <select id="selectRepeatInfo" resultType="net.summerfarm.model.DTO.coupon.CouponSenderRuleDTO">
    select css.id couponSenderId, css.`name` couponSenderName, css.status status from coupon_sender_rule csr left join coupon_sender_setup css on csr.coupon_sender_id = css.id
    where css.`status` in (0, 1)
      AND csr.scope_type = #{scopeType,jdbcType=INTEGER}
      AND css.sender_type = #{senderType,jdbcType=INTEGER}
      AND csr.scope_id in
    <foreach item="scopeId" index="index" collection="scopeIds" open="(" separator="," close=")">
      #{scopeId,jdbcType=INTEGER}
    </foreach>
  </select>

  <select id="selectByCouponSenderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from coupon_sender_rule
    where coupon_sender_id = #{senderSetupId,jdbcType=INTEGER}
  </select>
</mapper>