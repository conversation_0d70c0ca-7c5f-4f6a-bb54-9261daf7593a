<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.SeriesOfAreaMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.SeriesOfArea">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="series_type" jdbcType="INTEGER" property="seriesType" />
    <result column="series_id" jdbcType="INTEGER" property="seriesId" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <resultMap id="voMap" type="net.summerfarm.model.vo.SeriesOfAreaVO" extends="BaseResultMap">
    <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
    <result column="series_id" jdbcType="INTEGER" property="id"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, series_type, series_id, area_no, creator, create_time
  </sql>
  <sql id="Base_Column_List_With_Alias">
    soa.id,soa.series_type,soa.series_id,soa.area_no,soa.creator,soa.create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from series_of_area
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from series_of_area
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.SeriesOfArea" useGeneratedKeys="true">
    insert into series_of_area (series_type, series_id, area_no, 
      creator, create_time)
    values (#{seriesType,jdbcType=INTEGER}, #{seriesId,jdbcType=INTEGER}, #{areaNo,jdbcType=INTEGER}, 
      #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.SeriesOfArea" useGeneratedKeys="true">
    insert into series_of_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seriesType != null">
        series_type,
      </if>
      <if test="seriesId != null">
        series_id,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seriesType != null">
        #{seriesType,jdbcType=INTEGER},
      </if>
      <if test="seriesId != null">
        #{seriesId,jdbcType=INTEGER},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.SeriesOfArea">
    update series_of_area
    <set>
      <if test="seriesType != null">
        series_type = #{seriesType,jdbcType=INTEGER},
      </if>
      <if test="seriesId != null">
        series_id = #{seriesId,jdbcType=INTEGER},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.SeriesOfArea">
    update series_of_area
    set series_type = #{seriesType,jdbcType=INTEGER},
      series_id = #{seriesId,jdbcType=INTEGER},
      area_no = #{areaNo,jdbcType=INTEGER},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByModel" resultMap="voMap">
    select soa.id,
           series_type,
           series_id,
           soa.area_no,
           creator,
           soa.create_time,
           a.area_name
    from series_of_area soa
           left join area a on soa.area_no = a.area_no
    <where>
      <if test="seriesType != null">
        and soa.series_type = #{seriesType}
      </if>
      <if test="seriesId != null">
        and  soa.series_id = #{seriesId}
      </if>
    </where>
  </select>
  <delete id="deleteBySeries">
    delete from series_of_area where series_type = #{seriesType} and series_id = #{seriesId}
  </delete>
  <select id="selectAreaName" resultType="string">
    select
    a.area_name
    from series_of_area soa
    left join area a on soa.area_no = a.area_no
    where soa.series_type = #{seriesType}
        and soa.series_id = #{seriesId}
  </select>

  <insert id="insertBatch">
    insert into series_of_area(series_type, series_id, area_no, creator, create_time)
    values <foreach collection="seriesOfAreas" item="item" separator=",">
        (#{item.seriesType}, #{item.seriesId}, #{item.areaNo}, #{item.creator}, #{item.createTime})
    </foreach>
  </insert>

  <resultMap id="SeriesOfAreaBOResultMap" type="net.summerfarm.model.bo.area.SeriesOfAreaBO" extends="BaseResultMap">
    <result column="areaName" property="areaName"/>
  </resultMap>
  <select id="selectByTypeAndAreaNosAndSeriesIds" resultMap="SeriesOfAreaBOResultMap">
    select <include refid="Base_Column_List_With_Alias" />,a.area_name areaName
    from series_of_area soa join area a on soa.area_no = a.area_no
    where series_type = #{seriesType}
          and soa.area_no in
          <foreach collection="areaNos" item="areaNo" separator="," open="(" close=")">
            #{areaNo}
          </foreach>
          and series_id in
          <foreach collection="seriesIds" item="seriesId" separator="," open="(" close=")">
            #{seriesId}
          </foreach>
  </select>
    <resultMap id="SeriesOfAreaDTOResultMap" type="net.summerfarm.model.DTO.area.SeriesOfAreaDTO">
      <result column="area_no" property="areaNo"/>
      <result column="area_name" property="areaName"/>
      <result column="series_id" property="seriesId"/>
    </resultMap>
  <select id="selectByTypeAndSeriesId" resultMap="SeriesOfAreaDTOResultMap">
    select series_of_area.area_no,area_name
    from series_of_area left join area on series_of_area.area_no = area.area_no
    where series_id = #{seriesId} and series_type = #{seriesType}
  </select>

  <select id="selectBySeriesTypeAndAreaNos" resultMap="SeriesOfAreaDTOResultMap">
    select series_id,series_of_area.area_no,area_name
    from series_of_area left join area on series_of_area.area_no = area.area_no
    where series_type=#{seriesType,jdbcType=INTEGER}
            and series_of_area.area_no in <foreach item="areaNo" index="index" collection="areaNos" open="(" separator="," close=")">
                            #{areaNo,jdbcType=INTEGER}
                           </foreach>
  </select>

  <select id="selectTakeEffectBySeriesTypeAndAreaNos" resultType="net.summerfarm.model.DTO.area.SeriesOfAreaDTO">
    SELECT
    css.id seriesId, css.`name` couponSendSetupName, css.status status
    FROM
    coupon_sender_setup css
    LEFT JOIN series_of_area soa ON soa.series_id = css.id
    WHERE
    css.`status` in (0, 1)
    AND soa.series_type = #{seriesType,jdbcType=INTEGER}
    AND css.sender_type = #{senderType}
    AND soa.area_no in
    <foreach item="areaNo" index="index" collection="areaNos" open="(" separator="," close=")">
        #{areaNo,jdbcType=INTEGER}
    </foreach>
    GROUP BY css.id
  </select>
</mapper>