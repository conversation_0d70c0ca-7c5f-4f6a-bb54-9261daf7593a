<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CrmCommissionMerchantMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CrmCommissionMerchant">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="zone_name" jdbcType="VARCHAR" property="zoneName" />
    <result column="new_bd_reward" jdbcType="DECIMAL" property="newBdReward" />
    <result column="normal_bd_reward" jdbcType="DECIMAL" property="normalBdReward" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, zone_name, new_bd_reward, normal_bd_reward, updater, update_time, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_commission_merchant
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectMerchant" resultType="net.summerfarm.model.vo.CrmCommissionMerchantVo">
    select id, zone_name as zoneName, new_bd_reward as newBdReward, normal_bd_reward as normalBdReward
    from crm_commission_merchant
    <where>
      <if test="zoneName!=null and zoneName!=''">
        and zone_name like concat('%',#{zoneName},'%')
      </if>
    </where>
    order by create_time desc
  </select>
  <select id="selectByZoneName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_commission_merchant
    where zone_name = #{zoneName}
  </select>
  <select id="selectByPrimaryKeyList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_commission_merchant
    where id in
    <foreach collection="ids" separator="," item="item" open="(" close=")">
      #{item}
    </foreach>

  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crm_commission_merchant
    where id = #{id,jdbcType=INTEGER}
  </delete>
    <delete id="deleteByZoneName">
      delete from crm_commission_merchant
      where zone_name = #{zoneName}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CrmCommissionMerchant" useGeneratedKeys="true">
    insert into crm_commission_merchant (zone_name, new_bd_reward, normal_bd_reward, 
      updater, update_time, creator, 
      create_time)
    values (#{zoneName,jdbcType=VARCHAR}, #{newBdReward,jdbcType=DECIMAL}, #{normalBdReward,jdbcType=DECIMAL}, 
      #{updater,jdbcType=INTEGER}, sysdate(), #{creator,jdbcType=INTEGER},
     sysdate())
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CrmCommissionMerchant" useGeneratedKeys="true">
    insert into crm_commission_merchant
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="zoneName != null">
        zone_name,
      </if>
      <if test="newBdReward != null">
        new_bd_reward,
      </if>
      <if test="normalBdReward != null">
        normal_bd_reward,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="zoneName != null">
        #{zoneName,jdbcType=VARCHAR},
      </if>
      <if test="newBdReward != null">
        #{newBdReward,jdbcType=DECIMAL},
      </if>
      <if test="normalBdReward != null">
        #{normalBdReward,jdbcType=DECIMAL},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="copyMerchant">
    insert into crm_commission_merchant(zone_name, new_bd_reward, normal_bd_reward, updater, update_time, creator, create_time)
    select  #{info}, new_bd_reward, normal_bd_reward, #{adminId}, sysdate(), #{adminId}, sysdate()
    from crm_commission_merchant where zone_name = #{copyInfo}


  </insert>
  <update id="updateByPrimaryKeySelective">
    update crm_commission_merchant
    <set>
      <if test="zoneName != null">
        zone_name = #{zoneName,jdbcType=VARCHAR},
      </if>
      <if test="newBdReward != null">
        new_bd_reward = #{newBdReward,jdbcType=DECIMAL},
      </if>
      <if test="normalBdReward != null">
        normal_bd_reward = #{normalBdReward,jdbcType=DECIMAL},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id in
          <foreach collection="id" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>

  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CrmCommissionMerchant">
    update crm_commission_merchant
    set zone_name = #{zoneName,jdbcType=VARCHAR},
      new_bd_reward = #{newBdReward,jdbcType=DECIMAL},
      normal_bd_reward = #{normalBdReward,jdbcType=DECIMAL},
      updater = #{updater,jdbcType=INTEGER},
      update_time = sysdate()

    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>