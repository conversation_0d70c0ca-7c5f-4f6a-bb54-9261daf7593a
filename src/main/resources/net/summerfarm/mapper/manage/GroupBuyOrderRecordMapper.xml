<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.GroupBuyOrderRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.GroupBuyOrderRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="info_id" jdbcType="BIGINT" property="infoId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="contact" jdbcType="VARCHAR" property="contact" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, info_id, order_no, contact, phone, address, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from group_buy_order_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from group_buy_order_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.GroupBuyOrderRecord" useGeneratedKeys="true">
    insert into group_buy_order_record (create_time, update_time, info_id, 
      order_no, contact, phone, 
      address, remark)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{infoId,jdbcType=BIGINT}, 
      #{orderNo,jdbcType=VARCHAR}, #{contact,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.GroupBuyOrderRecord" useGeneratedKeys="true">
    insert into group_buy_order_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="infoId != null">
        info_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="contact != null">
        contact,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="infoId != null">
        #{infoId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.GroupBuyOrderRecord">
    update group_buy_order_record
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="infoId != null">
        info_id = #{infoId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        contact = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.GroupBuyOrderRecord">
    update group_buy_order_record
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      info_id = #{infoId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      contact = #{contact,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByInfoId" resultMap="BaseResultMap">
    select id,
      create_time,
      gboc.update_time,
      info_id,
      gboc.order_no,
      contact,
      phone,
      address,
      gboc.remark
    from group_buy_order_record gboc
    left join orders o on gboc.order_no = o.order_no
    where o.status in (2,3,6) and gboc.info_id = #{infoId}
  </select>
  <select id="sumGroupBuyInfo" resultType="java.math.BigDecimal">
    select ifnull(sum(o.total_price), 0)
    from group_buy_order_record g left join orders o on g.order_no = o.order_no
    where g.info_id = #{infoId} and o.status in (2,3,6)
  </select>
</mapper>