<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.AdvancePurchaseRecordMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.AdvancePurchaseRecord">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="advance_purchase_id" property="advancePurchaseId" />
        <result column="status" property="status" />
        <result column="remake" property="remake" />
        <result column="admin_name" property="adminName"  />
        <result column="admin_id" property="adminId"/>
        <result column="add_time" property="addTime" />
        <result column="update_time" property="updateTime" />
        <result column="purchase_no" property="purchaseNo"/>
        <result column="amount" property="amount"/>
    </resultMap>
    <sql id="Base">
        id,advance_purchase_id,status,remake,admin_name,admin_id,add_time,update_time,purchase_no,amount
    </sql>

    <insert id="addRecord" parameterType="net.summerfarm.model.domain.AdvancePurchaseRecord">
        insert into advance_purchase_record(add_time,update_time,advance_purchase_id,status,remake,admin_name,admin_id,purchase_no,amount)
        value (now(),now(),#{advancePurchaseId},#{status},#{remake},#{adminName},#{adminId},#{purchaseNo},#{amount})
    </insert>

    <update id="updateByPurchaseNo">
        update advance_purchase_record set purchase_no = #{newPurchaseNo} where purchase_no=#{oldPurchaseNo}
    </update>

    <select id="selectByAdvanceId" resultMap="BaseResultMap">
        select <include refid="Base"/>
        from advance_purchase_record
        where advance_purchase_id = #{advancePurchaseId}
        <if test="type != null and type == 0">
            and purchase_no is null
        </if>
        <if test="type != null and type == 1">
            and purchase_no is not null
        </if>
        order by id desc
    </select>
    <insert id="addBathRecords" parameterType="net.summerfarm.model.domain.AdvancePurchaseRecord">
        insert into advance_purchase_record (add_time,update_time,advance_purchase_id,status,remake,admin_name,admin_id,purchase_no,amount)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (now(),now(),#{item.advancePurchaseId},#{item.status},#{item.remake},#{item.adminName},#{item.adminId},#{item.purchaseNo},#{item.amount})
        </foreach>
    </insert>

    <select id="selectByPurchaseNo" resultMap="BaseResultMap" >
        select
            apr.id,
            apr.advance_purchase_id,
            apr.status,
            apr.remake,
            apr.admin_name,
            apr.admin_id,
            apr.add_time,
            apr.update_time,
            apr.purchase_no,
            apr.amount
        from advance_purchase_record apr
        left join advance_purchase ap on ap.id = apr.advance_purchase_id
        where apr.purchase_no = #{purchaseNo} and ap.supplier_id = #{supplierId}
        order by id desc
    </select>


</mapper>
