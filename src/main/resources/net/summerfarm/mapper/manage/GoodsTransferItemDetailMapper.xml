<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.GoodsTransferItemDetailMapper">

    <insert id="insertBathDetail" parameterType="net.summerfarm.model.domain.GoodsTransferItemDetail" useGeneratedKeys="true" keyProperty="id">
        insert into goods_transfer_item_detail (`goods_transfer_item_id`, `origin_gl_no` , `batch`,`quality_date`,`transfer_quantity` ,`new_gl_no`,quantity)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.goodsTransferItemId},#{item.originGlNo},#{item.batch},#{item.qualityDate},#{item.transferQuantity},#{item.newGlNo},#{item.quantity})
        </foreach>
    </insert>

    <select id="selectDetail" resultType="net.summerfarm.model.domain.GoodsTransferItemDetail">

        select id,
        goods_transfer_id goodsTransferId
        origin_gl_no originGlNo,
        batch  ,
        quality_date qualityDate,
        transfer_quantity transferQuantity,
        new_gl_no newGlNo ,
        from goods_transfer_item_detail where goods_transfer_id = #{id}

    </select>
</mapper>