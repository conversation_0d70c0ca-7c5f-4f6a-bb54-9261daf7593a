<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.AdminSkinMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.AdminSkin">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="show_flag" jdbcType="BOOLEAN" property="showFlag" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="background_image" jdbcType="VARCHAR" property="backgroundImage" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, admin_id, show_flag, logo, background_image, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from admin_skin
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from admin_skin
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByAdminId">
    delete from admin_skin
    where admin_id = #{adminId}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.AdminSkin" useGeneratedKeys="true">
    insert into admin_skin (admin_id, show_flag, logo, 
      background_image, create_time)
    values (#{adminId,jdbcType=INTEGER}, #{showFlag,jdbcType=BOOLEAN}, #{logo,jdbcType=VARCHAR}, 
      #{backgroundImage,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.AdminSkin" useGeneratedKeys="true">
    insert into admin_skin
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="showFlag != null">
        show_flag,
      </if>
      <if test="logo != null">
        logo,
      </if>
      <if test="backgroundImage != null">
        background_image,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="showFlag != null">
        #{showFlag,jdbcType=BOOLEAN},
      </if>
      <if test="logo != null">
        #{logo,jdbcType=VARCHAR},
      </if>
      <if test="backgroundImage != null">
        #{backgroundImage,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.AdminSkin">
    update admin_skin
    <set>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="showFlag != null">
        show_flag = #{showFlag,jdbcType=BOOLEAN},
      </if>
      <if test="logo != null">
        logo = #{logo,jdbcType=VARCHAR},
      </if>
      <if test="backgroundImage != null">
        background_image = #{backgroundImage,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.AdminSkin">
    update admin_skin
    set admin_id = #{adminId,jdbcType=INTEGER},
      show_flag = #{showFlag,jdbcType=BOOLEAN},
      logo = #{logo,jdbcType=VARCHAR},
      background_image = #{backgroundImage,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>