<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MerchantUpdateRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantUpdateRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="old_admin" jdbcType="BIGINT" property="oldAdmin" />
    <result column="change_type" jdbcType="TINYINT" property="changeType"/>
    <result column="new_admin" jdbcType="BIGINT" property="newAdmin" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, old_admin, new_admin, creator_id, create_time, change_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_update_record
    where id = #{id,jdbcType=BIGINT}
  </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MerchantUpdateRecord" useGeneratedKeys="true">
    insert into merchant_update_record (m_id, change_type, old_admin, new_admin,
      creator_id, create_time)
    values (#{mId,jdbcType=BIGINT}, #{changeType, jdbcType =TINYINT}, #{oldAdmin,jdbcType=BIGINT}, #{newAdmin,jdbcType=BIGINT},
      #{creatorId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MerchantUpdateRecord" useGeneratedKeys="true">
    insert into merchant_update_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="changeType != null">
        change_type,
      </if>
      <if test="oldAdmin != null">
        old_admin,
      </if>
      <if test="newAdmin != null">
        new_admin,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="changeType != null">
        #{changeType, jdbcType =TINYINT},
      </if>
      <if test="oldAdmin != null">
        #{oldAdmin,jdbcType=BIGINT},
      </if>
      <if test="newAdmin != null">
        #{newAdmin,jdbcType=BIGINT},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.MerchantUpdateRecord">
    update merchant_update_record
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="change_type != null">
        change_type = #{changeType, jdbcType =TINYINT}
      </if>
      <if test="oldAdmin != null">
        old_admin = #{oldAdmin,jdbcType=BIGINT},
      </if>
      <if test="newAdmin != null">
        new_admin = #{newAdmin,jdbcType=BIGINT},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.MerchantUpdateRecord">
    update merchant_update_record
    set m_id = #{mId,jdbcType=BIGINT},
      change_type = #{changeType, jdbcType =TINYINT}
      old_admin = #{oldAdmin,jdbcType=BIGINT},
      new_admin = #{newAdmin,jdbcType=BIGINT},
      creator_id = #{creatorId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="countMerchantByAdmin" resultType="java.lang.Integer">
    select count(1)
    from merchant_update_record
    <where>
      <if test="newAdmin != null">
        AND new_admin = #{newAdmin}
      </if>
      <if test="oldAdmin != null">
        AND old_admin = #{oldAdmin}
      </if>
      <if test="startTime != null">
        AND create_time  <![CDATA[ >= ]]> #{startTime}
      </if>
      <if test="endTime != null">
        AND create_time  <![CDATA[ <= ]]> #{endTime}
      </if>
    </where>
  </select>
</mapper>