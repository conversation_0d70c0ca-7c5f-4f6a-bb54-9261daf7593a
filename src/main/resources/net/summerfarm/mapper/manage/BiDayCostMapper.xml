<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.BiDayCostMapper">

    <select id="getCost" resultType="java.math.BigDecimal">
        SELECT sum(sku_cost)
        FROM bi_day_cost
        <where>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
            <if test="storeNo != null">
                AND store_no = #{storeNo}
            </if>
            <if test="startDate != null">
                AND adddate <![CDATA[>=]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND adddate <![CDATA[<=]]> #{endDate}
            </if>
        </where>
    </select>

    <select id="maxCostSku" resultType="java.lang.String">
        SELECT t.sku
        FROM (
          SELECT sku,SUM(sku_cost) cost
          FROM bi_day_cost
          <where>
              <if test="storeNos != null and storeNos.size!=0">
                  AND store_no IN
                  <foreach collection="storeNos" item="item" open="(" close=")" separator=",">
                      #{item}
                  </foreach>
              </if>
              <if test="startDate != null">
                  AND adddate <![CDATA[>=]]> #{startDate}
              </if>
              <if test="endDate != null">
                  AND adddate <![CDATA[<=]]> #{endDate}
              </if>
          </where>
          GROUP BY sku
          ORDER BY cost DESC
          <if test="limit != null">
              limit  #{limit}
          </if>
        ) t
    </select>

    <select id="maxCostSpu" resultType="java.lang.Long">
        SELECT t.pd_id
        FROM (
          SELECT p.pd_id,SUM(sku_cost) cost
          FROM bi_day_cost bdc
          INNER JOIN inventory i ON bdc.sku=i.sku
          INNER JOIN products p ON i.pd_id=p.pd_id
          <where>
              <if test="storeNos != null and storeNos.size!=0">
                  AND bdc.store_no IN
                  <foreach collection="storeNos" item="item" separator="," open="(" close=")">
                      #{item}
                  </foreach>
              </if>
              <if test="startDate != null">
                  AND adddate <![CDATA[>=]]> #{startDate}
              </if>
              <if test="endDate != null">
                  AND adddate <![CDATA[<=]]> #{endDate}
              </if>
          </where>
          GROUP BY p.pd_id
          ORDER BY cost DESC
          <if test="limit != null">
              limit  #{limit}
          </if>
        ) t
    </select>


    <select id="costSkuAll"  resultType="net.summerfarm.model.domain.BiDayCost">
        select SUM(sku_cost) skuCost,sku
        from bi_day_cost
        <where>
            <if test="storeNo == null">
                store_no in(1,2,8,9)
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo}
            </if>
            <if test="startDate != null">
                AND adddate <![CDATA[>=]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND adddate <![CDATA[<]]> #{endDate}
            </if>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
        </where>
        group by sku
        order by skuCost desc
        <if test="limit != null">
            limit  #{limit}
        </if>
    </select>


    <select id="costAll"  resultType="java.math.BigDecimal">

        select SUM(bdc.skuCost) cost from(
            select SUM(sku_cost) skuCost,sku
            from bi_day_cost
            <where>
                <if test="storeNo == null">
                    store_no in(1,2,8,9)
                </if>
                <if test="storeNo != null">
                    store_no = #{storeNo}
                </if>
                <if test="startDate != null">
                    AND adddate <![CDATA[>=]]> #{startDate}
                </if>
                <if test="endDate != null">
                    AND adddate <![CDATA[<=]]> #{endDate}
                </if>
            </where>
            group by sku
            order by skuCost desc
        ) bdc

    </select>

</mapper>