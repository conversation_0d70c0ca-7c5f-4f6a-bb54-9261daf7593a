<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MerchantPoolDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantPoolDetail">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="pool_info_id" jdbcType="BIGINT" property="poolInfoId"/>
    <result column="m_id" jdbcType="BIGINT" property="mId"/>
    <result column="size" jdbcType="VARCHAR" property="size"/>
    <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
    <result column="version" jdbcType="INTEGER" property="version"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `pool_info_id`, `m_id`, `size`, `area_no`, `version`, `create_time`, `update_time`
  </sql>

  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_pool_detail
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteById" parameterType="java.lang.Long">
    delete
    from merchant_pool_detail
    where `id` = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.MerchantPoolDetail">
    insert into merchant_pool_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="poolInfoId != null">
        `pool_info_id`,
      </if>
      <if test="mId != null">
        `m_id`,
      </if>
      <if test="size != null">
        `size`,
      </if>
      <if test="areaNo != null">
        `area_no`,
      </if>
      <if test="version != null">
        `version`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="poolInfoId != null">
        #{poolInfoId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="size != null">
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="net.summerfarm.model.domain.MerchantPoolDetail">
    update merchant_pool_detail
    <set>
      <if test="poolInfoId != null">
        `pool_info_id` = #{poolInfoId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        `m_id` = #{mId,jdbcType=BIGINT},
      </if>
      <if test="size != null">
        `size` = #{size,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        `area_no` = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        `version` = #{version,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByQuery" resultMap="BaseResultMap" parameterType="net.summerfarm.model.DTO.market.circle.MerchantQueryDTO">
    select
    <include refid="Base_Column_List"/>
    from
    merchant_pool_detail
    <where>
      pool_info_id = #{poolInfoId}
      and version = #{version}
      <if test="mId != null">
        and m_id = #{mId}
      </if>
      <if test="size != null">
        and size = #{size}
      </if>
      <if test="areaNos != null and areaNos.size > 0">
        and area_no in
        <foreach collection="areaNos" open="(" separator="," close=")" item="areaNo">
          #{areaNo}
        </foreach>
      </if>
    </where>
  </select>

  <select id="countByQuery" resultType="int" parameterType="net.summerfarm.model.DTO.market.circle.MerchantQueryDTO">
    select count(*)
    from merchant_pool_detail
    where pool_info_id = #{poolInfoId}
      and version = #{version}
  </select>

  <insert id="insertBatch" parameterType="net.summerfarm.model.domain.MerchantPoolDetail">
    insert into merchant_pool_detail (`pool_info_id`, `m_id`, `size`, `area_no`, `version`)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.poolInfoId,jdbcType=BIGINT},
      #{item.mId,jdbcType=BIGINT},#{item.size,jdbcType=VARCHAR},
      #{item.areaNo,jdbcType=INTEGER}, #{item.version,jdbcType=INTEGER})
    </foreach>
  </insert>

  <delete id="deleteByInfoId">
    delete
    from merchant_pool_detail
    <where>
      pool_info_id = #{poolInfoId}
      <if test="version != null">
        and `version` = #{version}
      </if>
    </where>
  </delete>

  <select id="getDetailByMId" resultMap="BaseResultMap">
    select mpd.*
    from merchant_pool_detail mpd
    right join merchant_pool_info mpi on mpd.pool_info_id = mpi.id and mpd.version = mpi.version
    where m_id = #{mId}
  </select>
</mapper>