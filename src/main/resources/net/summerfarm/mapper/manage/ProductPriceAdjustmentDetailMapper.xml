<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ProductPriceAdjustmentDetailMapper">

    <insert id="insert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO product_price_adjustment_detail (adjustment_id,sku,area_no,ladder_price,price,interest_rate,auto_flag) VALUES
            (#{adjustmentId},#{sku},#{areaNo},#{ladderPrice},#{price},#{interestRate},#{autoFlag})
    </insert>

</mapper>