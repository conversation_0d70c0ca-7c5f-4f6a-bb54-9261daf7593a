<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTaskMailPushRecordMapper">

    <resultMap id="withItem" type="net.summerfarm.model.domain.StockTaskMailPushRecord">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="stock_task_no" property="stockTaskNo" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="state" property="state" jdbcType="INTEGER"/>
        <result column="fail_reason" property="failReason" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.model.domain.StockTaskMailPushRecord">
        INSERT INTO stock_task_mail_push_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stockTaskNo != null">
                stock_task_no,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="failReason != null">
                fail_reason,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stockTaskNo != null">
                #{stockTaskNo},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="state != null">
                #{state},
            </if>
            <if test="failReason != null">
                #{failReason},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.model.domain.StockTaskMailPushRecord">
        INSERT INTO stock_task_mail_push_record
        (stock_task_no, create_time, admin_id, state, fail_reason)
        values
        <foreach collection="list" item="item" separator=",">
        (#{item.stockTaskNo}, #{item.createTime}, #{item.adminId}, #{item.state}, #{item.failReason})
        </foreach>
    </insert>

</mapper>
