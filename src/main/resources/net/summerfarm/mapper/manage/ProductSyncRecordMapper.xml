<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ProductSyncRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.ProductSyncRecord">
    <id column="product_sync_record_id" jdbcType="BIGINT" property="id" />
    <result column="product_sync_record_pd_id" jdbcType="BIGINT" property="pdId" />
    <result column="product_sync_record_sku" jdbcType="VARCHAR" property="sku" />
    <result column="product_sync_record_area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="product_sync_record_reason" jdbcType="VARCHAR" property="reason" />
    <result column="product_sync_record_retry_flag" jdbcType="TINYINT" property="retryFlag" />
    <result column="product_sync_record_api_type" jdbcType="VARCHAR" property="apiType" />
    <result column="product_sync_record_create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="product_sync_record_update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    product_sync_record.id as product_sync_record_id, product_sync_record.pd_id as product_sync_record_pd_id, 
    product_sync_record.sku as product_sync_record_sku, product_sync_record.area_no as product_sync_record_area_no, 
    product_sync_record.reason as product_sync_record_reason, product_sync_record.retry_flag as product_sync_record_retry_flag, 
    product_sync_record.api_type as product_sync_record_api_type, product_sync_record.create_time as product_sync_record_create_time, 
    product_sync_record.update_time as product_sync_record_update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from product_sync_record product_sync_record
    where product_sync_record.id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from product_sync_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.ProductSyncRecord">
    insert into product_sync_record (id, pd_id, sku, 
      area_no, reason, retry_flag, 
      api_type, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{pdId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR},
      #{areaNo,jdbcType=INTEGER}, #{reason,jdbcType=VARCHAR}, #{retryFlag,jdbcType=TINYINT}, 
      #{apiType,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.ProductSyncRecord">
    insert into product_sync_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="retryFlag != null">
        retry_flag,
      </if>
      <if test="apiType != null">
        api_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="retryFlag != null">
        #{retryFlag,jdbcType=TINYINT},
      </if>
      <if test="apiType != null">
        #{apiType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.ProductSyncRecord">
    update product_sync_record
    <set>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="retryFlag != null">
        retry_flag = #{retryFlag,jdbcType=TINYINT},
      </if>
      <if test="apiType != null">
        api_type = #{apiType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.ProductSyncRecord">
    update product_sync_record
    set pd_id = #{pdId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=VARCHAR},
      area_no = #{areaNo,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR},
      retry_flag = #{retryFlag,jdbcType=TINYINT},
      api_type = #{apiType,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>