<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PartnershipBuyConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PartnershipBuyConfig">
    <!--@mbg.generated-->
    <!--@Table market_partnership_buy_config-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="partnership_buy_name" jdbcType="VARCHAR" property="partnershipBuyName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="actual_end_time" jdbcType="TIMESTAMP" property="actualEndTime" />
    <result column="join_num" jdbcType="INTEGER" property="joinNum" />
    <result column="valid_time" jdbcType="INTEGER" property="validTime" />
    <result column="simulation_group" jdbcType="TINYINT" property="simulationGroup" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator"/>
    <result column="updater" jdbcType="INTEGER" property="creator"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, partnership_buy_name, `status`, start_time, end_time, actual_end_time, join_num, 
    valid_time, simulation_group, create_time, update_time,creator,updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from market_partnership_buy_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from market_partnership_buy_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PartnershipBuyConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into market_partnership_buy_config (partnership_buy_name, `status`, start_time, 
      end_time, actual_end_time, join_num, 
      valid_time, simulation_group, create_time, 
      update_time,creator,updater)
    values (#{partnershipBuyName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{actualEndTime,jdbcType=TIMESTAMP}, #{joinNum,jdbcType=INTEGER}, 
      #{validTime,jdbcType=INTEGER}, #{simulationGroup,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP},#{creator,jdbcType=INTEGER},#{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PartnershipBuyConfig" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into market_partnership_buy_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="partnershipBuyName != null">
        partnership_buy_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="actualEndTime != null">
        actual_end_time,
      </if>
      <if test="joinNum != null">
        join_num,
      </if>
      <if test="validTime != null">
        valid_time,
      </if>
      <if test="simulationGroup != null">
        simulation_group,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="partnershipBuyName != null">
        #{partnershipBuyName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualEndTime != null">
        #{actualEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="joinNum != null">
        #{joinNum,jdbcType=INTEGER},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=INTEGER},
      </if>
      <if test="simulationGroup != null">
        #{simulationGroup,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PartnershipBuyConfig">
    <!--@mbg.generated-->
    update market_partnership_buy_config
    <set>
      <if test="partnershipBuyName != null">
        partnership_buy_name = #{partnershipBuyName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualEndTime != null">
        actual_end_time = #{actualEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="joinNum != null">
        join_num = #{joinNum,jdbcType=INTEGER},
      </if>
      <if test="validTime != null">
        valid_time = #{validTime,jdbcType=INTEGER},
      </if>
      <if test="simulationGroup != null">
        simulation_group = #{simulationGroup,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PartnershipBuyConfig">
    <!--@mbg.generated-->
    update market_partnership_buy_config
    set partnership_buy_name = #{partnershipBuyName,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      actual_end_time = #{actualEndTime,jdbcType=TIMESTAMP},
      join_num = #{joinNum,jdbcType=INTEGER},
      valid_time = #{validTime,jdbcType=INTEGER},
      simulation_group = #{simulationGroup,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=INTEGER},
      updater = #{updater,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectEffectiveAreaNos" resultType="java.lang.Integer">
    select distinct area_no
    from market_partnership_buy_config mpbc left join series_of_area sfa on mpbc.id = sfa.series_id
    where status = 1
        and ((start_time <![CDATA[ <= ]]> #{startTime} and end_time >= #{startTime}) or
             (start_time <![CDATA[ <= ]]> #{endTime} and end_time >= #{endTime}))
        and sfa.series_type = 4
  </select>

  <resultMap id="PartnershipBuyConfigListDTOResultMap" type="net.summerfarm.model.DTO.market.PartnershipBuyConfigListDTO">
    <id column="id" property="id"/>
    <result column="partnership_buy_name" property="partnershipBuyName"/>
    <result column="start_time" property="startTime"/>
    <result column="end_time" property="endTime"/>
    <result column="status" property="status"/>
  </resultMap>
  <select id="selectListByCondtion" resultMap="PartnershipBuyConfigListDTOResultMap">
    select mpbc.id, partnership_buy_name, start_time, end_time, mpbc.`status`
    from market_partnership_buy_config mpbc
    <where>
      <if test="queryDTO.status != null">
        <choose>
          <when test="queryDTO.status == 0">
            and mpbc.status = 1 and start_time > now()
          </when>
          <when test="queryDTO.status == 2">
            and mpbc.status = #{queryDTO.status}
          </when>
          <otherwise>
            and mpbc.status = 1 and start_time <![CDATA[ <= ]]> now()
          </otherwise>
        </choose>
      </if>
      <if test="configIds != null and configIds.size() != 0">
        and mpbc.id in <foreach collection="configIds" open="(" close=")" separator="," item="configId" >
                              #{configId}
                            </foreach>
      </if>
    </where>
    order by id desc
  </select>

  <update id="updateStatusById">
    update market_partnership_buy_config
    set status = #{status}, actual_end_time = now(), updater = #{adminId}
    where id = #{id}
  </update>
</mapper>