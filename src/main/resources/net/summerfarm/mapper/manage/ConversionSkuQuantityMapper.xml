<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.ConversionSkuQuantityMapper">


    <insert id="saveSkuQuantity" parameterType="net.summerfarm.model.domain.ConversionSkuQuantity" useGeneratedKeys="true" keyProperty="id">
        insert into conversion_sku_quantity (update_time,add_time,`date`,sale_cnt_seven,sale_cnt_fifteen,min_sale_cnt,warehouse_no,sku,max_sale_seven)
        values (now(),now(),#{date},#{saleCntSeven},#{saleCntFifteen},#{minSaleCnt},#{wareohuseNo},#{sku},#{maxSaleSeven})
    </insert>

    <select id="selectDetail" parameterType="net.summerfarm.model.domain.ConversionSkuQuantity"
            resultType="net.summerfarm.model.domain.ConversionSkuQuantity">
        select min_sale_cnt minSaleCnt, sale_cnt_fifteen saleCntFifteen, sale_cnt_seven saleCntSeven, max_sale_seven maxSaleSeven
         from conversion_sku_quantity
         where sku = #{sku} and warehouse_no = #{warehouseNo} and `date` = #{date}
    </select>

    <insert id="saveBatchSkuQuantity" parameterType="net.summerfarm.model.domain.ConversionSkuQuantity">
        insert into conversion_sku_quantity (update_time,add_time,`date`,sale_cnt_seven,sale_cnt_fifteen,min_sale_cnt,warehouse_no,sku,max_sale_seven)
        values
        <foreach collection="list" separator="," index="index" item="item">
         (now(),now(),#{item.date},#{item.saleCntSeven},#{item.saleCntFifteen},#{item.minSaleCnt},#{item.warehouseNo},#{item.sku},#{item.maxSaleSeven})
        </foreach>
    </insert>
</mapper>