<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTaskPickMapper">

    <resultMap id="stockTaskPickVO" type="net.summerfarm.model.vo.StockTaskPickVO">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="add_time" property="addTime"/>
        <result column="sku" property="sku"/>
        <result column="pd_name" property="pdName"/>
        <result column="amount" property="amount"/>
        <result column="admin_id" property="adminId"/>
        <result column="admin_name" property="adminName"/>
        <result column="type" property="type"/>
        <result column="weight" property="weight"/>
        <result column="out_store_no" property="outStoreNo"/>
        <result column="store_no" property="storeNo"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="unit" property="unit"/>
        <result column="category" property="categoryName"/>
        <result column="categoryType" property="categoryType"/>
        <result column="storage_method" property="storageMethod"/>
        <result column="close_order_time" jdbcType="VARCHAR" property="closeOrderTime"/>
        <result column="name_remakes" property="nameRemakes"/>
        <result column="skuType" property="skuType"/>
        <collection property="stockTaskPickDetails" ofType ="net.summerfarm.model.domain.StockTaskPickDetail">
            <id column="stpdId" property="id"  jdbcType="INTEGER"/>
            <result column="stock_task_pick_id" property="stockTaskPickId"/>
            <result column="list_no" property="listNo"/>
            <result column="gl_no" property="glNo"/>
            <result column="quality_date" property="qualityDate"/>
            <result column="production_date" property="productionDate"/>
            <result column="should_quantity" property="shouldQuantity"/>
        </collection>
    </resultMap>
    <insert id="insertBatch"  parameterType="net.summerfarm.model.domain.StockTaskPick" useGeneratedKeys="true" keyProperty="id">
        insert into stock_task_pick (`add_time`, `sku` , `pd_name`,`weight`,`amount` ,`admin_id`,`admin_name`,`type`,store_no,delivery_time,close_order_time,out_store_no)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.addTime},#{item.sku},#{item.pdName},#{item.weight},#{item.amount},#{item.adminId},#{item.adminName}
            ,#{item.type},#{item.storeNo},#{item.deliveryTime},#{item.closeOrderTime},#{item.outStoreNo})
        </foreach>

    </insert>

</mapper>