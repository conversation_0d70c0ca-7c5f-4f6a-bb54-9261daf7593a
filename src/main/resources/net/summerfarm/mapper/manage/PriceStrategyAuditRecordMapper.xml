<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PriceStrategyAuditRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PriceStrategyAuditRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="audit_id" jdbcType="BIGINT" property="auditId" />
    <result column="strategy_id" jdbcType="INTEGER" property="strategyId" />
    <result column="old_price" jdbcType="DECIMAL" property="oldPrice" />
    <result column="new_price" jdbcType="DECIMAL" property="newPrice" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, audit_id, strategy_id, old_price, new_price
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from price_strategy_audit_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from price_strategy_audit_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PriceStrategyAuditRecord" useGeneratedKeys="true">
    insert into price_strategy_audit_record (create_time, update_time, audit_id, 
      strategy_id, old_price, new_price
      )
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{auditId,jdbcType=BIGINT}, 
      #{strategyId,jdbcType=INTEGER}, #{oldPrice,jdbcType=DECIMAL}, #{newPrice,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PriceStrategyAuditRecord" useGeneratedKeys="true">
    insert into price_strategy_audit_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="auditId != null">
        audit_id,
      </if>
      <if test="strategyId != null">
        strategy_id,
      </if>
      <if test="oldPrice != null">
        old_price,
      </if>
      <if test="newPrice != null">
        new_price,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditId != null">
        #{auditId,jdbcType=BIGINT},
      </if>
      <if test="strategyId != null">
        #{strategyId,jdbcType=INTEGER},
      </if>
      <if test="oldPrice != null">
        #{oldPrice,jdbcType=DECIMAL},
      </if>
      <if test="newPrice != null">
        #{newPrice,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PriceStrategyAuditRecord">
    update price_strategy_audit_record
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditId != null">
        audit_id = #{auditId,jdbcType=BIGINT},
      </if>
      <if test="strategyId != null">
        strategy_id = #{strategyId,jdbcType=INTEGER},
      </if>
      <if test="oldPrice != null">
        old_price = #{oldPrice,jdbcType=DECIMAL},
      </if>
      <if test="newPrice != null">
        new_price = #{newPrice,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PriceStrategyAuditRecord">
    update price_strategy_audit_record
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      audit_id = #{auditId,jdbcType=BIGINT},
      strategy_id = #{strategyId,jdbcType=INTEGER},
      old_price = #{oldPrice,jdbcType=DECIMAL},
      new_price = #{newPrice,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByAuditId" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from price_strategy_audit_record
    where audit_id = #{auditId}
  </select>
</mapper>