<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ShoppingCartMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.ShoppingCart">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="parent_sku" jdbcType="VARCHAR" property="parentSku" />
    <result column="product_type" jdbcType="TINYINT" property="productType" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, m_id, account_id, biz_id, sku, parent_sku, product_type, quantity, create_time, 
    update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from shopping_cart
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from shopping_cart
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.ShoppingCart">
    insert into shopping_cart (id, m_id, account_id, 
      biz_id, sku, parent_sku, 
      product_type, quantity, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, 
      #{bizId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{parentSku,jdbcType=VARCHAR}, 
      #{productType,jdbcType=TINYINT}, #{quantity,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.ShoppingCart">
    insert into shopping_cart
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="parentSku != null">
        parent_sku,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="parentSku != null">
        #{parentSku,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=TINYINT},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.ShoppingCart">
    update shopping_cart
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="parentSku != null">
        parent_sku = #{parentSku,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=TINYINT},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.ShoppingCart">
    update shopping_cart
    set m_id = #{mId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=VARCHAR},
      parent_sku = #{parentSku,jdbcType=VARCHAR},
      product_type = #{productType,jdbcType=TINYINT},
      quantity = #{quantity,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByMids">
    delete from shopping_cart
    where m_id in
    <foreach collection="mIdList" item="mId" separator="," open="(" close=")">
        #{mId}
    </foreach>
  </delete>

  <delete id="deleteByMid">
    delete from shopping_cart
    where m_id = #{mId,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByBizIdAndSku">
    delete from shopping_cart where biz_id = #{bizId} and sku = #{sku} limit 200
  </delete>

  <delete id="deleteByBizId">
    delete from shopping_cart where biz_id = #{bizId}  limit 200
  </delete>

  <select id="selectOrderReminder" resultType="net.summerfarm.model.vo.OrderItemVO">
    SELECT t.quantity amount,p.pd_name pdName,c.type
    FROM shopping_cart t
           INNER JOIN inventory i ON t.sku = i.sku
           INNER JOIN products p ON p.pd_id = i.pd_id
           INNER JOIN category c ON p.category_id = c.id
    WHERE t.quantity <![CDATA[ >= ]]> 50 AND t.m_id = #{mId} AND t.update_time <![CDATA[ >= ]]> #{time}
  </select>
</mapper>