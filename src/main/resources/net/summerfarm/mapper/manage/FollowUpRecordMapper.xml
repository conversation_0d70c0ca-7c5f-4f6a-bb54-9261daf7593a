<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.FollowUpRecordMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FollowUpRecord" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="m_id" property="mId" jdbcType="BIGINT" />
    <result column="admin_id" property="adminId" jdbcType="BIGINT" />
    <result column="admin_name" property="adminName" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="m_lifecycle" property="mLifecycle" jdbcType="INTEGER" />
    <result column="m_tag" property="mTag" jdbcType="VARCHAR" />
    <result column="m_last_order_time" property="mLastOrderTime" jdbcType="TIMESTAMP" />
    <result column="follow_up_way" property="followUpWay" jdbcType="VARCHAR" />
    <result column="follow_up_pic" property="followUpPic" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="priority" property="priority" jdbcType="INTEGER" />
    <result column="condition" property="condition" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="contact_id" property="contactId"  />
    <result column="next_follow_time" property="nextFollowTime"/>
    <result column="expected_content" property="expectedContent"/>
    <result column="visit_objective" property="visitObjective"/>
    <result column="whether_remark" property="whetherRemark"/>
    <result column="visit_type" property="visitType"/>
    <result column="location" property="location"/>
    <result column="kp_id" property="kpId"/>
    <result column="poi_note" property="poiNote"/>
    <result column="escort_admin_id" property="escortAdminId"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, admin_id, admin_name, creator, m_lifecycle, m_tag, m_last_order_time, follow_up_way, follow_up_pic, status,
    priority, `condition`, add_time,contact_id,next_follow_time,expected_content,visit_objective,whether_remark,visit_type,location,
    kp_id,poi_note,escort_admin_id
  </sql>

  <select id="count" parameterType="net.summerfarm.model.vo.FollowUpRecordVO" resultType="java.lang.Integer">
    SELECT  count(1) FROM  follow_up_record fur
    <where>
      <if test="status ==null">
        AND fur.status in (1,2,3,4)
      </if>
      <if test="status !=null">
        AND fur.status = #{status}
      </if>
      <if test="startTime !=null">
        AND fur.add_time <![CDATA[>=]]> #{startTime}
      </if>
      <if test="endTime !=null">
        AND fur.add_time <![CDATA[<]]> #{endTime}
      </if>
      <if test="adminId !=null" >
        AND fur.admin_id = #{adminId}
      </if>
      <if test="mId !=null" >
        AND fur.m_id = #{mId}
      </if>
      <if test="visitToHome !=null" >
        AND fur.follow_up_way IN ('有效拜访','普通上门拜访')
      </if>
    </where>
  </select>

  <select id="selectVO" parameterType="net.summerfarm.model.vo.FollowUpRecordVO" resultType="net.summerfarm.model.vo.FollowUpRecordVO" >
    SELECT t.id, t.m_id mId, t.admin_id adminId, t.admin_name adminName,t.follow_up_way followUpWay, t.follow_up_pic followUpPic,
      t.status, t.condition,t.add_time addTime,m.mname,concat(c.city,c.area,c.address,ifnull(c.house_number,'')) address,t.creator creator,
      m.grade,m.size mSize,t.location location,t.kp_id kpId,t.visit_objective visitObjective,t.visit_type visitType,t.escort_admin_id escortAdminId,
    c.phone phone
    FROM follow_up_record t
    LEFT JOIN merchant m ON  m.m_id = t.m_id
    LEFT JOIN contact c on c.contact_id = t.contact_id
    <where>
      <if test="adminId != null">
        AND t.admin_id = #{adminId}
      </if>
      <if test="contactId != null">
        AND t.contact_id = #{contactId}
      </if>
      <if test="addTime != null">
        AND date(t.add_time) = date(#{addTime})
      </if>
      <if test="startTime !=null">
        AND t.add_time <![CDATA[>=]]>  #{startTime}
      </if>
      <if test="endTime !=null">
        AND t.add_time <![CDATA[<]]>  #{endTime}
      </if>
      <if test="mId != null">
        AND t.m_id = #{mId}
      </if>
      <if test="areaNo != null">
        AND m.area_no = #{areaNo}
      </if>
      <if test="id != null">
        AND t.id = #{id}
      </if>
      <if test="visitType != null">
        AND t.visit_type = #{visitType}
      </if>
      <if test="mname != null">
        AND m.mname LIKE CONCAT(#{mname,jdbcType=VARCHAR},'%')
      </if>
      <if test="followUpWayList != null and followUpWayList.size() >0">
        AND t.follow_up_way in
        <foreach collection="followUpWayList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
    ORDER BY t.id desc
  </select>

  <update id="updateById" parameterType="net.summerfarm.model.domain.FollowUpRecord">
    UPDATE follow_up_record t
    <set>
      <if test="followUpPic != null" >
        t.follow_up_pic = #{followUpPic},
      </if>
      <if test="followUpWay != null" >
        t.follow_up_way = #{followUpWay},
      </if>
      <if test="status != null" >
        t.status =#{status},
      </if>
      <if test="condition != null" >
        t.condition = #{condition,jdbcType=VARCHAR},
      </if>
      <if test=" addTime !=null">
        t.add_time = #{addTime,jdbcType=TIMESTAMP}
      </if>
      <if test="expectedContent != null">
        t.expected_content = #{expectedContent}
      </if>
      <if test="visitObjective != null">
          t.visit_objective = #{visitObjective}
      </if>
    </set>
    where t.id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateUnfinished" >
    UPDATE follow_up_record t
    SET t.status = 9
    WHERE t.priority <![CDATA[<>]]> 0
    AND DATE(t.add_time) <![CDATA[<]]> CURDATE()
    AND t.status = 0
  </update>

<select id="select" resultMap="BaseResultMap" parameterType="net.summerfarm.model.domain.FollowUpRecord" >
  select
  <include refid="Base_Column_List" />
  from follow_up_record 
  <where>
    <if test="priority != null" >
      AND priority = #{priority}
    </if>
    <if test="status != null" >
      AND status = #{status}
    </if>
    <if test="mId != null">
      AND  m_id =#{mId}
    </if>
  </where>
</select>


  <select id="countAdmin" resultType="java.lang.Integer" >
    select
    count(*)
    from follow_up_record
    <where>
      status !=0
      <if test="adminId != null">
        AND  admin_id =#{adminId}
      </if>
      <if test="startTime != null">
        AND  add_time  <![CDATA[>=]]> #{startTime}
      </if>
    </where>
  </select>

  <select id="selectLast" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from follow_up_record
    <where>
      <if test="priority != null" >
        AND priority = #{priority}
      </if>
      <if test="status != null" >
        AND status = #{status}
      </if>
      <if test="mId != null">
        AND  m_id =#{mId}
      </if>
    </where>

    order by id desc
    limit 1
  </select>

  <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from follow_up_record
    where id=#{id}
  </select>

  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.model.domain.FollowUpRecord" >
    insert into follow_up_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="mId != null" >
        m_id,
      </if>
      <if test="adminId != null" >
        admin_id,
      </if>
      <if test="adminName != null" >
        admin_name,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="mLifecycle != null" >
        m_lifecycle,
      </if>
      <if test="mTag != null" >
        m_tag,
      </if>
      <if test="mLastOrderTime != null" >
        m_last_order_time,
      </if>
      <if test="followUpPic != null" >
        follow_up_pic,
      </if>
      <if test="followUpWay != null" >
        follow_up_way,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="condition != null" >
        `condition`,
      </if>
      <if test="addTime != null" >
        add_time,
      </if>
      <if test="priority != null" >
        priority,
      </if>
      <if test="contactId != null" >
        contact_id,
      </if>
      <if test="nextFollowTime != null">
        next_follow_time,
      </if>
      <if test="expectedContent != null">
        expected_content,
      </if>
      <if test=" visitObjective != null">
        visit_objective,
      </if>
      <if test="whetherRemark != null">
        whether_remark,
      </if>
      <if test="visitType != null">
        visit_type,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="kpId != null">
        kp_id,
      </if>
      <if test="poiNote != null">
        poi_note,
      </if>
      <if test="escortAdminId != null">
        escort_admin_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="mId != null" >
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="adminId != null" >
        #{adminId},
      </if>
      <if test="adminName != null" >
        #{adminName},
      </if>
      <if test="creator != null" >
        #{creator},
      </if>
      <if test="mLifecycle != null" >
        #{mLifecycle},
      </if>
      <if test="mTag != null" >
        #{mTag},
      </if>
      <if test="mLastOrderTime != null" >
        #{mLastOrderTime},
      </if>
      <if test="followUpPic != null" >
        #{followUpPic},
      </if>
      <if test="followUpWay != null" >
        #{followUpWay},
      </if>
      <if test="status != null" >
        #{status},
      </if>
      <if test="condition != null" >
        #{condition,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="priority != null" >
        #{priority},
      </if>
      <if test="contactId != null" >
        #{contactId},
      </if>
      <if test="nextFollowTime != null">
        #{nextFollowTime},
      </if>
      <if test="expectedContent != null">
        #{expectedContent},
      </if>
      <if test=" visitObjective != null">
        #{visitObjective},
      </if>
      <if test="whetherRemark != null">
        #{whetherRemark},
      </if>
      <if test="visitType != null">
        #{visitType},
      </if>
      <if test="location != null">
        #{location},
      </if>
      <if test="kpId != null">
        #{kpId},
      </if>
      <if test="poiNote != null">
        #{poiNote},
      </if>
      <if test="escortAdminId != null">
        #{escortAdminId},
      </if>
    </trim>
  </insert>
  <select id="selectByStart" resultType="net.summerfarm.model.vo.FollowUpRecordVO">
    select
    fur.follow_up_way followUpWay , fur.condition,m.mname,
    fur.admin_id adminId,a.realname adminName ,fur.add_time addTime,ar.area_name areaName,
    fur.visit_objective visitObjective,fur.escort_admin_id escortAdminId,fur.location location,fur.kp_id kpId
    from follow_up_record fur
    left join merchant m on m.m_id = fur.m_id
    left join area ar on ar.area_no = m.area_no
    left join admin a on a.admin_id= fur.admin_id
    WHERE visit_type = 0
      <if test="adminId != null">
        AND fur.admin_id = #{adminId}
      </if>
      <if test="startTime !=null">
        AND fur.add_time <![CDATA[>=]]>  #{startTime}
      </if>
      <if test="endTime !=null">
        AND fur.add_time <![CDATA[<]]>  #{endTime}
      </if>
      <if test="mId != null">
        AND fur.m_id = #{mId}
      </if>
      <if test="areaNo != null">
        AND m.area_no = #{areaNo}
      </if>
      <if test="mname != null">
        AND m.mname LIKE CONCAT(#{mname,jdbcType=VARCHAR},'%')
      </if>
      <if test="followUpWayList != null and followUpWayList.size() >0">
        AND fur.follow_up_way in
        <foreach collection="followUpWayList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    order by fur.add_time DESC
  </select>
  <select id="noteDetails" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM follow_up_record WHERE m_id = #{mId} AND whether_remark = #{couponId} LIMIT 1
  </select>

  <select id="selectEscortRecord" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" />
    FROM follow_up_record
    WHERE `status` = 1 AND visit_type = 1
    AND  escort_admin_id = #{adminId}
    and date(add_time) = #{date}
    and contact_id = #{contactId}
    and admin_id = #{escortAdminId}
  </select>
</mapper>