<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DynamicPriceTaskMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DynamicPriceTask">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="model_config_id" jdbcType="BIGINT" property="modelConfigId"/>
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo"/>
    <result column="category_type" jdbcType="TINYINT" property="categoryType"/>
    <result column="task_exe_time" jdbcType="TIMESTAMP" property="taskExeTime"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `model_config_id`, `warehouse_no`, `category_type`, `task_exe_time`, `status`,
    `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_task
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from dynamic_price_task
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.DynamicPriceTask">
    insert into dynamic_price_task (`id`, `model_config_id`, `warehouse_no`,
                                    `category_type`, `task_exe_time`, `status`,
                                    `create_time`, `update_time`)
    values (#{id,jdbcType=BIGINT}, #{modelConfigId,jdbcType=BIGINT},
            #{warehouseNo,jdbcType=INTEGER},
            #{categoryType,jdbcType=TINYINT}, #{taskExeTime,jdbcType=TIMESTAMP},
            #{status,jdbcType=TINYINT},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.DynamicPriceTask" useGeneratedKeys="true" keyProperty="id">
    insert into dynamic_price_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="modelConfigId != null">
        `model_config_id`,
      </if>
      <if test="warehouseNo != null">
        `warehouse_no`,
      </if>
      <if test="categoryType != null">
        `category_type`,
      </if>
      <if test="taskExeTime != null">
        `task_exe_time`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="modelConfigId != null">
        #{modelConfigId,jdbcType=BIGINT},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="categoryType != null">
        #{categoryType,jdbcType=TINYINT},
      </if>
      <if test="taskExeTime != null">
        #{taskExeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.model.domain.DynamicPriceTask">
    update dynamic_price_task
    <set>
      <if test="modelConfigId != null">
        `model_config_id` = #{modelConfigId,jdbcType=BIGINT},
      </if>
      <if test="warehouseNo != null">
        `warehouse_no` = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="categoryType != null">
        `category_type` = #{categoryType,jdbcType=TINYINT},
      </if>
      <if test="taskExeTime != null">
        `task_exe_time` = #{taskExeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DynamicPriceTask">
    update dynamic_price_task
    set `model_config_id` = #{modelConfigId,jdbcType=BIGINT},
        `warehouse_no`    = #{warehouseNo,jdbcType=INTEGER},
        `category_type`   = #{categoryType,jdbcType=TINYINT},
        `task_exe_time`   = #{taskExeTime,jdbcType=TIMESTAMP},
        `status`          = #{status,jdbcType=TINYINT},
        `create_time`     = #{createTime,jdbcType=TIMESTAMP},
        `update_time`     = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByQuery"
    parameterType="net.summerfarm.model.DTO.inventory.DynamicPriceTaskQueryDTO"
    resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_task
    <where>
      <if test="warehouseNos != null and warehouseNos.size > 0">
        and warehouse_no in
        <foreach collection="warehouseNos" item="warehouseNo" open="(" separator="," close=")" >
          #{warehouseNo}
        </foreach>
      </if>
      <if test="categoryType != null">
        and category_type = #{categoryType}
      </if>
      <if test="startTime != null">
        and task_exe_time &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        and task_exe_time &lt;= #{endTime}
      </if>
    </where>
    order by id desc
  </select>

  <select id="countByTaskExeTime" resultType="int">
    select count(*)
    from dynamic_price_task
    where task_exe_time = #{taskExeTime}
  </select>

  <select id="getTodayLast" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_task
    where category_type = #{categoryType}
      and task_exe_time &lt; CONCAT(CURRENT_DATE(),' ', DATE_FORMAT(now(),'%H'),':00:00')
      and task_exe_time &gt; CONCAT(CURDATE(),' 00:00:00')
    order by id desc
    limit 1
  </select>

  <update id="updateStatus" parameterType="java.lang.Integer">
    update dynamic_price_task
    set status = #{status}
    where status = 0
  </update>

</mapper>