<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CouponConfigDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CouponConfigDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="coupon_config_id" jdbcType="INTEGER" property="couponConfigId" />
    <result column="name" jdbcType="INTEGER" property="name" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="threshold" jdbcType="DECIMAL" property="threshold" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>

  <resultMap id="Map" type="net.summerfarm.model.domain.CouponConfigDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="coupon_config_id" jdbcType="INTEGER" property="couponConfigId" />
    <result column="coupon_id" jdbcType="INTEGER" property="couponId" />
    <result column="name" jdbcType="INTEGER" property="name" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="threshold" jdbcType="DECIMAL" property="threshold" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="effective_time" jdbcType="INTEGER" property="effectiveTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    id, coupon_config_id, `name`, money, threshold, `number`, create_time, creator, `status`, update_time,
    updater
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="Map">
    select id,coupon_config_id, `name`, money, `number`,threshold ,effective_time
    from coupon_config_detail
    where coupon_config_id = #{id,jdbcType=INTEGER} and status = 0
  </select>

  <select id="selectCoupon" parameterType="integer" resultMap="Map">
    select *
    from coupon_config_detail
    where coupon_config_id = #{id,jdbcType=INTEGER} and status = 0
  </select>

  <update id="deleteByPrimaryKey">
    update coupon_config_detail
    set status = 1
    where status = 0
  </update>

  <insert id="insert" parameterType="net.summerfarm.model.domain.CouponConfigDetail">
    insert into coupon_config_detail (coupon_config_id, `name`, money, threshold, number,effective_time, create_time, creator, `status`, update_time, updater)
    values (#{couponConfigId,jdbcType=INTEGER}, #{name,jdbcType=INTEGER}, #{money,jdbcType=DECIMAL}, #{threshold,jdbcType=DECIMAL},#{number},#{effectiveTime},
            now(), #{creator,jdbcType=VARCHAR}, 0, now(), #{updater,jdbcType=VARCHAR})
  </insert>

  <update id="updateByCouponId">
    update coupon_config_detail
    set coupon_id = #{couponId}
    where id = #{id} and status = 0
  </update>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CouponConfigDetail" useGeneratedKeys="true">
    insert into coupon_config_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      create_time,
      status,
      update_time,
      <if test="couponConfigId != null">
        coupon_config_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="threshold != null">
        threshold,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="effectiveTime != null">
        effective_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      now(),
      0,
      now(),
      <if test="couponConfigId != null">
        #{couponConfigId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="threshold != null">
        #{threshold,jdbcType=DECIMAL},
      </if>
      <if test="number != null">
        #{number},
      </if>
      <if test="effectiveTime != null">
        #{effectiveTime},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CouponConfigDetail">
    update coupon_config_detail
    <set>
      <if test="couponConfigId != null">
        coupon_config_id = #{couponConfigId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="threshold != null">
        threshold = #{threshold,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CouponConfigDetail">
    update coupon_config_detail
    set coupon_config_id = #{couponConfigId,jdbcType=INTEGER},
      `name` = #{name,jdbcType=INTEGER},
      money = #{money,jdbcType=DECIMAL},
      threshold = #{threshold,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>