<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DirectPurchaseRechargeRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DirectPurchaseRechargeRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="recharge_id" jdbcType="BIGINT" property="rechargeId" />
    <result column="pre_amount" jdbcType="DECIMAL" property="preAmount" />
    <result column="left_amount" jdbcType="DECIMAL" property="leftAmount" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="addtime" jdbcType="TIMESTAMP" property="addtime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, `type`, order_no, recharge_id, pre_amount, left_amount, `status`, addtime, 
    `operator`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from direct_purchase_recharge_record
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByKeys" resultType="net.summerfarm.model.vo.DirectPurchaseRechargeRecordVO">
    SELECT id, m_id mId, `type`, order_no orderNo, recharge_id rechargeId, pre_amount preAmount, left_amount leftAmount, `status`, addtime,
    `operator`, ABS(pre_amount - left_amount) differenceAmount
    FROM direct_purchase_recharge_record
    <where>
      <if test="mId != null">
        AND m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="type != null">
        AND `type` = #{type,jdbcType=TINYINT}
      </if>
      <if test="orderNo != null">
        AND order_no = #{orderNo,jdbcType=VARCHAR}
      </if>
      <if test="rechargeId != null">
        AND recharge_id = #{rechargeId,jdbcType=BIGINT}
      </if>
      <if test="preAmount != null">
        AND pre_amount = #{preAmount,jdbcType=DECIMAL}
      </if>
      <if test="leftAmount != null">
        AND left_amount = #{leftAmount,jdbcType=DECIMAL}
      </if>
      <if test="status != null">
        AND `status` = #{status,jdbcType=TINYINT}
      </if>
      <if test="addtime != null">
        AND addtime = #{addtime,jdbcType=TIMESTAMP}
      </if>
      <if test="operator != null">
        AND `operator` = #{operator,jdbcType=VARCHAR}
      </if>
    </where>
    ORDER BY addtime;
  </select>
    <select id="selectByIds" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List"></include>
      FROM direct_purchase_recharge_record
      where id in
      <foreach collection="list" index="index" item="id" open="(" close=")" separator=",">
      #{id}
      </foreach>
      order by recharge_id ASC
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DirectPurchaseRechargeRecord" useGeneratedKeys="true">
    insert into direct_purchase_recharge_record (m_id, `type`, order_no, 
      recharge_id, pre_amount, left_amount, 
      `status`, addtime, `operator`
      )
    values (#{mId,jdbcType=BIGINT}, #{type,jdbcType=TINYINT}, #{orderNo,jdbcType=VARCHAR}, 
      #{rechargeId,jdbcType=BIGINT}, #{preAmount,jdbcType=DECIMAL}, #{leftAmount,jdbcType=DECIMAL}, 
      #{status,jdbcType=TINYINT}, #{addtime,jdbcType=TIMESTAMP}, #{operator,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DirectPurchaseRechargeRecord" useGeneratedKeys="true">
    insert into direct_purchase_recharge_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="rechargeId != null">
        recharge_id,
      </if>
      <if test="preAmount != null">
        pre_amount,
      </if>
      <if test="leftAmount != null">
        left_amount,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="addtime != null">
        addtime,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="rechargeId != null">
        #{rechargeId,jdbcType=BIGINT},
      </if>
      <if test="preAmount != null">
        #{preAmount,jdbcType=DECIMAL},
      </if>
      <if test="leftAmount != null">
        #{leftAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="addtime != null">
        #{addtime,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.DirectPurchaseRechargeRecord">
    update direct_purchase_recharge_record
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="rechargeId != null">
        recharge_id = #{rechargeId,jdbcType=BIGINT},
      </if>
      <if test="preAmount != null">
        pre_amount = #{preAmount,jdbcType=DECIMAL},
      </if>
      <if test="leftAmount != null">
        left_amount = #{leftAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="addtime != null">
        addtime = #{addtime,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DirectPurchaseRechargeRecord">
    update direct_purchase_recharge_record
    set m_id = #{mId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=TINYINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      recharge_id = #{rechargeId,jdbcType=BIGINT},
      pre_amount = #{preAmount,jdbcType=DECIMAL},
      left_amount = #{leftAmount,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      addtime = #{addtime,jdbcType=TIMESTAMP},
      `operator` = #{operator,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAllMerchant" resultType="net.summerfarm.model.vo.DirectPurchaseMerchantVO">
    SELECT t1.m_id mId, t1.mname, IFNULL(t2.left_total, 0.00) leftAmount, (IFNULL(t3.payTotal,0.00) - IFNULL(t4.payedTotal,0.00)) receivableAmount
    FROM
        (SELECT dpr.m_id m_id, m.mname
        FROM direct_purchase_recharge dpr
        LEFT JOIN merchant m on dpr.m_id = m.m_id
        WHERE dpr.`status` = 1
        UNION
        SELECT o.m_id mId, m.mname
        FROM orders o
        LEFT JOIN merchant m on o.m_id = m.m_id
        WHERE o.status in (3,6) and o.type = 11 and receivable_status != 0) t1
    LEFT JOIN
        (SELECT m_id ,sum(left_amount) left_total
         FROM direct_purchase_recharge_record
         WHERE status = 0 group by m_id) t2 ON  t1.m_id = t2.m_id
    LEFT JOIN
        (SELECT m_id, sum(total_price) payTotal
         FROM orders o WHERE o.status in (3,6) and o.type =11 and receivable_status != 0 GROUP BY o.m_id) t3 on t1.m_id = t3.m_id
    LEFT JOIN
        (SELECT m_id, sum(dprr.pre_amount - dprr.left_amount) payedTotal
         FROM direct_purchase_recharge_record dprr where dprr.type = 0 GROUP BY dprr.m_id) t4 on t1.m_id = t4.m_id
    <where>
        <if test="mId != null">
          t1.m_id = #{mId}
        </if>
        <if test="mname != null">
          AND t1.mname LIKE CONCAT('%',#{mname},'%')
        </if>
        <if test="unReceivable != null ">
          <choose>
            <when test = "unReceivable == 1">
              AND (IFNULL(t3.payTotal,0.00) - IFNULL(t4.payedTotal,0.00)) > 0.00
            </when>
            <otherwise>
              AND (IFNULL(t3.payTotal,0.00) - IFNULL(t4.payedTotal,0.00)) = 0.00
            </otherwise>
          </choose>
        </if>
        <if test="usable != null">
          <choose>
            <when test = "usable == 1">
              AND IFNULL(t2.left_total, 0.00) > 0.00
            </when>
            <otherwise>
              AND IFNULL(t2.left_total, 0.00) = 0.00
            </otherwise>
          </choose>
        </if>
    </where>
  </select>
</mapper>