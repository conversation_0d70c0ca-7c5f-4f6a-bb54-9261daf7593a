<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.TimingOrderRefundWhiteListMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.TimingOrderRefundWhiteList">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="days_deferred"  jdbcType="INTEGER" property="daysDeferred" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="mname" jdbcType="VARCHAR" property="mname" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, days_deferred, order_time, mname, pd_name, sku, `operator`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from timing_order_refund_white_list
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByOrderNo" resultType="java.lang.Boolean">
    select if(count(*) > 0, true, false)
    from timing_order_refund_white_list
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from timing_order_refund_white_list
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.TimingOrderRefundWhiteList" useGeneratedKeys="true">
    insert into timing_order_refund_white_list (order_no,days_deferred, order_time, mname,
      pd_name, sku, `operator`, 
      create_time, update_time)
    values (#{orderNo,jdbcType=VARCHAR},#{daysDeferred,jdbcType=INTEGER}, #{orderTime,jdbcType=TIMESTAMP}, #{mname,jdbcType=VARCHAR},
      #{pdName,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.TimingOrderRefundWhiteList" useGeneratedKeys="true">
    insert into timing_order_refund_white_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="daysDeferred != null">
        days_deferred,
      </if>
      <if test="orderTime != null">
        order_time,
      </if>
      <if test="mname != null">
        mname,
      </if>
      <if test="pdName != null">
        pd_name,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="daysDeferred != null">
        #{daysDeferred,jdbcType=INTEGER},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mname != null">
        #{mname,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.TimingOrderRefundWhiteList">
    update timing_order_refund_white_list
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="daysDeferred != null">
        days_deferred = #{daysDeferred,jdbcType=INTEGER},
      </if>
      <if test="orderTime != null">
        order_time = #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mname != null">
        mname = #{mname,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        pd_name = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.TimingOrderRefundWhiteList">
    update timing_order_refund_white_list
    set order_no = #{orderNo,jdbcType=VARCHAR},
      days_deferred = #{daysDeferred,jdbcType=INTEGER},
      order_time = #{orderTime,jdbcType=TIMESTAMP},
      mname = #{mname,jdbcType=VARCHAR},
      pd_name = #{pdName,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      `operator` = #{operator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="batchDeleteWhiteList" parameterType="java.util.List">
    delete from timing_order_refund_white_list
    where id in
    <foreach collection="ids" close=")" open="(" separator="," item="id">
      #{id}
    </foreach>
  </delete>

  <select id="page" parameterType = "net.summerfarm.model.input.TimingOrderRefundWhiteListPageQuery" resultType="net.summerfarm.model.DTO.TimingOrderRefundWhiteListDTO">
    select  id, order_no orderNo,days_deferred daysDeferred, order_time orderTime, mname, pd_name pdName, sku, `operator`
    from timing_order_refund_white_list
    <where>
      <if test="pdName != null">
        and `pd_name` like CONCAT('%',#{pdName,jdbcType=VARCHAR},'%')
      </if>
      <if test="mname != null">
        and `mname` like CONCAT('%',#{mname,jdbcType=VARCHAR},'%')
      </if>
      <if test="orderNo != null">
        and `order_no` like CONCAT('%',#{orderNo,jdbcType=VARCHAR},'%')
      </if>
      <if test="sku != null">
        and `sku` like CONCAT('%',#{sku,jdbcType=VARCHAR},'%')
      </if>
      <if test="orderStartTime != null">
        and `order_time` >= #{orderStartTime,jdbcType=TIMESTAMP}
      </if>
      <if test="orderEndTime != null">
        and  #{orderEndTime,jdbcType=TIMESTAMP} >= `order_time`
      </if>
    </where>
    order by id DESC
  </select>

  <insert id="batchInsert">
    insert into timing_order_refund_white_list (order_no,days_deferred, order_time, mname,
                                                pd_name, sku, `operator`)
    values
    <foreach collection="records" item="item" index="index" separator=",">
      (#{item.orderNo,jdbcType=VARCHAR}, #{item.daysDeferred,jdbcType=INTEGER}, #{item.orderTime,jdbcType=TIMESTAMP}, #{item.mname,jdbcType=VARCHAR},
      #{item.pdName,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR}, #{item.operator,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="selectByOrderNos" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from timing_order_refund_white_list
    where order_no in
    <foreach collection="orderNos" close=")" open="(" separator="," item="orderNo">
      #{orderNo,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from timing_order_refund_white_list
  </select>
  <select id="selectByIds" resultType="net.summerfarm.model.domain.TimingOrderRefundWhiteList">
    select
    id, order_no orderNo,days_deferred daysDeferred, order_time orderTime, mname, pd_name pdName, sku, `operator`
    from timing_order_refund_white_list
    where id in
    <foreach collection="ids" close=")" open="(" separator="," item="id">
      #{id}
    </foreach>
  </select>

  <update id="batchUpdate">
    update `timing_order_refund_white_list`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="order_no = case " suffix=" end, ">
        <foreach collection="list" item="item">
          <if test="item.orderNo != null">
            when `id` = #{item.id} then #{item.orderNo}
          </if>
        </foreach>
      </trim>
      <trim prefix="days_deferred = case " suffix=" end, ">
        <foreach collection="list" item="item">
          <if test="item.daysDeferred != null">
            when `id` = #{item.id} then #{item.daysDeferred}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_time = case " suffix=" end, ">
        <foreach collection="list" item="item">
          <if test="item.orderTime != null">
            when `id` = #{item.id} then #{item.orderTime}
          </if>
        </foreach>
      </trim>
      <trim prefix="mname = case " suffix=" end, ">
        <foreach collection="list" item="item">
          <if test="item.mname != null">
            when `id` = #{item.id} then #{item.mname}
          </if>
        </foreach>
      </trim>
      <trim prefix="pd_name = case " suffix=" end, ">
        <foreach collection="list" item="item">
          <if test="item.pdName != null">
            when `id` = #{item.id} then #{item.pdName}
          </if>
        </foreach>
      </trim>
      <trim prefix="sku = case " suffix=" end, ">
        <foreach collection="list" item="item">
          <if test="item.sku != null">
            when `id` = #{item.id} then #{item.sku}
          </if>
        </foreach>
      </trim>
      <trim prefix="operator = case " suffix=" end, ">
        <foreach collection="list" item="item">
          <if test="item.operator != null">
            when `id` = #{item.id} then #{item.operator}
          </if>
        </foreach>
      </trim>
    </trim>
    where
    `id` in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item.id}
    </foreach>
  </update>
</mapper>