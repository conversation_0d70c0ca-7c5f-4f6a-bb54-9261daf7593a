<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DiscountCardAvailableMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DiscountCardAvailable">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="discount_card_id" jdbcType="INTEGER" property="discountCardId" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>
    <sql id="Base_Column_List">
    id, discount_card_id, sku, creator, create_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from discount_card_available
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from discount_card_available
    where id = #{id,jdbcType=INTEGER}
  </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DiscountCardAvailable" useGeneratedKeys="true">
    insert into discount_card_available (discount_card_id, sku, creator,
      create_time)
    values (#{discountCardId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertBatch">
        insert into discount_card_available (discount_card_id, sku, creator,create_time)
       values
        <foreach collection="list" item="item" separator=",">
          (#{item.discountCardId,jdbcType=INTEGER}, #{item.sku,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP})
        </foreach>

    </insert>


    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DiscountCardAvailable" useGeneratedKeys="true">
        insert into discount_card_available
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="discountCardId != null">
                discount_card_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="discountCardId != null">
                #{discountCardId,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.DiscountCardAvailable">
        update discount_card_available
        <set>
            <if test="discountCardId != null">
                discount_card_id = #{discountCardId,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DiscountCardAvailable">
    update discount_card_available
    set discount_card_id = #{discountCardId,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <select id="selectByCardId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from discount_card_available
        where discount_card_id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectBySku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from discount_card_available
        where sku = #{sku}
    </select>

    <select id="selectDetailByCardId" resultType="net.summerfarm.model.vo.DiscountCardAvailableVO">
      select dca.sku,i.weight,p.pd_name pdName from discount_card_available dca
      left join inventory i on i.sku = dca.sku
      left join products p on i.pd_id = p.pd_id
      where dca.discount_card_id = #{id,jdbcType=INTEGER}
      order by dca.create_time desc
    </select>
</mapper>