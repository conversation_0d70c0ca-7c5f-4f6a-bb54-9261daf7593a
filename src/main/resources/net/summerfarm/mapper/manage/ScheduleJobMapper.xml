<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.ScheduleJobMapper">

    <insert id="insert" parameterType="net.summerfarm.model.domain.ScheduleJob" useGeneratedKeys="true" keyProperty="id">
        insert into schedule_job (job_name, job_group,job_status,
        cron_expression,job_desc)
        values (#{jobName},#{jobGroup},
        #{jobStatus},#{cronExpression},#{jobDesc})
    </insert>

    <select id="select"  parameterType="net.summerfarm.model.domain.ScheduleJob" >
        SELECT *
        FROM schedule_job
        <where>
            <if test="jobStatus !=null">
                AND job_status=#{jobStatus}
            </if>
            <if test="id !=null">
                AND id=#{id}
            </if>
        </where>
    </select>

    <select id="selectById"  parameterType="net.summerfarm.model.domain.ScheduleJob" >
        SELECT *
        FROM schedule_job
        WHERE id=#{id}
    </select>

    <update id="update" parameterType="net.summerfarm.model.domain.ScheduleJob" >
        update activity
        <set >
            <if test="jobStatus !=null">
                AND job_status=#{jobStatus}
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>