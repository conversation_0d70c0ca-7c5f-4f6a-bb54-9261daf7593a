<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FeedbackWoProcessMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FeedbackWoProcess">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="wo_id" jdbcType="BIGINT" property="woId" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="content_type" jdbcType="TINYINT" property="contentType" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, wo_id, content, content_type, creator_id, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from feedback_wo_process
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByWoId" resultType="net.summerfarm.model.vo.FeedbackWoProcessVO">
    select fwp.id, fwp.wo_id woId, fwp.content_type contentType, fwp.creator_id creatorId, fwp.create_time createTime, fwp.content, a.realname creatorName
    from feedback_wo_process fwp
    left join admin a on a.admin_id = fwp.creator_id
      <where>
        <if test="woId != null">
          fwp.wo_id = #{woId}
        </if>
      </where>
      ORDER BY fwp.create_time DESC
    </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FeedbackWoProcess" useGeneratedKeys="true">
    insert into feedback_wo_process (wo_id, content, content_type, 
      creator_id, create_time)
    values (#{woId,jdbcType=BIGINT}, #{content,jdbcType=VARCHAR}, #{contentType,jdbcType=TINYINT}, 
      #{creatorId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FeedbackWoProcess" useGeneratedKeys="true">
    insert into feedback_wo_process
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="woId != null">
        wo_id,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="contentType != null">
        content_type,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="woId != null">
        #{woId,jdbcType=BIGINT},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        #{contentType,jdbcType=TINYINT},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FeedbackWoProcess">
    update feedback_wo_process
    <set>
      <if test="woId != null">
        wo_id = #{woId,jdbcType=BIGINT},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        content_type = #{contentType,jdbcType=TINYINT},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FeedbackWoProcess">
    update feedback_wo_process
    set wo_id = #{woId,jdbcType=BIGINT},
      content = #{content,jdbcType=VARCHAR},
      content_type = #{contentType,jdbcType=TINYINT},
      creator_id = #{creatorId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>