<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DaySaleRankMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DaySaleRank">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="rank_id" jdbcType="INTEGER" property="rankId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, area_no, sku, rank_id, brand_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from day_sale_rank
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from day_sale_rank
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DaySaleRank" useGeneratedKeys="true">
    insert into day_sale_rank (area_no, sku, rank_id, 
      brand_name)
    values (#{areaNo,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{rankId,jdbcType=INTEGER}, 
      #{brandName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DaySaleRank" useGeneratedKeys="true">
    insert into day_sale_rank
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="rankId != null">
        rank_id,
      </if>
      <if test="brandName != null">
        brand_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="rankId != null">
        #{rankId,jdbcType=INTEGER},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.DaySaleRank">
    update day_sale_rank
    <set>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="rankId != null">
        rank_id = #{rankId,jdbcType=INTEGER},
      </if>
      <if test="brandName != null">
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DaySaleRank">
    update day_sale_rank
    set area_no = #{areaNo,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      rank_id = #{rankId,jdbcType=INTEGER},
      brand_name = #{brandName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <delete id="deleteAll">
    delete from day_sale_rank
  </delete>
  <insert id="insertBatch">
    insert into day_sale_rank (area_no, sku, rank_id, brand_name, sales)
    values
    <foreach collection="daySaleRankList" item="item" separator=",">
      (#{item.areaNo}, #{item.sku}, #{item.rankId}, #{item.brandName}, #{item.sales})
    </foreach>
  </insert>
</mapper>