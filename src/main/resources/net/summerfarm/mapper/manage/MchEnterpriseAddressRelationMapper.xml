<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MchEnterpriseAddressRelationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MchEnterpriseAddressRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="enterprise_information_id" jdbcType="BIGINT" property="enterpriseInformationId" />
    <result column="contact_id" jdbcType="BIGINT" property="contactId" />
    <result column="valid_status" jdbcType="TINYINT" property="validStatus" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, enterprise_information_id, contact_id, valid_status, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mch_enterprise_address_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mch_enterprise_address_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MchEnterpriseAddressRelation" useGeneratedKeys="true">
    insert into mch_enterprise_address_relation (enterprise_information_id, contact_id, 
      valid_status, update_time, create_time
      )
    values (#{enterpriseInformationId,jdbcType=BIGINT}, #{contactId,jdbcType=BIGINT}, 
      #{validStatus,jdbcType=TINYINT}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MchEnterpriseAddressRelation" useGeneratedKeys="true">
    insert into mch_enterprise_address_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="enterpriseInformationId != null">
        enterprise_information_id,
      </if>
      <if test="contactId != null">
        contact_id,
      </if>
      <if test="validStatus != null">
        valid_status,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="enterpriseInformationId != null">
        #{enterpriseInformationId,jdbcType=BIGINT},
      </if>
      <if test="contactId != null">
        #{contactId,jdbcType=BIGINT},
      </if>
      <if test="validStatus != null">
        #{validStatus,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.MchEnterpriseAddressRelation">
    update mch_enterprise_address_relation
    <set>
      <if test="enterpriseInformationId != null">
        enterprise_information_id = #{enterpriseInformationId,jdbcType=BIGINT},
      </if>
      <if test="contactId != null">
        contact_id = #{contactId,jdbcType=BIGINT},
      </if>
      <if test="validStatus != null">
        valid_status = #{validStatus,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.MchEnterpriseAddressRelation">
    update mch_enterprise_address_relation
    set enterprise_information_id = #{enterpriseInformationId,jdbcType=BIGINT},
      contact_id = #{contactId,jdbcType=BIGINT},
      valid_status = #{validStatus,jdbcType=TINYINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mch_enterprise_address_relation
    where contact_id = #{contactId} and valid_status = 0
  </select>

  <update id="updateByKey" parameterType="java.lang.Long">
    update mch_enterprise_address_relation
    set  valid_status = 1
    where enterprise_information_id = #{id,jdbcType=BIGINT}  and valid_status = 0
  </update>
</mapper>