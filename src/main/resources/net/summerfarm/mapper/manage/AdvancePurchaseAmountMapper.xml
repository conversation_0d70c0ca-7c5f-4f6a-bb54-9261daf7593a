<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.AdvancePurchaseAmountMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.AdvancePurchaseAmount">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="supplier_id" property="supplierId" jdbcType="INTEGER"/>
        <result column="use_amount" property="useAmount" />
        <result column="total_amount" property="totalAmount" />
        <result column="add_time" property="addTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base">
        id,supplier_id,use_amount,total_amount,add_time,update_time
    </sql>

    <insert id="insertAdvancePurchaseAmount" parameterType="net.summerfarm.model.domain.AdvancePurchaseAmount">
        insert into advance_purchase_amount (add_time,update_time,supplier_id,total_amount,use_amount)
        value (now(),now(),#{supplierId},#{totalAmount},#{useAmount})
    </insert>

    <update id="addAdvancePurchaseAmount" parameterType="net.summerfarm.model.domain.AdvancePurchaseAmount">
       update advance_purchase_amount
       <set>
           update_time = now()
           <if test="totalAmount != null">
               ,total_amount = total_amount + #{totalAmount}
           </if>
           <if test="useAmount != null">
               , use_amount = use_amount + #{useAmount}
           </if>
       </set>
       where id = #{id}
    </update>

    <update id="subAdvancePurchaseAmount" parameterType="net.summerfarm.model.domain.AdvancePurchaseAmount">
       update advance_purchase_amount
       set use_amount = use_amount - #{useAmount},update_time = now()
       where id = #{id}
    </update>

    <select id="selectAmount" resultMap="BaseResultMap">
        select <include refid="Base"/>
        from advance_purchase_amount
        where supplier_id = #{supplierId}
    </select>

    <select id="selectUseAmount" resultType="net.summerfarm.model.vo.AdvancePurchaseAmountVO">
        select
        apa.supplier_id supplierId,
        apa.use_amount useAmount,
        s.name supplierName
        from advance_purchase_amount apa
        left join supplier s on s.id = apa.supplier_id
        where supplier_id = #{supplierId}

    </select>



</mapper>