<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockAllocationCategoryConfigMapper">

    <select id="select" resultType="net.summerfarm.model.vo.StockAllocationConfigVO"
            parameterType="net.summerfarm.model.input.StockAllocationConfigInput">
        select  cc.id, ppv.products_property_value brand,p.pd_name pdName , p.pd_no pdNo ,cc.admin_name adminName, p.pd_id pdId, cc.admin_id adminId
        from stock_allocation_category_config cc
        LEFT JOIN products p on p.pd_no = cc.pd_no
        LEFT JOIN products_property_value ppv on ppv.pd_id = p.pd_id and ppv.products_property_id = 2
        <where>
            <if test="brand != null">
                AND ppv.products_property_value like concat('%',#{brand},'%')
            </if>
            <if test="pdName != null">
                AND p.pd_name like concat('%',#{pdName},'%')
            </if>
            <if test="pdNo != null">
                AND p.pd_no like concat('%',#{pdNo},'%')
            </if>
            <if test="adminName != null">
                AND cc.admin_name like concat('%',#{adminName},'%')
            </if>
            <if test="adminId != null">
                AND cc.admin_id = #{adminId}
            </if>
        </where>
        ORDER BY cc.update_time DESC
    </select>

    <update id="update" parameterType="net.summerfarm.model.input.StockAllocationConfigInput" >
        update stock_allocation_category_config
        <set >
            <if test=" adminName!= null">
                admin_name= #{adminName},
            </if>
            <if test=" adminId!= null">
                admin_id= #{adminId},
            </if>
            <if test=" pdNo!= null">
                pd_no= #{pdNo},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from stock_allocation_category_config
    where id = #{id,jdbcType=INTEGER}
  </delete>

    <select id="selectByPdId" resultType="net.summerfarm.model.domain.StockAllocationCategoryConfig">
        select id,admin_name adminName,creator
        from stock_allocation_category_config
        where pd_no =#{pdNo}
        limit 1
    </select>

    <insert id="insert" parameterType="net.summerfarm.model.domain.StockAllocationCategoryConfig">
        insert into stock_allocation_category_config (pd_no,admin_id,admin_name,creator,create_time,update_time)
         values (#{pdNo},#{adminId},#{adminName},#{creator},now(),now())
    </insert>

    <select id="selectByAdminId"  parameterType="integer" resultType="java.lang.String">
        select pd_no
        from stock_allocation_category_config
        where admin_id= #{adminId}
    </select>
    <select id="selectList" resultType="net.summerfarm.model.DTO.purchase.StockAllocationCategoryConfigDTO">
        select
               cc.id, cc.pd_no pdNo, p.pd_id pdId, cc.admin_id adminId, cc.create_time createTime,cc.updater,
        cc.update_time updateTime, cc.admin_name adminName, cc.creator
            from
                   stock_allocation_category_config cc left join products p on cc.pd_no = p.pd_no
    limit #{startNum},#{pageSize}
    </select>
</mapper>
