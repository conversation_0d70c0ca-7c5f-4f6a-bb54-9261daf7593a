<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DynamicPriceRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DynamicPriceRecord">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="sku_task_id" jdbcType="BIGINT" property="skuTaskId"/>
    <result column="sku" jdbcType="VARCHAR" property="sku"/>
    <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
    <result column="old_sale_price" jdbcType="DECIMAL" property="oldSalePrice"/>
    <result column="old_gross_profit_rate" jdbcType="DECIMAL" property="oldGrossProfitRate"/>
    <result column="new_sale_price" jdbcType="DECIMAL" property="newSalePrice"/>
    <result column="new_gross_profit_rate" jdbcType="DECIMAL" property="newGrossProfitRate"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="reason" jdbcType="VARCHAR" property="reason"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `sku_task_id`, `sku`, `area_no`, `old_sale_price`, `old_gross_profit_rate`,
    `new_sale_price`, `new_gross_profit_rate`, `status`, `reason`, `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_record
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from dynamic_price_record
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.DynamicPriceRecord">
    insert into dynamic_price_record (`id`, `sku_task_id`, `sku`,
                                      `area_no`, `old_sale_price`, `old_gross_profit_rate`,
                                      `new_sale_price`, `new_gross_profit_rate`, `status`,
                                      `reason`, `create_time`, `update_time`)
    values (#{id,jdbcType=BIGINT}, #{skuTaskId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR},
            #{areaNo,jdbcType=INTEGER}, #{oldSalePrice,jdbcType=DECIMAL},
            #{oldGrossProfitRate,jdbcType=DECIMAL},
            #{newSalePrice,jdbcType=DECIMAL}, #{newGrossProfitRate,jdbcType=DECIMAL},
            #{status,jdbcType=TINYINT},
            #{reason,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.DynamicPriceRecord">
    insert into dynamic_price_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="skuTaskId != null">
        `sku_task_id`,
      </if>
      <if test="sku != null">
        `sku`,
      </if>
      <if test="areaNo != null">
        `area_no`,
      </if>
      <if test="oldSalePrice != null">
        `old_sale_price`,
      </if>
      <if test="oldGrossProfitRate != null">
        `old_gross_profit_rate`,
      </if>
      <if test="newSalePrice != null">
        `new_sale_price`,
      </if>
      <if test="newGrossProfitRate != null">
        `new_gross_profit_rate`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="reason != null">
        `reason`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="skuTaskId != null">
        #{skuTaskId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="oldSalePrice != null">
        #{oldSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="oldGrossProfitRate != null">
        #{oldGrossProfitRate,jdbcType=DECIMAL},
      </if>
      <if test="newSalePrice != null">
        #{newSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="newGrossProfitRate != null">
        #{newGrossProfitRate,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.model.domain.DynamicPriceRecord">
    update dynamic_price_record
    <set>
      <if test="skuTaskId != null">
        `sku_task_id` = #{skuTaskId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        `sku` = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        `area_no` = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="oldSalePrice != null">
        `old_sale_price` = #{oldSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="oldGrossProfitRate != null">
        `old_gross_profit_rate` = #{oldGrossProfitRate,jdbcType=DECIMAL},
      </if>
      <if test="newSalePrice != null">
        `new_sale_price` = #{newSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="newGrossProfitRate != null">
        `new_gross_profit_rate` = #{newGrossProfitRate,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="reason != null">
        `reason` = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DynamicPriceRecord">
    update dynamic_price_record
    set `sku_task_id`           = #{skuTaskId,jdbcType=BIGINT},
        `sku`                   = #{sku,jdbcType=VARCHAR},
        `area_no`               = #{areaNo,jdbcType=INTEGER},
        `old_sale_price`        = #{oldSalePrice,jdbcType=DECIMAL},
        `old_gross_profit_rate` = #{oldGrossProfitRate,jdbcType=DECIMAL},
        `new_sale_price`        = #{newSalePrice,jdbcType=DECIMAL},
        `new_gross_profit_rate` = #{newGrossProfitRate,jdbcType=DECIMAL},
        `status`                = #{status,jdbcType=TINYINT},
        `reason`                = #{reason,jdbcType=VARCHAR},
        `create_time`           = #{createTime,jdbcType=TIMESTAMP},
        `update_time`           = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="listBySkuTaskId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_record
    where sku_task_id = #{skuTaskId}
  </select>

</mapper>