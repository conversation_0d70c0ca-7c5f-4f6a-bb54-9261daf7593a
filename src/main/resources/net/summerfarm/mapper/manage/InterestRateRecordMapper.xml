<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.InterestRateRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.InterestRateRecord">
    <result column="id" property="id" jdbcType="INTEGER"/>
    <result column="sku" property="sku" jdbcType="VARCHAR"/>
    <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
    <result column="interest_rate_old" property="interestRateOld" jdbcType="DECIMAL"/>
    <result column="interest_rate_new" property="interestRateNew" jdbcType="DECIMAL"/>
    <result column="auto_flag_old" property="autoFlagOld" jdbcType="INTEGER"/>
    <result column="auto_flag_new" property="autoFlagNew" jdbcType="INTEGER"/>
    <result column="status" property="status" jdbcType="INTEGER"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    <result column="create_admin_name" property="createAdminName" jdbcType="VARCHAR"/>
  </resultMap>
  <insert id="insert" parameterType="net.summerfarm.model.domain.InterestRateRecord">
    insert into interest_rate_record (id, sku, area_no, 
      interest_rate_old, interest_rate_new, auto_flag_old, 
      auto_flag_new, status, create_time, 
      create_admin_name)
    values (#{id,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{areaNo,jdbcType=INTEGER}, 
      #{interestRateOld,jdbcType=DECIMAL}, #{interestRateNew,jdbcType=DECIMAL}, #{autoFlagOld,jdbcType=INTEGER}, 
      #{autoFlagNew,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createAdminName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.InterestRateRecord">
    insert into interest_rate_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="interestRateOld != null">
        interest_rate_old,
      </if>
      <if test="interestRateNew != null">
        interest_rate_new,
      </if>
      <if test="autoFlagOld != null">
        auto_flag_old,
      </if>
      <if test="autoFlagNew != null">
        auto_flag_new,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createAdminName != null">
        create_admin_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="interestRateOld != null">
        #{interestRateOld,jdbcType=DECIMAL},
      </if>
      <if test="interestRateNew != null">
        #{interestRateNew,jdbcType=DECIMAL},
      </if>
      <if test="autoFlagOld != null">
        #{autoFlagOld,jdbcType=INTEGER},
      </if>
      <if test="autoFlagNew != null">
        #{autoFlagNew,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createAdminName != null">
        #{createAdminName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <!-- 自定义SQL -->
  <insert id="insertBatch" parameterType="list" useGeneratedKeys="true" keyProperty="id">
    insert into interest_rate_record (id, sku, area_no,
    interest_rate_old, interest_rate_new, auto_flag_old,
    auto_flag_new, create_time,
    create_admin_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=INTEGER}, #{item.sku,jdbcType=VARCHAR}, #{item.areaNo,jdbcType=INTEGER},
      #{item.interestRateOld,jdbcType=DECIMAL}, #{item.interestRateNew,jdbcType=DECIMAL},
      #{item.autoFlagOld,jdbcType=INTEGER},
      #{item.autoFlagNew,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.createAdminName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="select" resultType="net.summerfarm.model.vo.InterestRateRecordVO">
    select itr.id id,
    la.large_area_no storeNo ,
    la.large_area_name storeName,
    a.area_no areaNo,
    a.area_name areaName,
    itr.sku sku,
    p.pd_name pdName,
    i.weight weight,
    itr.interest_rate_old interestRateOld,
    itr.interest_rate_new interestRateNew,
    itr.auto_flag_old autoFlagOld,
    itr.auto_flag_new autoFlagNew,
    itr.status status,
    itr.create_time createTime,
    itr.create_admin_name createAdminName,
    i.ext_type extType
    from interest_rate_record itr
    left join area a on itr.area_no = a.area_no
    left join inventory i on itr.sku = i.sku
    left join products p on i.pd_id = p.pd_id
    left join large_area la on la.large_area_no = a.large_area_no
    <where>
      <if test="largeAreaNo != null">
        and a.large_area_no = #{largeAreaNo}
      </if>
      <if test="areaNo != null">
        and a.area_no = #{areaNo}
      </if>
      <if test="status != null">
        and itr.status = #{status}
      </if>
      <if test="pdName != null and pdName != ''">
        and p.pd_name like concat('%', #{pdName}, '%')
      </if>
      <if test="sku != null">
        and itr.sku = #{sku}
      </if>
    </where>
    order by itr.id desc
  </select>
  <update id="updateById" parameterType="net.summerfarm.model.domain.InterestRateRecord">
    update interest_rate_record
    set status = #{status},
        interest_rate_new = #{interestRateNew},
        auto_flag_new = #{autoFlagNew}
    where id = #{id}
  </update>
  <select id="selectUnAudit" resultType="net.summerfarm.model.vo.InterestRateRecordVO">
    select irr.id                id,
       irr.interest_rate_new interestRateNew,
       irr.interest_rate_old interestRateOld,
       irr.auto_flag_new     autoFlagNew,
       irr.auto_flag_old     autoFlagOld,
       irr.area_no           areaNo,
       ak.show               `show`,
       ak.on_sale            onSale
    from interest_rate_record irr
             left join area a on a.area_no = irr.area_no
             left join area_sku ak on irr.area_no = ak.area_no and irr.sku = ak.sku
    where irr.status = 0 and a.large_area_no = #{largeAreaNo} and irr.sku = #{sku}
  </select>
  <select id="isUnAudit" resultType="boolean">
    select count(0) > 0 from interest_rate_record where sku = #{sku} and area_no = #{areaNo} and status = 0
  </select>
</mapper>