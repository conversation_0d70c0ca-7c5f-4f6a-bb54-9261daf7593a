<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.PurchasesImprestMapper">



    <update id="updateImprest" parameterType="net.summerfarm.model.domain.PurchasesImprestRecord">
        update purchases_imprest
        <set>
        <if test="updateTime != null">
            update_time = #{updateTime},
        </if>
        <if test="amount != null">
            amount = #{amount},
        </if>
        </set>
        where id =#{id}

    </update>
    <select id="queryImprest" resultType="net.summerfarm.model.domain.PurchasesImprest">
        select id,amount from purchases_imprest
        where status = 0
    </select>




</mapper>