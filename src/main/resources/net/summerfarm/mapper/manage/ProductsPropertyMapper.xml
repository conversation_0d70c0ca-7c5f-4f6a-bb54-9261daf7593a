<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ProductsPropertyMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.ProductsProperty">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="format_type" jdbcType="INTEGER" property="formatType" />
    <result column="format_str" jdbcType="VARCHAR" property="formatStr" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="mapping_id" jdbcType="INTEGER" property="mappingId"></result>
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, `type`, format_type, format_str, `status`, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from products_property
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from products_property
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.ProductsProperty" useGeneratedKeys="true">
    insert into products_property (`name`, `type`, format_type, 
      format_str, `status`, creator, 
      create_time)
    values (#{name,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{formatType,jdbcType=INTEGER}, 
      #{formatStr,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.ProductsProperty" useGeneratedKeys="true">
    insert into products_property
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="formatType != null">
        format_type,
      </if>
      <if test="formatStr != null">
        format_str,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="formatType != null">
        #{formatType,jdbcType=INTEGER},
      </if>
      <if test="formatStr != null">
        #{formatStr,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.ProductsProperty">
    update products_property
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="formatType != null">
        format_type = #{formatType,jdbcType=INTEGER},
      </if>
      <if test="formatStr != null">
        format_str = #{formatStr,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.ProductsProperty">
    update products_property
    set `name` = #{name,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      format_type = #{formatType,jdbcType=INTEGER},
      format_str = #{formatStr,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByType" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from products_property
    <where>
       status = 1
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
    </where>
  </select>
  <select id="selectProductsProperty" parameterType="net.summerfarm.model.domain.ProductsProperty"
          resultType="net.summerfarm.model.domain.ProductsProperty">
    select
    <include refid="Base_Column_List" />
    from products_property
    where status = 1 and `name` = #{name} and `type` = #{type}
  </select>

  <select id="selectByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from products_property
    where status = 1 and `name` = #{name}  limit 1
  </select>
</mapper>