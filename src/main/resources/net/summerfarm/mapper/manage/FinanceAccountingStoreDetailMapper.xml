<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceAccountingStoreDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceAccountingStoreDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="finance_accounting_store_id" jdbcType="BIGINT" property="financeAccountingStoreId" />
    <result column="after_sale_amount" jdbcType="DECIMAL" property="afterSaleAmount" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="total_amount_receivable" jdbcType="DECIMAL" property="totalAmountReceivable" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="out_times_fee" jdbcType="DECIMAL" property="outTimesFee" />
    <result column="bill_number" jdbcType="DECIMAL" property="billNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    id, finance_accounting_store_id, after_sale_amount, delivery_fee, total_price, total_amount_receivable,
    order_no, create_time, creator,bill_number
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_accounting_store_detail
    where finance_accounting_store_id = #{id}
  </select>

    <select id="selectBillByOrderNo" resultType="java.lang.String">
      select fasd.bill_number
      from finance_accounting_store_detail fasd
      where fasd.order_no = #{orderNo}
    </select>

    <select id="selectOrderNoByBillNumber" resultType="java.lang.String">
      select distinct (order_no) orderNo
      from finance_accounting_store_detail
      where bill_number = #{billNumber} and total_price > 0;
    </select>

  <select id="selectOrderNo" resultType="java.lang.String">
    select distinct order_no orderNo
    from finance_accounting_store_detail
    where bill_number = #{billNumber}
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_accounting_store_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAccountingStoreDetail" useGeneratedKeys="true">
    insert into finance_accounting_store_detail (finance_accounting_store_id, after_sale_amount,
      delivery_fee, total_price, total_amount_receivable,
      order_no, create_time, creator
      )
    values (#{financeAccountingStoreId,jdbcType=BIGINT}, #{afterSaleAmount,jdbcType=DECIMAL},
      #{deliveryFee,jdbcType=DECIMAL}, #{totalPrice,jdbcType=DECIMAL}, #{totalAmountReceivable,jdbcType=DECIMAL},
      #{orderNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAccountingStoreDetail" useGeneratedKeys="true">
    insert into finance_accounting_store_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="financeAccountingStoreId != null">
        finance_accounting_store_id,
      </if>
      <if test="afterSaleAmount != null">
        after_sale_amount,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="totalAmountReceivable != null">
        total_amount_receivable,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="billNumber != null">
        bill_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="financeAccountingStoreId != null">
        #{financeAccountingStoreId,jdbcType=BIGINT},
      </if>
      <if test="afterSaleAmount != null">
        #{afterSaleAmount,jdbcType=DECIMAL},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalAmountReceivable != null">
        #{totalAmountReceivable,jdbcType=DECIMAL},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
        <if test="billNumber != null">
        #{billNumber},
      </if>
    </trim>
  </insert>
    <insert id="batchInsert">
      insert into finance_accounting_store_detail
      (finance_accounting_store_id, after_sale_amount, delivery_fee,total_price, total_amount_receivable,order_no,
      create_time, creator, out_times_fee, bill_number)
      values
      <foreach collection="list" item="item" index="index" separator=",">
        (#{storeId}, #{item.afterSaleAmount},
        #{item.deliveryFee},#{item.totalPrice},#{item.totalAmountReceivable},#{item.orderNo},now(),#{item.creator},#{item.outTimesFee},#{billNumber})
      </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceAccountingStoreDetail">
    update finance_accounting_store_detail
    <set>
      <if test="financeAccountingStoreId != null">
        finance_accounting_store_id = #{financeAccountingStoreId,jdbcType=BIGINT},
      </if>
      <if test="afterSaleAmount != null">
        after_sale_amount = #{afterSaleAmount,jdbcType=DECIMAL},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalAmountReceivable != null">
        total_amount_receivable = #{totalAmountReceivable,jdbcType=DECIMAL},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey">
    update finance_accounting_store_detail
    set bill_number = #{billNumber}
    where bill_number = #{number}
  </update>

  <select id="selectOrderNum" parameterType="long" resultType="integer">
    select count(*)
    from finance_accounting_store_detail
    where finance_accounting_store_id = #{id}
  </select>
  <select id="selectOrderByBillNoOrderByOrderTime" resultType="java.lang.String">
    select fasd.order_no orderNo
    from finance_accounting_store_detail fasd
           left join orders os on fasd.order_no=os.order_no
    where bill_number = #{billNumber} and fasd.total_price > 0
    order by os.order_time
  </select>
  <select id="selectBillByOrderNoList" resultType="net.summerfarm.model.domain.FinanceAccountingStoreDetail">
    select fasd.bill_number billNumber,fasd.order_no orderNo
      from finance_accounting_store_detail fasd
      where fasd.order_no in
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>

</mapper>
