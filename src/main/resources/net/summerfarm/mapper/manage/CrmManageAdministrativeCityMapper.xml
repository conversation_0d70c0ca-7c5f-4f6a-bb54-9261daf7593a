<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CrmManageAdministrativeCityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CrmManageAdministrativeCity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mb_id" jdbcType="INTEGER" property="mbId" />
    <result column="administrative_city" jdbcType="VARCHAR" property="administrativeCity" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, mb_id, administrative_city, creator, delete_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_manage_administrative_city
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CrmManageAdministrativeCity" useGeneratedKeys="true">
    insert into crm_manage_administrative_city
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mbId != null">
        mb_id,
      </if>
      <if test="administrativeCity != null">
        administrative_city,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mbId != null">
        #{mbId,jdbcType=INTEGER},
      </if>
      <if test="administrativeCity != null">
        #{administrativeCity,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CrmManageAdministrativeCity">
    update crm_manage_administrative_city
    <set>
      <if test="mbId != null">
        mb_id = #{mbId,jdbcType=INTEGER},
      </if>
      <if test="administrativeCity != null">
        administrative_city = #{administrativeCity,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByZoneId" resultType="string">
    SELECT administrative_city FROM crm_manage_administrative_city
      WHERE delete_flag = 0
    <if test="id != null">
      AND mb_id = #{id}
    </if>
  </select>
  <select id="selectCity" resultType="string">
    SELECT administrative_city FROM crm_manage_administrative_city
    WHERE delete_flag = 0
    <if test="id != null">
      AND mb_id != #{id}
    </if>
  </select>
  <insert id="insertCity">
    insert into crm_manage_administrative_city (mb_id,administrative_city,creator,create_time)
    values
    <foreach collection="city" item="item" separator=",">
      (#{mbId},#{item},#{adminId},sysdate())
    </foreach>
  </insert>
  <update id="deleteCity">
    update crm_manage_administrative_city set delete_flag = 1 where mb_id = #{mbId}
  </update>
</mapper>