<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.AdminDataPermissionMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.AdminDataPermission">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="permission_value" property="permissionValue" jdbcType="VARCHAR"/>
        <result column="permission_name" property="permissionName" jdbcType="VARCHAR"/>
        <result column="addtime" property="addtime" jdbcType="TIMESTAMP"/>
        <result column="type" property="type"/>
        <result column="warehouse_no" property="warehouseNo"/>
    </resultMap>

    <insert id="insert" parameterType="net.summerfarm.model.domain.AdminDataPermission">
        insert into admin_data_permission (admin_id, permission_value, permission_name, addtime, type)
        values (#{adminId}, #{permissionValue}, #{permissionName}, now(), #{type});
    </insert>

    <select id="selectDistinct" resultType="java.lang.Integer">
        select DISTINCT a.admin_id
        from admin_data_permission a
        join admin b on a.admin_id = b.admin_id
        where a.permission_value in
        <foreach collection="collection" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByAdminId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select id, admin_id, permission_name, permission_value, addtime
        FROM admin_data_permission
        WHERE admin_id = #{adminId}
    </select>

    <select id="selectByWarehouseNo" resultType="net.summerfarm.model.domain.AdminDataPermission"
            parameterType="java.lang.Integer">
        select adp.admin_id adminId
        FROM admin_data_permission adp
        LEFT JOIN `admin` ad on ad.`admin_id` = adp.`admin_id`
        WHERE (adp.permission_value = #{warehouseNo} or adp.permission_value = 0)
        and ad.realname is not null
        <if test="adminIds != null and adminIds.size() > 0">
            and adp.admin_id in
            <foreach collection="adminIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            union all
            select admin_id adminId from admin_role where role_id = 1 and admin_id in
            <foreach collection="adminIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="adminIds == null or adminIds.size() == 0">
            union all
            select admin_id adminId from admin_role where role_id = 1
        </if>
    </select>

    <delete id="deleteByAdminId" parameterType="java.lang.Integer">
        delete
        from admin_data_permission
        where admin_id = #{adminId}
    </delete>
    <select id="hasAllDataPermission" resultType="boolean" parameterType="java.lang.Integer">
        select count(1) > 0
        FROM admin_data_permission
        WHERE permission_value = 0
          and admin_id = #{adminId}
    </select>
</mapper>