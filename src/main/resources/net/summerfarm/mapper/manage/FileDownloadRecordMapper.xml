<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.FileDownloadRecordMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FileDownloadRecord">
        <id column="id" property="id" />
        <result column="status" property="status" />
        <result column="file_name" property="fileName" />
        <result column="admin_id" property="adminId" />
        <result column="params" property="params" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="u_id" property="uId" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="creator" property="creator" />
        <result column="name_detail" property="nameDetail" />
        <result column="expiration_time" property="expirationTime" />
        <result column="file_name_address" property="fileNameAddress" />
        <result column="name_detail_address" property="nameDetailAddress" />
        <result column="oss_type" property="ossType" />
        <result column="biz_status" property="bizStatus" />
        <result column="tenant_id" property="tenantId" />
        <result column="source" property="source" />
        <result column="biz_result" property="bizResult" />
        <result column="source_file_name" property="sourceFileName" />
    </resultMap>

    <sql id="base_column_filed">
        id,status,file_name,admin_id,params,type,create_time,update_time,u_id,start_time,
        end_time,creator,name_detail,expiration_time,file_name_address,name_detail_address,
        oss_type,biz_status,tenant_id,source,biz_result,source_file_name
    </sql>

    <select id="selectByAdminId" resultMap="BaseResultMap">
        select
        <include refid="base_column_filed"/>
        from file_download_record
        where admin_id = #{adminId}
        and source=#{source}
        and tenant_id=#{tenantId}
        order by id desc
    </select>

    <select id="select" resultMap="BaseResultMap">
        select <include refid="base_column_filed"/>
        from file_download_record
        where status <![CDATA[<>]]> 3 and expiration_time is not null
        order by id desc
    </select>


    <select id="selectByType" resultMap="BaseResultMap">
        select <include refid="base_column_filed"/>
        from file_download_record
        where `type` = #{type}
        and source=#{source}
        and tenant_id=#{tenantId}
        <if test="startTime != null">
            and end_time <![CDATA[>=]]> #{startTime} and start_time <![CDATA[<=]]> #{startTime}
        </if>
        order by id desc
    </select>

    <insert id="insert" parameterType="net.summerfarm.model.domain.FileDownloadRecord" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into file_download_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null">
                file_name,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="uId != null">
                u_id,
            </if>
            <if test="params != null">
                params,
            </if>
            <if test="nameDetail != null">
                name_detail,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
                expiration_time,
            <if test="fileNameAddress != null">
                file_name_address,
            </if>
            <if test="nameDetailAddress != null">
                name_detail_address,
            </if>
            <if test="ossType != null">
                oss_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null">
                #{fileName},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="uId != null">
                #{uId},
            </if>
            <if test="params != null">
                #{params},
            </if>
            <if test="nameDetail != null">
                #{nameDetail},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
                #{expirationTime},
            <if test="fileNameAddress != null">
                #{fileNameAddress},
            </if>
            <if test="nameDetailAddress != null">
                #{nameDetailAddress},
            </if>
            <if test="ossType != null">
                #{ossType},
            </if>
        </trim>
    </insert>

    <update id="updateById">
        update file_download_record
        set status = #{status},file_name_address = #{fileNameAddress}
        where id = #{id}
    </update>

    <update id="update">
        update file_download_record
        set status = #{status}
        where u_id = #{uId}
    </update>

    <update id="updateFileName" parameterType="net.summerfarm.model.domain.FileDownloadRecord">
        update file_download_record
        set status = #{status},
            update_time = now()
        <if test="fileNameAddress!= null and fileNameAddress !=''">
            ,file_name_address=#{fileNameAddress}
        </if>
        where file_name = #{fileName} and status = 0
    </update>

    <update id="expireDownloadRecords" parameterType="long">
        update file_download_record
        set status = 3,
            update_time = now()
        where id = #{id} and oss_type = 1
    </update>
    <update id="updateFileNameByUid">
        update file_download_record
        set file_name = #{fileName},
        update_time = now()
        where u_id = #{uId}
    </update>
    <update id="updateNameDetail" parameterType="net.summerfarm.model.domain.FileDownloadRecord">
        update file_download_record
        set status = #{status},
            update_time = now()
        where file_name = #{nameDetail} and status = 0
    </update>
    <update id="updateExpiredData" parameterType="net.summerfarm.model.domain.FileDownloadRecord">
        update file_download_record
        set status = 3,
            update_time = now()
        where id = #{id}
    </update>
    <select id="selectByUid" resultMap="BaseResultMap">
        SELECT <include refid="base_column_filed"/> FROM file_download_record WHERE u_id = #{uId} LIMIT 1
    </select>

    <select id="selectPendingTasks" resultMap="BaseResultMap">
        SELECT <include refid="base_column_filed"/>
        FROM file_download_record
        WHERE status = 0
        and u_id is not null
        <if test="type !=null">and type = #{type}</if>
        order by id limit 100
    </select>

    <delete id="delete" parameterType="long">
        delete from file_download_record
        where id = #{id}
    </delete>

    <delete id="deleteAll" parameterType="long">
        delete from file_download_record
        where admin_id = #{adminId}
    </delete>
    <select id="selectById" parameterType="long"  resultType="net.summerfarm.model.domain.FileDownloadRecord">
        select id,status,`type`,file_name fileName,admin_id adminId,params,create_time createTime,update_time updateTime,start_time startTime,end_time endTime,name_detail nameDetail,
               expiration_time expirationTime,oss_type ossType,file_name_address fileNameAddress, name_detail_address nameDetailAddress
        from file_download_record
        where id = #{id}
    </select>
    <select id="selectByIdCollection"  resultMap="BaseResultMap">
        SELECT <include refid="base_column_filed"/>
        FROM file_download_record
        WHERE id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        <if test="type !=null">and type = #{type}</if>
        order by id
    </select>
</mapper>
