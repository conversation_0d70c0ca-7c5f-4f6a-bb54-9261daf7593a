<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.LuckyDrawActivityEquityPackageMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.LuckyDrawActivityEquityPackage">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="probability" jdbcType="DECIMAL" property="probability" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="surplus_quantity" jdbcType="INTEGER" property="surplusQuantity" />
    <result column="send_quantity" jdbcType="INTEGER" property="sendQuantity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, activity_id, name, probability, quantity, surplus_quantity, send_quantity, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lucky_draw_activity_equity_package
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lucky_draw_activity_equity_package
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.LuckyDrawActivityEquityPackage">
    insert into lucky_draw_activity_equity_package (id, activity_id, name, 
      probability, quantity, surplus_quantity, 
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{activityId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{probability,jdbcType=DECIMAL}, #{quantity,jdbcType=INTEGER}, #{surplusQuantity,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.LuckyDrawActivityEquityPackage">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lucky_draw_activity_equity_package
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="probability != null">
        probability,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="surplusQuantity != null">
        surplus_quantity,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="probability != null">
        #{probability,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="surplusQuantity != null">
        #{surplusQuantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.LuckyDrawActivityEquityPackage">
    update lucky_draw_activity_equity_package
    <set>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="probability != null">
        probability = #{probability,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
        surplus_quantity = quantity - send_quantity,
      </if>
      <if test="surplusQuantity != null">
        surplus_quantity = #{surplusQuantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.LuckyDrawActivityEquityPackage">
    update lucky_draw_activity_equity_package
    set activity_id = #{activityId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      probability = #{probability,jdbcType=DECIMAL},
      quantity = #{quantity,jdbcType=INTEGER},
      surplus_quantity = #{surplusQuantity,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByActivityId" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from lucky_draw_activity_equity_package
    where activity_id = #{activityId,jdbcType=BIGINT}
    </select>

  <delete id="batchDeleteById" parameterType="java.util.List">
    delete from lucky_draw_activity_equity_package
    where id in
    <foreach collection="list" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </delete>

  <select id="selectCountByEntity" resultType="int">
    select
    count(0)
    from lucky_draw_activity_equity_package
    where activity_id = #{activityId,jdbcType=BIGINT}
    and name in
    <foreach collection="list" item="name" open="(" close=")" separator=",">
      #{name}
    </foreach>
  </select>
</mapper>