<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DatabaseHandleRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DatabaseHandleRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="handle_desc" jdbcType="VARCHAR" property="handleDesc" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `status`, handle_desc, creator, create_time, updater, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from database_handle_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from database_handle_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DatabaseHandleRecord" useGeneratedKeys="true">
    insert into database_handle_record (`status`, handle_desc, creator, 
      create_time, updater, update_time
      )
    values (#{status,jdbcType=INTEGER}, #{handleDesc,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DatabaseHandleRecord" useGeneratedKeys="true">
    insert into database_handle_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="status != null">
        `status`,
      </if>
      <if test="handleDesc != null">
        handle_desc,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="handleDesc != null">
        #{handleDesc,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.DatabaseHandleRecord">
    update database_handle_record
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="handleDesc != null">
        handle_desc = #{handleDesc,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DatabaseHandleRecord">
    update database_handle_record
    set `status` = #{status,jdbcType=INTEGER},
      handle_desc = #{handleDesc,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="executeSql" parameterType="string">
    ${sql}
  </update>
</mapper>