<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.EnterpriseInformationMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.EnterpriseInformation">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="reg_number" jdbcType="VARCHAR" property="regNumber"/>
        <result column="reg_status" jdbcType="VARCHAR" property="regStatus"/>
        <result column="credit_code" jdbcType="VARCHAR" property="creditCode"/>
        <result column="estiblish_time" jdbcType="VARCHAR" property="estiblishTime"/>
        <result column="reg_capital" jdbcType="VARCHAR" property="regCapital"/>
        <result column="company_type" jdbcType="SMALLINT" property="companyType"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="org_number" jdbcType="VARCHAR" property="orgNumber"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="base" jdbcType="VARCHAR" property="base"/>
        <result column="legal_person_name" jdbcType="VARCHAR" property="legalPersonName"/>
        <result column="match_type" jdbcType="VARCHAR" property="matchType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , reg_number, reg_status, credit_code, estiblish_time, reg_capital, company_type,
    `name`,  org_number, `type`, base, legal_person_name, match_type, create_time,
    creator, `status`, update_time, updater
    </sql>
    <select id="selectByPrimaryKey" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from enterprise_information
        where credit_code = #{creditCode}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="long">
        delete
        from enterprise_information
        where id = #{id}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.model.domain.EnterpriseInformation" useGeneratedKeys="true">
        insert into enterprise_information (reg_number, reg_status, credit_code,
                                            estiblish_time, reg_capital, company_type,
                                            `name`,  org_number,
                                            `type`, base, legal_person_name,
                                            match_type, create_time, creator,
                                            `status`, update_time, updater)
        values (#{regNumber,jdbcType=VARCHAR}, #{regStatus,jdbcType=VARCHAR}, #{creditCode,jdbcType=VARCHAR},
                #{estiblishTime,jdbcType=VARCHAR}, #{regCapital,jdbcType=VARCHAR}, #{companyType,jdbcType=SMALLINT},
                #{name,jdbcType=VARCHAR},  #{orgNumber,jdbcType=VARCHAR},
                #{type,jdbcType=INTEGER}, #{base,jdbcType=VARCHAR}, #{legalPersonName,jdbcType=VARCHAR},
                #{matchType,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR},
                #{status,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.model.domain.EnterpriseInformation" useGeneratedKeys="true">
        insert into enterprise_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="regNumber != null">
                reg_number,
            </if>
            <if test="regStatus != null">
                reg_status,
            </if>
            <if test="creditCode != null">
                credit_code,
            </if>
            <if test="estiblishTime != null">
                estiblish_time,
            </if>
            <if test="regCapital != null">
                reg_capital,
            </if>
            <if test="companyType != null">
                company_type,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="orgNumber != null">
                org_number,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="base != null">
                base,
            </if>
            <if test="legalPersonName != null">
                legal_person_name,
            </if>
            <if test="matchType != null">
                match_type,
            </if>
            create_time,
            <if test="creator != null">
                creator,
            </if>
                `status`,
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="regNumber != null">
                #{regNumber,jdbcType=VARCHAR},
            </if>
            <if test="regStatus != null">
                #{regStatus,jdbcType=VARCHAR},
            </if>
            <if test="creditCode != null">
                #{creditCode,jdbcType=VARCHAR},
            </if>
            <if test="estiblishTime != null">
                #{estiblishTime,jdbcType=VARCHAR},
            </if>
            <if test="regCapital != null">
                #{regCapital,jdbcType=VARCHAR},
            </if>
            <if test="companyType != null">
                #{companyType,jdbcType=SMALLINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="orgNumber != null">
                #{orgNumber,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="base != null">
                #{base,jdbcType=VARCHAR},
            </if>
            <if test="legalPersonName != null">
                #{legalPersonName,jdbcType=VARCHAR},
            </if>
            <if test="matchType != null">
                #{matchType,jdbcType=VARCHAR},
            </if>
                now(),
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
                0,
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.EnterpriseInformation">
        update enterprise_information
        <set>
            <if test="regNumber != null">
                reg_number = #{regNumber,jdbcType=VARCHAR},
            </if>
            <if test="regStatus != null">
                reg_status = #{regStatus,jdbcType=VARCHAR},
            </if>
            <if test="creditCode != null">
                credit_code = #{creditCode,jdbcType=VARCHAR},
            </if>
            <if test="estiblishTime != null">
                estiblish_time = #{estiblishTime,jdbcType=VARCHAR},
            </if>
            <if test="regCapital != null">
                reg_capital = #{regCapital,jdbcType=VARCHAR},
            </if>
            <if test="companyType != null">
                company_type = #{companyType,jdbcType=SMALLINT},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="orgNumber != null">
                org_number = #{orgNumber,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="base != null">
                base = #{base,jdbcType=VARCHAR},
            </if>
            <if test="legalPersonName != null">
                legal_person_name = #{legalPersonName,jdbcType=VARCHAR},
            </if>
            <if test="matchType != null">
                match_type = #{matchType,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.EnterpriseInformation">
        update enterprise_information
        set reg_number        = #{regNumber,jdbcType=VARCHAR},
            reg_status        = #{regStatus,jdbcType=VARCHAR},
            credit_code       = #{creditCode,jdbcType=VARCHAR},
            estiblish_time    = #{estiblishTime,jdbcType=VARCHAR},
            reg_capital       = #{regCapital,jdbcType=VARCHAR},
            company_type      = #{companyType,jdbcType=SMALLINT},
            `name`            = #{name,jdbcType=VARCHAR},
            org_number        = #{orgNumber,jdbcType=VARCHAR},
            `type`            = #{type,jdbcType=INTEGER},
            base              = #{base,jdbcType=VARCHAR},
            legal_person_name = #{legalPersonName,jdbcType=VARCHAR},
            match_type        = #{matchType,jdbcType=VARCHAR},
            create_time       = #{createTime,jdbcType=TIMESTAMP},
            creator           = #{creator,jdbcType=VARCHAR},
            `status`          = #{status,jdbcType=INTEGER},
            update_time       = #{updateTime,jdbcType=TIMESTAMP},
            updater           = #{updater,jdbcType=VARCHAR}
        where id = #{id}
    </update>

    <select id="selectAll" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from enterprise_information
        where `name` like concat('%',#{name},'%')
        limit 50
    </select>

    <select id="selectName" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from enterprise_information
        where `name` = #{name} and status = 0
    </select>

    <select id="select" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from enterprise_information
        where `name` = #{name} and credit_code = #{creditCode} and status = 0
    </select>
    <select id="selectByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from enterprise_information
        <where>
            status = 0
            <if test="name != null">
            and `name` like concat(#{name},'%')
            </if>
            <if test="creditCode != null">
            and credit_code = #{creditCode}
            </if>
        </where>
        order by id desc
    </select>
    <select id="countByCreditCode" resultType="java.lang.Integer">
        select count(1) from enterprise_information where credit_code = #{creditCode} and status = 0
    </select>

    <select id="selectByTaxNum" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from enterprise_information
        where credit_code = #{creditCode} and status = 0
    </select>

</mapper>