<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PartnershipBuyOrderMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PartnershipBuyOrder">
    <!--@mbg.generated-->
    <!--@Table market_partnership_buy_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="partnership_buy_id" jdbcType="BIGINT" property="partnershipBuyId" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="role_type" jdbcType="TINYINT" property="roleType" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, partnership_buy_id, config_id, m_id, role_type, order_no, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from market_partnership_buy_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from market_partnership_buy_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PartnershipBuyOrder" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into market_partnership_buy_order (partnership_buy_id, config_id, m_id, 
      role_type, order_no, create_time, 
      update_time)
    values (#{partnershipBuyId,jdbcType=BIGINT}, #{configId,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, 
      #{roleType,jdbcType=TINYINT}, #{orderNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PartnershipBuyOrder" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into market_partnership_buy_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="partnershipBuyId != null">
        partnership_buy_id,
      </if>
      <if test="configId != null">
        config_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="roleType != null">
        role_type,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="partnershipBuyId != null">
        #{partnershipBuyId,jdbcType=BIGINT},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="roleType != null">
        #{roleType,jdbcType=TINYINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PartnershipBuyOrder">
    <!--@mbg.generated-->
    update market_partnership_buy_order
    <set>
      <if test="partnershipBuyId != null">
        partnership_buy_id = #{partnershipBuyId,jdbcType=BIGINT},
      </if>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="roleType != null">
        role_type = #{roleType,jdbcType=TINYINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PartnershipBuyOrder">
    <!--@mbg.generated-->
    update market_partnership_buy_order
    set partnership_buy_id = #{partnershipBuyId,jdbcType=BIGINT},
      config_id = #{configId,jdbcType=BIGINT},
      m_id = #{mId,jdbcType=BIGINT},
      role_type = #{roleType,jdbcType=TINYINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <resultMap id="AssociationOrderNoResultMap" type="net.summerfarm.model.DTO.market.AssociationOrderNoDTO">
    <result column="status" property="status"/>
    <collection property="orderNos" ofType="java.lang.String">
      <result column="order_no"/>
    </collection>
  </resultMap>
  <select id="selectAssociationOrderNoByOrderNo" resultMap="AssociationOrderNoResultMap">
    select mpbo.order_no,status
    from market_partnership_buy mpb
         left join market_partnership_buy_order mpbo on mpb.id = mpbo.partnership_buy_id
    where mpb.id = (select partnership_buy_id
                    from market_partnership_buy_order
                    where order_no = #{orderNo})
  </select>
</mapper>