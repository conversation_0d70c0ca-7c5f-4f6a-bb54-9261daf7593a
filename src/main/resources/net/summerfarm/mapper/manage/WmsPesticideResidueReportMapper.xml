<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.WmsPesticideResidueReportMapper">

    <select id="queryResidueReport" parameterType="net.summerfarm.model.domain.WmsPesticideResidueReport"
                                                resultType="net.summerfarm.model.domain.WmsPesticideResidueReport">
            select id,
                   create_time createTime,
                   update_time updateTime,
                   batch,
                   sku,
                   detection_result detectionResult,
                   sampling_base samplingBase,
                   number_samples numberSamples,
                   inhibition_rate inhibitionRate,
                   picture_url pictureUrl
            from wms_pesticide_residue_report
            where sku = #{sku} and batch = #{batch} and production_date = #{productionDate} and status = 0
    </select>

    <insert id="savePesticideResidueReport" parameterType="net.summerfarm.model.domain.WmsPesticideResidueReport">
          insert into wms_pesticide_residue_report (batch,sku,detection_result,sampling_base,number_samples,inhibition_rate,picture_url,production_date,status)
          value (#{batch},#{sku},#{detectionResult},#{samplingBase},#{numberSamples},#{inhibitionRate},#{pictureUrl},#{productionDate},#{status})
    </insert>

    <update id="updatePesticideResidueReport" parameterType="net.summerfarm.model.domain.WmsPesticideResidueReport">
        update wms_pesticide_residue_report
        <set>
            <if test="detectionResult != null">
               , detection_result = #{detectionResult}
            </if>
            <if test="samplingBase != null">
                , sampling_base = #{samplingBase}
            </if>
            <if test="numberSamples != null">
               , number_samples =#{numberSamples}
            </if>
            <if test="inhibitionRate != null">
                , inhibition_rate = #{inhibitionRate}
            </if>
            <if test="pictureUrl != null">
                , picture_url = #{pictureUrl}
            </if>
            <if test="status != null">
                , status = #{status}
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateStatus">
        update wms_pesticide_residue_report
        set  status = #{status}
        where id = #{id}
    </update>
</mapper>