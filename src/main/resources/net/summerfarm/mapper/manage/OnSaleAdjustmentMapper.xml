<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.OnSaleAdjustmentMapper">

    <insert id="insert" parameterType="net.summerfarm.model.domain.OnSaleAdjustment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO on_sale_adjustment(sku, area_no, on_sale, status, up_time, update_time, add_time)
        VALUES (#{sku} ,#{areaNo} ,#{onSale} ,#{status} ,#{upTime} ,#{updateTime} ,#{addTime} )
    </insert>

    <select id="selectList" parameterType="net.summerfarm.model.domain.OnSaleAdjustment"
            resultType="net.summerfarm.model.domain.OnSaleAdjustment">
        SELECT osa.id, osa.sku, osa.area_no areaNo, osa.on_sale onSale, osa.status, osa.up_time upTime, osa.update_time updateTime, osa.add_time addTime
        FROM on_sale_adjustment osa
        <where>
            <if test="status != null">
                AND osa.status = #{status}
            </if>
            <if test="areaNo != null">
                AND osa.area_no = #{areaNo}
            </if>
            <if test="sku != null">
                AND osa.sku = #{sku}
            </if>
            <if test="onSale != null">
                AND osa.on_sale = #{onSale}
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer"
            resultType="net.summerfarm.model.domain.OnSaleAdjustment">
        SELECT osa.id, osa.sku, osa.area_no areaNo, osa.on_sale onSale, osa.status, osa.up_time upTime, osa.update_time updateTime, osa.add_time addTime
        FROM on_sale_adjustment osa
        WHERE osa.id = #{id}
    </select>
  <select id="selectListByStatusAndUpTime" resultType="net.summerfarm.model.domain.OnSaleAdjustment">
      SELECT
        osa.id, osa.sku, osa.area_no areaNo, osa.on_sale onSale, osa.status, osa.up_time upTime, osa.update_time updateTime, osa.add_time addTime
      FROM on_sale_adjustment osa
      WHERE status = #{status} and on_sale = #{onSale} and up_time &lt;= #{maxUpTime}
  </select>

  <update id="update" parameterType="net.summerfarm.model.domain.OnSaleAdjustment">
        UPDATE on_sale_adjustment
        <set>
            <if test="status != null">
                status = #{status} ,
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime} ,
            </if>
        </set>
        WHERE id = #{id}
    </update>

</mapper>