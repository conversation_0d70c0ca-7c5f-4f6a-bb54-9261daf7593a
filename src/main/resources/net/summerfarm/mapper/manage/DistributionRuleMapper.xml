<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.DistributionRuleMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.vo.DistributionRuleVO">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL" />
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <collection property="distributionFreeRules" resultMap="BaseFreeResultMap"/>
    </resultMap>

    <resultMap id="BaseFreeResultMap" type="net.summerfarm.model.domain.DistributionFreeRule">
        <id column="rule_id" property="id" jdbcType="INTEGER" />
        <result column="rule_status" property="status" jdbcType="INTEGER" />
        <result column="rule" property="rule" jdbcType="VARCHAR" />
        <result column="distribution_id" property="distributionId" jdbcType="INTEGER" />
    </resultMap>
    <sql id="queryColumns">
      id,
      status,
      area_no as areaNo,
      delivery_fee as deliveryFee ,
      admin_id as adminId
    </sql>

    <select id="selectByAdminId" parameterType="java.lang.Integer" resultType="net.summerfarm.model.domain.DistributionRule">
        select
        <include refid="queryColumns"/>
        from distribution_fee
        where admin_id = #{adminId} and status = 0
    </select>


    <insert id="insertBathRule" parameterType="net.summerfarm.model.domain.DistributionRule" useGeneratedKeys="true" keyProperty="id">
        insert into distribution_fee
        (status,area_no,delivery_fee,admin_id)
        values
        <foreach collection ="list" item="item" separator =",">
        (#{item.status}, #{item.areaNo}, #{item.deliveryFee}, #{item.adminId})
        </foreach >
    </insert>

    <update id="updateRule" parameterType="net.summerfarm.model.domain.DistributionRule">
      update distribution_fee
      set
        <if test = "status != null">
          status = #{status} ,
        </if>
        <if test = "areaNo != null">
            area_no = #{areaNo} ,
        </if>
        <if test = "deliveryFee != null">
           deliveryFee = #{delivery_fee} ,
        </if>
        <if test="adminId != null">
           admin_id = #{adminId}
        </if>
        where
        <if test = "id != null">
             id = #{id} and
        </if>
        <if test = "adminId != null">
            admin_id = #{adminId}
        </if>
    </update>

    <insert id="insertBathRuleVO" parameterType="net.summerfarm.model.vo.DistributionRuleVO" useGeneratedKeys="true" keyProperty="id">
        insert into distribution_fee
        (status,area_no,delivery_fee,admin_id)
        values
        <foreach collection ="list" item="item" separator =",">
            (#{item.status}, #{item.areaNo}, #{item.deliveryFee}, #{item.adminId})
        </foreach >
    </insert>

    <select id="queryListRule" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select d.id, d.area_no areaNo, d.delivery_fee deliveryFee, d.status, d.admin_id adminId, r.id rule_id, r.status rule_status, r.rule , r.distribution_id distributionId
        from distribution_fee d
          left join rule_distribution_free r on r.distribution_id = d.id
          where d.admin_id = #{id} and d.status = 0
    </select>


    <select id="queryRuleList" resultMap="BaseResultMap">
        select d.id, d.area_no, d.delivery_fee , d.status, d.admin_id , r.id rule_id, r.status rule_status, r.rule
        from distribution_fee d
          left join rule_distribution_free r on r.distribution_id = d.id and r.status = 0
          where d.status = 0
          <if test="ids != null and ids.size!=0">
              AND d.admin_id IN
              <foreach collection="ids" open="(" close=")" item="item" separator=",">
                  #{item}
              </foreach>
          </if>
    </select>

    <select id="selectByAdminIdArea" resultType="net.summerfarm.model.domain.DistributionRule">
        select
        <include refid="queryColumns"/>
        from distribution_fee
        where admin_id = #{adminId} and status = 0 and area_no = #{areaNo}
    </select>



</mapper>