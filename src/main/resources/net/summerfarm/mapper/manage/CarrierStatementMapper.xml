<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CarrierStatementMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CarrierStatement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="driver" jdbcType="VARCHAR" property="driver" />
    <result column="carrier_name" jdbcType="VARCHAR" property="carrierName" />
    <result column="service_area" jdbcType="VARCHAR" property="serviceArea" />
    <result column="province_city" jdbcType="VARCHAR" property="provinceCity" />
    <result column="area_county" jdbcType="VARCHAR" property="areaCounty" />
    <result column="point_num" jdbcType="INTEGER" property="pointNum" />
    <result column="start_point_num" jdbcType="INTEGER" property="startPointNum" />
    <result column="start_price" jdbcType="DECIMAL" property="startPrice" />
    <result column="exceed_point_price" jdbcType="DECIMAL" property="exceedPointPrice" />
    <result column="subsidy_price" jdbcType="DECIMAL" property="subsidyPrice" />
    <result column="km" jdbcType="DECIMAL" property="km" />
    <result column="km_price" jdbcType="DECIMAL" property="kmPrice" />
    <result column="printing_fee" jdbcType="DECIMAL" property="printingFee" />
    <result column="front_warehouse_fee" jdbcType="DECIMAL" property="frontWarehouseFee" />
    <result column="help_order_fee" jdbcType="DECIMAL" property="helpOrderFee" />
    <result column="taxi_fare" jdbcType="DECIMAL" property="taxiFare" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, delivery_time, store_no, `path`, driver, carrier_name, 
    service_area, province_city, area_county, point_num, start_point_num, start_price, 
    exceed_point_price, subsidy_price, km, km_price, printing_fee, front_warehouse_fee, 
    help_order_fee, taxi_fare
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from carrier_statement
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectCarrierStatement" resultType="net.summerfarm.model.vo.CarrierStatementVo">
    select distinct
      cs.id, cs.create_time createTime, cs.update_time updateTime, cs.delivery_time deliveryTime, cs.store_no storeNo,
           cs.`path`, cs.driver, cs.carrier_name carrierName,
      cs.service_area serviceArea, cs.province_city provinceCity, cs.area_county areaCounty, cs.point_num pointNum,
           cs.start_point_num startPointNum, cs.start_price startPrice,cs.exceed_point_num exceedPointNum,
      cs.exceed_point_price exceedPointPrice, cs.subsidy_price subsidyPrice, km, cs.km_price kmPrice,cs.real_km realKm,cs.real_km_price realKmPrice,
           cs.printing_fee printingFee, cs.front_warehouse_fee frontWarehouseFee,
      cs.help_order_fee helpOrderFee, cs.taxi_fare taxiFare,wlc.store_name storeName,cs.carrier_quotation_id carrierQuotationId,cs.delivery_car_id DeliveryCarId
    from carrier_statement cs
    left join warehouse_logistics_center wlc on cs.store_no = wlc.store_no
    left join carrier_quotation_area cqa on cs.carrier_quotation_id = cqa.carrier_quotation_id
    <where>
      <if test="deliveryTime!= null">
         and cs.delivery_time = #{deliveryTime}
      </if>
      <if test="startTime !=null">
         and cs.delivery_time >= #{startTime}
      </if>
      <if test="endTime !=null">
         and cs.delivery_time &lt;= #{endTime}
      </if>
      <if test="carrierName!=null">
         and cs.carrier_name = #{carrierName}
      </if>
      <if test="serviceAreas!=null and serviceAreas.size > 0">
        and
        <foreach collection="serviceAreas" item="serviceArea" separator="or">
           cs.service_area = #{serviceArea}
        </foreach>
      </if>
      <if test="serviceArea!=null">
         and cs.service_area = #{serviceArea}
      </if>
      <if test="storeNo!=null">
         and cs.store_no = #{storeNo}
      </if>
      <if test="province !=null">
         and cqa.province = #{province}
      </if>
      <if test="city !=null">
        and cqa.city = #{city}
      </if>
      <if test="area !=null">
        and cqa.area = #{area}
      </if>
      <if test="county !=null">
         and cs.county = #{county}
      </if>
    </where>
    </select>
  <select id="selectCarrierStatementByExtra" resultType="net.summerfarm.model.vo.CarrierStatementVo">
    select id from carrier_statement
    where delivery_time = #{deliveryTime} AND store_no = #{storeNo} and path = #{path}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from carrier_statement
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CarrierStatement" useGeneratedKeys="true">
    insert into carrier_statement (create_time, update_time, delivery_time, 
      store_no, `path`, driver, 
      carrier_name, service_area, province_city, 
      area_county, point_num, start_point_num, 
      start_price, exceed_point_price, subsidy_price, 
      km, km_price, printing_fee, 
      front_warehouse_fee, help_order_fee, taxi_fare
      )
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deliveryTime,jdbcType=TIMESTAMP}, 
      #{storeNo,jdbcType=INTEGER}, #{path,jdbcType=VARCHAR}, #{driver,jdbcType=VARCHAR}, 
      #{carrierName,jdbcType=VARCHAR}, #{serviceArea,jdbcType=VARCHAR}, #{provinceCity,jdbcType=VARCHAR}, 
      #{areaCounty,jdbcType=VARCHAR}, #{pointNum,jdbcType=INTEGER}, #{startPointNum,jdbcType=INTEGER}, 
      #{startPrice,jdbcType=DECIMAL}, #{exceedPointPrice,jdbcType=DECIMAL}, #{subsidyPrice,jdbcType=DECIMAL}, 
      #{km,jdbcType=DECIMAL}, #{kmPrice,jdbcType=DECIMAL}, #{printingFee,jdbcType=DECIMAL}, 
      #{frontWarehouseFee,jdbcType=DECIMAL}, #{helpOrderFee,jdbcType=DECIMAL}, #{taxiFare,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CarrierStatement" useGeneratedKeys="true">
    insert into carrier_statement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="path != null">
        `path`,
      </if>
      <if test="driver != null">
        driver,
      </if>
      <if test="carrierName != null">
        carrier_name,
      </if>
      <if test="serviceArea != null">
        service_area,
      </if>
      <if test="provinceCity != null">
        province_city,
      </if>
      <if test="areaCounty != null">
        area_county,
      </if>
      <if test="pointNum != null">
        point_num,
      </if>
      <if test="startPointNum != null">
        start_point_num,
      </if>
      <if test="startPrice != null">
        start_price,
      </if>
      <if test="exceedPointPrice != null">
        exceed_point_price,
      </if>
      <if test="subsidyPrice != null">
        subsidy_price,
      </if>
      <if test="km != null">
        km,
      </if>
      <if test="kmPrice != null">
        km_price,
      </if>
      <if test="printingFee != null">
        printing_fee,
      </if>
      <if test="frontWarehouseFee != null">
        front_warehouse_fee,
      </if>
      <if test="helpOrderFee != null">
        help_order_fee,
      </if>
      <if test="taxiFare != null">
        taxi_fare,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="driver != null">
        #{driver,jdbcType=VARCHAR},
      </if>
      <if test="carrierName != null">
        #{carrierName,jdbcType=VARCHAR},
      </if>
      <if test="serviceArea != null">
        #{serviceArea,jdbcType=VARCHAR},
      </if>
      <if test="provinceCity != null">
        #{provinceCity,jdbcType=VARCHAR},
      </if>
      <if test="areaCounty != null">
        #{areaCounty,jdbcType=VARCHAR},
      </if>
      <if test="pointNum != null">
        #{pointNum,jdbcType=INTEGER},
      </if>
      <if test="startPointNum != null">
        #{startPointNum,jdbcType=INTEGER},
      </if>
      <if test="startPrice != null">
        #{startPrice,jdbcType=DECIMAL},
      </if>
      <if test="exceedPointPrice != null">
        #{exceedPointPrice,jdbcType=DECIMAL},
      </if>
      <if test="subsidyPrice != null">
        #{subsidyPrice,jdbcType=DECIMAL},
      </if>
      <if test="km != null">
        #{km,jdbcType=DECIMAL},
      </if>
      <if test="kmPrice != null">
        #{kmPrice,jdbcType=DECIMAL},
      </if>
      <if test="printingFee != null">
        #{printingFee,jdbcType=DECIMAL},
      </if>
      <if test="frontWarehouseFee != null">
        #{frontWarehouseFee,jdbcType=DECIMAL},
      </if>
      <if test="helpOrderFee != null">
        #{helpOrderFee,jdbcType=DECIMAL},
      </if>
      <if test="taxiFare != null">
        #{taxiFare,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
    <insert id="insertBatch">
        insert into carrier_statement
            ( delivery_time, store_no, path, driver, carrier_name, service_area, point_num, start_point_num,
              start_price, exceed_point_price, subsidy_price, km, km_price, printing_fee,
              front_warehouse_fee, help_order_fee, taxi_fare, exceed_point_num, carrier_quotation_id, extra,delivery_car_id,
                real_km, real_km_price)
        values
        <foreach collection="carrierStatements" item="item" separator=",">
            (#{item.deliveryTime},#{item.storeNo},#{item.path},#{item.driver},#{item.carrierName},#{item.serviceArea},
             #{item.pointNum},#{item.startPointNum},#{item.startPrice},#{item.exceedPointPrice},#{item.subsidyPrice},
             #{item.km},#{item.kmPrice},#{item.printingFee},#{item.frontWarehouseFee},#{item.helpOrderFee},#{item.taxiFare},
             #{item.exceedPointNum},#{item.carrierQuotationId},#{item.extra},#{item.deliveryCarId},#{item.realKm},#{item.realKmPrice}
             )
        </foreach>


    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CarrierStatement">
    update carrier_statement
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="path != null">
        `path` = #{path,jdbcType=VARCHAR},
      </if>
      <if test="driver != null">
        driver = #{driver,jdbcType=VARCHAR},
      </if>
      <if test="carrierName != null">
        carrier_name = #{carrierName,jdbcType=VARCHAR},
      </if>
      <if test="serviceArea != null">
        service_area = #{serviceArea,jdbcType=VARCHAR},
      </if>
      <if test="provinceCity != null">
        province_city = #{provinceCity,jdbcType=VARCHAR},
      </if>
      <if test="areaCounty != null">
        area_county = #{areaCounty,jdbcType=VARCHAR},
      </if>
      <if test="pointNum != null">
        point_num = #{pointNum,jdbcType=INTEGER},
      </if>
      <if test="startPointNum != null">
        start_point_num = #{startPointNum,jdbcType=INTEGER},
      </if>
      <if test="startPrice != null">
        start_price = #{startPrice,jdbcType=DECIMAL},
      </if>
      <if test="exceedPointPrice != null">
        exceed_point_price = #{exceedPointPrice,jdbcType=DECIMAL},
      </if>
      <if test="subsidyPrice != null">
        subsidy_price = #{subsidyPrice,jdbcType=DECIMAL},
      </if>
      <if test="km != null">
        km = #{km,jdbcType=DECIMAL},
      </if>
      <if test="kmPrice != null">
        km_price = #{kmPrice,jdbcType=DECIMAL},
      </if>
      <if test="printingFee != null">
        printing_fee = #{printingFee,jdbcType=DECIMAL},
      </if>
      <if test="frontWarehouseFee != null">
        front_warehouse_fee = #{frontWarehouseFee,jdbcType=DECIMAL},
      </if>
      <if test="helpOrderFee != null">
        help_order_fee = #{helpOrderFee,jdbcType=DECIMAL},
      </if>
      <if test="taxiFare != null">
        taxi_fare = #{taxiFare,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CarrierStatement">
    update carrier_statement
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      store_no = #{storeNo,jdbcType=INTEGER},
      `path` = #{path,jdbcType=VARCHAR},
      driver = #{driver,jdbcType=VARCHAR},
      carrier_name = #{carrierName,jdbcType=VARCHAR},
      service_area = #{serviceArea,jdbcType=VARCHAR},
      province_city = #{provinceCity,jdbcType=VARCHAR},
      area_county = #{areaCounty,jdbcType=VARCHAR},
      point_num = #{pointNum,jdbcType=INTEGER},
      start_point_num = #{startPointNum,jdbcType=INTEGER},
      start_price = #{startPrice,jdbcType=DECIMAL},
      exceed_point_price = #{exceedPointPrice,jdbcType=DECIMAL},
      subsidy_price = #{subsidyPrice,jdbcType=DECIMAL},
      km = #{km,jdbcType=DECIMAL},
      km_price = #{kmPrice,jdbcType=DECIMAL},
      printing_fee = #{printingFee,jdbcType=DECIMAL},
      front_warehouse_fee = #{frontWarehouseFee,jdbcType=DECIMAL},
      help_order_fee = #{helpOrderFee,jdbcType=DECIMAL},
      taxi_fare = #{taxiFare,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>