<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.RolePurviewMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.RolePurview" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="role_id" property="roleId" jdbcType="INTEGER" />
    <result column="purview_id" property="purviewId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, role_id, purview_id
  </sql>

  <!--删除角色-权限-->
  <delete id="delete" >
    DELETE FROM role_purview
    WHERE role_id = #{roleId,jdbcType=INTEGER} AND purview_id IN (
    <foreach collection="ids" item="item" separator="," >
      #{item,jdbcType=INTEGER}
    </foreach>
     )
  </delete>
  <!--新增角色-权限-->
  <insert id="insertList">
    INSERT INTO role_purview (role_id, purview_id)
    VALUES
    <foreach collection="ids" item="item" separator=",">
      (#{roleId},#{item})
    </foreach>

  </insert>

  <delete id="deleteAll">
    DELETE FROM role_purview
    WHERE role_id = #{roleId}
  </delete>



</mapper>