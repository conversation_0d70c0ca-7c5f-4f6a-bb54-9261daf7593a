<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DeliveryCarWarehouseFeeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DeliveryCarWarehouseFee">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delivery_car_id" jdbcType="INTEGER" property="deliveryCarId" />
    <result column="front_warehouse_fee" jdbcType="DECIMAL" property="frontWarehouseFee" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, delivery_car_id, front_warehouse_fee
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from delivery_car_warehouse_fee
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByDeliveryCarId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from delivery_car_warehouse_fee
    where delivery_car_id = #{deliveryCarId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from delivery_car_warehouse_fee
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DeliveryCarWarehouseFee" useGeneratedKeys="true">
    insert into delivery_car_warehouse_fee (create_time, update_time, delivery_car_id, 
      front_warehouse_fee)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deliveryCarId,jdbcType=INTEGER}, 
      #{frontWarehouseFee,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DeliveryCarWarehouseFee" useGeneratedKeys="true">
    insert into delivery_car_warehouse_fee
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deliveryCarId != null">
        delivery_car_id,
      </if>
      <if test="frontWarehouseFee != null">
        front_warehouse_fee,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryCarId != null">
        #{deliveryCarId,jdbcType=INTEGER},
      </if>
      <if test="frontWarehouseFee != null">
        #{frontWarehouseFee,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.DeliveryCarWarehouseFee">
    update delivery_car_warehouse_fee
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryCarId != null">
        delivery_car_id = #{deliveryCarId,jdbcType=INTEGER},
      </if>
      <if test="frontWarehouseFee != null">
        front_warehouse_fee = #{frontWarehouseFee,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DeliveryCarWarehouseFee">
    update delivery_car_warehouse_fee
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      delivery_car_id = #{deliveryCarId,jdbcType=INTEGER},
      front_warehouse_fee = #{frontWarehouseFee,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>