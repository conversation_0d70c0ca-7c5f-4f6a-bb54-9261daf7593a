<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MatchPurchaseOrderMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MatchPurchaseOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchase_no" jdbcType="VARCHAR" property="purchaseNo" />
    <result column="purchase_invoice_id" jdbcType="INTEGER" property="purchaseInvoiceId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="match_amount" jdbcType="DECIMAL" property="matchAmount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    id, purchase_no, purchase_invoice_id, sku, match_amount, `status`, create_time, creator, 
    update_time, updater
  </sql>

  <select id="selectMatchAmount" parameterType="java.lang.Integer" resultType="net.summerfarm.model.vo.MatchPurchaseOrderVO">
    select IFNULL(sum(match_amount),0) as matchAmounts
    from match_purchase_order
    where purchase_invoice_id = #{purchaseInvoiceId} and status = 0
  </select>

  <select id="selectNum" parameterType="java.lang.Integer" resultType="Integer">
    select count(1)
    from(select DISTINCT purchase_no,sku
       from match_purchase_order
       where purchase_invoice_id = #{purchaseInvoiceId} and status = 0) s
  </select>

  <select id="select" parameterType="java.lang.Integer" resultType="net.summerfarm.model.domain.MatchPurchaseOrder">
    select match_amount
    from match_purchase_order
    where purchase_invoice_id = #{purchaseInvoiceId} and status = 0
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="net.summerfarm.model.vo.MatchPurchaseOrderVO">
    select match_amount matchAmount,purchase_no purchaseNo,purchase_invoice_id purchaseInvoiceId,sku sku
    from match_purchase_order
    where id = #{id,jdbcType=INTEGER} and status = 0
  </select>

  <select id="selectList" parameterType="java.lang.Integer" resultType="net.summerfarm.model.vo.MatchPurchaseOrderVO">
    select mpo.id id,mpo.purchase_no purchaseNo,mpo.sku sku,mpo.match_amount matchAmount,mpo.updater updater,mpo.update_time updateTime,mpo.price price,pi.invoice_code invoiceCode,
           pi.invoice_number invoiceNumber,i.weight weight,p.pd_name pdName,mpo.updater updater,mpo.purchase_time purchaseTime,mpo.actual_tax_amount actualTaxAmount
    from match_purchase_order mpo
    left join purchase_invoice pi on mpo.purchase_invoice_id = pi.id
    left join inventory i on mpo.sku  = i.sku
    left join products p on i.pd_id = p.pd_id
    where mpo.purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER} and mpo.status = 0
  </select>

  <select id="selectListSum" resultType="net.summerfarm.model.vo.MatchPurchaseOrderVO">
    select sum(mpo.match_amount) matchAmounts
    from match_purchase_order mpo
    left join purchase_invoice pi on mpo.purchase_invoice_id = pi.id
    left join inventory i on mpo.sku  = i.sku
    left join products p on i.pd_id = p.pd_id
    where mpo.purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER} and mpo.status = 0
    limit #{pageIndex}
  </select>

  <select id="selectSplitSum" parameterType="integer" resultType="integer">
    select count(1)
    from match_purchase_order
    where purchase_invoice_id = #{id}
  </select>

  <select id="selectPurchasesSum" parameterType="integer" resultType="integer">
    select count(1)
    from(select DISTINCT purchase_no,sku
         from match_purchase_order
         where purchase_invoice_id = #{id} and status = 0) s
  </select>

  <select id="selectSkuSum" parameterType="integer" resultType="integer">
    select count(1)
    from(select DISTINCT sku,purchase_no
         from match_purchase_order
         where purchase_invoice_id = #{id} and status = 0) s
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from match_purchase_order
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MatchPurchaseOrder" useGeneratedKeys="true">
    insert into match_purchase_order (purchase_no, purchase_invoice_id, sku, 
      match_amount, `status`, create_time, 
      creator, update_time, updater
      )
    values (#{purchaseNo,jdbcType=VARCHAR}, #{purchaseInvoiceId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{matchAmount,jdbcType=DECIMAL}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MatchPurchaseOrder" useGeneratedKeys="true">
    insert into match_purchase_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="purchaseNo != null">
        purchase_no,
      </if>
      <if test="purchaseInvoiceId != null">
        purchase_invoice_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="matchAmount != null">
        match_amount,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="purchaseNo != null">
        #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseInvoiceId != null">
        #{purchaseInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="matchAmount != null">
        #{matchAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.MatchPurchaseOrder">
    update match_purchase_order
    <set>
      <if test="purchaseNo != null">
        purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseInvoiceId != null">
        purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="matchAmount != null">
        match_amount = #{matchAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.MatchPurchaseOrder">
    update match_purchase_order
    set purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      match_amount = #{matchAmount,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="insertBathDetail" parameterType="net.summerfarm.model.input.MatchPurchaseOrderInput">
    insert into match_purchase_order (`purchase_no`, `purchase_invoice_id` , `sku`,`match_amount`,`status` ,`create_time`,`creator`,`update_time`,`updater`,`price`,`purchase_time`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.purchaseNo},#{item.purchaseInvoiceId},#{item.sku},#{item.matchAmount},0,now(),#{item.creator},now(),#{item.updater},#{item.price},#{item.purchaseTime})
    </foreach>
  </insert>

  <insert id="insertDetail" parameterType="net.summerfarm.model.input.MatchPurchaseOrderInput">
    insert into match_purchase_order (`purchase_no`, `purchase_invoice_id` , `sku`,`match_amount`,`status` ,`create_time`,`creator`,`update_time`,`updater`,`price`,`purchase_time`)
    values (#{purchaseNo},#{purchaseInvoiceId},#{sku},#{matchAmount},0,now(),#{creator},now(),#{updater},#{price},#{purchaseTime})
  </insert>

  <select id="complete" parameterType="string" resultType="integer">
    select distinct count(pil.id)
    from match_purchase_order mpo
    left join purchase_invoice pi on mpo.purchase_invoice_id = pi.id and pi.delete_status = 0
    left join purchase_invoice_log pil on pi.id = pil.purchase_invoice_id and pil.status = 3 and pil.state = 0
    where mpo.purchase_no = #{purchaseNo} and mpo.status = 0
  </select>

  <select id="selectSum" parameterType="string" resultType="integer">
    select distinct count(pil.id)
    from match_purchase_order mpo
    left join purchase_invoice pi on mpo.purchase_invoice_id = pi.id and pi.delete_status = 0
    left join purchase_invoice_log pil on pi.id = pil.purchase_invoice_id  and pil.state = 0
    where mpo.purchase_no = #{purchaseNo} and mpo.status = 0
  </select>
  
  <update id="updateMatchLog" parameterType="net.summerfarm.model.vo.UpdateMatchLogVO">
    update match_purchase_order
    set status = 1,
        update_time = now(),
        updater = #{updater}
    where id = #{id} and status = 0
  </update>

  <update id="updateRejectMatchLog" parameterType="net.summerfarm.model.domain.MatchPurchaseOrder">
    update match_purchase_order
    set status = 2,
        update_time = now(),
        updater = #{updater}
    where id = #{id} and status = 0
  </update>

  <update id="updateReject" parameterType="net.summerfarm.model.domain.MatchPurchaseOrder">
    update match_purchase_order
    set status = 2,
        update_time = now(),
        updater = #{updater}
    where purchase_invoice_id = #{purchaseInvoiceId} and status = 0
  </update>
  <update id="addActualTaxAmount" parameterType="net.summerfarm.model.domain.MatchPurchaseOrder">
    update match_purchase_order
    set actual_tax_amount = #{actualTaxAmount}
    where id = #{id} and status = 0
  </update>
  
  <select id="selectByMatchMsg" resultType="net.summerfarm.model.domain.MatchPurchaseOrder">
    select m.purchase_no,m.sku
    from match_purchase_order m
    left join purchase_invoice_log p on m.purchase_invoice_id = p.purchase_invoice_id
    where p.status = 3 and p.state = 0 and m.update_time <![CDATA[>=]]> '2022-01-01'
    group by m.purchase_no,m.sku
  </select>

</mapper>