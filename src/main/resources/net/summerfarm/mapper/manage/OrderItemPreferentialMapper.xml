<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.OrderItemPreferentialMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.OrderItemPreferential">
        <!--@Table order_item_preferential-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="amount" column="amount" jdbcType="NUMERIC"/>
        <result property="orderItemId" column="order_item_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="relatedId" column="related_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryByOrderNo" resultMap="BaseResultMap">
        select
          id, order_no, amount, order_item_id, type, related_id, create_time, update_time
        from order_item_preferential
        where order_no = #{orderNo}
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.OrderItemPreferential" useGeneratedKeys="true">
        insert into order_item_preferential
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="orderItemId != null">
                order_item_id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="relatedId != null">
                related_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="discountsDetailSnapshot != null">
                discounts_detail_snapshot,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="orderItemId != null">
                #{orderItemId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                #{relatedId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="discountsDetailSnapshot != null">
                #{discountsDetailSnapshot,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>

