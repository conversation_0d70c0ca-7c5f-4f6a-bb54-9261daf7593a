<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PriceAdjustmentTriggerMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PriceAdjustmentTrigger">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="cost_price" jdbcType="DECIMAL" property="costPrice" />
    <result column="market_price" jdbcType="DECIMAL" property="marketPrice" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
    <result column="business_id" jdbcType="BIGINT" property="businessId"/>
    <result column="purchase_no" jdbcType="VARCHAR" property="purchaseNo"/>
  </resultMap>
  <sql id="Base_Column_List">
    id,cost_price,market_price,quantity,valid,business_id,purchase_no
  </sql>
  <insert id="insert" parameterType="net.summerfarm.model.domain.PriceAdjustmentTrigger">
    insert into price_adjustment_trigger (id, cost_price, market_price, 
      quantity, valid, business_id,purchase_no
      )
    values (#{id,jdbcType=INTEGER}, #{costPrice,jdbcType=DECIMAL}, #{marketPrice,jdbcType=DECIMAL}, 
      #{quantity,jdbcType=INTEGER}, #{valid,jdbcType=INTEGER}, #{businessId,jdbcType=BIGINT},#{purchaseNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.PriceAdjustmentTrigger">
    insert into price_adjustment_trigger
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="costPrice != null">
        cost_price,
      </if>
      <if test="marketPrice != null">
        market_price,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="purchaseNo != null">
        purchase_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="marketPrice != null">
        #{marketPrice,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="purchaseNo != null">
        #{purchaseNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="selectByBusinessId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from price_adjustment_trigger
    where business_id = #{businessId}
    order by valid desc
  </select>
  <select id="sumByTypeAndPrice" resultType="net.summerfarm.model.domain.PriceAdjustmentTrigger">
    select cost_price costPrice,sum(quantity) quantity,market_price marketPrice,purchase_no purchaseNo
    from price_adjustment_trigger
    where business_id = #{businessId} group by cost_price order by valid desc
  </select>
  <select id="selectLastTrigger" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from price_adjustment_trigger
    where business_id = (select business_id
                         from price_adjustment_pool pap
                         left join area a on pap.area_no = a.area_no
                         where a.large_area_no = #{storeNo}
                           and pap.sku = #{sku}
                         group by pap.business_id
                         order by pap.id desc
                         limit 1,1

    )order by valid desc
  </select>
  <select id="selectValidBatch" resultType="string">
    select purchase_no from price_adjustment_trigger where valid = 1 and business_id =#{businessId}
  </select>
</mapper>