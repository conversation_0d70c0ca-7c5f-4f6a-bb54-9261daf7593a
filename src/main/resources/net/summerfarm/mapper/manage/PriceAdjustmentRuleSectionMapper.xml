<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PriceAdjustmentRuleSectionMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PriceAdjustmentRuleSection">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="start_price" jdbcType="DECIMAL" property="startPrice" />
    <result column="end_price" jdbcType="DECIMAL" property="endPrice" />
    <result column="fluctuation_value" jdbcType="DECIMAL" property="fluctuationValue" />
    <result column="creater" jdbcType="INTEGER" property="creater" />
    <result column="creat_time" jdbcType="TIMESTAMP" property="creatTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, start_price, end_price, fluctuation_value, creater, creat_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from price_adjustment_rule_section
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from price_adjustment_rule_section
    order by start_price
  </select>
    <select id="selectByCycleCost" resultType="net.summerfarm.model.domain.PriceAdjustmentRuleSection">
      select fluctuation_value as fluctuationValue from
    price_adjustment_rule_section where #{endCycleCost} &gt;= start_price and #{endCycleCost} &lt;=
                                                                              (case
                                                                                end_price when -1 then start_price + 10000000 else end_price end)

    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from price_adjustment_rule_section
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteAll">
    delete from price_adjustment_rule_section
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PriceAdjustmentRuleSection" useGeneratedKeys="true">
    insert into price_adjustment_rule_section (start_price, end_price, fluctuation_value, 
      creater, creat_time)
    values (#{startPrice,jdbcType=DECIMAL}, #{endPrice,jdbcType=DECIMAL}, #{fluctuationValue,jdbcType=DECIMAL}, 
      #{creater,jdbcType=INTEGER}, #{creatTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PriceAdjustmentRuleSection" useGeneratedKeys="true">
    insert into price_adjustment_rule_section
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="startPrice != null">
        start_price,
      </if>
      <if test="endPrice != null">
        end_price,
      </if>
      <if test="fluctuationValue != null">
        fluctuation_value,
      </if>
      <if test="creater != null">
        creater,
      </if>
      <if test="creatTime != null">
        creat_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="startPrice != null">
        #{startPrice,jdbcType=DECIMAL},
      </if>
      <if test="endPrice != null">
        #{endPrice,jdbcType=DECIMAL},
      </if>
      <if test="fluctuationValue != null">
        #{fluctuationValue,jdbcType=DECIMAL},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=INTEGER},
      </if>
      <if test="creatTime != null">
        #{creatTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PriceAdjustmentRuleSection">
    update price_adjustment_rule_section
    <set>
      <if test="startPrice != null">
        start_price = #{startPrice,jdbcType=DECIMAL},
      </if>
      <if test="endPrice != null">
        end_price = #{endPrice,jdbcType=DECIMAL},
      </if>
      <if test="fluctuationValue != null">
        fluctuation_value = #{fluctuationValue,jdbcType=DECIMAL},
      </if>
      <if test="creater != null">
        creater = #{creater,jdbcType=INTEGER},
      </if>
      <if test="creatTime != null">
        creat_time = #{creatTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PriceAdjustmentRuleSection">
    update price_adjustment_rule_section
    set start_price = #{startPrice,jdbcType=DECIMAL},
      end_price = #{endPrice,jdbcType=DECIMAL},
      fluctuation_value = #{fluctuationValue,jdbcType=DECIMAL},
      creater = #{creater,jdbcType=INTEGER},
      creat_time = #{creatTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>