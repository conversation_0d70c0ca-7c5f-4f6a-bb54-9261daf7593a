<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ActivityBlackAndWhiteListMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.ActivityBlackAndWhiteList">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, sku, warehouse_no, warehouse_name, area_name, area_no, sort, type, creator, create_time, 
    update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from activity_black_and_white_list
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from activity_black_and_white_list
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.ActivityBlackAndWhiteList">
    insert into activity_black_and_white_list (id, sku, warehouse_no, 
      warehouse_name, area_name, area_no, 
      sort, type, creator, 
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{warehouseNo,jdbcType=INTEGER},
      #{warehouseName,jdbcType=VARCHAR}, #{areaName,jdbcType=VARCHAR}, #{areaNo,jdbcType=INTEGER}, 
      #{sort,jdbcType=INTEGER}, #{type,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.ActivityBlackAndWhiteList">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into activity_black_and_white_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.ActivityBlackAndWhiteList">
    update activity_black_and_white_list
    <set>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.ActivityBlackAndWhiteList">
    update activity_black_and_white_list
    set sku = #{sku,jdbcType=VARCHAR},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      area_name = #{areaName,jdbcType=VARCHAR},
      area_no = #{areaNo,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      type = #{type,jdbcType=TINYINT},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="page" parameterType="net.summerfarm.model.input.ActivityBlackAndWhiteListPageQuery" resultType="net.summerfarm.model.DTO.market.ActivityBlackAndWhiteListDTO">
    select
    ab.id, ab.sku, ab.warehouse_no warehouseNo, ab.warehouse_name warehouseName, ab.area_name areaName, ab.area_no areaNo, ab.sort, ab.type, ab.creator
    from activity_black_and_white_list ab
    <where>
      <if test="warehouseName != null">
        and ab.`warehouse_name` like CONCAT(#{warehouseName,jdbcType=VARCHAR},'%')
      </if>
      <if test="sku != null">
        and ab.`sku` like CONCAT(#{sku,jdbcType=VARCHAR},'%')
      </if>
      <if test="areaName != null">
        and ab.`area_name` like CONCAT(#{areaName,jdbcType=VARCHAR},'%')
      </if>
      <if test="type != null">
        and ab.`type` = #{type,jdbcType=INTEGER}
      </if>
      <if test="warehouseNo != null">
        and ab.`warehouse_no` = #{warehouseNo,jdbcType=INTEGER}
      </if>
      <if test="areaNo != null">
        and ab.`area_no` = #{areaNo,jdbcType=INTEGER}
      </if>
    </where>
    order by ab.id DESC
   </select>

  <delete id="batchDelete" parameterType="java.util.List">
    delete from activity_black_and_white_list
    where id in
      <foreach collection="ids" close=")" open="(" separator="," item="id">
        #{id}
      </foreach>
  </delete>

  <select id="checkRepeat" parameterType="net.summerfarm.model.domain.ActivityBlackAndWhiteList" resultType="java.lang.Integer">
    select count(0) from activity_black_and_white_list
    <where>
      <if test="sku != null">
        and `sku` = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="warehouseNo != null">
        and `warehouse_no` = #{warehouseNo,jdbcType=INTEGER}
      </if>
      <if test="areaNo != null">
        and `area_no` = #{areaNo,jdbcType=INTEGER}
      </if>
      <if test="sort != null">
        and `sort` = #{sort,jdbcType=INTEGER}
      </if>
      <if test="type != null">
        and `type` = #{type,jdbcType=INTEGER}
      </if>
    </where>
  </select>
</mapper>