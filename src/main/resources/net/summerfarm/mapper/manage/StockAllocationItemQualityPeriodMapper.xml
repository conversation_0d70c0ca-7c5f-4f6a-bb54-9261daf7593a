<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.StockAllocationItemQualityPeriodMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.StockAllocationItemQualityPeriod">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="stock_allocation_item_id" jdbcType="BIGINT" property="stockAllocationItemId"/>
        <result column="produce_period_start_time" jdbcType="DATE" property="producePeriodStartTime"/>
        <result column="produce_period_end_time" jdbcType="DATE" property="producePeriodEndTime"/>
        <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, stock_allocation_item_id, produce_period_start_time, produce_period_end_time,
    creator_id, creator_name, updater_id, updater_name, create_time, update_time, is_delete
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stock_allocation_item_quality_period
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="queryAllByStockAllocationItemId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stock_allocation_item_quality_period
        where is_delete=0
        <if test="list != null and list.size>0">
            AND stock_allocation_item_id IN
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <insert id="insert" parameterType="net.summerfarm.model.domain.StockAllocationItemQualityPeriod">
        insert into stock_allocation_item_quality_period (id, stock_allocation_item_id, produce_period_start_time,
                                                          produce_period_end_time, creator_id, creator_name,
                                                          updater_id, updater_name, create_time,
                                                          update_time, is_delete)
        values (#{id,jdbcType=BIGINT}, #{stockAllocationItemId,jdbcType=BIGINT},
                #{producePeriodStartTime,jdbcType=DATE},
                #{producePeriodEndTime,jdbcType=DATE}, #{creatorId,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
                #{updaterId,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" parameterType="net.summerfarm.model.domain.StockAllocationItemQualityPeriod">
        insert into stock_allocation_item_quality_period
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="stockAllocationItemId != null">
                stock_allocation_item_id,
            </if>
            <if test="producePeriodStartTime != null">
                produce_period_start_time,
            </if>
            <if test="producePeriodEndTime != null">
                produce_period_end_time,
            </if>
            <if test="tenantId !=null">
                tenant_id,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="creatorName != null">
                creator_name,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updaterName != null">
                updater_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="stockAllocationItemId != null">
                #{stockAllocationItemId,jdbcType=BIGINT},
            </if>
            <if test="producePeriodStartTime != null">
                #{producePeriodStartTime,jdbcType=DATE},
            </if>
            <if test="producePeriodEndTime != null">
                #{producePeriodEndTime,jdbcType=DATE},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="creatorId != null">
                #{creatorId,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="net.summerfarm.model.domain.StockAllocationItemQualityPeriod">
        update stock_allocation_item_quality_period
        <set>
            <if test="stockAllocationItemId != null">
                stock_allocation_item_id = #{stockAllocationItemId,jdbcType=BIGINT},
            </if>
            <if test="producePeriodStartTime != null">
                produce_period_start_time = #{producePeriodStartTime,jdbcType=DATE},
            </if>
            <if test="producePeriodEndTime != null">
                produce_period_end_time = #{producePeriodEndTime,jdbcType=DATE},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                creator_name = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.StockAllocationItemQualityPeriod">
        update stock_allocation_item_quality_period
        set stock_allocation_item_id  = #{stockAllocationItemId,jdbcType=BIGINT},
            produce_period_start_time = #{producePeriodStartTime,jdbcType=DATE},
            produce_period_end_time   = #{producePeriodEndTime,jdbcType=DATE},
            creator_id                = #{creatorId,jdbcType=INTEGER},
            creator_name              = #{creatorName,jdbcType=VARCHAR},
            updater_id                = #{updaterId,jdbcType=INTEGER},
            updater_name              = #{updaterName,jdbcType=VARCHAR},
            create_time               = #{createTime,jdbcType=TIMESTAMP},
            update_time               = #{updateTime,jdbcType=TIMESTAMP},
            is_delete                 = #{isDelete,jdbcType=TINYINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>