<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.CardRuleMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CardRule">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="card_id" property="cardId" jdbcType="INTEGER"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER" />
        <result column="merchant_type" property="merchantType" jdbcType="TINYINT"/>
        <result column="add_time" property="addTime"/>
    </resultMap>

    <sql id="BaseColumn">
        id,card_id,start_time,end_time,area_no,merchant_type,add_time
    </sql>

    <insert id="insert" parameterType="net.summerfarm.model.domain.CardRule">
        INSERT INTO card_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cardId != null">
                card_id,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="merchantType != null">
                merchant_type,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cardId != null">
                #{cardId},
            </if>
            <if test="startTime != null">
                #{startTime} ,
            </if>
            <if test="endTime != null">
                #{endTime} ,
            </if>
            <if test="areaNo != null">
                #{areaNo} ,
            </if>
            <if test="merchantType != null">
                #{merchantType} ,
            </if>
            <if test="addTime != null">
                #{addTime} ,
            </if>
        </trim>
    </insert>

    <select id="selectVO" parameterType="net.summerfarm.model.vo.CardRuleVO"
            resultType="net.summerfarm.model.vo.CardRuleVO">
        SELECT cr.id,cr.card_id cardId,cr.start_time startTime,cr.end_time endTime,cr.area_no areaNo,cr.merchant_type merchantType,
	      cr.add_time addTime,c.`name` cardName,c.card_type cardType,c.money,c.type,c.vaild_date vaildDate,c.vaild_time vaildTime
        FROM card_rule cr
        INNER JOIN card c ON cr.card_id = c.id
        <where>
            <if test="cardName != null">
                AND c.name LIKE concat('%',#{cardName},'%')
            </if>
            <if test="areaNo != null">
                AND cr.area_no = #{areaNo}
            </if>
            <if test="status != null">
                <if test="status == 0">
                    AND cr.start_time <![CDATA[>]]> NOW()
                </if>
                <if test="status == 1">
                    AND cr.start_time <![CDATA[<=]]> NOW()
                    AND cr.end_time <![CDATA[>=]]> NOW()
                </if>
                <if test="status == 2">
                    AND cr.end_time <![CDATA[<]]> NOW()
                </if>
            </if>
        </where>
    </select>

    <update id="update" parameterType="net.summerfarm.model.domain.CardRule">
        UPDATE card_rule
        <set>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM card_rule
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectList" parameterType="net.summerfarm.model.domain.CardRule"
            resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM card_rule
        WHERE
        (
        ( start_time <![CDATA[>=]]> #{startTime} AND start_time <![CDATA[<=]]> #{endTime}  ) OR
        ( end_time <![CDATA[>=]]> #{startTime}  AND  end_time <![CDATA[<=]]> #{endTime} ) OR
        (start_time <![CDATA[<=]]> #{startTime}  AND end_time <![CDATA[>=]]> #{endTime} )
        )
        <if test="id != null">
            AND id != #{id}
        </if>
        <if test="areaNo != null">
            AND area_no = #{areaNo}
        </if>
        <if test="merchantType != null">
            AND merchant_type = #{merchantType}
        </if>
    </select>

</mapper>