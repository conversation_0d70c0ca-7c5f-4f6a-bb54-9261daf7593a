<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.BiOnSaleTimeMapper">
    <insert id="insert" parameterType="net.summerfarm.model.domain.BiOnSaleTime">
      INSERT INTO bi_on_sale_time
      <trim prefix="(" suffix=")" suffixOverrides=",">
          <if test="sku != null">
              sku,
          </if>
          <if test="areaNo != null">
              area_no,
          </if>
          <if test="onSaleTime != null">
              on_sale_time,
          </if>
          <if test="result != null">
              result,
          </if>
          <if test="addDate != null">
              add_date,
          </if>
      </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sku != null">
                #{sku} ,
            </if>
            <if test="areaNo != null">
                #{areaNo} ,
            </if>
            <if test="onSaleTime != null">
                #{onSaleTime} ,
            </if>
            <if test="result != null">
                #{result} ,
            </if>
            <if test="addDate != null">
                #{addDate} ,
            </if>
        </trim>
    </insert>

    <select id="selectList" parameterType="net.summerfarm.model.domain.BiOnSaleTime"
            resultType="net.summerfarm.model.domain.BiOnSaleTime">
        SELECT id, sku, area_no areaNo, on_sale_time onSaleTime, result, add_date addDate
        FROM bi_on_sale_time
        <where>
            <if test="addDate != null">
                AND add_date = #{addDate}
            </if>
        </where>
    </select>


</mapper>