<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceOperatorLogMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceOperatorLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="additional_id" jdbcType="BIGINT" property="additionalId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="personnel_type" jdbcType="INTEGER" property="personnelType" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_time" jdbcType="TIMESTAMP" property="operatorTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operation_results" jdbcType="INTEGER" property="operationResults" />
  </resultMap>
  <sql id="Base_Column_List">
    id, additional_id, `type`, `status`, personnel_type, `operator`, operator_id, operator_time, 
    update_time,operation_results
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_operator_log
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectById" parameterType="net.summerfarm.model.domain.FinanceOperatorLog" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_operator_log
    <where>
      <if test=" id != null">
        and id = #{id}
      </if>
      <if test=" additionalId != null">
        and additional_id = #{additionalId}
      </if>
      <if test=" type != null">
        and `type` = #{type}
      </if>
      <if test=" status != null">
        and status = #{status}
      </if>
      <if test=" personnelType != null">
        and personnel_type = #{personnelType}
      </if>
      <if test=" operator != null">
        and operator = #{operator}
      </if>
      <if test=" operatorId != null">
        and operator_id = #{operatorId}
      </if>
      <if test=" operationResults != null">
        and operation_results = #{operationResults}
      </if>
    </where>
  </select>

  <select id="selectByAdvance" parameterType="net.summerfarm.model.domain.FinanceOperatorLog" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_operator_log
    <where>
      <if test=" id != null">
        and id = #{id}
      </if>
      <if test=" additionalId != null">
        and additional_id = #{additionalId}
      </if>
      <if test=" type != null">
        and `type` = #{type}
      </if>
      <if test=" status != null">
        and status = #{status}
      </if>
      <if test=" personnelType != null">
        and personnel_type = #{personnelType}
      </if>
      <if test=" operator != null">
        and operator = #{operator}
      </if>
      <if test=" operatorId != null">
        and operator_id = #{operatorId}
      </if>
      <if test=" operationResults != null">
        and operation_results = #{operationResults}
      </if>
    </where>
    order by update_time asc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_operator_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceOperatorLog" useGeneratedKeys="true">
    insert into finance_operator_log (additional_id, `type`, `status`, 
      personnel_type, `operator`, operator_id, 
      operator_time, update_time)
    values (#{additionalId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{personnelType,jdbcType=INTEGER}, #{operator,jdbcType=VARCHAR}, #{operatorId,jdbcType=INTEGER}, 
      #{operatorTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceOperatorLog" useGeneratedKeys="true">
    insert into finance_operator_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="additionalId != null">
        additional_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="personnelType != null">
        personnel_type,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorTime != null">
        operator_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="operationResults != null">
        operation_results,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="additionalId != null">
        #{additionalId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="personnelType != null">
        #{personnelType,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorTime != null">
        #{operatorTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationResults != null">
        #{operationResults},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceOperatorLog">
    update finance_operator_log
    <set>
      <if test="additionalId != null">
        additional_id = #{additionalId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="personnelType != null">
        personnel_type = #{personnelType,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorTime != null">
        operator_time = #{operatorTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceOperatorLog">
    update finance_operator_log
    set additional_id = #{additionalId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      personnel_type = #{personnelType,jdbcType=INTEGER},
      `operator` = #{operator,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=INTEGER},
      operator_time = #{operatorTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>