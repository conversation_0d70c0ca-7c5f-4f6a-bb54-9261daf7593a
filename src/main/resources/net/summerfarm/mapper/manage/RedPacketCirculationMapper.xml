<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.RedPacketCirculationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.RedPacketCirculation">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `type`, `name`, m_id, money, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from red_packet_circulation
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectTotalMoney" resultType="net.summerfarm.model.domain.RedPacketCirculation">
    select m_id as mId,sum(money) as money from red_packet_circulation group by m_id
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from red_packet_circulation
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.RedPacketCirculation" useGeneratedKeys="true">
    insert into red_packet_circulation (`type`, `name`, m_id, 
      money, creator, create_time
      )
    values (#{type,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{mId,jdbcType=BIGINT}, 
      #{money,jdbcType=DECIMAL}, #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.RedPacketCirculation" useGeneratedKeys="true">
    insert into red_packet_circulation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        `type`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.RedPacketCirculation">
    update red_packet_circulation
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.RedPacketCirculation">
    update red_packet_circulation
    set `type` = #{type,jdbcType=INTEGER},
      `name` = #{name,jdbcType=VARCHAR},
      m_id = #{mId,jdbcType=BIGINT},
      money = #{money,jdbcType=DECIMAL},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>