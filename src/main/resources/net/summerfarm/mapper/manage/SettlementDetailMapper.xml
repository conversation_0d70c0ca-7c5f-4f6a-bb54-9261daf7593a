<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.SettlementDetailMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.SettlementDetail" >
    <result column="id" property="id" jdbcType="INTEGER" />
    <result column="settlement_id" property="settlementId" jdbcType="INTEGER" />
    <result column="purchase_no" property="purchaseNo" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="purchase_plan_id" property="purchasePlanId" jdbcType="INTEGER" />
    <result column="sku_amount" property="skuAmount" jdbcType="DECIMAL" />
  </resultMap>
  <insert id="insert" parameterType="net.summerfarm.model.domain.SettlementDetail" >
    insert into settlement_detail (id, settlement_id, purchase_no,
      type, purchase_plan_id, sku_amount
      )
    values (#{id,jdbcType=INTEGER}, #{settlementId,jdbcType=INTEGER}, #{purchaseNo,jdbcType=VARCHAR},
      #{type,jdbcType=INTEGER}, #{purchasePlanId,jdbcType=INTEGER}, #{skuAmount,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.SettlementDetail" >
    insert into settlement_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="settlementId != null" >
        settlement_id,
      </if>
      <if test="purchaseNo != null" >
        purchase_no,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="purchasePlanId != null" >
        purchase_plan_id,
      </if>
      <if test="skuAmount != null" >
        sku_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="settlementId != null" >
        #{settlementId,jdbcType=INTEGER},
      </if>
      <if test="purchaseNo != null" >
        #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="purchasePlanId != null" >
        #{purchasePlanId,jdbcType=INTEGER},
      </if>
      <if test="skuAmount != null" >
        #{skuAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="select" parameterType="net.summerfarm.model.input.SettlementQuery" resultType="net.summerfarm.model.vo.SettlementPaymentRecordVO">
    select s.id,
       spr.settlement_count settlementCount,
       spr.status,
       s.supplier_name      supplierName,
       spr.amount       amount,
       spr.expected_time    expectedTime,
       spr.pay_time         payTime,
       spr.creator,
       spr.remark,
       p.area_no            storeNo,
       spr.auditor,
       spr.approver,
       spr.payer,
       spr.payment_voucher paymentVoucher
    from settlement_payment_record spr
             right join settlement s on spr.settlement_id = s.id
             left join settlement_detail sd on sd.settlement_id = s.id
             left join purchases_plan pp on pp.id = sd.purchase_plan_id and pp.plan_status = 1
             left join purchases p on pp.purchase_no = p.purchase_no
    <where>
        <if test="status != null">
            and spr.status = #{status}
        </if>
        <if test="storeNo != null">
            and p.area_no = #{storeNo}
        </if>
        <if test="settlementId != null">
            and s.id = #{settlementId}
        </if>
        <if test="supplierId != null">
            and s.supplier_id = #{supplierId}
        </if>
        <if test="purchaseNo != null">
            and p.purchase_no = #{purchaseNo}
        </if>
        <if test="creator != null">
            and s.creator like concat('%',#{creator},'%')
        </if>
        <if test="auditor != null">
            and spr.auditor like concat('%',#{auditor},'%')
        </if>
        <if test="approver != null">
            and spr.approver like concat('%',#{approver},'%')
        </if>
        <if test="payer != null">
            and spr.payer like concat('%',#{payer},'%')
        </if>
        <if test="settlementPayTime != null">
            and spr.pay_time like concat(#{settlementPayTime},'%')
        </if>
    </where>
    group by spr.id
    order by s.id desc, spr.id asc
  </select>
  <select id="selectBySettlementId" parameterType="integer" resultMap="BaseResultMap">
    select id, settlement_id, purchase_no, type, purchase_plan_id, sku_amount
    from settlement_detail where settlement_id = #{settlementId}
  </select>
  <select id="selectByPurchaseNo" parameterType="string" resultType="net.summerfarm.model.vo.SettlementDetailVO">
    select sd.id,
       sd.settlement_id settlementId,
       purchase_no purchaseNo,
       type,
       purchase_plan_id purchasePlanId,
       sku_amount skuAmount,
       spr.status payRecordStatus
    from settlement_detail sd
             left join settlement_payment_record spr on spr.settlement_id = sd.settlement_id
    where sd.purchase_no = #{purchaseNo}
  </select>

    <select id="selectSettlement" resultType="net.summerfarm.model.vo.SettlementDetailVO">
        select sd.id,sd.settlement_id settlementId,sd.purchase_no purchaseNo,sd.purchase_plan_id purchasePlanId
        from settlement_detail sd
        where sd.purchase_no =#{purchaseNo} and sd.purchase_plan_id = #{purchasePlanId} and sd.type = 1
    </select>

    <select id="selectSettleIdByPlanId" resultType="java.lang.Integer">
        select sd.settlement_id
        from settlement_detail sd inner join settlement s on sd.settlement_id = s.id
        where s.status != 4 and sd.purchase_plan_id = #{planId}
    </select>
</mapper>
