<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinancialInvoiceMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinancialInvoice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="invoice_id" jdbcType="BIGINT" property="invoiceId" />
    <result column="invoice_type" jdbcType="TINYINT" property="invoiceType" />
    <result column="amount_money" jdbcType="DECIMAL" property="amountMoney" />
    <result column="invoice_result" jdbcType="TINYINT" property="invoiceResult" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="handler_id" jdbcType="INTEGER" property="handlerId" />
    <result column="creator_remark" jdbcType="VARCHAR" property="creatorRemark" />
    <result column="handler_remark" jdbcType="VARCHAR" property="handlerRemark" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="express" jdbcType="VARCHAR" property="express" />
    <result column="finance_order_id" jdbcType="BIGINT" property="financeOrderId" />
    <result column="invoice_status" jdbcType="TINYINT" property="invoiceStatus" />
    <result column="serial_number"  property="serialNumber" />
    <result column="pdf_url" jdbcType="VARCHAR" property="pdfUrl" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="duty_free_good" jdbcType="TINYINT" property="dutyFreeGood" />
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="invoice_number" jdbcType="VARCHAR" property="invoiceNumber" />
    <result column="belong_type" jdbcType="TINYINT" property="belongType" />
    <result column="mail_address" jdbcType="VARCHAR" property="mailAddress" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="invoice_issue_time" jdbcType="TIMESTAMP" property="invoiceIssueTime" />
    <result column="redo_invoice_id" jdbcType="BIGINT" property="redoInvoiceId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="selling_entity_name" jdbcType="VARCHAR" property="sellingEntityName" />

  </resultMap>
  <resultMap id="VOResultMap" type="net.summerfarm.model.vo.FinancialInvoiceVO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="invoice_id" jdbcType="BIGINT" property="invoiceId" />
    <result column="invoice_type" jdbcType="TINYINT" property="invoiceType" />
    <result column="amount_money" jdbcType="DECIMAL" property="amountMoney" />
    <result column="invoice_result" jdbcType="TINYINT" property="invoiceResult" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="handler_id" jdbcType="INTEGER" property="handlerId" />
    <result column="creator_remark" jdbcType="VARCHAR" property="creatorRemark" />
    <result column="handler_remark" jdbcType="VARCHAR" property="handlerRemark" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="express" jdbcType="VARCHAR" property="express" />
    <result column="finance_order_id" jdbcType="BIGINT" property="financeOrderId" />
    <result column="invoice_status" jdbcType="TINYINT" property="invoiceStatus" />
    <result column="serial_number"  property="serialNumber" />
    <result column="pdf_url" jdbcType="VARCHAR" property="pdfUrl" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="duty_free_good" jdbcType="TINYINT" property="dutyFreeGood" />
    <result column="handle_time"  property="handleTime" />
    <result column="invoice_code" property="invoiceCode" />
    <result column="invoice_number"  property="invoiceNumber" />
    <result column="belong_type" jdbcType="TINYINT" property="belongType" />
    <result column="mail_address" jdbcType="VARCHAR" property="mailAddress" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_item_id" jdbcType="BIGINT" property="orderItemId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
  </resultMap>
  <sql id="Base_Column_List">
    id, invoice_id, invoice_type, amount_money, invoice_result, creator_id, handler_id,
    creator_remark, handler_remark, handle_time, update_time, create_time ,express,finance_order_id,invoice_status,
        serial_number,pdf_url,tax_amount,duty_free_good,invoice_code,invoice_number,mail_address,belong_type,title,creator_name,invoice_issue_time,redo_invoice_id,m_id, selling_entity_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from financial_invoice
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByFinanceOrderId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from financial_invoice
    where finance_order_id = #{financeOrderId} AND invoice_result = 0
  </select>

    <select id="selectByKey" resultType="net.summerfarm.model.vo.FinancialInvoiceVO">
      select fin.id, IFNULL(a3.realName, m.mname) customerName, IFNULL(fin.title,ico.invoice_title) invoiceTitle, ico.tax_number taxNumber,
      fin.invoice_type invoiceType, fin.amount_money amountMoney, fin.invoice_result invoiceResult, fin.create_time createTime,fin.mail_address mailAddress ,fin.belong_type,
      fin.invoice_id invoiceId,fin.invoice_status invoiceStatus,ifnull(a1.realname,fin.creator_name)creatorName,if(fin.amount_money <![CDATA[ < ]]> 0,0,1) invoiceColor,express,fin.creator_remark creatorRemark,
      fin.selling_entity_name sellingEntityName
      FROM financial_invoice  fin
      left join invoice_config ico on fin.invoice_id = ico.id
      left join `admin` a1 on fin.creator_id = a1.admin_id
      left join merchant m on ico.merchant_id = m.m_id
      left join  `admin`a3 on ico.admin_id = a3.admin_id
      <where>
        <if test="id != null">
          AND fin.id = #{id}
        </if>
        <if test="ids != null and ids.size > 0">
          AND fin.id in
          <foreach collection="ids" item="id" index="index" open="("  separator="," close=")">
            #{id}
          </foreach>
        </if>
        <if test="invoiceTitle != null">
          AND (ico.invoice_title LIKE CONCAT('%', #{invoiceTitle}, '%')
          or fin.title LIKE CONCAT('%', #{invoiceTitle}, '%'))
        </if>
        <if test="customerName != null">
          AND (a3.realname LIKE  CONCAT('%', #{customerName}, '%')
          OR  m.mname LIKE CONCAT('%', #{customerName}, '%'))
        </if>
        <if test="invoiceType != null">
          AND fin.invoice_type = #{invoiceType}
        </if>
        <if test="invoiceResult != null">
          AND fin.invoice_result = #{invoiceResult}
        </if>
        <if test="invoiceQueryStartTime != null">
          AND fin.create_time <![CDATA[ >= ]]> #{invoiceQueryStartTime}
        </if>
        <if test="invoiceQueryEndTime != null">
          AND fin.create_time <![CDATA[ <= ]]> #{invoiceQueryEndTime}
        </if>
        <if test="creatorId != null">
          AND fin.creator_id = #{creatorId}
        </if>
        <if test="invoiceStatus != null">
          AND fin.invoice_status  = #{invoiceStatus}
        </if>
        <if test="isExpressNotEmpty != null">
          <choose>
            <when test="isExpressNotEmpty">
              AND fin.express IS NOT NULL AND fin.express != ''
            </when>
            <otherwise>
              AND (fin.express IS NULL OR fin.express = '')
            </otherwise>
          </choose>
        </if>
        <if test="express != null and express != ''">
          AND fin.express=#{express}
        </if>
        <if test="sellingEntityName != null and sellingEntityName != ''">
          AND fin.selling_entity_name= #{sellingEntityName}
        </if>
      </where>
          order by fin.id DESC
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from financial_invoice
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinancialInvoice" useGeneratedKeys="true">
    insert into financial_invoice (invoice_id, invoice_type, amount_money,
      invoice_result, creator_id, handler_id, 
      creator_remark, handler_remark, handle_time,
       mail_address, belong_type,title,
      update_time, create_time)
    values (#{invoiceId,jdbcType=BIGINT}, #{invoiceType,jdbcType=TINYINT}, #{amountMoney,jdbcType=DECIMAL},
      #{invoiceResult,jdbcType=TINYINT}, #{creatorId,jdbcType=BIGINT}, #{handlerId,jdbcType=BIGINT}, 
      #{creatorRemark,jdbcType=VARCHAR}, #{handlerRemark,jdbcType=VARCHAR}, #{handleTime,jdbcType=TIMESTAMP},
      #{mailAddress,jdbcType=VARCHAR}, #{belongType,jdbcType=TINYINT},#{title,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinancialInvoice" useGeneratedKeys="false">
    insert into financial_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="amountMoney != null">
        amount_money,
      </if>
      <if test="invoiceResult != null">
        invoice_result,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="handlerId != null">
        handler_id,
      </if>
      <if test="creatorRemark != null">
        creator_remark,
      </if>
      <if test="handlerRemark != null">
        handler_remark,
      </if>
      <if test="handleTime != null">
        handle_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="financeOrderId != null">
        finance_order_id,
      </if>
      <if test="invoiceStatus != null">
        invoice_status,
      </if>
      <if test="serialNumber != null">
        serial_number,
      </if>
      <if test="pdfUrl != null">
        pdf_url,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="dutyFreeGood != null">
        duty_free_good,
      </if>
      <if test="invoiceCode != null">
        invoice_code,
      </if>
      <if test="invoiceNumber != null">
        invoice_number,
      </if>
      <if test="mailAddress != null">
        mail_address,
      </if>
      <if test="belongType != null">
        belong_type,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="sellingEntityName != null and sellingEntityName != ''">
        selling_entity_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=BIGINT},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="amountMoney != null">
        #{amountMoney,jdbcType=DECIMAL},
      </if>
      <if test="invoiceResult != null">
        #{invoiceResult,jdbcType=TINYINT},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="handlerId != null">
        #{handlerId,jdbcType=BIGINT},
      </if>
      <if test="creatorRemark != null">
        #{creatorRemark,jdbcType=VARCHAR},
      </if>
      <if test="handlerRemark != null">
        #{handlerRemark,jdbcType=VARCHAR},
      </if>
      <if test="handleTime != null">
        #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
        <if test="financeOrderId != null">
        #{financeOrderId},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus},
      </if>
      <if test="serialNumber != null">
        #{serialNumber},
      </if>
      <if test="pdfUrl != null">
        #{pdfUrl},
      </if>
      <if test="taxAmount != null">
        #{taxAmount},
      </if>
      <if test="dutyFreeGood != null">
        #{dutyFreeGood},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode},
      </if>
      <if test="invoiceNumber != null">
        #{invoiceNumber},
      </if>
      <if test="mailAddress != null">
        #{mailAddress},
      </if>
      <if test="belongType != null">
        #{belongType},
      </if>
      <if test="title != null">
        #{title},
      </if>
      <if test="sellingEntityName != null and sellingEntityName != ''">
        #{sellingEntityName},
      </if>
    </trim>

    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>

  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinancialInvoice">
    update financial_invoice
    <set>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=BIGINT},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="amountMoney != null">
        amount_money = #{amountMoney,jdbcType=DECIMAL},
      </if>
      <if test="invoiceResult != null">
        invoice_result = #{invoiceResult,jdbcType=TINYINT},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="handlerId != null">
        handler_id = #{handlerId,jdbcType=BIGINT},
      </if>
      <if test="creatorRemark != null">
        creator_remark = #{creatorRemark,jdbcType=VARCHAR},
      </if>
      <if test="handlerRemark != null">
        handler_remark = #{handlerRemark,jdbcType=VARCHAR},
      </if>
      <if test="handleTime != null">
        handle_time = #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceStatus != null">
        invoice_status = #{invoiceStatus},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber},
      </if>
      <if test="pdfUrl != null">
        pdf_url = #{pdfUrl},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount},
      </if>
      <if test="dutyFreeGood != null">
        duty_free_good = #{dutyFreeGood},
      </if>
      <if test="invoiceCode != null">
        invoice_code = #{invoiceCode},
      </if>
      <if test="invoiceNumber != null">
        invoice_number = #{invoiceNumber},
      </if>
      <if test="mailAddress != null">
        mail_address = #{mailAddress},
      </if>
      <if test="belongType != null">
        belong_type = #{belongType},
      </if>
      <if test="invoiceIssueTime != null">
        invoice_issue_time = #{invoiceIssueTime},
      </if>
      <if test="redoInvoiceId != null">
        redo_invoice_id = #{redoInvoiceId},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName},
      </if>
      <if test="mId != null">
        m_id = #{mId}
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByCancel" parameterType="net.summerfarm.model.vo.FinancialInvoiceVO">
    UPDATE financial_invoice
    SET invoice_result = 9,
        handler_remark = #{handlerRemark},
        update_time = now()
    WHERE id = #{id};
  </update>
  <update id="updateExpress" >
    update financial_invoice
    set express = #{express}
    where id = #{id}
  </update>

  <select id="selectIdByQuery" resultType="long">
    select distinct f.id
    from financial_invoice f
    left join financial_invoice_orderno_relation fi on f.id = fi.financial_invoice_id
    where fi.order_no = #{orderNo}
      <if test="invoiceResult != null">
        and f.invoice_result = #{invoiceResult}
      </if>
      <if test="invoiceStatus != null">
        and f.invoice_status = #{invoiceStatus}
      </if>
    <if test="creatorId != null">
      and f.creator_id = #{creatorId}
    </if>
  </select>

  <select id="selectFinancialInvoiceByOrder"
          resultType="net.summerfarm.model.vo.FinancialInvoiceVO">
    SELECT fi.id id,fi.invoice_id invoiceId, fi.invoice_type invoiceType, fi.amount_money amountMoney, fi.invoice_result invoiceResult, fi.creator_id creatorId,
           fi.creator_remark creatorRemark, fi.express express,
           fi.finance_order_id financeOrderId,fi.invoice_status invoiceStatus,fi.serial_number serialNumber,fi.pdf_url pdfUrl,fi.tax_amount taxAmount,
           fi.duty_free_good dutyFreeGood,fi.handle_time handleTime,
           fi.invoice_code invoiceCode,fi.invoice_number invoiceNumber,fi.create_time createTime,
           fi.mail_address mailAddress, fi.belong_type belongType,
           fio.order_no orderNo,fio.order_item_id orderItemId, fi.invoice_issue_time invoiceIssueTime
    FROM financial_invoice fi
    LEFT JOIN financial_invoice_orderno_relation fio ON fi.id = fio.financial_invoice_id
    <where>
      <if test="orderNo != null">
        AND fio.order_no = #{orderNo}
      </if>
      <if test="orderItemId != null">
        AND fio.order_item_id = #{orderItemId}
      </if>
      <if test="orderNoList !=null and orderNoList.size>0">
        and fio.order_no  in
        <foreach collection="orderNoList" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="orderItemIdList !=null and orderItemIdList.size>0">
        and fio.order_item_id in
        <foreach collection="orderItemIdList" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>
  <select id="selectFinancialInvoiceByQuery" resultType="net.summerfarm.model.domain.FinancialInvoice">
    select
    <include refid="Base_Column_List" />
    from financial_invoice
    <where>
      <if test="invoiceResult != null">
        and invoice_result = #{invoiceResult}
      </if>
      <if test="hasPdfUrl != null and !hasPdfUrl">
        and pdf_url IS NULL
      </if>
    </where>
  </select>


  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from financial_invoice
    <where>
         `id` in <foreach collection="invoiceIds" item="status" open="(" close=")" separator=",">
        #{status}
      </foreach>
    </where>
   order by  id desc
  </select>

  <select id="selectExportInvoice" resultType="net.summerfarm.model.vo.FinanceInvoiceExportVo">
    select fin.id                                   id,
           ico.company_receiver                     companyReceiver,
           ico.link_method                          linkMethod,
           ico.mail_address                         mailAddress,
           ico.company_email                        companyEmail,
           IFNULL(a3.realName, m.mname)             customerName,
           IFNULL(fin.title, ico.invoice_title)     invoiceTitle,
           ico.tax_number                           taxNumber,
           IF(0 > fin.amount_money, '红色', '蓝色') invoiceColor,
           invoice_type invoiceType,
           fin.amount_money                         amountMoney,
           fin.amount_money - fin.tax_amount        amountExcludingTax,
           fin.tax_amount                           taxAmount,
           fin.invoice_code                         invoiceCode,
           fin.invoice_number                       invoiceNumber,
           fin.invoice_result                       invoiceResult,
           fin.create_time                          createTime,
           IFNULL(fin.creator_name, a1.realname)    creator,
           fin.selling_entity_name sellingEntityName
    FROM financial_invoice fin
           left join invoice_config ico on fin.invoice_id = ico.id
           left join `admin` a1 on fin.creator_id = a1.admin_id
           left join merchant m on ico.merchant_id = m.m_id
           left join `admin` a3 on ico.admin_id = a3.admin_id
    <where>
      <if test="id != null">
        AND fin.id = #{id}
      </if>
      <if test="ids != null and ids.size > 0">
        AND fin.id in
        <foreach collection="ids" item="id" index="index" open="("  separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="invoiceTitle != null">
        AND (ico.invoice_title LIKE CONCAT('%', #{invoiceTitle}, '%')
        or fin.title LIKE CONCAT('%', #{invoiceTitle}, '%'))
      </if>
      <if test="customerName != null">
        AND (a3.realname LIKE  CONCAT('%', #{customerName}, '%'
        OR  m.mname LIKE CONCAT('%', #{customerName}, '%')))
      </if>
      <if test="invoiceType != null">
        AND fin.invoice_type = #{invoiceType}
      </if>
      <if test="invoiceResult != null">
        AND fin.invoice_result = #{invoiceResult}
      </if>
      <if test="invoiceQueryStartTime != null">
        AND fin.create_time <![CDATA[ >= ]]> #{invoiceQueryStartTime}
      </if>
      <if test="invoiceQueryEndTime != null">
        AND fin.create_time <![CDATA[ <= ]]> #{invoiceQueryEndTime}
      </if>
      <if test="invoiceStatus != null">
        AND fin.invoice_status  = #{invoiceStatus}
      </if>
      <if test="isExpressNotEmpty != null">
        <choose>
          <when test="isExpressNotEmpty">
            AND fin.express IS NOT NULL AND fin.express != ''
          </when>
          <otherwise>
            AND (fin.express IS NULL OR fin.express = '')
          </otherwise>
        </choose>
      </if>
      <if test="express != null and express != ''">
        AND fin.express=#{express}
      </if>
    </where>
    order by fin.id DESC
  </select>

  <select id="selectEmptyExpressByIds" resultType="java.lang.Long">
    select id
    from financial_invoice where (express is null or express = '') and id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>
  <update id="updateBatchById">
    update financial_invoice
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="express =case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          <if test="item.express != null">
            when id = #{item.id}
              then #{item.express}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
      #{item.id}
    </foreach>
  </update>

  <select id="selectRedoInvoices" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from financial_invoice
    <where>
      redo_invoice_id  <![CDATA[ = ]]> -1 and amount_money <![CDATA[ > ]]> 0
      <if test="invoiceIds != null and invoiceIds.size() != 0">
        and id in
        <foreach collection="invoiceIds" item="id" index="index" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="invoiceType != null">
        and invoice_type = #{invoiceType}
      </if>
      <if test="dutyFreeGood != null">
        and duty_free_good = #{dutyFreeGood}
      </if>
      <if test="invoiceResult != null">
        and invoice_result = #{invoiceResult}
      </if>
      <if test="startTime != null and endTime != null">
        AND create_time <![CDATA[>=]]> #{startTime}
        AND create_time <![CDATA[<]]> #{endTime}
      </if>
      <if test="failReason != null and failReason != ''">
        and handler_remark = #{failReason}
      </if>
      <if test="supportBillInvoice == null">
        and finance_order_id is null
      </if>
    </where>
    limit #{redoLimit}
  </select>

  <select id="selectByBillNo" resultMap="BaseResultMap">
    select i.amount_money, i.create_time
    from financial_invoice i
    inner join finance_accounting_period_order o on i.finance_order_id = o.id
    where o.bill_number = #{billNo} and i.invoice_result = 1 and i.invoice_status = 0
    order by i.id desc
    limit 1
  </select>
</mapper>