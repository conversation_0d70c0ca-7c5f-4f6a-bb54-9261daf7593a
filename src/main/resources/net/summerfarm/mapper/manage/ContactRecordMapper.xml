<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ContactRecordMapper">


    <insert id="insertContactRecord" parameterType="net.summerfarm.model.domain.ContactRecord">

      insert into  contact_record (add_time,update_time,contact_adjust_id,old_poi_note,old_province,old_city,old_area,old_address,old_house_number)
      value (now(),now(),#{contactAdjustId},#{oldPoiNote},#{oldProvince},#{oldCity},#{oldArea},#{oldAddress},#{oldHouseNumber})
    </insert>
</mapper>