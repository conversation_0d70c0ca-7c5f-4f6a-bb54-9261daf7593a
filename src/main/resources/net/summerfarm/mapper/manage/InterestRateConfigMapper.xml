<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.InterestRateConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.InterestRateConfig">
    <result column="id" property="id" jdbcType="INTEGER"/>
    <result column="sku" property="sku" jdbcType="VARCHAR"/>
    <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
    <result column="interest_rate" property="interestRate" jdbcType="DECIMAL"/>
    <result column="auto_flag" property="autoFlag" jdbcType="INTEGER"/>
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    <result column="create_admin_name" property="createAdminName" jdbcType="VARCHAR"/>
  </resultMap>
  <insert id="insert" parameterType="net.summerfarm.model.domain.InterestRateConfig">
    insert into interest_rate_config (id, sku, area_no, 
      interest_rate, auto_flag, update_time, 
      create_time,create_admin_name)
    values (#{id,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{areaNo,jdbcType=INTEGER}, 
      #{interestRate,jdbcType=DECIMAL}, #{autoFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP},#{createAdminName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.InterestRateConfig">
    insert into interest_rate_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="interestRate != null">
        interest_rate,
      </if>
      <if test="autoFlag != null">
        auto_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createAdminName != null">
        create_admin_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="interestRate != null">
        #{interestRate,jdbcType=DECIMAL},
      </if>
      <if test="autoFlag != null">
        #{autoFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createAdminName != null">
        #{createAdminName,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 自定义SQL -->
  <select id="selectByAreaNoAndSku" resultMap="BaseResultMap">
    select id,
       sku,
       area_no,
       interest_rate,
       auto_flag,
       update_time,
       create_time,
       create_admin_name
    from interest_rate_config
    where area_no = #{areaNo}
      and sku = #{sku}
    limit 1
  </select>
  <update id="updateConfigById" parameterType="net.summerfarm.model.domain.InterestRateConfig">
    update interest_rate_config
    set interest_rate = #{interestRate},
        auto_flag = #{autoFlag},
        update_time = #{updateTime},
        create_admin_name = #{createAdminName}
    where id = #{id}
  </update>
  <select id="select" resultType="net.summerfarm.model.vo.InterestRateConfigVO">
    select
    la.large_area_name   storeName,
    a.area_name              areaName,
    i.sku                  sku,
    p.pd_name                pdName,
    i.weight                 weight,
    ar.on_sale               onSale,
    ar.show                  `show`,
    a.area_no                areaNo,
    la.large_area_no            storeNo,
    irc.update_time          updateTime,
    irc.create_time          createTime,
    ifnull(irc.auto_flag, 0) autoFlag,
    irc.interest_rate        interestRate,
    irc.create_admin_name    createAdminName,
    i.ext_type extType
    from area_sku ar
    inner join inventory i on ar.sku = i.sku
    inner join products p on i.pd_id = p.pd_id
    inner join area a on ar.area_no = a.area_no
    left join large_area la on la.large_area_no = a.large_area_no
    left join interest_rate_config irc on a.area_no = irc.area_no and irc.sku = ar.sku
    <where>
      <if test="storeNo != null">
        and la.large_area_no = #{storeNo}
      </if>
      <if test="areaNo != null">
        and a.area_no = #{areaNo}
      </if>
      <if test="pdName != null and pdName != ''">
        and p.pd_name like concat('%', #{pdName}, '%')
      </if>
      <if test="sku != null">
        and ar.sku = #{sku}
      </if>
      <if test="onSale != null">
        and ar.on_sale = #{onSale}
      </if>
    </where>
    order by irc.id
  </select>
  <select id="selectBySku" resultType="net.summerfarm.model.vo.InterestRateConfigVO">
    select a.area_no areaNo,
       a.large_area_no   storeNo,
       a.area_name       areaName,
       ar.on_sale        onSale,
       ar.`show`         `show`,
       irc.interest_rate interestRate,
       irc.auto_flag     autoFlag,
       irc.create_time   createTime,
       irc.create_admin_name createAdminName
    from interest_rate_config irc
             left join area a on irc.area_no = a.area_no
             left join area_sku ar on irc.area_no = ar.area_no and irc.sku = ar.sku
    <where>
      <if test="sku != null">
        and irc.sku = #{sku}
      </if>
      <if test="storeNo != null">
        and a.large_area_no = #{storeNo}
      </if>
    </where>
  </select>
  <select id="selectDefaultBySku" resultType="net.summerfarm.model.vo.InterestRateConfigVO">
    select a.area_no   areaNo,
       a.large_area_no  storeNo,
       a.area_name areaName,
       ak.on_sale  onSale,
       ak.`show`   `show`,
       null        interestRate,
       0           autoFlag
    from area a
        inner join area_sku ak on a.area_no = ak.area_no
    where  sku = #{sku} and a.large_area_no = #{storeNo}
  </select>
  <select id="selectUnAudit" resultType="net.summerfarm.model.vo.InterestRateConfigVO">
    select irr.id id,
    wlc.store_name storeName,
    a.area_no areaNo,
    wlc.store_np storeNo,
    wsc.type         areaType,
    a.area_name areaName,
    irr.sku sku,
    p.pd_name pdName,
    i.weight weight,
    irr.interest_rate_new interestRate,
    irr.auto_flag_new autoFlag,
    irr.create_time createTime,
    irr.create_admin_name createAdminName,
    ar.on_sale onSale,
    ar.show `show`
    from interest_rate_record irr
    left join area a on irr.area_no = a.area_no
    left join inventory i on irr.sku = i.sku
    left join products p on i.pd_id = p.pd_id
    left join area_sku ar on a.area_no = ar.area_no and i.sku = ar.sku
    <where>
      irr.status = 0
      <if test="storeNo != null">
        and a.large_area_no = #{storeNo}
      </if>
      <if test="areaNo != null">
        and a.area_no = #{areaNo}
      </if>
      <if test="pdName != null and pdName != ''">
        and p.pd_name like concat('%', #{pdName}, '%')
      </if>
      <if test="sku != null">
        and irr.sku = #{sku}
      </if>
      <if test="onSale != null">
        and ar.on_sale = #{onSale}
      </if>
    </where>
    order by irr.id desc
  </select>
  <select id="selectConfig" resultMap="BaseResultMap">
    select irc.id,
           irc.sku,
           irc.area_no,
           irc.interest_rate,
           irc.auto_flag,
           irc.update_time,
           irc.create_time,
           irc.create_admin_name
    from interest_rate_config irc
        left join area a on irc.area_no = a.area_no
           left join (
            select area_no,store_no  from fence
            where status = 0
            group by area_no,store_no
            ) f on f.area_no = irc.area_no
           left join warehouse_inventory_mapping wim on f.store_no = wim.store_no and irc.sku = wim.sku
    where wim.warehouse_no = #{warehouseNo} and irc.sku = #{sku} and a.status =1
  </select>
  <select id="selectByAreaNo" resultMap="BaseResultMap">
     select id,
       sku,
       area_no,
       interest_rate,
       auto_flag,
       update_time,
       create_time,
       create_admin_name
     from interest_rate_config where area_no = #{areaNo}
  </select>
</mapper>