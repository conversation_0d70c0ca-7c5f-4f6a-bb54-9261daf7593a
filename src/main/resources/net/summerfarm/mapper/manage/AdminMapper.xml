<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.AdminMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.Admin">
        <id column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="login_fail_times" property="loginFailTimes" jdbcType="INTEGER"/>
        <result column="is_disabled" property="isDisabled" jdbcType="BIT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
        <result column="realname" property="realname" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="BIT"/>
        <result column="department" property="department" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="kp" property="kp" jdbcType="VARCHAR"/>
        <result column="saler_id" property="salerId" jdbcType="INTEGER"/>
        <result column="saler_name" property="salerName" jdbcType="VARCHAR"/>
        <result column="contract" property="contract" jdbcType="VARCHAR"/>
        <result column="contract_method" property="contractMethod" jdbcType="VARCHAR"/>
        <result column="name_remakes" property="nameRemakes" jdbcType="VARCHAR"/>
        <result column="operate_id" property="operateId" jdbcType="INTEGER"/>
        <result column="close_order_type" property="closeOrderType"/>
        <result column="cooperation_stage" property="cooperationStage"/>
        <result column="close_order_time" property="closeOrderTime"/>
        <result column="sku_sorting" property="skuSorting"/>
        <result column="update_close_order_time" property="updateCloseOrderTime"/>
        <result column="low_price_remainder" property="lowPriceRemainder"/>
        <result column="not_included_area" property="notIncludedArea"/>
        <result column="base_user_id" property="baseUserId"/>
        <result column="major_cycle" property="majorCycle"/>

    </resultMap>


    <resultMap id="AdminWithPurviews" type="net.summerfarm.model.vo.AdminVO">
        <id column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="login_fail_times" property="loginFailTimes" jdbcType="INTEGER"/>
        <result column="is_disabled" property="isDisabled" jdbcType="BIT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
        <result column="realname" property="realname" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="BIT"/>
        <result column="department" property="department" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="admin_switch" property="adminSwitch" jdbcType="INTEGER"/>
        <result column="bill_to_pay" property="billToPay" jdbcType="INTEGER"/>
        <collection property="roleVOs" resultMap="net.summerfarm.mapper.manage.RoleMapper.WithPurviews"/>
    </resultMap>

    <resultMap id="AdminVOMap" type="net.summerfarm.model.vo.AdminVO">
        <id column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="realname" property="realname" jdbcType="VARCHAR"/>
        <collection property="merchantVOS" ofType="net.summerfarm.model.vo.MerchantVO">
            <id column="m_id" property="mId" jdbcType="BIGINT"/>
            <result column="adminRealname" property="adminRealname" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        admin_id
        , create_time, login_fail_times, is_disabled, username, password,
    login_time, realname, gender, department, phone,contract_method, name_remakes,operate_id,close_order_type,
    kp, saler_id, saler_name, contract, cooperation_stage,close_order_time,update_close_order_time,sku_sorting,
    low_price_remainder,not_included_area,base_user_id,major_cycle
    </sql>


    <select id="selectInId" resultType="net.summerfarm.model.domain.Admin">
        SELECT realname, admin_id adminId,username, phone
        FROM admin
        WHERE admin_id in
        <foreach collection="array" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByAdminIds" resultType="net.summerfarm.model.domain.Admin">
        SELECT realname, admin_id adminId,username, phone, is_disabled
        isDisabled
        FROM admin
        WHERE admin_id in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectAll" resultType="net.summerfarm.model.vo.AdminVO">
        select distinct a.admin_id adminId, a.username, a.realname, a.phone, a.name_remakes nameRemakes, is_disabled
        isDisabled
        FROM admin a
        WHERE is_disabled=0
        <if test="baseUserIds != null and baseUserIds.size!=0">
            AND a.base_user_id in
            <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>


    <select id="selectMajor" resultType="net.summerfarm.model.vo.AdminVos">
        select a.admin_id adminId, a.realname name, a.contract_method contractMethod, a.name_remakes nameRemarks
        from admin a
        where is_disabled = 0
        <if test="baseUserIds != null and baseUserIds.size!=0">
            AND a.base_user_id in
            <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectSkuByAdminIdStr" resultType="java.lang.String">
        select distinct sku from major_price
        where admin_id in
        <foreach collection="adminIdStr.split(',')" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and now()<![CDATA[ < ]]>invalid_time and now()<![CDATA[ > ]]>valid_time and mall_show=0
    </select>


    <select id="selectByRoleTypes" resultType="net.summerfarm.model.vo.AdminVO">
        SELECT distinct a.admin_id adminId, a.username, a.realname, a.phone, a.name_remakes nameRemakes
        FROM admin a
        WHERE is_disabled = 0
        <if test="baseUserIds != null and baseUserIds.size!=0">
            AND a.base_user_id in
            <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="selectByInvitecode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select a.admin_id, a.username, a.realname, a.phone
        FROM admin a,
             invitecode i
        WHERE a.admin_id = i.admin_id
          AND i.invitecode = #{invitecode}
    </select>

    <!--查询管理员所有的信息-->


    <select id="selectByKey" parameterType="net.summerfarm.model.domain.Admin"
            resultType="net.summerfarm.model.domain.Admin">
        SELECT admin_id adminId,create_time createTime,login_fail_times loginFailTimes,is_disabled
        isDisabled,username,login_time loginTime,realname,
        gender,department,phone,kp,saler_id salerId,saler_name salerName,contract
        FROM admin
        <where>
            <if test="realname != null">
                AND realname LIKE concat('%',#{realname},'%')
            </if>
        </where>
    </select>

    <select id="selectWithRoles" resultMap="AdminWithPurviews" parameterType="java.lang.Integer">
        SELECT a.admin_id,
               a.create_time,
               a.login_fail_times,
               a.is_disabled,
               a.username,
               a.password,
               a.login_time,
               a.realname,
               a.gender,
               a.department,
               a.phone,
               a.admin_switch,
               a.bill_to_pay
        FROM admin a
        where a.admin_id = #{adminId,jdbcType=VARCHAR}
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT admin_id,
               create_time,
               login_fail_times,
               is_disabled,
               username,
               password,
               login_time,
               realname,
               gender,
               department,
               phone,
               NULL add_time
        FROM admin
        where username = #{username,jdbcType=VARCHAR}
    </select>

    <!--分页查询-->
    <select id="selectPageInfo" resultType="net.summerfarm.model.vo.AdminVO"
            parameterType="net.summerfarm.model.domain.Admin">
        SELECT
        a.admin_id adminId, a.create_time createTime, a.login_fail_times loginFailTimes, a.is_disabled isDisabled,
        a.username, a.login_time loginTime, a.realname, a.gender, a.department, a.phone ,a.admin_switch
        adminSwitch,a.credit_code creditCode,a.business_license_address businessLicenseAddress
        FROM admin a
        <where>
            <if test="realname !=null">
                AND a.realname like concat('%',#{realname},'%')
            </if>
            <if test="department !=null">
                AND a.department = #{department, jdbcType=VARCHAR}
            </if>
            <if test="adminId !=null">
                and a.admin_id = #{adminId}
            </if>
        </where>
    </select>


    <!--分页查询-->
    <select id="selectMajorPageInfo" resultType="net.summerfarm.model.vo.AdminVO"
            parameterType="net.summerfarm.model.vo.AdminVO">
        SELECT
        a.admin_id adminId, a.create_time createTime, a.login_fail_times loginFailTimes, a.is_disabled
        isDisabled,a.admin_type adminType,
        a.admin_grade adminGrade,a.admin_chain adminChain,
        a.username, a.login_time lsalerNameoginTime, a.realname, a.gender, a.department, a.phone, a.kp,a.saler_id
        salerId,a.saler_name salerName ,a.contract,a.contract_method contractMethod, a.name_remakes
        nameRemakes,a.close_order_type closeOrderType,
        a.operate_id operateId, a.major_cycle majorCycle, a2.realname operateName, a2.username operateUserName,
        a3.username saleUserName, a.cooperation_stage cooperationStage,
        a.close_order_time closeOrderTime,a.update_close_order_time updateCloseOrderTime,a.low_price_remainder
        lowPriceRemainder,a.not_included_area notIncludedArea,
        asm.show_flag showFlag,asm.logo,asm.background_image backgroundImage,a.sku_sorting skuSorting,a.admin_switch
        adminSwitch,a.credit_code creditCode,a.business_license_address businessLicenseAddress,a.bill_to_pay billToPay, ic.invitecode
        FROM admin a
        LEFT JOIN admin a2 ON a.operate_id = a2.admin_id
        LEFT JOIN admin a3 ON a.saler_id = a3.admin_id
        left join admin_skin asm on a.admin_id = asm.admin_id
        left join invitecode ic on ic.admin_id = a.admin_id
        <if test="sku != null or pdName != null">
            INNER JOIN (
            SELECT DISTINCT mp.admin_id
            FROM major_price mp
            LEFT JOIN inventory i ON mp.sku = i.sku
            LEFT JOIN products p ON i.pd_id = p.pd_id
            <where>
                <if test="sku != null">
                    AND mp.sku = #{sku}
                </if>
                <if test="pdName != null">
                    AND p.pd_name LIKE concat('%', #{pdName}, '%')
                </if>
            </where>
            ) t ON a.admin_id = t.admin_id
        </if>
        <if test="rebateFlag != null">
            left join (select admin_id from major_rebate mr where mr.status = 1 group by mr.admin_id) t2 on a.admin_id =
            t2.admin_id
        </if>
        <where>
            <if test="baseUserIds != null and baseUserIds.size!=0">
                AND a.base_user_id in
                <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="adminId !=null">
                AND a.admin_id = #{adminId}
            </if>
            <if test="realname !=null">
                AND a.realname like concat('%',#{realname},'%')
            </if>
            <if test="department !=null">
                AND a.department = #{department, jdbcType=VARCHAR}
            </if>
            <if test="kp != null">
                AND a.kp LIKE concat('%',#{kp},'%')
            </if>
            <if test="isDisabled != null">
                AND a.is_disabled = #{isDisabled,jdbcType=BIT}
            </if>
            <if test="salerName != null">
                AND a.saler_name LIKE concat('%',#{salerName},'%')
            </if>
            <if test="majorCycle != null">
                AND a.major_cycle = #{majorCycle}
            </if>
            <if test="nameRemakes != null">
                AND a.name_remakes LIKE concat('%',#{nameRemakes},'%')
            </if>
            <if test="contractMethod != null">
                and a.contract_method like concat('%', #{contractMethod} ,'%')
            </if>
            <if test="rebateFlag != null">
                <choose>
                    <when test="rebateFlag">
                        and t2.admin_id is not null
                    </when>
                    <otherwise>
                        and t2.admin_id is null
                    </otherwise>
                </choose>
            </if>
            <if test="salerId != null">
                AND a.saler_id = #{salerId}
            </if>
            <if test="adminType!=null">
                and a.admin_type = #{adminType}
            </if>
            <if test="adminGrade!=null">
                and a.admin_grade = #{adminGrade}
            </if>
        </where>
        order by a.admin_id
    </select>

    <!--auto code-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from admin
        where admin_id = #{adminId,jdbcType=INTEGER}
    </select>

    <select id="listNameByAdminId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin
        where admin_id in
        <foreach collection="items" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="selectAdminSwitch" resultType="net.summerfarm.model.domain.Admin" parameterType="java.lang.Integer">
        select admin_switch adminSwitch
        from admin
        where admin_id = #{adminId,jdbcType=INTEGER}
    </select>


    <insert id="insertSelective" parameterType="net.summerfarm.model.domain.Admin" useGeneratedKeys="true"
            keyProperty="adminId">
        insert into admin
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="loginFailTimes != null">
                login_fail_times,
            </if>
            <if test="isDisabled != null">
                is_disabled,
            </if>
            <if test="username != null">
                username,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="loginTime != null">
                login_time,
            </if>
            <if test="realname != null">
                realname,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="department != null">
                department,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="kp != null">
                kp,
            </if>
            <if test="salerId != null">
                saler_id,
            </if>
            <if test="salerName != null">
                saler_name,
            </if>
            <if test="contract != null">
                contract,
            </if>
            <if test="contractMethod != null">
                contract_method,
            </if>
            <if test="nameRemakes != null">
                name_remakes,
            </if>
            <if test="operateId != null">
                operate_id,
            </if>
            <if test="majorCycle != null">
                major_cycle,
            </if>
            <if test="closeOrderType != null">
                close_order_type,
            </if>
            <if test="cooperationStage != null">
                cooperation_stage,
            </if>
            <if test="closeOrderTime != null">
                close_order_time,
            </if>
            <if test="skuSorting != null">
                sku_sorting,
            </if>
            <if test="lowPriceRemainder != null">
                low_price_remainder,
            </if>
            <if test="notIncludedArea != null">
                not_included_area,
            </if>
            <if test="adminType!=null">
                admin_type,
            </if>
            <if test="adminChain!=null">
                admin_chain,
            </if>
            <if test="adminGrade!=null">
                admin_grade,
            </if>
            <if test="creditCode != null">
                credit_code,
            </if>
            <if test="businessLicenseAddress != null">
                business_license_address,
            </if>
            <if test="billToPay != null">
                bill_to_pay,
            </if>
            <if test="baseUserId != null">
                base_user_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="adminId != null">
                #{adminId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="loginFailTimes != null">
                #{loginFailTimes,jdbcType=INTEGER},
            </if>
            <if test="isDisabled != null">
                #{isDisabled,jdbcType=BIT},
            </if>
            <if test="username != null">
                #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="loginTime != null">
                #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="realname != null">
                #{realname,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=BIT},
            </if>
            <if test="department != null">
                #{department,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="kp != null">
                #{kp},
            </if>
            <if test="salerId != null">
                #{salerId},
            </if>
            <if test="salerName != null">
                #{salerName},
            </if>
            <if test="contract != null">
                #{contract},
            </if>
            <if test="contractMethod != null">
                #{contractMethod},
            </if>
            <if test="nameRemakes != null">
                #{nameRemakes},
            </if>
            <if test="operateId != null">
                #{operateId},
            </if>
            <if test="majorCycle != null">
                #{majorCycle},
            </if>
            <if test="closeOrderType != null">
                #{closeOrderType},
            </if>
            <if test="cooperationStage != null">
                #{cooperationStage},
            </if>
            <if test="closeOrderTime != null">
                #{closeOrderTime},
            </if>
            <if test="skuSorting != null">
                #{skuSorting},
            </if>
            <if test="lowPriceRemainder != null">
                #{lowPriceRemainder},
            </if>
            <if test="notIncludedArea != null">
                #{notIncludedArea},
            </if>
            <if test="adminType!=null">
                #{adminType},
            </if>
            <if test="adminChain!=null">
                #{adminChain},
            </if>
            <if test="adminGrade!=null">
                #{adminGrade},
            </if>
            <if test="creditCode != null">
                #{creditCode},
            </if>
            <if test="businessLicenseAddress != null">
                #{businessLicenseAddress},
            </if>
            <if test="billToPay != null">
                #{billToPay},
            </if>
            <if test="baseUserId!= null">
                #{baseUserId},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.Admin">
        update admin
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="loginFailTimes != null">
                login_fail_times = #{loginFailTimes,jdbcType=INTEGER},
            </if>
            <if test="isDisabled != null">
                is_disabled = #{isDisabled,jdbcType=BIT},
            </if>
            <if test="username != null">
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="loginTime != null">
                login_time = #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="realname != null">
                realname = #{realname,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=BIT},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="kp != null">
                kp = #{kp},
            </if>
            <if test="salerId != null">
                saler_id = #{salerId},
            </if>
            <if test="salerName != null">
                saler_name = #{salerName},
            </if>
            <if test="contract != null">
                contract = #{contract},
            </if>
            <if test="contractMethod != null">
                contract_method = #{contractMethod},
            </if>
            <if test="nameRemakes != null">
                name_remakes = #{nameRemakes},
            </if>
            <if test="operateId != null">
                operate_id = #{operateId},
            </if>
            <if test="majorCycle != null">
                major_cycle = #{majorCycle},
            </if>
            <if test="closeOrderType != null and closeOrderType == 1">
                close_order_type = #{closeOrderType},
            </if>
            <if test="closeOrderType != null and closeOrderType == 0">
                close_order_type = #{closeOrderType},
                close_order_time = null,
                update_close_order_time = null,
            </if>
            <if test="cooperationStage != null">
                cooperation_stage = #{cooperationStage},
            </if>
            <if test="updateCloseOrderTime != null">
                update_close_order_time = #{updateCloseOrderTime},
            </if>
            <if test="closeOrderTime != null">
                close_order_time = #{closeOrderTime},
            </if>
            <if test="lowPriceRemainder != null">
                low_price_remainder = #{lowPriceRemainder},
            </if>
            <if test="notIncludedArea != null">
                not_included_area = #{notIncludedArea},
            </if>
            <if test="skuSorting != null">
                sku_sorting = #{skuSorting},
            </if>
            <if test="adminType != null">
                admin_type = #{adminType},
            </if>
            <if test="adminChain!=null">
                admin_chain = #{adminChain},
            </if>
            <if test="adminGrade!=null">
                admin_grade = #{adminGrade},
            </if>
            <if test="adminSwitch != null">
                admin_switch = #{adminSwitch},
            </if>
            <if test="creditCode != null">
                credit_code = #{creditCode},
            </if>
            <if test="businessLicenseAddress != null">
                business_license_address = #{businessLicenseAddress},
            </if>
            <if test="billToPay != null">
                bill_to_pay = #{billToPay},
            </if>

        </set>
        where admin_id = #{adminId,jdbcType=INTEGER}
    </update>

    <select id="selectCreateTime" parameterType="java.util.HashMap" resultType="java.time.LocalDate">
        SELECT DATE_FORMAT(a.create_time, '%Y-%m-%d') 'createTime'
        FROM admin a
        WHERE a.admin_id = #{adminId}
    </select>

    <select id="selectByAdminId" parameterType="java.lang.Integer" resultMap="AdminVOMap">
        SELECT a.admin_id, a.realname, m.m_id, a.saler_name adminRealname, close_order_time closeOrderTime, base_user_id baseUserId
        FROM admin a
                 INNER JOIN merchant m ON a.admin_id = m.admin_id
        WHERE a.admin_id = #{adminId}
    </select>

    <select id="selectBdListByKaAdminId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin a
        where a.is_disabled = 0
        and a.admin_id in (
        select fur.admin_id from merchant m
        left join follow_up_relation fur on m.m_id = fur.m_id and fur.reassign = 0
        where m.admin_id = #{kaAdminId}
        group by fur.admin_id
        )
    </select>
    <select id="selectByRealName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin a
        where a.realname = #{realname}
    </select>

    <select id="selectByRealNameNew" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin a
        where a.realname = #{realname}
        limit 1
    </select>
    <select id="selectByPhone" parameterType="java.lang.String" resultType="net.summerfarm.model.domain.Admin">
        SELECT admin_id         adminId,
               create_time      createTime,
               login_fail_times loginFailTimes,
               is_disabled      isDisabled,
               username,
               login_time       loginTime,
               realname,
               gender,
               department,
               phone,
               kp,
               saler_id         salerId,
               saler_name       salerName,
               contract
        FROM admin
        where phone = #{phone}
          and is_disabled = 0
    </select>
    <select id="selectByRoleTypesNames" resultType="net.summerfarm.model.vo.ValuesVo">
        SELECT distinct a.admin_id as id, a.realname as value
        FROM admin a
        WHERE is_disabled = 0
        <if test="baseUserIds != null and baseUserIds.size!=0">
            AND a.base_user_id in
            <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="adminName!=null and adminName!=null">
            and a.realname like concat('%',#{adminName},'%')
        </if>


    </select>

    <select id="selectUpdateCloseTime" resultType="net.summerfarm.model.domain.Admin">
        SELECT admin_id                adminId,
               create_time             createTime,
               login_fail_times        loginFailTimes,
               is_disabled             isDisabled,
               username,
               login_time              loginTime,
               realname,
               gender,
               department,
               phone,
               update_close_order_time updateCloseOrderTime
        FROM admin
        where update_close_order_time is not null
    </select>

    <update id="updateCloseTime">
        update admin
        set close_order_time        = #{updateCloseTime},
            update_close_order_time = null
        where admin_id = #{adminId}
    </update>
    <select id="selectAdministration" resultType="net.summerfarm.model.domain.Admin">
        select a.username, a.realname
        from admin a
                 left join department_staff ds on a.phone = ds.mobile
        where ds.dept_id = #{departmentId}
          and ds.status = 1
          and a.phone <![CDATA[<>]]> '***********'
    </select>

    <select id="selectKeyAccount" resultType="net.summerfarm.model.domain.Admin">
        select ad.admin_id adminId,ad.realname,ad.name_remakes nameRemakes
        from admin ad
        <where>
            <foreach collection="keyAccountNames" item="keyAccountName" index="index" open="(" separator="or" close=")">
                ad.name_remakes like concat('%',#{keyAccountName},'%')
            </foreach>
            <if test="baseUserIds != null and baseUserIds.size!=0">
                AND a.base_user_id in
                <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and  ad.is_disabled = 0
        </where>
    </select>

    <select id="selectLikeUsername" resultType="net.summerfarm.model.domain.Admin">
        select username
        from admin
        where username like concat('%', #{username}, '%')
    </select>

    <select id="selectByAid" resultMap="BaseResultMap" parameterType="INTEGER">
        select
        <include refid="Base_Column_List"/>
        from admin where admin_id = #{adminId}
    </select>

    <select id="selectSalerName" resultType="net.summerfarm.model.domain.Admin">
        select distinct saler_name salerName, saler_id adminId
        from admin
        where admin_type = 0
          and is_disabled = 0
          and saler_id is not null
    </select>

    <select id="selectBD" resultType="integer">
        select distinct a.admin_id adminId
        FROM admin a
        WHERE is_disabled = 0 and
        a.base_user_id in
        <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByNameRemakes" resultType="net.summerfarm.model.domain.Admin">
        select ad.admin_id adminId, ad.name_remakes nameRemakes, ad.saler_id salerId, ad.saler_name salerName
        from admin ad
        where
        ad.base_user_id in
        <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and ad.is_disabled = 0
        and ad.name_remakes like concat(#{nameRemakes}, '%')
    </select>

    <select id="selectNotMajorAdmin" resultType="net.summerfarm.model.domain.Admin">
        select distinct a.admin_id adminId, a.realname
        from admin a
        <if test="baseUserIds != null and baseUserIds.size!=0">
           where  a.base_user_id not in
            <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="selectAllInfo" resultType="net.summerfarm.model.domain.Admin">
        select
        <include refid="Base_Column_List"/>
        from admin
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM admin
        WHERE admin_id in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectCBDAndSY" resultType="Integer">
        select admin_id adminId
        from xianmudb.admin
        where name_remakes like '%茶百道%'
           or name_remakes like '%书亦%'
    </select>

    <select id="listByIds" resultType="net.summerfarm.model.domain.Admin">
        SELECT admin_id adminId,username, phone
        FROM admin
        WHERE admin_id in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectDingAdmin" resultType="net.summerfarm.model.DTO.AdminDingDTO">
        SELECT
        aae.user_id userId,m.m_id mId,m.mname mName
        FROM follow_up_relation fur
        left join admin a on fur.admin_id = a.admin_id
        left join admin_auth_extend aae on a.admin_id = aae.admin_id
        left join merchant m on m.m_id = fur.m_id
        WHERE fur.m_id = #{mId,jdbcType=BIGINT} and  fur.reassign = 0 and aae.`type` = 0 and aae.status = 0
    </select>

    <select id="selectBdName" resultType="net.summerfarm.model.DTO.AdminDingDTO">
        SELECT
        a.realname bdName,a.phone bdPhone
        FROM follow_up_relation fur
        left join admin a on fur.admin_id = a.admin_id
        WHERE fur.m_id = #{mId,jdbcType=BIGINT} and  fur.reassign = 0
    </select>

    <select id="queryNameRemakes" resultType="net.summerfarm.model.domain.Admin">
        SELECT
            a.admin_id adminId,a.name_remakes nameRemakes
        FROM
            admin a
        WHERE
            a.`name_remakes` like concat(#{nameRemakes}, '%')
    </select>
    <select id="selectSamePwdCount" resultType="Integer">
        select count(*) from admin where password = #{password}
    </select>

    <select id="listByRealName" resultMap="BaseResultMap">
        SELECT
            admin_id, realname
        FROM
            admin
        WHERE
            realname like concat(#{realName}, '%')
    </select>


        <select id="listByRealNameLike" resultMap="BaseResultMap">
        SELECT
            admin_id, realname
        FROM
            admin
        WHERE
            realname like concat('%', #{realName}, '%')
    </select>


    <update id="updateBaseUserId">
        update admin  set base_user_id =#{userBaseId} where admin_id =  #{adminId}
    </update>

    <update id="updateDriverCarBaseUserId">
        update  tms_driver set base_user_id =#{userBaseId} where id =  #{id}
    </update>

    <update id="updateSupplierBaseUserId">
        update  srm_supplier_user set base_user_id =#{userBaseId} where id =  #{id}
    </update>

    <select id="selectUserBaseIdById" resultType="java.lang.Long">
        select  base_user_id from admin where admin_id =  #{adminId}
    </select>

    <select id="selectAdminIdByUserBaseId" resultType="java.lang.Integer">
        select  admin_id from admin where base_user_id =  #{userBaseId}
    </select>

    <select id="selectByUserBaseIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
            admin
        WHERE
        base_user_id in
        <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>


    <select id="selectAllAdmin" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
            admin
    </select>
    <select id="selectAdminMerchantCount" resultType="net.summerfarm.model.vo.finance.AdminOverviewExportVo">
        SELECT  a.name_remakes nameRemark,sum(if(m.direct=1,1,0)) billStoreCount,sum(if(m.direct!=1,1,0)) cashStoreCount
        from admin a
        left join merchant m on a.admin_id=m.admin_id
        where a.admin_id = #{adminId}
    </select>
    <select id="selectByNameRemakesList" resultType="net.summerfarm.model.domain.Admin">
        select ad.admin_id adminId, ad.name_remakes nameRemakes, ad.saler_id salerId, ad.saler_name salerName
        from admin ad
        where
        ad.name_remakes in
        <foreach collection="nameRemakes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and ad.is_disabled = 0
    </select>
</mapper>
