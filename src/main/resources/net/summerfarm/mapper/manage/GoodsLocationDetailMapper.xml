<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.GoodsLocationDetailMapper">
    <resultMap id="GoodsLocationDetailReq" type="net.summerfarm.model.input.GoodsLocationDetailOutReq">
        <id column="glId" property="glId" jdbcType="INTEGER"/>
        <result column="gl_no" property="glNo" jdbcType="VARCHAR"/>
        <collection property="details" ofType="net.summerfarm.model.domain.GoodsLocationDetail">
            <id column="id" property="id" jdbcType="INTEGER" />
            <result column="add_time" property="addTime"/>
            <result column="update_time" property="updateTime"/>
            <result column="gl_no" property="glNo"/>
            <result column="batch" property="batch"/>
            <result column="sku" property="sku"/>
            <result column="quality_date" property="qualityDate"/>
            <result column="quantity" property="quantity"/>
        </collection>
    </resultMap>

    <insert id="insertBathDetail" parameterType="net.summerfarm.model.domain.GoodsLocationDetail" useGeneratedKeys="true" keyProperty="id">
        insert into goods_location_detail (`gl_no`, `batch` , `sku`,`quality_date`,`quantity` ,`add_time`,`update_time`,`status`,`production_date`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.glNo},#{item.batch},#{item.sku},#{item.qualityDate},#{item.quantity},#{item.addTime},#{item.updateTime},#{item.status},#{item.productionDate})
        </foreach>
    </insert>



    <insert id="insertDetail" parameterType="net.summerfarm.model.domain.GoodsLocationDetail" useGeneratedKeys="true" keyProperty="id">
        insert into goods_location_detail (`gl_no`, `batch` , `sku`,`quality_date`,`quantity` ,`add_time`,`update_time`,`status`,`production_date`)
        values
        (#{glNo},#{batch},#{sku},#{qualityDate},#{quantity},#{addTime},#{updateTime},#{status},#{productionDate})
    </insert>


    <update id="updateDetail" parameterType="net.summerfarm.model.domain.GoodsLocationDetail" >
        update goods_location_detail
        <set>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="updateTime">
                update_time = now(),
            </if>
            <if test="status != null">
                status = #{status}
            </if>
        </set>
        where gl_no = #{glNo} and batch = #{batch} and sku = #{sku} and quality_date = #{qualityDate} and
        production_date = #{productionDate}
    </update>

    <select id="selectDetail" resultType="net.summerfarm.model.domain.GoodsLocationDetail">

        select id, gl_no glNo, batch, sku, quality_date qualityDate, quantity, add_time addTime, update_time updateTime, status,production_date productionDate
        from goods_location_detail
        where gl_no = #{glNo} and status = 0 and  quantity > 0
    </select>

    <select id="selectDetailBySku" resultType="net.summerfarm.model.input.GoodsTransferQuery">
        select gld.sku,p.pd_name pdName,i.weight,i.unit packing,p.storage_location storageLocation
        from goods_location_detail gld
            left  join inventory i on gld.sku = i.sku
            left  join products p on p.pd_id = i.pd_id
        where  status = 0 and  quantity > 0
            <if test="glNo != null">
                and gld.gl_no = #{glNo}
            </if>
            <if test="sku != null">
                and gld.sku = #{sku}
            </if>
            <if test="pdName != null">
                and p.pd_name like concat("%",#{pdName},"%")
            </if>
        group by gld.sku
    </select>
    <select id="selectDetailByAll" resultType="net.summerfarm.model.domain.GoodsLocationDetail">
        select gld.gl_no glNo,gld.batch,gld.quality_date qualityDate,gld.quantity quantity
        from goods_location_detail gld
                 left  join inventory i on gld.sku = i.sku
                 left  join products p on p.pd_id = i.pd_id
        where status = 0 and  quantity > 0
          and gld.sku = #{sku}
          <if test="glNo!=null">
            and gld.gl_no = #{glNo}
          </if>
    </select>
    <select id="selectAllDetail" resultType="net.summerfarm.model.vo.GoodsLocationDetailVO">
        select gld.gl_no glNo,gld.batch,gld.quality_date qualityDate,gld.quantity quantity,gld.production_date productionDate
        from goods_location_detail gld
        left  join inventory i on gld.sku = i.sku
        left  join products p on p.pd_id = i.pd_id
        where status = 0 and  quantity > 0
        and gld.sku = #{sku}
    </select>
    <select id="selectByDetail" resultType="net.summerfarm.model.domain.GoodsLocationDetail">

        select id, gl_no glNo, batch, sku, quality_date qualityDate, quantity, add_time addTime, update_time updateTime, status,sale_lock_quantity saleLockQuantity,production_date productionDate
        from goods_location_detail
        where gl_no = #{glNo} and sku=#{sku} and quality_date = #{qualityDate} and batch =#{batch} and status = 0 and  quantity > 0
    </select>

    <update id="updateQuantity">
        update goods_location_detail  set quantity = quantity + #{quantity}
        <if test="status != null">
            ,status = #{status}
        </if>
        where id =#{id}
    </update>


    <select id="selectBySku" resultType="net.summerfarm.model.vo.GoodsLocationDetailVO">

        select gld.id, gld.gl_no glNo, gld.batch, gld.sku, gld.quality_date qualityDate, gld.quantity, gld.add_time addTime,gld.update_time updateTime, gld.status
        ,p.purchase_time purchaseTime, (gld.quantity - gld.sale_lock_quantity) approveQuantity
        ,gld.production_date productionDate, gld.sale_lock_quantity saleLockQuantity,pp.supplier
        from goods_location_detail gld
          left join purchases_plan pp on pp.purchase_no = gld.batch and pp.sku = gld.sku  and pp.quality_date = gld.quality_date and pp.origin_id is not null and pp.plan_status = 1
          left join purchases p on p.purchase_no = gld.batch
        where gld.sku=#{sku} and gld.status = 0 and  gld.quantity > 0
        order by gld.quality_date , p.purchase_time
    </select>

    <select id="selectByPurchaseNo" resultType="net.summerfarm.model.vo.GoodsLocationDetailVO">

        select gld.id, gld.gl_no glNo, gld.batch, gld.sku, gld.quality_date qualityDate, gld.quantity, gld.add_time addTime,gld.update_time updateTime, gld.status
             ,p.purchase_time purchaseTime, (gld.quantity - gld.sale_lock_quantity) approveQuantity
             ,gld.production_date productionDate, gld.sale_lock_quantity saleLockQuantity,pp.supplier,pp.in_quantity,pp.in_price totalCost,convert(pp.in_price/pp.in_quantity,decimal (10,2)) singleCost,i.weight,pr.pd_name pdName
        from goods_location_detail gld
                 left join purchases_plan pp on pp.purchase_no = gld.batch and pp.sku = gld.sku  and pp.quality_date = gld.quality_date and  pp.origin_id is not null and pp.plan_status = 1
                 left join purchases p on p.purchase_no = gld.batch
                 left join inventory i on i.sku = pp.sku
                 left join products pr on i.pd_id = pr.pd_id
        where gld.batch=#{purchasesNo} and gld.status = 0 and  gld.quantity > 0
        order by gld.quality_date , p.purchase_time
    </select>

    <select id="selectOutReq" resultMap="GoodsLocationDetailReq">
        select gl.id glId,gld.* from goods_location gl
        inner join  goods_location_detail gld on gld.gl_no = gl.gl_no and gld.status = 0 and gld.quantity > 0
        where gl.store_no = #{storeNo} and gl.type != 1
    </select>
    <update id="updateLockQuantity">
        update goods_location_detail
        set  sale_lock_quantity = sale_lock_quantity + #{saleLockQuantity}
        where id =#{id}
    </update>

    <update id="updateQuantityByType">
        update goods_location_detail  set quantity = quantity + #{quantity}
        ,sale_lock_quantity = sale_lock_quantity + #{saleQuantity}
        <if test="status != null">
            ,status = #{status}
        </if>
        where id =#{id}
    </update>
    <select id="selectGoodsByGlNo" resultType="net.summerfarm.model.vo.GoodsLocationDetailVO">
        select  gld.sku,gl_no glNo,sum(quantity) quantity ,i.weight,p.pd_name pdName from goods_location_detail gld
        left join inventory i on i.sku = gld.sku
        left join products p on i.pd_id = p.pd_id
        where gl_no = #{glNo} and status = 0 and quantity > 0
        group by  gld.sku
    </select>
    <select id="selectDetailList" resultType="net.summerfarm.model.domain.GoodsLocationDetail">
        select id, gl_no glNo, batch, sku, quality_date qualityDate, quantity, add_time addTime, update_time updateTime, status
        from goods_location_detail
        where sku = #{sku} and batch = #{batch} and quality_date = #{qualityDate}  and status = 0 and  quantity > 0
         <if test="glNo != null">
             and gl_no = #{glNo}
         </if>

    </select>

    <select id="selectDetailListWithStoreNo" parameterType="net.summerfarm.model.vo.GoodsLocationDetailVO" resultType="net.summerfarm.model.domain.GoodsLocationDetail">
        select gld.id,
            gld.gl_no        glNo,
            gld.batch,
            gld.sku,
            gld.quality_date qualityDate,
            gld.quantity,
            gld.add_time     addTime,
            gld.update_time  updateTime,
            gld.status
        from goods_location_detail gld
            left join goods_location gl on gld.gl_no = gl.gl_no
        where gl.store_no = #{storeNo}
            and gld.sku = #{sku}
            and gld.batch = #{batch}
            and gld.quality_date = #{qualityDate}
            and gld.status = 0
            and gld.quantity > 0
            <if test="glNo != null">
                and gld.gl_no = #{glNo}
            </if>
    </select>

    <select id="selectBySkuSaleLock" resultType="net.summerfarm.model.vo.GoodsLocationDetailVO">
        select gld.id, gld.gl_no glNo, gld.batch, gld.sku, gld.quality_date qualityDate, (gld.quantity - gld.sale_lock_quantity) quantity, gld.add_time addTime,gld.update_time updateTime, gld.status
        ,p.purchase_time purchaseTime, (gld.quantity - gld.sale_lock_quantity) approveQuantity
        ,gld.production_date productionDate, gld.sale_lock_quantity saleLockQuantity,pp.supplier
        from goods_location_detail gld
          left join purchases_plan pp on pp.purchase_no = gld.batch and pp.sku = gld.sku and pp.plan_status = 1 and pp.quality_date = gld.quality_date and pp.origin_id is not null
          left join purchases p on p.purchase_no = gld.batch
        where gld.sku=#{sku} and gld.status = 0 and  gld.quantity > 0 and gld.quantity -  gld.sale_lock_quantity > 0
        order by gld.quality_date , p.purchase_time
    </select>


    <select id="selectDetailByGlNo" resultType="net.summerfarm.model.domain.GoodsLocationDetail">
        select id, gl_no glNo, batch, sku, quality_date qualityDate, quantity, add_time addTime, update_time updateTime, status,sale_lock_quantity saleLockQuantity
        from goods_location_detail
        where gl_no = #{glNo} and sku=#{sku} and quality_date = #{qualityDate} and batch =#{batch}
    </select>
    <select id="selectBySkuAndMin" resultType="net.summerfarm.model.vo.GoodsLocationDetailVO">
         select gld.id, gld.gl_no glNo, gld.batch, gld.sku, gld.quality_date qualityDate, gld.quantity, gld.add_time addTime,gld.update_time updateTime, gld.status
        ,p.purchase_time purchaseTime, (gld.quantity - gld.sale_lock_quantity) approveQuantity
        ,gld.production_date productionDate, gld.sale_lock_quantity saleLockQuantity,pp.supplier
        from goods_location_detail gld
          left join purchases_plan pp on pp.purchase_no = gld.batch and pp.sku = gld.sku and pp.plan_status = 1 and pp.quality_date = gld.quality_date and pp.origin_id is not null
          left join purchases p on p.purchase_no = gld.batch
        where gld.sku= #{sku} and gld.status = 0 and  gld.quantity > 0
        order by gld.quality_date , p.purchase_time
    </select>


    <select id="selectByDetailVO" resultType="net.summerfarm.model.domain.GoodsLocationDetail">
        select id, gl_no glNo, batch, sku, quality_date qualityDate, quantity, add_time addTime, update_time updateTime, status,sale_lock_quantity saleLockQuantity,production_date productionDate
        from goods_location_detail gld
        where sku=#{sku} and quality_date = #{qualityDate} and batch =#{batch} and status = 0 and  quantity > 0
        limit 1
    </select>

    <select id="selectBySkuAndNear" resultType="net.summerfarm.model.vo.GoodsLocationDetailVO">
         select gld.id, gld.gl_no glNo, gld.batch, gld.sku, gld.quality_date qualityDate, gld.quantity, gld.add_time addTime,gld.update_time updateTime, gld.status
       ,gld.quantity, gld.sale_lock_quantity saleLockQuantity,gld.production_date productionDate
        from goods_location_detail gld
        where gld.sku = #{sku}
        order by gld.quality_date desc , gld.production_date desc
        limit 1
    </select>

    <select id="selectQuantityBySku" resultType="net.summerfarm.model.vo.StoreRecordVO">
        select s.batch,s.quantity,s.quantity storeQuantity,s.quality_date qualityDate,
        s.production_date productionDate,s.gl_no glNo
        from
        (   select  sku, batch,quantity, quality_date ,production_date ,gl_no
            from goods_location_detail
            where sku = #{sku}
            order by id desc
        ) s
        group by s.sku, s.batch, s.quality_date,s.gl_no
    </select>
</mapper>
