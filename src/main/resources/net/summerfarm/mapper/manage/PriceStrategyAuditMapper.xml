<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PriceStrategyAuditMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PriceStrategyAudit">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="cost_price" jdbcType="DECIMAL" property="costPrice" />
    <result column="original_price" jdbcType="DECIMAL" property="originalPrice" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, area_no, sku, cost_price, original_price, `status`, 
    audit_time, auditor, creator
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from price_strategy_audit
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from price_strategy_audit
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PriceStrategyAudit" useGeneratedKeys="true">
    insert into price_strategy_audit (create_time, update_time, area_no, 
      sku, cost_price, original_price, 
      `status`, audit_time, auditor, 
      creator)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{areaNo,jdbcType=INTEGER}, 
      #{sku,jdbcType=VARCHAR}, #{costPrice,jdbcType=DECIMAL}, #{originalPrice,jdbcType=DECIMAL}, 
      #{status,jdbcType=INTEGER}, #{auditTime,jdbcType=TIMESTAMP}, #{auditor,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PriceStrategyAudit" useGeneratedKeys="true">
    insert into price_strategy_audit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="costPrice != null">
        cost_price,
      </if>
      <if test="originalPrice != null">
        original_price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="creator != null">
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PriceStrategyAudit">
    update price_strategy_audit
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="costPrice != null">
        cost_price = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        original_price = #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PriceStrategyAudit">
    update price_strategy_audit
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      area_no = #{areaNo,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      cost_price = #{costPrice,jdbcType=DECIMAL},
      original_price = #{originalPrice,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=INTEGER},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      auditor = #{auditor,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectInAudit" resultType="net.summerfarm.model.domain.PriceStrategyAudit">
    select
        <include refid="Base_Column_List"/>
    from price_strategy_audit
    where status = 0 and area_no = #{areaNo} and sku = #{sku}
  </select>
  <select id="selectWaitingAudit" resultType="net.summerfarm.model.domain.PriceStrategyAudit">
    select
    <include refid="Base_Column_List"/>
    from price_strategy_audit
    where status = 0 and id = #{id,jdbcType=BIGINT}
    for update
  </select>
</mapper>