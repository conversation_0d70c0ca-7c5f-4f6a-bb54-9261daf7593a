<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.AreaTopMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.AreaTop">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="top_id" jdbcType="INTEGER" property="topId" />
        <result column="area_no" jdbcType="INTEGER" property="areaNo" />
        <result column="area_name" jdbcType="VARCHAR" property="areaName" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <insert id="insertBatch">
        insert into area_top (top_id, area_no,area_name,creator,updater) VALUES
        <foreach collection="list" item="item" separator="," >
            (#{item.topId}, #{item.areaNo}, #{item.areaName},#{item.creator},#{item.updater})
        </foreach>
    </insert>

    <select id="select" resultType="net.summerfarm.model.DTO.AreaDTO">
        select area_no areaNo,area_name areaName from area_top where top_id = #{topId}
    </select>

    <delete id="deleteByTopId" parameterType="java.lang.Integer">
        delete from area_top where top_id = #{topId}
    </delete>

</mapper>