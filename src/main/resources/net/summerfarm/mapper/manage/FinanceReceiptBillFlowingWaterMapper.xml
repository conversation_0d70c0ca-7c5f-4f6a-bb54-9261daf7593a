<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceReceiptBillFlowingWaterMapper">

  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceReceiptBillFlowingWater">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="finance_receipt_id" jdbcType="BIGINT" property="financeReceiptId" />
    <result column="finance_order_id" jdbcType="BIGINT" property="financeOrderId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="receivable_amount" jdbcType="DECIMAL" property="receivableAmount" />
    <result column="un_written_off_amount" jdbcType="DECIMAL" property="unWrittenOffAmount" />
    <result column="receipt_amount" jdbcType="DECIMAL" property="receiptAmount" />
    <result column="other_amount" jdbcType="DECIMAL" property="otherAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
  </resultMap>

  <sql id="Base_Column_List">
    id, finance_receipt_id, finance_order_id, type, receivable_amount, un_written_off_amount, 
    receipt_amount, other_amount, create_time, creator
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_receipt_bill_flowing_water
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_receipt_bill_flowing_water
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.FinanceReceiptBillFlowingWater">
    insert into finance_receipt_bill_flowing_water (id, finance_receipt_id, finance_order_id, 
      type, receivable_amount, un_written_off_amount, 
      receipt_amount, other_amount, create_time, 
      creator)
    values (#{id,jdbcType=BIGINT}, #{financeReceiptId,jdbcType=BIGINT}, #{financeOrderId,jdbcType=BIGINT}, 
      #{type,jdbcType=TINYINT}, #{receivableAmount,jdbcType=DECIMAL}, #{unWrittenOffAmount,jdbcType=DECIMAL},
      #{receiptAmount,jdbcType=DECIMAL}, #{otherAmount,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.FinanceReceiptBillFlowingWater">
    insert into finance_receipt_bill_flowing_water
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="financeReceiptId != null">
        finance_receipt_id,
      </if>
      <if test="financeOrderId != null">
        finance_order_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="receivableAmount != null">
        receivable_amount,
      </if>
      <if test="unWrittenOffAmount != null">
        un_written_off_amount,
      </if>
      <if test="receiptAmount != null">
        receipt_amount,
      </if>
      <if test="otherAmount != null">
        other_amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="financeReceiptId != null">
        #{financeReceiptId,jdbcType=BIGINT},
      </if>
      <if test="financeOrderId != null">
        #{financeOrderId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="receivableAmount != null">
        #{receivableAmount,jdbcType=DECIMAL},
      </if>
      <if test="unWrittenOffAmount != null">
        #{unWrittenOffAmount,jdbcType=DECIMAL},
      </if>
      <if test="receiptAmount != null">
        #{receiptAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceReceiptBillFlowingWater">
    update finance_receipt_bill_flowing_water
    <set>
      <if test="financeReceiptId != null">
        finance_receipt_id = #{financeReceiptId,jdbcType=BIGINT},
      </if>
      <if test="financeOrderId != null">
        finance_order_id = #{financeOrderId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="receivableAmount != null">
        receivable_amount = #{receivableAmount,jdbcType=DECIMAL},
      </if>
      <if test="unWrittenOffAmount != null">
        un_written_off_amount = #{unWrittenOffAmount,jdbcType=DECIMAL},
      </if>
      <if test="receiptAmount != null">
        receipt_amount = #{receiptAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        other_amount = #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceReceiptBillFlowingWater">
    update finance_receipt_bill_flowing_water
    set finance_receipt_id = #{financeReceiptId,jdbcType=BIGINT},
      finance_order_id = #{financeOrderId,jdbcType=BIGINT},
      type = #{type,jdbcType=TINYINT},
      receivable_amount = #{receivableAmount,jdbcType=DECIMAL},
      un_written_off_amount = #{unWrittenOffAmount,jdbcType=DECIMAL},
      receipt_amount = #{receiptAmount,jdbcType=DECIMAL},
      other_amount = #{otherAmount,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>