<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.InvoiceMerchantRelationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.InvoiceConfig">
    <result column="invoice_id"  property="id" />
    <result column="merchant_id"  property="merchantId" />
    <result column="invoice_title"  property="invoiceTitle" />
  </resultMap>
  <sql id="Base_Column_List">
    id, invoice_id, merchant_id, update_time, create_time
  </sql>
  <select id="selectByMId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select im.id, im.invoice_id invoiceId, im.merchant_id merchantId, im.update_time updateTime, im.create_time createTime,
           i.invoice_title
    from invoice_merchant_relation im
    left join invoice_config i on im.invoice_id = i.id
    where im.merchant_id = #{mId} and status = 0 and i.type = 1
  </select>
  <select id="selectCount" resultType="java.lang.Integer">
    select COUNT(*)
    FROM invoice_merchant_relation
    where invoice_id = #{id} and status = 0
  </select>
  <select id="selectAllMerchant" resultType="net.summerfarm.model.vo.InvoiceMerchantRelationVO">
   select t2.id, t2.invoice_id invoiceId, IFNULL(t2.merchant_id, t1.m_id) merchantId, t2.invoice_title invoiceTitle,
   t1.admin_id adminId, t1.mname, t1.city, t1.direct
   FROM
   (select m.m_id, m.admin_id, m.mname, a.area_name city, m.direct from merchant m left join area a on m.area_no = a.area_no where m.admin_id = #{adminId} and m.islock != 2) t1
   LEFT JOIN
   (select imr.id, imr.invoice_id, imr.merchant_id, ic.invoice_title from invoice_config ic
   left join invoice_merchant_relation imr ON ic.id = imr.invoice_id where ic.admin_id = #{adminId} and imr.status = 0) t2
   ON t1.m_id = t2.merchant_id
   <where>
     <if test="mname != null">
       t1.mname LIKE CONCAT('%', #{mname}, '%')
     </if>
     <if test="city != null">
       AND t1.city LIKE CONCAT('%', #{city}, '%')
     </if>
     <if test="titleType == 0 and invoiceTitle != null">
       AND t2.invoice_title = #{invoiceTitle}
     </if>
     <if test="titleType == 1 ">
       AND t2.invoice_title is Null
     </if>
   </where>

  </select>
  <select id="selectByAdminId" resultType="net.summerfarm.model.vo.InvoiceMerchantRelationVO">
    select imr.id, imr.invoice_id invoiceId, imr.merchant_id merchantId, imr.update_time updateTime, imr.create_time createTime
    FROM invoice_config ic left join invoice_merchant_relation imr on ic.id = imr.invoice_id
    <where>
      <if test="adminId != null">
        ic.admin_id  = #{adminId}
      </if>
    </where>
  </select>
  <select id="selectBySelectKeys" resultType="net.summerfarm.model.vo.InvoiceMerchantRelationVO">
    select
    id, invoice_id invoiceId, merchant_id merchantId, update_time updateTime, create_time createTime
    FROM invoice_merchant_relation
    <where>
        status = 0
      <if test="invoiceId != null">
        AND invoice_id = #{invoiceId}
      </if>
      <if test="merchantId != null">
        AND merchant_id = #{merchantId}
      </if>
    </where>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from invoice_merchant_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <update id="deleteByInvoiceId" parameterType="java.lang.Long" >
    update invoice_merchant_relation
    set status = 1,
        update_time = now()
    WHERE invoice_id = #{invoiceId} and status =0
  </update>

  <update id="deleteMerchantId" parameterType="long">
    update invoice_merchant_relation
    set status = 1,
        update_time = now()
    where merchant_id = #{mId} and status = 0
  </update>

  <delete id="deleteByMerchantIds">
    DELETE
    FROM invoice_merchant_relation
    WHERE merchant_id IN
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.InvoiceMerchantRelation" useGeneratedKeys="true">
    insert into invoice_merchant_relation (invoice_id, merchant_id)
    values (#{invoiceId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.InvoiceMerchantRelation" useGeneratedKeys="true">
    insert into invoice_merchant_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.InvoiceMerchantRelation">
    update invoice_merchant_relation
    <set>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.InvoiceMerchantRelation">
    update invoice_merchant_relation
    set invoice_id = #{invoiceId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertNew">
    insert into invoice_merchant_relation (invoice_id, merchant_id, create_time, status)
    values (#{invoiceId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, now(), 0)
  </insert>

  <select id="selectHistory" resultType="net.summerfarm.model.vo.InvoiceMerchantRelationVO">
    select distinct i.invoice_id invoiceId,ic.admin_id adminId,ic.invoice_title invoiceTitle,ic.tax_number taxNumber
    from invoice_merchant_relation i
    left join invoice_config ic on i.invoice_id = ic.id
    where i.status  = 0
  </select>

  <select id="selectAllHistory" resultType="net.summerfarm.model.vo.InvoiceMerchantRelationVO">
    select i.invoice_id invoiceId,i.merchant_id merchantId
    from invoice_merchant_relation i
    left join invoice_config ic on i.invoice_id = ic.id
    where i.status  = 0
  </select>

  <select id="selectAll" parameterType="long" resultType="net.summerfarm.model.domain.InvoiceMerchantRelation">
    select i.id,i.invoice_id invoiceId,i.merchant_id merchantId
    from invoice_merchant_relation i
    left join invoice_config ic on i.invoice_id = ic.id
    left join merchant m on i.merchant_id = m.m_id
    where i.status  = 0 and i.invoice_id = #{invoiceId}
  </select>

  <select id="listByMIdAndInvoiceId" resultType="java.lang.Integer">
    select imr.merchant_id mId
    from invoice_merchant_relation imr
    where imr.invoice_id = #{invoiceId} and status = 0 and merchant_id in
    <foreach collection="mIdList" open="(" close=")" separator="," item="mId">
      #{mId}
    </foreach>
  </select>

  <insert id="insertBatch">
    insert into invoice_merchant_relation(invoice_id, merchant_id)
    values
    <foreach collection="mIdList" separator="," item="mId">
      (#{invoiceId}, #{mId})
    </foreach>
  </insert>

  <delete id="delBatch">
    delete
    from invoice_merchant_relation
    where invoice_id = #{invoiceId}
      and merchant_id in
    <foreach collection="mIdList" open="(" close=")" separator="," item="mId">
      #{mId}
    </foreach>
  </delete>
</mapper>