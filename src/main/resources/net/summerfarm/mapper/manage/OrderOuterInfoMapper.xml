<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.OrderOuterInfoMapper" >

    <insert id="insertList" parameterType="net.summerfarm.model.domain.OrderOuterInfo"  keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into  order_outer_info ( gmt_create,gmt_modified ,m_id  ,order_no ,delivery_date,mphone ,address ,remark ,amount ,sku ,xm_order_no,unit,`name`,
                    standard,outer_platform_id,province,city,area,detailed_address)
        values
        <foreach collection="list" separator="," item="item">
            (now(),now(),#{item.mId}, #{item.orderNo}, #{item.deliveryDate},
            #{item.mphone},#{item.address},
            #{item.remark}, #{item.amount}, #{item.sku},#{item.xmOrderNo},
            #{item.unit},#{item.name},#{item.standard},#{item.outerPlatformId},
            #{item.province},#{item.city},#{item.area},#{item.detailedAddress})
        </foreach>
    </insert>

    <select id="queryOrderOuter" resultType="net.summerfarm.model.domain.OrderOuterInfo">
      select id,order_no orderNo,m_id mId,status,failure_reason failureReason from order_outer_info
      where order_no = #{orderNo} and outer_platform_id = #{outerPlatformId}
    </select>

    <select id="queryInfoByXmOrderNo" resultType="net.summerfarm.model.domain.OrderOuterInfo">
        select id,order_no orderNo,m_id mId,gmt_create gmtCreate,outer_platform_id outerPlatformId,xm_order_no xmOrderNo
         from order_outer_info
        where xm_order_no = #{xmOrderNo}
    </select>

    <update id="updateOrderOuter" parameterType="net.summerfarm.model.domain.OrderOuterInfo">
        update order_outer_info
        <set>
            <if test= "xmOrderNo != null">
                xm_order_no = #{xmOrderNo},
            </if>
            <if test= "deliveryStatus != null">
                delivery_status = #{deliveryStatus},
            </if>
            <if test= "status != null">
                status = #{status},
            </if>
            <if test= "orderSuccessTime != null">
                order_success_time = #{orderSuccessTime},
            </if>
            <if test= "xMId != null">
                xm_id = #{xMId},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectByDeliveryStatus" resultType="net.summerfarm.model.domain.OrderOuterInfo">
        select id,order_no orderNo,m_id mId,xm_order_no xmOrderNo,gmt_create gmtCreate , amount , `name`, outer_platform_id outerPlatformId
        from order_outer_info
        where delivery_date <![CDATA[<]]> #{deliveryDate} and delivery_status <![CDATA[<]]> 2 and delivery_status is not null
                and delivery_date >= '2021-09-01'
    </select>

    <select id="selectOrderOuter" resultType="net.summerfarm.model.domain.OrderOuterInfo">
        select info.id,info.order_no orderNo,info.m_id mId,info.xm_order_no xmOrderNo
        from order_outer_info info
        left join order_outer_item item on item.order_no=info.order_no
        where info.gmt_create > #{gmtCreate} and info.m_id = #{mId}
        and info.xm_order_no is null
        and item.xm_sku in
        <foreach collection="orderOuterItemList" open="(" close=")" separator="," item="itemList">
            #{itemList.xmSku}
        </foreach>
        and item.amount in
        <foreach collection="orderOuterItemList" open="(" close=")" separator="," item="itemList">
            #{itemList.amount}
        </foreach>
    </select>

    <update id="updateOrderOuterById" parameterType="net.summerfarm.model.domain.OrderOuterInfo">
        update order_outer_info
        <set>
            <if test= "xmOrderNo != null">
                xm_order_no = #{xmOrderNo},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectOrderOuterInfo" resultType="net.summerfarm.model.vo.OrderOuterInfoVo" parameterType="net.summerfarm.model.vo.OrderOuterInfoVo" >
        select
            distinct oi.id,oi.gmt_create orderTime,oi.outer_platform_id outerPlatformId,op.outer_platform_name outerPlatformName,oi.m_id outerMId,oi.xm_id xMId,m.mname,oi.amount,oi.mphone,oi.address,
               oi.order_no outerOrderNo, oi.delivery_date deliveryDate, oi.failure_reason failureReason,oi.xm_order_no xmOrderNo,oi.order_success_time orderSuccessTime
        from order_outer_info oi
        left join merchant m on m.m_id=oi.xm_id
        left join order_outer_item ooi on ooi.order_no=oi.order_no and ooi.outer_platform_id=oi.outer_platform_id
        left join outer_platform op on op.outer_platform_id=oi.outer_platform_id
        where oi.status = #{status} AND DATE_FORMAT(oi.gmt_create, '%Y%m%d') >= '20210901'
        <if test="startTime != null">
            AND DATE_FORMAT(oi.gmt_create, '%Y%m%d')<![CDATA[ >= ]]> DATE_FORMAT(#{startTime}, '%Y%m%d')
        </if>
        <if test="endTime != null">
            AND DATE_FORMAT(oi.gmt_create, '%Y%m%d') <![CDATA[ <= ]]> DATE_FORMAT(#{endTime}, '%Y%m%d')
        </if>
        <if test="outerPlatformId != null">
            AND oi.outer_platform_id = #{outerPlatformId}
        </if>
        <if test="xmSku != null">
            AND ooi.xm_sku = #{xmSku}
        </if>
        <if test="mphone != null">
            AND oi.mphone = #{mphone}
        </if>
        <if test="outerOrderNo != null">
            AND oi.order_no = #{outerOrderNo}
        </if>
        <if test="xmOrderNo != null">
            AND oi.xm_order_no = #{xmOrderNo}
        </if>
        order by oi.gmt_create desc
    </select>

    <select id="selectPlaceOrder" resultType="net.summerfarm.model.domain.OrderOuterInfo" parameterType="net.summerfarm.model.vo.OrderOuterInfoVo" >
        select
            id,m_id mId,order_no orderNo,delivery_date deliveryDate,mphone,address,remark,amount,failure_reason failureReason,outer_platform_id outerPlatformId,
            province,city,area,detailed_address detailedAddress
        from order_outer_info
        where id in
            <foreach collection="placeOrderIds" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
    </select>

    <update id="updateFailureReason">
        update order_outer_info
            set failure_reason = #{failureReason}
        where id = #{id}
    </update>

    <insert id="insertItemList" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into order_outer_item (order_no, sku, amount, standard, unit, name, xm_sku, pd_name,create_time,outer_platform_id,item_id) values
        <foreach collection="list" separator="," item="item">
            (#{item.orderNo}, #{item.sku}, #{item.amount},
            #{item.standard},#{item.unit}, #{item.name},
            #{item.xmSku}, #{item.pdName},now(),#{item.outerPlatformId},#{item.itemId})
        </foreach>
    </insert>

    <update id="updateOrderOuterItem">
        update order_outer_item
        set xm_sku = #{xmSku},pd_name = #{pdName},outer_platform_id = #{outerPlatformId}
        where id = #{id}
    </update>

    <select id="selectOrderOuterItem" resultType="net.summerfarm.model.domain.OrderOuterItem">
        select
        id,order_no orderNo,sku,amount,standard,unit,name,xm_sku xmSku,pd_name pdName,outer_platform_id outerPlatformId,item_id itemId
        from order_outer_item
        where order_no = #{orderNo} and outer_platform_id = #{outerPlatformId}
    </select>

    <select id="outerOrderReport" resultType="net.summerfarm.model.vo.OrderOuterInfoVo" parameterType="net.summerfarm.model.vo.OrderOuterInfoVo" >
        select oi.id,
               oi.gmt_create        gmtCreate,
               oi.m_id              mId,
               oi.order_no          orderNo,
               oi.xm_order_no       xmOrderNo,
               oi.status,
               oi.outer_platform_id outerPlatformId
        from order_outer_info oi
        left join outer_platform op on op.outer_platform_id = oi.outer_platform_id
        where op.order_report_switch = 1
          and DATE_FORMAT(oi.gmt_create, '%Y-%m-%d') = DATE_FORMAT(#{dailyTime}, '%Y-%m-%d')
    </select>

    <select id="orderDeliveryNoticeList" resultType="net.summerfarm.model.vo.OrderOuterInfoVo">
        SELECT o.order_no xmOrderNo
        FROM delivery_plan dp
                 INNER JOIN orders o ON dp.order_no = o.order_no
            AND dp.delivery_time = #{deliveryTime}
            AND dp.status IN (2,3,6)
            AND o.status IN (2,3,6)
            AND o.type = 3 AND o.operate_id is null
    </select>

</mapper>