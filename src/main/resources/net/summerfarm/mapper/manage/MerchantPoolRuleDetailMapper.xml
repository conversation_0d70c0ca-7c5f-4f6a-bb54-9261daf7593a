<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MerchantPoolRuleDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantPoolRuleDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pool_info_id" jdbcType="BIGINT" property="poolInfoId" />
    <result column="rule_detail" jdbcType="LONGVARCHAR" property="ruleDetail" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    `id`, `pool_info_id`, `rule_detail`, `create_time`, `update_time`
  </sql>

  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_pool_rule_detail
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteById" parameterType="java.lang.Long">
    delete from merchant_pool_rule_detail
    where `id` = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.MerchantPoolRuleDetail" keyProperty="id" useGeneratedKeys="true">
    insert into merchant_pool_rule_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="poolInfoId != null">
        `pool_info_id`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
      <if test="ruleDetail != null">
        `rule_detail`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="poolInfoId != null">
        #{poolInfoId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleDetail != null">
        #{ruleDetail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="net.summerfarm.model.domain.MerchantPoolRuleDetail">
    update merchant_pool_rule_detail
    <set>
      <if test="poolInfoId != null">
        `pool_info_id` = #{poolInfoId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ruleDetail != null">
        `rule_detail` = #{ruleDetail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_pool_rule_detail
    where `pool_info_id` = #{poolInfoId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByInfoId" parameterType="java.lang.Long">
    delete from merchant_pool_rule_detail
    where `pool_info_id` = #{poolInfoId,jdbcType=BIGINT}
  </delete>

  <update id="updateByInfoId">
    update merchant_pool_rule_detail
    set `rule_detail` = #{ruleDetail}
    where `pool_info_id` = #{poolInfoId}
  </update>
</mapper>