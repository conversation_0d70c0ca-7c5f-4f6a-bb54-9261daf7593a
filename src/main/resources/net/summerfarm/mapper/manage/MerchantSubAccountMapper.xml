<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.MerchantSubAccountMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantSubAccount">
    <result column="account_id" property="accountId" jdbcType="BIGINT"/>
    <result column="m_id" property="mId" jdbcType="BIGINT"/>
    <result column="contact" property="contact" jdbcType="VARCHAR"/>
    <result column="phone" property="phone" jdbcType="VARCHAR"/>
    <result column="unionid" property="unionid" jdbcType="VARCHAR"/>
    <result column="openid" property="openid" jdbcType="VARCHAR"/>
    <result column="mp_openid" property="mpOpenid" jdbcType="VARCHAR"/>
    <result column="pop_view" property="popView" jdbcType="INTEGER"/>
    <result column="first_pop_view" property="firstPopView" jdbcType="INTEGER"/>
    <result column="cash_amount" property="cashAmount" jdbcType="DECIMAL"/>
    <result column="cash_update_time" property="cashUpdateTime" jdbcType="TIMESTAMP"/>
    <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
    <result column="last_order_time" property="lastOrderTime" jdbcType="TIMESTAMP"/>
    <result column="status" property="status" jdbcType="INTEGER"/>
    <result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
    <result column="m_info" property="mInfo" jdbcType="VARCHAR"/>
    <result column="register_time" property="registerTime" jdbcType="TIMESTAMP"/>
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
    <result column="audit_user" property="auditUser" jdbcType="INTEGER"/>
    <result column="type" property="type" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Base_Column_List">
    account_id, m_id, contact,
      phone, unionid, openid,
      mp_openid, pop_view, first_pop_view,
      cash_amount, cash_update_time, login_time,
      last_order_time, status, delete_flag,
      m_info, register_time, audit_time,
      audit_user,`type`
  </sql>
  <insert id="insert" parameterType="net.summerfarm.model.domain.MerchantSubAccount">
    insert into merchant_sub_account (account_id, m_id, contact,
      phone, unionid, openid,
      mp_openid, pop_view, first_pop_view,
      cash_amount, cash_update_time, login_time,
      last_order_time, status, delete_flag,
      m_info, register_time, audit_time,
      audit_user,`type`)
    values (#{accountId,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, #{contact,jdbcType=VARCHAR},
      #{phone,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, #{openid,jdbcType=VARCHAR},
      #{mpOpenid,jdbcType=VARCHAR}, #{popView,jdbcType=INTEGER}, #{firstPopView,jdbcType=INTEGER},
      #{cashAmount,jdbcType=DECIMAL}, #{cashUpdateTime,jdbcType=TIMESTAMP}, #{loginTime,jdbcType=TIMESTAMP},
      #{lastOrderTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{deleteFlag,jdbcType=INTEGER},
      #{mInfo,jdbcType=VARCHAR}, #{registerTime,jdbcType=TIMESTAMP}, #{auditTime,jdbcType=TIMESTAMP},
      #{auditUser,jdbcType=INTEGER},#{type,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.MerchantSubAccount" useGeneratedKeys="true"
          keyColumn="account_id" keyProperty="accountId">
    insert into merchant_sub_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="contact != null">
        contact,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="unionid != null">
        unionid,
      </if>
      <if test="openid != null">
        openid,
      </if>
      <if test="mpOpenid != null">
        mp_openid,
      </if>
      <if test="popView != null">
        pop_view,
      </if>
      <if test="firstPopView != null">
        first_pop_view,
      </if>
      <if test="cashAmount != null">
        cash_amount,
      </if>
      <if test="cashUpdateTime != null">
        cash_update_time,
      </if>
      <if test="loginTime != null">
        login_time,
      </if>
      <if test="lastOrderTime != null">
        last_order_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="mInfo != null">
        m_info,
      </if>
      <if test="registerTime != null">
        register_time,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="auditUser != null">
        audit_user,
      </if>
      <if test="type != null">
        `type`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="contact != null">
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="openid != null">
        #{openid,jdbcType=VARCHAR},
      </if>
      <if test="mpOpenid != null">
        #{mpOpenid,jdbcType=VARCHAR},
      </if>
      <if test="popView != null">
        #{popView,jdbcType=INTEGER},
      </if>
      <if test="firstPopView != null">
        #{firstPopView,jdbcType=INTEGER},
      </if>
      <if test="cashAmount != null">
        #{cashAmount,jdbcType=DECIMAL},
      </if>
      <if test="cashUpdateTime != null">
        #{cashUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loginTime != null">
        #{loginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastOrderTime != null">
        #{lastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="mInfo != null">
        #{mInfo,jdbcType=VARCHAR},
      </if>
      <if test="registerTime != null">
        #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditUser != null">
        #{auditUser,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="selectByMId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    where m_id = #{mId} and delete_flag = 1
    <if test="status != null">
      and status = #{status}
    </if>
    order by `type` asc, status desc, account_id asc
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account where delete_flag = 1 and account_id = #{accountId}
  </select>
  <select id="selectByPrimaryKeyIgnoreDel" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account where account_id = #{accountId}
  </select>
  <update id="deleteByPrimaryKey">
    update merchant_sub_account set delete_flag = 0 where account_id = #{accountId}
  </update>
  <update id="updateSelective" parameterType="net.summerfarm.model.domain.MerchantSubAccount">
    update merchant_sub_account
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="contact != null">
        contact = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="openid != null">
        openid = #{openid,jdbcType=VARCHAR},
      </if>
      <if test="mpOpenid != null">
        mp_openid = #{mpOpenid,jdbcType=VARCHAR},
      </if>
      <if test="popView != null">
        pop_view = #{popView,jdbcType=INTEGER},
      </if>
      <if test="firstPopView != null">
        first_pop_view = #{firstPopView,jdbcType=INTEGER},
      </if>
      <if test="cashAmount != null">
        cash_amount = #{cashAmount,jdbcType=DECIMAL},
      </if>
      <if test="cashUpdateTime != null">
        cash_update_time = #{cashUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loginTime != null">
        login_time = #{loginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastOrderTime != null">
        last_order_time = #{lastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="mInfo != null">
        m_info = #{mInfo,jdbcType=VARCHAR},
      </if>
      <if test="registerTime != null">
        register_time = #{registerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditUser != null">
        audit_user = #{auditUser,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER}
      </if>
    </set>
    where account_id = #{accountId}
  </update>
  <select id="selectMerchantMerge" resultType="net.summerfarm.model.vo.MerchantMergeVO">
    select m.m_id mId,
    m.mname mname,
    m.phone phone,
    a.admin_id bdId,
    a.realname bdName,
    ae.large_area_no orderStoreNo,
    m.area_no orderAreaNo,
    m.merge_admin mergeAdmin,
    m.merge_time mergeTime
    from merchant m
    left join follow_up_relation fur on m.m_id = fur.m_id and fur.reassign = 0
    left join admin a on fur.admin_id = a.admin_id
    left join area ae on ae.area_no = m.area_no
    <where>
      <if test="mname != null">
        and m.mname like concat('%',#{mname},'%')
      </if>
      <if test="phone != null">
        and m.phone like concat('%',#{phone},'%')
      </if>
      <if test="bdId != null">
        and a.admin_id = #{bdId}
      </if>
      <if test="baseUserIds != null and baseUserIds.size!=0">
        AND a.base_user_id in
        <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
  </select>
  <select id="selectMerchantInfo" parameterType="net.summerfarm.model.vo.MerchantMergeVO"
          resultType="net.summerfarm.model.vo.MerchantMergeVO">
    select m.m_id mId,
    m.mname mname,
    m.phone phone,
    a.admin_id bdId,
    a.realname bdName,
    ae.large_area_no orderStoreNo,
    m.area_no orderAreaNo,
    m.size mType,
    a2.name_remakes nameRemarks
    from merchant m
    left join follow_up_relation fur on m.m_id = fur.m_id and fur.reassign = 0
    left join admin a on fur.admin_id = a.admin_id
    left join area ae on ae.area_no = m.area_no
    left join admin a2 on m.admin_id = a2.admin_id
    <where>
      <if test="orderStoreNo != null">
        and ae.large_area_no = #{orderStoreNo}
      </if>
      <if test="orderAreaNo != null">
        and m.area_no = #{orderAreaNo}
      </if>
      <if test="mId != null">
        and m.m_id = #{mId}
      </if>
      <if test="mname != null">
        and m.mname like concat('%',#{mname},'%')
      </if>
      <if test="phone != null">
        and m.phone like concat('%',#{phone},'%')
      </if>
      <if test="baseUserIds != null and baseUserIds.size!=0">
        AND a.base_user_id in
        <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
  </select>
  <insert id="bakMerchantInfo" parameterType="net.summerfarm.model.domain.Merchant">
  insert into merchant_bak
  (m_id, role_id, mname, mcontact, openid, phone, islock, rank_id, register_time, login_time, invitecode, channel_code,
   inviter_channel_code, audit_time, audit_user, business_license, remark, shop_sign, other_proof, last_order_time,
   area_no, size, type, trade_area, trade_group, unionid, mp_openid, admin_id, direct, server, pop_view, member_integral,
   grade, sku_show, recharge_amount)
  select m_id, role_id, mname, mcontact, openid, phone, islock, rank_id, register_time, login_time, invitecode, channel_code,
   inviter_channel_code, audit_time, audit_user, business_license, remark, shop_sign, other_proof, last_order_time,
   area_no, size, type, trade_area, trade_group, unionid, mp_openid, admin_id, direct, server, pop_view, member_integral,
   grade, sku_show, recharge_amount
  from merchant
  where m_id = #{mId}
 </insert>
  <select id="selectManageByMid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account where type = 0 and m_id = #{mId} and delete_flag = 1
  </select>

  <select id="selectManageByMidDeleted" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account where type = 0 and m_id = #{mId} and delete_flag = 0
  </select>

  <select id="selectMergeDetail" resultType="net.summerfarm.model.vo.MerchantMergeVO">
    select m.m_id mId,
    m.mname mname,
    m.phone mphone,
    a.admin_id bdId,
    a.realname bdName,
    ae.large_area_no orderStoreNo,
    m.area_no orderAreaNo,
    m.size mType,
    a2.name_remakes nameRemarks,
    msa.type,
    msa.account_id accountId,
    msa.contact contact,
    msa.phone phone
    from merchant_sub_account  msa
    left join merchant m on m.m_id = msa.m_id
    left join follow_up_relation fur on msa.m_id = fur.m_id and fur.reassign = 0
    left join admin a on fur.admin_id = a.admin_id
    left join area ae on ae.area_no = m.area_no
    left join admin a2 on m.admin_id = a2.admin_id
    where msa.m_id = #{mId} and msa.delete_flag = 1
    <if test="baseUserIds != null and baseUserIds.size!=0">
      and a.base_user_id in
      <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    order by msa.type asc
  </select>
  <update id="backToUnAudit" parameterType="list">
    update merchant_sub_account set status = 0 where delete_flag = 1 and m_id in
    (
    <foreach collection="mIdList" separator="," item="item">
      #{item}
    </foreach>
    )
  </update>
  <update id="increaseCashAmount">
    update merchant_sub_account set cash_amount = cash_amount + #{amount} where account_id = #{accountId}
  </update>

  <insert id="insertRecord" parameterType="net.summerfarm.model.domain.MerchantSubAccount">
     insert into merchant_sub_account ( m_id, contact,
      phone, openid,register_time,type)
    values (#{mId,jdbcType=BIGINT}, #{contact,jdbcType=VARCHAR},
      #{phone,jdbcType=VARCHAR}, #{openid,jdbcType=VARCHAR},
     #{registerTime,jdbcType=TIMESTAMP},#{type,jdbcType=INTEGER})
  </insert>
  <select id="selectByPhone" resultType="net.summerfarm.model.domain.Merchant">
    select m.m_id mId,
       mname,
       mcontact,
       m.phone
    from merchant_sub_account msa inner join merchant m on msa.m_id = m.m_id
    where msa.phone = #{callPhone} and msa.delete_flag = 1
  </select>
  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    <where>
      delete_flag = 1
      <if test="accountId != null">
        and account_id = #{accountId}
      </if>
      <if test="phone != null">
        and phone = #{phone}
      </if>
      <if test="unionid != null">
        and unionid = #{unionid}
      </if>
      <if test="openId != null">
        and openid = #{openId}
      </if>
      <if test="mpOpenId != null">
        and mp_openid = #{mpOpenId}
      </if>
    </where>
  </select>

  <select id="selectByPhoneNum" resultType="net.summerfarm.model.domain.Merchant">
    select m.m_id mId,
       mname,
       mcontact,
       m.phone
    from merchant_sub_account msa inner join merchant m on msa.m_id = m.m_id
    where msa.phone = #{callPhone} and msa.delete_flag = 1 and msa.type = 1
  </select>
  <select id="selectByPrimaryKeyList" resultType="net.summerfarm.model.domain.MerchantSubAccount">
    select
    account_id as  accountId, m_id as mId, contact,
    phone, unionid, openid,
    mp_openid as mpOpenid, pop_view as popView, first_pop_view as firstPopView,
    cash_amount as cashAmount, cash_update_time as cashUpdateTime, login_time as loginTime,
    last_order_time as lastOrderTime, status, delete_flag as deleteFlag,
    m_info as mInfo, register_time as registerTime, audit_time as auditTime,
    audit_user as auditUser,`type`
    from merchant_sub_account where account_id in
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>

  <select id="batchGetByPhoneAndPass" resultType="net.summerfarm.model.domain.Merchant">
    select m.m_id mId,
           m.mname,
           m.phone,
           m.size,
           m.direct
    from merchant m
    where m.islock = 0
      and m.m_id in
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>


  <update id="updateMain2Base" >
    update merchant_sub_account set m_id =#{updateMid},type=1  where m_id =#{mId} and delete_flag = 1 and status  =1 and type = 0
  </update>
</mapper>