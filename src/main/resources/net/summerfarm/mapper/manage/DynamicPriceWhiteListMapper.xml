<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DynamicPriceWhiteListMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DynamicPriceWhiteList">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="sku" jdbcType="VARCHAR" property="sku"/>
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo"/>
    <result column="category_type" jdbcType="TINYINT" property="categoryType"/>
    <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `sku`, `warehouse_no`, `category_type`, `updater_id`, `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_white_list
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from dynamic_price_white_list
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.DynamicPriceWhiteList">
    insert into dynamic_price_white_list (`id`, `sku`, `warehouse_no`,
                                          `category_type`, `updater_id`, `create_time`,
                                          `update_time`)
    values (#{id,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{warehouseNo,jdbcType=INTEGER},
            #{categoryType,jdbcType=TINYINT}, #{updaterId,jdbcType=INTEGER},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.DynamicPriceWhiteList">
    insert into dynamic_price_white_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="sku != null">
        `sku`,
      </if>
      <if test="warehouseNo != null">
        `warehouse_no`,
      </if>
      <if test="categoryType != null">
        `category_type`,
      </if>
      <if test="updaterId != null">
        `updater_id`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="categoryType != null">
        #{categoryType,jdbcType=TINYINT},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.model.domain.DynamicPriceWhiteList">
    update dynamic_price_white_list
    <set>
      <if test="sku != null">
        `sku` = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        `warehouse_no` = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="categoryType != null">
        `category_type` = #{categoryType,jdbcType=TINYINT},
      </if>
      <if test="updaterId != null">
        `updater_id` = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DynamicPriceWhiteList">
    update dynamic_price_white_list
    set `sku`           = #{sku,jdbcType=VARCHAR},
        `warehouse_no`  = #{warehouseNo,jdbcType=INTEGER},
        `category_type` = #{categoryType,jdbcType=TINYINT},
        `updater_id`    = #{updaterId,jdbcType=INTEGER},
        `create_time`   = #{createTime,jdbcType=TIMESTAMP},
        `update_time`   = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertBatch" parameterType="net.summerfarm.model.domain.DynamicPriceWhiteList">
    insert into dynamic_price_white_list (`sku`, `warehouse_no`,
    `category_type`, `updater_id`)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.sku,jdbcType=VARCHAR}, #{item.warehouseNo,jdbcType=INTEGER},
      #{item.categoryType,jdbcType=TINYINT}, #{item.updaterId,jdbcType=INTEGER})
    </foreach>
  </insert>

  <select id="selectBySku" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_white_list
    where `sku` = #{sku}
  </select>

  <delete id="deleteBySku" parameterType="java.lang.String">
    delete
    from dynamic_price_white_list
    where `sku` = #{sku}
  </delete>

  <select id="listByQuery" parameterType="net.summerfarm.model.DTO.inventory.WhiteListQueryDTO"
    resultType="string">
    select distinct dpwl.sku
    from dynamic_price_white_list dpwl
    left join inventory i on dpwl.sku = i.sku
    left join products p on i.pd_id=p.pd_id
    <where>
      <if test="categoryType != null">
        and dpwl.category_type = #{categoryType}
      </if>
      <if test="pdName != null">
        and p.pd_name like CONCAT('%',#{pdName},'%')
      </if>
      <if test="sku != null">
        and dpwl.sku = #{sku}
      </if>
      <if test="warehouseNos != null and warehouseNos.size > 0">
        and dpwl.warehouse_no in
        <foreach collection="warehouseNos" item="warehouseNo" open="(" separator="," close=")">
          #{warehouseNo}
        </foreach>
      </if>
    </where>
    order by dpwl.update_time desc
  </select>

  <select id="listBySkus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_white_list
    where sku in
    <foreach collection="skus" item="sku" open="(" separator="," close=")">
      #{sku}
    </foreach>
  </select>

  <select id="listByCategoryType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_white_list
    where category_type = #{categoryType}
  </select>
</mapper>