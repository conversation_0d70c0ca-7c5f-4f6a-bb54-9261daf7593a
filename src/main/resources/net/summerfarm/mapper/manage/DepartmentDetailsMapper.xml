<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DepartmentDetailsMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DepartmentDetails">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="dept_id" jdbcType="BIGINT" property="deptId" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="parent_id" jdbcType="BIGINT" property="parentId" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DepartmentDetails" useGeneratedKeys="true">
        insert into department_details (dept_id,name,parent_id,status,create_time) value (#{deptId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT},1,#{createTime,jdbcType=TIMESTAMP})
    </insert>

    <select id="selectByDept" resultMap="BaseResultMap">
        select * from department_details where status = 1
    </select>

    <select id="selectByName" resultMap="BaseResultMap">
        select * from department_details where dept_id = #{deptId}
    </select>

    <update id="update" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DepartmentDetails" useGeneratedKeys="true">
        update department_details
        set name=#{name},parent_id=#{parentId},status = 1,update_time=#{updateTime}
        where dept_id=#{deptId}
    </update>
    <update id="updateDeptStatus" parameterType="net.summerfarm.model.domain.DepartmentDetails">
        update department_details
        set status = 0
    </update>
    <select id="selectById" parameterType="long" resultType="long">
        select parent_id from department_details where dept_id = #{deptId} and status = 1
    </select>

    <select id="selectByIdList" resultType="long">
        select parent_id from department_details
        WHERE dept_id IN
        <foreach collection="deptIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and status = 1
    </select>
</mapper>