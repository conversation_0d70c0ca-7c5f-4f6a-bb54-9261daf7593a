<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTakingDetailMapper">


    <select id="select" resultType="net.summerfarm.model.domain.StockTakingDetail"
            parameterType="net.summerfarm.model.domain.StockTakingDetail">
        select
        id,taking_id takingId, sku ,sku_name skuName,weight,
        quantity,quality_date qualityDate,batch,real_quantity realQuantity,reason,status
        from stocktakingdetail
        <where>
            <if test="sku != null">
                AND sku=#{sku}
            </if>
            <if test="batch != null">
                AND batch=#{batch}
            </if>
            <if test="skuName != null">
                AND sku_name LIKE CONCAT('%',#{skuName,jdbcType=VARCHAR},'%')
            </if>
            <if test="takingId != null">
                AND taking_id =#{takingId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        order by skuName desc
    </select>

    <insert id="insert" parameterType="net.summerfarm.model.domain.StockTakingDetail">
        insert into stocktakingdetail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="takingId != null">
                taking_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="skuName != null">
                sku_name,
            </if>
            <if test="weight != null">
                weight,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="qualityDate != null">
                quality_date,
            </if>
            <if test="batch != null">
                batch,
            </if>
            <if test="realQuantity != null">
                real_quantity,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="takingId != null">
                #{takingId},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="skuName != null">
                #{skuName},
            </if>
            <if test="weight != null">
                #{weight},
            </if>
            <if test="quantity != null">
                #{quantity},
            </if>
            <if test="qualityDate != null">
                #{qualityDate},
            </if>
            <if test="batch != null">
                #{batch},
            </if>
            <if test="realQuantity != null">
                #{realQuantity},
            </if>
            <if test="reason != null">
                #{reason},
            </if>
            <if test="status != null">
                #{status},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="net.summerfarm.model.domain.StockTakingDetail">
        insert into stocktakingdetail (taking_id,sku,sku_name,weight,quantity,batch,quality_date)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.takingId},#{item.sku},#{item.skuName},#{item.weight},#{item.quantity},#{item.batch},#{item.qualityDate})
        </foreach>
    </insert>

    <update id="update" parameterType="net.summerfarm.model.domain.StockTakingDetail">
        update stocktakingdetail
        <set>
            <if test="qualityDate != null">
                quality_date=#{qualityDate},
            </if>
            <if test="batch != null">
                batch=#{batch},
            </if>
            <if test="realQuantity != null">
                real_quantity=#{realQuantity},
            </if>
            <if test="quantity != null">
                quantity=#{quantity},
            </if>
            <if test="reason != null">
                reason=#{reason},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <delete id="delete">
        DELETE from stocktakingdetail where id=#{id}
    </delete>

    <select id="selectById" resultType="net.summerfarm.model.domain.StockTakingDetail">
        SELECT sku,status,quality_date qualityDate ,batch,taking_id takingId from stocktakingdetail where id=#{id}
    </select>


    <select id="selectOne" resultType="net.summerfarm.model.domain.StockTakingDetail" parameterType="net.summerfarm.model.domain.StockTakingDetail">
        SELECT  id from stocktakingdetail where
        batch=#{batch}  and taking_id=#{takingId} and        sku=#{sku}
        <choose>
            <when test="qualityDate == null">
                AND quality_date is null
            </when>
            <otherwise>
                AND quality_date = #{qualityDate}
            </otherwise>
        </choose>
    </select>
    <select id="selectByCondition" resultType="java.lang.Integer">
        select count(1)
        from stock_taking_item sti
                 left join stocktaking s on sti.taking_id = s.id
        where sti.sku =#{sku}
          and s.area_no = #{areaNo}
          and sti.state in(1,2)
    </select>

    <update id="updateNormal">
        update  stocktakingdetail set status=3 where sku in (select sku from ( select sku,quantity,sum(real_quantity) total from stocktakingdetail where  taking_id=#{takingId} group by sku) a
        where a.quantity =a.total)
    </update>

    <update id="updateUnNormal">
        update  stocktakingdetail set status=4 where sku in (select sku from ( select sku,quantity,sum(real_quantity) total from stocktakingdetail where  taking_id=#{takingId} group by sku) a
        where a.quantity !=a.total)
    </update>

    <select id="selectAll" resultType="net.summerfarm.model.domain.StockTakingDetail">
        select i.sku, i.weight,p.pd_name skuName, s.area_no areaNo,s.batch,s.quality_date qualityDate,s.store_quantity quantity ,ak.quantity areaQuantity from (select * from  (select * from store_record
                                                                                                                                                                                where area_no=#{areaNo}
                                                                                                                                                                                order by id desc) sr group by sr.batch,sr.quality_date,sr.sku,sr.area_no having store_quantity>0) s
                                                                                                                                                                   left join inventory i on s.sku=i.sku
                                                                                                                                                                   left join products p on p.pd_id=i.pd_id
                                                                                                                                                                   left join area_store ak on i.sku=ak.sku and ak.area_no=#{areaNo}
        where p.outdated=0 and i.outdated=0

        union

        select i.sku, i.weight,p.pd_name skuName, s.area_no areaNo,s.batch,s.quality_date qualityDate,s.store_quantity quantity ,ak.quantity areaQuantity from (select * from  (select * from store_record
                                                                                                                                                                                where area_no=#{areaNo}
                                                                                                                                                                                order by id desc) sr group by sr.sku,sr.area_no ) s
                                                                                                                                                                   left join inventory i on s.sku=i.sku
                                                                                                                                                                   left join products p on p.pd_id=i.pd_id
                                                                                                                                                                   left join area_store ak on i.sku=ak.sku and ak.area_no=#{areaNo}
        where   p.outdated=0 and i.outdated=0 and i.sku in (select sku from area_store
                                                            where  quantity=0 and area_no=#{areaNo} )
    </select>
</mapper>