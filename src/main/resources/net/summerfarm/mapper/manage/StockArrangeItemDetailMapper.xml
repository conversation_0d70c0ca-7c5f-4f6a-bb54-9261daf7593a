<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockArrangeItemDetailMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.StockArrangeItemDetail">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="stock_arrange_item_id" property="stockArrangeItemId" jdbcType="VARCHAR"/>
        <result column="stock_arrange_id" property="stockArrangeId" jdbcType="VARCHAR"/>
        <result column="stock_task_id" property="stockTaskId" jdbcType="VARCHAR"/>
        <result column="arr_quantity" property="arrQuantity" jdbcType="VARCHAR"/>
        <result column="quantity" property="quantity" jdbcType="VARCHAR"/>
        <result column="gl_no" property="glNo" jdbcType="VARCHAR"/>
        <result column="production_date" property="productionDate" jdbcType="VARCHAR"/>
        <result column="quality_date" property="qualityDate" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List" >
    id, sku, stock_arrange_item_id stockArrangeItemId, stock_arrange_id stockArrangeId, stock_task_id stockTaskId, arr_quantity arrQuantity, quantity, gl_no glNo,production_date productionDate,quality_date qualityDate,creator,create_time createTime,updater,update_time updateTime
  </sql>

    <insert id="insertByPurchases" keyProperty="id" useGeneratedKeys="true">
        insert into stock_arrange_item_detail (sku,stock_arrange_item_id,stock_arrange_id,
        stock_task_id,arr_quantity,quantity,gl_no,production_date,quality_date,creator,create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.sku},#{item.stockArrangeItemId},#{item.stockArrangeId},#{item.stockTaskId},#{item.arrQuantity},#{item.quantity}
            ,#{item.glNo},#{item.productionDate},#{item.qualityDate},#{item.creator},now())
        </foreach>
    </insert>

    <insert id="insert" parameterType="net.summerfarm.model.domain.StockArrangeItemDetail" keyProperty="id" useGeneratedKeys="true">
    insert into stock_arrange_item_detail (sku,stock_arrange_item_id,stock_arrange_id,
        stock_task_id,arr_quantity,quantity,gl_no,production_date,quality_date,creator,create_time)
    values (#{sku,jdbcType=VARCHAR}, #{stockArrangeItemId,jdbcType=INTEGER},#{stockArrangeId,jdbcType=INTEGER}, #{stockTaskId,jdbcType=INTEGER},
      #{arrQuantity,jdbcType=INTEGER}, #{quantity,jdbcType=DECIMAL}, #{glNo,jdbcType=DECIMAL},#{productionDate,jdbcType=INTEGER},#{qualityDate,jdbcType=INTEGER},
      #{creator,jdbcType=INTEGER}, now())
  </insert>

    <select id="selectByTaskId" resultType="net.summerfarm.model.vo.StockArrangeItemDetailVO">
        select
        said.id, sai.sku, sai.id stockArrangeItemId,sai.stock_arrange_id stockArrangeId, sa.stock_task_id stockTaskId, if(said.arr_quantity is null,sai.arrival_quantity,said.arr_quantity)  arrQuantity, said.quantity,pp.quantity purchaseQuantity,
        gl_no glNo,said.production_date productionDate,said.quality_date qualityDate,said.creator,said.create_time createTime,said.updater,said.update_time updateTime,sai.arrival_quantity arrivalQuantity,
        sai.actual_quantity actualQuantity,sai.pd_name pdName, sai.weight, sai.supplier ,sai.type ,sai.quality_time qualityTime ,sai.quality_time_unit qualityTimeUnit,sa.purchase_no purchaseNo,pp.id purchasePlanId
        from  stock_arrange sa
        left join stock_arrange_item sai on sai.stock_arrange_id = sa.id
        left join stock_arrange_item_detail said on sai.id = said.stock_arrange_item_id
        left join purchases_plan pp on pp.purchase_no= sa.purchase_no and pp.sku = sai.sku  and pp.plan_status = 1  and pp.origin_id IS NULL
        where sa.stock_task_id = #{id}
    </select>

    <select id="selectByStockArrangeId" resultType="net.summerfarm.model.vo.StockArrangeItemDetailVO" >
         select
        said.id, sai.sku, sai.id stockArrangeItemId,sai.stock_arrange_id stockArrangeId, stock_task_id stockTaskId, if(said.arr_quantity is null,sai.arrival_quantity,said.arr_quantity) arrQuantity, quantity,
        gl_no glNo,said.production_date productionDate,said.quality_date qualityDate,said.creator,said.create_time createTime,said.updater,said.update_time updateTime,sai.arrival_quantity arrivalQuantity,
        sai.pd_name pdName, sai.weight, sai.supplier ,sai.type ,sai.quality_time qualityTime ,sai.quality_time_unit qualityTimeUnit, if(count(wbpr.id) > 0, 1, 0) proveStatus,i.volume,i.weight_num weightNum,
        if(i.sku_pic is null, p.picture_path, i.sku_pic) pic,i.unit unit,sai.supplier_Id as supplierId
        from stock_arrange_item sai
        left join inventory i on sai.sku=i.sku
        left join products p on i.pd_id = p.pd_id
        left join stock_arrange_item_detail said on said.stock_arrange_item_id = sai.id
        left join (select * from warehouse_batch_prove_record where type = 9) wbpr on wbpr.source_id = said.id
        where sai.stock_arrange_id =  #{id} GROUP BY said.id order by sai.sku
    </select>

    <select id="selectByStockTaskId" resultType="net.summerfarm.model.domain.StockArrangeItemDetail">
        select stock_arrange_id stockArrangeId
        from stock_arrange_item_detail
        where stock_task_id = #{stockTaskId}
    </select>

    <select id="selectByStockArrangeItemId" resultType="net.summerfarm.model.vo.StockArrangeItemDetailVO">
        select
            said.id, sai.sku, sai.id stockArrangeItemId,sai.stock_arrange_id stockArrangeId, stock_task_id stockTaskId, if(said.arr_quantity is null,sai.arrival_quantity,said.arr_quantity) arrQuantity, quantity receiveQuantity,
            gl_no glNo,production_date productionDate,quality_date qualityDate,said.creator,said.create_time createTime,said.updater,said.update_time updateTime,
            sai.pd_name pdName, sai.weight, sai.supplier ,sai.type ,sai.quality_time qualityTime ,sai.quality_time_unit qualityTimeUnit
        from stock_arrange_item_detail said
        left join stock_arrange_item sai on said.stock_arrange_item_id = sai.id
        where sai.id =  #{stockArrangeItemId}
    </select>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.StockArrangeItemDetail">
        update stock_arrange_item_detail
        set quantity=quantity + #{batchQuantity}
        where  id = #{id}
    </update>


    <select id="getQuantityById" resultType="java.lang.Integer">
        select quantity
        from stock_arrange_item_detail where id =#{id}
    </select>
    <select id="selectByPurchaseNoSupplierSku" resultType="net.summerfarm.model.vo.StockArrangeItemDetailVO">
        select said.* from stock_arrange_item_detail said
        left join stock_arrange_item sai on said.stock_arrange_item_id = sai.id
        left join stock_arrange sa on sai.stock_arrange_id = sa.id
        where sa.purchase_no = #{purchaseNo} and sai.supplier_id = #{supplierId} and said.sku = #{sku}
    </select>

    <select id="selectById" resultType="net.summerfarm.model.vo.StockArrangeItemDetailVO">
        select sa.purchase_no purchaseNo,
            d.id id,
            d.sku sku,
            d.stock_arrange_item_id stockArrangeItemId,
            d.stock_arrange_id stockArrangeId,
            d.stock_task_id stockTaskId,
            d.arr_quantity arrQuantity,
            d.quantity quantity,
            d.gl_no glNo,
            d.production_date productionDate,
            d.quality_date qualityDate,
            d.create_time createTime,
            d.creator creator,
            d.update_time updateTime,
            d.updater updater
        from stock_arrange_item_detail d
        left join stock_arrange sa on d.stock_arrange_id = sa.id
        where d.id =#{stockArrangeItemDetailId}
    </select>
</mapper>