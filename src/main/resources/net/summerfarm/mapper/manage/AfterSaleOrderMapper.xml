<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.AfterSaleOrderMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.vo.AfterSaleOrderVO">
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
        <result column="after_sale_order_no" jdbcType="VARCHAR" property="afterSaleOrderNo" />
        <result column="proof_pic" jdbcType="VARCHAR" property="proofPic" />
    </resultMap>

    <select id="selectCount" resultType="Long"
            parameterType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select
        COUNT(*)
        FROM after_sale_order t
        LEFT JOIN after_sale_proof p ON p.after_sale_order_no = t.after_sale_order_no
        <if test="mname != null or phone !=null or areaNo != null or queryAreaNoList != null">
            LEFT JOIN merchant m on t.m_id = m.m_id
        </if>
        <if test="deliveryStatus != null and deliveryStatus > 0">
            left join  after_sale_delivery_path asdp on asdp.after_sale_no = t.after_sale_order_no
        </if>
        <if test="pdId != null">
            LEFT JOIN order_item oi on t.order_no = oi.order_no AND t.sku = oi.sku and oi.suit_id=t.suit_id
            LEFT join inventory i on i.sku = oi.sku
        </if>
        <if test="mSize != null">
            LEFT JOIN orders o on t.order_no = o.order_no
        </if>
        <if test="queryStoreNoList != null and queryStoreNoList.size != 0">
            LEFT JOIN delivery_plan dp on dp.order_no = t.order_no and dp .id = t.delivery_id
        </if>
        <where>
            <if test="mname != null">
                AND m.mname like concat(#{mname},'%')
            </if>
            <if test="afterSaleOrderStatus != null">
                AND t.after_sale_order_status = #{afterSaleOrderStatus}
            </if>
            <if test="phone !=null and phone != ''">
                and m.phone like concat(#{phone},'%')
            </if>
            <if test="areaNo != null">
                AND m.area_no = #{areaNo}
            </if>
            <if test="queryAreaNoList != null and queryAreaNoList.size != 0">
                and m.area_no in
                <foreach collection="queryAreaNoList" open="(" close=")" separator="," item="it">
                    #{it}
                </foreach>
            </if>
            <if test="startTime != null">
                AND t.add_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND t.add_time <![CDATA[<]]> #{endTime}
            </if>
            <if test="afterSaleOrderNo != null">
                AND t.after_sale_order_no = #{afterSaleOrderNo}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo}
            </if>
            <if test="type != null">
                AND t.type = #{type}
            </if>
            <if test="deliveryed != null">
                AND t.deliveryed = #{deliveryed}
            </if>
            <if test="pdId != null">
                AND i.pd_id = #{pdId}
            </if>
            <if test="mSize != null">
                AND o.m_size = #{mSize}
            </if>
            <if test="handleType != null">
                AND p.handle_type = #{handleType}
            </if>
            <if test="deliveryStatus != null and deliveryStatus == 0">
                AND p.handle_type in (0,1,2,3)
            </if>
            <if test="handleTypeList != null and handleTypeList.size != 0">
                and p.handle_type in
                <foreach collection="handleTypeList" open="(" close=")" separator="," item="it">
                    #{it}
                </foreach>
            </if>
            <if test="deliveryStatus != null and deliveryStatus > 0">
                AND asdp.status = #{deliveryStatus}
            </if>
            <if test="queryStoreNoList != null and queryStoreNoList.size != 0">
                and t.order_no in
                <foreach collection="orderNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="select" resultType="net.summerfarm.model.vo.AfterSaleOrderVO"
            parameterType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select
        p.recovery_num recoveryNum,t.after_sale_order_status afterSaleOrderStatus,
        t.delivery_id deliveryId,o.delivery_fee deliveryFee,
        t.id, t.after_sale_order_no afterSaleOrderNo, t.m_id mId, t.order_no orderNo, t.sku, t.status,t.after_sale_unit afterSaleUnit,
        t.add_time addTime, m.mname,a.area_name areaName,oi.pd_name pdName,oi.weight,t.type, m.area_no areaNo,t.times,
        t.deliveryed,t.suit_id suitId,i.ext_type extType,
        t.account_id accountId, msa.contact subAccountContact,msa.phone subAccountPhone,
        c.address,dp.quantity deliveryQuantity ,dp.delivery_time deliveryDate,o.m_size mSize,o.status orderStatus,
        t.recovery_type recoveryType,asdp.status deliveryStatus, t.after_sale_remark_type afterSaleRemarkType,
        t.after_sale_remark afterSaleRemark, oi.actual_total_price actualTotalPrice,p.quantity quantity
        FROM after_sale_order t
        LEFT JOIN after_sale_proof p ON p.after_sale_order_no = t.after_sale_order_no
        left join  after_sale_delivery_path asdp on asdp.after_sale_no = t.after_sale_order_no
        LEFT JOIN merchant m on t.m_id = m.m_id
        left join merchant_sub_account msa on t.m_id = msa.m_id and t.account_id = msa.account_id
        LEFT JOIN order_item oi on t.order_no = oi.order_no AND t.sku = oi.sku and oi.suit_id=t.suit_id
        LEFT join inventory i on i.sku = oi.sku
        LEFT JOIN area a on m.area_no = a.area_no
        LEFT JOIN orders o on t.order_no = o.order_no
        LEFT JOIN delivery_plan dp on dp.order_no = t.order_no and dp .id = t.delivery_id
        left join contact c on c.contact_id  = dp.contact_id
        <where>
            <if test="mname != null">
                AND m.mname like concat(#{mname},'%')
            </if>
            <if test="mId != null">
                AND m.m_id = #{mId}
            </if>
            <if test="afterSaleOrderStatus != null">
                AND t.after_sale_order_status = #{afterSaleOrderStatus}
            </if>
            <if test="phone !=null and phone != ''">
                and m.phone like concat(#{phone},'%')
            </if>
            <if test="mSize != null">
                AND o.m_size = #{mSize}
            </if>
            <if test="areaNo != null">
                AND m.area_no = #{areaNo}
            </if>
            <if test="afterSaleOrderNo != null">
                AND t.after_sale_order_no = #{afterSaleOrderNo}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="pdId != null">
                AND i.pd_id = #{pdId}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo}
            </if>
            <if test="type != null">
                AND t.type = #{type}
            </if>
            <if test="deliveryed != null">
                AND t.deliveryed = #{deliveryed}
            </if>
            <if test="handleType != null">
                AND p.handle_type = #{handleType}
            </if>
            <if test="deliveryStatus != null and deliveryStatus == 0">
                AND p.handle_type in (0,1,2,3)
            </if>
            <if test="deliveryStatus != null and deliveryStatus > 0">
                AND asdp.status = #{deliveryStatus}
            </if>
            <if test="startTime != null">
                AND t.add_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND t.add_time <![CDATA[<]]> #{endTime}
            </if>
            <if test="queryStoreNoList != null and queryStoreNoList.size != 0">
                and t.order_no in
                <foreach collection="orderNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="queryAreaNoList != null and queryAreaNoList.size != 0">
                and m.area_no in
                <foreach collection="queryAreaNoList" open="(" close=")" separator="," item="it">
                    #{it}
                </foreach>
            </if>
            <if test="handleTypeList != null and handleTypeList.size != 0">
                and p.handle_type in
                <foreach collection="handleTypeList" open="(" close=")" separator="," item="it">
                    #{it}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByOrderNo" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select t.m_id mId, t.account_id accountId, t.after_sale_order_no afterSaleOrderNo,t.add_time addTime, t.after_sale_unit afterSaleUnit, t.type,t.order_no orderNo,t.deliveryed,t.suit_id suitId,t.sku
        ,t.delivery_id deliveryId
        FROM after_sale_order t
        LEFT JOIN (select o.sku, o.order_no,o.weight,o.pd_name,o.suit_id from order_item o where o.order_no = #{orderNo}) oi on t.order_no = oi.order_no AND t.sku = oi.sku and oi.suit_id=t.suit_id
        WHERE t.order_no = #{orderNo} and t.status in(0,1,2)
    </select>

    <select id="selectByAfterSaleOrderNo" parameterType="java.lang.String" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        /*FORCE_MASTER*/  select t.m_id mId, t.account_id accountId, t.after_sale_order_no afterSaleOrderNo,t.add_time addTime, t.after_sale_unit afterSaleUnit, t.type,t.order_no orderNo,t.deliveryed,t.suit_id suitId,t.sku
        ,t.delivery_id deliveryId,t.status
        from after_sale_order t
        where t.after_sale_order_no = #{afterSaleOrderNo}
    </select>

    <update id="updateByAfterSaleOrderNo" parameterType="net.summerfarm.model.domain.AfterSaleOrder">
        update after_sale_order
        <set>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="closer != null">
                closer = #{closer},
            </if>
            <if test="closeTime != null">
                close_time = #{closeTime},
            </if>
            <if test="view != null">
                view = #{view},
            </if>
            <if test="afterSaleRemark != null">
                after_sale_remark = #{afterSaleRemark},
            </if>
        </set>
        where after_sale_order_no = #{afterSaleOrderNo}
    </update>

    <insert id="insertSelective" parameterType="net.summerfarm.model.domain.AfterSaleOrder">
        insert into after_sale_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="mId != null" >
                m_id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="afterSaleOrderNo != null" >
                after_sale_order_no,
            </if>
            <if test="orderNo != null" >
                order_no,
            </if>
            <if test="sku != null" >
                sku,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="afterSaleUnit != null" >
                after_sale_unit,
            </if>
            <if test="addTime != null" >
                add_time,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="grade != null">
                grade,
            </if>
            <if test="deliveryed != null">
                deliveryed,
            </if>
            <if test="suitId != null">
                suit_id,
            </if>
            <if test="times != null">
                times,
            </if>
            <if test="view != null">
                view,
            </if>
            <if test="isFull != null">
                is_full,
            </if>
            <if test="deliveryId != null">
                delivery_id,
            </if>
            <if test="recoveryType != null">
                recovery_type,
            </if>
            <if test="afterSaleRemarkType != null">
                after_sale_remark_type,
            </if>
            <if test="afterSaleRemark != null">
                after_sale_remark,
            </if>
            <if test="afterSaleOrderStatus != null">
                after_sale_order_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="mId != null" >
                #{mId},
            </if>
            <if test="accountId != null">
                #{accountId},
            </if>
            <if test="afterSaleOrderNo != null" >
                #{afterSaleOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null" >
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="sku != null" >
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=INTEGER},
            </if>
            <if test="afterSaleUnit != null" >
                #{afterSaleUnit,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null" >
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="grade != null">
                #{grade,jdbcType=INTEGER},
            </if>
            <if test="deliveryed != null" >
                #{deliveryed},
            </if>
            <if test="suitId != null" >
                #{suitId},
            </if>
            <if test="times != null" >
                #{times},
            </if>
            <if test="view != null" >
                #{view},
            </if>
            <if test="isFull != null">
                #{isFull},
            </if>
            <if test="deliveryId != null">
                #{deliveryId},
            </if>
            <if test="recoveryType != null">
                #{recoveryType},
            </if>
            <if test="afterSaleRemarkType != null">
                #{afterSaleRemarkType},
            </if>
            <if test="afterSaleRemark != null">
                #{afterSaleRemark},
            </if>
            <if test="afterSaleOrderStatus != null">
                #{afterSaleOrderStatus},
            </if>
        </trim>
    </insert>
    <insert id="saveBatchAfterSaleOrder">
        insert into
        after_sale_order
        (add_time,m_id,account_id,after_sale_order_no,order_no,sku,status, after_sale_unit, type,deliveryed,suit_id,times,recovery_type,`view`,product_type)
        VALUES
        <foreach collection="list"  item="item" separator=",">
            (now(),#{item.mId},#{item.accountId},#{item.afterSaleOrderNo},#{item.orderNo},#{item.sku},#{item.status},
            #{item.afterSaleUnit},#{item.type},#{item.deliveryed},#{item.suitId},#{item.times},#{item.recoveryType},#{item.view},#{item.productType})
        </foreach>
    </insert>

    <select id="countByMId" resultType="java.lang.Integer">
        SELECT ifnull(count(1),0)
        FROM after_sale_order t
        WHERE t.m_id = #{mId}
    </select>

    <select id="countNotClose" resultType="java.lang.Integer">
        SELECT count(1)
        FROM after_sale_order t
        WHERE t.m_id = #{mId} and t.status not in (2,3,11) and t.type != 10
    </select>
    <update id="changeOrderMerchant">
        update after_sale_order set m_id = #{newMid},account_id = #{accountId} where m_id = #{oldMid}
    </update>


    <select id="queryAfterOrderByMId" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select m.mname,m.direct,aso.order_no orderNo, o.order_time orderTime,aso.add_time addTime,aso.sku,p.pd_name pdName,i.weight,i.type skuType,asp.quantity,aso.after_sale_unit afterSaleUnit,
            aso.deliveryed,asp.refund_type refundType,asp.after_sale_type afterSaleType,asp.handle_num handleNum,o.delivery_fee deliveryFee,asp.auditetime,asp.handle_type handleType,aso.after_sale_order_no afterSaleOrderNo
            from after_sale_order aso
            inner join orders o on o.order_no = aso.order_no
            inner join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no and asp.status = 2
            inner join inventory i on i.sku = aso.sku
            inner join products p on i.pd_id = p.pd_id
            inner join merchant m on aso.m_id = m.m_id
            inner join delivery_plan dp ON o.order_no=dp.order_no
        where m.admin_id = #{majorAdminId}
            AND o.m_size = '大客户'
            <if test="startTime != null">
                AND o.order_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
            AND o.order_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="timeDimension !=null and timeDimension == 1">
                <if test="billStartTime != null">
                    AND o.order_time >= #{billStartTime}
                </if>
                <if test="billEndTime != null">
                    AND o.order_time <![CDATA[<=]]> #{billEndTime}
                </if>
            </if>
            <if test="timeDimension !=null and timeDimension == 2">
                <if test="billStartTime != null">
                    AND dp.delivery_time >= #{billStartTime}
                </if>
                <if test="billEndTime != null">
                    AND dp.delivery_time <![CDATA[<=]]> #{billEndTime}
                </if>
            </if>
            <if test="status != null ">
                AND o.status = #{status}
            </if>
            <if test="mId != null">
                AND o.m_id = #{mId}
            </if>
            <if test= "settlementMethod != null">
                AND o.direct = #{settlementMethod}
            </if>
            order by aso.order_no
    </select>


    <select id="queryAfterOrderQuantity" resultType="java.lang.Integer">
        select sum(asp.quantity) from after_sale_order aso
        inner join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
        inner join delivery_plan dp on dp.order_no = aso.order_no and dp.delivery_time = #{deliveryDate} and dp.deliverytype = 0
        where aso.status = 2 and aso.deliveryed = 0 and  aso.add_time > #{addTime} and aso.sku = #{sku} and asp.refund_type != '其他'
    </select>

    <select id="queryByOrderNoQuantity" resultType="java.lang.Integer">
        select sum(asp.quantity) from after_sale_order aso
        inner join after_sale_proof asp on asp.after_sale_order_no = aso.after_sale_order_no
        where aso.order_no = #{orderNo} and  aso.sku =#{sku} and aso.add_time > #{afterTime}
        and aso.deliveryed = 0 and asp.refund_type !='其他'and aso.status = 2
    </select>
    <select id="selectBySelectKeys" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select aso.after_sale_order_no afterSaleOrderNo, aso.order_no orderNo, aso.sku, aso.status, aso.deliveryed, aso.status,
               asp.handle_type handleType, IFNULL(asp.handle_num, 0.00) handleNum, asp.updatetime
        from after_sale_order aso
        left join after_sale_proof asp on asp.after_sale_order_no = aso.after_sale_order_no
        <where>
            <if test="orderNo != null">
                AND aso.order_no = #{orderNo}
            </if>
            <if test="afterSaleOrderNo != null">
                AND aso.after_sale_order_no = #{afterSaleOrderNo}
            </if>
            <if test="sku != null">
                AND aso.sku = #{sku}
            </if>
            <if test="status != null">
                AND aso.status = #{status}
            </if>
            <if test="refundTag != null and refundTag == 0">
                AND asp.handle_type IN (2,3,4,5,9,10,11,12,13,14)
            </if>
        </where>
    </select>
    <select id="queryByOrderNo" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select aso.after_sale_order_no afterSaleOrderNo, aso.order_no orderNo, aso.sku, aso.status, aso.deliveryed, aso.status,
               asp.handle_type handleType, IFNULL(asp.handle_num, 0.00) handleNum, asp.updatetime
        from after_sale_order aso
            inner join after_sale_proof asp on asp.after_sale_order_no = aso.after_sale_order_no
        <where>
            aso.status in (0,1,2) AND asp.handle_type IN (2,3,4,5,9,10,11,12,13,14)
            <if test="orderNo != null">
                AND aso.order_no = #{orderNo}
            </if>
        </where>
    </select>
    <select id="queryBySelectKey" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select aso.after_sale_order_no afterSaleOrderNo, aso.order_no orderNo, aso.sku, aso.status, aso.deliveryed, aso.status,
        asp.handle_type handleType, IFNULL(asp.handle_num, 0.00) handleNum
        from after_sale_order aso inner join after_sale_proof asp on asp.after_sale_order_no = aso.after_sale_order_no
        <where>
            <if test="orderNo != null">
                AND aso.order_no = #{orderNo}
            </if>
            <if test="sku != null">
                AND aso.sku = #{sku}
            </if>
            <if test="status != null" >
                AND aso.status = #{status}
            </if>
        </where>
    </select>

    <select id="selectByOrderNoNew" parameterType="net.summerfarm.model.domain.AfterSaleOrder" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select t.after_sale_order_no afterSaleOrderNo,t.add_time addTime, t.after_sale_unit afterSaleUnit, t.order_no orderNo,
        t.type ,t.status,t.sku,t.suit_id suitId,t.deliveryed ,t.delivery_id deliveryId, p.pd_name pdName, i.weight
        FROM after_sale_order t
        left join inventory i on i.sku = t.sku
        left join products p on i.pd_id = p.pd_id
        WHERE t.order_no = #{orderNo} and t.status in(0,1,2)
        <if test="sku != null">
            AND t.sku = #{sku}
        </if>
        <if test="deliveryId != null">
            AND t.delivery_id = #{deliveryId}
        </if>
    </select>

    <select id="selectQuantityByStoreNo" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select aso.order_no orderNo,sum(asp.quantity) afterSaleQuantity from after_sale_order aso
        left join after_sale_proof asp on asp.after_sale_order_no = aso.after_sale_order_no
        left join orders o on aso.order_no = o.order_no
        left join area a on a.area_no = o.area_no
        left join delivery_plan dp on dp.order_no = o.order_no
        where o.type = 1 and o.status in(2,3) and dp.order_store_no = #{storeNo}  and asp.status = 2
        and asp.after_sale_type = 0 and aso.sku = #{sku}
             <if test="areaNo != null">
                 AND o.area_no = #{areaNo}
             </if>
        group by o.order_no
    </select>

    <select id="selectReplenishment" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select aso.after_sale_order_no afterSaleOrderNo ,aso.deliveryed,asp.quantity,aso.order_no orderNo,
               aso.suit_id suitId,aso.sku,asp.handle_type handleType,aso.m_id mId,aso.delivery_id deliveryId
        from after_sale_order aso
        left join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
        where asp.status = 0 and asp.handle_type = 7 and aso.deliveryed = 1
    </select>

    <select id="selectByOrderNoSku" resultType="net.summerfarm.model.domain.AfterSaleOrder">
        select id from after_sale_order where order_no = #{orderNo} and sku = #{sku}
        <if test="productType!=null">
            and product_type = #{productType}
        </if>
    </select>
    <select id="selectReplenishmentByOrderNo" parameterType="net.summerfarm.model.domain.AfterSaleOrder"
            resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select aso.after_sale_order_no afterSaleOrderNo ,aso.deliveryed,asp.quantity,aso.order_no orderNo,
               aso.suit_id suitId,aso.sku,asp.handle_type handleType,aso.m_id mId,aso.delivery_id deliveryId
        from after_sale_order aso
        left join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
        where asp.status in (0,1,2) and asp.handle_type = 7 and aso.deliveryed = 1
        and aso.sku =#{sku} and aso.order_no = #{orderNo} and aso.suit_id = #{suitId}
    </select>

    <select id="selectByOrderNoSkuPrice" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select  IFNULL(sum(asp.handle_num),0) handleNum
        from after_sale_order aso
        left join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
        <where>
            aso.order_no = #{orderNo} and aso.sku = #{sku} and aso.status = 2
            <if test="suitId != null">
                and aso.suit_id = #{suitId}
            </if>
        </where>
    </select>

    <select id="selectAppointTime" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select  IFNULL(sum(asp.handle_num),0) handleNum
        from after_sale_order aso
        left join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
        <where>
            aso.order_no = #{orderNo} and aso.sku = #{sku} and aso.status = 2
            and asp.handletime <![CDATA[>=]]> #{startTime}
            and asp.handletime <![CDATA[<]]> #{endTime}
            <if test="suitId != null">
                and aso.suit_id = #{suitId}
            </if>
        </where>
    </select>

    <select id="selectExport" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        SELECT  t.order_no as orderNo,
        o.`order_time` orderTime,
        m.mname mname,
        t.add_time addTime,
        m.province,
        m.city,
        m.area,
        t.sku,
        oi.pd_name spu,
        oi.price price,
        oi.original_price originalPrice,
        oi.amount amount,
        p.quantity quantity,
        p.handle_num handleNum,
        p.handle_remark handleRemark,
        p.`after_sale_type` afterSaleType,
        if(t.`deliveryed`= 0,'未到货售后','已到货售后') as afterSaleSituation,

        (case p.`handle_type` when 0 then '返券' when 2 then '退款' when 3 then '录入账单'
        when 4 then '退货退款' when 5 then '退货录入账单' when 6 then '换货'
        when 7 then '补发' when 8 then '人工退款中' else '' end) as compensateType,

        case when t.type= 1 and t.status= 2 then '极速售后成功'
        when t.type= 1 and t.status= 3 then '极速售后失败'
        when t.type= 0
        and t.status= 2 then '普通订单成功' else 'others' end afterSaleOrderType,

        CASE p.handle_type WHEN 0 THEN '返券' WHEN 2 THEN '退款' WHEN 3 THEN '录入账单' when 4 then '退货退款' when 5 then '退货录入账单' ELSE 'others' END compensateMode,
        m.size mSize,p.auditer as auditer,
        p.auditetime as auditetime,
        case o.status when 1 THEN '待支付' when 2 THEN '待配送' when 3 THEN '待收货' when 6 THEN '已收货'
            when 7 THEN '申请退款订单' when 8 THEN '已退款订单' when 9 THEN '支付失败订单' when 10 THEN '支付中断超时关闭订单' when 11 THEN '已撤销订单'
            when 12 THEN '待支付尾款' when 13 THEN '尾款支付超时,订单关闭' when 14 THEN '手动关闭订单' end orderStautsName

        FROM after_sale_order t
        LEFT JOIN after_sale_proof p ON p.after_sale_order_no = t.after_sale_order_no
        left join  after_sale_delivery_path asdp on asdp.after_sale_no = t.after_sale_order_no
        LEFT JOIN merchant m on t.m_id = m.m_id
        LEFT JOIN order_item oi on t.order_no = oi.order_no AND t.sku = oi.sku and oi.suit_id=t.suit_id
        LEFT JOIN orders o on t.order_no = o.order_no
        LEFT JOIN delivery_plan dp on dp.order_no = t.order_no and dp .id = t.delivery_id
        left join contact c on c.contact_id  = dp.contact_id
        left join admin ad on ad.admin_id = m.`admin_id`
        LEFT join inventory i on i.sku = oi.sku
        <where>
            <if test="mname != null">
                AND m.mname like concat('%',#{mname},'%')
            </if>
            <if test="phone !=null and phone != ''">
                and m.phone like concat('%',#{phone},'%')
            </if>
            <if test="mSize != null">
                AND o.m_size like concat('%',#{mSize},'%')
            </if>
            <if test="areaNo != null">
                AND m.area_no = #{areaNo}
            </if>
            <!--            <if test="afterSaleOrderNo != null">-->
            <!--                AND t.after_sale_order_no like concat('%',#{afterSaleOrderNo},'%')-->
            <!--            </if>-->
            <if test="afterSaleOrderNo != null">
                AND t.after_sale_order_no = #{afterSaleOrderNo}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="pdId != null">
                AND i.pd_id  = #{pdId}
            </if>
            <!--            <if test="orderNo != null">-->
            <!--                AND t.order_no like concat('%', #{orderNo},'%')-->
            <!--            </if>-->
            <if test="orderNo != null">
                AND t.order_no = #{orderNo}
            </if>
            <if test="type != null">
                AND t.type = #{type}
            </if>
            <if test="purchaser != null">
                AND ad.realname like concat('%', #{purchaser},'%')
            </if>
            <if test="deliveryed != null">
                AND t.deliveryed = #{deliveryed}
            </if>
            <if test="handleType != null">
                AND p.handle_type = #{handleType}
            </if>
            <if test="deliveryStatus != null and deliveryStatus == 0">
                AND p.handle_type in (0,1,2,3)
            </if>
            <if test="deliveryStatus != null and deliveryStatus > 0">
                AND asdp.status = #{deliveryStatus}
            </if>
            <if test=" purchaser != null">
                AND ad.realname like concat('%',#{purchaser},'%')
            </if>
            <if test="startTime != null">
                AND t.add_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND t.add_time <![CDATA[<]]> #{endTime}
            </if>
            <if test="queryStoreNoList != null and queryStoreNoList.size != 0">
                and dp.order_store_no in
                <foreach collection="queryStoreNoList" open="(" close=")" separator="," item="it">
                    #{it}
                </foreach>
            </if>
            <if test="queryAreaNoList != null and queryAreaNoList.size != 0">
                and m.area_no in
                <foreach collection="queryAreaNoList" open="(" close=")" separator="," item="it">
                    #{it}
                </foreach>
            </if>
            <if test="handleTypeList != null and handleTypeList.size != 0">
                and p.handle_type in
                <foreach collection="handleTypeList" open="(" close=")" separator="," item="it">
                    #{it}
                </foreach>
            </if>
        </where>


    </select>

    <select id="selectMonthOrderNo" resultType="net.summerfarm.model.domain.AfterSaleOrder">
        select distinct aso.order_no orderNo
        FROM after_sale_order aso
        left join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
        <where>
            aso.status = 2 and aso.status = 2 and asp.handle_type in (3,5,10,12,14) and aso.deliveryed = 1
            <if test="mId != null">
                AND aso.m_id = #{mId}
            </if>
            <if test="startTime != null and endTime != null">
                AND aso.add_time <![CDATA[>=]]> #{startTime}
                AND aso.add_time <![CDATA[<]]> #{endTime}
            </if>
            <if test="orderNos != null and orderNos.size > 0">
                and aso.order_no not in
                <foreach collection="orderNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByAfterOrderNo" parameterType="net.summerfarm.model.domain.AfterSaleOrder" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select t.after_sale_order_no afterSaleOrderNo,t.add_time addTime, t.after_sale_unit afterSaleUnit,t.order_no orderNo,
        t.type ,t.status,t.sku,t.suit_id suitId,t.deliveryed ,t.delivery_id deliveryId, p.pd_name pdName, i.weight, asp.handletime updatetime,i.type skuType,
        asp.handle_type handleType,asp.after_sale_type afterSaleType,asp.auditetime
        FROM after_sale_order t
        left join after_sale_proof asp on t.after_sale_order_no = asp.after_sale_order_no
        left join inventory i on i.sku = t.sku
        left join products p on i.pd_id = p.pd_id
        WHERE t.order_no = #{orderNo} and t.status = 2 and t.deliveryed = 1 and asp.handle_type in (3,5,10,14) and t.add_time <![CDATA[>=]]> #{startTime} AND t.add_time <![CDATA[<]]> #{endTime}

    </select>

    <select id="selectUndelivered" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select t.after_sale_order_no afterSaleOrderNo,t.add_time addTime, t.after_sale_unit afterSaleUnit,t.order_no orderNo,
               t.type ,t.status,t.sku,t.suit_id suitId,t.deliveryed ,t.delivery_id deliveryId, p.pd_name pdName, i.weight, t.update_time updatetime,i.type skuType, IFNULL(asp.quantity,0) quantity,
               asp.handle_type handleType,asp.after_sale_type afterSaleType
        FROM after_sale_order t
                 left join after_sale_proof asp on t.after_sale_order_no = asp.after_sale_order_no
                 left join inventory i on i.sku = t.sku
                 left join products p on i.pd_id = p.pd_id
        WHERE t.status = 2 and t.add_time <![CDATA[>=]]> #{startTime} AND t.add_time <![CDATA[<]]> #{endTime} and t.deliveryed = 0 and t.m_id = #{mId} and asp.handle_type in (3,5,10)
        ORDER by t.update_time
    </select>

    <select id="undeliveredByOrdersList" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select t.after_sale_order_no afterSaleOrderNo,t.add_time addTime, t.after_sale_unit afterSaleUnit,t.order_no orderNo,
               t.type ,t.status,t.sku,t.suit_id suitId,t.deliveryed ,t.delivery_id deliveryId, p.pd_name pdName, i.weight, t.update_time updatetime,i.type skuType, IFNULL(asp.quantity,0) quantity,
               asp.handle_type handleType,asp.after_sale_type afterSaleType,asp.auditetime
        FROM after_sale_order t
                 left join after_sale_proof asp on t.after_sale_order_no = asp.after_sale_order_no
                 left join inventory i on i.sku = t.sku
                 left join products p on i.pd_id = p.pd_id
        WHERE t.status = 2 and t.deliveryed = 0 and t.m_id = #{mId} and asp.handle_type in (3,5,10,14)
        <if test="ordersList != null and ordersList.size > 0">
            and t.order_no in
            <foreach collection="ordersList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        ORDER by t.update_time
    </select>


    <select id="getONoAndANByAfterSaleNo" resultType="net.summerfarm.model.vo.OrderNoAreaNoVo">
        SELECT
            o.order_no orderNo,
            wip.warehouse_no areaNo,
            asdp.out_store_no AS outStoreNo
        FROM
            after_sale_order t
                INNER JOIN orders o ON o.order_no = t.order_no
                INNER JOIN after_sale_delivery_path asdp ON asdp.after_sale_no = t.after_sale_order_no
                left JOIN warehouse_inventory_mapping wip ON wip.sku = t.sku
                AND asdp.out_store_no = wip.store_no
        WHERE
            t.after_sale_order_no=#{afterSaleNo}
    </select>

    <select id="getAfterSaleRemarkTypeList" resultType="integer">
        SELECT
            t.after_sale_remark_type
        FROM
            after_sale_order t
            INNER JOIN after_sale_proof s ON t.after_sale_order_no = s.after_sale_order_no
        WHERE
            t.order_no = #{taskNo}
            and
            t.sku = #{sku}
            <if test="afterType == 13">
                AND s.handle_type IN ( 9, 10 )
            </if>
            <if test="afterType == 19">
                AND s.handle_type not IN ( 9, 10 )
            </if>
    </select>
  <select id="countBydeliveryPlanId" resultType="java.lang.Integer">
      select count(1) from after_sale_order where delivery_id = #{dpId} and status not in (3,11)
  </select>

    <select id="selectByDeliveryFeeCount" resultType="java.lang.Integer">
    select count(1) from after_sale_order
    where order_no=#{orderNo} and refund_freight = 1 and status in (0,1,2)
  </select>
    <select id="selectByOneProof" resultType="net.summerfarm.model.vo.AfterSaleOrderVO"
            parameterType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select
        p.recovery_num recoveryNum,t.after_sale_order_status afterSaleOrderStatus,
        t.delivery_id deliveryId,o.delivery_fee deliveryFee,
        t.id, t.after_sale_order_no afterSaleOrderNo, t.m_id mId, t.order_no orderNo, t.sku, t.status,t.after_sale_unit afterSaleUnit,
        t.add_time addTime, m.mname,a.area_name areaName,pd.pd_name pdName,t.type, m.area_no areaNo,t.times,
        t.deliveryed,t.suit_id suitId,i.ext_type extType, t.snapshot snapshot,
        t.account_id accountId, msa.contact subAccountContact,msa.phone subAccountPhone,
        c.address,dp.quantity deliveryQuantity ,dp.delivery_time deliveryDate,o.m_size mSize,o.status orderStatus, o.type orderType,
        t.recovery_type recoveryType,asdp.status deliveryStatus, t.after_sale_remark_type afterSaleRemarkType,
        t.after_sale_remark afterSaleRemark,p.quantity quantity,p.handle_secondary_remark handleSecondaryRemark,t.carrying_goods carryingGoods,p.apply_secondary_remark applySecondaryRemark
        ,o.order_time orderTime
        FROM after_sale_order t
        LEFT JOIN after_sale_proof p ON p.after_sale_order_no = t.after_sale_order_no
        left join  after_sale_delivery_path asdp on asdp.after_sale_no = t.after_sale_order_no
        LEFT JOIN merchant m on t.m_id = m.m_id
        left join merchant_sub_account msa on t.m_id = msa.m_id and t.account_id = msa.account_id
        LEFT join inventory i on i.sku = t.sku
        left join products pd on i.pd_id = pd.pd_id
        LEFT JOIN area a on m.area_no = a.area_no
        LEFT JOIN orders o on t.order_no = o.order_no
        LEFT JOIN delivery_plan dp on dp.order_no = t.order_no and dp .id = t.delivery_id
        left join contact c on c.contact_id  = dp.contact_id
        <where>
            t.status = p.status
            <if test="mname != null">
                AND m.mname like concat(#{mname},'%')
            </if>
            <if test="mId != null">
                AND m.m_id = #{mId}
            </if>
            <if test="afterSaleOrderStatus != null">
                AND t.after_sale_order_status = #{afterSaleOrderStatus}
            </if>
            <if test="mIdList !=null and mIdList.size() > 0">
                and m.m_id in
                <foreach collection="mIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="mSize != null">
                AND o.m_size = #{mSize}
            </if>
            <if test="areaNo != null">
                AND m.area_no = #{areaNo}
            </if>
            <if test="afterSaleOrderNo != null">
                AND t.after_sale_order_no = #{afterSaleOrderNo}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="pdId != null">
                AND i.pd_id = #{pdId}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo}
            </if>
            <if test="type != null">
                AND t.type = #{type}
            </if>
            <if test="deliveryed != null">
                AND t.deliveryed = #{deliveryed}
            </if>
            <if test="handleType != null">
                AND p.handle_type = #{handleType}
            </if>
            <if test="deliveryStatus != null and deliveryStatus == 0">
                AND p.handle_type in (0,1,2,3)
            </if>
            <if test="deliveryStatus != null and deliveryStatus > 0">
                AND asdp.status = #{deliveryStatus}
            </if>
            <if test="startTime != null">
                AND t.add_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND t.add_time <![CDATA[<]]> #{endTime}
            </if>
            <if test="queryStoreNoList != null and queryStoreNoList.size != 0">
                and dp.order_store_no in
                <foreach collection="queryStoreNoList" open="(" close=")" separator="," item="it">
                    #{it}
                </foreach>
            </if>
            <if test="queryAreaNoList != null and queryAreaNoList.size != 0">
                and m.area_no in
                <foreach collection="queryAreaNoList" open="(" close=")" separator="," item="it">
                    #{it}
                </foreach>
            </if>
            <if test="handleTypeList != null and handleTypeList.size != 0">
                and p.handle_type in
                <foreach collection="handleTypeList" open="(" close=")" separator="," item="it">
                    #{it}
                </foreach>
            </if>
            <if test="orderSource != null and orderSource == 0">
                AND o.type != 30
            </if>
            <if test="orderSource != null  and orderSource == 1">
                AND o.type = 30
            </if>
        </where>
        order by t.add_time
    </select>
    <select id="selectMoneyByMid" resultType="java.math.BigDecimal">
        select IFNULL(sum(asp.handle_num),0)
        from after_sale_order aso
        left join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
        where aso.m_id = #{MId} and aso.add_time <![CDATA[>=]]> #{beforeTime} and aso.add_time <![CDATA[<]]> #{now}
        and aso.status not in (0,1,3,11) and asp.handle_type not in (13,14)
        <if test="deliveryed != null">
            and aso.deliveryed = #{deliveryed}
        </if>
    </select>
    <select id="selectAfterOrder" resultType="net.summerfarm.model.domain.AfterSaleOrder">
        select order_no orderNo
      from after_sale_order
      where order_no = #{orderNo} and suit_id =#{suitId} and sku = #{sku} and status in(2,3,6)
    </select>
    <select id="countByAfterSaleOrderNo" resultType="java.lang.Integer">
            SELECT count(1) FROM after_sale_order t WHERE t.after_sale_order_no = #{afterSaleOrderNo}
    </select>
    <select id="afterSaleCountByMId" resultType="java.lang.Integer">
            SELECT count(1) FROM after_sale_order t WHERE t.m_id = #{mId} and t.account_id = #{accountId}
    </select>

    <select id="selectAfterOrderByOrderNo" resultType="net.summerfarm.model.domain.AfterSaleOrder">
        select aso.after_sale_order_no afterSaleOrderNo,aso.delivery_id deliveryId,
        aso.after_sale_remark afterSaleRemark,sku
        from after_sale_order aso
        left join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
        where order_no = #{orderNo} and aso.status = 2
        <if test="list != null and list.size != 0">
            and asp.handle_type in
            <foreach collection="list" open="(" close=")" separator="," item="it">
                #{it}
            </foreach>
        </if>
    </select>
    <select id="selectAfterSaleAmountByOrderNos" resultType="java.math.BigDecimal">
        select ifnull(SUM(asp.handle_num),0)
        FROM after_sale_order t
                 left join after_sale_proof asp on t.after_sale_order_no = asp.after_sale_order_no
                 left join inventory i on i.sku = t.sku
                 left join products p on i.pd_id = p.pd_id
        WHERE t.status = 2 and t.deliveryed = 1 and asp.handle_type in (3, 5, 10, 14) and t.`order_no` IN
        <foreach collection="orderNos" separator="," open="(" close=")" item="orderNo">
            #{orderNo}
        </foreach>
    </select>
    <select id="selectAfterSaleOrderByOrderNos" resultType="net.summerfarm.model.vo.finance.AdminAfterSaleExportVO">
        select t.after_sale_order_no                              afterSaleOrderNo,
               t.m_id                                             mId,
               t.order_no                                         orderNo,
               asp.auditetime                                     afterSaleTime,
               t.sku                                              sku,
               p.pd_name                                          pdName,
               i.weight                                           weight,
               IF(i.type = 1, "代仓", "自营")                     property,
               asp.`quantity`                                     quantity,
               IF(t.`deliveryed` = 0, "未到货售后", "已到货售后") deliveryed,
               asp.handle_type                                    handleType,
               asp.after_sale_type                                afterSaleType,
               asp.handle_num                                     handleNum
        FROM after_sale_order t
                 left join after_sale_proof asp on t.after_sale_order_no = asp.after_sale_order_no
                 left join inventory i on i.sku = t.sku
                 left join products p on i.pd_id = p.pd_id
        WHERE t.status = 2 and t.deliveryed = 1
        <if test="orderNos != null and orderNos.size() != 0">
            and t.order_no in
            <foreach collection="orderNos" separator="," open="(" close=")" item="orderNo">
                #{orderNo}
            </foreach>
        </if>
        <if test="mIdList != null and mIdList.size() != 0">
            and t.m_id in
            <foreach collection="mIdList" separator="," open="(" close=")" item="mId">
                #{mId}
            </foreach>
        </if>
        <if test="afterSaleStartTime != null and afterSaleEndTime != null">
            and asp.updatetime between #{afterSaleStartTime} and #{afterSaleEndTime}
        </if>
    </select>

    <select id="selectAfterSaleProofForExport" resultType="net.summerfarm.model.vo.AfterSaleOrderVO" fetchSize="500">
        select
            t.`m_id` as mId,
            m.mname as mname,
            t.order_no as orderNo,
            pd.pd_name as pdName,
            t.sku,
            asp.apply_remark as applyRemark, <!-- 售后备注 -->
            asp.quantity, <!--  售后数量，可能是g，可能是盒 -->
            i.after_sale_unit as afterSaleUnit, <!--  售后单位 -->
            asp.handle_num as handleNum, <!--  售后金额 -->
            t.after_sale_order_no afterSaleOrderNo,
            t.add_time addTime,
            asp.proof_pic proofPic
        FROM after_sale_order t
        left join after_sale_proof asp on t.after_sale_order_no = asp.after_sale_order_no
        LEFT join inventory i on i.sku = t.sku
        left join products pd on i.pd_id = pd.pd_id
        LEFT JOIN merchant m on m.m_id = t.m_id
        WHERE t.status = 2 
            AND t.after_sale_order_status = #{input.afterSaleOrderStatus}
            AND t.add_time BETWEEN DATE(#{input.startTime}) AND DATE(#{input.endTime}) + INTERVAL 1 DAY - INTERVAL 1 SECOND
            AND length(asp.proof_pic) > 1
        <if test="input.pdId != null">
            AND i.pd_id = #{input.pdId}
        </if>
        order by t.add_time desc
    </select>
    <select id="queryByAfterSaleOrderNo" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select t.m_id mId, t.account_id accountId, t.after_sale_order_no afterSaleOrderNo,t.add_time addTime, t.after_sale_unit afterSaleUnit, t.type,t.order_no orderNo,t.deliveryed,t.suit_id suitId,t.sku
        ,t.delivery_id deliveryId,t.status,i.sub_type subType
        from after_sale_order t LEFT JOIN inventory i on t.sku = i.sku
        where t.after_sale_order_no = #{afterSaleOrderNo}
    </select>

    <select id="selectSuccessByRefundAfterSaleOrder" resultType="net.summerfarm.model.vo.AfterSaleOrderVO">
        select aso.after_sale_unit afterSaleUnit,asp.quantity,asp.handle_type handleType,asp.handle_num handleNum,aso.deliveryed,aso.sku
        from after_sale_order aso
        left join after_sale_proof asp ON aso.after_sale_order_no = asp.after_sale_order_no
        where asp.status = 2 and asp.handle_type = 2
        and aso.deliveryed = 0 and aso.order_no = #{orderNo}
    </select>
</mapper>
