<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockStoreBackUpMapper" >

  <insert id="insert" parameterType="net.summerfarm.model.domain.StockStoreBackUp" useGeneratedKeys="true" keyProperty="id">
    insert into stock_store_back_up
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="cutStoreNo != null">
        cut_store_no,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeNo != null">
        #{storeNo},
      </if>
      <if test="cutStoreNo != null">
        #{cutStoreNo},
      </if>
      <if test="adminId != null">
        #{adminId},
      </if>
    </trim>
  </insert>
  <select id="selectAllLogisticsCenter" resultType="net.summerfarm.warehouse.model.vo.WarehouseLogisticsCenterVO">
  select ss.store_no storeNo, wlc.store_name storeName
  from stock_store_back_up ss
  left join  warehouse_logistics_center wlc on wlc.store_no= ss.store_no
  group by ss.store_no
  </select>
</mapper>