<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.WarehousePatrolCheckMapper">
    <insert id="saveBatchPatrolCheck" parameterType="net.summerfarm.model.domain.WarehousePatrolCheck">
        insert into warehouse_patrol_check (update_time,warehouse_no,sku,check_time,`type`)
        values
        <foreach collection ="list" item="item" separator =",">
            (now(),#{item.warehouseNo},#{item.sku},now(),#{item.type})
        </foreach>
    </insert>

    <select id="selectPatrolCheck"  resultType= "net.summerfarm.model.domain.WarehousePatrolCheck">
        select  warehouse_no warehouseNo,sku
        from warehouse_patrol_check
        where date(check_time) = #{date} and `type` = #{type}
    </select>

</mapper>