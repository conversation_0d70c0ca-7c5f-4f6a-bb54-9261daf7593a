<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DistributionRulesMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DistributionRules">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="ageing" jdbcType="TINYINT" property="ageing" />
    <result column="product_type" jdbcType="TINYINT" property="productType" />
    <result column="sill_type" jdbcType="TINYINT" property="sillType" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="express_fee" jdbcType="DECIMAL" property="expressFee" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, type, type_id, area_no, ageing, product_type, sill_type, number, amount, delivery_fee, 
    express_fee, creator, create_time, updater, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distribution_rules
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from distribution_rules
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.DistributionRules">
    insert into distribution_rules (id, type, type_id, 
      area_no, ageing, product_type, 
      sill_type, number, amount, 
      delivery_fee, express_fee, creator, 
      create_time, updater, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{type,jdbcType=TINYINT}, #{typeId,jdbcType=BIGINT}, 
      #{areaNo,jdbcType=INTEGER}, #{ageing,jdbcType=TINYINT}, #{productType,jdbcType=TINYINT}, 
      #{sillType,jdbcType=TINYINT}, #{number,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, 
      #{deliveryFee,jdbcType=DECIMAL}, #{expressFee,jdbcType=DECIMAL}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.DistributionRules">
    insert into distribution_rules
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="ageing != null">
        ageing,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="sillType != null">
        sill_type,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="expressFee != null">
        express_fee,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=BIGINT},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="ageing != null">
        #{ageing,jdbcType=TINYINT},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=TINYINT},
      </if>
      <if test="sillType != null">
        #{sillType,jdbcType=TINYINT},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="expressFee != null">
        #{expressFee,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.DistributionRules">
    update distribution_rules
    <set>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=BIGINT},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="ageing != null">
        ageing = #{ageing,jdbcType=TINYINT},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=TINYINT},
      </if>
      <if test="sillType != null">
        sill_type = #{sillType,jdbcType=TINYINT},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="expressFee != null">
        express_fee = #{expressFee,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DistributionRules">
    update distribution_rules
    set type = #{type,jdbcType=TINYINT},
      type_id = #{typeId,jdbcType=BIGINT},
      area_no = #{areaNo,jdbcType=INTEGER},
      ageing = #{ageing,jdbcType=TINYINT},
      product_type = #{productType,jdbcType=TINYINT},
      sill_type = #{sillType,jdbcType=TINYINT},
      number = #{number,jdbcType=INTEGER},
      amount = #{amount,jdbcType=DECIMAL},
      delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      express_fee = #{expressFee,jdbcType=DECIMAL},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert">
    insert into distribution_rules (type, type_id,
    area_no, ageing, product_type,
    sill_type, number, amount,
    delivery_fee, express_fee, creator
    )
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.type,jdbcType=TINYINT}, #{item.typeId,jdbcType=BIGINT},
      #{item.areaNo,jdbcType=INTEGER}, #{item.ageing,jdbcType=TINYINT}, #{item.productType,jdbcType=TINYINT},
      #{item.sillType,jdbcType=TINYINT}, #{item.number,jdbcType=INTEGER}, #{item.amount,jdbcType=DECIMAL},
      #{item.deliveryFee,jdbcType=DECIMAL}, #{item.expressFee,jdbcType=DECIMAL}, #{item.creator,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <delete id="deleteInfo">
    delete from distribution_rules
    where type =  #{type} and type_id in
    <foreach collection="list" item="typeId" index="index" separator="," open="(" close=")">
      #{typeId}
    </foreach>
  </delete>
</mapper>