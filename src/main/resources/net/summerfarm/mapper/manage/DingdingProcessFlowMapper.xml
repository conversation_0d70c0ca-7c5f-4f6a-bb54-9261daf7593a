<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DingdingProcessFlowMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.dingding.DingdingProcessFlow">
    <!--@mbg.generated-->
    <!--@Table dingding_process_flow-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId" />
    <result column="process_code" jdbcType="VARCHAR" property="processCode" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="process_status" jdbcType="INTEGER" property="processStatus" />
    <result column="process_url" jdbcType="VARCHAR" property="processUrl" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="form_data" jdbcType="VARCHAR" property="formData"/>
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="platform_type" jdbcType="VARCHAR" property="platformType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, process_instance_id, process_code, biz_type, biz_id, process_status, process_url, 
    remark, form_data, creator, updater, create_time, update_time,platform_type
  </sql>
  <sql id="Base_Column_List_Without_Form_data">
    <!--@mbg.generated-->
    id, process_instance_id, process_code, biz_type, biz_id, process_status, process_url,
    remark, form_data, creator, updater, create_time, update_time,platform_type
  </sql>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.dingding.DingdingProcessFlow" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into dingding_process_flow (process_instance_id, process_code, biz_type, 
      biz_id, process_status, process_url, 
      remark, form_data, creator, updater,
      create_time, update_time)
    values (#{processInstanceId,jdbcType=VARCHAR}, #{processCode,jdbcType=VARCHAR}, #{bizType,jdbcType=INTEGER}, 
      #{bizId,jdbcType=BIGINT}, #{processStatus,jdbcType=INTEGER}, #{processUrl,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR},#{formData,jdbcType=VARCHAR}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.dingding.DingdingProcessFlow" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into dingding_process_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="processInstanceId != null">
        process_instance_id,
      </if>
      <if test="processCode != null">
        process_code,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="processStatus != null">
        process_status,
      </if>
      <if test="processUrl != null">
        process_url,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="formData != null ">
        form_data,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="processInstanceId != null">
        #{processInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="processCode != null">
        #{processCode,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="processStatus != null">
        #{processStatus,jdbcType=INTEGER},
      </if>
      <if test="processUrl != null">
        #{processUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="formData != null ">
        #{formData,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="selectByProcessInstanceId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List_Without_Form_data"/>
    from dingding_process_flow
    where process_instance_id=#{processInstanceId,jdbcType=VARCHAR}
  </select>

  <update id="updateById">
    update dingding_process_flow
    <set>
      <if test="flow.processStatus != null">
        process_status = #{flow.processStatus,jdbcType=INTEGER},
      </if>
      <if test="flow.processUrl != null">
        process_url = #{flow.processUrl,jdbcType=VARCHAR},
      </if>
      <if test="flow.remark != null">
        remark = #{flow.remark,jdbcType=VARCHAR},
      </if>
      <if test="flow.updater != null">
        updater = #{flow.updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{flow.id,jdbcType=BIGINT}
  </update>

  <select id="selectOneByBizIdAndBizType" resultMap="BaseResultMap">
    select <include refid="Base_Column_List_Without_Form_data"/>
    from dingding_process_flow
    where biz_id=#{bizId,jdbcType=BIGINT} and biz_type=#{bizType,jdbcType=INTEGER}
    limit 1
  </select>

  <select id="selectOneByBizIdAndBizTypeOrderById" resultMap="BaseResultMap">
    select <include refid="Base_Column_List_Without_Form_data"/>
    from dingding_process_flow
    where biz_id=#{bizId,jdbcType=BIGINT} and biz_type=#{bizType,jdbcType=INTEGER}
    order by id desc
    limit 1
  </select>
</mapper>