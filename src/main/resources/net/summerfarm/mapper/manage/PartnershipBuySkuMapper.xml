<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PartnershipBuySkuMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PartnershipBuySku">
    <!--@mbg.generated-->
    <!--@Table market_partnership_buy_sku-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="min_sale_num" jdbcType="INTEGER" property="minSaleNum" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, config_id, sku, min_sale_num, create_time, update_time, delete_flag, creator, 
    updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from market_partnership_buy_sku
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PartnershipBuySku" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into market_partnership_buy_sku (config_id, sku, min_sale_num, 
      create_time, update_time, delete_flag, 
      creator, updater)
    values (#{configId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{minSaleNum,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=TINYINT}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PartnershipBuySku" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into market_partnership_buy_sku
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="configId != null">
        config_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="minSaleNum != null">
        min_sale_num,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="minSaleNum != null">
        #{minSaleNum,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PartnershipBuySku">
    <!--@mbg.generated-->
    update market_partnership_buy_sku
    <set>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="minSaleNum != null">
        min_sale_num = #{minSaleNum,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PartnershipBuySku">
    <!--@mbg.generated-->
    update market_partnership_buy_sku
    set config_id = #{configId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=VARCHAR},
      min_sale_num = #{minSaleNum,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      delete_flag = #{deleteFlag,jdbcType=TINYINT},
      creator = #{creator,jdbcType=INTEGER},
      updater = #{updater,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <resultMap id="PartnershipBuySkuDTOResultMap" type="net.summerfarm.model.DTO.market.PartnershipBuySkuDTO">
    <result column="sku" property="sku"/>
    <result column="min_sale_num" property="minSaleNum"/>
    <result column="adjust_type" property="type"/>
    <result column="amount" property="amount"/>
    <result column="logo" property="logo"/>
    <result column="weight" property="weight" />
    <result column="pd_name" property="pdName"/>
  </resultMap>
  <select id="selectByConfigId" resultMap="PartnershipBuySkuDTOResultMap">
    select mpbs.sku,min_sale_num,adjust_type,amount,weight,ifnull(i.sku_pic, p.picture_path) logo,
           p.pd_name
    from market_partnership_buy_sku mpbs
        left join price_strategy ps on mpbs.id = ps.business_id and type = 5
        left join inventory i on mpbs.sku = i.sku
        left join products p on i.pd_id = p.pd_id
    where config_id=#{configId,jdbcType=BIGINT} and delete_flag = 0
  </select>
  <select id="selectAllByConfigId" resultMap="BaseResultMap">
    select mpbs.id,mpbs.config_id,mpbs.sku,mpbs.min_sale_num,mpbs.create_time,mpbs.update_time,mpbs.delete_flag,mpbs.creator,
    mpbs.updater
    from market_partnership_buy_sku mpbs
        left join price_strategy ps on mpbs.id = ps.business_id and type = 5
    where config_id=#{configId,jdbcType=BIGINT}
  </select>

  <update id="updateMinSaleNumByConfigIdAndSku">
    update market_partnership_buy_sku
    set min_sale_num = #{minSaleNum}, updater = #{adminId}, delete_flag = 0
    where config_id = #{configId} and sku = #{sku}
  </update>

  <update id="updateDeleteFlagByConfigIdAndSkuIn">
    update market_partnership_buy_sku
    set delete_flag=#{deleteFlag,jdbcType=TINYINT},updater = #{adminId}
    where config_id=#{configId,jdbcType=BIGINT}
          and sku in <foreach item="item" index="index" collection="skus" open="(" separator="," close=")">
                          #{item,jdbcType=VARCHAR}
                     </foreach>
  </update>
</mapper>