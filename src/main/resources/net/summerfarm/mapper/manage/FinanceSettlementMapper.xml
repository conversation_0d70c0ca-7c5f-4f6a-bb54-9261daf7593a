<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceSettlementMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceSettlement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="settlement_method" jdbcType="INTEGER" property="settlementMethod" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="settlement_method" jdbcType="INTEGER" property="settlementMethod" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    id, admin_id, settlement_method, create_time, creator, `status`, update_time, updater,type
  </sql>
  <select id="selectByPrimaryKey" parameterType="integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_settlement
    where admin_id = #{adminId} and `status` = 0
  </select>

  <select id="selectPaidOnceMonth" parameterType="integer" resultType="net.summerfarm.model.domain.FinanceSettlement">
    select id,admin_id adminId
    from finance_settlement
    where settlement_method = #{settlementMethod} and `status` = 0
  </select>
  <select id="selectByDayAndAdminId" resultType="net.summerfarm.model.domain.FinanceSettlement">
    select id, admin_id adminId,settlement_method settlementMethod
    from finance_settlement
    where `status` = 0 and type = 0
    <if test="settlementMethod!=null">
      <if test="isLastDayOfMonth">and settlement_method >= #{settlementMethod}</if>
      <if test="!isLastDayOfMonth">and settlement_method = #{settlementMethod}</if>
    </if>
    <if test="adminId!=null">and admin_id=#{adminId}</if>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_settlement
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceSettlement"
          useGeneratedKeys="true">
    insert into finance_settlement (admin_id, settlement_method, create_time, creator, `status`, type)
    values (#{adminId,jdbcType=INTEGER}, #{settlementMethod,jdbcType=INTEGER}, now(),
            #{creator,jdbcType=VARCHAR}, 0, #{type})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceSettlement" useGeneratedKeys="true">
    insert into finance_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="settlementMethod != null">
        settlement_method,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="settlementMethod != null">
        #{settlementMethod,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceSettlement">
    update finance_settlement
    <set>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="settlementMethod != null">
        settlement_method = #{settlementMethod,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceSettlement">
    update finance_settlement
    set admin_id = #{adminId,jdbcType=INTEGER},
      settlement_method = #{settlementMethod,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByAdminAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_settlement
    where admin_id = #{adminId} and `status` = 0 and type = #{type}
  </select>

  <delete id="deleteByAdminIdAndType">
    delete
    from finance_settlement
    where admin_id = #{adminId} and type not in
    <foreach collection="type" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>
</mapper>