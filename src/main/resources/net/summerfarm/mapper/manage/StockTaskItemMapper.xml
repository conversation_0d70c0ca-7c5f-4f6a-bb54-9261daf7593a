<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTaskItemMapper">

    <resultMap id="withDetail" type="net.summerfarm.model.vo.StockTaskItemVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="stock_task_id" property="stockTaskId" jdbcType="INTEGER"/>
        <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="pack" property="pack" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR" />
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="actual_quantity" property="actualQuantity" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="name_remakes" property="nameRemakes" />
        <result column="category_id" property="categoryId" jdbcType="INTEGER" />
        <result column="pd_id" property="pdId"/>
        <result column="storage_location" property="storageLocation"/>
        <result column="categoryType" property="categoryType"/>
        <result column="skuType" property="skuType"/>
        <result column="extType" property="extType"/>
        <result column="weight_num" property="weightNum"/>
        <collection property="stockTaskItemDetailVOS" column="id" javaType="ArrayList"
                    select="net.summerfarm.mapper.manage.StockTaskItemDetailMapper.selectByItemId"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.model.domain.StockTaskItem">
        INSERT INTO stock_task_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stockTaskId != null">
                stock_task_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="actualQuantity != null">
                actual_quantity,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stockTaskId != null">
                #{stockTaskId},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="quantity != null">
                #{quantity},
            </if>
            <if test="actualQuantity != null">
                #{actualQuantity},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="net.summerfarm.model.domain.StockTaskItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO stock_task_item (stock_task_id,sku,quantity,actual_quantity)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.stockTaskId},#{item.sku},#{item.quantity},#{item.actualQuantity})
        </foreach>
    </insert>

    <select id="select" parameterType="net.summerfarm.model.domain.StockTaskItem" resultType="net.summerfarm.model.domain.StockTaskItem">
        SELECT id,stock_task_id stockTaskId,sku,quantity,actual_quantity actualQuantity
        FROM stock_task_item
        <where>
            <if test="stockTaskId != null">
                AND stock_task_id = #{stockTaskId}
            </if>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
        </where>
    </select>

    <update id="update" parameterType="net.summerfarm.model.domain.StockTaskItem">
        UPDATE stock_task_item
        <set>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="actualQuantity != null">
                actual_quantity = #{actualQuantity},
            </if>
            <if test="oldQuantity != null">
                old_quantity = #{oldQuantity},
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByStockTaskId" resultMap="withDetail">
       /*FORCE_MASTER*/
       SELECT sti.id,sti.stock_task_id,p.pd_name,sti.sku,i.weight,i.pack,i.unit,sti.quantity,sti.actual_quantity,ar.status,ad.name_remakes,
          p.category_id,p.pd_id,p.storage_location,c.type categoryType,i.type skuType,i.ext_type extType,i.volume,i.weight_num,c.category,i.volume
        FROM stock_task st
	    INNER JOIN stock_task_item sti ON st.id=sti.stock_task_id
	    INNER JOIN inventory i ON sti.sku=i.sku
        INNER JOIN products p ON i.pd_id=p.pd_id
        INNER JOIN category c on p.category_id = c.id
	    LEFT JOIN warehouse_stock_ext  ar	ON st.area_no=ar.warehouse_no AND sti.sku=ar.sku
        LEFT JOIN admin ad ON  ad.admin_id = i.admin_id
	    WHERE st.id = #{stockTaskId,jdbcType=INTEGER}
	    order by c.type,sti.id desc
    </select>

    <select id="selectByStockTaskIdList" resultMap="withDetail">
       SELECT sti.id,sti.stock_task_id,p.pd_name,sti.sku,i.weight,i.pack,i.unit,sti.quantity,sti.actual_quantity,ar.status,ad.name_remakes,
          p.category_id,p.pd_id,p.storage_location,c.type categoryType,i.type skuType,i.ext_type extType,i.volume,i.weight_num,c.category,i.volume
        FROM stock_task st
	    INNER JOIN stock_task_item sti ON st.id=sti.stock_task_id
	    INNER JOIN inventory i ON sti.sku=i.sku
        INNER JOIN products p ON i.pd_id=p.pd_id
        INNER JOIN category c on p.category_id = c.id
	    LEFT JOIN warehouse_stock_ext  ar	ON st.area_no=ar.warehouse_no AND sti.sku=ar.sku
        LEFT JOIN admin ad ON  ad.admin_id = i.admin_id
	    WHERE st.id IN
        <foreach collection="stockTaskIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
	    order by c.type desc
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer"
            resultType="net.summerfarm.model.domain.StockTaskItem">
        SELECT id,stock_task_id stockTaskId,sku,actual_quantity actualQuantity
        FROM stock_task_item
        WHERE id = #{id}
    </select>
    <select id="selectAutoTransfer" resultType="net.summerfarm.model.vo.StockTaskItemVO">
        select sti.id id, stock_task_id stockTaskId, sku, quantity, actual_quantity actualQuantity,st.state taskState, case st.state when 2 then updatetime else null end finishTime
        from stock_task_item sti
                 left join stock_task st on sti.stock_task_id = st.id
        where st.admin_id is null
          and st.type = 82
          and st.area_no = #{storeNo}
          and st.addtime &gt; #{startTime}
          and st.addtime &lt; #{endTime}
          and sti.sku = #{sku}
        order by sti.id desc
    </select>


    <select id="queryNumberBySku" resultType="net.summerfarm.model.domain.StockTaskItem">
        select sti.sku,sti.quantity,sti.actual_quantity actualQuantity from stock_task st
        inner join stock_task_item sti where sti.stock_task_id = st.id and sku = #{sku}
        and date(expect_time) = #{date} and st.type = 51;
    </select>

    <select id="queryNumberByOrderNo" resultType="net.summerfarm.model.domain.StockTaskItem">
        select sti.quantity,sti.actual_quantity actualQuantity,sti.sku from stock_task st
        inner join stock_task_item sti on st.id = sti.stock_task_id and sti.sku=#{sku} and st.task_no =#{orderNo} and type = 51
    </select>
    <select id="queryStockTaskItem" resultType="net.summerfarm.model.domain.StockTaskItem">
         SELECT id,stock_task_id stockTaskId,sku,quantity,actual_quantity actualQuantity
        FROM stock_task_item
        where stock_task_id = #{stockTaskId} and sku =#{sku}
    </select>
    <select id="selectOne" resultType="net.summerfarm.model.domain.StockTaskItem">
        select sti.id, stock_task_id stockTaskId, sku, quantity, actual_quantity actualQuantity, old_quantity oldQuantity
        from stock_task_item sti
            left join stock_task st on sti.stock_task_id = st.id
        where st.type = #{type} and st.task_no = #{taskNo} and sti.sku = #{sku}
    </select>
    <select id="selectByPurchaseNo" resultType="net.summerfarm.model.domain.StockTaskItem">
        select sti.id, stock_task_id stockTaskId, sku, quantity, actual_quantity actualQuantity
        from stock_task_item sti
                 left join stock_task st on sti.stock_task_id = st.id
        where st.type = #{type}
          and st.task_no = #{taskNo}
    </select>
    <select id="countCapacity" resultType="java.math.BigDecimal">
        SELECT ROUND(sum(capacity)/1000,2) total_capacity from (
        SELECT i.weight_num* sti.quantity capacity FROM `stock_task_item` sti LEFT JOIN inventory i on sti.sku=i.sku
        WHERE stock_task_id in
        <foreach collection="stockTaskIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        ) s
    </select>


</mapper>