<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceInvoiceExpandMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceInvoiceExpand">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="financialInvoiceId" column="financial_invoice_id" jdbcType="BIGINT"/>
            <result property="preferentialAmount" column="preferential_amount" jdbcType="DECIMAL"/>
            <result property="deliveryFee" column="delivery_fee" jdbcType="DECIMAL"/>
            <result property="deliveryPreferentialAmount" column="delivery_preferential_amount" jdbcType="DECIMAL"/>
            <result property="outTimesFee" column="out_times_fee" jdbcType="DECIMAL"/>
            <result property="timeFrameFee" column="time_frame_fee" jdbcType="DECIMAL"/>
            <result property="refundAmount" column="refund_amount" jdbcType="DECIMAL"/>
            <result property="skuAdjustPrice" column="sku_adjust_price" jdbcType="DECIMAL"/>
            <result property="deliveryFeeAdjustPrice" column="delivery_fee_adjust_price" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        financial_invoice_id,preferential_amount,delivery_fee,
        delivery_preferential_amount,out_times_fee,time_frame_fee,
        refund_amount,sku_adjust_price,delivery_fee_adjust_price
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from finance_invoice_expand
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectByFinancialInvoiceId" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from finance_invoice_expand
        where financial_invoice_id = #{financialInvoiceId}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from finance_invoice_expand
        where  id = #{id,jdbcType=BIGINT} 
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceInvoiceExpand" useGeneratedKeys="true">
        insert into finance_invoice_expand
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="financialInvoiceId != null">financial_invoice_id,</if>
                <if test="preferentialAmount != null">preferential_amount,</if>
                <if test="deliveryFee != null">delivery_fee,</if>
                <if test="deliveryPreferentialAmount != null">delivery_preferential_amount,</if>
                <if test="outTimesFee != null">out_times_fee,</if>
                <if test="timeFrameFee != null">time_frame_fee,</if>
                <if test="refundAmount != null">refund_amount,</if>
                <if test="skuAdjustPrice != null">sku_adjust_price,</if>
                <if test="deliveryFeeAdjustPrice != null">delivery_fee_adjust_price,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="financialInvoiceId != null">#{financialInvoiceId,jdbcType=BIGINT},</if>
                <if test="preferentialAmount != null">#{preferentialAmount,jdbcType=DECIMAL},</if>
                <if test="deliveryFee != null">#{deliveryFee,jdbcType=DECIMAL},</if>
                <if test="deliveryPreferentialAmount != null">#{deliveryPreferentialAmount,jdbcType=DECIMAL},</if>
                <if test="outTimesFee != null">#{outTimesFee,jdbcType=DECIMAL},</if>
                <if test="timeFrameFee != null">#{timeFrameFee,jdbcType=DECIMAL},</if>
                <if test="refundAmount != null">#{refundAmount,jdbcType=DECIMAL},</if>
                <if test="skuAdjustPrice != null">#{skuAdjustPrice,jdbcType=DECIMAL},</if>
                <if test="deliveryFeeAdjustPrice != null">#{deliveryFeeAdjustPrice,jdbcType=DECIMAL},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceInvoiceExpand">
        update finance_invoice_expand
        <set>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="financialInvoiceId != null">
                    financial_invoice_id = #{financialInvoiceId,jdbcType=BIGINT},
                </if>
                <if test="preferentialAmount != null">
                    preferential_amount = #{preferentialAmount,jdbcType=DECIMAL},
                </if>
                <if test="deliveryFee != null">
                    delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
                </if>
                <if test="deliveryPreferentialAmount != null">
                    delivery_preferential_amount = #{deliveryPreferentialAmount,jdbcType=DECIMAL},
                </if>
                <if test="outTimesFee != null">
                    out_times_fee = #{outTimesFee,jdbcType=DECIMAL},
                </if>
                <if test="timeFrameFee != null">
                    time_frame_fee = #{timeFrameFee,jdbcType=DECIMAL},
                </if>
                <if test="refundAmount != null">
                    refund_amount = #{refundAmount,jdbcType=DECIMAL},
                </if>
                <if test="skuAdjustPrice != null">
                    sku_adjust_price = #{skuAdjustPrice,jdbcType=DECIMAL},
                </if>
                <if test="deliveryFeeAdjustPrice != null">
                    delivery_fee_adjust_price = #{deliveryFeeAdjustPrice,jdbcType=DECIMAL},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>

</mapper>
