<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.AreaSkuRecordMapper">
    <insert id="insertSelective" parameterType="net.summerfarm.model.domain.AreaSkuRecord">
        INSERT INTO area_sku_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recorder != null">
                recorder,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="typeName != null">
                type_name,
            </if>
            <if test="resultStatus != null">
                result_status,
            </if>
            <if test="addtime != null">
                addtime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recorder != null">
                #{recorder},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="areaNo">
                #{areaNo},
            </if>
            <if test="typeName != null">
                #{typeName},
            </if>
            <if test="resultStatus != null">
                #{resultStatus,jdbcType=BIT},
            </if>
            <if test="addtime != null">
                #{addtime},
            </if>
        </trim>
    </insert>

    <select id="select" parameterType="net.summerfarm.model.domain.AreaSkuRecord"
            resultType="net.summerfarm.model.domain.AreaSkuRecord">
        SELECT id,recorder,sku,area_no areaNo,type_name typeName,result_status resultStatus,addtime
        FROM area_sku_record
        <where>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
            <if test="areaNo != null">
                AND area_no = #{areaNo}
            </if>
            <if test="typeName != null">
                AND type_name = #{typeName}
            </if>
            <if test="resultStatus != null">
                AND result_status = #{resultStatus,jdvcType=BIT}
            </if>
        </where>
    </select>

    <select id="selectNearlyRecord" resultType="net.summerfarm.model.domain.AreaSkuRecord">
        SELECT asr.id,asr.recorder,asr.sku,asr.area_no areaNo,asr.type_name typeName,asr.result_status resultStatus,asr.addtime
        FROM area_sku_record asr,
        (SELECT MAX(id) id
        FROM area_sku_record
        WHERE sku=#{sku}
        AND area_no=#{areaNo}
        AND type_name=#{typeName}
        AND addtime <![CDATA[<]]> #{endTime}  ) s
        WHERE asr.id=s.id
    </select>

    <insert id="insertBatch">
        insert area_sku_record
        (recorder, sku, area_no, type_name, result_status, addtime)
        values
        <foreach collection="sku" item="item" separator=",">
            (#{operator},#{item},#{areaNo},'上下架',0,now())
        </foreach>
    </insert>

    <select id="selectRecord" resultType="net.summerfarm.model.domain.AreaSkuRecord">
        SELECT asr.id,asr.recorder,asr.sku,asr.area_no areaNo,asr.type_name typeName,asr.result_status resultStatus,asr.addtime
        FROM area_sku_record asr
        <where>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
            <if test="areaNo != null">
                AND area_no = #{areaNo}
            </if>
            <if test="endTime != null">
                AND addtime <![CDATA[<]]> #{endTime}
            </if>
            <if test="startTime != null">
                AND addtime <![CDATA[>]]> #{startTime}
            </if>
        </where>
    </select>

    <select id="selectMaxRecord" resultType="net.summerfarm.model.domain.AreaSkuRecord">
        SELECT asr.id,asr.recorder,asr.sku,asr.area_no areaNo,asr.type_name typeName,asr.result_status resultStatus,asr.addtime
            FROM area_sku_record asr ,
            (SELECT MAX(id) id
            FROM area_sku_record
            WHERE sku=#{sku}
            AND area_no=#{areaNo}
               ) s
            WHERE asr.id=s.id
    </select>

    <select id="selectList" parameterType="net.summerfarm.model.vo.AreaSkuRecordVO"
            resultType="net.summerfarm.model.domain.AreaSkuRecord">
        SELECT sku, area_no areaNo, type_name typeName, result_status resultStatus, addtime
        FROM area_sku_record
        <where>
            <if test="startTime != null">
                AND addtime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND addtime <![CDATA[<]]> #{endTime}
            </if>
            <if test="typeName != null">
                AND type_name = #{typeName}
            </if>
        </where>
    </select>
    <select id="selectFirstOnSale" resultType="java.time.LocalDateTime">
        select addtime from area_sku_record
        where type_name = '上下架'
          and result_status = 1
          and area_no = #{areaNo}
          and sku = #{sku}
        order by id desc limit 1
    </select>

    <select id="selectLastSellOut" resultType="integer">
        select result_status  from area_sku_record
        where type_name = '售罄'
          and area_no = #{areaNo}
          and sku = #{sku}
        order by id desc limit 1
    </select>
</mapper>