<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PartnershipBuyMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PartnershipBuy">
    <!--@mbg.generated-->
    <!--@Table market_partnership_buy-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="group_buy_time" jdbcType="TIMESTAMP" property="groupBuyTime" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="min_sale_num" jdbcType="INTEGER" property="minSaleNum" />
    <result column="partnership_buy_sku_id" jdbcType="BIGINT" property="partnershipBuySkuId" />
    <result column="auto_group_buy" jdbcType="TINYINT" property="autoGroupBuy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, config_id, `status`, start_time, end_time, group_buy_time, sku, min_sale_num, 
    partnership_buy_sku_id, auto_group_buy, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from market_partnership_buy
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from market_partnership_buy
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PartnershipBuy" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into market_partnership_buy (config_id, `status`, start_time, 
      end_time, group_buy_time, sku, 
      min_sale_num, partnership_buy_sku_id, auto_group_buy, 
      create_time, update_time)
    values (#{configId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{groupBuyTime,jdbcType=TIMESTAMP}, #{sku,jdbcType=VARCHAR}, 
      #{minSaleNum,jdbcType=INTEGER}, #{partnershipBuySkuId,jdbcType=BIGINT}, #{autoGroupBuy,jdbcType=TINYINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PartnershipBuy" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into market_partnership_buy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="configId != null">
        config_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="groupBuyTime != null">
        group_buy_time,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="minSaleNum != null">
        min_sale_num,
      </if>
      <if test="partnershipBuySkuId != null">
        partnership_buy_sku_id,
      </if>
      <if test="autoGroupBuy != null">
        auto_group_buy,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupBuyTime != null">
        #{groupBuyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="minSaleNum != null">
        #{minSaleNum,jdbcType=INTEGER},
      </if>
      <if test="partnershipBuySkuId != null">
        #{partnershipBuySkuId,jdbcType=BIGINT},
      </if>
      <if test="autoGroupBuy != null">
        #{autoGroupBuy,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PartnershipBuy">
    <!--@mbg.generated-->
    update market_partnership_buy
    <set>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupBuyTime != null">
        group_buy_time = #{groupBuyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="minSaleNum != null">
        min_sale_num = #{minSaleNum,jdbcType=INTEGER},
      </if>
      <if test="partnershipBuySkuId != null">
        partnership_buy_sku_id = #{partnershipBuySkuId,jdbcType=BIGINT},
      </if>
      <if test="autoGroupBuy != null">
        auto_group_buy = #{autoGroupBuy,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PartnershipBuy">
    <!--@mbg.generated-->
    update market_partnership_buy
    set config_id = #{configId,jdbcType=BIGINT},
      `status` = #{status,jdbcType=TINYINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      group_buy_time = #{groupBuyTime,jdbcType=TIMESTAMP},
      sku = #{sku,jdbcType=VARCHAR},
      min_sale_num = #{minSaleNum,jdbcType=INTEGER},
      partnership_buy_sku_id = #{partnershipBuySkuId,jdbcType=BIGINT},
      auto_group_buy = #{autoGroupBuy,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>