<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ProductPriceAdjustmentMapper">

    <insert id="insert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO product_price_adjustment (sku,pd_name,weight,up_time,reason,status,create_admin_name,audit_admin_name) VALUES
            (#{sku},#{pdName},#{weight},#{upTime},#{reason},#{status},#{createAdminName},#{auditAdminName})
    </insert>

</mapper>