<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.PriceAdjustmentPoolMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PriceAdjustmentPool" >
    <result column="id" property="id" jdbcType="INTEGER" />
    <result column="area_no" property="areaNo" jdbcType="INTEGER" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="original_cost_price" property="originalCostPrice" jdbcType="DECIMAL" />
    <result column="cost_price" property="costPrice" jdbcType="DECIMAL" />
    <result column="original_market_price" property="originalMarketPrice" jdbcType="DECIMAL" />
    <result column="market_price" property="marketPrice" jdbcType="DECIMAL" />
    <result column="ladder_price" property="ladderPrice" jdbcType="VARCHAR"/>
    <result column="reason" property="reason" jdbcType="VARCHAR" />
    <result column="create_admin_name" property="createAdminName" jdbcType="VARCHAR" />
    <result column="business_id" property="businessId" jdbcType="BIGINT"/>
    <result column="up_time" property="upTime" jdbcType="TIMESTAMP" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="original_price" property="originalPrice" jdbcType="DECIMAL"/>
    <result column="price" property="price" jdbcType="DECIMAL"/>
  </resultMap>
  <sql id="Base_column">
    id, area_no, sku,
      status, original_cost_price, cost_price,
      original_market_price, market_price, reason,
      create_admin_name, business_id, up_time, create_time,
      update_time,original_price,price,ladder_price
  </sql>
  <insert id="insert" parameterType="net.summerfarm.model.domain.PriceAdjustmentPool" >
    insert into price_adjustment_pool (id, area_no, sku,
      status, original_cost_price, cost_price,
      original_market_price, market_price, ladder_price, reason,
      create_admin_name, business_id, up_time, create_time,
      update_time)
    values (#{id,jdbcType=INTEGER}, #{areaNo,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER}, #{originalCostPrice,jdbcType=DECIMAL}, #{costPrice,jdbcType=DECIMAL},
      #{originalMarketPrice,jdbcType=DECIMAL}, #{marketPrice,jdbcType=DECIMAL}, #{ladderPrice,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR},
      #{createAdminName,jdbcType=VARCHAR}, #{businessId,jdbcType=BIGINT}, #{upTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.PriceAdjustmentPool" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into price_adjustment_pool
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="areaNo != null" >
        area_no,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="originalCostPrice != null" >
        original_cost_price,
      </if>
      <if test="costPrice != null" >
        cost_price,
      </if>
      <if test="originalMarketPrice != null" >
        original_market_price,
      </if>
      <if test="marketPrice != null" >
        market_price,
      </if>
      <if test="ladderPrice != null">
        ladder_price,
      </if>
      <if test="reason != null" >
        reason,
      </if>
      <if test="createAdminName != null" >
        create_admin_name,
      </if>
      <if test="upTime != null" >
        up_time,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="originalPrice != null" >
        original_price,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="ruleFlag !=null">
        rule_flag,
      </if>
      <if test="interestRate !=null">
          interest_rate,
      </if>
      <if test="autoFlag !=null">
           auto_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="areaNo != null" >
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="originalCostPrice != null" >
        #{originalCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null" >
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="originalMarketPrice != null" >
        #{originalMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="marketPrice != null" >
        #{marketPrice,jdbcType=DECIMAL},
      </if>
      <if test="ladderPrice != null">
        #{ladderPrice,jdbcType=VARCHAR},
      </if>
      <if test="reason != null" >
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createAdminName != null" >
        #{createAdminName,jdbcType=VARCHAR},
      </if>
      <if test="upTime != null" >
        #{upTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="originalPrice != null" >
        #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="ruleFlag !=null">
        #{ruleFlag},
      </if>
      <if test="interestRate !=null">
        #{interestRate},
      </if>
      <if test="autoFlag !=null">
        #{autoFlag},
      </if>
    </trim>
  </insert>
    <delete id="deleteStatus">
        delete from price_adjustment_pool
        where rule_flag=0 and  area_no = #{areaNo} and status = #{status} and create_admin_name = #{creater} and sku =#{sku} and create_time between #{startTime}  and #{endTime}
    </delete>
    <select id="select" resultType="net.summerfarm.model.vo.PriceAdjustmentPoolVO">
    select pap.id                    id,
       pap.sku                   sku,
       pap.original_cost_price   originalCostPrice,
       pap.cost_price            costPrice,
       pap.original_market_price originalMarketPrice,
       pap.market_price          marketPrice,
       pap.create_admin_name     createAdminName,
       pap.create_time           createTime,
       pap.up_time               upTime,
       pap.ladder_price          ladderPrice,
       pap.reason                reason,
       i.weight                  weight,
      la.large_area_no               storeNo,
      la.large_area_name             storeName,
      a.area_no                 areaNo,
      a.area_name               areaName,
      p.pd_name                 pdName,
      if(ifnull(ak.ladder_price, '[]') = '[]', 0, 1) ladderStatus,
      ak.price                  price,
      pap.business_id           businessId,
      ak.on_sale                onSale,
      i.ext_type                extType,
      ak.m_type                 mType
      from price_adjustment_pool pap
      inner join area a on pap.area_no = a.area_no
      inner join large_area la on a.large_area_no = la.large_area_no
      inner join inventory i on pap.sku = i.sku
      inner join products p on i.pd_id = p.pd_id
      left join area_sku ak on ak.area_no = pap.area_no and ak.sku = pap.sku
      left join category c on p.category_id = c.id
      left join category pc on c.parent_id = pc.id
      <where>
        <if test="ruleFlag!=null">
            and pap.rule_flag=#{ruleFlag}
        </if>
        <if test="ruleFlag == null">
           and pap.rule_flag = 2
        </if>
        <if test="storeNo != null">
        and a.large_area_no = #{storeNo}
        </if>
        <if test="areaNo != null">
          and a.area_no = #{areaNo}
        </if>
        <if test="status != null">
          and pap.status = #{status}
        </if>
        <if test="pdName != null">
          and p.pd_name like concat('%',#{pdName},'%')
        </if>
        <if test="sku != null">
          and i.sku = #{sku}
        </if>
        <if test="onSale != null">
          and ak.on_sale = #{onSale}
        </if>
        <if test="businessId!=null">
          and pap.business_id = #{businessId}
        </if>
          <if test="categoryId != null">
              AND p.category_id = #{categoryId}
          </if>
          <if test="parentCategoryId != null">
              AND c.parent_id = #{parentCategoryId}
          </if>
          <if test="grandCategoryId != null">
              AND pc.parent_id = #{grandCategoryId}
          </if>
          <if test="categoryTypes != null">
              and c.type IN
              <foreach collection="categoryTypes" item="type" open="(" separator="," close=")">
                  #{type}
              </foreach>
          </if>
          <if test="type != null">
              and i.type = #{type}
          </if>
          <if test="extType != null">
              and i.ext_type = #{extType}
          </if>
          <if test="subType != null">
              and i.sub_type = #{subType}
          </if>
          <if test="mType != null">
              and ak.m_type = #{mType}
          </if>
      </where>
    order by pap.id desc
  </select>
  <select id="selectByBusinessId" resultType="net.summerfarm.model.vo.PriceAdjustmentPoolVO">
    select pap.id                                         id,
       pap.sku                                        sku,
       pap.original_cost_price                        originalCostPrice,
       pap.cost_price                                 costPrice,
       pap.original_market_price                      originalMarketPrice,
       pap.market_price                               marketPrice,
       pap.create_admin_name                          createAdminName,
       pap.create_time                                createTime,
       pap.up_time                                    upTime,
       pap.ladder_price                               ladderPrice,
       pap.interest_rate                              interestRate,
       pap.auto_flag                                  autoFlag,
       pap.status                                     status,
       pap.reason                                     reason,
       i.weight                                       weight,
       a.large_area_no                                   storeNo,
       a.area_no                                      areaNo,
       a.area_name                                    areaName,
       p.pd_name                                      pdName,
       if(ifnull(ak.ladder_price, '[]') = '[]', 0, 1) ladderStatus,
       ak.ladder_price                                ladderPrice,
       ak.price price,
      i.sub_type subType
    from price_adjustment_pool pap
             inner join area a on pap.area_no = a.area_no
             inner join inventory i on pap.sku = i.sku
             inner join products p on i.pd_id = p.pd_id
             left join area_sku ak on ak.area_no = pap.area_no and ak.sku = pap.sku
             where pap.business_id = #{businessId} order by pap.id desc
  </select>

  <select id="selectByBusinessIdAndRuleFlag" resultType="net.summerfarm.model.vo.PriceAdjustmentPoolVO">
      /*FORCE_MASTER*/
    select id,area_no areaNo,sku,original_market_price as originalMarketPrice ,market_price as marketPrice ,
    rule_flag ruleFlag, create_admin_name createAdminName
    from price_adjustment_pool
    where
    rule_flag = #{ruleFlag} and  business_id =#{businessId}
</select>

  <select id="selectById" resultType="net.summerfarm.model.vo.PriceAdjustmentPoolVO">
    select pap.id                                     id,
       pap.sku                                        sku,
       pap.original_cost_price                        originalCostPrice,
       pap.cost_price                                 costPrice,
       pap.original_market_price                      originalMarketPrice,
       pap.market_price                               marketPrice,
       pap.create_admin_name                          createAdminName,
       pap.create_time                                createTime,
       pap.up_time                                    upTime,
       pap.business_id                                businessId,
       pap.ladder_price                               ladderPrice,
       pap.status                                     status,
       pap.reason                                     reason,
       i.weight                                       weight,
       la.large_area_no                               largeAreaNo,
       la.large_area_name                             largeAreaName,
       a.area_no                                      areaNo,
       a.area_name                                    areaName,
       p.pd_name                                      pdName,
       if(ifnull(ak.ladder_price, '[]') = '[]', 0, 1) ladderStatus,
       ak.ladder_price                                ladderPrice,
       ak.price price
    from price_adjustment_pool pap
       inner join area a on pap.area_no = a.area_no
       inner join inventory i on pap.sku = i.sku
       inner join products p on i.pd_id = p.pd_id
       left join large_area la on la.large_area_no = a.large_area_no
       left join area_sku ak on ak.area_no = pap.area_no and ak.sku = pap.sku
       where pap.id = #{id} order by pap.id desc
  </select>

    <select id="selectByIdForceMaster" resultType="net.summerfarm.model.vo.PriceAdjustmentPoolVO">
        /*FORCE_MASTER*/
        select pap.id                                     id,
               pap.sku                                        sku,
               pap.original_cost_price                        originalCostPrice,
               pap.cost_price                                 costPrice,
               pap.original_market_price                      originalMarketPrice,
               pap.market_price                               marketPrice,
               pap.create_admin_name                          createAdminName,
               pap.create_time                                createTime,
               pap.up_time                                    upTime,
               pap.business_id                                businessId,
               pap.ladder_price                               ladderPrice,
               pap.status                                     status,
               pap.reason                                     reason,
               i.weight                                       weight,
               la.large_area_no                               largeAreaNo,
               la.large_area_name                             largeAreaName,
               a.area_no                                      areaNo,
               a.area_name                                    areaName,
               p.pd_name                                      pdName,
               if(ifnull(ak.ladder_price, '[]') = '[]', 0, 1) ladderStatus,
               ak.ladder_price                                ladderPrice,
               ak.price price
        from price_adjustment_pool pap
                 inner join area a on pap.area_no = a.area_no
                 inner join inventory i on pap.sku = i.sku
                 inner join products p on i.pd_id = p.pd_id
                 left join large_area la on la.large_area_no = a.large_area_no
                 left join area_sku ak on ak.area_no = pap.area_no and ak.sku = pap.sku
        where pap.id = #{id} order by pap.id desc
    </select>

  <update id="updateStatusByBusinessId">
    update price_adjustment_pool set status = #{status}
    <if test="status == 3">
      ,up_time = now()
    </if>
    where business_id = #{businessId}
  </update>
  <update id="updateStatus">
    update price_adjustment_pool set status = #{newStatus} where status = #{oldStatus} and area_no = #{areaNo} and sku = #{sku}
    <if test="ruleFlag!=null">
        and rule_flag = #{ruleFlag}
    </if>
  </update>
  <select id="selectNewestEffective" resultMap="BaseResultMap">
    select
       pap.id,
       pap.area_no,
       pap.sku,
       pap.status,
       pap.original_cost_price,
       pap.cost_price,
       pap.original_market_price,
       pap.market_price,
       pap.ladder_price,
       pap.reason,
       pap.create_admin_name,
       pap.up_time,
       pap.business_id,
       pap.create_time,
       pap.update_time,
       pap.original_price,
       pap.price
    from price_adjustment_pool pap
    left join area a on pap.area_no = a.area_no
    where a.parent_no = #{storeNo} and pap.sku = #{sku}
    order by pap.id desc limit 1
  </select>
  <select id="selectOutOfTime" resultType="long">
    select
    distinct business_id
    from price_adjustment_pool
    where status = 0 and create_time &lt;= #{endTime}
  </select>
  <update id="updateStatus2waitAdjust">
     update price_adjustment_pool set status = 1, up_time = #{upTime} where business_id = #{businessId}
  </update>
  <select id="selectWaitAudit" resultType="net.summerfarm.model.vo.PriceAdjustmentPoolVO">
     select pap.id                                     id,
       pap.sku                                        sku,
       pap.original_cost_price                        originalCostPrice,
       pap.cost_price                                 costPrice,
       pap.original_market_price                      originalMarketPrice,
       pap.market_price                               marketPrice,
       pap.create_admin_name                          createAdminName,
       pap.create_time                                createTime,
       pap.up_time                                    upTime,
       pap.business_id                                businessId,
       pap.ladder_price                               ladderPrice,
       pap.status                                     status,
       pap.reason                                     reason,
       i.weight                                       weight,
       a.large_area_no                                   storeNo,
       a.area_no                                      areaNo,
       a.area_name                                    areaName,
       p.pd_name                                      pdName,
       if(ifnull(ak.ladder_price, '[]') = '[]', 0, 1) ladderStatus,
       ak.ladder_price                                ladderPrice,
       ak.price price
    from price_adjustment_pool pap
       inner join area a on pap.area_no = a.area_no
       inner join inventory i on pap.sku = i.sku
       inner join products p on i.pd_id = p.pd_id
       left join area_sku ak on ak.area_no = pap.area_no and ak.sku = pap.sku
       where pap.status = 0 and pap.sku = #{sku} and a.large_area_no = #{storeNo}
  </select>
  <select id="selectBusinessId" resultType="long">
    select business_id from price_adjustment_pool where status = 5 and area_no = #{areaNo} and sku= #{sku} and original_market_price = #{oPrice} and market_price = #{price} order by id desc limit 1
  </select>
  <select id="updateStatus2success">
    update price_adjustment_pool set status = 3,update_time = #{upTime} where status = 1 and area_no = #{areaNo} and sku = #{sku}
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select
    <include refid="Base_column"/>
    from price_adjustment_pool where id = #{id}
  </select>

    <select id="selectByPrimaryKeyForceMaster" resultMap="BaseResultMap">
        /*FORCE_MASTER*/
        select
        <include refid="Base_column"/>
        from price_adjustment_pool where id = #{id}
    </select>
    <select id="selectByCondition" resultType="java.lang.Integer">
        /*FORCE_MASTER*/
        select count(1) from price_adjustment_pool
        where status = #{status} and area_no = #{areaNo} and sku = #{sku}
        <if test="upTime == null and type ==1 ">
            and up_time is null
        </if>
        <if test="upTime != null and type == 1 ">
            and up_time = #{upTime}
        </if>
    </select>
    <select id="selectByAreaNO" resultType="net.summerfarm.model.vo.PriceAdjustmentPoolVO">
        select id,area_no areaNo,sku,original_market_price as originalMarketPrice ,market_price as marketPrice ,
        rule_flag ruleFlag, create_admin_name createAdminName
        from price_adjustment_pool
        where
            rule_flag = 0
        and create_time between #{yesterdayTen} and #{nowTen} and area_no =#{areaNo}
    </select>
    <select id="selectAreaNoByTime" resultType="java.lang.Integer">
        select distinct area_no
        from price_adjustment_pool
        where
            rule_flag = 0
          and create_time between #{startTime} and #{endTime}
    </select>
    <update id="updateByBusinessIdAndAreaNo">
    update price_adjustment_pool set market_price = #{price}, ladder_price=#{ladderPrice} where business_id = #{businessId} and area_no = #{areaNo} and sku = #{sku}
  </update>
    <update id="updateRuleFlag">
        update price_adjustment_pool set rule_flag = #{ruleFlag}
        <if test="status!=null">
            , status = #{status}
        </if>
        where rule_flag = 0
    <if test="id">
        and id = #{id}
    </if>
    </update>
    <update id="updatePriceAndCost">
        update price_adjustment_pool set cost_price = #{firstCycleCost} ,original_market_price = #{originalPrice} , market_price = #{newPrice}  where id = #{id}
    </update>
    <update id="updateBusinessIdById">
        update price_adjustment_pool set business_id = #{nextBusinessId} where id = #{id}
    </update>

  <select id="selectWaitExcuteByAreaNoAndSku" resultMap="BaseResultMap">
    select <include refid="Base_column" />
    from price_adjustment_pool
    where sku = #{sku} and area_no = #{areaNo} and status = 1
    order by id desc
    limit 1
  </select>

  <select id="selectManualBySkuAndAreaNo" resultMap="BaseResultMap">
    select <include refid="Base_column" />
    from price_adjustment_pool
    where sku = #{sku} and area_no = #{areaNo}
    and create_time BETWEEN #{startTime} AND #{endTime}
    and create_admin_name is not null and create_admin_name not in( '自动','系统默认')
    limit 1
  </select>
</mapper>