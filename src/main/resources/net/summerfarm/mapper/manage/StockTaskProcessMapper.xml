<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTaskProcessMapper">

    <resultMap id="withDetail" type="net.summerfarm.model.vo.StockTaskProcessVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="stock_task_id" property="stockTaskId" jdbcType="INTEGER"/>
        <result column="addtime" property="addtime"/>
        <result column="recorder" property="recorder" jdbcType="VARCHAR"/>
        <result column="task_no" property="taskNo" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="expect_time" property="expectTime"/>
        <result column="dimension" property="dimension"/>
        <collection property="processDetailVOS" column="id" javaType="ArrayList"
                    select="net.summerfarm.mapper.manage.StockTaskProcessDetailMapper.selectByProcessId"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="net.summerfarm.model.domain.StockTaskProcess">
        INSERT INTO stock_task_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stockTaskId != null">
                stock_task_id,
            </if>
            <if test="addtime != null">
                addtime,
            </if>
            <if test="recorder != null">
                recorder,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stockTaskId != null">
                #{stockTaskId},
            </if>
            <if test="addtime != null">
                #{addtime},
            </if>
            <if test="recorder != null">
                #{recorder},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
        </trim>
    </insert>

    <update id="updateTimeByTaskId">
        update stock_task_process
        set addtime = #{updateTime}
        where stock_task_id = #{taskId}
    </update>

    <select id="selectStockTaskProcessVO" resultMap="withDetail">
        SELECT DISTINCT stp.stock_task_id,stp.id,st.type,st.task_no ,st.area_no area_no ,
        st.expect_time ,stp.recorder,st.state,stp.addtime,st.dimension,pp.supplier
        FROM stock_task_process stp
        INNER JOIN stock_task st ON stp.stock_task_id=st.id
        INNER JOIN stock_task_process_detail stpd ON stp.id=stpd.stock_task_process_id
        LEFT JOIN purchases_plan pp ON st.task_no = pp.purchase_no
        INNER JOIN inventory i ON stpd.sku=i.sku
        INNER JOIN products p ON i.pd_id=p.pd_id
        <where>
            stp.tenant_id = IFNULL(#{tenantId},1)
            <if test="list != null and list.size > 0">
                AND st.type IN
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="type != null">
                AND st.type = #{type}
            </if>
            <if test="areaNo != null">
                AND st.area_no = #{areaNo}
            </if>
            <if test="stockTaskId != null">
                AND st.id = #{stockTaskId}
            </if>
            <if test="pdName != null">
                AND p.pd_name LIKE concat('%',#{pdName},'%')
            </if>
            <if test="dimension != null">
                AND st.dimension = #{dimension}
            </if>
            <if test="supplierId != null">
                AND pp.supplier_id = #{supplierId}
            </if>
            <if test="sku != null">
                AND i.sku = #{sku}
            </if>
            <if test="startAddTime != null">
                AND stp.addtime <![CDATA[>=]]> #{startAddTime}
            </if>
            <if test="endAddTime != null">
                AND stp.addtime <![CDATA[<]]> #{endAddTime}
            </if>
        </where>
        GROUP BY stp.stock_task_id, stp.id
        ORDER BY stp.addtime DESC
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer"
            resultType="net.summerfarm.model.vo.StockTaskProcessVO">
        /*FORCE_MASTER*/ SELECT stp.id, st.id stockTaskId, st.task_no taskNo, stp.addtime, st.area_no areaNo, stp.recorder, stp.tenant_id tenantId
        FROM stock_task_process stp
                 INNER JOIN stock_task st ON stp.stock_task_id = st.id
        WHERE stp.id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByInboundOrderId" parameterType="java.lang.Long"
            resultType="net.summerfarm.model.domain.StockTaskProcess">
        SELECT stp.id, st.id stockTaskId, st.task_no taskNo, stp.addtime, st.area_no areaNo, stp.recorder
        FROM stock_task_process stp
                 INNER JOIN stock_task st ON stp.stock_task_id = st.id
        WHERE stp.in_bound_order_id = #{inboundId}
    </select>

    <select id="selectByTaskNo" resultType="net.summerfarm.model.domain.StockTaskProcess">
        SELECT stp.id
        FROM stock_task_process stp
                 INNER JOIN stock_task st ON stp.stock_task_id = st.id
        WHERE st.task_no = #{listNo}
    </select>

    <select id="selectByTaskId" resultType="java.lang.Long">
        select id
        from stock_task_process
        where stock_task_id = #{taskId}
    </select>

    <select id="selectByTaskIdNew" resultType="net.summerfarm.model.domain.StockTaskProcess">
        SELECT stp.id
        FROM stock_task st
                 INNER JOIN stock_task_process stp ON stp.stock_task_id = st.id
        WHERE st.id = #{stockTaskId}
    </select>

</mapper>
