<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ContactAdjustMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.ContactAdjust">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="add_time" property="addTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="contact_id" property="contactId"/>
        <result column="new_poi" property="newPoi"/>
        <result column="m_id" property="mId"/>
        <result column="status" property="status"/>
    </resultMap>
    
    <select id="selectContactAdjustList" resultType="net.summerfarm.model.vo.MerchantVO" parameterType="net.summerfarm.model.vo.MerchantVO">
        select ca.id contactAdjustId, a.realname adminRealname,
              m.m_id mId,m.mname, m.mcontact,
              m.phone, m.province, m.city, m.area,
              m.address,m.area_no areaNo ,ar.area_name areaName,a2.contract_method contractMethod,
              m.size,m.type,m.direct,m.admin_id adminId,m.grade,a2.realname,a2.name_remakes nameRemakes,a2.admin_type adminType,
               m.enterprise_scale enterpriseScale,ca.add_time registerTime,ic.invoice_title invoiceTitle
        from contact_adjust ca
        left join merchant m on ca.m_id = m.m_id
        left join invoice_config ic on m.m_id = ic.merchant_id and ic.valid_status = 0
        LEFT JOIN invitecode i on i.invitecode = m.invitecode
        LEFT JOIN admin a on a.admin_id = i.admin_id
        left join admin a2 on a2.admin_id = m.admin_id
        left join area ar on m.area_no = ar.area_no
        <where>
            ca.status = 0
            <if test="mId !=null and mId !=0 ">
                and m.m_id=#{mId,jdbcType=BIGINT}
            </if>

            <if test="size !=null ">
                and m.size LIKE CONCAT('%',#{size,jdbcType=VARCHAR},'%')
            </if>
            <if test="enterpriseScale!=null">
                and m.enterprise_scale = #{enterpriseScale}
            </if>
            <if test="adminType!=null">
                and a2.admin_type = #{adminType}
            </if>
            <if test="direct !=null ">
                and m.direct=#{direct}
            </if>
            <if test="phone !=null">
                and m.phone like CONCAT('%',#{phone,jdbcType=VARCHAR},'%')
            </if>
            <if test="mname !=null">
                and m.mname LIKE CONCAT('%',#{mname,jdbcType=VARCHAR},'%')
            </if>
            <if test="registerTime !=null ">
                and m.register_time >=#{registerTime,jdbcType=TIMESTAMP}
            </if>
            <if test="areaNo !=null ">
                and m.area_no =#{areaNo}
            </if>
            <if test="grade != null">
                <choose>
                    <when test="grade == 100">
                        AND  grade IS NULL
                    </when>
                    <otherwise>
                        AND grade = #{grade}
                    </otherwise>
                </choose>
            </if>

        </where>
        order by ca.add_time desc
    </select>

    <select id="selectContactAdjustDetail" resultType="net.summerfarm.model.vo.ContactAdjustVO" >
          select  ca.id,
                  ca.new_poi newPoi, ca.status,
                  ca.new_province newProvince ,
                  ca.new_city newCity,
                  ca.new_area newArea,
                  ca.new_address newAddress,
                  c.contact_id contactId,
                  c.province, c.city,
                  c.area,c.address,
                  c.poi_note poiNote,
                  m.area_no areaNo,
                  c.contact ,
                  c.phone,
                  c.house_number houseNumber,
                  c.address_remark as addressRemark
        from contact_adjust  ca
        left join contact c on ca.contact_id = c.contact_id
        left join merchant m on m.m_id = c.m_id
        where ca.id = #{contactAdjustId}
    </select>

    <update id="updateContactAdjustStatus" parameterType="net.summerfarm.model.domain.ContactAdjust">
        update contact_adjust
        <set>
            status = #{status},
            <if test="newHouseNumber != null">
                new_house_number = #{newHouseNumber}
            </if>
        </set>  where id = #{id}
    </update>

    <update id="updateStatus" parameterType="net.summerfarm.model.domain.ContactAdjust">
        update contact_adjust
           set status =3
          where status = 2
    </update>

    
</mapper>