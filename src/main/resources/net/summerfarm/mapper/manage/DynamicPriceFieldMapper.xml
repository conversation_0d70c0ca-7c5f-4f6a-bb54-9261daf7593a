<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DynamicPriceFieldMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DynamicPriceField">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="field_name" jdbcType="VARCHAR" property="fieldName"/>
    <result column="field_alias" jdbcType="VARCHAR" property="fieldAlias"/>
    <result column="x_comment" jdbcType="VARCHAR" property="xComment"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `field_name`, `field_alias`, `x_comment`, `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_field
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from dynamic_price_field
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.DynamicPriceField">
    insert into dynamic_price_field (`id`, `field_name`, `field_alias`,
                                     `x_comment`, `create_time`, `update_time`)
    values (#{id,jdbcType=BIGINT}, #{fieldName,jdbcType=VARCHAR}, #{fieldAlias,jdbcType=VARCHAR},
            #{xComment,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.DynamicPriceField">
    insert into dynamic_price_field
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="fieldName != null">
        `field_name`,
      </if>
      <if test="fieldAlias != null">
        `field_alias`,
      </if>
      <if test="xComment != null">
        `x_comment`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fieldName != null">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldAlias != null">
        #{fieldAlias,jdbcType=VARCHAR},
      </if>
      <if test="xComment != null">
        #{xComment,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.model.domain.DynamicPriceField">
    update dynamic_price_field
    <set>
      <if test="fieldName != null">
        `field_name` = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldAlias != null">
        `field_alias` = #{fieldAlias,jdbcType=VARCHAR},
      </if>
      <if test="xComment != null">
        `x_comment` = #{xComment,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DynamicPriceField">
    update dynamic_price_field
    set `field_name`  = #{fieldName,jdbcType=VARCHAR},
        `field_alias` = #{fieldAlias,jdbcType=VARCHAR},
        `x_comment`   = #{xComment,jdbcType=VARCHAR},
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
        `update_time` = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from
    dynamic_price_field
  </select>

  <select id="listByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from
    dynamic_price_field
    <where>
      id in
      <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </where>
  </select>
</mapper>