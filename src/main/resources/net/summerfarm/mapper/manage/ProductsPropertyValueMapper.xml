<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ProductsPropertyValueMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.ProductsPropertyValue">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="pd_id" jdbcType="INTEGER" property="pdId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="products_property_id" jdbcType="INTEGER" property="productsPropertyId" />
    <result column="products_property_value" jdbcType="VARCHAR" property="productsPropertyValue" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pd_id, sku, products_property_id, products_property_value, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from products_property_value
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from products_property_value
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.ProductsPropertyValue" useGeneratedKeys="true">
    insert into products_property_value (pd_id, sku, products_property_id,
      products_property_value, creator, create_time
      )
    values (#{pdId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{productsPropertyId,jdbcType=INTEGER},
      #{productsPropertyValue,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.ProductsPropertyValue" useGeneratedKeys="true">
    insert into products_property_value
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="productsPropertyId != null">
        products_property_id,
      </if>
      <if test="productsPropertyValue != null">
        products_property_value,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pdId != null">
        #{pdId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="productsPropertyId != null">
        #{productsPropertyId,jdbcType=INTEGER},
      </if>
      <if test="productsPropertyValue != null">
        #{productsPropertyValue,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.ProductsPropertyValue">
    update products_property_value
    <set>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="productsPropertyId != null">
        products_property_id = #{productsPropertyId,jdbcType=INTEGER},
      </if>
      <if test="productsPropertyValue != null">
        products_property_value = #{productsPropertyValue,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.ProductsPropertyValue">
    update products_property_value
    set pd_id = #{pdId,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      products_property_id = #{productsPropertyId,jdbcType=INTEGER},
      products_property_value = #{productsPropertyValue,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateG2g">
    update products_property_value
    set products_property_value =REPLACE(products_property_value, 'G', 'g')
    where sku in
    <foreach close=")" collection="skus" item="item" open="(" separator=",">
      #{item}
    </foreach> and products_property_id =5
  </update>
  <select id="selectValue" resultType="net.summerfarm.model.vo.ProductsPropertyValueVO">
    select ppv.id,
      ppv.pd_id                   pdId,
      ppv.sku,
      ppv.products_property_id    productsPropertyId,
      ppv.products_property_value productsPropertyValue,
      ppv.creator,
      ppv.create_time         createTime,
      name,
      pp.type,
      format_type             formatType,
      format_str              formatStr
    from products_property_value ppv
    left join products_property pp on ppv.products_property_id = pp.id
    <where>
        pp.status = 1
      <if test="pdId != null">
        and ppv.pd_id = #{pdId}
      </if>
      <choose>
        <when test="sku != null">
          and ppv.sku = #{sku}
        </when>
        <otherwise>
          and ppv.sku is null
        </otherwise>
      </choose>
    </where>
    order by pp.create_time
  </select>

  <select id="selectBySkuAndSpu" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from products_property_value
    where sku = #{sku} and pd_id =#{pdId} and products_property_id = #{productsPropertyId}
  </select>

  <select id="selectKeyPropertyValue" resultMap="BaseResultMap">
    select
    DISTINCT ppv.products_property_value FROM products_property_value ppv
    LEFT JOIN products p on p.pd_id=ppv.pd_id
    where p.category_id=#{categoryId} and ppv.products_property_id=#{productsPropertyId}
  </select>

  <delete id="deleteByPdId">
    delete from products_property_value
    where pd_id = #{pdId} and
          sku is not null and
          products_property_id not in
          <foreach collection="noDel" item="item" index="index" open="(" separator="," close=")">
            #{item}
          </foreach>
  </delete>
  <delete id="deleteCategoryKeyPropertyValue">
    delete from products_property_value
    where products_property_id = #{propertyId}
      and pd_id in (select p.pd_id from products p where p.category_id = #{categoryId})
  </delete>
  <select id="selectPdNoByKeyValue" resultType="string">
    select p.pd_no from products_property_value ppv
        left join products p on ppv.pd_id = p.pd_id
    where p.category_id = #{categoryId}
      and p.create_type = #{createType}
      and ppv.products_property_id = #{productsPropertyId}
      and ppv.products_property_value = #{productsPropertyValue}
      and p.outdated  in(-1,0)
      and p.audit_status in(0,1)
  </select>
  <select id="selectSkuBySaleValue" resultType="string">
    select ppv.sku from products_property_value ppv
        inner join inventory i on ppv.sku = i.sku
    where ppv.pd_id = #{pdId}
      and i.ext_type = #{extType}
      and ppv.products_property_id = #{productsPropertyId}
      and ppv.products_property_value = #{productsPropertyValue}
    and i.type = #{type}
    and i.admin_id = #{adminId}
  </select>

  <delete id="deleteSpuPropertyValue">
    delete from products_property_value where pd_id = #{pdId,jdbcType=BIGINT} and sku is null
  </delete>
  <select id="selectBrandId" resultType="long">
    SELECT ppv.pd_id FROM products_property_value ppv
    INNER JOIN brand b ON b.`name` = ppv.products_property_value
    WHERE ppv.products_property_id = 2 GROUP BY ppv.pd_id
  </select>

  <resultMap id="SkuPropertyValueMap" type="net.summerfarm.model.DTO.inventory.SkuPropertyInfoDTO">
    <result column="sku" property="sku"/>
    <collection property="skuPropertyValues" ofType="net.summerfarm.model.DTO.inventory.SkuPropertyValueDTO">
      <result column="products_property_id" property="productsPropertyId"/>
      <result column="products_property_value" property="productsPropertyValue"/>
    </collection>
  </resultMap>
  <select id="selectByPdIdAndSkusAndPropertyType" resultMap="SkuPropertyValueMap">
    select sku, products_property_id, products_property_value
    from products_property pp
         left join products_property_value ppv on pp.id = ppv.products_property_id
    where pd_id = #{pdId}
        and sku in <foreach collection="skus" item="sku" open="(" close=")" separator=",">
                        #{sku}
                   </foreach>
        and type = #{type}
  </select>
  <resultMap id="SkuPropertyValueDTOMap" type="net.summerfarm.model.DTO.inventory.SkuPropertyValueDTO">
    <result column="products_property_id" property="productsPropertyId"/>
    <result column="products_property_value" property="productsPropertyValue"/>
  </resultMap>
  <select id="selectByPdIdAndSkuAndPropertyType" resultMap="SkuPropertyValueDTOMap">
    select sku, products_property_id, products_property_value
    from products_property pp
    left join products_property_value ppv on pp.id = ppv.products_property_id
    where pd_id = #{pdId} and sku = #{sku} and type = #{type}
  </select>

  <select id="selectByProductsPropertyIdInAndPdIdOrSku" resultMap="BaseResultMap">
    select ppv.id, pd_id, sku, products_property_id, products_property_value,
    ppv.creator, ppv.create_time
    from products_property_value ppv left join products_property pp on ppv.products_property_id = pp.id
    where products_property_id in
    <foreach item="item" index="index" collection="productsPropertyIds"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
    and pd_id=#{pdId} and ((type = 0) or (sku=#{sku} and type = 1))
  </select>

  <resultMap id="ProductsPropertyResutMap" type="net.summerfarm.model.domain.ProductsPropertyValueInfo">
    <id column="id" property="id"/>
    <result column="pd_id" property="pdId"/>
    <result column="sku" property="sku"/>
    <result column="products_property_id" property="productsPropertyId"/>
    <result column="products_property_value" property="productsPropertyValue"/>
    <result column="creator" property="creator"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="type" property="type"/>
  </resultMap>
  <select id="selectByPdIdAndProductsPropertyIds" resultMap="ProductsPropertyResutMap">
    select ppv.id,ppv.pd_id,ppv.sku,ppv.products_property_id,ppv.products_property_value,ppv.creator,
            ppv.create_time,ppv.update_time,pp.type
    from products_property_value ppv left join products_property pp on ppv.products_property_id = pp.id
    where pd_id=#{pdId,jdbcType=INTEGER} and ppv.sku is null and products_property_id in
        <foreach item="item" index="index" collection="productsPropertyIds" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

  <select id="selectAllBySku" resultMap="ProductsPropertyResutMap">
    select ppv.id,ppv.pd_id,ppv.sku,ppv.products_property_id,ppv.products_property_value,ppv.creator,
           ppv.create_time,ppv.update_time,pp.type
    from products_property_value ppv left join products_property pp on ppv.products_property_id = pp.id
    where ((pd_id = #{pdId} and pp.type = 0) or (pd_id = #{pdId} and sku = #{sku} and type = 1))
          <if test="productsPropertyIds != null and productsPropertyIds.size() != 0">
            and products_property_id in <foreach item="item" index="index" collection="productsPropertyIds" open="(" separator="," close=")">
                                            #{item,jdbcType=INTEGER}
                                        </foreach>
          </if>
  </select>

  <select id="selectAllproductsPropertyValuepropertyId" resultType="java.lang.String">
    select products_property_value
    from products_property_value
    where products_property_id = #{propertyId}
    limit #{pageVo.index}, #{pageVo.pageSize}
  </select>
  <select id="selectByPdIdAndPropertyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from products_property_value
    where pd_id = #{pdId} and products_property_id = #{propertyId}
  </select>

  <select id="selectAllLevelPropertySku" resultType="java.lang.String">
    select distinct sku
    from products_property_value
    where products_property_id = #{productsPropertyId}
      and sku is not null
  </select>
  <select id="selectByPdIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from products_property_value
    where pd_id in
    <foreach collection="pdIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>

  <select id="listByPdIds" resultType="net.summerfarm.model.vo.ProductsPropertyValueVO">
    select ppv.id,
    pd_id                   pdId,
    sku,
    ppv.products_property_id    productsPropertyId,
    ppv.products_property_value productsPropertyValue,
    name
    from products_property_value ppv
    left join products_property pp on ppv.products_property_id = pp.id
    <where>
      pp.status = 1 and ppv.sku is null
      <if test="pdIds != null and pdIds.size > 0">
        and ppv.pd_id in
        <foreach collection="pdIds" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="selectByPdIdAndSkuAndProductsPropertyIds" resultMap="ProductsPropertyResutMap">
    select ppv.id,ppv.pd_id,ppv.sku,ppv.products_property_id,ppv.products_property_value,ppv.creator,
    ppv.create_time,ppv.update_time,pp.type
    from products_property_value ppv left join products_property pp on ppv.products_property_id = pp.id
    where pd_id=#{pdId,jdbcType=INTEGER} and ppv.sku = #{sku} and products_property_id in
    <foreach item="item" index="index" collection="productsPropertyIds" open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>
    <select id="listValueByPropertyId" resultType="java.lang.String">
      select DISTINCT(products_property_value) FROM products_property_value WHERE products_property_id = #{productsPropertyId} and products_property_value is not null and products_property_value!="" and products_property_value!="无" and products_property_value!=" 无" and products_property_value!="  无" and products_property_value!="-" and products_property_value!=" -";
    </select>
  <select id="queryContainsGSku" resultType="java.lang.String">
    SELECT sku
    FROM products_property_value
    WHERE products_property_id = 5
    AND BINARY products_property_value LIKE '%G%'
    AND products_property_value NOT LIKE '%KG%'
  </select>
</mapper>
