<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTaskProcessDetailMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.model.domain.StockTaskProcessDetail" >
        INSERT INTO stock_task_process_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stockTaskProcessId != null">
                stock_task_process_id,
            </if>
            <if test="itemId != null">
                item_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="listNo != null">
                list_no,
            </if>
            <if test="qualityDate != null">
                quality_date,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="productionDate != null">
                production_date,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="transferSku != null">
                transfer_sku,
            </if>
            <if test="transferQuantity != null">
                transfer_quantity,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="transferScale != null">
                transfer_scale,
            </if>
            <if test="glNo != null">
                gl_no,
            </if>
            <if test="inGlNo != null">
                in_gl_no,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="liableOwner != null">
                liable_owner,
            </if>
            <if test="cabinetCode != null">
                cabinet_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stockTaskProcessId != null">
                #{stockTaskProcessId},
            </if>
            <if test="itemId != null">
                #{itemId},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="listNo != null">
                #{listNo},
            </if>
            <if test="qualityDate != null">
                #{qualityDate},
            </if>
            <if test="quantity != null">
                #{quantity},
            </if>
            <if test="productionDate != null">
                #{productionDate},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="transferSku != null">
                #{transferSku},
            </if>
            <if test="transferQuantity != null">
                #{transferQuantity},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="transferScale != null">
                #{transferScale},
            </if>
            <if test="glNo != null">
                #{glNo},
            </if>
            <if test="inGlNo != null">
                #{inGlNo},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="liableOwner != null">
                #{liableOwner,jdbcType=VARCHAR},
            </if>
            <if test="cabinetCode != null">
                #{cabinetCode},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="net.summerfarm.model.domain.StockTaskProcessDetail">
        INSERT INTO stock_task_process_detail(stock_task_process_id,item_id,sku,list_no,quality_date,quantity,production_date, transfer_sku, transfer_quantity, create_time, creator, transfer_scale,gl_no,in_gl_no,transfer_list_no
            , cabinet_code)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.stockTaskProcessId},#{item.itemId},#{item.sku},#{item.listNo},#{item.qualityDate},#{item.quantity},#{item.productionDate}, #{item.transferSku}, #{item.transferQuantity}, #{item.createTime}, #{item.creator},
            #{item.transferScale},#{item.glNo},#{item.inGlNo},#{item.transferListNo}
            ,#{item.cabinetCode}
            )
        </foreach>
    </insert>

    <update id="updateState" parameterType="int">
        update stock_task_process_detail
        set `state` = #{state}
        where id = #{id}
    </update>

    <select id="selectByProcessId" resultType="net.summerfarm.model.vo.StockTaskProcessDetailVO">
        /*FORCE_MASTER*/ SELECT	stpd.id,p.pd_name pdName,i.pack,i.weight,stpd.sku,stpd.quantity,stpd.quality_date qualityDate,
               stpd.production_date productionDate,stpd.list_no listNo,stpd.remark,stpe.is_complete isComplete,stpe.remark proveRemark
        ,ad.name_remakes nameRemakes,stpd.gl_no glNo,i.ext_type extType,i.inv_id skuId, stpd.liable_owner liableOwner
        FROM stock_task_process_detail stpd
        LEFT JOIN stock_task_process_expand stpe on stpe.stock_task_process_detail_id= stpd.id
        INNER JOIN inventory i ON stpd.sku=i.sku
        INNER JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN admin ad on ad.admin_id = i.admin_id
        WHERE  stpd.stock_task_process_id = #{stockTaskProcessId,jdbcType=INTEGER} and status = 1
    </select>

    <select id="selectByTaskId" resultType="net.summerfarm.model.vo.StockTaskProcessDetailVO">
        SELECT stpd.id,
               stpd.sku sku,
               stpd.quantity quantity,
               stpd.quality_date    qualityDate,
               stpd.production_date productionDate,
               stpd.list_no         listNo
        FROM stock_task_process_detail stpd
                 inner join stock_task_process stp on stp.id = stpd.stock_task_process_id
        WHERE stp.stock_task_id = #{stockTaskId}
    </select>



    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="net.summerfarm.model.domain.StockTaskProcessDetail">
        SELECT id,stock_task_process_id stockTaskProcessId,item_id itemId,sku,list_no listNo,quality_date qualityDate,quantity,
        add_time addTime,`state`
        FROM stock_task_process_detail
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        DELETE
        FROM stock_task_process_detail
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <select id="selectTransferByProcessId" parameterType="java.lang.Integer" resultType="net.summerfarm.model.vo.StockTaskProcessDetailVO">
    select d.id,
           stock_task_process_id stockTaskProcessId,
           item_id itemId,
           d.sku,
           list_no listNo,
           quality_date qualityDate,
           quantity,
           d.production_date productionDate,
           d.remark,
           transfer_sku transferSku,
           transfer_quantity transferQuantity,
           d.create_time createTime,
           d.creator,
           i.weight,
           p.pd_name pdName,
           i2.weight transferWeight,
           p2.pd_name transferPdName,
           d.transfer_scale transferScale,
           d.transfer_list_no transferListNo,
           d.add_time addTime
    from stock_task_process_detail d
             left join stock_task_process stp on d.stock_task_process_id = stp.id
             left join inventory i on d.sku = i.sku
             left join products p on i.pd_id = p.pd_id
             left join inventory i2 on d.transfer_sku = i2.sku
             left join products p2 on i2.pd_id = p2.pd_id
    WHERE stp.stock_task_id = #{taskId} and d.status = 1

    order by d.id desc
    </select>

    <select id="selectTransferByProcessIdForSync" parameterType="java.lang.Integer" resultType="net.summerfarm.data.dto.StockTaskProcessDetailDTO">
    select d.id,
           stock_task_process_id stockTaskProcessId,
           item_id itemId,
           d.sku,
           list_no listNo,
           quality_date qualityDate,
           quantity,
           d.production_date productionDate,
           d.remark,
           transfer_sku transferSku,
           transfer_quantity transferQuantity,
           d.create_time createTime,
           d.creator,
           i.weight,
           p.pd_name pdName,
           i2.weight transferWeight,
           p2.pd_name transferPdName,
           d.transfer_scale transferScale,
           d.transfer_list_no transferListNo,
           d.add_time createdAt
    from stock_task_process_detail d
             left join stock_task_process stp on d.stock_task_process_id = stp.id
             left join inventory i on d.sku = i.sku
             left join products p on i.pd_id = p.pd_id
             left join inventory i2 on d.transfer_sku = i2.sku
             left join products p2 on i2.pd_id = p2.pd_id
    WHERE stp.stock_task_id = #{taskId} and d.status = 1

    order by d.id desc
    </select>

    <select id="selectByProcess" parameterType="net.summerfarm.model.domain.StockTaskProcessDetail" resultType="net.summerfarm.model.vo.StockTaskProcessDetailVO">
        select stpd.quality_date qualityDate, sum(stpd.quantity) inQuantity, stpd.list_no listNo,stpd.gl_no glNo, stpd.sku,stpd.production_date productionDate   from stock_task st
            left join stock_task_process stp on st.id = stp.stock_task_id
            inner join stock_task_process_detail stpd on stpd.stock_task_process_id = stp.id and stpd.list_no = #{listNo} and stpd.sku = #{sku} and stpd.quality_date = #{qualityDate}
            where st.type = 11
        group by stpd.quality_date,stpd.list_no,stpd.gl_no,stpd.sku
    </select>

    <select id="selectByProcessDetail" parameterType="net.summerfarm.model.vo.StockTaskProcessDetailVO" resultType="net.summerfarm.model.vo.StockTaskProcessDetailVO">
        select stpd.quality_date qualityDate, sum(stpd.quantity) inQuantity, stpd.list_no listNo,stpd.gl_no glNo, stpd.sku,stpd.production_date productionDate
        from stock_task st
            left join stock_task_process stp on st.id = stp.stock_task_id
            inner join stock_task_process_detail stpd on stpd.stock_task_process_id = stp.id and stpd.list_no = #{listNo} and st.task_no = #{taskNo} and stpd.sku = #{sku} and stpd.quality_date = #{qualityDate}
            where st.type = 10
        group by stpd.quality_date,stpd.list_no,stpd.gl_no,stpd.sku
    </select>
    <select id="selectByPurchaseNoAndSku" resultType="net.summerfarm.model.domain.StockTaskProcessDetail">
      select stpd.id,
       stock_task_process_id stockTaskProcessId,
       item_id itemId,
       sku,
       list_no listNo,
       quality_date quantityDate,
       quantity,
       production_date productionDate,
       stpd.remark,
       transfer_scale transferScale,
       transfer_sku transferSku,
       transfer_quantity transferQuantity,
       create_time createTime,
       creator
      from stock_task_process_detail stpd
        left join stock_task_process stp on stpd.stock_task_process_id = stp.id
        left join stock_task st on stp.stock_task_id = st.id
      <where>
            st.state in (1, 2)
            and stpd.status = 1
          <if test="type != null">
              and st.type = #{type}
          </if>
          <if test="purchaseNo != null">
              and list_no = #{purchaseNo}
          </if>
          <if test="sku != null">
              and transfer_sku = #{sku}
          </if>
      </where>
      order by stpd.id asc
    </select>
    <select id="selectTransferUnValidDetail" resultType="net.summerfarm.model.domain.StockTaskProcessDetail">
      select stpd.id,
       stock_task_process_id stockTaskProcessId,
       item_id itemId,
       sku,
       list_no listNo,
       quality_date qualityDate,
       quantity,
       production_date productionDate,
       remark,
       transfer_sku transferSku,
       transfer_quantity transferQuantity,
       transfer_scale transferScale,
       create_time createTime,
       creator,
       gl_no glNo,
       in_gl_no inGlNo,
       status
      from stock_task_process_detail stpd
               left join stock_task_process stp on stpd.stock_task_process_id = stp.id
      where stpd.status = 0 and stp.stock_task_id = #{stockTaskId} and stpd.transfer_sku = #{sku}
    </select>
    <select id="unFinishTemporaryTransferTask" resultType="boolean">
    select  count(1) > 0 from stock_task_process_detail stpd
      left join stock_task_process stp on stp.id = stpd.stock_task_process_id
      left join stock_task st on stp.stock_task_id = st.id
    where st.type = 82
      and st.state in (0, 1)
      and st.remark = '临保'
      and st.area_no = #{storeNo}
      and stpd.transfer_sku = #{sku}
      and stpd.list_no = #{listNo}
      and stpd.quality_date =  #{qualityDate}
      <if test="glNo != null">
        and stpd.gl_no = #{glNo}
      </if>
    </select>

    <select id="selectByStockTaskId"  resultType="net.summerfarm.model.vo.StockTaskProcessDetailVO">
      select stpd.id,
       stock_task_process_id stockTaskProcessId,
       item_id itemId,
       sku,
       list_no listNo,
       quality_date qualityDate,
       quantity,
       production_date productionDate,
       remark,
       transfer_sku transferSku,
       transfer_quantity transferQuantity,
       transfer_scale transferScale,
       create_time createTime,
       creator,
       gl_no glNo,
       in_gl_no inGlNo,
       status
      from stock_task_process stp
               left join stock_task_process_detail stpd on stpd.stock_task_process_id = stp.id
      where stp.stock_task_id = #{stockTaskId} and stpd.sku=#{sku}
    </select>

    <select id="selectWarehouseByPurchaseNo" resultType="net.summerfarm.model.domain.StockTaskProcessDetail">
        select stpd.sku,stpd.quantity
        from stock_task_process_detail stpd
        left join stock_task_process stp on stpd.stock_task_process_id = stp.id
        left join stock_task st on stp.stock_task_id = st.id
        where
        st.state in (1, 2)
        and stpd.status = 1
        and st.task_no = #{purchaseNo}
    </select>

    <select id="selectByProcessList" parameterType="net.summerfarm.model.input.StockTaskProcessDetailInput" resultType="net.summerfarm.model.vo.StockTaskProcessDetailVO">
        select  stpd.sku ,stpd.list_no listNo,stpd.stock_task_process_id stockTaskProcessId,stpd.add_time addTime,
        stpd.quantity,((pp.price/pp.quantity) * stpd.quantity) price,pp.supplier_id supplierId,stpd.id stockTaskProcessDetailId,st.type
        from (select id,type from stock_task where type in (11,56)) st
        left join stock_task_process stp on st.id = stp.stock_task_id
        inner join stock_task_process_detail stpd on stpd.stock_task_process_id = stp.id
        left join purchases_plan pp on stpd.list_no = pp.purchase_no and stpd.sku = pp.sku and pp.origin_id is null
        left join settlement_detail sd on pp.purchase_no = sd.purchase_no and pp.id = sd.purchase_plan_id
        left join settlement s on sd.settlement_id = s.id
        where
        (stpd.state <![CDATA[<>]]> 1 or stpd.state is null)
        and (sd.id is null or (sd.id is not null and s.status = 4))
        AND stpd.id IN
        <foreach collection="ids" item="it" open="(" separator="," close=")">
            #{it}
        </foreach>
    </select>

    <select id="selectByList" parameterType="net.summerfarm.model.input.StockTaskProcessDetailInput"
            resultType="net.summerfarm.module.pms.model.vo.AccountStockProcessVO">
        select stpd.sku,
               stpd.list_no                                         listNo,
               stpd.stock_task_process_id                           stockTaskProcessId,
               stpd.add_time                                        addTime,
               stpd.quantity,
               round(((pp.price / pp.quantity) * stpd.quantity), 2) price,
               pp.supplier_id                                       supplierId,
               stpd.id                                              stockTaskProcessDetailId,
               st.type
        from (select id,type,area_no from stock_task where type in (11,21,56)) st
        left join stock_task_process stp on st.id = stp.stock_task_id
        inner join stock_task_process_detail stpd on stpd.stock_task_process_id = stp.id
        left join purchases_plan pp on stpd.list_no = pp.purchase_no and stpd.sku = pp.sku and pp.origin_id is null and pp.plan_status=1
        left join purchases p on p.purchase_no = pp.purchase_no
        where
        pp.supplier_id = #{supplierId}
        and (stpd.state <![CDATA[<>]]> 1 or stpd.state is null)
        and NOT EXISTS ( select 1 FROM settlement se LEFT JOIN `settlement_detail` d on d.`settlement_id` = se.`id`
        where se.status!=4 and d.`purchase_no` = pp.purchase_no and se.supplier_id=pp.supplier_id)
        <if test="purchasesCreator != null and purchasesCreator != ''">
            and p.purchaser = #{purchasesCreator}
        </if>
        <if test=" startTime != null and endTime != null">
            and stpd.add_time <![CDATA[>=]]> #{startTime} and stpd.add_time <![CDATA[<]]> #{endTime}
        </if>
        <if test="skipStoreOnly == 1">
            and st.type in (21)
        </if>
        <if test="skipStoreOnly == 0">
            and st.type in (11,56)
        </if>
        <if test="skipStoreOnly == null">
            and st.type in (11,56,21)
        </if>
        <if test=" stockTaskProcessDetailId != null">
            and stpd.id = #{stockTaskProcessDetailId}
        </if>
        <if test="pdName != null">
            and pp.title like concat(#{pdName}, '%')
        </if>
        <if test=" sku != null">
            and stpd.sku = #{sku}
        </if>
        <if test=" type != null">
            and st.type = #{type}
        </if>
        <if test=" purchaseNo != null">
            and stpd.list_no = #{purchaseNo}
        </if>
        <if test=" receiptNo != null">
            and stp.id = #{receiptNo} and st.type in (11, 21)
        </if>
        <if test=" returnOrderNo != null">
            and stp.id = #{returnOrderNo} and st.type = 56
        </if>
        <if test="warehouseNoList != null">
            AND st.area_no IN
            <foreach collection="warehouseNoList" item="item" open="(" close=")" separator="," index="index">
                #{item}
            </foreach>
        </if>
        ORDER BY stpd.add_time desc
    </select>

    <select id="selectBySkuAndPurchaseNo" resultType="net.summerfarm.model.domain.StockTaskProcessDetail">
        SELECT s.id,s.stock_task_process_id stockTaskProcessId,s.item_id itemId,sku,s.list_no listNo,s.quality_date qualityDate,s.quantity,s.add_time addTime
        FROM stock_task_process_detail s
                 LEFT JOIN stock_task_process stp on stp.id = s.stock_task_process_id
                 LEFT JOIN stock_task st on st.id = stp.stock_task_id
        where list_no = #{listNo} and sku = #{sku} and st.type in (11,56) and (s.state is null or s.state = 0)
    </select>

    <select id="selectBySkuAndStockStorageItemIdAndListNoAndQDate" resultType="net.summerfarm.model.domain.StockTaskProcessDetail">
        SELECT remark
        FROM stock_task_process_detail s
        where s.item_id = #{id} and s.sku = #{sku} and s.list_no = #{listNo} and s.quality_date = #{qualityDate}
        order by create_time desc
        limit 1
    </select>

    <update id="updateByState" >
        update stock_task_process_detail
        set `state` = 1
        where list_no = #{purchaseNo} and sku = #{sku}
    </update>

</mapper>