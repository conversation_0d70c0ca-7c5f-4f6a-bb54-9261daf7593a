<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.MerchantCardRecordMapper">

    <select id="selectVO" parameterType="net.summerfarm.model.domain.MerchantCardRecord"
            resultType="net.summerfarm.model.vo.MerchantCardRecordVO">
        SELECT mcr.id,mcr.merchant_card_id merchantCardId,mcr.order_no orderNo,mcr.add_time addTime,o.total_price totalPrice,o.order_time orderTime,a.delivery_fee deliveryFee
        FROM merchant_card_record mcr
        INNER JOIN orders o ON mcr.order_no=o.order_no
        INNER JOIN area a ON o.area_no=a.area_no
        <where>
            <if test="merchantCardId != null">
                AND mcr.merchant_card_id = #{merchantCardId}
            </if>
        </where>
    </select>
</mapper>