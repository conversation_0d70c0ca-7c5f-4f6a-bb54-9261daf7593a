<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CarrierQuotationAreaMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CarrierQuotationArea">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="carrier_quotation_id" jdbcType="BIGINT" property="carrierQuotationId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="county" jdbcType="VARCHAR" property="county" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, carrier_quotation_id, province, city, area, county
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from carrier_quotation_area
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByCondition" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from carrier_quotation_area
      where carrier_quotation_id = #{carrierQuotationId}
    </select>
    <select id="selectByCarrierQuotation" resultType="java.lang.String">
        select area from carrier_quotation_area cqa
        left join carrier_quotation cq on cqa.carrier_quotation_id = cq.id
        where cqa.province = #{province} and cqa.city = #{city} and cq.delete_flag = 0
        <if test="area!=null and area!=''">
            and area = #{area}
        </if>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from carrier_quotation_area
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByCarrierQuotationId">
    delete from carrier_quotation_area
    where carrier_quotation_id = #{id}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CarrierQuotationArea" useGeneratedKeys="true">
    insert into carrier_quotation_area (create_time, update_time, carrier_quotation_id, 
      province, city, area, 
      county)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{carrierQuotationId,jdbcType=BIGINT}, 
      #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, 
      #{county,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CarrierQuotationArea" useGeneratedKeys="true">
    insert into carrier_quotation_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="carrierQuotationId != null">
        carrier_quotation_id,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="county != null">
        county,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="carrierQuotationId != null">
        #{carrierQuotationId,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        #{county,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CarrierQuotationArea">
    update carrier_quotation_area
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="carrierQuotationId != null">
        carrier_quotation_id = #{carrierQuotationId,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        county = #{county,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CarrierQuotationArea">
    update carrier_quotation_area
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      carrier_quotation_id = #{carrierQuotationId,jdbcType=BIGINT},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      county = #{county,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>