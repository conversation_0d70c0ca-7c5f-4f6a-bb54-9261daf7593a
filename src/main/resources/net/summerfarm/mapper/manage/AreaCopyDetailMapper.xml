<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.AreaCopyDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.AreaCopyDetail">
    <!--@mbg.generated-->
    <!--@Table area_copy_detail-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="copy_id" jdbcType="BIGINT" property="copyId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="original_price" jdbcType="DECIMAL" property="originalPrice" />
    <result column="new_price" jdbcType="DECIMAL" property="newPrice" />
    <result column="origin_interest_rate" jdbcType="DECIMAL" property="originInterestRate" />
    <result column="new_interest_rate" jdbcType="DECIMAL" property="newInterestRate" />
    <result column="auto_flag" jdbcType="TINYINT" property="autoFlag" />
    <result column="on_sale" jdbcType="TINYINT" property="onSale" />
    <result column="home_show" jdbcType="INTEGER" property="homeShow" />
    <result column="close_sale" jdbcType="INTEGER" property="closeSale" />
    <result column="close_sale_time" jdbcType="TIMESTAMP" property="closeSaleTime" />
    <result column="open_sale" jdbcType="INTEGER" property="openSale" />
    <result column="open_sale_time" jdbcType="TIMESTAMP" property="openSaleTime" />
    <result column="sales_mode" jdbcType="INTEGER" property="salesMode" />
    <result column="corner_status" jdbcType="INTEGER" property="cornerStatus" />
    <result column="corner_open_time" jdbcType="TIMESTAMP" property="cornerOpenTime" />
    <result column="m_type" jdbcType="INTEGER" property="mType" />
    <result column="interest_rate_fluctuation" jdbcType="DECIMAL" property="interestRateFluctuation" />
    <result column="price_fluctuation" jdbcType="DECIMAL" property="priceFluctuation" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, copy_id, sku, original_price, new_price, origin_interest_rate, new_interest_rate, 
    auto_flag, on_sale, home_show, close_sale, close_sale_time, open_sale, open_sale_time, 
    sales_mode, corner_status, corner_open_time, m_type, interest_rate_fluctuation, price_fluctuation, 
    create_time, update_time, `status`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from area_copy_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from area_copy_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.AreaCopyDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into area_copy_detail (copy_id, sku, original_price, 
      new_price, origin_interest_rate, new_interest_rate, 
      auto_flag, on_sale, home_show, 
      close_sale, close_sale_time, open_sale, 
      open_sale_time, sales_mode, corner_status, 
      corner_open_time, m_type, interest_rate_fluctuation, 
      price_fluctuation, create_time, update_time, 
      `status`,limited_quantity)
    values (#{copyId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{originalPrice,jdbcType=DECIMAL}, 
      #{newPrice,jdbcType=DECIMAL}, #{originInterestRate,jdbcType=DECIMAL}, #{newInterestRate,jdbcType=DECIMAL}, 
      #{autoFlag,jdbcType=TINYINT}, #{onSale,jdbcType=TINYINT}, #{homeShow,jdbcType=INTEGER}, 
      #{closeSale,jdbcType=INTEGER}, #{closeSaleTime,jdbcType=TIMESTAMP}, #{openSale,jdbcType=INTEGER}, 
      #{openSaleTime,jdbcType=TIMESTAMP}, #{salesMode,jdbcType=INTEGER}, #{cornerStatus,jdbcType=INTEGER}, 
      #{cornerOpenTime,jdbcType=TIMESTAMP}, #{mType,jdbcType=INTEGER}, #{interestRateFluctuation,jdbcType=DECIMAL}, 
      #{priceFluctuation,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=INTEGER},#{limitedQuantity,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.AreaCopyDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into area_copy_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="copyId != null">
        copy_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="originalPrice != null">
        original_price,
      </if>
      <if test="newPrice != null">
        new_price,
      </if>
      <if test="originInterestRate != null">
        origin_interest_rate,
      </if>
      <if test="newInterestRate != null">
        new_interest_rate,
      </if>
      <if test="autoFlag != null">
        auto_flag,
      </if>
      <if test="onSale != null">
        on_sale,
      </if>
      <if test="homeShow != null">
        home_show,
      </if>
      <if test="closeSale != null">
        close_sale,
      </if>
      <if test="closeSaleTime != null">
        close_sale_time,
      </if>
      <if test="openSale != null">
        open_sale,
      </if>
      <if test="openSaleTime != null">
        open_sale_time,
      </if>
      <if test="salesMode != null">
        sales_mode,
      </if>
      <if test="cornerStatus != null">
        corner_status,
      </if>
      <if test="cornerOpenTime != null">
        corner_open_time,
      </if>
      <if test="mType != null">
        m_type,
      </if>
      <if test="interestRateFluctuation != null">
        interest_rate_fluctuation,
      </if>
      <if test="priceFluctuation != null">
        price_fluctuation,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="limitedQuantity != null">
        limited_quantity,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="copyId != null">
        #{copyId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="originalPrice != null">
        #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="newPrice != null">
        #{newPrice,jdbcType=DECIMAL},
      </if>
      <if test="originInterestRate != null">
        #{originInterestRate,jdbcType=DECIMAL},
      </if>
      <if test="newInterestRate != null">
        #{newInterestRate,jdbcType=DECIMAL},
      </if>
      <if test="autoFlag != null">
        #{autoFlag,jdbcType=TINYINT},
      </if>
      <if test="onSale != null">
        #{onSale,jdbcType=TINYINT},
      </if>
      <if test="homeShow != null">
        #{homeShow,jdbcType=INTEGER},
      </if>
      <if test="closeSale != null">
        #{closeSale,jdbcType=INTEGER},
      </if>
      <if test="closeSaleTime != null">
        #{closeSaleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="openSale != null">
        #{openSale,jdbcType=INTEGER},
      </if>
      <if test="openSaleTime != null">
        #{openSaleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="salesMode != null">
        #{salesMode,jdbcType=INTEGER},
      </if>
      <if test="cornerStatus != null">
        #{cornerStatus,jdbcType=INTEGER},
      </if>
      <if test="cornerOpenTime != null">
        #{cornerOpenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mType != null">
        #{mType,jdbcType=INTEGER},
      </if>
      <if test="interestRateFluctuation != null">
        #{interestRateFluctuation,jdbcType=DECIMAL},
      </if>
      <if test="priceFluctuation != null">
        #{priceFluctuation,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="limitedQuantity != null">
        limited_quantity = #{limitedQuantity,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.AreaCopyDetail">
    <!--@mbg.generated-->
    update area_copy_detail
    <set>
      <if test="copyId != null">
        copy_id = #{copyId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="originalPrice != null">
        original_price = #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="newPrice != null">
        new_price = #{newPrice,jdbcType=DECIMAL},
      </if>
      <if test="originInterestRate != null">
        origin_interest_rate = #{originInterestRate,jdbcType=DECIMAL},
      </if>
      <if test="newInterestRate != null">
        new_interest_rate = #{newInterestRate,jdbcType=DECIMAL},
      </if>
      <if test="autoFlag != null">
        auto_flag = #{autoFlag,jdbcType=TINYINT},
      </if>
      <if test="onSale != null">
        on_sale = #{onSale,jdbcType=TINYINT},
      </if>
      <if test="homeShow != null">
        home_show = #{homeShow,jdbcType=INTEGER},
      </if>
      <if test="closeSale != null">
        close_sale = #{closeSale,jdbcType=INTEGER},
      </if>
      <if test="closeSaleTime != null">
        close_sale_time = #{closeSaleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="openSale != null">
        open_sale = #{openSale,jdbcType=INTEGER},
      </if>
      <if test="openSaleTime != null">
        open_sale_time = #{openSaleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="salesMode != null">
        sales_mode = #{salesMode,jdbcType=INTEGER},
      </if>
      <if test="cornerStatus != null">
        corner_status = #{cornerStatus,jdbcType=INTEGER},
      </if>
      <if test="cornerOpenTime != null">
        corner_open_time = #{cornerOpenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mType != null">
        m_type = #{mType,jdbcType=INTEGER},
      </if>
      <if test="interestRateFluctuation != null">
        interest_rate_fluctuation = #{interestRateFluctuation,jdbcType=DECIMAL},
      </if>
      <if test="priceFluctuation != null">
        price_fluctuation = #{priceFluctuation,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="limitedQuantity != null">
        limited_quantity = #{limitedQuantity,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.AreaCopyDetail">
    <!--@mbg.generated-->
    update area_copy_detail
    set copy_id = #{copyId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=VARCHAR},
      original_price = #{originalPrice,jdbcType=DECIMAL},
      new_price = #{newPrice,jdbcType=DECIMAL},
      origin_interest_rate = #{originInterestRate,jdbcType=DECIMAL},
      new_interest_rate = #{newInterestRate,jdbcType=DECIMAL},
      auto_flag = #{autoFlag,jdbcType=TINYINT},
      on_sale = #{onSale,jdbcType=TINYINT},
      home_show = #{homeShow,jdbcType=INTEGER},
      close_sale = #{closeSale,jdbcType=INTEGER},
      close_sale_time = #{closeSaleTime,jdbcType=TIMESTAMP},
      open_sale = #{openSale,jdbcType=INTEGER},
      open_sale_time = #{openSaleTime,jdbcType=TIMESTAMP},
      sales_mode = #{salesMode,jdbcType=INTEGER},
      corner_status = #{cornerStatus,jdbcType=INTEGER},
      corner_open_time = #{cornerOpenTime,jdbcType=TIMESTAMP},
      m_type = #{mType,jdbcType=INTEGER},
      interest_rate_fluctuation = #{interestRateFluctuation,jdbcType=DECIMAL},
      price_fluctuation = #{priceFluctuation,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      limited_quantity = #{limitedQuantity,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into area_copy_detail
    (copy_id, sku, original_price, new_price, origin_interest_rate, new_interest_rate, 
      auto_flag, on_sale, home_show, close_sale, close_sale_time, open_sale, open_sale_time, 
      sales_mode, corner_status, corner_open_time, m_type, interest_rate_fluctuation, 
      price_fluctuation, create_time, update_time, `status`,limited_quantity)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.copyId,jdbcType=BIGINT}, #{item.sku,jdbcType=VARCHAR}, #{item.originalPrice,jdbcType=DECIMAL}, 
        #{item.newPrice,jdbcType=DECIMAL}, #{item.originInterestRate,jdbcType=DECIMAL}, 
        #{item.newInterestRate,jdbcType=DECIMAL}, #{item.autoFlag,jdbcType=TINYINT}, #{item.onSale,jdbcType=TINYINT}, 
        #{item.homeShow,jdbcType=INTEGER}, #{item.closeSale,jdbcType=INTEGER}, #{item.closeSaleTime,jdbcType=TIMESTAMP}, 
        #{item.openSale,jdbcType=INTEGER}, #{item.openSaleTime,jdbcType=TIMESTAMP}, #{item.salesMode,jdbcType=INTEGER}, 
        #{item.cornerStatus,jdbcType=INTEGER}, #{item.cornerOpenTime,jdbcType=TIMESTAMP}, 
        #{item.mType,jdbcType=INTEGER}, #{item.interestRateFluctuation,jdbcType=DECIMAL}, 
        #{item.priceFluctuation,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER},#{item.limitedQuantity})
    </foreach>
  </insert>
  <update id="updateStatusByCopyId">
    update area_copy_detail
    set status = #{status}
    where copy_id = #{copyId}
  </update>

  <update id="updateEditInfoById">
    update area_copy_detail
    set new_price = #{detail.newPrice},
        new_interest_rate = #{detail.newInterestRate},
        auto_flag = #{detail.autoFlag},
        price_fluctuation = #{priceFluctuation},
        interest_rate_fluctuation = #{interestRateFluctuation}
    where id = #{id}
  </update>

  <resultMap id="CopyDetailListDTOResultMap" type="net.summerfarm.model.DTO.areacopy.CopyDetailListDTO">
    <result column="id" property="id"/>
    <result column="picture_path" property="picturePath"/>
    <result column="pd_name" property="pdName"/>
    <result column="weight" property="weight"/>
    <result column="sku" property="sku"/>
    <result column="new_price" property="newPrice"/>
    <result column="original_price" property="originalPrice"/>
    <result column="price_fluctuation" property="priceFluctuation"/>
    <result column="new_interest_rate" property="newInterestRate"/>
    <result column="origin_interest_rate" property="originInterestRate"/>
    <result column="interest_rate_fluctuation" property="interestRateFluctuation"/>
    <result column="auto_flag" property="autoFlag"/>
  </resultMap>
  <select id="selectDetailList" resultMap="CopyDetailListDTOResultMap">
    select acd.id,ifnull(i.sku_pic, p.picture_path) picture_path,p.pd_name,i.weight,i.sku,acd.new_price,acd.original_price,acd.price_fluctuation,
        acd.new_interest_rate,acd.origin_interest_rate,acd.interest_rate_fluctuation,acd.auto_flag
    from area_copy_detail acd
        left join inventory i on acd.sku = i.sku
        left join products p on i.pd_id = p.pd_id
    where copy_id = #{copyId} and (price_fluctuation > 0 or interest_rate_fluctuation > 0)
    <choose>
      <when test="sortType == null or sortType == 0">
        order by price_fluctuation desc
      </when>
      <otherwise>
        order by interest_rate_fluctuation desc
      </otherwise>
    </choose>
  </select>

  <delete id="deleteByCopyId">
    delete
    from area_copy_detail
    where copy_id = #{copyId}
  </delete>

  <update id="updateStatusById">
    update area_copy_detail
    set status = #{status}
    where id = #{id}
  </update>

  <select id="countByCopyId" resultType="java.lang.Long">
    select count(*)
    from area_copy_detail
    where copy_id = #{copyId}
  </select>
</mapper>