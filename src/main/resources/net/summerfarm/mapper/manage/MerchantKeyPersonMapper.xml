<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MerchantKeyPersonMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantKeyPerson">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="role" jdbcType="TINYINT" property="role" />
    <result column="person_name" jdbcType="VARCHAR" property="personName" />
    <result column="phone" jdbcType="CHAR" property="phone" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="realname"  property="createName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, `role`, person_name, phone, create_id, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_key_person
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MerchantKeyPerson" useGeneratedKeys="true">
    insert into merchant_key_person
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="role != null">
        `role`,
      </if>
      <if test="personName != null">
        person_name,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="role != null">
        #{role,jdbcType=TINYINT},
      </if>
      <if test="personName != null">
        #{personName,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=CHAR},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.MerchantKeyPerson">
    update merchant_key_person
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="role != null">
        `role` = #{role,jdbcType=TINYINT},
      </if>
      <if test="personName != null">
        person_name = #{personName,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=CHAR},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectKeyPerson" resultMap="BaseResultMap">
    select mkp.id, mkp.m_id, mkp.`role`, mkp.person_name, mkp.phone, mkp.create_id, mkp.update_time, mkp.create_time, a.realname
    from merchant_key_person mkp
    LEFT JOIN admin a ON a.admin_id = mkp.create_id
    where m_id = #{mId}
  </select>
</mapper>