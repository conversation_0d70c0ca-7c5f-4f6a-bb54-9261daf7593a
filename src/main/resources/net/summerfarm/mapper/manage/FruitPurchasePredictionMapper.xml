<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="net.summerfarm.mapper.manage.FruitPurchasePredictionMapper">
    <insert id="insertBatch" parameterType="list">
        INSERT INTO fruit_purchase_prediction(prediction_date, sku, pd_name, weight, area_no, quantity,
        purchase_reference, sales_prediction, recent_seven_days_sales, recent_three_days_sales,
        work_day_sales_bm, work_day_sales_ss, purchaser,pre_sale_second_day,pre_sale_third_day,pre_sale_fourth_day,
        pre_sale_fifth_day,pre_sale_sixth_day,pre_sale_seventh_day,vip_avg_sales,single_avg_sales,sales_rate)
        VALUES
        <foreach collection="list" item="it" separator="," >
            (#{it.predictionDate}, #{it.sku}, #{it.pdName}, #{it.weight}, #{it.areaNo}, #{it.quantity},
            #{it.purchaseReference}, #{it.salesPrediction}, #{it.recentSevenDaysSales}, #{it.recentThreeDaysSales},
            #{it.workDaySalesBM}, #{it.workDaySalesSS}, #{it.purchaser},#{it.preSaleSecondDay},#{it.preSaleThirdDay},
            #{it.preSaleFourthDay},#{it.preSaleFifthDay},#{it.preSaleSixthDay},#{it.preSaleSeventhDay},#{it.vipAvgSales},
            #{it.singleAvgSales},#{it.salesRate})
        </foreach>
    </insert>

    <sql id="cloumns">
        sku, pd_name pdName, weight, area_no areaNo, quantity, purchase_reference purchaseReference, sales_prediction
        salesPrediction, recent_seven_days_sales recentSevenDaysSales, recent_three_days_sales recentThreeDaysSales,
        work_day_sales_bm workDaySalesBM, work_day_sales_ss workDaySalesSS, purchaser , pre_sale_second_day preSaleSecondDay,
        pre_sale_third_day preSaleThirdDay, pre_sale_fourth_day preSaleFourthDay , pre_sale_fifth_day preSaleFifthDay,
        pre_sale_sixth_day preSaleSixthDay, pre_sale_seventh_day preSaleSeventhDay , vip_avg_sales vipAvgSales,
        single_avg_sales singleAvgSales , sales_rate salesRate
    </sql>

    <select id="selectiveQuery" parameterType="net.summerfarm.model.input.FruitPurchasePredictionReq"
            resultType="net.summerfarm.model.domain.FruitPurchasePredictionDO">
        SELECT <include refid="cloumns"/>
        FROM fruit_purchase_prediction
        WHERE prediction_date = CURRENT_DATE
        AND area_no = #{storeNo}
        <if test="sku != null and sku != ''">
            AND sku = #{sku}
        </if>
        <if test="onSaleSkus != null and onSaleSkus.size() > 0">
            AND sku IN
            <foreach collection="onSaleSkus"  item="it" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        <if test="pdName != null and pdName !=   ''">
            AND pd_name LIKE concat('%', #{pdName}, '%')
        </if>
        <if test="purchaser != null and purchaser != ''">
            AND purchaser = #{purchaser}
        </if>
        ORDER BY id ASC
    </select>

    <select id="countTodayData" resultType="int">
        SELECT count(*)
        FROM fruit_purchase_prediction
        WHERE prediction_date = CURRENT_DATE
    </select>

</mapper>