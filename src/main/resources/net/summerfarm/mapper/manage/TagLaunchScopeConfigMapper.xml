<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.TagLaunchScopeConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.market.TagLaunchScopeConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="info_id" jdbcType="BIGINT" property="infoId" />
    <result column="scope_id" jdbcType="BIGINT" property="scopeId" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `info_id`, `scope_id`, `updater_id`, `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tag_launch_scope_config
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tag_launch_scope_config
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.market.TagLaunchScopeConfig">
    insert into tag_launch_scope_config (`id`, `info_id`, `scope_id`, 
      `updater_id`, `create_time`, `update_time`
      )
    values (#{id,jdbcType=BIGINT}, #{infoId,jdbcType=BIGINT}, #{scopeId,jdbcType=BIGINT}, 
      #{updaterId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.market.TagLaunchScopeConfig">
    insert into tag_launch_scope_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="infoId != null">
        `info_id`,
      </if>
      <if test="scopeId != null">
        `scope_id`,
      </if>
      <if test="updaterId != null">
        `updater_id`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="infoId != null">
        #{infoId,jdbcType=BIGINT},
      </if>
      <if test="scopeId != null">
        #{scopeId,jdbcType=BIGINT},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.market.TagLaunchScopeConfig">
    update tag_launch_scope_config
    <set>
      <if test="infoId != null">
        `info_id` = #{infoId,jdbcType=BIGINT},
      </if>
      <if test="scopeId != null">
        `scope_id` = #{scopeId,jdbcType=BIGINT},
      </if>
      <if test="updaterId != null">
        `updater_id` = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.market.TagLaunchScopeConfig">
    update tag_launch_scope_config
    set `info_id` = #{infoId,jdbcType=BIGINT},
      `scope_id` = #{scopeId,jdbcType=BIGINT},
      `updater_id` = #{updaterId,jdbcType=INTEGER},
      `create_time` = #{createTime,jdbcType=TIMESTAMP},
      `update_time` = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertBatch" parameterType="net.summerfarm.model.domain.market.TagLaunchScopeConfig">
    insert into tag_launch_scope_config (`info_id`, `scope_id`,`updater_id`)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.infoId,jdbcType=BIGINT}, #{item.scopeId,jdbcType=BIGINT},
       #{item.updaterId,jdbcType=INTEGER})
    </foreach>
  </insert>

  <delete id="deleteByInfoId" parameterType="java.lang.Long">
    delete from tag_launch_scope_config
    where `info_id` = #{infoId,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByScopeId" parameterType="java.lang.Long">
    delete from tag_launch_scope_config
    where `info_id` = #{infoId} and `scope_id` = #{scopeId}
  </delete>

  <select id="listByInfoId" parameterType="java.lang.Long" resultType="long">
    select scope_id from tag_launch_scope_config
    where `info_id` = #{infoId}
  </select>
</mapper>