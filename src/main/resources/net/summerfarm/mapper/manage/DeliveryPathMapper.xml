<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.DeliveryPathMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DeliveryPath">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="store_no" property="storeNo" jdbcType="INTEGER"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="contact_id" property="contactId" jdbcType="BIGINT"/>
        <result column="time_frame" property="timeFrame" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="total_volume" property="totalVolume" jdbcType="DECIMAL"/>
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="addtime" property="addtime"/>
        <result column="type" property="type"/>
        <result column="path_status" property="pathStatus"/>
        <result column="finish_poi" property="finishPoi"/>
        <result column="finish_poi_name" property="finishPoiName"/>
        <result column="finish_distance" property="finishDistance"/>
        <result column="delivery_pic" property="deliveryPic"/>
        <result column="finish_time" property="finishTime"/>
        <result column="delivery_type" property="deliveryType"/>
        <result column="intercept_type" property="interceptType"/>
        <result column="brand_type" property="brandType"/>
    </resultMap>

    <sql id="BaseColumn">
        id,store_no,delivery_time,contact_id,time_frame,remark,total_volume,path,sort,addtime,`type`,path_status,finish_poi,finish_poi_name,finish_distance,
        delivery_pic,finish_time,delivery_type,intercept_type,brand_type
    </sql>

    <select id="selectVOList" parameterType="net.summerfarm.model.domain.DeliveryPath" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        SELECT m.mname,dp.contact_id contactId,CONCAT(c.province,c.city,c.area,c.address,ifnull(c.house_number,'')) address,c.contact,c.phone,c.distance,c.poi_note poiNote,
        dp.time_frame timeFrame,dp.remark,dp.id,dp.path,dp.sort
        FROM delivery_path dp
        INNER JOIN contact c ON dp.contact_id=c.contact_id
        INNER JOIN merchant m ON c.m_id=m.m_id
        <where>
            <if test="storeNo != null">
                AND dp.store_no = #{storeNo}
            </if>
            <if test="path != null">
                AND dp.path = #{path}
            </if>
            <if test="deliveryTime != null">
                AND dp.delivery_time = #{deliveryTime}
            </if>
        </where>
    </select>

    <select id="pathList" parameterType="net.summerfarm.model.domain.DeliveryPath" resultType="java.lang.String">
        SELECT path
        FROM delivery_path
        <where>
            <if test="storeNo != null">
                AND store_no = #{storeNo}
            </if>
            <if test="deliveryTime != null">
                AND delivery_time = #{deliveryTime}
            </if>
        </where>
        GROUP BY path
    </select>

    <insert id="insertBatch" parameterType="net.summerfarm.model.domain.DeliveryPath" useGeneratedKeys="true" keyProperty="id">
        INSERT into delivery_path(store_no,delivery_time,contact_id,time_frame,remark,total_volume,path,sort,addtime,total_price,`type`,delivery_type,brand_type)
        VALUES
        <foreach collection="list" item="item"  separator=",">
            (#{item.storeNo},#{item.deliveryTime},#{item.contactId},#{item.timeFrame},#{item.remark},#{item.totalVolume},#{item.path},#{item.sort},#{item.addtime},#{item.totalPrice},#{item.type},#{item.deliveryType},#{item.brandType})
        </foreach>
    </insert>

    <select id="select" parameterType="net.summerfarm.model.vo.DeliveryPathVO" resultType="net.summerfarm.model.vo.DeliveryPathVO">
      SELECT dp.id,dp.store_no storeNo,dp.delivery_time deliveryTime,dp.contact_id contactId,dp.time_frame timeFrame,dp.remark,dp.total_volume totalVolume,dp.path,dp.sort,
        dp.addtime,c.distance,c.poi_note poiNote,m.mname,CONCAT(c.province,c.city,c.area,c.address,ifnull(c.house_number,'')) address,c.contact,c.phone,dp.total_price totalPrice,dp.path_status pathStatus,
        dp.finish_time finishTime,dp.delivery_type deliveryType,dp.intercept_type as interceptType,dp.brand_type brandType
      FROM delivery_path dp
      INNER JOIN contact c ON dp.contact_id = c.contact_id
      INNER JOIN merchant m ON c.m_id = m.m_id
      <where>
          <if test="storeNo != null ">
              AND dp.store_no = #{storeNo}
          </if>
          <if test="deliveryTime != null">
              AND dp.delivery_time = #{deliveryTime}
          </if>
          <if test="mname != null">
              AND m.mname LIKE concat('%',#{mname} ,'%')
          </if>
          <if test="phone != null">
              AND c.phone = #{phone}
          </if>
          <if test="address != null">
              AND CONCAT(c.province,c.city,c.area,c.address) LIKE concat('%',#{address} ,'%')
          </if>
          <if test="pathStatus != null and pathStatus != 3 and pathStatus != 4">
              AND dp.path_status = #{pathStatus}
          </if>
          <if test="pathStatus != null and pathStatus == 3">
              AND dp.path_status != 2
          </if>
          <if test="pathStatus != null and pathStatus == 4">
              AND dp.delivery_type = 0
              AND dp.intercept_type = 2
          </if>
          <if test="path != null ">
              AND dp.path = #{path}
          </if>
          <if test=" deliveryType != null and deliveryType == 0 ">
              AND dp.delivery_type = #{deliveryType}
          </if>
          <if test=" deliveryType != null and deliveryType != 0 ">
              AND dp.delivery_type != 0
          </if>
          <if test=" brandType != null ">
              AND
              (dp.brand_type is null
              or brand_type= 0)
          </if>
          <if test=" interceptType != null ">
              AND dp.intercept_type = #{interceptType}
          </if>
      </where>
    </select>

    <select id="selectSass" parameterType="net.summerfarm.model.vo.DeliveryPathVO" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        SELECT
        0 type,
        0 interceptType,
        dp.id,
        dp.store_no storeNo,
        dp.delivery_time deliveryTime,
        dp.contact_id contactId,
        dp.time_frame timeFrame,
        dp.remark,
        dp.total_volume totalVolume,
        dp.path,
        dp.sort,
        dp.addtime,
        c.distance,
        c.poi poiNote,
        CONCAT(
        c.province,
        c.city,
        ifnull(c.area,''),
        c.address) address,
        c.name as contact,
        c.phone,
        dp.total_price totalPrice,
        dp.path_status pathStatus,
        dp.finish_time finishTime,
        dp.delivery_type deliveryType,
        dp.brand_type brandType,
        c.mname
        FROM
        delivery_path dp
        INNER JOIN outside_contact c ON dp.contact_id = c.id
        <where>
          <if test="storeNo != null ">
              AND dp.store_no = #{storeNo}
          </if>
          <if test="deliveryTime != null">
              AND dp.delivery_time = #{deliveryTime}
          </if>
          <if test="mname != null">
              AND c.mname LIKE concat('%',#{mname} ,'%')
          </if>
          <if test="phone != null">
              AND c.phone = #{phone}
          </if>
          <if test="address != null">
              AND CONCAT(c.province,c.city,c.area,c.address) LIKE concat('%',#{address} ,'%')
          </if>
          <if test="pathStatus != null and pathStatus != 3">
              AND dp.path_status = #{pathStatus}
          </if>
          <if test="pathStatus != null and pathStatus == 3">
              AND dp.path_status != 2
          </if>
          <if test="path != null ">
              AND dp.path = #{path}
          </if>
          <if test=" deliveryType != null and deliveryType == 0 ">
              AND dp.delivery_type = #{deliveryType}
          </if>
          <if test=" deliveryType != null and deliveryType != 0 ">
              AND dp.delivery_type != 0
          </if>
          <if test=" brandType != null ">
              AND dp.brand_type = #{brandType}
          </if>
          <if test=" interceptType != null">
              AND dp.intercept_type = #{interceptType}
          </if>
      </where>
    </select>

    <select id="selectVO" parameterType="net.summerfarm.model.domain.DeliveryPath" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        SELECT m.mname,dp.contact_id contactId,CONCAT(c.province,c.city,c.area,c.address,ifnull(c.house_number,'')) address,c.contact,c.phone,c.distance,c.poi_note poiNote,
        dp.time_frame timeFrame,dp.remark,dp.id,dp.path,dp.sort,dp.total_volume totalVolume
        FROM delivery_path dp
        INNER JOIN contact c ON dp.contact_id=c.contact_id
        INNER JOIN merchant m ON c.m_id=m.m_id
        <where>
            <if test="storeNo != null">
                AND dp.store_no = #{storeNo}
            </if>
            <choose>
                <when test="path != null">
                    AND dp.path = #{path}
                </when>
                <otherwise>
                    AND dp.path IS NULL
                </otherwise>
            </choose>
            <if test="deliveryTime != null">
                AND dp.delivery_time = #{deliveryTime}
            </if>
        </where>
    </select>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DeliveryPath">
        UPDATE delivery_path
        <set>
            <if test="sort != null">
                sort = #{sort} ,
            </if>
            <if test="path != null">
                path = #{path} ,
            </if>
            <if test="interceptType != null">
                intercept_type = #{interceptType} ,
            </if>
            <if test="deliveryType != null">
                delivery_type = #{deliveryType} ,
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="update" parameterType="net.summerfarm.model.domain.DeliveryPath">
        UPDATE delivery_path
        SET
        sort = #{sort} ,
        path = #{path}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM delivery_path
        WHERE store_no = #{storeNo}
        AND delivery_time = #{deliveryTime}
        AND path = #{path}
    </select>

    <update id="updateSortAdd">
        UPDATE delivery_path
        SET sort = sort + 1
        WHERE path = #{path}
        AND delivery_time = #{deliveryTime}
        AND store_no = #{storeNo}
        AND sort <![CDATA[>=]]> #{sort}
    </update>

    <update id="updateSortSub">
        UPDATE delivery_path
        SET sort = sort - 1
        WHERE path = #{path}
        AND delivery_time = #{deliveryTime}
        AND store_no = #{storeNo}
        AND sort <![CDATA[>=]]> #{sort}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM delivery_path
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getListSelectByPrimaryKey" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM delivery_path
        <where>
            <if test="ids != null">
                and id IN
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                     #{id}
                </foreach>
            </if>
        </where>

    </select>

    <select id="selectOne" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM delivery_path
        WHERE contact_id = #{contactId}
        <if test="storeNo != null">
            AND store_no = #{storeNo}
        </if>
        AND delivery_time = #{deliveryTime}
        and (brand_type is null
        or brand_type= 0)
        limit 1
    </select>

    <delete id="delete">
        DELETE
        FROM delivery_path
        WHERE store_no = #{storeNo}
        AND delivery_time = #{deliveryTime}
    </delete>

    <select id="selectById" parameterType="java.lang.Integer" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        SELECT
            dp.id,
            dp.store_no storeNo,
            dp.delivery_time deliveryTime,
            c.contact_id contactId,
            dp.time_frame timeFrame,
            dp.remark ,dp.path,dp.sort,dp.addtime,
            dp.path_status pathStatus,
            dp.finish_poi finishPoi,
            dp.finish_poi_name finishPoiName ,
            dp.finish_distance finishDistance,
            dp.delivery_pic deliveryPic,
            CONCAT(c.province,c.city,c.area,c.address) address,`type`,
             dp.sign_for_status signForStatus,dp.sign_for_remarks signForRemarks,
             dp.delivery_type  deliveryType,
               c.m_id mId,
               dp.brand_type brandType,
            dp.intercept_type as interceptType,
               dp.out_distance  outDistance,
               dp.out_remark  outRemark
        FROM delivery_path dp
        INNER JOIN contact c ON dp.contact_id = c.contact_id
        WHERE dp.id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectSaaSById" parameterType="java.lang.Integer" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        SELECT
            dp.id,
            dp.store_no storeNo,
            dp.delivery_time deliveryTime,
            c.id contactId,
            dp.time_frame timeFrame,
            dp.remark ,dp.path,dp.sort,dp.addtime,
            dp.path_status pathStatus,
            dp.finish_poi finishPoi,
            dp.finish_poi_name finishPoiName ,
            dp.finish_distance finishDistance,
            dp.delivery_pic deliveryPic,
            CONCAT(c.province,c.city,ifnull(c.area,''),c.address) address,`type`,
             dp.sign_for_status signForStatus,dp.sign_for_remarks signForRemarks,
             dp.delivery_type  deliveryType,
               dp.brand_type brandType,
               c.mname
        FROM delivery_path dp
        INNER JOIN outside_contact c ON dp.contact_id = c.id
        WHERE dp.id = #{id,jdbcType=INTEGER}
    </select>

    <update id="updateStatus" parameterType="net.summerfarm.model.domain.DeliveryPath">
        UPDATE delivery_path
        SET
        path_status = #{pathStatus}

        WHERE delivery_time = #{deliveryTime} and store_no =#{storeNo} and path is not null
    </update>

    <update id="updateStatusByPath" parameterType="net.summerfarm.model.domain.DeliveryPath">
        UPDATE delivery_path
        SET
        path_status = #{pathStatus}
        WHERE delivery_time = #{deliveryTime} and store_no =#{storeNo} and path =#{path}
    </update>

    <select id="selectByStoreNo" parameterType="net.summerfarm.model.domain.DeliveryPath" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        SELECT m.mname,dp.contact_id contactId,CONCAT(c.province,c.city,c.area,c.address,ifnull(c.house_number,'')) address,c.contact,c.phone,c.distance,c.poi_note poiNote,dp.store_no storeNo,dp.delivery_time deliveryTime,
        dp.time_frame timeFrame,dp.remark,dp.id,dp.path,dp.sort,dp.total_volume totalVolume,dp.path_status pathStatus
        FROM delivery_path dp
        INNER JOIN contact c ON dp.contact_id=c.contact_id
        INNER JOIN merchant m ON c.m_id=m.m_id
        where dp.brand_type = 0 and dp.store_no = #{storeNo} AND dp.delivery_time = #{deliveryTime}

    </select>

    <select id="selectDeliveryByStoreNo" parameterType="net.summerfarm.model.domain.DeliveryPath" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        SELECT m.mname,dp.contact_id contactId,CONCAT(c.province,c.city,c.area,c.address,ifnull(c.house_number,'')) address,c.contact,c.phone,c.distance,c.poi_note poiNote,dp.store_no storeNo,dp.delivery_time deliveryTime,c.province,c.city,c.area,dp.brand_type brandType,
               dp.time_frame timeFrame,dp.remark,dp.id,dp.path,dp.sort,dp.total_volume totalVolume,dp.path_status pathStatus
        FROM delivery_path dp
                 INNER JOIN contact c ON dp.contact_id=c.contact_id
                 INNER JOIN merchant m ON c.m_id=m.m_id
        where dp.store_no = #{storeNo} AND dp.delivery_time = #{deliveryTime} AND dp.path is not null
    </select>

    <select id="selectByStoreNos" resultType="net.summerfarm.model.vo.DeliveryPathByStoreNoVO">
        select  CASE WHEN dcp.tms_car_id IS NULL THEN dc.id ELSE td.id END deliveryCarId,
                CASE WHEN dcp.tms_car_id IS NULL THEN dc.driver ELSE td.name END driver,
               dcp.path as path, sum(if(dp.path_status=2,1,0)) as deliveredCount ,
               dcp.tms_car_id as tmsCarId,
        sum(if(dp.path_status!=2,1,0)) as unDeliveredCount,
        count(dcp.id) as deliveryAmount, dp.store_no as storeNo,dp.delivery_time deliveryTime,
        sum(dp.out_distance ) as outDistanceCount
        from delivery_car_path dcp
        LEFT JOIN delivery_path dp on dp.delivery_time = dcp.delivery_time and dp.store_no = dcp.store_no and dp.path = dcp.path
        LEFT JOIN delivery_car dc on dc.id = dcp.delivery_car_id
        LEFT JOIN tms_driver td ON td.id = dcp.delivery_car_id
        <where>
            <if test="deliveryTime != null and deliveryEndTime != null">
                and dp.delivery_time <![CDATA[>=]]> #{deliveryTime}
                and dp.delivery_time <![CDATA[<=]]> #{deliveryEndTime}
            </if>
            <if test="deliveryTime != null and deliveryEndTime == null">
                and dp.delivery_time = #{deliveryTime}
            </if>
            <if test="storeNos != null">
                and dcp.store_no IN
                <foreach collection="storeNos" item="storeNo" index="index" open="(" close=")" separator=",">
                    #{storeNo}
                </foreach>
            </if>
        </where>
        group by dcp.store_no , dcp.id  order by  dp.store_no asc, unDeliveredCount desc
    </select>

    <select id="selectCompleteDelivery" resultType="net.summerfarm.model.vo.CompleteDeliveryMsgVO">
        SELECT cd.region as region,wlc.store_name as storeName,cd.city AS city,m.mname as mname,m.size as size,
        cd.complete_delivery_time as completeDeliveryTime,dcp.path as path,dc.phone as driverPhone,
        dc.driver as driver,
        cd.store_no as storeNo,
        m.area_no as areaNo,m.m_id as mId,
        m.admin_id as adminId,dep.finish_time as finishTime
        FROM  delivery_path dep
        LEFT JOIN contact con on dep.contact_id=con.contact_id
        INNER JOIN merchant m ON con.m_id = m.m_id
        LEFT JOIN delivery_car_path dcp on dep.delivery_time = dcp.delivery_time and dep.store_no = dcp.store_no and dep.path = dcp.path
        LEFT JOIN delivery_car dc on dc.id = dcp.delivery_car_id
        LEFT JOIN ad_code_msg acm ON acm.city = con.city AND acm.area = con.area
        INNER JOIN complete_delivery cd on cd.store_no = dcp.store_no
        AND cd.city = acm.city
        AND cd.STATUS = 0
        INNER JOIN warehouse_logistics_center wlc on con.store_no = wlc.store_no
        WHERE dep.delivery_time = #{deliveryTime} and dep.path_status in(1,0) and cd.store_no in
        <foreach collection="storeNos" item="storeNo" index="index" open="(" close=")" separator=",">
            #{storeNo}
        </foreach>
    </select>

    <select id="selectDown" resultType="net.summerfarm.model.vo.CompleteDeliveryMsgVO">
        SELECT cd.region as region,wlc.store_name as storeName,ar.area_name as areaName,m.mname as mname,m.size as size,
        cd.complete_delivery_time as completeDeliveryTime,dcp.path as path,
        CASE WHEN dcp.tms_car_id IS NULL THEN dc.phone ELSE td.phone END driverPhone,
        CASE WHEN dcp.tms_car_id IS NULL THEN dc.driver ELSE td.name END driver,
        cd.store_no as storeNo,
        m.area_no as areaNo,m.m_id as mId,
        m.admin_id as adminId,dep.finish_time as finishTime
        FROM  delivery_path dep
        LEFT JOIN contact con on dep.contact_id=con.contact_id
        INNER JOIN merchant m ON con.m_id = m.m_id
        LEFT JOIN delivery_car_path dcp on dep.delivery_time = dcp.delivery_time and dep.store_no = dcp.store_no and dep.path = dcp.path
        INNER JOIN area ar on m.area_no = ar.area_no
        LEFT JOIN delivery_car dc on dc.id = dcp.delivery_car_id
        LEFT JOIN tms_driver td on td.id = dcp.delivery_car_id
        INNER JOIN complete_delivery cd on cd.store_no = dcp.store_no and ar.area_no = cd.area_no and cd.status = 0
        INNER JOIN warehouse_logistics_center wlc on con.store_no = wlc.store_no
        WHERE dep.delivery_time = #{deliveryTime} and cd.store_no in
        <foreach collection="storeNos" item="storeNo" index="index" open="(" close=")" separator=",">
            #{storeNo}
        </foreach>
    </select>

    <select id="selectCompleteDeliveryDown" resultType="net.summerfarm.model.vo.CompleteDeliveryMsgVO">
        SELECT
        cd.region AS region,
        wlc.store_name AS storeName,
        cd.city AS city,
        m.mname AS mname,
        m.size AS size,
        cd.complete_delivery_time AS completeDeliveryTime,
        dcp.path AS path,
        CASE WHEN dcp.tms_car_id IS NULL THEN dc.phone ELSE td.phone END driverPhone,
        CASE WHEN dcp.tms_car_id IS NULL THEN dc.driver ELSE td.name END driver,
        cd.store_no AS storeNo,
        m.area_no AS areaNo,
        m.m_id AS mId,
        m.admin_id AS adminId,
        dep.finish_time AS finishTime,
        con.address AS contactAddress,
        cd.id
        FROM
        delivery_path dep
        LEFT JOIN contact con ON dep.contact_id = con.contact_id
        INNER JOIN merchant m ON con.m_id = m.m_id
        LEFT JOIN delivery_car_path dcp ON dep.delivery_time = dcp.delivery_time
        AND dep.store_no = dcp.store_no
        AND dep.path = dcp.path
        LEFT JOIN delivery_car dc ON dc.id = dcp.delivery_car_id
        LEFT JOIN tms_driver td on td.id = dcp.delivery_car_id
        LEFT JOIN (
        SELECT
        acm.city,
        acm.province,
        acm.area,
        t.`complete_delivery_time`,
        t.region,
        t.store_no,
        t.id
        FROM
        complete_delivery t
        LEFT JOIN complete_delivery_ad_code_mapping s ON t.id = s.complete_delivery_id
        LEFT JOIN ad_code_msg acm ON acm.ad_code = s.ad_code
        WHERE
        t.`status` = 0
        AND acm.`status` = 0
        ) cd ON cd.city = con.city
        AND cd.area = con.area
        INNER JOIN warehouse_logistics_center wlc ON con.store_no = wlc.store_no
        WHERE dep.intercept_type != 2 and dep.`brand_type` =0 and dep.delivery_time = #{deliveryTime} and dep.store_no in
        <foreach collection="storeNos" item="storeNo" index="index" open="(" close=")" separator=",">
            #{storeNo}
        </foreach>
    </select>

    <select id="selectSaasCompleteDeliveryDown" resultType="net.summerfarm.model.vo.CompleteDeliveryMsgVO">
        SELECT cd.region AS region,
        wlc.store_name AS storeName,
        cd.city AS city,
        con.mname AS mname,
        '单店' AS size,
        cd.complete_delivery_time AS completeDeliveryTime,
        dcp.path AS path,
        CASE WHEN dcp.tms_car_id IS NULL THEN dc.phone ELSE td.phone END driverPhone,
        CASE WHEN dcp.tms_car_id IS NULL THEN dc.driver ELSE td.name END driver,
        cd.store_no AS storeNo,
        dep.finish_time AS finishTime,
        con.address AS contactAddress,
        cd.id
        FROM delivery_path dep
        LEFT JOIN outside_contact con ON dep.contact_id= con.id
        LEFT JOIN delivery_car_path dcp ON dep.delivery_time= dcp.delivery_time
        AND dep.store_no= dcp.store_no
        AND dep.path= dcp.path
        LEFT JOIN delivery_car dc ON dc.id= dcp.delivery_car_id
        LEFT JOIN tms_driver td on td.id= dcp.delivery_car_id
        INNER JOIN(
        SELECT acm.city, acm.province, acm.area, t.`complete_delivery_time`, t.region, t.store_no, t.id
        FROM complete_delivery t
        LEFT JOIN complete_delivery_ad_code_mapping s ON t.id= s.complete_delivery_id
        LEFT JOIN ad_code_msg acm ON acm.ad_code= s.ad_code
        WHERE t.`status`= 0
        AND acm.`status`= 0) cd ON con.city=cd.city
        AND cd.area= con.area
        INNER JOIN warehouse_logistics_center wlc ON dep.store_no= wlc.store_no
        WHERE dep.intercept_type != 2 and dep.`brand_type` =1 and dep.delivery_time = #{deliveryTime} and dep.store_no in
        <foreach collection="storeNos" item="storeNo" index="index" open="(" close=")" separator=",">
            #{storeNo}
        </foreach>
    </select>

    <delete id="deleteDeliveryPath">
        DELETE
        FROM delivery_path
        WHERE id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectPreviousDeliveryDate"  resultType="java.time.LocalDate">
        select delivery_time from delivery_path
        where  store_no = #{storeNo} and path = #{path} and delivery_time <![CDATA[<]]> #{deliveryTime}
        order by delivery_time Desc
        limit 1
    </select>

    <select id="selectPathMsg" resultMap="BaseResultMap">
        select
        <include refid="BaseColumn"/>
        from delivery_path
        where  store_no = #{storeNo} and path = #{path} and delivery_time = #{deliveryTime}
        order by finish_time Desc
    </select>

    <select id="selectCompleteDeliveryByStore" resultType="net.summerfarm.model.vo.CompleteDeliveryMsgVO">
        SELECT count(1) as customerSum,ar.area_name as areaName,cd.store_no as storeNo,m.area_no as areaNo,wlc.store_name as storeName
        FROM  delivery_path dep
        LEFT JOIN contact con on dep.contact_id=con.contact_id
        INNER JOIN merchant m ON con.m_id = m.m_id
        LEFT JOIN delivery_car_path dcp on dep.delivery_time = dcp.delivery_time and dep.store_no = dcp.store_no and dep.path = dcp.path
        INNER JOIN area ar on m.area_no = ar.area_no
        LEFT JOIN delivery_car dc on dc.id = dcp.delivery_car_id
        INNER JOIN complete_delivery cd on cd.store_no = dcp.store_no and ar.area_no = cd.area_no and cd.status = 0
        INNER JOIN warehouse_logistics_center wlc on con.store_no = wlc.store_no
        WHERE dep.delivery_time = #{deliveryTime} and dep.path_status in(1,0) and cd.store_no = #{storeNo}
    </select>

    <select id="selectPath" resultType="net.summerfarm.model.vo.CompleteDeliveryMsgVO">
        SELECT dep.path,m.admin_id adminId
        FROM  delivery_path dep
        LEFT JOIN contact con on dep.contact_id=con.contact_id
        INNER JOIN merchant m ON con.m_id = m.m_id
        WHERE dep.delivery_time = #{deliveryTime} and dep.path_status in(1,0) and dep.store_no = #{storeNo} and dep.intercept_type != 2
    </select>
    <select id="selectListByCondition" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        select c.province,c.city,c.area,c.distance,c.poi_note poiNote,dp.finish_time finishTime from delivery_path dp
         left join contact c on dp.contact_id = c.contact_id
        where dp.brand_type = 0 and dp.delivery_time =#{deliveryTime} and dp.store_no = #{storeNo} and dp.path= #{path} and path_status = 2

    </select>
    <select id="selectSaasListByCondition" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        select c.province,c.city,c.area,c.distance,c.poi poiNote,dp.finish_time finishTime from delivery_path dp
         left join outside_contact c on dp.contact_id = c.id
        where dp.brand_type = 1 and dp.delivery_time =#{deliveryTime} and dp.store_no = #{storeNo} and dp.path= #{path} and path_status = 2

    </select>


    <select id="selectByStoreNoAndDeliveryTime" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            delivery_path
        WHERE
            store_no = #{storeNo}
          AND delivery_time= #{deliveryTime}
    </select>

    <select id="selectExchangeNum" resultType="integer">
        SELECT
            count(*)
        FROM
            delivery_path dp
                LEFT JOIN after_sale_delivery_path asdp ON dp.delivery_time = asdp.delivery_time
                AND dp.contact_id = asdp.concat_id
                LEFT JOIN after_sale_order aso ON aso.after_sale_order_no = asdp.after_sale_no
                LEFT JOIN after_sale_proof asf ON asf.after_sale_order_no = aso.after_sale_order_no
        WHERE
            dp.id = #{pathId}
          AND asf.handle_type in (4,6)
    </select>
    <select id="selectByOrderNo" resultType="net.summerfarm.model.domain.DeliveryPath">
        SELECT
       dt.id id,
       dt.store_no storeNo,
       dt.delivery_time deliveryTime,
       dt.contact_id contactId,
       dt.time_frame timeFrame,
       dt.remark remark,
       dt.total_volume totalVolume,
       dt.path path,
       dt.sort sort,
       dt.addtime addtime,
       dt.`type` type,
       dt.path_status pathStatus,
       dt.finish_poi finishPoi,
       dt.finish_poi_name finishPoiName,
       dt.finish_distance finishDistance,
       dt.delivery_pic deliveryPic,
       dt.finish_time finishTime
        FROM delivery_path dt
        INNER JOIN  delivery_plan dp ON dt.contact_id = dp.contact_id
        and dt.store_no = dp.order_store_no and dt.delivery_time = dp.delivery_time and dp.order_no = #{orderNo}
        WHERE brand_type is null
        or brand_type= 0
        limit 1
    </select>

    <select id="getStoreNoAndSendNum" resultType="net.summerfarm.model.vo.CompleteDeliveryMsgVO">
        SELECT
            count(*) as customerSum,
            t.store_no as storeNo,
            wlc.store_name as storeName
        FROM
            delivery_path t
        LEFT JOIN warehouse_logistics_center wlc on t.store_no = wlc.store_no
        WHERE
            t.delivery_time = #{deliveryTime}
          AND t.store_no = #{storeNo}
          AND t.path_status IN (1,0)
          AND t.intercept_type != 2
    </select>

    <select id="selectCompleteDeliveryNum" resultType="integer">
        SELECT
            count(*)
        FROM
            delivery_path t
        WHERE
            t.delivery_time = #{deliveryTime}
          AND t.store_no = #{storeNo}
          AND t.path_status = 2
    </select>

    <select id="getDeliveryPathVo" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        select
            c.province,
            c.city,
            c.area,
            c.distance,
            c.poi_note poiNote,
            dp.finish_time finishTime
        from delivery_path dp
                 left join contact c on dp.contact_id = c.contact_id
        where
            dp.delivery_time =#{deliveryTime}
          and dp.store_no = #{storeNo}
          and dp.path= #{path}
          and path_status = 2
          and dp.brand_type = 0
    </select>


    <select id="getSaasDeliveryPathVo" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        select
            c.province,
            c.city,
            c.area,
            c.distance,
            c.poi poiNote,
            dp.finish_time finishTime
        from delivery_path dp
                 left join outside_contact c on dp.contact_id = c.id
        where
            dp.delivery_time =#{deliveryTime}
          and dp.store_no = #{storeNo}
          and dp.path= #{path}
          and path_status = 2
          and dp.brand_type = 1
    </select>

    <select id="selectSaasByStoreNo" parameterType="net.summerfarm.model.domain.DeliveryPath" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        SELECT dp.contact_id contactId,CONCAT(c.province,c.city,c.area,c.address) address,c.name contact,c.phone,c.distance,c.poi poiNote,
               dp.time_frame timeFrame,dp.remark,dp.id,dp.path,dp.sort,dp.total_volume totalVolume
        FROM delivery_path dp
                 INNER JOIN outside_contact c ON dp.contact_id=c.id
        where dp.brand_type = 1 and dp.store_no = #{storeNo} AND dp.delivery_time = #{deliveryTime}
    </select>

    <select id="selectSaasByContact" parameterType="net.summerfarm.model.domain.DeliveryPath" resultType="net.summerfarm.model.vo.OrderAndSendVo">
        SELECT dp.path_status AS giveState,dp.id,dp.path,dp.sort,dp.total_volume totalVolume,
            CASE WHEN dcp.tms_car_id IS NULL THEN dc.phone ELSE td.phone END driverPhone,
            CASE WHEN dcp.tms_car_id IS NULL THEN dc.driver ELSE td.name END driverName
        FROM delivery_path dp
                Left JOIN outside_contact c ON dp.contact_id=c.id
                left join delivery_car_path dcp ON dcp.delivery_time = dp.delivery_time AND dcp.path = dp.path and dcp.store_no=dp.store_no
                LEFT JOIN delivery_car dc ON dcp.delivery_car_id = dc.id
                LEFT JOIN tms_driver td on td.id = dcp.delivery_car_id
        where dp.brand_type = 1 and dp.store_no = #{storeNo} AND dp.delivery_time = #{deliveryTime} and dp.contact_id = #{contactId}

    </select>

    <select id="selectDeliveryPath" resultMap="BaseResultMap">
        select <include refid="BaseColumn"/>
        from delivery_path
        <where>
            <if test="storeNo != null">
                AND store_no = #{storeNo}
            </if>
            <if test="deliveryTime != null">
                AND delivery_time = #{deliveryTime}
            </if>
            <if test="path != null">
                AND path = #{path}
            </if>
            <if test="pathStatus!= null">
                AND path_status = #{pathStatus}
            </if>
        </where>
    </select>
    <select id="selectDeliveryPathByStoreNo" resultType="net.summerfarm.model.vo.DeliveryPathVO">
        SELECT m.mname,dp.contact_id contactId,CONCAT(c.province,c.city,c.area,c.address,ifnull(c.house_number,'')) address,c.contact,c.phone,c.distance,c.poi_note poiNote,dp.store_no storeNo,dp.delivery_time deliveryTime,c.province,c.city,c.area,dp.brand_type brandType,
               dp.time_frame timeFrame,dp.remark,dp.id,dp.path,dp.sort,dp.total_volume totalVolume,dp.path_status pathStatus
        FROM delivery_path dp
                 INNER JOIN contact c ON dp.contact_id=c.contact_id
                 INNER JOIN merchant m ON c.m_id=m.m_id
        where dp.store_no = #{storeNo} AND dp.delivery_time = #{deliveryTime} AND dp.path=#{path}
        <if test="brandType!= null">
            AND dp.brand_type = #{brandType}
        </if>
    </select>
    <select id="selectMatePathByDistricts" resultType="string">
        SELECT distinct dp.path
        FROM delivery_path dp
                 INNER JOIN contact c ON dp.contact_id=c.contact_id
                 INNER JOIN merchant m ON c.m_id=m.m_id
        where dp.store_no = #{storeNo} AND dp.delivery_time = #{deliveryTime}
          and c.city=#{city}
        <if test="districts != null and districts.size != 0">
            and c.area in
            <foreach item = "district" collection="districts" separator="," open="(" close=")">
                #{district}
            </foreach>
        </if>
    </select>

    <select id="selectDeliveryPathMsg" resultType="net.summerfarm.model.domain.DeliveryPath">
          SELECT  dp.path
        FROM delivery_path dp
        where dp.store_no = #{storeNo} and dp.delivery_time =#{deliveryTime}
        <if test="list != null and list.size != 0">
            and dp.contact_id in
            <foreach item = "item" collection="list" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

    </select>
</mapper>