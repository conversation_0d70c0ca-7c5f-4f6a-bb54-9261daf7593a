<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PrepayInventoryMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PrepayInventory">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="prepay_price" jdbcType="DECIMAL" property="prepayPrice" />
    <result column="prepay_amount" jdbcType="INTEGER" property="prepayAmount" />
    <result column="used_amount" jdbcType="INTEGER" property="usedAmount" />
    <result column="usable_type" jdbcType="INTEGER" property="usableType" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="delivery_cycle" jdbcType="INTEGER" property="deliveryCycle" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="effect_time" jdbcType="TIMESTAMP" property="effectTime" />
    <result column="lose_effect_time" jdbcType="TIMESTAMP" property="loseEffectTime" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, admin_id, sku, prepay_price, prepay_amount, used_amount, usable_type, pay_type,
    delivery_cycle, `status`, effect_time, lose_effect_time, auditor, audit_time, updater, update_time,
    creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from prepay_inventory
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from prepay_inventory
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PrepayInventory" useGeneratedKeys="true">
    insert into prepay_inventory (admin_id, sku, prepay_price,
      prepay_amount, used_amount, usable_type,
      pay_type, delivery_cycle, `status`,
      effect_time, lose_effect_time, auditor, audit_time,
      updater, update_time, creator,
      create_time)
    values (#{adminId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{prepayPrice,jdbcType=DECIMAL},
      #{prepayAmount,jdbcType=INTEGER}, #{usedAmount,jdbcType=INTEGER}, #{usableType,jdbcType=INTEGER},
      #{payType,jdbcType=INTEGER}, #{deliveryCycle,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
      #{effectTime,jdbcType=TIMESTAMP}, #{loseEffectTime,jdbcType=TIMESTAMP}, #{auditor,jdbcType=VARCHAR}, #{auditTime,jdbcType=TIMESTAMP},
      #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PrepayInventory" useGeneratedKeys="true">
    insert into prepay_inventory
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="prepayPrice != null">
        prepay_price,
      </if>
      <if test="prepayAmount != null">
        prepay_amount,
      </if>
      <if test="usedAmount != null">
        used_amount,
      </if>
      <if test="usableType != null">
        usable_type,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="deliveryCycle != null">
        delivery_cycle,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="effectTime != null">
        effect_time,
      </if>
      <if test="loseEffectTime != null">
        lose_effect_time,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="prepayPrice != null">
        #{prepayPrice,jdbcType=DECIMAL},
      </if>
      <if test="prepayAmount != null">
        #{prepayAmount,jdbcType=INTEGER},
      </if>
      <if test="usedAmount != null">
        #{usedAmount,jdbcType=INTEGER},
      </if>
      <if test="usableType != null">
        #{usableType,jdbcType=INTEGER},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="deliveryCycle != null">
        #{deliveryCycle,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="effectTime != null">
        #{effectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loseEffectTime != null">
        #{loseEffectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PrepayInventory">
    update prepay_inventory
    <set>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="prepayPrice != null">
        prepay_price = #{prepayPrice,jdbcType=DECIMAL},
      </if>
      <if test="prepayAmount != null">
        prepay_amount = #{prepayAmount,jdbcType=INTEGER},
      </if>
      <if test="usedAmount != null">
        used_amount = #{usedAmount,jdbcType=INTEGER},
      </if>
      <if test="usableType != null">
        usable_type = #{usableType,jdbcType=INTEGER},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="deliveryCycle != null">
        delivery_cycle = #{deliveryCycle,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="effectTime != null">
        effect_time = #{effectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loseEffectTime != null">
        lose_effect_time = #{loseEffectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PrepayInventory">
    update prepay_inventory
    set admin_id = #{adminId,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      prepay_price = #{prepayPrice,jdbcType=DECIMAL},
      prepay_amount = #{prepayAmount,jdbcType=INTEGER},
      used_amount = #{usedAmount,jdbcType=INTEGER},
      usable_type = #{usableType,jdbcType=INTEGER},
      pay_type = #{payType,jdbcType=INTEGER},
      delivery_cycle = #{deliveryCycle,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      effect_time = #{effectTime,jdbcType=TIMESTAMP},
      lose_effect_time = #{loseEffectTime,jdbcType=TIMESTAMP},
      auditor = #{auditor,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByIdWithDeliveryCycle" parameterType="net.summerfarm.model.domain.PrepayInventory">
    update prepay_inventory
    <set>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="prepayPrice != null">
        prepay_price = #{prepayPrice,jdbcType=DECIMAL},
      </if>
      <if test="prepayAmount != null">
        prepay_amount = #{prepayAmount,jdbcType=INTEGER},
      </if>
      <if test="usedAmount != null">
        used_amount = #{usedAmount,jdbcType=INTEGER},
      </if>
      <if test="usableType != null">
        usable_type = #{usableType,jdbcType=INTEGER},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      delivery_cycle = #{deliveryCycle},
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="effectTime != null">
        effect_time = #{effectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loseEffectTime != null">
        lose_effect_time = #{loseEffectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByAdminId" resultType="net.summerfarm.model.vo.PrepayInventoryVO">
    select id,
       pi.admin_id adminId,
       pi.sku,
       prepay_price prepayPrice,
       prepay_amount prepayAmount,
       used_amount usedAmount,
       usable_type usableType,
       pay_type payType,
       delivery_cycle deliveryCycle,
       pi.status,
       effect_time effectTime,
       lose_effect_time loseEffectTime,
       pi.auditor,
       pi.audit_time auditTime,
       pi.updater,
       pi.update_time updateTime,
       pi.creator,
       pi.create_time createTime,
       i.weight,
       i.ext_type extType,
       p.pd_name pdName,
       a.realname
    from prepay_inventory pi
             left join inventory i on pi.sku = i.sku
             left join products p on i.pd_id = p.pd_id
             left join admin a on pi.admin_id = a.admin_id
    <where>
      <if test="adminId != null">
        and pi.admin_id = #{adminId}
      </if>
      <if test="status != null">
        and pi.status = #{status}
      </if>
      <if test="sku">
        and pi.sku like concat('%', #{sku},'%')
      </if>
      <if test="realname != null">
        and a.realname like concat('%', #{realname}, '%')
      </if>
      <if test="statusArr != null and statusArr.length != 0">
        and pi.status in
        <foreach collection="statusArr" open="(" item="el" separator="," close=")">
          #{el}
        </foreach>
      </if>
    </where>
    order by pi.id
    <if test="adminId == null">
      desc
    </if>
  </select>
  <select id="unUseQuantity" resultType="net.summerfarm.model.domain.PrepayInventory">
    select sku, sum(prepay_amount) prepayAmount, sum(used_amount) usedAmount
    from prepay_inventory
    where status in (1, 2)
    group by sku
  </select>
  <select id="unUseSkuQuantity" resultType="net.summerfarm.model.domain.PrepayInventory">
    select ifnull(sum(prepay_amount - used_amount), 0)
    from prepay_inventory
    where status in (1, 2)
    and sku = #{sku}
  </select>
  <select id="selectExpireAtDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from prepay_inventory
    where status in (1, 2)
      and delivery_cycle is not null
      and effect_time is not null
      and datediff(#{calcDate}, effect_time) = delivery_cycle
  </select>
  <select id="selectInEffectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from prepay_inventory
    where status = 2
     and admin_id = #{adminId}
     and sku = #{sku}
  </select>

  <select id="selectPrepayInventoryListById" resultType="net.summerfarm.model.domain.PrepayInventory">
    select
    <include refid="Base_Column_List" />
    from prepay_inventory
    where id in
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>

  <update id="updateUsedAmount">
    update prepay_inventory
    set updater = #{record.updater},update_time = #{record.updateTime},used_amount = used_amount - #{amount}
      <if test="record.status != null">
        ,status = #{record.status}
      </if>
    where id = #{record.id}
  </update>

</mapper>
