<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.TmsLackGoodsApprovedMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.TmsLackGoodsApproved">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="delivery_path_id" jdbcType="INTEGER" property="deliveryPathId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="m_id" jdbcType="INTEGER" property="mId" />
    <result column="lack_num" jdbcType="INTEGER" property="lackNum" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="lack_type" jdbcType="INTEGER" property="lackType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="responsible" jdbcType="INTEGER" property="responsible" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="buy_out" jdbcType="INTEGER" property="buyOut" />
    <result column="buy_out_money" jdbcType="DECIMAL" property="buyOutMoney" />
    <result column="judgment_opinion" jdbcType="VARCHAR" property="judgmentOpinion" />
    <result column="stock_task_id" jdbcType="INTEGER" property="stockTaskId" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="approved_admin_id" jdbcType="INTEGER" property="approvedAdminId" />
    <result column="approved_time" jdbcType="TIMESTAMP" property="approvedTime" />
    <result column="responsibility_admin_id" jdbcType="INTEGER" property="responsibilityAdminId" />
    <result column="responsibility_time" jdbcType="TIMESTAMP" property="responsibilityTime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="stock_lack_num" jdbcType="INTEGER" property="stockLackNum" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, store_no, warehouse_no, delivery_path_id, sku, m_id, lack_num, money, lack_type, remark,
    responsible, state, buy_out, buy_out_money, judgment_opinion, stock_task_id, pic, 
    create_time, update_time, approved_admin_id,approved_time,responsibility_admin_id,responsibility_time,order_no,amount,stock_lack_num
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tms_lack_goods_approved
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tms_lack_goods_approved
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.TmsLackGoodsApproved">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into tms_lack_goods_approved (id, store_no, warehouse_no,
      delivery_path_id, sku, m_id, 
      lack_num, money, lack_type, 
      remark, responsible, state, 
      buy_out, buy_out_money, judgment_opinion, 
      stock_task_id, pic, create_time, 
      update_time,finish_time,order_no,amount,stock_lack_num)
    values (#{id,jdbcType=BIGINT}, #{storeNo,jdbcType=INTEGER}, #{warehouseNo},
      #{deliveryPathId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{mId,jdbcType=INTEGER}, 
      #{lackNum,jdbcType=INTEGER}, #{money,jdbcType=DECIMAL}, #{lackType,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{responsible,jdbcType=INTEGER}, #{state,jdbcType=INTEGER}, 
      #{buyOut,jdbcType=INTEGER}, #{buyOutMoney,jdbcType=DECIMAL}, #{judgmentOpinion,jdbcType=VARCHAR}, 
      #{stockTaskId,jdbcType=INTEGER}, #{pic,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP},#{finishTime},#{orderNo},#{amount},#{stockLackNum})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.TmsLackGoodsApproved">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into tms_lack_goods_approved
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="deliveryPathId != null">
        delivery_path_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="lackNum != null">
        lack_num,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="lackType != null">
        lack_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="responsible != null">
        responsible,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="buyOut != null">
        buy_out,
      </if>
      <if test="buyOutMoney != null">
        buy_out_money,
      </if>
      <if test="judgmentOpinion != null">
        judgment_opinion,
      </if>
      <if test="stockTaskId != null">
        stock_task_id,
      </if>
      <if test="pic != null">
        pic,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="finishTime != null">
        finish_time,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="stockLackNum != null">
        stock_lack_num,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo},
      </if>
      <if test="deliveryPathId != null">
        #{deliveryPathId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=INTEGER},
      </if>
      <if test="lackNum != null">
        #{lackNum,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="lackType != null">
        #{lackType,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="responsible != null">
        #{responsible,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="buyOut != null">
        #{buyOut,jdbcType=INTEGER},
      </if>
      <if test="buyOutMoney != null">
        #{buyOutMoney,jdbcType=DECIMAL},
      </if>
      <if test="judgmentOpinion != null">
        #{judgmentOpinion,jdbcType=VARCHAR},
      </if>
      <if test="stockTaskId != null">
        #{stockTaskId,jdbcType=INTEGER},
      </if>
      <if test="pic != null">
        #{pic,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finishTime != null">
        #{finishTime},
      </if>
      <if test="orderNo != null">
        #{orderNo},
      </if>
      <if test="amount != null">
        #{orderNo},
      </if>
      <if test="stockLackNum != null">
        #{stockLackNum},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.TmsLackGoodsApproved">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tms_lack_goods_approved
    <set>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo},
      </if>
      <if test="deliveryPathId != null">
        delivery_path_id = #{deliveryPathId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=INTEGER},
      </if>
      <if test="lackNum != null">
        lack_num = #{lackNum,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="lackType != null">
        lack_type = #{lackType,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="responsible != null">
        responsible = #{responsible,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="buyOut != null">
        buy_out = #{buyOut,jdbcType=INTEGER},
      </if>
      <if test="buyOutMoney != null">
        buy_out_money = #{buyOutMoney,jdbcType=DECIMAL},
      </if>
      <if test="judgmentOpinion != null">
        judgment_opinion = #{judgmentOpinion,jdbcType=VARCHAR},
      </if>
      <if test="stockTaskId != null">
        stock_task_id = #{stockTaskId,jdbcType=INTEGER},
      </if>
      <if test="pic != null">
        pic = #{pic,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approvedAdminId != null">
        approved_admin_id = #{approvedAdminId},
      </if>
      <if test="approvedTime != null">
        approved_time = #{approvedTime},
      </if>
      <if test="responsibilityAdminId != null">
        responsibility_admin_id = #{responsibilityAdminId},
      </if>
      <if test="responsibilityTime != null">
        responsibility_time = #{responsibilityTime},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo},
      </if>
      <if test="amount != null">
        amount = #{amount},
      </if>
      <if test="stockLackNum != null">
        stock_lack_num = #{stockLackNum},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.TmsLackGoodsApproved">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tms_lack_goods_approved
    set store_no = #{storeNo,jdbcType=INTEGER},
      warehouse_no = #{warehouseNo},
      delivery_path_id = #{deliveryPathId,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      m_id = #{mId,jdbcType=INTEGER},
      lack_num = #{lackNum,jdbcType=INTEGER},
      money = #{money,jdbcType=DECIMAL},
      lack_type = #{lackType,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      responsible = #{responsible,jdbcType=INTEGER},
      state = #{state,jdbcType=INTEGER},
      buy_out = #{buyOut,jdbcType=INTEGER},
      buy_out_money = #{buyOutMoney,jdbcType=DECIMAL},
      judgment_opinion = #{judgmentOpinion,jdbcType=VARCHAR},
      stock_task_id = #{stockTaskId,jdbcType=INTEGER},
      pic = #{pic,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      approved_admin_id = #{approvedAdminId},
      approved_time = #{approvedTime},
      responsibility_admin_id = #{responsibilityAdminId},
      responsibility_time = #{responsibilityTime},
      order_no = #{orderNo},
      amount = #{amount},
      stock_lack_num = #{stockLackNum}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectOrderInfoByPathId" parameterType="integer" resultType="net.summerfarm.model.vo.LackGoodsOrderInfoVO">
    SELECT
      dpl.order_no orderNo,
      oi.sku,
      oi.amount,
      oi.original_price price,
      o.order_time orderTime,
      wip.warehouse_no areaNo,
      dpl.order_store_no storeNo,
      dp.id deliveryPathId,
      o.m_id mId,
      dp.finish_time finishTime
    FROM
      delivery_path dp
        LEFT JOIN delivery_plan dpl ON dp.delivery_time = dpl.delivery_time
        AND dp.contact_id = dpl.contact_id
        AND dpl.order_store_no = dp.store_no
        LEFT JOIN orders o ON o.order_no = dpl.order_no
        LEFT JOIN order_item oi ON oi.order_no = dpl.order_no
        LEFT JOIN warehouse_inventory_mapping wip ON wip.store_no = dpl.order_store_no
        AND wip.sku = oi.sku
    WHERE
      dp.id = #{deliveryPathId}
      AND o.`status` != 8
	  AND dpl.intercept_flag = 0
    ORDER BY
      o.order_time asc
  </select>

  <select id="getDataList" parameterType="net.summerfarm.model.DTO.LackGoodsApprovedDTO" resultType="net.summerfarm.model.vo.LackGoodsApprovedVO">
    SELECT
      tlga.id,
      tlga.store_no AS storeNo,
      tlga.warehouse_no AS warehouseNo,
      dp.finish_time AS finishTime,
      m.mname,
      tlga.sku,
      p.pd_name AS pdName,
      tlga.lack_num AS lackNum,
      tlga.money,
     (tlga.lack_num*tlga.money) AS totalMoney,
      tlga.lack_type AS lackType,
      tlga.remark,
      tlga.responsible,
      tlga.state,
      CASE WHEN dcp.tms_car_id IS NULL THEN dc.phone ELSE td.phone END driverPhone,
      CASE WHEN dcp.tms_car_id IS NULL THEN dc.driver ELSE td.name END driverName,
      concat(c.province,c.city,c.area,c.address,ifnull(c.house_number,'')) address,
      concat(dp.path,'-',dp.sort) AS pathName,
      dp.delivery_pic AS deliveryPic,
      i.weight,
      tlga.buy_out AS buyOut,
      tlga.buy_out_money AS buyOutMoney,
      tlga.judgment_opinion AS judgmentOpinion,
      tlga.order_no     AS orderNo,
       c.phone,
      tlga.amount,
       tlga.pic,
      tlga.stock_lack_num AS stockLackNum,
      tlga.stock_task_id AS stockTaskId
    FROM
      `tms_lack_goods_approved` tlga
        LEFT JOIN delivery_path dp ON tlga.delivery_path_id = dp.id
        LEFT JOIN contact c ON c.contact_id=dp.contact_id
        LEFT JOIN delivery_car_path dcp ON dcp.delivery_time = dp.delivery_time
        AND dcp.store_no = dp.store_no
        AND dcp.path = dp.path
        LEFT JOIN delivery_car dc ON dc.id = dcp.delivery_car_id
        AND dcp.path = dp.path
        AND dcp.store_no = dp.store_no
        LEFT JOIN tms_driver td on td.id = dcp.delivery_car_id
        LEFT JOIN inventory i ON i.sku = tlga.sku
        LEFT JOIN products p ON p.pd_id = i.pd_id
        LEFT JOIN merchant m ON m.m_id = tlga.m_id
    WHERE 1=1
      <if test="id != null">
        AND tlga.id = #{id}
      </if>
      <if test="warehouseNo != null">
        AND tlga.warehouse_no = #{warehouseNo}
      </if>
      <if test="storeNo != null">
        AND tlga.store_no = #{storeNo}
      </if>
      <if test="state != null">
        AND tlga.state = #{state}
      </if>
      <if test="startTime != null and endTime != null">
        AND dp.finish_time <![CDATA[<=]]> #{endTime} and dp.finish_time <![CDATA[>=]]> #{startTime}
      </if>
      <if test="mname != null and mname != ''">
        AND m.mname like CONCAT('%',#{mname},'%')
      </if>
      <if test="pdName != null and pdName != ''">
        AND p.pd_name like CONCAT('%',#{pdName},'%')
      </if>
      <if test="sku != null and sku != ''">
        AND tlga.sku = #{sku}
      </if>
      <if test="driverName != null and driverName != ''">
        AND dc.driver like CONCAT('%',#{driverName},'%')
      </if>
      <if test="lackType != null">
        AND tlga.lack_type = #{lackType}
      </if>
      <if test="lackTypes != null and lackTypes.size > 0">
        AND
        <foreach collection="lackTypes" open="(" close=")" item="lackType" separator="or">
          (tlga.lack_type like CONCAT('%',#{lackType},'%'))
        </foreach>
      </if>
      <if test="areaNos != null and areaNos.size > 0">
        AND tlga.warehouse_no in
        <foreach collection="areaNos" open="(" close=")" item="areaNo" separator=",">
          #{areaNo}
        </foreach>
      </if>
      <if test="storeNos != null and storeNos.size > 0">
        AND tlga.store_no in
        <foreach collection="storeNos" open="(" close=")" item="storeNo" separator=",">
          #{storeNo}
        </foreach>
      </if>
      <if test="ids != null and ids.size > 0">
        AND tlga.id in
        <foreach collection="ids" open="(" close=")" item="id" separator=",">
          #{id}
        </foreach>
      </if>
    order by tlga.id desc
  </select>

  <update id="batchUpdate" parameterType="net.summerfarm.model.DTO.BatchLackToCondemnDTO">
    UPDATE tms_lack_goods_approved
    SET state = 3,
        responsible = #{responsible},
        buy_out = #{buyOut},
        buy_out_money = lack_num * money,
        judgment_opinion = #{judgmentOpinion},
        update_time = now(),
        responsibility_admin_id = #{responsibilityAdminId},
        responsibility_time = now()
    WHERE
    id IN
    <foreach collection="ids" open="(" close=")" item="id" separator=",">
        #{id}
    </foreach>

  </update>

  <select id="selectByDeliveryPathId" parameterType="integer" resultMap="BaseResultMap">
    SELECT
     *
    FROM
      tms_lack_goods_approved
    WHERE
      delivery_path_id = #{deliveryPathId}
  </select>
</mapper>