<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.PurchaseAccountingMapper">


    <insert id="createPurchaseAccount" parameterType="net.summerfarm.model.domain.PurchaseAccounting" useGeneratedKeys="true" keyProperty="id">

        insert into purchase_accounting (`create_time`,`create_name`,`examine_time`,`approval_time`,`create_id`,`examine_id`,`approval_id`,examine_remark,`approval_remark`,
        `examine_name`,`approval_name`,`total_amount`,`create_amount`,`payee_name`,`invoice_url`,`status`,`payee_id`,`create_remark`)
        values (#{createTime}, #{createName}, #{examineTime}, #{approvalTime}, #{createId}, #{examineId},#{approvalId},#{examineRemark},#{approvalRemark}
        ,#{examineName},#{approvalName},#{totalAmount},#{createAmount},#{payeeName},#{invoiceUrl},#{status},#{payeeId},#{createRemark})
    </insert>

    <update id="update" parameterType="net.summerfarm.model.domain.PurchaseAccounting">
        update purchase_accounting
        <set>
            <if test="examineTime != null">
                examine_time = #{examineTime},
            </if>
            <if test="approvalTime != null">
                approval_time = #{approvalTime},
            </if>
            <if test="examineId != null">
                examine_id = #{examineId},
            </if>
            <if test="approvalId != null">
                approval_id = #{approvalId},
            </if>
            <if test="examineRemark != null">
                examine_remark = #{examineRemark},
            </if>
            <if test="approvalRemark != null">
                approval_remark = #{approvalRemark},
            </if>
            <if test="createRemark != null">
                create_remark = #{createRemark},
            </if>
            <if test="examineName != null">
                examine_name = #{examineName},
            </if>
            <if test="approvalName != null">
                approval_name = #{approvalName},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="invoiceUrl != null">
                invoice_url = #{invoiceUrl},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="selectById" resultType="net.summerfarm.model.domain.PurchaseAccounting">
        select id,create_time createTime, status,total_amount totalAmount,create_name createName, create_id createId from purchase_accounting
        where id =#{id}
    </select>

    <select id="selectTotalAmount" resultType="java.math.BigDecimal">
        select sum(total_amount) aomunt from purchase_accounting
        where status = 2
    </select>

    <select id="selectPurchaseAccount" resultType="net.summerfarm.model.domain.PurchaseAccounting">
        select pa.id,pa.total_amount totalAmount,pa.status,pa.create_time createTime,pa.create_name createName from purchase_accounting pa
        inner join purchases_account_plan pap on pa.id = pap.account_id
        <if test="purchaseNo != null">
          and pap.purchases_no = #{purchaseNo}
        </if>
        <where>
            <if test="status != null">
               and  pa.status = #{status}
            </if>
            <if test="id != null">
               and  pa.id = #{id}
            </if>
        </where>
        group by pa.id
        order by pa.create_time
      </select>
    <select id="selectByAccountId" resultType="net.summerfarm.model.vo.PurchaseAccountingVO">
        select id,
        create_time createTime,
        create_name createName,
        examine_time examineTime,
        approval_time approvalTime,
        examine_remark examineRemark,
        approval_remark approvalRemark,
        examine_name examineName,
        approval_name approvalName,
        total_amount totalAmount,
        create_amount createAmount,
        payee_name payeeName,
        create_remark createRemark,
        invoice_url invoiceUrl,status
        from purchase_accounting
        where id =#{id}
    </select>
</mapper>