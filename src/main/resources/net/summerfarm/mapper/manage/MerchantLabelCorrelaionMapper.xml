<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MerchantLabelCorrelaionMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantLabelCorrelaion">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="label_id" jdbcType="BIGINT" property="labelId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, label_id, m_id
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_label_correlation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_label_correlation
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.MerchantLabelCorrelaion">
    insert into merchant_label_correlation (id, label_id, m_id, 
      create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{labelId,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.MerchantLabelCorrelaion">
    insert into merchant_label_correlation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="labelId != null">
        label_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="labelId != null">
        #{labelId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.MerchantLabelCorrelaion">
    update merchant_label_correlation
    <set>
      <if test="labelId != null">
        label_id = #{labelId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.MerchantLabelCorrelaion">
    update merchant_label_correlation
    set label_id = #{labelId,jdbcType=BIGINT},
      m_id = #{mId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getList" parameterType="net.summerfarm.model.domain.MerchantLabelCorrelaion" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_label_correlation
    <where>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
    </where>
  </select>

  <delete id="deleteByEntity" parameterType="net.summerfarm.model.domain.MerchantLabelCorrelaion">
    delete from merchant_label_correlation
    <where>
      <if test="mId != null">
        and m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="type != null">
        and label_id in (select id from merchant_label where type = #{type,jdbcType=TINYINT})
      </if>
    </where>
  </delete>

  <insert id="batchSave" parameterType="java.util.List">
    insert into merchant_label_correlation (label_id, m_id, create_time)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.labelId,jdbcType=BIGINT}, #{item.mId,jdbcType=BIGINT}, now())
    </foreach>
  </insert>
</mapper>