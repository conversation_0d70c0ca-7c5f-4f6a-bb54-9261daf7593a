<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.TrustChangeRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.TrustChangeRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="success_flag" jdbcType="BOOLEAN" property="successFlag" />
    <result column="result_msg" jdbcType="VARCHAR" property="resultMsg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, store_no, sku, warehouse_no, success_flag, result_msg, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from trust_change_record
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="selectCount" resultType="java.lang.Integer">
      select id from trust_change_record tcr  order by id desc limit 1
    </select>
  <select id="selectByMaxId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from trust_change_record
    where id > #{id} and store_no= #{storeNo} and success_flag=0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from trust_change_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.TrustChangeRecord" useGeneratedKeys="true">
    insert into trust_change_record (store_no, sku, warehouse_no, 
      success_flag, result_msg, create_time
      )
    values (#{storeNo,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{warehouseNo,jdbcType=INTEGER}, 
      #{successFlag,jdbcType=BOOLEAN}, #{resultMsg,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.TrustChangeRecord" useGeneratedKeys="true">
    insert into trust_change_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="successFlag != null">
        success_flag,
      </if>
      <if test="resultMsg != null">
        result_msg,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="successFlag != null">
        #{successFlag,jdbcType=BOOLEAN},
      </if>
      <if test="resultMsg != null">
        #{resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.TrustChangeRecord">
    update trust_change_record
    <set>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="successFlag != null">
        success_flag = #{successFlag,jdbcType=BOOLEAN},
      </if>
      <if test="resultMsg != null">
        result_msg = #{resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.TrustChangeRecord">
    update trust_change_record
    set store_no = #{storeNo,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      success_flag = #{successFlag,jdbcType=BOOLEAN},
      result_msg = #{resultMsg,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>