<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MarketCouponSendScopeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MarketCouponSendScope">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="send_id" jdbcType="BIGINT" property="sendId" />
    <result column="scope_id" jdbcType="BIGINT" property="scopeId" />
    <result column="scope_type" jdbcType="TINYINT" property="scopeType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, send_id, scope_id, scope_type, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from market_coupon_send_scope
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from market_coupon_send_scope
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.MarketCouponSendScope">
    insert into market_coupon_send_scope (id, send_id, scope_id, 
      scope_type, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{sendId,jdbcType=BIGINT}, #{scopeId,jdbcType=BIGINT}, 
      #{scopeType,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.MarketCouponSendScope">
    insert into market_coupon_send_scope
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sendId != null">
        send_id,
      </if>
      <if test="scopeId != null">
        scope_id,
      </if>
      <if test="scopeType != null">
        scope_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sendId != null">
        #{sendId,jdbcType=BIGINT},
      </if>
      <if test="scopeId != null">
        #{scopeId,jdbcType=BIGINT},
      </if>
      <if test="scopeType != null">
        #{scopeType,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.MarketCouponSendScope">
    update market_coupon_send_scope
    <set>
      <if test="sendId != null">
        send_id = #{sendId,jdbcType=BIGINT},
      </if>
      <if test="scopeId != null">
        scope_id = #{scopeId,jdbcType=BIGINT},
      </if>
      <if test="scopeType != null">
        scope_type = #{scopeType,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.MarketCouponSendScope">
    update market_coupon_send_scope
    set send_id = #{sendId,jdbcType=BIGINT},
      scope_id = #{scopeId,jdbcType=BIGINT},
      scope_type = #{scopeType,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertBatch">
    insert into market_coupon_send_scope (id, send_id, scope_id,
                                          scope_type
    )
    values
        <foreach collection="list" item="item" index="index" separator=",">
        (#{item.id,jdbcType=BIGINT}, #{item.sendId,jdbcType=BIGINT}, #{item.scopeId,jdbcType=BIGINT},
        #{item.scopeType,jdbcType=TINYINT}
        )
        </foreach>
    </insert>

  <select id="selectBySendId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from market_coupon_send_scope
    where send_id = #{sendId,jdbcType=BIGINT}
  </select>
</mapper>