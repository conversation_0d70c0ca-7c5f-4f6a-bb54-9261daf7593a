<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CrmConfigChangeRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CrmConfigChangeRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="pm_id" jdbcType="INTEGER" property="pmId" />
    <result column="handle_name" jdbcType="VARCHAR" property="handleName" />
    <result column="new_value" jdbcType="VARCHAR" property="newValue" />
    <result column="old_value" jdbcType="VARCHAR" property="oldValue" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `type`, pm_id, handle_name, new_value, old_value, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_config_change_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_config_change_record
    where create_time <![CDATA[>=]]> #{startTime} and create_time <![CDATA[<]]> #{endTime}

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crm_config_change_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CrmConfigChangeRecord" useGeneratedKeys="true">
    insert into crm_config_change_record (`type`, pm_id, handle_name, 
       new_value, old_value,
      creator, create_time)
    values (#{type,jdbcType=INTEGER}, #{pmId,jdbcType=INTEGER}, #{handleName,jdbcType=VARCHAR},
            #{newValue,jdbcType=VARCHAR}, #{oldValue,jdbcType=VARCHAR},
      #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CrmConfigChangeRecord" useGeneratedKeys="true">
    insert into crm_config_change_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        `type`,
      </if>
      <if test="pmId != null">
        pm_id,
      </if>
      <if test="handleName != null">
        handle_name,
      </if>
      <if test="newValue != null">
        new_value,
      </if>
      <if test="oldValue != null">
        old_value,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="pmId != null">
        #{pmId,jdbcType=INTEGER},
      </if>
      <if test="handleName != null">
        #{handleName,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null">
        #{newValue,jdbcType=VARCHAR},
      </if>
      <if test="oldValue != null">
        #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CrmConfigChangeRecord">
    update crm_config_change_record
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="pmId != null">
        pm_id = #{pmId,jdbcType=INTEGER},
      </if>
      <if test="handleName != null">
        handle_name = #{handleName,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null">
        new_value = #{newValue,jdbcType=VARCHAR},
      </if>
      <if test="oldValue != null">
        old_value = #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CrmConfigChangeRecord">
    update crm_config_change_record
    set `type` = #{type,jdbcType=INTEGER},
      pm_id = #{pmId,jdbcType=INTEGER},
      handle_name = #{handleName,jdbcType=VARCHAR},
      new_value = #{newValue,jdbcType=VARCHAR},
      old_value = #{oldValue,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>