<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MerchantMonthPurmoneyMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantMonthPurmoney">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="m_id" jdbcType="VARCHAR" property="mId" />
    <result column="month_purmoney" jdbcType="VARCHAR" property="monthPurmoney" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, month_purmoney, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_month_purmoney
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByMId" resultType="java.lang.String">
    select month_purmoney from merchant_month_purmoney where m_id = #{mId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from merchant_month_purmoney
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByMid">
    delete from merchant_month_purmoney
    where m_id = #{mId}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MerchantMonthPurmoney" useGeneratedKeys="true">
    insert into merchant_month_purmoney (m_id, month_purmoney, creator, 
      create_time)
    values (#{mId,jdbcType=VARCHAR}, #{monthPurmoney,jdbcType=VARCHAR}, #{creator,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MerchantMonthPurmoney" useGeneratedKeys="true">
    insert into merchant_month_purmoney
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="monthPurmoney != null">
        month_purmoney,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=VARCHAR},
      </if>
      <if test="monthPurmoney != null">
        #{monthPurmoney,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.MerchantMonthPurmoney">
    update merchant_month_purmoney
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=VARCHAR},
      </if>
      <if test="monthPurmoney != null">
        month_purmoney = #{monthPurmoney,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.MerchantMonthPurmoney">
    update merchant_month_purmoney
    set m_id = #{mId,jdbcType=VARCHAR},
      month_purmoney = #{monthPurmoney,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>