<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ActivityBasicInfoMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.market.ActivityBasicInfo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="is_permanent" jdbcType="TINYINT" property="isPermanent"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="need_pre" jdbcType="TINYINT" property="needPre"/>
    <result column="pre_start_time" jdbcType="TIMESTAMP" property="preStartTime"/>
    <result column="pre_end_time" jdbcType="TIMESTAMP" property="preEndTime"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="tag" jdbcType="TINYINT" property="tag"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
    <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <resultMap id="ResultMapForPage" type="net.summerfarm.model.DTO.market.ActivityPageRespDTO">
    <id column="id" jdbcType="BIGINT" property="basicInfoId"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="is_permanent" jdbcType="TINYINT" property="isPermanent"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="tag" jdbcType="TINYINT" property="tag"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
    <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>


  <resultMap id="ActivityAndSkuMap" type="net.summerfarm.model.domain.Activity">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="area_no" property="areaNo"/>
    <result column="status" property="status"/>
    <result column="start_time" property="startTime"/>
    <result column="end_time" property="endTime"/>
    <result column="remark" property="info"/>
    <collection property="skuList" ofType="net.summerfarm.model.vo.ActivitySkuVO">
      <result column="sku" property="sku"/>
      <result column="sku_name" property="skuName"/>
      <result column="activity_price" property="activityPrice"/>
      <result column="ladder_price" property="ladderPrice"/>
      <result column="weight" property="weight"/>
      <result column="logo" property="logo"/>
      <result column="sale_price" property="salePrice"/>
      <result column="pd_id" property="pdId"/>
      <result column="base_sale_quantity" property="baseSaleQuantity"/>
      <result column="base_sale_unit" property="baseSaleUnit"/>
    </collection>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `name`, `start_time`, `end_time`, `is_permanent`, `status`, `need_pre`, `pre_start_time`,
    `pre_end_time`, `type`, `tag`, `remark`, `creator_id`, `updater_id`, `del_flag`,
    `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_basic_info
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from activity_basic_info
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.market.ActivityBasicInfo">
    insert into activity_basic_info (`id`, `name`, `start_time`,
                                     `end_time`, `is_permanent`, `status`,
                                     `need_pre`, `pre_start_time`, `pre_end_time`,
                                     `type`, `tag`, `remark`,
                                     `creator_id`, `updater_id`, `del_flag`,
                                     `create_time`, `update_time`)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP},
            #{endTime,jdbcType=TIMESTAMP}, #{isPermanent,jdbcType=TINYINT},
            #{status,jdbcType=TINYINT},
            #{needPre,jdbcType=TINYINT}, #{preStartTime,jdbcType=TIMESTAMP},
            #{preEndTime,jdbcType=TIMESTAMP},
            #{type,jdbcType=INTEGER}, #{tag,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR},
            #{creatorId,jdbcType=INTEGER}, #{updaterId,jdbcType=INTEGER},
            #{delFlag,jdbcType=TINYINT},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.market.ActivityBasicInfo"
    useGeneratedKeys="true" keyProperty="id">
    insert into activity_basic_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="startTime != null">
        `start_time`,
      </if>
      <if test="endTime != null">
        `end_time`,
      </if>
      <if test="isPermanent != null">
        `is_permanent`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="needPre != null">
        `need_pre`,
      </if>
      <if test="preStartTime != null">
        `pre_start_time`,
      </if>
      <if test="preEndTime != null">
        `pre_end_time`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="tag != null">
        `tag`,
      </if>
      <if test="remark != null">
        `remark`,
      </if>
      <if test="creatorId != null">
        `creator_id`,
      </if>
      <if test="updaterId != null">
        `updater_id`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isPermanent != null">
        #{isPermanent,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="needPre != null">
        #{needPre,jdbcType=TINYINT},
      </if>
      <if test="preStartTime != null">
        #{preStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preEndTime != null">
        #{preEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.model.domain.market.ActivityBasicInfo">
    update activity_basic_info
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        `start_time` = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        `end_time` = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isPermanent != null">
        `is_permanent` = #{isPermanent,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="needPre != null">
        `need_pre` = #{needPre,jdbcType=TINYINT},
      </if>
      <if test="preStartTime != null">
        `pre_start_time` = #{preStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preEndTime != null">
        `pre_end_time` = #{preEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="tag != null">
        `tag` = #{tag,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        `remark` = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        `creator_id` = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="updaterId != null">
        `updater_id` = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey"
    parameterType="net.summerfarm.model.domain.market.ActivityBasicInfo">
    update activity_basic_info
    set `name`           = #{name,jdbcType=VARCHAR},
        `start_time`     = #{startTime,jdbcType=TIMESTAMP},
        `end_time`       = #{endTime,jdbcType=TIMESTAMP},
        `is_permanent`   = #{isPermanent,jdbcType=TINYINT},
        `status`         = #{status,jdbcType=TINYINT},
        `need_pre`       = #{needPre,jdbcType=TINYINT},
        `pre_start_time` = #{preStartTime,jdbcType=TIMESTAMP},
        `pre_end_time`   = #{preEndTime,jdbcType=TIMESTAMP},
        `type`           = #{type,jdbcType=INTEGER},
        `tag`            = #{tag,jdbcType=TINYINT},
        `remark`         = #{remark,jdbcType=VARCHAR},
        `creator_id`     = #{creatorId,jdbcType=INTEGER},
        `updater_id`     = #{updaterId,jdbcType=INTEGER},
        `del_flag`       = #{delFlag,jdbcType=TINYINT},
        `create_time`    = #{createTime,jdbcType=TIMESTAMP},
        `update_time`    = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByScope" parameterType="net.summerfarm.model.DTO.market.ActivityBasicQueryDTO"
    resultMap="net.summerfarm.mapper.manage.ActivityScopeConfigMapper.BaseResultMap">
    select asco.*
    from activity_basic_info abi
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    where
    abi.type = #{type}
    <if test="id != null">
      and abi.id != #{id}
    </if>
    and (abi.start_time &lt;= #{endTime} and abi.end_time &gt;= #{startTime}
    or abi.is_permanent = 1)
    and abi.del_flag = 0 and asco.del_flag = 0
    and asco.scope_type = #{scopeType}
    and asco.scope_id in
    <foreach collection="scopeIds" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="listByQuery" parameterType="net.summerfarm.model.DTO.market.ActivityBasicQueryDTO"
    resultMap="ResultMapForPage">
    select distinct abi.*
    from activity_basic_info abi
    <if test="sku != null">
      left join activity_item_config aic on abi.id = aic.basic_info_id
      left join activity_sku_detail asd on aic.id = asd.item_config_id
    </if>
    <if test="scopeList != null and scopeList.size > 0">
      left join activity_scope_config asco on abi.id = asco.basic_info_id
    </if>
    <where>
      abi.del_flag = 0
      <if test="id != null">
        and abi.id = #{id}
      </if>
      <if test="type != null">
        and abi.type = #{type}
      </if>
      <if test="name != null">
        and abi.name like CONCAT('%', #{name},'%')
      </if>
      <if test="sku != null">
        and sku = #{sku} and asd.del_flag = 0
      </if>
      <if test="scopeList != null and scopeList.size > 0">
        and asco.del_flag = 0
        and (scope_id,scope_type) in
        <foreach collection="scopeList" item="scope" open="(" separator="," close=")">
          (#{scope.scopeId}, #{scope.scopeType})
        </foreach>
      </if>
      <if test="activityStatus != null">
        <choose>
          <when test="activityStatus == 0">
            and (abi.start_time &gt; now()
            or (abi.start_time &lt;= now() and abi.end_time &gt;= now() and status = 0)
            or (abi.is_permanent = 1 and status = 0))
          </when>
          <when test="activityStatus == 1">
            and ((abi.start_time &lt;= now() and abi.end_time &gt;= now())
            or abi.is_permanent = 1)
            and status = 1
          </when>
          <when test="activityStatus == 2">
            and abi.end_time &lt; now()
          </when>
        </choose>
      </if>
      <if test="creatorId != null">
        and abi.creator_id = #{creatorId}
      </if>
    </where>
    order by abi.id desc
  </select>

  <select id="listByScope"
    resultType="net.summerfarm.model.DTO.market.ActivityItemScopeDTO">
    /*FORCE_MASTER*/
    select aic.basic_info_id basicInfoId,aic.id itemConfigId, asco.scope_id scopeId
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    <where>
      abi.del_flag = 0 and aic.del_flag = 0 and asco.del_flag = 0
      <if test="type != null">
        and abi.type = #{type}
      </if>
      <if test="list != null and list.size > 0">
        and (scope_id,scope_type) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
          (#{item.scopeId},#{item.scopeType})
        </foreach>
      </if>
      <if test="activityStatus != null">
        <choose>
          <when test="activityStatus == 0">
            and (abi.start_time &lt; now()
            or (abi.start_time &lt;= now() and abi.end_time &gt;= now() and status = 0)
            or (abi.is_permanent = 1 and status = 0))
          </when>
          <when test="activityStatus == 1">
            and ((abi.start_time &lt;= now() and abi.end_time &gt;= now())
            or abi.is_permanent = 1)
            and status = 1
          </when>
          <when test="activityStatus == 2">
            and abi.end_time &lt; now()
          </when>
        </choose>
      </if>
    </where>
    order by abi.id desc
  </select>

  <select id="getEffectingById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_basic_info
    where id = #{id} and ((start_time &lt;= now() and end_time &gt;= now())
    or is_permanent = 1)
    and status = 1
  </select>

  <select id="listValidByScope"
    resultType="net.summerfarm.model.DTO.market.ActivityItemScopeDTO">
    select aic.basic_info_id basicInfoId,aic.id itemConfigId, asco.scope_id scopeId
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    <where>
      abi.del_flag = 0 and aic.del_flag = 0 and asco.del_flag = 0 and (abi.end_time &gt; now() ||
      abi.is_permanent = 1)
      <if test="type != null">
        and abi.type = #{type}
      </if>
      <if test="list != null and list.size > 0">
        and (scope_id,scope_type) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
          (#{item.scopeId},#{item.scopeType})
        </foreach>
      </if>
    </where>
    order by abi.id desc
  </select>

  <select id="listValidByQuery" resultMap="ActivityAndSkuMap">
    SELECT abi.*,asp.sku,asp.sale_price, asp.activity_price, asp.ladder_price,i.base_sale_quantity,i.base_sale_unit,i.weight, p.pd_id, p.pd_name
    sku_name,ifnull(i.sku_pic, p.picture_path) logo from activity_basic_info abi
    left join activity_sku_price asp on abi.id = asp.basic_info_id
    left join inventory i on asp.sku = i .sku
    left join products p on i.pd_id = p.pd_id
    where abi.del_flag = 0
    and (abi.end_time > NOW() or abi.is_permanent = 1)
    and abi.type = 0
    <if test="areaNo !=null">
      and asp.area_no = #{areaNo}
    </if>
    <if test="productName !=null">
      and p.pd_name like CONCAT('%',#{productName},'%')
    </if>
    order by abi.is_permanent desc,abi.end_time desc
  </select>


  <select id="listValidByNowTime" resultType="long">
    /*FORCE_MASTER*/
    select distinct abi.id
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    left join activity_scene_config ascc on abi.id = ascc.basic_info_id
    <where>
      abi.del_flag = 0 and aic.del_flag = 0 and asco.del_flag = 0
      and ((abi.start_time &lt;= now() and abi.end_time &gt;= now())
      or abi.is_permanent = 1) and status = 1
      and ascc.del_flag = 0
      and asco.scope_type in (2,3)
    </where>
    order by abi.id desc
  </select>


  <select id="listValidInitDataByNowTime" resultType="long">
    /*FORCE_MASTER*/
    select distinct abi.id
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    left join activity_scene_config ascc on abi.id = ascc.basic_info_id
    <where>
      abi.del_flag = 0 and aic.del_flag = 0 and asco.del_flag = 0
      and (abi.end_time &gt;= now()
      or abi.is_permanent = 1)
      and ascc.del_flag = 0
      and asco.scope_type in (2,3)
    </where>
    order by abi.id desc
  </select>

  <select id="listInValidInitDataByStartDate" resultType="long">
    /*FORCE_MASTER*/
    select distinct t.id from activity_basic_info t where t.id not in (
    select distinct abi.id
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    left join activity_scene_config ascc on abi.id = ascc.basic_info_id
    <where>
      abi.del_flag = 0 and aic.del_flag = 0 and asco.del_flag = 0
      and (abi.end_time &gt;= now()
      or abi.is_permanent = 1)
      and ascc.del_flag = 0
      and asco.scope_type in (2,3)
    </where>
    )
    and t.start_time &gt;= #{startDate}
    and t.end_time &lt;= #{endDate}
    order by t.id desc

  </select>




  <select id="listAreaNoByBasicId" resultType="java.lang.String">
    /*FORCE_MASTER*/
    select distinct (CONCAT(asd.sku, "-", asco.scope_id))
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    left join activity_sku_detail asd on asd.item_config_id = aic.id
    <where>
      abi.del_flag = 0 and aic.del_flag = 0 and asco.del_flag = 0
      and asd.del_flag = 0
      and asco.scope_type = 2
      and abi.id in
      <foreach collection="basicIdList" item="item" open="(" separator="," close=")">
        (#{item})
      </foreach>
    </where>
    order by abi.id desc
  </select>


  <select id="listLargeAreaNoByBasicId" resultType="java.lang.String">
    /*FORCE_MASTER*/
    select distinct (CONCAT(asd.sku, "-", a.area_no))
    from activity_basic_info abi
    left join activity_item_config aic on abi.id = aic.basic_info_id
    left join activity_scope_config asco on abi.id = asco.basic_info_id
    left join activity_sku_detail asd on asd.item_config_id = aic.id
    left join area a on a.large_area_no = asco.scope_id
    <where>
      abi.del_flag = 0 and aic.del_flag = 0 and asco.del_flag = 0
      and asd.del_flag = 0
      and asco.scope_type = 3
      and abi.id in
      <foreach collection="basicIdList" item="item" open="(" separator="," close=")">
        (#{item})
      </foreach>
    </where>
    order by abi.id desc
  </select>


  <select id="selectUnderwayByEntity" parameterType="net.summerfarm.model.domain.market.ActivityBasicInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from activity_basic_info
    where end_time > now() and now() >= start_time
      <if test="status !=null">
        and status = #{status}
      </if>
      <if test="type !=null">
        and `type` = #{type}
      </if>
        <if test="tag !=null">
            and `tag` = #{tag}
        </if>
      <if test="remark !=null">
        and remark = #{remark}
      </if>
      <if test="delFlag !=null">
        and del_flag = #{delFlag}
      </if>
  </select>
</mapper>
