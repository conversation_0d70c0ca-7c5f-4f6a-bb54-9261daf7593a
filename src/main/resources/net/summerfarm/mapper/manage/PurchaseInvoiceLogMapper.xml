<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PurchaseInvoiceLogMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PurchaseInvoiceLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchase_invoice_id" jdbcType="INTEGER" property="purchaseInvoiceId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    id, purchase_invoice_id, `status`, `state`, create_time, creator, update_time, updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select matching_schedule matchingSchedule
    from purchase_invoice_log
    where purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER} and state = 0
    group by id desc
    limit 1
  </select>
  <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select id, purchase_invoice_id, `status`, `state`, create_time, creator, update_time, updater
    from purchase_invoice_log
    where purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER} and `state` = 0
    group by id desc
    limit 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from purchase_invoice_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PurchaseInvoiceLog" useGeneratedKeys="true">
    insert into purchase_invoice_log (purchase_invoice_id, `status`, `state`, 
      create_time, creator, update_time, 
      updater)
    values (#{purchaseInvoiceId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{state,jdbcType=INTEGER},
      now(), #{creator,jdbcType=VARCHAR}, now(),
      #{updater,jdbcType=VARCHAR})
  </insert>

  <update id="updateState">
    update purchase_invoice_log
    set updater = #{updater},
        update_time = now(),
        state = 1
    where purchase_invoice_id = #{id} and state = 0 and status = #{status}
  </update>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PurchaseInvoiceLog" useGeneratedKeys="true">
    insert into purchase_invoice_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="purchaseInvoiceId != null">
        purchase_invoice_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="purchaseInvoiceId != null">
        #{purchaseInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PurchaseInvoiceLog">
    update purchase_invoice_log
    <set>
      <if test="purchaseInvoiceId != null">
        purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PurchaseInvoiceLog">
    update purchase_invoice_log
    set purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      `state` = #{state,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="toFlashBack">
    update purchase_invoice_log
    set updater = #{updater},
        update_time = now(),
        state = #{state}
    where purchase_invoice_id = #{id,jdbcType=INTEGER} and state = 0 and status = #{status}
  </update>

  <select id="selectSubmission" parameterType="integer" resultType="net.summerfarm.model.vo.PurchaseInvoiceLogVO">
    select update_time submissionTime,updater submissionTimeMan
    from purchase_invoice_log
    where purchase_invoice_id = #{id,jdbcType=INTEGER} and state = 3 and status = 0
    order by update_time desc
    limit 1
  </select>

  <select id="selectMatchCompletion" parameterType="integer" resultType="net.summerfarm.model.vo.PurchaseInvoiceLogVO">
    select update_time matchCompletionTime,updater matchCompletionTimeMan
    from purchase_invoice_log
    where purchase_invoice_id = #{id,jdbcType=INTEGER} and state = 4 and status = 1
    order by update_time desc
    limit 1
  </select>

  <select id="selectReviewFiling" parameterType="integer" resultType="net.summerfarm.model.vo.PurchaseInvoiceLogVO">
    select update_time reviewFilingTime,updater reviewFilingTimeMan
    from purchase_invoice_log
    where purchase_invoice_id = #{id,jdbcType=INTEGER} and state = 5 and status = 2
    order by update_time desc
    limit 1
  </select>
</mapper>