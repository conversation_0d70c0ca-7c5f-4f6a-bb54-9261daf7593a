<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PriceAdjustmentRuleAreaMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PriceAdjustmentRuleArea">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="creater" jdbcType="INTEGER" property="creater" />
    <result column="creat_time" jdbcType="TIMESTAMP" property="creatTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, area_no, warehouse_no, store_no, creater, creat_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from price_adjustment_rule_area
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="selectByFieldName" resultType="java.lang.Integer">
        select ${fieldName}
        from price_adjustment_rule_area

    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from price_adjustment_rule_area
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteAll">
    delete from price_adjustment_rule_area
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PriceAdjustmentRuleArea" useGeneratedKeys="true">
    insert into price_adjustment_rule_area (area_no, warehouse_no, store_no, 
      creater, creat_time)
    values (#{areaNo,jdbcType=INTEGER}, #{warehouseNo,jdbcType=INTEGER}, #{storeNo,jdbcType=INTEGER}, 
      #{creater,jdbcType=INTEGER}, #{creatTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PriceAdjustmentRuleArea" useGeneratedKeys="true">
    insert into price_adjustment_rule_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="creater != null">
        creater,
      </if>
      <if test="creatTime != null">
        creat_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=INTEGER},
      </if>
      <if test="creatTime != null">
        #{creatTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="insertByFieldName">
    insert into price_adjustment_rule_area (${fieldName},creater,creat_time)
    values
    <foreach collection="info" item="item"  separator=",">
      (#{item},#{adminId},now())
    </foreach>



  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PriceAdjustmentRuleArea">
    update price_adjustment_rule_area
    <set>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="creater != null">
        creater = #{creater,jdbcType=INTEGER},
      </if>
      <if test="creatTime != null">
        creat_time = #{creatTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PriceAdjustmentRuleArea">
    update price_adjustment_rule_area
    set area_no = #{areaNo,jdbcType=INTEGER},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      store_no = #{storeNo,jdbcType=INTEGER},
      creater = #{creater,jdbcType=INTEGER},
      creat_time = #{creatTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="queryAreaIsOpen" resultType="boolean">
    select count(1) > 0 from price_adjustment_rule_area where area_no = #{areaNo}
  </select>
</mapper>