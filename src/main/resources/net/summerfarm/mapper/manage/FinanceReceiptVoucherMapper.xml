<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceReceiptVoucherMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceReceiptVoucher">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="finance_receipt_id" jdbcType="BIGINT" property="financeReceiptId" />
    <result column="receipt_amount" jdbcType="DECIMAL" property="receiptAmount" />
    <result column="receipt_voucher" jdbcType="VARCHAR" property="receiptVoucher" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="customer_payment_time" jdbcType="DATE" property="customerPaymentTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, finance_receipt_id, receipt_amount, receipt_voucher, create_time, update_time,customer_payment_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_receipt_voucher
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_receipt_voucher
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.FinanceReceiptVoucher">
    insert into finance_receipt_voucher ( finance_receipt_id, receipt_amount,
        customer_payment_time
      )
    values ( #{financeReceiptId,jdbcType=BIGINT}, #{receiptAmount,jdbcType=DECIMAL},  #{customerPaymentTime}
      )
  </insert>


  <insert id="insertBatch" parameterType="net.summerfarm.model.domain.FinanceReceiptVoucher">
    INSERT INTO finance_receipt_voucher(finance_receipt_id,receipt_amount,customer_payment_time)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.financeReceiptId},#{item.receiptAmount},#{item.customerPaymentTime})
    </foreach>
  </insert>


  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.FinanceReceiptVoucher">
    insert into finance_receipt_voucher
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="financeReceiptId != null">
        finance_receipt_id,
      </if>
      <if test="receiptAmount != null">
        receipt_amount,
      </if>
      <if test="receiptVoucher != null">
        receipt_voucher,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="financeReceiptId != null">
        #{financeReceiptId,jdbcType=BIGINT},
      </if>
      <if test="receiptAmount != null">
        #{receiptAmount,jdbcType=DECIMAL},
      </if>
      <if test="receiptVoucher != null">
        #{receiptVoucher,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceReceiptVoucher">
    update finance_receipt_voucher
    <set>
      <if test="financeReceiptId != null">
        finance_receipt_id = #{financeReceiptId,jdbcType=BIGINT},
      </if>
      <if test="receiptAmount != null">
        receipt_amount = #{receiptAmount,jdbcType=DECIMAL},
      </if>
      <if test="receiptVoucher != null">
        receipt_voucher = #{receiptVoucher,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceReceiptVoucher">
    update finance_receipt_voucher
    set finance_receipt_id = #{financeReceiptId,jdbcType=BIGINT},
      receipt_amount = #{receiptAmount,jdbcType=DECIMAL},
      receipt_voucher = #{receiptVoucher,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByReceiptIdList" resultMap="BaseResultMap">
    select id,
           receipt_amount,
           receipt_voucher,
           customer_payment_time
    from finance_receipt_voucher
    where finance_receipt_id = #{financeReceiptId}
    order by customer_payment_time is null asc,customer_payment_time desc
  </select>

</mapper>