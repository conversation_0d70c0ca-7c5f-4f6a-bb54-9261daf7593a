<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.RefundSlipVoucherMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.RefundSlipVoucher">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="refund_slip_id" jdbcType="INTEGER" property="refundSlipId" />
    <result column="voucher_address" jdbcType="VARCHAR" property="voucherAddress" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    id, refund_slip_id, voucher_address, create_time, creator, `status`, update_time, 
    updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from refund_slip_voucher
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from refund_slip_voucher
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert"  parameterType="net.summerfarm.model.RefundSlipVoucher" >
    insert into refund_slip_voucher (refund_slip_id, voucher_address, create_time, 
      creator, `status`)
    values (#{refundSlipId,jdbcType=INTEGER}, #{voucherAddress,jdbcType=VARCHAR}, now(),
      #{creator,jdbcType=VARCHAR}, 0)
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.RefundSlipVoucher" useGeneratedKeys="true">
    insert into refund_slip_voucher
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="refundSlipId != null">
        refund_slip_id,
      </if>
      <if test="voucherAddress != null">
        voucher_address,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="refundSlipId != null">
        #{refundSlipId,jdbcType=INTEGER},
      </if>
      <if test="voucherAddress != null">
        #{voucherAddress,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.RefundSlipVoucher">
    update refund_slip_voucher
    <set>
      <if test="refundSlipId != null">
        refund_slip_id = #{refundSlipId,jdbcType=INTEGER},
      </if>
      <if test="voucherAddress != null">
        voucher_address = #{voucherAddress,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.RefundSlipVoucher">
    update refund_slip_voucher
    set refund_slip_id = #{refundSlipId,jdbcType=INTEGER},
      voucher_address = #{voucherAddress,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectVoucherAddress" parameterType="integer" resultType="string">
    select voucher_address
    from refund_slip_voucher
    where refund_slip_id = #{id} and status = 0
  </select>

</mapper>