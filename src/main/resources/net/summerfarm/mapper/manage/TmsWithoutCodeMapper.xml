<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.TmsWithoutCodeMapper">



    <select id="selectTmsWithoutCode" parameterType="net.summerfarm.model.domain.TmsWithoutCode"
            resultType="net.summerfarm.model.domain.TmsWithoutCode">
        select delivery_path_id deliveryPathId,
            sku,quantity,remake,pictures
        from  tms_without_code
        <where>
            <if test="deliveryPathId != null">
               AND delivery_path_id = #{deliveryPathId}
            </if>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
        </where>
     </select>


</mapper>