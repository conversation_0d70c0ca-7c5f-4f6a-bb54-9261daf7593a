<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.CouponMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.Coupon" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="money" property="money" jdbcType="DECIMAL" />
    <result column="threshold" property="threshold" jdbcType="DECIMAL" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="vaild_date" property="vaildDate" jdbcType="TIMESTAMP" />
    <result column="vaild_time" property="vaildTime" jdbcType="INTEGER" />
    <result column="grouping" property="grouping" jdbcType="INTEGER" />
    <result column="reamrk" property="reamrk" jdbcType="VARCHAR" />
    <result column="category_id" property="categoryId" jdbcType="VARCHAR" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="agio_type" property="agioType" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="start_time" property="startTime"/>
    <result column="start_date" property="startDate"/>
    <result column="task_tag" property="taskTag"/>
    <result column="delete_tag" property="deleteTag"/>
    <result column="limit_flag" property="limitFlag"/>
    <result column="activity_scope" property="activityScope"/>
    <result column="quantity_claimed" property="quantityClaimed"/>
    <result column="grant_amount" property="grantAmount"/>
    <result column="grant_limit" property="grantLimit"/>
    <result column="creator" property="creator"/>
    <result column="endTime" property="taskEndTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="TaskTimeMap" type="net.summerfarm.model.domain.Coupon" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="money" property="money" jdbcType="DECIMAL" />
    <result column="threshold" property="threshold" jdbcType="DECIMAL" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="grouping" property="grouping" jdbcType="INTEGER" />
    <result column="reamrk" property="reamrk" jdbcType="VARCHAR" />
    <result column="category_id" property="categoryId" jdbcType="VARCHAR" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="agio_type" property="agioType" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="endTime" property="vaildDate" jdbcType="TIMESTAMP" />
    <result column="startTime" property="startDate"/>
    <result column="delete_tag" property="deleteTag"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, name, code, money, threshold, type, vaild_date, vaild_time, `grouping`, reamrk, add_time, category_id, sku,agio_type,status, start_time, start_date,limit_flag,
    task_tag,delete_tag,activity_scope,quantity_claimed,grant_amount,grant_limit, creator
  </sql>

  <!-- 使用金额筛选，对已发放给用户的券不做处理 -->
  <select id="selectByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List" />
    from coupon
    where threshold = money + 0.01 and code = #{code}
    limit 1
  </select>

  <select id="select" resultMap="BaseResultMap">
    select c.id, c.name, c.code, c.money, c.threshold, c.type, c.vaild_date, c.vaild_time, c.`grouping`, c.reamrk, c.add_time, c.category_id, c.sku,c.agio_type,c.status,
    c.start_time, c.start_date,c.limit_flag,c.task_tag,c.delete_tag,sce.endTime,c.activity_scope,c.quantity_claimed,c.grant_amount,c.grant_limit,c.creator
    FROM coupon c
    INNER JOIN (SELECT (CASE WHEN vaild_date IS NULL THEN date_add(startTime, interval vaild_time day) ELSE vaild_date END) AS endTime,startTime,id
      FROM (SELECT (CASE WHEN start_date IS NULL THEN IFNULL(date_add(add_time, interval start_time day),add_time) ELSE start_date END) AS startTime,vaild_date,vaild_time,id
          FROM coupon) sc) sce ON sce.id = c.id
    <where>
      <if test="id!=null">
        and c.id = #{id}
      </if>
      <if test="code != null and code !=''" >
        AND c.code LIKE CONCAT(#{code},'%')
      </if>
      <if test="name != null and name !=''">
        AND c.name LIKE concat(#{name},'%')
      </if>
      <if test="money != null">
        AND c.money = #{money}
      </if>
      <if test="threshold != null">
        AND c.threshold = #{threshold}
      </if>
      <if test="type != null">
        AND c.type = #{type}
      </if>
      <if test="grouping != null">
        AND c.grouping = #{grouping}
      </if>
      <if test="agioType != null">
        AND c.agio_type = #{agioType}
      </if>
      <if test="status != null">
        AND c.status = #{status}
      </if>
      <if test="startTime != null">
        AND c.start_time = #{startTime}
      </if>
      <if test="startDate != null">
        AND c.start_date = #{startDate}
      </if>
      <if test="autoCreated != null">
        AND c.auto_created = #{autoCreated}
      </if>
      <if test="sku != null">
        AND c.sku = #{sku}
      </if>
      <if test="activityScope != null">
        AND c.activity_scope = #{activityScope}
      </if>
      <if test="vaildTime != null">
        AND c.vaild_time = #{vaildTime}
      </if>
      <if test="quantityClaimed != null">
        AND c.quantity_claimed = #{quantityClaimed}
      </if>
      <if test="grantLimit != null">
        AND c.grant_limit = #{grantLimit}
      </if>
      <if test="categoryId != null and categoryId != ''">
        AND c.category_id = #{categoryId}
      </if>
    </where>
    ORDER BY id DESC
  </select>

  <update id="update"  parameterType="net.summerfarm.model.domain.Coupon">
    update coupon
    <set>
      <if test="status != null">
        status=#{status},
      </if>
      <if test="deleteTag != null">
        delete_tag = #{deleteTag},
      </if>
    </set>
    where id=#{id}
  </update>
  
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    /*FORCE_MASTER*/
    select
    <include refid="Base_Column_List" />
    from coupon
    where id = #{id,jdbcType=INTEGER}
  </select>

  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.model.domain.Coupon" >
    insert into coupon (name, code, category_id, sku,
      money, threshold, type, 
      vaild_date, vaild_time, grouping, reamrk,
      add_time,agio_type,status, start_time, start_date,limit_flag,task_tag, activity_scope,
                        quantity_claimed, grant_amount, grant_limit, creator, auto_created)
    values (#{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{categoryId}, #{sku},
      #{money,jdbcType=DECIMAL}, #{threshold,jdbcType=DECIMAL}, #{type,jdbcType=TINYINT}, 
      #{vaildDate,jdbcType=TIMESTAMP}, #{vaildTime,jdbcType=INTEGER}, #{grouping,jdbcType=INTEGER}, #{reamrk,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP},#{agioType},#{status}, #{startTime}, #{startDate},#{limitFlag},#{taskTag}, #{activityScope},
            #{quantityClaimed}, #{grantAmount}, #{grantLimit}, #{creator}, #{autoCreated})
  </insert>

  <insert id="insertAuto" parameterType="net.summerfarm.model.domain.Coupon" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into coupon (name, category_id,sku,money, threshold, type,vaild_time, grouping,add_time,agio_type,status,
                        quantity_claimed, grant_amount, grant_limit, creator)
    values (#{name,jdbcType=VARCHAR}, #{categoryId}, #{sku},#{money,jdbcType=DECIMAL}, #{threshold,jdbcType=DECIMAL}, 0,#{vaildTime,jdbcType=INTEGER}, 0,now(),1,1,
            #{quantityClaimed}, #{grantAmount}, #{grantLimit}, #{creator})
  </insert>

  <update id="updateNot"  parameterType="net.summerfarm.model.domain.Coupon">
    update coupon set status=2 where type=1 and  now() > vaild_date
  </update>

  <select id="selectCoupon" parameterType="net.summerfarm.model.domain.Coupon" resultType="integer">
    select IFNULL(count(1),0)
    from coupon
    where name = #{name} and money = #{money} and threshold = #{threshold} and type = 0 and vaild_time = #{vaildTime} and grouping = 0 and category_id = #{categoryId}
    and status = 1
  </select>

  <select id="selectByCoupon" parameterType="net.summerfarm.model.domain.Coupon" resultType="net.summerfarm.model.domain.Coupon">
    select id
    from coupon
    where name = #{name} and money = #{money} and threshold = #{threshold} and type = 0 and vaild_time = #{vaildTime} and grouping = 0 and category_id = #{categoryId}
    and status = 1
    limit 1
  </select>

<!--auto generated by MybatisCodeHelper on 2021-11-24-->
  <select id="selectByIdIn" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from coupon
    where id in
    <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
    </foreach>
</select>
  <select id="selectTaskList" resultMap="TaskTimeMap">
    SELECT endTime,startTime,id,name,code,money,threshold,type,grouping,reamrk,add_time,category_id,sku,agio_type,status,delete_tag
      FROM (SELECT (CASE WHEN vaild_date IS NULL THEN date_add(startTime, interval vaild_time day) ELSE vaild_date END)
      AS endTime,startTime,id,name,code,money,threshold,type,grouping,reamrk,add_time,category_id,sku,agio_type,status,delete_tag
        FROM (SELECT (CASE WHEN start_date IS NULL THEN IFNULL(date_add(add_time, interval start_time day),add_time) ELSE start_date END)
        AS startTime,vaild_date,vaild_time,id,name,code,money,threshold,type,grouping,reamrk,add_time,category_id,sku,agio_type,status,delete_tag
        FROM coupon WHERE task_tag = 1
        <if test="taskName != null">
          AND name LIKE CONCAT('%',#{taskName} ,'%')
        </if>
        <if test="status != null and status == 3">
          AND delete_tag = 1
        </if>
        <if test="queryType !=null and queryType == 1">
          AND delete_tag = 0
        </if>
        ) sc
      ) esi
    <where>
      <if test="status != null and status == 0">
        AND NOW() <![CDATA[<=]]> startTime
        AND delete_tag = 0
      </if>
      <if test="status != null and status == 1">
        AND NOW() BETWEEN startTime AND endTime
        AND delete_tag = 0
      </if>
      <if test="status != null and status == 2">
        AND NOW() <![CDATA[>=]]> endTime
        AND delete_tag = 0
      </if>
      <if test="startTime != null and endTime != null">
        AND delete_tag = 0
        AND (#{startTime} BETWEEN startTime AND endTime
        OR #{endTime} BETWEEN startTime AND endTime
        OR (#{startTime} <![CDATA[<=]]> startTime AND #{endTime} <![CDATA[>=]]> endTime))
      </if>
    </where>
    ORDER BY id DESC
  </select>
  <update id="taskUpdate">
    update coupon set delete_tag = 1 where id = #{id}
  </update>

  <select id="selectByEntity" resultMap="BaseResultMap" parameterType="net.summerfarm.model.domain.Coupon">
    select
    <include refid="Base_Column_List" />
    from coupon
    <where>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="id != null">
        and id = #{id}
      </if>
    </where>
  </select>

  <update id="updateGrantAmount">
    update coupon
    set grant_amount = grant_amount - #{amount}
    where id = #{id} and grant_amount >= #{amount}
  </update>

  <update id="sendBackGrantAmount">
    update coupon
    set grant_amount = grant_amount + #{amount}
    where id = #{id} and grant_limit > 0
  </update>

  <select id="listByIds" resultMap="BaseResultMap">
    select c.id, c.name, c.code, c.money, c.threshold, c.type, c.vaild_date, c.vaild_time, c.`grouping`, c.reamrk, c.add_time, c.category_id, c.sku,c.agio_type,c.status,
           c.start_time, c.start_date,c.limit_flag,c.task_tag,c.delete_tag,sce.endTime,c.activity_scope
    FROM coupon c
           INNER JOIN (SELECT (CASE WHEN vaild_date IS NULL THEN date_add(startTime, interval vaild_time day) ELSE vaild_date END) AS endTime,startTime,id
                       FROM (SELECT (CASE WHEN start_date IS NULL THEN IFNULL(date_add(add_time, interval start_time day),add_time) ELSE start_date END) AS startTime,vaild_date,vaild_time,id
                             FROM coupon) sc) sce ON sce.id = c.id
    <where>
      <if test=" list != null and list.size > 0">
        c.id in
        <foreach collection="list" open="(" separator="," close=")" item="id">
          #{id}
        </foreach>
      </if>
    </where>

  </select>
</mapper>