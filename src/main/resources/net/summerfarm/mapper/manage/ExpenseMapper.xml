<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ExpenseMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.Expense">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="delivery_path_id" jdbcType="INTEGER" property="deliveryPathId" />
    <result column="delivery_time" jdbcType="INTEGER" property="deliveryTime" />
    <result column="state" jdbcType="VARCHAR" property="state" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="store_no" jdbcType="VARCHAR" property="storeNo" />
    <result column="is_review" jdbcType="VARCHAR" property="isReview" />
    <result column="m_id" jdbcType="INTEGER" property="mId" />
    <result column="mname" jdbcType="VARCHAR" property="mname" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, driver_id, delivery_path_id,delivery_time, state, type, store_no, is_review isReview, m_id,mname, updater, update_time, create_time,status
  </sql>
  <update id="updateExpense">
    update expense
    set status = #{status},
        reason = #{reason}
    where id = #{id}

  </update>

  <select id="select"  resultType="net.summerfarm.model.vo.ExpenseVO">
    select
    e.id, driver_id driverId, delivery_path_id deliveryPathId,delivery_time deliveryTime, state, e.store_no storeNo,w.store_name storeName, m_id mId,mname, e.updater, e.update_time updateTime,e.creator,e.create_time createTime,type,is_review isReview
    ,e.status,e.reason
    from expense e
    LEFT JOIN warehouse_logistics_center w on w.store_no = e.store_no
    <where>
    <if test="status!=null" >
      and e.status = #{status}
    </if>
    <if test="id != null">
      AND e.id = #{id}
    </if>
    <if test="storeNo !=null">
      and e.store_no = #{storeNo}
    </if>
    <if test="createTime != null">
      AND e.create_time = #{createTime}
    </if>
    <if test="mname != null">
      AND e.mname like CONCAT('%', #{mname},'%')
    </if>
      <if test="isReview != null">
        AND e.is_review = #{isReview}
      </if>
      <if test="deliveryTime != null and deliveryEndTime != null">
        and e.delivery_time <![CDATA[>=]]> #{deliveryTime}
        and e.delivery_time <![CDATA[<=]]> #{deliveryEndTime}
      </if>
      <if test="deliveryTime != null and deliveryEndTime == null">
        and e.delivery_time = #{deliveryTime}
      </if>
      <if test="typeList != null and typeList.size != 0">
        and e.type in
        <foreach collection="typeList" open="(" close=")" separator="," item="it">
          #{it}
        </foreach>
      </if>
    </where>
    order by e.create_time desc
  </select>

  <select id="selectExport" parameterType="net.summerfarm.model.domain.Expense" resultType="net.summerfarm.model.vo.ExpenseDetailVO">
    select
    e.delivery_path_id deliveryPathId,
    w.store_name storeName,
    e.delivery_time deliveryTime,
    e.driver_id driverId,
    ed.type,
    ed.is_review isReview,
    e.mname,
    ed.amount,
    ed.remark,
    e.create_time createTime,
    ed.mileage,
    ed.creator,
    ed.start_address startAddress,
    ed.end_address endAddress,
    e.status
    from expense e
    LEFT JOIN expense_detail ed on e.id = ed.expense_id
    LEFT JOIN warehouse_logistics_center w on w.store_no = e.store_no
    <where>
      <if test="status !=null">
        and e.status = #{status}
      </if>
      <if test="storeNo !=null">
        and e.store_no = #{storeNo}
      </if>
      <if test="createTime != null">
        AND e.create_time = #{createTime}
      </if>
      <if test="isReview != null">
        AND e.is_review = #{isReview}
      </if>
      <if test="typeList != null and typeList.size != 0">
        and e.type in
        <foreach collection="typeList" open="(" close=")" separator="," item="it">
          #{it}
        </foreach>
      </if>
      <if test="mname != null">
        AND e.mname like CONCAT('%', #{mname},'%')
      </if>
      <if test="deliveryTime != null and deliveryEndTime != null">
        and e.delivery_time <![CDATA[>=]]> #{deliveryTime}
        and e.delivery_time <![CDATA[<=]]> #{deliveryEndTime}
      </if>
      <if test="deliveryTime != null and deliveryEndTime == null">
        and e.delivery_time = #{deliveryTime}
      </if>
    </where>
  </select>
  <select id="selectByCondition" resultType="net.summerfarm.model.vo.ExpenseVO">
    select ed.amount,ed.type,ed.state from expense e
     left join expense_detail ed on e.id = ed.expense_id
     inner join delivery_path dp on delivery_path_id = dp.id
    where e.store_no = #{storeNo} and e.delivery_time = #{time} and e.driver_id = #{deliveryCarId} and dp.path =#{path} and status = 1
  </select>
  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from expense
    where id = #{id}

  </select>

</mapper>