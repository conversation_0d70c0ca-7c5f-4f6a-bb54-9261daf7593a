<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockAllocationConfigRecordMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.model.domain.StockAllocationConfigRecord" >
        insert into stock_allocation_config_record
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="configId != null" >
                config_id,
            </if>
            <if test="listNo != null" >
                list_no,
            </if>
            <if test="warehouseNo != null" >
                warehouse_no,
            </if>
            <if test="allocationWarehouseNo != null" >
                allocation_warehouse_no,
            </if>
            <if test="cycleType != null" >
                cycle_type,
            </if>
            <if test="logisticsTime != null">
                `logistics_time`,
            </if>
            <if test="nextDayArrive != null">
                next_day_arrive,
            </if>
            <if test="salePartake != null">
                sale_partake,
            </if>
            <if test="creator !=null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="intervalDays != null">
                interval_days,
            </if>
            <if test="saleQuantityStart != null">
                sale_quantity_start,
            </if>
            <if test="saleQuantityEnd != null">
                sale_quantity_end,
            </if>
            <if test="purchasePartake != null">
                purchase_partake,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="configId != null" >
                #{configId,jdbcType=VARCHAR},
            </if>
            <if test="listNo != null" >
                #{listNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null" >
                #{warehouseNo,jdbcType=INTEGER},
            </if>
            <if test="allocationWarehouseNo != null" >
                #{allocationWarehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="cycleType != null" >
                #{cycleType,jdbcType=VARCHAR},
            </if>
            <if test="logisticsTime != null">
                #{logisticsTime},
            </if>
            <if test="nextDayArrive != null">
                #{nextDayArrive},
            </if>
            <if test="salePartake != null">
                #{salePartake},
            </if>
            <if test="creator !=null">
                #{creator},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="intervalDays != null">
                #{intervalDays},
            </if>
            <if test="saleQuantityStart != null">
                #{saleQuantityStart},
            </if>
            <if test="saleQuantityEnd != null">
                #{saleQuantityEnd},
            </if>
            <if test="purchasePartake != null">
                #{purchasePartake},
            </if>
        </trim>
    </insert>
    <select id="selectOne" resultType="net.summerfarm.model.domain.StockAllocationConfigRecord">
    select sale_quantity_start saleQuantityStart,sale_quantity_end saleQuantityEnd,cycle_type cycleType,next_day_arrive nextDayArrive,interval_days intervalDays,sale_partake salePartake,purchase_partake purchasePartake,logistics_time logisticsTime from stock_allocation_config_record sacr where list_no=#{taskNo} limit 1
    </select>

</mapper>