<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceBankFlowingWaterMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceBankFlowingWater">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="trading_day" jdbcType="VARCHAR" property="tradingDay" />
    <result column="trading_time" jdbcType="VARCHAR" property="tradingTime" />
    <result column="value_date" jdbcType="VARCHAR" property="valueDate" />
    <result column="trading_type" jdbcType="VARCHAR" property="tradingType" />
    <result column="abstract_text" jdbcType="VARCHAR" property="abstractText" />
    <result column="transaction_amount" jdbcType="DECIMAL" property="transactionAmount" />
    <result column="mark" jdbcType="VARCHAR" property="mark" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="process_instance_number" jdbcType="VARCHAR" property="processInstanceNumber" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="purpose" jdbcType="VARCHAR" property="purpose" />
    <result column="business_reference_number" jdbcType="VARCHAR" property="businessReferenceNumber" />
    <result column="business_summary" jdbcType="VARCHAR" property="businessSummary" />
    <result column="other_summaries" jdbcType="VARCHAR" property="otherSummaries" />
    <result column="bank_area_no" jdbcType="VARCHAR" property="bankAreaNo" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="bank_no" jdbcType="VARCHAR" property="bankNo" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_address" jdbcType="VARCHAR" property="bankAddress" />
    <result column="company_division" jdbcType="VARCHAR" property="companyDivision" />
    <result column="company_account" jdbcType="VARCHAR" property="companyAccount" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="information_signs" jdbcType="VARCHAR" property="informationSigns" />
    <result column="information_existence" jdbcType="VARCHAR" property="informationExistence" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="reversal_flag" jdbcType="VARCHAR" property="reversalFlag" />
    <result column="extended_summary" jdbcType="VARCHAR" property="extendedSummary" />
    <result column="transaction_analysis_code" jdbcType="VARCHAR" property="transactionAnalysisCode" />
    <result column="payment_order_no" jdbcType="VARCHAR" property="paymentOrderNo" />
    <result column="enterprise_identification_code" jdbcType="VARCHAR" property="enterpriseIdentificationCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="claim_status" jdbcType="INTEGER" property="claimStatus" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, trading_day, trading_time, value_date, trading_type, abstract_text, transaction_amount,
    mark, serial_number, process_instance_number, business_name, purpose, business_reference_number, 
    business_summary, other_summaries, bank_area_no, user_name, account_number, bank_no, 
    bank_name, bank_address, company_division, company_account, company_name, information_signs, 
    information_existence, bill_no, reversal_flag, extended_summary, transaction_analysis_code, 
    payment_order_no, enterprise_identification_code, create_time, update_time, updater, 
    claim_status,pay_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_bank_flowing_water
    where id = #{id,jdbcType=BIGINT} and mark = 'C'
  </select>

  <select id="selectBySerialNumber" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_bank_flowing_water
    where serial_number = #{serialNumber}
  </select>

  <select id="selectList" parameterType="net.summerfarm.model.input.FinanceBankFlowingWaterInput" resultType="net.summerfarm.model.vo.FinanceBankFlowingWaterVO">
    select
    id, trading_day tradingDay, trading_time tradingTime, value_date valueDate, trading_type tradingType, abstract_text abstractText, transaction_amount transactionAmount,
    mark, serial_number serialNumber, process_instance_number processInstanceNumber, business_name businessName, purpose, business_reference_number businessReferenceNumber,
    business_summary businessSummary, other_summaries otherSummaries, bank_area_no bankAreaNo, user_name userName, account_number accountNumber, bank_no bankNo,
    bank_name bankName, bank_address bankAddress, company_division companyDivision, company_account companyAccount, company_name companyName, information_signs informationSigns,
    information_existence informationExistence, bill_no billNo, reversal_flag reversalFlag, extended_summary extendedSummary, transaction_analysis_code transactionAnalysisCode,
    payment_order_no paymentOrderNo, enterprise_identification_code enterpriseIdentificationCode, create_time createTime, update_time updateTime, updater,
    claim_status claimStatus,pay_type payType
    from finance_bank_flowing_water
    <where>
      mark = 'C'
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="serialNumber != null and serialNumber!='' ">
        and serial_number = #{serialNumber}
      </if>
      <if test="userName != null and userName!='' and payType==null ">
        and user_name LIKE CONCAT(#{userName},'%')
      </if>
      <if test="userName != null and userName!='' and  payType!=null ">
        and user_name = #{userName}
      </if>
      <if test="startTime !=null and endTime != null">
        and create_time <![CDATA[>=]]> #{startTime}
        and create_time <![CDATA[<=]]> #{endTime}
      </if>
      <if test="claimStatus != null">
        and claim_status = #{claimStatus}
      </if>
      <if test="claimStatusList != null and statusList != null">
        and claim_status in
        <foreach collection="statusList" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
      <if test="payType!=null">
        and (pay_type=#{payType}
        <if test="emptyFlag==null or emptyFlag==0">
          or pay_type is null
        </if>)
      </if>
      <if test="tradingDay!=null">
        and to_days(trading_day) = to_days(#{tradingDay})
      </if>
      <if test="transactionPayerQueryBlacklist != null and transactionPayerQueryBlacklist.size() != 0">
        and user_name not in
        <foreach collection="transactionPayerQueryBlacklist" open="(" close=")" separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
    order by trading_day desc
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_bank_flowing_water
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceBankFlowingWater" useGeneratedKeys="true">
    insert into finance_bank_flowing_water (trading_day, trading_time, value_date, 
      trading_type, abstract_text, transaction_amount,
      mark, serial_number, process_instance_number, 
      business_name, purpose, business_reference_number, 
      business_summary, other_summaries, bank_area_no, 
      user_name, account_number, bank_no, 
      bank_name, bank_address, company_division, 
      company_account, company_name, information_signs, 
      information_existence, bill_no, reversal_flag, 
      extended_summary, transaction_analysis_code, 
      payment_order_no, enterprise_identification_code, 
      create_time, update_time, updater, 
      claim_status)
    values (#{tradingDay,jdbcType=VARCHAR}, #{tradingTime,jdbcType=VARCHAR}, #{valueDate,jdbcType=VARCHAR}, 
      #{tradingType,jdbcType=VARCHAR}, #{abstract_text,jdbcType=VARCHAR}, #{transactionAmount,jdbcType=DECIMAL},
      #{mark,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR}, #{processInstanceNumber,jdbcType=VARCHAR}, 
      #{businessName,jdbcType=VARCHAR}, #{purpose,jdbcType=VARCHAR}, #{businessReferenceNumber,jdbcType=VARCHAR}, 
      #{businessSummary,jdbcType=VARCHAR}, #{otherSummaries,jdbcType=VARCHAR}, #{bankAreaNo,jdbcType=VARCHAR}, 
      #{userName,jdbcType=VARCHAR}, #{accountNumber,jdbcType=VARCHAR}, #{bankNo,jdbcType=VARCHAR}, 
      #{bankName,jdbcType=VARCHAR}, #{bankAddress,jdbcType=VARCHAR}, #{companyDivision,jdbcType=VARCHAR}, 
      #{companyAccount,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{informationSigns,jdbcType=VARCHAR}, 
      #{informationExistence,jdbcType=VARCHAR}, #{billNo,jdbcType=VARCHAR}, #{reversalFlag,jdbcType=VARCHAR}, 
      #{extendedSummary,jdbcType=VARCHAR}, #{transactionAnalysisCode,jdbcType=VARCHAR}, 
      #{paymentOrderNo,jdbcType=VARCHAR}, #{enterpriseIdentificationCode,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, 
      #{claimStatus})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceBankFlowingWater" useGeneratedKeys="true">
    insert into finance_bank_flowing_water
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tradingDay != null">
        trading_day,
      </if>
      <if test="tradingTime != null">
        trading_time,
      </if>
      <if test="valueDate != null">
        value_date,
      </if>
      <if test="tradingType != null">
        trading_type,
      </if>
      <if test="abstractText != null">
        abstract_text,
      </if>
      <if test="transactionAmount != null">
        transaction_amount,
      </if>
      <if test="mark != null">
        mark,
      </if>
      <if test="serialNumber != null">
        serial_number,
      </if>
      <if test="processInstanceNumber != null">
        process_instance_number,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="purpose != null">
        purpose,
      </if>
      <if test="businessReferenceNumber != null">
        business_reference_number,
      </if>
      <if test="businessSummary != null">
        business_summary,
      </if>
      <if test="otherSummaries != null">
        other_summaries,
      </if>
      <if test="bankAreaNo != null">
        bank_area_no,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="accountNumber != null">
        account_number,
      </if>
      <if test="bankNo != null">
        bank_no,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankAddress != null">
        bank_address,
      </if>
      <if test="companyDivision != null">
        company_division,
      </if>
      <if test="companyAccount != null">
        company_account,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="informationSigns != null">
        information_signs,
      </if>
      <if test="informationExistence != null">
        information_existence,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="reversalFlag != null">
        reversal_flag,
      </if>
      <if test="extendedSummary != null">
        extended_summary,
      </if>
      <if test="transactionAnalysisCode != null">
        transaction_analysis_code,
      </if>
      <if test="paymentOrderNo != null">
        payment_order_no,
      </if>
      <if test="enterpriseIdentificationCode != null">
        enterprise_identification_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="claimStatus != null">
        claim_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tradingDay != null">
        #{tradingDay,jdbcType=VARCHAR},
      </if>
      <if test="tradingTime != null">
        #{tradingTime,jdbcType=VARCHAR},
      </if>
      <if test="valueDate != null">
        #{valueDate,jdbcType=VARCHAR},
      </if>
      <if test="tradingType != null">
        #{tradingType,jdbcType=VARCHAR},
      </if>
      <if test="abstractText != null">
        #{abstractText,jdbcType=VARCHAR},
      </if>
      <if test="transactionAmount != null">
        #{transactionAmount,jdbcType=DECIMAL},
      </if>
      <if test="mark != null">
        #{mark,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="processInstanceNumber != null">
        #{processInstanceNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="purpose != null">
        #{purpose,jdbcType=VARCHAR},
      </if>
      <if test="businessReferenceNumber != null">
        #{businessReferenceNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessSummary != null">
        #{businessSummary,jdbcType=VARCHAR},
      </if>
      <if test="otherSummaries != null">
        #{otherSummaries,jdbcType=VARCHAR},
      </if>
      <if test="bankAreaNo != null">
        #{bankAreaNo,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null">
        #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="bankNo != null">
        #{bankNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAddress != null">
        #{bankAddress,jdbcType=VARCHAR},
      </if>
      <if test="companyDivision != null">
        #{companyDivision,jdbcType=VARCHAR},
      </if>
      <if test="companyAccount != null">
        #{companyAccount,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="informationSigns != null">
        #{informationSigns,jdbcType=VARCHAR},
      </if>
      <if test="informationExistence != null">
        #{informationExistence,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="reversalFlag != null">
        #{reversalFlag,jdbcType=VARCHAR},
      </if>
      <if test="extendedSummary != null">
        #{extendedSummary,jdbcType=VARCHAR},
      </if>
      <if test="transactionAnalysisCode != null">
        #{transactionAnalysisCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentOrderNo != null">
        #{paymentOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseIdentificationCode != null">
        #{enterpriseIdentificationCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        #{claimStatus},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceBankFlowingWater">
    update finance_bank_flowing_water
    <set>
      <if test="tradingDay != null">
        trading_day = #{tradingDay,jdbcType=VARCHAR},
      </if>
      <if test="tradingTime != null">
        trading_time = #{tradingTime,jdbcType=VARCHAR},
      </if>
      <if test="valueDate != null">
        value_date = #{valueDate,jdbcType=VARCHAR},
      </if>
      <if test="tradingType != null">
        trading_type = #{tradingType,jdbcType=VARCHAR},
      </if>
      <if test="abstractText != null">
        abstract_text = #{abstract_text,jdbcType=VARCHAR},
      </if>
      <if test="transactionAmount != null">
        transaction_amount = #{transactionAmount,jdbcType=DECIMAL},
      </if>
      <if test="mark != null">
        mark = #{mark,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="processInstanceNumber != null">
        process_instance_number = #{processInstanceNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="purpose != null">
        purpose = #{purpose,jdbcType=VARCHAR},
      </if>
      <if test="businessReferenceNumber != null">
        business_reference_number = #{businessReferenceNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessSummary != null">
        business_summary = #{businessSummary,jdbcType=VARCHAR},
      </if>
      <if test="otherSummaries != null">
        other_summaries = #{otherSummaries,jdbcType=VARCHAR},
      </if>
      <if test="bankAreaNo != null">
        bank_area_no = #{bankAreaNo,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="bankNo != null">
        bank_no = #{bankNo,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAddress != null">
        bank_address = #{bankAddress,jdbcType=VARCHAR},
      </if>
      <if test="companyDivision != null">
        company_division = #{companyDivision,jdbcType=VARCHAR},
      </if>
      <if test="companyAccount != null">
        company_account = #{companyAccount,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="informationSigns != null">
        information_signs = #{informationSigns,jdbcType=VARCHAR},
      </if>
      <if test="informationExistence != null">
        information_existence = #{informationExistence,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="reversalFlag != null">
        reversal_flag = #{reversalFlag,jdbcType=VARCHAR},
      </if>
      <if test="extendedSummary != null">
        extended_summary = #{extendedSummary,jdbcType=VARCHAR},
      </if>
      <if test="transactionAnalysisCode != null">
        transaction_analysis_code = #{transactionAnalysisCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentOrderNo != null">
        payment_order_no = #{paymentOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseIdentificationCode != null">
        enterprise_identification_code = #{enterpriseIdentificationCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        claim_status = #{claimStatus},
      </if>
      <if test="payType != null">
        pay_type = #{payType},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceBankFlowingWater">
    update finance_bank_flowing_water
    set
      updater = #{updater},
      pay_type = #{payType},
      claim_status = #{claimStatus}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>