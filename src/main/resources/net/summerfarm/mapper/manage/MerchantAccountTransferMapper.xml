<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MerchantAccountTransferMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.crm.MerchantAccountTransfer">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="mname" jdbcType="VARCHAR" property="mname" />
    <result column="transfer_m_id" jdbcType="BIGINT" property="transferMId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="area_no" jdbcType="BIGINT" property="areaNo" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="addr" jdbcType="VARCHAR" property="addr" />
    <result column="bd_name" jdbcType="VARCHAR" property="bdName" />
    <result column="transfer_mname" jdbcType="VARCHAR" property="transferMname" />
    <result column="transfer_bd_name" jdbcType="VARCHAR" property="transferBdName" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="transfer_phone" jdbcType="VARCHAR" property="transferPhone" />

  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, m_id, mname, transfer_m_id, operator_name, area_no, 
    area_name, remark, addr, bd_name, transfer_mname, transfer_bd_name,transfer_phone,phone
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_account_transfer
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="select" parameterType="net.summerfarm.model.input.merchant.TransferMerchantQueryInput" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from merchant_account_transfer
    <where>
      <if test="mName != null">
        and mname like CONCAT (#{mName} ,'%')
      </if>
      <if test="mId != null">
        and m_id = #{mId}
      </if>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="transferMids != null and transferMids.size > 0">
        and  transfer_m_id in
        <foreach collection="transferMids" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
      </if>
    </where>
    order by create_time desc
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_account_transfer
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.crm.MerchantAccountTransfer" useGeneratedKeys="true">
    insert into merchant_account_transfer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="mname != null">
        mname,
      </if>
      <if test="transferMId != null">
        transfer_m_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="addr != null">
        addr,
      </if>
      <if test="bdName != null">
        bd_name,
      </if>
      <if test="transferMname != null">
        transfer_mname,
      </if>
      <if test="transferBdName != null">
        transfer_bd_name,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="transferPhone != null">
        transfer_phone,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="mname != null">
        #{mname,jdbcType=VARCHAR},
      </if>
      <if test="transferMId != null">
        #{transferMId,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=BIGINT},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addr != null">
        #{addr,jdbcType=VARCHAR},
      </if>
      <if test="bdName != null">
        #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="transferMname != null">
        #{transferMname,jdbcType=VARCHAR},
      </if>
      <if test="transferBdName != null">
        #{transferBdName,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="transferPhone != null">
        #{transferPhone,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

</mapper>