<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockInspectDetailMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.StockInspectDetail">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="stock_task_process_id" property="stockTaskProcessId" jdbcType="INTEGER"/>
        <result column="area_no" property="areaNo" jdbcType="VARCHAR"/>
        <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="storage_location" property="storageLocation" jdbcType="INTEGER"/>
        <result column="storage_method" property="storageMethod" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="quality_time" property="qualityTime" jdbcType="INTEGER"/>
        <result column="quality_time_unit" property="qualityTimeUnit" jdbcType="VARCHAR"/>
        <result column="supplier" property="supplier" jdbcType="VARCHAR"/>
        <result column="supplier_id" property="supplierId" jdbcType="INTEGER"/>
        <result column="batch" property="batch" jdbcType="VARCHAR"/>
        <result column="quantity" property="quantity"/>
        <result column="in_quantity" property="inQuantity"/>
        <result column="check_quantity" property="checkQuantity"/>
        <result column="qualified_quantity" property="qualifiedQuantity" jdbcType="INTEGER"/>
        <result column="photos" property="photos" jdbcType="VARCHAR"/>
        <result column="evaluate" property="evaluate"/>
        <result column="status" property="status"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="damage_quantity" property="damageQuantity"/>
        <result column="inspect_persons" property="inspectPersons"/>
        <result column="receipt_method" property="receiptMethod"/>
        <result column="audit_proof" property="auditProof"/>
        <result column="pick_num" property="pickNum"/>
        <result column="failed_num" property="failedNum"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , sku, stock_task_process_id, area_no, pd_name, weight, type, storage_location,storage_method,unit, quality_time, quality_time_unit, supplier, supplier_id, batch, quantity,
    in_quantity,  check_quantity, qualified_quantity, photos, evaluate, status, updater, update_time, creator, create_time,damage_quantity ,inspect_persons,
    receipt_method,audit_proof,pick_num,failed_num
    </sql>

    <select id="select" resultType="net.summerfarm.model.vo.StockInspectDetailVO">
        select
        sid.id, sid.sku, sid.stock_task_process_id stockTaskProcessId, sid.area_no areaNo, sid.pd_name pdName,
        sid.weight, sid.type, sid.storage_location storageLocation,sid.storage_method storageMethod,sid.unit,
        sid.quality_time qualityTime, sid.quality_time_unit qualityTimeUnit,
        sid.supplier, sid.supplier_id supplierId, sid.batch, sid.quantity,
        sid.in_quantity inQuantity, sid.check_quantity checkQuantity, sid.qualified_quantity qualifiedQuantity,
        sid.photos, sid.evaluate, sid.status, sid.updater,
        sid.update_time updateTime, sid.creator, sid.create_time createTime
        from stock_inspect_detail sid
        <where>
            <if test="areaNo != null">
                and sid.area_no = #{areaNo,jdbcType=INTEGER}
            </if>
            <if test="stockTaskProcessId != null">
                and sid.stock_task_process_id = #{stockTaskProcessId}
            </if>
            <if test="sku != null">
                and sid.sku = #{sku,jdbcType=VARCHAR}
            </if>
            <if test="querySkus != null and querySkus.size > 0">
                and sid.sku in
                <foreach collection="querySkus" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="batch != null">
                and sid.batch = #{batch,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and sid.status = #{status,jdbcType=INTEGER}
            </if>
            <if test="taskStartTime!=null">
                and sid.create_time >=#{taskStartTime}
            </if>
            <if test="taskEndTime!=null">
                and sid.create_time &lt;= #{taskEndTime}
            </if>
        </where>
        ORDER by sid.status asc , sid.create_time desc
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stock_inspect_detail
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stock_inspect_detail
        where status = 0 and stock_task_process_id = #{stockTaskProcessId} and sku = #{sku}
    </select>

    <select id="selectByCondition" parameterType="net.summerfarm.model.domain.StockInspectDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stock_inspect_detail
        <where>
            <if test="sku != null">
                and sku = #{sku,jdbcType=VARCHAR}
            </if>
            <if test="supplierId != null">
                and supplier_id = #{supplierId,jdbcType=INTEGER}
            </if>
            <if test="areaNo != null">
                and area_no = #{areaNo,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <update id="update" parameterType="net.summerfarm.model.domain.StockInspectDetail">
        update stock_inspect_detail
        <set>
            <if test="checkQuantity != null">
                check_quantity = #{checkQuantity},
            </if>
            <if test="qualifiedQuantity != null">
                qualified_quantity = #{qualifiedQuantity},
            </if>
            <if test="failedNum != null">
                failed_num = #{failedNum},
            </if>
            <if test="auditProof != null">
                audit_proof = #{auditProof},
            </if>
            <if test="receiptMethod != null">
                receipt_method = #{receiptMethod},
            </if>
            <if test="pickNum != null">
                pick_num = #{pickNum},
            </if>
            <if test="photos != null">
                photos = #{photos},
            </if>
            <if test="evaluate != null">
                evaluate = #{evaluate},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="updater != null">
                updater = #{updater},
            </if>
            <if test="damageQuantity != null">
                damage_quantity = #{damageQuantity},
            </if>
            <if test="inspectPersons != null">
                inspect_persons = #{inspectPersons},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="insert" parameterType="net.summerfarm.model.domain.StockInspectDetail">
        insert into stock_inspect_detail (sku, stock_task_process_id, area_no, pd_name, weight, type,
                                          storage_location, storage_method, unit, quality_time, quality_time_unit,
                                          supplier, supplier_id, batch, quantity,
                                          in_quantity, check_quantity, qualified_quantity, photos, evaluate, status,
                                          updater, update_time,
                                          creator, create_time, tenant_id)
        values (#{sku,jdbcType=INTEGER}, #{stockTaskProcessId,jdbcType=INTEGER}, #{areaNo,jdbcType=INTEGER},
                #{pdName,jdbcType=INTEGER}, #{weight,jdbcType=DECIMAL}, #{type,jdbcType=DECIMAL},
                #{storageLocation,jdbcType=INTEGER}, #{storageMethod,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR},
                #{qualityTime,jdbcType=DECIMAL}, #{qualityTimeUnit,jdbcType=VARCHAR}, #{supplier,jdbcType=VARCHAR},
                #{supplierId,jdbcType=INTEGER}, #{batch,jdbcType=VARCHAR}, #{quantity,jdbcType=INTEGER},
                #{inQuantity,jdbcType=INTEGER},
                #{checkQuantity,jdbcType=INTEGER}, #{qualifiedQuantity,jdbcType=INTEGER}, #{photos,jdbcType=TIMESTAMP},
                #{evaluate,jdbcType=TIMESTAMP},
                #{status,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{tenantId})
    </insert>
</mapper>
