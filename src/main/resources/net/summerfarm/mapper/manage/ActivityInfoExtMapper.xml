<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ActivityInfoExtMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.market.ActivityInfoExt">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="basic_info_id" jdbcType="BIGINT" property="basicInfoId" />
    <result column="limit_type" jdbcType="TINYINT" property="limitType" />
    <result column="limit_num" jdbcType="INTEGER" property="limitNum" />
    <result column="rule_detail" jdbcType="VARCHAR" property="ruleDetail" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `basic_info_id`, `limit_type`, `limit_num`, `rule_detail`, `updater_id`, `del_flag`, 
    `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from activity_info_ext
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from activity_info_ext
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.market.ActivityInfoExt">
    insert into activity_info_ext (`id`, `basic_info_id`, `limit_type`, 
      `limit_num`, `rule_detail`, `updater_id`, 
      `del_flag`, `create_time`, `update_time`
      )
    values (#{id,jdbcType=BIGINT}, #{basicInfoId,jdbcType=BIGINT}, #{limitType,jdbcType=TINYINT}, 
      #{limitNum,jdbcType=INTEGER}, #{ruleDetail,jdbcType=VARCHAR}, #{updaterId,jdbcType=INTEGER}, 
      #{delFlag,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.market.ActivityInfoExt">
    insert into activity_info_ext
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="basicInfoId != null">
        `basic_info_id`,
      </if>
      <if test="limitType != null">
        `limit_type`,
      </if>
      <if test="limitNum != null">
        `limit_num`,
      </if>
      <if test="ruleDetail != null">
        `rule_detail`,
      </if>
      <if test="updaterId != null">
        `updater_id`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="basicInfoId != null">
        #{basicInfoId,jdbcType=BIGINT},
      </if>
      <if test="limitType != null">
        #{limitType,jdbcType=TINYINT},
      </if>
      <if test="limitNum != null">
        #{limitNum,jdbcType=INTEGER},
      </if>
      <if test="ruleDetail != null">
        #{ruleDetail,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.market.ActivityInfoExt">
    update activity_info_ext
    <set>
      <if test="basicInfoId != null">
        `basic_info_id` = #{basicInfoId,jdbcType=BIGINT},
      </if>
      <if test="limitType != null">
        `limit_type` = #{limitType,jdbcType=TINYINT},
      </if>
      <if test="limitNum != null">
        `limit_num` = #{limitNum,jdbcType=INTEGER},
      </if>
      <if test="ruleDetail != null">
        `rule_detail` = #{ruleDetail,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        `updater_id` = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.market.ActivityInfoExt">
    update activity_info_ext
    set `basic_info_id` = #{basicInfoId,jdbcType=BIGINT},
      `limit_type` = #{limitType,jdbcType=TINYINT},
      `limit_num` = #{limitNum,jdbcType=INTEGER},
      `rule_detail` = #{ruleDetail,jdbcType=VARCHAR},
      `updater_id` = #{updaterId,jdbcType=INTEGER},
      `del_flag` = #{delFlag,jdbcType=TINYINT},
      `create_time` = #{createTime,jdbcType=TIMESTAMP},
      `update_time` = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>
</mapper>