<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.StockTaskWaveSkuOccupyMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.wms.StockTaskWaveSkuOccupyDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="stock_task_id" jdbcType="BIGINT" property="stockTaskId" />
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="occupy_quantity" jdbcType="INTEGER" property="occupyQuantity" />
        <result column="not_occupy_quantity" jdbcType="INTEGER" property="notOccupyQuantity" />
        <result column="remain_occupy_quantity" jdbcType="INTEGER" property="remainOccupyQuantity" />
        <result column="remain_not_occupy_quantity" jdbcType="INTEGER" property="remainNotOccupyQuantity" />

    </resultMap>
    <sql id="Base_Column_List">
        id, create_time, update_time, stock_task_id, warehouse_no, sku,
            occupy_quantity, not_occupy_quantity, remain_occupy_quantity, remain_not_occupy_quantity
    </sql>

    <insert id="insert" parameterType="net.summerfarm.model.domain.wms.StockTaskWaveSkuOccupyDO">
        insert into wms_stock_task_wave_sku_occupy (create_time, update_time, stock_task_id, warehouse_no, sku,
                                                        occupy_quantity, not_occupy_quantity,
                                                        remain_occupy_quantity, remain_not_occupy_quantity)
        values (now(), now(), #{stockTaskId}, #{warehouseNo}, #{sku},
                #{occupyQuantity}, #{notOccupyQuantity},
                #{remainOccupyQuantity}, #{remainNotOccupyQuantity})
    </insert>


    <insert id="insertList" parameterType="net.summerfarm.model.domain.wms.StockTaskWaveSkuOccupyDO"
            useGeneratedKeys="true">
        insert into wms_stock_task_wave_sku_occupy (create_time, update_time, stock_task_id, warehouse_no, sku,
        occupy_quantity, not_occupy_quantity,
        remain_occupy_quantity, remain_not_occupy_quantity)
        VALUES
        <foreach collection="recordList" item="i" index="index" separator=",">
            (now(), now(), #{i.stockTaskId}, #{i.warehouseNo}, #{i.sku},
             #{i.occupyQuantity}, #{i.notOccupyQuantity},
            #{i.remainOccupyQuantity}, #{i.remainNotOccupyQuantity})
        </foreach>
    </insert>

    <select id="selectListByStockTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wms_stock_task_wave_sku_occupy
        where stock_task_id = #{stockTaskId,jdbcType=INTEGER}
        <if test="skuList != null and skuList.size != 0">
            and sku in
            <foreach collection="skuList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updatePickChange">
        update wms_stock_task_wave_sku_occupy
        set remain_occupy_quantity = remain_occupy_quantity - #{remainOccupyQuantityReduce},
            remain_not_occupy_quantity = remain_not_occupy_quantity - #{remainNotOccupyQuantityReduce}
        where id = #{id}
          and remain_occupy_quantity >= #{remainOccupyQuantityReduce}
          and remain_not_occupy_quantity >= #{remainNotOccupyQuantityReduce}
    </update>
</mapper>