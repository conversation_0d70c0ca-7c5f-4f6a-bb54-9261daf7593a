<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ProductsPropertyMappingMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.ProductsPropertyMapping">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="mapping_id" jdbcType="INTEGER" property="mappingId" />
    <result column="products_property_id" jdbcType="INTEGER" property="productsPropertyId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `type`, mapping_id, products_property_id, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from products_property_mapping
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from products_property_mapping
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.ProductsPropertyMapping" useGeneratedKeys="true">
    insert into products_property_mapping (`type`, mapping_id, products_property_id,
      creator, create_time)
    values (#{type,jdbcType=INTEGER}, #{mappingId,jdbcType=INTEGER}, #{productsPropertyId,jdbcType=INTEGER},
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.ProductsPropertyMapping" useGeneratedKeys="true">
    insert into products_property_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        `type`,
      </if>
      <if test="mappingId != null">
        mapping_id,
      </if>
      <if test="productsPropertyId != null">
        products_property_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="mappingId != null">
        #{mappingId,jdbcType=INTEGER},
      </if>
      <if test="productsPropertyId != null">
        #{productsPropertyId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.ProductsPropertyMapping">
    update products_property_mapping
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="mappingId != null">
        mapping_id = #{mappingId,jdbcType=INTEGER},
      </if>
      <if test="productsPropertyId != null">
        products_property_id = #{productsPropertyId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.ProductsPropertyMapping">
    update products_property_mapping
    set `type` = #{type,jdbcType=INTEGER},
      mapping_id = #{mappingId,jdbcType=INTEGER},
      products_property_id = #{productsPropertyId,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectAnchoredProperty" resultMap="net.summerfarm.mapper.manage.ProductsPropertyMapper.BaseResultMap">
    select pp.id,
       name,
       pp.type,
       format_type,
       format_str,
       status,
       pp.creator,
       pp.create_time
    from products_property pp
             left join products_property_mapping ppm on pp.id = ppm.products_property_id
    where pp.status = 1 and ppm.type = #{type} and ppm.mapping_id = #{mappingId}
  </select>
  <delete id="deleteBySelective">
    delete from products_property_mapping
    where `type` = #{type,jdbcType=INTEGER} and products_property_id = #{productsPropertyId,jdbcType=INTEGER} and mapping_id IN
    <foreach collection="mappingIds" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>
  <delete id="deleteCategoryKeyPropertyMapping">
    delete from products_property_mapping where type = 0 and mapping_id = #{categoryId} and products_property_id = #{propertyId}
  </delete>

  <select id="selectCategoryKeyPropertyMapping" resultMap="net.summerfarm.mapper.manage.ProductsPropertyMapper.BaseResultMap">
    select <include refid="Base_Column_List" /> from products_property_mapping where type = 0 and mapping_id = #{categoryId}
  </select>

  <select id="selectSpuCategoryValue" resultType="net.summerfarm.model.vo.ProductsPropertyValueVO">
    select ppv.id,
           ppv.pd_id                   pdId,
           ppv.sku,
           pp.id                       productsPropertyId,
           ppv.products_property_value productsPropertyValue,
           ppv.creator,
           ppv.create_time             createTime,
           pp.name,
           pp.type,
           pp.format_type              formatType,
           pp.format_str               formatStr
    from products_property pp
           inner join products_property_mapping ppm
                      on pp.id = ppm.products_property_id and ppm.mapping_id = #{category,jdbcType=INTEGER} and
                         ppm.type = 0
           left join products_property_value ppv
                     on ppv.products_property_id = pp.id and ppv.pd_id = #{pdId,jdbcType=BIGINT} and ppv.sku is null
    order by pp.create_time


  </select>
  <select id="selectAnchoredPropertyByMappingIds"
          resultMap="net.summerfarm.mapper.manage.ProductsPropertyMapper.BaseResultMap">
    select pp.id,
    name,
    pp.type,
    format_type,
    format_str,
    status,
    pp.creator,
    pp.create_time,
           ppm.mapping_id
    from products_property pp
    left join products_property_mapping ppm on pp.id = ppm.products_property_id
    where pp.status = 1 and ppm.type = #{type} and ppm.mapping_id in
    <foreach collection="mappingIds" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>
</mapper>