<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTakingListDetailMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.StockTakingListDetail">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="stock_taking_list_id" property="stockTakingListId" jdbcType="INTEGER" />
        <result column="sku" property="sku" jdbcType="VARCHAR" />
        <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR" />
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="quality_date" property="qualityDate" jdbcType="DATE" />
        <result column="batch" property="batch" jdbcType="VARCHAR"/>
        <result column="real_quantity" property="realQuantity" jdbcType="INTEGER"/>
        <result column="reason" property="reason" jdbcType="VARCHAR" />
        <result column="production_date" property="productionDate" jdbcType="DATE" />
        <result column="gl_no" property="glNo"  />

    </resultMap>

    <sql id="BaseColumn">
        id,stock_taking_list_id,sku,pd_name,weight,quantity,quality_date,batch,real_quantity,reason,production_date,gl_no
    </sql>

    <insert id="insertBatch" parameterType="net.summerfarm.model.domain.StockTakingListDetail">
        INSERT INTO stock_taking_list_detail(stock_taking_list_id,sku,pd_name,weight,quantity,quality_date,batch,real_quantity,reason,production_date,gl_no,reason_type)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.stockTakingListId},#{item.sku},#{item.pdName},#{item.weight},#{item.quantity},#{item.qualityDate},#{item.batch},#{item.realQuantity},#{item.reason},
              #{item.productionDate},#{item.glNo},#{item.reasonType})
        </foreach>
    </insert>

    <delete id="delete" parameterType="java.lang.Integer">
        DELETE
        FROM stock_taking_list_detail
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <select id="countPurchasesNo" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(1) c
        FROM stock_taking_list_detail stld
        WHERE stld.batch=#{purchasesNo}
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="net.summerfarm.model.vo.StockTakingListDetailVO">
        SELECT stld.id,stld.sku,stld.pd_name pdName,stld.weight,sti.store_quantity storeQuantity,stld.batch,stld.quality_date qualityDate,stld.production_date productionDate,
	      stld.quantity,stld.real_quantity realQuantity,stld.reason,stld.reason_type reasonType, ad.name_remakes nameRemakes,stld.gl_no glNo,i.ext_type extType
        FROM stock_taking_list stl
        INNER JOIN stock_taking_list_detail stld ON stl.id = stld.stock_taking_list_id
        INNER JOIN stock_taking_item sti ON stl.taking_id=sti.taking_id AND stld.sku=sti.sku
        LEFT JOIN inventory i ON i.sku = stld.sku
        LEFT JOIN admin ad  ON ad.admin_id = i.admin_id
        WHERE stl.id=#{id,jdbcType=INTEGER}
        group by sku,batch,qualityDate,glNo
    </select>

    <select id="selectList" resultType="net.summerfarm.model.vo.StockTakingListDetailVO">
        SELECT stld.id,stld.sku,stld.pd_name pdName,stld.weight,sti.store_quantity storeQuantity,stld.batch,stld.quality_date qualityDate,
	      stld.quantity,stld.real_quantity realQuantity,stld.reason
        FROM stock_taking_list stl
        INNER JOIN stock_taking_list_detail stld ON stl.id = stld.stock_taking_list_id
        INNER JOIN stock_taking_item sti ON stl.taking_id=sti.taking_id AND stld.sku=sti.sku
        WHERE stl.id=#{id}
        <if test="pdName != null">
            AND stld.pd_name LIKE concat('%',#{pdName},'%')
        </if>
        <if test="sku != null">
            AND stld.sku = #{sku}
        </if>
        <if test="diff != null">
            <choose>
                <when test="diff">
                    AND ( stld.quantity != stld.real_quantity OR stld.quantity IS NULL )
                </when>
                <otherwise>
                    AND stld.quantity = stld.real_quantity
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="select" parameterType="net.summerfarm.model.domain.StockTakingListDetail" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM stock_taking_list_detail
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="stockTakingListId != null">
                AND stock_taking_list_id = #{stockTakingListId}
            </if>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
        </where>
    </select>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.StockTakingListDetail">
        UPDATE stock_taking_list_detail
        SET real_quantity = #{realQuantity},
        reason = #{reason},
        reason_type = #{reasonType}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectFinishDetails" resultType="net.summerfarm.model.domain.StockTakingListDetail">
        SELECT stld.sku,ifnull(stld.real_quantity,0) realQuantity
        FROM stocktaking st
        INNER JOIN stock_taking_list stl ON st.id = stl.taking_id AND st.area_no = #{areaNo} AND stl.status = 2
        AND stl.updatetime <![CDATA[>=]]> #{startTime}
        AND stl.updatetime <![CDATA[<]]> #{endTime}
        INNER JOIN stock_taking_list_detail stld ON stl.id = stld.stock_taking_list_id
    </select>

    <select id="selectByTakingId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        stld.id,stld.stock_taking_list_id,stld.sku,stld.pd_name,stld.weight,stld.quantity,stld.quality_date,stld.batch,stld.real_quantity,stld.reason,stld.production_date,stld.gl_no
        FROM stock_taking_list_detail stld
        LEFT JOIN stock_taking_list stl on stl.id=stld.stock_taking_list_id
        where stl.taking_id=#{takingId}
    </select>

</mapper>