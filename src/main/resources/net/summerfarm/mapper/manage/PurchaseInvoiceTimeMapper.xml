<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PurchaseInvoiceTimeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PurchaseInvoiceTime">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchase_invoice_id" jdbcType="INTEGER" property="purchaseInvoiceId" />
    <result column="actual_tax_rate" jdbcType="DECIMAL" property="actualTaxRate" />
    <result column="actual_tax_amount" jdbcType="DECIMAL" property="actualTaxAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="file_time" jdbcType="VARCHAR" property="fileTime" />
    <result column="remakes" jdbcType="VARCHAR" property="remakes" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, purchase_invoice_id, actual_tax_rate, actual_tax_amount, I, creator,
    file_time, remakes, update_time, updater, delete_status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="net.summerfarm.model.domain.PurchaseInvoiceTime">
    select id, purchase_invoice_id purchasenvoiced, actual_tax_rate actualTaxRate, actual_tax_amount actualTaxAmount, create_time createTime, creator,
      file_time fileTime, remakes, update_time updateTime, updater, delete_status deleteStatus
    from purchase_invoice_time
    where purchase_invoice_id = #{id,jdbcType=INTEGER} and delete_status = 0
  </select>
  <update id="deleteByPrimaryKey">
    update purchase_invoice_time
    set delete_status = 1,
        update_time = now(),
        updater = #{updater}
    where purchase_invoice_id = #{id,jdbcType=INTEGER} and delete_status = 0
  </update>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.vo.PurchaseInvoiceTimeVO" useGeneratedKeys="true">
    insert into purchase_invoice_time (purchase_invoice_id, actual_tax_rate, 
      actual_tax_amount, create_time, creator, 
      file_time, remakes, update_time, 
      updater, delete_status)
    values (#{purchaseInvoiceId,jdbcType=INTEGER}, #{actualTaxRate,jdbcType=DECIMAL}, 
      #{actualTaxAmount,jdbcType=DECIMAL}, now() , #{creator,jdbcType=VARCHAR},
      #{fileTime,jdbcType=VARCHAR}, #{remakes,jdbcType=VARCHAR}, now() ,
      #{updater,jdbcType=VARCHAR}, 0 )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PurchaseInvoiceTime" useGeneratedKeys="true">
    insert into purchase_invoice_time
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="purchaseInvoiceId != null">
        purchase_invoice_id,
      </if>
      <if test="actualTaxRate != null">
        actual_tax_rate,
      </if>
      <if test="actualTaxAmount != null">
        actual_tax_amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="fileTime != null">
        file_time,
      </if>
      <if test="remakes != null">
        remakes,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="purchaseInvoiceId != null">
        #{purchaseInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="actualTaxRate != null">
        #{actualTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="actualTaxAmount != null">
        #{actualTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="fileTime != null">
        #{fileTime,jdbcType=VARCHAR},
      </if>
      <if test="remakes != null">
        #{remakes,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PurchaseInvoiceTime">
    update purchase_invoice_time
    <set>
      <if test="purchaseInvoiceId != null">
        purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="actualTaxRate != null">
        actual_tax_rate = #{actualTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="actualTaxAmount != null">
        actual_tax_amount = #{actualTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="fileTime != null">
        file_time = #{fileTime,jdbcType=VARCHAR},
      </if>
      <if test="remakes != null">
        remakes = #{remakes,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where purchase_invoice_id = #{purchaseInvoiceId}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PurchaseInvoiceTime">
    update purchase_invoice_time
    set purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER},
      actual_tax_rate = #{actualTaxRate,jdbcType=DECIMAL},
      actual_tax_amount = #{actualTaxAmount,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      file_time = #{fileTime,jdbcType=VARCHAR},
      remakes = #{remakes,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      delete_status = #{deleteStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>