<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.SalePredictionConfigMapper">

    <select id="selectByType" resultType="net.summerfarm.model.domain.SalePredictionConfig">
        select type,k1,k2,k3 from sale_prediction_config where type=#{type}
    </select>

    <insert id="insert" parameterType="net.summerfarm.model.domain.SalePredictionConfig" keyColumn="id" useGeneratedKeys="true">
        insert into sale_prediction_config ( type, k1, k2, k3) values(#{type},#{k1},#{k2},#{k3})
    </insert>

    <update id="updateByType" parameterType="net.summerfarm.model.domain.SalePredictionConfig">
        update sale_prediction_config
        <set>
            <if test="k1 != null" >
                k1 = #{k1},
            </if>
            <if test="k1 != null" >
                k2 = #{k2},
            </if>
            <if test="k1 != null" >
                k3 = #{k3},
            </if>
        </set>
        where type = #{type}
    </update>
</mapper>
