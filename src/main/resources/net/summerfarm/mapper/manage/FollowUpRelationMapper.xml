<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.FollowUpRelationMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FollowUpRelation">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
        <result column="reassign_time" property="reassignTime" jdbcType="TIMESTAMP"/>
        <result column="last_follow_up_time" property="lastFollowUpTime" jdbcType="TIMESTAMP"/>
        <result column="admin_name" property="adminName" jdbcType="VARCHAR"/>
        <result column="reassign" property="reassign" jdbcType="BIT"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="care_bd_id" property="careBdId" jdbcType="INTEGER"/>

    </resultMap>
    <sql id="Base_Column_List">
    id, m_id, admin_id, admin_name, add_time, last_follow_up_time, reassign, reassign_time,reason,care_bd_id
  </sql>

    <update id="updateReassign" parameterType="net.summerfarm.model.domain.FollowUpRelation">
    update follow_up_relation
    <set>
        <if test="adminId != null" >
            admin_id = #{adminId},
        </if>
        <if test="adminName != null" >
            admin_name = #{adminName},
        </if>
        <if test="lastFollowUpTime != null" >
            last_follow_up_time = #{lastFollowUpTime},
        </if>
        <if test="reassign != null" >
            reassign = #{reassign},
        </if>
        <if test="reassignTime != null" >
            reassign_time = #{reassignTime},
        </if>
        <if test="addTime != null and reassign == false" >
            add_time = #{addTime},
        </if>
        <if test="reason != null" >
            reason = #{reason},
        </if>
        <if test="dangerDay != null" >
            danger_day = #{dangerDay},
        </if>
        <if test="followType != null" >
            follow_type = #{followType},
        </if>
        <if test="careBdId != null" >
            care_bd_id = #{careBdId},
        </if>
        <if test="timingFollowType != null">
            timing_follow_type = #{timingFollowType}
        </if>
    </set>
     WHERE id = #{id}
  </update>

    <select id="selectOne" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from follow_up_relation
        <where>
            <if test="mId != null">
                AND m_id = #{mId}
            </if>
            <if test="reassign != null">
                AND reassign =#{reassign}
            </if>
            <if test="adminName !=null">
                AND admin_name = #{adminName}
            </if>
        </where>
        order by id desc
        limit 1
    </select>

    <select id="countFollowUp" resultType="java.lang.Integer" >
        select
        count(*)
        from follow_up_relation fur
        LEFT JOIN merchant m on fur.m_id=m.m_id
        <where>
            <if test="adminId != null">
                AND  fur.admin_id =#{adminId}
            </if>
            <if test="reassign != null">
                AND fur.reassign =#{reassign}
            </if>
            <if test="areaNo != null">
                AND m.area_no =#{areaNo}
            </if>
            <if test="'true'.toString() == reassign.toString()">
                AND  reassign_time  <![CDATA[>=]]> #{startTime}
            </if>
            <if test="'false'.toString() == reassign.toString()">
                AND  add_time  <![CDATA[>=]]> #{startTime}
            </if>
        </where>
    </select>


    <select id="select" resultMap="BaseResultMap">
        select
        fur.id, fur.m_id, fur.admin_id, fur.admin_name, fur.add_time, fur.last_follow_up_time, fur.reassign,
        fur.reassign_time,fur.reason,m.size
        from follow_up_relation fur
        left join merchant m on fur.m_id=m.m_id
        <where>
            <if test="mId != null">
                AND fur.m_id = #{mId}
            </if>
            <if test="reassign != null">
                AND fur.reassign =#{reassign}
            </if>
            <if test="adminName !=null">
                AND fur.admin_name = #{adminName}
            </if>
            <if test="adminId != null">
                AND fur.admin_id = #{adminId}
            </if>
            <if test="size != null">
                AND m.size = #{size}
            </if>
        </where>
    </select>


    <insert id="insertSelective" parameterType="net.summerfarm.model.domain.FollowUpRelation">
        insert into follow_up_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                m_id,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="reassign != null">
                reassign,
            </if>
            <if test="adminName != null">
                admin_name,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="reassignTime != null">
                reassign_time,
            </if>
            <if test="careBdId != null">
                care_bd_id,
            </if>
            <if test="timingFollowType != null">
                timing_follow_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                #{mId,jdbcType=BIGINT},
            </if>
            <if test="adminId != null">
                #{adminId,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="reassign != null">
                #{reassign,jdbcType=BIT},
            </if>
            <if test="adminName != null">
                #{adminName},
            </if>
            <if test="reason != null">
                #{reason},
            </if>
            <if test="reassignTime != null">
                #{reassignTime},
            </if>
            <if test="careBdId != null">
                #{careBdId},
            </if>
            <if test="timingFollowType != null">
                #{timingFollowType},
            </if>
        </trim>
    </insert>

    <select id="getMerchaWithBD" resultType="net.summerfarm.model.vo.ReleaseVO">
        select m.m_id mId,f.add_time addTime,
        (case when m.last_order_time is NULL then f.add_time
        when f.add_time>m.last_order_time   then f.add_time
        else m.last_order_time end) as orderTime,
        (case when fr.followTime is NULL then f.add_time
        when f.add_time>fr.followTime  then f.add_time
        else fr.followTime end) as followTime
        from merchant m
        left join follow_up_relation f on m.m_id=f.m_id
        left join (select f.m_id,max(f.add_time) followTime from follow_up_record f
        INNER JOIN merchant m ON m.m_id = f.m_id
        where f.status in (1,2) AND m.islock = 0
        <if test="mId != null">
            AND f.m_id= #{mId}
        </if>
        group by f.m_id) fr on fr.m_id=m.m_id
        where m.islock=0 and f.reassign=0 and m.size !='大客户'
        <if test="mId != null">
            AND m.m_id=#{mId}
        </if>
    </select>
    <select id="getMerchaWithOpenSea" resultType="net.summerfarm.model.vo.ReleaseVO">
        select m.m_id mId,f.add_time followTime,
        (case when m.last_order_time is NULL then f.add_time
        when f.add_time>m.last_order_time   then f.add_time
        else m.last_order_time end) as orderTime
        from merchant m
        left join follow_up_relation f on m.m_id=f.m_id
        where m.islock=0 and m.size !='大客户' and f.reassign=1
        <if test="mId != null">
            AND m.m_id=#{mId}
        </if>
        ORDER BY f.add_time DESC
    </select>
    <update id="updateReassignByAdminId">
        update follow_up_relation
        <set>
            <if test="reassign != null" >
                reassign = #{reassign},
            </if>

            <if test="reassignTime != null" >
                reassign_time = #{reassignTime},
            </if>

            <if test="reason != null" >
                reason = #{reason},
            </if>

            <if test="dangerDay != null" >
                danger_day = #{dangerDay},
            </if>
            <if test="followType != null" >
                follow_type = #{followType},
            </if>
            <if test="careBdId != null">
                care_bd_id = #{careBdId},
            </if>
            <if test="timingFollowType != null">
                timing_follow_type = #{timingFollowType}
            </if>

        </set>
        WHERE admin_id = #{adminId} and reassign = 0
    </update>
    <update id="updateReassignByAdminIdArea">
        update follow_up_relation fur left join merchant m on m.m_id = fur.m_id
        <set>
            <if test="followUpRelation.reassign != null" >
                reassign = #{followUpRelation.reassign},
            </if>

            <if test="followUpRelation.reassignTime != null" >
                reassign_time = #{followUpRelation.reassignTime},
            </if>

            <if test="followUpRelation.reason != null" >
                reason = #{followUpRelation.reason},
            </if>

            <if test="followUpRelation.dangerDay != null" >
                danger_day = #{followUpRelation.dangerDay},
            </if>
            <if test="followUpRelation.followType != null" >
                follow_type = #{followUpRelation.followType},
            </if>
            <if test="followUpRelation.timingFollowType != null">
                timing_follow_type = #{followUpRelation.timingFollowType}
            </if>
        </set>
        <where>
               fur.reassign = 0
            <if test="adminId!=null">
                and fur.admin_id = #{adminId}
            </if>
            <if test="infoArea.size()>0">
                and m.area_no in
                <foreach collection="infoArea" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>
    <delete id="deleteByMid">
        delete from follow_up_relation where m_id = #{mId}
    </delete>
    <select id="selectByMid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from follow_up_relation
        where m_id = #{mId}
    </select>
    <select id="selectByAreaNo" resultMap="BaseResultMap">
        select
        fur.id, fur.m_id, fur.admin_id, fur.admin_name, fur.add_time, fur.last_follow_up_time, fur.reassign,
        fur.reassign_time,fur.reason,m.size
        from follow_up_relation fur
        left join merchant m on fur.m_id=m.m_id
        <where>
            <if test="mId != null">
                AND fur.m_id = #{mId}
            </if>
            <if test="reassign != null">
                AND fur.reassign =#{reassign}
            </if>
            <if test="adminName !=null">
                AND fur.admin_name = #{adminName}
            </if>
            <if test="adminId != null">
                AND fur.admin_id = #{adminId}
            </if>
            <if test="size != null">
                AND m.size = #{size}
            </if>
            <if test="areaNo != null">
                AND m.area_no = #{areaNo}
            </if>
        </where>
    </select>
    <select id="selectPrivateSea" parameterType="net.summerfarm.model.input.PrivateSeaInput" resultType="net.summerfarm.model.vo.PrivateSeaVO">
        select m.m_id mId, m.mname,m.size, m.area_no areaNo,m.last_order_time lastOrderTime,fur.danger_day dangerDay,m.register_time registerTime,
        <if test="lifecycle != null">
               ml.lifecycle,
        </if>
        m.grade grade
        from merchant m
        left join follow_up_relation fur on m.m_id = fur.m_id and fur.reassign = 0
        <if test="lifecycle != null">
         left join (select m_id, lifecycle
                    from merchant_lifecycle
                    where add_time <![CDATA[ >= ]]> #{aDayEarlier}
                    group by m_id) ml on ml.m_id = m.m_id
        </if>
        WHERE m.islock = 0
            <if test="adminId != null">
               and fur.admin_id = #{adminId}
            </if>
            <if test="areaNo != null">
                and m.area_no = #{areaNo}
            </if>
            <if test="lifecycle != null">
                and ml.lifecycle = #{lifecycle}
            </if>
            <if test="grade != null">
                and m.grade = #{grade}
            </if>
            <if test="mname != null and mname != ''">
                and m.mname like concat(#{mname}, '%')
            </if>
            <if test="orderCurrentMonth != null and orderCurrentMonth">
                and m.last_order_time <![CDATA[ > ]]>  #{atBeginningOfMonth}
            </if>
            <if test="orderCurrentMonth != null and !orderCurrentMonth">
                and (m.last_order_time <![CDATA[ < ]]>  #{atBeginningOfMonth} or  m.last_order_time is null)
            </if>
        <choose>
            <when test="sortType == 2">
                order by fur.danger_day
            </when>
            <when test="sortType == 3">
                order by fur.danger_day desc
            </when>
        </choose>
    </select>
  <select id="countByMId" resultType="int">
    select ifnull(count(*), 0) from crm_relation_record where m_id = #{mId}
  </select>

    <select id="selectNotInAllMId" resultType="java.lang.Long">
        select m.m_id from merchant m
        left join follow_up_relation fur on fur.m_id = m.m_id
        where m.admin_id = #{adminId} and fur.id is null
    </select>
    <select id="queryFollow" resultType="net.summerfarm.model.vo.MerchantVO">
        SELECT admin_name adminName,reassign reassign FROM merchant m
        LEFT JOIN follow_up_relation f ON f.m_id = m.m_id
        WHERE m.islock = 0 AND f.m_id = #{mId} AND f.reassign = 0
    </select>
    <select id="selectByAreaNos" resultMap="BaseResultMap">
        SELECT f.id,f.m_id, f.admin_id, f.admin_name, f.add_time, f.last_follow_up_time, f.reassign, f.reassign_time,f.reason
        FROM follow_up_relation f
        LEFT JOIN merchant m ON f.m_id = m.m_id
        WHERE f.reassign = 0 AND f.admin_id = #{adminId} AND m.area_no IN
        <foreach collection="infoArea" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectPrivateNum" resultType="integer">
        select count(*)
        from follow_up_relation fur
        LEFT JOIN merchant m on fur.m_id=m.m_id
        WHERE m.islock = 0 AND fur.reassign = 0 AND m.size = '单店'
        <if test="adminId != null">
            and fur.admin_id = #{adminId}
        </if>
        <if test="areaNos!=null and areaNos.size()>0">
            and m.area_no in
            <foreach collection="areaNos" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectBdType" resultType="integer">
        SELECT f.m_id FROM `crm_bd_team` cbt
        INNER JOIN follow_up_relation f ON cbt.admin_id = f.admin_id
        WHERE cbt.type = 2 and cbt.is_locked = 1 AND f.reassign = 0
    </select>
    <select id="selectMerchantNum" resultType="net.summerfarm.model.vo.SalesDataVo">
        SELECT
            IFNULL(SUM(coalesce(f.reassign,1) = 0),0) privateMerchant,
            IFNULL(SUM(coalesce(f.reassign,1) = 1),0) openMerchant
        FROM follow_up_relation f
        INNER JOIN merchant m ON f.m_id = m.m_id
        WHERE m.islock = 0
        AND m.area_no IN
        <foreach collection="areaNo" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        <if test="type != null and type == 1">
            AND f.admin_id NOT IN (SELECT admin_id FROM crm_bd_team WHERE is_locked = 1 AND type = 2)
        </if>
        <if test="type != null and type == 2">
            AND f.reassign = 0 AND f.admin_id IN (SELECT admin_id FROM crm_bd_team WHERE type =2 AND is_locked = 1)
        </if>
    </select>
    <select id="selectOperateMerchantInOpenSea" resultType="integer">
        SELECT count(*) FROM follow_up_relation f
        INNER JOIN merchant m ON f.m_id = m.m_id
        WHERE f.reassign = 1 AND m.operate_status = 1
        AND m.area_no IN
        <foreach collection="areaNo" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="listByMIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM follow_up_relation
        WHERE  reassign = 0
        AND m_id IN
        <foreach collection="mIds" open="(" separator="," close=")" item="mId">
            #{mId}
        </foreach>
    </select>


    <select id="selectLastFollowOne" resultMap="BaseResultMap">
        select
        fur.id, fur.m_id, fur.admin_id, fur.admin_name, fur.add_time, fur.last_follow_up_time, fur.reassign,
        fur.reassign_time,fur.reason,m.size,care_bd_id
        from follow_up_relation fur
        left join merchant m on fur.m_id=m.m_id
        where fur.m_id = #{mId} and fur.reassign=0 and  fur.admin_id =#{adminId}
        limit 1
    </select>

    <select id="selectNotReassignByMid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM follow_up_relation
        WHERE reassign = 0
        AND m_id =#{mId}
    </select>

    <select id="batchByMids" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM follow_up_relation
        WHERE m_id IN
        <foreach collection="mIds" open="(" separator="," close=")" item="mId">
            #{mId}
        </foreach>
    </select>
</mapper>