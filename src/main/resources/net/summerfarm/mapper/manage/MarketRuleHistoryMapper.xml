<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="net.summerfarm.mapper.manage.MarketRuleHistoryMapper">


    <select id="selectList"  parameterType="java.lang.String" resultType="net.summerfarm.model.domain.MarketRuleHistory">
      select order_no orderNo,value,rule_level ruleLevel,type,market_rule_id marketRuleId,detail from market_rule_history
      where order_no=#{orderNo}
      order by type desc

    </select>

</mapper>