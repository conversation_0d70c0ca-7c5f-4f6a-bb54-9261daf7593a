<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockStoreBackUpDetailMapper" >

  <insert id="insertMapping">
   insert into stock_store_back_up_detail(store_back_up_id,warehouse_no,create_time,sku)
      select #{id},warehouse_no,now(),sku from warehouse_inventory_mapping where store_no =#{storeNo};
  </insert>
</mapper>