<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.SupplierMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.input.SupplierReq">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="category_array" property="categoryArray" jdbcType="VARCHAR" />
        <result column="product_array" property="productArray" jdbcType="VARCHAR"/>
        <result column="manager" property="manager" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="contract" property="contract" jdbcType="VARCHAR" />
        <result column="invoice" property="invoice" jdbcType="BOOLEAN" />
        <result column="delivery_frequent" property="deliveryFrequent" jdbcType="VARCHAR"/>
        <result column="settle_form" property="settleForm" jdbcType="INTEGER"/>
        <result column="settle_type" property="settleType" jdbcType="INTEGER"/>
        <result column="custom_start_date" property="customStartDate" jdbcType="TIMESTAMP"/>
        <result column="custom_cycle" property="customCycle" jdbcType="INTEGER"/>
        <result column="credit_days" property="creditDays" jdbcType="INTEGER"/>
        <result column="account_name" property="supplierName" jdbcType="VARCHAR"/>
        <result column="supplier_type" property="supplierType" jdbcType="INTEGER"/>
        <result column="tax_number" property="taxNumber" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="module_type" jdbcType="INTEGER" property="moduleType" />
        <result column="total_score" jdbcType="INTEGER" property="totalScore" />
        <result column="score_json" jdbcType="LONGVARCHAR" property="scoreJson" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <collection property="connectList" column="id" javaType="ArrayList" ofType="net.summerfarm.model.domain.SupplierConnect" select="net.summerfarm.mapper.manage.SupplierConnectMapper.selectBySupplierId"/>
        <collection property="accountList" column="id" javaType="ArrayList" ofType="net.summerfarm.model.domain.SupplierAccount" select="selectAccountBySupplierId"/>
        <collection property="supplierFileList" column="id" javaType="ArrayList" ofType="net.summerfarm.model.domain.SupplierRelatedFile" select="net.summerfarm.mapper.manage.SupplierRelatedFileMapper.listBySupplierId"/>
    </resultMap>

    <resultMap id="BaseResultDetailMap" type="net.summerfarm.model.input.SupplierReq">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="category_array" property="categoryArray" jdbcType="VARCHAR" />
        <result column="product_array" property="productArray" jdbcType="VARCHAR"/>
        <result column="manager" property="manager" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="contract" property="contract" jdbcType="VARCHAR" />
        <result column="invoice" property="invoice" jdbcType="BOOLEAN" />
        <result column="delivery_frequent" property="deliveryFrequent" jdbcType="VARCHAR"/>
        <result column="settle_form" property="settleForm" jdbcType="INTEGER"/>
        <result column="settle_type" property="settleType" jdbcType="INTEGER"/>
        <result column="custom_start_date" property="customStartDate" jdbcType="TIMESTAMP"/>
        <result column="custom_cycle" property="customCycle" jdbcType="INTEGER"/>
        <result column="credit_days" property="creditDays" jdbcType="INTEGER"/>
        <result column="account_name" property="supplierName" jdbcType="VARCHAR"/>
        <result column="supplier_type" property="supplierType" jdbcType="INTEGER"/>
        <result column="tax_number" property="taxNumber" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="module_type" jdbcType="INTEGER" property="moduleType" />
        <result column="total_score" jdbcType="INTEGER" property="totalScore" />
        <result column="score_json" jdbcType="LONGVARCHAR" property="scoreJson" />
        <result column="left_days" property="leftDays" jdbcType="INTEGER"/>
        <result column="qr_code_show_supplier_switch" property="qrCodeShowSupplierSwitch" jdbcType="INTEGER"/>
        <collection property="connectList" column="id" javaType="ArrayList" ofType="net.summerfarm.model.domain.SupplierConnect" select="net.summerfarm.mapper.manage.SupplierConnectMapper.selectBySupplierId"/>
        <collection property="accountList" column="id" javaType="ArrayList" ofType="net.summerfarm.model.domain.SupplierAccount" select="selectAccountBySupplierId"/>
        <collection property="supplierFileList" column="id" javaType="ArrayList" ofType="net.summerfarm.model.input.SupplierFileReq" select="net.summerfarm.mapper.manage.SupplierRelatedFileMapper.listBySupplierId"/>
        <collection property="supplierContractList" column="id" javaType="ArrayList" ofType="net.summerfarm.model.domain.SupplierContract" select="net.summerfarm.mapper.manage.SupplierContractMapper.selectFinishedBySupplierId"/>
    </resultMap>

<!--    <resultMap extends="BaseResultMap" id="supplierGradeReq" type="net.summerfarm.model.input.SupplierGradeReq">-->
<!--        <result column="module_type" jdbcType="INTEGER" property="moduleType" />-->
<!--        <result column="total_score" jdbcType="INTEGER" property="totalScore" />-->
<!--        <result column="score_json" jdbcType="LONGVARCHAR" property="scoreJson" />-->
<!--    </resultMap>-->

    <select id="selectAccountBySupplierId" parameterType="java.lang.Integer" resultType="net.summerfarm.model.domain.SupplierAccount">
        select id, supplier_id supplierId, pay_type payType, account_name accountName, account_bank accountBank, account_ascription accountAscription, account, creator
        from supplier_account where supplier_id = #{id}
    </select>

    <select id="selectByAccount" parameterType="java.lang.Integer" resultType="net.summerfarm.model.domain.SupplierAccount">
        select id, supplier_id supplierId, pay_type payType, account_name accountName, account_bank accountBank, account_ascription accountAscription, account, creator
        from supplier_account where supplier_id = #{id}
            limit 1
    </select>

    <insert id="insert" parameterType="net.summerfarm.model.domain.Supplier" useGeneratedKeys="true" keyProperty="id">
    insert into supplier (name, address,category_array,product_array,
    manager,invoice,remark,contract,delivery_frequent,settle_form,settle_type,custom_start_date,custom_cycle,credit_days,supplier_type,tax_number,status)
    values (#{name},#{address},#{categoryArray},
    #{productArray},#{manager},#{invoice,jdbcType=BIT},#{remark},#{contract},#{deliveryFrequent},#{settleForm},#{settleType},#{customStartDate},#{customCycle},#{creditDays},#{supplierType},#{taxNumber},#{status})
  </insert>

    <update id="update" parameterType="net.summerfarm.model.domain.Supplier">
        update supplier
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="productArray != null">
                product_array = #{productArray},
            </if>
            <if test="categoryArray != null">
                category_array = #{categoryArray},
            </if>
            <if test="manager != null">
                manager = #{manager},
            </if>
            <if test="invoice != null">
                invoice = #{invoice},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="contract != null">
                contract = #{contract},
            </if>
            <if test="settleForm != null">
                settle_form = #{settleForm},
            </if>
            <if test="settleType != null">
                settle_type = #{settleType},
            </if>
            <if test="customStartDate != null">
                custom_start_date = #{customStartDate},
            </if>
            <if test="customCycle != null">
                custom_cycle = #{customCycle},
            </if>
            <if test="creditDays != null">
                credit_days = #{creditDays},
            </if>
            <if test="supplierType != null">
                supplier_type = #{supplierType},
            </if>
            <if test="taxNumber != null">
                tax_number = #{taxNumber},
            </if>
            <if test="auditPassDate != null">
                audit_pass_date = #{auditPassDate},
            </if>
            <choose>
                <when test="deliveryFrequent != null">
                    delivery_frequent = #{deliveryFrequent},
                </when>
                <otherwise>
                    delivery_frequent = NULL ,
                </otherwise>
            </choose>
        </set>
        where id = #{id}
    </update>
    <update id="enableSupplier">
        UPDATE supplier set status = 0, audit_pass_date = CURDATE()  where id = #{id}
    </update>
    <update id="switchToAuditing">
        UPDATE supplier set status = 2 where id = #{id}
    </update>

    <update id="switchToRefuse">
        UPDATE supplier set status = 3 where id = #{id}
    </update>

    <update id="switchToDisable">
        UPDATE supplier set status = 2 where id = #{id}
    </update>
    <select id="select" resultMap="BaseResultMap" parameterType="net.summerfarm.model.input.SupplierReq">
        SELECT s.id,s.name,s.address,s.category_array,s.product_array,s.manager,s.remark,s.contract,sa.account_name,s.supplier_type,s.tax_number,
               s.delivery_frequent,IFNULL(psc.settle_form, 5) settle_form ,psc.settle_type,psc.custom_start_date,psc.custom_cycle,psc.credit_days,s.invoice,s.status,s.creator,s.create_time,s.updater,s.update_time
        FROM supplier s
        LEFT JOIN supplier_connect sc ON s.id = sc.supplier_id
        LEFT JOIN supplier_account sa on s.id = sa.supplier_id
        LEFT JOIN (select settle_form, settle_type, custom_start_date, custom_cycle, credit_days, supplier_id from purchase_supplier_contract where status = 3 and valid = 1) psc on s.id = psc.supplier_id
        <where>
            s.status != 2 and s.status != 3 and s.tenant_id=1
            <if test="phone != null">
                AND sc.phone = #{phone}
            </if>
            <if test="name !=null">
                AND s.name like CONCAT('%',#{name},'%')
            </if>
            <if test="settleForm != null">
                AND psc.settle_form = #{settleForm}
            </if>
            <if test="categoryKey != null">
                AND s.category_array like CONCAT('%',#{categoryKey},'%')
            </if>
            <if test="manager != null">
                AND s.manager = #{manager}
            </if>
            <if test="status != null">
                AND s.status = #{status}
            </if>
        </where>
        GROUP BY s.id
        ORDER by s.id DESC
    </select>

    <select id="selectAll" resultType="net.summerfarm.model.domain.Supplier">
        SELECT id,name,address,product_array productArray,manager,remark,contract,
        settle_form settleForm,settle_type settleType,custom_start_date customStartDate,custom_cycle customCycle,credit_days creditDays
        FROM supplier
    </select>

    <update id="batchShutDown" parameterType="java.util.List">
        update supplier set status = 1 where id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="batchUpdatePurchaser">
        UPDATE supplier SET manager =
        <foreach collection="list" item="item" index="index" separator=" " open="case ID" close="end">
            when #{item.id} then #{item.purchaser}
        </foreach>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>
    <update id="updateSelective" parameterType="net.summerfarm.model.domain.Supplier">
        update supplier
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="productArray != null">
                product_array = #{productArray},
            </if>
            <if test="categoryArray != null">
                category_array = #{categoryArray},
            </if>
            <if test="manager != null">
                manager = #{manager},
            </if>
            <if test="invoice != null">
                invoice = #{invoice},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="contract != null">
                contract = #{contract},
            </if>
            <if test="settleForm != null">
                settle_form = #{settleForm},
            </if>
            <if test="settleType != null">
                settle_type = #{settleType},
            </if>
            <if test="customStartDate != null">
                custom_start_date = #{customStartDate},
            </if>
            <if test="customCycle != null">
                custom_cycle = #{customCycle},
            </if>
            <if test="creditDays != null">
                credit_days = #{creditDays},
            </if>
            <if test="supplierType != null">
                supplier_type = #{supplierType},
            </if>
            <if test="taxNumber != null">
                tax_number = #{taxNumber},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <choose>
                <when test="deliveryFrequent != null">
                    delivery_frequent = #{deliveryFrequent},
                </when>
                <otherwise>
                    delivery_frequent = NULL ,
                </otherwise>
            </choose>
        </set>
        where id = #{id}
    </update>
    <update id="updateQrCodeSwitch">
        update supplier
        SET qr_code_show_supplier_switch = #{qrCodeShowSupplierSwitch}
        where id = #{id}
    </update>

    <select id="selectTaxNumber" resultType="integer">
        select count(1)
        from supplier
        where tax_number= #{taxNumber} and tenant_id=1
    </select>

    <select id="selectByName" resultType="net.summerfarm.model.domain.Supplier">
        SELECT id,name,address,product_array productArray,manager,remark,contract,tax_number taxNumber,
        settle_form settleForm,settle_type settleType,custom_start_date customStartDate,custom_cycle customCycle,credit_days creditDays
        FROM supplier
        WHERE name = #{name} and tenant_id=1
            limit 1
    </select>

    <select id="selectByTaxNumber" resultType="net.summerfarm.model.domain.Supplier">
        SELECT id,name,address,product_array productArray,manager,remark,contract,tax_number taxNumber,
               settle_form settleForm,settle_type settleType,custom_start_date customStartDate,custom_cycle customCycle,credit_days creditDays
        FROM supplier
        WHERE  tax_number= #{taxNumber} and name = #{name} and tenant_id=1
            limit 1
    </select>


    <select id="selectListByName" resultType="net.summerfarm.model.domain.Supplier">
        SELECT id,name,address,product_array productArray,manager,remark,contract,
        settle_form settleForm,settle_type settleType,custom_start_date customStartDate,custom_cycle customCycle,credit_days creditDays
        FROM supplier
        WHERE name = #{name} and tenant_id=1
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="net.summerfarm.model.domain.Supplier">
        SELECT id,name,address,product_array productArray,remark,contract,delivery_frequent deliveryFrequent,
        settle_form settleForm,settle_type settleType,custom_start_date customStartDate,custom_cycle customCycle,credit_days creditDays,tax_number taxNumber,category_array categoryArray
        ,supplier_type supplierType, status, qr_code_show_supplier_switch qrCodeShowSupplierSwitch
        FROM supplier
        WHERE id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectBill" resultMap="BaseResultMap" parameterType="integer">
        SELECT s.id,s.name,s.address,s.category_array,s.product_array,s.manager,s.remark,s.contract,s.supplier_type,s.tax_number,
               s.delivery_frequent,psc.settle_form,psc.settle_type,psc.custom_start_date,psc.custom_cycle,psc.credit_days,s.invoice,s.status
        FROM supplier s
                 LEFT JOIN purchase_supplier_contract psc on s.id = psc.supplier_id and psc.status = 3 and psc.valid = 1
        where  s.id = #{supplierId}
    </select>
    <select id="selectManager" parameterType="integer" resultType="net.summerfarm.model.domain.Supplier">
        SELECT id,name,manager,tax_number taxNumber,supplier_type supplierType
        FROM supplier
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectList" resultType="net.summerfarm.model.vo.SupplierVO">
        select s.id supplierId, s.name name ,sa.account_name supplierName,s.tax_number taxNumber,s.manager manager,s.supplier_type type
        FROM supplier s
        left join supplier_account sa on s.id = sa.supplier_id
        where s.tenant_id=1
        group by s.id
    </select>

    <select id="selectInvoiceList" resultType="net.summerfarm.model.vo.InvoiceSupplierVO">
        select s.id id, s.name name,s.name corporateName ,s.tax_number taxNumber,s.manager manager,s.supplier_type supplierType
        FROM supplier s
    </select>

    <select id="selectId" resultType="net.summerfarm.model.domain.Supplier">
        select s.id id, s.name name ,s.manager manager
        FROM supplier s
        left join supplier_account sa on s.id = sa.supplier_id
        where sa.account_name = #{supplierName} and s.tenant_id=1
        limit 1
    </select>

    <select id="selectDetail" resultMap="BaseResultMap" parameterType="integer">
        SELECT s.id,s.name,s.address,s.category_array,s.product_array,s.manager,s.remark,s.contract,sa.account_name,s.supplier_type,s.tax_number,
               s.delivery_frequent,s.settle_form,s.settle_type,s.custom_start_date,s.custom_cycle,s.credit_days,s.invoice,s.status,s.audit_pass_date,
                sg.module_type, sg.total_score, sg.score_json,s.creator,s.create_time,s.updater,s.update_time
        FROM supplier s
        LEFT JOIN supplier_connect sc ON s.id = sc.supplier_id
        LEFT JOIN supplier_account sa on s.id = sa.supplier_id
        LEFT JOIN purchase_supplier_grade sg on s.id = sg.supplier_id
        where s.id = #{id}
        group by s.id
    </select>

    <select id="selectSupplierName" resultType="net.summerfarm.model.vo.SupplierVO">
        select s.id, s.name,s.settle_form settleForm,s.tax_number taxNumber
        from supplier s where tenant_id=1
    </select>

    <select id="selectBySupplierName" resultType="net.summerfarm.model.domain.Supplier">
        SELECT s.id,s.name
        FROM supplier s
        WHERE  s.name = #{name} and  s.tenant_id=1 and s.status = 0
    </select>
    <select id="selectAuditByType" resultMap="BaseResultMap" parameterType="net.summerfarm.model.input.SupplierReq">
        SELECT s.id,s.name,s.manager,s.status,s.creator,s.create_time,s.updater,s.update_time
        FROM supplier s
        <where>
            status != 0 and status != 1 and s.tenant_id=1
            <if test="name !=null">
                AND s.name like CONCAT('%',#{name},'%')
            </if>
            <if test="id != null">
                AND s.id = #{id}
            </if>
            <if test="status != null">
                AND s.status = #{status}
            </if>
            <if test="manager != null">
                AND s.manager = #{manager}
            </if>
        </where>
        GROUP BY s.id
        order by s.id desc
    </select>
    <select id="getJudgeDateList" resultType="net.summerfarm.model.param.SupplierJudgeDateParam">
        SELECT id,audit_pass_date judgeDate
        FROM
        supplier
        where status = 0 and audit_pass_date is not null and tenant_id=1
    </select>
    <select id="selectLastPurchaserGroupBySupplierId" resultType="net.summerfarm.model.param.SupplierPurchaserParam">
        SELECT s.id, p.purchaser
        FROM supplier s
        LEFT JOIN purchases_plan pp ON pp.supplier_id = s.id
        LEFT JOIN purchases p ON p.purchase_no = pp.purchase_no
        ,(SELECT max( p.id ) id,pp.supplier_id supplier_id
        FROM purchases p
        LEFT JOIN purchases_plan pp ON p.purchase_no = pp.purchase_no
        WHERE supplier_id IS NOT NULL and pp.`origin_id`  is null
        GROUP BY pp.supplier_id) temp
        WHERE s.id = temp.supplier_id and p.id = temp.id and s.status = 0 and s.tenant_id=1
        group by  s.id, p.purchaser  order by s.id asc
    </select>

    <select id="creditCodeCheck" resultType="net.summerfarm.model.domain.Supplier">
        select id,tax_number taxNumber from supplier where tax_number = #{taxNumber} and tenant_id=1
    </select>

    <select id="detail" resultMap="BaseResultDetailMap">
        SELECT s.id,s.name,s.address,s.category_array,s.product_array,s.manager,s.remark,s.contract,sa.account_name,s.supplier_type,s.tax_number,
        s.delivery_frequent,s.settle_form,s.settle_type,s.custom_start_date,s.custom_cycle,s.credit_days,s.invoice,s.audit_pass_date,s.status,
        sg.module_type, sg.total_score, sg.score_json,(30 - datediff(CURDATE(), s.audit_pass_date)) as left_days, s.qr_code_show_supplier_switch
        FROM supplier s
        LEFT JOIN supplier_connect sc ON s.id = sc.supplier_id
        LEFT JOIN supplier_account sa on s.id = sa.supplier_id
        LEFT JOIN purchase_supplier_grade sg on s.id = sg.supplier_id
        where s.id = #{id}
        group by s.id
    </select>

    <select id="selectEnabledSupplierName" resultType="net.summerfarm.model.vo.SupplierVO">
        select s.id, s.name,s.settle_form settleForm,s.tax_number taxNumber
        from supplier s where status = 0 and tenant_id=1
    </select>
    <select id="checkValidContract" resultType="java.lang.Integer">
        select count(1) from supplier s left join purchase_supplier_contract sc on s.id = sc.supplier_id where sc.valid = 1 and s.id = #{id}
    </select>
    <select id="selectEnableButNoContract" resultMap="BaseResultMap" parameterType="net.summerfarm.model.input.SupplierReq">
        SELECT s.id,s.name,s.address,s.category_array,s.product_array,s.manager,s.remark,s.contract,sa.account_name,s.supplier_type,s.tax_number,
        s.delivery_frequent,psc.settle_form,psc.settle_type,psc.custom_start_date,psc.custom_cycle,psc.credit_days,s.invoice,s.status
        FROM supplier s
        LEFT JOIN supplier_connect sc ON s.id = sc.supplier_id
        LEFT JOIN supplier_account sa on s.id = sa.supplier_id
        LEFT JOIN purchase_supplier_contract psc on s.id = psc.supplier_id
        <where>
            s.status != 2 and s.status != 3 and psc.`supplier_id` IS NULL and s.tenant_id=1
            <if test="phone != null">
                AND sc.phone = #{phone}
            </if>
            <if test="name !=null">
                AND s.name like CONCAT('%',#{name},'%')
            </if>
            <if test="categoryKey != null">
                AND s.category_array like CONCAT('%',#{categoryKey},'%')
            </if>
            <if test="manager != null">
                AND s.manager = #{manager}
            </if>
            <if test="status != null">
                AND s.status = #{status}
            </if>
        </where>
        GROUP BY s.id
        ORDER by s.id DESC
    </select>
    <select id="selectSupplierBaseInfoByInput" resultType="net.summerfarm.model.DTO.purchase.SupplierBaseInfoDTO">
        select id, name, address, `status` from supplier
        <where>
            tenant_id=1
            <if test="id != null">
                and id =#{id}
            </if>
            <if test="idList != null and idList.size > 0">
                and id in
                <foreach collection="idList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="nameStartWith != null and nameStartWith != ''">
                and name like concat(#{nameStartWith},'%')
            </if>
            <if test="nameLike != null and nameLike != ''">
                and name like concat('%',#{nameLike},'%')
            </if>
            <if test="status != null">
                and status =#{status}
            </if>
            <if test="statusList != null and statusList.size > 0">
                and status in
                <foreach collection="statusList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
    </where>
    </select>
    <select id="selectByPrimaryKeyList"  resultType="net.summerfarm.model.domain.Supplier">
        SELECT id,name,address,product_array productArray,remark,contract,delivery_frequent deliveryFrequent,
        settle_form settleForm,settle_type settleType,custom_start_date customStartDate,custom_cycle customCycle,credit_days creditDays
        ,supplier_type supplierType, status, qr_code_show_supplier_switch qrCodeShowSupplierSwitch
        FROM supplier
        WHERE id in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectWithSaasTenantId" resultType="net.summerfarm.model.vo.SupplierTenantVO">
    select  s.id as supplierId,
            s.name as supplierName,
            s.supplier_type as supplierType,
            s.tax_number as taxNum,
            s.create_time as createTime,
            s.creator as creator
    from supplier s
    where s.tenant_id = #{tenantId} and s.status = 0
        <if test="name != null and name != ''">
            and s.name like concat('%',#{name},'%')
        </if>
        <if test="supplierType!=null and supplierType==1">
            and s.supplier_type=1
        </if>
        <if test="supplierType!=null and supplierType!=1">
            and s.supplier_type!=1
        </if>
    order by s.id desc
    </select>

    <select id="countNameWithSaas" resultType="java.lang.Integer">
        select count(*) from supplier
        where name = #{supplierName} and status=0 and tenant_id = #{tenantId}
        <if test="id!=null">
            and id!=#{id}
        </if>
    </select>

    <select id="countTenantSupplier" resultType="java.lang.Integer">
        select count(*)
        from supplier s
        where s.tenant_id = #{tenantId} and   (s.tax_number = #{taxNum} or s.name = #{supplierName} )
             and s.status = 0
        <if test="id!=null">
            and s.id!=#{id}
        </if>
    </select>


    <insert id="insertWithSaasSource" parameterType="net.summerfarm.model.domain.Supplier" useGeneratedKeys="true" keyProperty="id">
        insert into supplier (name,supplier_type,tax_number,creator,source,tenant_id,remark,category_array)
        values (#{name},#{supplierType},#{taxNumber},#{creator},#{source},#{tenantId},#{remark},#{categoryArray})
    </insert>

    <update id="updateByIdWithSaas">
        update supplier set name=#{name},supplier_type=#{supplierType},tax_number=#{taxNumber},source=#{source},tenant_id=#{tenantId},remark=#{remark},category_array=#{categoryArray}
        where id=#{id}
    </update>

    <select id="allSuppliers" resultType="net.summerfarm.model.vo.SupplierTenantVO">
        select  s.id as supplierId,
                s.id as id,
                s.name as supplierName,
                s.supplier_type as supplierType,
                s.tax_number as taxNum,
                s.create_time as createTime,
                s.creator as creator
        from supplier s
        where s.tenant_id = #{tenantId} and s.status = 0
    </select>

    <select id="selectEnabledSupplierNameWithSource" resultType="net.summerfarm.model.vo.SupplierVO">
        select s.id, s.name,s.settle_form settleForm,s.tax_number taxNumber
        from supplier s where status = 0 and source = #{source}
    </select>

    <select id="selectSupplierNameWithSource" resultType="net.summerfarm.model.vo.SupplierVO">
        select s.id, s.name,s.settle_form settleForm,s.tax_number taxNumber,s.source
        from supplier s
    </select>

    <select id="listSupplier" resultType="net.summerfarm.model.vo.SupplierTenantVO" parameterType="net.summerfarm.model.input.SaasSupplierReq">
        select
        s.id as supplierId,
        s.name as supplierName,
        s.supplier_type as supplierType,
        s.tax_number as taxNum,
        s.create_time as createTime,
        s.creator as creator,
        s.status as status
        from supplier s
        <where>
            <if test="supplierId!=null">
                and s.id =#{supplierId}
            </if>
            <if test="supplierIds!=null and supplierIds.size!=0">
                and s.id in
                <foreach collection="supplierIds" item="supplierId" open="(" close=")" separator=",">
                    #{supplierId}
                </foreach>
            </if>
            <if test="tenantId!=null">
                and s.tenant_id=#{tenantId}
            </if>
            <if test="status!=null">
                and s.status=#{status}
            </if>
        </where>
    </select>
    <select id="selectReqByName" resultType="net.summerfarm.model.input.SupplierReq">
        select s.id, s.name,s.supplier_type supplierType,s.tax_number taxNumber
        from supplier s
        where `name` like concat('%', #{supplierName} '%');
    </select>

    <select id="selectByNameWithTenantId" resultType="net.summerfarm.model.domain.Supplier">
        SELECT id,name,address,product_array productArray,manager,remark,contract,
        settle_form settleForm,settle_type settleType,custom_start_date customStartDate,custom_cycle customCycle,credit_days creditDays
        FROM supplier
        WHERE name = #{name} and tenant_id=#{tenantId}
        limit 1
    </select>
</mapper>