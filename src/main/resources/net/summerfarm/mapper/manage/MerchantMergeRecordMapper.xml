<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.MerchantMergeRecordMapper">

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into merchant_merge_record(master_merchant_id, old_merchant_id)
        values (#{masterMerchantId}, #{oldMerchantId})
    </insert>

</mapper>
