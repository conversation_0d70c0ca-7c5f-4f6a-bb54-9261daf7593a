<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.SupplierContractMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.SupplierContract">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="settle_form" jdbcType="TINYINT" property="settleForm" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="settle_type" jdbcType="TINYINT" property="settleType" />
    <result column="custom_start_date" jdbcType="DATE" property="customStartDate" />
    <result column="custom_cycle" jdbcType="INTEGER" property="customCycle" />
    <result column="credit_days" jdbcType="INTEGER" property="creditDays" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, supplier_id, file_url, settle_form, start_date, end_date, settle_type,
    custom_start_date, custom_cycle, credit_days, status, valid, creator, create_time, updater, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_supplier_contract
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAuditByType" resultType="net.summerfarm.model.input.SupplierContractReq">
    select sc.id,s.name,sc.creator,sc.create_time createTime,sc.status, s.id supplierId
    FROM purchase_supplier_contract sc left join supplier s on s.id = sc.supplier_id
    <where>
      sc.status != 3
      <if test="name !=null">
        AND s.name like CONCAT('%',#{name},'%')
      </if>
      <if test="id != null">
        AND sc.id = #{id}
      </if>
      <if test="contractStatus != null">
        AND sc.status = #{contractStatus}
      </if>
      <if test="manager != null">
        AND sc.creator = #{manager}
      </if>
    </where>
    order by sc.id desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_supplier_contract
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.SupplierContract">
    insert into purchase_supplier_contract (id, supplier_id, file_url, 
      status, settle_form, start_date, 
      end_date, settle_type, custom_start_date, 
      custom_cycle, credit_days, creator, 
      create_time, updater, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{supplierId,jdbcType=INTEGER}, #{fileUrl,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{settleForm,jdbcType=TINYINT}, #{startDate,jdbcType=DATE}, 
      #{endDate,jdbcType=DATE}, #{settleType,jdbcType=TINYINT}, #{customStartDate,jdbcType=DATE}, 
      #{customCycle,jdbcType=INTEGER}, #{creditDays,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.SupplierContract" useGeneratedKeys="true" keyProperty="id">
    insert into purchase_supplier_contract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="settleForm != null">
        settle_form,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="settleType != null">
        settle_type,
      </if>
      <if test="customStartDate != null">
        custom_start_date,
      </if>
      <if test="customCycle != null">
        custom_cycle,
      </if>
      <if test="creditDays != null">
        credit_days,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="settleForm != null">
        #{settleForm,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DATE},
      </if>
      <if test="settleType != null">
        #{settleType,jdbcType=TINYINT},
      </if>
      <if test="customStartDate != null">
        #{customStartDate,jdbcType=DATE},
      </if>
      <if test="customCycle != null">
        #{customCycle,jdbcType=INTEGER},
      </if>
      <if test="creditDays != null">
        #{creditDays,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.SupplierContract">
    update purchase_supplier_contract
    <set>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="settleForm != null">
        settle_form = #{settleForm,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=DATE},
      </if>
      <if test="settleType != null">
        settle_type = #{settleType,jdbcType=TINYINT},
      </if>
      <if test="customStartDate != null">
        custom_start_date = #{customStartDate,jdbcType=DATE},
      </if>
      <if test="customCycle != null">
        custom_cycle = #{customCycle,jdbcType=INTEGER},
      </if>
      <if test="creditDays != null">
        credit_days = #{creditDays,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.SupplierContract">
    update purchase_supplier_contract
    set supplier_id = #{supplierId,jdbcType=INTEGER},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      settle_form = #{settleForm,jdbcType=TINYINT},
      start_date = #{startDate,jdbcType=DATE},
      end_date = #{endDate,jdbcType=DATE},
      settle_type = #{settleType,jdbcType=TINYINT},
      custom_start_date = #{customStartDate,jdbcType=DATE},
      custom_cycle = #{customCycle,jdbcType=INTEGER},
      credit_days = #{creditDays,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="expireAllCurrentContract">
    update purchase_supplier_contract set valid = 0 where supplier_id = #{supplierId} and status = 3
  </update>
    <update id="batchInvalid">
      update purchase_supplier_contract set valid = 0 where status = 3 and supplier_id in
      <foreach item="item" index="index" collection="list"
               open="(" separator="," close=")">
        #{item}
      </foreach>
    </update>
  <update id="expireContract">
    update purchase_supplier_contract set valid = 0 where status = 3 and valid = 1 and curdate() > end_date
  </update>

  <select id="selectFinishedBySupplierId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from purchase_supplier_contract where status = 3 and supplier_id = #{id}
  </select>
</mapper>