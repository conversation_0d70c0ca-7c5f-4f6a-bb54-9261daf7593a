<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockWarehouseChangeTaskMapper" >

  <insert id="batchInsertTask" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
    insert into stock_warehouse_change_task (store_no,cut_store_no,`type`,admin_id,create_time) VALUES
    <foreach collection="list" item="item" separator="," >
      (#{item.storeNo},#{item.cutStoreNo},#{item.type}, #{item.adminId}, now())
    </foreach>
  </insert>
    <update id="updateState">
    update stock_warehouse_change_task set state=#{state} where id=#{id}
    </update>
  <select id="selectTaskByIds" resultType="net.summerfarm.model.domain.StockWarehouseChangeTask">
    select id,create_time createTime ,store_no storeNo, cut_store_no cutStoreNo ,`type` ,state ,back_up_id backUpId, admin_id adminId
    from stock_warehouse_change_task
    where id  in
    <foreach collection="ids" open="(" close=")" separator="," item="it">
      #{it}
    </foreach>
  </select>

  <select id="selectTaskById" resultType="net.summerfarm.model.domain.StockWarehouseChangeTask">
    select create_time createTime ,store_no storeNo, cut_store_no cutStoreNo ,`type` ,state ,back_up_id backUpId, admin_id adminId
    from stock_warehouse_change_task
    where id  = #{cutTaskId}
  </select>
</mapper>