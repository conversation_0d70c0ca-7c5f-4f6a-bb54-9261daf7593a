<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MajorPriceAdjustmentRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MajorPriceAdjustmentRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="interest_rate" jdbcType="DECIMAL" property="interestRate" />
    <result column="fixed_price" jdbcType="DECIMAL" property="fixedPrice" />
    <result column="original_cost_price" jdbcType="DECIMAL" property="originalCostPrice" />
    <result column="cost_price" jdbcType="DECIMAL" property="costPrice" />
    <result column="original_market_price" jdbcType="DECIMAL" property="originalMarketPrice" />
    <result column="market_price" jdbcType="DECIMAL" property="marketPrice" />
    <result column="major_cycle" jdbcType="INTEGER" property="majorCycle" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="create_admin_name" jdbcType="VARCHAR" property="createAdminName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, admin_id, area_no, sku, status, interest_rate, fixed_price, original_cost_price, 
    cost_price, original_market_price, market_price, major_cycle, reason, create_admin_name, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from major_price_adjustment_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from major_price_adjustment_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.MajorPriceAdjustmentRecord">
    insert into major_price_adjustment_record (id, admin_id, area_no, 
      sku, status, interest_rate, 
      fixed_price, original_cost_price, cost_price, 
      original_market_price, market_price, major_cycle, 
      reason, create_admin_name, create_time
      )
    values (#{id,jdbcType=INTEGER}, #{adminId,jdbcType=INTEGER}, #{areaNo,jdbcType=INTEGER}, 
      #{sku,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{interestRate,jdbcType=DECIMAL},
      #{fixedPrice,jdbcType=DECIMAL}, #{originalCostPrice,jdbcType=DECIMAL}, #{costPrice,jdbcType=DECIMAL}, 
      #{originalMarketPrice,jdbcType=DECIMAL}, #{marketPrice,jdbcType=DECIMAL}, #{majorCycle,jdbcType=INTEGER}, 
      #{reason,jdbcType=VARCHAR}, #{createAdminName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.MajorPriceAdjustmentRecord">
    insert into major_price_adjustment_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="interestRate != null">
        interest_rate,
      </if>
      <if test="fixedPrice != null">
        fixed_price,
      </if>
      <if test="originalCostPrice != null">
        original_cost_price,
      </if>
      <if test="costPrice != null">
        cost_price,
      </if>
      <if test="originalMarketPrice != null">
        original_market_price,
      </if>
      <if test="marketPrice != null">
        market_price,
      </if>
      <if test="majorCycle != null">
        major_cycle,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="createAdminName != null">
        create_admin_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="interestRate != null">
        #{interestRate,jdbcType=DECIMAL},
      </if>
      <if test="fixedPrice != null">
        #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="originalCostPrice != null">
        #{originalCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="originalMarketPrice != null">
        #{originalMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="marketPrice != null">
        #{marketPrice,jdbcType=DECIMAL},
      </if>
      <if test="majorCycle != null">
        #{majorCycle,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createAdminName != null">
        #{createAdminName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.MajorPriceAdjustmentRecord">
    update major_price_adjustment_record
    <set>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="interestRate != null">
        interest_rate = #{interestRate,jdbcType=DECIMAL},
      </if>
      <if test="fixedPrice != null">
        fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="originalCostPrice != null">
        original_cost_price = #{originalCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        cost_price = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="originalMarketPrice != null">
        original_market_price = #{originalMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="marketPrice != null">
        market_price = #{marketPrice,jdbcType=DECIMAL},
      </if>
      <if test="majorCycle != null">
        major_cycle = #{majorCycle,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createAdminName != null">
        create_admin_name = #{createAdminName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.MajorPriceAdjustmentRecord">
    update major_price_adjustment_record
    set admin_id = #{adminId,jdbcType=INTEGER},
      area_no = #{areaNo,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      interest_rate = #{interestRate,jdbcType=DECIMAL},
      fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      original_cost_price = #{originalCostPrice,jdbcType=DECIMAL},
      cost_price = #{costPrice,jdbcType=DECIMAL},
      original_market_price = #{originalMarketPrice,jdbcType=DECIMAL},
      market_price = #{marketPrice,jdbcType=DECIMAL},
      major_cycle = #{majorCycle,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR},
      create_admin_name = #{createAdminName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>