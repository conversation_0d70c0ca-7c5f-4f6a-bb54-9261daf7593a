<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.SupplierCoordinationConfigMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.SupplierCoordinationConfig">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="supplierId" column="supplier_id" jdbcType="BIGINT"/>
        <result property="poCoordinationTab" column="po_coordination_tab" jdbcType="TINYINT"/>
        <result property="inboundCoordinationTab" column="inbound_coordination_tab" jdbcType="TINYINT"/>
        <result property="reconciliationCoordinationTab" column="reconciliation_coordination_tab" jdbcType="TINYINT"/>
        <result property="invoiceCoordinationTab" column="invoice_coordination_tab" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id as id,
        supplier_id as supplierId,
        po_coordination_tab as poCoordinationTab,
        inbound_coordination_tab as inboundCoordinationTab,
        reconciliation_coordination_tab as reconciliationCoordinationTab,
        invoice_coordination_tab as invoiceCoordinationTab,
        create_time as createTime,
        update_time as updateTime,
        creator as creator,
        updater as updater
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from supplier_coordination_config
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from supplier_coordination_config
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.model.domain.SupplierCoordinationConfig" useGeneratedKeys="true">
        insert into supplier_coordination_config
        ( id, supplier_id, po_coordination_tab
        , inbound_coordination_tab, reconciliation_coordination_tab, invoice_coordination_tab
        , create_time, update_time, creator
        , updater)
        values ( #{id,jdbcType=BIGINT}, #{supplierId,jdbcType=BIGINT}, #{poCoordinationTab,jdbcType=TINYINT}
               , #{inboundCoordinationTab,jdbcType=TINYINT}, #{reconciliationCoordinationTab,jdbcType=TINYINT}
               , #{invoiceCoordinationTab,jdbcType=TINYINT}
               , #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}
               , #{updater,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.model.domain.SupplierCoordinationConfig" useGeneratedKeys="true">
        insert into supplier_coordination_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="supplierId != null">supplier_id,</if>
            <if test="poCoordinationTab != null">po_coordination_tab,</if>
            <if test="inboundCoordinationTab != null">inbound_coordination_tab,</if>
            <if test="reconciliationCoordinationTab != null">reconciliation_coordination_tab,</if>
            <if test="invoiceCoordinationTab != null">invoice_coordination_tab,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="updater != null">updater,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="supplierId != null">#{supplierId,jdbcType=BIGINT},</if>
            <if test="poCoordinationTab != null">#{poCoordinationTab,jdbcType=TINYINT},</if>
            <if test="inboundCoordinationTab != null">#{inboundCoordinationTab,jdbcType=TINYINT},</if>
            <if test="reconciliationCoordinationTab != null">#{reconciliationCoordinationTab,jdbcType=TINYINT},</if>
            <if test="invoiceCoordinationTab != null">#{invoiceCoordinationTab,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
            <if test="updater != null">#{updater,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.SupplierCoordinationConfig">
        update supplier_coordination_config
        <set>
            <if test="supplierId != null">
                supplier_id = #{supplierId,jdbcType=BIGINT},
            </if>
            <if test="poCoordinationTab != null">
                po_coordination_tab = #{poCoordinationTab,jdbcType=TINYINT},
            </if>
            <if test="inboundCoordinationTab != null">
                inbound_coordination_tab = #{inboundCoordinationTab,jdbcType=TINYINT},
            </if>
            <if test="reconciliationCoordinationTab != null">
                reconciliation_coordination_tab = #{reconciliationCoordinationTab,jdbcType=TINYINT},
            </if>
            <if test="invoiceCoordinationTab != null">
                invoice_coordination_tab = #{invoiceCoordinationTab,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.SupplierCoordinationConfig">
        update supplier_coordination_config
        set supplier_id                     = #{supplierId,jdbcType=BIGINT},
            po_coordination_tab             = #{poCoordinationTab,jdbcType=TINYINT},
            inbound_coordination_tab        = #{inboundCoordinationTab,jdbcType=TINYINT},
            reconciliation_coordination_tab = #{reconciliationCoordinationTab,jdbcType=TINYINT},
            invoice_coordination_tab        = #{invoiceCoordinationTab,jdbcType=TINYINT},
            create_time                     = #{createTime,jdbcType=TIMESTAMP},
            update_time                     = #{updateTime,jdbcType=TIMESTAMP},
            creator                         = #{creator,jdbcType=VARCHAR},
            updater                         = #{updater,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateBySupplierIdSelective" parameterType="net.summerfarm.model.domain.SupplierCoordinationConfig">
        update supplier_coordination_config
        <set>
            <if test="supplierId != null">
                supplier_id = #{supplierId,jdbcType=BIGINT},
            </if>
            <if test="poCoordinationTab != null">
                po_coordination_tab = #{poCoordinationTab,jdbcType=TINYINT},
            </if>
            <if test="inboundCoordinationTab != null">
                inbound_coordination_tab = #{inboundCoordinationTab,jdbcType=TINYINT},
            </if>
            <if test="reconciliationCoordinationTab != null">
                reconciliation_coordination_tab = #{reconciliationCoordinationTab,jdbcType=TINYINT},
            </if>
            <if test="invoiceCoordinationTab != null">
                invoice_coordination_tab = #{invoiceCoordinationTab,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
        </set>
        where supplier_id = #{supplierId,jdbcType=BIGINT}
    </update>

    <select id="selectBySupplierId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from supplier_coordination_config
        where supplier_id=#{supplierId}
    </select>

</mapper>
