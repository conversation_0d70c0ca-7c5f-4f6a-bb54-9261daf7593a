<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.SettlementPaymentRecordMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.SettlementPaymentRecord" >
    <result column="id" property="id" jdbcType="INTEGER" />
    <result column="settlement_id" property="settlementId" jdbcType="INTEGER" />
    <result column="amount" property="amount" jdbcType="DECIMAL" />
    <result column="expected_time" property="expectedTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="settlement_count" property="settlementCount" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="creator_adminId" property="creatorAdminId" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="auditor" property="auditor" jdbcType="VARCHAR" />
    <result column="auditor_adminId" property="auditorAdminId" jdbcType="INTEGER"/>
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
    <result column="approver" property="approver" jdbcType="VARCHAR" />
    <result column="approver_adminId" property="approverAdminId" jdbcType="INTEGER"/>
    <result column="approver_time" property="approverTime" jdbcType="TIMESTAMP" />
    <result column="payer" property="payer" jdbcType="VARCHAR" />
    <result column="payer_adminId" property="payerAdminId" jdbcType="INTEGER" />
    <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
    <result column="payment_voucher" property="paymentVoucher" jdbcType="VARCHAR" />
  </resultMap>


  <sql id="Base_filed">
      id,settlement_id,amount,expected_time,remark,settlement_count,status,creator,creator_adminId,create_time,auditor,auditor_adminId,audit_time,
       approver,approver_adminId,approver_time,payer,payer_adminId,pay_time,payment_voucher
  </sql>

  <insert id="insert" parameterType="net.summerfarm.model.domain.SettlementPaymentRecord" >
    insert into settlement_payment_record (id, settlement_id, amount,
      expected_time, remark, settlement_count,
      status, creator, create_time,
      auditor, audit_time, payer,
      pay_time, payment_voucher)
    values (#{id,jdbcType=INTEGER}, #{settlementId,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL},
      #{expectedTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{settlementCount,jdbcType=INTEGER},
      #{status,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{auditor,jdbcType=VARCHAR}, #{auditTime,jdbcType=TIMESTAMP}, #{payer,jdbcType=VARCHAR},
      #{payTime,jdbcType=TIMESTAMP}, #{paymentVoucher,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.SettlementPaymentRecord" useGeneratedKeys="true" keyColumn="id">
    insert into settlement_payment_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="settlementId != null" >
        settlement_id,
      </if>
      <if test="amount != null" >
        amount,
      </if>
      <if test="expectedTime != null" >
        expected_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="settlementCount != null" >
        settlement_count,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="creatorAdminId != null" >
        creator_adminId,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="auditor != null" >
        auditor,
      </if>
      <if test="auditorAdminId != null" >
        auditor_adminId,
      </if>
      <if test="auditTime != null" >
        audit_time,
      </if>
      <if test="approver != null" >
          approver,
      </if>
      <if test="approverAdminId != null" >
        approver_adminId,
      </if>
      <if test="approverTime != null" >
        approver_time,
      </if>
      <if test="payer != null" >
        payer,
      </if>
      <if test="payerAdminId != null" >
        payer_adminId,
      </if>
      <if test="payTime != null" >
        pay_time,
      </if>
      <if test="paymentVoucher != null" >
        payment_voucher,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="settlementId != null" >
        #{settlementId,jdbcType=INTEGER},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="expectedTime != null" >
        #{expectedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="settlementCount != null" >
        #{settlementCount,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatorAdminId != null" >
        #{creatorAdminId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null" >
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditorAdminId != null" >
        #{auditorAdminId,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null" >
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approver != null" >
        #{approver,jdbcType=VARCHAR},
      </if>
      <if test="approverAdminId != null" >
        #{approverAdminId,jdbcType=INTEGER},
      </if>
      <if test="approverTime != null" >
        #{approverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payer != null" >
        #{payer,jdbcType=VARCHAR},
      </if>
      <if test="payerAdminId != null" >
        #{payerAdminId,jdbcType=INTEGER},
      </if>
      <if test="payTime != null" >
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentVoucher != null" >
        #{paymentVoucher,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateSelective" parameterType="net.summerfarm.model.domain.SettlementPaymentRecord">
    update settlement_payment_record
    <set>
      <if test="settlementId != null" >
        settlement_id = #{settlementId,jdbcType=INTEGER},
      </if>
      <if test="amount != null" >
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="expectedTime != null" >
        expected_time = #{expectedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="settlementCount != null" >
        settlement_count = #{settlementCount,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null" >
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditorAdminId != null" >
        auditor_adminId = #{auditorAdminId,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null" >
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approver != null" >
        approver = #{approver,jdbcType=VARCHAR},
      </if>
      <if test="approverAdminId != null" >
        approver_adminId = #{approverAdminId,jdbcType=INTEGER},
      </if>
      <if test="approverTime != null" >
        approver_time = #{approverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payer != null" >
        payer = #{payer,jdbcType=VARCHAR},
      </if>
      <if test="payerAdminId != null" >
        payer_adminId = #{payerAdminId,jdbcType=INTEGER},
      </if>
      <if test="payTime != null" >
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentVoucher != null" >
        payment_voucher = #{paymentVoucher,jdbcType=VARCHAR},
      </if>
      <if test="withdrawTime != null" >
        withdraw_time = #{withdrawTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id}
  </update>
  <select id="selectBySettlementId" parameterType="integer" resultMap="BaseResultMap">
    select <include refid="Base_filed"/>
    from settlement_payment_record where settlement_id = #{settlementId}
  </select>

  <select id="selectById" parameterType="integer" resultMap="BaseResultMap">
    select <include refid="Base_filed"/>
    from settlement_payment_record where id = #{recordId}
  </select>

  <select id="inSettleTotal" resultType="java.math.BigDecimal">
    select ifnull(sum(amount), 0) inSettle from settlement_payment_record where status in (1, 3)
  </select>


    <select id="selectRemainNewHandle" resultType="net.summerfarm.model.domain.SettlementPaymentRecord">
      select
           count(1) count,
           status,
           settlement_id as settlementId,
           creator,
           creator_adminId as creatorAdminId,
           create_time as createTime,
           auditor_adminId as auditorAdminId,
           auditor,
           audit_time as auditTime,
           approver_adminId as approverAdminId,
           approver,
           approver_adminId as approverAdminId,
           approver_time as approverTime,
           payer_adminId as payerAdminId,
           payer
        from settlement_payment_record where status in (1,3,6) GROUP BY status,case when status='1' then auditor when status='3' then payer else approver end;
    </select>

  <select id="selectInSettle" resultMap="BaseResultMap">
    select <include refid="Base_filed"></include>
    from settlement_payment_record where status in (1,3,6)
  </select>

  <select id="selectStatus" parameterType="integer" resultType="decimal">
    select ifnull(sum(amount),0)
    from settlement_payment_record
    where status = 5 and settlement_id = #{settlementId}
  </select>

</mapper>
