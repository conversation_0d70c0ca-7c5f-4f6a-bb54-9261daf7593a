<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.BusinessMissionMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.BusinessMission">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="gmv" property="gmv" jdbcType="DOUBLE"/>
        <result column="year" property="year" jdbcType="INTEGER"/>
        <result column="month" property="month" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, gmv, year, month, update_time,area_no
  </sql>


    <select id="select" resultType="net.summerfarm.model.domain.BusinessMission" parameterType="net.summerfarm.model.domain.BusinessMission">
        SELECT
        t.id, t.gmv, t.year, t.month,t.area_no areaNo,
        a.area_name areaName
        FROM business_mission t
        LEFT JOIN area a on t.area_no=a.area_no
        <where>
            <if test="year!=null">
                AND t.year = #{year}
            </if>
            <if test="month!=null">
                AND t.month = #{month}
            </if>
            <if test="areaNo!=null">
                AND t.area_no = #{areaNo}
            </if>
        </where>
        ORDER BY  t.update_time desc
    </select>

    <!--auto-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from business_mission
        where id = #{id,jdbcType=INTEGER}
    </select>

    <insert id="insert" parameterType="net.summerfarm.model.domain.BusinessMission">
    insert into business_mission (id, gmv, year, month, 
      update_time,area_no)
    values (#{id,jdbcType=INTEGER}, #{gmv,jdbcType=DOUBLE}, #{year,jdbcType=INTEGER}, #{month,jdbcType=INTEGER},
      #{updateTime,jdbcType=TIMESTAMP},#{areaNo})
  </insert>


    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.BusinessMission">
    update business_mission
    set gmv = #{gmv,jdbcType=DOUBLE},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>