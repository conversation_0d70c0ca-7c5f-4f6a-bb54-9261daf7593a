<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceAccountingPeriodOrderMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceAccountingPeriodOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bill_generation_time" jdbcType="TIMESTAMP" property="billGenerationTime" />
    <result column="bill_number" jdbcType="CHAR" property="billNumber" />
    <result column="invoice_id" jdbcType="BIGINT" property="invoiceId" />
    <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle" />
    <result column="name_remakes" jdbcType="VARCHAR" property="nameRemakes" />
    <result column="bill_confirmation_time" jdbcType="TIMESTAMP" property="billConfirmationTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="saler_id" jdbcType="INTEGER" property="salerId" />
    <result column="saler_name" jdbcType="VARCHAR" property="salerName" />
    <result column="after_sale_amount" jdbcType="DECIMAL" property="afterSaleAmount" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="store_quantity" jdbcType="INTEGER" property="storeQuantity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="bill_cycle" jdbcType="VARCHAR" property="billCycle" />
    <result column="out_times_fee" jdbcType="DECIMAL" property="outTimesFee" />
  </resultMap>

  <resultMap id="PeriodVoMap" type="net.summerfarm.model.vo.finance.FinanceAccountingPeriodOrderResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bill_generation_time" jdbcType="TIMESTAMP" property="billGenerationTime" />
    <result column="bill_number" jdbcType="CHAR" property="billNumber" />
    <result column="invoice_id" jdbcType="BIGINT" property="invoiceId" />
    <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle" />
    <result column="name_remakes" jdbcType="VARCHAR" property="nameRemakes" />
    <result column="bill_confirmation_time" jdbcType="TIMESTAMP" property="billConfirmationTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="saler_id" jdbcType="INTEGER" property="salerId" />
    <result column="saler_name" jdbcType="VARCHAR" property="salerName" />
    <result column="after_sale_amount" jdbcType="DECIMAL" property="afterSaleAmount" />
    <result column="bill_cycle" jdbcType="VARCHAR" property="billCycle" />
    <result column="receivableAmount" jdbcType="DECIMAL" property="receivableAmount" />
    <result column="adjustmentAmount" jdbcType="DECIMAL" property="adjustmentAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, bill_generation_time, bill_number, invoice_id, invoice_title, name_remakes, bill_confirmation_time,
    `type`, admin_id, saler_name, after_sale_amount, delivery_fee, total_price, store_quantity,
    create_time, creator, update_time, updater, bill_cycle,out_times_fee,saler_id
  </sql>

  <select id="selectByAdmin" parameterType="java.lang.Long" resultType="string">
    select distinct invoice_title invoiceTitle
    from finance_accounting_period_order
    where admin_id = #{adminId}
  </select>

  <select id="selectBySaleName" parameterType="java.lang.Long" resultType="string">
    select distinct saler_name salerName
    from finance_accounting_period_order
    where admin_id = #{adminId}
  </select>

  <select id="selectList" parameterType="net.summerfarm.model.input.AccountingPeriodOrderQuery" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select fapo.id id,fapo.bill_generation_time billGenerationTime,fapo.update_time updateTime,fapo.bill_number billNumber,fapo.bill_cycle billCycle,fapo.total_price totalPrice,fapo.delivery_fee deliveryFee,
           fapo.invoice_title invoiceTitle,fapo.name_remakes nameRemakes,fapo.saler_name salerName,fapo.saler_id salerId,fapo.store_quantity storeQuantity,fapo.after_sale_amount afterSaleAmount,fapo.out_times_fee outTimesFee,
           fapo.receipt_status receiptStatus,fapo.admin_id adminId,fapo.type,fapo.customer_confirm_status customerConfirmStatus,fapo.write_off_amount writeOffAmount
    from finance_accounting_period_order fapo
    left join finance_accounting_store fas on fapo.id = fas.finance_order_id
    left join finance_accounting_store_detail fasd on fas.id = fasd.finance_accounting_store_id
    <where>
      <if test="salerId != null">
        and fapo.saler_id = #{salerId}
      </if>
      <if test="startTime != null">
        and fapo.bill_generation_time <![CDATA[>=]]> #{startTime}
      </if>
      <if test="endTime != null">
        and fapo.bill_generation_time <![CDATA[<=]]> #{endTime}
      </if>
      <if test="billNumber != null">
        and fapo.bill_number = #{billNumber}
      </if>
      <if test="mId != null">
        and fas.m_id = #{mId}
      </if>
      <if test="mname != null">
        and fas.mname = #{mname}
      </if>
      <if test="invoiceTitle != null">
        and fapo.invoice_title like  concat('%',#{invoiceTitle},'%')
      </if>
      <if test="nameRemakes != null">
        and fapo.name_remakes = #{nameRemakes}
      </if>
      <if test="areaNo != null">
        and fas.area_no = #{areaNo}
      </if>
      <if test="selectName != null">
        and (fapo.saler_name LIKE CONCAT(#{selectName},'%') or fapo.name_remakes LIKE CONCAT(#{selectName},'%'))
      </if>
      <if test="salerName != null">
        and fapo.saler_name = #{salerName}
      </if>
      <if test="orderNo != null">
        and fasd.order_no = #{orderNo}
      </if>
      <if test="receiptStatusList !=null and receiptStatusList.size > 0">
        and fapo.receipt_status in
        <foreach collection="receiptStatusList" separator="," open="(" close=")" item="receiptStatus">
          #{receiptStatus}
        </foreach>
      </if>
      <if test="periodOrderType == 1">
        and fapo.type = 0
        and fapo.customer_confirm_status = 0
      </if>
      <if test="periodOrderType == 2">
        and fapo.type = 0
        and fapo.customer_confirm_status = 1
        and fapo.financial_audit = 0
      </if>
      <if test="periodOrderType == 3">
        and fapo.type = 1
        and fapo.customer_confirm_status = 1
        and fapo.financial_audit = 1
        and fapo.receipt_status in (0,1)
      </if>
      <if test="periodOrderType == 4">
        and fapo.type = 1
        and fapo.customer_confirm_status = 1
        and fapo.financial_audit = 1
        and fapo.receipt_status = 2
      </if>
    </where>
    group by fapo.id
    order by
    <choose>
      <when test="orderType == 0">fapo.update_time DESC</when>
      <when test="orderType == 1">fapo.bill_generation_time DESC</when>
      <otherwise>fapo.update_time DESC</otherwise>
    </choose>
  </select>

  <select id="selectToday" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select ifnull(sum(store_quantity),0) storeQuantity , count(*) `size`
    from finance_accounting_period_order
    where bill_generation_time <![CDATA[>=]]> #{localDateTime} and `type` = 0
  </select>

  <select id="selectOverdue" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select sum(store_quantity) storeQuantity , count(*) `size`
    from finance_accounting_period_order
    where bill_generation_time <![CDATA[<=]]> #{localDateTime} and `type` = 0
  </select>

  <delete id="deleteZeroOrders">
    delete from finance_accounting_period_order
    where after_sale_amount = 0 and delivery_fee = 0 and total_price = 0
  </delete>
  <delete id="deleteById">
    delete
    from finance_accounting_period_order
    where id = #{id}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAccountingPeriodOrder" useGeneratedKeys="true">
    insert into finance_accounting_period_order (bill_generation_time, bill_number, invoice_id,
      invoice_title, name_remakes, bill_confirmation_time,
      `type`, admin_id, saler_name,
      after_sale_amount, delivery_fee, total_price,
      store_quantity, create_time, creator,
      update_time, updater,
      bill_cycle)
    values (#{billGenerationTime,jdbcType=TIMESTAMP}, #{billNumber,jdbcType=CHAR}, #{invoiceId,jdbcType=BIGINT},
      #{invoiceTitle,jdbcType=VARCHAR}, #{nameRemakes,jdbcType=VARCHAR}, #{billConfirmationTime,jdbcType=TIMESTAMP},
      #{type,jdbcType=INTEGER}, #{adminId,jdbcType=INTEGER}, #{salerName,jdbcType=VARCHAR},
      #{afterSaleAmount,jdbcType=DECIMAL}, #{deliveryFee,jdbcType=DECIMAL}, #{totalPrice,jdbcType=DECIMAL},
      #{storeQuantity,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR},
      #{billCycle,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAccountingPeriodOrder" useGeneratedKeys="true">
    insert into finance_accounting_period_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="billGenerationTime != null">
        bill_generation_time,
      </if>
      <if test="billNumber != null">
        bill_number,
      </if>
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="invoiceTitle != null">
        invoice_title,
      </if>
      <if test="nameRemakes != null">
        name_remakes,
      </if>
      <if test="billConfirmationTime != null">
        bill_confirmation_time,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="salerName != null">
        saler_name,
      </if>
      <if test="afterSaleAmount != null">
        after_sale_amount,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="storeQuantity != null">
        store_quantity,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="billCycle != null">
        bill_cycle,
      </if>
      <if test="salerId != null">
        saler_id,
      </if>
      <if test="customerType != null">
        customer_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="billGenerationTime != null">
        #{billGenerationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billNumber != null">
        #{billNumber,jdbcType=CHAR},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=BIGINT},
      </if>
      <if test="invoiceTitle != null">
        #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="nameRemakes != null">
        #{nameRemakes,jdbcType=VARCHAR},
      </if>
      <if test="billConfirmationTime != null">
        #{billConfirmationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="salerName != null">
        #{salerName,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleAmount != null">
        #{afterSaleAmount,jdbcType=DECIMAL},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="storeQuantity != null">
        #{storeQuantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="billCycle != null">
        #{billCycle,jdbcType=VARCHAR},
      </if>
      <if test="salerId != null">
        #{salerId},
      </if>
      <if test="customerType != null">
        #{customerType}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceAccountingPeriodOrder">
    update finance_accounting_period_order
    <set>
      <if test="billGenerationTime != null">
        bill_generation_time = #{billGenerationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billNumber != null">
        bill_number = #{billNumber,jdbcType=CHAR},
      </if>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=BIGINT},
      </if>
      <if test="invoiceTitle != null">
        invoice_title = #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="nameRemakes != null">
        name_remakes = #{nameRemakes,jdbcType=VARCHAR},
      </if>
      <if test="billConfirmationTime != null">
        bill_confirmation_time = #{billConfirmationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="salerId != null">
        saler_id = #{salerId,jdbcType=INTEGER},
      </if>
      <if test="salerName != null">
        saler_name = #{salerName,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleAmount != null">
        after_sale_amount = #{afterSaleAmount,jdbcType=DECIMAL},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="storeQuantity != null">
        store_quantity = #{storeQuantity,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="billCycle != null">
        bill_cycle = #{billCycle,jdbcType=VARCHAR},
      </if>
      <if test="receiptStatus != null">
        receipt_status = #{receiptStatus},
      </if>
      <if test="customerConfirmStatus != null">
        customer_confirm_status = #{customerConfirmStatus},
      </if>
      <if test="writeOffAmount != null">
        write_off_amount = #{writeOffAmount},
      </if>
      <if test="financialAudit != null">
        financial_audit = #{financialAudit},
      </if>
      <if test="salesInvoicing != null">
        sales_invoicing = #{salesInvoicing},
      </if>
      <if test="remarks != null">
        remarks = #{remarks},
      </if>
      <if test="outTimesFee != null">
        out_times_fee = #{outTimesFee},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceAccountingPeriodOrder">
    update finance_accounting_period_order
    set after_sale_amount = #{afterSaleAmount},
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
        total_price = #{totalPrice,jdbcType=DECIMAL},
        out_times_fee = #{outTimesFee},
        store_quantity = #{storeQuantity}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByQuantity" parameterType="long">
    update finance_accounting_period_order
    set store_quantity = store_quantity - 1,
        update_time = now()
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectOne" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from finance_accounting_period_order
    <where>
      <if test="billNumber != null">
        bill_number = #{billNumber}
      </if>
    </where>
  </select>

  <select id="selectByBillNumber" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select fapd.bill_cycle    billCycle,
           fapd.id              id,
           fapd.bill_generation_time billGenerationTime,
           fapd.update_time   updateTime,
           fapd.name_remakes  nameRemakes,
           fapd.invoice_title invoiceTitle,
           fapd.total_price   totalPrice,
           fapd.delivery_fee  deliveryFee,
           fapd.after_sale_amount  afterSaleAmount,
           fapd.type          type,
           ic.tax_number      taxNumber,
           fapd.write_off_amount      writeOffAmount,
           sum(ifnull(fa.total_price,0))     adjustPrice,
           sum(ifnull(fa.total_delivery,0))  adjustDelivery,
           sum(ifnull(fa.total,0)) total,
           store_quantity storeQuantity
    from finance_accounting_period_order fapd
           left join finance_adjustment fa on fapd.bill_number = fa.bill_number and fa.status = 1
           left join invoice_config ic on ic.id = fapd.invoice_id
    where fapd.bill_number = #{billNumber}
  </select>

  <select id="selectOrderNoList" resultType="java.lang.String">
    select fasd.order_no
    from finance_accounting_period_order fapo
    left join finance_accounting_store fas on fapo.id = fas.finance_order_id
    left join finance_accounting_store_detail fasd on fas.id = fasd.finance_accounting_store_id
    where fapo.bill_number = #{billNumber}
  </select>

  <select id="selectById" parameterType="long" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select id,
           bill_generation_time    billGenerationTime,
           bill_number             billNumber,
           invoice_id              invoiceId,
           invoice_title           invoiceTitle,
           name_remakes            nameRemakes,
           bill_confirmation_time  billConfirmationTime,
           `type`,
           admin_id                adminId,
           saler_id                salerId,
           saler_name              salerName,
           after_sale_amount       afterSaleAmount,
           delivery_fee            deliveryFee,
           total_price             totalPrice,
           store_quantity          storeQuantity,
           create_time             createTime,
           creator                 creator,
           update_time             updateTime,
           updater                 updater,
           bill_cycle              billCycle,
           out_times_fee           outTimesFee,
           receipt_status          receiptStatus,
           customer_confirm_status customerConfirmStatus,
           write_off_amount        writeOffAmount,
           saler_id                salerId,
           financial_audit         financialAudit,
           sales_invoicing         salesInvoicing
    from finance_accounting_period_order
    where id = #{id} and `type` = 0
  </select>

  <select id="selectBillNumber" parameterType="string" resultType="integer">
    SELECT COUNT(*)
    FROM finance_accounting_period_order
    where bill_number like concat(#{billNumber},'%')
  </select>

  <select id="selectMoney" parameterType="long" resultType="net.summerfarm.model.domain.FinanceAccountingPeriodOrder">
    SELECT IFNULL(sum(fa.after_sale_money),0) afterSaleAmount,IFNULL(sum(fa.delivery_fee),0) deliveryFee,
           IFNULL(sum(fa.total_price),0) totalPrice,IFNULL(sum(fa.out_times_fee),0) outTimesFee,ifnull(count(fa.m_id), 0) storeQuantity
    FROM finance_accounting_store fa
    where fa.finance_order_id = #{id}
  </select>

  <select id="selectConfirm" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select fapo.bill_cycle                   billCycle,
           fapo.bill_number                  billNumber,
           fapo.update_time                  updateTime,
           fapo.total_price                  totalPrice,
           fapo.delivery_fee                 deliveryFee,
           fapo.after_sale_amount            afterSaleAmount,
           fapo.type                         type,
           sum(ifnull(fa.total_price, 0))    adjustPrice,
           sum(ifnull(fa.total_delivery, 0)) adjustDelivery,
           sum(ifnull(fa.total, 0))          total
    from finance_accounting_period_order fapo
           left join finance_adjustment fa on fapo.bill_number = fa.bill_number
    where fapo.type = 1 and fa.status = 1
    group by fapo.bill_number
  </select>

  <select id="selectByDownload" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select id,
           bill_generation_time   billGenerationTime,
           bill_number            billNumber,
           invoice_id             invoiceId,
           invoice_title          invoiceTitle,
           name_remakes           nameRemakes,
           bill_confirmation_time billConfirmationTime,
           `type`,
           admin_id               adminId,
           saler_name             salerName,
           after_sale_amount      afterSaleAmount,
           delivery_fee           deliveryFee,
           total_price            totalPrice,
           store_quantity         storeQuantity,
           create_time            createTime,
           creator                creator,
           update_time            updateTime,
           updater                updater,
           bill_cycle             billCycle,
           out_times_fee          outTimesFee,
           receipt_status          receiptStatus,
           customer_confirm_status customerConfirmStatus,
           write_off_amount        writeOffAmount,
           saler_id                salerId,
           financial_audit         financialAudit,
           sales_invoicing         salesInvoicing
    from finance_accounting_period_order
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="type != null">
        and `type` = #{type}
      </if>
    </where>
  </select>

  <select id="selectBillNumberList" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select distinct fapo.id          id,
                    fapo.bill_number billNumber
    from finance_accounting_period_order fapo
           left join finance_accounting_store_detail fasd on fasd.bill_number = fapo.bill_number
           left join orders o on o.order_no = fasd.order_no
    where fapo.admin_id = #{adminId}
      and fapo.invoice_id = #{invoiceId}
      and fapo.type = 1
      and o.invoice_status = 0
  </select>

  <select id="selectBySalerIdList" parameterType="net.summerfarm.model.input.AccountingPeriodOrderQuery" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select distinct fapo.id id,
        case
          when fapo.admin_id is null then fas.mname
          else fapo.name_remakes end nameRemakes,
        fapo.update_time updateTime,
        fapo.invoice_title invoiceTitle,
        fapo.invoice_id invoiceId,
        fapo.bill_cycle billCycle,
        fapo.receipt_status receiptStatus,
        fapo.customer_confirm_status customerConfirmStatus,
        ad.bill_to_pay billToPay,
        fapo.bill_number billNumber,
        fapo.type,
        fapo.financial_audit financialAudit,
        fapo.total_price totalPrice
    from finance_accounting_period_order fapo
    left join finance_accounting_store fas on fas.finance_order_id = fapo.id
    left join `admin` ad on ad.admin_id = fapo.admin_id
    <where>
      fapo.saler_id = #{salerId} and fapo.`type` <![CDATA[<>]]> 2
      <if test="nameRemakes != null">
        and fapo.name_remakes like concat(#{nameRemakes}, '%')
      </if>
      <if test="customerConfirmStatus != null">
        and fapo.customer_confirm_status = #{customerConfirmStatus}
      </if>
      <if test="receiptStatus != null">
        and fapo.receipt_status = #{receiptStatus}
      </if>
      <if test="financialAudit != null">
      and fapo.financial_audit = #{financialAudit}
      </if>
      <if test="periodOrderType == 1">
        and fapo.type = 0
        and fapo.customer_confirm_status = 0
      </if>
      <if test="periodOrderType == 2">
        and fapo.type = 0
        and fapo.customer_confirm_status = 1
        and fapo.financial_audit = 0
      </if>
      <if test="periodOrderType == 3">
        and fapo.type = 1
        and fapo.customer_confirm_status = 1
        and fapo.financial_audit = 1
        and fapo.receipt_status in (0,1)
      </if>
      <if test="periodOrderType == 4">
        and fapo.type = 1
        and fapo.customer_confirm_status = 1
        and fapo.financial_audit = 1
        and fapo.receipt_status = 2
      </if>
    </where>
    order by fapo.update_time desc
  </select>

  <select id="selectByInvoiceBillList" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select fapo.id                id,
           fapo.bill_number       billNumber,
           fapo.bill_cycle        billCycle,
           fapo.write_off_amount  writeOffAmount,
           fapo.total_price       totalPrice,
           fapo.after_sale_amount afterSaleAmount,
           fapo.update_time updateTime
    from finance_accounting_period_order fapo
    <if test="mId !=null">
      left join finance_accounting_store fas on fas.finance_order_id = fapo.id
    </if>
    <where>
      <if test="salerId !=null">
        and fapo.saler_id = #{salerId}
      </if>
      <if test="adminId !=null">
        and fapo.admin_id = #{adminId}
      </if>
      <if test="mId !=null">
        and fas.m_id = #{mId} and fapo.admin_id is null
      </if>
      and fapo.invoice_id = #{invoiceId}
      and fapo.type = 1
      and fapo.receipt_status != 2
      and fapo.customer_confirm_status = 1
      and fapo.financial_audit = 1
    </where>
    order by fapo.id DESC
  </select>

  <select id="selectByIdInfo" parameterType="java.lang.Long" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select distinct fapo.id                id,
           fapo.bill_number       billNumber,
           case
            when fapo.admin_id is null then fas.mname
            else fapo.name_remakes end nameRemakes,
           fapo.bill_cycle        billCycle,
           fapo.write_off_amount  writeOffAmount,
           fapo.total_price       totalPrice,
           fapo.after_sale_amount afterSaleAmount,
           fapo.receipt_status receiptStatus,
           fapo.updater,
           fapo.customer_confirm_status customerConfirmStatus
    from finance_accounting_period_order fapo
    left join finance_accounting_store fas on fas.finance_order_id = fapo.id
    where fapo.id = #{id}
  </select>

  <select id="selectCurrentDayBillList" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select distinct fapo.id           id,
           fapo.saler_id     salerId,
           case
            when fapo.admin_id is null then fas.mname
            else fapo.name_remakes end nameRemakes,
           fapo.bill_cycle   billCycle
    from finance_accounting_period_order fapo
    left join finance_accounting_store fas on fas.finance_order_id = fapo.id
    where DATE_FORMAT(fapo.bill_generation_time,'%Y-%m-%d') = #{currentDay}
  </select>

  <select id="selectByPeriodOrderNameRemakes" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select distinct fapo.admin_id adminId, fapo.name_remakes nameRemakes, fapo.saler_id salerId, fapo.saler_name salerName,bill_number billNumber
    from finance_accounting_period_order fapo
    where fapo.type = 1
      and fapo.receipt_status != 2 and fapo.name_remakes like concat(#{nameRemakes},'%')
      and fapo.customer_confirm_status = 1
      and fapo.financial_audit = 1
  </select>

  <select id="selectByPeriodOrderMname" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select distinct fas.m_id mId, fas.mname, fapo.saler_id salerId, fapo.saler_name salerName
    from finance_accounting_period_order fapo
           left join finance_accounting_store fas on fas.finance_order_id = fapo.id
    where fapo.type = 1
      and fapo.receipt_status != 2 and fapo.admin_id is null
      and fas.mname like concat(#{mname},'%')
      and fapo.customer_confirm_status = 1
      and fapo.financial_audit = 1
  </select>

  <select id="selectByPeriodOrderInvoice" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select distinct fapo.invoice_id id, fapo.invoice_title invoiceTitle
    from finance_accounting_period_order fapo
    <if test="mId !=null">
      left join finance_accounting_store fas on fas.finance_order_id = fapo.id
    </if>
    where fapo.type = 1
      and fapo.receipt_status != 2
    and fapo.customer_confirm_status = 1
    and fapo.financial_audit = 1
    <if test="adminId !=null">
      and fapo.admin_id = #{adminId}
    </if>
    <if test="mId !=null">
      and fas.m_id = #{mId} and fapo.admin_id is null
    </if>
  </select>


  <select id="selectBillGenerationTimeByMId" resultType="java.time.LocalDateTime">
    select bill_generation_time
    from finance_accounting_period_order fpo
           inner join
         finance_accounting_store fas on fpo.id = fas.finance_order_id
    where m_id = #{mId} order by fpo.create_time desc limit 1
  </select>
    <select id="allList" resultType="net.summerfarm.model.vo.finance.FinanceAccountingPeriodOrderSummary">
      SELECT name_remakes                                                                                                                                                         nameRemakes,
             SUM(IF(customer_confirm_status = 0 and type = 0, 1, 0))                                                                                                              unconfirmedBillQuantity,
             SUM(IF(customer_confirm_status = 0 and type = 0, fapo.total_price + fapo.out_times_fee - fapo.after_sale_amount, 0))                                                 unconfirmedAmount,
             SUM(IF(customer_confirm_status = 1 and type = 0, 1, 0))                                                                                                              unauditedBillQuantity,
             SUM(IF(customer_confirm_status = 1 and type = 0, fapo.total_price + fapo.out_times_fee - fapo.after_sale_amount, 0))                                                 unauditedAmount,
             SUM(IF(customer_confirm_status = 1 and type = 1 and receipt_status != 2, 1, 0))                                                                                      receivableBillQuantity,
             SUM(IF(customer_confirm_status = 1 and type = 1 and receipt_status != 2, fapo.total_price + fapo.out_times_fee - fapo.write_off_amount - fapo.after_sale_amount, 0)) receivableAmount,
             count(1)                                                                                                                                                             billQuantity,
             fapo.saler_name                                                                                                                                                      salerName,
             sum(store_quantity)                                                                                                                                                  storeQuantity,
             sum(write_off_amount)                                                                                                                                                writeOffAmount,
             customer_type                                                                                                                                                        customerType
      FROM finance_accounting_period_order fapo
      <where>
        <if test="customerType!=null">
          customer_type=#{customerType}
        </if>
        <if test="salerName != null">
          and fapo.saler_name = #{salerName}
        </if>
        <if test="salerId != null">
          and fapo.saler_id = #{salerId}
        </if>
        <if test="startTime != null">
          and fapo.bill_generation_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
          and fapo.bill_generation_time <![CDATA[<=]]> #{endTime}
        </if>
        <if test="billNumber != null">
          and fapo.bill_number = #{billNumber}
        </if>
        <if test="mId != null">
          and fapo.id IN ( SELECT finance_order_id from finance_accounting_store fas where fapo.id=fas.finance_order_id and `m_id` =#{mId})
        </if>
        <if test="mname != null">
          and fapo.id IN ( SELECT finance_order_id from finance_accounting_store fas where fapo.id=fas.finance_order_id and `mname` =#{mname})
        </if>
        <if test="orderNo != null">
          and fapo.bill_number IN ( SELECT bill_number from finance_accounting_store_detail fasd where fapo.bill_number=fasd.bill_number and `order_no` =#{orderNo})
        </if>
        <if test="invoiceTitle != null">
          and fapo.invoice_title like concat(#{invoiceTitle},'%')
        </if>
        <if test="nameRemakes != null">
          and fapo.name_remakes = #{nameRemakes}
        </if>
      </where>
      group by name_remakes
    </select>
  <select id="listPeriod" resultType="net.summerfarm.model.vo.finance.FinanceAccountingPeriodOrderResult">
    SELECT fapo.id,
           fapo.bill_generation_time                                                         billGenerationTime,
           fapo.bill_number                                                                  billNumber,
           fapo.invoice_id                                                                   invoiceId,
           fapo.invoice_title                                                                invoiceTitle,
           fapo.name_remakes                                                                 nameRemakes,
           fapo.bill_confirmation_time                                                       billConfirmationTime,
           fapo.`type`,
           fapo.admin_id                                                                     adminId,
           fapo.saler_name                                                                   salerName,
           sum(fapo.total_price + out_times_fee - after_sale_amount)                         receivableAmount,
           fapo.after_sale_amount                                                            afterSaleAmount,
           fapo.bill_cycle                                                                   billCycle,
           fapo.out_times_fee                                                                outTimesFee,
           fapo.saler_id                                                                     salerId,
           fapo.store_quantity                                                               storeQuantity,
           customer_confirm_status                                                           customerConfirmStatus,
           receipt_status                                                                    receiptStatus,
           fapo.customer_Type                                                                customerType,
           financial_audit                                                                   financialAudit,
           write_off_amount                                                                  writeOffAmount,
           ad.bill_to_pay billToPay
    from `finance_accounting_period_order` fapo
    left join `admin` ad on ad.admin_id = fapo.admin_id
    <where>
      <if test="salerId != null">
        and fapo.saler_id = #{salerId}
      </if>
      <if test="salerName != null">
        and fapo.saler_name = #{salerName}
      </if>
      <if test="startTime != null">
        and fapo.bill_generation_time <![CDATA[>=]]> DATE_FORMAT (#{startTime},'%Y-%m-%d %H:%i:%s')
      </if>
      <if test="endTime != null">
        and fapo.bill_generation_time <![CDATA[<=]]> DATE_SUB( DATE_ADD(#{endTime}, INTERVAL 1 DAY),INTERVAL 1 SECOND)
      </if>
      <if test="billNumber != null">
        and fapo.bill_number = #{billNumber}
      </if>
      <if test="mId != null">
        and fapo.id IN ( SELECT finance_order_id from finance_accounting_store fas where fapo.id=fas.finance_order_id and `m_id` =#{mId})
      </if>
      <if test="mname != null">
        and fapo.id IN ( SELECT finance_order_id from finance_accounting_store fas where fapo.id=fas.finance_order_id and `mname` =#{mname})
      </if>
      <if test="invoiceTitle != null">
        and fapo.invoice_title like  concat('%',#{invoiceTitle},'%')
      </if>
      <if test="nameRemakes != null and nameRemakes != ''">
        and fapo.name_remakes = #{nameRemakes}
      </if>
      <if test="orderNo != null">
        and fapo.bill_number in (select DISTINCT( `bill_number`) from finance_accounting_store_detail where `order_no` = #{orderNo} )
      </if>
      <if test="customerConfirmStatus != null">
        and customer_confirm_status=#{customerConfirmStatus}
      </if>
      <if test="type != null">
        and `type`=#{type}
      </if>
      <if test="invoiceStatus == 0">
        and fapo.`bill_number` NOT IN( select DISTINCT (`bill_number`) from`finance_accounting_store_detail` fasd
        LEFT JOIN `orders` os on os.`order_no` = fasd.`order_no`
        where invoice_status in(1,2) )
      </if>
      <if test="invoiceStatus == 1">
        and fapo.`bill_number` IN( select DISTINCT (`bill_number`) from`finance_accounting_store_detail` fasd
        LEFT JOIN `orders` os on os.`order_no` = fasd.`order_no`
        where invoice_status = 0 )
        and fapo.`bill_number` IN( select DISTINCT (`bill_number`) from`finance_accounting_store_detail` fasd
        LEFT JOIN `orders` os on os.`order_no` = fasd.`order_no`
        where invoice_status in (1,2) )
      </if>
      <if test="invoiceStatus == 2">
        and fapo.`bill_number` NOT IN( select DISTINCT (`bill_number`) from`finance_accounting_store_detail` fasd
        LEFT JOIN `orders` os on os.`order_no` = fasd.`order_no`
        where invoice_status =0 )
      </if>
      <if test="receiptStatus != null">
        and `receipt_status`=#{receiptStatus}
      </if>
      <if test="financialAudit != null">
        and `financial_audit`=#{financialAudit}
      </if>
      <if test="periodOrderType == 1">
        and fapo.type = 0
        and fapo.customer_confirm_status = 0
      </if>
      <if test="periodOrderType == 2">
        and fapo.type = 0
        and fapo.customer_confirm_status = 1
        and fapo.financial_audit = 0
      </if>
      <if test="periodOrderType == 3">
        and fapo.type = 1
        and fapo.customer_confirm_status = 1
        and fapo.financial_audit = 1
        and fapo.receipt_status in (0,1)
        <if test="reqType==null">
          and (fapo.`bill_number`  IN(
          select DISTINCT (`bill_number`) from
          `finance_accounting_store_detail` fasd
          LEFT JOIN `orders` os on os.`order_no` = fasd.`order_no`
          where invoice_status IN(1,2) ) or write_off_amount!=0)
        </if>
      </if>
      <if test="periodOrderType == 4">
        and fapo.type = 1
        and fapo.customer_confirm_status = 1
        and fapo.financial_audit = 1
        and fapo.receipt_status = 2
      </if>
      <if test="periodOrderType == 5">
        and fapo.type = 1
        and fapo.customer_confirm_status = 1
        and fapo.receipt_status = 0
        and (fapo.`bill_number` NOT IN(
        select DISTINCT (`bill_number`) from
        `finance_accounting_store_detail` fasd
        LEFT JOIN `orders` os on os.`order_no` = fasd.`order_no`
        where invoice_status IN(1,2) ) and write_off_amount = 0)
      </if>
    </where>
    GROUP BY fapo.`bill_number`
    ORDER BY  fapo.`receipt_status` asc ,fapo.`bill_generation_time` desc
  </select>
  <select id="selectOverview" resultType="net.summerfarm.model.vo.finance.FinancePeriodOverviewExportVO">
    SELECT `bill_cycle`                                               billCycle,
           bill_generation_time                                       billGenerationTime,
           fapo.bill_number                                           billNo,
           name_remakes                                               nameRemakes,
           IF(customer_type = 0, '品牌', '单店')                      customerType,
           invoice_title                                              invoiceTitle,
           fapo.`total_price` + `out_times_fee` - `after_sale_amount` totalReceivableAmount,
           (fapo.`total_price` - `delivery_fee`)                      totalPrice,
           `after_sale_amount`                                        afterSaleAmount,
           `delivery_fee`                                             deliveryFee,
           `out_times_fee`                                            outTimesFee
    from `finance_accounting_period_order` fapo
    where fapo.`bill_number` = #{billNo}
  </select>
  <select id="selectPeriodDetail" resultType="net.summerfarm.model.vo.finance.FinancePeriodOrderItemExportVO">
    SELECT order_pay_type                   orderType
         , m.m_id                           mId
         , os.`order_no`                    orderNo
         , os.`order_time`                  orderTime
         , os.`status`                      orderStatus
         , oi.`pd_name`                     pdName
         , oi.`sku`
         , oi.`weight`
         , oi.id                            orderItemId
         , oi.rebate_type                   rebateType
         , rebate_number                    rebateNumber
         , IF(i.`type` = 0, "自营", "代仓") property
         , oi.`amount`                      quantity
         , oi.original_price                copeUnitPrice
         , oi.`price`                       actualUnitPrice
         , oi.price * oi.amount             actualPaidPrice
         , `delivery_fee`                   deliveryFee
         , os.`remark`                      orderRemark
         , m.`mname`
         , i.sub_type                       subType
    from orders os
           LEFT JOIN `order_item` oi on oi.`order_no` = os.`order_no`
           LEFT JOIN `merchant` m on os.`m_id` = m.`m_id`
           LEFT JOIN `inventory` i on oi.`sku` = i.`sku`
    where os.`order_no` = #{orderNo} and i.`type` = 0 and oi.`status` != 8
  </select>
  <select id="selectPeriodAdjustmentItem" resultType="net.summerfarm.model.vo.finance.FinancePeriodAdjustmentItemExportVO">
    SELECT fad.m_id                               mId,
           fad.adjust_no                          adjustNo,
           oi.`order_no`                          orderNo,
           oi.`add_time`                          orderTime,
           fa.`approve_time`                      adjustmentTime,
           oi.`sku`                               sku,
           oi.`pd_name`                           pdName,
           oi.`weight`                            weight,
           if(i.type = 0, '自营', if(fad.sku is null,null,'代仓'))         property,
           oi.`amount`                            quantity,
           fad.`adjust_price`                     adjustmentActualAmount,
           fad.`adjust_delivery`                  adjustmentDeliveryAmount,
           fad.`adjust_price` + `adjust_delivery` adjustmentTotalAmount
    from finance_adjustment fa
           LEFT JOIN finance_adjustment_detail fad on fad.`adjust_no` = fa.`adjust_no`
           LEFT JOIN `order_item` oi on oi.id = fad.`order_item_id`
           LEFT JOIN inventory i on i.sku = oi.sku
    where fa.`bill_number` = #{billNo}  and fa.`status` = 1 and average_flag=1
  </select>
  <select id="selectReceivableOverview"
          resultType="net.summerfarm.model.vo.finance.FinanceReceivableOverviewExportVO">
    select fapo.`bill_number`                                                                                             billNo,
           `bill_cycle`                                                                                                   billCycle,
           `name_remakes`                                                                                                 nameRemakes,
           `invoice_title`                                                                                                invoiceTitle,
           `saler_name`                                                                                                   salerName,
           fapo.total_price + out_times_fee - after_sale_amount                      receivableAmount,
           `write_off_amount`                                                                                             writeOffAmount,
           fapo.total_price + out_times_fee - after_sale_amount  - `write_off_amount` unWriteOffAmount,
           DATE_FORMAT(`bill_generation_time`, '%Y-%m-%d')                                                                billGenerationTime,
           DATE_FORMAT(DATE_ADD(`bill_generation_time`, INTERVAL #{paymentCycle} DAY), '%Y-%m-%d')                        dueDate,
           IF(customer_confirm_status = 1, '是', '否')                                                                    billConfirmFlag,
           IF(receipt_status = 2, '已全额收款', '未全额收款')                                                                  receiptStatus,
           if(customer_type=0,'品牌','单店') customerType
    from `finance_accounting_period_order` fapo
    where fapo.bill_number in
    <foreach collection="billNos" item="billNo" open="(" close=")" separator=",">
      #{billNo}
    </foreach>
    group by fapo.id
    ORDER BY fapo.id desc
  </select>
  <select id="overdueDetails" resultType="net.summerfarm.model.vo.finance.FinanceAccountingPeriodOrderSummary">
    SELECT `name_remakes`                                                                                                    nameRemakes,
           SUM(fapo.`total_price` + `out_times_fee` - `after_sale_amount` - `write_off_amount` + ifnull(fa.total, 0))        receivableAmount,
           SUM(IF(DATEDIFF(now(), fapo.`bill_generation_time`) > 37,
                  fapo.`total_price` + `out_times_fee` - `after_sale_amount` - `write_off_amount` + ifnull(fa.total, 0), 0)) overdueAmount,
           SUM(IF(fapo.`customer_confirm_status` = 0, 1, 0))                                                                 unconfirmedBillQuantity,
           SUM(IF(fapo.`customer_confirm_status` = 0 and DATEDIFF(now(), fapo.`bill_generation_time`) > 37, 1, 0))           overdueUnconfirmedBillQuantity
    from `finance_accounting_period_order` fapo
           LEFT JOIN (SELECT sum(total) total, bill_number from finance_adjustment fa where status=1 GROUP BY bill_number) fa on fa.bill_number = fapo.bill_number
    <where>
      <if test="isOverview!=null and isOverview">
        DATEDIFF(now(), fapo.`bill_generation_time`)>37
      </if>
      <if test="salerId!=null">
        and saler_id=#{salerId}
      </if>
    </where>
    GROUP BY fapo.`name_remakes`
  </select>
  <select id="selectAfterSaleByMIdAndDate" resultType="net.summerfarm.model.vo.finance.FinancePeriodAfterSaleItemExportVO">
    select t.after_sale_order_no afterSaleOrderNo,t.order_no orderNo, asp.auditetime afterSaleTime,t.sku sku, p.pd_name pdName,i.weight weight,IF(i.type=0,"自营","代仓") property,
    asp.`quantity` quantity,IF(t.`deliveryed` = 0, "未到货售后", "已到货售后") deliveryed,asp.handle_type handleType,asp.after_sale_type afterSaleType,asp.handle_num handleNum,t.after_sale_unit afterSaleUnit
    FROM after_sale_order t
    left join after_sale_proof asp on t.after_sale_order_no = asp.after_sale_order_no
    left join inventory i on i.sku = t.sku
    left join products p on i.pd_id = p.pd_id
    WHERE t.status = 2 and t.deliveryed = 1 and asp.handle_type in (3,5,10,14) and t.m_id=#{mId}
    <if test="isAfterUpdate">
      and asp.updatetime <![CDATA[>=]]> #{dateStart} AND asp.updatetime <![CDATA[<]]> #{dateEnd}
    </if>
    <if test="!isAfterUpdate">
      and t.add_time <![CDATA[>=]]> #{dateStart} AND t.add_time <![CDATA[<]]> #{dateEnd}
    </if>
  </select>
  <select id="selectByNameRemakes" resultType="net.summerfarm.model.vo.FinanceAccountingPeriodOrderVO">
    select fapo.admin_id                adminId,
           fapo.name_remakes            nameRemakes,
           fapo.saler_id                salerId,
           fapo.saler_name              salerName,
           fapo.bill_number             billNumber,
           fapo.customer_confirm_status customerConfirmStatus,
           fapo.type                    type,
           fapo.receipt_status          receiptStatus,
           fapo.financial_audit         financialAudit,
           after_sale_amount            afterSaleAmount,
           delivery_fee                 deliveryFee,
           total_price                  totalPrice,
           out_times_fee                outTimesFee,
           write_off_amount             writeOffAmount
    from finance_accounting_period_order fapo
    where fapo.name_remakes = #{nameRemakes}
  </select>
  <select id="selectBillNo" resultType="java.lang.Integer">
    SELECT substring(bill_number, -3)
    FROM finance_accounting_period_order
    where bill_number like concat(#{billNumber}, '%')
    ORDER BY bill_number desc limit 1
  </select>
  <select id="selectByBillNos" resultType="net.summerfarm.model.vo.BillInfoVo">
    select id billId,bill_cycle billCycle, bill_number billNo, total_price + out_times_fee - after_sale_amount receivableAmount, write_off_amount writeOffAmount,receipt_status receiptStatus
    from finance_accounting_period_order
    where  bill_number in
    <foreach collection="billNos" separator="," open="(" close=")" item="billNo">
      #{billNo}
    </foreach>
  </select>
  <select id="selectNotInvoicedAmount" resultType="java.math.BigDecimal">
    SELECT ifnull(SUM(os.total_price), 0)
    from `finance_accounting_period_order` fapo
           INNER JOIN `finance_accounting_store_detail` fasd on fapo.`bill_number` = fasd.`bill_number`
           INNER JOIN `orders` os on fasd.`order_no` = os.`order_no`
    where fapo.`name_remakes` = #{nameRemakes}
      and os.`invoice_status` = 0
    GROUP BY fapo.`bill_number`
  </select>
  <select id="selectStoreDetailByBillNo" resultType="net.summerfarm.model.vo.finance.FinanceStoreDetailVo">
    SELECT
      fas.`m_id` mId,fas.`mname` ,if(fas.`m_id`is null,null,fapo.`saler_name` )  salerName,fas.`total_price` - fas.`after_sale_money` receivableAmount
    from
      `finance_accounting_period_order` fapo
        LEFT JOIN `finance_accounting_store` fas on fapo.id = fas.`finance_order_id`
    where fapo.bill_number=#{billNo}
    <if test="mname!=null">AND fas.mname like concat(#{mname},'%')</if>
    <if test="mId!=null">AND fas.m_id = #{mId}</if>
    <if test="salerName!=null"> and fapo.saler_name= #{salerName}</if>
  </select>
  <select id="selectInterTemporalAfterSale" resultType="net.summerfarm.model.vo.finance.FinancePeriodOrderItemExportVO">
    select t.order_no orderNo, os.order_time orderTime, asp.handle_num afterSaleAmount, -asp.handle_num copeTotalAmount, t.`m_id` mId,t.sku,os.remark orderRemark,os.`order_pay_type` orderType
    FROM after_sale_order t
           LEFT JOIN after_sale_proof asp on t.after_sale_order_no = asp.after_sale_order_no
           LEFT JOIN `orders` os on os.`order_no` = t.`order_no`
           LEFT JOIN `finance_accounting_store_detail` fasd on fasd.order_no = os.`order_no`
    WHERE t.status = 2
      and t.deliveryed = 1
      and asp.handle_type in (3, 5, 10, 14)
      and asp.`updatetime`  <![CDATA[>=]]> #{startDateTime}
      AND asp.updatetime <![CDATA[<]]> #{endDateTime}
      and os.`order_time` <![CDATA[<]]> DATE_SUB(#{startDateTime}, INTERVAL 1 DAY)
      and os.`order_pay_type` IN (1, 3)
      and t.m_id = #{mId}
      and t.type = 0
      and fasd.bill_number!=#{billNo}
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_accounting_period_order
    where id = #{id}
  </select>
</mapper>
