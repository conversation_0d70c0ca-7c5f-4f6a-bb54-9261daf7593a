<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DynamicPriceModelConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DynamicPriceModelConfig">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="category_type" jdbcType="TINYINT" property="categoryType"/>
    <result column="exe_time" jdbcType="VARCHAR" property="exeTime"/>
    <result column="upper_limit" jdbcType="DECIMAL" property="upperLimit"/>
    <result column="formula" jdbcType="VARCHAR" property="formula"/>
    <result column="fields" jdbcType="VARCHAR" property="fields"/>
    <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `category_type`, `exe_time`, `upper_limit`, `formula`, `fields`,
    `updater_id`, `del_flag`, `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_model_config
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from dynamic_price_model_config
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.DynamicPriceModelConfig">
    insert into dynamic_price_model_config (`id`, `category_type`, `exe_time`,
                                            `upper_limit`, `formula`, `fields`,
                                            `updater_id`, `del_flag`,
                                            `create_time`, `update_time`)
    values (#{id,jdbcType=BIGINT}, #{categoryType,jdbcType=TINYINT}, #{exeTime,jdbcType=VARCHAR},
            #{upperLimit,jdbcType=DECIMAL}, #{formula,jdbcType=VARCHAR}, #{fields,jdbcType=VARCHAR},
            #{updaterId,jdbcType=INTEGER}, #{delFlag,jdbcType=TINYINT},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.DynamicPriceModelConfig"
    useGeneratedKeys="true" keyProperty="id">
    insert into dynamic_price_model_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="categoryType != null">
        `category_type`,
      </if>
      <if test="exeTime != null">
        `exe_time`,
      </if>
      <if test="upperLimit != null">
        `upper_limit`,
      </if>
      <if test="formula != null">
        `formula`,
      </if>
      <if test="fields != null">
        `fields`,
      </if>
      <if test="updaterId != null">
        `updater_id`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="categoryType != null">
        #{categoryType,jdbcType=TINYINT},
      </if>
      <if test="exeTime != null">
        #{exeTime,jdbcType=VARCHAR},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="formula != null">
        #{formula,jdbcType=VARCHAR},
      </if>
      <if test="fields != null">
        #{fields,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.model.domain.DynamicPriceModelConfig">
    update dynamic_price_model_config
    <set>
      <if test="categoryType != null">
        `category_type` = #{categoryType,jdbcType=TINYINT},
      </if>
      <if test="exeTime != null">
        `exe_time` = #{exeTime,jdbcType=VARCHAR},
      </if>
      <if test="upperLimit != null">
        `upper_limit` = #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="formula != null">
        `formula` = #{formula,jdbcType=VARCHAR},
      </if>
      <if test="fields != null">
        `fields` = #{fields,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        `updater_id` = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey"
    parameterType="net.summerfarm.model.domain.DynamicPriceModelConfig">
    update dynamic_price_model_config
    set `category_type` = #{categoryType,jdbcType=TINYINT},
        `exe_time`      = #{exeTime,jdbcType=VARCHAR},
        `upper_limit`   = #{upperLimit,jdbcType=DECIMAL},
        `formula`       = #{formula,jdbcType=VARCHAR},
        `fields`        = #{fields,jdbcType=VARCHAR},
        `updater_id`    = #{updaterId,jdbcType=INTEGER},
        `del_flag`      = #{delFlag,jdbcType=TINYINT},
        `create_time`   = #{createTime,jdbcType=TIMESTAMP},
        `update_time`   = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateDelFlag" parameterType="long">
    update dynamic_price_model_config
    set del_flag = 1
    where id = #{id}
  </update>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_model_config
    where category_type = #{categoryType} and del_flag = 0 order by exe_time
  </select>

  <select id="listByExeTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_model_config
    where exe_time = #{exeTime}
      and del_flag = 0
    <if test="categoryType != null">
      and category_type = #{categoryType}
    </if>
  </select>
</mapper>