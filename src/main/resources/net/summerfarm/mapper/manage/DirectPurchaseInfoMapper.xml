<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DirectPurchaseInfoMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DirectPurchaseInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="purchase_no" jdbcType="VARCHAR" property="purchaseNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="receive_place" jdbcType="VARCHAR" property="receivePlace" />
    <result column="logistics_info" jdbcType="VARCHAR" property="logisticsInfo" />
    <result column="addtime" jdbcType="TIMESTAMP" property="addtime" />
    <result column="contact_id" jdbcType="BIGINT" property="contactId"/>
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
    <result column="one_click_ship_time" jdbcType="TIMESTAMP" property="oneClickShipTime" />
    <result column="delivery_date" jdbcType="TIMESTAMP" property="deliveryDate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, purchase_no, order_no, send_time, receive_place, logistics_info, addtime, contact_id, contact_phone, one_click_ship_time, delivery_date
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from direct_purchase_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByOrderNo" resultType="net.summerfarm.model.domain.DirectPurchaseInfo">
    select
    id, m_id mId, purchase_no purchaseNo, order_no orderNo, send_time sendTime , receive_place receivePlace, logistics_info logisticsInfo,
    addtime, contact_id contactId, contact_phone contactPhone, one_click_ship_time oneClickShipTime, delivery_date deliveryDate
    from direct_purchase_info
    where order_no = #{orderNo}
  </select>
  <select id="selectByPurchaseNo" resultType="net.summerfarm.model.vo.DirectPurchaseInfoVO">
     SELECT dpi.id id, dpi.m_id mId, m.mname mname, dpi.purchase_no purchaseNo, dpi.order_no orderNo,
     dpi.send_time sendTime, dpi.receive_place receivePlace, dpi.logistics_info logisticsInfo, dpi.addtime addtime,
     dpi.contact_id contactId, dpi.contact_phone contactPhone, one_click_ship_time oneClickShipTime, delivery_date deliveryDate
     FROM direct_purchase_info dpi
     LEFT JOIN merchant m on m.m_id = dpi.m_id
     <where>
       <if test="purchaseNo != null">
         purchase_no = #{purchaseNo}
       </if>
     </where>
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DirectPurchaseInfo" useGeneratedKeys="true">
    insert into direct_purchase_info (m_id, purchase_no, order_no, 
      send_time, receive_place, logistics_info,
      addtime,contact_id, contact_phone)
    values (#{mId,jdbcType=BIGINT}, #{purchaseNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, 
      #{sendTime,jdbcType=TIMESTAMP}, #{receivePlace,jdbcType=VARCHAR}, #{logisticsInfo,jdbcType=VARCHAR},
      #{addtime,jdbcType=TIMESTAMP}, #{contactId, jdbcType=BIGINT}), #{contactPhone, jdbcType=VARCHAR}
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DirectPurchaseInfo" useGeneratedKeys="true">
    insert into direct_purchase_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="purchaseNo != null">
        purchase_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="receivePlace != null">
        receive_place,
      </if>
      <if test="logisticsInfo != null">
        logistics_info,
      </if>
      <if test="addtime != null">
        addtime,
      </if>
      <if test="contactId != null">
        contact_id ,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="purchaseNo != null">
        #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receivePlace != null">
        #{receivePlace,jdbcType=VARCHAR},
      </if>
      <if test="logisticsInfo != null">
        #{logisticsInfo,jdbcType=VARCHAR},
      </if>
      <if test="addtime != null">
        #{addtime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactId != null">
        #{contactId,jdbcType=BIGINT},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.DirectPurchaseInfo">
    update direct_purchase_info
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="purchaseNo != null">
        purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receivePlace != null">
        receive_place = #{receivePlace,jdbcType=VARCHAR},
      </if>
      <if test="logisticsInfo != null">
        logistics_info = #{logisticsInfo,jdbcType=VARCHAR},
      </if>
      <if test="addtime != null">
        addtime = #{addtime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactId != null">
        contact_id = #{contactId,jdbcType=BIGINT},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="oneClickShipTime != null">
        one_click_ship_time = #{oneClickShipTime},
      </if>
      <if test="deliveryDate != null">
        delivery_date = #{deliveryDate},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DirectPurchaseInfo">
    update direct_purchase_info
    set m_id = #{mId,jdbcType=BIGINT},
      purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      receive_place = #{receivePlace,jdbcType=VARCHAR},
      logistics_info = #{logisticsInfo,jdbcType=VARCHAR},
      addtime = #{addtime,jdbcType=TIMESTAMP}
      contact_id = #{contactId,jdbcType=BIGINT},
      contact_phone = #{contactPhone,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>