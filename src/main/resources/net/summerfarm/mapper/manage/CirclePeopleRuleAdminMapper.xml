<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CirclePeopleRuleAdminMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CirclePeopleRuleAdmin">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="rule_id" jdbcType="INTEGER" property="ruleId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, rule_id, m_id, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from circle_people_rule_admin
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from circle_people_rule_admin
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByRuleId">
    delete from circle_people_rule_admin where rule_id =#{id}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CirclePeopleRuleAdmin" useGeneratedKeys="true">
    insert into circle_people_rule_admin (rule_id, m_id, creator, 
      create_time)
    values (#{ruleId,jdbcType=INTEGER}, #{mId,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CirclePeopleRuleAdmin" useGeneratedKeys="true">
    insert into circle_people_rule_admin
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ruleId != null">
        #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
    <insert id="insertMids">
        insert into circle_people_rule_admin(rule_id,m_id,creator,create_time) VALUES
      <foreach collection="mIds" item="mid" separator=",">
        (#{ruleId},#{mid},#{adminId},now())
      </foreach>

    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CirclePeopleRuleAdmin">
    update circle_people_rule_admin
    <set>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CirclePeopleRuleAdmin">
    update circle_people_rule_admin
    set rule_id = #{ruleId,jdbcType=INTEGER},
      m_id = #{mId,jdbcType=BIGINT},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>