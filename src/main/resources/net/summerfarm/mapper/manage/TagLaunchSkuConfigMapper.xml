<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.TagLaunchSkuConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.market.TagLaunchSkuConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="info_id" jdbcType="BIGINT" property="infoId" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `info_id`, `biz_id`, `updater_id`, `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tag_launch_sku_config
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tag_launch_sku_config
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.market.TagLaunchSkuConfig">
    insert into tag_launch_sku_config (`id`, `info_id`, `biz_id`, `updater_id`,
      `create_time`, `update_time`)
    values (#{id,jdbcType=BIGINT}, #{infoId,jdbcType=BIGINT}, #{bizId,jdbcType=VARCHAR}, #{updaterId,jdbcType=INTEGER},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.market.TagLaunchSkuConfig">
    insert into tag_launch_sku_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="infoId != null">
        `info_id`,
      </if>
      <if test="bizId != null">
        `biz_id`,
      </if>
      <if test="updaterId != null">
        `updater_id`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="infoId != null">
        #{infoId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.market.TagLaunchSkuConfig">
    update tag_launch_sku_config
    <set>
      <if test="infoId != null">
        `info_id` = #{infoId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        `biz_id` = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        `updater_id` = #{updaterId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.market.TagLaunchSkuConfig">
    update tag_launch_sku_config
    set `info_id` = #{infoId,jdbcType=BIGINT},
      `biz_id` = #{bizId,jdbcType=VARCHAR},
      `updater_id` = #{updaterId,jdbcType=INTEGER},
      `create_time` = #{createTime,jdbcType=TIMESTAMP},
      `update_time` = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertBatch" parameterType="net.summerfarm.model.domain.market.TagLaunchSkuConfig">
    insert into tag_launch_sku_config (`info_id`, `biz_id` ,`updater_id`)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.infoId,jdbcType=BIGINT}, #{item.bizId,jdbcType=VARCHAR},
      #{item.updaterId,jdbcType=INTEGER})
    </foreach>
  </insert>

  <delete id="deleteByInfoId" parameterType="java.lang.Long">
    delete from tag_launch_sku_config
    where `info_id` = #{infoId,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByBizId">
    delete from tag_launch_sku_config
    where `info_id` = #{infoId} and biz_id = #{bizId}
  </delete>


  <select id="listByInfoId" resultType="string">
    select biz_id from tag_launch_sku_config
    where `info_id` = #{infoId}
  </select>
</mapper>