<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.FollowWhiteListMapper">


    <sql id="resultType">
        id,
        m_id mId,
        gmt_create gmtCreate,
        gmt_modified gmtModified,
        status
    </sql>

    
    <select id="queryFollowWhiteList" resultType="net.summerfarm.model.vo.FollowWhiteListVO">
        select ml.id,ml.m_id mId,m.size msize,m.mname,a.area_name areaName,a.area_no areaNo,m.grade,
            if(coalesce(fu.reassign,1) = 0,fu.admin_name,'公海客户') adminName
        from merchant_follow_white_list ml
            left join follow_up_relation fu on fu.m_id = ml.m_id
            left  join merchant m on m.m_id = ml.m_id
            left join  area a on a.area_no = m.area_no
        where ml.status = 1
        <if test="areaNo != null">
            and a.area_no = #{areaNo}
        </if>
        <if test="mSize != null">
            and  m.size = #{mSize,jdbcType=VARCHAR}
        </if>
        <if test="mname != null">
            and m.mname LIKE CONCAT(#{mname,jdbcType=VARCHAR},'%')
        </if>
        <if test="adminId != null">
            and fu.admin_id = #{adminId} and fu.reassign = 0
        </if>
        <if test="mId != null">
            and m.m_id = #{mId}
        </if>
        ORDER BY ml.id DESC
    </select>

    <select id="queryFollowWhiteListOne" resultType="net.summerfarm.model.domain.FollowWhiteList">
        select  <include refid="resultType"/>
        from merchant_follow_white_list
        where m_id = #{mId} and status = 1
    </select>

    <insert id="insertFollowWhite" parameterType="net.summerfarm.model.domain.FollowWhiteList" useGeneratedKeys="true" keyProperty="id">
        insert into merchant_follow_white_list (m_id, gmt_create, gmt_modified, status)
        values (#{mId}, #{gmtCreate}, #{gmtModified}, #{status})
    </insert>
    
    <update id="updateFollowWhite" parameterType="net.summerfarm.model.domain.FollowWhiteList">
        update from merchant_follow_white_list
        set
        gmt_modified=now()
        <if test="status != null">
            , status = #{status}
        </if>
        where m_id = #{mid} and status = 1
    </update>

    <update id="deleteFollowWhite" >
        delete from merchant_follow_white_list
        where m_id = #{mId} and status = 1
    </update>

    <select id="selectNumByBd" resultType="integer">
        SELECT count(*) FROM merchant_follow_white_list mfwl
        INNER JOIN follow_up_relation f ON mfwl.m_id = f.m_id
        INNER JOIN merchant m on f.m_id = m.m_id
        WHERE f.reassign = 0 AND m.size = '单店' AND m.islock = 0
        <if test="adminId != null">
            and f.admin_id = #{adminId}
        </if>
        <if test="areaNos!=null and areaNos.size()>0">
            and m.area_no in
            <foreach collection="areaNos" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>