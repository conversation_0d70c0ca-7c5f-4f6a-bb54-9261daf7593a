<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CirclePeopleRelationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CirclePeopleRelation">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="BOOLEAN" property="type" />
    <result column="rule_id" jdbcType="INTEGER" property="ruleId" />
    <result column="type_id" jdbcType="VARCHAR" property="typeId" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `type`, rule_id, type_id, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from circle_people_relation
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="selectNameOrIdByActId" resultType="java.lang.String">
      select concat(cpr.name,'/',cpr.id) from circle_people_relation
          cprd left join circle_people_rule cpr on cprd.rule_id = cpr.id
      where cprd.type =#{type}  and type_id = #{id}

    </select>
    <select id="selectExistById" resultType="java.lang.Long">
     <if test="type==0">
       select distinct cprm.m_id from circle_people_relation cpr
       left join circle_people_rule cpri on cpr.rule_id = cpri.id
       left join circle_people_rule_admin cprm on cpri.id = cprm.rule_id
       left join panic_buy pb on cpr.type_id = pb.id
       where cpr.type = #{type} and cpr.type_id = #{id}
       and cpr.rule_id &lt;&gt;#{ruleId}
       and cprm.m_id in (select m_id from circle_people_rule_admin cprm where rule_id =#{ruleId})
       and pb.end_time &gt; now()
     </if>
      <if test="type==1">
        select distinct cprm.m_id from circle_people_relation cpr
        left join circle_people_rule cpri on cpr.rule_id = cpri.id
        left join circle_people_rule_admin cprm on cpri.id = cprm.rule_id
        left join strict_selection ss on cpr.type_id = ss.id
        where cpr.type = #{type} and cpr.type_id = #{id}
        and cpr.rule_id &lt;&gt;#{ruleId}
        and cprm.m_id in (select m_id from circle_people_rule_admin cprm where rule_id =#{ruleId})
        and ss.end_time &gt; now()
      </if>
      <if test="type==2">
        select distinct cprm.m_id from circle_people_relation cpr
        left join circle_people_rule cpri on cpr.rule_id = cpri.id
        left join circle_people_rule_admin cprm on cpri.id = cprm.rule_id
        left join banner b on cpr.type_id = b.id
        where cpr.type = #{type} and cpr.type_id = #{id}
        and cpr.rule_id &lt;&gt;#{ruleId}
        and cprm.m_id in (select m_id from circle_people_rule_admin cprm where rule_id =#{ruleId})
        and b.end_time &gt; now()
      </if>

    </select>
  <select id="selectExist" resultType="java.lang.Integer">
    select count(1) from circle_people_relation cpr
    left join strict_selection ss on ss.id = cpr.type_id
    left join series_of_area soa on ss.id = soa.series_id and series_type =0
    where cpr.type =#{type}
      and cpr.rule_id =#{ruleId}
    and ss.start_time = #{startTime} and ss.end_time = #{endTime} and soa.area_no in
    <foreach collection="areaNos" item="areaNo" open="(" close=")" separator=",">
      #{areaNo}
    </foreach>
    <if test="id!=null">
      and cpr.type_id &lt;&gt; #{id}
    </if>
  </select>
    <select id="selectByRuleId" resultType="java.lang.Integer">
    select sum(num) from (
      select count(1) as num
      from circle_people_relation cpr
      left join panic_buy pb on cpr.type_id = pb.id and cpr.type = 0
      where rule_id =#{id}  and pb.end_time &gt; now()
     union all
      select count(1) as num
      from circle_people_relation cpr
             left join strict_selection ss on cpr.type_id = ss.id and type = 1
      where rule_id =#{id} and ss.end_time &gt; now()
      union all
      select count(1) as num
      from circle_people_relation cpr
             left join banner b on cpr.type_id = b.id and cpr.type =2
      where rule_id =#{id} and b.end_time &gt; now()
      union all
      select count(1) as num
      from circle_people_relation cpr
             left join coupon_sender_setup css on cpr.type_id = css.id and cpr.type =3
      where rule_id =#{id} and css.end_time &gt; now()
        ) a


    </select>
  <select id="selectByTypeIds" resultType="java.lang.Integer">
    select count(*) from circle_people_relation where type = #{type}
    and type_id in
    <foreach collection="typeIds" item="item" open="(" close=")" separator=",">
        #{item}
    </foreach>

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from circle_people_relation
    where id = #{id,jdbcType=INTEGER}
  </delete>
    <delete id="deletByTypeId">
      delete from circle_people_relation
      where type =#{type} and type_id = #{id}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CirclePeopleRelation" useGeneratedKeys="true">
    insert into circle_people_relation (`type`, rule_id, type_id, 
      creator, create_time)
    values (#{type,jdbcType=BOOLEAN}, #{ruleId,jdbcType=INTEGER}, #{typeId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CirclePeopleRelation" useGeneratedKeys="true">
    insert into circle_people_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        `type`,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=BOOLEAN},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CirclePeopleRelation">
    update circle_people_relation
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=BOOLEAN},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where type_id = #{id,jdbcType=INTEGER} and type = 1
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CirclePeopleRelation">
    update circle_people_relation
    set `type` = #{type,jdbcType=BOOLEAN},
      rule_id = #{ruleId,jdbcType=INTEGER},
      type_id = #{typeId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectTakeEffectByEntity" resultType="net.summerfarm.model.DTO.CirclePeopleRelationDTO">
    SELECT
    css.id typeId, css.`name` couponSendSetupName, css.status status
    FROM
    coupon_sender_setup css
    LEFT JOIN circle_people_relation cpr ON cpr.type_id = css.id
    WHERE
    css.`status` in (0, 1)
    AND cpr.type = #{type}
    AND cpr.rule_id = #{ruleId,jdbcType=INTEGER}
    AND css.sender_type = #{senderType}
    GROUP BY css.id;
  </select>
</mapper>