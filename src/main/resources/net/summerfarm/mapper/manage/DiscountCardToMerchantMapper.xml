<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DiscountCardToMerchantMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DiscountCardToMerchant">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="discount_card_id" jdbcType="INTEGER" property="discountCardId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="total_times" jdbcType="INTEGER" property="totalTimes" />
    <result column="used_times" jdbcType="INTEGER" property="usedTimes" />
    <result column="deadline" jdbcType="DATE" property="deadline" />
    <result column="updator" jdbcType="VARCHAR" property="updator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, discount_card_id, `status`, total_times, used_times, deadline, updator, 
    update_time, creator, create_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from discount_card_to_merchant
    where id = #{id,jdbcType=INTEGER}
  </select>


  <select id="selectByCardId" parameterType="net.summerfarm.model.vo.DiscountCardToMerchantVO" resultType = "net.summerfarm.model.vo.DiscountCardToMerchantVO">
    select dc.name cardName,dc.discount,a.area_name areaName,fur.admin_name bdName,dm.create_time createTime,a.area_no areaNo,dm.deadline,dm.status,dc.vaild_days vaildDays,m.mname userName
    from discount_card_to_merchant  dm
    left join  discount_card dc on dc.id = dm.discount_card_id
    inner join merchant m on m.m_id = dm.m_id
    <if test="userName != null">
      and m.mname like  CONCAT('%',#{userName} ,'%')
    </if>
    left join area a on a.area_no = m.area_no
    left join follow_up_relation fur on m.m_id = fur.m_id and  fur.reassign = 0
    where dm.discount_card_id = #{discountCardId}
    <if test="status != null">
      and dm.status = #{status}
    </if>
    <if test="areaNo != null">
      and a.area_no = #{areaNo}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from discount_card_to_merchant
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DiscountCardToMerchant" useGeneratedKeys="true">
    insert into discount_card_to_merchant (m_id, discount_card_id, `status`,
      total_times, used_times, deadline,
      updator, update_time, creator,
      create_time)
    values (#{mId,jdbcType=BIGINT}, #{discountCardId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
      #{totalTimes,jdbcType=INTEGER}, #{usedTimes,jdbcType=INTEGER}, #{deadline,jdbcType=DATE},
      #{updator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DiscountCardToMerchant" useGeneratedKeys="true">
    insert into discount_card_to_merchant
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="discountCardId != null">
        discount_card_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="totalTimes != null">
        total_times,
      </if>
      <if test="usedTimes != null">
        used_times,
      </if>
      <if test="deadline != null">
        deadline,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="discountCardId != null">
        #{discountCardId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="totalTimes != null">
        #{totalTimes,jdbcType=INTEGER},
      </if>
      <if test="usedTimes != null">
        #{usedTimes,jdbcType=INTEGER},
      </if>
      <if test="deadline != null">
        #{deadline,jdbcType=DATE},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.DiscountCardToMerchant">
    update discount_card_to_merchant
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="discountCardId != null">
        discount_card_id = #{discountCardId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="totalTimes != null">
        total_times = #{totalTimes,jdbcType=INTEGER},
      </if>
      <if test="usedTimes != null">
        used_times = #{usedTimes,jdbcType=INTEGER},
      </if>
      <if test="deadline != null">
        deadline = #{deadline,jdbcType=DATE},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DiscountCardToMerchant">
    update discount_card_to_merchant
    set m_id = #{mId,jdbcType=BIGINT},
      discount_card_id = #{discountCardId,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      total_times = #{totalTimes,jdbcType=INTEGER},
      used_times = #{usedTimes,jdbcType=INTEGER},
      deadline = #{deadline,jdbcType=DATE},
      updator = #{updator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="invalidDiscountCard">
    update discount_card_to_merchant
    set status = 0
    where status = 1 and m_id = #{mId}
  </update>

  <update id="invalidDiscountCardById">
    update discount_card_to_merchant
    set status = 0, update_time = now()
    where status = 1 and id = #{id}
  </update>

  <update id="returnCardById">
    UPDATE discount_card_to_merchant set `total_times`  = `total_times` - 300, update_time = now()
    where `id` = #{id} and `total_times` >= 300
  </update>

  <select id="selectByParam" resultType="net.summerfarm.model.domain.DiscountCardToMerchant" resultMap="BaseResultMap">
    select id, m_id, discount_card_id, status, total_times, used_times, deadline,updator, update_time,creator, create_time
    from discount_card_to_merchant
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="mId != null">
        and m_id = #{mId}
      </if>
      <if test="discountCardId != null">
        and discount_card_id = #{discountCardId}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="totalTimes != null">
        and total_times = #{totalTimes}
      </if>
      <if test="usedTimes != null">
        and used_times = #{usedTimes}
      </if>
      <if test="deadline != null">
        and deadline = #{deadline}
      </if>
      <if test=" updator != null">
        and updator = #{updator}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime}
      </if>
      <if test="creator != null">
        and creator = #{creator}
      </if>
    </where>

  </select>
  <select id="selectUsableDiscountCard" resultMap="BaseResultMap">
    select dctm.* from discount_card_to_merchant dctm
    where dctm.status = 1
      and dctm.discount_card_id = #{discountCardId}
      and dctm.m_id = #{mId}
      and dctm.deadline >= #{now} limit 1
  </select>
</mapper>