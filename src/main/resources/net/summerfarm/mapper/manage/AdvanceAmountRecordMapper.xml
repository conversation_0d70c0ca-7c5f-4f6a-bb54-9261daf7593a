<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.AdvanceAmountRecordMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.AdvanceAmountRecord">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="supplier_id" property="supplierId" jdbcType="INTEGER"/>
        <result column="supplier_name" property="supplierName" jdbcType="INTEGER"/>
        <result column="use_amount" property="useAmount" />
        <result column="total_amount" property="totalAmount" />
        <result column="add_time" property="addTime" />
        <result column="purchase_no" property="purchaseNo"/>
        <result column="update_time" property="updateTime" />
    </resultMap>
    <sql id="Base">
        id,supplier_id,use_amount,total_amount,add_time,update_time,purchase_no,supplier_name
    </sql>

    <insert id="insertAmountRecord" parameterType="net.summerfarm.model.domain.AdvancePurchaseAmount">
        insert into advance_amount_record(add_time,update_time,supplier_id,supplier_name,purchase_no,total_amount,use_amount)
        value (now(),now(),#{supplierId},#{supplierName},#{purchaseNo},#{totalAmount},#{useAmount})
    </insert>

    <select id="selectAmount" resultMap="BaseResultMap">
        select <include refid="Base"/>
        from advance_amount_record
        where purchase_no = #{purchaseNo}
    </select>

    <insert id="insertRecordBath" parameterType="net.summerfarm.model.domain.AdvancePurchaseAmount">
        insert into advance_amount_record(add_time,update_time,supplier_id,supplier_name,purchase_no,total_amount,use_amount)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (now(),now(),#{item.supplierId},#{item.supplierName},#{item.purchaseNo},#{item.totalAmount},#{item.useAmount})
        </foreach>
    </insert>

</mapper>