<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.MarketRuleDetailMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="net.summerfarm.model.domain.MarketRuleDetail">
        insert into market_rule_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">
                rule_id,
            </if>
            <if test="categoryId != null">
                category_id,
            </if>
            <if test="categoryName != null">
                category_name,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="pdName != null">
                pd_name,
            </if>
            <if test="weight != null">
                weight,
            </if>
            <if test="threshold != null">
                threshold_paid_amount,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">
                #{ruleId},
            </if>
            <if test="categoryId != null">
                #{categoryId},
            </if>
            <if test="categoryName != null">
                #{categoryName},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="pdName != null">
                #{pdName},
            </if>
            <if test="weight != null">
                #{weight},
            </if>
            <if test="threshold != null">
                #{threshold},
            </if>
        </trim>
    </insert>

    <update id="updateBatch">
        <foreach collection="list" item="item" index="index" separator=";">
            update market_rule_detail
            <set>
                <if test="item.threshold != null">
                    threshold = #{item.threshold},
                </if>
            </set>
            where id = #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>


    <select id="selectList" parameterType="java.lang.Integer"
            resultType="net.summerfarm.model.vo.MarketRuleDetailVO">
        select
               mrd.category_id categoryId,
               mrd.category_name categoryName,
               mrd.sku,
               mrd.pd_name pdName,
               mrd.weight,
               mrd.id,
               mrd.threshold_paid_amount threshold,
               i.ext_type extType
        from market_rule_detail mrd
        left join inventory i on mrd.sku = i.sku
        where mrd.rule_id = #{ruleId}

    </select>

    <delete id="delete">
        delete
        from market_rule_detail
        where rule_id = #{ruleId,jdbcType=INTEGER}
    </delete>
    <delete id="deleteBatch">
        delete
        from market_rule_detail
        where id in
        <foreach collection="list" item="id" close=")" open="(" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <select id="batchSeleteList" parameterType="java.util.List" resultType="net.summerfarm.model.vo.MarketRuleDetailVO">
        select
            mrd.category_id categoryId,
            mrd.category_name categoryName,
            mrd.sku,
            mrd.pd_name pdName,
            mrd.weight,
            i.ext_type extType
        from market_rule_detail mrd
                 left join inventory i on mrd.sku = i.sku
        where mrd.rule_id in
        <foreach collection="list" item="ruleId" close=")" open="(" separator=",">
            #{ruleId,jdbcType=INTEGER}
        </foreach>

    </select>
    <select id="selectById" resultType="net.summerfarm.model.domain.MarketRuleDetail">
        select mrd.category_id categoryId,
               mrd.category_name categoryName,
               mrd.sku,
               mrd.pd_name pdName,
               mrd.weight,
               mrd.id,
               mrd.threshold_paid_amount threshold
        from market_rule_detail mrd
        where mrd.id = #{id}
    </select>
    <select id="selectByReturnIdAndSkus" resultType="net.summerfarm.model.DTO.market.MarketRuleDetailDTO">
        select mrd.id,
               mrd.rule_id ruleId,
               mrd.sku,
               mrd.threshold_paid_amount threshold,
               mr.name ruleName,
               mr.start_time startTime,
               mr.end_time endTime
        from market_rule_detail mrd
        left join market_rule mr on mrd.rule_id = mr.id
        where mr.status = 1 and mrd.rule_id in
        <foreach collection="ruleIds" item="ruleId" close=")" open="(" separator=",">
            #{ruleId,jdbcType=INTEGER}
        </foreach>
        and mrd.sku in
        <foreach collection="skus" item="sku" close=")" open="(" separator=",">
            #{sku,jdbcType=VARCHAR}
        </foreach>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into market_rule_detail (rule_id, category_id, category_name, sku, pd_name, weight) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.ruleId}, #{item.categoryId}, #{item.categoryName}, #{item.sku}, #{item.pdName}, #{item.weight})
        </foreach>
    </insert>

    <insert id="insertBatchV2" parameterType="java.util.List">
        insert into market_rule_detail (rule_id, category_id, category_name, sku, pd_name, weight, threshold_paid_amount) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.ruleId}, #{item.categoryId}, #{item.categoryName}, #{item.sku}, #{item.pdName}, #{item.weight}, #{item.threshold})
        </foreach>
    </insert>
</mapper>