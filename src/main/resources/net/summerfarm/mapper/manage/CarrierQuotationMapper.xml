<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CarrierQuotationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CarrierQuotation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="carrier_id" jdbcType="BIGINT" property="carrierId" />
    <result column="service_area" jdbcType="VARCHAR" property="serviceArea" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="start_price" jdbcType="DECIMAL" property="startPrice" />
    <result column="start_point_num" jdbcType="INTEGER" property="startPointNum" />
    <result column="exceed_point_price" jdbcType="INTEGER" property="exceedPointPrice" />
    <result column="subsidy_price" jdbcType="DECIMAL" property="subsidyPrice" />
    <result column="printing_fee" jdbcType="DECIMAL" property="printingFee" />
    <result column="front_warehouse_fee" jdbcType="DECIMAL" property="frontWarehouseFee" />
    <result column="km_price" jdbcType="DECIMAL" property="kmPrice" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, creator, create_time, updater, update_time, carrier_id, service_area, store_no, 
    `type`, start_price, start_point_num, exceed_point_price, subsidy_price, printing_fee,
    front_warehouse_fee, km_price, delete_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from carrier_quotation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByCondition" resultType="net.summerfarm.model.vo.CarrierQuotationVo">
    select cq.id, cq.creator, cq.create_time createTime, cq.updater, cq.update_time updateTime,
    carrier_id carrierId, service_area serviceArea, cq.store_no storeNo, type, start_price startPrice, start_point_num startPointNum,
    exceed_point_price exceedPointPrice, subsidy_price subsidyPrice, printing_fee printingFee,
    front_warehouse_fee frontWarehouseFee, km_price kmPrice,
    delete_flag deleteFlag,c.carrier_name carrierName,wlc.store_name storeName
    from carrier_quotation cq
    left join carrier c on cq.carrier_id = c.id
    left join warehouse_logistics_center wlc on wlc.store_no = cq.store_no
    <where>
      <if test="id !=null">
        and cq.id = #{id}
      </if>
      <if test="carrierName !=null">
        and cq.carrier_name = #{carrierName}
      </if>
      <if test="carrierId !=null">
        and carrier_id = #{carrierId}
      </if>
      <if test="storeNo!=null">
        and cq.store_no = #{storeNo}
      </if>
      <if test="type !=null">
        and type = #{type}
      </if>
      <if test="deleteFlag!=null">
        and delete_flag = #{deleteFlag}
      </if>
      <if test="serviceArea!=null">
        and service_area = #{serviceArea}
      </if>
    </where>
    order by cq.id desc
  </select>
    <select id="selectArea" resultType="net.summerfarm.model.domain.CarrierQuotation">
      select service_area serviceArea from carrier_quotation
      where
      delete_flag = 0
        <if test="serviceArea!=null">
          and service_area LIKE CONCAT('%',#{serviceArea},'%')
        </if>

    </select>
    <select id="selectByDeliveryCarId" resultType="net.summerfarm.model.vo.CarrierQuotationVo">
      select td.NAME driver,c.carrier_name carrierName,cq.service_area serviceArea,cq.start_point_num startPointNum,cq.start_price startPrice,
             cq.exceed_point_price exceedPointPrice,cq.km_price kmPrice,cq.printing_fee printingFee,cq.id,cq.type,cq.carrier_id carrierId
      from carrier c
             INNER JOIN delivery_car_path dcp ON dcp.carrier_id = c.id and dcp.delivery_time=DATE_SUB(CURDATE(),INTERVAL 1 DAY)
             INNER JOIN tms_driver td ON td.id = dcp.delivery_car_id
             left join carrier_quotation cq on c.id = cq.carrier_id and cq.delete_flag = 0
      where td.id = #{deliveryCarId}

    </select>
  <select id="selectLastByAreaField" resultType="net.summerfarm.model.vo.CarrierStatementVo">
    select
    ifnull(
    <if test="type == 'printing_fee' ">
      printing_fee,0) printingFee
    </if>
    <if test="type == 'km_price' ">
      km_price,0) kmPrice
    </if>
    <if test="type == 'subsidy_price' ">
      subsidy_price,0) subsidyPrice
    </if>
    <if test="type == 'start_price' ">
      start_price,0) startPrice
    </if>
    <if test="type == 'exceed_point_price' ">
      exceed_point_price,0) exceedPointPrice
    </if>
    <if test="type =='start_point_num'">
      start_point_num,0) startPointNum
    </if>
        ,cq.id carrierQuotationId,service_area serviceArea
    from carrier_quotation_area cqa
    left join carrier_quotation cq on cqa.carrier_quotation_id = cq.id
    <where>
      cq.delete_flag = 0
      and
      cq.carrier_id in
      <foreach collection="carrierQuotationVos" item="carrier" separator="," open="(" close=")">
        #{carrier.carrierId}
      </foreach>
      and
      (
      <foreach collection="deliveryPathVOS" item="item" separator=" or ">
        (cqa.city =#{item.city}
        <if test="item.area !=null and item.area!=''">
          and cqa.area =#{item.area}
        </if>)
      </foreach>
      )
    </where>
    order by
    <if test="type == 'printing_fee' ">
      printing_fee
    </if>
    <if test="type == 'km_price' ">
      km_price
    </if>
    <if test="type == 'subsidy_price' ">
      subsidy_price
    </if>
    <if test="type == 'start_price' ">
      start_price
    </if>
    <if test="type == 'exceed_point_price' ">
      exceed_point_price
    </if>
    <if test="type =='start_point_num'">
      start_point_num
    </if>
     desc limit 1

  </select>
  <select id="existByDeliveryPath" resultType="java.lang.Integer">
    select count(1) from carrier_quotation_area cqa
    left join carrier_quotation cq on cqa.carrier_quotation_id = cq.id
    where cq.type = 1 and cq.delete_flag = 0 and (
    <foreach collection="deliveryPathVOS" item="item" separator=" or ">
      cqa.province=#{item.province} and cqa.city =#{item.city}
      <if test="item.area!=null and item.area!=''">
        and cqa.area =#{item.area}
      </if>
    </foreach>
    )
  </select>
  <select id="selectByCarrierQuotationVo" resultType="net.summerfarm.model.vo.CarrierQuotationVo">
        select id from
          carrier_quotation
        where
        carrier_id = #{carrierId} and
        store_no = #{storeNo} and
        service_area = #{serviceArea} and delete_flag = 0


  </select>
  <select id="selectByCarrierId" resultType="java.math.BigDecimal">
        select front_warehouse_fee from carrier_quotation where delete_flag = 0 and carrier_id = #{carrierId} order by front_warehouse_fee desc  limit 1
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from carrier_quotation
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CarrierQuotation" useGeneratedKeys="true">
    insert into carrier_quotation (creator, create_time, updater, 
      update_time, carrier_id, service_area, 
      store_no, `type`, start_price, 
      start_point_num, exceed_point_price, subsidy_price,
      printing_fee, front_warehouse_fee, km_price, 
      delete_flag)
    values (#{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{carrierId,jdbcType=BIGINT}, #{serviceArea,jdbcType=VARCHAR}, 
      #{storeNo,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{startPrice,jdbcType=DECIMAL}, 
      #{startPointNum,jdbcType=INTEGER}, #{exceedPointPrice}, #{subsidyPrice,jdbcType=DECIMAL},
      #{printingFee,jdbcType=DECIMAL}, #{frontWarehouseFee,jdbcType=DECIMAL}, #{kmPrice,jdbcType=DECIMAL}, 
      #{deleteFlag,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CarrierQuotation" useGeneratedKeys="true">
    insert into carrier_quotation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="carrierId != null">
        carrier_id,
      </if>
      <if test="serviceArea != null">
        service_area,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="startPrice != null">
        start_price,
      </if>
      <if test="startPointNum != null">
        start_point_num,
      </if>
      <if test="exceedPointPrice != null">
        exceed_point_price,
      </if>
      <if test="subsidyPrice != null">
        subsidy_price,
      </if>
      <if test="printingFee != null">
        printing_fee,
      </if>
      <if test="frontWarehouseFee != null">
        front_warehouse_fee,
      </if>
      <if test="kmPrice != null">
        km_price,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="carrierId != null">
        #{carrierId,jdbcType=BIGINT},
      </if>
      <if test="serviceArea != null">
        #{serviceArea,jdbcType=VARCHAR},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="startPrice != null">
        #{startPrice,jdbcType=DECIMAL},
      </if>
      <if test="startPointNum != null">
        #{startPointNum,jdbcType=INTEGER},
      </if>
      <if test="exceedPointPrice != null">
        #{exceedPointPrice},
      </if>
      <if test="subsidyPrice != null">
        #{subsidyPrice,jdbcType=DECIMAL},
      </if>
      <if test="printingFee != null">
        #{printingFee,jdbcType=DECIMAL},
      </if>
      <if test="frontWarehouseFee != null">
        #{frontWarehouseFee,jdbcType=DECIMAL},
      </if>
      <if test="kmPrice != null">
        #{kmPrice,jdbcType=DECIMAL},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CarrierQuotation">
    update carrier_quotation
    <set>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="carrierId != null">
        carrier_id = #{carrierId,jdbcType=BIGINT},
      </if>
      <if test="serviceArea != null">
        service_area = #{serviceArea,jdbcType=VARCHAR},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="startPrice != null">
        start_price = #{startPrice,jdbcType=DECIMAL},
      </if>
      <if test="startPointNum != null">
        start_point_num = #{startPointNum,jdbcType=INTEGER},
      </if>
      <if test="exceedPointPrice != null">
        exceed_point_price = #{exceedPointPrice},
      </if>
      <if test="subsidyPrice != null">
        subsidy_price = #{subsidyPrice,jdbcType=DECIMAL},
      </if>
      <if test="printingFee != null">
        printing_fee = #{printingFee,jdbcType=DECIMAL},
      </if>
      <if test="frontWarehouseFee != null">
        front_warehouse_fee = #{frontWarehouseFee,jdbcType=DECIMAL},
      </if>
      <if test="kmPrice != null">
        km_price = #{kmPrice,jdbcType=DECIMAL},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CarrierQuotation">
    update carrier_quotation
    set
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      carrier_id = #{carrierId,jdbcType=BIGINT},
      service_area = #{serviceArea,jdbcType=VARCHAR},
      store_no = #{storeNo,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      start_price = #{startPrice,jdbcType=DECIMAL},
      start_point_num = #{startPointNum,jdbcType=INTEGER},
      exceed_point_price = #{exceedPointPrice},
      subsidy_price = #{subsidyPrice,jdbcType=DECIMAL},
      printing_fee = #{printingFee,jdbcType=DECIMAL},
      front_warehouse_fee = #{frontWarehouseFee,jdbcType=DECIMAL},
      km_price = #{kmPrice,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateDeleteFlag">
      update  carrier_quotation SET delete_flag = 1 where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>