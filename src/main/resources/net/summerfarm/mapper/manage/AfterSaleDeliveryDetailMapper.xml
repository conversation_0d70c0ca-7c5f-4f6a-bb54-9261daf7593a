<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.AfterSaleDeliveryDetailMapper">


    <update id="updateById" parameterType="net.summerfarm.model.domain.AfterSaleDeliveryDetail" >
        update after_sale_delivery_detail
        <set >
            <if test="interceptFlag != null">
                intercept_flag = #{interceptFlag},
            </if>
            <if test="interceptTime != null">
                intercept_time = #{interceptTime},
            </if>
            <if test="showFlag != null">
                show_flag = #{showFlag},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateInterceptFlagByIdList" parameterType="net.summerfarm.model.domain.AfterSaleDeliveryDetail" >
        update after_sale_delivery_detail
        <set>
            <if test="interceptFlag != null">
                intercept_flag = #{interceptFlag},
            </if>
            <if test="interceptTime != null">
                intercept_time = #{interceptTime},
            </if>
            <if test="showFlag != null">
                show_flag = #{showFlag},
            </if>
        </set>
        where
        <if test="afterSaleDeliveryDetailIdList != null">
            id in
            <foreach collection="afterSaleDeliveryDetailIdList" item="id" index="index" open="("  separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>
</mapper>