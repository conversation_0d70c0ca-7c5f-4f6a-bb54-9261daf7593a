<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.TgStockMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.TgStock" >
    <id column="s_id" property="sId" jdbcType="BIGINT" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="quantity" property="quantity" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    s_id, sku, quantity
  </sql>


  <select id="selectBySku" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from stock
    <!--where sku in (<foreach collection="list" item="item" separator="," >-->
    <!--#{item.sku,jdbcType=VARCHAR}-->
  <!--</foreach>)-->
  </select>
  



</mapper>