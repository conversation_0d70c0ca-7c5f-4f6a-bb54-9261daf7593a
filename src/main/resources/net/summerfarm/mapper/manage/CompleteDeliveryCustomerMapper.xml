<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.CompleteDeliveryCustomerMapper">

    <insert id="insertBatchCompleteDelivery" parameterType="net.summerfarm.model.domain.CompleteDeliveryCustomer" useGeneratedKeys="true" keyProperty="id">
        insert into complete_delivery_customer (complete_delivery_id,admin_id,name_remakes,create_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.completeDeliveryId},#{item.adminId},#{item.nameRemakes},now())
        </foreach>
    </insert>

    <insert id="insert">
        insert into complete_delivery_customer (complete_delivery_id,admin_id,name_remakes,create_time)
        values (#{id},#{idAdmin},#{nameRemakes},now())
    </insert>

    <select id="selectAdmin" resultType="net.summerfarm.model.domain.CompleteDeliveryCustomer">
        select id,complete_delivery_id completeDeliveryId,admin_id adminId,name_remakes nameRemakes
        from complete_delivery_customer
        where complete_delivery_id = #{id}
    </select>

    <select id="selectAdminNameRemakes" resultType="net.summerfarm.model.domain.CompleteDeliveryCustomer">
        select cdc.id,cdc.complete_delivery_id completeDeliveryId,cdc.admin_id adminId,ad.name_remakes nameRemakes
        from complete_delivery_customer cdc
        left from admin ad on cdc.admin_id = ad.admin_id
        where cdc.complete_delivery_id = #{id}
    </select>

    <delete id="deleteOldAdminId">
        delete from complete_delivery_customer
        where complete_delivery_id = #{id} and admin_id = #{idAdmin}
    </delete>

    <delete id="deleteEmpty">
        delete from complete_delivery_customer
        where complete_delivery_id = #{id}
    </delete>

    <select id="selectAdminId" resultType="integer" parameterType="integer">
        select count(1)
        from complete_delivery_customer
        where admin_id = #{adminId}
    </select>

    <select id="accountSum" resultType="net.summerfarm.model.domain.CompleteDeliveryCustomer" parameterType="integer">
        select distinct cdc.admin_id adminId
        from complete_delivery_customer cdc
        left join complete_delivery cd on cdc.complete_delivery_id = cd.id
        where cd.store_no = #{storeNo} and cdc.admin_id is not null
    </select>


    <select id="selectByCompleteDeliveryId" resultType="net.summerfarm.model.domain.CompleteDeliveryCustomer">
        select id,
               complete_delivery_id as completeDeliveryId,
               admin_id as adminId,
               name_remakes as nameRemakes,
               status,
               update_time as updateTime,
               create_time as createTime
        from complete_delivery_customer
        where complete_delivery_id = #{completeDeliveryId}
    </select>
</mapper>