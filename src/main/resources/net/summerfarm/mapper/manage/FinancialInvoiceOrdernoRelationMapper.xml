<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinancialInvoiceOrdernoRelationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinancialInvoiceOrdernoRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="financial_invoice_id" jdbcType="BIGINT" property="financialInvoiceId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, financial_invoice_id, order_no, update_time, create_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from financial_invoice_orderno_relation
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByFinancialInvoiceId" resultType="net.summerfarm.model.domain.FinancialInvoiceOrdernoRelation">
    select
    id, financial_invoice_id financialInvoiceId, order_no orderNo, update_time updateTime, create_time createTime,
    order_item_id orderItemId
    from financial_invoice_orderno_relation
    where financial_invoice_id = #{id}
  </select>
  <select id="countOrdersByInvoiceId" resultType="java.lang.Integer">
    select COUNT(DISTINCT order_no)
    from financial_invoice_orderno_relation
    where financial_invoice_id = #{id}
  </select>
  <select id="selectByOrderItems" resultType="net.summerfarm.model.domain.FinancialInvoiceOrdernoRelation">
    select fr.id,
           fr.financial_invoice_id financialInvoiceId,
           fr.order_no             orderNo,
           fr.order_item_id        orderItemId,
           fr.update_time          updateTime,
           fr.create_time          createTime,
           fr.order_item_id        orderItemId
    from financial_invoice_orderno_relation fr
      inner join financial_invoice fi on fr.financial_invoice_id = fi.id
      inner join order_item oi on oi.id = fr.order_item_id
    where fi.invoice_result = 1 and invoice_status=0 and amount_money>0 and oi.sku != "DF001TF0001" and fr.order_item_id in
    <foreach collection="oderItems" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
    order by fi.create_time asc
  </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinancialInvoiceOrdernoRelation" useGeneratedKeys="true">
    insert into financial_invoice_orderno_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="financialInvoiceId != null">
        financial_invoice_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="financialInvoiceId != null">
        #{financialInvoiceId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch">
    insert into financial_invoice_orderno_relation (financial_invoice_id,order_no,order_item_id) VALUES
    <foreach collection="list" separator="," item="item">
      (#{item.financialInvoiceId}, #{item.orderNo}, #{item.orderItemId})
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinancialInvoiceOrdernoRelation">
    update financial_invoice_orderno_relation
    <set>
      <if test="financialInvoiceId != null">
        financial_invoice_id = #{financialInvoiceId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>