<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.AfterSaleDeliveryPathMapper">

    <resultMap id="oldDistOrderMap" type="net.summerfarm.model.DTO.tms.OldDistOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="outer_order_id" jdbcType="VARCHAR" property="outerOrderId"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="outer_contact_id" jdbcType="VARCHAR" property="outerContactId"/>
        <result column="expect_begin_time" jdbcType="TIMESTAMP" property="expectBeginTime"/>
        <result column="store_no" jdbcType="INTEGER" property="storeNo"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>

        <collection property="distOrderItemList" ofType="net.summerfarm.model.DTO.tms.OldDistOrderItem">
            <id column="item_id" jdbcType="BIGINT" property="id"/>
            <result column="outer_item_id" jdbcType="VARCHAR" property="outerItemId"/>
            <result column="quantity" jdbcType="INTEGER" property="quantity"/>
            <result column="item_delivery_type" jdbcType="INTEGER" property="deliveryType"/>
        </collection>
    </resultMap>

    <select id="closingOrderByAfterSale" resultMap="net.summerfarm.mapper.manage.OrdersMapper.ClosingOrderMap" >
         select asdp.id orderId, asdd.as_delivery_path_id orderNo, m.mname userName, m.m_id, con.contact, asdp.delivery_time
            deliveryTime,m.show_price showPrice,asdp.type deliveryType,asdd.type orderItemDeliveryType,
            con.phone phone, con.area, CONCAT(con.province,con.city,con.area,con.address,ifnull(con.house_number,'')) deliveryAddress,
            asdd.id id,p.pd_name , asdd.quantity amount,  i.unit,p.category_id categoryId,c1.type categoryType,
            i.weight,asdd.sku,con.delivery_car deliveryCar,con.contact_id, m.area_no areaNo,dep.path,dep.sort,con.path prePath,
            dep.total_volume totalVolume,con.distance,c1.category,c2.category parentCategory,p.storage_location,m.admin_id adminId,a.sku_sorting,
            i.type skuType,a.name_remakes nameRemakes,i.ext_type extType,i.pd_id,i.weight_num,a.name_remakes outerBrandName,0 brandType
         from after_sale_delivery_path  asdp
        left join after_sale_delivery_detail asdd on asdd.as_delivery_path_id = asdp.id and asdd.status = 1
        LEFT JOIN delivery_path dep ON asdp.concat_id = dep.contact_id AND asdp.delivery_time = dep.delivery_time
        left join merchant m on m.m_id = asdp.m_id
        left join contact con on asdp.concat_id = con.contact_id
        LEFT JOIN inventory i ON i.sku = asdd.sku
        LEFT JOIN products p ON i.pd_id=p.pd_id
        left join area on area.area_no=m.area_no
        LEFT join admin a on a.admin_id = m.admin_id
        LEFT JOIN category c1 ON p.category_id=c1.id
        LEFT JOIN category c2 ON c1.parent_id=c2.id
        <where>
            asdp.delivery_time = #{deliveryTime} and asdp.status >= 1
            and asdd.show_flag = 0
            <if test="areaNo != null">
                AND m.area_no = #{areaNo}
            </if>
            <if test="parentNo != null">
                AND asdp.out_store_no = #{parentNo}
            </if>
        </where>
    </select>

    <select id="tmsClosingOrderByAfterSale" resultMap="net.summerfarm.mapper.manage.OrdersMapper.ClosingOrderMap" >
        SELECT
            tdo.id orderId,
            tdo.outer_order_id orderNo,
            m.mname userName,
            m.m_id,
            tdse.`name` contact,
            tdo.expect_begin_time deliveryTime,
            m.show_price showPrice,
            tdo.type deliveryType,
            tdi.delivery_type orderItemDeliveryType,
            tdse.phone phone,
            ifnull(tdse.area,'') area,
            CONCAT(
                    tdse.provice,
                    tdse.city,
                    ifnull(tdse.area,''),
                    tdse.address) deliveryAddress,
            tdse.id endSiteId,
            tdi.id id,
            tdi.outer_item_name pd_name,
            tdi.quantity amount,
            tdi.unit,
            p.category_id categoryId,
            c1.type categoryType,
            i.weight,
            i.weight_num,
            tdi.outer_item_id sku,
            tdo.outer_contact_id contact_id,
            m.area_no areaNo,
            tdb.path_code path,
            IF(tdb.path_id > 0 ,tdsite.sequence,null) sort,
            c1.category,
            c2.category parentCategory,
            p.storage_location,
            m.admin_id adminId,
            a.sku_sorting,
            i.type skuType,
            a.name_remakes nameRemakes,
            i.ext_type extType,
            i.pd_id,
            0 brandType,
            tdsite.outer_brand_name outerBrandName,
            tdsite.send_remark sendRemark
        FROM
            tms_dist_order tdo
                LEFT JOIN tms_dist_item tdi ON tdo.id = tdi.dist_order_id
                LEFT JOIN tms_dist_site tdse ON tdse.id = tdo.end_site_id
                LEFT JOIN tms_dist_site tdsb ON tdsb.id = tdo.begin_site_id
                LEFT JOIN tms_delivery_order tdeo ON tdeo.dist_order_id = tdo.id
                LEFT JOIN tms_delivery_batch tdb ON tdb.id = tdeo.batch_id
                LEFT JOIN tms_delivery_site tdsite ON tdsite.delivery_batch_id = tdeo.batch_id
                AND tdo.end_site_id = tdsite.site_id
                LEFT JOIN merchant m ON m.m_id = tdo.outer_client_id
                LEFT JOIN inventory i ON i.sku = tdi.outer_item_id
                LEFT JOIN products p ON i.pd_id = p.pd_id
                LEFT JOIN area ON area.area_no = m.area_no
                LEFT JOIN admin a ON a.admin_id = m.admin_id
                LEFT JOIN category c1 ON p.category_id = c1.id
                LEFT JOIN category c2 ON c1.parent_id = c2.id
        WHERE
            tdo.expect_begin_time = #{deliveryTime}
          AND tdo.state NOT IN ( 18, 19 )
          AND tdo.source IN ( 201 )
          AND tdsb.out_business_no = #{storeNo}
    </select>

    <select id="closingOrderByAfterSaleNew" resultMap="net.summerfarm.mapper.manage.OrdersMapper.ClosingOrderMap" >
         select asdp.id orderId, asdd.as_delivery_path_id orderNo, m.mname userName, m.m_id, con.contact, asdp.delivery_time
            deliveryTime,m.show_price showPrice,asdp.type deliveryType,asdd.type orderItemDeliveryType,
            con.phone phone, con.area, CONCAT(con.province,con.city,con.area,con.address,ifnull(con.house_number,'')) deliveryAddress,
            asdd.id id,p.pd_name , asdd.quantity amount,  i.unit,p.category_id categoryId,c1.type categoryType,
            i.weight,asdd.sku,con.delivery_car deliveryCar,con.contact_id, m.area_no areaNo,dep.path,dep.sort,con.path prePath,
            dep.total_volume totalVolume,con.distance,c1.category,c2.category parentCategory,p.storage_location,m.admin_id adminId,a.sku_sorting,
            i.type skuType,a.name_remakes nameRemakes,i.ext_type extType,0 as brandType
         from after_sale_delivery_path  asdp
        left join after_sale_delivery_detail asdd on asdd.as_delivery_path_id = asdp.id and asdd.status = 1
        LEFT JOIN delivery_path dep ON asdp.concat_id = dep.contact_id AND asdp.delivery_time = dep.delivery_time
        left join merchant m on m.m_id = asdp.m_id
        left join contact con on asdp.concat_id = con.contact_id
        LEFT JOIN inventory i ON i.sku = asdd.sku
        LEFT JOIN products p ON i.pd_id=p.pd_id
        left join area on area.area_no=m.area_no
        LEFT join admin a on a.admin_id = m.admin_id
        LEFT JOIN category c1 ON p.category_id=c1.id
        LEFT JOIN category c2 ON c1.parent_id=c2.id
        <where>
            asdp.delivery_time = #{deliveryTime} and asdp.status >= 1
            <if test="interceptFlag != null">
                and asdd.intercept_flag = #{interceptFlag}
            </if>
            <if test="areaNo != null">
                AND m.area_no = #{areaNo}
            </if>
            <if test="parentNo != null">
                AND asdp.out_store_no = #{parentNo}
            </if>
        </where>
    </select>

    <select id="afterSaleTask" resultType="net.summerfarm.model.domain.OrderItem">
        select asdp.`after_sale_no` orderNo, asdd.sku , asdd.quantity amount from after_sale_delivery_path asdp
        inner join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id and asdd.type = 0 and asdd.status = 1
        inner join warehouse_inventory_mapping wim on wim.store_no = asdp.out_store_no and wim.sku = asdd.sku and  wim.warehouse_no = #{outStoreNo}
        INNER JOIN area_store ar ON wim.sku = ar.sku AND wim.warehouse_no = ar.area_no
        where asdp.out_store_no = #{storeNo} and asdp.delivery_time = #{deliveryTime} and asdp.status = 1

    </select>

    <select id="afterSaleTasking" resultType="net.summerfarm.model.domain.OrderItem">
        select asdp.`after_sale_no` orderNo, asdd.sku , asdd.quantity amount from after_sale_delivery_path asdp
          inner join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id and asdd.type = 0 and asdd.status = 1
          inner join warehouse_inventory_mapping wim on wim.store_no = asdp.out_store_no and wim.sku = asdd.sku and  wim.warehouse_no = #{outStoreNo}
          INNER JOIN area_store ar ON wim.sku = ar.sku AND wim.warehouse_no = ar.area_no
          INNER JOIN inventory i ON i.sku = asdd.sku AND (i.sub_type != 1 OR i.`sub_type` IS NULL)
        where asdp.out_store_no = #{storeNo} and asdp.delivery_time = #{deliveryTime} and asdp.status in (1, 2)

    </select>

    <select id="afterSaleTaskingForCross" resultType="net.summerfarm.model.domain.OrderItem">
        select asdp.`after_sale_no` orderNo, asdd.sku , asdd.quantity amount from after_sale_delivery_path asdp
          inner join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id and asdd.type = 0 and asdd.status = 1
          inner join warehouse_inventory_mapping wim on wim.store_no = asdp.out_store_no and wim.sku = asdd.sku and  wim.warehouse_no = #{outStoreNo}
          INNER JOIN area_store ar ON wim.sku = ar.sku AND wim.warehouse_no = ar.area_no
          INNER JOIN inventory i ON i.sku = asdd.sku AND i.sub_type = 1
        where asdp.out_store_no = #{storeNo} and asdp.delivery_time = #{deliveryTime} and asdp.status in (1, 2)

    </select>

    <select id="selectAfterSaleTask" resultType="net.summerfarm.model.domain.OrderItem">
        select asdd.sku , asdd.quantity amount from after_sale_delivery_path asdp
                                                        inner join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id and asdd.type = 0 and asdd.status = 1
                                                        inner join warehouse_inventory_mapping wim on wim.store_no = asdp.out_store_no and wim.sku = asdd.sku and  wim.warehouse_no = #{outStoreNo}
                                                        INNER JOIN area_store ar ON wim.sku = ar.sku AND wim.warehouse_no = ar.area_no
        where asdp.out_store_no = #{storeNo} and asdp.delivery_time = #{deliveryTime} and asdp.status = #{status}

    </select>
    <select id="selectAfterDetail" resultType="net.summerfarm.model.vo.OrderItemVO">
        select asdd.sku , asdd.quantity amount,i.weight,p.pd_name pdName ,asdd.type orderItemDeliveryType
        from after_sale_delivery_path asdp
        inner join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id and  asdd.status = 1
        inner join inventory i on asdd.sku = i.sku
        inner join products p on i.pd_id = p.pd_id
        where asdp.delivery_time= #{deliveryTime} and asdp.concat_id = #{contactId} and asdp.status >= 1
    </select>

    <select id="afterSaleTaskIn" resultType="net.summerfarm.model.domain.OrderItem">
        select asdd.sku , asdd.quantity amount from after_sale_delivery_path asdp
        inner join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id and asdd.type = 1 and asdd.status = 1
        left join warehouse_inventory_mapping wim on wim.store_no = asdp.out_store_no and wim.sku = asdd.sku
        where wim.warehouse_no = #{outStoreNo} and asdp.out_store_no = #{storeNo} and asdp.delivery_time = #{deliveryTime} and asdp.status > 0 and asdp.type > 0
    </select>

    <select id="afterSaleByNo" resultType ="net.summerfarm.model.domain.DeliveryPathShortSku">
        select dpss.sku,dpss.short_cnt,dpss.type,dpss.remark
        from after_sale_delivery_path asdp
        left join delivery_path dp on dp.contact_id = asdp.concat_id and dp.delivery_time = asdp.delivery_time
        inner join delivery_path_short_sku dpss on dp.id = dpss.delivery_path_id
        where asdp.status >= 1 and asdp.after_sale_no = #{afterSaleNo} and dpss.type = #{type}
    </select>
    <select id="selectExchangeGoods" resultType="net.summerfarm.model.domain.ExchangeGoods">
        select asdd.sku,asdd.quantity,asdd.pd_name pdName,asdd.weight from after_sale_delivery_path asdp
        left join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id
        where asdd.type = 1  and asdp.after_sale_no = #{afterSaleNo}
    </select>
    <select id="selectPathByNo" resultType="net.summerfarm.model.domain.AfterSaleDeliveryPath">
      select id,delivery_time deliveryTime,out_store_no outStoreNo,concat_id  concatId,`type`, status
      from after_sale_delivery_path where after_sale_no = #{afterSaleNo}

    </select>

    <update id="updateDeliveryPath">
        update after_sale_delivery_path set status = #{status} where after_sale_no  = #{afterSaleNo}
    </update>
    <update id="updateDeliveryPathByPath" parameterType="net.summerfarm.model.domain.AfterSaleDeliveryPath">
         update after_sale_delivery_path set status = #{status}
         where delivery_time =#{deliveryTime} and concat_id=#{concatId} and status > 0
    </update>

    <select id="selectDetail" resultType="net.summerfarm.model.domain.AfterSaleDeliveryDetail">
        select sku,quantity from after_sale_delivery_detail
        where as_delivery_path_id = #{id} and status = 1 and `type` = 0
    </select>

    <select id="afterSalePick" resultType="net.summerfarm.model.domain.StockTaskPick">
        select asdd.sku , asdd.quantity amount ,m.admin_id adminId,asdd.weight,a.name_remakes adminName,asdd.pd_name pdName
        from after_sale_delivery_path asdp
        inner join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id AND asdd.type = 0 AND asdd.status = 1
        left join merchant m on m.m_id = asdp.m_id
        left join admin a on a.admin_id = m.admin_id
        left join area on area.area_no = m.area_no
        left join warehouse_inventory_mapping wim on asdd.sku = wim.sku and asdp.out_store_no= wim.store_no
        where asdp.out_store_no = #{storeNo} AND asdp.delivery_time = #{deliveryTime} AND asdp.status > 0  and
         wim.warehouse_no = #{outStoreNo}
        <choose>
            <when test="closeTime != null">
                AND a.close_order_time = #{closeTime}
                AND m.size = '大客户'
                AND a.close_order_type = 1
            </when>
            <otherwise>
                AND (( a.close_order_time IS NULL
                and m.size = '大客户'
                AND a.close_order_type = 0) or (m.admin_id is null))
            </otherwise>
        </choose>
    </select>

    <select id="closingOrderBySplit" parameterType="net.summerfarm.model.vo.MerchantVO"
            resultMap="net.summerfarm.mapper.manage.OrdersMapper.ClosingOrderMap">
        select asdp.id orderId, asdd.as_delivery_path_id orderNo, m.mname userName, m.m_id, asdp.delivery_time
            deliveryTime,m.show_price showPrice,asdp.type deliveryType,asdd.type orderItemDeliveryType,asdd.id id,asdd.sku, asdd.quantity amount
         from after_sale_delivery_path  asdp
        left join after_sale_delivery_detail asdd on asdd.as_delivery_path_id = asdp.id and asdd.status = 1
        left join merchant m on m.m_id = asdp.m_id
        where asdp.delivery_time >= #{deliveryTime}
        AND m.area_no= #{areaNo}
        and m.province = #{province}
        and m.city = #{city}
        and m.area = #{area} and asdp.status >= 1

    </select>

    <update id="updateAfterPathStoreNo">
        update after_sale_delivery_path set out_store_no = #{storeNo}
        where
        <if test="idList != null and idList.size > 0">
            id in
            <foreach collection="idList" item="id" index="index" open="("  separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <select id="selectAfterSaleQuantityByStoreNo" resultType="java.lang.Integer">
        select ifnull(sum(asdd.quantity),0) amount
        from after_sale_delivery_path asdp
                 left join after_sale_delivery_detail asdd on asdd.as_delivery_path_id = asdp.id
                 left join warehouse_inventory_mapping wim on asdd.sku = wim.sku and asdp.out_store_no = wim.store_no
        where asdd.type = 0
          and asdd.sku = #{sku}
          and asdp.delivery_time > #{deliveryDate}
          and asdp.status = 1
          and wim.store_no = #{storeNo}
    </select>

    <select id="selectAfterSaleQuantity" resultType="java.lang.Integer">
        select ifnull(sum(asdd.quantity),0) amount
        from after_sale_delivery_path asdp
                 left join after_sale_delivery_detail asdd on asdd.as_delivery_path_id = asdp.id
                 left join warehouse_inventory_mapping wim on asdd.sku = wim.sku and asdp.out_store_no = wim.store_no
        where asdd.type = 0
          and asdd.sku = #{sku}
          and asdp.delivery_time > #{deliveryDate}
          and asdp.status = 1
          and wim.warehouse_no = #{warehouseNo}
    </select>

    <select id="selectAfterSaleOrders" resultType="net.summerfarm.model.vo.OrderItemVO">
        select
        asdd.quantity amount,
        (case m.size when '单店' then 0 when '小连锁' then 1 when '大连锁' then 2
        when '大客户' then 3  else 0 end) as customerType ,
        asdp.after_sale_no orderNo,
        m.grade,
        m.m_id mId,
        asdp.gmt_create orderTime,
        asdp.delivery_time deliveryTime
        from after_sale_delivery_path asdp
                 left join after_sale_delivery_detail asdd on asdd.as_delivery_path_id = asdp.id
                 left join warehouse_inventory_mapping wim on asdd.sku = wim.sku and asdp.out_store_no = wim.store_no
                 left join merchant m on m.m_id =asdp.m_id
        where asdd.type = 0
          and asdd.sku = #{sku}
          and asdp.delivery_time > #{deliveryDate}
          and asdp.status = 1
          and wim.warehouse_no = #{warehouseNo}
    </select>

    <select id="selectAfterSaleOrdersByStoreNo" resultType="net.summerfarm.model.vo.OrderItemVO">
        select
        asdd.quantity amount,
        asdp.after_sale_no orderNo,
        m.grade,
        m.m_id mId,
        asdp.gmt_create orderTime,
        cc.phone
        from after_sale_delivery_path asdp
                 left join after_sale_delivery_detail asdd on asdd.as_delivery_path_id = asdp.id
                 left join warehouse_inventory_mapping wim on asdd.sku = wim.sku and asdp.out_store_no = wim.store_no
                 left join merchant m on m.m_id =asdp.m_id
                 left join contact cc on cc.contact_id= asdp.concat_id
        where asdd.type = 0
          and asdd.sku = #{sku}
          and asdp.delivery_time > #{deliveryDate}
          and asdp.status = 1
          and wim.store_no = #{storeNo}
    </select>

    <update id="updateMerchantId">
        update after_sale_delivery_path set m_id = #{newMid}  where m_id = #{oldMid}
    </update>
    <update id="updateDeliveryPathStatus">
        update after_sale_delivery_path set status = 2
         where delivery_time =#{deliveryTime} and concat_id=#{contactId} and status > 0
    </update>


    <select id="getAfterSaleOrderInfo" resultType="net.summerfarm.model.vo.ReturnSaleNeedStockVo">
        SELECT
            asdd.sku,
            asdd.quantity amount,
            asdp.out_store_no outStoreNo,
            asdp.after_sale_no as afterSaleNo,
            aso.order_no orderNo,
            asdp.m_id mId
        FROM
            after_sale_delivery_detail asdd
            INNER JOIN after_sale_delivery_path asdp ON asdp.id = asdd.as_delivery_path_id
            AND asdp.STATUS > 0
            AND asdp.type > 0
            INNER JOIN after_sale_order aso on aso.after_sale_order_no=asdp.after_sale_no and aso.status=2
            INNER JOIN after_sale_proof asp ON asp.after_sale_order_no = aso.after_sale_order_no
        WHERE
            asp.auditetime &gt;= #{gmtCreate}
          AND asdd.type = 1
          AND asdd.STATUS = 1
          AND asp.handle_type != 11
          AND asp.handle_type != 12
    </select>

    <select id="getAfterDataByDTAndCId" resultType="net.summerfarm.model.vo.GetAfterDataVO">
        SELECT
            t.id,
            asdd.type,
            asdd.sku,
            asdd.quantity,
            asdd.id as afterSaleDeliveryDetailId,
            t.after_sale_no as afterSaleNo,
            wip.warehouse_no AS areaNo,
            dp.order_store_no AS storeNo,
            t.delivery_time as deliveryTime,
            t.concat_id as contactId,
            asf.handle_type as handleType,
            aso.order_no as orderNo
        FROM
            after_sale_delivery_path t
        LEFT JOIN after_sale_delivery_detail asdd ON t.id = asdd.as_delivery_path_id
        LEFT JOIN after_sale_order aso ON t.after_sale_no = aso.after_sale_order_no
        LEFT JOIN after_sale_proof asf ON asf.after_sale_order_no = aso.after_sale_order_no
        LEFT JOIN orders o ON o.order_no = aso.order_no
        LEFT JOIN delivery_plan dp ON dp.order_no = o.order_no
        left JOIN warehouse_inventory_mapping wip ON wip.sku = asdd.sku
        AND dp.order_store_no = wip.store_no
        WHERE
              1=1
            <if test="deliveryTime != null">
                and t.delivery_time = #{deliveryTime}
            </if>
            <if test="contactId != null">
                and t.concat_id = #{contactId}
            </if>
          AND t.status = 1
          AND asdd.status = 1
          AND asdd.intercept_flag = 0
          AND aso.STATUS =2
          AND asf.handle_type = 7
    </select>

    <select id="selectReissueByPathId" parameterType="integer" resultType="net.summerfarm.model.vo.LackGoodsOrderInfoVO">
        SELECT
            aso.order_no AS orderNo,
            oi.sku,
            asdd.quantity amount,
            oi.original_price price,
            t.gmt_create orderTime,
            wip.warehouse_no AS areaNo,
            dp.order_store_no AS storeNo,
            dpa.id deliveryPathId,
            o.m_id mId,
            dpa.finish_time finishTime
        FROM
            after_sale_delivery_path t
                LEFT JOIN delivery_path dpa ON t.delivery_time= dpa.delivery_time AND t.concat_id=dpa.contact_id AND t.out_store_no=dpa.store_no
                LEFT JOIN after_sale_delivery_detail asdd ON t.id = asdd.as_delivery_path_id
                LEFT JOIN after_sale_order aso ON t.after_sale_no = aso.after_sale_order_no
                LEFT JOIN after_sale_proof asf ON asf.after_sale_order_no = aso.after_sale_order_no
                LEFT JOIN orders o ON o.order_no = aso.order_no
                LEFT JOIN order_item oi ON oi.order_no = o.order_no AND asdd.sku=oi.sku
                LEFT JOIN delivery_plan dp ON dp.order_no = o.order_no
                LEFT JOIN warehouse_inventory_mapping wip ON wip.sku = asdd.sku
                AND dp.order_store_no = wip.store_no
        WHERE
            1 = 1
          AND dpa.id= #{pathId}
          AND t.STATUS > 0
          AND asdd.STATUS = 1
          AND asdd.intercept_flag = 0
          AND aso.STATUS = 2
          AND asf.handle_type = 7
          AND asdd.type = 0
        order by t.gmt_create desc
    </select>

    <select id="closingOrderByAfterSaleByMid" resultMap="net.summerfarm.mapper.manage.OrdersMapper.ClosingOrderMap" >
        select asdp.id orderId, asdd.as_delivery_path_id orderNo, m.mname userName, m.m_id, con.contact, asdp.delivery_time
        deliveryTime,m.show_price showPrice,asdp.type deliveryType,asdd.type orderItemDeliveryType,
        con.phone phone, con.area, CONCAT(con.province,con.city,con.area,con.address,ifnull(con.house_number,'')) deliveryAddress,
        asdd.id id,p.pd_name , asdd.quantity amount,  i.unit,p.category_id categoryId,c1.type categoryType,
        i.weight,asdd.sku,con.delivery_car deliveryCar,con.contact_id, m.area_no areaNo,dep.path,dep.sort,con.path prePath,
        dep.total_volume totalVolume,con.distance,c1.category,c2.category parentCategory,p.storage_location,m.admin_id adminId,a.sku_sorting,
        i.type skuType,a.name_remakes nameRemakes,i.ext_type extType,0 brandType
        from after_sale_delivery_path  asdp
        left join after_sale_delivery_detail asdd on asdd.as_delivery_path_id = asdp.id and asdd.status = 1
        LEFT JOIN delivery_path dep ON asdp.concat_id = dep.contact_id AND asdp.delivery_time = dep.delivery_time
        left join merchant m on m.m_id = asdp.m_id
        left join contact con on asdp.concat_id = con.contact_id
        LEFT JOIN inventory i ON i.sku = asdd.sku
        LEFT JOIN products p ON i.pd_id=p.pd_id
        left join area on area.area_no=m.area_no
        LEFT join admin a on a.admin_id = m.admin_id
        LEFT JOIN category c1 ON p.category_id=c1.id
        LEFT JOIN category c2 ON c1.parent_id=c2.id
        <where>
            asdp.delivery_time <![CDATA[ >= ]]>  #{startDate}  and asdp.delivery_time <![CDATA[<]]>  #{endDate}  and asdp.status >= 1
            and asdd.show_flag = 0
            <if test="mId != null">
                AND con.m_id=#{mId}
            </if>
        </where>
    </select>
    <select id="selectByAfterNo" resultType="net.summerfarm.model.vo.AfterSaleDeliveryPathVO">
        select asdp.delivery_time deliveryTime ,asdp.status,dp.finish_time finishTime,IF(ISNULL(dpsk.remark),0,1) signForStatus,dpsk.remark,asdd.type
        from  after_sale_delivery_path asdp
        left join after_sale_delivery_detail asdd on asdd.as_delivery_path_id = asdp.id
        left join delivery_path dp on dp.delivery_time = asdp.delivery_time and dp.contact_id = asdp.concat_id and dp.store_no = asdp.out_store_no
        left join delivery_car_path dcp on dcp.delivery_time = dp.delivery_time and dp.path = dcp.path and dp.store_no = dcp.store_no
        left join delivery_path_short_sku dpsk on dpsk.delivery_path_id = dp.id and dpsk.sku = asdd.sku
        where asdp.after_sale_no = #{afterSaleOrderNo}
    </select>

    <select id="selectByAfterNoHandle" resultType="net.summerfarm.model.vo.AfterSaleDeliveryPathVO">
           select asdp.delivery_time deliveryTime ,asdp.status,dp.finish_time finishTime,IF(ISNULL(dpsk.remark),0,1) signForStatus,
           dpsk.remark,asdd.type,dcp.delivery_car_id deliveryCarId,asdp.after_sale_no afterSaleNo
        from  after_sale_delivery_path asdp
        left join after_sale_delivery_detail asdd on asdd.as_delivery_path_id = asdp.id and asdd.type = 1
        left join delivery_path dp on dp.delivery_time = asdp.delivery_time and dp.contact_id = asdp.concat_id and dp.store_no = asdp.out_store_no
        left join delivery_car_path dcp on dcp.delivery_time = dp.delivery_time and dp.path = dcp.path and dp.store_no = dcp.store_no
        left join delivery_path_short_sku dpsk on dpsk.delivery_path_id = dp.id and dpsk.sku = asdd.sku
        where asdp.after_sale_no in
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
     </select>
    <select id="selectAfterDetailNew" resultType="net.summerfarm.model.vo.OrderItemVO">
        select asdd.sku , asdd.quantity amount,i.weight,p.pd_name pdName ,asdd.type orderItemDeliveryType,i.unit,i.type skuType,
        ad.name_remakes nameRemakes,p.picture_path skuPic,aso.order_no orderNo,c.type categoryType,aso.suit_id  suitId,asdd.intercept_time interceptTime
        from after_sale_delivery_path asdp
        inner join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id and asdd.status = 1
        inner join after_sale_order aso on asdp.after_sale_no = aso.after_sale_order_no
        inner join inventory i on asdd.sku = i.sku
        inner join products p on i.pd_id = p.pd_id
        LEFT JOIN admin ad on i.admin_id = ad.admin_id
        left join category c on c.id = p.category_id
        where asdp.delivery_time= #{deliveryTime} and asdp.concat_id = #{concatId} and asdp.status >= 1 and asdp.out_store_no =#{outStoreNo}
    </select>
    <select id="selectAfterSaleDetail" resultType="net.summerfarm.model.vo.OrderItemVO">
        select asdd.sku , asdd.quantity amount,i.weight,p.pd_name pdName ,asdd.type orderItemDeliveryType,i.unit,i.type skuType,
        ad.name_remakes nameRemakes,p.picture_path skuPic,aso.order_no orderNo,c.type categoryType,aso.suit_id  suitId,asdd.intercept_time interceptTime
        from after_sale_delivery_path asdp
        inner join after_sale_delivery_detail asdd on asdp.id = asdd.as_delivery_path_id and asdd.status = 1
        inner join after_sale_order aso on asdp.after_sale_no = aso.after_sale_order_no
        inner join inventory i on asdd.sku = i.sku
        inner join products p on i.pd_id = p.pd_id
        LEFT JOIN admin ad on i.admin_id = ad.admin_id
        left join category c on c.id = p.category_id
        where asdp.delivery_time= #{deliveryTime} and asdp.concat_id = #{concatId} and asdp.status >= 1 and asdp.after_sale_no =#{afterSaleNo}
    </select>
    <select id="selectAfterSaleDeliveryPlanByStoreNo" resultMap="oldDistOrderMap" >
        SELECT asdp.id,
        asdp.after_sale_no AS outer_order_id,
        201                AS `source`,
        asdp.concat_id     AS outer_contact_id,
        asdp.delivery_time AS expect_begin_time,
        asdp.out_store_no  AS store_no,
        con.province,
        con.city,
        con.area,
        asdd.id AS item_id,
        asdd.sku AS outer_item_id,
        asdd.quantity,
        asdd.type AS item_delivery_type
        FROM after_sale_delivery_path asdp
        LEFT JOIN contact con ON asdp.concat_id = con.contact_id
        INNER JOIN after_sale_delivery_detail asdd
        ON asdd.as_delivery_path_id = asdp.id AND asdd.status = 1 AND asdd.intercept_flag = 0
        WHERE asdp.status >= 1
        <if test="deliveryDate != null">
            AND asdp.delivery_time <![CDATA[>]]> #{deliveryDate}
        </if>

        <if test="storeNo != null">
            and asdp.out_store_no=#{storeNo}
        </if>
    </select>
    <update id="updateAfterSaleDeliveryPlanStoreNo">
        update after_sale_delivery_path set out_store_no = #{newStoreNo}
        where out_store_no = #{oldStoreNo}
        and id IN
        <foreach collection="afterSaleDeliveryPlanIds" item="id" open="("  separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>