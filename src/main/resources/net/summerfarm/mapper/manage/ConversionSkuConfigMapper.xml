<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.ConversionSkuConfigMapper">

    <insert id="saveConfig" parameterType="net.summerfarm.model.domain.ConversionSkuConfig">
        insert into conversion_sku_config (add_time, update_time, warehouse_no, pd_id, in_sku, out_sku, admin_id, status, rates)
        value (now(),now(),#{warehouseNo},#{pdId},#{inSku},#{outSku},#{adminId},#{status},#{rates})
    </insert>

    <insert id="batchSaveConfig" parameterType="net.summerfarm.model.vo.ConversionSkuConfigVO">

        insert into conversion_sku_config (add_time, update_time, warehouse_no, pd_id, in_sku, out_sku, admin_id, status, rates)
        values
        <foreach collection="list" index="index"  separator="," item="item">
            (now(),now(),#{item.warehouseNo},#{item.pdId},#{item.inSku},#{item.outSku},#{item.adminId},
            #{item.status},#{item.rates})
        </foreach>

    </insert>

    <select id="selectListConfigVO" parameterType="net.summerfarm.model.vo.ConversionSkuConfigVO" resultType="net.summerfarm.model.vo.ConversionSkuConfigVO">
        select csc.id,
            wsc.warehouse_name warehouseName,
            csc.warehouse_no warehouseNo,
            p.pd_name pdName,
            csc.in_sku inSku,
            i.weight inSkuWeight,
            csc.out_sku outSku,
            o.weight outSkuWeight,
            csc.rates,csc.status,
            csc.admin_id admin_id,
            a.realname adminName,
            csc.update_time updateTime
        from conversion_sku_config csc
        left join inventory i on i.sku = csc.in_sku
        left join inventory o on o.sku = csc.out_sku
        left join products p on csc.pd_id = p.pd_id
        left join admin a on a.admin_id = csc.admin_id
        left join warehouse_storage_center  wsc on wsc.warehouse_no= csc.warehouse_no
        <where>
            <if test="warehouseNo != null">
                AND csc.warehouse_no = #{warehouseNo}
            </if>
            <if test="pdName != null">
                AND p.pd_name = #{pdName}
            </if>
            <if test="inSku != null">
                AND csc.in_sku = #{inSku}
            </if>
            <if test="outSku != null">
                AND csc.out_sku = #{outSku}
            </if>
            <if test="status != null">
                AND csc.status = #{status}
            </if>
        </where>
    </select>

    <update id="updateSaveConfig" parameterType="net.summerfarm.model.domain.ConversionSkuConfig">
      update conversion_sku_config set status = #{status},update_time = now(),admin_id = #{adminId} where id = #{id}
    </update>

    <select id="selectConfig" parameterType="net.summerfarm.model.domain.ConversionSkuConfig" resultType="net.summerfarm.model.domain.ConversionSkuConfig">
      select
          id,
          warehouse_no warehouseNo,
          pd_id pdId,
          in_sku inSku,
          out_sku outSku,
          admin_id adminId,
          status,
          rates
      from conversion_sku_config
      <where>
        <if test="inSku != null">
           AND in_sku = #{inSku}
        </if>
        <if test="outSku != null">
          AND out_sku = #{outSku}
        </if>
        <if test="warehouseNo != null">
          AND warehouse_no = #{warehouseNo}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
      </where>
    </select>
    <select id="selectConfigDetail"  resultType="net.summerfarm.model.domain.ConversionSkuConfig">

      select
          id,
          warehouse_no warehouseNo,
          pd_id pdId,
          in_sku inSku,
          out_sku outSku,
          admin_id adminId,
          status,
          rates
      from conversion_sku_config
      where id = #{id}
    </select>

</mapper>