<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.FinanceAccountVerificationTempMapper">

<insert id="insertBatch">
    insert into finance_account_verification_temp (verification_id,finance_account_statement_id,supplier_id,pay_amount,write_off_amount,input_amount_total,input_source_id,input_type,output_amount_total,output_source_id,output_type,create_time,creator,del_flag,input_stock_task_id,output_stock_task_id)
    values
    <foreach collection="list" item="item" index="index" separator=",">
        (#{item.verificationId},#{item.financeAccountStatementId},#{item.supplierId},#{item.payAmount},#{item.writeOffAmount},#{item.inputAmountTotal},#{item.inputSourceId},#{item.inputType},#{item.outputAmountTotal},#{item.outputSourceId},#{item.outputType},#{item.createTime},#{item.creator},0,#{item.inputStockTaskId},#{item.outputStockTaskId})
    </foreach>
    </insert>



<delete id="truncateTable">
    truncate finance_account_verification_temp
</delete>

<select id="queryAmountByPurchaseListAndSupplier" resultType="net.summerfarm.model.vo.FinanceAccountTypeAmountVO">
    select input_source_id as purchaseNo, ifnull(sum(write_off_amount),0) as amount
    from finance_account_verification_temp
    where input_type =1 and input_source_id in
    <foreach collection="list" separator="," open="(" item="purchaseNo" close=")" >
        #{purchaseNo}
    </foreach>
    and supplier_id=#{supplierId} and output_type=#{outType}
    group by input_source_id
    </select>
</mapper>