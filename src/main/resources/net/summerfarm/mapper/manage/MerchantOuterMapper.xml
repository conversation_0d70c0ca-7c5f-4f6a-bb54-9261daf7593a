<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="net.summerfarm.mapper.manage.MerchantOuterMapper">

    <sql id="base_column">
        id, m_id mId, outer_no outerNo, remark, gmt_modified gmtModified, gmt_create gmtCreate,outer_platform_id outerPlatformId
    </sql>

    <insert id="insert" parameterType="net.summerfarm.model.domain.MerchantOuterDO">
        INSERT INTO merchant_outer(m_id, outer_no, remark,outer_platform_id)
        VALUES(#{mId}, #{outerNo}, #{remark},#{outerPlatformId})
    </insert>

    <select id="selectByMid" resultType="net.summerfarm.model.domain.MerchantOuterDO">
        SELECT <include refid="base_column"/>
        FROM merchant_outer
        WHERE m_id = #{mId} and outer_platform_id = #{outerPlatformId}
    </select>

    <select id="selectByOuterNo" resultType="net.summerfarm.model.domain.MerchantOuterDO">
        SELECT <include refid="base_column"/>
        FROM merchant_outer
        WHERE outer_no = #{outerNo} and outer_platform_id = #{outerPlatformId}
    </select>

    <select id="selectByMIdList" resultType="net.summerfarm.model.domain.MerchantOuterDO">
        SELECT <include refid="base_column"/>
        FROM merchant_outer
        <where>
            <if test="mIdList != null ">
                m_id in
                <foreach collection="mIdList" item="mId" separator="," open="(" close=")">
                    #{mId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectMerchantOrder" resultType="net.summerfarm.model.vo.MerchantOrderVO">
        select distinct dp.order_no orderNo,dp.contact_id contactId,mo.outer_no outerNo,mo.m_id mId,dp.delivery_time deliveryTime ,
                        dp.order_store_no storeNo,m.mname mName
        from merchant_outer mo
                 inner join contact c on c.m_id = mo.m_id
                 inner join delivery_plan dp on dp.contact_id = c.contact_id
                 inner join  orders o on o.order_no = dp.order_no
                 inner join  area a on a.area_no = o.area_no
                 inner join merchant m on m.m_id = mo.m_id
                 inner join  order_item oi on oi.order_no = dp.order_no
        where dp.delivery_time = #{deliveryDate} and dp.order_no like '04%' and dp.status in(2,3,6)
        and oi.sku in
        <foreach collection="skus" item="sku" separator="," open="(" close=")">
            #{sku}
        </foreach>
    </select>

    <update id="update" parameterType="net.summerfarm.model.domain.MerchantOuterDO">
        update  merchant_outer set outer_no = #{outerNo}, gmt_modified = now(), outer_platform_id = #{outerPlatformId}
        where id = #{id}
    </update>

    <delete id="deleteByIdBatch">
        delete from merchant_outer where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </delete>

    <select id="selectByOuterMerchant" resultType="net.summerfarm.model.vo.MerchantOuterDOVO">
        SELECT mo.id, mo.m_id mId, mo.outer_no outerNo, mo.remark, mo.gmt_modified gmtModified, mo.gmt_create gmtCreate,
               mo.outer_platform_id outerPlatformId,m.area_no areaNo
        FROM merchant_outer mo
        left join merchant m on m.m_id = mo.m_id
        WHERE mo.outer_platform_id = #{outerPlatformId} and m.admin_id = #{adminId}
    </select>
    <select id="selectByMidReceiptNotice" resultType="java.lang.Integer">
        SELECT count(1) FROM merchant_outer m
        LEFT JOIN outer_platform p on p.outer_platform_id = m.outer_platform_id
        WHERE m.m_id = #{mId} and p.push_order_switch=1
    </select>

</mapper>