<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FrontCategoryToSkuMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FrontCategoryToSku">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="front_category_id" jdbcType="BIGINT" property="frontCategoryId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, front_category_id, sku,pd_name, creator, create_time,updater,update_time
  </sql>
  <select id="queryByFrontCategoryIds" resultType="net.summerfarm.model.domain.FrontCategoryToSku">
    select
    <include refid="Base_Column_List" />
    from front_category_to_sku
    where front_category_id in
    <foreach collection="frontCategoryIds" item="id" close=")" open="(" separator=",">
      #{id}
    </foreach>
  </select>
  <select id="queryByFrontCategoryIdAndPdName" resultType="java.lang.String">
    select
    sku
    from front_category_to_sku
    where front_category_id = #{frontCategoryId,jdbcType=BIGINT}
    and pd_name like concat('%',#{pdName,jdbcType=VARCHAR},'%')
  </select>
  <select id="queryByFrontCategoryIdAndSkuList" resultType="java.lang.String">
    select
      sku
    from front_category_to_sku
    where front_category_id = #{frontCategoryId,jdbcType=BIGINT}
    and sku in
      <foreach collection="skus" item="sku" close=")" open="(" separator=",">
        #{sku}
      </foreach>
  </select>
  <select id="queryByFrontCategoryId" resultType="java.lang.String">
    select
      sku
    from front_category_to_sku
    where front_category_id = #{frontCategoryId}
  </select>

  <delete id="removeByFrontCategoryId">
    delete from front_category_to_sku
    where front_category_id = #{frontCategoryId,jdbcType=BIGINT}
  </delete>
  <insert id="insertBatch">
    insert into front_category_to_sku (front_category_id, sku,pd_name ,creator,updater)
    values
    <foreach collection="addList" item="item" index="index" separator=",">
      (#{item.frontCategoryId,jdbcType=BIGINT}, #{item.sku,jdbcType=VARCHAR},#{item.pdName,jdbcType=VARCHAR},#{item.creator,jdbcType=VARCHAR}, #{item.updater,jdbcType=VARCHAR})
    </foreach>
  </insert>


</mapper>