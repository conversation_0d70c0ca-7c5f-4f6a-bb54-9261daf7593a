<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.OuterPlatformPushRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.OuterPlatformPushRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="on_sale" jdbcType="INTEGER" property="onSale" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="xm_order_no" jdbcType="VARCHAR" property="xmOrderNo" />
    <result column="push_status" jdbcType="INTEGER" property="pushStatus" />
    <result column="push_times" jdbcType="INTEGER" property="pushTimes" />
    <result column="req_content" jdbcType="VARCHAR" property="reqContent" />
    <result column="res_content" jdbcType="VARCHAR" property="resContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, type, sku, area_no, admin_id, on_sale, price, xm_order_no, push_status,
    push_times, req_content, res_content, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from outer_platform_push_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from outer_platform_push_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.OuterPlatformPushRecord">
    insert into outer_platform_push_record (id, type, sku, 
      area_no, admin_id, on_sale, 
      price, xm_order_no,
      push_status, push_times, req_content, 
      res_content, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{areaNo,jdbcType=INTEGER}, #{adminId,jdbcType=INTEGER}, #{onSale,jdbcType=INTEGER}, 
      #{price,jdbcType=DECIMAL}, #{xmOrderNo,jdbcType=VARCHAR},
      #{pushStatus,jdbcType=INTEGER}, #{pushTimes,jdbcType=INTEGER}, #{reqContent,jdbcType=VARCHAR}, 
      #{resContent,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.OuterPlatformPushRecord">
    insert into outer_platform_push_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="onSale != null">
        on_sale,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="xmOrderNo != null">
        xm_order_no,
      </if>
      <if test="pushStatus != null">
        push_status,
      </if>
      <if test="pushTimes != null">
        push_times,
      </if>
      <if test="reqContent != null">
        req_content,
      </if>
      <if test="resContent != null">
        res_content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="onSale != null">
        #{onSale,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="xmOrderNo != null">
        #{xmOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=INTEGER},
      </if>
      <if test="pushTimes != null">
        #{pushTimes,jdbcType=INTEGER},
      </if>
      <if test="reqContent != null">
        #{reqContent,jdbcType=VARCHAR},
      </if>
      <if test="resContent != null">
        #{resContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.OuterPlatformPushRecord">
    update outer_platform_push_record
    <set>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="onSale != null">
        on_sale = #{onSale,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="xmOrderNo != null">
        xm_order_no = #{xmOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="pushStatus != null">
        push_status = #{pushStatus,jdbcType=INTEGER},
      </if>
      <if test="pushTimes != null">
        push_times = #{pushTimes,jdbcType=INTEGER},
      </if>
      <if test="reqContent != null">
        req_content = #{reqContent,jdbcType=VARCHAR},
      </if>
      <if test="resContent != null">
        res_content = #{resContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.OuterPlatformPushRecord">
    update outer_platform_push_record
    set type = #{type,jdbcType=INTEGER},
      sku = #{sku,jdbcType=VARCHAR},
      area_no = #{areaNo,jdbcType=INTEGER},
      admin_id = #{adminId,jdbcType=INTEGER},
      on_sale = #{onSale,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      xm_order_no = #{xmOrderNo,jdbcType=VARCHAR},
      push_status = #{pushStatus,jdbcType=INTEGER},
      push_times = #{pushTimes,jdbcType=INTEGER},
      req_content = #{reqContent,jdbcType=VARCHAR},
      res_content = #{resContent,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectRecordByLatestOne" resultType="net.summerfarm.model.domain.OuterPlatformPushRecord">
    select id,type,sku,area_no areaNo,admin_id adminId,on_sale onSale,price,xm_order_no xmOrderNo,
        push_status pushStatus,push_times pushTimes from outer_platform_push_record
        <where>
            <if test="type !=null">
              and type = #{type}
            </if>
            <if test="sku !=null">
              and sku = #{sku}
            </if>
            <if test="areaNo !=null">
              and area_no = #{areaNo}
            </if>
            <if test="adminId !=null">
              and admin_id = #{adminId}
            </if>
            <if test="pushStatus !=null">
              and push_status = #{pushStatus}
            </if>
        </where>
        order by id desc limit 1
  </select>

  <select id="selectRecordByXmOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from outer_platform_push_record where xm_order_no = #{xmOrderNo} and type = #{type}
  </select>

</mapper>