<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.DistributionFreeRuleMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DistributionFreeRule">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="rule" property="rule" jdbcType="VARCHAR" />
        <result column="distribution_id" property="distributionId" jdbcType="INTEGER" />
    </resultMap>

    <sql id="queryColumns">
        id , status ,rule ,distribution_id as distributionId
    </sql>

    <select id="queryById" parameterType="java.lang.Integer" resultType="net.summerfarm.model.domain.DistributionFreeRule">

        select <include refid="queryColumns"/>
        from rule_distribution_free
        where id = #{id}
    </select>

    <insert id="insertFreeRule" parameterType="net.summerfarm.model.domain.DistributionFreeRule" useGeneratedKeys="true" keyProperty="id">
        insert into rule_distribution_free
        (status ,rule , distribution_id)
        values
        <foreach collection ="list" item="item" separator =",">
            (#{item.status}, #{item.rule}, #{item.distributionId})
        </foreach >
    </insert>

    <insert id="insertFreeRuleVO" parameterType="net.summerfarm.model.vo.DistributionFreeRuleVO" useGeneratedKeys="true" keyProperty="id">
        insert into rule_distribution_free
        (status ,rule , distribution_id)
        values
        <foreach collection ="list" item="item" separator =",">
            (#{item.status}, #{item.rule}, #{item.distributionId})
        </foreach >
    </insert>


    <update id="updateFreeRule" parameterType="net.summerfarm.model.domain.DistributionFreeRule">
        update rule_distribution_free
        set
          <if test = "status != null">
            status = #{status} ,
          </if>
          <if test = "rule != null">
              rule = #{rule} ,
          </if>
          <if test = "distributionId != null">
            distribution_id = #{distributionId} ,
          </if>
          <if test="id != null">
            id =#{id}
          </if>
        where  id = #{id}

    </update>



</mapper>