<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTaskItemDetailMapper">

    <resultMap id="baseMap" type="net.summerfarm.model.domain.StockTaskItemDetail">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="stock_task_item_id" property="stockTaskItemId" jdbcType="INTEGER"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="list_no" property="listNo" jdbcType="VARCHAR"/>
        <result column="quality_date" property="qualityDate" jdbcType="DATE"/>
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="cabinet_code" property="cabinetCode" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="VOMap" type="net.summerfarm.model.vo.StockTaskItemDetailVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="stock_task_item_id" property="stockTaskItemId" jdbcType="INTEGER"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="list_no" property="listNo" jdbcType="VARCHAR"/>
        <result column="quality_date" property="qualityDate" jdbcType="DATE"/>
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="production_date" property="productionDate" jdbcType="DATE" />
        <result column="stock_task_id" property = "stockTaskId" jdbcType="INTEGER"/>
        <result column="gl_no" property="glNo"/>
        <result column="should_quantity" property="shouldQuantity"/>
        <result column="out_store_quantity" property="outStoreQuantity"/>
        <result column="system_generation" property="systemGeneration"/>
        <result column="cabinet_code" property="cabinetCode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,stock_task_item_id,sku,list_no,quality_date,quantity,remark
    </sql>

    <insert id="insert" parameterType="net.summerfarm.model.domain.StockTaskItemDetail">
        INSERT INTO stock_task_item_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stockTaskItemId != null">
                stock_task_item_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="listNo != null">
                list_no,
            </if>
            <if test="qualityDate != null">
                quality_date,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="productionDate != null">
                production_date,
            </if>
            <if test="glNo != null">
                gl_no,
            </if>
            <if test="shouldQuantity != null">
                should_quantity,
            </if>
            <if test="outStoreQuantity != null">
                out_store_quantity,
            </if>
            <if test="systemGeneration != null">
                system_generation,
            </if>
            <if test="cabinetCode != null">
                cabinet_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stockTaskItemId != null">
                #{stockTaskItemId},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="listNo != null">
                #{listNo},
            </if>
            <if test="qualityDate != null">
                #{qualityDate},
            </if>
            <if test="quantity != null">
                #{quantity},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="productionDate != null">
                #{productionDate},
            </if>
            <if test="glNo != null">
                #{glNo},
            </if>
            <if test="shouldQuantity != null">
                #{shouldQuantity},
            </if>
            <if test="outStoreQuantity != null">
                #{outStoreQuantity},
            </if>
            <if test="systemGeneration != null">
                #{systemGeneration},
            </if>
            <if test="cabinetCode != null">
                #{cabinetCode},
            </if>
        </trim>
    </insert>

    <select id="selectOne" parameterType="net.summerfarm.model.domain.StockTaskItemDetail" resultType="net.summerfarm.model.domain.StockTaskItemDetail">
        SELECT id,stock_task_item_id stockTaskItemId,sku,list_no listNo,quality_date qualityDate,quantity,remark,should_quantity shouldQuantity,out_store_quantity outStoreQuantity,system_generation systemGeneration
            ,cabinet_code cabinetCode
        FROM stock_task_item_detail
        <where>
            <if test="stockTaskItemId != null">
                AND stock_task_item_id = #{stockTaskItemId}
            </if>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
            <if test="listNo != null">
                AND list_no = #{listNo}
            </if>
            <if test="cabinetCode != null">
                AND cabinet_code = #{cabinetCode,jdbcType=VARCHAR}
            </if>
            <if test="glNo != null">
                AND gl_no = #{glNo}
            </if>
            <choose>
                <when test="qualityDate == null">
                    AND quality_date IS NULL
                </when>
                <otherwise>
                    AND quality_date = #{qualityDate}
                </otherwise>
            </choose>
        </where>
    </select>


    <select id="selectOneNew" parameterType="net.summerfarm.model.domain.StockTaskItemDetail" resultType="net.summerfarm.model.domain.StockTaskItemDetail">
        SELECT id,stock_task_item_id stockTaskItemId,sku,list_no listNo,quality_date qualityDate,gl_no glNo,quantity,remark,should_quantity shouldQuantity,out_store_quantity outStoreQuantity,system_generation systemGeneration
        FROM stock_task_item_detail
        <where>
            <if test="stockTaskItemId != null">
                AND stock_task_item_id = #{stockTaskItemId}
            </if>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
            <if test="listNo != null">
                AND list_no = #{listNo}
            </if>
            <if test="glNo != null">
                AND gl_no = #{glNo}
            </if>
            <choose>
                <when test="qualityDate == null">
                    AND quality_date IS NULL
                </when>
                <otherwise>
                    AND quality_date = #{qualityDate}
                </otherwise>
            </choose>
        </where>
    </select>

    <update id="update" parameterType="net.summerfarm.model.domain.StockTaskItemDetail">
        UPDATE stock_task_item_detail
        <set>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="outStoreQuantity != null">
                out_store_quantity = #{outStoreQuantity},
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByItemId" resultMap="VOMap">
        SELECT
            id,
            stock_task_item_id,
            sku,
            list_no,
            cabinet_code,
            system_generation,
            remark,
            production_date,
            quality_date,
            sum(quantity)           quantity,
            sum(should_quantity)    should_quantity,
            sum(out_store_quantity) out_store_quantity
        FROM stock_task_item_detail
        WHERE stock_task_item_id = #{stockTaskItemId,jdbcType=INTEGER}
        group by list_no,quality_date, cabinet_code
    </select>

    <insert id="merge" parameterType="net.summerfarm.model.domain.StockTaskItemDetail">
        INSERT INTO stock_task_item_detail(stock_task_item_id,sku,list_no,quality_date,quantity,remark,production_date)
        VALUES (#{stockTaskItemId} ,#{sku} ,#{listNo} ,#{qualityDate} ,#{quantity} ,#{remark} ,#{productionDate})
        ON DUPLICATE KEY UPDATE quantity = quantity + #{quantity} ,remark = #{remark}
    </insert>
    <select id="selectList" resultMap="VOMap">
         SELECT stid.*
        FROM stock_task_item_detail stid
        WHERE stock_task_item_id = #{stockTaskItemId,jdbcType=INTEGER}
        and stid.sku = #{sku}
    </select>

    <select id="hasUnFinishDamageTask" resultType="boolean">
        select count(1) > 0
        from stock_task_item_detail stid
                 left join stock_task_item sti on stid.stock_task_item_id = sti.id
                 left join stock_task st on sti.stock_task_id = st.id
        where st.type = 53
          and st.state = 0
          and st.area_no = #{storeNo}
          and sti.sku = #{sku}
          and stid.list_no = #{batch}
          and stid.quality_date = #{qualityDate}
    </select>

    <select id="selectByTaskNoAndType" resultMap="VOMap">
     select wdsi.sku,wdsi.quality_date,wdsi.quantity,wdsi.list_no
     from stock_task_item wdst
     left join stock_task st on wdst.stock_task_id= st.id
     inner join stock_task_item_detail wdsi on wdst.id = wdsi.stock_task_item_id
     where st.task_no=#{taskNo} and st.type=#{type}
  </select>

    <select id="selectByTaskIdsAndTypeAndSku" resultMap="VOMap">
        select wdsi.sku,wdsi.quality_date,wdsi.quantity,wdsi.list_no
        from stock_task_item wdst
                 left join stock_task st on wdst.stock_task_id= st.id
                 inner join stock_task_item_detail wdsi on wdst.id = wdsi.stock_task_item_id
        where
              st.task_no in
              <foreach collection="taskNos" open="(" close=")" separator="," item="item">
                  #{item}
              </foreach>
               and st.type=#{type} and wdsi.sku=#{sku}
    </select>
</mapper>