<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.TmsTaskOperateRecordMapper">

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tms_task_operate_record (addtime,operate_type,operator,operator_id,remark,delivery_path_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.addTime},#{item.operateType},#{item.operator},#{item.operatorId},#{item.remark},#{item.deliveryPathId})
        </foreach>
    </insert>

    <select id="selectByDeliveryPathId" resultType="net.summerfarm.model.domain.TmsTaskOperateRecord">
        select addtime addTime,operator,operate_type operateType,remark
        from tms_task_operate_record
        where delivery_path_id = #{deliveryPathId}
        order by id
    </select>

</mapper>