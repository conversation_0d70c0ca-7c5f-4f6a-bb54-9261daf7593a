<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.InvoiceEmailOverrideMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.InvoiceEmailOverride">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="invoice_config_id" jdbcType="BIGINT" property="invoiceConfigId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, invoice_config_id, m_id, email, creator, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from invoice_email_override
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByConfigIdAndMId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from invoice_email_override
    where invoice_config_id = #{invoiceConfigId,jdbcType=BIGINT}
    and m_id = #{mId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from invoice_email_override
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.InvoiceEmailOverride" useGeneratedKeys="true" keyProperty="id">
    insert into invoice_email_override (invoice_config_id, m_id, email,
    creator)
    values (#{invoiceConfigId,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT}, #{email,jdbcType=VARCHAR},
    #{creator,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.InvoiceEmailOverride" useGeneratedKeys="true" keyProperty="id">
    insert into invoice_email_override
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceConfigId != null">
        invoice_config_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceConfigId != null">
        #{invoiceConfigId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.InvoiceEmailOverride">
    update invoice_email_override
    <set>
      <if test="invoiceConfigId != null">
        invoice_config_id = #{invoiceConfigId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.InvoiceEmailOverride">
    update invoice_email_override
    set invoice_config_id = #{invoiceConfigId,jdbcType=BIGINT},
      m_id = #{mId,jdbcType=BIGINT},
      email = #{email,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert">
    insert into invoice_email_override (invoice_config_id, m_id, email, creator, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.invoiceConfigId,jdbcType=BIGINT}, #{item.mId,jdbcType=BIGINT}, #{item.email,jdbcType=VARCHAR}, 
       #{item.creator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

</mapper>
