<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockStorageItemDetailMapper">


    <insert id="insertBatch">
        insert into stock_storage_item_detail (stock_storage_item_id, actual_in_quantity , purchase_no, quality_date,production_date,gl_no , create_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{stockStorageItemId}, #{item.actualOutQuantity}, #{item.purchaseNo}, #{item.qualityDate}, #{item.productionDate}, #{item.glNo}, now())
        </foreach>
    </insert>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into stock_storage_item_detail (stock_storage_item_id, actual_in_quantity , purchase_no, quality_date,production_date,gl_no , create_time)
        VALUES
        (#{stockStorageItemId}, #{actualInQuantity}, #{purchaseNo}, #{qualityDate}, #{productionDate}, #{glNo}, now())
    </insert>

    <update id="updateByPrimaryKey">
        update stock_storage_item_detail
        <set>
            <if test="actualInQuantity != null">
                actual_in_quantity = #{actualInQuantity,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="select" parameterType="java.lang.Integer" resultType="net.summerfarm.model.domain.StockAllocationItemDetail">
    SELECT
        s.id,
        s.purchase_no             purchaseNo,
        sum(s.actual_in_quantity) actualOutQuantity,
        s.quality_date            qualityDate,
        s.production_date         productionDate,
        s.stock_storage_item_id              stockAllocationItemId
    FROM  stock_storage_item_detail s
    WHERE s.stock_storage_item_id = #{stockStorageItemId}
    group by s.purchase_no, s.quality_date, s.production_date

  </select>

    <select id="selectOne" parameterType="net.summerfarm.model.domain.StockStorageItemDetail" resultType="net.summerfarm.model.domain.StockStorageItemDetail">
        SELECT
        id, stock_storage_item_id stockStorageItemId, purchase_no purchaseNo, actual_in_quantity actualInQuantity, quality_date qualityDate,
        production_date productionDate,gl_no glNo
        FROM stock_storage_item_detail
        <where>
            <if test="purchaseNo != null">
                and purchase_no = #{purchaseNo}
            </if>
            <if test="stockStorageItemId != null">
                and stock_storage_item_id = #{stockStorageItemId}
            </if>
            <if test="glNo != null">
                and gl_no = #{glNo}
            </if>
            <if test="qualityDate != null">
                AND quality_date = #{qualityDate}
            </if>
        </where>
    </select>

    <select id="selectByItemId" resultType="net.summerfarm.model.domain.StockShipmentItemDetail">
    SELECT s.purchase_no purchaseNo,s.quantity , s.actual_out_quantity actualOutQuantity,s.quality_date qualityDate, s.production_date productionDate,s.gl_no glNo
    FROM  stock_shipment_item_detail s
    LEFT  JOIN stock_shipment_item t on t.id=s.stock_shipment_item_id
    WHERE t.id = #{stockShipmentItemId}
    </select>
    <select id="selectProcess" resultType="net.summerfarm.model.vo.StockTaskProcessDetailVO">
    SELECT s.purchase_no purchaseNo, s.actual_out_quantity quantity,s.quality_date qualityDate, s.production_date productionDate,
    s.gl_no glNo ,i.ext_type extType,i.pack,i.weight,p.pd_name pdName,t.sku
    FROM  stock_storage_item t
    LEFT  JOIN stock_storage_item_detail s on t.id=s.stock_storage_item_id
    LEFT JOIN inventory i ON t.sku=i.sku
    LEFT  JOIN products p ON i.pd_id=p.pd_id
    WHERE t.id = #{stockStorageItemId}
    </select>

    <select id="selectByTaskId" resultType="net.summerfarm.model.vo.StockTaskProcessDetailVO">
    SELECT s.purchase_no purchaseNo, s.actual_in_quantity quantity,s.quality_date qualityDate, s.production_date productionDate,
    s.gl_no glNo ,i.ext_type extType,i.pack,i.weight,p.pd_name pdName,t.sku,s.purchase_no listNo
    FROM  stock_storage_item t
    LEFT  JOIN stock_storage_item_detail s on t.id=s.stock_storage_item_id
    LEFT JOIN inventory i ON t.sku=i.sku
    LEFT  JOIN products p ON i.pd_id=p.pd_id
    WHERE t.stock_task_id = #{id}
    </select>

    <select id="selectSumQuantity" parameterType="net.summerfarm.model.domain.StockStorageItemDetail" resultType="net.summerfarm.model.domain.StockStorageItemDetail">
        SELECT
        id, stock_allocation_item_id, purchase_no, sum(actual_out_quantity) actual_out_quantity,sum(in_quantity)  in_quantity,
        reject_quantity, reject_reason, quality_date, updatetime, addtime,production_date,gl_no
        FROM stock_allocation_item_detail
        WHERE stock_allocation_item_id = #{stockStorageItemId}
        AND purchase_no = #{purchaseNo}
        <if test="glNo != null">
            and gl_no = #{glNo}
        </if>
        <choose>
            <when test="qualityDate == null">
                AND quality_date IS  NULL
            </when>
            <otherwise>
                AND quality_date = #{qualityDate}
            </otherwise>
        </choose>
    </select>
    <select id="selectMergeDetail" resultType="net.summerfarm.model.domain.StockAllocationItemDetail">
    SELECT s.id,s.purchase_no purchaseNo, sum(s.actual_in_quantity) actualOutQuantity,s.quality_date qualityDate, s.production_date productionDate,t.id stockAllocationItemId
    FROM  stock_storage_item_detail s
    LEFT  JOIN stock_storage_item t on t.id = s.stock_storage_item_id
    WHERE t.id = #{stockStorageItemId}
    group by s.purchase_no,s.quality_date
    </select>
    <select id="selectByListNo" resultType="net.summerfarm.model.domain.StockAllocationItemDetail">
        select ssid.purchase_no purchaseNo,ssid.production_date productionDate
        from stock_task_storage sts
        left join stock_storage_item ssi on sts.id =ssi.stock_task_storage_id
        left join stock_storage_item_detail ssid on ssid.stock_storage_item_id= ssi.id
        where ssid.purchase_no=#{batch} and ssi.sku=#{sku}
    </select>
</mapper>