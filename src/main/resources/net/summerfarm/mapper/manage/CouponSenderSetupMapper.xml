<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CouponSenderSetupMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CouponSenderSetup">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="sender_type" jdbcType="INTEGER" property="senderType"/>
    <result column="status" jdbcType="INTEGER" property="status"/>
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, start_time, end_time, `type`, sender_type, status, creator, create_time, updater, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from coupon_sender_setup
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByCondition" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from coupon_sender_setup
      <where>
        <if test="id!=null">
          and id = #{id}
        </if>
        <if test="status != null">
          <choose>
            <when test="status == 2">
              and status in (2, 3)
            </when>
            <otherwise>
              and status = #{status}
            </otherwise>
          </choose>
        </if>
        <if test="senderType != null">
          and sender_type = #{senderType}
        </if>
        <if test="name!=null">
          and name like CONCAT('%',#{name},'%')
        </if>
      </where>
      order by create_time desc
  </select>
  <select id="selectByName" resultType="java.lang.Integer">
    select count(1) from coupon_sender_setup where
    name = #{name}
    <if test="id!=null">
      and id &lt;&gt; #{id}
    </if>
  </select>
  <select id="selectCouponIdExist" resultType="java.lang.Integer">
    select count(*) from coupon_sender_setup css
    left join  coupon_sender_relation csr on css.id = csr.coupon_sender_id
    where now() &lt; css.end_time
    and csr.coupon_id = #{couponId}
    <if test="id!=null">
      and css.id &lt;&gt; #{id}
    </if>

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from coupon_sender_setup
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CouponSenderSetup" useGeneratedKeys="true">
    insert into coupon_sender_setup (`name`, start_time, end_time, 
      `type`, creator, create_time, 
      updater, update_time, sender_type, status)
    values (#{name,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=BOOLEAN}, #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},#{senderType,jdbcType=INTEGER},#{status,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CouponSenderSetup" useGeneratedKeys="true">
    insert into coupon_sender_setup
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="senderType != null">
        sender_type,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="senderType != null">
        #{senderType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CouponSenderSetup">
    update coupon_sender_setup
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="senderType != null">
        sender_type = #{senderType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CouponSenderSetup">
    update coupon_sender_setup
    set `name` = #{name,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=BOOLEAN},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      sender_type = #{senderType,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="countByCouponIds" resultType="net.summerfarm.model.bo.CouponSenderSetupCouponCountBO">
    select coupon_id couponId, css.id senderSetupId
    from coupon_sender_setup css left join coupon_sender_relation csr on css.id = csr.coupon_sender_id
    where coupon_id in <foreach collection="couponIds" item="couponId" open="(" close=")" separator=",">
                           #{couponId}
                       </foreach> and status in (0, 1)
  </select>

  <select id="selectByTypeAndStatusAndIdNotIn" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from coupon_sender_setup
    where `type`=#{type} and sender_type = #{senderType}
            and `status` in
              <foreach item="status" index="index" collection="statusList" open="(" separator="," close=")">
                #{status,jdbcType=INTEGER}
              </foreach>
            <if test="ids != null and ids.size() != 0">
              and id not in
              <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
                #{id,jdbcType=INTEGER}
              </foreach>
            </if>
  </select>

  <update id="updateStatusById">
    update coupon_sender_setup
    set status = #{status}
    where id in <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
  </update>

  <select id="getEffectiveList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from coupon_sender_setup
    where `type`= #{type} and status in (0, 1)
    and end_time > now()
  </select>
</mapper>