<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CirclePeopleRuleMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CirclePeopleRule">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="BOOLEAN" property="type" />
    <result column="file_id" jdbcType="VARCHAR" property="fileId" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `name`, `type`, file_id, creator, create_time, updater, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from circle_people_rule
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="selectByName" resultType="net.summerfarm.model.vo.CirclePeopleVo">
      select cip.id,cip.name,cip.type ruleType,cip.file_id fileId,error_report errorReport,
            cip.create_time createTime,cip.update_time updateTime
      from circle_people_rule cip
      <where>
        <if test="name!=null and name !=''">
          cip.name like CONCAT('%', #{name}, '%')
        </if>
      </where>
      order by cip.create_time desc


    </select>
  <select id="selectByNameId" resultType="java.lang.String">
    select concat(name,'/',id) from circle_people_rule
    <where>
        <if test="nameOrId!=null and nameOrId!=''">
          id like CONCAT('%', #{nameOrId}, '%') or name like CONCAT('%', #{nameOrId}, '%')

        </if>
    </where>


  </select>
  <select id="selectActByType" resultType="java.lang.String">

      select concat('秒杀活动',cpr.type_id) from circle_people_relation cpr
      left join panic_buy pb on cpr.type_id = pb.id
      where pb.end_time &gt; now()  and cpr.rule_id = #{id}
      union all
    select concat('严选活动',cpr.type_id) from circle_people_relation cpr
      left join strict_selection ss on cpr.type_id = ss.id
      where ss.end_time &gt; now()  and cpr.rule_id = #{id}
    union all
      select distinct concat('资源位',b.name) from circle_people_relation cpr
      left join banner b on cpr.type_id = b.id
      where b.end_time &gt; now()  and cpr.rule_id = #{id}
    union all
      select distinct concat('发放设置',css.id) from circle_people_relation cpr
      left join coupon_sender_setup css on cpr.type_id = css.id
      where css.end_time > now()  and cpr.rule_id = #{id}

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from circle_people_rule
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CirclePeopleRule" useGeneratedKeys="true">
    insert into circle_people_rule (`name`, `type`, file_id, 
      creator, create_time, updater, 
      update_time)
    values (#{name,jdbcType=VARCHAR}, #{type,jdbcType=BOOLEAN}, #{fileId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CirclePeopleRule" useGeneratedKeys="true">
    insert into circle_people_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="fileId != null">
        file_id,
      </if>
        error_report,
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=BOOLEAN},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=VARCHAR},
      </if>

        #{errorReport,jdbcType=VARCHAR},

      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CirclePeopleRule">
    update circle_people_rule
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=BOOLEAN},
      </if>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=VARCHAR},
      </if>

          error_report = #{errorReport,jdbcType=VARCHAR},

      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CirclePeopleRule">
    update circle_people_rule
    set `name` = #{name,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=BOOLEAN},
      file_id = #{fileId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectActMid" resultType="java.lang.Integer">
    SELECT DISTINCT cpr.type_id FROM circle_people_rule cpri
    LEFT JOIN circle_people_relation cpr ON cpri.id = cpr.rule_id
    LEFT JOIN circle_people_rule_admin cpra ON cpri.id = cpra.rule_id
    WHERE cpr.type = 0
    <if test="mId != null">
      AND cpra.m_id = #{mId}
    </if>
    AND cpr.type_id IN
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>

  <select id="listByIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from circle_people_rule
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")" index="index">
      #{id}
    </foreach>
  </select>
</mapper>