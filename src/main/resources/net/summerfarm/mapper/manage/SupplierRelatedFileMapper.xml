<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.SupplierRelatedFileMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.input.SupplierFileReq">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="file_type" jdbcType="INTEGER" property="fileType" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, supplier_id, file_type, file_url, start_date, end_date, creator, create_time, 
    updater, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from purchase_supplier_related_file
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="listBySupplierId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />, IF(end_date is null, 1 ,IF(end_date >= curdate(), 1, 0)) valid from purchase_supplier_related_file where supplier_id = #{supplierId}
  </select>
    <select id="checkValidFile" resultType="java.lang.Integer">
      select count(1) from purchase_supplier_related_file where supplier_id = #{supplierId} and valid = 0
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_supplier_related_file
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.SupplierRelatedFile">
    insert into purchase_supplier_related_file (id, supplier_id, file_type,
                                                file_url, start_date, end_date,
                                                creator, create_time, updater,
                                                update_time)
    values (#{id,jdbcType=BIGINT}, #{supplierId,jdbcType=INTEGER}, #{fileType,jdbcType=INTEGER},
            #{fileUrl,jdbcType=VARCHAR}, #{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE},
            #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR},
            #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.SupplierRelatedFile">
    insert into purchase_supplier_related_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DATE},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.SupplierRelatedFile">
    update purchase_supplier_related_file
    <set>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=DATE},
      </if>
        end_date = #{endDate,jdbcType=DATE},
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.SupplierRelatedFile">
    update purchase_supplier_related_file
    set supplier_id = #{supplierId,jdbcType=INTEGER},
        file_type = #{fileType,jdbcType=INTEGER},
        file_url = #{fileUrl,jdbcType=VARCHAR},
        start_date = #{startDate,jdbcType=DATE},
        end_date = #{endDate,jdbcType=DATE},
        updater = #{updater,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateBySupplierId">

  </update>

  <delete id="removeBySupplierId">
    delete from purchase_supplier_related_file where supplier_id = #{supplierId}
  </delete>

</mapper>