<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FenceDeliveryLogMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FenceDeliveryLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fence_id" jdbcType="TIMESTAMP" property="fenceId" />
    <result column="delivery_frequent" jdbcType="VARCHAR" property="deliveryFrequent" />
    <result column="next_delivery_date" jdbcType="TIMESTAMP" property="nextDeliveryDate" />
    <result column="effect_flag" jdbcType="TINYINT" property="effectFlag" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fence_id, delivery_frequent, next_delivery_date, effect_flag, delete_flag, creator, 
    create_time, updater, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fence_delivery_log
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByEffectFlagId" resultType="net.summerfarm.model.domain.FenceDeliveryLog">
      select  <include refid="Base_Column_List" />
      from fence_delivery_log where
      effect_flag = #{effectFlag} and delete_flag = 0 and fence_id = #{fenceId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from fence_delivery_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <update id="deleteByEffectFlag">
      update fence_delivery_log set delete_flag = 1 where fence_id = #{id} and effect_flag =#{effectFlag}
    </update>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FenceDeliveryLog" useGeneratedKeys="true">
    insert into fence_delivery_log (fence_id, delivery_frequent, next_delivery_date, 
      effect_flag, delete_flag, creator, 
      create_time, updater, update_time
      )
    values (#{fenceId,jdbcType=TIMESTAMP}, #{deliveryFrequent,jdbcType=VARCHAR}, #{nextDeliveryDate,jdbcType=TIMESTAMP}, 
      #{effectFlag,jdbcType=TINYINT}, #{deleteFlag,jdbcType=TINYINT}, #{creator,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FenceDeliveryLog" useGeneratedKeys="true">
    insert into fence_delivery_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fenceId != null">
        fence_id,
      </if>
      <if test="deliveryFrequent != null">
        delivery_frequent,
      </if>
      <if test="nextDeliveryDate != null">
        next_delivery_date,
      </if>
      <if test="effectFlag != null">
        effect_flag,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="frequentMethod != null">
        frequent_method,
      </if>
      <if test="deliveryFrequentInterval != null">
        delivery_frequent_interval,
      </if>
      <if test="beginCalculateDate != null">
        begin_calculate_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fenceId != null">
        #{fenceId,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryFrequent != null">
        #{deliveryFrequent,jdbcType=VARCHAR},
      </if>
      <if test="nextDeliveryDate != null">
        #{nextDeliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="effectFlag != null">
        #{effectFlag,jdbcType=TINYINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="frequentMethod != null">
        #{frequentMethod},
      </if>
      <if test="deliveryFrequentInterval != null">
        #{deliveryFrequentInterval},
      </if>
      <if test="beginCalculateDate != null">
        #{beginCalculateDate},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FenceDeliveryLog">
    update fence_delivery_log
    <set>
      <if test="fenceId != null">
        fence_id = #{fenceId,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryFrequent != null">
        delivery_frequent = #{deliveryFrequent,jdbcType=VARCHAR},
      </if>
      <if test="nextDeliveryDate != null">
        next_delivery_date = #{nextDeliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="effectFlag != null">
        effect_flag = #{effectFlag,jdbcType=TINYINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FenceDeliveryLog">
    update fence_delivery_log
    set fence_id = #{fenceId,jdbcType=TIMESTAMP},
      delivery_frequent = #{deliveryFrequent,jdbcType=VARCHAR},
      next_delivery_date = #{nextDeliveryDate,jdbcType=TIMESTAMP},
      effect_flag = #{effectFlag,jdbcType=TINYINT},
      delete_flag = #{deleteFlag,jdbcType=TINYINT},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>