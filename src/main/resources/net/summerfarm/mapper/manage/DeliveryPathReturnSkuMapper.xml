<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.DeliveryPathReturnSkuMapper">
    <select id="selectByDeliveryPath" resultType="net.summerfarm.model.domain.DeliveryPathReturnSku">
        select id, delivery_path_id, sku, quantity returnCnt, remark
        from delivery_path_return_sku
        where delivery_path_id = #{deliveryPathId}
          and sku = #{sku} order by id desc limit 1
    </select>
</mapper>