<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DatabaseHandleRecordDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DatabaseHandleRecordDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="VARCHAR" property="recordId" />
    <result column="handle_table" jdbcType="VARCHAR" property="handleTable" />
    <result column="handle_columns" jdbcType="VARCHAR" property="handleColumns" />
    <result column="handle_pk" jdbcType="VARCHAR" property="handlePk" />
    <result column="handle_id" jdbcType="INTEGER" property="handleId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="old_value" jdbcType="VARCHAR" property="oldValue" />
    <result column="new_value" jdbcType="VARCHAR" property="newValue" />
  </resultMap>
  <sql id="Base_Column_List">
    id, record_id, handle_table, handle_columns, handle_pk, handle_id, `type`, old_value, new_value
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from database_handle_record_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from database_handle_record_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DatabaseHandleRecordDetail" useGeneratedKeys="true">
    insert into database_handle_record_detail (record_id, handle_table, handle_columns, handle_pk,
      handle_id, `type`, old_value, 
      new_value)
    values (#{recordId,jdbcType=INTEGER}, #{handleTable,jdbcType=VARCHAR}, #{handleColumns,jdbcType=VARCHAR}, #{handlePk,jdbcType=VARCHAR},
            #{handleId,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{oldValue,jdbcType=VARCHAR},
      #{newValue,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DatabaseHandleRecordDetail" useGeneratedKeys="true">
    insert into database_handle_record_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordId != null">
        record_id,
      </if>
      <if test="handleTable != null">
        handle_table,
      </if>
      <if test="handleColumns != null">
        handle_columns,
      </if>
      <if test="handlePk != null">
        handle_pk,
      </if>
      <if test="handleId != null">
        handle_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="oldValue != null">
        old_value,
      </if>
      <if test="newValue != null">
        new_value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordId != null">
        #{recordId,jdbcType=INTEGER},
      </if>
      <if test="handleTable != null">
        #{handleTable,jdbcType=VARCHAR},
      </if>
      <if test="handleColumns != null">
        #{handleColumns,jdbcType=VARCHAR},
      </if>
      <if test="handlePk != null">
        #{handlePk,jdbcType=VARCHAR},
      </if>
      <if test="handleId != null">
        #{handleId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="oldValue != null">
        #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null">
        #{newValue,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.DatabaseHandleRecordDetail">
    update database_handle_record_detail
    <set>
      <if test="recordId != null">
        record_id = #{recordId,jdbcType=INTEGER},
      </if>
      <if test="handleTable != null">
        handle_table = #{handleTable,jdbcType=VARCHAR},
      </if>
      <if test="handleColumns != null">
        handle_columns = #{handleColumns,jdbcType=VARCHAR},
      </if>
      <if test="handlePk != null">
        handle_pk = #{handlePk,jdbcType=VARCHAR},
      </if>
      <if test="handleId != null">
        handle_id = #{handleId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="oldValue != null">
        old_value = #{oldValue,jdbcType=VARCHAR},
      </if>
      <if test="newValue != null">
        new_value = #{newValue,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DatabaseHandleRecordDetail">
    update database_handle_record_detail
    set record_id = #{recordId,jdbcType=INTEGER},
      handle_table = #{handleTable,jdbcType=VARCHAR},
      handle_columns = #{handleColumns,jdbcType=VARCHAR},
      handle_pk = #{handlePk,jdbcType=VARCHAR},
      handle_id = #{handleId,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      old_value = #{oldValue,jdbcType=VARCHAR},
      new_value = #{newValue,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertBatch" parameterType="net.summerfarm.model.domain.DatabaseHandleRecordDetail">
    insert into
        database_handle_record_detail (record_id, handle_table, handle_columns, handle_pk, handle_id, `type`, old_value, new_value)
    values
    <foreach collection="list" item="item" separator=",">
      (
       #{item.recordId,jdbcType=INTEGER}, #{item.handleTable,jdbcType=VARCHAR}, #{item.handleColumns,jdbcType=VARCHAR},#{item.handlePk,jdbcType=VARCHAR},
       #{item.handleId,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER}, #{item.oldValue,jdbcType=VARCHAR}, #{item.newValue,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <select id="selectPageList" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from  database_handle_record_detail
    where record_id = #{restoreId}
    order by handle_table, handle_id
    limit #{pageStart}, #{pageSize}
  </select>
</mapper>