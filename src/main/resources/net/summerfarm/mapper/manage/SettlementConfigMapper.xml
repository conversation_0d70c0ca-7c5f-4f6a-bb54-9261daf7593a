<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.SettlementConfigMapper">

    <resultMap type="net.summerfarm.model.domain.SettlementConfig" id="BaseResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="auditor" column="auditor" jdbcType="VARCHAR"/>
        <result property="approver" column="approver" jdbcType="VARCHAR"/>
        <result property="threshold" column="threshold" jdbcType="BIGINT"/>
        <result property="payer" column="payer" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="base_filed">id,type,auditor,approver,threshold,payer</sql>

    <select id="selectConfigList" resultMap="BaseResultMap">
        select <include refid="base_filed"/> from settlement_config;
    </select>

    <select id="selectByType" resultType="net.summerfarm.model.domain.SettlementConfig">
        select <include refid="base_filed"/> from settlement_config where type=#{pdType}
    </select>

    <select id="selectByConfig" resultType="net.summerfarm.model.domain.SettlementConfig">
        select <include refid="base_filed"/> from settlement_config where `type` is null
    </select>

    <select id="selectBySettlementId" resultType="net.summerfarm.model.domain.SettlementConfig">
        select sc.id,sc.type,sc.auditor,sc.approver,sc.threshold,sc.payer from settlement_config sc left join settlement s on sc.type=s.pd_type where s.id=#{settlementId}
    </select>


    <insert id="insertBatch"  parameterType="net.summerfarm.model.domain.SettlementConfig" useGeneratedKeys="true" keyProperty="id">
        insert into settlement_config (type, auditor, approver, threshold, payer,create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.type}, #{item.auditor}, #{item.approver}, #{item.threshold}, #{item.payer},now())
        </foreach>
    </insert>

    <update id="updateByType" keyProperty="id" useGeneratedKeys="true">
        update settlement_config
        <set>
            <if test="type!=null">type=#{type,jdbcType=INTEGER},</if>
            <if test="auditor!=null">auditor=#{auditor,jdbcType=VARCHAR},</if>
            <if test="approver!=null">approver=#{approver,jdbcType=VARCHAR},</if>
            threshold=#{threshold,jdbcType=BIGINT},
            <if test="payer!=null">payer=#{payer,jdbcType=VARCHAR},</if>
            update_time=now()
        </set>
        where type = #{type,jdbcType=INTEGER}
    </update>

    <update id="updateById" keyProperty="id" useGeneratedKeys="true">
        update settlement_config
        <set>
            <if test="payer!=null">payer=#{payer,jdbcType=VARCHAR},</if>
            update_time=now()
        </set>
        where id = #{id}
    </update>

    <select id="selectPurchaseNoInProcess" resultType="java.lang.String">
        select   d.`purchase_no`
        FROM settlement se
        LEFT JOIN `settlement_detail` d on d.`settlement_id` = se.`id`
        where  se.status!=4 and se.supplier_id=#{supplierId} and d.`purchase_no` in
        <foreach collection="purchaseSets" item="purchaseNo" open="(" close=")" separator=",">
            #{purchaseNo}
        </foreach>
    </select>
</mapper>

