<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockAllocationQuickRecordMapper">

    <update id="update" parameterType="net.summerfarm.model.domain.StockAllocationQuickRecord">
        update stock_allocation_quick_record
        <set >
            <if test="inQuantity != null" >
                in_quantity = #{inQuantity,jdbcType=INTEGER},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=INTEGER},
            </if>
            update_time= now()
        </set>
        where list_no = #{listNo} and sku = #{sku}
    </update>

    <insert id="insert" parameterType="net.summerfarm.model.domain.StockAllocationQuickRecord">
        insert into stock_allocation_quick_record
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="sku != null" >
                sku,
            </if>
            <if test="listNo != null" >
                list_no,
            </if>
            <if test="actualOutQuantity != null" >
                actual_out_quantity,
            </if>
            <if test="inQuantity != null" >
                in_quantity,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="outStore != null" >
                out_store,
            </if>
            <if test="inStore != null" >
                in_store,
            </if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="sku != null" >
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="listNo != null" >
                #{listNo,jdbcType=INTEGER},
            </if>
            <if test="actualOutQuantity != null" >
                #{actualOutQuantity},
            </if>
            <if test="inQuantity != null" >
                #{inQuantity},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="outStore != null" >
                #{outStore,jdbcType=INTEGER},
            </if>
            <if test="inStore != null" >
                #{inStore,jdbcType=INTEGER},
            </if>
            now()
        </trim>
    </insert>

    <select id="selectBySelective" resultType="net.summerfarm.model.domain.StockAllocationQuickRecord">
        select
        id,sku,list_no listNo,in_quantity inQuantity,actual_out_quantity actualOutQuantity
        from stock_allocation_quick_record
        where list_no = #{listNo} and sku = #{sku} and status in (0,1)
    </select>

    <select id="selectByState" resultType="net.summerfarm.model.domain.StockAllocationQuickRecord">
        select
        id,sku,list_no listNo,in_quantity inQuantity,actual_out_quantity actualOutQuantity,out_store outStore,in_store inStore
        from stock_allocation_quick_record
        where status in (0,1)
    </select>

    <select id="selectByAreaNo" resultType="net.summerfarm.model.domain.StockAllocationQuickRecord">
        select
        id,sku,list_no listNo,in_quantity inQuantity,actual_out_quantity actualOutQuantity
        from stock_allocation_quick_record
        where in_store = #{areaNo} and sku = #{sku} and status in (0,1)
    </select>

</mapper>