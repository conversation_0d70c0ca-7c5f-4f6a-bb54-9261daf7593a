<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.SupplierUpdateLogMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.SupplierUpdateLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchase_no" jdbcType="VARCHAR" property="purchaseNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="old_name" jdbcType="VARCHAR" property="oldName" />
    <result column="new_name" jdbcType="VARCHAR" property="newName" />
    <result column="old_supplier_id" jdbcType="INTEGER" property="oldSupplierId" />
    <result column="new_supplier_id" jdbcType="INTEGER" property="newSupplierId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    id, purchase_no, sku, pd_name, warehouse_name,weight, old_name, new_name, old_supplier_id, new_supplier_id,
    update_time, updater
  </sql>
  <select id="selectAll" parameterType="net.summerfarm.model.vo.PurchasesPlanVO" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from supplier_update_log
    <where>
      <if test=" purchaseTimeStart !=null and purchaseTimeEnd != null ">
        and update_time <![CDATA[>=]]> #{purchaseTimeStart} and update_time <![CDATA[<=]]> #{purchaseTimeEnd}
      </if>
    </where>
    ORDER BY update_time desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from supplier_update_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.SupplierUpdateLog">
    insert into supplier_update_log (purchase_no, sku, pd_name, warehouse_name,
      weight, old_name, new_name, 
      old_supplier_id, new_supplier_id, update_time, 
      updater)
    values (#{purchaseNo,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{pdName,jdbcType=VARCHAR}, #{warehouseName,jdbcType=VARCHAR},
      #{weight,jdbcType=VARCHAR}, #{oldName,jdbcType=VARCHAR}, #{newName,jdbcType=VARCHAR}, 
      #{oldSupplierId,jdbcType=INTEGER}, #{newSupplierId,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.SupplierUpdateLog" useGeneratedKeys="true">
    insert into supplier_update_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="purchaseNo != null">
        purchase_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="pdName != null">
        pd_name,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="oldName != null">
        old_name,
      </if>
      <if test="newName != null">
        new_name,
      </if>
      <if test="oldSupplierId != null">
        old_supplier_id,
      </if>
      <if test="newSupplierId != null">
        new_supplier_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="purchaseNo != null">
        #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=VARCHAR},
      </if>
      <if test="oldName != null">
        #{oldName,jdbcType=VARCHAR},
      </if>
      <if test="newName != null">
        #{newName,jdbcType=VARCHAR},
      </if>
      <if test="oldSupplierId != null">
        #{oldSupplierId,jdbcType=INTEGER},
      </if>
      <if test="newSupplierId != null">
        #{newSupplierId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.SupplierUpdateLog">
    update supplier_update_log
    <set>
      <if test="purchaseNo != null">
        purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null">
        pd_name = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=VARCHAR},
      </if>
      <if test="oldName != null">
        old_name = #{oldName,jdbcType=VARCHAR},
      </if>
      <if test="newName != null">
        new_name = #{newName,jdbcType=VARCHAR},
      </if>
      <if test="oldSupplierId != null">
        old_supplier_id = #{oldSupplierId,jdbcType=INTEGER},
      </if>
      <if test="newSupplierId != null">
        new_supplier_id = #{newSupplierId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.SupplierUpdateLog">
    update supplier_update_log
    set purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      pd_name = #{pdName,jdbcType=VARCHAR},
      weight = #{weight,jdbcType=VARCHAR},
      old_name = #{oldName,jdbcType=VARCHAR},
      new_name = #{newName,jdbcType=VARCHAR},
      old_supplier_id = #{oldSupplierId,jdbcType=INTEGER},
      new_supplier_id = #{newSupplierId,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>