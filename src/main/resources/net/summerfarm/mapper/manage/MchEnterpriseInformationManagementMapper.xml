<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MchEnterpriseInformationManagementMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MchEnterpriseInformationManagement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle" />
    <result column="tax_number" jdbcType="VARCHAR" property="taxNumber" />
    <result column="valid_status" jdbcType="TINYINT" property="validStatus" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="link_method" jdbcType="VARCHAR" property="linkMethod" />
    <result column="legal_person_name" jdbcType="VARCHAR" property="legalPersonName" />
    <result column="verification" jdbcType="INTEGER" property="verification" />
    <result column="business_license_address" jdbcType="VARCHAR" property="businessLicenseAddress" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, invoice_title, tax_number, valid_status, update_time, create_time, link_method, 
    legal_person_name, verification, business_license_address, creator, updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mch_enterprise_information_management
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mch_enterprise_information_management
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MchEnterpriseInformationManagement" useGeneratedKeys="true">
    insert into mch_enterprise_information_management (m_id, invoice_title, tax_number, 
      valid_status, update_time, create_time, 
      link_method, legal_person_name, verification, 
      business_license_address, creator, updater
      )
    values (#{mId,jdbcType=BIGINT}, #{invoiceTitle,jdbcType=VARCHAR}, #{taxNumber,jdbcType=VARCHAR}, 
      #{validStatus,jdbcType=TINYINT}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{linkMethod,jdbcType=VARCHAR}, #{legalPersonName,jdbcType=VARCHAR}, #{verification,jdbcType=INTEGER}, 
      #{businessLicenseAddress,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.MchEnterpriseInformationManagement" useGeneratedKeys="true">
    insert into mch_enterprise_information_management
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="invoiceTitle != null">
        invoice_title,
      </if>
      <if test="taxNumber != null">
        tax_number,
      </if>
      <if test="validStatus != null">
        valid_status,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="linkMethod != null">
        link_method,
      </if>
      <if test="legalPersonName != null">
        legal_person_name,
      </if>
      <if test="verification != null">
        verification,
      </if>
      <if test="businessLicenseAddress != null">
        business_license_address,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="invoiceTitle != null">
        #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="taxNumber != null">
        #{taxNumber,jdbcType=VARCHAR},
      </if>
      <if test="validStatus != null">
        #{validStatus,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="linkMethod != null">
        #{linkMethod,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonName != null">
        #{legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="verification != null">
        #{verification,jdbcType=INTEGER},
      </if>
      <if test="businessLicenseAddress != null">
        #{businessLicenseAddress,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.MchEnterpriseInformationManagement">
    update mch_enterprise_information_management
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="invoiceTitle != null">
        invoice_title = #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="taxNumber != null">
        tax_number = #{taxNumber,jdbcType=VARCHAR},
      </if>
      <if test="validStatus != null">
        valid_status = #{validStatus,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="linkMethod != null">
        link_method = #{linkMethod,jdbcType=VARCHAR},
      </if>
      <if test="legalPersonName != null">
        legal_person_name = #{legalPersonName,jdbcType=VARCHAR},
      </if>
      <if test="verification != null">
        verification = #{verification,jdbcType=INTEGER},
      </if>
      <if test="businessLicenseAddress != null">
        business_license_address = #{businessLicenseAddress,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.MchEnterpriseInformationManagement">
    update mch_enterprise_information_management
    set m_id = #{mId,jdbcType=BIGINT},
      invoice_title = #{invoiceTitle,jdbcType=VARCHAR},
      tax_number = #{taxNumber,jdbcType=VARCHAR},
      valid_status = #{validStatus,jdbcType=TINYINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      link_method = #{linkMethod,jdbcType=VARCHAR},
      legal_person_name = #{legalPersonName,jdbcType=VARCHAR},
      verification = #{verification,jdbcType=INTEGER},
      business_license_address = #{businessLicenseAddress,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>