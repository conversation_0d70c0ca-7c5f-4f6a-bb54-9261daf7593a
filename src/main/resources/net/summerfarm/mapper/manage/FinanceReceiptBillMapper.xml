<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceReceiptBillMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceReceiptBill">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="finance_receipt_id" jdbcType="BIGINT" property="financeReceiptId" />
    <result column="finance_order_id" jdbcType="BIGINT" property="financeOrderId" />
    <result column="receivable_amount" jdbcType="DECIMAL" property="receivableAmount" />
    <result column="receipt_amount" jdbcType="DECIMAL" property="receiptAmount" />
    <result column="other_amount" jdbcType="DECIMAL" property="otherAmount" />
    <result column="receipt_voucher" jdbcType="VARCHAR" property="receiptVoucher" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, finance_receipt_id, finance_order_id, receipt_amount, other_amount,
    receivable_amount, receipt_voucher, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_receipt_bill
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_receipt_bill
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.FinanceReceiptBill">
    insert into finance_receipt_bill (id, finance_receipt_id, finance_order_id,
        receivable_amount, receipt_amount, other_amount, receipt_voucher,
        create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{financeReceiptId,jdbcType=BIGINT}, #{financeOrderId,jdbcType=BIGINT},
            #{receivableAmount,jdbcType=DECIMAL}, #{receiptAmount,jdbcType=DECIMAL}, #{otherAmount,jdbcType=DECIMAL},
            #{receiptVoucher,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.FinanceReceiptBill">
    insert into finance_receipt_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="financeReceiptId != null">
        finance_receipt_id,
      </if>
      <if test="financeOrderId != null">
        finance_order_id,
      </if>
      <if test="receivableAmount != null">
        receivable_amount,
      </if>
      <if test="receiptAmount != null">
        receipt_amount,
      </if>
      <if test="otherAmount != null">
        other_amount,
      </if>
      <if test="receiptVoucher != null">
        receipt_voucher,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="financeReceiptId != null">
        #{financeReceiptId,jdbcType=BIGINT},
      </if>
      <if test="financeOrderId != null">
        #{financeOrderId,jdbcType=BIGINT},
      </if>
      <if test="receivableAmount != null">
        #{receivableAmount,jdbcType=DECIMAL},
      </if>
      <if test="receiptAmount != null">
        #{receiptAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="receiptVoucher != null">
        #{receiptVoucher,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceReceiptBill">
    update finance_receipt_bill
    <set>
      <if test="financeReceiptId != null">
        finance_receipt_id = #{financeReceiptId,jdbcType=BIGINT},
      </if>
      <if test="financeOrderId != null">
        finance_order_id = #{financeOrderId,jdbcType=BIGINT},
      </if>
      <if test="receivableAmount != null">
        receipt_amount = #{receivableAmount,jdbcType=DECIMAL},
      </if>
      <if test="receiptAmount != null">
        receivable_amount = #{receiptAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        other_amount = #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="receiptVoucher != null">
        receipt_voucher = #{receiptVoucher,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceReceiptBill">
    update finance_receipt_bill
    set finance_receipt_id = #{financeReceiptId,jdbcType=BIGINT},
      finance_order_id = #{financeOrderId,jdbcType=BIGINT},
      receivable_amount = #{receivableAmount,jdbcType=DECIMAL},
      receipt_amount = #{receiptAmount,jdbcType=DECIMAL},
      other_amount = #{otherAmount,jdbcType=DECIMAL},
      receipt_voucher = #{receiptVoucher,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByReceiptIdList" resultType="net.summerfarm.model.vo.FinanceReceiptBillVO">
    select
      frb.id billId,
      frb.finance_receipt_id financeReceiptId,
      frb.finance_order_id financeOrderId,
      fapo.bill_number billNumber,
      fapo.bill_cycle billCycle,
      fapo.update_time billUpdateTime,
      frb.receivable_amount receivableAmount,
      frb.receipt_amount receiptAmount,
      frb.other_amount otherAmount,
      IFNULL(frb.receipt_amount, 0) + IFNULL(frb.other_amount, 0) writtenOffAmount,
      frb.receipt_voucher receiptVoucher
    from finance_receipt_bill frb
    left join finance_accounting_period_order fapo on fapo.id = frb.finance_order_id
    where frb.finance_receipt_id = #{financeReceiptId}
  </select>

  <select id="countReceiptBillAmount" resultType="net.summerfarm.model.vo.FinanceReceiptBillVO">
    select sum(IFNULL(frb.receipt_amount, 0) + IFNULL(frb.other_amount, 0)) writtenOffAmount,
    sum(IFNULL(frb.other_amount, 0)) otherAmount
    from finance_receipt_bill frb
    left join finance_receipt fr on fr.id = frb.finance_receipt_id
    left join finance_accounting_period_order fapo on fapo.id = frb.finance_order_id
    <where>
      <if test="financeReceiptId !=null ">
        and frb.finance_receipt_id = #{financeReceiptId}
      </if>
      <if test="financeOrderId !=null ">
        and frb.finance_order_id = #{financeOrderId}
      </if>
      <if test="writeOffStatus !=null ">
        and fr.write_off_status = #{writeOffStatus}
      </if>
    </where>
  </select>

  <select id="selectByOrderIdList" resultType="net.summerfarm.model.vo.FinanceReceiptBillVO">
    select
      frb.finance_receipt_id id,
      fr.create_time createTime,
      fr.creator,
      fr.update_time updateTime,
      fr.updater,
      IFNULL(frb.receipt_amount, 0) + IFNULL(frb.other_amount, 0) writtenOffAmount,
      frb.receivable_amount receivableAmount,
      fr.finance_bank_flowing_water_id financeBankFlowingWaterId,
      fr.id financeReceiptId
    from finance_receipt_bill frb
           left join finance_receipt fr on fr.id = frb.finance_receipt_id
    where frb.finance_order_id = #{financeOrderId}
      and write_off_status = #{writeOffStatus}
      order by fr.id desc
  </select>

</mapper>