<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FinanceInvoicePartnerMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceInvoicePartner">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="supplier_type" jdbcType="INTEGER" property="supplierType" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_admin_id" jdbcType="INTEGER" property="creatorAdminId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, supplier_id, supplier_type, creator, creator_admin_id, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_invoice_partner
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectSupplierId" parameterType="integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_invoice_partner
    where supplier_id = #{supplierId} and supplier_type = 0
  </select>
  <select id="select" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from finance_invoice_partner
  </select>
  <select id="selectBySupplierIdAndExpenseType" resultType="net.summerfarm.model.domain.FinanceInvoicePartner">
    select
    id , supplier_id supplierId
    from finance_invoice_partner
    <where>
      <if test="expenseType != null">
        and supplier_type = #{expenseType}
      </if>
      <if test="supplierIdList != null">
        and supplier_id in
        <foreach collection="supplierIdList" separator="," open="(" close=")" item="item">
         #{item}
        </foreach>
      </if>
    </where>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_invoice_partner
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceInvoicePartner" useGeneratedKeys="true">
    insert into finance_invoice_partner (supplier_id, supplier_type, creator, 
      creator_admin_id, create_time, update_time
      )
    values (#{supplierId,jdbcType=INTEGER}, #{supplierType,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, 
      #{creatorAdminId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceInvoicePartner" useGeneratedKeys="true">
    insert into finance_invoice_partner
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="supplierType != null">
        supplier_type,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="creatorAdminId != null">
        creator_admin_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="supplierId != null">
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierType != null">
        #{supplierType,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatorAdminId != null">
        #{creatorAdminId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceInvoicePartner">
    update finance_invoice_partner
    <set>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierType != null">
        supplier_type = #{supplierType,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatorAdminId != null">
        creator_admin_id = #{creatorAdminId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceInvoicePartner">
    update finance_invoice_partner
    set supplier_id = #{supplierId,jdbcType=INTEGER},
      supplier_type = #{supplierType,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      creator_admin_id = #{creatorAdminId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>