<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.WillExpirePriceLogMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.WillExpirePriceLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="remaining_expired_days" jdbcType="INTEGER" property="remainingExpiredDays" />
    <result column="warn_days" jdbcType="INTEGER" property="warnDays" />
    <result column="sell_out_rate" jdbcType="DECIMAL" property="sellOutRate" />
    <result column="turnover_rate" jdbcType="DECIMAL" property="turnoverRate" />
    <result column="sale_price" jdbcType="DECIMAL" property="salePrice" />
    <result column="sales_pace" jdbcType="DECIMAL" property="salesPace" />
    <result column="discout" jdbcType="DECIMAL" property="discout" />
    <result column="original_price" jdbcType="DECIMAL" property="originalPrice" />
    <result column="cal_price" jdbcType="DECIMAL" property="calPrice" />
    <result column="reason" jdbcType="TINYINT" property="reason" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <sql id="Base_Column_List">
    `id`, `area_no`, `sku`, `remaining_expired_days`, `warn_days`, `sell_out_rate`, `turnover_rate`, 
    `sale_price`, `sales_pace`, `discout`, `original_price`, `cal_price`, `reason`, `update_time`,
    `create_time`
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from will_expire_price_log
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from will_expire_price_log
    where `id` = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.WillExpirePriceLog">
    insert into will_expire_price_log (`id`, `area_no`, `sku`, 
      `remaining_expired_days`, `warn_days`, `sell_out_rate`, 
      `turnover_rate`, `sale_price`, `sales_pace`, 
      `discout`, `original_price`, `cal_price`, `reason`,
      `update_time`, `create_time`)
    values (#{id,jdbcType=BIGINT}, #{areaNo,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{remainingExpiredDays,jdbcType=INTEGER}, #{warnDays,jdbcType=INTEGER}, #{sellOutRate,jdbcType=DECIMAL}, 
      #{turnoverRate,jdbcType=DECIMAL}, #{salePrice,jdbcType=DECIMAL}, #{salesPace,jdbcType=DECIMAL}, 
      #{discout,jdbcType=DECIMAL}, #{originalPrice,jdbcType=DECIMAL}, #{calPrice,jdbcType=DECIMAL}, #{reason,javaType=INTEGER},
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.WillExpirePriceLog">
    insert into will_expire_price_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="areaNo != null">
        `area_no`,
      </if>
      <if test="sku != null">
        `sku`,
      </if>
      <if test="remainingExpiredDays != null">
        `remaining_expired_days`,
      </if>
      <if test="warnDays != null">
        `warn_days`,
      </if>
      <if test="sellOutRate != null">
        `sell_out_rate`,
      </if>
      <if test="turnoverRate != null">
        `turnover_rate`,
      </if>
      <if test="salePrice != null">
        `sale_price`,
      </if>
      <if test="salesPace != null">
        `sales_pace`,
      </if>
      <if test="discout != null">
        `discout`,
      </if>
      <if test="originalPrice != null">
        `original_price`,
      </if>
      <if test="calPrice != null">
        `cal_price`,
      </if>
      <if test="reason != null">
          `reason`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="remainingExpiredDays != null">
        #{remainingExpiredDays,jdbcType=INTEGER},
      </if>
      <if test="warnDays != null">
        #{warnDays,jdbcType=INTEGER},
      </if>
      <if test="sellOutRate != null">
        #{sellOutRate,jdbcType=DECIMAL},
      </if>
      <if test="turnoverRate != null">
        #{turnoverRate,jdbcType=DECIMAL},
      </if>
      <if test="salePrice != null">
        #{salePrice,jdbcType=DECIMAL},
      </if>
      <if test="salesPace != null">
        #{salesPace,jdbcType=DECIMAL},
      </if>
      <if test="discout != null">
        #{discout,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="calPrice != null">
        #{calPrice,jdbcType=DECIMAL},
      </if>
      <if test="reason != null">
        #{reason,javaType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.WillExpirePriceLog">
    update will_expire_price_log
    <set>
      <if test="areaNo != null">
        `area_no` = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        `sku` = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="remainingExpiredDays != null">
        `remaining_expired_days` = #{remainingExpiredDays,jdbcType=INTEGER},
      </if>
      <if test="warnDays != null">
        `warn_days` = #{warnDays,jdbcType=INTEGER},
      </if>
      <if test="sellOutRate != null">
        `sell_out_rate` = #{sellOutRate,jdbcType=DECIMAL},
      </if>
      <if test="turnoverRate != null">
        `turnover_rate` = #{turnoverRate,jdbcType=DECIMAL},
      </if>
      <if test="salePrice != null">
        `sale_price` = #{salePrice,jdbcType=DECIMAL},
      </if>
      <if test="salesPace != null">
        `sales_pace` = #{salesPace,jdbcType=DECIMAL},
      </if>
      <if test="discout != null">
        `discout` = #{discout,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        `original_price` = #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="calPrice != null">
        `cal_price` = #{calPrice,jdbcType=DECIMAL},
      </if>
      <if test="reason != null">
        `reason` = #{reason,javaType=INTEGER},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="getLastBySkuAndArea" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from will_expire_price_log
    <where>
      <if test="areaNo != null">
        area_no = #{areaNo}
      </if>
      <if test="sku != null">
        and sku = #{sku}
      </if>
    </where>
    order by id desc limit 1;
  </select>
</mapper>