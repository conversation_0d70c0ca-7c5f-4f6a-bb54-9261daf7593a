<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DynamicPriceFieldConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DynamicPriceFieldConfig">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="field_id" jdbcType="BIGINT" property="fieldId"/>
    <result column="model_config_id" jdbcType="BIGINT" property="modelConfigId"/>
    <result column="lower_limit" jdbcType="DECIMAL" property="lowerLimit"/>
    <result column="upper_limit" jdbcType="DECIMAL" property="upperLimit"/>
    <result column="formula" jdbcType="VARCHAR" property="formula"/>
    <result column="binding_value" jdbcType="DECIMAL" property="bindingValue"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `field_id`, `model_config_id`, `lower_limit`, `upper_limit`, `formula`, `binding_value`, `del_flag`,
    `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_field_config
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from dynamic_price_field_config
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.DynamicPriceFieldConfig">
    insert into dynamic_price_field_config (`id`, `field_id`, `model_config_id`,
                                            `lower_limit`, `upper_limit`, `formula`,
                                            `binding_value`, `del_flag`, `create_time`, `update_time`)
    values (#{id,jdbcType=BIGINT}, #{fieldId,jdbcType=BIGINT}, #{modelConfigId,jdbcType=BIGINT},
            #{lowerLimit,jdbcType=DECIMAL}, #{upperLimit,jdbcType=DECIMAL},
            #{formula,jdbcType=VARCHAR},
            #{bindingValue,jdbcType=DECIMAL},
            #{delFlag,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.DynamicPriceFieldConfig">
    insert into dynamic_price_field_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="fieldId != null">
        `field_id`,
      </if>
      <if test="modelConfigId != null">
        `model_config_id`,
      </if>
      <if test="lowerLimit != null">
        `lower_limit`,
      </if>
      <if test="upperLimit != null">
        `upper_limit`,
      </if>
      <if test="formula != null">
        `formula`,
      </if>
      <if test="bindingValue != null">
        `binding_value`,
      </if>
      <if test="delFlag != null">
        `del_flag`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fieldId != null">
        #{fieldId,jdbcType=BIGINT},
      </if>
      <if test="modelConfigId != null">
        #{modelConfigId,jdbcType=BIGINT},
      </if>
      <if test="lowerLimit != null">
        #{lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="formula != null">
        #{formula,jdbcType=VARCHAR},
      </if>
      <if test="bindingValue != null">
        #{bindingValue,jdbcType=DECIMAL},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.model.domain.DynamicPriceFieldConfig">
    update dynamic_price_field_config
    <set>
      <if test="fieldId != null">
        `field_id` = #{fieldId,jdbcType=BIGINT},
      </if>
      <if test="modelConfigId != null">
        `model_config_id` = #{modelConfigId,jdbcType=BIGINT},
      </if>
      <if test="lowerLimit != null">
        `lower_limit` = #{lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="upperLimit != null">
        `upper_limit` = #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="formula != null">
        `formula` = #{formula,jdbcType=VARCHAR},
      </if>
      <if test="bindingValue != null">
        `binding_value` = #{bindingValue,jdbcType=DECIMAL},
      </if>
      <if test="delFlag != null">
        `del_flag` = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey"
    parameterType="net.summerfarm.model.domain.DynamicPriceFieldConfig">
    update dynamic_price_field_config
    set `field_id`        = #{fieldId,jdbcType=BIGINT},
        `model_config_id` = #{modelConfigId,jdbcType=BIGINT},
        `lower_limit`     = #{lowerLimit,jdbcType=DECIMAL},
        `upper_limit`     = #{upperLimit,jdbcType=DECIMAL},
        `formula`         = #{formula,jdbcType=VARCHAR},
        `binding_value`   = #{bindingValue,jdbcType=DECIMAL},
        `del_flag`        = #{delFlag,jdbcType=TINYINT},
        `create_time`     = #{createTime,jdbcType=TIMESTAMP},
        `update_time`     = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertBatch" parameterType="net.summerfarm.model.domain.DynamicPriceFieldConfig">
    insert into dynamic_price_field_config (`field_id`, `model_config_id`,
    `lower_limit`, `upper_limit`, `formula`,
    `binding_value`)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.fieldId,jdbcType=BIGINT}, #{item.modelConfigId,jdbcType=BIGINT},
      #{item.lowerLimit,jdbcType=DECIMAL}, #{item.upperLimit,jdbcType=DECIMAL}, #{item.formula,jdbcType=VARCHAR},
      #{item.bindingValue,jdbcType=DECIMAL})
    </foreach>
  </insert>

  <update id="deleteByModelConfigId" parameterType="java.lang.Long">
    update dynamic_price_field_config
    set del_flag = 1
    where `model_config_id` = #{modelConfigId}
  </update>

  <select id="listByModelConfigId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_field_config
    where `model_config_id` = #{modelConfigId}
  </select>
</mapper>