<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.ContactDeliveryLogMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.ContactDeliveryLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="contact_id" jdbcType="INTEGER" property="contactId" />
    <result column="delivery_frequent" jdbcType="VARCHAR" property="deliveryFrequent" />
    <result column="effect_flag" jdbcType="TINYINT" property="effectFlag" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, contact_id, delivery_frequent, effect_flag, delete_flag, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from contact_delivery_log
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByEffectMid" resultType="net.summerfarm.model.domain.ContactDeliveryLog">
      select
      <include refid="Base_Column_List" />
      from contact_delivery_log
      where contact_id = #{contactId} and effect_flag = #{effectFlag} and delete_flag = 0
    </select>
    <select id="selectByCityArea" resultType="net.summerfarm.model.vo.ContactVO">
      select cdl.contact_id contactId,cdl.delivery_frequent deliveryFrequent  from contact_delivery_log cdl
       left join contact c on c.contact_id = cdl.contact_id
      where  cdl.effect_flag = 0 and cdl.delete_flag = 0 and c.status = 1 and
        <foreach collection="adCodeMsgs" item="item" open="(" close=")" separator="or">
            c.city = #{item.city} and c.area =#{item.area}
        </foreach>

    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from contact_delivery_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.ContactDeliveryLog" useGeneratedKeys="true">
    insert into contact_delivery_log (contact_id, delivery_frequent, effect_flag, 
      delete_flag, creator, create_time
      )
    values (#{contactId,jdbcType=INTEGER}, #{deliveryFrequent,jdbcType=VARCHAR}, #{effectFlag,jdbcType=TINYINT}, 
      #{deleteFlag,jdbcType=TINYINT}, #{creator,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.ContactDeliveryLog" useGeneratedKeys="true">
    insert into contact_delivery_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contactId != null">
        contact_id,
      </if>
      <if test="deliveryFrequent != null">
        delivery_frequent,
      </if>
      <if test="effectFlag != null">
        effect_flag,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contactId != null">
        #{contactId,jdbcType=INTEGER},
      </if>
      <if test="deliveryFrequent != null">
        #{deliveryFrequent,jdbcType=VARCHAR},
      </if>
      <if test="effectFlag != null">
        #{effectFlag,jdbcType=TINYINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.ContactDeliveryLog">
    update contact_delivery_log
    <set>
      <if test="contactId != null">
        contact_id = #{contactId,jdbcType=INTEGER},
      </if>
      <if test="deliveryFrequent != null">
        delivery_frequent = #{deliveryFrequent,jdbcType=VARCHAR},
      </if>
      <if test="effectFlag != null">
        effect_flag = #{effectFlag,jdbcType=TINYINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.ContactDeliveryLog">
    update contact_delivery_log
    set contact_id = #{contactId,jdbcType=INTEGER},
      delivery_frequent = #{deliveryFrequent,jdbcType=VARCHAR},
      effect_flag = #{effectFlag,jdbcType=TINYINT},
      delete_flag = #{deleteFlag,jdbcType=TINYINT},
      creator = #{creator,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>