<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CrmCommissionRationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CrmCommissionRation">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="zone_name" jdbcType="VARCHAR" property="zoneName" />
    <result column="lower_below" jdbcType="DECIMAL" property="lowerBelow" />
    <result column="lower_finish" jdbcType="DECIMAL" property="lowerFinish" />
    <result column="lower_beyond" jdbcType="DECIMAL" property="lowerBeyond" />
    <result column="lower_new_bd" jdbcType="DECIMAL" property="lowerNewBd" />
    <result column="lower_new_area" jdbcType="DECIMAL" property="lowerNewArea" />
    <result column="base_below" jdbcType="DECIMAL" property="baseBelow" />
    <result column="base_finish" jdbcType="DECIMAL" property="baseFinish" />
    <result column="base_beyond" jdbcType="DECIMAL" property="baseBeyond" />
    <result column="base_new_bd" jdbcType="DECIMAL" property="baseNewBd" />
    <result column="base_new_area" jdbcType="DECIMAL" property="baseNewArea" />
    <result column="excess_below" jdbcType="DECIMAL" property="excessBelow" />
    <result column="excess_finish" jdbcType="DECIMAL" property="excessFinish" />
    <result column="excess_beyond" jdbcType="DECIMAL" property="excessBeyond" />
    <result column="excess_new_bd" jdbcType="DECIMAL" property="excessNewBd" />
    <result column="excess_new_area" jdbcType="DECIMAL" property="excessNewArea" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, zone_name,lower_below,lower_finish,lower_beyond,lower_new_bd,lower_new_area,
    base_below, base_finish, base_beyond, base_new_bd, base_new_area,
    excess_below, excess_finish, excess_beyond, excess_new_bd, excess_new_area, updater, 
    update_time, creator, create_time
  </sql>
  <delete id="deleteByZoneName">
    delete from crm_commission_ration
    where zone_name = #{zoneName}
  </delete>
</mapper>