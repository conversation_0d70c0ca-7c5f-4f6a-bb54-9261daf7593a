<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.PurchasesAccountPlanMapper">
    <!--批量插入-->
    <insert id="insertBatch">
        INSERT INTO purchases_account_plan (account_id,plan_id,purchases_no,back_quantity,back_cost,accounting_quantity,accounting_cost,type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountId},#{item.planId},#{item.purchasesNo},#{item.backQuantity},#{item.backCost}
            ,#{item.accountingQuantity},#{item.accountingCost},#{item.type})
        </foreach>
    </insert>

    <select id="selectAccount" resultType="net.summerfarm.model.domain.PurchasesAccountPlan">
        select pap.id,account_id accountId,purchases_no purchasesNo from purchases_account_plan pap
        inner join purchase_accounting pa on pa.id = pap.account_id and pa.status != 3
        where type = 1 and purchases_no =#{purchasesNo}
    </select>
    <select id="selectPurchases" resultType="net.summerfarm.model.domain.PurchasesAccountPlan">
        select pap.id,account_id accountId,purchases_no purchasesNo from purchases_account_plan pap
        inner join purchase_accounting pa on pa.id = pap.account_id and pa.status != 3
        where type = 0 and plan_id =#{id}

    </select>

    <select id="selectByAccountId" resultType="net.summerfarm.model.domain.PurchasesAccountPlan">
        select id,account_id accountId,
        purchases_no purchasesNo,
        from purchases_account_plan pap
        where account_id =#{id}
    </select>

    <select id="selectAccountPlanVO" resultType= "net.summerfarm.model.vo.PurchasesAccountPlanVO">
    select pp.purchase_no purchaseNo,
    p.purchase_time purchaseTime
    ,pp.sku,pp.supplier,pp.title,pp.specification,pp.quantity,pp.price totalPrice,
    pp.supplier_id supplierId,i.weight,p.logistics_cost logisticsCost,pap.type,
    back_quantity backQuantity,
    back_cost backCost,
    accounting_quantity accountingQuantity,
    accounting_cost accountingCost,
    pd.pd_name pdName,i.weight,i.ext_type extType
    from purchases p
        inner join purchases_plan pp on p.purchase_no = pp.purchase_no  and pp.plan_status = 1 and pp.origin_id is null
        inner join inventory i on i.sku = pp.sku
        INNER JOIN products pd ON i.pd_id = pd.pd_id
        inner join purchases_account_plan pap on pap.plan_id = pp.id and pap.account_id  = #{id}
        inner join supplier s on s.id = pp.supplier_id
    </select>


    <select id="selectLogistics"  resultType= "net.summerfarm.model.vo.PurchasesAccountPlanVO">
        select
         p.purchase_no purchaseNo,
         p.purchase_time purchaseTime,
         p.logistics_cost logisticsCost,
         pap.type
         from purchases p
        inner join purchases_account_plan pap on p.purchase_no = pap.purchases_no and pap.type = 1
        where pap.account_id = #{id}
    </select>

    <select id="selectPurchaseById" resultType="net.summerfarm.model.domain.PurchasesAccountPlan">
        select pa.id from purchases_plan pp
        inner join purchases_account_plan pap on pp.id = pap.plan_id
        inner join purchase_accounting pa on pa.id = pap.account_id and pa.status in(0,1,2)
       where pp.purchase_no = #{purchaseNo} and pp.sku =#{sku} and pp.plan_status = 1
    </select>



</mapper>