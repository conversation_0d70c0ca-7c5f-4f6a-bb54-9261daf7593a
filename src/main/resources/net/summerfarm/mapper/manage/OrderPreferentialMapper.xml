<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.OrderPreferentialMapper">

    <select id="selectPreferential" resultType="net.summerfarm.model.domain.OrderPreferential">
       select id,type,amount,order_no orderNo,activity_name activityName
          from order_preferential where order_no =#{orderNo}
    </select>

    <select id="selectDeliveryFeeAmountByOrderNo" resultType="java.math.BigDecimal">
        select IFNULL(amount, 0) from order_preferential where order_no =#{orderNo} and type = 19
    </select>

</mapper>