<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.CarrierAccountMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CarrierAccount">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="carrier_id" jdbcType="BIGINT" property="carrierId" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_bank" jdbcType="VARCHAR" property="accountBank" />
    <result column="account_ascription" jdbcType="VARCHAR" property="accountAscription" />
    <result column="account" jdbcType="VARCHAR" property="account" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, carrier_id, pay_type, account_name, account_bank, account_ascription, 
    account
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from carrier_account
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="selectByid" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List" />
      FROM carrier_account WHERE carrier_id = #{carrierId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from carrier_account
    where id = #{id,jdbcType=INTEGER}
  </delete>
    <delete id="deleteByCarrierId">
      delete from carrier_account where carrier_id = #{id}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CarrierAccount" useGeneratedKeys="true">
    insert into carrier_account (create_time, update_time, carrier_id, 
      pay_type, account_name, account_bank, 
      account_ascription, account)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{carrierId,jdbcType=BIGINT}, 
      #{payType,jdbcType=INTEGER}, #{accountName,jdbcType=VARCHAR}, #{accountBank,jdbcType=VARCHAR}, 
      #{accountAscription,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.CarrierAccount" useGeneratedKeys="true">
    insert into carrier_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="carrierId != null">
        carrier_id,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="accountBank != null">
        account_bank,
      </if>
      <if test="accountAscription != null">
        account_ascription,
      </if>
      <if test="account != null">
        account,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="carrierId != null">
        #{carrierId,jdbcType=BIGINT},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountBank != null">
        #{accountBank,jdbcType=VARCHAR},
      </if>
      <if test="accountAscription != null">
        #{accountAscription,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.CarrierAccount">
    update carrier_account
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="carrierId != null">
        carrier_id = #{carrierId,jdbcType=BIGINT},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountBank != null">
        account_bank = #{accountBank,jdbcType=VARCHAR},
      </if>
      <if test="accountAscription != null">
        account_ascription = #{accountAscription,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        account = #{account,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.CarrierAccount">
    update carrier_account
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      carrier_id = #{carrierId,jdbcType=BIGINT},
      pay_type = #{payType,jdbcType=INTEGER},
      account_name = #{accountName,jdbcType=VARCHAR},
      account_bank = #{accountBank,jdbcType=VARCHAR},
      account_ascription = #{accountAscription,jdbcType=VARCHAR},
      account = #{account,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>