<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MerchantPoolTagMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantPoolTag">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="description" jdbcType="VARCHAR" property="description"/>
    <result column="relation_symbol" jdbcType="VARCHAR" property="relationSymbol"/>
    <result column="field_type" jdbcType="TINYINT" property="fieldType"/>
    <result column="type" jdbcType="TINYINT" property="type"/>
    <result column="creator" jdbcType="VARCHAR" property="creator"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `name`, `description`, `relation_symbol`, `field_type`, `type`, `creator`,
    `create_time`, `update_time`
  </sql>

  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_pool_tag
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteById" parameterType="java.lang.Long">
    delete
    from merchant_pool_tag
    where `id` = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.MerchantPoolTag">
    insert into merchant_pool_tag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="description != null">
        `description`,
      </if>
      <if test="relationSymbol != null">
        `relation_symbol`,
      </if>
      <if test="fieldType != null">
        `field_type`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="creator != null">
        `creator`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="relationSymbol != null">
        #{relationSymbol,jdbcType=VARCHAR},
      </if>
      <if test="fieldType != null">
        #{fieldType,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="net.summerfarm.model.domain.MerchantPoolTag">
    update merchant_pool_tag
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        `description` = #{description,jdbcType=VARCHAR},
      </if>
      <if test="relationSymbol != null">
        `relation_symbol` = #{relationSymbol,jdbcType=VARCHAR},
      </if>
      <if test="fieldType != null">
        `field_type` = #{fieldType,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        `creator` = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from
    merchant_pool_tag
    where type=#{type}
  </select>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from
    merchant_pool_tag
  </select>

</mapper>