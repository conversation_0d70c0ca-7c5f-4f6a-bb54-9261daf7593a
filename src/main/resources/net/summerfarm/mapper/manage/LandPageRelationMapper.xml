<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.LandPageRelationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.LandPageRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="land_page_id" jdbcType="BIGINT" property="landPageId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="item_son_id" jdbcType="BIGINT" property="itemSonId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="outdated" jdbcType="BOOLEAN" property="outdated" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, land_page_id, item_id, item_son_id, sort, outdated, add_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from land_page_relation
    where id = #{id,jdbcType=BIGINT}
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from land_page_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.LandPageRelation" useGeneratedKeys="true">
    insert into land_page_relation (land_page_id, item_id, item_son_id, 
      sort, outdated, add_time, 
      update_time)
    values (#{landPageId,jdbcType=BIGINT}, #{itemId,jdbcType=BIGINT}, #{itemSonId,jdbcType=BIGINT}, 
      #{sort,jdbcType=INTEGER}, #{outdated,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.LandPageRelation" useGeneratedKeys="false">
    insert into land_page_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="landPageId != null">
        land_page_id,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="itemSonId != null">
        item_son_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="outdated != null">
        outdated,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="landPageId != null">
        #{landPageId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=BIGINT},
      </if>
      <if test="itemSonId != null">
        #{itemSonId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="outdated != null">
        #{outdated,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.LandPageRelation">
    update land_page_relation
    <set>
      <if test="landPageId != null">
        land_page_id = #{landPageId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="itemSonId != null">
        item_son_id = #{itemSonId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="outdated != null">
        outdated = #{outdated,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.LandPageRelation">
    update land_page_relation
    set land_page_id = #{landPageId,jdbcType=BIGINT},
      item_id = #{itemId,jdbcType=BIGINT},
      item_son_id = #{itemSonId,jdbcType=BIGINT},
      sort = #{sort,jdbcType=INTEGER},
      outdated = #{outdated,jdbcType=BOOLEAN},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByKeys">
    update land_page_relation
    set sort = #{sort}
    where item_id = #{itemId} AND item_son_id = #{itemSonId}
  </update>

  <update id="updateByItemSonId" parameterType="net.summerfarm.model.domain.LandPageRelation">
    update land_page_relation
    <set>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where item_son_id = #{itemSonId}
  </update>
</mapper>