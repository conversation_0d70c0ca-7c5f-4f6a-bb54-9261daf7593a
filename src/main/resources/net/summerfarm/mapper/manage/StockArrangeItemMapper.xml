<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockArrangeItemMapper">


    <insert id="insertByPurchases" parameterType="net.summerfarm.model.vo.StockArrangeItemDetailVO" keyProperty="id" useGeneratedKeys="true">
    insert into stock_arrange_item (stock_arrange_id,sku,arrival_quantity, pd_name, weight, supplier, `type`, quality_time,
      quality_time_unit,create_time, supplier_id)
      values (#{stockArrangeId,jdbcType=VARCHAR}, #{sku,jdbcType=INTEGER},#{totalQuantity,jdbcType=INTEGER}, #{pdName,jdbcType=INTEGER},
      #{weight,jdbcType=INTEGER}, #{supplier,jdbcType=DECIMAL}, #{type,jdbcType=DECIMAL},#{qualityTime,jdbcType=INTEGER},#{qualityTimeUnit,jdbcType=INTEGER},
      now(), #{supplierId})
  </insert>

    <select id="selectByTaskId" resultType="net.summerfarm.model.domain.StockArrangeItem" >
        select
        sai.id, sai.stock_arrange_id stockArrangeId,sai.sku,sai.arrival_quantity arrivalQuantity,sai.actual_quantity actualQuantity,sai.pd_name pdName,sai.weight,
        sai.supplier,sai.type,sai.quality_time qualityTime,sai.quality_time_unit qualityTimeUnit
        from  stock_arrange sa
        left join  stock_arrange_item sai on sai.stock_arrange_id= sa.id
        where sa.stock_task_id = #{taskId}
    </select>

    <select id="selectByTaskIdAndSku" resultType="net.summerfarm.model.domain.StockArrangeItem" >
        select
        sai.id, sai.stock_arrange_id stockArrangeId,sai.sku,sai.arrival_quantity arrivalQuantity,sai.actual_quantity actualQuantity,sai.pd_name pdName,sai.weight,
        sai.supplier,sai.type,sai.quality_time qualityTime,sai.quality_time_unit qualityTimeUnit
        from  stock_arrange sa
        left join stock_arrange_item sai on sai.stock_arrange_id= sa.id
        where sa.stock_task_id = #{taskId} and sai.sku = #{sku}
    </select>

    <update id="updateActualQuantity">
        update stock_arrange_item  set actual_quantity = actual_quantity + #{inQuantity}
        where stock_arrange_id = #{stockArrangeId}  and sku=#{sku}
    </update>

    <insert id="insert" parameterType="net.summerfarm.model.domain.StockArrangeItem" keyProperty="id" useGeneratedKeys="true">
    insert into stock_arrange_item (stock_arrange_id,sku,arrival_quantity,actual_quantity, pd_name, weight, supplier, `type`, quality_time,
      quality_time_unit,create_time)
      values (#{stockArrangeId,jdbcType=VARCHAR}, #{sku,jdbcType=INTEGER},#{arrivalQuantity,jdbcType=INTEGER},#{actualQuantity,jdbcType=INTEGER}, #{pdName,jdbcType=INTEGER},
      #{weight,jdbcType=INTEGER}, #{supplier,jdbcType=DECIMAL}, #{type,jdbcType=DECIMAL},#{qualityTime,jdbcType=INTEGER},#{qualityTimeUnit,jdbcType=INTEGER},
      now())
  </insert>

    <select id="selectByArrangeDate" resultType="net.summerfarm.model.domain.StockArrangeItem" >
        select
        sai.sku , sai.arrival_quantity arrivalQuantity, sai.actual_quantity actualQuantity
        from stock_arrange sa
        left join purchases p on p.purchase_no= sa.purchase_no
        left join stock_arrange_item sai on sai.stock_arrange_id= sa.id
        where p.area_no = #{warehouseNo} and  sa.arrange_time  <![CDATA[>=]]> #{allocationDate} and  sa.arrange_time  <![CDATA[<=]]> #{nextAllocationDate} and  sa.state = 0
    </select>

    <select id="onWayArrangeInfos" resultType="net.summerfarm.model.domain.StockArrangeItem">
        select
        sai.id id,
        sai.stock_arrange_id stockArrangeId,
        sai.sku sku,
        sai.pd_name pdName,
        sai.weight weight,
        sai.type,
        sai.supplier supplier,
        sai.quality_time qualityTime,
        sai.quality_time_unit qualityTimeUnit,
        sai.arrival_quantity arrivalQuantity,
        sai.actual_quantity actualQuantity
        from
        stock_arrange sa left join stock_arrange_item sai on sa.id = sai.stock_arrange_id
        left join stock_task st on sa.stock_task_id = st.id
        where sa.state = 0 and st.type = 11
        <if test="warehouseNo != null">
            and st.area_no = #{warehouseNo}
        </if>
        <if test="skuId != null">
            and sai.sku = #{skuId}
        </if>
        <if test="startDate != null">
            and sa.arrange_time >= #{startDate}
        </if>
        <if test="endDate != null">
            and sa.arrange_time <![CDATA[<]]> #{endDate}
        </if>
    </select>
    <select id="selectShipQuantityByPurchaseNoSkuAndSupplierId" resultType="java.lang.Integer">
        select (select IFNULL(sum(arrival_quantity),0)
                from stock_arrange_item sai
                         left join stock_arrange sa on sa.id = sai.stock_arrange_id
                where sa.purchase_no = #{purchaseNo} and sai.sku = #{sku} and sai.supplier_id = #{supplierId} and sa.state = 0) +
               (select IFNULL(sum(actual_quantity),0)
                from stock_arrange_item sai
                         left join stock_arrange sa on sa.id = sai.stock_arrange_id
                where sa.purchase_no = #{purchaseNo} and sai.sku = #{sku} and sai.supplier_id = #{supplierId} and sa.state = 1)
    </select>
    <select id="selectReceiptQuantityByPurchaseNoSkuAndSupplierId" resultType="java.lang.Integer">
       select IFNULL(sum(actual_quantity),0) receiptQuantity
        from stock_arrange_item sai
                 left join stock_arrange sa on sa.id = sai.stock_arrange_id
        where sa.purchase_no = #{purchaseNo} and sai.sku = #{sku} and sai.supplier_id = #{supplierId} and sa.state in (0, 1)
    </select>
    <select id="selectByStockArrangeId" resultType="net.summerfarm.model.vo.srm.SrmStockArrangeItemVO">
        select sai.id id,sai.arrival_quantity arrivalQuantity, sai.actual_quantity actualQuantity,
        sai.pd_name pdName,sai.sku sku, sai.weight weight, i.weight_num as weightNum,if(i.sku_pic is null , p.picture_path, i.sku_pic) pic
        from stock_arrange_item sai
        left join stock_arrange sa on sai.stock_arrange_id = sa.id
        left join inventory i on sai.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        where sa.id = #{stockArrangeId}
    </select>

    <select id="selectByPurchasesNo" resultType="net.summerfarm.model.vo.srm.SrmStockArrangeItemVO">
        select sai.id stockArrangeItemId,sai.arrival_quantity arrivalQuantity, sai.actual_quantity actualQuantity,
               sai.pd_name pdName,sai.sku sku, sai.weight weight, if(i.sku_pic is null , p.picture_path, i.sku_pic) pic
        from stock_arrange_item sai
                 left join stock_arrange sa on sai.stock_arrange_id = sa.id
                 left join inventory i on sai.sku = i.sku
                 left join products p on i.pd_id = p.pd_id
        where sa.purchase_no = #{purchasesNo}
    </select>

    <select id="selectCountByPurchaseSkuList" resultType="net.summerfarm.model.vo.pms.PurchaseSkuCountVO">
        select sum(said.quantity) actCount ,
                sai.sku sku,
                sa.purchase_no purchaseNo
        from stock_arrange_item_detail said
        left join stock_arrange_item sai on said.stock_arrange_item_id = sai.id
        left join stock_arrange sa on sai.stock_arrange_id = sa.id
        where (sa.purchase_no,sai.sku) in
        (<foreach collection="skuQueryList" item="item" index="index" separator=",">
        (#{item.purchaseNo},#{item.sku})
        </foreach>)
        group by sa.purchase_no,sai.sku
    </select>

    <update id="addAbnormalQuantity">
        update stock_arrange_item
        set abnormal_quantity=#{quantity} + abnormal_quantity
        where id = #{id}
    </update>
</mapper>