<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MerchantPoolTagConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantPoolTagConfig">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="tag_id" jdbcType="BIGINT" property="tagId"/>
    <result column="value" jdbcType="VARCHAR" property="value"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `tag_id`, `value`, `create_time`, `update_time`
  </sql>

  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_pool_tag_config
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteById" parameterType="java.lang.Long">
    delete
    from merchant_pool_tag_config
    where `id` = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.MerchantPoolTagConfig">
    insert into merchant_pool_tag_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="tagId != null">
        `tag_id`,
      </if>
      <if test="value != null">
        `value`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=BIGINT},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="net.summerfarm.model.domain.MerchantPoolTagConfig">
    update merchant_pool_tag_config
    <set>
      <if test="tagId != null">
        `tag_id` = #{tagId,jdbcType=BIGINT},
      </if>
      <if test="value != null">
        `value` = #{value,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByTagId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from
    merchant_pool_tag_config
    where tag_id = #{tagId}
  </select>

</mapper>