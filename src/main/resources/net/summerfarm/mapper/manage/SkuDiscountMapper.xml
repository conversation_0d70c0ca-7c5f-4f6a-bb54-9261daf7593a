<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.SkuDiscountMapper">

    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id">
        insert into sku_discount (sku, discount,overlap,creator, updater)
        values
        <foreach collection="list" separator="," item="item" >
            ( #{item.sku,jdbcType=VARCHAR},#{item.discount},#{item.overlap}, #{item.creator,jdbcType=INTEGER},#{item.updater,jdbcType=INTEGER})

        </foreach>
        on duplicate key update discount = values(discount), overlap=values(overlap),updater = values(updater)
    </insert>

    <update id="update">
        update sku_discount set discount = #{discount} ,overlap = #{overlap},updater = #{updater} where sku = #{sku};
    </update>

    <select id="select" parameterType="java.lang.String" resultType="net.summerfarm.model.vo.SkuDiscountVO">
        select discount,overlap from sku_discount where sku = #{sku};
    </select>
</mapper>