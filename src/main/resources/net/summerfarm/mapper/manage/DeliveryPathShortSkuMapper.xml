<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.DeliveryPathShortSkuMapper">
    <insert id="insertBatch">
        insert into delivery_path_short_sku (delivery_path_id,sku,short_cnt,addtime,`type`,remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.deliveryPathId},#{item.sku},#{item.shortCnt},now(),#{item.type},#{item.remark})
        </foreach>
    </insert>

    <select id="selectByDeliveryPathId" resultType="net.summerfarm.model.domain.DeliveryPathShortSku">
        select delivery_path_id deliveryPathId ,sku ,short_cnt shortCnt,`type`, remark from delivery_path_short_sku
        where delivery_path_id =#{deliveryPathId}
    </select>

    <select id="selectByDeliveryPath" resultType="net.summerfarm.model.domain.DeliveryPathShortSku">
                select delivery_path_id deliveryPathId ,sku ,short_cnt shortCnt,`type`, remark from delivery_path_short_sku
        where delivery_path_id = #{deliveryPathId} and sku =#{sku} and `type` = #{type}
    </select>

    <select id="getDataByDPIdAndSku" resultType="net.summerfarm.model.domain.DeliveryPathShortSku">
        select delivery_path_id deliveryPathId,remark
            from delivery_path_short_sku
        where delivery_path_id = #{deliveryPathId} and sku =#{sku}
    </select>
</mapper>