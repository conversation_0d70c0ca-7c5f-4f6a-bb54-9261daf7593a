<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DirectPurchaseRechargeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DirectPurchaseRecharge">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="recharge_num" jdbcType="DECIMAL" property="rechargeNum" />
    <result column="picture_paths" jdbcType="VARCHAR" property="picturePaths" />
    <result column="applicant" jdbcType="VARCHAR" property="applicant" />
    <result column="addtime"  property="addtime" />
    <result column="handler" jdbcType="VARCHAR" property="handler" />
    <result column="processingtime" property="processingtime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, recharge_num, picture_paths, applicant, addtime, `handler`, processingtime, 
    `status`, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from direct_purchase_recharge
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByKeys" parameterType="net.summerfarm.model.input.DirectPurchaseRechargeQuery" resultType="net.summerfarm.model.vo.DirectPurchaseRechargeVO">
   SELECT dpr.id id, dpr.m_id mId, m.mname mname, dpr.recharge_num rechargeNum, dpr.remark remark,
    dpr.picture_paths picturePaths, dpr.applicant applicant, dpr.handler handler,
    dpr.status status, dpr.addtime addtime, dpr.processingtime processingtime, dprr.left_amount leftAmount
   FROM direct_purchase_recharge dpr
   LEFT JOIN merchant m ON m.m_id = dpr.m_id
   LEFT JOIN direct_purchase_recharge_record dprr on dpr.id = dprr.recharge_id
   <where>
     <if test="mId != null">
       AND dpr.m_id = #{mId}
     </if>
     <if test="mname != null">
       AND m.mname LIKE CONCAT('%',#{mname},'%')
     </if>
     <if test="applicant != null">
       AND dpr.applicant LIKE CONCAT('%',#{applicant},'%')
     </if>
     <if test="status != null">
       AND dpr.status = #{status}
     </if>
     <if test="startTime != null">
       AND dpr.addtime <![CDATA[>=]]> #{startTime}
     </if>
     <if test="endTime != null">
       AND dpr.addtime <![CDATA[<=]]> #{endTime}
     </if>
     <if test="leftFlag != null">
        <choose>
          <when test="leftFlag == 0">
            AND dprr.left_amount <![CDATA[<=]]> 0
          </when>
          <otherwise>
            AND dprr.left_amount > 0
          </otherwise>
        </choose>
     </if>
       AND IFNULL(dprr.status, -1) != 1
   </where>
    ORDER BY dpr.addtime DESC
  </select>
  <select id="selectByorderNo" resultType="net.summerfarm.model.vo.DirectPurchaseOrderVO">
    SELECT dpr.id id , dpr.m_id mId, dpr.recharge_num rechargeNum, dpr.addtime addtime, t1.difference_amount differenceAmount
    FROM direct_purchase_recharge dpr
    INNER JOIN
    (SELECT recharge_id, ABS(pre_amount - left_amount) difference_amount
     FROM direct_purchase_recharge_record
     WHERE order_no = #{orderNo} and `type` = 0 ) t1
     ON dpr.id = t1.recharge_id
     WHERE dpr.status = 1
     ORDER BY dpr.addtime
  </select>
  <select id="selectBymId" resultType="net.summerfarm.model.vo.DirectPurchaseOrderVO">
    SELECT dpr.id id, dpr.m_id mId, dpr.recharge_num rechargeNum, dprr.left_amount leftAmount, dpr.addtime addtime,
           dprr.id recordId
    FROM direct_purchase_recharge_record dprr
    LEFT JOIN  direct_purchase_recharge dpr ON dprr.recharge_id = dpr.id
    WHERE
    dpr.status = 1
    AND  dprr.status = 0
    AND  dprr.left_amount > 0
    <if test="mId != null" >
      AND dprr.m_id = #{mId}
    </if>
    ORDER  BY dpr.addtime;
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.DirectPurchaseRecharge" useGeneratedKeys="true">
    insert into direct_purchase_recharge (m_id, recharge_num, picture_paths, 
      applicant, addtime, `handler`, 
      processingtime, `status`, remark
      )
    values (#{mId,jdbcType=BIGINT}, #{rechargeNum,jdbcType=DECIMAL}, #{picturePaths,jdbcType=VARCHAR}, 
      #{applicant,jdbcType=VARCHAR}, #{addtime,jdbcType=TIMESTAMP}, #{handler,jdbcType=VARCHAR},
      #{processingtime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="rechargeId" parameterType="net.summerfarm.model.domain.DirectPurchaseRecharge" useGeneratedKeys="true">
    insert into direct_purchase_recharge
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="rechargeNum != null">
        recharge_num,
      </if>
      <if test="picturePaths != null">
        picture_paths,
      </if>
      <if test="applicant != null">
        applicant,
      </if>
      <if test="addtime != null">
        addtime,
      </if>
      <if test="handler != null">
        `handler`,
      </if>
      <if test="processingtime != null">
        processingtime,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="rechargeNum != null">
        #{rechargeNum,jdbcType=DECIMAL},
      </if>
      <if test="picturePaths != null">
        #{picturePaths,jdbcType=VARCHAR},
      </if>
      <if test="applicant != null">
        #{applicant,jdbcType=VARCHAR},
      </if>
      <if test="addtime != null">
        #{addtime,jdbcType=TIMESTAMP},
      </if>
      <if test="handler != null">
        #{handler,jdbcType=VARCHAR},
      </if>
      <if test="processingtime != null">
        #{processingtime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.DirectPurchaseRecharge">
    update direct_purchase_recharge
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="rechargeNum != null">
        recharge_num = #{rechargeNum,jdbcType=DECIMAL},
      </if>
      <if test="picturePaths != null">
        picture_paths = #{picturePaths,jdbcType=VARCHAR},
      </if>
      <if test="applicant != null">
        applicant = #{applicant,jdbcType=VARCHAR},
      </if>
      <if test="addtime != null">
        addtime = #{addtime,jdbcType=TIMESTAMP},
      </if>
      <if test="handler != null">
        `handler` = #{handler,jdbcType=VARCHAR},
      </if>
      <if test="processingtime != null">
        processingtime = #{processingtime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DirectPurchaseRecharge">
    update direct_purchase_recharge
    set m_id = #{mId,jdbcType=BIGINT},
      recharge_num = #{rechargeNum,jdbcType=DECIMAL},
      picture_paths = #{picturePaths,jdbcType=VARCHAR},
      applicant = #{applicant,jdbcType=VARCHAR},
      addtime = #{addtime,jdbcType=TIMESTAMP},
      `handler` = #{handler,jdbcType=VARCHAR},
      processingtime = #{processingtime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>