<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.PurchasesImprestRecordMapper">



    <insert id="createPurchasesRecord" parameterType="net.summerfarm.model.domain.PurchasesImprestRecord" useGeneratedKeys="true" keyProperty="id" >
       insert into purchases_imprest_record
       (type_name,origin_amount,new_amount,status,create_time,examine_time,examine_id,examine_name,imprest_id,create_id,create_name)
       values (#{typeName},#{originAmount},#{newAmount},#{status},#{createTime},#{examineTime},#{examineId},#{examineName},#{imprestId},#{createId},#{createName})
    </insert>


    <select id="queryRecord" resultType="net.summerfarm.model.domain.PurchasesImprestRecord">
        select
         type_name typeName,
         origin_amount originAmount ,
         new_amount newAmount,
         status,
         create_id createId,
         create_name createName,
         create_time createTime,
         examine_time,
         examine_id,
         examine_name
        from purchases_imprest_record
        where status = 0
    </select>


    <select id="queryById" resultType="net.summerfarm.model.domain.PurchasesImprestRecord">
        select
          id,
         type_name typeName,
         origin_amount originAmount ,
         new_amount newAmount,
         status,
         create_id createId,
         create_name createName,
         create_time createTime,
         examine_time examineTime,
         examine_id examineId,
         examine_name examineName,
         imprest_id imprestId
        from purchases_imprest_record
        where id =#{id}
    </select>
    <update id="updateRecord" parameterType="net.summerfarm.model.domain.PurchasesImprestRecord">
        update purchases_imprest_record
        <set>
        <if test="status != null">
            status = #{status},
        </if>
        <if test="examineTime != null">
            examine_time = #{examineTime},
        </if>
        <if test="examineId != null">
           examine_id = #{examineId},
        </if>
        <if test="examineName != null">
            examine_name = #{examineName},
        </if>
        </set>
        where id =#{id}

    </update>
    <select id="queryByRecord" resultType="net.summerfarm.model.domain.PurchasesImprestRecord">
        select
          id,
         type_name typeName,
         origin_amount originAmount ,
         new_amount newAmount,
         status,
         create_id createId,
         create_name createName,
         create_time createTime,
         examine_time examineTime,
         examine_id examineId,
         examine_name examineName,
         imprest_id imprestId
        from purchases_imprest_record
        <where>
            <if test="status != null">
                status = #{status}
            </if>
            <if test="createTime != null">
                and create_time >= #{createTime}
            </if>
        </where>
        order by create_time
    </select>




</mapper>