<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.AfterSaleWoProcessMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.AfterSaleWoProcess">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="wo_id" jdbcType="INTEGER" property="woId" />
    <result column="context" jdbcType="VARCHAR" property="context" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, wo_id, context, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from after_sale_wo_process
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from after_sale_wo_process
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.AfterSaleWoProcess" useGeneratedKeys="true">
    insert into after_sale_wo_process (wo_id, context, creator, 
      create_time)
    values (#{woId,jdbcType=INTEGER}, #{context,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.AfterSaleWoProcess" useGeneratedKeys="true">
    insert into after_sale_wo_process
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="woId != null">
        wo_id,
      </if>
      <if test="context != null">
        context,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="woId != null">
        #{woId,jdbcType=INTEGER},
      </if>
      <if test="context != null">
        #{context,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.AfterSaleWoProcess">
    update after_sale_wo_process
    <set>
      <if test="woId != null">
        wo_id = #{woId,jdbcType=INTEGER},
      </if>
      <if test="context != null">
        context = #{context,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.AfterSaleWoProcess">
    update after_sale_wo_process
    set wo_id = #{woId,jdbcType=INTEGER},
      context = #{context,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByWoId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from after_sale_wo_process
    where wo_id = #{woId}
    order by id desc
  </select>
</mapper>