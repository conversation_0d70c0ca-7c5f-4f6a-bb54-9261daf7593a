<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.InventoryMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.Inventory">
        <id column="inv_id" property="invId" jdbcType="BIGINT"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="ait_id" property="aitId" jdbcType="INTEGER"/>
        <result column="pd_id" property="pdId" jdbcType="BIGINT"/>
        <result column="origin" property="origin" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="pack" property="pack" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="maturity" property="maturity" jdbcType="VARCHAR"/>
        <result column="production_date" property="productionDate" jdbcType="DATE"/>
        <result column="storage_method" property="storageMethod" jdbcType="VARCHAR"/>
        <result column="sale_price" property="salePrice" jdbcType="DECIMAL"/>
        <result column="base_sale_quantity" jdbcType="INTEGER" property="baseSaleQuantity"/>
        <result column="base_sale_unit" jdbcType="INTEGER" property="baseSaleUnit"/>
        <result column="promotion_price" property="promotionPrice" jdbcType="DECIMAL"/>
        <result column="outdated" property="outdated" jdbcType="BIT"/>
        <result column="after_sale_quantity" property="afterSaleQuantity" jdbcType="INTEGER"/>
        <result column="introduction" property="introduction" jdbcType="VARCHAR"/>
        <result column="volume" property="volume" jdbcType="VARCHAR"/>
        <result column="weight_num" property="weightNum" jdbcType="DECIMAL"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="sub_type" property="subType" jdbcType="INTEGER"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="sample_pool" property="samplePool"/>
        <result column="sku_pic" property="skuPic"/>
        <result column="after_sale_unit" property="afterSaleUnit" jdbcType="VARCHAR"/>
        <result column="audit_status" property="auditStatus" jdbcType="INTEGER"/>
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="auditor" property="auditor" jdbcType="INTEGER"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="ext_type" property="extType" jdbcType="INTEGER"/>
        <result column="create_type" property="createType" jdbcType="INTEGER"/>
        <result column="refuse_reason" property="refuseReason" jdbcType="VARCHAR"/>
        <result column="is_domestic" property="isDomestic" jdbcType="VARCHAR"/>
        <result column="net_weight_num" jdbcType="DECIMAL" property="netWeightNum" />
        <result column="video_url" jdbcType="VARCHAR" property="videoUrl" />
        <result column="after_sale_rule_detail" jdbcType="VARCHAR" property="afterSaleRuleDetail" />
        <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
        <result column="buyer_id" jdbcType="BIGINT" property="buyerId" />
        <result column="net_weight_unit" jdbcType="VARCHAR" property="netWeightUnit" />
    </resultMap>

    <resultMap id="BySpuMap" type="net.summerfarm.model.vo.InventoryVO">
        <id column="inv_id" property="invId" jdbcType="BIGINT"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="ait_id" property="aitId" jdbcType="INTEGER"/>
        <result column="pd_id" property="pdId" jdbcType="BIGINT"/>
        <result column="origin" property="origin" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="pack" property="pack" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="maturity" property="maturity" jdbcType="VARCHAR"/>
        <result column="production_date" property="productionDate" jdbcType="DATE"/>
        <result column="storage_method" property="storageMethod" jdbcType="VARCHAR"/>
        <result column="sale_price" property="salePrice" jdbcType="DECIMAL"/>
        <result column="promotion_price" property="promotionPrice" jdbcType="DECIMAL"/>
        <result column="outdated" property="outdated" jdbcType="BIT"/>
        <result column="after_sale_quantity" property="afterSaleQuantity" jdbcType="INTEGER"/>
        <result column="introduction" property="introduction" jdbcType="VARCHAR"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="base_sale_quantity" jdbcType="INTEGER" property="baseSaleQuantity"/>
        <result column="base_sale_unit" jdbcType="INTEGER" property="baseSaleUnit"/>
        <result column="after_sale_unit" jdbcType="VARCHAR" property="afterSaleUnit"/>
        <result column="volume" property="volume" jdbcType="VARCHAR"/>
        <result column="weight_num" property="weightNum" jdbcType="DECIMAL"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="sku_pic" property="skuPic" jdbcType="VARCHAR"/>
        <collection property="areaSkuVOS" ofType="net.summerfarm.model.vo.AreaSkuVO">
            <id column="id" property="id"/>
            <result column="aksku" property="sku"/>
            <result column="area_name" property="areaName"/>
            <result column="areaNo" property="areaNo"/>
            <result column="original_price" property="originalPrice"/>
            <result column="price" property="price"/>
            <result column="ladder_price" property="ladderPrice"/>
            <result column="limited_quantity" property="limitedQuantity"/>
            <result column="sales_mode" property="salesMode"/>
            <result column="on_sale" property="onSale"/>
            <result column="show" property="show"/>
            <result column="parentNo" property="parentNo"/>
            <result column="info" property="info"/>
            <result column="mType" property="mType"/>
        </collection>
    </resultMap>

    <resultMap id="SkuMap" type="net.summerfarm.model.vo.InventoryVO">
        <id column="inv_id" property="invId" jdbcType="BIGINT"/>
        <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
        <result column="name_remakes" property="nameRemakes" jdbcType="VARCHAR"/>
        <result column="sample_pool" property="samplePool" jdbcType="VARCHAR"/>
        <result column="audit_status" property="auditStatus" jdbcType="VARCHAR"/>
        <result column="audit_time" property="auditTime" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="ext_type" property="extType" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="create_remark" property="createRemark" jdbcType="VARCHAR"/>
        <result column="weight_notes" property="weightNotes" jdbcType="VARCHAR"/>
        <result column="is_domestic" property="isDomestic" jdbcType="VARCHAR"/>
        <result column="average_price_flag" property="averagePriceFlag" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="ait_id" property="aitId" jdbcType="INTEGER"/>
        <result column="pd_id" property="pdId" jdbcType="BIGINT"/>
        <result column="origin" property="origin" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="pack" property="pack" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="maturity" property="maturity" jdbcType="VARCHAR"/>
        <result column="production_date" property="productionDate" jdbcType="DATE"/>
        <result column="storage_method" property="storageMethod" jdbcType="VARCHAR"/>
        <result column="sale_price" property="salePrice" jdbcType="DECIMAL"/>
        <result column="promotion_price" property="promotionPrice" jdbcType="DECIMAL"/>
        <result column="outdated" property="outdated" jdbcType="BIT"/>
        <result column="after_sale_quantity" property="afterSaleQuantity" jdbcType="INTEGER"/>
        <result column="introduction" property="introduction" jdbcType="VARCHAR"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="base_sale_quantity" jdbcType="INTEGER" property="baseSaleQuantity"/>
        <result column="base_sale_unit" jdbcType="INTEGER" property="baseSaleUnit"/>
        <result column="after_sale_unit" jdbcType="VARCHAR" property="afterSaleUnit"/>
        <result column="volume" property="volume" jdbcType="VARCHAR"/>
        <result column="weight_num" property="weightNum" jdbcType="DECIMAL"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="sub_type" property="subType" jdbcType="INTEGER"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="sku_pic" property="skuPic" jdbcType="VARCHAR"/>
        <result column="create_type" property="createType" jdbcType="INTEGER"/>
        <collection property="productLabelValueVos" ofType="net.summerfarm.model.vo.ProductLabelValueVo">
            <id column="id" property="id"/>
            <result column="sku" property="sku"/>
            <result column="label_id" property="labelId"/>
            <result column="label_value" property="labelValue"/>
            <result column="label_field" property="labelField"/>
            <result column="label_name" property="labelName"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        inv_id, sku, sku_name, ait_id, pd_id, origin, unit, pack, weight, production_date,
        storage_method, sale_price,
        promotion_price, maturity, after_sale_quantity,outdated,volume,weight_num ,
        type,sub_type,admin_id,base_sale_quantity,base_sale_unit,sample_pool, sku_pic, after_sale_unit,
        audit_status,audit_time,creator,ext_type,create_type,auditor,refuse_reason, is_domestic,
        net_weight_num,video_url,after_sale_rule_detail,buyer_name,buyer_id,net_weight_unit
    </sql>

    <select id="selectBySpu" resultMap="BySpuMap" parameterType="net.summerfarm.model.input.InventoryReq">
        SELECT
        i.inv_id,i.sku, i.sku_name, p.pd_name, i.origin, i.unit, i.pack, i.weight, i.production_date , ak.sku aksku,
        i.storage_method, i.base_sale_quantity, i.base_sale_unit, i.after_sale_unit,
        i.promotion_price , i.maturity, i.after_sale_quantity ,i.volume,i.weight_num,
        ak.id, ak.`original_price`, ak.`price`, ak.`ladder_price`, ak.`limited_quantity`, ak.`sales_mode`,
        ak.on_sale, i.ext_type extType,
        ak.area_no areaNo,ak.show,a.parent_no parentNo, a.area_name,ak.info,ak.m_type mType, ak.area_no areaNoFilter
        FROM inventory i
        LEFT JOIN products p on i.pd_id = p.pd_id
        LEFT JOIN area_sku ak on ak.sku = i.sku
        LEFT JOIN area a on a.area_no = ak.area_no
        <where>
            <if test="outdated != null">
                AND i.outdated= #{outdated}
            </if>
            <if test="pdId != null">
                AND p.pd_id = #{pdId}
            </if>
            <if test="sku != null">
                AND i.sku = #{sku}
            </if>
            <if test="areaNo != null">
                AND ak.area_no = #{areaNo}
            </if>
        </where>
    </select>

    <select id="selectVOBySpu" parameterType="net.summerfarm.model.input.InventoryReq"
            resultMap="SkuMap">
        SELECT
        i.inv_id ,i.sku,i.sku_name, p.pd_name , i.origin, i.unit, i.pack, i.weight, i.production_date ,
        i.storage_method , i.base_sale_quantity , i.base_sale_unit ,
        i.promotion_price , i.maturity, i.after_sale_quantity ,i.volume,i.weight_num ,
        a.name_remakes  ,a.admin_id  ,type,i.sub_type,i.sample_pool ,i.sku_pic , i.after_sale_unit ,i.outdated,
        i.audit_status , i.audit_time , i.creator, i.ext_type, p.real_name , i.create_remark ,
        i.pd_id ,i.weight_notes ,i.is_domestic ,i.average_price_flag,plv.label_id,
        plv.label_value,pl.label_field,pl.label_name,plv.id, i.create_type
        FROM inventory i
        LEFT JOIN products p on i.pd_id = p.pd_id
        LEFT JOIN admin a on a.admin_id = i.admin_id
        LEFT JOIN product_label_value plv on i.sku = plv.sku
        LEFT JOIN product_label pl on plv.label_id = pl.id
        <where>
            <if test="outdated != null">
                AND i.outdated= #{outdated}
            </if>
            <if test="pdId != null">
                AND p.pd_id = #{pdId}
            </if>
            <if test="sku != null">
                AND i.sku = #{sku}
            </if>
            <if test="auditStatus != null">
                AND i.audit_status = #{auditStatus}
            </if>
            <if test="extType != null">
                AND i.ext_type = #{extType}
            </if>
            <if test="samplePool != null">
                AND i.sample_pool = #{samplePool}
            </if>
        </where>
    </select>


    <select id="selectSkus" resultType="net.summerfarm.model.vo.SKUVO">
        SELECT  i.sku, p.pd_name pdName,i.weight weight, i.maturity, i.base_sale_unit baseSaleUnit, i.base_sale_quantity baseSaleQuantity,p.pd_id pdId,c.category,
        p.quality_time qualityTime,p.quality_time_unit qualityTimeUnit,i.volume,i.weight_num weightNum, i.type characters,p.pd_no pdNo,
        ad.name_remakes nameRemakes,p.category_id categoryId, i.ext_type extType,i.sku_pic skuPic,p.picture_path spuPic,c.type categoryType,i.type `type`,i.sub_type subType,
        i.is_domestic isDomestic
        <if test="areaNo != null">
            ,ar.quantity
        </if>
        FROM inventory i
        <if test="areaNo != null">
            INNER JOIN area_store ar ON i.sku = ar.sku
        </if>
        LEFT JOIN products p on i.pd_id =p.pd_id
        LEFT JOIN category c ON p.category_id=c.id
        left JOIN admin ad on ad.admin_id = i.admin_id
        where i.outdated=0
        <!-- 特殊逻辑，商城过滤saas仅自营品 -->
        and i.create_type != 3
        <if test="areaNo != null">
            AND ar.area_no = #{areaNo}
        </if>
        <if test="characters != null">
            AND i.type = #{characters}
        </if>
        <if test="categoryId != null">
            AND p.category_id = #{categoryId}
        </if>
    </select>

    <select id="selectList" parameterType="net.summerfarm.model.domain.Inventory" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM inventory
        <where>
            <!-- 特殊逻辑，商城过滤saas仅自营品 -->
            create_type != 3
            <if test="outdated != null">
                AND outdated =#{outdated}
            </if>
            <if test="pdId != null">
                AND pd_id = #{pdId}
            </if>
            <if test="auditStatus != null">
                AND audit_status = #{auditStatus}
            </if>
            <if test="extType != null">
                AND ext_type = #{extType}
            </if>
        </where>
    </select>

    <select id="selectByPdIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM inventory
        WHERE outdated = 0
        AND pd_id IN
        <foreach collection="pdIds" item="it" open="(" separator="," close=")">
            #{it}
        </foreach>
    </select>

    <select id="selectOne" resultMap="BaseResultMap" parameterType="net.summerfarm.model.domain.Inventory">
        select
        <include refid="Base_Column_List"/>
        FROM inventory
        <where>
            <if test="sku != null">
                AND sku =#{sku}
            </if>
            <if test="invId != null">
                AND inv_id =#{invId}
            </if>
            <if test="extType != null">
                AND ext_type = #{extType}
            </if>
        </where>
    </select>

    <select id="selectByIds" resultType="net.summerfarm.model.domain.Inventory" >
        select
        inv_id invId, sku, after_sale_unit afterSaleUnit, after_sale_quantity afterSaleQuantity, pd_id pdId, tenant_id tenantId
        FROM inventory
        where inv_id IN
        <foreach collection="invIds" item="it" open="(" separator="," close=")">
            #{it}
        </foreach>
    </select>

    <select id="countStoreStock" resultType="java.lang.Integer">
        select count(1)
        from area_store a
        left join inventory i on a.sku = i.sku
        <where>
            i.outdated = 0
            <if test="selectKeys.sku != null ">
                AND a.sku =#{selectKeys.sku}
            </if>
            <if test="skusByPdId != null and skusByPdId.size > 0">
                AND a.sku IN
                <foreach collection="skusByPdId" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
            <if test="selectKeys.areaNo != null ">
                AND a.area_no=#{selectKeys.areaNo}
            </if>
            <if test="selectKeys.areaNoList != null">
                AND a.area_no IN
                <foreach collection="selectKeys.areaNoList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="selectKeys.onSaleSkus != null and selectKeys.onSaleSkus.size() > 0">
                AND a.sku IN
                <foreach collection="selectKeys.onSaleSkus" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
            <if test="hasInventory != null and hasInventory == 1">
                and a.quantity > 0
            </if>
            <if test="hasInventory != null and hasInventory == 0">
                and a.quantity = 0
            </if>
        </where>
    </select>

    <select id="selectStoreStock" resultType="net.summerfarm.model.vo.StockVO"
            parameterType="net.summerfarm.model.vo.StockVO">
        select i.sku, p.pd_name productName,i.maturity, i.weight, i.ext_type extType,
        a.quantity storeQuantity,a.lock_quantity lockQuantity,a.road_quantity roadQuantity,a.area_no areaNo,p.pd_id pdId,p.picture_path picturePath,a.online_quantity shareQuantity,a.safe_quantity safeQuantity,p.storage_location storageLocation
        ,ad.name_remakes nameRemakes,a.reserve_max_quantity reserveMaxQuantity ,a.reserve_min_quantity reserveMinQuantity,
        if(a.reserve_max_quantity  > a.reserve_use_quantity + a.reserve_min_quantity,a.reserve_use_quantity, a.reserve_max_quantity - a.reserve_min_quantity) reserveUseQuantity,
        i.type skuType, i.is_domestic as isDomestic
        from inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN area_store a ON a.sku=i.sku
        LEFT join admin ad on ad.admin_id = i.admin_id
        LEFT JOIN category c ON p.category_id = c.id
        <where>
            i.outdated=0
            <if test="sku != null ">
                AND i.sku =#{sku}
            </if>
            <if test="productName != null">
                AND p.pd_name LIKE CONCAT('%',#{productName},'%')
            </if>
            <if test="areaNo != null ">
                AND a.area_no=#{areaNo}
            </if>
            <if test="areaNoList != null">
                AND a.area_no IN
                <foreach collection="areaNoList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="onSaleSkus != null and onSaleSkus.size() > 0">
                AND i.sku IN
                <foreach collection="onSaleSkus" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
            order by (case when c.type = 4 then 0 else 1 end) asc, i.pd_id desc
        </where>
    </select>

    <select id="selectStoreStockEx" resultType="net.summerfarm.model.vo.StockVO">
        select a.sku,
        a.quantity storeQuantity,a.lock_quantity lockQuantity,a.road_quantity roadQuantity,a.area_no areaNo,a.online_quantity shareQuantity,a.safe_quantity safeQuantity
        ,a.reserve_max_quantity reserveMaxQuantity ,a.reserve_min_quantity reserveMinQuantity,
        if(a.reserve_max_quantity  > a.reserve_use_quantity + a.reserve_min_quantity,a.reserve_use_quantity, a.reserve_max_quantity - a.reserve_min_quantity) reserveUseQuantity
        from area_store a
        left join inventory i on a.sku = i.sku
        <where>
            i.outdated = 0
            <if test="selectKeys.sku != null ">
                AND a.sku =#{selectKeys.sku}
            </if>
            <if test="skusByPdId != null and skusByPdId.size > 0">
                AND a.sku IN
                <foreach collection="skusByPdId" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
            <if test="selectKeys.areaNo != null ">
                AND a.area_no=#{selectKeys.areaNo}
            </if>
            <if test="selectKeys.areaNoList != null">
                AND a.area_no IN
                <foreach collection="selectKeys.areaNoList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="selectKeys.onSaleSkus != null and selectKeys.onSaleSkus.size() > 0">
                AND a.sku IN
                <foreach collection="selectKeys.onSaleSkus" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
            <if test="hasInventory != null and hasInventory == 1">
                and a.quantity > 0
            </if>
            <if test="hasInventory != null and hasInventory == 0">
                and a.quantity = 0
            </if>
        </where>
    </select>

    <select id="selectStoreStockV1" resultType="net.summerfarm.model.vo.StockVO"
            parameterType="net.summerfarm.model.vo.StockVO">
        select i.sku, p.pd_name productName,i.maturity, i.weight, i.ext_type extType,
        a.quantity storeQuantity,a.lock_quantity lockQuantity,a.road_quantity roadQuantity,a.area_no areaNo,p.pd_id pdId,p.picture_path picturePath,a.online_quantity shareQuantity,a.safe_quantity safeQuantity,p.storage_location storageLocation
        ,ad.name_remakes nameRemakes,a.reserve_max_quantity reserveMaxQuantity ,a.reserve_min_quantity reserveMinQuantity,
        if(a.reserve_max_quantity  > a.reserve_use_quantity + a.reserve_min_quantity,a.reserve_use_quantity, a.reserve_max_quantity - a.reserve_min_quantity) reserveUseQuantity,
        i.type skuType, i.is_domestic as isDomestic
        from inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN area_store a ON a.sku=i.sku
        LEFT join admin ad on ad.admin_id = i.admin_id
        LEFT JOIN category c ON p.category_id = c.id
        <where>
            i.outdated=0
            <if test="selectKeys.sku != null ">
                AND i.sku =#{selectKeys.sku}
            </if>
            <if test="selectKeys.productName != null">
                AND p.pd_name LIKE CONCAT('%',#{selectKeys.productName},'%')
            </if>
            <if test="selectKeys.areaNo != null ">
                AND a.area_no=#{selectKeys.areaNo}
            </if>
            <if test="selectKeys.areaNoList != null">
                AND a.area_no IN
                <foreach collection="selectKeys.areaNoList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="selectKeys.onSaleSkus != null and selectKeys.onSaleSkus.size() > 0">
                AND i.sku IN
                <foreach collection="selectKeys.onSaleSkus" item="it" open="(" separator="," close=")">
                    #{it}
                </foreach>
            </if>
            <if test="hasInventory != null and hasInventory == 1">
                and a.quantity > 0
            </if>
            <if test="hasInventory != null and hasInventory == 0">
                and a.quantity = 0
            </if>
            order by (case when c.type = 4 then 0 else 1 end) asc, i.pd_id desc
        </where>
    </select>

    <select id="selectBySkuAndAreaNo" resultType="net.summerfarm.model.vo.InventoryVO">
    SELECT
    i.inv_id invId, i.sku, i.ait_id aitId, p.pd_name productName, i.origin, i.unit, i.pack, i.weight, i.production_date productionDate, i.type,i.sku_pic skuPic,
    if(a.share=0,a.quantity,ar.online_quantity) quantity, i.storage_method storageMethod,
    IFNULL(a.price,i.sale_price) salePrice, a.share,
    i.promotion_price promotionPrice, a.on_sale onSale, a.priority, a.sales_mode salesMode, a.limited_quantity limitedQuantity,a.m_type mType,i.introduction, i.maturity, i.after_sale_quantity afterSaleQuantity,
     p.storage_location storageLocation,p.picture_path picturePath,p.pd_name  productName,p.pd_id pdId, i.ext_type extType,p.quality_time qualityTime,p.quality_time_unit qualityTimeUnit
    FROM inventory i LEFT JOIN products p on i.pd_id = p.pd_id
    LEFT JOIN area_sku a on i.sku = a.sku AND a.area_no = #{areaNo}
    LEFT JOIN area area on a.area_no=area.area_no
    left join (
        select store_no ,area_no from fence where area_no = #{areaNo} and status = 0 limit 1
    ) fe   on fe.area_no = a.area_no
    Left join warehouse_inventory_mapping wim on wim.sku = i.sku and fe.store_no = wim.store_no
    LEFT JOIN area_store ar on ar.sku=i.sku and wim.warehouse_no = ar.area_no
    WHERE  i.sku = #{sku,jdbcType=VARCHAR}
    AND i.outdated = 0
  </select>

    <!--根据条件查询数量-->
    <select id="count" resultType="int" parameterType="hashmap">
        SELECT COUNT(1) FROM inventory
        <where>
            <if test="invId != null">
                AND inv_id = #{invId,jdbcType=BIGINT}
            </if>
            <if test="sku != null">
                AND sku = #{sku,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!--根据条件查询-->
    <select id="select" resultType="net.summerfarm.model.vo.InventoryVO"
            parameterType="net.summerfarm.model.input.InventoryReq">
        SELECT
        i.inv_id invId, i.sku,p.pd_id pdId, p.pd_name productName, ifnull(i.sku_pic, p.picture_path) picturePath,
        i.origin, i.unit, i.pack, i.weight, i.production_date productionDate,
        i.storage_method storageMethod,
        IFNULL(ak.price,i.sale_price) salePrice,c.id categoryId,c.category categoryName,
        i.promotion_price promotionPrice, ak.on_sale onSale, ak.priority,ak.sales_mode salesMode, ak.limited_quantity
        limitedQuantity,
        i.maturity, i.after_sale_quantity afterSaleQuantity, ak.ladder_price ladderPrice, ak.area_no areaNo,
        i.volume,i.weight_num weightNum,i.sku_pic skuPic, i.ext_type extType

        FROM inventory i
        LEFT JOIN products p on i.pd_id = p.pd_id
        left join  category c on c.id=p.category_id
        LEFT JOIN area_sku ak on ak.sku = i.sku
        <where>
            <!-- 特殊逻辑，商城过滤saas仅自营品 -->
            i.create_type != 3
            <if test="outdated != null">
                AND i.outdated= #{outdated}
            </if>
            <if test="salesMode != null">
                AND i.sales_mode = #{salesMode}
            </if>
            <if test="sku !=null">
                AND i.sku LIKE CONCAT('%',#{sku},'%')
            </if>
            <if test="type != null">
                AND i.type = #{type}
            </if>
            <if test="pdName != null">
                AND p.pd_name LIKE CONCAT('%',#{pdName},'%')
            </if>
            <if test="supplierVisible != null">
                AND i.supplier_visible = #{supplierVisible}
            </if>
            <if test="pdId != null">
                AND p.pd_id = #{pdId}
            </if>
            <if test="onSale != null">
                AND ak.on_sale = #{onSale}
            </if>
            <if test="extType != null">
                AND i.ext_type = #{extType}
            </if>
            <if test="mType != null">
                AND ak.m_type = #{mType}
            </if>
            <if test="areaNoList != null">
                AND ak.area_no in
                <foreach collection="areaNoList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="categoryList != null">
                AND  p.category_id in
                <foreach collection="categoryList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="categoryTypeList != null">
                AND  c.type in
                <foreach collection="categoryTypeList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
         group by i.inv_id order by ak.priority
    </select>

    <select id="selectSkuList" parameterType="net.summerfarm.model.input.InventoryReq"
            resultType="net.summerfarm.model.vo.InventoryVO">
        SELECT t.sku, t.pdName, t.weight, a3.quantity, a3.area_no storeNo
        <if test="onSale != null">
            , t.onSale
        </if>
        FROM (
        SELECT i.sku,p.pd_name pdName,i.weight,ar.quantity,ar.area_no storeNo
        <if test="onSale != null">
            <!-- 一个上架即为上架中.... -->
            ,max(ak.on_sale) onSale
        </if>
        FROM inventory i
        LEFT JOIN products p on i.pd_id = p.pd_id
        left join category c on c.id=p.category_id
        LEFT JOIN area_store ar ON i.sku=ar.sku
        <if test="onSale != null">
            LEFT JOIN warehouse_inventory_mapping wim on wim.warehouse_no = ar.area_no and ar.sku = wim.sku
            LEFT JOIN fence f on f.store_no = wim.store_no
            Left Join area a on a.area_no = f.area_no
            LEFT JOIN area_sku ak ON a.area_no = ak.area_no AND ar.sku = ak.sku
        </if>
        WHERE i.outdated=0
        <!-- 特殊逻辑，商城过滤saas仅自营品 -->
        and i.create_type != 3
        and ar.area_no = #{storeNo}
        <if test="categoryList != null">
            and p.category_id in
            <foreach collection="categoryList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY i.sku
        ) t
        LEFT JOIN area_store a3 ON t.sku = a3.sku
        WHERE a3.area_no = #{storeNo}
    </select>

    <!--根据sku更新sku信息-->
    <update id="update" parameterType="net.summerfarm.model.domain.Inventory">
        update inventory
        <set>
            <if test="aitId != null">
                ait_id = #{aitId,jdbcType=INTEGER},
            </if>
            <if test="pdId != null">
                pd_id = #{pdId,jdbcType=BIGINT},
            </if>
            <if test="skuName != null">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="origin != null">
                origin = #{origin,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="pack != null">
                pack = #{pack,jdbcType=VARCHAR},
            </if>
            <if test="weight != null">
                weight = #{weight,jdbcType=VARCHAR},
            </if>
            <if test="productionDate != null">
                production_date = #{productionDate,jdbcType=DATE},
            </if>
            <if test="storageMethod != null">
                storage_method = #{storageMethod,jdbcType=VARCHAR},
            </if>
            <if test="salePrice != null">
                sale_price = #{salePrice,jdbcType=DECIMAL},
            </if>
            <if test="promotionPrice != null">
                promotion_price = #{promotionPrice,jdbcType=DECIMAL},
            </if>
            <if test="introduction != null">
                introduction = #{introduction},
            </if>
            <if test="salesMode != null">
                sales_mode = #{salesMode},
            </if>
            <if test="maturity != null">
                maturity = #{maturity},
            </if>
            <if test="outdated != null">
                outdated = #{outdated},
            </if>
            <if test="afterSaleQuantity != null">
                after_sale_quantity = #{afterSaleQuantity},
            </if>
            <if test="baseSaleQuantity != null">
                base_sale_quantity = #{baseSaleQuantity},
            </if>
            <if test="baseSaleUnit != null">
                base_sale_unit = #{baseSaleUnit},
            </if>
            <if test="volume != null">
                volume = #{volume},
            </if>
            <if test="weightNum != null">
                weight_num = #{weightNum},
            </if>
            <if test="type != null">
                type = #{type} ,
            </if>
            <if test="adminId != null">
                admin_id = #{adminId},
            </if>
            <if test="samplePool != null">
                sample_pool = #{samplePool},
            </if>
            <if test="skuPic != null and skuPic != ''">
                sku_pic = #{skuPic},
            </if>
            <if test="afterSaleUnit != null and afterSaleUnit != ''">
                after_sale_unit = #{afterSaleUnit},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime},
            </if>
            <if test="creator != null">
                creator = #{creator},
            </if>
            <if test="extType != null">
                ext_type = #{extType},
            </if>
            <if test="createRemark != null">
                create_remark = #{createRemark},
            </if>
            <if test="auditor != null">
                auditor = #{auditor},
            </if>
            <if test="supplierVisible != null">
                supplier_visible = #{supplierVisible},
            </if>
            <if test="isDomestic != null">
                is_domestic = #{isDomestic},
            </if>
            <if test="averagePriceFlag != null">
                average_price_flag = #{averagePriceFlag},
            </if>
            <if test="refuseReason != null">
                refuse_reason = #{refuseReason},
            </if>
            <if test="createType != null">
                create_type = #{createType},
            </if>
            <if test="taskType != null">
                task_type = #{taskType},
            </if>
            <if test="subType!=null">
                sub_type=#{subType}
            </if>
        </set>
        where sku = #{sku,jdbcType=VARCHAR}
    </update>

    <update id="updateByPdId">
        update inventory
        SET outdated= #{outdated}
        where pd_id IN
        <foreach collection="array" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="deleteAll">
        update inventory
        SET outdated=1
        where sku IN
        <foreach collection="array" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="invId"
            parameterType="net.summerfarm.model.domain.Inventory">
        insert into inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sku != null">
                sku,
            </if>
            <if test="skuName != null">
                sku_name,
            </if>
            <if test="createType != null">
                create_type,
            </if>
            <if test="aitId != null">
                ait_id,
            </if>
            <if test="pdId != null">
                pd_id,
            </if>
            <if test="origin != null">
                origin,
            </if>
            <if test="unit != null">
                unit,
            </if>
            <if test="pack != null">
                pack,
            </if>
            <if test="weight != null">
                weight,
            </if>
            <if test="productionDate != null">
                production_date,
            </if>
            <if test="storageMethod != null">
                storage_method,
            </if>
            <if test="salePrice != null">
                sale_price,
            </if>
            <if test="promotionPrice != null">
                promotion_price,
            </if>
            <if test="salesMode != null">
                sales_mode,
            </if>
            <if test="maturity != null">
                maturity,
            </if>
            <if test="afterSaleQuantity != null">
                after_sale_quantity,
            </if>
            <if test="baseSaleQuantity != null">
                base_sale_quantity,
            </if>
            <if test="baseSaleUnit != null">
                base_sale_unit,
            </if>
            <if test="volume != null">
                volume,
            </if>
            <if test="weightNum != null">
                weight_num,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="skuPic != null and skuPic != ''">
                sku_pic,
            </if>
            <if test="afterSaleUnit != null and afterSaleUnit != ''">
                after_sale_unit,
            </if>
            <if test="auditStatus != null">
                audit_status,
            </if>
            <if test="auditTime != null">
                audit_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="extType != null">
                ext_type,
            </if>
            <if test="createRemark != null">
                create_remark,
            </if>
            <if test="outdated != null">
                outdated,
            </if>
            <if test="taskType != null">
                task_type,
            </if>
            <if test="weightNotes != null">
                weight_notes,
            </if>
            <if test="supplierVisible != null">
                supplier_visible,
            </if>
            <if test="isDomestic != null">
                is_domestic,
            </if>
            <if test="averagePriceFlag != null">
                average_price_flag,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="subType != null">
                sub_type,
            </if>
            <if test="samplePool != null">
                sample_pool,
            </if>
            <if test="netWeightNum != null" >
                net_weight_num,
            </if>
            <if test="netWeightUnit != null" >
                net_weight_unit,
            </if>
            <if test="videoUrl != null" >
                video_url,
            </if>
            <if test="videoUploadUser != null" >
                video_upload_user,
            </if>
            <if test="videoUploadTime != null" >
                video_upload_time,
            </if>
            <if test="afterSaleRuleDetail != null" >
                after_sale_rule_detail,
            </if>
            <if test="buyerName != null" >
                buyer_name,
            </if>
            <if test="buyerId != null" >
                buyer_id,
            </if>
            <if test="quoteType != null" >
                quote_type,
            </if>
            <if test="minAutoAfterSaleThreshold != null" >
                min_auto_after_sale_threshold,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="skuName != null">
                #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="createType != null">
                #{createType,jdbcType=INTEGER},
            </if>
            <if test="aitId != null">
                #{aitId,jdbcType=INTEGER},
            </if>
            <if test="pdId != null">
                #{pdId,jdbcType=BIGINT},
            </if>
            <if test="origin != null">
                #{origin,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
            <if test="pack != null">
                #{pack,jdbcType=VARCHAR},
            </if>
            <if test="weight != null">
                #{weight,jdbcType=VARCHAR},
            </if>
            <if test="productionDate != null">
                #{productionDate,jdbcType=DATE},
            </if>
            <if test="storageMethod != null">
                #{storageMethod,jdbcType=VARCHAR},
            </if>
            <if test="salePrice != null">
                #{salePrice,jdbcType=DECIMAL},
            </if>
            <if test="promotionPrice != null">
                #{promotionPrice,jdbcType=DECIMAL},
            </if>
            <if test="salesMode != null">
                #{salesMode},
            </if>
            <if test="maturity != null">
                #{maturity},
            </if>
            <if test="afterSaleQuantity != null">
                #{afterSaleQuantity},
            </if>
            <if test="baseSaleQuantity != null">
                #{baseSaleQuantity},
            </if>
            <if test="baseSaleUnit != null">
                #{baseSaleUnit},
            </if>
            <if test="volume != null">
                #{volume},
            </if>
            <if test="weightNum != null">
                #{weightNum} ,
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="skuPic != null and skuPic != ''">
                #{skuPic},
            </if>
            <if test="afterSaleUnit != null and afterSaleUnit != ''">
                #{afterSaleUnit},
            </if>
            <if test="auditStatus != null">
                #{auditStatus},
            </if>
            <if test="auditTime != null">
                #{auditTime},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="extType != null">
                #{extType},
            </if>
            <if test="createRemark != null">
                #{createRemark},
            </if>
            <if test="outdated != null">
                #{outdated},
            </if>
            <if test="taskType != null">
                #{taskType},
            </if>
            <if test="weightNotes != null">
                #{weightNotes},
            </if>
            <if test="supplierVisible != null">
                #{supplierVisible},
            </if>
            <if test="isDomestic != null">
                #{isDomestic},
            </if>
            <if test="averagePriceFlag != null">
                #{averagePriceFlag},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="subType != null">
                #{subType},
            </if>
            <if test="samplePool != null">
                #{samplePool},
            </if>
            <if test="netWeightNum != null" >
                #{netWeightNum,jdbcType=DECIMAL},
            </if>
            <if test="netWeightUnit != null" >
                #{netWeightUnit,jdbcType=VARCHAR},
            </if>
            <if test="videoUrl != null" >
                #{videoUrl,jdbcType=VARCHAR},
            </if>
            <if test="videoUploadUser != null" >
                #{videoUploadUser,jdbcType=VARCHAR},
            </if>
            <if test="videoUploadTime != null" >
                #{videoUploadTime},
            </if>
            <if test="afterSaleRuleDetail != null" >
                #{afterSaleRuleDetail,jdbcType=VARCHAR},
            </if>
            <if test="buyerName != null" >
                #{buyerName,jdbcType=VARCHAR},
            </if>
            <if test="buyerId != null" >
                #{buyerId,jdbcType=BIGINT},
            </if>
            <if test="quoteType != null" >
                #{quoteType},
            </if>
            <if test="minAutoAfterSaleThreshold != null" >
                #{minAutoAfterSaleThreshold},
            </if>
        </trim>
    </insert>

    <select id="selectSkuType" resultType="net.summerfarm.model.vo.InventoryVO">
        SELECT i.sku,i.create_type createType,p.pd_name pdName, p.real_name realName,i.weight,i.type,p.category_id categoryId,c.type categoryType,i.volume,i.weight_num weightNum,ad.name_remakes nameRemakes,i.pd_id pdId,i.origin,i.is_domestic isDomestic,
        i.base_sale_quantity baseSaleQuantity ,i.base_sale_unit baseSaleUnit,p.storage_location storageLocation,i.sample_pool samplePool,i.ext_type extType,i.sub_type subType,
        p.quality_time qualityTime,p.quality_time_unit qualityTimeUnit,i.sku_pic skuPic,p.picture_path picturePath,c.category categoryName,i.pack,i.unit,p.quality_time_type qualityTimeType,
        i.video_url videoUrl, i.video_upload_user videoUploadUser, i.video_upload_time videoUploadTime, p.detail_picture as detailPicture
        FROM inventory i
        LEFT JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN category c ON p.category_id=c.id
        LEFT JOIN admin ad on ad.admin_id = i.admin_id
        WHERE i.outdated = 0 and i.sku = #{sku}
    </select>

    <select id="selectByPdId" resultType="net.summerfarm.model.domain.Inventory">
        SELECT inv_id invId, sku, ait_id, pd_id pdId, origin, unit, pack, weight, production_date productionDate,storage_method storageMethod, sale_price salePrice,
          promotion_price promotionPrice, maturity, after_sale_quantity afterSaleQuantity,outdated, buyer_id buyerId, sub_type subType
        FROM inventory
        WHERE pd_id = #{pdId,jdbcType=INTEGER}
    </select>
    <select id="selectNameRemakes" resultType="string">
        select a.name_remakes
        from admin a
                 right join inventory i on a.admin_id = i.admin_id
        where i.type = 1 and i.sku = #{sku}
    </select>


    <select id="selectSkusByType" resultType="net.summerfarm.model.vo.SKUVO">
        SELECT  i.sku, i.admin_id adminId ,p.pd_name pdName,i.weight weight, i.maturity, i.base_sale_unit baseSaleUnit, i.base_sale_quantity baseSaleQuantity, i.type, p.pd_id pdId,c.category,
        p.quality_time qualityTime,p.quality_time_unit qualityTimeUnit ,i.type characters,ad.name_remakes nameRemakes, i.ext_type extType
        FROM inventory i
        LEFT JOIN products p on i.pd_id =p.pd_id
        LEFT JOIN category c ON p.category_id=c.id
        LEFT JOIN  admin ad on ad.admin_id = i.admin_id
        where i.outdated=0 and i.type = #{type}
        <if test="adminId != null">
            and i.admin_id = #{adminId}
        </if>

    </select>

    <select id="queryBySku" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM inventory i
        where i.outdated=0  and i.sku = #{sku}
    </select>

    <select id="queryBySkus"  resultType="net.summerfarm.model.vo.InventoryVO">
        SELECT  i.sku ,i.type,i.admin_id adminId ,ad.name_remakes nameRemakes, i.ext_type extType
        FROM inventory i
        left JOIN  admin ad on ad.admin_id = i.admin_id
        where i.sku IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryWeightNumBySkus"  resultType="net.summerfarm.model.vo.InventoryVO">
        SELECT  sku, weight_num weightNum
        FROM inventory
        where sku IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectSkuByCategoryType" resultType="string">
        select i.sku from inventory i
        left join products p on i.pd_id = p.pd_id
        left join category c on p.category_id = c.id
        where i.outdated = 0 and c.outdated = 0 and c.type = #{type} and c.id not in (723,724,726,727,728,729,731)
    </select>
    <select id="isFruit" resultType="boolean" parameterType="string">
        select c.type = 4 from inventory i
        left join products p on i.pd_id = p.pd_id
        left join category c on p.category_id = c.id
        where i.sku = #{sku}
    </select>

    <select id="selectInventoryVOBySku" resultType="net.summerfarm.model.vo.InventoryVO">
        SELECT i.sku,p.pd_name pdName,i.weight,i.type,p.category_id categoryId,i.volume,i.weight_num weightNum,p.pd_no pdNo,
               ad.name_remakes nameRemakes,i.pd_id pdId,i.origin, p.warn_time warnTime, i.ext_type extType,i.is_domestic isDomestic,
               p.quality_time qualityTime,p.quality_time_unit qualityTimeUnit,c.type categoryType,p.storage_location storageLocation, i.inv_id invId,i.unit,i.tenant_id as tenantId, i.sub_type as subType
        FROM inventory i
        LEFT JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN category c ON p.category_id=c.id
        LEFT JOIN admin ad on ad.admin_id = i.admin_id
        WHERE i.sku = #{sku}
    </select>
    <select id="selectSampleSku" resultType="net.summerfarm.model.vo.InventoryVO">

        SELECT
        i.inv_id invId, i.sku,p.pd_id pdId, p.pd_name productName, p.picture_path picturePath,
        i.origin, i.unit, i.pack, i.weight, i.production_date productionDate,
        i.storage_method storageMethod,
        i.sale_price salePrice,c.id categoryId,c.category categoryName,
        i.promotion_price promotionPrice,
        i.maturity, i.after_sale_quantity afterSaleQuantity,
        i.volume,i.weight_num weightNum,i.sku_pic skuPic, i.ext_type extType
        FROM inventory i
        LEFT JOIN products p on i.pd_id = p.pd_id
        left join  category c on c.id=p.category_id
        where i.outdated = 0 and i.sample_pool = 1
    </select>

    <select id="selectSampleSkuByArea"  parameterType="java.lang.Integer" resultType="net.summerfarm.model.vo.InventoryVO">

        SELECT
        i.inv_id invId, i.sku,p.pd_id pdId, p.pd_name pdName, p.picture_path picturePath,
        i.origin, i.unit, i.pack, i.weight, i.production_date productionDate,
        i.storage_method storageMethod,
        i.sale_price salePrice,c.id categoryId,c.category categoryName,
        i.promotion_price promotionPrice,
        i.maturity, i.after_sale_quantity afterSaleQuantity,
        i.volume,i.weight_num weightNum,i.sku_pic skuPic,
        ars.online_quantity quantity, i.ext_type extType
        FROM inventory i
        LEFT JOIN products p on i.pd_id = p.pd_id
        left join  category c on c.id=p.category_id
        left join warehouse_inventory_mapping wim on wim.sku = i.sku
        left join area_store ars on i.sku = ars.sku and wim.warehouse_no = ars.area_no
        where  wim.store_no = #{areaNo} and i.outdated = 0 and i.sample_pool = 1
        <!-- 特殊逻辑，商城过滤saas仅自营品 -->
        and i.create_type != 3
        <if test="queryStr != null">
            and (i.sku like concat('%', #{queryStr}, '%') or p.pd_name like concat('%', #{queryStr}, '%'))
        </if>
    </select>

    <select id="selectNullAfterSaleUnit" resultType="net.summerfarm.model.vo.InventoryVO">
      select inv_id invId, pd_id pdId, sku
      from inventory
      where after_sale_unit is null
      limit 200
    </select>

    <update id="updateAfterSaleUnit" parameterType="net.summerfarm.model.vo.InventoryVO">
        update inventory
        set after_sale_unit = #{afterSaleUnit}
        where sku = #{sku}
    </update>

    <select id="queryPdNameBySkus" resultType="net.summerfarm.model.vo.InventoryVO">
        SELECT i.sku sku, p.pd_name productName, i.weight, i.type,i.sub_type subType, p.storage_location storageLocation, p.quality_time qualityTime, p.quality_time_unit qualityTimeUnit, p.pd_id pdId, i.unit, p.storage_method storageMethod
        FROM inventory i
        INNER JOIN products p ON i.pd_id = p.pd_id
        WHERE i.sku IN
        <foreach collection="skus" item="it" open="(" separator="," close=")">
            #{it}
        </foreach>
        AND p.outdated = 0
        AND i.outdated = 0
    </select>

    <select id="selectSkuWMSInfo" resultType="net.summerfarm.model.domain.InventoryWMSInfo">
        SELECT i.sku sku, i.unit packing, p.storage_location storageLocation,
               concat(gc.category,'-',pc.category,'-', c.category) secondLevelCategory, c.type categoryType
        FROM inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN category c ON p.category_id = c.id
        left join category pc on c.parent_id = pc.id
        left join category gc on pc.parent_id = gc.id
        WHERE i.sku = #{sku}
    </select>

    <select id="batchSelectSkuWMSInfo" parameterType="list" resultType="net.summerfarm.model.domain.InventoryWMSInfo">
        SELECT i.sku sku, i.unit packing, p.storage_location storageLocation,
        concat(gc.category,'-',pc.category,'-',c.category) secondLevelCategory, c.type categoryType, i.inv_id skuId
        FROM inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        LEFT JOIN category c ON p.category_id = c.id
        LEFT JOIN category pc ON pc.id = c.parent_id
        LEFT JOIN category gc ON gc.id = pc.parent_id
        WHERE i.sku IN
        <foreach collection="list" item="it" open="(" separator="," close=")">
            #{it}
        </foreach>
    </select>

    <select id="selectSkuBaseInfosBySku" resultType="net.summerfarm.model.DTO.purchase.SkuBaseInfoDTO">
        select
               i.inv_id invId, i.sku, i.pd_id pdId, p.pd_name pdName, ifnull(i.sku_pic, p.picture_path) picturePath, i.weight, i.volume, i.weight_num weightNum,
               p.storage_location storageArea, i.unit packaging, p.category_id categoryId, i.type pdAttribute, i.ext_type extType, i.outdated, i.admin_id adminId,
               i.sku_name skuName, i.sku_pic skuPic, i.is_domestic isDomestic,p.quality_time as qualityTime,p.quality_time_unit as qualityTimeUnit
        from
             inventory i left join products p on i.pd_id = p.pd_id
        where
              i.sku in
        <foreach collection="skus" open="(" separator="," close=")" item="el">
            #{el}
        </foreach>

    </select>

    <select id="selectBDGmvExcludeSku" resultType="java.lang.String">
        SELECT i.sku
        FROM inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        WHERE p.pd_no IN (52, 56)
    </select>
    <select id="selectSkuByAddTime" resultType="net.summerfarm.model.vo.InventoryVO">
        select i.sku,p.category_id categoryId, p.product_name productName from inventory i
        left join products p on p.pd_id = i.pd_id
        where  i.add_time >= #{startTime} and i <![CDATA[<]]> #{endTime}
    </select>
    <select id="selectSkuInfo" resultType="net.summerfarm.model.vo.InventoryVO">
        select i.sku, p.pd_name pdName, i.weight, i.pd_id pdId
        from inventory i
        left join products p on i.pd_id = p.pd_id
        where
            p.outdated = 0
        AND i.outdated = 0
    </select>
    <select id="selectOutdatedSku" resultType="net.summerfarm.model.vo.InventoryVO">
        select i.sku, p.pd_name pdName, i.weight
        from inventory i
        left join products p on i.pd_id = p.pd_id
        where
            i.outdated = 1
    </select>

    <select id="match2SkuOrName" resultType="net.summerfarm.model.vo.InventoryVO">
        select i.sku,p.pd_name productName,i.weight,ifnull(i.sku_pic, p.picture_path) picturePath,p.pd_no pdNo,
        i.type ,p.quality_time qualityTime, p.quality_time_unit qualityTimeUnit, ppv.products_property_value brand
        from inventory i
        left join products p on i.pd_id = p.pd_id
        left join products_property_value ppv on ppv.pd_id = p.pd_id and ppv.products_property_id = 2
        where i.outdated=0 and (p.pd_name like concat('%',#{queryStr},'%') or i.sku like concat('%',#{queryStr},'%') )
        <if test="skus != null and skus.size() > 0">
            and i.sku in
            <foreach collection="skus" open="(" separator="," close=")" item="el">
                #{el}
            </foreach>
        </if>
    </select>

    <select id="skuQuery" resultType="net.summerfarm.model.vo.InventoryVO">
        select  i.inv_id invId, i.sku,p.pd_id pdId, p.pd_name pdName, ifnull(i.sku_pic, p.picture_path) picturePath,
        i.origin, i.unit, i.pack, i.weight, i.production_date productionDate,
        i.storage_method storageMethod,
        i.sale_price salePrice,c.id categoryId,c.category categoryName,
        i.promotion_price promotionPrice,
        i.maturity, i.after_sale_quantity afterSaleQuantity,
        i.volume,i.weight_num weightNum,ifnull(i.sku_pic, p.picture_path) picturePath, i.ext_type extType
        from inventory i
        left join products p on i.pd_id = p.pd_id
        left join category c on p.category_id = c.id
        where i.outdated = 0 and p.outdated = 0
        <!-- 特殊逻辑，商城过滤saas仅自营品 -->
        and i.create_type != 3
        <if test="sku != null">
            and i.sku = #{sku}
        </if>
        <if test="pdName != null">
            and p.pd_name like concat('%', #{pdName},'%')
        </if>
        <if test="categoryId != null">
            and p.category_id = #{categoryId}
        </if>
        <if test="parentCategoryId != null">
            and c.parent_id = #{parentCategoryId}
        </if>
        <if test="categoryIdList != null and categoryIdList.size() > 0">
            and p.category_id in
            <foreach collection="categoryIdList" open="(" separator="," close=")" item="el">
                #{el}
            </foreach>
        </if>
        <if test="parentCategoryIdList != null and parentCategoryIdList.size() > 0">
            and c.parent_id in
            <foreach collection="parentCategoryIdList" open="(" separator="," close=")" item="el">
                #{el}
            </foreach>
        </if>
    </select>


    <select id="selectStockNew" resultType="net.summerfarm.model.vo.StockVO"
            parameterType="net.summerfarm.model.vo.StockVO">

        select distinct i.sku, p.pd_name productName,ifnull(i.sku_pic, p.picture_path) picturePath ,
        ar.online_quantity shareQuantity,
        ar.area_no areaNo,
        ar.lock_quantity  lockQuantity,
        ar.road_quantity roadQuantity,
        ar.quantity  storeQuantity,
        p.pd_id pdId,i.weight,i.ext_type extType,
        ar.safe_quantity safeQuantity,
        ar.sync,ar.support_reserved supportReserved,
        ad.name_remakes nameRemakes,
        IFNULL(ar.quantity ,0) - IFNULL(ar.lock_quantity,0) - IFNULL(ar.safe_quantity,0) sellableStore,
        ar.reserve_max_quantity reserveMaxQuantity,
        ar.reserve_min_quantity  reserveMinQuantity,
        if(ar.reserve_max_quantity - ar.reserve_min_quantity > ar.reserve_use_quantity,ar.reserve_use_quantity,ar.reserve_max_quantity - ar.reserve_min_quantity) reserveUseQuantity
        from inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        inner JOIN area_store ar on ar.sku = i.sku
        Left join warehouse_inventory_mapping wim on wim.warehouse_no = ar.area_no and ar.sku = wim.sku
        LEFT JOIN admin ad on ad.admin_id = i.admin_id
        <where>
            i.outdated=0
            <if test="sku != null ">
                AND i.sku LIKE CONCAT('%',#{sku},'%')
            </if>
            <if test="productName != null">
                AND p.pd_name LIKE CONCAT('%',#{productName},'%')
            </if>
            <if test="selectSync != null">
                AND ar.sync = #{selectSync}
            </if>
            <if test="supportReserved != null">
                AND ar.support_reserved = #{supportReserved}
            </if>
            <if test="areaNo !=null">
                AND ar.area_no = #{areaNo}
            </if>
            <if test="storeNo !=null">
                AND wim.store_no = #{storeNo}
            </if>
            <if test="categoryId != null">
                AND p.category_id IN (SELECT id FROM category WHERE parent_id=#{categoryId} OR id=#{categoryId})
            </if>
            <if test="minSellableStore != null">
                AND ar.quantity <![CDATA[>=]]> ar.lock_quantity + ar.safe_quantity + #{minSellableStore}
            </if>
            <if test="maxSellableStore != null">
                AND  ar.quantity <![CDATA[<=]]> ar.lock_quantity +  ar.safe_quantity + #{maxSellableStore}
            </if>
        </where>
        /*todo*/
        <if test="orderBy != null ">
            order by ${orderBy}
        </if>
        <if test="orderBy == null ">
            order by shareQuantity desc
        </if>
    </select>

    <select id="selectFruit" resultType="net.summerfarm.model.domain.StockBoard">
        select  distinct i.sku,ar.area_no warehouseNo
        from  inventory i
                  left join products p on i.pd_id = p.pd_id
                  inner join category c on p.category_id = c.id
                  left join area_store ar on ar.sku=i.sku
                  INNER join warehouse_inventory_mapping wim on ar.area_no = wim.warehouse_no and ar.sku=wim.sku
                  inner join (
            select area_no,store_no from fence where status = 0
            group by area_no,store_no
        ) f on f.store_no = wim.store_no
                  INNER JOIN area_sku ak ON f.area_no = ak.area_no and i.sku=ak.sku
        where ak.on_sale=1 and p.outdated=0 and i.outdated=0 and c.type=4;
    </select>

    <!--根据条件查询-->
    <select id="selectBySku" resultType="net.summerfarm.model.vo.InventoryVO"
            parameterType="net.summerfarm.model.input.InventoryReq">
        SELECT p.storage_location storageLocation
        FROM inventory i
        LEFT JOIN products p on i.pd_id = p.pd_id
        left join  category c on c.id=p.category_id
        <where>
            <if test="sku !=null">
                AND i.sku LIKE CONCAT('%',#{sku},'%')
            </if>
            <if test="invId !=null">
                AND i.inv_id =#{invId}
            </if>
        </where>
    </select>

    <select id="selectBySkuList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from inventory
        where sku in
        <foreach collection="skuList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="matchSkuOrName" resultType="net.summerfarm.model.vo.InventoryVO">
        select i.sku,p.pd_name productName,i.weight,ifnull(i.sku_pic, p.picture_path) picturePath,p.pd_no pdNo,i.is_domestic isDomestic, i.weight_num weightNum, i.volume volume,
        i.type ,p.quality_time qualityTime, p.quality_time_unit qualityTimeUnit, ppv.products_property_value brand, i.ext_type extType,p.storage_location storageLocation,i.pd_id pdId,p.category_id categoryId,
        i.sku_name skuName, i.sku_pic skuPic,i.create_type createType
        from inventory i
        left join products p on i.pd_id = p.pd_id
        left join products_property_value ppv on ppv.pd_id = p.pd_id and ppv.products_property_id = 2
        where i.outdated=0
        <!-- 特殊逻辑，商城过滤saas仅自营品 -->
        and i.create_type != 3
          <choose>
              <when test="skuList != null and skuList.size > 0">
                  and i.sku in
                  <foreach collection="skuList" open="(" close=")" separator="," item="item">
                      #{item}
                  </foreach>
              </when>
              <otherwise>
                  and (p.pd_name like concat('%',#{queryStr},'%') or i.sku like concat('%',#{queryStr},'%') )
              </otherwise>
          </choose>
        <if test="samplePool != null">
            and i.sample_pool = #{samplePool}
        </if>
    </select>

    <select id="selectProperty" resultType="net.summerfarm.model.vo.InventoryVO">
        select  i.sku,p.pd_name productName,i.weight,ifnull(i.sku_pic, p.picture_path) picturePath,p.pd_no pdNo,
        i.type ,p.quality_time qualityTime, p.quality_time_unit qualityTimeUnit, ppv.products_property_value brand
        from inventory i
        left join products p on i.pd_id = p.pd_id
        left join products_property_value ppv on ppv.pd_id = p.pd_id and ppv.products_property_id = 2
        where i.outdated=0 and i.sku=#{sku}
        limit 1
    </select>

    <select id="selectBySkuStr" resultType="net.summerfarm.model.vo.InventoryVO" parameterType="net.summerfarm.model.input.AdminSkuMappingInput">
        select distinct i.sku,p.pd_name productName,i.weight,ifnull(i.sku_pic, p.picture_path) picturePath
        from inventory i left join products p on i.pd_id = p.pd_id left join admin_sku_mapping asm on asm.sku=i.sku
        where
        i.sku IN
        <foreach collection="skuStr.split(',')" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and i.outdated=0
        <!-- 特殊逻辑，商城过滤saas仅自营品 -->
        and i.create_type != 3
        <if test="isOrder == 1">
            and asm.admin_id=#{adminId}
            order by asm.add_time desc
        </if>
    </select>

    <select id="selectOneBySku" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from inventory
        where sku = #{sku}
    </select>

    <select id="selectAreaNoAndStorage" resultType="net.summerfarm.model.DTO.AreaStorageDTO">
        SELECT
            ak.sku,
            ak.area_no areaNo,
            a.area_name areaName,
            wim.warehouse_no warehouseNo,
            wsc.warehouse_name warehouseName
        FROM area_sku ak
                 RIGHT JOIN area a on a.area_no = ak.area_no and a.status = 1
                 LEFT JOIN (
            select area_no ,store_no from fence
            where status = 0
            group by area_no
        ) f on f.area_no = a.area_no
        left join warehouse_inventory_mapping wim on ak.sku = wim.sku and f.store_no = wim.store_no
        inner join warehouse_storage_center wsc on wsc.warehouse_no = wim.warehouse_no
        where ak.sku = #{sku}
    </select>

    <select id="selectWarehouseNo" resultType="java.lang.Integer">
        select warehouse_no from warehouse_inventory_mapping where store_no in(
            select * from(select store_no from fence where area_no = #{areaNo} and status = 0 limit 1) as a) and sku = #{sku}
    </select>


    <select id="selectCycleCost" resultType="java.math.BigDecimal">
        select first_cycle_cost
        from cycle_inventory_cost
        <where>
            <if test="warehouseNo!=null">
                and warehouse_no= #{warehouseNo}
            </if>
            <if test="sku!=null">
                and sku = #{sku}
            </if>
        </where>
    </select>

    <select id="selectBySkusAndExtTypes" resultType="java.lang.String">
        select sku
        from inventory
        where sku in <foreach collection="skus" item="sku" open="(" close=")" separator=",">
        #{sku}
    </foreach>
        and ext_type in <foreach collection="extTypes" item="extType" open="(" close=")" separator=",">
        #{extType}
    </foreach>
    </select>

    <select id="checkIsFruit" resultType="java.lang.Boolean">
        select count(1)
        from inventory i
                 left join products p on i.pd_id = p.pd_id
                 left join category c on p.category_id = c.id
        where c.type = 4
          and sku = #{sku}
    </select>

    <select id="selectInventoryBySku" resultType="net.summerfarm.model.vo.InventoryVO">
        SELECT
            i.sku,
            p.pd_name pdName,
            i.weight,
            i.type,
            p.category_id categoryId,
            i.volume,
            i.weight_num weightNum,
            i.net_weight_num netWeightNum,
            p.pd_no pdNo,
            i.pd_id pdId,
            i.origin,
            p.warn_time warnTime,
            i.ext_type extType,
            ifnull(i.sku_pic, p.picture_path) picturePath,
            p.quality_time qualityTime,
            p.quality_time_unit qualityTimeUnit,
            c.type categoryType,
            p.storage_location storageLocation,
            i.unit
        FROM
            inventory i
                LEFT JOIN products p ON i.pd_id = p.pd_id
                LEFT JOIN category c ON p.category_id = c.id
        WHERE
            i.sku = #{sku} and i.outdated = 0
    </select>

    <select id="selectBySkuAndOutdated" resultType="net.summerfarm.model.vo.InventoryVO">
        SELECT i.sku,p.pd_name pdName,i.weight,i.type,p.category_id categoryId,i.volume,i.weight_num weightNum,p.pd_no pdNo,
               i.pd_id pdId,i.origin, p.warn_time warnTime, i.ext_type extType,
               p.quality_time qualityTime,p.quality_time_unit qualityTimeUnit,c.type categoryType,p.storage_location storageLocation
        FROM inventory i
                 LEFT JOIN products p ON i.pd_id=p.pd_id
                 LEFT JOIN category c ON p.category_id=c.id
        WHERE i.sku = #{sku}
          and i.outdated in
        <foreach item="outdated" index="index" collection="outdatedList" open="(" separator="," close=")">
            #{outdated,jdbcType=INTEGER}
        </foreach>

    </select>

    <select id="selectByQueryInput" resultType="net.summerfarm.model.domain.Inventory">
        SELECT  <include refid="Base_Column_List" />
        FROM inventory
        <where>
            <if test = "invId != null">
                and inv_id = #{invId}
            </if>
            <if test = "sku != null">
                and sku = #{sku}
            </if>
            <if test = "pdId != null">
                and pd_id = #{pdId}
            </if>
            <if test = "salesMode != null">
                and sales_mode = #{salesMode}
            </if>
            <if test = "createType != null">
                and create_type = #{createType}
            </if>
            <if test = "outdated != null">
                and outdated = #{outdated}
            </if>
            <if test = "type != null">
                and `type` = #{type}
            </if>
            <if test = "adminId != null">
                and admin_id = #{adminId}
            </if>
            <if test = "auditStatus != null">
                and audit_status = #{auditStatus}
            </if>
            <if test = "creator != null">
                and creator = #{creator}
            </if>
            <if test = "extType != null">
                and ext_type = #{extType}
            </if>
            <if test = "auditor != null">
                and auditor = #{auditor}
            </if>
            <if test = "subType != null">
                and sub_type = #{subType}
            </if>
        </where>
    </select>

    <select id="selectByPdIdAndExtType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from inventory
        where pd_id = #{pdId} and ext_type = #{extType}
    </select>

    <select id="selectByExtTypes" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from inventory
        where ext_type in
        <foreach item="extType" index="index" collection="extTypes" open="(" separator="," close=")">
            #{extType,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="selectSkuVisible" resultType="java.lang.Integer">
        select count(1) from inventory where sku = #{sku} and supplier_visible = 1;
    </select>
    <select id="getPicBySku" resultType="java.lang.String">
        select if(i.sku_pic is null , p.picture_path, i.sku_pic) pic
        from inventory i
        left join products p on i.pd_id = p.pd_id
        where i.sku = #{sku}
    </select>
    <select id="getRecentPurchaserIdBySku" resultType="java.lang.Integer">
        SELECT p.creator_id
        FROM purchases p
        LEFT JOIN purchases_plan pp ON p.purchase_no= pp.purchase_no
        WHERE pp.sku = #{sku}
        order by p.id desc limit 1
    </select>

    <select id="queryNeedSynchronizedSkuInfo" resultType="com.cosfo.summerfarm.model.dto.SummerfarmSynchronizedSkuDTO">
        select i.inv_id as skuId,
               i.pd_id as spuId,
               i.weight as specification,
               '' as specificationUnit,
               p.category_id thirdCategoryId,
               '' as brandId,
               case when p.pd_name is null or p.pd_name = '' then p.real_name ELSE p.pd_name END as title,
               p.pddetail as subTitle,
               p.picture_path as mainPicture,
               p.detail_picture as detailPicture,
               p.storage_location,
               p.quality_time as guaranteePeriod,
               if(p.quality_time_unit = 'day', 0, 1) as guaranteeUnit,
               p.origin
        from inventory i
        left join products p on i.pd_id = p.pd_id
        where i.add_time between #{startTime} and #{endTime} and i.create_type != 2
        and i.create_type != 3
    </select>

    <select id="selectSupplierVisible" resultMap="SkuMap"
            parameterType="net.summerfarm.model.input.InventoryReq">
        SELECT
        i.inv_id ,i.sku, p.pd_name , i.origin, i.unit, i.pack, i.weight, i.production_date ,
        i.storage_method , i.base_sale_quantity , i.base_sale_unit ,
        i.promotion_price , i.maturity, i.after_sale_quantity ,i.volume,i.weight_num ,
        a.name_remakes  ,a.admin_id adminId ,type,i.sample_pool ,i.sku_pic , i.after_sale_unit ,i.outdated,
        i.audit_status , i.audit_time , i.creator, i.ext_type extType, p.real_name , i.create_remark ,
        i.pd_id ,i.weight_notes ,i.is_domestic ,i.average_price_flag,plv.label_id,
        plv.label_value,pl.label_field,pl.label_name,plv.id
        FROM inventory i
        LEFT JOIN products p on i.pd_id = p.pd_id
        LEFT JOIN admin a on a.admin_id = i.admin_id
        LEFT JOIN product_label_value plv on i.sku = plv.sku
        LEFT JOIN product_label pl on plv.label_id = pl.id
        where   i.ext_type in (0,3,4) and i.outdated= 0 and i.type = 0
        <if test="sku != null">
            AND i.sku = #{sku}
        </if>
        <if test="pdName != null">
            AND p.pd_name LIKE CONCAT('%',#{pdName},'%')
        </if>
    </select>

    <select id="queryNeedSynchronizedSkuInfoBySkuId" resultType="com.cosfo.summerfarm.model.dto.SummerfarmSynchronizedSkuDTO">
        select i.inv_id as skuId,
               i.sku as sku,
               i.pd_id as spuId,
               i.weight      as specification,
               i.unit        as specificationUnit,
               p.category_id    thirdCategoryId,
               i.sku_pic     as skuPicture,
               i.sku_name    as skuTitle,
               i.creator     as creator,
               i.create_type as createType,
               p.pd_no       as spu,
               ''            as brandId,
               case when p.pd_name is null or p.pd_name = '' then p.real_name ELSE p.pd_name END as title,
               i.volume as volume,
               i.weight_num as weightNum,
               p.pddetail as subTitle,
               p.picture_path as mainPicture,
               p.detail_picture as detailPicture,
               p.quality_time as guaranteePeriod,
               if(p.quality_time_unit = 'day', 0, 1) as guaranteeUnit,
               p.origin,
               i.type,
               i.admin_id adminId,
               case p.storage_location when  1 then 2 when 2 then 1 when 3 then 0 end as storageLocation
        from inventory i
                 left join products p on i.pd_id = p.pd_id
        where i.inv_id = #{skuId}
    </select>
    <select id="selectBySkusc" resultMap="SkuMap" >
        select
        <include refid="Base_Column_List"></include>,
        label_id,
        label_value,
        label_field,
        label_name
        from inventory i
        left join product_label_value plv on i.sku = plv.sku
        left join product_label pl on plv.label_id = pl.id
        where i.sku = #{sku}
    </select>

    <resultMap id="EffectiveInventoryResultMap" type="net.summerfarm.model.DTO.inventory.InventoryInfoDTO">
        <result column="sku" property="sku"/>
        <result column="sku_name" property="skuName"/>
        <result column="weight" property="weight"/>
        <result column="unit" property="unit"/>
        <result column="sku_pic" property="skuPic"/>
        <result column="base_sale_quantity" property="baseSaleQuantity"/>
        <result column="base_sale_unit" property="baseSaleUnit"/>
        <result column="outdated" property="outdated"/>
        <result column="pd_id" property="pdId"/>
        <result column="pd_name" property="pdName"/>
        <result column="pddetail" property="pddetail"/>
        <result column="slogan" property="slogan"/>
        <result column="other_slogan" property="otherSlogan"/>
        <result column="quality_time" property="qualityTime"/>
        <result column="quality_time_unit" property="qualityTimeUnit"/>
        <result column="products_outdated" property="productsOutdated"/>
        <result column="picture_path" property="picturePath"/>
        <result column="brand_id" property="brandId"/>
        <result column="ext_type" property="extType"/>
        <result column="category_id" property="categoryId"/>
        <result column="limited_quantity" property="limitedQuantity"/>
        <result column="type" property="type"/>
        <result column="sub_type" property="subType"/>
    </resultMap>
    <select id="selectEffectiveInventoryBySku" resultMap="EffectiveInventoryResultMap">
        select i.sku, i.sku_name, i.weight, i.unit, i.sku_pic, i.base_sale_quantity, i.base_sale_unit, i.outdated, i.pd_id,
               p.pd_name, p.pddetail, p.slogan, p.other_slogan, p.quality_time, p.quality_time_unit, p.outdated as `products_outdated`,
               p.picture_path,p.brand_id,p.category_id,i.ext_type,limited_quantity,i.type,i.sub_type
        from inventory i left join products p on i.pd_id = p.pd_id
        where sku = #{sku} and i.outdated <![CDATA[ <= ]]> 0 and p.outdated <![CDATA[ <= ]]> 0
    </select>

    <select id="selectBySkuMsg" resultType="net.summerfarm.model.vo.InventoryVO">
        SELECT i.sku,i.create_type createType,p.pd_name pdName, p.real_name realName,i.weight,i.type,p.category_id categoryId,c.type categoryType,i.volume,i.weight_num weightNum,ad.name_remakes nameRemakes,i.pd_id pdId,i.origin,i.is_domestic isDomestic,
        i.base_sale_quantity baseSaleQuantity ,i.base_sale_unit baseSaleUnit,p.storage_location storageLocation,i.sample_pool samplePool,unit unit,
        p.quality_time qualityTime,p.quality_time_unit qualityTimeUnit,i.sku_pic skuPic,p.picture_path picturePath,c.category categoryName
        FROM inventory i
        LEFT JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN category c ON p.category_id=c.id
        LEFT JOIN admin ad on ad.admin_id = i.admin_id
        WHERE  i.sku = #{sku}
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from inventory
        where inv_id = #{id}
    </select>

    <select id="listByPdIdsNoCareOutdated" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM inventory
        WHERE pd_id IN
        <foreach collection="pdIds" item="it" open="(" separator="," close=")">
            #{it}
        </foreach>
    </select>

    <select id="selectByMinWeightNum" resultType="net.summerfarm.model.domain.Inventory">
        select
        <include refid="Base_Column_List"/>
        FROM inventory
        where weight_num > #{weightNum}
        or weight_num is null
        or weight_num = 0
    </select>

    <select id="selectByMinVolume" resultType="net.summerfarm.model.domain.Inventory">
        select
        <include refid="Base_Column_List"/>
        FROM inventory
        where
            SUBSTRING_INDEX(`volume`,'*', 1) > #{data}
        or
            SUBSTRING_INDEX(SUBSTRING_INDEX(`volume`,'*',-2), '*', 1 ) > #{data}
        or
            SUBSTRING_INDEX(`volume`,'*',-1) > #{data}
        or volume is null
    </select>

    <select id="selectInventorySumBySku" resultType="java.lang.Integer" >
        select sum(store_quantity) where sku = #{sku}
    </select>

    <select id="selectBySkuStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM inventory
        WHERE outdated = #{outdated}
    </select>

    <select id="selectNewSku" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM inventory
        WHERE
        add_time > DATE_SUB(CURDATE(), INTERVAL 15 DAY)
        <if test="outdated != null">
            AND outdated = #{outdated}
        </if>
        <if test="auditStatus != null">
            AND audit_status = #{auditStatus,jdbcType=INTEGER}
        </if>

    </select>
    <select id="saasMatchSkuOrName" resultType="net.summerfarm.model.vo.InventoryVO">
        select i.sku,p.pd_name productName,i.weight,ifnull(i.sku_pic, p.picture_path) picturePath,p.pd_no pdNo,i.is_domestic isDomestic, i.weight_num weightNum, i.volume volume,
        i.type ,p.quality_time qualityTime, p.quality_time_unit qualityTimeUnit, ppv.products_property_value brand, i.ext_type extType,i.unit unit, p.storage_location  storageLocation,i.pd_id pdId,
        i.create_type createType
        from inventory i
        left join products p on i.pd_id = p.pd_id
        left join products_property_value ppv on ppv.pd_id = p.pd_id and ppv.products_property_id = 2
        where i.outdated = 0 and i.admin_id = #{adminId} and i.audit_status = 1 and i.type = 1
        <if test="createTypeList != null and createTypeList.size > 0">
            and i.create_type in
            <foreach collection="createTypeList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryStr != null and queryStr != ''">
            and (p.pd_name like concat('%',#{queryStr},'%') or i.sku like concat('%',#{queryStr},'%') )
        </if>
        <if test="skuList != null and skuList.size() != 0">
            and i.sku in
            <foreach collection="skuList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByPdIdAndAdminId" resultType="net.summerfarm.model.domain.Inventory">
    SELECT inv_id invId, sku, ait_id, pd_id pdId, origin, unit, pack, weight, production_date productionDate,storage_method storageMethod, sale_price salePrice,
    promotion_price promotionPrice, maturity, after_sale_quantity afterSaleQuantity,outdated
    FROM inventory
    WHERE pd_id = #{pdId,jdbcType=INTEGER}
    <if test="adminId != null">
        and admin_id = #{adminId}
    </if>
    <if test="adminId == null">
        and admin_id is null
    </if>
    </select>

    <select id="selectSkuInfoBySkuList" resultType="net.summerfarm.model.vo.InventorySkuVO">
        select i.sku as sku,
               p.pd_name as pdName,
               i.weight as weight,
               ifnull(i.sku_pic, p.picture_path) as pic,
               i.unit as unit
        from inventory i
        left join products p on i.pd_id = p.pd_id
        where i.sku in
        <foreach collection="skuList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectSkuAfterSale" resultType="net.summerfarm.model.vo.InventoryVO">
        SELECT i.sku,i.create_type createType,p.pd_name pdName, p.real_name realName,i.weight,i.type,p.category_id categoryId,c.type categoryType,i.volume,i.weight_num weightNum,ad.name_remakes nameRemakes,i.pd_id pdId,i.origin,i.is_domestic isDomestic,
        i.base_sale_quantity baseSaleQuantity ,i.base_sale_unit baseSaleUnit,p.storage_location storageLocation,i.sample_pool samplePool,i.ext_type extType,
        p.quality_time qualityTime,p.quality_time_unit qualityTimeUnit,i.sku_pic skuPic,p.picture_path picturePath,c.category categoryName,i.pack,i.unit
        FROM inventory i
        LEFT JOIN products p ON i.pd_id=p.pd_id
        LEFT JOIN category c ON p.category_id=c.id
        LEFT JOIN admin ad on ad.admin_id = i.admin_id
        WHERE i.sku = #{sku}
    </select>

    <select id="selectSaasAdminIdList" resultType="java.lang.Integer">
        SELECT DISTINCT( `admin_id`)   FROM `inventory` WHERE `create_type`  = 2;
    </select>

    <update id="updateTenantIdByAdminId">
        UPDATE `inventory` set `tenant_id` = #{tenantId} where `admin_id` =#{adminId}
    </update>
    <update id="batchUpdateOutdated">
        update inventory i
        set i.outdated =
        case i.sku
        <foreach collection="list" item="item">
            when #{item.sku} then #{item.outdated}
        </foreach>
        end
        where i.sku in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item.sku}
        </foreach>
    </update>

    <select id="selectNeedSyncToItemSku" resultType="net.summerfarm.model.vo.inventory.SkuSyncToItemVO">
        SELECT
            p.`pd_id` pdId,
            case when p.pd_name is null or p.pd_name = '' then p.real_name ELSE p.pd_name END as  pdName,
            p.pddetail,
            p.`picture_path` picturePath,
            p.`detail_picture` detailPicture,
            p.category_id categoryId,
            p.`other_slogan` otherSlogan,
            p.`after_sale_type` afterSaleType,
            p.`after_sale_time` afterSaleTime,
            p.`refund_type` refundType,
            p.`warn_time` warnTime,
            p.`outdated`,
            p.`add_time` addTime,
            p.`update_time` updateTime,
            i.`inv_id` invId,
            i.sku,
            i.`sku_name` skuName,
            i.`weight` weight,
            i.`unit`,
            i.`weight_notes` weightNotes,
            i.`base_sale_quantity` baseSaleQuantity,
            i.`after_sale_quantity` afterSaleQuantity,
            i.`after_sale_unit` afterSaleUnit,
            i.`sku_pic` skuPic,
            i.`origin`,
            i.`admin_id` adminId,
            i.`type`,
            i.`ext_type` extType,
            i.`base_sale_unit` baseSaleUnit,
            i.`sample_pool` samplePool,
            i.`average_price_flag` averagePriceFlag,
            i.`outdated` skuOutdated,
            i.`add_time` skuAddTime ,
            i.`update_time` skuUpdateTime,
            i.net_weight_num netWeightNum,
            i.video_url videoUrl,
            i.after_sale_rule_detail afterSaleRuleDetail,
            i.buyer_name buyerName,
            i.buyer_id buyerId,
            i.net_weight_unit netWeightUnit,
            i.video_upload_user videoUploadUser,
            i.video_upload_time videoUploadTime,
            i.quote_type quoteType,
            i.min_auto_after_sale_threshold minAutoAfterSaleThreshold
        FROM
            inventory i
                LEFT JOIN products p ON i.pd_id = p.pd_id
        WHERE
          i.create_type in (0,1)
          and i.outdated in (0,1)
          and p.category_id is not null
          and p.outdated != 3
          and i.inv_id in
        <foreach collection="skus" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectNeedSyncToItemByPdId" resultType="net.summerfarm.model.vo.inventory.SkuSyncToItemVO">
        SELECT
        p.`pd_id` pdId,
        case when p.pd_name is null or p.pd_name = '' then p.real_name ELSE p.pd_name END as  pdName,
        p.pddetail,
        p.`picture_path` picturePath,
        p.`detail_picture` detailPicture,
        p.category_id categoryId,
        p.`other_slogan` otherSlogan,
        p.`after_sale_type` afterSaleType,
        p.`after_sale_time` afterSaleTime,
        p.`refund_type` refundType,
        p.`warn_time` warnTime,
        p.`outdated`,
        p.`add_time` addTime,
        p.`update_time` updateTime,
        i.`inv_id` invId,
        i.sku,
        i.`sku_name` skuName,
        i.`weight` weight,
        i.`unit`,
        i.`weight_notes` weightNotes,
        i.`base_sale_quantity` baseSaleQuantity,
        i.`after_sale_quantity` afterSaleQuantity,
        i.`after_sale_unit` afterSaleUnit,
        i.`sku_pic` skuPic,
        i.`origin`,
        i.`admin_id` adminId,
        i.`type`,
        i.`ext_type` extType,
        i.`base_sale_unit` baseSaleUnit,
        i.`sample_pool` samplePool,
        i.`average_price_flag` averagePriceFlag,
        i.`outdated` skuOutdated,
        i.`add_time` skuAddTime ,
        i.`update_time` skuUpdateTime,
        i.net_weight_num netWeightNum,
        i.video_url videoUrl,
        i.after_sale_rule_detail afterSaleRuleDetail,
        i.buyer_name buyerName,
        i.buyer_id buyerId,
        i.net_weight_unit netWeightUnit,
        i.video_upload_user videoUploadUser,
        i.video_upload_time videoUploadTime
        FROM
        inventory i
        LEFT JOIN products p ON i.pd_id = p.pd_id
        WHERE
        i.create_type in (0,1)
        and i.outdated in (0,1)
        and p.category_id is not null
        and p.outdated != 3
        and p.pd_id in
        <foreach collection="pdIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectNeedSyncToItemProduct" resultType="java.lang.Long">
        SELECT
        i.`pd_id`
        FROM
        inventory i
        WHERE
        and i.create_type in (0,1)
        and i.`pd_id` in
        <foreach collection="pdIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        group by pd_id
    </select>
    <select id="queryNeedSyncSkuList" resultType="com.cosfo.manage.client.product.req.SummerFarmSynchronizedSkuReq">
        select i.inv_id as skuId,
               i.sku as sku,
               i.pd_id as spuId,
               i.weight as specification,
               i.unit as specificationUnit,
               i.volume as volume,
               i.weight_num as weightNum,
               i.sku_pic as skuPicture,
               i.sku_name as skuTitle,
               i.creator as creator,
               i.create_type as createType,
               i.outdated as outdated,
               p.pd_no as spu,
               p.category_id thirdCategoryId,
               p.category_id categoryId,
               '' as brandId,
               case when p.pd_name is null or p.pd_name = '' then p.real_name ELSE p.pd_name END as title,
               p.pddetail as subTitle,
               p.picture_path as mainPicture,
               p.detail_picture as detailPicture,
               p.quality_time as guaranteePeriod,
               if(p.quality_time_unit = 'day', 0, 1) as guaranteeUnit,
               p.origin,
               i.type,
               i.admin_id adminId,
               case p.storage_location when  1 then 2 when 2 then 1 when 3 then 0 end as storageLocation,
               i.sub_type as subType,
               i.is_domestic as placeType,
               i.tenant_id as tenantId
        from inventory i
                 left join products p on i.pd_id = p.pd_id
        <where>
            <if test="pdIds != null and pdIds.size()>0">
                and i.pd_id in
                <foreach collection="pdIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="skuIds != null and skuIds.size()>0">
                and i.inv_id in
                <foreach collection="skuIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            and i.create_type in (0,1)
            and i.outdated in (0,1)
            and p.category_id is not null
            and p.outdated != 3
        </where>
    </select>

    <select id="queryNeedSyncSkuListForGoods" resultType="net.summerfarm.goods.client.req.XmSyncSkuReq">
        select i.inv_id as xmSkuId,
        i.sku as sku,
        i.pd_id as xmSpuId,
        i.weight as specification,
        i.unit as specificationUnit,
        i.volume as volume,
        i.weight_num as weightNum,
        i.sku_pic as skuPicture,
        i.sku_name as skuTitle,
        i.creator as creator,
        i.create_type as createType,
        i.outdated as outdated,
        p.pd_no as spu,
        p.category_id thirdCategoryId,
        p.category_id categoryId,
        '' as brandId,
        case when p.pd_name is null or p.pd_name = '' then p.real_name ELSE p.pd_name END as title,
        p.pddetail as subTitle,
        p.picture_path as mainPicture,
        p.detail_picture as detailPicture,
        p.quality_time as guaranteePeriod,
        if(p.quality_time_unit = 'day', 0, 1) as guaranteeUnit,
        p.origin,
        i.type,
        i.admin_id adminId,
        case p.storage_location when  1 then 2 when 2 then 1 when 3 then 0 end as storageLocation,
        i.sub_type as subType,
        i.is_domestic as placeType,
        i.tenant_id as tenantId,
        i.buyer_id as buyerId,
        i.buyer_name as buyerName,
        i.net_weight_num as netWeightNum,
        i.net_weight_unit as netWeightUnit,
        i.quote_type quoteType,
        p.warn_time as warnTime
        from inventory i
        left join products p on i.pd_id = p.pd_id
        <where>
            <if test="pdIds != null and pdIds.size()>0">
                and i.pd_id in
                <foreach collection="pdIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="skuIds != null and skuIds.size()>0">
                and i.inv_id in
                <foreach collection="skuIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            and i.create_type in (0,1)
            and i.outdated in (0,1)
            and p.category_id is not null
            and p.outdated != 3
        </where>
    </select>
    <select id="queryNeedSyncSkuInfo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from inventory i
        <where>
            <if test="startTime != null and endTime != null">
                AND i.update_time between #{startTime} and #{endTime}
            </if>
            <if test="skuId != null">
                AND i.inv_id <![CDATA[> ]]>  #{skuId}
            </if>
            <if test="maxSkuId != null">
                AND i.inv_id <![CDATA[<= ]]> #{maxSkuId}
            </if>
            and i.create_type in (0,1)
            and i.outdated in (0,1)
        </where>
        order by i.inv_id asc
        limit 200
    </select>
    <select id="selectBySkus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        from inventory i
        WHERE
         i.sku in
        <foreach collection="skus" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and i.create_type in (0,1)
        and i.outdated in (0,1)
    </select>
    <select id="selectSkuListBySku" resultType="net.summerfarm.model.domain.Inventory">
        select <include refid="Base_Column_List"/>
        from inventory
        where sku in
        <foreach collection="skus" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectInventoryVOBySkus" resultType="net.summerfarm.model.vo.InventoryVO">
        SELECT i.sku,i.sub_type subType, p.pd_name pdName,i.weight,i.type,p.category_id categoryId,i.volume,i.weight_num weightNum,p.pd_no pdNo,
               ad.name_remakes nameRemakes,i.pd_id pdId,i.origin, p.warn_time warnTime, i.ext_type extType,i.is_domestic isDomestic,
               p.quality_time qualityTime,p.quality_time_unit qualityTimeUnit,c.type categoryType,p.storage_location storageLocation, i.inv_id invId,i.unit, ifnull(i.sku_pic, p.picture_path) picturePath
        FROM inventory i
                 LEFT JOIN products p ON i.pd_id=p.pd_id
                 LEFT JOIN category c ON p.category_id=c.id
                 LEFT JOIN admin ad on ad.admin_id = i.admin_id
        WHERE i.sku in
        <foreach collection="skus" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>


    <select id="selectFruitSkuBySkus" resultType="string">
        select
        distinct t.sku
        from
        inventory t
        left JOIN `products` p on p.`pd_id` = t.`pd_id`
        left join category c on c.id = p.`category_id`
        WHERE t.`outdated`  = 0 and c.type = 4 and t.sku in
        <foreach collection="skus" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="querySkuIdsByAdminId" resultType="java.lang.Long">
        SELECT inv_id
        from inventory i
        WHERE
        admin_id = #{adminId}
        and i.outdated  = 0

        and i.`type` = 1
    </select>

    <select id="batchSelectSkuInfo" resultType="net.summerfarm.model.vo.InventoryVO">
        select i.sku, p.warn_time warnTime, p.pd_name pdName, i.weight, i.ext_type extType
             ,i.sub_type subType
        FROM
            inventory i
                LEFT JOIN products p ON i.pd_id = p.pd_id
        <where>
            <if test="skuIds != null and skuIds.size()>0">
                and i.sku in
                <foreach collection="skuIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryAllSku" resultType="net.summerfarm.model.vo.SKUVO">
        select i.sku, p.pd_name pdName
        FROM
            inventory i
                LEFT JOIN products p ON i.pd_id = p.pd_id
        where i.sku = #{sku}
    </select>

    <select id="listAllPriceAdjustmentJobSku" resultType="net.summerfarm.model.vo.InventoryVO">
        SELECT
            i.sku,
            c.type categoryType
        from inventory i
            left join products p on i.pd_id = p.pd_id
            left join category c on p.category_id = c.id
        where i.ext_type = 0
          and i.create_type in (0,1)
          and i.sub_type = 3
          and i.outdated = 0
          and p.outdated = 0
          and c.type = 4
    </select>

    <select id="checkAutoGenerateMajorPrice" resultMap="SkuMap">
        select i.sku, p.pd_name, i.weight
        from inventory i
                 left join products p on i.pd_id = p.pd_id
                 left join category c on p.category_id = c.id
        where i.sku in
            <foreach collection="skuList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        and i.create_type in (0, 1)
        and i.outdated = 0
        and p.outdated = 0
        and i.ext_type = 0
        and i.type = 0
        and c.type <![CDATA[< ]]> 4
    </select>
</mapper>
