<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.GmvMonthDataMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.GmvMonthData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="dairy_gmv" jdbcType="DECIMAL" property="dairyGmv" />
    <result column="fruit_gmv" jdbcType="DECIMAL" property="fruitGmv" />
    <result column="gmv_time" jdbcType="TIMESTAMP" property="gmvTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="realname" jdbcType="VARCHAR" property="realname" />
  </resultMap>
  <sql id="Base_Column_List">
    id, admin_id, area_no, dairy_gmv, fruit_gmv, gmv_time, create_time, creator, `status`, 
    update_time, updater
  </sql>
  <select id="selectByPrimaryKey" resultType="net.summerfarm.model.domain.GmvMonthData">
    select sum(dairy_gmv) dairyGmv, sum(fruit_gmv) fruitGmv, gmv_time gmvTime
    from gmv_month_data
    where status = 0
    <if test="startTime !=null">
      AND gmv_time <![CDATA[>]]>  #{startTime}
    </if>
    <if test="endTime !=null">
      AND gmv_time <![CDATA[<=]]>  #{endTime}
    </if>
    <if test="adminId != null">
      AND admin_id = #{adminId}
    </if>
    group by gmv_time
  </select>

  <select id="selectByBD" resultType="net.summerfarm.model.domain.GmvMonthData">
    select sum(dairy_gmv) dairyGmv, sum(fruit_gmv) fruitGmv,admin_id adminId,realname realname
    from gmv_month_data
    where status = 0
    <if test="startTime !=null">
      AND gmv_time <![CDATA[>]]>  #{startTime}
    </if>
    <if test="endTime !=null">
      AND gmv_time <![CDATA[<=]]>  #{endTime}
    </if>
    <if test="adminId != null">
      AND admin_id = #{adminId}
    </if>
  </select>

  <select id="selectByAdminId" resultType="integer">
    select distinct admin_id adminId
    from gmv_month_data
    where status = 0 and gmv_time <![CDATA[>]]> #{startTime}
  </select>

  <select id="selectAreaNo" resultType="net.summerfarm.model.domain.GmvMonthData">
    select sum(dairy_gmv) dairyGmv, sum(fruit_gmv) fruitGmv,admin_id adminId,sum(amount) amount
    from gmv_month_data
    where status = 0
    <if test="startTime !=null">
      AND gmv_time <![CDATA[>]]>  #{startTime}
    </if>
    <if test="endTime !=null">
      AND gmv_time <![CDATA[<]]>  #{endTime}
    </if>
    <if test="adminId != null">
      AND admin_id = #{adminId}
    </if>
    <if test="areaNo != null">
      AND area_no = #{areaNo}
    </if>
    group by area_no
  </select>

  <select id="selectOne" parameterType="integer" resultType="net.summerfarm.model.domain.GmvMonthData">
    select id, admin_id adminId, area_no areaNo, dairy_gmv dairyGmv, fruit_gmv fruitGmv, gmv_time gmvTime
    from gmv_month_data
    where status = 0 and area_no = #{areaNo} and admin_id = #{adminId}
    limit 1
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from gmv_month_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.GmvMonthData" useGeneratedKeys="true">
    insert into gmv_month_data (admin_id, area_no, dairy_gmv, 
      fruit_gmv, gmv_time, create_time, 
      creator, `status`, update_time, 
      updater)
    values (#{adminId,jdbcType=INTEGER}, #{areaNo,jdbcType=INTEGER}, #{dairyGmv,jdbcType=DECIMAL}, 
      #{fruitGmv,jdbcType=DECIMAL}, #{gmvTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.GmvMonthData" useGeneratedKeys="true">
    insert into gmv_month_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="dairyGmv != null">
        dairy_gmv,
      </if>
      <if test="fruitGmv != null">
        fruit_gmv,
      </if>
      <if test="gmvTime != null">
        gmv_time,
      </if>
        create_time,
      <if test="creator != null">
        creator,
      </if>
        `status`,
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="dairyGmv != null">
        #{dairyGmv,jdbcType=DECIMAL},
      </if>
      <if test="fruitGmv != null">
        #{fruitGmv,jdbcType=DECIMAL},
      </if>
      <if test="gmvTime != null">
        #{gmvTime,jdbcType=TIMESTAMP},
      </if>
        now(),
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
        0,
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="amount != null">
        #{amount},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.GmvMonthData">
    update gmv_month_data
    <set>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="dairyGmv != null">
        dairy_gmv = #{dairyGmv,jdbcType=DECIMAL},
      </if>
      <if test="fruitGmv != null">
        fruit_gmv = #{fruitGmv,jdbcType=DECIMAL},
      </if>
      <if test="gmvTime != null">
        gmv_time = #{gmvTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.GmvMonthData">
    update gmv_month_data
    set amount = #{amount}
    where id = #{id,jdbcType=BIGINT} and `status` = 0
  </update>
</mapper>