<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.GoodsTransferItemMapper">

    <resultMap id="VoMap" type="net.summerfarm.model.vo.GoodsTransferItemVO">
        <id column="id" property="id"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="pd_name" property="pdName"/>
        <result column="weight" property="weight"/>
        <result column="sku" property="sku"/>
        <result column="ext_type" property="extType"/>
        <collection property="goodsTransferItemDetails" ofType="net.summerfarm.model.domain.GoodsTransferItemDetail">
            <id column="gtId" property="id" jdbcType="INTEGER" />
            <result column="goods_transfer_item_id"  property="goodsTransferItemId"/>
            <result column="origin_gl_no" property="originGlNo"/>
            <result column="batch" property="batch"/>
            <result column="quality_date" property="qualityDate"/>
            <result column="quantity" property="quantity"/>
            <result column="transfer_quantity" property="transferQuantity"/>
            <result column="new_gl_no" property="newGlNo"/>
        </collection>


    </resultMap>
    <insert id="insertGoodsTransferItem" parameterType="net.summerfarm.model.domain.GoodsTransferItem" useGeneratedKeys="true" keyProperty="id">
        insert into goods_transfer_item (sku,pd_name,weight,goods_transfer_id,total_quantity)
        values(#{sku},#{pdName},#{weight},#{goodsTransferId},#{totalQuantity})
    </insert>

    <select id="selectByGoodsTransferId" resultMap="VoMap">
        select gti.id, gti.sku, gti.pd_name, gti.weight, gti.goods_transfer_id,gtid.id gtId , gti.total_quantity,gtid.goods_transfer_item_id,
               gtid.origin_gl_no, gtid.batch, gtid.quality_date, gtid.quantity, gtid.transfer_quantity, gtid.new_gl_no, i.ext_type extType
        from goods_transfer_item gti
        inner join goods_transfer_item_detail gtid  on gti.id = gtid.goods_transfer_item_id
        left join inventory i on gti.sku = i.sku
        where gti.goods_transfer_id = #{id}
    </select>


</mapper>