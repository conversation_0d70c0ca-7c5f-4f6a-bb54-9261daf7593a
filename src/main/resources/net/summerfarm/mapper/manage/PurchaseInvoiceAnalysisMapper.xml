<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PurchaseInvoiceAnalysisMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PurchaseInvoiceAnalysis">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="admin_id" jdbcType="BIGINT" property="adminId" />
    <result column="purchase_invoice_id" jdbcType="INTEGER" property="purchaseInvoiceId" />
    <result column="analysis_type" jdbcType="INTEGER" property="analysisType" />
    <result column="billing_date" jdbcType="VARCHAR" property="billingDate" />
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="invoice_number" jdbcType="VARCHAR" property="invoiceNumber" />
    <result column="excluding_tax" jdbcType="DECIMAL" property="excludingTax" />
    <result column="included_tax" jdbcType="DECIMAL" property="includedTax" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="tax_rate" jdbcType="INTEGER" property="taxRate" />
    <result column="invoice_type" jdbcType="INTEGER" property="invoiceType" />
    <result column="invoice_form" jdbcType="INTEGER" property="invoiceForm" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="tax_number" jdbcType="INTEGER" property="taxNumber" />
    <result column="purchaser" jdbcType="VARCHAR" property="purchaser" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, purchase_invoice_id, analysis_type, billing_date, invoice_code, invoice_number, 
    excluding_tax, included_tax, tax_amount, tax_rate, invoice_type, invoice_form, supplier_name, 
    tax_number, purchaser, fail_reason, create_time, creator, update_time, updater, delete_status
  </sql>

  <select id="selectFile" parameterType="java.lang.Integer" resultType="integer">
    select count(1)
    from purchase_invoice_analysis
    where admin_id = #{adminId} and delete_status = 0
  </select>

  <update id="updateStatus">
    update purchase_invoice_analysis
    set delete_status = 1,
        update_time = now(),
        updater = #{updater}
    where admin_id = #{adminId}
  </update>

  <update id="updateSuccessStatus">
    update purchase_invoice_analysis
    set delete_status = 1,
        update_time = now(),
        updater = #{updater}
    where admin_id = #{adminId} and analysis_type = 0
  </update>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_invoice_analysis
    where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from purchase_invoice_analysis
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PurchaseInvoiceAnalysis" useGeneratedKeys="true">
    insert into purchase_invoice_analysis (admin_id, analysis_type,
      billing_date, invoice_code, invoice_number, 
      excluding_tax, included_tax, tax_amount, 
      tax_rate, invoice_type, invoice_form, 
      supplier_name, tax_number, purchaser, 
      fail_reason, create_time, creator, 
      update_time, updater, delete_status , type,invoice_type_face
      )
    values (#{adminId},  #{analysisType,jdbcType=INTEGER},
      #{billingDate,jdbcType=VARCHAR}, #{invoiceCode,jdbcType=VARCHAR}, #{invoiceNumber,jdbcType=VARCHAR},
      #{excludingTax,jdbcType=DECIMAL}, #{includedTax,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, 
      #{taxRate,jdbcType=INTEGER}, #{invoiceType,jdbcType=INTEGER}, #{invoiceForm,jdbcType=INTEGER}, 
      #{supplierName,jdbcType=VARCHAR}, #{taxNumber,jdbcType=INTEGER}, #{purchaser,jdbcType=VARCHAR}, 
      #{failReason,jdbcType=VARCHAR}, now(), #{creator,jdbcType=VARCHAR},
      now(), #{updater,jdbcType=VARCHAR}, #{deleteStatus,jdbcType=INTEGER},#{type,jdbcType=INTEGER},#{invoiceTypeFace}
      )
  </insert>

  <select id="selectList" resultType="net.summerfarm.model.domain.PurchaseInvoiceAnalysis">
    select id,admin_id adminId, analysis_type analysisType,
           billing_date billingDate, invoice_code invoiceCode, invoice_number invoiceNumber,
           excluding_tax excludingTax, included_tax includedTax, tax_amount taxAmount,
           tax_rate taxRate, invoice_type invoiceType, invoice_form invoiceForm,
           supplier_name supplierName, tax_number taxNumber, purchaser purchaser,
           fail_reason failReason, delete_status , type,invoice_type_face invoiceTypeFace
    from purchase_invoice_analysis
    where admin_id = #{adminId} and analysis_type = #{analysisType} and delete_status = 0
  </select>

  <select id="selectIncludedTaxSum" resultType="decimal">
    select sum(included_tax) as includedTaxSum
    from purchase_invoice_analysis
    where admin_id = #{adminId} and analysis_type = #{analysisType} and delete_status = 0
  </select>

  <select id="selectHaveMoney" resultType="integer">
    select count(1)
    from purchase_invoice_analysis
    where admin_id = #{adminId} and analysis_type = #{analysisType} and delete_status = 0
  </select>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PurchaseInvoiceAnalysis" useGeneratedKeys="true">
    insert into purchase_invoice_analysis
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="purchaseInvoiceId != null">
        purchase_invoice_id,
      </if>
      <if test="analysisType != null">
        analysis_type,
      </if>
      <if test="billingDate != null">
        billing_date,
      </if>
      <if test="invoiceCode != null">
        invoice_code,
      </if>
      <if test="invoiceNumber != null">
        invoice_number,
      </if>
      <if test="excludingTax != null">
        excluding_tax,
      </if>
      <if test="includedTax != null">
        included_tax,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="invoiceForm != null">
        invoice_form,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="taxNumber != null">
        tax_number,
      </if>
      <if test="purchaser != null">
        purchaser,
      </if>
      <if test="failReason != null">
        fail_reason,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="purchaseInvoiceId != null">
        #{purchaseInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="analysisType != null">
        #{analysisType,jdbcType=INTEGER},
      </if>
      <if test="billingDate != null">
        #{billingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNumber != null">
        #{invoiceNumber,jdbcType=VARCHAR},
      </if>
      <if test="excludingTax != null">
        #{excludingTax,jdbcType=DECIMAL},
      </if>
      <if test="includedTax != null">
        #{includedTax,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="invoiceForm != null">
        #{invoiceForm,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="taxNumber != null">
        #{taxNumber,jdbcType=INTEGER},
      </if>
      <if test="purchaser != null">
        #{purchaser,jdbcType=VARCHAR},
      </if>
      <if test="failReason != null">
        #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PurchaseInvoiceAnalysis">
    update purchase_invoice_analysis
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="purchaseInvoiceId != null">
        purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="analysisType != null">
        analysis_type = #{analysisType,jdbcType=INTEGER},
      </if>
      <if test="billingDate != null">
        billing_date = #{billingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceCode != null">
        invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNumber != null">
        invoice_number = #{invoiceNumber,jdbcType=VARCHAR},
      </if>
      <if test="excludingTax != null">
        excluding_tax = #{excludingTax,jdbcType=DECIMAL},
      </if>
      <if test="includedTax != null">
        included_tax = #{includedTax,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="invoiceForm != null">
        invoice_form = #{invoiceForm,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="taxNumber != null">
        tax_number = #{taxNumber,jdbcType=INTEGER},
      </if>
      <if test="purchaser != null">
        purchaser = #{purchaser,jdbcType=VARCHAR},
      </if>
      <if test="failReason != null">
        fail_reason = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deleteStatus != null">
        delete_status = #{deleteStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PurchaseInvoiceAnalysis">
    update purchase_invoice_analysis
    set admin_id = #{admin_id,jdbcType=INTEGER},
      purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER},
      analysis_type = #{analysisType,jdbcType=INTEGER},
      billing_date = #{billingDate,jdbcType=TIMESTAMP},
      invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      invoice_number = #{invoiceNumber,jdbcType=VARCHAR},
      excluding_tax = #{excludingTax,jdbcType=DECIMAL},
      included_tax = #{includedTax,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      tax_rate = #{taxRate,jdbcType=INTEGER},
      invoice_type = #{invoiceType,jdbcType=INTEGER},
      invoice_form = #{invoiceForm,jdbcType=INTEGER},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      tax_number = #{taxNumber,jdbcType=INTEGER},
      purchaser = #{purchaser,jdbcType=VARCHAR},
      fail_reason = #{failReason,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      delete_status = #{deleteStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectSameTime" resultType="integer">
    select count(1)
    from purchase_invoice_analysis
    where invoice_code = #{invoiceCode,jdbcType=VARCHAR} and invoice_number = #{invoiceNumber,jdbcType=VARCHAR} and delete_status = 0 and analysis_type = 0
  </select>
</mapper>