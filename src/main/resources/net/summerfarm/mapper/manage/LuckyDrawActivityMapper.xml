<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.LuckyDrawActivityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.LuckyDrawActivity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="preheat_time" jdbcType="TIMESTAMP" property="preheatTime" />
    <result column="rule" jdbcType="VARCHAR" property="rule" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="buoy_image" jdbcType="VARCHAR" property="buoyImage" />
    <result column="preheat_background" jdbcType="VARCHAR" property="preheatBackground" />
    <result column="end_background" jdbcType="VARCHAR" property="endBackground" />
    <result column="no_draw_background" jdbcType="VARCHAR" property="noDrawBackground" />
    <result column="draw_background" jdbcType="VARCHAR" property="drawBackground" />
    <result column="shard_image" jdbcType="VARCHAR" property="shardImage" />
    <result column="shard_title" jdbcType="VARCHAR" property="shardTitle" />
    <result column="shard_remake" jdbcType="VARCHAR" property="shardRemake" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, name, start_time, end_time, preheat_time, rule, type, buoy_image, preheat_background, 
    end_background, no_draw_background, draw_background, shard_image, shard_title, shard_remake, 
    status, creator, updater, create_time, update_time
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lucky_draw_activity
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lucky_draw_activity
    where id = #{id,jdbcType=BIGINT}
  </delete>
 
  <insert id="insert" parameterType="net.summerfarm.model.domain.LuckyDrawActivity">
    insert into lucky_draw_activity (id, name, start_time, 
      end_time, preheat_time, rule, 
      type, buoy_image, preheat_background, 
      end_background, no_draw_background, draw_background, 
      shard_image, shard_title, shard_remake, 
      status, creator, updater, 
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{preheatTime,jdbcType=TIMESTAMP}, #{rule,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{buoyImage,jdbcType=VARCHAR}, #{preheatBackground,jdbcType=VARCHAR}, 
      #{endBackground,jdbcType=VARCHAR}, #{noDrawBackground,jdbcType=VARCHAR}, #{drawBackground,jdbcType=VARCHAR}, 
      #{shardImage,jdbcType=VARCHAR}, #{shardTitle,jdbcType=VARCHAR}, #{shardRemake,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.LuckyDrawActivity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lucky_draw_activity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="preheatTime != null">
        preheat_time,
      </if>
      <if test="rule != null">
        rule,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="buoyImage != null">
        buoy_image,
      </if>
      <if test="preheatBackground != null">
        preheat_background,
      </if>
      <if test="endBackground != null">
        end_background,
      </if>
      <if test="noDrawBackground != null">
        no_draw_background,
      </if>
      <if test="drawBackground != null">
        draw_background,
      </if>
      <if test="shardImage != null">
        shard_image,
      </if>
      <if test="shardTitle != null">
        shard_title,
      </if>
      <if test="shardRemake != null">
        shard_remake,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preheatTime != null">
        #{preheatTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rule != null">
        #{rule,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="buoyImage != null">
        #{buoyImage,jdbcType=VARCHAR},
      </if>
      <if test="preheatBackground != null">
        #{preheatBackground,jdbcType=VARCHAR},
      </if>
      <if test="endBackground != null">
        #{endBackground,jdbcType=VARCHAR},
      </if>
      <if test="noDrawBackground != null">
        #{noDrawBackground,jdbcType=VARCHAR},
      </if>
      <if test="drawBackground != null">
        #{drawBackground,jdbcType=VARCHAR},
      </if>
      <if test="shardImage != null">
        #{shardImage,jdbcType=VARCHAR},
      </if>
      <if test="shardTitle != null">
        #{shardTitle,jdbcType=VARCHAR},
      </if>
      <if test="shardRemake != null">
        #{shardRemake,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.LuckyDrawActivity">
    update lucky_draw_activity
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preheatTime != null">
        preheat_time = #{preheatTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rule != null">
        rule = #{rule,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="buoyImage != null">
        buoy_image = #{buoyImage,jdbcType=VARCHAR},
      </if>
      <if test="preheatBackground != null">
        preheat_background = #{preheatBackground,jdbcType=VARCHAR},
      </if>
      <if test="endBackground != null">
        end_background = #{endBackground,jdbcType=VARCHAR},
      </if>
      <if test="noDrawBackground != null">
        no_draw_background = #{noDrawBackground,jdbcType=VARCHAR},
      </if>
      <if test="drawBackground != null">
        draw_background = #{drawBackground,jdbcType=VARCHAR},
      </if>
      <if test="shardImage != null">
        shard_image = #{shardImage,jdbcType=VARCHAR},
      </if>
      <if test="shardTitle != null">
        shard_title = #{shardTitle,jdbcType=VARCHAR},
      </if>
      <if test="shardRemake != null">
        shard_remake = #{shardRemake,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.LuckyDrawActivity">
    update lucky_draw_activity
    set name = #{name,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      preheat_time = #{preheatTime,jdbcType=TIMESTAMP},
      rule = #{rule,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      buoy_image = #{buoyImage,jdbcType=VARCHAR},
      preheat_background = #{preheatBackground,jdbcType=VARCHAR},
      end_background = #{endBackground,jdbcType=VARCHAR},
      no_draw_background = #{noDrawBackground,jdbcType=VARCHAR},
      draw_background = #{drawBackground,jdbcType=VARCHAR},
      shard_image = #{shardImage,jdbcType=VARCHAR},
      shard_title = #{shardTitle,jdbcType=VARCHAR},
      shard_remake = #{shardRemake,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      creator = #{creator,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getPage" resultType="net.summerfarm.model.vo.LuckDrawActivityVO" parameterType="net.summerfarm.model.input.LuckDrawActivityPageQuery">
    select id, name, start_time startTime, end_time endTime, preheat_time preheatTime, rule, type, creator, updater from lucky_draw_activity
    <where>
      status = 0
      <if test="name != null">
        and `name` like CONCAT(#{name,jdbcType=VARCHAR},'%')
      </if>
      <if test="activityId != null">
        and id = #{activityId,jdbcType=BIGINT}
      </if>
      <if test="creator != null">
        and creator like CONCAT(#{creator,jdbcType=VARCHAR},'%')
      </if>
      <if test="status != null and status == 1">
        and preheat_time > now()
      </if>
      <if test="status != null and status == 2">
        and now() >= preheat_time and start_time > now()
      </if>
      <if test="status != null and status == 3">
        and now() >= start_time and end_time > now()
      </if>
      <if test="status != null and status == 4">
        and now() >= end_time
      </if>
    </where>
    order by id DESC
    </select>

  <select id="getCount" resultType="int" parameterType="net.summerfarm.model.domain.LuckyDrawActivity">
    select count(0) from lucky_draw_activity
    <where>
      status = 0
      <if test="type != null">
        and `type` = #{type,jdbcType=TINYINT}
      </if>
      <if test="preheatTime != null">
        and (((preheat_time &gt;= #{preheatTime,jdbcType=TIMESTAMP} AND preheat_time &lt;= #{endTime,jdbcType=TIMESTAMP})
        OR (preheat_time &lt;= #{preheatTime,jdbcType=TIMESTAMP} AND end_time &gt;= #{endTime,jdbcType=TIMESTAMP})
        OR (end_time &gt;= #{preheatTime,jdbcType=TIMESTAMP} AND end_time &lt;= #{endTime,jdbcType=TIMESTAMP})))
      </if>
      <if test="id != null">
        and id != #{id,jdbcType=BIGINT}
      </if>
    </where>
  </select>
</mapper>