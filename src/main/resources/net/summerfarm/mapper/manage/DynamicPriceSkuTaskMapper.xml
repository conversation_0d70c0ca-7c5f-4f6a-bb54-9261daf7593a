<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.DynamicPriceSkuTaskMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.DynamicPriceSkuTask">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="task_id" jdbcType="BIGINT" property="taskId"/>
    <result column="sku" jdbcType="VARCHAR" property="sku"/>
    <result column="field_value_json" jdbcType="VARCHAR" property="fieldValueJson"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    `id`
    , `task_id`, `sku`, `field_value_json`, `create_time`, `update_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_sku_task
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete
    from dynamic_price_sku_task
    where `id` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.DynamicPriceSkuTask">
    insert into dynamic_price_sku_task (`id`, `task_id`, `sku`,
                                        `field_value_json`, `create_time`, `update_time`)
    values (#{id,jdbcType=BIGINT}, #{taskId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR},
            #{fieldValueJson,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.DynamicPriceSkuTask" useGeneratedKeys="true" keyProperty="id">
    insert into dynamic_price_sku_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="taskId != null">
        `task_id`,
      </if>
      <if test="sku != null">
        `sku`,
      </if>
      <if test="fieldValueJson != null">
        `field_value_json`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="fieldValueJson != null">
        #{fieldValueJson,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective"
    parameterType="net.summerfarm.model.domain.DynamicPriceSkuTask">
    update dynamic_price_sku_task
    <set>
      <if test="taskId != null">
        `task_id` = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        `sku` = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="fieldValueJson != null">
        `field_value_json` = #{fieldValueJson,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.DynamicPriceSkuTask">
    update dynamic_price_sku_task
    set `task_id`          = #{taskId,jdbcType=BIGINT},
        `sku`              = #{sku,jdbcType=VARCHAR},
        `field_value_json` = #{fieldValueJson,jdbcType=VARCHAR},
        `create_time`      = #{createTime,jdbcType=TIMESTAMP},
        `update_time`      = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByTaskId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dynamic_price_sku_task
    where task_id = #{taskId}
  </select>

</mapper>