<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.MerchantPoolInfoMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.MerchantPoolInfo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="create_way" jdbcType="TINYINT" property="createWay"/>
    <result column="update_way" jdbcType="TINYINT" property="updateWay"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="data_source" jdbcType="TINYINT" property="dataSource"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="version" jdbcType="INTEGER" property="version"/>
    <result column="creator" jdbcType="VARCHAR" property="creator"/>
    <result column="updater" jdbcType="VARCHAR" property="updater"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    , `name`, `create_way`, `update_way`, `status`, `remark`, `version`,
    `creator`, `updater`, `create_time`, `update_time`, data_source
  </sql>

  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_pool_info
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteById" parameterType="java.lang.Long">
    delete
    from merchant_pool_info
    where `id` = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="net.summerfarm.model.domain.MerchantPoolInfo" keyProperty="id"
    useGeneratedKeys="true">
    insert into merchant_pool_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="createWay != null">
        `create_way`,
      </if>
      <if test="updateWay != null">
        `update_way`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="remark != null">
        `remark`,
      </if>
      <if test="version != null">
        `version`,
      </if>
      <if test="creator != null">
        `creator`,
      </if>
      <if test="updater != null">
        `updater`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="createWay != null">
        #{createWay,jdbcType=TINYINT},
      </if>
      <if test="updateWay != null">
        #{updateWay,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="net.summerfarm.model.domain.MerchantPoolInfo">
    update merchant_pool_info
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="createWay != null">
        `create_way` = #{createWay,jdbcType=TINYINT},
      </if>
      <if test="updateWay != null">
        `update_way` = #{updateWay,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        `remark` = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        `version` = #{version,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        `creator` = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        `updater` = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from
    merchant_pool_info
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="name != null">
        and `name` like CONCAT(#{name},'%')
      </if>
      <if test="creator != null">
        and `creator` like CONCAT(#{creator},'%')
      </if>
      <if test="createWay != null">
        and `create_way` = #{createWay}
      </if>
      <if test="updateWay != null">
        and `update_way` = #{updateWay}
      </if>
      <if test="status != null">
        and `status` = #{status}
      </if>
    </where>
    order by id desc
  </select>

  <select id="listCreators" resultType="string">
    select distinct creator
    from merchant_pool_info
    where creator like CONCAT(#{creator}, '%')
  </select>

  <select id="listAllOnlineUpdate" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from merchant_pool_info
    where update_way = 1 and data_source = 0
  </select>
  <select id="selectByName" resultMap="BaseResultMap">
    select distinct id,name
    from merchant_pool_info
    where name like CONCAT('%',#{merchantName}, '%')
  </select>

  <select id="getNameByIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from merchant_pool_info
    where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
  </select>
</mapper>
