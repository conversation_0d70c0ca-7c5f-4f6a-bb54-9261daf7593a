<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PurchaseInvoiceFileMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PurchaseInvoiceFile">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchase_invoice_id" jdbcType="INTEGER" property="purchaseInvoiceId" />
    <result column="file_address" jdbcType="VARCHAR" property="fileAddress" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, purchase_invoice_id, file_address, create_time, creator, update_time, updater, 
    delete_status
  </sql>

  <select id="selectFile" resultType="integer">
    select count(1)
    from purchase_invoice_file
    where purchase_invoice_id = #{id} and delete_status = 0
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_invoice_file
    where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from purchase_invoice_file
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PurchaseInvoiceFile" useGeneratedKeys="true">
    insert into purchase_invoice_file (purchase_invoice_id, file_address, create_time,
      creator, update_time, updater, 
      delete_status)
    values (#{purchaseInvoiceId,jdbcType=INTEGER}, #{fileAddress,jdbcType=VARCHAR}, now(),
      #{creator,jdbcType=VARCHAR}, now(), #{updater,jdbcType=VARCHAR}, 0)
  </insert>

  <update id="delete">
    update purchase_invoice_file
    set  delete_status = 1,
        updater = #{updater},
        update_time = now()
    where purchase_invoice_id = #{id} and delete_status = 0
  </update>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PurchaseInvoiceFile" useGeneratedKeys="true">
    insert into purchase_invoice_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="purchaseInvoiceId != null">
        purchase_invoice_id,
      </if>
      <if test="fileAddress != null">
        file_address,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="purchaseInvoiceId != null">
        #{purchaseInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="fileAddress != null">
        #{fileAddress,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PurchaseInvoiceFile">
    update purchase_invoice_file
    <set>
      <if test="purchaseInvoiceId != null">
        purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="fileAddress != null">
        file_address = #{fileAddress,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deleteStatus != null">
        delete_status = #{deleteStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PurchaseInvoiceFile">
    update purchase_invoice_file
    set purchase_invoice_id = #{purchaseInvoiceId,jdbcType=INTEGER},
      file_address = #{fileAddress,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      delete_status = #{deleteStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>