<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PurchasePlanCostChangeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PurchasePlanCostChange">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchase_plan_id" jdbcType="INTEGER" property="purchasePlanId" />
    <result column="old_total_cost" jdbcType="DECIMAL" property="oldTotalCost" />
    <result column="new_total_cost" jdbcType="DECIMAL" property="newTotalCost" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, purchase_plan_id, old_total_cost, new_total_cost, `status`, remark, auditor, 
    audit_time, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_plan_cost_change
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from purchase_plan_cost_change
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PurchasePlanCostChange" useGeneratedKeys="true">
    insert into purchase_plan_cost_change (purchase_plan_id, old_total_cost, new_total_cost, 
      `status`, remark, auditor, 
      audit_time, creator, create_time
      )
    values (#{purchasePlanId,jdbcType=INTEGER}, #{oldTotalCost,jdbcType=DECIMAL}, #{newTotalCost,jdbcType=DECIMAL}, 
      #{status,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{auditor,jdbcType=VARCHAR}, 
      #{auditTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.PurchasePlanCostChange" useGeneratedKeys="true">
    insert into purchase_plan_cost_change
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="purchasePlanId != null">
        purchase_plan_id,
      </if>
      <if test="oldTotalCost != null">
        old_total_cost,
      </if>
      <if test="newTotalCost != null">
        new_total_cost,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="purchasePlanId != null">
        #{purchasePlanId,jdbcType=INTEGER},
      </if>
      <if test="oldTotalCost != null">
        #{oldTotalCost,jdbcType=DECIMAL},
      </if>
      <if test="newTotalCost != null">
        #{newTotalCost,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.PurchasePlanCostChange">
    update purchase_plan_cost_change
    <set>
      <if test="purchasePlanId != null">
        purchase_plan_id = #{purchasePlanId,jdbcType=INTEGER},
      </if>
      <if test="oldTotalCost != null">
        old_total_cost = #{oldTotalCost,jdbcType=DECIMAL},
      </if>
      <if test="newTotalCost != null">
        new_total_cost = #{newTotalCost,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.PurchasePlanCostChange">
    update purchase_plan_cost_change
    set purchase_plan_id = #{purchasePlanId,jdbcType=INTEGER},
      old_total_cost = #{oldTotalCost,jdbcType=DECIMAL},
      new_total_cost = #{newTotalCost,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      auditor = #{auditor,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectVO" parameterType="net.summerfarm.model.input.PurchasePlanCostChangeReq" resultType="net.summerfarm.model.vo.PurchasePlanCostChangeVO">
    select ppcc.id,
      pp.purchase_no      purchaseNo,
      pp.sku,
      p.pd_name           pdName,
      i.weight,
      ppcc.new_total_cost newTotalCost,
      ppcc.old_total_cost oldTotalCost,
      ppcc.status,
      ppcc.auditor,
      ppcc.remark,
      ppcc.audit_time     auditTime,
      ppcc.creator,
      ppcc.create_time    createTime,
      s.name supplier,
      pp.quantity
    from purchase_plan_cost_change ppcc
    left join purchases_plan pp on pp.id = ppcc.purchase_plan_id
    left join inventory i on i.sku = pp.sku
    left join products p on i.pd_id = p.pd_id
    left join supplier s on pp.supplier_id = s.id
    <where>
      and pp.plan_status = 1
      and exists(select 1 from purchases where purchase_no = pp.purchase_no and tenant_id=#{tenantId})
      <if test="id != null">
        and ppcc.id = #{id}
      </if>
      <if test="status != null">
        and ppcc.status = #{status}
      </if>
      <if test="purchaseNo != null">
        and pp.purchase_no = #{purchaseNo}
      </if>
      <if test="sku != null">
        and pp.sku = #{sku}
      </if>
    </where>
    order by ppcc.id desc
  </select>
  <select id="select" parameterType="net.summerfarm.model.domain.PurchasePlanCostChange" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from purchase_plan_cost_change
    <where>
      <if test="purchasePlanId != null">
        and purchase_plan_id = #{purchasePlanId}
      </if>
      <if test="status != null">
        and `status` = #{status}
      </if>
    </where>
  </select>
</mapper>