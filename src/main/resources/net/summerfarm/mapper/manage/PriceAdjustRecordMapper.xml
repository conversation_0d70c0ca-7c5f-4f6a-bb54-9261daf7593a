<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PriceAdjustRecordMapper">

    <insert id="insertBatch">
        INSERT INTO product_price_adjustment_record
        (sku,area_no,price,original_price,ladder_price,original_ladder_price,interest_rate_new,interest_rate_old,auto_flag_new,auto_flag_old)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.sku},#{item.areaNo},#{item.price},#{item.originalPrice},#{item.ladderPrice},#{item.originalLadderPrice},#{item.interestRateNew},#{item.interestRateOld},#{item.autoFlagNew},#{item.autoFlagOld})
        </foreach>
    </insert>

</mapper>