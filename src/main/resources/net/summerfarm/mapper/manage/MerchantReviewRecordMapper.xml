<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="net.summerfarm.mapper.manage.MerchantReviewRecordMapper">

    <insert id="insert" parameterType="net.summerfarm.model.domain.MerchantReviewRecord">
        INSERT INTO merchant_review_record(m_id, islock, audit_user, audit_name, remark)
        VALUES(#{mId}, #{isLock}, #{auditUser}, #{auditName}, #{remark})
    </insert>

</mapper>