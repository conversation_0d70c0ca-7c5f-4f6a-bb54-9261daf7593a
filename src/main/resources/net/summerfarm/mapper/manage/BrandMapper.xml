<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.BrandMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.Brand" >
    <id column="brand_id" property="brandId" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="alias" property="alias" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="DATE" />
    <result column="logo_path" property="logoPath" jdbcType="VARCHAR" />
    <result column="priority" property="priority" jdbcType="INTEGER" />
    <result column="firstcharacter" property="firstcharacter" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    brand_id, name, alias, logo_path, priority, firstcharacter, create_time
  </sql>


  <select id="select" resultMap="BaseResultMap" parameterType="net.summerfarm.model.domain.Brand">
    SELECT
    <include refid="Base_Column_List"/>
    FROM brand
    <where>
      <if test="name != null and name != ''">
        AND name like CONCAT(#{name,jdbcType=VARCHAR}, '%')
      </if>
    </where>
  </select>


  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.Brand" >
    insert into brand
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="name != null" >
        name,
      </if>
      <if test="alias != null" >
        alias,
      </if>
      <if test="logoPath != null" >
        logo_path,
      </if>
      <if test="priority != null" >
        priority,
      </if>
      <if test="firstcharacter != null" >
        firstcharacter,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="alias != null" >
        #{alias,jdbcType=VARCHAR},
      </if>
      <if test="logoPath != null" >
        #{logoPath,jdbcType=VARCHAR},
      </if>
      <if test="priority != null" >
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="firstcharacter != null" >
        #{firstcharacter,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.Brand" >
    update brand
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="alias != null" >
        alias = #{alias,jdbcType=VARCHAR},
      </if>
      <if test="logoPath != null" >
        logo_path = #{logoPath,jdbcType=VARCHAR},
      </if>
      <if test="priority != null" >
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="firstcharacter != null" >
        firstcharacter = #{firstcharacter,jdbcType=VARCHAR},
      </if>
    </set>
    where brand_id = #{brandId,jdbcType=INTEGER}
  </update>

  <delete id="delete" parameterType="java.lang.Integer">
    delete from brand where brand_id=#{brandId,jdbcType=INTEGER}
  </delete>

<!--auto generated by MybatisCodeHelper on 2022-02-14-->
  <select id="selectByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from brand
    where `name`=#{name,jdbcType=VARCHAR}
  </select>

<!--auto generated by MybatisCodeHelper on 2022-07-19-->
  <select id="selectByBrandId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from brand
    where brand_id=#{brandId,jdbcType=BIGINT}
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from brand
  </select>
</mapper>