<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.TmsTaskMapper">


    <insert id="insertTmsTask" parameterType="net.summerfarm.model.domain.TmsTask" useGeneratedKeys="true" keyProperty="id">
        insert into  tms_task (delivery_time,`path`,`sku_cnt`,`concat_cnt` ,`task_status` , `addtime`,`pick_up_time`,store_no)
        values (#{deliveryTime},#{path},#{skuCnt},#{concatCnt},#{taskStatus},#{addTime},#{pickUpTime},#{storeNo})
    </insert>
    <select id="selectCountTmsTask" resultType="java.lang.Integer">
        select count(*) from  tms_task where delivery_time =#{deliverTime} and store_no =#{storeNo}
    </select>


    <select id="selectTmsTask" resultType="net.summerfarm.model.domain.TmsTask">
        select addtime addTime,
               pick_up_time pickUpTime,
               loading_photos loadingPhotos,
               out_warehouse_temperature outWarehouseTemperature,
               over_out_time_reason overOutTimeReason,
               out_time outTime,
               punch_time punchTime,
               id
        from  tms_task
        where delivery_time =#{deliverTime} and store_no =#{storeNo} and path =#{path}
    </select>

    <select id="selectTmsTaskByPathId" resultType="integer">
        SELECT
            count(*)
        FROM
            tms_task tt
                LEFT JOIN delivery_path dp ON tt.delivery_time = dp.delivery_time
                AND tt.path = dp.path
                AND tt.store_no = dp.store_no
        where dp.id = #{id}
    </select>

    <select id="selectTodayTmsTask" resultType="net.summerfarm.model.domain.TmsTask">
        SELECT
            t.path,
            t.out_time outTime,
            t.store_no storeNo
        FROM
            tms_task t
        WHERE
            t.delivery_time = CURDATE()
    </select>

    <select id="selectTodayDownTmsTask" resultType="net.summerfarm.model.vo.OutTimeMonitorDownloadVO">
        SELECT
            tt.delivery_time deliveryTime,
            tt.path,
            tt.concat_cnt shopNum,
            td.`name` driver,
            td.phone driverPhone,
            tt.out_time outTime,
            tt.id,
            tt.store_no storeNo
        FROM
            tms_task tt
                LEFT JOIN delivery_car_path dcp ON dcp.delivery_time = tt.delivery_time
                AND dcp.store_no = tt.store_no
                AND dcp.path = tt.path
                LEFT JOIN tms_driver td ON td.id = dcp.delivery_car_id
        WHERE
            tt.delivery_time = #{day}
    </select>

    <select id="selectTmsTaskByDateStatus" resultType="net.summerfarm.model.domain.TmsTask">
        SELECT
            tt.delivery_time deliveryTime,
            tt.store_no storeNo,
            tt.task_status taskStatus,
            tt.path
        FROM
            tms_task tt
        WHERE
            tt.delivery_time = #{deliveryTime}
          <if test="status != null">
              and task_status = #{status}
          </if>
    </select>

    <update id="update" parameterType="net.summerfarm.model.domain.TmsTask">
        update tms_task set task_status = #{taskStatus}
        where delivery_time = #{deliveryTime} and path =#{path} and store_no = #{storeNo}
    </update>
</mapper>