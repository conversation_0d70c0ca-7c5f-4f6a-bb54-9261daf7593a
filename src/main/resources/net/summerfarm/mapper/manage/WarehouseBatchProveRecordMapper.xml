<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.WarehouseBatchProveRecordMapper">

    <insert id="batchInsert">
        insert into warehouse_batch_prove_record (create_time, sku, batch, production_date,
        quality_date, quality_inspection_report, customs_declaration_certificate,
        nucleic_acid_detection,disinfection_certificate,supervision_warehouse_certificate,
        detection_result,sampling_base,number_samples,inhibition_rate,pesticide_residue_pictures,creator,
        `type`,source_id
        ) values
        <foreach collection="list" separator="," item="item">
            (now(), #{item.sku}, #{item.batch},
            #{item.productionDate},#{item.qualityDate},
            #{item.qualityInspectionReport}, #{item.customsDeclarationCertificate}
            , #{item.nucleicAcidDetection}, #{item.disinfectionCertificate}
            , #{item.supervisionWarehouseCertificate}, #{item.detectionResult}
            , #{item.samplingBase}, #{item.numberSamples}, #{item.inhibitionRate}
            , #{item.pesticideResiduePictures}, #{item.creator}, #{item.type}
            , #{item.sourceId}
            )
        </foreach>
    </insert>
    <select id="selectByBatchAndSku" resultType="net.summerfarm.model.vo.WarehouseBatchProveRecordVO">
        select
        bp.id,
        bp.sku,
        bp.batch,
        bp.customs_declaration_certificate customsDeclarationCertificate,
        bp.disinfection_certificate disinfectionCertificate,
        bp.quality_inspection_report qualityInspectionReport,
        bp.nucleic_acid_detection nucleicAcidDetection,
        bp.supervision_warehouse_certificate supervisionWarehouseCertificate,
        bp.production_date productionDate,
        bp.quality_date qualityDate,
        bp.detection_result detectionResult,
        bp.sampling_base samplingBase,
        bp.number_samples numberSamples,
        bp.inhibition_rate inhibitionRate,
        bp.pesticide_residue_pictures pesticideResiduePictures
        from (
        select
        batch,
        sku,
        customs_declaration_certificate ,
        disinfection_certificate ,
        quality_inspection_report ,
        nucleic_acid_detection ,
        supervision_warehouse_certificate ,
        id,production_date ,
        quality_date,
        detection_result ,
        sampling_base ,
        number_samples ,
        inhibition_rate ,
        pesticide_residue_pictures
        from warehouse_batch_prove_record
        <where>
            <choose>
                <when test="isSupplier != null">
                    AND type = 9
                </when>
                <otherwise>
                    AND type != 9
                </otherwise>
            </choose>
            <if test="batch != null">
                and batch = #{batch}
            </if>
            <if test="sku!=null">
                and sku = #{sku}
            </if>
            <if test="qualityDate!=null">
                and quality_date = #{qualityDate}
            </if>
            <if test="productionDate!=null">
                and production_date = #{productionDate}
            </if>
            <if test="inType!=null">
                and `type` = #{inType}
            </if>
            <if test="processId!=null">
                and source_id = #{processId}
            </if>
            <if test="batches != null and batches.size != 0 ">
                and batch in
                <foreach collection="batches" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="productionDates != null and productionDates.size != 0 ">
                and production_date in
                <foreach collection="productionDates" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="productionDateStart!=null">
                and production_date >= #{productionDateStart}
            </if>
            <if test="productionDateEnd!=null">
                and production_date &lt;= #{productionDateEnd}
            </if>
            and (customs_declaration_certificate is not null or
            disinfection_certificate is not null or
            quality_inspection_report is not null or
            nucleic_acid_detection is not null or
            supervision_warehouse_certificate is not null or
            detection_result is not null or
            sampling_base is not null or
            number_samples is not null or
            inhibition_rate is not null or
            pesticide_residue_pictures is not null)
        </where>
        order by id desc) bp
        group by batch,production_date
    </select>
    <select id="selectByArrangeItemDetailId" resultType="net.summerfarm.model.vo.WarehouseBatchProveRecordVO">
        select customs_declaration_certificate   customsDeclarationCertificate,
               disinfection_certificate          disinfectionCertificate,
               quality_inspection_report         qualityInspectionReport,
               nucleic_acid_detection            nucleicAcidDetection,
               supervision_warehouse_certificate supervisionWarehouseCertificate,
               pesticide_residue_pictures        pesticideResiduePictures
        from warehouse_batch_prove_record
        where type = 9
          and source_id = #{stockArrangeItemDetailId}
    </select>

    <insert id="insert">
        insert into warehouse_batch_prove_record (create_time, sku, batch, production_date,
                                                  quality_date, quality_inspection_report,
                                                  customs_declaration_certificate,
                                                  nucleic_acid_detection, disinfection_certificate,
                                                  supervision_warehouse_certificate,
                                                  detection_result, sampling_base, number_samples, inhibition_rate,
                                                  pesticide_residue_pictures, creator,
                                                  `type`, source_id)
        values ( now(), #{item.sku}, #{item.batch},
                 #{item.productionDate}, #{item.qualityDate},
                 #{item.qualityInspectionReport}, #{item.customsDeclarationCertificate}
               , #{item.nucleicAcidDetection}, #{item.disinfectionCertificate}
               , #{item.supervisionWarehouseCertificate}, #{item.detectionResult}
               , #{item.samplingBase}, #{item.numberSamples}, #{item.inhibitionRate}
               , #{item.pesticideResiduePictures}, #{item.creator}, #{item.type}
               , #{item.sourceId})
    </insert>

    <update id="updateByDetailId">
        update warehouse_batch_prove_record
        set quality_inspection_report         = #{item.qualityInspectionReport},
            customs_declaration_certificate   = #{item.customsDeclarationCertificate},
            nucleic_acid_detection            = #{item.nucleicAcidDetection},
            disinfection_certificate          = #{item.disinfectionCertificate},
            supervision_warehouse_certificate = #{item.supervisionWarehouseCertificate},
            detection_result                  = #{item.detectionResult},
            sampling_base                     = #{item.samplingBase},
            number_samples                    = #{item.numberSamples},
            inhibition_rate                   = #{item.inhibitionRate},
            pesticide_residue_pictures        = #{item.pesticideResiduePictures}
        where type = 9
          and source_id = #{item.sourceId}
    </update>

    <select id="selectDetail" resultType="net.summerfarm.model.vo.WarehouseBatchProveRecordVO">
        select id                                id,
               sku                               sku,
               batch                             batch,
               production_date                   productionDate,
               quality_date                      qualityDate,
               quality_inspection_report         qualityInspectionReport,
               customs_declaration_certificate   customsDeclarationCertificate,
               nucleic_acid_detection            nucleicAcidDetection,
               disinfection_certificate          disinfectionCertificate,
               supervision_warehouse_certificate supervisionWarehouseCertificate,
               detection_result                  detectionResult,
               sampling_base                     samplingBase,
               number_samples                    numberSamples,
               inhibition_rate                   inhibitionRate,
               pesticide_residue_pictures        pesticideResiduePictures,
               creator                           creator,
               `type`                            type,
               source_id                         sourceId
        from warehouse_batch_prove_record
        where type = 9
          and source_id = #{stockArrangeItemDetailId}
    </select>

    <select id="countByDetailId" resultType="int">
        select count(*)
        from warehouse_batch_prove_record
        where type = 9
          and source_id = #{stockArrangeItemDetailId}
    </select>

    <select id="selectInBatchProve" resultType="net.summerfarm.model.vo.WarehouseBatchProveRecordVO">
        select * from (select
        s.id, s.batch, s.sku, s.type,s.area_no areaNo,s.store_quantity quantity,s.quality_date
        qualityDate,s.production_date productionDate,s.lot_type lotType
        from
        (select *
        from store_record sr
        <where>
            <if test="warehouseNo != null">
                AND sr.area_no= #{warehouseNo}
            </if>
            <if test="sku != null">
                AND sr.sku= #{sku}
            </if>
            <if test="batch != null">
                and sr.batch = #{batch}
            </if>
            <if test="productionDateStart!=null">
                and sr.production_date >= #{productionDateStart}
            </if>
            <if test="productionDateEnd!=null">
                and sr.production_date &lt;= #{productionDateEnd}
            </if>
        </where>
        order by sr.id desc) s
        group by s.sku, s.batch, s.quality_date) t
        order by id desc
    </select>
</mapper>