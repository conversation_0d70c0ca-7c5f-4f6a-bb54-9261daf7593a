<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockStorageItemMapper">

    <resultMap id="WithDetail" type="net.summerfarm.module.pms.model.vo.AllocationOrderItemEntityVO">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="sku" property="sku" jdbcType="VARCHAR" />
        <result column="quantity" property="outQuantity" jdbcType="INTEGER" />
        <result column="task_no" property="listNo" jdbcType="INTEGER" />
        <collection property="stockAllocationItemDetails" column="id" javaType="ArrayList"
                    select="net.summerfarm.mapper.manage.StockStorageItemDetailMapper.select"/>
    </resultMap>

    <resultMap id="withDetail" type="net.summerfarm.model.vo.StockTaskProcessVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <collection property="processDetailVOS" column="id" javaType="ArrayList"
                    select="net.summerfarm.mapper.manage.StockStorageItemDetailMapper.selectProcess"/>
    </resultMap>

    <resultMap id="selectByStockTaskIdWithDetail" type="net.summerfarm.model.vo.StockTaskStorageItemVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="stock_task_id" property="stockTaskId" jdbcType="INTEGER"/>
        <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="pack" property="pack" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR" />
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="actual_quantity" property="actualQuantity" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="name_remakes" property="nameRemakes" />
        <result column="category_id" property="categoryId" jdbcType="INTEGER" />
        <result column="pd_id" property="pdId"/>
        <result column="storage_location" property="storageLocation"/>
        <result column="categoryType" property="categoryType"/>
        <result column="skuType" property="skuType"/>
        <result column="extType" property="extType"/>
        <result column="recycleState" property="recycleState"/>
        <result column="remark" property="remark"/>
        <result column="task_no" property="taskNo"/>
        <collection property="stockTaskItemDetailVOS" column="id" javaType="ArrayList"
                    select="net.summerfarm.mapper.manage.StockStorageItemMapper.selectByItemId"/>
    </resultMap>


    <resultMap id="getBatchStorageSkuDetail" type="net.summerfarm.model.vo.StockTaskStorageItemVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="stock_task_id" property="stockTaskId" jdbcType="INTEGER"/>
        <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="pack" property="pack" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR" />
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="actual_quantity" property="actualQuantity" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="name_remakes" property="nameRemakes" />
        <result column="category_id" property="categoryId" jdbcType="INTEGER" />
        <result column="pd_id" property="pdId"/>
        <result column="storage_location" property="storageLocation"/>
        <result column="categoryType" property="categoryType"/>
        <result column="skuType" property="skuType"/>
        <result column="extType" property="extType"/>
        <result column="recycleState" property="recycleState"/>
        <result column="remark" property="remark"/>
        <result column="task_no" property="taskNo"/>
    </resultMap>

    <resultMap id="VOMap" type="net.summerfarm.model.vo.StockTaskItemDetailVO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="stock_storage_item_id" property="stockTaskItemId" jdbcType="INTEGER"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="quality_date" property="qualityDate" jdbcType="DATE"/>
        <result column="actual_in_quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="production_date" property="productionDate" jdbcType="DATE" />
        <result column="gl_no" property="glNo"/>
        <result column="purchase_no" property="listNo"/>
    </resultMap>

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.StockStorageItem" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="sku" property="sku" jdbcType="VARCHAR" />
        <result column="stock_task_id" property="stockTaskId" jdbcType="VARCHAR" />
        <result column="stock_task_storage_id" property="stockTaskStorageId"/>
        <result column="quantity" property="quantity" jdbcType="INTEGER" />
        <result column="actual_quantity" property="actualQuantity" jdbcType="VARCHAR" />
        <result column="add_time" property="addTime" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    </resultMap>

    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="list.stockStorageItemId" keyColumn="id" >
        insert into stock_storage_item (stock_task_id, sku, quantity, add_time)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{stockTaskId}, #{item.sku}, #{item.outQuantity}, now())
        </foreach>
    </insert>
    <insert id="insert" useGeneratedKeys="true" keyProperty="entity.id" keyColumn = "id" >
        insert into stock_storage_item (stock_task_id, sku, quantity, add_time,remark)
        VALUES
        (#{stockTaskId}, #{entity.sku}, #{entity.outQuantity}, now(),#{entity.remark})
    </insert>

    <insert id="insertBean" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.model.domain.StockStorageItem">
        INSERT INTO stock_storage_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stockTaskId != null">
                stock_task_id,
            </if>
            <if test="sku != null">
                sku,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="actualQuantity != null">
                actual_quantity,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stockTaskId != null">
                #{stockTaskId},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="quantity != null">
                #{quantity},
            </if>
            <if test="actualQuantity != null">
                #{actualQuantity},
            </if>
            <if test="addTime != null">
                #{addTime},
            </if>
        </trim>
    </insert>
    <update id="update">
        update stock_storage_item
        <set >
            <if test="actualQuantity != null" >
                actual_quantity = #{actualQuantity},
            </if>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateQuantity">
        update stock_storage_item
        <set >
            <if test="amount != null">
                quantity = quantity + #{amount},
            </if>
        </set>
        where id = #{stockStorageItemId,jdbcType=INTEGER}
    </update>


    <select id="select" parameterType="string" resultMap="WithDetail">
    SELECT t.id, t.sku, t.quantity,st.task_no, ar2.status inStatus,t.remark
     ,ar1.status outStatus,s.in_store inStore,s.out_store outStore,t.actual_quantity actualInQuantity
    FROM  stock_storage_item t
    LEFT  JOIN stock_task st on st.id= t.stock_task_id
    LEFT  JOIN stock_allocation_list s on s.list_no=st.task_no
      LEFT  JOIN warehouse_stock_ext ar1 on ar1.sku=t.sku and ar1.warehouse_no = s.out_store
    LEFT  JOIN warehouse_stock_ext ar2 on ar2.sku=t.sku and ar2.warehouse_no = s.in_store
    WHERE st.task_no = #{listNo} and st.type= 10
  </select>

    <select id="selectByTaskId" resultMap="withDetail">
    SELECT  st.id stockTaskId,st.type,st.expect_time expectTime,a.realname recorder,st.task_no taskNo,st.area_no areaNo,st.addtime
    FROM  stock_task st
    LEFT JOIN  admin a on st.admin_id =a.admin_id
    where st.id= #{stockTaskId}
    </select>
    <select id="selectShipmentItem" resultType="net.summerfarm.model.domain.StockShipmentItemDetail">
        select sum(ssid.actual_out_quantity) actualOutQuantity
        from stock_task st
        left join stock_shipment_item ssi on ssi.stock_task_id =st.id
        left join stock_shipment_item_detail ssid on ssid.stock_shipment_item_id =ssi.id
        where  st.`type` =50  and st.task_no =#{listNo} and ssid.purchase_no=#{purchaseNo} and ssid.quality_date=#{qualityDate} and ssi.sku =#{sku}
    </select>

    <select id="selectShipmentItemSum" resultType="net.summerfarm.model.domain.StockShipmentItemDetail">
        select sum(ssid.actual_out_quantity) actualOutQuantity
        from stock_task st
        left join stock_shipment_item ssi on ssi.stock_task_id =st.id
        left join stock_shipment_item_detail ssid on ssid.stock_shipment_item_id =ssi.id
        where  st.`type` =50  and st.task_no =#{listNo} and ssid.purchase_no=#{purchaseNo} and ssid.quality_date=#{qualityDate} and ssi.sku =#{sku}
    </select>
    <select id="selectById" resultMap="BaseResultMap">
    SELECT * FROM  stock_storage_item WHERE id =  #{id}
    </select>

    <select id="selectByTaskNo" resultType="net.summerfarm.model.domain.StockStorageItem">
    SELECT t.id, t.sku, t.quantity,st.id  stockTaskId, t.actual_quantity actualQuantity
    FROM  stock_storage_item t
    LEFT  JOIN stock_task st on st.id= t.stock_task_id
    WHERE st.task_no = #{taskNo} and st.type= #{type}
    </select>

    <select id="selectByTaskNoAndType" resultType="net.summerfarm.model.vo.StockStorageItemDetailVO">
    SELECT t.sku,tl.purchase_no purchaseNo , tl.actual_in_quantity actualInQuantity , tl.quality_date qualityDate, tl.gl_no glNo
    FROM   stock_storage_item t
    LEFT JOIN  stock_storage_item_detail tl on tl.stock_storage_item_id=t.id
    LEFT  JOIN stock_task st on st.id= t.stock_task_id
    WHERE st.task_no = #{taskNo} and st.type= #{type}
    </select>


    <select id="selectByStockStorageItem" parameterType="net.summerfarm.model.domain.StockStorageItem" resultType="net.summerfarm.model.domain.StockStorageItem">
        SELECT id,stock_task_id stockTaskId,sku,quantity,actual_quantity actualQuantity
        FROM stock_storage_item
        <where>
            <if test="stockTaskId != null">
                AND stock_task_id = #{stockTaskId}
            </if>
            <if test="sku != null">
                AND sku = #{sku}
            </if>
        </where>
    </select>

    <select id="selectSkuQuantity" resultMap="getBatchStorageSkuDetail">
        SELECT
        st.id as stock_task_id,
        sti.sku,
        sti.quantity,
        sti.actual_quantity
        FROM
        stock_task st
        INNER JOIN stock_storage_item sti ON st.id = sti.stock_task_id
        WHERE 1=1
        <if test=" stockTaskIdSet != null and stockTaskIdSet.size != 0">
            and st.id in
            <foreach collection="stockTaskIdSet" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="sku != null and sku != ''">
            AND sti.sku = #{sku}
        </if>
        AND st.process_state != 2
        AND sti.actual_quantity != sti.quantity
        AND st.type IN (13,	19)
    </select>

    <select id="selectByStockTaskId" resultMap="selectByStockTaskIdWithDetail">
        SELECT DISTINCT sti.id,sti.stock_task_id,p.pd_name,sti.sku,i.weight,i.pack,i.unit,sti.quantity,sti.actual_quantity,IFNULL(ar2.`status`,ar.`status`) status,ad.name_remakes,
               p.category_id,p.pd_id,p.storage_location,c.type categoryType,i.type skuType,i.ext_type extType,0 as recycleState,st.task_no
        FROM stock_task st
                 INNER JOIN stock_storage_item sti ON st.id=sti.stock_task_id
                 INNER JOIN inventory i ON sti.sku=i.sku
                 INNER JOIN products p ON i.pd_id=p.pd_id
                 INNER JOIN category c on p.category_id = c.id
                 LEFT JOIN warehouse_stock_ext  ar	ON st.area_no=ar.warehouse_no AND sti.sku=ar.sku
                 LEFT JOIN warehouse_stock_ext ar2 ON st.out_store_no=ar2.warehouse_no AND sti.sku=ar2.sku
                 LEFT JOIN admin ad ON  ad.admin_id = i.admin_id
        WHERE st.id = #{stockTaskId,jdbcType=INTEGER}
        order by c.type desc
    </select>

    <select id="getBatchStorageSkuDetail" parameterType="net.summerfarm.model.DTO.BatchStorageSkuDetailDTO" resultMap="getBatchStorageSkuDetail">
        SELECT DISTINCT
        sti.id,
        sti.stock_task_id,
        p.pd_name,
        sti.sku,
        i.weight,
        i.pack,
        i.unit,
        sti.quantity,
        sti.actual_quantity,
        IFNULL( ar2.`status`, ar.`status` ) STATUS,
        ad.name_remakes,
        p.category_id,
        p.pd_id,
        p.storage_location,
        c.type categoryType,
        i.type skuType,
        i.ext_type extType,
        st.task_no
        FROM
        stock_task st
        INNER JOIN stock_storage_item sti ON st.id = sti.stock_task_id
        INNER JOIN inventory i ON sti.sku = i.sku
        INNER JOIN products p ON i.pd_id = p.pd_id
        INNER JOIN category c ON p.category_id = c.id
        LEFT JOIN warehouse_stock_ext ar ON st.area_no = ar.warehouse_no
        AND sti.sku = ar.sku
        LEFT JOIN warehouse_stock_ext ar2 ON st.out_store_no = ar2.warehouse_no
        AND sti.sku = ar2.sku
        LEFT JOIN admin ad ON ad.admin_id = i.admin_id
        WHERE
        st.process_state != 2
        and sti.actual_quantity != sti.quantity
        and st.out_store_no = #{storeNo}
        and st.expect_time <![CDATA[ >= ]]> #{startTime}
        and st.expect_time <![CDATA[<]]> #{endTime}
        and st.type in (13,19)
    </select>

    <select id="getBatchStorageSkuDetailByIds" resultMap="getBatchStorageSkuDetail">
        SELECT DISTINCT
        sti.id,
        sti.stock_task_id,
        p.pd_name,
        sti.sku,
        i.weight,
        i.pack,
        i.unit,
        sti.quantity,
        sti.actual_quantity,
        IFNULL( ar2.`status`, ar.`status` ) STATUS,
        ad.name_remakes,
        p.category_id,
        p.pd_id,
        p.storage_location,
        c.type categoryType,
        i.type skuType,
        i.ext_type extType,
        st.task_no
        FROM
        stock_task st
        INNER JOIN stock_storage_item sti ON st.id = sti.stock_task_id
        INNER JOIN inventory i ON sti.sku = i.sku
        INNER JOIN products p ON i.pd_id = p.pd_id
        INNER JOIN category c ON p.category_id = c.id
        LEFT JOIN warehouse_stock_ext ar ON st.area_no = ar.warehouse_no
        AND sti.sku = ar.sku
        LEFT JOIN warehouse_stock_ext ar2 ON st.out_store_no = ar2.warehouse_no
        AND sti.sku = ar2.sku
        LEFT JOIN admin ad ON ad.admin_id = i.admin_id
        WHERE
        <if test=" stockTaskIdList != null and stockTaskIdList.size != 0">
             st.id in
            <foreach collection="stockTaskIdList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByItemId" resultMap="VOMap">
        SELECT
            id,
            stock_storage_item_id,
            purchase_no,
            sum(actual_in_quantity) actual_in_quantity,
            quality_date,
            production_date
        FROM stock_storage_item_detail stid
        WHERE stock_storage_item_id = #{stockTaskItemId,jdbcType=INTEGER}
        GROUP BY purchase_no, quality_date
    </select>

    <select id="selectItemByTaskIdAndSku" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            stock_storage_item t
        WHERE
            t.stock_task_id = #{stockTaskId}
          AND t.sku = #{sku}
          AND t.actual_quantity != t.quantity
    </select>

    <select id="queryTransInItems" resultType="net.summerfarm.model.domain.StockStorageItem">
        select
        sst.id id,
        sst.stock_task_id stockTaskId,
        sst.sku sku,
        sst.quantity quantity,
        sst.actual_quantity actualQuantity,
        sst.add_time addTime,
        sst.update_time updateTime,
        sst.remark remark
        from stock_task st left join stock_storage_item sst on st.id = sst.stock_task_id
        where
        st.type = 10 and st.state in (0,1)

        <if test="skuId != null">
            and sst.sku = #{skuId}
        </if>
        <if test="warehouseNo != null">
            and st.area_no = #{warehouseNo}
        </if>
        <if test="startDate != null">
            and st.expect_time >= #{startDate}
        </if>
        <if test="endDate != null">
            and st.expect_time <![CDATA[<=]]> #{endDate}
        </if>
    </select>

    <select id="countCapacity" resultType="java.math.BigDecimal">
        SELECT ROUND(sum(capacity)/1000,2) total_capacity from (
        SELECT i.weight_num* sti.quantity capacity FROM `stock_storage_item` sti LEFT JOIN inventory i on sti.sku=i.sku WHERE stock_task_id in
        <foreach collection="stockTaskIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        ) s
    </select>

    <select id="selectOneByTaskIdAndSku" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            stock_storage_item t
        WHERE
            t.stock_task_id = #{stockTaskId}
          AND t.sku = #{sku}
    </select>

    <select id="selectListByTaskId" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            stock_storage_item t
        WHERE
            t.stock_task_id = #{stockTaskId}
    </select>
</mapper>