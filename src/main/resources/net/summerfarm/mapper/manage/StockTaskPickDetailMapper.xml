<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.StockTaskPickDetailMapper">

    <insert id="insertBatch"  parameterType="net.summerfarm.model.domain.StockTaskPickDetail" useGeneratedKeys="true" keyProperty="id">
        insert into stock_task_pick_detail  (`stock_task_pick_id`, `add_time` , `sku`,`gl_no`,`list_no` ,`quality_date`,`production_date`,should_quantity)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.stockTaskPickId},now(),#{item.sku},#{item.glNo},#{item.listNo},#{item.qualityDate},#{item.productionDate},#{item.shouldQuantity})
        </foreach>
    </insert>


</mapper>