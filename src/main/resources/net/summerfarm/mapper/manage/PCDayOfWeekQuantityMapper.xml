<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.PCDayOfWeekQuantityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.PCDayOfWeekQuantity">
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="week_1" jdbcType="INTEGER" property="week1" />
    <result column="week_2" jdbcType="INTEGER" property="week2" />
    <result column="week_3" jdbcType="INTEGER" property="week3" />
    <result column="week_4" jdbcType="INTEGER" property="week4" />
    <result column="week_5" jdbcType="INTEGER" property="week5" />
    <result column="week_6" jdbcType="INTEGER" property="week6" />
    <result column="week_7" jdbcType="INTEGER" property="week7" />
    <result column="std" jdbcType="REAL" property="std" />
    <result column= "last_cycle_quantity" jdbcType="INTEGER" property= "lastCycleQuantity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="turnover_rate" jdbcType="FLOAT" property="turnoverRate"/>
  </resultMap>
  <insert id="insert" parameterType="net.summerfarm.model.domain.PCDayOfWeekQuantity">
    insert into pc_day_of_week_quantity (type, store_no, sku, 
      week_1, week_2, week_3, 
      week_4, week_5, week_6, 
      week_7, std, last_cycle_quantity, create_time, max_threshold, turnover_rate
      )
    values (#{type,jdbcType=INTEGER}, #{storeNo,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{week1,jdbcType=INTEGER}, #{week2,jdbcType=INTEGER}, #{week3,jdbcType=INTEGER}, 
      #{week4,jdbcType=INTEGER}, #{week5,jdbcType=INTEGER}, #{week6,jdbcType=INTEGER}, 
      #{week7,jdbcType=INTEGER}, #{std,jdbcType=REAL}, #{lastCycleQuantity,jdbcType=INTEGER},
      #{createTime,jdbcType=TIMESTAMP}, #{maxThreshold, jdbcType=INTEGER},
      #{turnoverRate,jdbcType=FLOAT}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.PCDayOfWeekQuantity">
    insert into pc_day_of_week_quantity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        type,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="week1 != null">
        week_1,
      </if>
      <if test="week2 != null">
        week_2,
      </if>
      <if test="week3 != null">
        week_3,
      </if>
      <if test="week4 != null">
        week_4,
      </if>
      <if test="week5 != null">
        week_5,
      </if>
      <if test="week6 != null">
        week_6,
      </if>
      <if test="week7 != null">
        week_7,
      </if>
      <if test="std != null">
        std,
      </if>
      <if test="lastCycleQuantity">
        last_cycle_quantity,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="maxThreshold != null">
        max_threshold,
      </if>
      <if test="turnoverRate != null">
        turnover_rate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="week1 != null">
        #{week1,jdbcType=INTEGER},
      </if>
      <if test="week2 != null">
        #{week2,jdbcType=INTEGER},
      </if>
      <if test="week3 != null">
        #{week3,jdbcType=INTEGER},
      </if>
      <if test="week4 != null">
        #{week4,jdbcType=INTEGER},
      </if>
      <if test="week5 != null">
        #{week5,jdbcType=INTEGER},
      </if>
      <if test="week6 != null">
        #{week6,jdbcType=INTEGER},
      </if>
      <if test="week7 != null">
        #{week7,jdbcType=INTEGER},
      </if>
      <if test="std != null">
        #{std,jdbcType=REAL},
      </if>
      <if test="lastCycleQuantity">
        #{lastCycleQuantity},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="maxThreshold != null">
        #{maxThreshold},
      </if>
      <if test="turnoverRate != null">
        #{turnoverRate},
      </if>
    </trim>
  </insert>
  <insert id="insertOrUpdate" parameterType="net.summerfarm.model.domain.PCDayOfWeekQuantity">
    insert into pc_day_of_week_quantity(`type`, store_no, sku, week_1, week_2, week_3, week_4, week_5, week_6, week_7, std, last_cycle_quantity, create_time, max_threshold, turnover_rate)
    value(#{type}, #{storeNo}, #{sku}, #{week1}, #{week2}, #{week3}, #{week4}, #{week5}, #{week6}, #{week7}, #{std}, #{lastCycleQuantity}, #{createTime}, #{maxThreshold},#{turnoverRate})
    on duplicate key update
        week_1 = #{week1},
        week_2 = #{week2},
        week_3 = #{week3},
        week_4 = #{week4},
        week_5 = #{week5},
        week_6 = #{week6},
        week_7 = #{week7},
        std = #{std},
        last_cycle_quantity = #{lastCycleQuantity},
        create_time = #{createTime},
        max_threshold = #{maxThreshold},
        turnover_rate = #{turnoverRate}
  </insert>
</mapper>