<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.StockTaskProcessExpandMapper">
  <insert id="insert">
    insert into stock_task_process_expand
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="stockTaskProcessDetailId != null">
        stock_task_process_detail_id,
      </if>
      <if test="isComplete != null">
        is_complete,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="stockTaskProcessDetailId != null">
        #{stockTaskProcessDetailId},
      </if>
      <if test="isComplete != null">
        #{isComplete},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
    </trim>
  </insert>
</mapper>