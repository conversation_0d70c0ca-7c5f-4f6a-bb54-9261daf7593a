<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.manage.FrontCategoryConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FrontCategoryConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="sort_top" jdbcType="INTEGER" property="sortTop" />
    <result column="large_area_no" jdbcType="INTEGER" property="largeAreaNo" />
    <result column="front_category_id" jdbcType="INTEGER" property="frontCategoryId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sku, sort_top, large_area_no, front_category_id, creator, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from front_category_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectSkuNums" resultType="java.lang.Integer">
    select
    count(1)
    from front_category_config
    where large_area_no = #{largeAreaNo,jdbcType=INTEGER} and front_category_id = #{frontCategoryId,jdbcType=INTEGER} and sort_top = 1
  </select>
  <select id="selectSku" resultType="java.lang.String">
    select
    sku
    from front_category_config
    where large_area_no = #{largeAreaNo,jdbcType=INTEGER} and front_category_id = #{frontCategoryId,jdbcType=INTEGER} and sort_top = 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from front_category_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <delete id="deleteSku">
    delete from front_category_config
    where front_category_id = #{frontCategoryId,jdbcType=INTEGER}
      <if test="sku != null">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="sku != null">
        and large_area_no = #{largeAreaNo,jdbcType=INTEGER}
      </if>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FrontCategoryConfig" useGeneratedKeys="true">
    insert into front_category_config (sku, sort_top, large_area_no, 
      front_category_id, creator, create_time, 
      update_time)
    values (#{sku,jdbcType=VARCHAR}, #{sortTop,jdbcType=INTEGER}, #{largeAreaNo,jdbcType=INTEGER}, 
      #{frontCategoryId,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FrontCategoryConfig" useGeneratedKeys="true">
    insert into front_category_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sku != null">
        sku,
      </if>
      <if test="sortTop != null">
        sort_top,
      </if>
      <if test="largeAreaNo != null">
        large_area_no,
      </if>
      <if test="frontCategoryId != null">
        front_category_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="sortTop != null">
        #{sortTop,jdbcType=INTEGER},
      </if>
      <if test="largeAreaNo != null">
        #{largeAreaNo,jdbcType=INTEGER},
      </if>
      <if test="frontCategoryId != null">
        #{frontCategoryId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FrontCategoryConfig">
    update front_category_config
    <set>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="sortTop != null">
        sort_top = #{sortTop,jdbcType=INTEGER},
      </if>
      <if test="largeAreaNo != null">
        large_area_no = #{largeAreaNo,jdbcType=INTEGER},
      </if>
      <if test="frontCategoryId != null">
        front_category_id = #{frontCategoryId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FrontCategoryConfig">
    update front_category_config
    set sku = #{sku,jdbcType=VARCHAR},
      sort_top = #{sortTop,jdbcType=INTEGER},
      large_area_no = #{largeAreaNo,jdbcType=INTEGER},
      front_category_id = #{frontCategoryId,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>