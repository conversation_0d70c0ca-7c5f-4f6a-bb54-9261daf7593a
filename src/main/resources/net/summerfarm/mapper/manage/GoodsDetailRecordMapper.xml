<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.GoodsDetailRecordMapper">

    <insert id="insertRecord" parameterType="net.summerfarm.model.domain.GoodsDetailRecord" useGeneratedKeys="true" keyProperty="id">
        insert into goods_detail_record (`origin_gl_no`, `sku` , `pd_name`,`batch`,`new_gl_no` ,`add_time`,`quantity`,`admin_id`,`admin_name`,`type_name`)
        values (#{originGlNo},#{sku},#{pdName},#{batch},#{newGlNo},#{addTime},#{quantity},#{adminId},#{adminName},#{typeName} )
    </insert>

    <insert id="insertBathRecord" parameterType="net.summerfarm.model.domain.GoodsDetailRecord" useGeneratedKeys="true" keyProperty="id">
        insert into goods_detail_record (`origin_gl_no`, `sku` , `pd_name`,`batch`,`new_gl_no` ,`add_time`,`quantity`,`admin_id`,`admin_name`,`type_name`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.originGlNo},#{item.sku},#{item.pdName},#{item.batch},#{item.newGlNo},#{item.addTime},#{item.quantity},#{item.adminId},#{item.adminName},#{item.typeName} )
        </foreach>
    </insert>

    <select id="selectRecord" resultType="net.summerfarm.model.vo.GoodsDetailRecordVO">
        select gdr.id, origin_gl_no originGlNo , gdr.sku,
           pd_name pdName
          , gdr.batch, new_gl_no newGlNo, gdr.add_time addTime, gdr.quantity, gdr.admin_id adminId,
          gdr.admin_name adminName, type_name  typeName,a.area_name areaName
          from  goods_detail_record gdr
          left join goods_location gl on gl.gl_no = gdr.new_gl_no
          left join area a on gl.store_no = a.area_no
    </select>

    <select id="selectRecordDetail" resultType="net.summerfarm.model.domain.GoodsDetailRecord">
      select id, origin_gl_no originGlNo , sku,
       pd_name pdName
      , batch, new_gl_no newGlNo, add_time addTime, quantity, admin_id adminId, admin_name adminName, type_name  typeName
      from  goods_detail_record
      where id = #{id}
    </select>
</mapper>
