<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.PurchasePrepaymentPoolMapper">

    <resultMap type="net.summerfarm.model.domain.PurchasePrepaymentPool" id="PurchasePrepaymentPoolMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/>
        <result property="bindingAmount" column="binding_amount" jdbcType="DECIMAL"/>
        <result property="unboundAmount" column="unbound_amount" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="PurchasePrepaymentPoolMap">
        select id,
               supplier_id,
               supplier_name,
               total_amount,
               binding_amount,
               unbound_amount,
               create_time,
               update_time
        from xianmudb.purchase_prepayment_pool
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="PurchasePrepaymentPoolMap">
        select
        id, supplier_id, supplier_name, total_amount, binding_amount, unbound_amount, create_time, update_time
        from xianmudb.purchase_prepayment_pool
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="supplierId != null">
                and supplier_id = #{supplierId}
            </if>
            <if test="supplierName != null and supplierName != ''">
                and supplier_name = #{supplierName}
            </if>
            <if test="totalAmount != null">
                and total_amount = #{totalAmount}
            </if>
            <if test="bindingAmount != null">
                and binding_amount = #{bindingAmount}
            </if>
            <if test="unboundAmount != null">
                and unbound_amount = #{unboundAmount}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <select id="selectOne" resultMap="PurchasePrepaymentPoolMap">
        select
        id, supplier_id, supplier_name, total_amount, binding_amount, unbound_amount, create_time, update_time
        from xianmudb.purchase_prepayment_pool
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="supplierId != null">
                and supplier_id = #{supplierId}
            </if>
            <if test="supplierName != null and supplierName != ''">
                and supplier_name = #{supplierName}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into xianmudb.purchase_prepayment_pool(supplier_id, supplier_name, total_amount, binding_amount,
                                                      unbound_amount)
        values (#{supplierId}, #{supplierName}, #{totalAmount}, #{bindingAmount}, #{unboundAmount})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into xianmudb.purchase_prepayment_pool(supplier_id, supplier_name, total_amount, binding_amount,
        unbound_amount)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.supplierId}, #{entity.supplierName}, #{entity.totalAmount}, #{entity.bindingAmount},
            #{entity.unboundAmount})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update xianmudb.purchase_prepayment_pool
        <set>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="supplierName != null and supplierName != ''">
                supplier_name = #{supplierName},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount},
            </if>
            <if test="bindingAmount != null">
                binding_amount = #{bindingAmount},
            </if>
            <if test="unboundAmount != null">
                unbound_amount = #{unboundAmount},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updatePoolAmount">
        update purchase_prepayment_pool
        set total_amount = total_amount + #{totalAmount}, binding_amount = binding_amount + #{bindingAmount}, unbound_amount = unbound_amount + #{unboundAmount},update_time = now()
        where id = #{id}
    </update>
</mapper>

