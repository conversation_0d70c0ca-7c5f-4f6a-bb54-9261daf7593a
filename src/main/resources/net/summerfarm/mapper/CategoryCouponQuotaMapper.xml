<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.CategoryCouponQuotaMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CategoryCouponQuota">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="adminId" column="admin_id" jdbcType="BIGINT"/>
        <result property="adminName" column="admin_name" jdbcType="VARCHAR"/>
        <result property="quota" column="quota" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="type" column="type" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,admin_id,admin_name,quota,create_time,update_time,type
    </sql>

    <update id="updateQuota">
        update category_coupon_quota
        set quota=quota + #{quota}
        where admin_id = #{adminId} and type = #{quotaType}
    </update>
</mapper>
