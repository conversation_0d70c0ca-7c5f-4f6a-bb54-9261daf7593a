<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.GoodsTaskWhiteListMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.GoodsTaskWhiteListPO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="operator" jdbcType="VARCHAR" property="operator" />
        <result column="gmt_created" jdbcType="BIGINT" property="gmtCreated" />
        <result column="gmt_modified" jdbcType="BIGINT" property="gmtModified" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
        <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
        <result column="push_status" jdbcType="TINYINT" property="pushStatus" />
    </resultMap>
    <sql id="Base_Column_List">
        id, sku, push_status, creator, operator, gmt_created, gmt_modified, is_deleted, last_ver
    </sql>
    <select id="selectBySku" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from goods_task_white_list
        where sku = #{sku, jdbcType=VARCHAR}
    </select>

    <update id="updateBySkuSelective" parameterType="net.summerfarm.model.domain.GoodsTaskWhiteListPO">
        update goods_task_white_list
        <set>
            last_ver = last_ver + 1,
            <if test="pushStatus != null">
                push_status = #{pushStatus},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=BIGINT},
            </if>
        </set>
        where sku = #{sku,jdbcType=VARCHAR}
    </update>

    <insert id="insert" parameterType="net.summerfarm.model.domain.GoodsTaskWhiteListPO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into goods_task_white_list (sku, push_status, creator, operator,
        gmt_created, gmt_modified, is_deleted, last_ver)
        values (#{sku,jdbcType=VARCHAR}, #{pushStatus,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
        #{gmtCreated,jdbcType=BIGINT}, #{gmtModified,jdbcType=BIGINT}, 0,1)
    </insert>

    <select id="selectListByPushStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from goods_task_white_list
        where push_status = #{pushStatus,jdbcType=TINYINT}
    </select>
</mapper>