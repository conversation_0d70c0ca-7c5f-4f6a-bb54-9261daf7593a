<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.CategoryCouponQuotaChangeMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.CategoryCouponQuotaChange">
        <!--@Table category_coupon_quota_change-->
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="adminId" column="admin_id" jdbcType="BIGINT"/>
        <result property="adminName" column="admin_name" jdbcType="VARCHAR"/>
        <result property="quota" column="quota" jdbcType="DECIMAL"/>
        <result property="basePrice" column="base_price" jdbcType="DECIMAL"/>
        <result property="rewardRule" column="reward_rule" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="merchantCouponId" column="merchant_coupon_id" jdbcType="INTEGER"/>
        <result property="dingtalkBizId" column="dingtalk_biz_id" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="quotaType" column="quota_type" jdbcType="INTEGER"/>
        <result property="poolId" column="pool_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,admin_id,admin_name,
        quota,base_price,reward_rule,
        type,merchant_coupon_id,dingtalk_biz_id,
        creator,creator_name,remark,
        create_time,quota_type, pool_id
    </sql>

    <select id="selectByBizId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from category_coupon_quota_change where dingtalk_biz_id=#{bizId}
    </select>
    <select id="selectByMerchantCouponId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from category_coupon_quota_change where merchant_coupon_id=#{merchantCouponId}
    </select>
    <delete id="delByBizId">
        delete
        from category_coupon_quota_change
        where dingtalk_biz_id = #{bizId}
    </delete>

<!--auto generated by MybatisCodeHelper on 2024-12-30-->
    <update id="updateSelective">
        update category_coupon_quota_change
        <set>
            <if test="updated.adminId != null">
                admin_id = #{updated.adminId,jdbcType=BIGINT},
            </if>
            <if test="updated.adminName != null">
                admin_name = #{updated.adminName,jdbcType=VARCHAR},
            </if>
            <if test="updated.quota != null">
                quota = #{updated.quota,jdbcType=DECIMAL},
            </if>
            <if test="updated.basePrice != null">
                base_price = #{updated.basePrice,jdbcType=DECIMAL},
            </if>
            <if test="updated.rewardRule != null">
                reward_rule = #{updated.rewardRule,jdbcType=INTEGER},
            </if>
            <if test="updated.type != null">
                type = #{updated.type,jdbcType=TINYINT},
            </if>
            <if test="updated.merchantCouponId != null">
                merchant_coupon_id = #{updated.merchantCouponId,jdbcType=INTEGER},
            </if>
            <if test="updated.dingtalkBizId != null">
                dingtalk_biz_id = #{updated.dingtalkBizId,jdbcType=INTEGER},
            </if>
            <if test="updated.creator != null">
                creator = #{updated.creator,jdbcType=BIGINT},
            </if>
            <if test="updated.creatorName != null">
                creator_name = #{updated.creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updated.remark != null">
                remark = #{updated.remark,jdbcType=VARCHAR},
            </if>
            <if test="updated.createTime != null">
                create_time = #{updated.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.quotaType != null">
                quota_type = #{updated.quotaType,jdbcType=INTEGER},
            </if>
            <if test="updated.poolId != null">
                pool_id = #{updated.poolId,jdbcType=BIGINT},
            </if>
        </set>
        where id=#{updated.id,jdbcType=BIGINT}
    </update>
</mapper>
