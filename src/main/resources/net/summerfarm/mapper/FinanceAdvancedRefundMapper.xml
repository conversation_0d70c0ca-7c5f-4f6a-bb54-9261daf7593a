<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.FinanceAdvancedRefundMapper">

    <resultMap type="net.summerfarm.model.domain.FinanceAdvancedRefund" id="FinanceAdvancedRefundMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="refundAmount" column="refund_amount" jdbcType="DECIMAL"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="paymentVoucher" column="payment_voucher" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="FinanceAdvancedRefundMap">
        select id,
               supplier_id,
               supplier_name,
               refund_amount,
               status,
               payment_voucher,
               creator,
               create_time,
               updater,
               update_time
        from xianmudb.finance_advanced_refund
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="FinanceAdvancedRefundMap">
        select
        id, supplier_id, supplier_name, refund_amount, status, payment_voucher, creator, create_time, updater,
        update_time
        from xianmudb.finance_advanced_refund
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="supplierId != null">
                and supplier_id = #{supplierId}
            </if>
            <if test="supplierName != null and supplierName != ''">
                and supplier_name = #{supplierName}
            </if>
            <if test="refundAmount != null">
                and refund_amount = #{refundAmount}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="paymentVoucher != null and paymentVoucher != ''">
                and payment_voucher = #{paymentVoucher}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and updater = #{updater}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
        order by id desc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into xianmudb.finance_advanced_refund(supplier_id, supplier_name, refund_amount, status, payment_voucher,
                                                     creator, updater)
        values (#{supplierId}, #{supplierName}, #{refundAmount}, #{status}, #{paymentVoucher}, #{creator}, #{updater})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update xianmudb.finance_advanced_refund
        <set>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="supplierName != null and supplierName != ''">
                supplier_name = #{supplierName},
            </if>
            <if test="refundAmount != null">
                refund_amount = #{refundAmount},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="paymentVoucher != null and paymentVoucher != ''">
                payment_voucher = #{paymentVoucher},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>

