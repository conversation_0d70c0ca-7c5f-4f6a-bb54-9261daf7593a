<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.FinancePurchaseInvoiceWalletsMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinancePurchaseInvoiceWallets">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="wallets_no" jdbcType="VARCHAR" property="walletsNo" />
    <result column="invoice_quantity" jdbcType="INTEGER" property="invoiceQuantity" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="manager" jdbcType="VARCHAR" property="manager" />
    <result column="total_included_tax" jdbcType="DECIMAL" property="totalIncludedTax" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="file_time" jdbcType="VARCHAR" property="fileTime" />
    <result column="remakes" jdbcType="VARCHAR" property="remakes" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="invoice_review_time" jdbcType="TIMESTAMP" property="invoiceReviewTime" />
    <result column="invoice_review" jdbcType="VARCHAR" property="invoiceReview" />
    <result column="creator_admin_id" jdbcType="INTEGER" property="creatorAdminId" />
    <result column="expense_type" jdbcType="INTEGER" property="expenseType" />
    <result column="creator_role_type" jdbcType="VARCHAR" property="creatorRoleType" />
    <result column="bill_feature" jdbcType="VARCHAR" property="billFeature" />

  </resultMap>
  <sql id="Base_Column_List">
    id, wallets_no, invoice_quantity, supplier_id, supplier_name, manager, total_included_tax, 
    `status`, file_time, remakes, delete_status, create_time, creator, update_time, updater,invoice_review_time,invoice_review,expense_type,creator_admin_id,creator_role_type,bill_feature
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_purchase_invoice_wallets
    where id = #{id,jdbcType=BIGINT} and delete_status = 0
  </select>
  <update id="update" parameterType="java.lang.Long">
    update finance_purchase_invoice_wallets
    set delete_status = 1
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinancePurchaseInvoiceWallets" useGeneratedKeys="true">
    insert into finance_purchase_invoice_wallets (wallets_no, invoice_quantity, supplier_id, 
      supplier_name, manager, total_included_tax, 
      `status`, file_time, remakes, 
      delete_status, create_time, creator, 
      update_time, updater)
    values (#{walletsNo,jdbcType=VARCHAR}, #{invoiceQuantity,jdbcType=INTEGER}, #{supplierId,jdbcType=INTEGER}, 
      #{supplierName,jdbcType=VARCHAR}, #{manager,jdbcType=VARCHAR}, #{totalIncludedTax,jdbcType=DECIMAL}, 
      #{status,jdbcType=INTEGER}, #{fileTime,jdbcType=VARCHAR}, #{remakes,jdbcType=VARCHAR}, 
      #{deleteStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinancePurchaseInvoiceWallets" useGeneratedKeys="true">
    insert into finance_purchase_invoice_wallets
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="walletsNo != null">
        wallets_no,
      </if>
      <if test="invoiceQuantity != null">
        invoice_quantity,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="manager != null">
        manager,
      </if>
      <if test="totalIncludedTax != null">
        total_included_tax,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="fileTime != null">
        file_time,
      </if>
      <if test="remakes != null">
        remakes,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="invoiceReviewTime != null">
        invoice_review_time,
      </if>
      <if test="invoiceReview != null">
        invoice_review,
      </if>
      <if test="creatorAdminId != null">
        creator_admin_id,
      </if>
      <if test="expenseType != null">
        expense_type,
      </if>
      <if test="creatorRoleType != null">
        creator_role_type,
      </if>
      <if test="billFeature != null">
        bill_feature,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="walletsNo != null">
        #{walletsNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceQuantity != null">
        #{invoiceQuantity,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="manager != null">
        #{manager,jdbcType=VARCHAR},
      </if>
      <if test="totalIncludedTax != null">
        #{totalIncludedTax,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="fileTime != null">
        #{fileTime,jdbcType=VARCHAR},
      </if>
      <if test="remakes != null">
        #{remakes,jdbcType=VARCHAR},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="invoiceReviewTime != null">
        #{invoiceReviewTime},
      </if>
      <if test="invoiceReview != null">
        #{invoiceReview},
      </if>
      <if test="creatorAdminId != null">
        #{creatorAdminId},
      </if>
      <if test="expenseType != null">
        #{expenseType},
      </if>
      <if test="creatorRoleType != null">
        #{creatorRoleType},
      </if>
      <if test="billFeature != null">
        #{billFeature},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinancePurchaseInvoiceWallets">
    update finance_purchase_invoice_wallets
    <set>
      <if test="walletsNo != null">
        wallets_no = #{walletsNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceQuantity != null">
        invoice_quantity = #{invoiceQuantity,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="manager != null">
        manager = #{manager,jdbcType=VARCHAR},
      </if>
      <if test="totalIncludedTax != null">
        total_included_tax = #{totalIncludedTax,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="fileTime != null">
        file_time = #{fileTime,jdbcType=VARCHAR},
      </if>
      <if test="remakes != null">
        remakes = #{remakes,jdbcType=VARCHAR},
      </if>
      <if test="deleteStatus != null">
        delete_status = #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="invoiceReviewTime != null">
        invoice_review_time = #{invoiceReviewTime},
      </if>
      <if test="invoiceReview != null">
        invoice_review = #{invoiceReview},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT} and delete_status = 0
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinancePurchaseInvoiceWallets">
    update finance_purchase_invoice_wallets
    set
      delete_status = 1,
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT} and delete_status = 0 and status = 2
  </update>
  <update id="updateInvoiceBack" parameterType="net.summerfarm.model.domain.FinancePurchaseInvoiceWallets">
    update finance_purchase_invoice_wallets
    set
      delete_status = 1,
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT} and delete_status = 0 and status = 4
  </update>

  <update id="updateType" parameterType="net.summerfarm.model.domain.FinancePurchaseInvoiceWallets">
    update finance_purchase_invoice_wallets
    set
      accounting_period_type = #{accountingPeriodType},
      accounting_period_amount = #{accountingPeriodAmount},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = now()
    where id = #{id,jdbcType=BIGINT} and delete_status = 0
  </update>

  <select id="selectSum" parameterType="string" resultType="int">
    select count(*)
    from finance_purchase_invoice_wallets
    where wallets_no like concat(#{walletsNo},'%')
  </select>

  <select id="selectList" parameterType="net.summerfarm.model.input.FinancePurchaseInvoiceWalletsInput" resultType="net.summerfarm.model.vo.FinancePurchaseInvoiceWalletsVO">
    select f.id,f.wallets_no walletsNo,f.invoice_quantity invoiceQuantity,f.total_included_tax totalIncludedTax,f.supplier_name supplierName,f.supplier_id supplierId,f.manager,f.create_time createTime,
           f.update_time updateTime, f.remakes,f.accounting_period_amount accountingPeriodAmount,f.accounting_period_type  accountingPeriodType,f.creator,f.expense_type expenseType,f.creator_admin_id creatorAdminId
    from finance_purchase_invoice_wallets f
    inner join purchase_invoice p on f.id = p.wallets_id
    <if test="purchaseNo != null">
      left join finance_account_statement fa on fa.wallets_id = f.id
      left join finance_account_statement_detail fasd on fa.id = fasd.finance_account_statement_id
    </if>
    <where>
      f.`status` = #{status} and f.delete_status = 0
      <if test=" startTime != null and endTime != null">
        and f.create_time <![CDATA[>=]]> #{startTime} and f.create_time <![CDATA[<=]]> #{endTime}
      </if>
      <if test=" updateTimeStart != null and updateTimeEnd != null">
        and f.update_time <![CDATA[>=]]> #{updateTimeStart} and f.update_time <![CDATA[<=]]> #{updateTimeEnd}
      </if>
      <if test=" billingDateStart != null and billingDateEnd != null">
        and p.billing_date <![CDATA[>=]]> #{billingDateStart} and p.billing_date <![CDATA[<=]]> #{billingDateEnd}
      </if>
      <if test=" billingDateStart != null and billingDateEnd != null and updateTimeStart != null and updateTimeEnd != null">
        and p.billing_date <![CDATA[>=]]> #{billingDateStart} and p.billing_date <![CDATA[<=]]> #{billingDateEnd}
        and f.update_time <![CDATA[>=]]> #{updateTimeStart} and f.update_time <![CDATA[<=]]> #{updateTimeEnd}
      </if>
      <if test=" reviewStartTime != null and reviewEndTime != null ">
        and f.update_time <![CDATA[>=]]> #{reviewStartTime} and f.update_time <![CDATA[<=]]> #{reviewEndTime}
      </if>
      <if test="invoiceCode !=null ">
        and p.invoice_code LIKE CONCAT(#{invoiceCode},'%')
      </if>
      <if test="invoiceNumber !=null">
        and p.invoice_number LIKE CONCAT(#{invoiceNumber},'%')
      </if>
      <if test=" walletsNo != null">
        and f.wallets_no = #{walletsNo}
      </if>
      <if test=" supplierName != null">
        and f.supplier_name = #{supplierName}
      </if>
      <if test=" manager != null">
        and f.manager = #{manager}
      </if>
      <if test=" accountingPeriodType != null">
        and f.accounting_period_type = #{accountingPeriodType}
      </if>
      <if test="purchaseNo != null">
        and fasd.purchase_no like CONCAT (#{purchaseNo} ,'%')
      </if>
      <if test=" expenseType != null">
        and f.expense_type = #{expenseType}
      </if>
      <if test=" creator != null">
        and f.creator = #{creator}
      </if>
    </where>
    group by f.id
    order by
    <choose>
      <when test="orderType == 1"> f.create_time desc</when>
      <when test="orderType == 2"> f.update_time desc</when>
      <when test="orderType == 3"> f.update_time desc</when>
      <otherwise>f.id</otherwise>
    </choose>
  </select>

  <select id="totalAmountIncludingTax" parameterType="net.summerfarm.model.input.FinancePurchaseInvoiceWalletsInput" resultType="net.summerfarm.model.vo.FinancePurchaseInvoiceWalletsVO">
    select f.total_included_tax totalAmountIncludingTax
    from finance_purchase_invoice_wallets f
    inner join purchase_invoice p on f.id = p.wallets_id
    <where>
      f.`status` = #{status} and f.delete_status = 0
      <if test=" startTime != null and endTime != null">
        and f.create_time <![CDATA[>=]]> #{startTime} and f.create_time <![CDATA[<=]]> #{endTime}
      </if>
      <if test=" updateTimeStart != null and updateTimeEnd != null">
        and f.update_time <![CDATA[>=]]> #{updateTimeStart} and f.update_time <![CDATA[<=]]> #{updateTimeEnd}
      </if>
      <if test=" invoiceCode != null">
        and p.invoice_code = #{invoiceCode}
      </if>
      <if test=" invoiceNumber != null">
        and p.invoice_number = #{invoiceNumber}
      </if>
      <if test=" walletsNo != null">
        and f.wallets_no = #{walletsNo}
      </if>
      <if test=" supplierName != null">
        and f.supplier_name = #{supplierName}
      </if>
      <if test=" manager != null">
        and f.manager = #{manager}
      </if>
      <if test=" expenseType != null">
        and f.expense_type = #{expenseType}
      </if>
      <if test=" creator != null">
        and f.creator = #{creator}
      </if>
      <if test=" accountingPeriodType != null">
        and f.accounting_period_type = #{accountingPeriodType}
      </if>
    </where>
    group by f.id
  </select>

</mapper>