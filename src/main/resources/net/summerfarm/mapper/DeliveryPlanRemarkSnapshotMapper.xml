<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.DeliveryPlanRemarkSnapshotMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.crm.DeliveryPlanRemarkSnapshot">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="business_id" jdbcType="VARCHAR" property="businessId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="address_remark" jdbcType="VARCHAR" property="addressRemark" />
    <result column="contact" jdbcType="VARCHAR" property="contact" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_default" jdbcType="INTEGER" property="isDefault" />
    <result column="poi_note" jdbcType="VARCHAR" property="poiNote" />
    <result column="distance" jdbcType="DECIMAL" property="distance" />
    <result column="house_number" jdbcType="VARCHAR" property="houseNumber" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="acm_id" jdbcType="INTEGER" property="acmId" />
    <result column="back_store_no" jdbcType="INTEGER" property="backStoreNo" />
    <result column="delivery_frequent" jdbcType="VARCHAR" property="deliveryFrequent" />
    <result column="delivery_rule" jdbcType="VARCHAR" property="deliveryRule" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, business_id, `type`, address_remark, contact, phone, 
    email, province, city, area, address, `status`, remark, is_default, poi_note, distance, 
    house_number, store_no, acm_id, back_store_no, delivery_frequent, delivery_rule, 
    delivery_fee
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from delivery_plan_remark_snapshot
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from delivery_plan_remark_snapshot
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.crm.DeliveryPlanRemarkSnapshot" useGeneratedKeys="true">
    insert into delivery_plan_remark_snapshot (create_time, update_time, business_id, 
      `type`, address_remark, contact, 
      phone, email, province, 
      city, area, address, 
      `status`, remark, is_default, 
      poi_note, distance, house_number, 
      store_no, acm_id, back_store_no, 
      delivery_frequent, delivery_rule, delivery_fee
      )
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{businessId,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{addressRemark,jdbcType=VARCHAR}, #{contact,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{isDefault,jdbcType=INTEGER}, 
      #{poiNote,jdbcType=VARCHAR}, #{distance,jdbcType=DECIMAL}, #{houseNumber,jdbcType=VARCHAR}, 
      #{storeNo,jdbcType=INTEGER}, #{acmId,jdbcType=INTEGER}, #{backStoreNo,jdbcType=INTEGER}, 
      #{deliveryFrequent,jdbcType=VARCHAR}, #{deliveryRule,jdbcType=VARCHAR}, #{deliveryFee,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.crm.DeliveryPlanRemarkSnapshot" useGeneratedKeys="true">
    insert  into delivery_plan_remark_snapshot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="addressRemark != null">
        address_remark,
      </if>
      <if test="contact != null">
        contact,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDefault != null">
        is_default,
      </if>
      <if test="poiNote != null">
        poi_note,
      </if>
      <if test="distance != null">
        distance,
      </if>
      <if test="houseNumber != null">
        house_number,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="acmId != null">
        acm_id,
      </if>
      <if test="backStoreNo != null">
        back_store_no,
      </if>
      <if test="deliveryFrequent != null">
        delivery_frequent,
      </if>
      <if test="deliveryRule != null">
        delivery_rule,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="addressRemark != null">
        #{addressRemark,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=INTEGER},
      </if>
      <if test="poiNote != null">
        #{poiNote,jdbcType=VARCHAR},
      </if>
      <if test="distance != null">
        #{distance,jdbcType=DECIMAL},
      </if>
      <if test="houseNumber != null">
        #{houseNumber,jdbcType=VARCHAR},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="acmId != null">
        #{acmId,jdbcType=INTEGER},
      </if>
      <if test="backStoreNo != null">
        #{backStoreNo,jdbcType=INTEGER},
      </if>
      <if test="deliveryFrequent != null">
        #{deliveryFrequent,jdbcType=VARCHAR},
      </if>
      <if test="deliveryRule != null">
        #{deliveryRule,jdbcType=VARCHAR},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.crm.DeliveryPlanRemarkSnapshot">
    update delivery_plan_remark_snapshot
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="addressRemark != null">
        address_remark = #{addressRemark,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        contact = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=INTEGER},
      </if>
      <if test="poiNote != null">
        poi_note = #{poiNote,jdbcType=VARCHAR},
      </if>
      <if test="distance != null">
        distance = #{distance,jdbcType=DECIMAL},
      </if>
      <if test="houseNumber != null">
        house_number = #{houseNumber,jdbcType=VARCHAR},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="acmId != null">
        acm_id = #{acmId,jdbcType=INTEGER},
      </if>
      <if test="backStoreNo != null">
        back_store_no = #{backStoreNo,jdbcType=INTEGER},
      </if>
      <if test="deliveryFrequent != null">
        delivery_frequent = #{deliveryFrequent,jdbcType=VARCHAR},
      </if>
      <if test="deliveryRule != null">
        delivery_rule = #{deliveryRule,jdbcType=VARCHAR},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.crm.DeliveryPlanRemarkSnapshot">
    update delivery_plan_remark_snapshot
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      business_id = #{businessId,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=TINYINT},
      address_remark = #{addressRemark,jdbcType=VARCHAR},
      contact = #{contact,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      is_default = #{isDefault,jdbcType=INTEGER},
      poi_note = #{poiNote,jdbcType=VARCHAR},
      distance = #{distance,jdbcType=DECIMAL},
      house_number = #{houseNumber,jdbcType=VARCHAR},
      store_no = #{storeNo,jdbcType=INTEGER},
      acm_id = #{acmId,jdbcType=INTEGER},
      back_store_no = #{backStoreNo,jdbcType=INTEGER},
      delivery_frequent = #{deliveryFrequent,jdbcType=VARCHAR},
      delivery_rule = #{deliveryRule,jdbcType=VARCHAR},
      delivery_fee = #{deliveryFee,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectByTypeBusinessIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from delivery_plan_remark_snapshot
    where type = #{type}
    and business_id in
    <foreach collection="businessIds" open="(" close=")"  item="item"  separator=",">
      #{item}
    </foreach>
  </select>

  <delete id="deleteByTypeBusinessId">
    delete from delivery_plan_remark_snapshot
    where business_id = #{businessId} and type=#{type,jdbcType=BIGINT}
  </delete>
</mapper>