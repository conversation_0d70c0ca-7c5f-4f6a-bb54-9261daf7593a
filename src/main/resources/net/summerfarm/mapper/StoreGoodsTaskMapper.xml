<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.StoreGoodsTaskMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.StoreGoodsTaskPO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="task_source" jdbcType="INTEGER" property="taskSource" />
        <result column="task_status" jdbcType="INTEGER" property="taskStatus" />
        <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
        <result column="pd_id" jdbcType="BIGINT" property="pdId" />
        <result column="sku" jdbcType="INTEGER" property="sku" />
        <result column="sku_name" jdbcType="INTEGER" property="skuName" />
        <result column="weight" jdbcType="VARCHAR" property="weight" />
        <result column="storage_location" jdbcType="TINYINT" property="storageLocation" />
        <result column="volume" jdbcType="VARCHAR" property="volume" />
        <result column="weight_num" jdbcType="DECIMAL" property="weightNum" />
        <result column="push_status" jdbcType="TINYINT" property="pushStatus" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="operator" jdbcType="VARCHAR" property="operator" />
        <result column="gmt_created" jdbcType="BIGINT" property="gmtCreated" />
        <result column="gmt_modified" jdbcType="BIGINT" property="gmtModified" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
        <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="is_domestic" jdbcType="TINYINT" property="isDomestic" />
        <result column="quality_time" jdbcType="INTEGER" property="qualityTime" />
        <result column="quality_time_unit" jdbcType="VARCHAR" property="qualityTimeUnit" />
        <result column="quality_time_type" jdbcType="TINYINT" property="qualityTimeType" />
        <result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
    </resultMap>

    <sql id="Base_Column_List">
        id, task_source, task_status, warehouse_no, pd_id, sku, sku_name, weight, storage_location,
    volume, weight_num, push_status, creator, operator, gmt_created, gmt_modified, is_deleted, last_ver,
    unit, is_domestic, quality_time, quality_time_unit, quality_time_type, pic_url
    </sql>

    <insert id="insert" parameterType="net.summerfarm.model.domain.StoreGoodsTaskPO">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into goods_check_task (task_source, task_status,
        warehouse_no, pd_id, sku, sku_name,
        weight, storage_location, volume,
        weight_num, push_status, creator, operator,
        gmt_created, gmt_modified, is_deleted,
        last_ver, unit, is_domestic, quality_time,
        quality_time_unit, quality_time_type, pic_url)
        values ( #{taskSource,jdbcType=INTEGER}, #{taskStatus,jdbcType=INTEGER},
        #{warehouseNo,jdbcType=INTEGER}, #{pdId,jdbcType=BIGINT}, #{sku,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR},
        #{weight,jdbcType=VARCHAR}, #{storageLocation,jdbcType=TINYINT}, #{volume,jdbcType=VARCHAR},
        #{weightNum,jdbcType=DECIMAL}, #{pushStatus,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
        #{gmtCreated,jdbcType=BIGINT}, #{gmtModified,jdbcType=BIGINT}, 0, 1,
        #{unit,jdbcType=VARCHAR}, #{isDomestic,jdbcType=TINYINT}, #{qualityTime,jdbcType=INTEGER}, #{qualityTimeUnit,jdbcType=VARCHAR},
        #{qualityTimeType,jdbcType=TINYINT}, #{picUrl,jdbcType=VARCHAR})
    </insert>

    <select id="selectList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from goods_check_task
        <where>
            <if test = "warehouseNo != null">
                warehouse_no = #{warehouseNo}
            </if>
            <if test = "sku != null">
                AND sku = #{sku}
            </if>
            <if test = "pdId != null">
                AND pd_id = #{pdId}
            </if>
            <if test = "storageLocation != null">
                AND storage_location = #{storageLocation}
            </if>
            <if test = "taskStatus != null">
                AND task_status = #{taskStatus}
            </if>
        </where>
        order by gmt_created desc
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from goods_check_task
        where id = #{id,jdbcType=BIGINT}
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.StoreGoodsTaskPO">
        update goods_check_task
        <set>
            operator = #{operator},
            last_ver = last_ver + 1,
            <if test="taskSource != null">
                task_source = #{taskSource,jdbcType=INTEGER},
            </if>
            <if test="taskStatus != null">
                task_status = #{taskStatus,jdbcType=INTEGER},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=INTEGER},
            </if>
            <if test="skuName != null">
                sku_name = #{skuName,jdbcType=INTEGER},
            </if>
            <if test="weight != null">
                weight = #{weight,jdbcType=VARCHAR},
            </if>
            <if test="storageLocation != null">
                storage_location = #{storageLocation,jdbcType=TINYINT},
            </if>
            <if test="volume != null">
                volume = #{volume,jdbcType=VARCHAR},
            </if>
            <if test="pushStatus != null">
                push_status = #{pushStatus,jdbcType=INTEGER},
            </if>
            <if test="weightNum != null">
                weight_num = #{weightNum,jdbcType=DECIMAL},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="isDomestic != null">
                is_domestic = #{isDomestic,jdbcType=TINYINT},
            </if>
            <if test="qualityTime != null">
                quality_time = #{qualityTime,jdbcType=INTEGER},
            </if>
            <if test="qualityTimeUnit != null">
                quality_time_unit = #{qualityTimeUnit,jdbcType=VARCHAR},
            </if>
            <if test="qualityTimeType != null">
                quality_time_type = #{qualityTimeType,jdbcType=TINYINT},
            </if>
            <if test="picUrl != null">
                pic_url = #{picUrl,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectBySkuListAndStatus" resultType="java.lang.String">
        select distinct sku
        from goods_check_task
        where sku in
        <foreach collection="skuList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and task_status = #{taskStatus}
    </select>

    <select id="selectByStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from goods_check_task
        where task_status = #{taskStatus}
    </select>

    <select id="selectBySkuList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from goods_check_task
        where sku in
        <foreach collection="skuList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>

    </select>

    <select id="selectBySkuAndWarehouseNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from goods_check_task
        where warehouse_no = #{warehouseNo}
        and sku = #{sku}
    </select>

    <select id="selectBySkuAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from goods_check_task
        where sku = #{sku}
        and task_status = #{taskStatus}
    </select>
</mapper>