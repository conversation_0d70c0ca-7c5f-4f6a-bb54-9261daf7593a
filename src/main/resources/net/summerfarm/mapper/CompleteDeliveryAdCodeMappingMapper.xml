<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.CompleteDeliveryAdCodeMappingMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.purchase.CompleteDeliveryAdCodeMapping">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ad_code" jdbcType="VARCHAR" property="adCode" />
    <result column="complete_delivery_id" jdbcType="INTEGER" property="completeDeliveryId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, ad_code, complete_delivery_id, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from complete_delivery_ad_code_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from complete_delivery_ad_code_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.purchase.CompleteDeliveryAdCodeMapping">
    insert into complete_delivery_ad_code_mapping (id, ad_code, complete_delivery_id, 
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{adCode,jdbcType=VARCHAR}, #{completeDeliveryId,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.purchase.CompleteDeliveryAdCodeMapping">
    insert into complete_delivery_ad_code_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="adCode != null">
        ad_code,
      </if>
      <if test="completeDeliveryId != null">
        complete_delivery_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="adCode != null">
        #{adCode,jdbcType=VARCHAR},
      </if>
      <if test="completeDeliveryId != null">
        #{completeDeliveryId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.purchase.CompleteDeliveryAdCodeMapping">
    update complete_delivery_ad_code_mapping
    <set>
      <if test="adCode != null">
        ad_code = #{adCode,jdbcType=VARCHAR},
      </if>
      <if test="completeDeliveryId != null">
        complete_delivery_id = #{completeDeliveryId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.purchase.CompleteDeliveryAdCodeMapping">
    update complete_delivery_ad_code_mapping
    set ad_code = #{adCode,jdbcType=VARCHAR},
      complete_delivery_id = #{completeDeliveryId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByAdCodes" resultType="net.summerfarm.model.vo.CityStoreNoVO">
    SELECT
      ad.area,ad.area
    FROM
      complete_delivery_ad_code_mapping cd
	LEFT JOIN ad_code_msg ad ON cd.ad_code=ad.ad_code
    WHERE
    <if test="adCodes != null">
      ad.ad_code in
      <foreach collection="adCodes" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

  <insert id="insertBatch">
    insert into complete_delivery_ad_code_mapping ( ad_code, complete_delivery_id,
    create_time, update_time)
    values
    <foreach collection="completeDeliveryAdCodeMappings" item="item" index="index" separator=",">
      ( #{item.adCode,jdbcType=VARCHAR}, #{item.completeDeliveryId,jdbcType=INTEGER},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <delete id="deleteByCompleteDeliveryId" parameterType="java.lang.Integer">
    delete from complete_delivery_ad_code_mapping
    where complete_delivery_id = #{id}
  </delete>

  <select id="selectAddressByCDId" resultType="net.summerfarm.model.vo.AreaAdCodeVO">
    SELECT s.`area`,s.ad_code adCode
    FROM complete_delivery_ad_code_mapping t
           LEFT JOIN `ad_code_msg` s on t.`ad_code`= s.`ad_code`
    where t.`complete_delivery_id` = #{completeDeliveryId}  and s.`status` in (0,3)
  </select>

  <select id="selectAreaByCodeList" resultType="java.lang.String">
    select ad_code from complete_delivery_ad_code_mapping
  </select>

  <select id="selectAreaByCompleteId" resultType="java.lang.String">
    select ad_code from complete_delivery_ad_code_mapping where complete_delivery_id != #{completeDeliveryId}
  </select>

  <delete id="deleteByAdCode">
    delete from complete_delivery_ad_code_mapping where ad_code = #{adCode}
  </delete>
</mapper>