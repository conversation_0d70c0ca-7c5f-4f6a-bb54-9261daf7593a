<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.PurchaseReplenishmentOrderReasonMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.purchase.PurchaseReplenishmentOrderReason">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="reason_code" jdbcType="INTEGER" property="reasonCode" />
    <result column="reason_remark" jdbcType="VARCHAR" property="reasonRemark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, reason_code, reason_remark, create_time, update_time, updater, creator, 
    del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_replenishment_order_reason
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_replenishment_order_reason
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.purchase.PurchaseReplenishmentOrderReason">
    insert into purchase_replenishment_order_reason (id, order_id, reason_code, 
      reason_remark, create_time, update_time, 
      updater, creator, del_flag
      )
    values (#{id,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{reasonCode,jdbcType=INTEGER}, 
      #{reasonRemark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{creator,jdbcType=INTEGER}, #{delFlag,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.purchase.PurchaseReplenishmentOrderReason">
    insert into purchase_replenishment_order_reason
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="reasonCode != null">
        reason_code,
      </if>
      <if test="reasonRemark != null">
        reason_remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="reasonCode != null">
        #{reasonCode,jdbcType=INTEGER},
      </if>
      <if test="reasonRemark != null">
        #{reasonRemark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.purchase.PurchaseReplenishmentOrderReason">
    update purchase_replenishment_order_reason
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="reasonCode != null">
        reason_code = #{reasonCode,jdbcType=INTEGER},
      </if>
      <if test="reasonRemark != null">
        reason_remark = #{reasonRemark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.purchase.PurchaseReplenishmentOrderReason">
    update purchase_replenishment_order_reason
    set order_id = #{orderId,jdbcType=BIGINT},
      reason_code = #{reasonCode,jdbcType=INTEGER},
      reason_remark = #{reasonRemark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=INTEGER},
      creator = #{creator,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>