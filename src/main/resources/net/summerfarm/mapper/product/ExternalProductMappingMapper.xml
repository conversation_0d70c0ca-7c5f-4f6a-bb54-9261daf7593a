<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.product.ExternalProductMappingMapper">
    <!-- 结果集映射 -->
    <resultMap id="externalProductMappingResultMap" type="net.summerfarm.model.ExternalProductMapping">
        <id column="id" property="id" jdbcType="NUMERIC"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="internal_value" property="internalValue" jdbcType="VARCHAR"/>
        <result column="external_value" property="externalValue" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="externalProductMappingColumns">
        t.id,
          t.create_time,
          t.update_time,
          t.type,
          t.internal_value,
          t.external_value
    </sql>

    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
    <insert id="insertSelective" parameterType="net.summerfarm.model.ExternalProductMapping" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO external_product_mapping
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                id,
            </if>
            create_time,update_time,
            <if test="type != null">
                type,
            </if>
            <if test="internalValue != null">
                internal_value,
            </if>
            <if test="externalValue != null">
                external_value,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id,jdbcType=NUMERIC},
            </if>
            now(),now(),
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="internalValue != null">
                #{internalValue,jdbcType=VARCHAR},
            </if>
            <if test="externalValue != null">
                #{externalValue,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>