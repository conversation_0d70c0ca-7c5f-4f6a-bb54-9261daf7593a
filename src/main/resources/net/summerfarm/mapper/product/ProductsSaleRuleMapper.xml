<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.product.ProductsSaleRuleMapper">
    <!-- 结果集映射 -->
    <resultMap id="productsSaleRuleResultMap" type="net.summerfarm.model.ProductsSaleRule">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="pd_id" property="pdId" jdbcType="NUMERIC"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="base_sale_quantity" property="baseSaleQuantity" jdbcType="INTEGER"/>
		<result column="operator" property="operator" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="productsSaleRuleColumns">
          t.id,
          t.pd_id,
          t.status,
          t.base_sale_quantity,
          t.operator,
          t.create_time,
          t.update_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="pdId != null">
                AND t.pd_id = #{pdId}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="baseSaleQuantity != null">
                AND t.base_sale_quantity = #{baseSaleQuantity}
            </if>
			<if test="operator != null and operator !=''">
                AND t.operator = #{operator}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="pdId != null">
                    t.pd_id = #{pdId},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="baseSaleQuantity != null">
                    t.base_sale_quantity = #{baseSaleQuantity},
                </if>
                <if test="operator != null">
                    t.operator = #{operator},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="productsSaleRuleResultMap" >
        SELECT <include refid="productsSaleRuleColumns" />
        FROM products_sale_rule t
		WHERE t.id = #{id}
    </select>


    <!-- 根据pdID获取数据 -->
    <select id="selectByPdId" parameterType="java.lang.Long" resultMap="productsSaleRuleResultMap" >
        SELECT <include refid="productsSaleRuleColumns" />
        FROM products_sale_rule t
        WHERE t.pd_id = #{pdId}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.model.param.ProductsSaleRuleQueryParam"  resultType="net.summerfarm.model.vo.ProductsSaleRuleVO" >
        SELECT
            t.id id,
            t.pd_id pdId,
            p.pd_name pdName,
            p.pd_no pdNo,
            t.status status,
            t.base_sale_quantity baseSaleQuantity,
            t.operator operator,
            t.create_time createTime,
            t.update_time updateTime
        FROM products_sale_rule t
        left join products p on p.pd_id = t.pd_id
        <where>
            <if test="pdId !=null">
                AND t.pd_id = #{pdId}
            </if>
            <if test="status !=null">
                AND t.status = #{status}
            </if>
            <if test="pdName !=null">
                AND p.pd_name like CONCAT(#{pdName},'%')
            </if>
            <if test="pdNo !=null">
                AND p.pd_no = #{pdNo}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.model.ProductsSaleRule" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO products_sale_rule
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="pdId != null">
				  pd_id,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="baseSaleQuantity != null">
				  base_sale_quantity,
              </if>
              <if test="operator != null">
				  operator,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="pdId != null">
				#{pdId,jdbcType=NUMERIC},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="baseSaleQuantity != null">
				#{baseSaleQuantity,jdbcType=INTEGER},
              </if>
              <if test="operator != null">
				#{operator,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.model.ProductsSaleRule" >
        UPDATE products_sale_rule t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.model.ProductsSaleRule" >
        DELETE FROM products_sale_rule
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>