<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.product.ProductConfigMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.ProductConfig">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="config_type" jdbcType="INTEGER" property="configType" />
        <result column="config_key" jdbcType="VARCHAR" property="configKey" />
        <result column="config_value" jdbcType="VARCHAR" property="configValue" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="operator" jdbcType="VARCHAR" property="operator" />
        <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
        <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    </resultMap>
    <sql id="Base_Column_List">
        id, config_type, config_key, config_value, creator, operator, gmt_created, gmt_modified,
    is_deleted, last_ver
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from product_config
        where id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insert" parameterType="net.summerfarm.model.ProductConfig">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into product_config (config_type, config_key, config_value,
        creator, operator, gmt_created,
        gmt_modified, is_deleted, last_ver
        )
        values (#{configType,jdbcType=INTEGER}, #{configKey,jdbcType=VARCHAR}, #{configValue,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, now(),now(),0,1
        )
    </insert>

    <select id="selectListByTypeAndKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from product_config
        where config_type = #{configType,jdbcType=INTEGER} and config_key = #{configKey,jdbcType=VARCHAR}
    </select>
</mapper>