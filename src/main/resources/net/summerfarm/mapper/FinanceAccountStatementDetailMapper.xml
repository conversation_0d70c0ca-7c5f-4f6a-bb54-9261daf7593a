<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.FinanceAccountStatementDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceAccountStatementDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="finance_account_statement_id" jdbcType="BIGINT" property="financeAccountStatementId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="stock_task_process_id" jdbcType="INTEGER" property="stockTaskProcessId" />
    <result column="purchase_no" jdbcType="VARCHAR" property="purchaseNo" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="adjust_amount" jdbcType="DECIMAL" property="adjustAmount" />
    <result column="stock_task_process_detail_id" jdbcType="INTEGER" property="stockTaskProcessDetailId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, finance_account_statement_id, `type`, stock_task_process_id, purchase_no, sku, 
    amount, create_time,adjust_amount,stock_task_process_detail_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from finance_account_statement_detail
    where finance_account_statement_id = #{id,jdbcType=BIGINT}
  </select>

  <select id="select" parameterType="java.lang.Long" resultType="int">
    select count(*)
    from finance_account_statement_detail
    where finance_account_statement_id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from finance_account_statement_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAccountStatementDetail" useGeneratedKeys="true">
    insert into finance_account_statement_detail (finance_account_statement_id, `type`, stock_task_process_id, 
      purchase_no, sku, amount, 
      create_time,adjust_amount,stock_task_process_detail_id)
    values (#{financeAccountStatementId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{stockTaskProcessId,jdbcType=INTEGER}, 
      #{purchaseNo,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP},#{adjustAmount},#{stockTaskProcessDetailId})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAccountStatementDetail" useGeneratedKeys="true">
    insert into finance_account_statement_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="financeAccountStatementId != null">
        finance_account_statement_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="stockTaskProcessId != null">
        stock_task_process_id,
      </if>
      <if test="purchaseNo != null">
        purchase_no,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="financeAccountStatementId != null">
        #{financeAccountStatementId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="stockTaskProcessId != null">
        #{stockTaskProcessId,jdbcType=INTEGER},
      </if>
      <if test="purchaseNo != null">
        #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinanceAccountStatementDetail">
    update finance_account_statement_detail
    <set>
      <if test="financeAccountStatementId != null">
        finance_account_statement_id = #{financeAccountStatementId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="stockTaskProcessId != null">
        stock_task_process_id = #{stockTaskProcessId,jdbcType=INTEGER},
      </if>
      <if test="purchaseNo != null">
        purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinanceAccountStatementDetail">
    update finance_account_statement_detail
    set finance_account_statement_id = #{financeAccountStatementId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=INTEGER},
      stock_task_process_id = #{stockTaskProcessId,jdbcType=INTEGER},
      purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByWalletsId" parameterType="net.summerfarm.model.input.StockTaskWalletsInput" resultType="net.summerfarm.model.vo.FinanceAccountStatementDetailVO">
    select fa.id,
           d.add_time                                  addTime,
           fa.type                                     type,
           fa.stock_task_process_id                    stockTaskProcessId,
           fa.purchase_no                              purchaseNo,
           fa.finance_account_statement_id             financeAccountStatementId,
           i.weight                                    weight,
           fa.sku,
           d.quantity                                  quantity,
           fa.amount,
           ((pp.price / pp.quantity) * d.quantity)     excludingTax,
           ifnull(t.tax_rate_code, tt.tax_rate_code)   taxRateCode,
           ifnull(t.tax_rate_value, tt.tax_rate_value) taxRateValue,
           p.pd_name                                   pdName,
           fa.adjust_amount                            adjustAmount,
           round((pp.price / pp.quantity), 2)          purchasePrice
    from finance_account_statement_detail fa
           inner join finance_account_statement f on fa.finance_account_statement_id = f.id
           left join stock_task_process_detail d
                     on d.stock_task_process_id = fa.stock_task_process_id and d.id = fa.stock_task_process_detail_id
           left join purchases_plan pp on fa.purchase_no = pp.purchase_no and fa.sku = pp.sku and pp.origin_id is null
           left join inventory i on fa.sku = i.sku
           left join products p on i.pd_id = p.pd_id
           left join tax_rate_config t on p.pd_id = t.pd_id
           left join tax_rate_config tt on p.category_id = tt.category_id
    <where>
      f.wallets_id = #{walletsId}
      <if test=" purchaseNo != null">
        and fa.purchase_no = #{purchaseNo}
      </if>
      <if test=" sku != null">
        and fa.sku = #{sku}
      </if>
      <if test=" financeAccountStatementId != null">
        and fa.finance_account_statement_id = #{financeAccountStatementId}
      </if>
      <if test=" taxRateCode != null">
        and tt.tax_rate_code = #{taxRateCode}
      </if>
      <if test=" pdName != null">
        and p.pd_name =#{pdName}
      </if>
      <if test=" type != null and type == 11">
        <!-- 前端传参入库，同时兼容采购入库11和越仓入库21 -->
        and fa.type in (11, 21)
      </if>
      <if test=" type != null and type == 56">
        and fa.type = #{type}
      </if>
      <if test=" stockTaskProcessId != null">
        and fa.stock_task_process_id = #{stockTaskProcessId}
      </if>
    </where>
    group by fa.id
    order by
    <choose>
      <when test="sequence == 1">d.add_time ASC</when>
      <when test="sequence == 2">d.add_time DESC</when>
      <otherwise>d.add_time ASC</otherwise>
    </choose>
  </select>

  <select id="selectMoney" parameterType="net.summerfarm.model.input.StockTaskWalletsInput" resultType="net.summerfarm.model.vo.FinanceAccountStatementDetailVO">
    select ((pp.price/pp.quantity) * d.quantity) excludingTax,fa.type
    from finance_account_statement_detail fa
    inner join finance_account_statement f on fa.finance_account_statement_id = f.id
    left join stock_task_process_detail d on d.stock_task_process_id = fa.stock_task_process_id and d.id = fa.stock_task_process_detail_id
    left join purchases_plan pp on fa.purchase_no = pp.purchase_no and fa.sku = pp.sku and pp.origin_id is null and pp.plan_status  = 1
    left join inventory i on fa.sku = i.sku
    left join products p on i.pd_id = p.pd_id
    left join tax_rate_config t on p.category_id = t.category_id or p.pd_id = t.pd_id
    <where>
      f.wallets_id = #{walletsId}
      <if test=" purchaseNo != null">
        and fa.purchase_no = #{purchaseNo}
      </if>
      <if test=" sku != null">
        and fa.sku = #{sku}
      </if>
      <if test=" financeAccountStatementId != null">
        and fa.finance_account_statement_id = #{financeAccountStatementId}
      </if>
      <if test=" taxRateCode != null">
        and t.tax_rate_code = #{taxRateCode}
      </if>
      <if test=" pdName != null">
        and p.pd_name =#{pdName}
      </if>
      <if test=" type != null and type == 11">
        <!-- 前端传参入库，同时兼容采购入库11和越仓入库21 -->
        and fa.type in (11, 21)
      </if>
      <if test=" type != null and type == 56">
        and fa.type = #{type}
      </if>
      <if test=" stockTaskProcessId != null">
        and fa.stock_task_process_id = #{stockTaskProcessId}
      </if>
    </where>
    group by fa.id
  </select>

  <select id="selectAll" parameterType="net.summerfarm.model.input.FinanceAccountStatementInput" resultType="net.summerfarm.model.vo.FinanceAccountStatementDetailVO" >
    select fa.id,
           d.add_time                              addTime,
           fa.type                                 type,
           fa.stock_task_process_id                stockTaskProcessId,
           fa.purchase_no                          listNo,
           fa.finance_account_statement_id         financeAccountStatementId,
           fa.sku,
           d.quantity                              quantity,
           fa.amount,
           p.pd_name                               pdName,
           ((pp.price / pp.quantity) * d.quantity) price,
           i.weight,
           ifnull(fa.adjust_amount, 0)             adjustAmount,
           IFNULL(i.sku_pic, p.picture_path)       detailPicture,
           round((pp.price / pp.quantity), 2)      purchasePrice
    from finance_account_statement_detail fa
    left join stock_task_process_detail d on d.stock_task_process_id = fa.stock_task_process_id and d.id = fa.stock_task_process_detail_id
    left join purchases_plan pp on fa.purchase_no = pp.purchase_no and fa.sku = pp.sku and pp.origin_id is null
    left join inventory i on fa.sku = i.sku
    left join products p on i.pd_id = p.pd_id
    <where>
      <if test=" id != null">
        and fa.finance_account_statement_id = #{id}
      </if>
      <if test=" type != null">
        and fa.type = #{type}
      </if>
      <if test=" pdNames != null and pdNames.size != 0">
        and p.pd_name in
        <foreach collection="pdNames" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test=" purchaseNo != null">
        and fa.purchase_no = #{purchaseNo}
      </if>
      <if test=" sku != null">
        and fa.sku = #{sku}
      </if>
      <if test=" costAdjustment != null">
        and fa.adjust_amount <![CDATA[<>]]> 0
      </if>
      <if test=" receiptNo != null">
        and fa.stock_task_process_id = #{receiptNo} and fa.type in (11, 21)
      </if>
      <if test=" returnOrderNo != null">
        and fa.stock_task_process_id = #{returnOrderNo} and fa.type = 56
      </if>
    </where>
    group by fa.id
  </select>

  <select id="selectByPurchaseNo" resultType="net.summerfarm.model.vo.FinanceAccountStatementDetailVO">
    select fa.id,
           fa.type                         type,
           fa.stock_task_process_id        stockTaskProcessId,
           fa.purchase_no                  listNo,
           fa.finance_account_statement_id financeAccountStatementId,
           fa.sku,
           fa.amount,
           ifnull(fa.adjust_amount, 0)     adjustAmount
    from finance_account_statement_detail fa
           left join finance_account_statement f on f.id = fa.finance_account_statement_id
    where fa.purchase_no = #{purchaseNo}
      and f.supplier_id = #{supplierId}
      and fa.sku = #{sku}
      and f.status in (1, 0, 5, 6, 7, 8)
  </select>

  <select id="selectProcessAccount" resultType="string">
    select fa.purchase_no
    from finance_account_statement_detail fa
           left join finance_account_statement f on f.id = fa.finance_account_statement_id
    where
    f.supplier_id = #{supplierId}
    and f.status in (0, 1, 2, 5, 6, 7)
    <if test = "purchaseNoList != null and purchaseNoList.size > 0">
      and fa.purchase_no  in
      <foreach collection="purchaseNoList" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="selectByAccountId" resultType="net.summerfarm.model.vo.FinanceAccountDetailVO">
    SELECT
      d.type,
      d.stock_task_process_id as stockTaskProcessId,
      d.purchase_no as purchaseNo,
      d.sku as sku,
      ifnull(d.amount,0) as endAmount,
      ifnull(d.adjust_amount,0) as adjustAmount,
      d.stock_task_process_detail_id as stockTaskProcessDetailId,
      s.supplier_id as supplierId,
      stpd.add_time as addTime,
      s.update_time as finishedTime,
      s.create_time as createTime,
      d.finance_account_statement_id as accountId
    FROM
      `finance_account_statement_detail` d
        left join finance_account_statement s on s.id =d.finance_account_statement_id
        left join stock_task_process_detail stpd on d.stock_task_process_detail_id = stpd.id
    where d.finance_account_statement_id=#{accountId}
  </select>

  <select id="selectByAccountList" resultType="net.summerfarm.model.vo.FinanceAccountDetailVO">
    SELECT
      d.type,
      d.stock_task_process_id as stockTaskProcessId,
      d.purchase_no as purchaseNo,
      d.sku as sku,
      ifnull(d.amount,0) as endAmount,
      ifnull(d.adjust_amount,0) as adjustAmount,
      d.stock_task_process_detail_id as stockTaskProcessDetailId,
      s.supplier_id as supplierId,
      stpd.add_time as addTime,
      s.update_time as finishedTime,
      s.create_time as createTime,
      d.finance_account_statement_id as accountId
    FROM
      `finance_account_statement_detail` d
        left join finance_account_statement s on s.id =d.finance_account_statement_id
        left join stock_task_process_detail stpd on d.stock_task_process_detail_id = stpd.id
    where d.finance_account_statement_id in
    <foreach collection="list" open="(" close=")" item="id" separator=",">
      #{id}
    </foreach>
    order by d.finance_account_statement_id asc
  </select>

  <select id="queryDetailByParam" resultType="net.summerfarm.module.pms.model.vo.AccountDetailVO">
    select fa.id                                       detailId,
           f.id                                        financeAccountId,
           d.add_time                                  addTime,
           fa.type                                     type,
           fa.stock_task_process_id                    stockTaskProcessId,
           fa.purchase_no                              purchaseNo,
           fa.finance_account_statement_id             financeAccountStatementId,
           i.weight                                    weight,
           fa.sku,
           d.quantity                                  quantity,
           p.pd_name                                   pdName,
           fa.amount,
           ((pp.price / pp.quantity) * d.quantity)     excludingTax,
           ifnull(t.tax_rate_code, tt.tax_rate_code)   taxRateCode,
           ifnull(t.tax_rate_value, tt.tax_rate_value) taxRateValue,
           fa.adjust_amount                            adjustAmount,
           round((pp.price / pp.quantity), 2)          purchasePrice
    from finance_account_statement_detail fa
    inner join finance_account_statement f on fa.finance_account_statement_id = f.id
    left join stock_task_process_detail d  on d.stock_task_process_id = fa.stock_task_process_id and d.id = fa.stock_task_process_detail_id
    left join purchases_plan pp on fa.purchase_no = pp.purchase_no and fa.sku = pp.sku and pp.origin_id is null and pp.plan_status = 1
    left join inventory i on fa.sku = i.sku
    left join products p on i.pd_id = p.pd_id
    left join tax_rate_config t on p.pd_id = t.pd_id
    left join tax_rate_config tt on p.category_id = tt.category_id
    <where>
      <if test=" pdNames != null and pdNames.size != 0">
        and p.pd_name in
        <foreach collection="pdNames" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test=" costAdjustment != null">
        and fa.adjust_amount <![CDATA[<>]]> 0
      </if>
      <if test=" receiptNo != null">
        and fa.stock_task_process_id = #{receiptNo} and fa.type in (11, 21)
      </if>
      <if test=" returnOrderNo != null">
        and fa.stock_task_process_id = #{returnOrderNo} and fa.type = 56
      </if>
      <if test="walletsId != null">
        and f.wallets_id = #{walletsId}
      </if>
      <if test=" purchaseNo != null">
        and fa.purchase_no = #{purchaseNo}
      </if>
      <if test=" sku != null">
        and fa.sku = #{sku}
      </if>
      <if test=" financeAccountStatementId != null">
        and fa.finance_account_statement_id = #{financeAccountStatementId}
      </if>
      <if test=" taxRateCode != null">
        and tt.tax_rate_code = #{taxRateCode}
      </if>
      <if test=" pdName != null">
        and p.pd_name =#{pdName}
      </if>
      <if test=" type != null and type == 11">
        <!-- 前端传参入库，同时兼容采购入库11和越仓入库21 -->
        and fa.type in (11, 21)
      </if>
      <if test=" type != null and type == 56">
        and fa.type = #{type}
      </if>
      <if test=" stockTaskProcessId != null">
        and fa.stock_task_process_id = #{stockTaskProcessId}
      </if>
    </where>
    group by fa.id
    order by
    <choose>
      <when test="sequence == 1">d.add_time ASC</when>
      <when test="sequence == 2">d.add_time DESC</when>
      <otherwise>d.add_time ASC</otherwise>
    </choose>
  </select>
</mapper>