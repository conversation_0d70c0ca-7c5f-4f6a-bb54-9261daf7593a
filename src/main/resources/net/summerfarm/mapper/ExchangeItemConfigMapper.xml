<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.ExchangeItemConfigMapper">

    <sql id="Base_Column_List">
    id,scope_config_id,sku, priority,
    </sql>
    <insert id="insertExchangeItem" parameterType="net.summerfarm.model.domain.ExchangeItemConfig">
        insert into exchange_item_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scopeConfigId != null">
                scope_config_id,
            </if>
            <if test="sku != null">
                `sku`,
            </if>
            <if test="priority != null">
                `priority`,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="adjustType != null">
                adjust_type,
            </if>
            <if test="amount != null">
                amount,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scopeConfigId != null">
                #{scopeConfigId},
            </if>
            <if test="sku != null">
                #{sku},
            </if>
            <if test="priority != null">
                #{priority},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="adjustType != null">
                #{adjustType},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
        </trim>
    </insert>
    <update id="updateExchangeItemById" parameterType="net.summerfarm.model.domain.ExchangeItemConfig">
        update exchange_item_config
        <set>
            <if test="scopeConfigId != null">
                scope_config_id = #{scopeConfigId},
            </if>
            <if test="sku != null">
                sku = #{sku},
            </if>
            <if test="priority != null">
                priority =  #{priority},
            </if>
            <if test="adjustType != null">
                adjust_type =  #{adjustType},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <delete id="deleteExchangeItemById">
        delete from exchange_item_config where id = #{id}
    </delete>

    <select id="selectByScopeId" resultType="net.summerfarm.model.vo.ExchangeItemConfigVO">
        select id,scope_config_id scopeConfigId,eic.sku, eic.priority,eic.creator,eic.update_time updateTime,eic.create_time createTime,eic.adjust_type adjustType,amount,
        p.pd_name productName
        from exchange_item_config eic left join inventory i on eic.sku = i.sku
        left join products p on i.pd_id = p.pd_id
        where scope_config_id =#{scopeId}
    </select>
    <select id="selectById" resultType="net.summerfarm.model.domain.ExchangeItemConfig">
        select id,eic.scope_config_id scopeConfigId,eic.sku, eic.priority,eic.creator,eic.update_time updateTime,eic.create_time createTime,eic.adjust_type adjustType,amount
        from exchange_item_config eic
        where id = #{id}
    </select>
    <select id="selectCountItemByScopeId" resultType="java.lang.Integer">
        select count(1) from exchange_item_config where scope_config_id = #{scopeConfigId}
    </select>
</mapper>