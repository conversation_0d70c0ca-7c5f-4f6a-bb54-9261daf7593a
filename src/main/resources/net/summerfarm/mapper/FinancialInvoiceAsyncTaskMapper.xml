<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.FinancialInvoiceAsyncTaskMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinancialInvoiceAsyncTask">
    <!--@mbg.generated-->
    <!--@Table financial_invoice_async_task-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="invoice_id" jdbcType="BIGINT" property="invoiceId" />
    <result column="task_result" jdbcType="TINYINT" property="taskResult" />
    <result column="invoke_count" jdbcType="INTEGER" property="invokeCount" />
    <result column="invoke_params" jdbcType="LONGVARCHAR" property="invokeParams" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, `type`, invoice_id, task_result, invoke_count,
    invoke_params, task_id
  </sql>
  <select id="selectByIds" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from financial_invoice_async_task
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from financial_invoice_async_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from financial_invoice_async_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinancialInvoiceAsyncTask" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into financial_invoice_async_task (`type`,
    invoice_id, task_result,
    invoke_count, invoke_params, task_id)
    values (#{type,jdbcType=TINYINT},
    #{invoiceId,jdbcType=BIGINT}, #{taskResult,jdbcType=TINYINT},
    #{invokeCount,jdbcType=INTEGER}, #{invokeParams,jdbcType=LONGVARCHAR}, #{taskId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinancialInvoiceAsyncTask" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into financial_invoice_async_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="taskResult != null">
        task_result,
      </if>
      <if test="invokeCount != null">
        invoke_count,
      </if>
      <if test="invokeParams != null">
        invoke_params,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=BIGINT},
      </if>
      <if test="taskResult != null">
        #{taskResult,jdbcType=TINYINT},
      </if>
      <if test="invokeCount != null">
        #{invokeCount,jdbcType=INTEGER},
      </if>
      <if test="invokeParams != null">
        #{invokeParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.FinancialInvoiceAsyncTask">
    <!--@mbg.generated-->
    update financial_invoice_async_task
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=BIGINT},
      </if>
      <if test="taskResult != null">
        task_result = #{taskResult,jdbcType=TINYINT},
      </if>
      <if test="invokeCount != null">
        invoke_count = #{invokeCount,jdbcType=INTEGER},
      </if>
      <if test="invokeParams != null">
        invoke_params = #{invokeParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.FinancialInvoiceAsyncTask">
    <!--@mbg.generated-->
    update financial_invoice_async_task
    set create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    `type` = #{type,jdbcType=TINYINT},
    invoice_id = #{invoiceId,jdbcType=BIGINT},
    task_result = #{taskResult,jdbcType=TINYINT},
    invoke_count = #{invokeCount,jdbcType=INTEGER},
    invoke_params = #{invokeParams,jdbcType=LONGVARCHAR},
    invoke_count = #{invokeCount,jdbcType=INTEGER},
    task_id = #{taskId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryWaitInvokeTasks" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from financial_invoice_async_task
    where task_result in (0, 3)
    and invoke_count &lt; #{maxInvokeCount,jdbcType=INTEGER}
    order by id
    limit #{limit}
  </select>

  <select id="queryLastByInvoiceIdAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from financial_invoice_async_task
    where invoice_id = #{invoiceId,jdbcType=BIGINT} and type = #{type,jdbcType=TINYINT}
    order by id desc
    limit 1
  </select>

  <update id="updateTaskResult">
    update financial_invoice_async_task
    set task_result = #{taskResult,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="removeTaskId">
    update financial_invoice_async_task
    set task_id = null
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>