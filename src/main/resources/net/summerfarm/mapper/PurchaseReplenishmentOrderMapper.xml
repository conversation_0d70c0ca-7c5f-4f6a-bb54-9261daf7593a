<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.PurchaseReplenishmentOrderMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.purchase.PurchaseReplenishmentOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pd_id" jdbcType="BIGINT" property="pdId" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="warehouse_no" jdbcType="INTEGER" property="warehouseNo" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="view_date" jdbcType="TIMESTAMP" property="viewDate" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="replenishment_type" jdbcType="INTEGER" property="replenishmentType" />
    <result column="activity_flag" jdbcType="INTEGER" property="activityFlag" />
    <result column="current_enabled_quantity" jdbcType="INTEGER" property="currentEnabledQuantity" />
    <result column="current_on_way_quantity" jdbcType="INTEGER" property="currentOnWayQuantity" />
    <result column="current_transfer_in_quantity" jdbcType="INTEGER" property="currentTransferInQuantity" />
    <result column="sales_history_quantity" jdbcType="INTEGER" property="salesHistoryQuantity" />
    <result column="transfer_out_history_quantity" jdbcType="INTEGER" property="transferOutHistoryQuantity" />
    <result column="pre_day" jdbcType="INTEGER" property="preDay" />
    <result column="backlog_day" jdbcType="INTEGER" property="backlogDay" />
    <result column="safe_water_level" jdbcType="INTEGER" property="safeWaterLevel" />
    <result column="advice_replenishment_quantity" jdbcType="INTEGER" property="adviceReplenishmentQuantity" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="final_replenishment_quantity" jdbcType="INTEGER" property="finalReplenishmentQuantity" />
    <result column="final_supplier_id" jdbcType="INTEGER" property="finalSupplierId" />
    <result column="final_supplier_name" jdbcType="VARCHAR" property="finalSupplierName" />
    <result column="final_admin_id" jdbcType="INTEGER" property="finalAdminId" />
    <result column="final_admin_name" jdbcType="VARCHAR" property="finalAdminName" />
    <result column="relation_type" jdbcType="INTEGER" property="relationType" />
    <result column="relation_id" jdbcType="VARCHAR" property="relationId" />
    <result column="create_date" jdbcType="INTEGER" property="createDate" />
    <result column="source" jdbcType="INTEGER" property="source"/>
    <result column="replenishment_plan_purchase_task_id" jdbcType="INTEGER" property="replenishmentPlanPurchaseTaskId"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pd_id, pd_name, warehouse_no, warehouse_name, sku_id, view_date, order_status, replenishment_type,
    activity_flag, current_enabled_quantity, current_on_way_quantity, current_transfer_in_quantity, 
    sales_history_quantity, transfer_out_history_quantity, pre_day, backlog_day, safe_water_level, 
    advice_replenishment_quantity, supplier_id, admin_id, final_replenishment_quantity, 
    final_supplier_id, final_supplier_name, final_admin_id, final_admin_name, relation_type, 
    relation_id, create_date,source, replenishment_plan_purchase_task_id,create_time, updater, update_time, creator, del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from purchase_replenishment_order
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectList" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from purchase_replenishment_order
        where del_flag = 0
      <if test="warehouseNo != null">
        and warehouse_no = #{warehouseNo}
      </if>
      <if test="supplierId != null">
        and final_supplier_id = #{supplierId}
      </if>
      <if test="adminId != null">
        and final_admin_id = #{adminId}
      </if>
      <if test="orderStatus != null">
        and order_status = #{orderStatus}
      </if>
      <if test="replenishmentType != null">
        and replenishment_type = #{replenishmentType}
      </if>
      <if test="skuId != null">
        and sku_id = #{skuId}
      </if>
      <if test="skuName != null and skuName != ''">
        and pd_name like CONCAT(#{skuName},'%')
      </if>
      <if test="createDate != null">
        and create_date = #{createDate}
      </if>
      <if test="startViewDate != null">
        and view_date >= #{startViewDate}
      </if>
      <if test="endViewDate != null">
        and view_date <![CDATA[<]]> #{endViewDate}
      </if>
      <if test="source != null">
        and source =#{source}
      </if>
      <if test="orderIds != null and orderIds.size != 0">
        and id in
        <foreach collection="orderIds" open="(" close=")" separator="," item="orderId">
          #{orderId}
        </foreach>
      </if>
      <if test="sortList != null and !sortList.isEmpty()">
        ORDER BY
        <foreach collection="sortList" item="sortField" separator=",">
          <choose>
            <when test="sortField.sortBy != null and sortField.orderBy != null">
              <if test="sortField.orderBy.equalsIgnoreCase('asc')">
                ${sortField.sortBy},id ASC
              </if>
              <if test="sortField.orderBy.equalsIgnoreCase('desc')">
                ${sortField.sortBy},id DESC
              </if>
            </when>
          </choose>
        </foreach>
      </if>
      <if test="sortList == null or sortList.isEmpty()">
        order by sku_id,id asc
      </if>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_replenishment_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.model.domain.purchase.PurchaseReplenishmentOrder">
    insert into purchase_replenishment_order (id, pd_id, pd_name,
      warehouse_no, warehouse_name, sku_id, view_date,
      order_status, replenishment_type, activity_flag,
      current_enabled_quantity, current_on_way_quantity,
      current_transfer_in_quantity, sales_history_quantity,
      transfer_out_history_quantity, pre_day, backlog_day,
      safe_water_level, advice_replenishment_quantity,
      supplier_id, admin_id, final_replenishment_quantity,
      final_supplier_id, final_supplier_name, final_admin_id,
      final_admin_name, relation_type, relation_id,
      create_date, create_time, updater,
      update_time, creator, del_flag
      )
    values (#{id,jdbcType=BIGINT}, #{pdId,jdbcType=BIGINT}, #{pdName,jdbcType=VARCHAR},
      #{warehouseNo,jdbcType=INTEGER}, #{warehouseName,jdbcType=VARCHAR}, #{skuId,jdbcType=VARCHAR}, #{viewDate,jdbcType=TIMESTAMP},
      #{orderStatus,jdbcType=INTEGER}, #{replenishmentType,jdbcType=INTEGER}, #{activityFlag,jdbcType=INTEGER},
      #{currentEnabledQuantity,jdbcType=INTEGER}, #{currentOnWayQuantity,jdbcType=INTEGER},
      #{currentTransferInQuantity,jdbcType=INTEGER}, #{salesHistoryQuantity,jdbcType=INTEGER},
      #{transferOutHistoryQuantity,jdbcType=INTEGER}, #{preDay,jdbcType=INTEGER}, #{backlogDay,jdbcType=INTEGER},
      #{safeWaterLevel,jdbcType=INTEGER}, #{adviceReplenishmentQuantity,jdbcType=INTEGER},
      #{supplierId,jdbcType=INTEGER}, #{adminId,jdbcType=INTEGER}, #{finalReplenishmentQuantity,jdbcType=INTEGER},
      #{finalSupplierId,jdbcType=INTEGER}, #{finalSupplierName,jdbcType=VARCHAR}, #{finalAdminId,jdbcType=INTEGER},
      #{finalAdminName,jdbcType=VARCHAR}, #{relationType,jdbcType=INTEGER}, #{relationId,jdbcType=VARCHAR},
      #{createDate,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER},
      #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{delFlag,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.model.domain.purchase.PurchaseReplenishmentOrder">
    insert into purchase_replenishment_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pdId != null">
        pd_id,
      </if>
      <if test="pdName != null">
        pd_name,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="viewDate != null">
        view_date,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="replenishmentType != null">
        replenishment_type,
      </if>
      <if test="activityFlag != null">
        activity_flag,
      </if>
      <if test="currentEnabledQuantity != null">
        current_enabled_quantity,
      </if>
      <if test="currentOnWayQuantity != null">
        current_on_way_quantity,
      </if>
      <if test="currentTransferInQuantity != null">
        current_transfer_in_quantity,
      </if>
      <if test="salesHistoryQuantity != null">
        sales_history_quantity,
      </if>
      <if test="transferOutHistoryQuantity != null">
        transfer_out_history_quantity,
      </if>
      <if test="preDay != null">
        pre_day,
      </if>
      <if test="backlogDay != null">
        backlog_day,
      </if>
      <if test="safeWaterLevel != null">
        safe_water_level,
      </if>
      <if test="adviceReplenishmentQuantity != null">
        advice_replenishment_quantity,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="finalReplenishmentQuantity != null">
        final_replenishment_quantity,
      </if>
      <if test="finalSupplierId != null">
        final_supplier_id,
      </if>
      <if test="finalSupplierName != null">
        final_supplier_name,
      </if>
      <if test="finalAdminId != null">
        final_admin_id,
      </if>
      <if test="finalAdminName != null">
        final_admin_name,
      </if>
      <if test="relationType != null">
        relation_type,
      </if>
      <if test="relationId != null">
        relation_id,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="replenishmentPlanPurchaseTaskId != null">
        replenishment_plan_purchase_task_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pdId != null">
        #{pdId,jdbcType=BIGINT},
      </if>
      <if test="pdName != null">
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="viewDate != null">
        #{viewDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="replenishmentType != null">
        #{replenishmentType,jdbcType=INTEGER},
      </if>
      <if test="activityFlag != null">
        #{activityFlag,jdbcType=INTEGER},
      </if>
      <if test="currentEnabledQuantity != null">
        #{currentEnabledQuantity,jdbcType=INTEGER},
      </if>
      <if test="currentOnWayQuantity != null">
        #{currentOnWayQuantity,jdbcType=INTEGER},
      </if>
      <if test="currentTransferInQuantity != null">
        #{currentTransferInQuantity,jdbcType=INTEGER},
      </if>
      <if test="salesHistoryQuantity != null">
        #{salesHistoryQuantity,jdbcType=INTEGER},
      </if>
      <if test="transferOutHistoryQuantity != null">
        #{transferOutHistoryQuantity,jdbcType=INTEGER},
      </if>
      <if test="preDay != null">
        #{preDay,jdbcType=INTEGER},
      </if>
      <if test="backlogDay != null">
        #{backlogDay,jdbcType=INTEGER},
      </if>
      <if test="safeWaterLevel != null">
        #{safeWaterLevel,jdbcType=INTEGER},
      </if>
      <if test="adviceReplenishmentQuantity != null">
        #{adviceReplenishmentQuantity,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="finalReplenishmentQuantity != null">
        #{finalReplenishmentQuantity,jdbcType=INTEGER},
      </if>
      <if test="finalSupplierId != null">
        #{finalSupplierId,jdbcType=INTEGER},
      </if>
      <if test="finalSupplierName != null">
        #{finalSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="finalAdminId != null">
        #{finalAdminId,jdbcType=INTEGER},
      </if>
      <if test="finalAdminName != null">
        #{finalAdminName,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=INTEGER},
      </if>
      <if test="relationId != null">
        #{relationId,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source},
      </if>
      <if test="replenishmentPlanPurchaseTaskId != null">
       #{replenishmentPlanPurchaseTaskId},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
    insert into purchase_replenishment_order (pd_id, pd_name,
    warehouse_no, warehouse_name, sku_id, view_date,
    order_status, replenishment_type, activity_flag,
    current_enabled_quantity, current_on_way_quantity,
    current_transfer_in_quantity, sales_history_quantity,
    transfer_out_history_quantity, pre_day, backlog_day,
    safe_water_level, advice_replenishment_quantity,
    supplier_id, admin_id, final_replenishment_quantity,
    final_supplier_id, final_supplier_name, final_admin_id,
    final_admin_name, relation_type, relation_id,
    create_date, create_time, updater,
    update_time, creator, del_flag,source,replenishment_plan_purchase_task_id
    )
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.pdId}, #{entity.pdName},
      #{entity.warehouseNo}, #{entity.warehouseName}, #{entity.skuId}, #{entity.viewDate},
      #{entity.orderStatus}, #{entity.replenishmentType}, #{entity.activityFlag},
      #{entity.currentEnabledQuantity}, #{entity.currentOnWayQuantity},
      #{entity.currentTransferInQuantity}, #{entity.salesHistoryQuantity},
      #{entity.transferOutHistoryQuantity}, #{entity.preDay}, #{entity.backlogDay},
      #{entity.safeWaterLevel}, #{entity.adviceReplenishmentQuantity},
      #{entity.supplierId}, #{entity.adminId}, #{entity.finalReplenishmentQuantity},
      #{entity.finalSupplierId}, #{entity.finalSupplierName}, #{entity.finalAdminId},
      #{entity.finalAdminName}, #{entity.relationType}, #{entity.relationId},
      #{entity.createDate}, #{entity.createTime}, #{entity.updater},
      #{entity.updateTime}, #{entity.creator}, #{entity.delFlag},#{entity.source},#{entity.replenishmentPlanPurchaseTaskId}
      )
    </foreach>
  </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.purchase.PurchaseReplenishmentOrder">
    update purchase_replenishment_order
    <set>
      <if test="pdId != null">
        pd_id = #{pdId,jdbcType=BIGINT},
      </if>
      <if test="pdName != null">
        pd_name = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="viewDate != null">
        view_date = #{viewDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="replenishmentType != null">
        replenishment_type = #{replenishmentType,jdbcType=INTEGER},
      </if>
      <if test="activityFlag != null">
        activity_flag = #{activityFlag,jdbcType=INTEGER},
      </if>
      <if test="currentEnabledQuantity != null">
        current_enabled_quantity = #{currentEnabledQuantity,jdbcType=INTEGER},
      </if>
      <if test="currentOnWayQuantity != null">
        current_on_way_quantity = #{currentOnWayQuantity,jdbcType=INTEGER},
      </if>
      <if test="currentTransferInQuantity != null">
        current_transfer_in_quantity = #{currentTransferInQuantity,jdbcType=INTEGER},
      </if>
      <if test="salesHistoryQuantity != null">
        sales_history_quantity = #{salesHistoryQuantity,jdbcType=INTEGER},
      </if>
      <if test="transferOutHistoryQuantity != null">
        transfer_out_history_quantity = #{transferOutHistoryQuantity,jdbcType=INTEGER},
      </if>
      <if test="preDay != null">
        pre_day = #{preDay,jdbcType=INTEGER},
      </if>
      <if test="backlogDay != null">
        backlog_day = #{backlogDay,jdbcType=INTEGER},
      </if>
      <if test="safeWaterLevel != null">
        safe_water_level = #{safeWaterLevel,jdbcType=INTEGER},
      </if>
      <if test="adviceReplenishmentQuantity != null">
        advice_replenishment_quantity = #{adviceReplenishmentQuantity,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="finalReplenishmentQuantity != null">
        final_replenishment_quantity = #{finalReplenishmentQuantity,jdbcType=INTEGER},
      </if>
      <if test="finalSupplierId != null">
        final_supplier_id = #{finalSupplierId,jdbcType=INTEGER},
      </if>
      <if test="finalSupplierName != null">
        final_supplier_name = #{finalSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="finalAdminId != null">
        final_admin_id = #{finalAdminId,jdbcType=INTEGER},
      </if>
      <if test="finalAdminName != null">
        final_admin_name = #{finalAdminName,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        relation_type = #{relationType,jdbcType=INTEGER},
      </if>
      <if test="relationId != null">
        relation_id = #{relationId,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.purchase.PurchaseReplenishmentOrder">
    update purchase_replenishment_order
    set pd_id = #{pdId,jdbcType=BIGINT},
      pd_name = #{pdName,jdbcType=VARCHAR},
      warehouse_no = #{warehouseNo,jdbcType=INTEGER},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      sku_id = #{skuId,jdbcType=VARCHAR},
      view_date = #{viewDate,jdbcType=TIMESTAMP},
      order_status = #{orderStatus,jdbcType=INTEGER},
      replenishment_type = #{replenishmentType,jdbcType=INTEGER},
      activity_flag = #{activityFlag,jdbcType=INTEGER},
      current_enabled_quantity = #{currentEnabledQuantity,jdbcType=INTEGER},
      current_on_way_quantity = #{currentOnWayQuantity,jdbcType=INTEGER},
      current_transfer_in_quantity = #{currentTransferInQuantity,jdbcType=INTEGER},
      sales_history_quantity = #{salesHistoryQuantity,jdbcType=INTEGER},
      transfer_out_history_quantity = #{transferOutHistoryQuantity,jdbcType=INTEGER},
      pre_day = #{preDay,jdbcType=INTEGER},
      backlog_day = #{backlogDay,jdbcType=INTEGER},
      safe_water_level = #{safeWaterLevel,jdbcType=INTEGER},
      advice_replenishment_quantity = #{adviceReplenishmentQuantity,jdbcType=INTEGER},
      supplier_id = #{supplierId,jdbcType=INTEGER},
      admin_id = #{adminId,jdbcType=INTEGER},
      final_replenishment_quantity = #{finalReplenishmentQuantity,jdbcType=INTEGER},
      final_supplier_id = #{finalSupplierId,jdbcType=INTEGER},
      final_supplier_name = #{finalSupplierName,jdbcType=VARCHAR},
      final_admin_id = #{finalAdminId,jdbcType=INTEGER},
      final_admin_name = #{finalAdminName,jdbcType=VARCHAR},
      relation_type = #{relationType,jdbcType=INTEGER},
      relation_id = #{relationId,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateReplenishmentOrder">
    update purchase_replenishment_order set
    <if test="finalReplenishmentQuantity != null">
      final_replenishment_quantity = #{finalReplenishmentQuantity},
    </if>
    <if test="finalSupplierId != null">
      final_supplier_id = #{finalSupplierId},
    </if>
    <if test="finalSupplierId != null">
      final_supplier_name = #{finalSupplierName},
    </if>
    <if test="adminId != null">
      final_admin_id = #{adminId},
    </if>
    <if test="adminName != null">
      final_admin_name = #{adminName},
    </if>
    update_time = now(),updater = #{operatorId}
    where id = #{orderId} and del_flag = 0 and order_status = 1
  </update>
  <update id="closeReplenishmentOrder">
    update purchase_replenishment_order set
    <if test="targetStatus != null">
      order_status = #{targetStatus},
    </if>
     update_time = now(),updater = #{operatorId}
    where del_flag = 0
    <if test="orderId != null">
      and id = #{orderId}
    </if>
    <if test="originalStatus != null">
      and order_status = #{originalStatus}
    </if>
    <if test="createDate != null">
      and create_date = #{createDate}
    </if>
  </update>
  <update id="updateRelationId">
    update purchase_replenishment_order
    set
        updater = #{operatorId}, update_time = now(), order_status = #{targetStatus}, relation_type = #{relationType} , relation_id = #{relationId}
    where
    del_flag = 0 and order_status = #{originalStatus} and create_date = #{createDate}
    and id in
      <foreach collection="orderIds" open="(" close=")" separator="," item="orderId">
        #{orderId}
      </foreach>
  </update>
</mapper>