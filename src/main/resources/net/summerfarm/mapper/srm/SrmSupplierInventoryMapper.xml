<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.srm.SrmSupplierInventoryMapper">
    <resultMap id="ProductMap" type="net.summerfarm.model.vo.srm.SrmSupplierInventoryVo">
        <result column="pd_name" property="pdName" jdbcType="VARCHAR" />
        <result column="pic" property="pic" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
    </resultMap>



    <select id="select" resultMap="ProductMap">
        select  p.pd_name,IFNULL(i.sku_pic,p.picture_path) pic,i.weight,i.sku
        from inventory i
        left join products p  on i.pd_id = p.pd_id
        left join category c on p.category_id = c.id
        left join category pc on c.parent_id = pc.id
        left join product_label_value plv on i.sku = plv.sku
        left join product_label pl on plv.label_id = pl.id
        <where>
            pl.label_field = 'supplier_visible' and plv.label_value = 1 and i.outdated = 0
            <if test="pdName !=null">
                AND p.pd_name like CONCAT('%',#{pdName},'%')
            </if>
            <if test="categoryId != null">
                AND p.category_id = #{categoryId}
            </if>
            <if test="parentCategoryId != null">
                AND c.parent_id = #{parentCategoryId}
            </if>
            <if test="grandCategoryId != null">
                AND pc.parent_id = #{grandCategoryId}
            </if>
        </where>
        GROUP BY i.inv_id
    </select>


</mapper>