<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.TaxRateCodeNameMappingMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.TaxRateCodeNameMapping">
    <!--@mbg.generated-->
    <!--@Table tax_rate_code_name_mapping-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tax_rate_code" jdbcType="VARCHAR" property="taxRateCode" />
    <result column="short_name" jdbcType="VARCHAR" property="shortName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, tax_rate_code, short_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tax_rate_code_name_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from tax_rate_code_name_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.TaxRateCodeNameMapping" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tax_rate_code_name_mapping (create_time, update_time, tax_rate_code, 
      short_name)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{taxRateCode,jdbcType=VARCHAR}, 
      #{shortName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.TaxRateCodeNameMapping" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tax_rate_code_name_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="taxRateCode != null">
        tax_rate_code,
      </if>
      <if test="shortName != null">
        short_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxRateCode != null">
        #{taxRateCode,jdbcType=VARCHAR},
      </if>
      <if test="shortName != null">
        #{shortName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.TaxRateCodeNameMapping">
    <!--@mbg.generated-->
    update tax_rate_code_name_mapping
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxRateCode != null">
        tax_rate_code = #{taxRateCode,jdbcType=VARCHAR},
      </if>
      <if test="shortName != null">
        short_name = #{shortName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.TaxRateCodeNameMapping">
    <!--@mbg.generated-->
    update tax_rate_code_name_mapping
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tax_rate_code = #{taxRateCode,jdbcType=VARCHAR},
      short_name = #{shortName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCodes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tax_rate_code_name_mapping
    where tax_rate_code in
    <foreach collection="taxRateCodes" item="code" open="(" separator="," close=")">
      #{code}
    </foreach>
  </select>
</mapper>