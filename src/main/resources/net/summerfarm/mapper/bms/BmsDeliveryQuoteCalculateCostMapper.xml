<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.bms.BmsDeliveryQuoteCalculateCostMapper">

    <insert id="batchInsertCosts">
        insert into bms_delivery_quota_calculate_cost(
        calculate_name, bms_delivery_quotation_id, formula, calculate_type
        )values
        <foreach collection="items" item="item" index="index" separator=",">
            (#{item.calculateName}, #{item.bmsDeliveryQuotationId}, #{item.formula}, #{item.calculateType})
        </foreach>
    </insert>
    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into bms_delivery_quota_calculate_cost (calculate_name, formula, calculate_type)
        values (#{calculateName},#{formula},#{calculateType})
    </insert>
    <update id="updateById">
        update bms_delivery_quota_calculate_cost
        <set>
            <if test="calculateName != null">
                calculate_name = #{calculateName},
            </if>
            <if test="formula != null">
                formula = #{formula},
            </if>
            <if test="calculateType != null">
                calculate_type = #{calculateType}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <delete id="deleteByQuotationId">
        update bms_delivery_quota_calculate_cost set status = -1 where bms_delivery_quotation_id = #{quotationId} and
        id not in <foreach collection="dbIds" item="item" open="(" separator="," close=")">
        #{item}
    </foreach>
    </delete>
    <delete id="deleteAllByQuotationId">
        update bms_delivery_quota_calculate_cost set status = -1 where bms_delivery_quotation_id = #{quotationId}
    </delete>
    <select id="selectByQuotationId"
            resultType="net.summerfarm.model.vo.bms.BmsDeliveryQuoteCalculateCostVO">
        select calculate_name calculateName, formula ,id ,calculate_type calculateType, bms_delivery_quotation_id bmsDeliveryQuotationId
        from bms_delivery_quota_calculate_cost
        where bms_delivery_quotation_id =#{quotationId} and status = 0
    </select>
    <select id="selectAllCalculateName" resultType="java.lang.String">
        select distinct
        calculate_name calculateName
        from bms_delivery_quota_calculate_cost
        where status = 0 and bms_delivery_quotation_id
        in
        <foreach collection="quotationIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="selectByQuotataionIds" resultType="net.summerfarm.model.vo.bms.BmsDeliveryQuoteCalculateCostVO">
        select id, calculate_name calculateName, calculate_type calculateType
        from bms_delivery_quota_calculate_cost
        where bms_delivery_quotation_id
        in
        <foreach collection="quotationIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and calculate_name in
        <foreach collection="calculateNames" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by calculate_name
    </select>

</mapper>