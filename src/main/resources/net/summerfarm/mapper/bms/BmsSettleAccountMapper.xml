<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.bms.BmsSettleAccountMapper">
    <insert id="insert" keyProperty="id" keyColumn="id" useGeneratedKeys="true">
        insert into bms_settle_account(delivery_start_date, delivery_end_date, quotation_area_id, carrier_id, store_no,
                                       province, city, business_type, bidder_id, bidder_name, settle_target_id, settle_target_name,quotation_area_name,settle_account_no)
        values (#{deliveryStartDate}, #{deliveryEndDate}, #{quotationAreaId}, #{carrierId}, #{storeNo}, #{province}, #{city}, #{businessType}, #{bidderId},#{bidderName},
        #{settleTargetId}, #{settleTargetName}, #{quotationAreaName}, #{settleAccountNo})
    </insert>
    <update id="checkSettleAccount">
        update bms_settle_account
        set status = 1
        where
            id in
            <foreach item = "item" collection="list" separator="," open="(" close=")">
            #{item}
            </foreach>
    </update>
    <update id="relationReconciliation">
        update bms_settle_account set reconciliation_id =#{reconciliationId} where id=#{id}
    </update>
    <select id="selectSettleAccount" resultType="net.summerfarm.model.vo.bms.BmsSettleAccountVO">
        select
        bsa.id,
        bsa.delivery_start_date deliveryStartDate,
        bsa.delivery_end_date deliveryEndDate,
        bdqa.area serviceAreaName,
        bsa.store_no storeNo,
        ca.carrier_name carrierName,
        bsa.province,
        bsa.city,
        bsa.status,
        bdr.reconciliation_no reconciliationNo,
        bsa.discharge_amount dischargeAmount
        from bms_settle_account bsa
        left join carrier ca on bsa.carrier_id = ca.id
        left join bms_delivery_quotation_area bdqa on bdqa.id=bsa.quotation_area_id
        left join bms_delivery_reconciliation bdr on bdr.id = bsa.reconciliation_id
        <where>
            <if test="businessType != null">
                AND bsa.business_type =#{businessType}
            </if>
            <if test="haveReconciliation != null and haveReconciliation == 0">
                AND bsa.reconciliation_id is null
            </if>
            <if test="haveReconciliation != null and haveReconciliation == 1">
                AND bsa.reconciliation_id is not null
            </if>
            <if test="deliveryStartDate != null">
                AND bsa.delivery_start_date <![CDATA[>=]]> #{deliveryStartDate}
            </if>
            <if test="deliveryEndDate != null">
                AND bsa.delivery_end_date <![CDATA[<=]]> #{deliveryEndDate}
            </if>
            <if test="carrierId != null">
                AND bsa.carrier_id =#{carrierId}
            </if>
            <if test="province != null">
                AND bsa.province =#{province}
            </if>
            <if test="cities != null and cities.size != 0 " >
                and (
                <foreach collection="cities" item="it" index="index" separator="or">
                  bsa.city = #{it}
                </foreach>
                )
            </if>
            <if test="status != null">
                AND bsa.status =#{status}
            </if>
            <if test="reconciliationNo != null">
                AND bdr.reconciliation_no =#{reconciliationNo}
            </if>
            <if test="settleAccountIds != null">
                and bsa.id in
                <foreach item = "id" collection="settleAccountIds" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="filterSettleAccountIds != null">
                and bsa.id not in
                <foreach item = "id" collection="filterSettleAccountIds" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="serviceAreaId != null">
                AND bsa.quotation_area_id =#{serviceAreaId}
            </if>
            <if test="storeNo != null">
                AND bsa.store_no =#{storeNo}
            </if>
        </where>
        order by delivery_start_date desc , bsa.id desc
    </select>

    <select id="selectBaseSettleAccountBySupplier" resultType="net.summerfarm.model.vo.bms.BmsSettleAccountVO">
        select
        bsa.id,
        bsa.delivery_start_date deliveryStartDate,
        bsa.delivery_end_date deliveryEndDate,
        bdr.reconciliation_no reconciliationNo,
        from bms_settle_account bsa
        left join bms_delivery_reconciliation bdr on bdr.id = bsa.reconciliation_id
        <where>
            <if test="reconciliationNo != null">
                AND bdr.reconciliation_no =#{reconciliationNo}
            </if>

        </where>
        order by delivery_start_date desc , bsa.id desc
    </select>

    <update id="updateByPrimaryKeySelective">
        update bms_settle_account
        <set>
            <if test="dischargeAmount != null">
                discharge_amount = #{dischargeAmount},
            </if>
        </set>
        where id =#{id}
    </update>
    <select id="selectByPrimaryKey" resultType="net.summerfarm.model.vo.bms.BmsSettleAccountVO">
        select
            bsa.id,
            bsa.delivery_start_date deliveryDate,
            bsa.`quotation_area_name`  serviceAreaName,
            bsa.store_no storeNo,
            bsa.`bidder_name`  carrierName,
            bsa.province,
            bsa.city,
            bsa.status,
            bsa.discharge_amount dischargeAmount,
            bsa.bidder_id bidderId
        from bms_settle_account bsa
        where bsa.id =#{accountId}
    </select>
    <select id="selectByIds" resultType="net.summerfarm.model.vo.bms.BmsSettleAccountVO">
        select
            bsa.id,
            bsa.delivery_start_date deliveryStartDate,
            bsa.delivery_end_date deliveryEndDate,
            bsa.store_no storeNo,
            bsa.province,
            bsa.city,
            bsa.status,
            bsa.quotation_area_id quotationAreaId,
            bsa.carrier_id carrierId
        from bms_settle_account bsa
        where bsa.id in
              <foreach item = "id" collection="list" separator="," open="(" close=")">
                #{id}
              </foreach>
        order by id desc
    </select>
    <select id="sumPayableAmount" resultType="java.math.BigDecimal">
        select ifnull(sum(bcd.amount),0)
        from bms_settle_account_item bsai
        left join bms_delivery_settle_accounts_detail bdsad on bsai.settle_accounts_details_id = bdsad.id
        left join bms_calculation_details bcd on bcd.source_id = bdsad.id
        left join bms_delivery_quota_calculate_cost bdqcc on bcd.calculate_cost_id = bdqcc.id
        where bsai.settle_account_id
        in
        <foreach item = "settleAccountId" collection="list" separator="," open="(" close=")">
            #{settleAccountId}
        </foreach>
        and bcd.source_type = 0
        and bdqcc.calculate_name != '扣减费用'
    </select>
    <select id="sumDeductionAmount" resultType="java.math.BigDecimal">
        select ifnull(sum(bcd.amount),0)
        from bms_settle_account_item bsai
        left join bms_delivery_settle_accounts_detail bdsad on bsai.settle_accounts_details_id = bdsad.id
        left join bms_calculation_details bcd on bcd.source_id = bdsad.id
        left join bms_delivery_quota_calculate_cost bdqcc on bcd.calculate_cost_id = bdqcc.id
        where bsai.settle_account_id
        in
        <foreach item = "settleAccountId" collection="list" separator="," open="(" close=")">
            #{settleAccountId}
        </foreach>
        and bcd.source_type = 0
        and bdqcc.calculate_name = '扣减费用'
    </select>
    <select id="sumDischargeAmount" resultType="java.math.BigDecimal">
        select ifnull(sum(discharge_amount),0)
        from bms_settle_account
        where id
        in
        <foreach item = "settleAccountId" collection="list" separator="," open="(" close=")">
            #{settleAccountId}
        </foreach>
    </select>
    <select id="selectByDetailId" resultType="net.summerfarm.model.vo.bms.BmsSettleAccountVO">
        select bsa.store_no storeNo, bsa.delivery_start_date deliveryDate
        from bms_settle_account_item bsai
                 left join bms_settle_account bsa on bsa.id = bsai.settle_account_id
        where bsai.settle_accounts_details_id =#{id}
    </select>
    <select id="selectByStoreNoAndDeliveryDate" resultType="java.lang.Integer">
        select count(1) from bms_settle_account where store_no = #{storeNo} and delivery_start_date = #{deliveryDate}
    </select>
</mapper>