<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.PurchaseSupplierPaymentMapper">

    <resultMap type="net.summerfarm.model.domain.PurchaseSupplierPayment" id="PurchaseSupplierPaymentMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="purchaseNo" column="purchase_no" jdbcType="VARCHAR"/>
        <result property="advanceAmount" column="advance_amount" jdbcType="DECIMAL"/>
        <result property="paymentAmount" column="payment_amount" jdbcType="DECIMAL"/>
        <result property="writeOffAmount" column="write_off_amount" jdbcType="DECIMAL"/>
        <result property="adjustAmount" column="adjust_amount" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap type="net.summerfarm.model.vo.PurchaseSupplierPaymentVO" id="VOMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="purchaseNo" column="purchase_no" jdbcType="VARCHAR"/>
        <result property="advanceAmount" column="advance_amount" jdbcType="DECIMAL"/>
        <result property="totalAdvanceAmount" column="total_advance_amount" jdbcType="DECIMAL"/>
        <result property="paymentAmount" column="payment_amount" jdbcType="DECIMAL"/>
        <result property="totalPaymentAmount" column="total_payment_amount" jdbcType="DECIMAL"/>
        <result property="writeOffAmount" column="write_off_amount" jdbcType="DECIMAL"/>
        <result property="totalWriteOfAmount" column="total_write_off_amount" jdbcType="DECIMAL"/>
        <result property="adjustAmount" column="adjust_amount" jdbcType="DECIMAL"/>
        <result property="totalAdjustAmount" column="total_adjust_amount" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="PurchaseSupplierPaymentMap">
        select id,
               supplier_id,
               supplier_name,
               purchase_no,
               advance_amount,
               payment_amount,
               write_off_amount,
               adjust_amount,
               create_time,
               update_time
        from purchase_supplier_payment
        where id = #{id}
    </select>


    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="VOMap">
        select
        id, supplier_id, supplier_name, purchase_no, ifnull(advance_amount, 0) advance_amount, ifnull(payment_amount, 0)
        payment_amount,
        ifnull(write_off_amount, 0) write_off_amount, ifnull(adjust_amount, 0) adjust_amount, create_time, update_time
        from purchase_supplier_payment
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="supplierId != null">
                and supplier_id = #{supplierId}
            </if>
            <if test="supplierName != null and supplierName != ''">
                and supplier_name = #{supplierName}
            </if>
            <if test="purchaseNo != null and purchaseNo != ''">
                and purchase_no = #{purchaseNo}
            </if>
            <if test="advanceAmount != null">
                and advance_amount = #{advanceAmount}
            </if>
            <if test="paymentAmount != null">
                and payment_amount = #{paymentAmount}
            </if>
            <if test="writeOffAmount != null">
                and write_off_amount = #{writeOffAmount}
            </if>
            <if test="adjustAmount != null">
                and adjust_amount = #{adjustAmount}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectOne" resultMap="VOMap">
        select id,
               supplier_id,
               supplier_name,
               purchase_no,
               ifnull(advance_amount, 0) advance_amount,
               ifnull(payment_amount, 0) payment_amount,
               ifnull(write_off_amount, 0) write_off_amount,
               ifnull(adjust_amount, 0) adjust_amount,
               create_time,
               update_time
        from purchase_supplier_payment
        <where>
            <if test="supplierId != null">
                supplier_id = #{supplierId}
            </if>
            <if test="purchaseNo != null">
                and purchase_no = #{purchaseNo}
            </if>
        </where>
    </select>

    <select id="querySummaryByNo" resultMap="VOMap">
        select
            ifnull(sum(advance_amount), 0) total_advance_amount,
            ifnull(sum(payment_amount), 0) total_payment_amount,
            ifnull(sum(write_off_amount), 0) total_write_off_amount,
            ifnull(sum(adjust_amount), 0) total_adjust_amount
        from purchase_supplier_payment
        where purchase_no = #{purchaseNo} and supplier_id = #{supplierId}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into xianmudb.purchase_supplier_payment(supplier_id, supplier_name, purchase_no,
                                                       advance_amount, payment_amount, write_off_amount, adjust_amount)
        values (#{supplierId}, #{supplierName}, #{purchaseNo}, #{advanceAmount},
                #{paymentAmount}, #{writeOffAmount}, #{adjustAmount})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into xianmudb.purchase_supplier_payment(supplier_id, supplier_name, purchase_no,
        advance_amount, payment_amount, write_off_amount, adjust_amount)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.supplierId}, #{entity.supplierName}, #{entity.purchaseNo},
            #{entity.advanceAmount}, #{entity.paymentAmount}, #{entity.writeOffAmount}, #{entity.adjustAmount})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update purchase_supplier_payment
        <set>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="supplierName != null and supplierName != ''">
                supplier_name = #{supplierName},
            </if>
            <if test="purchaseNo != null and purchaseNo != ''">
                purchase_no = #{purchaseNo},
            </if>
            <if test="advanceAmount != null">
                advance_amount = #{advanceAmount},
            </if>
            <if test="paymentAmount != null">
                payment_amount = #{paymentAmount},
            </if>
            <if test="writeOffAmount != null">
                write_off_amount = #{writeOffAmount},
            </if>
            <if test="adjustAmount != null">
                adjust_amount = #{adjustAmount},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id} and supplier_id = #{supplierId} and purchase_no = #{purchaseNo}
    </update>

    <update id="updateAmount">
        update purchase_supplier_payment
        set advance_amount = advance_amount + #{advanceAmount}
        where id = #{id}
    </update>

    <select id="selectByPay" resultType="net.summerfarm.model.domain.PurchaseSupplierPayment">
        select id,
        supplier_id supplierId,
        supplier_name supplierName,
        purchase_no purchaseNo,
        advance_amount advanceAmount,
        payment_amount paymentAmount,
        write_off_amount writeOffAmount,
        adjust_amount adjustAmount,
        create_time createTime,
        update_time updateTime
        from purchase_supplier_payment
        where supplier_id = #{supplierId} and purchase_no = #{purchaseNo}
    </select>

    <select id="selectPay" resultType="net.summerfarm.model.domain.PurchaseSupplierPayment">
        select ifnull(advance_amount,0) advanceAmount,ifnull(payment_amount,0) paymentAmount,ifnull(write_off_amount,0) writeOffAmount
        from purchase_supplier_payment
        where supplier_id = #{supplierId} and purchase_no = #{purchaseNo}
    </select>
    <select id="selectSumAmount" resultType="java.lang.Integer">
        select ifnull(sum(ifnull(advance_amount,0))+sum(ifnull(payment_amount,0)),0) paymentAmount
        from purchase_supplier_payment
        where  purchase_no = #{purchaseNo}
    </select>

    <select id="queryAdvanceWriteOffAmountList" resultType="net.summerfarm.model.vo.PurchaseAmountVO">
        select  purchase_no as purchaseNo,
        ifnull(write_off_amount,0) amount
        from purchase_supplier_payment
        where  purchase_no in
        <foreach collection="list" open="(" close=")" separator="," item="purchaseNo">
            #{purchaseNo}
        </foreach>
        and supplier_id = #{supplierId}
        GROUP BY purchase_no
    </select>
</mapper>

