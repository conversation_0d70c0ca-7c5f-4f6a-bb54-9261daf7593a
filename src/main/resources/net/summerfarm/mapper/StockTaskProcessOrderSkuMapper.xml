<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.StockTaskProcessOrderSkuMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.StockTaskProcessOrderSkuDO">
        <!--@mbg.generated-->
        <!--@Table wms_stock_task_process_order_sku-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="stock_task_process_id" jdbcType="BIGINT" property="stockTaskProcessId" />
        <result column="stock_task_id" jdbcType="BIGINT" property="stockTaskId" />
        <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="quantity" jdbcType="INTEGER" property="quantity" />
        <result column="purchase_batch" jdbcType="VARCHAR" property="purchaseBatch" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="operator" jdbcType="VARCHAR" property="operator" />
        <result column="gmt_created" jdbcType="BIGINT" property="gmtCreated" />
        <result column="gmt_modified" jdbcType="BIGINT" property="gmtModified" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
        <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, stock_task_process_id, stock_task_id, out_order_no, sku, quantity, purchase_batch,quality_date
        creator, `operator`, gmt_created, gmt_modified, is_deleted, last_ver
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from wms_stock_task_process_order_sku
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from wms_stock_task_process_order_sku
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.StockTaskProcessOrderSkuDO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into wms_stock_task_process_order_sku (stock_task_process_id, stock_task_id, out_order_no,
        sku, quantity, purchase_batch,quality_date,
        creator, `operator`, gmt_created,
        gmt_modified, is_deleted, last_ver
        )
        values (#{stockTaskProcessId,jdbcType=BIGINT}, #{stockTaskId,jdbcType=BIGINT}, #{outOrderNo,jdbcType=VARCHAR},
        #{sku,jdbcType=VARCHAR}, #{quantity,jdbcType=INTEGER}, #{purchaseBatch,jdbcType=VARCHAR},#{qualityDate,jdbcType=DATE},
        #{creator,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{gmtCreated,jdbcType=BIGINT},
        #{gmtModified,jdbcType=BIGINT}, #{isDeleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER}
        )
    </insert>

    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.StockTaskProcessOrderSkuDO">
        <!--@mbg.generated-->
        update wms_stock_task_process_order_sku
        set stock_task_process_id = #{stockTaskProcessId,jdbcType=BIGINT},
        stock_task_id = #{stockTaskId,jdbcType=BIGINT},
        out_order_no = #{outOrderNo,jdbcType=VARCHAR},
        sku = #{sku,jdbcType=VARCHAR},
        quantity = #{quantity,jdbcType=INTEGER},
        purchase_batch = #{purchaseBatch,jdbcType=VARCHAR},
        creator = #{creator,jdbcType=VARCHAR},
        `operator` = #{operator,jdbcType=VARCHAR},
        gmt_created = #{gmtCreated,jdbcType=BIGINT},
        gmt_modified = #{gmtModified,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=TINYINT},
        last_ver = #{lastVer,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectListByProcessId" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List" />
        from wms_stock_task_process_order_sku
        where stock_task_process_id = #{processId,jdbcType=BIGINT}
    </select>
</mapper>