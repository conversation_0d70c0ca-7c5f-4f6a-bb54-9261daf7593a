<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.PurchaseAdvancedOrderMapper">

    <resultMap type="net.summerfarm.model.domain.PurchaseAdvancedOrder" id="PurchaseAdvancedOrderMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/>
        <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="payType" column="pay_type" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="pdType" column="pd_type" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="supplierAccountId" column="supplier_account_id" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap type="net.summerfarm.model.vo.PurchaseAdvancedOrderVO" id="PurchaseAdvancedOrderVOMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/>
        <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="payType" column="pay_type" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="pdType" column="pd_type" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="accountAscription" column="account_ascription" jdbcType="VARCHAR"/>
        <result property="accountBank" column="account_bank" jdbcType="VARCHAR"/>
        <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="auditor" column="auditor" jdbcType="VARCHAR"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="approver" column="approver" jdbcType="VARCHAR"/>
        <result property="approveTime" column="approve_time" jdbcType="TIMESTAMP"/>
        <result property="payer" column="payer" jdbcType="VARCHAR"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="paymentReview" column="payment_review" jdbcType="VARCHAR"/>
        <result property="paymentReviewTime" column="payment_review_time" jdbcType="TIMESTAMP"/>
        <result property="supplierAccountId" column="supplier_account_id" jdbcType="INTEGER"/>
        <result property="currentProcessor" column="current_processor" jdbcType="VARCHAR"/>
        <result property="deleteReason" column="delete_reason" jdbcType="INTEGER"/>
        <collection property="purchaseInAdvanceDetail" column="id" javaType="ArrayList"
                    select="net.summerfarm.mapper.PurchaseBindingPrepaymentMapper.queryByPurchaseAdvancedOrderId"/>
    </resultMap>

    <resultMap type="net.summerfarm.model.vo.PurchaseAdvancedOrderVO" id="VOMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/>
        <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="payType" column="pay_type" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="pdType" column="pd_type" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="accountAscription" column="account_ascription" jdbcType="VARCHAR"/>
        <result property="accountBank" column="account_bank" jdbcType="VARCHAR"/>
        <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="auditor" column="auditor" jdbcType="VARCHAR"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="approver" column="approver" jdbcType="VARCHAR"/>
        <result property="approveTime" column="approve_time" jdbcType="TIMESTAMP"/>
        <result property="payer" column="payer" jdbcType="VARCHAR"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="paymentVoucher" column="payment_voucher" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="INTEGER"/>
        <result property="temporaryRemark" column="temporary_remark" jdbcType="VARCHAR"/>
        <result property="currentProcessor" column="current_processor" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="PurchaseAdvancedOrderMap">
        select id,
               type,
               total_amount,
               supplier_id,
               supplier_name,
               pay_type,
               create_time,
               pd_type,
               remark,
               status,
               supplier_account_id
        from purchase_advanced_order
        where id = #{id}
    </select>


    <select id="queryAll" resultMap="PurchaseAdvancedOrderMap">
        select
        id, type, total_amount, supplier_id, supplier_name, pay_type, create_time, pd_type, remark, status
        from purchase_advanced_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="totalAmount != null">
                and total_amount = #{totalAmount}
            </if>
            <if test="supplierId != null">
                and supplier_id = #{supplierId}
            </if>
            <if test="supplierName != null and supplierName != ''">
                and supplier_name = #{supplierName}
            </if>
            <if test="payType != null">
                and pay_type = #{payType}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="pdType != null">
                and pd_type = #{pdType}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="selectLastId" resultType="java.lang.Long">
        select id
        from purchase_advanced_order
        order by id desc
        limit 1

    </select>

    <select id="selectDetail" resultMap="PurchaseAdvancedOrderVOMap">
        select pao.id,
               pao.status,
               pao.supplier_name,
               pao.total_amount,
               pao.pay_type,
               pao.supplier_id,
               pao.remark,
               pao.supplier_account_id,
               pao.current_processor,
               pao.delete_reason
        from purchase_advanced_order pao
        where pao.id = #{purchaseInAdvanceId}
    </select>

    <select id="listAll" resultMap="VOMap">
        select pao.id, pao.type, pao.total_amount, pao.supplier_id, pao.supplier_name, pao.pay_type, pao.create_time,fol.operator creator,
        pao.pd_type, pao.remark, pao.status,fpo.payment_voucher,pao.state,pao.temporary_remark,pao.current_processor
        from purchase_advanced_order pao
        left join finance_payment_order fpo on pao.id = fpo.additional_id and fpo.type = 1
        left join purchase_binding_prepayment pbp on pao.id = pbp.purchase_advanced_order_id and pbp.binding_status <![CDATA[ <> ]]> 3
        left join finance_operator_log fol on pao.id = fol.additional_id and fol.type = 1 and fol.status = 0 and fol.personnel_type = 6
        <where>
            <if test="id != null">
                and pao.id = #{id}
            </if>
            <if test="supplierId != null">
                and pao.supplier_id = #{supplierId}
            </if>
            <if test="status != null">
                and pao.status = #{status}
            </if>
            <if test="currentProcessor != null">
                and pao.current_processor = #{currentProcessor}
            </if>
            <if test=" creatorAdminId != null">
                and fol.operator_id = #{creatorAdminId}
            </if>
            <if test=" purchaseNo != null">
                and pbp.purchase_no like CONCAT (#{purchaseNo} ,'%')
            </if>
        </where>
        group by pao.id desc
        order by pao.id desc
    </select>

    <select id="listByAll" parameterType="integer" resultType="net.summerfarm.model.vo.PurchaseAdvancedOrderVO">
        select pao.id, pao.type, pao.total_amount totalAmount, pao.supplier_id supplierId, pao.supplier_name supplierName, pao.pay_type payType, far.create_time createTime,
        pao.pd_type pdType, pao.remark, pao.status,far.auditor,far.approver,far.payer,far.creator,fpo.payment_voucher paymentVoucher,pao.state,pao.temporary_remark temporaryRemark,
               far.creator_admin_id creatorAdminId,far.auditor_admin_id auditorAdminId,far.approver_admin_id approverAdminId,far.payer_admin_id payerAdminId,
               far.audit_time auditTime,far.pay_time payTime,far.approve_time approveTime,far.cancel_time cancelTime
        from purchase_advanced_order pao
        left join finance_audit_record far on pao.id = far.additional_id and far.type = 1
        left join finance_payment_order fpo on pao.id = fpo.additional_id and fpo.type = 1
        where pao.status = #{status}
        order by pao.id desc
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into purchase_advanced_order(type, total_amount, supplier_id, supplier_name,
                                            pay_type, pd_type, remark, status, supplier_account_id,current_processor)
        values (#{type}, #{totalAmount}, #{supplierId}, #{supplierName}, #{payType}, #{pdType},
                #{remark}, #{status}, #{supplierAccountId} #{currentProcessor})
    </insert>

    <insert id="insertByAll" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into purchase_advanced_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">
                `type`,
            </if>
            <if test="totalAmount != null">
                total_amount,
            </if>
            <if test="supplierId != null">
                supplier_id,
            </if>
            <if test="supplierName != null">
                supplier_name,
            </if>
            <if test="payType != null">
                pay_type,
            </if>
            <if test="pdType != null">
                pd_type,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="supplierAccountId != null">
                supplier_account_id,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="temporaryRemark != null">
                temporary_remark,
            </if>
            <if test="currentProcessor != null">
                current_processor,
            </if>
            <if test="deleteReason != null">
                delete_reason,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">
                #{type},
            </if>
            <if test="totalAmount != null">
                #{totalAmount},
            </if>
            <if test="supplierId != null">
                #{supplierId},
            </if>
            <if test="supplierName != null">
                #{supplierName},
            </if>
            <if test="payType != null">
                #{payType},
            </if>
            <if test="pdType != null">
                #{pdType},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="supplierAccountId != null">
                #{supplierAccountId},
            </if>
            <if test="state != null">
                #{state},
            </if>
            <if test="temporaryRemark != null">
                #{temporaryRemark},
            </if>
            <if test="currentProcessor != null">
                #{currentProcessor},
            </if>
            <if test="deleteReason != null">
                #{deleteReason},
            </if>
        </trim>
    </insert>

    <update id="update">
        update purchase_advanced_order
        <set>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="supplierName != null and supplierName != ''">
                supplier_name = #{supplierName},
            </if>
            <if test="payType != null">
                pay_type = #{payType},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="pdType != null">
                pd_type = #{pdType},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="state != null">
                `state` = #{state},
            </if>
            <if test="temporaryRemark != null">
                temporary_remark = #{temporaryRemark},
            </if>
            <if test="deleteReason != null">
                delete_reason = #{deleteReason},
            </if>
            <if test="currentProcessor != null">
                current_processor = #{currentProcessor},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>

