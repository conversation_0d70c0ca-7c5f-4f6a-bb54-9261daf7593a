<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.StockTaskOrderSkuMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.StockTaskOrderSkuDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="stock_task_id" jdbcType="BIGINT" property="stockTaskId" />
        <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
        <result column="sku" jdbcType="VARCHAR" property="sku" />
        <result column="quantity" jdbcType="INTEGER" property="quantity" />
        <result column="actual_quantity" jdbcType="INTEGER" property="actualQuantity" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="operator" jdbcType="VARCHAR" property="operator" />
        <result column="gmt_created" jdbcType="BIGINT" property="gmtCreated" />
        <result column="gmt_modified" jdbcType="BIGINT" property="gmtModified" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
        <result column="last_ver" jdbcType="INTEGER" property="lastVer" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, stock_task_id, out_order_no, sku, quantity, actual_quantity, creator, `operator`,
        gmt_created, gmt_modified, is_deleted, last_ver
    </sql>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.StockTaskOrderSkuDO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into wms_stock_task_order_sku (stock_task_id, out_order_no, sku,
        quantity, actual_quantity, creator,
        `operator`, gmt_created, gmt_modified,
        is_deleted, last_ver)
        values (#{stockTaskId,jdbcType=BIGINT}, #{outOrderNo,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR},
        #{quantity,jdbcType=INTEGER}, #{actualQuantity,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR},
        #{operator,jdbcType=VARCHAR}, #{gmtCreated,jdbcType=BIGINT}, #{gmtModified,jdbcType=BIGINT},
        #{isDeleted,jdbcType=TINYINT}, #{lastVer,jdbcType=INTEGER})
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.StockTaskOrderSkuDO">
        <!--@mbg.generated-->
        update wms_stock_task_order_sku
        <set>
            <if test="stockTaskId != null">
                stock_task_id = #{stockTaskId,jdbcType=BIGINT},
            </if>
            <if test="outOrderNo != null">
                out_order_no = #{outOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="sku != null">
                sku = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="actualQuantity != null">
                actual_quantity = #{actualQuantity,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                `operator` = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreated != null">
                gmt_created = #{gmtCreated,jdbcType=BIGINT},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="lastVer != null">
                last_ver = #{lastVer,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.StockTaskOrderSkuDO">
        <!--@mbg.generated-->
        update wms_stock_task_order_sku
        set stock_task_id = #{stockTaskId,jdbcType=BIGINT},
        out_order_no = #{outOrderNo,jdbcType=VARCHAR},
        sku = #{sku,jdbcType=VARCHAR},
        quantity = #{quantity,jdbcType=INTEGER},
        actual_quantity = #{actualQuantity,jdbcType=INTEGER},
        creator = #{creator,jdbcType=VARCHAR},
        operator = #{operator,jdbcType=VARCHAR},
        gmt_created = #{gmtCreated,jdbcType=BIGINT},
        gmt_modified = #{gmtModified,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=TINYINT},
        last_ver = #{lastVer,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectListByTaskIdAndSkuList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wms_stock_task_order_sku
        where stock_task_id = #{stockTaskId} and sku in
        <foreach collection="skuList" item="sku" open="(" close=")" separator=",">
            #{sku,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectListByTaskIdAndSku" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wms_stock_task_order_sku
        where stock_task_id = #{stockTaskId} and sku = #{sku,jdbcType=VARCHAR}
    </select>

    <update id="updateActualQuantityById">
        update wms_stock_task_order_sku
            set actual_quantity = #{actualQuantity},
                gmt_modified = unix_timestamp(now()) * 1000,
                last_ver = last_ver + 1
            where id = #{id,jdbcType=BIGINT} and quantity >= #{actualQuantity}
    </update>

    <select id="selectListByTaskId" resultMap="BaseResultMap">
        /*FORCE_MASTER*/ select
        <include refid="Base_Column_List" />
        from wms_stock_task_order_sku
        where stock_task_id = #{stockTaskId}
    </select>

    <select id="selectListByOutOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from wms_stock_task_order_sku
        where out_order_no = #{outOrderNo,jdbcType=VARCHAR}
    </select>

    <select id="selectOutOrderNoByOutOrderNoList" resultType="java.lang.String">
        select
            distinct wstos.out_order_no
        from wms_stock_task_order_sku wstos
        inner join stock_task st on st.id = wstos.stock_task_id
            and st.area_no = #{warehouseNo}
            and st.out_store_no = #{storeNo}
            and st.type = #{taskType}
            AND DATE_FORMAT(st.expect_time,'%Y-%m-%d')= #{deliveryDate}
        where wstos.out_order_no in
        <foreach collection="outOrderNoList" item="outOrderNo1" open="(" close=")" separator=",">
            #{outOrderNo1}
        </foreach>
    </select>

    <select id="selectOutOrderNoByNotCancel" resultType="java.lang.String">
        select
            distinct wstos.out_order_no
        from wms_stock_task_order_sku wstos
        inner join stock_task st on st.id = wstos.stock_task_id
        and st.area_no = #{warehouseNo}
        and st.out_store_no = #{storeNo}
        and st.type = #{taskType}
        AND DATE_FORMAT(st.expect_time,'%Y-%m-%d')= #{deliveryDate}
        where st.state not in (3, 4)
    </select>

    <select id="selectListNoByOutOrderNoList" resultMap="BaseResultMap">
        select
            wstos.id,
            wstos.stock_task_id,
            wstos.out_order_no,
            wstos.sku,
            wstos.quantity,
            wstos.actual_quantity,
            wstos.creator,
            wstos.`operator`,
            wstos.gmt_created,
            wstos.gmt_modified,
            wstos.is_deleted,
            wstos.last_ver
        from wms_stock_task_order_sku wstos
        inner join stock_task st on st.id = wstos.stock_task_id
        and st.area_no = #{warehouseNo}
        and st.out_store_no = #{storeNo}
        and st.type = #{taskType}
        AND DATE_FORMAT(st.expect_time,'%Y-%m-%d')= #{deliveryDate}
        where wstos.out_order_no in
        <foreach collection="outOrderNoList" item="outOrderNo1" open="(" close=")" separator=",">
            #{outOrderNo1}
        </foreach>
    </select>
</mapper>