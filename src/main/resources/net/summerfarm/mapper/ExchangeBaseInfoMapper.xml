<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.ExchangeBaseInfoMapper">
    <resultMap id="baseVOMap" type="net.summerfarm.model.vo.ExchangeBaseInfoVO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="trigger_num" jdbcType="INTEGER" property="triggerNum" />
        <result column="purchase_limit" jdbcType="INTEGER" property="purchaseLimit" />
        <result column="discount_percentage" jdbcType="INTEGER" property="discountPercentage" />
        <result column="discount" jdbcType="DECIMAL" property="discount" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="effect_time_type" jdbcType="INTEGER" property="effectTimeType" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <collection property="exchangeScopeConfigVOList" ofType="net.summerfarm.model.vo.ExchangeScopeConfigVO">
            <id column="esc_id" jdbcType="BIGINT" property="id" />
            <result column="area_name" property="areaName"/>
            <result column="esc_type" property="type"/>
            <result column="scope_id" property="scopeId"/>
            <result column="merchant_name" property="merchantName"/>
            <result column="esc_status" property="status"/>
        </collection>
    </resultMap>
    <insert id="insertExchangeBaseInfo" parameterType="net.summerfarm.model.vo.ExchangeBaseInfoVO" useGeneratedKeys="true" keyProperty="id">
        insert into exchange_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="discount != null">
                discount,
            </if>
            <if test="discountPercentage != null">
                `discount_percentage`,
            </if>
            <if test="effectTimeType != null">
                effect_time_type,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="purchaseLimit != null">
                purchase_limit,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="triggerNum != null">
                trigger_num,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="discount != null">
                #{discount},
            </if>
            <if test="discountPercentage != null">
                #{discountPercentage},
            </if>
            <if test="effectTimeType != null">
                #{effectTimeType},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="purchaseLimit != null">
                #{purchaseLimit},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="triggerNum != null">
                #{triggerNum},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="isDelete != null">
                #{isDelete},
            </if>
        </trim>
    </insert>
    <insert id="insertExchangeScopeConfig" parameterType="net.summerfarm.model.vo.ExchangeBaseInfoVO" useGeneratedKeys="true" keyProperty="id">
        insert into exchange_scope_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="baseInfoId != null">
                base_info_id,
            </if>
            <if test="scopeId != null">
                `scope_id`,
            </if>
            <if test="type != null">
               `type`,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="baseInfoId != null">
                #{baseInfoId},
            </if>
            <if test="scopeId != null">
                #{scopeId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>
    <update id="updateById" parameterType="net.summerfarm.model.domain.ExchangeBaseInfo">
        update exchange_base_info
        <set>
            <if test="purchaseLimit != null">
                purchase_limit = #{purchaseLimit},
            </if>
            <if test="discount != null">
                discount = #{discount},
            </if>
            <if test="discountPercentage != null">
                discount_percentage = #{discountPercentage},
            </if>
            <if test="effectTimeType != null">
                effect_time_type = #{effectTimeType},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="triggerNum != null">
                trigger_num = #{triggerNum},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="deleteById" parameterType="net.summerfarm.model.domain.ExchangeBaseInfo">
        update exchange_base_info
               set is_delete = 1
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectPageByExchangeBaseInfoVO" resultMap="baseVOMap">
        SELECT ebi.name,mpi.name merchant_name,ebi.creator,ebi.create_time,
        ebi.status,ebi.effect_time_type,ebi.start_time,ebi.end_time,ebi.id,ebi.type,esc.type esc_type
        FROM exchange_base_info ebi
        left join exchange_scope_config esc on ebi.id = esc.base_info_id
        left join merchant_pool_info mpi on mpi.id = esc.scope_id
        <where>
            ebi.is_delete = 0
            <if test="name != null">
                and ebi.name like concat('%',#{name},'%')
            </if>
            <if test="creator != null">
                and ebi.creator like concat('%',#{creator},'%')
            </if>
            <if test="merchantPoolId != null">
                and mpi.id = #{merchantPoolId}
            </if>
            <if test="status != null">
                <choose>
                    <when test="status == 0">
                        and ((ebi.effect_time_type = 0  and (start_time > now() or (start_time <![CDATA[ <= ]]> now() and end_time > now() and ebi.status = 0))) or (ebi.effect_time_type = 1 and ebi.status = 0))
                    </when>
                    <when test="status == 2">
                        and ebi.effect_time_type = 0 and  end_time <![CDATA[ < ]]> now()
                    </when>
                    <otherwise>
                        and ebi.status = 1 and ((ebi.effect_time_type = 1) or (ebi.effect_time_type = 0 and start_time <![CDATA[ <= ]]> now() and end_time > now()))
                    </otherwise>
                </choose>
            </if>
        </where>
        order by ebi.create_time desc
    </select>
    <select id="selectExchangeBaseInfoDetailById" resultMap="baseVOMap">
        SELECT ebi.name,a.area_name,mpi.name merchant_name,ebi.creator,ebi.create_time,ebi.status,ebi.effect_time_type,ebi.id,ebi.remark,
        ebi.start_time,ebi.end_time,esc.status esc_status,esc.scope_id,esc.type esc_type,ebi.trigger_num,ebi.purchase_limit,ebi.discount_percentage,ebi.discount,esc.id esc_id
        FROM exchange_base_info ebi
        left join exchange_scope_config esc on ebi.id = esc.base_info_id
        left join area a on esc.scope_id = a.id
        left join merchant_pool_info mpi on mpi.id = esc.scope_id
        where ebi.is_delete = 0 and ebi.id = #{id}
    </select>
    <select id="selectExchangeScopeConfigCount" resultMap="baseVOMap" >
        select ebi.name,ebi.creator,ebi.create_time,
        ebi.status,ebi.effect_time_type,ebi.start_time ,ebi.end_time,ebi.id,ebi.type
        from exchange_scope_config esc
        inner join exchange_base_info ebi on ebi.id = esc.base_info_id
        where esc.scope_id = #{scopeId} and esc.type = #{type} and ebi.is_delete = 0 and ebi.status = 1 and esc.status = 1
    </select>
    <select id="selectCreator" resultType="java.lang.String">
        select distinct creator
        from exchange_base_info
        where creator like concat('%',#{creator},'%')
    </select>
</mapper>