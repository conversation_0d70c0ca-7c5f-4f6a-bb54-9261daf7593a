<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.FinanceAccountAdjustMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.FinanceAccountAdjust">
    <!--@mbg.generated-->
    <!--@Table finance_account_adjust-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="finance_account_statement_id" jdbcType="BIGINT" property="financeAccountStatementId" />
    <result column="finance_account_statement_detail_id" jdbcType="BIGINT" property="financeAccountStatementDetailId" />
    <result column="adjust_amount" jdbcType="DECIMAL" property="adjustAmount" />
    <result column="adjust_type" jdbcType="VARCHAR" property="adjustType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, finance_account_statement_id, finance_account_statement_detail_id, adjust_amount, 
    adjust_type, remark, create_time, updater, update_time, creator, del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from finance_account_adjust
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.FinanceAccountAdjust" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into finance_account_adjust
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="financeAccountStatementId != null">
        finance_account_statement_id,
      </if>
      <if test="financeAccountStatementDetailId != null">
        finance_account_statement_detail_id,
      </if>
      <if test="adjustAmount != null">
        adjust_amount,
      </if>
      <if test="adjustType != null and adjustType != ''">
        adjust_type,
      </if>
      <if test="remark != null and remark != ''">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="financeAccountStatementId != null">
        #{financeAccountStatementId,jdbcType=BIGINT},
      </if>
      <if test="financeAccountStatementDetailId != null">
        #{financeAccountStatementDetailId,jdbcType=BIGINT},
      </if>
      <if test="adjustAmount != null">
        #{adjustAmount,jdbcType=DECIMAL},
      </if>
      <if test="adjustType != null and adjustType != ''">
        #{adjustType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into finance_account_adjust
    (finance_account_statement_id, finance_account_statement_detail_id, adjust_amount, 
      adjust_type, remark, create_time, updater, update_time, creator, del_flag)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.financeAccountStatementId,jdbcType=BIGINT}, #{item.financeAccountStatementDetailId,jdbcType=BIGINT}, 
        #{item.adjustAmount,jdbcType=DECIMAL}, #{item.adjustType,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=INTEGER}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=INTEGER}, #{item.delFlag,jdbcType=TINYINT})
    </foreach>
  </insert>

  <select id="selectByFinanceAccountDetailIds" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from finance_account_adjust
    where finance_account_statement_detail_id in
    <foreach collection="list" item="financeAccountStatementDetailId" open="(" separator="," close=")">
        #{financeAccountStatementDetailId}
    </foreach>
  </select>
</mapper>