<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.FinancePaymentOrderMapper">

    <resultMap type="net.summerfarm.model.domain.FinancePaymentOrder" id="FinancePaymentOrderMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="additionalId" column="additional_id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="deleteReason" column="delete_reason" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="paymentVoucher" column="payment_voucher" jdbcType="VARCHAR"/>
        <result property="applicationTime" column="application_time" jdbcType="TIMESTAMP"/>
        <result property="approvalTime" column="approval_time" jdbcType="TIMESTAMP"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="net.summerfarm.model.vo.FinancePaymentOrderVO" id="VOMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="additionalId" column="additional_id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="deleteReason" column="delete_reason" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="paymentVoucher" column="payment_voucher" jdbcType="VARCHAR"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
        <result property="applicationTime" column="application_time" jdbcType="TIMESTAMP"/>
        <result property="approvalTime" column="approval_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="FinancePaymentOrderMap">
        select id,
               additional_id,
               type,
               amount,
               remark,
               status,
               delete_reason,
               creator,
               create_time,
               updater,
               update_time,
               payment_voucher,
               application_time,
               approval_time,
               supplier_name
        from xianmudb.finance_payment_order
        where id = #{id}
    </select>

    <!--查询单个-->
    <select id="selectOne" resultMap="FinancePaymentOrderMap">
        select id,
               additional_id,
               type,
               amount,
               remark,
               status,
               creator,
               create_time,
               updater,
               update_time,
               payment_voucher,
               application_time,
               approval_time
        from xianmudb.finance_payment_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="additionalId != null">
                and additional_id = #{additionalId}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="creator != null and creator != ''">
                and fpo.creator = #{creator}
            </if>
            <if test="createTime != null">
                and fpo.create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and fpo.updater = #{updater}
            </if>
            <if test="updateTime != null">
                and fpo.update_time = #{updateTime}
            </if>
            <if test="paymentVoucher != null and paymentVoucher != ''">
                and fpo.payment_voucher = #{paymentVoucher}
            </if>
        </where>
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="VOMap">
        select
        fpo.id, fpo.additional_id, fpo.type, fpo.amount, fpo.remark, fpo.status, fpo.creator, fpo.create_time,
        fpo.updater, fpo.update_time, fpo.payment_voucher, fpo.supplier_name ,fpo.approval_time  ,fpo.application_time
        from finance_payment_order fpo
        <where>
            <if test="id != null">
                and fpo.id = #{id}
            </if>
            <if test="additionalId != null">
                and fpo.additional_id = #{additionalId}
            </if>
            <if test="type != null">
                and fpo.type = #{type}
            </if>
            <if test="amount != null">
                and fpo.amount = #{amount}
            </if>
            <if test="remark != null and remark != ''">
                and fpo.remark = #{remark}
            </if>
            <if test="status != null">
                and fpo.status = #{status}
            </if>
            <if test="creator != null and creator != ''">
                and fpo.creator = #{creator}
            </if>
            <if test="createTime != null">
                and fpo.create_time = #{createTime}
            </if>
            <if test="updater != null and updater != ''">
                and fpo.updater = #{updater}
            </if>
            <if test="updateTime != null">
                and fpo.update_time = #{updateTime}
            </if>
            <if test="paymentVoucher != null and paymentVoucher != ''">
                and fpo.payment_voucher = #{paymentVoucher}
            </if>
            <if test="supplierId != null">
                and  fpo.supplier_id = #{supplierId}
            </if>
            <if test="supplierName != null">
                and  fpo.supplier_name = #{supplierName}
            </if>
        </where>
        order by fpo.id desc
    </select>

    <select id="queryData" resultMap="VOMap">
    select
    fpo.id, fpo.additional_id, fpo.type, fpo.amount, fpo.remark, fpo.status, fpo.creator, fpo.create_time,
    fpo.updater, fpo.update_time, fpo.payment_voucher, pao.supplier_name supplier_name, pao.supplier_id supplier_id,fpo.approval_time ,fpo.application_time
    from finance_payment_order fpo
        left join purchase_advanced_order pao on pao.id = fpo.additional_id
    where
        fpo.type = #{type}
        order by fpo.id desc
    </select>
    <select id="queryDataOne" resultMap="VOMap">
        select
            fpo.id, fpo.additional_id, fpo.type, fpo.amount, fpo.remark, fpo.status, fpo.creator, fpo.create_time,
            fpo.updater, fpo.update_time, fpo.payment_voucher, f.supplier_name supplier_name,f.supplier_id supplier_id,fpo.approval_time  ,fpo.application_time
        from finance_payment_order fpo
                 left join finance_account_statement f on f.id = fpo.additional_id
        where
            fpo.type = #{type}
        order by fpo.id desc
    </select>
    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true" >
        insert into xianmudb.finance_payment_order(additional_id, type, amount, remark, status, creator,
                                                   updater, payment_voucher,application_time, approval_time)
        values (#{additionalId}, #{type}, #{amount}, #{remark}, #{status}, #{creator}, #{updater}, #{paymentVoucher} ,#{applicationTime}, #{approvalTime])
    </insert>

    <insert id="insertData" keyProperty="id" useGeneratedKeys="true" >
        insert into xianmudb.finance_payment_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="additionalId != null">
                additional_id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="paymentVoucher != null">
                payment_voucher,
            </if>
            <if test="applicationTime != null">
                application_time,
            </if>
            <if test="approvalTime != null">
                approval_time,
            </if>
            <if test="supplierId != null">
                supplier_id,
            </if>
            <if test="supplierName != null">
                supplier_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="additionalId != null">
                #{additionalId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="updater != null">
                #{updater},
            </if>
            <if test="paymentVoucher != null">
                #{paymentVoucher},
            </if>
            <if test="applicationTime != null">
                #{applicationTime},
            </if>
            <if test="approvalTime != null">
                #{approvalTime},
            </if>
            <if test="supplierId != null">
                #{supplierId},
            </if>
            <if test="supplierName != null">
                #{supplierName},
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update xianmudb.finance_payment_order
        <set>
            <if test="additionalId != null">
                additional_id = #{additionalId},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="paymentVoucher != null and paymentVoucher != ''">
                payment_voucher = #{paymentVoucher},
            </if>
            <if test="applicationTime != null">
                application_time = #{applicationTime},
            </if>
            <if test="approvalTime != null">
                approval_time = #{approvalTime},
            </if>
            <if test="deleteReason != null">
                delete_reason = #{deleteReason},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId},
            </if>
            <if test="supplierName != null">
                supplier_name = #{supplierName},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getIdByFinishedAccountIdList" resultType="net.summerfarm.model.vo.FinancePaymentOrderVO">
        select id as id,
        additional_id as additionalId
        from finance_payment_order
        where type=2 and additional_id in
        <foreach collection="list" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        and status = 2
        group by additional_id
    </select>
</mapper>

