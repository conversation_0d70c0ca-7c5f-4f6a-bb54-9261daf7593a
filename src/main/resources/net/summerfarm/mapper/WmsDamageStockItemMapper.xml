<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mapper.WmsDamageStockItemMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.model.domain.WmsDamageStockItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="damage_stock_task_id" jdbcType="BIGINT" property="damageStockTaskId" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="list_no" jdbcType="VARCHAR" property="listNo" />
    <result column="quality_date" jdbcType="DATE" property="qualityDate" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="production_date" jdbcType="DATE" property="productionDate" />
    <result column="gl_no" jdbcType="VARCHAR" property="glNo" />
    <result column="should_quantity" jdbcType="INTEGER" property="shouldQuantity" />
    <result column="out_store_quantity" jdbcType="INTEGER" property="outStoreQuantity" />
    <result column="reason_type" jdbcType="VARCHAR" property="reasonType" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="cabinet_code" jdbcType="VARCHAR" property="cabinetCode" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, damage_stock_task_id, sku, list_no, quality_date, quantity, 
    production_date, gl_no, should_quantity , out_store_quantity, reason_type, reason,actual_quantity actualQuantity, cabinet_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wms_damage_stock_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByDamageStockTaskId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from wms_damage_stock_item
    where damage_stock_task_id = #{id}
  </select>
    <select id="selectByCondition" resultType="net.summerfarm.model.domain.WmsDamageStockItem">
      select wdsi.reason_type reasonType from wms_damage_stock_task wdst
      left join wms_damage_stock_item wdsi on wdst.id = wdsi.damage_stock_task_id
      where wdst.stock_task_id = #{id} limit 1
    </select>
  <select id="selectByTaskNoAndType" resultMap="BaseResultMap">
     select wdsi.sku,wdsi.quality_date,wdsi.should_quantity,wdsi.list_no  from stock_task st
     left join wms_damage_stock_task wdst on wdst.stock_task_id= st.id
     left join wms_damage_stock_item wdsi on wdst.id = wdsi.damage_stock_task_id
     where st.task_no=#{taskNo} and st.type=#{type}
  </select>
  <select id="selectByTaskIdsAndTypeAndSku" resultMap="BaseResultMap">
    select wdsi.sku,wdsi.quality_date,wdsi.should_quantity,wdsi.list_no  from stock_task st
        left join wms_damage_stock_task wdst on wdst.stock_task_id= st.id
        left join wms_damage_stock_item wdsi on wdst.id = wdsi.damage_stock_task_id
    where st.task_no in
    <foreach collection="taskNos" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
     and st.type=#{type} and wdsi.sku = #{sku}
  </select>
    <select id="selectLockBatch" resultType="java.lang.Integer">
     select ifnull(sum(wdsi.should_quantity), 0)
     from wms_damage_stock_task wdst
     left join wms_damage_stock_item wdsi on wdst.id = wdsi.damage_stock_task_id
     where wdst.status in (0,1)
     and wdst.warehouse_no = #{areaNo}
     and wdsi.list_no= #{batch}
     and wdsi.quality_date = #{qualityDate}
     and wdsi.sku= #{sku}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wms_damage_stock_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <delete id="deleteByTaskId">
      delete from wms_damage_stock_item
      where damage_stock_task_id = #{id}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.WmsDamageStockItem" useGeneratedKeys="true">
    insert into wms_damage_stock_item (create_time, update_time, damage_stock_task_id, 
      sku, list_no, quality_date, 
      quantity, production_date, gl_no, 
      should_quantity, out_store_quantity, reason_type, 
      reason, cabinet_code)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{damageStockTaskId,jdbcType=BIGINT}, 
      #{sku,jdbcType=VARCHAR}, #{listNo,jdbcType=VARCHAR}, #{qualityDate,jdbcType=DATE}, 
      #{quantity,jdbcType=INTEGER}, #{productionDate,jdbcType=DATE}, #{glNo,jdbcType=VARCHAR}, 
      #{shouldQuantity,jdbcType=INTEGER}, #{outStoreQuantity,jdbcType=INTEGER}, #{reasonType,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{cabinetCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.model.domain.WmsDamageStockItem" useGeneratedKeys="true">
    insert into wms_damage_stock_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="damageStockTaskId != null">
        damage_stock_task_id,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="listNo != null">
        list_no,
      </if>
      <if test="qualityDate != null">
        quality_date,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="actualQuantity!=null">
        actual_quantity,
      </if>
      <if test="productionDate != null">
        production_date,
      </if>
      <if test="glNo != null">
        gl_no,
      </if>
      <if test="shouldQuantity != null">
        should_quantity,
      </if>
      <if test="outStoreQuantity != null">
        out_store_quantity,
      </if>
      <if test="reasonType != null">
        reason_type,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="liableOwner != null">
        liable_owner,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="cabinetCode != null">
        cabinet_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="damageStockTaskId != null">
        #{damageStockTaskId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="listNo != null">
        #{listNo,jdbcType=VARCHAR},
      </if>
      <if test="qualityDate != null">
        #{qualityDate,jdbcType=DATE},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="actualQuantity!=null">
        #{actualQuantity},
      </if>
      <if test="productionDate != null">
        #{productionDate,jdbcType=DATE},
      </if>
      <if test="glNo != null">
        #{glNo,jdbcType=VARCHAR},
      </if>
      <if test="shouldQuantity != null">
        #{shouldQuantity,jdbcType=INTEGER},
      </if>
      <if test="outStoreQuantity != null">
        #{outStoreQuantity,jdbcType=INTEGER},
      </if>
      <if test="reasonType != null">
        #{reasonType,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="liableOwner != null">
        #{liableOwner,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="cabinetCode != null">
        #{cabinetCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.WmsDamageStockItem">
    update wms_damage_stock_item
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="damageStockTaskId != null">
        damage_stock_task_id = #{damageStockTaskId,jdbcType=BIGINT},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="listNo != null">
        list_no = #{listNo,jdbcType=VARCHAR},
      </if>
      <if test="qualityDate != null">
        quality_date = #{qualityDate,jdbcType=DATE},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="productionDate != null">
        production_date = #{productionDate,jdbcType=DATE},
      </if>
      <if test="glNo != null">
        gl_no = #{glNo,jdbcType=VARCHAR},
      </if>
      <if test="shouldQuantity != null">
        should_quantity = #{shouldQuantity,jdbcType=INTEGER},
      </if>
      <if test="outStoreQuantity != null">
        out_store_quantity = #{outStoreQuantity,jdbcType=INTEGER},
      </if>
      <if test="reasonType != null">
        reason_type = #{reasonType,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="cabinetCode != null">
        cabinet_code = #{cabinetCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.model.domain.WmsDamageStockItem">
    update wms_damage_stock_item
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      damage_stock_task_id = #{damageStockTaskId,jdbcType=BIGINT},
      sku = #{sku,jdbcType=VARCHAR},
      list_no = #{listNo,jdbcType=VARCHAR},
      quality_date = #{qualityDate,jdbcType=DATE},
      quantity = #{quantity,jdbcType=INTEGER},
      production_date = #{productionDate,jdbcType=DATE},
      gl_no = #{glNo,jdbcType=VARCHAR},
      should_quantity = #{shouldQuantity,jdbcType=INTEGER},
      out_store_quantity = #{outStoreQuantity,jdbcType=INTEGER},
      reason_type = #{reasonType,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      cabinet_code = #{cabinetCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>