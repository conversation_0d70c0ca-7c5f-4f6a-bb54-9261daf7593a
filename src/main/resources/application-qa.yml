server:
  port: 80
  servlet:
    session:
      timeout: 3600
spring:
  application:
    id: manage
    name: manage
  tomcat:
    accept-count: 500
    max-connections: 200
    max-threads: 2
    min-spare-threads: 2

  redis:
    host: test-redis.summerfarm.net
    password: xianmu619
    port: 6379
    timeout: 6000
    database: 2
    jedis:
      pool:
        max-active: 4 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 4 # 连接池中的最大空闲连接
        min-idle: 4 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 5
    jedis:
      pool:
        max-active: 4 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 4 # 连接池中的最大空闲连接
        min-idle: 4 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  profiles:
    active: local
  http:
    encoding:
      charset: UTF-8
  datasource:
    tomcat:
      initial-size: 0
      max-active: 20
      max-idle: 20
      min-idle: 1
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 30000
  freemarker:
    template-loader-path: classpath:static/template/
    suffix: .ftl
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: a40f0ca3-5bb6-417f-9df0-fb12c4c464a7
    groupId: manage
    appKey: O/s1zzgepUPeTgGkPejGqQ==
    enable: true
    enabled: true
logging:
  level:
    root:  info
    org.springframework:  INFO
    org.mybatis:  INFO
    com.summerfarm: INFO
  pattern:
    console: "%d - %msg%n"

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 关闭es健康检查
management:
  health:
    elasticsearch:
      enabled: false
log-path: ${APP_LOG_DIR:../log}

mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 1
  maxActive: 20
  maxWait: 6000
  minIdle: 1
  offline:
    password: xianmu619
    url: **************************************************************************************************
    username: test
  password: xianmu619
  testWhileIdle: true
  url: **************************************************************************************************************
  username: qa

rocketmq:
  consumer:
    access-key: Rocketmq
    secret-key: Rocketmq
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: Rocketmq
    group: GID_manage
    secret-key: Rocketmq
    sendMsgTimeout: 10000
stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.feishu.cn
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory
xianmu:
  mall:
    domain: https://qah5.summerfarm.net
dms:
  accessKeyId: LTAI5tPZ4eRj2vMx8tfZY49C00
  accessKeySecret: zNwstNpZuurzzPHike39bEl7uUat0y00
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: 34792f7a-aaa2-41ee-8a7f-53be483c2533
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
    corethreads: 10 #核心线程数，默认0
    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
    queues: 200 #默认0
    alive: 30000 #默认60 * 1000ms
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    threadpool: cached #线程池类型，主要有fixed，cached，eager，limited
    corethreads: 10 #核心线程数，默认0
    threads: 20 #最大线程数，默认Integer.MAX_VALUE，2的32次方-1
    queues: 200 #默认0
    alive: 30000 #默认60 * 1000ms
saas:
  domain: http://cosfo-mall-svc
  host: http://cosfo-mall-svc

xm:
  oss:
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devossperm.summerfarm.net
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devosstemp.summerfarm.net
  log:
    enable: true
    resp: true

dingTalk:
  workNotice:
    stockArrange:
      appKey: dingkgj359gpyufmhewq
      appSecret: 3-FVw314POXaPNTwHBjMHUcAtcrEHcmJnve8JBBLwul90NVIH4RdowHL8aocLKAj
      agentId: 2601674134
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: b781e552-933d-44c5-b642-49dd30c5ba5f