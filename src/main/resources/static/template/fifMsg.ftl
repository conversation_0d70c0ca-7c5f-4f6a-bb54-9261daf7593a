<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
</head>
<#-- 备货 -->
<body>
<p>截止「${month}月${dayOfMonth}日」截单时间，如下配送计划，<strong>还未锁定库存</strong>，有劳合理安排采购或调拨，感谢</p>

<table border="1px">
    <tr>
        <th>计划配送日</th>
        <th>涉及客户数</th>
        <th>商品件数</th>
    </tr>

    <#list modelSonDelivery as key>
        <tr>
            <td>${key["monthValue"]}</td>
            <td>${key["size"]}</td>
            <td>${key["sumAmount"]}</td>
        </tr>
    </#list>
</table>
<p>SKU对应仓库库存情况：</p>
<table border="1px">
    <tr>
        <th>计划配送日</th>
        <th>中心仓</th>
        <th>sku</th>
        <th>商品名称</th>
        <th>规格</th>
        <th>省心送未配送且未锁定数量</th>
        <th>当前中心仓库存</th>
        <th>Gap↑</th>
    </tr>
    <#list modelSonWareHouse as key>
        <tr>
            <td>${key["deliveryTime"]}</td>
            <td>${key["warehouseName"]}</td>
            <td>${key["sku"]}</td>
            <td>${key["pdName"]}</td>
            <td>${key["weight"]}</td>
            <td>${key["sumQuantity"]}</td>
            <td>${key["quantity"]}</td>
            <td>${key["gap"]}</td>
        </tr>
    </#list>
</table>
<p>详细清单如下：</p>
<table border="1px">
    <tr>
        <th>计划配送日</th>
        <th>商品名称</th>
        <th>规格</th>
        <th>数量</th>
        <th>sku</th>
        <th>门店名称</th>
        <th>订单号</th>
        <th>下单时间</th>
    </tr>
    <#list modelSonDetail as key>
        <tr>
            <td>${key["deliveryTime"]}</td>
            <td>${key["pdName"]}</td>
            <td>${key["weight"]}</td>
            <td>${key["quantity"]}</td>
            <td>${key["sku"]}</td>
            <td>${key["mName"]}</td>
            <td>${key["orderNo"]}</td>
            <td>${key["orderTime"]}</td>
        </tr>
    </#list>
</table>
</body>
</html>