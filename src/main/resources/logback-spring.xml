<?xml version="1.0" encoding="UTF-8" ?>

<configuration>
    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>
                [%d{yyyy-MM-dd HH:mm:ss}] [%p] [%X{xm-rqid}] [%X{token}] [%X{xm-phone}] [%X{xm-uid}] [%X{xm-platform}] [%X{xm-biz}] [%X{xm-tenant-id}] [%X{EagleEye-TraceID}] [%X{EagleEye-RpcID}] [%X{xm-inbound-flag}] [%c][%M][%L] -> %msg%n
            </pattern>
        </layout>
    </appender>

    <springProperty scope="context" name="logPath" source="log-path"/>

    <!--  数据埋点  -->
    <appender name="dbpAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss}●[%X{traceId}]%m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!--滚动策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--相对路径 有错-->
            <fileNamePattern>${logPath}/dbp_manage.%d.log</fileNamePattern>
        </rollingPolicy>
    </appender>
    <logger name="dbpLog" level="info" additivity="false">
        <appender-ref ref="dbpAppender" />
    </logger>
    <logger name="RocketmqClient" additivity="false">
        <level value="error" />
    </logger>
    <root level="info">
        <appender-ref ref="consoleLog"/>
    </root>
</configuration>
