<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!-- 去掉从指定路径获取驱动包，该从pom配置文件获取-->
    <context id="DB2Tables" targetRuntime="MyBatis3">
        <!-- 集成lombok插件-->
        <plugin type="net.summerfarm.common.plugins.LombokPlugin"/>

        <!-- 自定义注释插件-->
        <commentGenerator type="net.summerfarm.common.plugins.MysqlCommentGenerator">
            <property name="dateFormat" value="yyyy/MM/dd HH:mm:ss"/>
            <!-- <property name="suppressDate" value="true"/>-->
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <!-- <property name="suppressAllComments" value="true"/>-->
        </commentGenerator>

        <!--数据库链接URL，用户名、密码 -->
        <!--<jdbcConnection driverClass="com.mysql.jdbc.Driver" connectionURL="******************************************************************************************" userId="root" password="root@mysql">-->
        <!--        <jdbcConnection driverClass="com.mysql.jdbc.Driver"-->
        <!--                        connectionURL="************************************************************************************"-->
        <!--                        userId="test" password="xianmu619">-->
        <!--            <property name="useInformationSchema" value="true"/>-->
        <!--        </jdbcConnection>-->
        <!--离线数据库-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="********************************************************************************************"
                        userId="test" password="xianmu619">
            <property name="useInformationSchema" value="true"/>
        </jdbcConnection>

        <!-- 类型解析器 默认false，把JDBC DECIMAL 和 NUMERIC 类型解析为 Integer，为 true时把JDBC DECIMAL 和 NUMERIC 类型解析为java.math.BigDecimal -->
        <!-- 解析TINYINT为java的Integer类型-->
        <javaTypeResolver type="net.summerfarm.common.plugins.TinyIntJavaTypeResolver">
            <property name="forceBigDecimals" value="false"/>
            <!--当useJSR310Types为true时，就会jdbc对应的日期类型会转成java8中的LocateDateTime类型，如果useJSR310Types为false，则还是转成java.util.Date类型-->
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>

        <!-- 生成模型的包名和位置-->
        <!--        <javaModelGenerator targetPackage="net.summerfarm.model.domain.plan" targetProject="src/main/java">-->
        <javaModelGenerator targetPackage="net.summerfarm.model.domain.offline" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- 生成映射文件的包名和位置-->
        <!--        <sqlMapGenerator targetPackage="net.summerfarm.mapper.plan" targetProject="src/main/resources">-->
        <sqlMapGenerator targetPackage="net.summerfarm.mapper.offline" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- 生成DAO的包名和位置-->
        <!--        <javaClientGenerator type="XMLMAPPER" targetPackage="net.summerfarm.mapper.plan" targetProject="src/main/java">-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="net.summerfarm.mapper.offline"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>


        <!-- 要生成的表 tableName是数据库中的表名或视图名 domainObjectName是实体类名-->


        <table tableName="replenishment_plan_base_info" domainObjectName="ReplenishmentPlanBaseInfo"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"/>

    </context>
</generatorConfiguration>