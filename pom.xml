<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.2.2.RELEASE</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>summerfarm-manage</artifactId>
    <groupId>net.summerfarm</groupId>
    <packaging>jar</packaging>
    <version>1.0.0</version>
    <name>manageApplication</name>

    <!--各个jar包的版本-->
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <!-- 此版本兼容easyexcel-->
        <poi.version>4.1.2</poi.version>
        <!--各个jar包的版本-->
        <!--    依赖版本统一配置、便于管理    -->
        <fastjson.version>1.2.29</fastjson.version>
        <lombok.version>1.16.18</lombok.version>
        <typehandlers.version>1.0.1</typehandlers.version>
        <datatype.version>2.9.2</datatype.version>
        <swagger.version>2.7.0</swagger.version>
        <qiniu.version>[7.7.0, 7.10.99]</qiniu.version>
        <starter.version>2.1.1</starter.version>
        <mysql-connector.version>8.0.23</mysql-connector.version>
        <mybatis.version>3.5.0</mybatis.version>
        <druid.version>1.2.6</druid.version>
        <redis.version>3.1.0</redis.version>
        <commons-math3.version>3.6.1</commons-math3.version>
        <xstream.version>1.4.7</xstream.version>
        <es.version>7.10.0</es.version>
        <gauva.version>28.2-jre</gauva.version>
        <easyexcel.version>3.1.0</easyexcel.version>
        <QLExpress.version>3.2.7</QLExpress.version>
        <smart-doc.version>2.5.3-xm</smart-doc.version>
        <!-- 临时包  合并代码有问题-->
        <xianmu-auth-sdk>1.1.2</xianmu-auth-sdk>
        <bms.service.client>1.2.3-RELEASE</bms.service.client>
        <manage.client>1.0.56-RELEASE</manage.client>
        <wnc-client>1.3.8-RELEASE</wnc-client>
        <xianmu-robot-util.version>1.0.2</xianmu-robot-util.version>
        <sf-mall-manage-client.version>1.0.7-RELEASE</sf-mall-manage-client.version>
        <message-client.version>1.1-RELEASE</message-client.version>
        <common-client.version>1.0.19-RELEASE</common-client.version>
        <nacos-config.version>0.2.10</nacos-config.version>
        <cosfo-order-client>1.0.5</cosfo-order-client>
        <usercenter-client.version>1.1.7</usercenter-client.version>
        <marketing-center-client.version>1.0.12-RELEASE</marketing-center-client.version>
        <inventory-client.verison>2.0.15-RELEASE</inventory-client.verison>
        <inventory-sdk.verison>2.0.16-RELEASE</inventory-sdk.verison>
        <pms-client>1.6.0-RELEASE</pms-client>
        <goods-center.version>1.2.0-RELEASE</goods-center.version>
        <item-center-client.version>1.0.38-RELEASE</item-center-client.version>
        <cosfo-manage-client.version>1.3.8-RELEASE</cosfo-manage-client.version>
        <payment-sdk.version>1.0.9-RELEASE</payment-sdk.version>
        <ofc-client.version>1.6.5-RELEASE</ofc-client.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-mybatis-interceptor-support</artifactId>
            <version>1.0.6-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.arms.apm</groupId>
            <version>1.7.5</version>
            <artifactId>arms-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <version>${nacos-config.version}</version>
        </dependency>
        <!--    核心依赖模块    -->
        <dependency>
            <artifactId>summerfarm-inventory-sdk</artifactId>
            <groupId>net.summerfarm</groupId>
            <version>${inventory-sdk.verison}</version>
            <exclusions>
                <exclusion>
                    <groupId>net.summerfarm.wms</groupId>
                    <artifactId>summerfarm-wms-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.summerfarm.wnc</groupId>
                    <artifactId>summerfarm-wnc-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>summerfarm-inventory-client</artifactId>
            <version>${inventory-client.verison}</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-common</artifactId>
            <version>1.5.18-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>qiniu-java-sdk</artifactId>
                    <groupId>com.qiniu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okio</artifactId>
                    <groupId>com.squareup.okio</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mysql-connector-java</artifactId>
                    <groupId>mysql</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-datatype-jsr310</artifactId>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch-rest-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
                <exclusion>
                    <groupId>net.xianmu.common</groupId>
                    <artifactId>xianmu-robot-util</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-common</artifactId>
            <!--      根据实际版本修改，线上禁止SNAPSHOT版本     -->
            <version>1.1.5-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-rocketmq-support</artifactId>
            <version>1.2.2</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-task-support</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-redis-support</artifactId>
            <version>1.2.1</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-log-support</artifactId>
            <version>1.0.14-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.manage.client</groupId>
            <artifactId>manage-client</artifactId>
            <version>${manage.client}</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>crm-client</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>mall-client</artifactId>
            <version>1.0.31-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>cosfo-manage-client</artifactId>
            <version>${cosfo-manage-client.version}</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm.wms</groupId>
            <artifactId>summerfarm-wms-client</artifactId>
            <version>1.5.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-client</artifactId>
            <version>${wnc-client}</version>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-client</artifactId>
            <version>${usercenter-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>item-center-client</artifactId>
            <version>${item-center-client.version}</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-payment-sdk</artifactId>
            <version>${payment-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-warehouse</artifactId>
            <version>1.3.8-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>qiniu-java-sdk</artifactId>
                    <groupId>com.qiniu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mysql-connector-java</artifactId>
                    <groupId>mysql</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>summerfarm-common</artifactId>
                    <groupId>net.summerfarm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-datatype-jsr310</artifactId>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch-rest-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>shiro-core</artifactId>
            <groupId>org.apache.shiro</groupId>
            <version>1.2.5</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo.summerfarm</groupId>
            <artifactId>saas-to-summerfarm</artifactId>
            <version>1.6.13-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>ofc-client</artifactId>
            <version>${ofc-client.version}</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-sdk</artifactId>
            <version>${xianmu-auth-sdk}</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>sf-mall-manage-client</artifactId>
            <version>${sf-mall-manage-client.version}</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-robot-util</artifactId>
            <version>${xianmu-robot-util.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>message-client</artifactId>
            <version>${message-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.xianmu.common</groupId>
                    <artifactId>xianmu-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>marketing-center-client</artifactId>
            <version>${marketing-center-client.version}</version>
        </dependency>

        <!--  springboot 核心依赖包  -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>classmate</artifactId>
                    <groupId>com.fasterxml</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-datatype-jsr310</artifactId>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>classmate</artifactId>
                    <groupId>com.fasterxml</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- validation -->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-datatype-jsr310</artifactId>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>HdrHistogram</artifactId>
                    <groupId>org.hdrhistogram</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.restdocs</groupId>
            <artifactId>spring-restdocs-mockmvc</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--引入java mail 发邮件用-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <!-- alibaba json -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <!--  lombok  -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <!--mybatis整合spring组件-->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${starter.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>${mybatis.version}</version>
        </dependency>
        <!--数据库组件——mysql连接组件-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector.version}</version>
            <scope>runtime</scope>
        </dependency>
        <!--alibaba开源数据库连接池-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <!--alibaba动态脚本引擎解析工具-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>QLExpress</artifactId>
            <version>${QLExpress.version}</version>
        </dependency>
        <dependency>
            <groupId> e-iceblue </groupId>
            <artifactId>spire.doc.free</artifactId>
            <version>3.9.0</version>
        </dependency>
        <!--  mybatis类型处理  -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
            <version>${typehandlers.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${datatype.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>
        <!--redis依赖-->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${redis.version}</version>
        </dependency>
        <!--  swagger  -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mapstruct</artifactId>
                    <groupId>org.mapstruct</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>classmate</artifactId>
                    <groupId>com.fasterxml</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--   apache数学工具包     -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-math3</artifactId>
            <version>${commons-math3.version}</version>
        </dependency>
        <!--    xstream    -->
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>${xstream.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.yedaxia</groupId>
            <artifactId>japidocs</artifactId>
            <version>1.3</version>
        </dependency>
        <!--    es连接    -->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>${es.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>${es.version}</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>${es.version}</version>
        </dependency>
        <!--    阿里云odbs    -->
        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-sdk-core</artifactId>
            <version>0.35.5-public</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Google guava工具包-->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${gauva.version}</version>
        </dependency>
        <!-- hutool 工具包-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>4.1.5</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.11.1</version>
        </dependency>
        <!-- redisson -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.16.4</version>
        </dependency>
        <!--nacos依赖-->
       <!-- <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <version>0.2.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>-->
        <!-- AOP依赖,一定要加,否则权限拦截验证不生效 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!--google二维码依赖-->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.3.3</version>
        </dependency>
        <!-- 上传组件包 -->
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <!--excel等导入导出-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>

        <!--    定时任务调度    -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.2.3</version>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz-jobs</artifactId>
            <version>2.2.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml</groupId>
            <artifactId>classmate</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.taobao.dingtalk</groupId>
            <artifactId>dingtalk</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.5.21</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-ram</artifactId>
            <version>3.3.0</version>
        </dependency>

        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
            <version>portable-1.7.8</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <!--  七牛上传SDK  -->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <version>${qiniu.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.14.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>1.4.38</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dms_enterprise20181101</artifactId>
            <version>1.28.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>org.jacoco.agent</artifactId>
                    <groupId>org.jacoco</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-dubbo-support</artifactId>
            <version>1.0.9</version>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
            <version>2.7.15</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>ram20150501</artifactId>
            <version>1.0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>tea-util</artifactId>
                    <groupId>com.aliyun</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tea-openapi</artifactId>
                    <groupId>com.aliyun</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>tea</artifactId>
                    <groupId>com.aliyun</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dom4j</artifactId>
                    <groupId>org.dom4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>credentials-java</artifactId>
                    <groupId>com.aliyun</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-oss-support</artifactId>
            <version>1.0.7</version>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>message-client</artifactId>
            <version>1.3.0-RELEASE</version>
        </dependency>

        <!--freemarker -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
            <version>2.0.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
            <version>1.8.8.4.Final</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>tms-api</artifactId>
            <version>0.0.13</version>
            <exclusions>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-authorization</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>tms-client</artifactId>
            <version>1.1.7-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-authorization</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>poi-ooxml-schemas</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--mapstruct-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.2.0.Final</version>
        </dependency>
        <!--发票第三方百旺加解密sdk-->
        <dependency>
            <groupId>cn.bwjf</groupId>
            <artifactId>bcpkix</artifactId>
            <version>1.60</version>
        </dependency>
        <dependency>
            <groupId>cn.bwjf</groupId>
            <artifactId>bcprov-ext</artifactId>
            <version>1.60</version>
        </dependency>
        <dependency>
            <groupId>cn.bwjf</groupId>
            <artifactId>xjbw-security-access</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>1.3.5</version>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-client</artifactId>
            <version>1.1.13</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.11</version>
        </dependency>

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>bms-service-client</artifactId>
            <version>${bms.service.client}</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>common-client</artifactId>
            <version>${common-client.version}</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>xianmu-download-support</artifactId>
            <version>1.0.8</version>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>order-center-client</artifactId>
            <version>${cosfo-order-client}</version>

        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-pms-client</artifactId>
            <version>${pms-client}</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>goods-center-client</artifactId>
            <version>${goods-center.version}</version>
        </dependency>
    </dependencies>

    <build>
        <defaultGoal>install</defaultGoal>

        <plugins>
            <!--自动生成mybatis相关代码 插件-->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.4.0</version>
                <configuration>
                    <configurationFile>src/main/resources/generatorConfig.xml</configurationFile>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.mybatis.generator</groupId>
                        <artifactId>mybatis-generator-core</artifactId>
                        <version>1.4.0</version>
                    </dependency>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>${mysql.version}</version>
                        <scope>runtime</scope>
                    </dependency>
                </dependencies>
            </plugin>
            <!--设置默认Java compiler-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.2.0.Final</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>

                    </annotationProcessorPaths>
                    <showWarnings>true</showWarnings>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>net.summerfarm.ManageApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
