# queryContainsGSku 方法问题分析与修复

## 问题概述

`ProductsPropertyValueMapper#queryContainsGSku` 方法存在多个问题，主要涉及返回类型不匹配、SQL逻辑不完善和性能问题。

## 详细问题分析

### 1. 返回类型不匹配

**问题描述**：
- Java接口定义返回 `PageInfo<String>`
- XML实现返回 `List<String>`
- 调用方使用 `PageHelper.startPage()` 期望分页结果

**影响**：
- 分页功能可能不正常工作
- 类型转换可能出现问题
- 调用方代码逻辑混乱

### 2. SQL逻辑问题

**原始SQL**：
```sql
SELECT sku
FROM products_property_value
WHERE products_property_id = 5
AND BINARY products_property_value LIKE '%G%'
AND products_property_value NOT LIKE '%KG%'
```

**存在的问题**：

#### 2.1 大小写处理不一致
- 使用 `BINARY` 查找大写 'G'，但排除条件用普通 `LIKE` 查找 'KG'
- 可能遗漏小写 'g' 的情况
- 逻辑不够严谨

#### 2.2 缺少必要的空值检查
- 没有检查 `sku IS NOT NULL`
- 没有检查 `products_property_value IS NOT NULL`
- 可能返回无效数据

#### 2.3 可能的重复数据
- 没有使用 `DISTINCT`
- 同一个 SKU 可能有多条属性记录

#### 2.4 缺少排序
- 结果集没有排序，分页结果可能不稳定

### 3. 性能问题

**问题**：
- 没有合适的索引优化
- 使用 `LIKE '%G%'` 这种前缀通配符查询性能较差
- 大数据量时可能导致性能问题

### 4. 调用场景分析

**使用场景**：在 `SkuG2gProcessor` 定时任务中使用
```java
// 分页查询包含大写G的SKU
PageHelper.startPage(pageIndex, 100);
page = mapper.queryContainsGSku();
List<String> skus = page.getList();

// 批量更新：将G替换为g
mapper.updateG2g(skus);
inventoryMapper.updateG2g(skus);
```

**目的**：批量处理商品属性值中的大小写问题，将 'G' 替换为 'g'

## 修复方案

### 1. SQL优化

**修复后的SQL**：
```sql
SELECT DISTINCT sku
FROM products_property_value
WHERE products_property_id = 5
  AND sku IS NOT NULL
  AND products_property_value IS NOT NULL
  AND (
    (BINARY products_property_value LIKE '%G%' AND BINARY products_property_value NOT LIKE '%KG%')
    OR 
    (BINARY products_property_value LIKE '%g%' AND BINARY products_property_value NOT LIKE '%kg%')
  )
ORDER BY sku
```

**改进点**：
1. **添加 DISTINCT**：避免重复的SKU
2. **空值检查**：确保数据有效性
3. **大小写兼容**：同时处理大写G和小写g
4. **逻辑一致性**：排除条件也使用BINARY确保一致性
5. **添加排序**：确保分页结果稳定

### 2. 接口定义保持不变

由于调用方已经使用 `PageHelper` 进行分页，接口定义 `PageInfo<String>` 是正确的，XML实现会被PageHelper自动包装成PageInfo对象。

### 3. 性能优化建议

**建议添加索引**：
```sql
-- 为查询条件添加复合索引
CREATE INDEX idx_ppv_property_value ON products_property_value(products_property_id, products_property_value, sku);
```

**或者更具体的索引**：
```sql
-- 针对属性ID=5的索引
CREATE INDEX idx_ppv_property5_sku ON products_property_value(products_property_id, sku) 
WHERE products_property_id = 5;
```

## 修复效果

### 1. 功能完善
- ✅ 正确处理大小写G的情况
- ✅ 避免返回重复SKU
- ✅ 过滤无效数据
- ✅ 分页结果稳定

### 2. 性能提升
- ✅ 减少重复数据处理
- ✅ 稳定的排序结果
- ✅ 更精确的查询条件

### 3. 代码健壮性
- ✅ 空值安全
- ✅ 逻辑一致性
- ✅ 更好的可维护性

## 测试建议

### 1. 功能测试
```sql
-- 测试查询是否正确返回包含G但不包含KG的SKU
SELECT DISTINCT sku, products_property_value
FROM products_property_value
WHERE products_property_id = 5
  AND sku IS NOT NULL
  AND products_property_value IS NOT NULL
  AND (
    (BINARY products_property_value LIKE '%G%' AND BINARY products_property_value NOT LIKE '%KG%')
    OR 
    (BINARY products_property_value LIKE '%g%' AND BINARY products_property_value NOT LIKE '%kg%')
  )
ORDER BY sku
LIMIT 10;
```

### 2. 性能测试
- 测试大数据量下的查询性能
- 验证分页功能是否正常
- 检查内存使用情况

### 3. 集成测试
- 验证 `SkuG2gProcessor` 定时任务是否正常运行
- 确认批量更新功能正常
- 检查日志输出是否正确

## 注意事项

1. **数据备份**：在生产环境执行前建议备份相关数据
2. **索引影响**：新增索引可能影响写入性能，需要评估
3. **业务逻辑**：确认修复后的逻辑符合业务需求
4. **监控告警**：部署后需要监控定时任务的执行情况

## 后续优化建议

1. **参数化查询**：将硬编码的 `products_property_id = 5` 改为可配置参数
2. **批处理优化**：考虑使用更大的批次大小提高处理效率
3. **错误处理**：增加更详细的错误处理和重试机制
4. **监控指标**：添加处理数量、耗时等监控指标
