# Excel SKU 数字格式问题修复

## 问题描述

**现象**：
- Excel 中 SKU 列设置为文本格式，显示为 `2238845347`
- Java 读取后变成科学计数法：`2.238845347E9`

## 问题分析

### 根本原因
1. **Excel 内部存储**：即使列格式设置为文本，如果数据本身是数字形式输入的，Excel 内部仍以 `double` 类型存储
2. **POI 读取机制**：Apache POI 的 `getCellValue()` 方法检测到数字类型后，返回 `double` 值
3. **字符串转换**：`double` 值调用 `toString()` 时，大数字自动转换为科学计数法

### 代码执行流程
```java
// 1. Excel 单元格类型检测
cell.getCellType() == CellType.NUMERIC  // true

// 2. 获取数值
cell.getNumericCellValue()  // 返回 2.238845347E9 (double)

// 3. 转换为字符串
ExcelUtils.getCellValue(cell).toString()  // "2.238845347E9"
```

### 问题代码位置
```java
// ExcelUtils.java
public static String getCellStringValue(Cell cell){
    if (cell == null){
        return null;
    }
    return ExcelUtils.getCellValue(cell).toString();  // ← 问题在这里
}

public static Object getCellValue(Cell cell) {
    switch (cell.getCellType()) {
        case NUMERIC:
            obj = cell.getNumericCellValue();  // ← 返回 double，大数字变科学计数法
            break;
        // ...
    }
    return obj;
}
```

## 修复方案

### 核心思路
对数字类型的单元格进行特殊处理，使用 `DataFormatter` 获取单元格的显示值，保持原始格式。

### 修复后的代码
```java
public static String getCellStringValue(Cell cell){
    if (cell == null){
        return null;
    }

    // 针对数字类型特殊处理，避免科学计数法
    if (cell.getCellType() == CellType.NUMERIC && !DateUtil.isCellDateFormatted(cell)) {
        // 使用 DataFormatter 来获取单元格的显示值，保持原始格式
        DataFormatter formatter = new DataFormatter();
        String formattedValue = formatter.formatCellValue(cell);
        
        // 如果格式化后仍然是科学计数法，则使用 DecimalFormat 处理
        if (formattedValue.contains("E") || formattedValue.contains("e")) {
            double numericValue = cell.getNumericCellValue();
            // 对于整数，使用无小数点格式
            if (numericValue == Math.floor(numericValue)) {
                return String.format("%.0f", numericValue);
            } else {
                // 对于小数，保留适当的精度
                return String.format("%.10f", numericValue).replaceAll("0+$", "").replaceAll("\\.$", "");
            }
        }
        return formattedValue;
    }

    return ExcelUtils.getCellValue(cell).toString();
}
```

### 修复逻辑说明

1. **类型检测**：
   ```java
   cell.getCellType() == CellType.NUMERIC && !DateUtil.isCellDateFormatted(cell)
   ```
   - 检测是否为数字类型且非日期格式

2. **优先使用 DataFormatter**：
   ```java
   DataFormatter formatter = new DataFormatter();
   String formattedValue = formatter.formatCellValue(cell);
   ```
   - `DataFormatter` 会根据单元格的格式设置返回显示值
   - 如果 Excel 中设置为文本格式，会尽量保持原始显示

3. **科学计数法检测与处理**：
   ```java
   if (formattedValue.contains("E") || formattedValue.contains("e")) {
       // 手动格式化处理
   }
   ```
   - 如果仍然是科学计数法，使用 `String.format` 强制转换

4. **整数与小数区分**：
   ```java
   if (numericValue == Math.floor(numericValue)) {
       return String.format("%.0f", numericValue);  // 整数格式
   }
   ```
   - 整数：使用 `%.0f` 格式，避免小数点
   - 小数：使用 `%.10f` 并去除尾部零

## 修复效果

### 测试用例
| Excel 显示 | 修复前读取结果 | 修复后读取结果 |
|-----------|--------------|--------------|
| 2238845347 | 2.238845347E9 | 2238845347 |
| 1234567890 | 1.23456789E9 | 1234567890 |
| 123.456 | 123.456 | 123.456 |
| 100.00 | 100.0 | 100 |

### 兼容性保证
- ✅ 字符串类型：保持原有逻辑不变
- ✅ 日期类型：保持原有逻辑不变
- ✅ 布尔类型：保持原有逻辑不变
- ✅ 公式类型：保持原有逻辑不变
- ✅ 小数字：正常显示，无影响
- ✅ 大整数：修复科学计数法问题

## 影响范围

### 直接影响的功能
1. **商品批量导入**：SKU 编码读取
2. **区域 SKU 导入**：SKU 相关字段读取
3. **其他 Excel 导入功能**：所有使用 `ExcelUtils.getCellStringValue()` 的地方

### 潜在风险评估
- **低风险**：修复只影响数字类型的单元格处理
- **向后兼容**：其他类型的单元格处理逻辑完全不变
- **性能影响**：微小，只增加了类型检测和格式化处理

## 测试建议

### 1. 功能测试
- 测试包含大数字 SKU 的 Excel 导入
- 验证 SKU 编码是否正确读取
- 测试其他数字字段（价格、数量等）

### 2. 兼容性测试
- 测试包含文本、日期、布尔值的 Excel 文件
- 验证原有功能是否正常
- 测试不同 Excel 版本（.xls, .xlsx）

### 3. 边界测试
- 测试极大数字（超过 Long 范围）
- 测试小数精度
- 测试科学计数法输入

### 4. 性能测试
- 测试大文件导入性能
- 对比修复前后的处理时间

## 部署建议

1. **测试环境验证**：先在测试环境充分测试各种 Excel 格式
2. **灰度发布**：可以考虑先在部分功能中启用
3. **监控日志**：关注导入功能的日志输出
4. **回滚准备**：保留原始代码，便于快速回滚

## 相关文件

- `src/main/java/net/summerfarm/common/excel/utils/ExcelUtils.java` - 主要修复文件
- `src/main/java/net/summerfarm/service/impl/ProductsServiceImpl.java` - 使用该工具类的业务代码

## 技术要点

### Apache POI 单元格类型处理
- `CellType.NUMERIC`：数字类型
- `CellType.STRING`：字符串类型
- `DateUtil.isCellDateFormatted()`：日期格式检测

### DataFormatter 的作用
- 根据单元格格式设置返回显示值
- 尊重 Excel 中的格式设置
- 处理各种数字格式（货币、百分比等）

### 数字精度处理
- `Math.floor()` 检测整数
- `String.format("%.0f")` 整数格式化
- 正则表达式去除尾部零：`replaceAll("0+$", "")`
