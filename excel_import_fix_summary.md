# Excel 导入 SKU 数据读取修复总结

## 问题描述
在 `ProductsService#createProductBatch` 方法中，Excel 导入功能无法正确读取 SKU 相关数据，原因是代码中的索引映射与实际 Excel 表头结构不匹配。

## Excel 表头结构（共38列，索引0-37）
```
索引  | 列名
-----|--------------------
0    | 三级类目
1    | SPU编码  
2    | 商品名称（必填）
3    | 副标题
4    | 实物名称（必填）
5    | 贮存区域（必填）
6    | 保质期时长（月/天）（必填）
7    | 到期预警x天（选填）
8    | 产地
9    | 品牌
10   | 储藏温度
11   | 面筋含量
12   | 品种
13   | 熟度
14   | 每100g含蛋白质
15   | 每100g乳脂含量
16   | 其它介绍
17   | SKU编码 ⭐
18   | 性质（自营-代销不入仓等）
19   | 代仓所属
20   | 类型（常规/拆包/不卖）
21   | 进口/国产
22   | 展示平均值（是/否）
23   | 最小起售量
24   | 起售规格
25   | 包装(必填)
26   | 体积（长*宽*高）
27   | 重量（千克）
28   | 规格-区间（净重/毛重+最低重量）
29   | 规格-区间2（最高重量+单位）
30   | 规格-容量
31   | 规格-数量
32   | 果规
33   | 尺寸
34   | 级别
35   | 口味
36   | 售后单位
37   | 最大售后量
```

## 修复的方法和文件

### 1. `getRowOriginTrimStringData` 方法
**文件**: `src/main/java/net/summerfarm/service/impl/ProductsServiceImpl.java`

**修改内容**:
- 将 SKU 编码读取索引从 `result[16]` 改为 `result[17]`
- 调整默认值设置的索引位置

**修改前**:
```java
Category category = categoryMapper.selectBySku(result[16]);
InventoryVO select = inventoryMapper.selectInventoryVOBySku(result[16]);
if (StringUtils.isBlank(result[17])) {
    result[17] = "自营";
}
```

**修改后**:
```java
Category category = categoryMapper.selectBySku(result[17]);
InventoryVO select = inventoryMapper.selectInventoryVOBySku(result[17]);
if (StringUtils.isBlank(result[18])) {
    result[18] = "自营";
}
```

### 2. `originDataToInventory` 方法
**文件**: `src/main/java/net/summerfarm/service/impl/ProductsServiceImpl.java`

**修改内容**: 将所有基于 `lastIndex` 的相对索引计算改为直接使用绝对索引

**主要字段映射**:
```java
// SKU编码: lastIndex - 21 → 索引17
String sku = originStringData[17];

// 性质: lastIndex - 20 → 索引18  
String productType = originStringData[18];

// 代仓所属: lastIndex - 19 → 索引19
String replaceBelong = originStringData[19];

// 类型: lastIndex - 18 → 索引20
String extType = originStringData[20];

// 进口/国产: lastIndex - 17 → 索引21
String isDomestic = originStringData[21];

// 展示平均值: lastIndex - 16 → 索引22
String avg = originStringData[22];

// 最小起售量: lastIndex - 15 → 索引23
String baseSaleQuantity = originStringData[23];

// 起售规格: lastIndex - 14 → 索引24
String baseSaleUnit = originStringData[24];

// 包装: lastIndex - 13 → 索引25
String unit = originStringData[25];

// 体积: lastIndex - 12 → 索引26
String volume = originStringData[26];

// 重量: lastIndex - 11 → 索引27
String weightNum = originStringData[27];

// 售后单位: lastIndex - 2 → 索引36
String afterSaleUnit = originStringData[36];

// 最大售后量: lastIndex - 1 → 索引37
String afterSaleQuantity = originStringData[37];
```

### 3. `originDataToSaleProperty` 方法
**文件**: `src/main/java/net/summerfarm/service/impl/ProductsServiceImpl.java`

**修改内容**: 修复销售属性的索引映射

**主要字段映射**:
```java
// 规格-区间: lastIndex - 10 → 索引28
String firstSection = originStringData[28];

// 规格-区间2: lastIndex - 9 → 索引29
String endSection = originStringData[29];

// 规格-容量: lastIndex - 8 → 索引30
String w1 = originStringData[30];

// 规格-数量: lastIndex - 7 → 索引31
String w2 = originStringData[31];

// 果规: lastIndex - 6 → 索引32
String fruit = originStringData[32];

// 尺寸: lastIndex - 5 → 索引33
String size = originStringData[33];

// 级别: lastIndex - 4 → 索引34
String level = originStringData[34];

// 口味: lastIndex - 3 → 索引35
String toast = originStringData[35];
```

### 4. `originDataToProduct` 方法
**文件**: `src/main/java/net/summerfarm/service/impl/ProductsServiceImpl.java`

**修改内容**:
```java
// 其它介绍: lastIndex - 23 → 索引16
products.setOtherSlogan(originStringData[16]);
```

## 修复效果

1. **SKU 编码正确读取**: 现在能从正确的列（索引17）读取 SKU 编码
2. **SKU 属性正确映射**: 所有 SKU 相关属性都能从正确的列读取
3. **销售属性正确解析**: 规格、果规、尺寸等销售属性能正确解析
4. **数据完整性**: 确保所有 Excel 列的数据都能被正确读取和处理

## 测试建议

1. 使用提供的 Excel 表头格式创建测试文件
2. 填入测试数据，确保每列都有相应的测试值
3. 执行批量导入功能，验证：
   - SKU 编码是否正确读取
   - SKU 属性是否正确设置
   - 销售属性是否正确解析
   - 数据是否完整保存到数据库

## 注意事项

1. 此修复基于提供的 Excel 表头结构，如果表头结构发生变化，需要相应调整索引
2. 建议在生产环境部署前进行充分测试
3. 可以考虑将索引映射提取为配置文件，便于后续维护
